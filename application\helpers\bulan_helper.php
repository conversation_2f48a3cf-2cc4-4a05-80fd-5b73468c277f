<?php  if ( ! defined('BASEPATH')) exit('No direct script access allowed');
if ( ! function_exists('bulan'))
{
    function bulan($bln)
    {
        switch ($bln)
        {
            case 1:
            return "<PERSON><PERSON><PERSON>";
            break;
            case 2:
            return "<PERSON><PERSON><PERSON>";
            break;
            case 3:
            return "<PERSON><PERSON>";
            break;
            case 4:
            return "April";
            break;
            case 5:
            return "<PERSON>";
            break;
            case 6:
            return "Juni";
            break;
            case 7:
            return "<PERSON><PERSON>";
            break;
            case 8:
            return "Agustus";
            break;
            case 9:
            return "September";
            break;
            case 10:
            return "Oktober";
            break;
            case 11:
            return "November";
            break;
            case 12:
            return "Desember";
            break;
        }
    }
}