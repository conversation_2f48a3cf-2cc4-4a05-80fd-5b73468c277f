<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SkriningModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_skrining_mpp';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib Angka',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_skrining_mpp', $data);
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_skrining_mpp.id', $id);
    $this->db->update('keperawatan.tb_skrining_mpp', $data);
  }

  public function history($nomr, $param)
  {
    if (isset($param)) {
      if ($param == 'jumlah') {
        $this->db->select('s.id');
      } elseif ($param == 'tabel') {
        $this->db->select(
          's.id, s.tanggal, s.waktu, s.perlu_mpp, master.getNamaLengkapPegawai(ap.NIP) pengisi, s.created_at, s.status'
        );
      }
    }
    $this->db->from('keperawatan.tb_skrining_mpp s');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = s.oleh', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = s.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('p.NORM', $nomr);
    if (isset($param)) {
      if ($param == 'jumlah') {
        $this->db->where('s.status', 1);
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        $this->db->order_by('s.tanggal', 'desc');
        $this->db->order_by('s.waktu', 'desc');
        return $this->db->get();
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  public function detail($id)
  {
    $this->db->select(
      'id, nokun, tanggal, waktu, rawat_inap_penuh, isu_sosial, bunuh_diri, potensi_komplain, usia, kognitif_rendah,
      penyakit_kronis, fungsional_rendah, riwayat_peralatan_medis, gangguan_mental, sering_masuk, biaya_tinggi,
      pembiayaan_kompleks, melebihi_lama_rawat, perlu_mpp'
    );
    $this->db->from('keperawatan.tb_skrining_mpp');
    $this->db->where('id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function periksa($nomr)
  {
    $this->db->select('s.perlu_mpp');
    $this->db->from('keperawatan.tb_skrining_mpp s');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = s.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('p.NORM', $nomr);
    $this->db->where('s.status', 1);
    $this->db->order_by('s.created_at', 'desc');
    $query = $this->db->get();
    return $query->row();
  }
}

/* End of file SkriningModel.php */
/* Location: ./application/model/MPP/SkriningModel.php */