<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BuktiPelayananRJ extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    // if (!in_array(8, $this->session->userdata('akses'))) {
    //   redirect('login');
    // }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'emr/buktiPelayananRJ/BuktiPelayananRJModel'));
  }


  public function index()
  {
    $nomr  = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'nomr'  => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'resep' => $this->masterModel->referensi(1742),
      'radiologi' => $this->masterModel->referensi(1743),
      'laboratorium' => $this->masterModel->referensi(1744),
      'rehab' => $this->masterModel->referensi(1745),
    );

    $this->load->view('Pengkajian/emr/buktiPelayananRJ/index', $data);
  }

  public function icd10()
  {
    $result = $this->BuktiPelayananRJModel->icd10();
    $data = array();
    foreach ($result as $row) {
      $sub_array = array();
      $sub_array['id'] = $row->CODE;
      $sub_array['text'] = $row->CODE . ' - ' . $row->STR;
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function icd9()
  {
    $result = $this->BuktiPelayananRJModel->icd9();
    $data = array();
    foreach ($result as $row) {
      $sub_array = array();
      $sub_array['id'] = $row->CODE;
      $sub_array['text'] = $row->CODE . ' - ' . $row->STR;
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function simpanBukti()
  {
    $this->db->trans_begin();

    $post = $this->input->post();
    $dataicd10 = array();
    $dataicd9 = array();

    $dataBukti = array (
      'nokun'                      => $post['nokun'],
      'tanggal_bprj'               => $post['tanggal'],
      'anamnesa'                   => $post['anamnesa_singkat'],
      'pemeriksaan_fisik'          => $post['pemeriksaan_fisik'],
      'pemeriksaan_penunjang'      => $post['pemeriksaan_penunjang'],
      'resep'                      => $post['resep'],
      'radiologi'                  => $post['radiologi'],
      'lab'                        => $post['laboratorium'],
      'rehab'                      => $post['rehab'],
      'dokter'                     => $post['dokter'],
      'oleh'                       => $this->session->userdata('id'),
    );
    // echo "<pre>";print_r($dataBukti);echo "</pre>";

    $this->BuktiPelayananRJModel->simpanBuktiPelayananRJ($dataBukti);
    $idBukti = $this->db->insert_id();

        $dmicd10 = $post['diagnosa_medis_icd10'];
    
        $index = 0; // Set index array awal dengan 0
        foreach($dmicd10 as $datadmicd10){ // Kita buat perulangan berdasarkan nis sampai data terakhir
          array_push($dataicd10, array(
                'bukti_pelayanan_rj_fk'      => $idBukti,
                'tindakan_icd10'             => $dmicd10[$index],
            ));
            
            $index++;
        }
    
        // echo "<pre>";print_r($dataicd10);echo "</pre>";
        $this->BuktiPelayananRJModel->batch_icd10($dataicd10);

        $tpicd9 = $post['tindakan_prosedur_icd9'];
    
        $index = 0; // Set index array awal dengan 0
        foreach($tpicd9 as $datadmicd9){ // Kita buat perulangan berdasarkan nis sampai data terakhir
          array_push($dataicd9, array(
                'bukti_pelayanan_rj_fk'     => $idBukti,
                'tindakan_icd9'             => $tpicd9[$index],
            ));
            
            $index++;
        }
    
        // echo "<pre>";print_r($dataicd9);echo "</pre>";
        $this->BuktiPelayananRJModel->batch_icd9($dataicd9);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyBuktiPelayananRJ()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    // $nomr = $this->uri->segment(6);
    $listBukti = $this->BuktiPelayananRJModel->listHistoryBuktiPelayananRJ($nomr);

    $data = array();
    $no = 1;
    foreach ($listBukti->result() as $BPRJ) {
        $status = 'Aktif';
        $button = '<a href="#modalBuktiPelayananRJ" class="btn btn-primary btn-block" data-id="'.$BPRJ->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>
        <a href="#cetak" class="btn btn-success btn-block item_cetak" data-toggle="modal" data-backdrop="static" data-keyboard="false" id="item_cetak" data="'.$BPRJ->id.'"><i class="fas fa-file-alt"></i> Cetak </a>';

      $data[] = array(
        $no,
        $BPRJ->nokun,
        $BPRJ->DOKTERPELAKSANA,
        $BPRJ->OLEH,
        date("d-m-Y H:i:s",strtotime($BPRJ->tanggal)),
        $status,
        $button,

      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listBukti->num_rows(),
      "recordsFiltered" => $listBukti->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalEditBuktiPelayananRJ()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    $gpBuktiPelayananRJ = $this->BuktiPelayananRJModel->getBuktiPelayananRJ($id);
    $gpIcd10 = $this->BuktiPelayananRJModel->geticd10($id);
    $gpIcd9 = $this->BuktiPelayananRJModel->geticd9($id);
    $nokun = $gpBuktiPelayananRJ['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'gpBuktiPelayananRJ' => $gpBuktiPelayananRJ,
      'gpIcd10' => $gpIcd10,
      'gpIcd9' => $gpIcd9,
      'IdgpIcd10' => $gpIcd10,
      'IdgpIcd9' => $gpIcd9,
      'icd10' => $this->BuktiPelayananRJModel->icd10_new(),
      'icd9' => $this->BuktiPelayananRJModel->icd9_new(),
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'resep' => $this->masterModel->referensi(1742),
      'radiologi' => $this->masterModel->referensi(1743),
      'laboratorium' => $this->masterModel->referensi(1744),
      'rehab' => $this->masterModel->referensi(1745),
      'getNomr' => $getNomr,
    );

    $this->load->view('Pengkajian/emr/buktiPelayananRJ/modalBuktiPelayananRJ', $data);
  }

  public function updateBukti()
  {
    $this->db->trans_begin();

    $id = $this->input->post('id');
    $idicd10 = $this->input->post('idicd10');
    $idicd9 = $this->input->post('idicd9');
    $post = $this->input->post();
    $dataupdateicd10 = array();
    $dataupdateicd9 = array();

    $dataUpdate = array (
      'tanggal_bprj'               => $post['tanggal_edit'],
      'anamnesa'                   => $post['anamnesa_singkat_edit'],
      'pemeriksaan_fisik'          => $post['pemeriksaan_fisik_edit'],
      'pemeriksaan_penunjang'      => $post['pemeriksaan_penunjang_edit'],
      'resep'                      => $post['resep_edit'],
      'radiologi'                  => $post['radiologi_edit'],
      'lab'                        => $post['laboratorium_edit'],
      'rehab'                      => $post['rehab_edit'],
      'dokter'                     => $post['dokter_edit'],
      'oleh_update'                => $this->session->userdata('id'),
    );

    // echo "<pre>";print_r($dataUpdate);echo "</pre>";
    $this->BuktiPelayananRJModel->updateBuktiPelayananRJ($dataUpdate,$id);

    if(isset($post['diagnosa_medis_icd10_edit'])){
        $this->BuktiPelayananRJModel->batch_delete_icd10($idicd10);

        $dmicd10update = $post['diagnosa_medis_icd10_edit'];
        $index = 0; // Set index array awal dengan 0
        foreach($dmicd10update as $datadmicd10){ // Kita buat perulangan berdasarkan nis sampai data terakhir
          array_push($dataupdateicd10, array(
                'bukti_pelayanan_rj_fk'     => $id,
                'tindakan_icd10'             => $dmicd10update[$index],
            ));
            
            $index++;
        }
    
        // echo "<pre>";print_r($dataupdateicd10);echo "</pre>";
        $this->BuktiPelayananRJModel->batch_icd10($dataupdateicd10);

    }
    if(isset($post['tindakan_prosedur_icd9_edit'])){
        $this->BuktiPelayananRJModel->batch_delete_icd9($idicd9);

        $dmicd9update = $post['tindakan_prosedur_icd9_edit'];
        $index = 0; // Set index array awal dengan 0
        foreach($dmicd9update as $datadmicd9){ // Kita buat perulangan berdasarkan nis sampai data terakhir
          array_push($dataupdateicd9, array(
                'bukti_pelayanan_rj_fk'     => $id,
                'tindakan_icd9'             => $dmicd9update[$index],
            ));
            
            $index++;
        }
    
        // echo "<pre>";print_r($dataupdateicd9);echo "</pre>";
        $this->BuktiPelayananRJModel->batch_icd9($dataupdateicd9);
    }

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}

/* End of file BuktiPelayananRJ.php */
/* Location: ./application/controllers/emr/BuktiPelayananRJ.php */
