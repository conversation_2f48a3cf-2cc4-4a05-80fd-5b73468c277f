<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Permintaan extends ci_controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(21, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->library('cart');
    $this->load->model(array('inventory/Model_barang', 'inventory/Model_permintaan', 'inventory/Model_pengiriman'));
    // chek_session();
  }

  function index()
  { {
      $barang         = $this->Model_barang->tampilkan_data();
      $ruangan        = $this->Model_permintaan->tampilkan_ruangan()->result();
      $ruanganuser    = $this->Model_permintaan->tampilkan_ruanganuser()->result();
      $gudang         = $this->Model_permintaan->tampilkan_gudang()->result();
      $data_barang    = $this->cart->contents();
      $data           = array(
        'title'         => 'Halaman Input Permintaan',
        'isi'           => 'inventory/permintaan/index',
        'barang'        => $barang,
        'ruangan'       => $ruangan,
        'gudang'        => $gudang,
        'data_barang'   => $data_barang,
        'ruanganuser'   => $ruanganuser,
      );
      $this->load->view('layout/wrapper', $data);
    }
  }

  function pilih_barang()
  {
    if ($this->input->is_ajax_request() == true) {
      $barang         = $this->input->post('ID_BARANG');
      $jumlah         = $this->input->post('JUMLAH');
      $ruang          = $this->input->post('RUANG');
      $gudang         = $this->input->post('GUDANG');
      $nama           = $this->input->post('NAMA_BARANG');
      $tglpermintaan  = $this->input->post('TANGGAL_PERMINTAAN');
      $satuan         = $this->input->post('SATUAN');
      $harga          = '1000';
      $user           = $this->session->userdata("id_user");
      // echo "<pre>";print_r($_POST);exit();
      $data     = array(
        'id'         => $barang,
        'qty'        => $jumlah,
        'price'      => $harga,
        'name'       => $nama,
        'gudang'     => $gudang,
        'ruang'      => $ruang,
        'user'       => $user,
        'satuan'     => $satuan,
        'tglpermintaan' => $tglpermintaan
      );
      $this->form_validation->set_rules(
        'RUANG',
        'Ruangan',
        'trim|required'
      );

      if ($this->form_validation->run() == true) {

        $this->cart->insert($data);

        echo $this->tampil_barang();
      } else {
        echo $this->tampil_barang_error();
      }
    }
  }

  function tampil_barang()
  {
    $output = '';
    $no = 0;
    $data_barang = $this->cart->contents();
    foreach ($data_barang as $barang) {
      $no++;
      $output .= '
    <tr>
    <td>' . $barang['name'] . '</td>
    <td>' . $barang['satuan'] . '</td>
    <td>' . $barang['qty'] . '</td>
    <td><button type="button" id="' . $barang['rowid'] . '" class="hapus_cart btn btn-danger btn-sm"><i class="fas fa-ban"></i></button></td>
    </tr>
    ';
    }
    return $output;
  }

  function tampil_barang_error()
  {
    echo '
  <tr>
  <td colspan="4"><div class="alert alert-danger alert-dismissible" role="alert">
  <button type="button" class="close" data-dismiss="alert" aria-label="Close">
  <span aria-hidden="true">&times;</span>
  </button>
  <strong>Warning !</strong> Nama Ruangan peminta belum dipilih
  </div></td>
  </tr>
  ';
  }

  function list_barang()
  {
    echo $this->tampil_barang();
  }

  function hapus_barang()
  {
    $data = array(
      'rowid' => $this->input->post('row_id'),
      'qty' => 0,
    );
    $this->cart->update($data);
    echo $this->tampil_barang();
  }

  function simpan_order()
  {
    foreach (array_slice($this->cart->contents(), 0, 1) as $item) {
      $idpermintaan = $this->Model_permintaan->get_no_permintaan();
      $oleh         = $this->session->userdata("id");
      $tanggalminta = date('Y-m-d');
      $data = array(
        'NOMOR'               => $idpermintaan,
        'TANGGAL_PERMINTAAN'  => $tanggalminta,
        'ASAL'                => $item['ruang'],
        'TUJUAN'              => $item['gudang'],
        'OLEH'                => $oleh
      );
      $this->db->insert('invenumum.permintaan', $data);
    }
    foreach ($this->cart->contents() as $detil) {
      $data_detail = array(
        'PERMINTAAN' => $idpermintaan,
        'BARANG' => $detil['id'],
        'JUMLAH' => $detil['qty']
      );
      $this->db->insert('invenumum.permintaan_detil', $data_detail);
    }
    $this->cart->destroy();
    redirect('inventory/permintaan');
  }

  function getnopermintaan($ruangan)
  {
    $tanggal = date("Y-m-d");
    $query = $this->db->query("SELECT invenumum.generateNoPermintaan('$ruangan','$tanggal') ID")->row();
    return $query->ID;
  }

  public function pilihbarang()
  {
    $id = $this->uri->segment(4);
    $result = $this->Model_permintaan->getbaranggudang($id);
    $json = array();
    foreach ($result as $row) {
      $stok = $row['STOK'] == 0 ? "style='color:red'" : "";
      $tampilstok = $row['STOK'] == 0 ? ' - 0' : "";
      $json[] = array('id' => $row['ID_BARANG_GUDANG'], 'text' => "<div $stok>" . $row['BARANG'] . $tampilstok . "</div>", 'html' => "<div $stok><b>" . $row['BARANG']  . $tampilstok . "</div>");
    }
    echo json_encode($json);
  }

  function tampil_nama_barang()
  {
    $id = $this->input->post('id');
    $data = $this->Model_permintaan->get_subkategori_nama($id);
    echo json_encode($data);
  }

  function tampil_satuan_barang()
  {
    $id = $this->input->post('id');
    $data = $this->Model_permintaan->get_satuan($id);
    echo json_encode($data);
  }

  function tampil_id_gudang()
  {
    $id = $this->input->post('id');
    $data = $this->Model_permintaan->get_id_gudang($id);
    echo json_encode($data);
  }

  public function datapermintaan()
  {
    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listpermintaan = $this->Model_permintaan->datapermintaan();
    $data  = array();
    $no    = 1;
    foreach ($listpermintaan->result() as $lp) {
      if ($lp->STATUS == 1) {

        $ok = "<p class='text-success'>Prosess</p>";
      } else {

        $ok = "<p class='text-info'>Final</p>";
      }

      $data[] = array(
        $no,
        $lp->RUANG_ASAL,
        $lp->TUJUAN,
        date("d-m-Y", strtotime($lp->TANGGAL)),
        $ok,
        '<a href="#detailpermintaan" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-id="' . $lp->NOMOR . '"><i class="fas fa-search"></i> Lihat</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listpermintaan->num_rows(),
      "recordsFiltered" => $listpermintaan->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function datamodal()
  {
    $id       = $this->input->post('id');
    $record =  $this->Model_permintaan->detail_minta($id)->result_array();
    $data     = array(
      'record' => $record,
    );
    $this->load->view('inventory/permintaan/ModalDetilPermintaan', $data);
  }

  function tampil_id_barang()
  {
    $id = $this->input->post('id');
    $data = $this->Model_permintaan->get_subkategori($id);
    echo json_encode($data);
  }
}
