<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Sofa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/ruanganIntensif/SofaMasukModel', 'rekam_medis/rawat_inap/ruanganIntensif/SofaKeluarModel'));
    }

    public function index()
    {
        $data = array(
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'getsofa' => $this->SofaMasukModel->get_sofa($this->uri->segment(2)),
            'getsofakeluar' => $this->SofaKeluarModel->get_sofa($this->uri->segment(2)),
        );
        $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/sofa/index', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $rules = $this->SofaMasukModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataSOFA = array(
                        'nopen'             => $post['nopen'],
                        'skor_indikator_1'  => $post['skor_indikator_1'],
                        'skor_indikator_2'  => $post['skor_indikator_2'],
                        'skor_indikator_3'  => $post['skor_indikator_3'],
                        'skor_indikator_4'  => $post['skor_indikator_4'],
                        'skor_indikator_5'  => $post['skor_indikator_5'],
                        'skor_indikator_6'  => $post['skor_indikator_6'],
                        'nilai_masuk_1'     => $post['nilai_masuk_1'],
                        'nilai_masuk_2'     => $post['nilai_masuk_2'],
                        'nilai_masuk_3'     => $post['nilai_masuk_3'],
                        'nilai_masuk_4'     => $post['nilai_masuk_4'],
                        'nilai_masuk_5'     => $post['nilai_masuk_5'],
                        'nilai_masuk_6'     => $post['nilai_masuk_6'],
                        'total_masuk'       => $post['total_masuk'],
                        'oleh'              => $this->session->userdata('id')
                    );
                    $this->db->replace('keperawatan.tb_sofa_masuk', $dataSOFA);

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'ambil') {
                $post = $this->input->post(NULL, TRUE);
                $dataSOFA = $this->SofaMasukModel->get($post['nopen'], true);

                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataSOFA
                ));
            } else if ($param == 'count') {
                $result = $this->SofaMasukModel->get_count();;
                echo json_encode($result);
            }
        }
    }

    public function actionKeluar($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $rules = $this->SofaKeluarModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataSOFA = array(
                        'nopen'             => $post['nopen'],
                        'skor_indikator_1'  => $post['skor_indikator_1'],
                        'skor_indikator_2'  => $post['skor_indikator_2'],
                        'skor_indikator_3'  => $post['skor_indikator_3'],
                        'skor_indikator_4'  => $post['skor_indikator_4'],
                        'skor_indikator_5'  => $post['skor_indikator_5'],
                        'skor_indikator_6'  => $post['skor_indikator_6'],
                        'nilai_keluar_1'     => $post['nilai_keluar_1'],
                        'nilai_keluar_2'     => $post['nilai_keluar_2'],
                        'nilai_keluar_3'     => $post['nilai_keluar_3'],
                        'nilai_keluar_4'     => $post['nilai_keluar_4'],
                        'nilai_keluar_5'     => $post['nilai_keluar_5'],
                        'nilai_keluar_6'     => $post['nilai_keluar_6'],
                        'total_keluar'       => $post['total_keluar'],
                        'oleh'              => $this->session->userdata('id')
                    );
                    $this->db->replace('keperawatan.tb_sofa_keluar', $dataSOFA);

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'ambil') {
                $post = $this->input->post(NULL, TRUE);
                $dataSOFA = $this->SofaKeluarModel->get($post['nopen'], true);

                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataSOFA
                ));
            } else if ($param == 'count') {
                $result = $this->SofaKeluarModel->get_count();;
                echo json_encode($result);
            }
        }
    }

    public function datatables()
    {
        $result = $this->SofaMasukModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_sofa" data-id="' . $row->NOPEN . '"><i class="fa fa-eye"></i> Lihat</a><a class="btn btn-warning btn-block btn-sm" href="#" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row->TANGGAL;
            $sub_array[] = $row->RUANGAN_KUNJUNGAN;
            $sub_array[] = $row->DPJP;
            $sub_array[] = $row->USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->SofaMasukModel->total_count(),
            "recordsFiltered"   => $this->SofaMasukModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function datatablesKeluar()
    {
        $result = $this->SofaKeluarModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_sofa_keluar" data-id="' . $row->NOPEN . '"><i class="fa fa-eye"></i> Lihat</a><a class="btn btn-warning btn-block btn-sm" href="#" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row->TANGGAL;
            $sub_array[] = $row->RUANGAN_KUNJUNGAN;
            $sub_array[] = $row->DPJP;
            $sub_array[] = $row->USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->SofaKeluarModel->total_count(),
            "recordsFiltered"   => $this->SofaKeluarModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
