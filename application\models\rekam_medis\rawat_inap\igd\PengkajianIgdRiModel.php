<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengkajianIgdRiModel extends MY_Model {
  protected $_table_name = 'keperawatan.tb_keperawatan';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>ka.'
      ),
    ),
  );

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('pk.NOMOR NOKUN
        , rk.ID ID_RUANGAN
        , rk.DESKRIPSI RUANGAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , kp.id_emr ID_EMR_PERAWAT
        , kp.created_at TANGGAL_PENGKAJIAN_PERAWAT
        , master.getNamaLengkapPegawai(peng.NIP) USER_PERAWAT
        , md.id_emr ID_EMR_MEDIS
        , md.created_at TANGGAL_PENGKAJIAN_MEDIS
        , master.getNamaLengkapPegawai(pemed.NIP) USER_MEDIS
        , p.NOMOR NOPEN
        , p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , kp.created_by ID_USER_PERAWAT
        , md.created_by ID_USER_MEDIS
        , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
        , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18, 2, 1) USIA
        , kp.jenis JENIS_PENGKAJIAN_PERAWAT
        , md.jenis JENIS_PENGKAJIAN_MEDIS
        ,HOUR(TIMEDIFF(NOW(),md.created_at)) DURASI_MEDIS,IF(HOUR(TIMEDIFF(NOW(),md.created_at))<=24,1,0) STATUS_EDIT_MEDIS
        ,HOUR(TIMEDIFF(NOW(),kp.created_at)) DURASI_PERAWAT,IF(HOUR(TIMEDIFF(NOW(),kp.created_at))<=24,1,0) STATUS_EDIT_PERAWAT');
    $this->db->from('pendaftaran.kunjungan pk');
    $this->db->join('keperawatan.tb_keperawatan kp','pk.NOMOR = kp.nokun AND kp.flag=1 AND kp.`status`=1','LEFT');
    $this->db->join('medis.tb_medis md','pk.NOMOR = md.nokun AND md.flag=1 AND md.`status`=1','LEFT');
    $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
    $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
    $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
    $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
    $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
    $this->db->join('master.pasien pas','pas.NORM = p.NORM','LEFT');
    $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
    $this->db->join('master.ruangan rp','rp.ID = tp.RUANGAN','LEFT');
    $this->db->join('master.referensi refpj','refpj.ID = pj.JENIS AND refpj.JENIS=10','LEFT');
    $this->db->join('aplikasi.pengguna peng','peng.ID = kp.created_by','LEFT');
    $this->db->join('aplikasi.pengguna pemed','pemed.ID = md.created_by','LEFT');
    

    $this->db->where('p.NORM',$this->input->post('nomr'));
    $this->db->where("(kp.id_emr IS NOT NULL OR md.id_emr IS NOT NULL)");
    $this->db->where("(kp.jenis!=6 OR md.jenis!=6)");
    $this->db->group_by('pk.NOMOR');
    $this->db->order_by('pk.MASUK ', 'DESC');
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }

  //  Hitung jumlah indeks barthel
  function get_count_indeksBarthel($nokun){
    $query = $this->db->query(
      "SELECT COUNT(tbbi.nokun) JUMLAH
      FROM keperawatan.tb_barthel_indek tbbi
      WHERE tbbi.nokun = '$nokun'"
    );
    if ($query->num_rows() > 0) {
     return $query->row()->JUMLAH;
   }
   return false;
 }

 // Simpan tanda vital
public function simpanTandaVital($dataTandaVital)
{
  $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
  return $this->db->insert_id();
}

public function hasilIndeksBarthel($nokun)
 {
    $query = $this->db->query(
        "SELECT *
        FROM keperawatan.tb_barthel_indek bi
        WHERE bi.nokun='$nokun' AND bi.ref='9' AND bi.`status`='1'"
    );
    return $query->row_array();
}

  // Simpan kesadaran
public function simpanKesadaran($dataKesedaran)
{
  $this->db->insert('db_pasien.tb_kesadaran', $dataKesedaran);
  return $this->db->insert_id();
}  

  // Simpan O2
public function simpanO2($dataO2)
{
  $this->db->insert('db_pasien.tb_o2', $dataO2);
  return $this->db->insert_id();
}

  public function getNomrIgd($nopen)
    {
        $query = $this->db->query(
            "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
        , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , pas.JENIS_KELAMIN ID_JK
        , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
        , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
        , p.TANGGAL TANGGAL_DAFTAR
        , r.JENIS_KUNJUNGAN
        , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
        , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
        ) DESKRIPSI_DIAGNOSA_MASUK
        , ref.ID IDPENJAMIN
        , ref.DESKRIPSI PENJAMIN
        , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
        dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
        )))) STATUS_KUNJUNGAN
        , penggu.ID ID_USER
        , dok.ID ID_DOKTER
        , pas.TANGGAL_LAHIR
        , dtt.NAME_PIC
        , pk.REF
        , mkp.NOMOR NOTLPN
        ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) ID_TUJUAN_FARMASI

        , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) TUJUAN_FARMASI
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
        , ppk.NAMA RUJUKAN_DARI
        , refdar.DESKRIPSI GOL_DARAH
        , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=9
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_KEPERAWATAN_DEWASA_RI
        , (SELECT id_emr FROM medis.tb_medis kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=9
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_MEDIS_DEWASA_RI

        FROM pendaftaran.pendaftaran p
        LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
        LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.`STATUS` NOT IN (0)
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
        LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND refdar.JENIS=6
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
        LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
        LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM

        WHERE tp.`STATUS` not in (0,1)
        AND p.`STATUS`!= 0
        #AND pk.`STATUS` NOT IN (0) 
          AND p.NOMOR = '$nopen'
        GROUP BY dtt.ID #DESC"
        );
        return $query->row_array();
    }

    // Get Pengkajian Rawat Inap IGD
public function getPengkajian($idemr)
{
  $query = $this->db->query(
    'SELECT kp.id_emr ID_EMR
    , master.getNamaLengkapPegawai(peng.NIP) USER
    , p.NOMOR NOPEN
    , p.TANGGAL TANGGAL_DAFTAR
    , rp.DESKRIPSI RUANGAN_PENDAFTARAN, refpj.DESKRIPSI PENJAMIN
    , dm.ICD DIAGNOSA_MASUK
    , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
    , pk.NOMOR KUNJUNGAN, pk.MASUK TANGGAL_MASUK_RUANGAN
    , pk.KELUAR TANGGAL_KELUAR_RUANGAN
    , rk.DESKRIPSI RUANGAN_KUNJUNGAN, p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
    , IF(pas.JENIS_KELAMIN = 1,
    "Laki-Laki",
    "Perempuan") JK
    , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
    , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
    , TIMEDIFF(NOW()
    , (SELECT kep.created_at
            FROM keperawatan.tb_keperawatan kep
                LEFT JOIN pendaftaran.pendaftaran pen ON pen.NOMOR = kep.nopen
            WHERE kep.status = 1 AND pen.NORM = p.NORM
            ORDER BY kep.created_at DESC
            LIMIT 1)) DURATION_KEPERAWATAN
    , IF(HOUR(TIMEDIFF(NOW(),MAX(kp.created_at)))<=24=1,1,IF(kp.flag=1,0,1)) STATUS_EDIT     
     , kp.created_at TANGGAL_PEMBUATAN_EMR
    , kp.rujukan, kp.diagnosa_masuk DIAGNOSA_MASUK_PERMINTAAN_DIRAWAT
    , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dm.ICD LIMIT 1) DESKRIPSI_DIAGNOSA_MASUK
    , (SELECT anam.id_auto_allo FROM keperawatan.tb_anamnesa_perawat anam
            WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
        LIMIT 1) ID_AUTO_ALLO
    , (SELECT var.variabel FROM keperawatan.tb_anamnesa_perawat anam
            LEFT JOIN db_master.variabel var ON var.id_variabel = anam.id_auto_allo
            WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
        LIMIT 1) AUTO_ALLO
    , (SELECT anam.hubungan_dengan_pasien FROM keperawatan.tb_anamnesa_perawat anam
            WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
        LIMIT 1) HUBUNGAN_DENGAN_PASIEN
    , (SELECT anam.allo_nama FROM keperawatan.tb_anamnesa_perawat anam
            WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
        LIMIT 1) ALLO_NAMA
    , (SELECT anam.info_dari_keluarga_pasien FROM keperawatan.tb_anamnesa_perawat anam
            WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
            LIMIT 1) INFO_DARI_KELUARGA_PASIEN
     
     /*START KUNJUNGAN_IGD*/
     , igz.jenis_kunjungan JENIS_KUNJUNGAN
     , igz.rujukan RUJUKAN_KEDUA
     , igz.nama_pengantar NAMA_PENGANTAR
     , igz.dibawa_dengan DIBAWA_DENGAN
     , igz.hubungan_pasien HUBUNGAN_PASIEN
     /*END KUNJUNGAN_IGD*/
         
    /*START RIWAYAT KESEHATAN SEBELUMNYA*/
        , rke.riwpeng_operasi_hemo RIWPENG_OPERASI
        , rke.pengobatan_operasi_lainnya_hemo PENGOBATAN_OPERASI_LAINNYA
        , rke.riwpeng_radiasi_hemo RIWPENG_RADIASI
        , rke.pengobatan_radiasi_lainnya_hemo PENGOBATAN_RADIASI_LAINNYA
        , rke.riwpeng_terapi_hemo RIWPENG_KEMOTERAPI
        , rke.pengobatan_terapi_lainnya_hemo PENGOBATAN_KEMOTERAPI_LAINNYA
        , rke.pengobatan_lainlain_lainnya_hemo PENGOBATAN_LAINLAIN_LAINNYA  
     , valerg.id_variabel ID_ALERGI, rke.isi_alergi ISI_ALERGI, rke.reaksi_alergi REAKSI_ALERGI
    , vtran.id_variabel ID_RIWAYAT_TRANSFUSI, vralg.id_variabel ID_REAKSI_TRANSFUSI
    , rke.isi_reaksi_transfusi ISI_REAKSI_TRANSFUSI
    /*END RIWAYAT KESEHATAN SEBELUMNYA*/   

    /*START PEMERIKSAAN FISIK*/
    , (SELECT kes.id FROM db_pasien.tb_kesadaran kes
            LEFT JOIN db_master.variabel kesad ON kesad.id_variabel=kes.kesadaran
        WHERE kes.data_source=10 AND kes.`status`=1
        AND kes.nomr=p.NORM AND kes.ref=kp.id_emr
        ORDER BY kes.created_at DESC
        LIMIT 1) ID_TABEL_KESADARAN
    , (SELECT kes.kesadaran FROM db_pasien.tb_kesadaran kes
            LEFT JOIN db_master.variabel kesad ON kesad.id_variabel=kes.kesadaran
        WHERE kes.data_source=10 AND kes.`status`=1
        AND kes.nomr=p.NORM AND kes.ref=kp.id_emr
        ORDER BY kes.created_at DESC
        LIMIT 1) ID_VARIABEL_KESADARAN
    , (SELECT kesad.variabel FROM db_pasien.tb_kesadaran kes
            LEFT JOIN db_master.variabel kesad ON kesad.id_variabel=kes.kesadaran
        WHERE kes.data_source=10 AND kes.`status`=1
        AND kes.nomr=p.NORM AND kes.ref=kp.id_emr
        ORDER BY kes.created_at DESC
        LIMIT 1) KESADARAN
    
     , (SELECT vit.id FROM db_pasien.tb_tanda_vital vit
            WHERE vit.data_source=10 AND vit.`status`=1
            AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
            ORDER BY vit.created_at DESC
        LIMIT 1) ID_TABEL_TANDA_VITAL
    , (SELECT vit.td_sistolik FROM db_pasien.tb_tanda_vital vit
            WHERE vit.data_source=10 AND vit.`status`=1
            AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
        ORDER BY vit.created_at DESC
        LIMIT 1) TD_SISTOLIK
    , (SELECT vit.td_diastolik FROM db_pasien.tb_tanda_vital vit
            WHERE vit.data_source=10 AND vit.`status`=1
            AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
            ORDER BY vit.created_at DESC
        LIMIT 1) TD_DIASTOLIK
    , (SELECT vit.pernapasan FROM db_pasien.tb_tanda_vital vit
            WHERE vit.data_source=10 AND vit.`status`=1
            AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
            ORDER BY vit.created_at DESC
        LIMIT 1) PERNAPASAN
    , (SELECT vit.nadi FROM db_pasien.tb_tanda_vital vit
            WHERE vit.data_source=10 AND vit.`status`=1
            AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
            ORDER BY vit.created_at DESC
        LIMIT 1) NADI
    , (SELECT vit.suhu FROM db_pasien.tb_tanda_vital vit
            WHERE vit.data_source=10 AND vit.`status`=1
            AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
            ORDER BY vit.created_at DESC
        LIMIT 1) SUHU

    , (SELECT tb.id FROM db_pasien.tb_tb_bb tb
            WHERE tb.data_source=10 AND tb.`status`=1
        AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
        ORDER BY tb.created_at DESC
        LIMIT 1) ID_TB_BB
    , (SELECT tb.tb FROM db_pasien.tb_tb_bb tb
            WHERE tb.data_source=10 AND tb.`status`=1
        AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
        ORDER BY tb.created_at DESC
        LIMIT 1) TB
    , (SELECT tb.bb FROM db_pasien.tb_tb_bb tb
            WHERE tb.data_source=10 AND tb.`status`=1
            AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
        ORDER BY tb.created_at DESC
        LIMIT 1) BB
    , (SELECT tb.jenis FROM db_pasien.tb_tb_bb tb
            WHERE tb.data_source=10 AND tb.`status`=1
        AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
        ORDER BY tb.created_at DESC
        LIMIT 1) JENIS_TB_BB
     , (SELECT o2.id FROM db_pasien.tb_o2 o2
            WHERE o2.data_source=10 AND o2.`status`=1
        AND o2.nomr=p.NORM AND o2.ref=kp.id_emr
        ORDER BY o2.created_at DESC
        LIMIT 1) ID_O2
   , (SELECT ewz.id FROM keperawatan.tb_ews ewz
            WHERE ewz.nokun=kp.nokun AND ewz.`status`=1 AND ewz.ref=kp.id_emr
        ORDER BY ewz.date_created DESC
        LIMIT 1) ID_EWS

    , (SELECT o2.saturasi_o2 FROM db_pasien.tb_o2 o2
            WHERE o2.data_source=10 AND o2.`status`=1
        AND o2.nomr=p.NORM AND o2.ref=kp.id_emr
        ORDER BY o2.created_at DESC
        LIMIT 1) SATURASI
        
     , (SELECT o2.penggunaan_o2 FROM db_pasien.tb_o2 o2
            WHERE o2.data_source=10 AND o2.`status`=1
        AND o2.nomr=p.NORM AND o2.ref=kp.id_emr
        ORDER BY o2.created_at DESC
        LIMIT 1) PENGGUNAAAN_O2

    , pf.apakah_ingin_masuk_keews APAKAH_INGIN_MASUK_KEEWS
    
    /*START STATUS FUNGSIONAL*/
    , bii.id ID_BERTHEL_INDEX
    /*END STATUS FUNGSIONAL*/

     /*START SKRINING NYERI*/
     , sny.id ID_SKRINING_NYERI
    , sny.metode METODE, sny.skor SKOR_NYERI, sny.farmakologi FARMAKOLOGI, sny.non_farmakologi NON_FARMAKOLOGI
    , sny.efek_samping ID_EFEK_SAMPING, sny.ket_efek_samping KET_EFEK_SAMPING, sny.provokative PROVOKATIVE
    , sny.quality QUALITY, sny.quality_lainnya QUALITY_LAINNYA, sny.regio REGIO, sny.severity SEVERITY, sny.time ID_TIME, sny.ket_time KET_TIME
     /*START SKRINING GIZI*/
     
     /*START SKRINING RESIKO JATUH*/
     , pusin.id_variabel ID_RESIKO_JATUH
     , berdi.id_variabel ID_RESIKO_JATUH_BERDIRI
     , jatuh.id_variabel ID_RESKIO_JATUH_6BLN
     /*END SKRINING RESIKO JATUH*/
     
    , psiko.id_variabel ID_PSIKOLOGIS
    , pf.isi_psikologi PSIKOLOGIS_LAINNYA
    , hukel.id_variabel ID_HUB_KELUARGA, fut.id_variabel ID_NAFKAH_UTAMA
    , serum.id_variabel ID_TINGGAL_SERUMAH, spiri.id_variabel ID_SPIRITUAL
    , pf.sebutkan_keyakinan SPIRITUAL_LAINNYA
    , pf.pengobatan_alternatif ID_PENGOBATAN_ALTERNATIF
    , pf.sebutkan_pengobatan_alternatif PENGOBATAN_ALTERNATIF
    , pf.pengobatan_bertentangan ID_PENGOBATAN_BERTENTANGAN
    , pf.sebutkan_pengobatan_bertentangan PENGOBATAN_BERTENTANGAN
     
     /*START MASALAH KESEHATAN*/
     , pf.masalah_kesehatan_keperawatan MASALAH_KESEHATAN
     /*END MASALAH KESEHATAN*/
     
     /*START DIAGNOSIS KEPERAWATAN*/
    , db_master.getIDAsuhanKeperawatan(kp.id_emr) ID_ASUHAN_KEPERAWATAN
    , db_master.getAsuhanKeperawatan(kp.id_emr) ASUHAN_KEPERAWATAN
    , db_master.getIDAsuhanKeperawatanDiagnosa(kp.id_emr) ID_DIAGNOSA_KEP
    , db_master.getAsuhanKeperawatanDiagnosa(kp.id_emr) DIAGNOSA_KEP
    , db_master.getIDAsuhanKeperawatanNOC(kp.id_emr) ID_NOC
    , db_master.getAsuhanKeperawatanNOC(kp.id_emr) NOC
    , db_master.getIDAsuhanKeperawatanNIC(kp.id_emr) ID_NIC
    , db_master.getAsuhanKeperawatanNIC(kp.id_emr) NIC
    , kp.`status` STATUS_EMR, db_master.getLainLainIDDiagnosa(kp.id_emr) ID_DIAGNOSA_LAIN_LAIN
    , db_master.getLainLainDiagnosa(kp.id_emr) DIAGNOSA_LAIN_LAIN
    , db_master.getLainLainIDNOC(kp.id_emr) ID_NOC_LAIN_LAIN, db_master.getLainLainNOC(kp.id_emr) NOC_LAIN_LAIN
    , db_master.getLainLainIDNIC(kp.id_emr) ID_NIC_LAIN_LAIN, db_master.getLainLainNIC(kp.id_emr) NIC_LAIN_LAIN
     /*END DIAGNOSIS KEPERAWATAN*/

    , keperawatan.getMasalahKesehatanParentID(kp.id_emr) MASALAH_KESEHATAN_PARENT_ID
    , keperawatan.getMasalahKesehatanParentDESC(kp.id_emr) MASALAH_KESEHATAN_PARENT_DESC

    , keperawatan.getMasalahKesehatanChildID(kp.id_emr) MASALAH_KESEHATAN_CHILD_ID
    , keperawatan.getMasalahKesehatanChildDESC(kp.id_emr) MASALAH_KESEHATAN_CHILD_DESC
    
    FROM keperawatan.tb_keperawatan kp

    LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
    LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
    LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
    LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
    LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
    LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
    LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
    LEFT JOIN master.ruangan rp ON rp.ID = tp.RUANGAN
    LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS = 10
    LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by
    LEFT JOIN keperawatan.tb_barthel_indek bii ON bii.nokun = kp.nokun AND bii.`status`=1 AND bii.ref=9
    LEFT JOIN keperawatan.tb_skrining_nyeri sny ON sny.ref = kp.id_emr AND sny.data_source=10 AND sny.`status`=1
    LEFT JOIN keperawatan.tb_skala_braden brad ON brad.nokun = kp.nokun AND brad.`status`=1
    LEFT JOIN keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa pde ON pde.nokun = kp.nokun AND pde.`status`=1
    LEFT JOIN keperawatan.tb_penilaian_risiko_skala_morse peris ON peris.nokun = kp.nokun AND peris.`status`=1
    LEFT JOIN keperawatan.tb_penilaian_humptydumpty_igd hump ON hump.nokun = kp.nokun AND hump.`status`=1
     LEFT JOIN keperawatan.tb_perencanaan_pemulangan_pasien ppp ON ppp.id_emr = kp.id_emr AND ppp.`status`=1
    LEFT JOIN db_pasien.tb_o2 odua ON odua.ref = kp.id_emr AND odua.data_source=1 AND odua.`status`=1

    LEFT JOIN keperawatan.tb_riwayat_kesehatan rke ON rke.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel valerg ON valerg.id_variabel = rke.alergi
    LEFT JOIN db_master.variabel vtran ON vtran.id_variabel = rke.riwayat_transfusi
    LEFT JOIN db_master.variabel vralg ON vralg.id_variabel = rke.reaksi_transfusi
    LEFT JOIN db_master.variabel vkebi ON vkebi.id_variabel = rke.kebiasaan
    LEFT JOIN db_master.variabel vrkan ON vrkan.id_variabel = rke.riwayat_kanker
    LEFT JOIN db_master.variabel vmet ON vmet.id_variabel = rke.riwayat_metabolik
    LEFT JOIN db_master.variabel vdd ON vdd.id_variabel = rke.deteksidini
    
    LEFT JOIN keperawatan.tb_kunjungan_igd igz ON igz.id_emr = kp.id_emr
    
    LEFT JOIN keperawatan.tb_riwayat_kelahiran rkel ON rkel.id_emr = kp.id_emr
     LEFT JOIN keperawatan.tb_skrining_gizi sgiz ON sgiz.id_emr = kp.id_emr
     
    LEFT JOIN keperawatan.tb_pemeriksaan_fisik pf ON pf.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_skrining_gizi sg ON sg.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_ews ewz ON ewz.ref = kp.id_emr AND ewz.`status`=1
    LEFT JOIN db_master.variabel penurbb ON penurbb.id_variabel = sg.penurunan_bb
    LEFT JOIN db_master.variabel asumak ON asumak.id_variabel = sg.asupan_bb
    LEFT JOIN db_master.variabel mulu ON mulu.id_variabel = pf.mulut
    LEFT JOIN db_master.variabel eso ON eso.id_variabel = pf.esophagus
    LEFT JOIN db_master.variabel abdo ON abdo.id_variabel = pf.abdomen

    LEFT JOIN db_master.variabel idung ON idung.id_variabel = pf.keluhan_pada_hidung
    LEFT JOIN db_master.variabel dada ON dada.id_variabel = pf.keluhan_pada_dada
    LEFT JOIN db_master.variabel jantung ON jantung.id_variabel = pf.keluhan_pada_jantung
    LEFT JOIN db_master.variabel pcjan ON pcjan.id_variabel = pf.alat_pacu_jantung
    LEFT JOIN db_master.variabel kelpar ON kelpar.id_variabel = pf.keluhan_pada_paru
    LEFT JOIN db_master.variabel pendar ON pendar.id_variabel = pf.perdarahan
    LEFT JOIN db_master.variabel keltid ON keltid.id_variabel = pf.keluhan_istirahat
    LEFT JOIN db_master.variabel mob ON mob.id_variabel = pf.kemampuan_mobilisasi
    LEFT JOIN db_master.variabel spiri ON spiri.id_variabel = pf.keyakinan

    LEFT JOIN keperawatan.tb_perencanaan_tindakan pertin ON pertin.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_edukasi_keperawatan ek ON ek.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel pusin ON pusin.id_variabel = pf.vertigo
    LEFT JOIN db_master.variabel berdi ON berdi.id_variabel = pf.sulit_berdiri
    LEFT JOIN db_master.variabel jatuh ON jatuh.id_variabel = pf.jatuh_dlm_6
    LEFT JOIN db_master.variabel psiko ON psiko.id_variabel = pf.psikologis
    LEFT JOIN db_master.variabel hukel ON hukel.id_variabel = pf.hub_keluarga
    LEFT JOIN db_master.variabel fut ON fut.id_variabel = pf.nafkah_utama
    LEFT JOIN db_master.variabel serum ON serum.id_variabel = pf.tinggal

    LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan pak ON pak.id_emr = kp.id_emr
    LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON akd.ID = pak.id_asuhan_keperawatan_detil

    WHERE kp.`status`=1 AND kp.jenis=9 AND kp.id_emr="' . $idemr . '"

    GROUP BY kp.id_emr'
  );
return $query->row_array();
}

    public function getTandaVital($nokun)
    {
        $query = $this->db->query(
            "SELECT t.*, ds.deskripsi, master.getNamaLengkapPegawai(ap.NIP) oleh_desc
            FROM db_pasien.tb_tanda_vital t
            LEFT JOIN db_master.tb_data_source ds ON t.data_source = ds.id
            LEFT JOIN aplikasi.pengguna ap ON t.oleh = ap.ID
            LEFT JOIN pendaftaran.kunjungan pk ON t.nokun = pk.NOMOR
            WHERE t.nokun = '$nokun'
            ORDER BY t.created_at DESC
            LIMIT 1"
        );
        return $query->row_array();
    }

    public function getTbBb($nokun)
    {
        $query = $this->db->query(
            "SELECT t.*, ds.deskripsi, master.getNamaLengkapPegawai(ap.NIP) oleh_desc
            FROM db_pasien.tb_tb_bb t
            LEFT JOIN db_master.tb_data_source ds ON t.data_source = ds.id
            LEFT JOIN aplikasi.pengguna ap ON t.oleh = ap.ID
            WHERE t.nokun = '$nokun'
            ORDER BY t.created_at DESC
            LIMIT 1"
        );
        return $query->row_array();
    }

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
