<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class CERIModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s <PERSON>ajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('c.id, c.nokun, c.created_at tanggal, ru.DESKRIPSI ruangan, peng.NAMA user');
        $this->db->from('keperawatan.tb_ceri c');
        $this->db->join('pendaftaran.kunjungan kun','kun.NOMOR = c.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','pen.NOMOR = kun.NOPEN','LEFT');
        $this->db->join('master.ruangan ru','kun.RUANGAN = ru.ID','LEFT');
        $this->db->join('aplikasi.pengguna peng','c.oleh = peng.ID','LEFT');
        $this->db->where('c.STATUS !=','0');
        $this->db->where('pen.NORM',$this->input->post('nomr'));
        $this->db->order_by('c.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id_ceri)
    {
      $query = $this->db->query(
        'SELECT c.`*` FROM keperawatan.tb_ceri c
        WHERE c.id = "'.$id_ceri.'"'
      );
      return $query->row_array();
    }

    public function getHistoryCPIS($nokun)
    {
      $query = $this->db->query(
        'SELECT cpis.`*`, peng.NAMA FROM keperawatan.tb_cpis cpis
        LEFT JOIN aplikasi.pengguna peng ON cpis.oleh = peng.ID
        WHERE cpis.nokun = "'.$nokun.'"'
      );
      return $query->result_array();
    }

}
