<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianRiim extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRiimModel'
    ));
  }

  public function index($idLoadNorm, $idLoadNopen, $idLoadNokun, $idLoad){
  	$norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->PengkajianRiimModel->getNomrRiim($nopen);
    $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
    if($idLoad === "00000"){
    $getPengkajian = $this->PengkajianRiimModel->getPengkajian($getIdEmr);
    }else{
    $getPengkajian = $this->PengkajianRiimModel->getPengkajian($idLoad);
    }
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'idLoad' => $idLoad,
      'pasien' => $getNomr,
      'getPengkajian' => $getPengkajian,
      'anamnesis' => $this->masterModel->referensi(54),
      'hasilLaboratorium' => $this->masterModel->referensi(165),
      'hasilBmpTerakhir' => $this->masterModel->referensi(166),
      'kemoterapiTerdahulu' => $this->masterModel->referensi(167),
      'tindakanPerawatanTerakhir' => $this->masterModel->referensi(168),
      'riwayatGVHD' => $this->masterModel->referensi(169),
      'ESASnyeri' => $this->masterModel->referensi(170),
      'ESASlelah' => $this->masterModel->referensi(171),
      'ESASmual' => $this->masterModel->referensi(172),
      'ESASdepresi' => $this->masterModel->referensi(173),
      'ESAScemas' => $this->masterModel->referensi(174),
      'ESASmengantuk' => $this->masterModel->referensi(175),
      'ESASnafsuMakan' => $this->masterModel->referensi(176),
      'ESASsehat' => $this->masterModel->referensi(177),
      'ESASsesakNapas' => $this->masterModel->referensi(178),
      'ESASmasalah' => $this->masterModel->referensi(179),
    );
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/pengkajianRiim', $data);
  }

  public function simpanPengkajianRiim($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 12,
          'diagnosa_masuk' => $post['rujukanRiim'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataPemeriksaanFisik = array(
          'id_emr' => $getIdEmr,
          'rhesus' => isset($post['rhesusTerapiSistemikRi']) ? $post['rhesusTerapiSistemikRi'] : "",
          'skala_nyeri' => isset($post['skala_nyeri']) ? $post['skala_nyeri'] : "",
          'skala_lelah' => isset($post['skala_lelah']) ? $post['skala_lelah'] : "",
          'skala_mual' => isset($post['skala_mual']) ? $post['skala_mual'] : "",
          'skala_depresi' => isset($post['skala_depresi']) ? $post['skala_depresi'] : "",
          'skala_cemas' => isset($post['skala_cemas']) ? $post['skala_cemas'] : "",
          'skala_mengantuk' => isset($post['skala_mengantuk']) ? $post['skala_mengantuk'] : "",
          'skala_nafsu_makan' => isset($post['skala_nafsuMakan']) ? $post['skala_nafsuMakan'] : "",
          'skala_sehat' => isset($post['skala_sehat']) ? $post['skala_sehat'] : "",
          'skala_sesak_napas' => isset($post['skala_sesakNapas']) ? $post['skala_sesakNapas'] : "",
          'skala_masalah' => isset($post['skala_masalah']) ? $post['skala_masalah'] : "",
        );
        
        // echo "<pre>data Pemeriksaan fisik ";print_r($dataPemeriksaanFisik);echo "</pre>";

        $dataRiwayatKesehatan = array(
          'id_emr' => $getIdEmr,
          'hasil_laboratorium' => isset($post['hasilLaboratoriumRiim']) ? $post['hasilLaboratoriumRiim'] : "",
          'isi_laboratorium' => isset($post['hasil_laboratorium_desk']) ? json_encode($post['hasil_laboratorium_desk']) : "",
          'hasil_BMP' => isset($post['hasilBmpTerakhirRiim']) ? $post['hasilBmpTerakhirRiim'] : "",
          'isi_BMP' => isset($post['deskHasilBmpTerakhirRiim']) ? json_encode($post['deskHasilBmpTerakhirRiim']) : "",
          'kemoterapi' => isset($post['hasilKemoterapiRiim']) ? $post['hasilKemoterapiRiim'] : "",
          'isi_kemoterapi' => isset($post['deskHasilKemoterapiRiim']) ? json_encode($post['deskHasilKemoterapiRiim']) : "",
          'tindakan_perawatan' => isset($post['tindakanPerawatanTerakhirRiim']) ? $post['tindakanPerawatanTerakhirRiim'] : "",
          'perawatan_lain' => isset($post['deskTindakanPerawatanTerakhirRiim']) ? json_encode($post['deskTindakanPerawatanTerakhirRiim']) : "",
          'riwayat_graft' => isset($post['riwayatGvhdRiim']) ? $post['riwayatGvhdRiim'] : "",
        );

        // echo "<pre>data Riwayat Kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 12,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0 ,
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";
        // exit();
        
        $this->db->trans_begin();
        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->where('ref', $post['idemr']);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          // $this->db->replace('db_pasien.tb_tb_bb', $dataTbBb);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      }

      else if($param == 'count') {
        $result = $this->DewasaModel->get_count();
        echo json_encode($result);
      }

      else if($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataDewasaModel = $this->DewasaModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataDewasaModel
        ));
      }
    }
  }
}