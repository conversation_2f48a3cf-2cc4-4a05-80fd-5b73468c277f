<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AlatOperasiModel extends MY_Model
{
  protected $_table_name = 'medis.tb_alat_operasi';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib <PERSON>ka',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpanInstrumen($dataInstrumen)
  {
    $this->db->insert('medis.tb_alat_operasi_instrumen', $dataInstrumen);
    return $this->db->insert_id();
  }

  public function simpanKassa($dataKassa)
  {
    $this->db->insert('medis.tb_alat_operasi_kassa', $dataKassa);
    return $this->db->insert_id();
  }

  public function simpanTambahan($dataTambahan)
  {
    $this->db->insert('medis.tb_alat_operasi_tambahan', $dataTambahan);
    return $this->db->insert_id();
  }

  public function simpan($data)
  {
    $this->db->insert('medis.tb_alat_operasi', $data);
  }

  public function history($nokun, $param, $id)
  {
    if (isset($id)) {
      // Detail history
      $this->db->select(
        'a.id, a.id_instrumen, a.id_tambahan, a.id_kassa, a.nokun, a.tanggal, a.jam, a.tindakan_operasi,
        a.set_instrumen, i.duk_klem_pendek_pre_op, i.duk_klem_pendek_tambahan, i.duk_klem_sedang_pre_op,
        i.duk_klem_sedang_tambahan, i.duk_klem_panjang_pre_op, i.duk_klem_panjang_tambahan, i.scalpel_pendek_pre_op,
        i.scalpel_pendek_tambahan, i.scalpel_sedang_pre_op, i.scalpel_sedang_tambahan, i.scalpel_panjang_pre_op,
        i.scalpel_panjang_tambahan, i.anatomis_pendek_pre_op, i.anatomis_pendek_tambahan, i.anatomis_sedang_pre_op,
        i.anatomis_sedang_tambahan, i.anatomis_panjang_pre_op, i.anatomis_panjang_tambahan, i.diatermi_pendek_pre_op,
        i.diatermi_pendek_tambahan, i.diatermi_sedang_pre_op, i.diatermi_sedang_tambahan, i.diatermi_panjang_pre_op,
        i.diatermi_panjang_tambahan, i.cirurgis_pendek_pre_op, i.cirurgis_pendek_tambahan, i.cirurgis_sedang_pre_op,
        i.cirurgis_sedang_tambahan, i.cirurgis_panjang_pre_op, i.cirurgis_panjang_tambahan, i.benang_pendek_pre_op,
        i.benang_pendek_tambahan, i.benang_sedang_pre_op, i.benang_sedang_tambahan, i.benang_panjang_pre_op,
        i.benang_panjang_tambahan, i.jaringan_pendek_pre_op, i.jaringan_pendek_tambahan, i.jaringan_sedang_pre_op,
        i.jaringan_sedang_tambahan, i.jaringan_panjang_pre_op, i.jaringan_panjang_tambahan, i.kocher_pendek_pre_op,
        i.kocher_pendek_tambahan, i.kocher_sedang_pre_op, i.kocher_sedang_tambahan, i.kocher_panjang_pre_op,
        i.kocher_panjang_tambahan, i.naldvoeder_pendek_pre_op, i.naldvoeder_pendek_tambahan,
        i.naldvoeder_sedang_pre_op, i.naldvoeder_sedang_tambahan, i.naldvoeder_panjang_pre_op,
        i.naldvoeder_panjang_tambahan, i.langen_beck_pendek_pre_op, i.langen_beck_pendek_tambahan,
        i.langen_beck_sedang_pre_op, i.langen_beck_sedang_tambahan, i.langen_beck_panjang_pre_op,
        i.langen_beck_panjang_tambahan, i.preparir_pendek_pre_op, i.preparir_pendek_tambahan, i.preparir_sedang_pre_op,
        i.preparir_sedang_tambahan, i.preparir_panjang_pre_op, i.preparir_panjang_tambahan, i.klem_lurus_pendek_pre_op,
        i.klem_lurus_pendek_tambahan, i.klem_lurus_sedang_pre_op, i.klem_lurus_sedang_tambahan,
        i.klem_lurus_panjang_pre_op, i.klem_lurus_panjang_tambahan, i.liver_hak_pendek_pre_op,
        i.liver_hak_pendek_tambahan, i.liver_hak_sedang_pre_op, i.liver_hak_sedang_tambahan, i.liver_hak_panjang_pre_op,
        i.liver_hak_panjang_tambahan, i.tampon_pendek_pre_op, i.tampon_pendek_tambahan, i.tampon_sedang_pre_op,
        i.tampon_sedang_tambahan, i.tampon_panjang_pre_op, i.tampon_panjang_tambahan, i.klem_usus_pendek_pre_op,
        i.klem_usus_pendek_tambahan, i.klem_usus_sedang_pre_op, i.klem_usus_sedang_tambahan, i.klem_usus_panjang_pre_op,
        i.klem_usus_panjang_tambahan, i.bebkok_pendek_pre_op, i.bebkok_pendek_tambahan, i.bebkok_sedang_pre_op,
        i.bebkok_sedang_tambahan, i.bebkok_panjang_pre_op, i.bebkok_panjang_tambahan, i.spatel_pendek_pre_op,
        i.spatel_pendek_tambahan, i.spatel_sedang_pre_op, i.spatel_sedang_tambahan, i.spatel_panjang_pre_op,
        i.spatel_panjang_tambahan, i.mekolik_pendek_pre_op, i.mekolik_pendek_tambahan, i.mekolik_sedang_pre_op,
        i.mekolik_sedang_tambahan, i.mekolik_panjang_pre_op, i.mekolik_panjang_tambahan, t.suction_pre_op,
        t.suction_tambahan, t.k_medium_pre_op, t.k_medium_tambahan, t.retraktor_pre_op, t.retraktor_tambahan,
        t.l_hak_pre_op, t.l_hak_tambahan, t.jarum_lepas_pre_op, t.jarum_lepas_tambahan, t.nm_tambahan_6,
        t.tambahan_6_pre_op, t.tambahan_6_tambahan, t.nm_tambahan_7, t.tambahan_7_pre_op, t.tambahan_7_tambahan,
        t.nm_tambahan_8, t.tambahan_8_pre_op, t.tambahan_8_tambahan, k.kassa_besar_masuk_1, k.kassa_besar_masuk_2,
        k.kassa_besar_masuk_3, k.kassa_besar_masuk_4, k.kassa_besar_masuk_5, k.kassa_besar_masuk_6,
        k.kassa_besar_masuk_7, k.kassa_besar_masuk_8, k.kassa_besar_masuk_9, k.kassa_besar_masuk_10,
        k.kassa_besar_keluar_1, k.kassa_besar_keluar_2, k.kassa_besar_keluar_3, k.kassa_besar_keluar_4,
        k.kassa_besar_keluar_5, k.kassa_besar_keluar_6, k.kassa_besar_keluar_7, k.kassa_besar_keluar_8,
        k.kassa_besar_keluar_9, k.kassa_besar_keluar_10, k.deskripsi_kassa_besar, k.kassa_kecil_masuk_1,
        k.kassa_kecil_masuk_2, k.kassa_kecil_masuk_3, k.kassa_kecil_masuk_4, k.kassa_kecil_masuk_5,
        k.kassa_kecil_masuk_6, k.kassa_kecil_masuk_7, k.kassa_kecil_masuk_8, k.kassa_kecil_masuk_9,
        k.kassa_kecil_masuk_10, k.kassa_kecil_keluar_1, k.kassa_kecil_keluar_2, k.kassa_kecil_keluar_3,
        k.kassa_kecil_keluar_4, k.kassa_kecil_keluar_5, k.kassa_kecil_keluar_6, k.kassa_kecil_keluar_7,
        k.kassa_kecil_keluar_8, k.kassa_kecil_keluar_9, k.kassa_kecil_keluar_10, k.deskripsi_kassa_kecil,
        k.depper_besar_masuk_1, k.depper_besar_masuk_2, k.depper_besar_masuk_3, k.depper_besar_masuk_4,
        k.depper_besar_masuk_5, k.depper_besar_masuk_6, k.depper_besar_masuk_7, k.depper_besar_masuk_8,
        k.depper_besar_masuk_9, k.depper_besar_masuk_10, k.depper_besar_keluar_1, k.depper_besar_keluar_2,
        k.depper_besar_keluar_3, k.depper_besar_keluar_4, k.depper_besar_keluar_5, k.depper_besar_keluar_6,
        k.depper_besar_keluar_7, k.depper_besar_keluar_8, k.depper_besar_keluar_9, k.depper_besar_keluar_10,
        k.deskripsi_depper_besar, k.depper_kecil_masuk_1, k.depper_kecil_masuk_2, k.depper_kecil_masuk_3,
        k.depper_kecil_masuk_4, k.depper_kecil_masuk_5, k.depper_kecil_masuk_6, k.depper_kecil_masuk_7,
        k.depper_kecil_masuk_8, k.depper_kecil_masuk_9, k.depper_kecil_masuk_10, k.depper_kecil_keluar_1,
        k.depper_kecil_keluar_2, k.depper_kecil_keluar_3, k.depper_kecil_keluar_4, k.depper_kecil_keluar_5,
        k.depper_kecil_keluar_6, k.depper_kecil_keluar_7, k.depper_kecil_keluar_8, k.depper_kecil_keluar_9,
        k.depper_kecil_keluar_10, k.deskripsi_depper_kecil, k.nm_kassa_5, k.kassa_5_masuk_1, k.kassa_5_masuk_2,
        k.kassa_5_masuk_3, k.kassa_5_masuk_4, k.kassa_5_masuk_5, k.kassa_5_masuk_6, k.kassa_5_masuk_7,
        k.kassa_5_masuk_8, k.kassa_5_masuk_9, k.kassa_5_masuk_10, k.kassa_5_keluar_1, k.kassa_5_keluar_2,
        k.kassa_5_keluar_3, k.kassa_5_keluar_4, k.kassa_5_keluar_5, k.kassa_5_keluar_6, k.kassa_5_keluar_7,
        k.kassa_5_keluar_8, k.kassa_5_keluar_9, k.kassa_5_keluar_10, k.deskripsi_kassa_5, k.nm_kassa_6,
        k.kassa_6_masuk_1, k.kassa_6_masuk_2, k.kassa_6_masuk_3, k.kassa_6_masuk_4, k.kassa_6_masuk_5,
        k.kassa_6_masuk_6, k.kassa_6_masuk_7, k.kassa_6_masuk_8, k.kassa_6_masuk_9, k.kassa_6_masuk_10,
        k.kassa_6_keluar_1, k.kassa_6_keluar_2, k.kassa_6_keluar_3, k.kassa_6_keluar_4, k.kassa_6_keluar_5,
        k.kassa_6_keluar_6, k.kassa_6_keluar_7, k.kassa_6_keluar_8, k.kassa_6_keluar_9, k.kassa_6_keluar_10,
        k.deskripsi_kassa_6, a.alat, a.ket_alat, a.kassa, a.ket_kassa, a.xray, a.ket_xray, a.dok_operator,
        a.instrumentator, a.instrumentator_2, a.onloop'
      );
    } elseif (isset($param)) {
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->select('a.id');
      } elseif ($param == 'tabel') {
        // Tabel history
        $this->db->select(
          'a.id, a.tanggal, a.jam, master.getNamaLengkapPegawai(peng.NIP) pengisi, a.created_at, a.status'
        );
      }
    }
    $this->db->from('medis.tb_alat_operasi a');
    $this->db->join('medis.tb_alat_operasi_instrumen i', 'i.id = a.id_instrumen', 'left');
    $this->db->join('medis.tb_alat_operasi_tambahan t', 't.id = a.id_tambahan', 'left');
    $this->db->join('medis.tb_alat_operasi_kassa k', 'k.id = a.id_kassa', 'left');
    if (isset($id)) {
      // Detail history
      $this->db->where('a.id', $id);
      $query = $this->db->get();
      return $query->row_array();
    } elseif (isset($param)) {
      $this->db->join('aplikasi.pengguna peng', 'peng.ID = a.oleh');
      $this->db->where('a.nokun', $nokun);
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->where('a.status', 1);
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        // Tabel history
        return $this->db->get();
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  public function ubahInstrumen($dataInstrumen, $id)
  {
    $this->db->where('medis.tb_alat_operasi_instrumen.id', $id);
    $this->db->update('medis.tb_alat_operasi_instrumen', $dataInstrumen);
  }

  public function ubahKassa($dataKassa, $id)
  {
    $this->db->where('medis.tb_alat_operasi_kassa.id', $id);
    $this->db->update('medis.tb_alat_operasi_kassa', $dataKassa);
  }

  public function ubahTambahan($dataTambahan, $id)
  {
    $this->db->where('medis.tb_alat_operasi_tambahan.id', $id);
    $this->db->update('medis.tb_alat_operasi_tambahan', $dataTambahan);
  }

  public function ubah($data, $id)
  {
    $this->db->where('medis.tb_alat_operasi.id', $id);
    $this->db->update('medis.tb_alat_operasi', $data);
  }
}
