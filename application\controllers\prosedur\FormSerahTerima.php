<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormSerahTerima extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel'
      ]
    );
  }

  public function index()
  {
    $nomr = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    $id_srht = $this->uri->segment(7);

    if ($id_srht == '') {
      $jenisSerahShowHide = 1;
    } else {
      $jenisSerahShowHide = 2;
    }

    $data = [
      'ruangan' => 1,
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'jenisSerahShowHide' => $jenisSerahShowHide,
      'cekNopenSerahTerima' => $this->pengkajianAwalModel->cekNopenSerahTerima($nopen),
      'kondisisaatiniprosedurpratindakan' => $this->masterModel->referensi(418),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'historySerahTerima' => $this->pengkajianAwalModel->historySerahTerima($nomr),
      'kesadaran' => $this->masterModel->referensi(5),
      'risikojatuhprosedur_pasca' => $this->masterModel->referensi(447),
      'kesadaran_pasca' => $this->masterModel->referensi(446),
      'kondisisaatiniprosedurpascatindakan' => $this->masterModel->referensi(437),
      'penyakitmenular' => $this->masterModel->referensi(400),
      'suratizintindakan' => $this->masterModel->referensi(401),
      'penandaansisioperasi' => $this->masterModel->referensi(402),
      'hasilpemeriksaanpratindakan' => $this->masterModel->referensi(403),
      'hasilpemeriksaanpascatindakan' => $this->masterModel->referensi(414),
      'fotopratindakan' => $this->masterModel->referensi(404),
      'getSerahTerima' => $this->pengkajianAwalModel->getSerahTerima($id_srht),
      'ctscanpratindakan' => $this->masterModel->referensi(405),
      'usgpascatindakan' => $this->masterModel->referensi(411),
      'usgpratindakan' => $this->masterModel->referensi(406),
      'ctscanpascatindakan' => $this->masterModel->referensi(410),
      'mripratindakan' => $this->masterModel->referensi(407),
      'mripascatindakan' => $this->masterModel->referensi(412),
      'echocardiografipascatindakan' => $this->masterModel->referensi(413),
      'risikojatuhprosedur' => $this->masterModel->referensi(438),
      'implant_pra' => $this->masterModel->referensi(439),
      'implant_pasca' => $this->masterModel->referensi(448),
      'pemakaian_alat_bantu_pra' => $this->masterModel->referensi(440),
      'pemakaian_alat_bantu_pasca' => $this->masterModel->referensi(449),
      'gigi_palsu_pra' => $this->masterModel->referensi(441),
      'echocardiografipratindakan' => $this->masterModel->referensi(408),
      'gigi_palsu_pasca' => $this->masterModel->referensi(450),
      'ketersediaandarah' => $this->masterModel->referensi(442),
      'ketersediaanalatobat' => $this->masterModel->referensi(443),
      'instruksipratindakan' => $this->masterModel->referensi(444),
      'instruksipascatindakan' => $this->masterModel->referensi(445),
      'program_terapi_pra' => $this->masterModel->referensi(451),
      'program_terapi_pasca' => $this->masterModel->referensi(452),
      'fotopascatindakan' => $this->masterModel->referensi(409),
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'rencanaserahterima' => $this->masterModel->referensi(478),
      'perawat' => $this->masterModel->listPetugasSerahTerima(105090101, null),
      'pengisi' => $this->session->userdata('id')
    ];
    // echo '<pre>';print_r($data);exit();

    $this->load->view('Pengkajian/serahterima/index', $data);
  }

  public function viewInputPascaTindakan()
  {
    $idserah = $this->input->post('idserah');

    $data = [
      'idserah' => $idserah,
      'nokun' => $this->input->post('nokun'),
      'uri4' => $this->uri->segment(4),
      'uri5' => $this->uri->segment(5),
      'uri6' => $this->uri->segment(6),
      'kondisisaatiniprosedurpratindakan' => $this->masterModel->referensi(418),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'kesadaran' => $this->masterModel->referensi(5),
      'risikojatuhprosedur_pasca' => $this->masterModel->referensi(447),
      'kesadaran_pasca' => $this->masterModel->referensi(446),
      'kondisisaatiniprosedurpascatindakan' => $this->masterModel->referensi(437),
      'penyakitmenular' => $this->masterModel->referensi(400),
      'suratizintindakan' => $this->masterModel->referensi(401),
      'penandaansisioperasi' => $this->masterModel->referensi(402),
      'hasilpemeriksaanpratindakan' => $this->masterModel->referensi(403),
      'hasilpemeriksaanpascatindakan' => $this->masterModel->referensi(414),
      'fotopratindakan' => $this->masterModel->referensi(404),
      'ctscanpratindakan' => $this->masterModel->referensi(405),
      'usgpascatindakan' => $this->masterModel->referensi(411),
      'usgpratindakan' => $this->masterModel->referensi(406),
      'ctscanpascatindakan' => $this->masterModel->referensi(410),
      'mripratindakan' => $this->masterModel->referensi(407),
      'mripascatindakan' => $this->masterModel->referensi(412),
      'echocardiografipascatindakan' => $this->masterModel->referensi(413),
      'risikojatuhprosedur' => $this->masterModel->referensi(438),
      'implant_pra' => $this->masterModel->referensi(439),
      'implant_pasca' => $this->masterModel->referensi(448),
      'pemakaian_alat_bantu_pra' => $this->masterModel->referensi(440),
      'pemakaian_alat_bantu_pasca' => $this->masterModel->referensi(449),
      'gigi_palsu_pra' => $this->masterModel->referensi(441),
      'echocardiografipratindakan' => $this->masterModel->referensi(408),
      'gigi_palsu_pasca' => $this->masterModel->referensi(450),
      'ketersediaandarah' => $this->masterModel->referensi(442),
      'ketersediaanalatobat' => $this->masterModel->referensi(443),
      'instruksipratindakan' => $this->masterModel->referensi(444),
      'instruksipascatindakan' => $this->masterModel->referensi(445),
      'program_terapi_pra' => $this->masterModel->referensi(451),
      'program_terapi_pasca' => $this->masterModel->referensi(452),
      'fotopascatindakan' => $this->masterModel->referensi(409),
      'getSerahTerima' => $this->pengkajianAwalModel->getSerahTerima($idserah),
      'rencanaserahterima' => $this->masterModel->referensi(478),
      'perawat' => $this->masterModel->listPetugasSerahTerima(105090101, null),
    ];
    // echo '<pre>';print_r($data);exit();

    $this->load->view('Pengkajian/serahterima/viewInputPascaTindakan', $data);
  }

  public function inputPascaTindakan()
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $post = $this->input->post();
      $getid_serahterima = $post['id_serahterima'];

      $dataSerahTerima = [
        'id_serahterima' => $getid_serahterima,
        'nokun' => $post['nokun'] ?? null,
        'pukul_pasca_tindakan' => $post['pukul_pasca_tindakan'] ?? null,
        'tekanan_darah_1_pasca_tindakan' => $post['tekanan_darah_1_pasca'] ?? null,
        'tekanan_darah_2_pasca_tindakan' => $post['tekanan_darah_2_pasca'] ?? null,
        'napas_pasca_tindakan' => $post['pernapasan_pasca'] ?? null,
        'nadi_pasca_tindakan' => $post['nadi_pasca'] ?? null,
        'suhu_pasca_tindakan' => $post['suhu_pasca'] ?? null,
        'hasil_pemeriksaan_pasca_tindakan' => $post['hasilpemeriksaanpascatindakan'] ?? null,
        'foto_pasca_tindakan' => $post['fotopascatindakan'] ?? null,
        'nama_foto_pasca_tindakan' => $post['sebutkanfoto_pasca'] ?? null,
        'jumlah_foto_pasca_tindakan' => $post['sebutkanlembarfoto_pasca'] ?? null,
        'ctscan_pasca_tindakan' => $post['ctscanpascatindakan'] ?? null,
        'nama_ctscan_pasca_tindakan' => $post['sebutkanctscan_pasca'] ?? null,
        'jumlah_ctscan_pasca_tindakan' => $post['sebutkanlembarctscan_pasca'] ?? null,
        'usg_pasca_tindakan' => $post['usgpascatindakan'] ?? null,
        'nama_usg_pasca_tindakan' => $post['sebutkanusg_pasca'] ?? null,
        'jumlah_usg_pasca_tindakan' => $post['sebutkanlembarusg_pasca'] ?? null,
        'mri_pasca_tindakan' => $post['mripascatindakan'] ?? null,
        'nama_mri_pasca_tindakan' => $post['sebutkanmri_pasca'] ?? null,
        'jumlah_mri_pasca_tindakan' => $post['sebutkanlembarmri_pasca'] ?? null,
        'echocardiografi_pasca_tindakan' => $post['echocardiografipascatindakan'] ?? null,
        'nama_echocardiografi_pasca_tindakan' => $post['sebutkanechocardiografi_pasca'] ?? null,
        'jumlah_echocardiografi_pasca_tindakan' => $post['sebutkanlembarechocardiografi_pasca'] ?? null,
        'hasil_pemeriksaan_lainnya_pasca_tindakan' => $post['lainnya_pascatindakan'] ?? null,
        'alergi_pasca_tindakan' => $post['alergi_pasca'] ?? null,
        'isi_alergi_pasca_tindakan' => $post['sebutkanalergi_pasca'] ?? null,
        'kesadaran_pasca_tindakan' => $post['kesadaran_pasca'] ?? null,
        'risiko_jatuh_pasca_tindakan' => $post['risikojatuhprosedur_pasca'] ?? null,
        'implant_pasca_tindakan' => $post['implant_pasca'] ?? null,
        'isi_implant_pasca_tindakan' => $post['sebutkanimplant_pasca'] ?? null,
        'pemakaian_alat_bantu_pasca_tindakan' => $post['pemakaian_alat_bantu_pasca'] ?? null,
        'gigi_palsu_pasca_tindakan' => $post['gigi_palsu_pasca'] ?? null,
        'lensa_kontak_pasca_tindakan' => $post['lensa_kontak_pasca'] ?? null,
        'program_terapi_pasca_tindakan' => $post['program_terapi_pasca'] ?? null,
        'isi_program_terapi_pasca_tindakan' => $post['sebutkanprogramterapipasca'] ?? null,
        'penyerah_pasca' => $post['penyerah_pasca'] ?? null,
        'oleh_pasca' => $post['oleh_pasca'] ?? null,
        'status' => 1,
        'created_at_pasca' => date('Y-m-d H:i:s'),
      ];
      // echo '<pre>';print_r($dataSerahTerima);echo '</pre>';exit();

      $this->db->where('tb_serahterima.id_serahterima', $getid_serahterima);
      $this->db->update('keperawatan.tb_serahterima', $dataSerahTerima);

      $this->db->delete('keperawatan.tb_serahterima_kondisi_pasca', ['id_serahterima' => $getid_serahterima]);
      $datakondisipasca = [];
      $index_kondisi_pasca = 0;
      $keterangan_kondisi_pasca = [1430, 1433];
      if (isset($post['kondisisaatiniprosedurpascatindakan'])) {
        foreach ($post['kondisisaatiniprosedurpascatindakan'] as $input) {
          if ($post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca] != '') {
            $id = 'keterangan_kondisi_pasca' . $post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca];
            array_push(
              $datakondisipasca,
              [
                'id_serahterima' => $getid_serahterima,
                'id_variabel' => $post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca],
                'keterangan' => $post[$id] ?? null
              ]
            );
          }
          $index_kondisi_pasca++;
        }
        $this->db->insert_batch('keperawatan.tb_serahterima_kondisi_pasca', $datakondisipasca);
      }

      $this->db->delete('keperawatan.tb_serahterima_instruksi_pasca', ['id_serahterima' => $getid_serahterima]);
      $datainstruksipasca = [];
      $index_instruksi_pasca = 0;
      $keterangan_instruksi_pasca = [1483, 1484, 1485];
      if (isset($post['instruksipascatindakan'])) {
        foreach ($post['instruksipascatindakan'] as $input) {
          if ($post['instruksipascatindakan'][$index_instruksi_pasca] != '') {
            $id = 'keterangan_instruksi_pasca' . $post['instruksipascatindakan'][$index_instruksi_pasca];
            array_push(
              $datainstruksipasca,
              [
                'id_serahterima' => $getid_serahterima,
                'id_variabel' => $post['instruksipascatindakan'][$index_instruksi_pasca],
                'keterangan' => $post[$id] ?? null
              ]
            );
          }
          $index_instruksi_pasca++;
        }
        $this->db->insert_batch('keperawatan.tb_serahterima_instruksi_pasca', $datainstruksipasca);
      }
    }
  }

  public function action_serahterima($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);echo '</pre>';exit();
        $getid_serahterima = !empty($post['id_serahterima']) ? $post['id_serahterima'] : $this->pengkajianAwalModel->getIdEmr();
        $dataSerahTerima = [
          'id_serahterima' => $getid_serahterima,
          'nokun' => $post['nokun'] ?? null,
          'rencanaserahterima' => $post['rencanaserahterima'] ?? null,
          'rencanatindakan' => $post['sebutkanrencanatindakan'] ?? null,
          'rencanapemeriksaandiagnostik' => $post['sebutkanrencanapd'] ?? null,
          'pukul_pra_tindakan' => $post['pukul_pra_tindakan'] ?? null,
          'tekanan_darah_1_pra_tindakan' => $post['tekanan_darah_1_pra'] ?? null,
          'tekanan_darah_2_pra_tindakan' => $post['tekanan_darah_2_pra'] ?? null,
          'napas_pra_tindakan' => $post['pernapasan_pra'] ?? null,
          'nadi_pra_tindakan' => $post['nadi_pra'] ?? null,
          'suhu_pra_tindakan' => $post['suhu_pra'] ?? null,
          'pukul_pasca_tindakan' => $post['pukul_pasca_tindakan'] ?? null,
          'tekanan_darah_1_pasca_tindakan' => $post['tekanan_darah_1_pasca'] ?? null,
          'tekanan_darah_2_pasca_tindakan' => $post['tekanan_darah_2_pasca'] ?? null,
          'napas_pasca_tindakan' => $post['pernapasan_pasca'] ?? null,
          'nadi_pasca_tindakan' => $post['nadi_pasca'] ?? null,
          'suhu_pasca_tindakan' => $post['suhu_pasca'] ?? null,
          'diagnosis' => $post['diagnosis'] ?? null,
          'penyakit_menular' => $post['penyakitmenular'] ?? null,
          'isi_penyakit_menular' => $post['sebutkanpenyakitmenular'] ?? null,
          'surat_izin_tindakan' => $post['suratizintindakan'] ?? null,
          'penandaan_sisi_operasi' => $post['penandaansisioperasi'] ?? null,
          'isi_penandaan_sisi_operasi' => $post['sebutkansisioperasi'] ?? null,
          'hasil_pemeriksaan_pra_tindakan' => $post['hasilpemeriksaanpratindakan'] ?? null,
          'foto_pra_tindakan' => $post['fotopratindakan'] ?? null,
          'nama_foto_pra_tindakan' => $post['sebutkanfoto'] ?? null,
          'jumlah_foto_pra_tindakan' => $post['sebutkanlembarfoto'] ?? null,
          'ctscan_pra_tindakan' => $post['ctscanpratindakan'] ?? null,
          'nama_ctscan_pra_tindakan' => $post['sebutkanctscan'] ?? null,
          'jumlah_ctscan_pra_tindakan' => $post['sebutkanlembarctscan'] ?? null,
          'usg_pra_tindakan' => $post['usgpratindakan'] ?? null,
          'nama_usg_pra_tindakan' => $post['sebutkanusg'] ?? null,
          'jumlah_usg_pra_tindakan' => $post['sebutkanlembarusg'] ?? null,
          'mri_pra_tindakan' => $post['mripratindakan'] ?? null,
          'nama_mri_pra_tindakan' => $post['sebutkanmri'] ?? null,
          'jumlah_mri_pra_tindakan' => $post['sebutkanlembarmri'] ?? null,
          'echocardiografi_pra_tindakan' => $post['echocardiografipratindakan'] ?? null,
          'nama_echocardiografi_pra_tindakan' => $post['sebutkanechocardiografi'] ?? null,
          'jumlah_echocardiografi_pra_tindakan' => $post['sebutkanlembarechocardiografi'] ?? null,
          'hasil_pemeriksaan_lainnya_pra_tindakan' => $post['lainnya_pratindakan'] ?? null,
          'hasil_pemeriksaan_pasca_tindakan' => $post['hasilpemeriksaanpascatindakan'] ?? null,
          'foto_pasca_tindakan' => $post['fotopascatindakan'] ?? null,
          'nama_foto_pasca_tindakan' => $post['sebutkanfoto_pasca'] ?? null,
          'jumlah_foto_pasca_tindakan' => $post['sebutkanlembarfoto_pasca'] ?? null,
          'ctscan_pasca_tindakan' => $post['ctscanpascatindakan'] ?? null,
          'nama_ctscan_pasca_tindakan' => $post['sebutkanctscan_pasca'] ?? null,
          'jumlah_ctscan_pasca_tindakan' => $post['sebutkanlembarctscan_pasca'] ?? null,
          'usg_pasca_tindakan' => $post['usgpascatindakan'] ?? null,
          'nama_usg_pasca_tindakan' => $post['sebutkanusg_pasca'] ?? null,
          'jumlah_usg_pasca_tindakan' => $post['sebutkanlembarusg_pasca'] ?? null,
          'mri_pasca_tindakan' => $post['mripascatindakan'] ?? null,
          'nama_mri_pasca_tindakan' => $post['sebutkanmri_pasca'] ?? null,
          'jumlah_mri_pasca_tindakan' => $post['sebutkanlembarmri_pasca'] ?? null,
          'echocardiografi_pasca_tindakan' => $post['echocardiografipascatindakan'] ?? null,
          'nama_echocardiografi_pasca_tindakan' => $post['sebutkanechocardiografi_pasca'] ?? null,
          'jumlah_echocardiografi_pasca_tindakan' => $post['sebutkanlembarechocardiografi_pasca'] ?? null,
          'hasil_pemeriksaan_lainnya_pasca_tindakan' => $post['lainnya_pascatindakan'] ?? null,
          'alergi_pra_tindakan' => $post['alergi_pra'] ?? null,
          'isi_alergi_pra_tindakan' => $post['sebutkanalergi_pra'] ?? null,
          'alergi_pasca_tindakan' => $post['alergi_pasca'] ?? null,
          'isi_alergi_pasca_tindakan' => $post['sebutkanalergi_pasca'] ?? null,
          'kesadaran_pra_tindakan' => $post['kesadaran_pra'] ?? null,
          'kesadaran_pasca_tindakan' => $post['kesadaran_pasca'] ?? null,
          'risiko_jatuh_pra_tindakan' => $post['risikojatuhprosedur'] ?? null,
          'risiko_jatuh_pasca_tindakan' => $post['risikojatuhprosedur_pasca'] ?? null,
          'implant_pra_tindakan' => $post['implant_pra'] ?? null,
          'isi_implant_pra_tindakan' => $post['sebutkanimplant_pra'] ?? null,
          'implant_pasca_tindakan' => $post['implant_pasca'] ?? null,
          'isi_implant_pasca_tindakan' => $post['sebutkanimplant_pasca'] ?? null,
          'pemakaian_alat_bantu_pra_tindakan' => $post['pemakaian_alat_bantu_pra'] ?? null,
          'pemakaian_alat_bantu_pasca_tindakan' => $post['pemakaian_alat_bantu_pasca'] ?? null,
          'gigi_palsu_pra_tindakan' => $post['gigi_palsu_pra'] ?? null,
          'gigi_palsu_pasca_tindakan' => $post['gigi_palsu_pasca'] ?? null,
          'lensa_kontak_pra_tindakan' => $post['lensa_kontak_pra'] ?? null,
          'lensa_kontak_pasca_tindakan' => $post['lensa_kontak_pasca'] ?? null,
          'lainnya_assesment' => $post['lainnya_assesment'] ?? null,
          'ketersediaan_darah' => $post['ketersediaandarah'] ?? null,
          'isi_ketersediaan_darah' => $post['sebutkanketersediaandarah'] ?? null,
          'ketersediaan_alatobat' => $post['ketersediaanalatobat'] ?? null,
          'isi_ketersediaan_alatobat' => $post['sebutkanketersediaanalatobat'] ?? null,
          'program_terapi_pra_tindakan' => $post['program_terapi_pra'] ?? null,
          'isi_program_terapi_pra_tindakan' => $post['sebutkanprogramterapipra'] ?? null,
          'program_terapi_pasca_tindakan' => $post['program_terapi_pasca'] ?? null,
          'isi_program_terapi_pasca_tindakan' => $post['sebutkanprogramterapipasca'] ?? null,
          'penerima' => $post['penerima'] ?? null,
          'penyerah' => $post['penyerah'] ?? null,
          'status' => 1,
          'oleh' => $post['pengisi'] ?? null,
        ];

        if (!empty($post['id_serahterima'])) {
          $this->db->replace('keperawatan.tb_serahterima', $dataSerahTerima);

          $this->db->delete('keperawatan.tb_serahterima_kondisi_pra', ['id_serahterima' => $getid_serahterima]);
          $datakondisipra = [];
          $index_kondisi_pra = 0;
          $keterangan_kondisi_pra = [1395, 1398];
          if (isset($post['kondisisaatiniprosedurpratindakan'])) {
            foreach ($post['kondisisaatiniprosedurpratindakan'] as $input) {
              if ($post['kondisisaatiniprosedurpratindakan'][$index_kondisi_pra] != '') {
                $id = 'keterangan_kondisi_pra' . $post['kondisisaatiniprosedurpratindakan'][$index_kondisi_pra];
                array_push(
                  $datakondisipra,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['kondisisaatiniprosedurpratindakan'][$index_kondisi_pra],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_kondisi_pra++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_kondisi_pra', $datakondisipra);
          }

          $this->db->delete('keperawatan.tb_serahterima_kondisi_pasca', ['id_serahterima' => $getid_serahterima]);
          $datakondisipasca = [];
          $index_kondisi_pasca = 0;
          $keterangan_kondisi_pasca = [1430, 1433];
          if (isset($post['kondisisaatiniprosedurpascatindakan'])) {
            foreach ($post['kondisisaatiniprosedurpascatindakan'] as $input) {
              if ($post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca] != '') {
                $id = 'keterangan_kondisi_pasca' . $post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca];
                array_push(
                  $datakondisipasca,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_kondisi_pasca++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_kondisi_pasca', $datakondisipasca);
          }

          $this->db->delete('keperawatan.tb_serahterima_instruksi_pra', ['id_serahterima' => $getid_serahterima]);
          $datainstruksipra = [];
          $index_instruksi_pra = 0;
          $keterangan_instruksi_pra = [1476];
          if (isset($post['instruksipratindakan'])) {
            foreach ($post['instruksipratindakan'] as $input) {
              if ($post['instruksipratindakan'][$index_instruksi_pra] != '') {
                $id = 'keterangan_instruksi_pra' . $post['instruksipratindakan'][$index_instruksi_pra];
                array_push(
                  $datainstruksipra,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['instruksipratindakan'][$index_instruksi_pra],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_instruksi_pra++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_instruksi_pra', $datainstruksipra);
          }

          $this->db->delete('keperawatan.tb_serahterima_instruksi_pasca', ['id_serahterima' => $getid_serahterima]);
          $datainstruksipasca = [];
          $index_instruksi_pasca = 0;
          $keterangan_instruksi_pasca = [1483, 1484, 1485];
          if (isset($post['instruksipascatindakan'])) {
            foreach ($post['instruksipascatindakan'] as $input) {
              if ($post['instruksipascatindakan'][$index_instruksi_pasca] != '') {
                $id = 'keterangan_instruksi_pasca' . $post['instruksipascatindakan'][$index_instruksi_pasca];
                array_push(
                  $datainstruksipasca,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['instruksipascatindakan'][$index_instruksi_pasca],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_instruksi_pasca++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_instruksi_pasca', $datainstruksipasca);
          }

          $result = ['status' => 'success', 'pesan' => 'ubah'];
        } else {
          $result = ['status' => 'failed'];
          $this->db->insert('keperawatan.tb_serahterima', $dataSerahTerima);

          $datakondisipra = [];
          $index_kondisi_pra = 0;
          $keterangan_kondisi_pra = [1395, 1398];
          if (isset($post['kondisisaatiniprosedurpratindakan'])) {
            foreach ($post['kondisisaatiniprosedurpratindakan'] as $input) {
              if ($post['kondisisaatiniprosedurpratindakan'][$index_kondisi_pra] != '') {
                $id = 'keterangan_kondisi_pra' . $post['kondisisaatiniprosedurpratindakan'][$index_kondisi_pra];
                array_push(
                  $datakondisipra,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['kondisisaatiniprosedurpratindakan'][$index_kondisi_pra],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_kondisi_pra++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_kondisi_pra', $datakondisipra);
          }

          $datakondisipasca = [];
          $index_kondisi_pasca = 0;
          $keterangan_kondisi_pasca = [1430, 1433];
          if (isset($post['kondisisaatiniprosedurpascatindakan'])) {
            foreach ($post['kondisisaatiniprosedurpascatindakan'] as $input) {
              if ($post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca] != '') {
                $id = 'keterangan_kondisi_pasca' . $post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca];
                array_push(
                  $datakondisipasca,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['kondisisaatiniprosedurpascatindakan'][$index_kondisi_pasca],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_kondisi_pasca++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_kondisi_pasca', $datakondisipasca);
          }

          $datainstruksipra = [];
          $index_instruksi_pra = 0;
          $keterangan_instruksi_pra = [1476];
          if (isset($post['instruksipratindakan'])) {
            foreach ($post['instruksipratindakan'] as $input) {
              if ($post['instruksipratindakan'][$index_instruksi_pra] != '') {
                $id = 'keterangan_instruksi_pra' . $post['instruksipratindakan'][$index_instruksi_pra];
                array_push(
                  $datainstruksipra,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['instruksipratindakan'][$index_instruksi_pra],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_instruksi_pra++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_instruksi_pra', $datainstruksipra);
          }

          $datainstruksipasca = [];
          $index_instruksi_pasca = 0;
          $keterangan_instruksi_pasca = [1483, 1484, 1485];
          if (isset($post['instruksipascatindakan'])) {
            foreach ($post['instruksipascatindakan'] as $input) {
              if ($post['instruksipascatindakan'][$index_instruksi_pasca] != '') {
                $id = 'keterangan_instruksi_pasca' . $post['instruksipascatindakan'][$index_instruksi_pasca];
                array_push(
                  $datainstruksipasca,
                  [
                    'id_serahterima' => $getid_serahterima,
                    'id_variabel' => $post['instruksipascatindakan'][$index_instruksi_pasca],
                    'keterangan' => $post[$id] ?? null
                  ]
                );
              }
              $index_instruksi_pasca++;
            }
            $this->db->insert_batch('keperawatan.tb_serahterima_instruksi_pasca', $datainstruksipasca);
          }

          $result = ['status' => 'success'];
        }
        echo json_encode($result);
      }
    }
  }
}

/* End of file FormSerahTerima.php */
/* Location: ./application/controllers/prosedur/FormSerahTerima.php */