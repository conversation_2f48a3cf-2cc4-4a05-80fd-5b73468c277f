<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ViewAllPengkajian extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == FALSE) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'laporanModel',
        'masterModel',
        'pengkajianAwalModel',
        'siteMarkingModel',
        'radioterapi/Ct_simulatorModel',
        'radioterapi/simulatorInformationModel',
        'radioterapi/TreatmentDoseModel',
        'rekam_medis/MedisModel',
        'uploadRmModel'
      )
    );
  }

  public function index()
  {
    $data = array(
      'title' => 'Halaman View All Pengkajian',
      'isi' => 'Laporan/ViewAllPengkajian/cariMr',
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function dataPengkajian()
  {
    $nomr = $this->input->post('nomr');
    $dataPasien = $this->masterModel->dataDiriPasien($nomr);

    $data = array(
      'title' => 'Halaman View All Pengkajian',
      'isi' => 'Laporan/ViewAllPengkajian/index',
      'nomr' => $nomr,
      'dataPasien' => $dataPasien,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function historyLapPengkajian()
  {
    $draw = intval($this->input->POST("draw"));
    $start = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->POST('nomr');

    $listHistoryMedis = $this->pengkajianAwalModel->listHistoryPengkajianMedis($nomr);

    $data = array();
    foreach ($listHistoryMedis->result() as $lhm) {
      if ($lhm->ID_EMR_PERAWAT != '') {
        $historyPerawat = '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $lhm->ID_EMR_PERAWAT . '" class="btn btn-success btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> View Keperawatan</a>';

        if ($lhm->USIA == 1) {
          $historyPerawat = '<a href="/reports/simrskd/keperawatananak/pengkajianRJKeperawatanAnak.php?format=pdf&id=' . $lhm->ID_EMR_PERAWAT . '" class="btn btn-success btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> View Keperawatan</a>';
        }

        if ($lhm->ID_RUANGAN == '105120101') {
          $historyPerawat = '<a href="/reports/simrskd/radioterapi/pengkajian_keperawatan_radio_terapi.php?format=pdf&id=' . $lhm->ID_EMR_PERAWAT . '" class="btn btn-success btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> View Keperawatan</a>';
        }
      } else {
        $historyPerawat = '<button class="btn btn-purple btn-block btn-sm" disabled><i class="fa fa-eye"></i> View Keperawatan</button>';
      }

      if ($lhm->ID_EMR_MEDIS != '') {
        $historyMedis = '<button type="button" data-name="emr.pengkajian.pengkajian_medis" data-parameter=\'{"ID_EMR":"' . $lhm->ID_EMR_MEDIS . '"}\' class="btn btn-warning btn-block btn-sm tombolCetakan" target="_blank"><i class="fa fa-eye"></i> View Medis</button>';

        if ($lhm->USIA == 1) {
          $historyMedis = '<a href="/reports/simrskd/keperawatananak/pengkajianRJMedisAnak.php?format=pdf&id=' . $lhm->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> View Medis</a>';
        }

        if ($lhm->ID_RUANGAN == '105120101') {
          $historyMedis = '<a href="/reports/simrskd/radioterapi/pengkajian_medis_radio_terapi.php?format=pdf&id=' . $lhm->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> View Medis</a>';
        }
      } else {
        $historyMedis = '<button class="btn btn-purple btn-block btn-sm" disabled><i class="fa fa-eye"></i> View Medis</button>';
      }

      $data[] = array(
        $lhm->RUANGAN,
        date("d-m-Y H:i:s", strtotime($lhm->TANGGAL_KUNJUNGAN)),
        $lhm->NOKUN,
        $lhm->DPJP,
        $historyPerawat . '<br>' . $historyMedis,
        $lhm->USER_PERAWAT != '' ? $lhm->USER_PERAWAT : '',
        $lhm->USER_MEDIS != '' ? $lhm->USER_MEDIS : '',
      );
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $listHistoryMedis->num_rows(),
      "recordsFiltered" => $listHistoryMedis->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function historyLapCppt()
  {
    $draw = intval($this->input->POST("draw"));
    $start = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->POST('nomr');

    $history_pasien = $this->pengkajianAwalModel->historyCpptPasien($nomr);

    $data = array();
    $no = 1;
    foreach ($history_pasien->result() as $hp) {
      $cetak = '<a href="/reports/simrskd/cppt/cpptnew.php?format=pdf&id=' . $hp->IDCPPT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
      if ($hp->IDRUANGAN == 105120101) {
        $cetak = '<a href="/reports/simrskd/cppt/cpptRadioterapinew.php?format=pdf&id=' . $hp->IDCPPT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
      } elseif ($hp->IDRUANGAN == 105110101) {
        $cetak = '<a href="/reports/simrskd/cppt/cpptrehab.php?format=pdf&nokun=' . $hp->nokun . '&jenis=' . $hp->JENIS_CPPT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
      }

      $btnEdit = $hp->JENIS_CPPT == 1 ? "Edit CPPT" : " Edit CPPT Konsul";
      $data[] = array(
        $no,
        $cetak,
        date("d-m-Y H:i:s", strtotime($hp->tanggal)),
        $hp->RUANGAN,
        $hp->PROFESI,
        $hp->NAMAPEGAWAI,
        $hp->DOKTERDPJP,
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $history_pasien->num_rows(),
      "recordsFiltered" => $history_pasien->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function historyLapSummaryList()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblSummaryL = $this->pengkajianAwalModel->tblSummary($nomr);

    $data = array();
    $no = 1;
    foreach ($tblSummaryL->result() as $ts) {
      $button = $ts->jenis == 2 ? '<a href="#detailLapSLP" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $ts->id . '"><i class="fas fa-edit"></i> Edit</a>' : '<a href="#detailLapSummary" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $ts->id . '"><i class="fas fa-edit"></i> Edit</a>';

      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($ts->tanggal)),
        $ts->RUANGASAL,
        $ts->NAMADOKTER,
        $button
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblSummaryL->num_rows(),
      "recordsFiltered" => $tblSummaryL->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function historyLapKonsul()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblKonsulL = $this->laporanModel->tampilHistoryKonsul($nomr);

    $data = array();
    $no = 1;
    foreach ($tblKonsulL->result() as $tkl) {
      if ($tkl->STATUS == 0) {
        $statusKonsul = $tkl->STATUS_KONSUL;
      } elseif ($tkl->STATUS == 1) {
        $statusKonsul = $tkl->STATUS_KONSUL;
      } elseif ($tkl->STATUS == 2) {
        $statusKonsul = $tkl->STATUS_KONSUL;
      }

      if ($tkl->id_dokter_tujuan != 0) {
        $dokterTujuan = $tkl->DOKTER_TUJUAN;
      } else {
        if ($tkl->dokter_jawab != '') {
          $dokterTujuan = $tkl->DOKTER_PENJAWAB;
        } else {
          $dokterTujuan = '-';
        }
      }

      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($tkl->tanggal)),
        $tkl->DOKTER_PENGIRIM,
        $tkl->RUANGAN,
        $tkl->SMF,
        $dokterTujuan,
        $statusKonsul,
        '<button type="button" data-name="emr.konsul.konsul" data-parameter=\'{"ID":"' . $tkl->ID_KONSUL . '"}\' class="btn btn-warning btn-block btn-sm tombolCetakan" target="_blank"><i class="fa fa-eye"></i> View</button>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblKonsulL->num_rows(),
      "recordsFiltered" => $tblKonsulL->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function historyLapStatusLokalis()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblStatusLokalisL = $this->laporanModel->tampilHistoryStatusLokalis($nomr);

    $data = array();
    $no = 1;
    foreach ($tblStatusLokalisL->result() as $tslk) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($tslk->TGL_INPUT)),
        $tslk->JUDUL,
        $tslk->CATATAN,
        '<a href="#detailLapStatusLokalis" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $tslk->ID . '"><i class="fa fa-eye"></i> View</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblStatusLokalisL->num_rows(),
      "recordsFiltered" => $tblStatusLokalisL->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function detailStatusLokalis()
  {
    $id = $this->input->post('id');
    $hasilFotoLokalis = $this->pengkajianAwalModel->hasilFotoLokalis($id);
    echo '
    <div class="modal-header">
      <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . date("d-m-Y H:i:s", strtotime($hasilFotoLokalis['TGL_INPUT'])) . '</span> ]</h4>
      <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
    </div>
    <div class="modal-body">
      <div class="row form-group">
        <div class="col-lg-12">
          <label for="judulLokalis" class="col-form-label">Judul Lokalis</label>
          <input type="text" name="judul" class="form-control" placeholder="[ Judul Lokalis ]" value="' . $hasilFotoLokalis['JUDUL'] . '" readonly>
        </div>
      </div>
      <div class="row form-group">
        <div class="col-lg-6">
          <label class="col-form-label">Hasil Foto</label><br>
          <img src="data:image;base64,' . base64_encode($hasilFotoLokalis['DATA']) . '"  alt="Foto Lokalis">
        </div>
        <div class="col-lg-6">
          <label for="catatan" class="col-form-label">Catatan</label>
          <textarea class="form-control" rows="21" placeholder="[Catatan ]" name="catatan" readonly>' . $hasilFotoLokalis['CATATAN'] . '</textarea>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-warning" data-dismiss="modal"><i class="fa fa-refresh"></i> Close</button>
    </div>';
  }

  public function historyLapStatusLokalisCppt()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblStatusLokalisLCppt = $this->laporanModel->tampilHistoryStatusLokalisCppt($nomr);

    $data = array();
    $no = 1;
    foreach ($tblStatusLokalisLCppt->result() as $tslk) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($tslk->tglinput)),
        $tslk->judul,
        $tslk->catatan,
        '<a href="#detailLapStatusLokalisCppt" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $tslk->id . '"><i class="fa fa-eye"></i> View</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblStatusLokalisLCppt->num_rows(),
      "recordsFiltered" => $tblStatusLokalisLCppt->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function detailStatusLokalisCppt()
  {
    $id = $this->input->post('id');
    $hasilFotoLokalis = $this->pengkajianAwalModel->hasilFotoCpptSoap($id);
    echo '
    <div class="modal-header">
      <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . date("d-m-Y H:i:s", strtotime($hasilFotoLokalis['tglinput'])) . '</span> ]</h4>
      <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
    </div>
    <div class="modal-body">
      <div class="row form-group">
        <div class="col-lg-12">
          <label for="judulLokalis" class="col-form-label">Judul Lokalis</label>
          <input type="text" name="judul" class="form-control" placeholder="[ Judul Lokalis ]" value="' . $hasilFotoLokalis['judul'] . '" readonly>
        </div>
      </div>
      <div class="row form-group">
        <div class="col-lg-6">
          <label class="col-form-label">Hasil Foto</label><br>
          <img src="data:image;base64,' . base64_encode($hasilFotoLokalis['data']) . '"  alt="Foto Lokalis">
        </div>
        <div class="col-lg-6">
          <label for="catatan" class="col-form-label">Catatan</label>
          <textarea class="form-control" rows="21" placeholder="[Catatan ]" name="catatan" readonly>' . $hasilFotoLokalis['catatan'] . '</textarea>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-warning" data-dismiss="modal"><i class="fa fa-refresh"></i> Close</button>
    </div>';
  }

  public function historyLapPenunjangPk()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblPkL = $this->laporanModel->history_pasien_pk($nomr);

    $data = array();
    $no = 1;
    foreach ($tblPkL->result() as $tblPk) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($tblPk->TGLMASUK)),
        $tblPk->NOKUN,
        '<a href="#detailPenunjangPkL" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $tblPk->NOKUN . '"><i class="fa fa-eye"></i> View</a>'
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblPkL->num_rows(),
      "recordsFiltered" => $tblPkL->num_rows(),
      "data" => $data,
    );
    echo json_encode($output);
  }

  public function modaldetailPenunjangPkL()
  {
    $nokun = $this->input->post('id');
    $dp = $this->pengkajianAwalModel->data_pasien_pk($nokun);
    $get_tgl_sampling = $this->pengkajianAwalModel->get_tgl_sampling($nokun);
    $get_tgl_hasil = $this->pengkajianAwalModel->get_tgl_hasil($nokun);
    $hasil_lab = $this->pengkajianAwalModel->hasil_lab_pk($nokun);

    $data = array(
      'dp' => $dp,
      'get_tgl_sampling' => $get_tgl_sampling,
      'get_tgl_hasil' => $get_tgl_hasil,
      'hasil_lab' => $hasil_lab,
    );

    $this->load->view('Pengkajian/patologiKlinik/hasil_lab', $data);
  }

  public function tblSitoL()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblSitoL = $this->laporanModel->sitologi($nomr);

    $data = array();
    $no = 1;
    foreach ($tblSitoL->result() as $sito) {
      $data[] = array(
        $no,
        $sito->NOMOR_LAB,
        date("d-m-Y H:i:s", strtotime($sito->TANGGAL_ORDER)),
        '<a href="#detailPenunjangPaSito" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $sito->NOMOR_LAB . '"><i class="fa fa-eye"></i> View</a>'
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblSitoL->num_rows(),
      "recordsFiltered" => $tblSitoL->num_rows(),
      "data" => $data,
    );
    echo json_encode($output);
  }

  public function tblHistoL()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblHistoL = $this->laporanModel->histologi($nomr);

    $data = array();
    $no = 1;
    foreach ($tblHistoL->result() as $histo) {
      $data[] = array(
        $no,
        $histo->NOMOR_LAB,
        date("d-m-Y H:i:s", strtotime($histo->TANGGAL_ORDER)),
        '<a href="#detailPenunjangPaHisto" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $histo->NOMOR_LAB . '"><i class="fa fa-eye"></i> View</a>'
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblHistoL->num_rows(),
      "recordsFiltered" => $tblHistoL->num_rows(),
      "data" => $data,
    );
    echo json_encode($output);
  }

  public function tblImunoL()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblImunoL = $this->laporanModel->imuno($nomr);

    $data = array();
    $no = 1;
    foreach ($tblImunoL->result() as $imuno) {
      $data[] = array(
        $no,
        $imuno->NOMOR_IMUNO,
        date("d-m-Y H:i:s", strtotime($imuno->TANGGAL_ORDER)),
        '<a href="#detailPenunjangPaImuno" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $imuno->NOMOR_IMUNO . '"><i class="fa fa-eye"></i> View</a>'
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblImunoL->num_rows(),
      "recordsFiltered" => $tblImunoL->num_rows(),
      "data" => $data,
    );
    echo json_encode($output);
  }

  public function modal_sito()
  {
    $nolab = $this->input->POST('id');
    $sitologi = $this->pengkajianAwalModel->hasil_sito($nolab);

    $data = array(
      'sitologi' => $sitologi,
    );

    $this->load->view('Pengkajian/patologiAnatomi/hasil_sito', $data);
  }

  public function modal_histo()
  {
    $nolab = $this->input->POST('id');
    $histologi = $this->pengkajianAwalModel->hasil_histo($nolab);
    $data = array(
      'histologi' => $histologi,
    );

    $this->load->view('Pengkajian/patologiAnatomi/hasil_histo', $data);
  }

  public function modal_Imuno()
  {
    $nolab = $this->input->POST('id');

    $imunohisto = $this->pengkajianAwalModel->hasil_imunohistokimia($nolab);
    $data = array(
      'imunohisto' => $imunohisto,
    );

    $this->load->view('Pengkajian/patologiAnatomi/hasil_imunohistokimia', $data);
  }

  public function tblRadiologiL()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblRadL = $this->laporanModel->LkunRad($nomr);

    $data = array();
    $no = 1;
    foreach ($tblRadL->result() as $radL) {
      $data[] = array(
        $no,
        $radL->TINDAKAN,
        date("d-m-Y H:i:s", strtotime($radL->TANGGAL_KUNJUNGAN)),
        '<a href="#viewFotoExpertise" class="btn btn-sm btn-primary btn-block color-dark_blue" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-nomr="' . $radL->NORM . '" data-nopen="' . $radL->NOPEN . '" data-nokun="' . $radL->KUNJUNGAN . '"><i class="fa fa-eye"></i> View</a>',
        '<a href="#viewFoto" class="btn btn-sm btn-warning btn-block color-dark_blue" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-nomr="' . $radL->NORM . '"><i class="fa fa-eye"></i> View</a>',
        '<a href="#viewExpertise" class="btn btn-sm btn-success btn-block color-dark_blue" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-nokun="' . $radL->KUNJUNGAN . '"><i class="fa fa-eye"></i> View</a>',
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblRadL->num_rows(),
      "recordsFiltered" => $tblRadL->num_rows(),
      "data" => $data,
    );
    echo json_encode($output);
  }

  public function viewFotoExpertise()
  {
    $nomr = $this->input->post('nomr');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');

    $Vkun = $this->pengkajianAwalModel->Vkun($nokun);
    $q = $Vkun[0];

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'Vkun' => $Vkun,
      'q' => $q,
    );

    $this->load->view('Pengkajian/radiologi/modalViewFotoExpertise', $data);
  }

  public function detail_tindakan()
  {
    $pengguna = $this->session->userdata('id');
    $mr = $this->input->post('mr');
    $idtin = $this->input->post('idtin');
    $Dtindakan = $this->pengkajianAwalModel->Dtindakan($mr, $idtin);
    $gettindakan = $this->pengkajianAwalModel->get_tindakan($idtin);
    $hasilex = $this->pengkajianAwalModel->expertise($idtin);
    $studiID = $gettindakan['study_id_link'];

    $dok_nuklir = $this->pengkajianAwalModel->dokter_nuklir();
    $dok_radiologi = $this->pengkajianAwalModel->dokter_radiologi();

    if ($hasilex['HASIL'] != '') {
      $expertise = '1';
    } else {
      $expertise = '0';
    }

    if ($gettindakan > 0) {
      $studiID = $gettindakan['study_id_link'];
      $studi_datetime = $gettindakan['study_date_time'];
    } else {
      $studiID = $Dtindakan['study_id_link'];
      $studi_datetime = $Dtindakan['study_date_time'];
    }

    $data = array(
      'idtin' => $idtin,
      'pengguna' => $pengguna,
      'Dtindakan' => $Dtindakan,
      'gettindakan' => $gettindakan,
      'hasilex' => $hasilex,
      'studiID' => $studiID,
      'studi_datetime' => $studi_datetime,
      'dok_nuklir' => $dok_nuklir,
      'dok_radiologi' => $dok_radiologi,
      'mr' => $mr,
      'expertise' => $expertise,
    );
    $this->load->view('Pengkajian/radiologi/detailModalViewFotoExpertise', $data);
  }

  public function viewFoto()
  {
    $nomr = $this->input->post('nomr');

    $tbl_Hfoto = $this->pengkajianAwalModel->tb_hasil_foto($nomr);

    $data = array(
      'tbl_Hfoto' => $tbl_Hfoto,
      'nomr' => $nomr,
    );

    $this->load->view('Pengkajian/radiologi/modalViewFoto', $data);
  }

  public function viewExpertise()
  {
    $nokun = $this->input->post('nokun');
    $tindakan = $this->pengkajianAwalModel->Vkun($nokun);
    $nama = $tindakan[0];

    $data = array(
      'tindakan' => $tindakan,
      'nama' => $nama,
    );

    $this->load->view('Pengkajian/radiologi/modalViewExpertise', $data);
  }

  public function isiExpertise()
  {
    $id_tindakan_medis = $this->input->post('id');
    $tindakan = $this->pengkajianAwalModel->expertise($id_tindakan_medis);

    echo '<div class="row">';
    echo '<div class="col-lg-12">';
    echo '<div class="form-group">';
    echo '<label for="hasil">HASIL</label>';
    echo '<textarea name="txthasil" class="form-control" rows="12" placeholder="[ Hasil ]" readonly>' . $tindakan["HASIL"] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div><br><br>';

    echo '<div class="row">';
    echo '<div class="col-lg-6">';
    echo '<div class="form-group">';
    echo '<label for="dokter_nuklir">DOKTER SPESIALIS NUKLIR</label>';
    echo '<input type="text" class="form-control" value="' . $tindakan["DOKTER_SATU"] . '" readonly>';
    echo '</div>';
    echo '</div>';

    echo '<div class="col-lg-6">';
    echo '<div class="form-group">';
    echo '<label for="dokter_radiologi">DOKTER SPESIALIS RADIOLOGI</label>';
    echo '<input type="text" class="form-control" value="' . $tindakan["DOKTER_DUA"] . '" readonly>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    echo '<div class="row">';
    echo '<div class="col-lg-2 offset-lg-10">';
    echo '<div class="form-group">';
    echo '<a="#" class="btn btn-block btn-primary btn-sm back-modal" data="' . $tindakan["KUNJUNGAN"] . '"><i class="fa fa-refresh"></i> Back</a>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
  }

  public function historyLapPraOperasi()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblPraOperasi = $this->laporanModel->historypraoperasi($nomr);

    $data = array();
    $no = 1;
    foreach ($tblPraOperasi->result() as $tpo) {
      $data[] = array(
        $no,
        $tpo->TANGGAL_JAM_OPERASI,
        $tpo->RUANGAN,
        $tpo->DPJP,
        $tpo->USER,
        '<a href="#detailLapPraOperasi" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $tpo->NOKUN . '"><i class="fa fa-print"></i> Cetak</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblPraOperasi->num_rows(),
      "recordsFiltered" => $tblPraOperasi->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function keteranganLapPraOperasi()
  {
    $nokun = $this->input->post('nokun');
    $hasilLapPraOperasi = $this->laporanModel->hasilLapPraOperasi($nokun);

    $data = array(
      'hasilLapPraOperasi' => $hasilLapPraOperasi,
      'riwayatpenggunaanobat' => $this->masterModel->referensi(288),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'psikologis' => $this->masterModel->referensi(13),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'sosialDanEkonomiPencariNafkah' => $this->masterModel->referensi(15),
      'sosialDanEkonomiTinggalSerumah' => $this->masterModel->referensi(16),
      'kesadaran' => $this->masterModel->referensi(5),
      'persiapandarah' => $this->masterModel->referensi(324),
      'persiapanalatkhusus' => $this->masterModel->referensi(325),
    );

    $this->load->view('Laporan/ViewAllPengkajian/viewModalPraOperasi', $data);
  }

  public function historyLapOperasi()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblOperasi = $this->laporanModel->historylaporanoperasi($nomr);

    $data = array();
    $no = 1;
    foreach ($tblOperasi->result() as $top) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($top->TANGGAL_SERAHTERIMA)),
        $top->RUANGAN_KUNJUNGAN,
        $top->DPJP,
        $top->USER,
        '<a href="#detailLapOperasi" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $top->id_laporan_operasi . '"><i class="fa fa-print"></i> Cetak</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblOperasi->num_rows(),
      "recordsFiltered" => $tblOperasi->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function keteranganLapOperasi()
  {
    $id = $this->input->post('id');
    $hasilLapOperasi = $this->laporanModel->getLaporanOperasi($id);

    $data = array(
      'hasilLapOperasi' => $hasilLapOperasi,
      'listDr' => $this->masterModel->listDr(),
      'listDrAnestesi' => $this->masterModel->listDrAnestesi(),
      'jenis_anestesi' => $this->masterModel->referensi(584),
      'kategori_operasi' => $this->masterModel->referensi(578),
      'sifat_operasi' => $this->masterModel->referensi(580),
      'jenis_pembedahan' => $this->masterModel->referensi(581),
      'antibiotik_propilaksis' => $this->masterModel->referensi(582),
      'teknik_anestesi_lokal' => $this->masterModel->referensi(623),
      'respon_hipersensitivitas' => $this->masterModel->referensi(624),
      'kejadian_toksikasi' => $this->masterModel->referensi(625),
      'komplikasi' => $this->masterModel->referensi(626),
      'jumlah_kehilangan_darah' => $this->masterModel->referensi(627),
      'transfusi' => $this->masterModel->referensi(628),
      'spesimen' => $this->masterModel->referensi(629),
      'pemasangan_implan' => $this->masterModel->referensi(630),
    );

    $this->load->view('Laporan/ViewAllPengkajian/viewModalOperasi', $data);
  }

  public function tblHistorySiteMarking()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $listSiteMarking = $this->siteMarkingModel->listSiteMarking($nomr);

    $data = array();
    $no = 1;
    foreach ($listSiteMarking->result() as $lsm) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($lsm->tanggal)),
        $lsm->judul,
        $lsm->OLEH,
        '<a href="#detailLapSiteMarking" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $lsm->id . '"><i class="fa fa-eye"></i> View</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $listSiteMarking->num_rows(),
      "recordsFiltered" => $listSiteMarking->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function keteranganSm()
  {
    $id = $this->input->post('id');
    $hasilFotoSm = $this->siteMarkingModel->hasilFotoSm($id);

    echo '
    <div class="row">
      <div class="col-sm-12">
        <div class="form-group">
          <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . date("d-m-Y H:i:s", strtotime($hasilFotoSm['tanggal'])) . '</span> ]</h4>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <div class="form-group">
          <label for="judulSiteMarking">Judul Site Marking</label>
          <input type="text" class="form-control" placeholder="[ Judul Site Marking ]" value="' . $hasilFotoSm['judul'] . '" readonly>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-12">
        <div class="form-group">
          <label>Hasil Foto Site Marking</label><br>
          <img src="data:image;base64,' . base64_encode($hasilFotoSm['data']) . '" alt="Foto Site Marking">
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="form-group">
          <label for="catatan">Catatan</label>
          <textarea class="form-control" cols="15" rows="10" placeholder="[Catatan ]"  readonly>' . $hasilFotoSm['catatan'] . '</textarea>
        </div>
      </div>
    </div>';
  }

  public function tblHistoryOdontogram()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $listOdontogram = $this->laporanModel->listOdontogram($nomr);

    $data = array();
    $no = 1;
    foreach ($listOdontogram->result() as $lom) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($lom->TANGGAL_ODONTO)),
        $lom->RUANGAN_KUNJUNGAN,
        $lom->USER,
        $lom->DPJP,
        '<a href="#detailOdontogram" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $lom->NOKUN . '"><i class="fa fa-eye"></i> View</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $listOdontogram->num_rows(),
      "recordsFiltered" => $listOdontogram->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function keteranganOdontogram()
  {
    $id = $this->input->post('id');
    $hasilOdontogram = $this->laporanModel->getOdontogram($id);

    $data = array(
      'hasilOdontogram' => $hasilOdontogram,
      'occlusi' => $this->masterModel->referensi(453),
      'torus_palatinus' => $this->masterModel->referensi(454),
      'torus_mandibularis' => $this->masterModel->referensi(455),
      'palatum' => $this->masterModel->referensi(456),
      'diastema' => $this->masterModel->referensi(457),
      'gigi_anomali' => $this->masterModel->referensi(458),
      'foto' => $this->masterModel->referensi(459),
      'foto_rontgen' => $this->masterModel->referensi(460),
    );

    $this->load->view('Laporan/ViewAllPengkajian/viewModalOdontogram', $data);
  }

  // public function tblCtSimulator()
  // {
  //   $draw = intval($this->input->post("draw"));
  //   $start = intval($this->input->post("start"));
  //   $length = intval($this->input->post("length"));

  //   $nomr = $this->input->post('nomr');

  //   $listCtSimulator = $this->Ct_simulatorModel->listHistoryCtSimulator($nomr);

  //   $data = array();
  //   $no = 1;
  //   foreach ($listCtSimulator->result() as $ctSim) {
  //     $data[] = array(
  //       $no,
  //       date("d-m-Y H:i:s", strtotime($ctSim->TANGGALCTDR)),
  //       $ctSim->OLEHDR,
  //       $ctSim->OLEHRAD,
  //       $ctSim->OLEHFIS,
  //       '<a href="/reports/simrskd/ctsimulator/ctsimulator.php?format=pdf&nokun=' . $ctSim->nokun . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> View</a>'
  //     );

  //     $no++;
  //   }

  //   $output = array(
  //     "draw" => $draw,
  //     "recordsTotal" => $listCtSimulator->num_rows(),
  //     "recordsFiltered" => $listCtSimulator->num_rows(),
  //     "data" => $data
  //   );
  //   echo json_encode($output);
  // }

  public function tblCtSimulator()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $nokun = $this->input->post('nokun');
    $listCtSimulator = $this->Ct_simulatorModel->listHistoryCtSimulatorNew($nomr);

    $cekDokter = $this->Ct_simulatorModel->getIsianCtDokter($nokun)->num_rows();
    $cekRadiografer = $this->Ct_simulatorModel->getIsianCtRadiografer($nokun)->num_rows();
    $cekFisikaMedis = $this->Ct_simulatorModel->getIsianCtFisikaMedis($nokun)->num_rows();  

    $data = array();
    $no = 1;

    // if($this->session->userdata('profesi') == 11 && $historyCT['verifdokter']==0 && ($cekDokter > 0 && $cekRadiografer > 0 && $cekFisikaMedis > 0)){
    //   $btnVerif = '<a data-id='.$historyCT['dokter_id'].'" class="btn btn-success btn-sm  btn-block btnverifdokter" target="_blank"><i class="fa fa-check"></i> Verif</a>';
    // }elseif($historyCT['verifdokter']==1){
    //   $btnVerif = '<span class="text-success">Sudah diverif </span>';
    // }else{
    //   $btnVerif = '-';
    // }
    // print_r($listCtSimulator->result_array());
    foreach ($listCtSimulator->result_array() as $historyCT) {
        $profesi = $this->session->userdata('profesi');
    
        // Dokter
        $disableDr = (empty($historyCT['dokter_id']) && $profesi != 11);
        $styleDr = $disableDr ? 'pointer-events: none; opacity: 0.5;' : '';
        $textDr = $disableDr ? 'Belum Ada Form' : 'Form Dokter';
        $btnDr = '<a href="#modalCtDr" class="btn btn-purple btn-block" 
                        style="' . $styleDr . '" 
                        data-id="' . (!empty($historyCT['dokter_id']) ? $historyCT['dokter_id'] : '') . '" 
                        data-toggle="modal" 
                        data-backdrop="static" 
                        data-keyboard="false">
                            <i class="fa fa-file"></i> ' . $textDr . '
                        </a>';
    
        // Radiografer
        $disableRad = (empty($historyCT['radiografer_id']) && $profesi != 8);
        $styleRad = $disableRad ? 'pointer-events: none; opacity: 0.5;' : '';
        $textRad = $disableRad ? 'Belum Ada Form' : 'Form Radiografer';
        $btnRad = '<a href="#modalCtRad" class="btn btn-success btn-block" 
                        style="' . $styleRad . '" 
                        data-id="' . (!empty($historyCT['radiografer_id']) ? $historyCT['radiografer_id'] : '') . '" 
                        data-idctdr="' . (!empty($historyCT['dokter_id']) ? $historyCT['dokter_id'] : '') . '"
                        data-toggle="modal" 
                        data-backdrop="static" 
                        data-keyboard="false">
                            <i class="fa fa-file"></i> ' . $textRad . '
                        </a>';
    
        // Fisika Medis
        $disableFis = (empty($historyCT['fisikamedis_id']) && $profesi != 17);
        $styleFis = $disableFis ? 'pointer-events: none; opacity: 0.5;' : '';
        $textFis = $disableFis ? 'Belum Ada Form' : 'Form Fisika Medis';
        $btnFis = '<a href="#modalCtFis" class="btn btn-info btn-block" 
                        style="' . $styleFis . '" 
                        data-id="' . (!empty($historyCT['fisikamedis_id']) ? $historyCT['fisikamedis_id'] : '') . '"
                        data-idctdr="' . (!empty($historyCT['dokter_id']) ? $historyCT['dokter_id'] : '') . '" 
                        data-toggle="modal" 
                        data-backdrop="static" 
                        data-keyboard="false">
                            <i class="fa fa-file"></i> ' . $textFis . '
                        </a>';
          // Status Verifikasi
          if ($historyCT['verifdokter'] == 0) {
            if ($profesi == 11 && !empty($historyCT['radiografer_id']) && !empty($historyCT['fisikamedis_id'])) {
                // Tambahkan pengecekan apakah historyCT['oleh_id'] sama dengan session userdata('id')
                if ($historyCT['oleh_id'] == $this->session->userdata('id')) {
                    $statusVerif = '<a class="btn btn-success btn-block btnverifdokter" data-id="' . $historyCT['dokter_id'] . '"><i class="fa fa-check"></i> Verif</a>';
                } else {
                    $statusVerif = 'Belum Verifikasi Dokter';
                }
            } elseif ($profesi != 11 && !empty($historyCT['radiografer_id']) && !empty($historyCT['fisikamedis_id'])) {
                $statusVerif = 'Belum Verifikasi Dokter';
            } elseif (empty($historyCT['radiografer_id']) || empty($historyCT['fisikamedis_id'])) {
                $statusVerif = 'Form Belum Lengkap';
            }
          } elseif ($historyCT['verifdokter'] == 1) {
            $statusVerif = '<span class="text-success">Sudah diverif <b>' . $historyCT['dokter_verif'] . '</b></span><br>' . $historyCT['dokter_tglverif'];
          } else {
            $statusVerif = 'Belum Verifikasi Dokter';
          }

    
        $data[] = array(
            $no,
            $historyCT['nokun'],
            $btnDr . ' ' . $historyCT['dokter_tanggal'],
            $btnRad . ' ' . $historyCT['radiografer_tanggal'],
            $btnFis . ' ' . $historyCT['fisikamedis_tanggal'],
            '<a href="/reports/simrskd/ctsimulator/ctsimulator.php?format=pdf&nokun=' . $historyCT['nokun'] . '" class="btn btn-warning btn-sm btn-block" target="_blank"><i class="fa fa-print"></i> View</a>',
            $statusVerif
        );
        $no++;
    }
  

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listCtSimulator->num_rows(),
      "recordsFiltered" => $listCtSimulator->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function tblSimulatorInformation()
  {
    $draw = intval($this->input->POST("draw"));
    $start = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listSimulatorInformation = $this->simulatorInformationModel->historySimulatorInformation($nomr);

    $data = array();
    $no = 1;
    foreach ($listSimulatorInformation->result() as $historySi) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($historySi->TANGGALSIDR)),
        $historySi->OLEHDR,
        $historySi->OLEHRAD,
        $historySi->OLEHFIS,
        '<a href="/reports/simrskd/ctsimulator/informasictsimulator.php?format=pdf&nokun=' . $historySi->nokun . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> View</a>'
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $listSimulatorInformation->num_rows(),
      "recordsFiltered" => $listSimulatorInformation->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function tblTreatmentDose()
  {
    $draw = intval($this->input->POST("draw"));
    $start = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $tblTreatmentDose = $this->TreatmentDoseModel->tblHistoryTreatmentDose($nomr);

    $data = array();
    $no = 1;
    foreach ($tblTreatmentDose->result() as $ttd) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($ttd->tanggal)),
        $ttd->OLEHDR,
        $ttd->OLEHRAD,
        $ttd->OLEHFIS,
        '<a href="/reports/simrskd/mendus/treatmentdose.php?format=pdf&nokun=' . $ttd->nokun . '&nomr=' . $ttd->NORM . '" class="btn btn-sm btn-block btn-warning" target="_blank"><i class="fa fa-print"></i> Cetak</a>',
      );
      $no++;
    }
    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblTreatmentDose->num_rows(),
      "recordsFiltered" => $tblTreatmentDose->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function historyLapPermintaanDiRawat()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblPermintaanDiRawat = $this->laporanModel->listHistoryDirawat($nomr);

    $data = array();
    $no = 1;
    foreach ($tblPermintaanDiRawat->result() as $pdr) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($pdr->TANGGAL)),
        isset($pdr->TANGGAL_DIRAWAT) ? date("d-m-Y", strtotime($pdr->TANGGAL_DIRAWAT)) : "",
        $pdr->NOKUN,
        $pdr->OLEH,
        '<a href="#detailLapPermintaanDiRawat" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $pdr->ID . '"><i class="fa fa-eye"></i> View</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblPermintaanDiRawat->num_rows(),
      "recordsFiltered" => $tblPermintaanDiRawat->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function keteranganPermintaanDiRawat()
  {
    $id = $this->input->post('id');
    $HistoryDirawat = $this->pengkajianAwalModel->historyDetailPermintaanDirawat($id);

    $data = array(
      'HistoryDirawat' => $HistoryDirawat,
      'listkebutuhanpelayanan' => $this->masterModel->referensi(266),
      'listpku' => $this->masterModel->referensi(261),
      'listRencanaPerawatan' => $this->masterModel->referensi(258),
      'listRencana' => $this->masterModel->referensi(260),
      'listDiet' => $this->masterModel->referensi(56),
      'listKebutuhanPelayanan' => $this->masterModel->referensi(266),
      'listPerbaikanKeadaanUmum' => $this->masterModel->referensi(261),
      'listKasus' => $this->masterModel->referensi(257),
      'listJenisDiet' => $this->masterModel->referensi(57),
      'listRuangan' => $this->masterModel->listRuangan(),
      'listDrUmum' => $this->masterModel->listDrUmum()
    );

    $this->load->view('Laporan/ViewAllPengkajian/viewModalPermintaanDiRawat', $data);
  }

  public function historyLapLberkasPasien()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblLberkasPasien = $this->laporanModel->get_listUpload($nomr);

    $data = array();
    $no = 1;
    foreach ($tblLberkasPasien->result() as $tlp) {
      $data[] = array(
        $no,
        date("d-m-Y", strtotime($tlp->TANGGAL_RM)),
        $tlp->TYPE,
        $tlp->NAME,
        $tlp->BERKAS,
        '<a href="#detailLapLberkasPasien" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-id="' . $tlp->ID . '"><i class="fa fa-eye"></i> View</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblLberkasPasien->num_rows(),
      "recordsFiltered" => $tblLberkasPasien->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function getFileEmr()
  {
    $id = $this->input->post('id');
    $embed = $this->uploadRmModel->downloadFile($id);
    ?>
    <embed class="media" src="<?= base_url(); ?>bankdata/upload_emr/uploadRM/<?php echo $embed['NAME'] ?>" width="100%;" height="700px;"></embed>
    <?php
  }

  public function tbLaporanPengkajianAdmin(){
        $result = $this->MedisModel->historyPengkajian();

        $data = array();
        foreach ($result as $row){
          $tombolCetak = "";
          $action = "";
          $verif = "";
          $namaVerif = "";
          $userLogin = $this->session->userdata('status');
          $userLoginFarmasi = $this->session->userdata('profesi');

          // KONDISI UNTUK ADA PENGKAJIAN PERAWAT
          if($row -> ID_EMR_PERAWAT != null){
            if($userLogin == ""){
              $jenisPengkajianPerawat = $row -> JENIS_PENGKAJIAN_KEPERAWATAN;

              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }

              if($row -> JENIS_PENGKAJIAN_MEDIS == 1){
                $tombolCetak .= '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $row -> ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';

                if ($row -> USIA == 1) {
                  $tombolCetak .= '<a href="/reports/simrskd/keperawatananak/pengkajianRJKeperawatanAnak.php?format=pdf&id=' . $row -> ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
                }

                if ($row -> ID_RUANGAN == '105120101') {
                  $tombolCetak .= '<a href="/reports/simrskd/radioterapi/pengkajian_keperawatan_radio_terapi.php?format=pdf&id=' . $row -> ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
                }
              }

              if($jenisPengkajianPerawat == 5){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Dewasa</button>';
                $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/dewasa/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a><a href="/reports/simrskd/Rawatinap/dewasa/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr='.$row -> ID_EMR_MEDIS.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
              }elseif($jenisPengkajianPerawat == 6){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatAnak" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</button>';
              }elseif($jenisPengkajianPerawat == 7){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View PengkajianRemaja</button>';
              }elseif($jenisPengkajianPerawat == 8){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemik" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Terapi</button>';
              }elseif($jenisPengkajianPerawat == 9){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatIGD" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian IGD</button>';
                $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_AwalIgd.php?format=pdf&id='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
              }elseif($jenisPengkajianPerawat == 10){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiKritis" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Kritis</button>';
                $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/pengkajianKritisPerawat.php?format=pdf&id='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
              }elseif($jenisPengkajianPerawat == 11){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
                $action .= '<a href="#lihatPemantauanAsesmenRestrain" class="btn btn-primary btn-block btn-sm showPemantauanAsesmenRestrain" data-toggle="modal" data-idpemasres="'.$row -> ID_EMR_PERAWAT.'" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Pemantauan Restrain</a>';
                $tombolCetak .= '<a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/restrain/PengkajianRestrain.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
              }elseif($jenisPengkajianPerawat == 12){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiim" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian RIIM</button>';
              }elseif($jenisPengkajianPerawat == 13){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRira" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian RIRA</button>';
              }elseif($jenisPengkajianPerawat == 14){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Pengkajian Akhir Kehidupan</button>';
              }elseif($jenisPengkajianPerawat == 15){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiBrak" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Ri Brakhiterapi</button>';
                $tombolCetak .= '<a href="/reports/simrskd/Brakhiterapi/PengkajianRIKeperawatanBrakhiterapi.php?format=pdf&id=' . $row -> ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
              }elseif($jenisPengkajianPerawat == 16){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRadioterapiRi" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Ri Radioterapi</button>';
              }elseif($jenisPengkajianPerawat == 17){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemikRJ" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Perawat Terapi Sistemik RJ</button>';
                $tombolCetak .= '<a href="/reports/simrskd/sistemik/PengkajianSistemikRj.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
              }else{
                $tombolCetak .= '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $row -> ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
              }
            }elseif($userLogin == ""){
              $jenisPengkajianMedis = $row -> JENIS_PENGKAJIAN_MEDIS;
              $jenisPengkajianPerawat = $row -> JENIS_PENGKAJIAN_KEPERAWATAN;
              if($row -> STATUS_VERIFIKASI == 0){
                if($row -> JENIS_PENGKAJIAN_KEPERAWATAN == 9){
                  $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_AwalIgd.php?format=pdf&id='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
                }elseif($row -> JENIS_PENGKAJIAN_KEPERAWATAN == 10){
                  $verif = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat-pasien-kritis" data-id="'.$row -> ID_EMR_PERAWAT.'">Verif</button>';
                }elseif($row -> JENIS_PENGKAJIAN_KEPERAWATAN == 15){
                  $verif = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat-pasien-brakhiterapi" data-id="'.$row -> ID_EMR_PERAWAT.'">Verif</button>';
                }else{
                  $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
                }
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }

              if($jenisPengkajianPerawat == 5){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Dewasaa</button>';
                $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/dewasa/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr=' . $row -> ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
              }elseif($jenisPengkajianPerawat == 6){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatAnak" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</button>';
              }elseif($jenisPengkajianPerawat == 7){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Remaja</button>';
              }elseif($jenisPengkajianPerawat == 8){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemik" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Terapi</button>';
              }elseif($jenisPengkajianPerawat == 9){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatIGD" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian IGD</button>';
              }elseif($jenisPengkajianPerawat == 10){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat-pasien-kritis" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Kritis</button>';
                $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/PengkajianKritisPerawat.php?format=pdf&id='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
              }elseif($jenisPengkajianPerawat == 11){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
                $action .= '<a href="#lihatPemantauanAsesmenRestrain" class="btn btn-primary btn-block btn-sm showPemantauanAsesmenRestrain" data-toggle="modal" data-idpemasres="'.$row -> ID_EMR_PERAWAT.'" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Pemantauan Restrain</a>';
                $tombolCetak .= '<a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/restrain/PengkajianRestrain.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
              }elseif($jenisPengkajianPerawat == 12){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiim" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian RIIM</button>';
              }elseif($jenisPengkajianPerawat == 13){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRira" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian RIRA</button>';
              }elseif($jenisPengkajianPerawat == 14){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Pengkajian Akhir Kehidupan</button>';
              }elseif($jenisPengkajianPerawat == 15){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat-pasien-brakhiterapi" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Brakhiterapi</button>';
              }elseif($jenisPengkajianPerawat == 17){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemikRJ" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian Perawat Terapi Sistemik RJ</button>';
                $tombolCetak .= '<a href="/reports/simrskd/sistemik/PengkajianSistemikRj.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
              }else{
                $tombolCetak .= '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $row -> ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
              }
            }

          }

          // KONDISI UNTUK ADA PENGKAJIAN MEDIS
          if($row -> ID_EMR_MEDIS != null){

            // CETAK RAWAT JALAN
            if($row -> JENIS_PENGKAJIAN_MEDIS == 1){

              if ($row -> USIA == 1) {
                $tombolCetak .= '<a href="/reports/simrskd/keperawatananak/pengkajianRJMedisAnak.php?format=pdf&id=' . $row -> ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
              }else{
                $tombolCetak .= '<button type="button" data-name="emr.pengkajian.pengkajian_medis" data-parameter=\'{"ID_EMR":"' . $row->ID_EMR_MEDIS . '"}\' class="btn btn-warning btn-block btn-sm tombolCetakan" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></button>';
              }

              if ($row -> ID_RUANGAN == '105120101') {
                $tombolCetak .= '<a href="/reports/simrskd/radioterapi/pengkajian_medis_radio_terapi.php?format=pdf&id=' . $row -> ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
              }
            }

            if($userLogin == ""){
              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
              
              if($row -> JENIS_PENGKAJIAN_MEDIS != 1){
                if($row -> JENIS_PENGKAJIAN_MEDIS == 9){
                  $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOKUN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis IGD</button>';
                  $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_medis_igd.php?format=pdf&id=' . $row -> ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
                }elseif($row -> JENIS_PENGKAJIAN_MEDIS == 10){
                  $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisKritis" data-id="'.$row -> NOKUN.'" data-idemr="'.$row -> ID_EMR_MEDIS.'" data-status="1"><i class="fa fa-eye"></i>  View Medis Kritis</button>';
                  $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/PengkajianKritisMedis.php?format=pdf&id='.$row -> ID_EMR_MEDIS.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
                }elseif($row -> JENIS_PENGKAJIAN_MEDIS == 15){
                  $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisBrakhiterapi" data-id="'.$row -> NOKUN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
                  $tombolCetak .= '<a href="/reports/simrskd/Brakhiterapi/MedisRIKeperawatanBrakhiterapi.php?format=pdf&id=' . $row -> ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
                }elseif($row -> JENIS_PENGKAJIAN_MEDIS == 14){
                  $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPAKMedis" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis Pengkajian Akhir Kehidupan</button>';
                }else{
                  $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOKUN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
                  $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/medis/PengkajianRIMedisDewasa.php?format=pdf&id=' . $row -> ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
                }
              }
            }elseif($userLogin == "" || $userLoginFarmasi == 5){
              if($row -> STATUS_VERIFIKASI == 0){
                if($row -> ID_EMR_PERAWAT != null){
                  $verif = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'">Verif</button>';
                }else{
                  $verif = $row -> INFO_VERIFIKASI;
                }
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
              
              if($row -> JENIS_PENGKAJIAN_MEDIS == 5){
                $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOKUN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis Dewasa</button>';
              }elseif($row -> JENIS_PENGKAJIAN_MEDIS == 9){
                $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_medis_igd.php?format=pdf&id=' . $row -> ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
              }elseif($row -> JENIS_PENGKAJIAN_MEDIS == 10){
                $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisKritis" data-id="'.$row -> NOKUN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
                $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/PengkajianKritisMedis.php?format=pdf&id='.$row -> ID_EMR_MEDIS.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
              }elseif($row -> JENIS_PENGKAJIAN_MEDIS == 15){
                $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisBrakhiterapi" data-id="'.$row -> NOKUN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
              }elseif($row -> JENIS_PENGKAJIAN_MEDIS == 14){
                $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPAKMedis" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis Pengkajian Akhir Kehidupan</button>';
              }else{
                $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOKUN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
              }
            }
          }

          if($row->TANGGAL_PENGKAJIAN_MEDIS != null) {
            $userMedisTgl = $row -> USER_MEDIS." (".date('d M Y H:i:s', strtotime($row->TANGGAL_PENGKAJIAN_MEDIS)).")";
          } else {
            $userMedisTgl = "";
          }

          if($row->TANGGAL_PENGKAJIAN_PERAWAT != null) {
            $userPerawatTgl = $row -> USER_PERAWAT." (".date('d M Y H:i:s', strtotime($row->TANGGAL_PENGKAJIAN_PERAWAT)).")";
          } else {
            $userPerawatTgl = "";
          }


          $sub_array = array();
          $sub_array[] = $row -> INFO;
          $sub_array[] = $verif;
          $sub_array[] = $row -> RUANGAN;
          $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
          $sub_array[] = $row -> DPJP;
          $sub_array[] = $namaVerif;
          $sub_array[] = $action;
          $sub_array[] = $tombolCetak;
          $sub_array[] = $userMedisTgl;
          $sub_array[] = $userPerawatTgl;
          $data[] = $sub_array;
        }

        $output = array(
            "draw" => intval($this->input->post("draw")),
            "data"              => $data
        );
        echo json_encode($output);
    }

}

/* End of file ViewAllPengkajian.php */
/* Location: ./application/controllers/laporan/ViewAllPengkajian.php */