<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>mnesa extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
        redirect('login');
    }

    $this->load->model(array('rekam_medis/AnamnesaModel'));
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        if($param == 'ambil'){
            $post = $this->input->post(NULL,TRUE);
            $dataAnamnesa = $this->AnamnesaModel->get($post['id'], true);

            echo json_encode(array(
            'status' => 'success',
            'data'   => $dataAnamnesa
            ));
        }
    }
  }
  public function actionPerawat($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        if($param == 'ambil'){
            $post = $this->input->post(NULL,TRUE);
            $id = $post['id'];
            $dataAnamnesa = $this->AnamnesaModel->getPerawat($id, true);

            echo json_encode(array(
            'status' => 'success',
            'data'   => $dataAnamnesa
            ));
        }
    }
  }

}
