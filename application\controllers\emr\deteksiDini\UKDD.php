<?php
defined('BASEPATH') or exit('No direct script access allowed');

class UKDD extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/TbBbModel',
        'emr/deteksiDini/UKDDModel',
        'emr/deteksiDini/KankerPayudaraModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
      )
    );
  }

  public function index()
  {
    $id = $this->input->post('id');
    $namaJenisPengguna = null;
    $khususDokter = null;
    $ariaDokter = null;
    $khususPerawat = null;
    $nokun = $this->uri->segment(7);

    // <PERSON><PERSON> jenis pengguna
    $jenisPengguna = $this->session->userdata('status');
    if ($jenisPengguna == 1) {
      $namaJenisPengguna = 'Dokter';
      $khususDokter = null;
      $ariaDokter = null;
      $khususPerawat = 'disabled';
    } elseif ($jenisPengguna == 2) {
      $namaJenisPengguna = 'Perawat';
      $khususDokter = 'disabled';
      $ariaDokter = "aria-disabled= 'true'";
      $khususPerawat = null;
    } else {
      $khususDokter = 'disabled';
      $ariaDokter = "aria-disabled= 'true'";
      $khususPerawat = 'disabled';
    }
    // Akhir jenis pengguna

    $data = [
      'jenisPengguna' => $jenisPengguna,
      'khususDokter' => $khususDokter,
      'ariaDokter' => $ariaDokter,
      'khususPerawat' => $khususPerawat,
      'idPengguna' => $this->session->userdata('id'),

      // Mulai riwayat penyakit keluarga
      'diabetesMellitus' => $this->masterModel->referensi(290),
      'hipertensi' => $this->masterModel->referensi(291),
      'jantung' => $this->masterModel->referensi(292),
      'merokok' => $this->masterModel->referensi(293),
      // Akhir riwayat penyakit keluarga

      // Mulai faktor risiko atau gejala kanker
      'perokokPasif' => $this->masterModel->referensi(294),
      'sukaAlkohol' => $this->masterModel->referensi(295),
      'riwayatHepatitis' => $this->masterModel->referensi(296),
      'riwayatHepatitisKeluarga' => $this->masterModel->referensi(297),
      'pernahOperasi' => $this->masterModel->referensi(298),
      'pernahTransfusi' => $this->masterModel->referensi(299),
      'penyalahgunaanObat' => $this->masterModel->referensi(300),
      'makanDaging' => $this->masterModel->referensi(301),
      'makanDiasap' => $this->masterModel->referensi(302),
      'polipUsus' => $this->masterModel->referensi(303),
      'infeksiUsus' => $this->masterModel->referensi(304),
      'BABBerubah' => $this->masterModel->referensi(305),
      'BABBerdarah' => $this->masterModel->referensi(306),
      'riwayatKankerKeluarga' => $this->masterModel->referensi(307),
      'HPHT' => $this->masterModel->referensi(1787),
      'karsinogen' => $this->masterModel->referensi(1822),
      'polusi' => $this->masterModel->referensi(1823),
      'rumahSehat' => $this->masterModel->referensi(1824),
      'overweight' => $this->masterModel->referensi(1825),
      'aktifitasFisik' => $this->masterModel->referensi(1826),
      'dietRendah' => $this->masterModel->referensi(1827),
      'sindromametabolik' => $this->masterModel->referensi(1828),
      // Akhir faktor risiko atau gejala kanker

      // Mulai khusus wanita
      'kawin' => $this->masterModel->referensi(308),
      'melahirkan' => $this->masterModel->referensi(309),
      'pernahMenyusui' => $this->masterModel->referensi(310),
      'kontrasepsiHormonal' => $this->masterModel->referensi(311),
      'hormonalLain' => $this->masterModel->referensi(312),
      'haid' => $this->masterModel->referensi(313),
      'menopause' => $this->masterModel->referensi(314),
      'keluhanVagina' => $this->masterModel->referensi(315),
      'keputihan' => $this->masterModel->referensi(363),
      'pendarahanSpontan' => $this->masterModel->referensi(364),
      'pendarahanSenggama' => $this->masterModel->referensi(316),
      'papsSmear' => $this->masterModel->referensi(317),
      // Akhir khusus wanita

      'rencanaPemeriksaanPenunjang' => $this->masterModel->referensi(1795),

      // Mulai faktor risiko
      'jenkelWanita' => $this->masterModel->referensi(1417),
      'lebih50' => $this->masterModel->referensi(1418),
      'kankerPayudaraKelTingkat1' => $this->masterModel->referensi(1419),
      'kankerPayudaraKelTingkat2' => $this->masterModel->referensi(1428),
      'radioterapiPanjang' => $this->masterModel->referensi(1420),
      'tumorPayudaraJinak' => $this->masterModel->referensi(1421),
      'kankerOvarium' => $this->masterModel->referensi(1422),
      'menarcheDini' => $this->masterModel->referensi(1423),
      'menopauseTerlambat' => $this->masterModel->referensi(1424),
      'menikah' => $this->masterModel->referensi(1785),
      'memilikiAnak' => $this->masterModel->referensi(1425),
      'melahirkan30' => $this->masterModel->referensi(1426),
      'riwayatMenyusui' => $this->masterModel->referensi(1427),
      'obatHormonal' => $this->masterModel->referensi(1429),
      // Akhir faktor risiko

      // Mulai gejala
      'menopauseGejala' => $this->masterModel->referensi(1440),
      'menstruasi' => $this->masterModel->referensi(1441),
      'menyusui' => $this->masterModel->referensi(1442),
      'benjolanPayudara' => $this->masterModel->referensi(1408),
      'nyeriPayudara' => $this->masterModel->referensi(1443),
      'bengkakPayudara' => $this->masterModel->referensi(1444),
      'benjolanKetiak' => $this->masterModel->referensi(1409),
      'benjolanLeher' => $this->masterModel->referensi(1445),
      'lukaPayudara' => $this->masterModel->referensi(1410),
      'putingTertarik' => $this->masterModel->referensi(1786),
      'kulitJeruk' => $this->masterModel->referensi(1411),
      'lesungPayudara' => $this->masterModel->referensi(1412),
      'keluarCairan' => $this->masterModel->referensi(1413),
      'nyeriTulang' => $this->masterModel->referensi(1414),
      'batukSesakNapas' => $this->masterModel->referensi(1415),
      'mualMuntah' => $this->masterModel->referensi(1416),
      // Akhir gejala

      // Mulai pemeriksaan fisik khusus
      'simetris' => $this->masterModel->referensi(1431),
      'benjolanTerlihat' => $this->masterModel->referensi(1788),
      'lokasi' => $this->masterModel->referensi(1789),
      'kulit' => $this->masterModel->referensi(1433),
      'areolaPapilla' => $this->masterModel->referensi(1434),
      'benjolanTeraba' => $this->masterModel->referensi(1435),
      'permukaan' => $this->masterModel->referensi(1790),
      'konsistensi' => $this->masterModel->referensi(1791),
      'batas' => $this->masterModel->referensi(1792),
      'mobilisasi' => $this->masterModel->referensi(1793),
      'nyeriTekan' => $this->masterModel->referensi(1794),
      'kgbAksilaTeraba' => $this->masterModel->referensi(1436),
      'kgbAksilaSoliterMultipel' => $this->masterModel->referensi(1437),
      'kgbSupraklavikulaTeraba' => $this->masterModel->referensi(1447),
      'kgbSupraklavikulaSoliterMultipel' => $this->masterModel->referensi(1448),
      // Akhir pemeriksaan fisik khusus

      // Mulai pemeriksaan fisik umum
      'keadaanUmum' => $this->masterModel->referensi(320),
      'keadaanGizi' => $this->masterModel->referensi(321),
      'habitus' => $this->masterModel->referensi(322),
      'tandaVitalCppt' => $this->pengkajianAwalModel->tandaVitalCppt($nokun),
      // Akhir pemeriksaan fisik umum
    ];

    if (isset($id)) {
      $detail = $this->UKDDModel->detail($id);
      $data['detail'] = $detail;
      $data['pasien'] = $this->pengkajianAwalModel->getNomr($detail['nokun']);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/emr/deteksiDini/UKDD/detail', $data);
    } else {
      $nomr = $this->uri->segment(5);
      $nokun = $this->uri->segment(7);
      $data['nokun'] = $nokun;
      $data['jumlah'] = $this->UKDDModel->jumlah($nomr);
      $data['pasien'] = $this->pengkajianAwalModel->getNomr($nokun);
      $data['namaPengguna'] = $this->session->userdata('nama');
      $data['namaJenisPengguna'] = $namaJenisPengguna;
      if ($jenisPengguna == 1) {
        $data['cekTerbaru'] = $this->UKDDModel->cekTerbaru($nokun);
      }
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/emr/deteksiDini/UKDD/index', $data);
    }
  }

  public function simpan()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->UKDDModel->rules;
      $this->form_validation->set_rules($rules);

      if ($this->form_validation->run() == true) {
        $nokun = $post['nokun'] ?? null;
        $nomr = $post['nomr'] ?? null;
        $tanggal = $post['tanggal'] ?? null;
        $waktu = $post['waktu'] ?? null;
        $tb = isset($post['tb']) ? round($post['tb'], 2) : 0;
        $bb = isset($post['bb']) ? round($post['bb'], 2) : 0;
        $oleh = $this->session->userdata['id'];
        $status = 1;
        $dataSource = 23;

        // Mulai data uji kesehatan deteksi dini
        $dataUKDD = array(
          'nokun' => $nokun,
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'keluhan' => $post['keluhan'] ?? null,
          'riwayat_penyakit' => $post['riwayat_penyakit'] ?? null,
          'diabetes_mellitus' => $post['diabetes_mellitus'] ?? null,
          'pen_diabetes_mellitus' => $post['pen_diabetes_mellitus'] ?? null,
          'hipertensi' => $post['hipertensi'] ?? null,
          'pen_hipertensi' => $post['pen_hipertensi'] ?? null,
          'penyakit_jantung' => $post['penyakit_jantung'] ?? null,
          'pen_jantung' => $post['pen_jantung'] ?? null,
          'merokok' => $post['merokok'] ?? null,
          'lama_merokok' => $post['lama_merokok'] ?? null,
          'mulai_merokok' => $post['mulai_merokok'] ?? null,
          'sampai_merokok' => $post['sampai_merokok'] ?? null,
          'perokok_pasif' => $post['perokok_pasif'] ?? null,
          'karsinogen' => $post['karsinogen'] ?? null,
          'polusi' => $post['polusi'] ?? null,
          'rumah_sehat' => $post['rumah_sehat'] ?? null,
          'suka_alkohol' => $post['suka_alkohol'] ?? null,
          'riwayat_hepatitis' => $post['riwayat_hepatitis'] ?? null,
          'riwayat_hepatitis_keluarga' => $post['riwayat_hepatitis_keluarga'] ?? null,
          'pen_hepatitis' => $post['pen_hepatitis'] ?? null,
          'pernah_operasi' => $post['pernah_operasi'] ?? null,
          'ket_pernah_operasi' => $post['ket_pernah_operasi'] ?? null,
          'pernah_transfusi' => $post['pernah_transfusi'] ?? null,
          'penyalahgunaan_obat' => $post['penyalahgunaan_obat'] ?? null,
          'overweight' => $post['overweight'] ?? null,
          'aktifitas_fisik' => $post['aktifitas_fisik'] ?? null,
          'diet_rendah' => $post['diet_rendah'] ?? null,
          'sindroma_metabolik' => $post['sindroma_metabolik'] ?? null,
          'makan_daging' => $post['makan_daging'] ?? null,
          'makan_diasap' => $post['makan_diasap'] ?? null,
          'polip_usus' => $post['polip_usus'] ?? null,
          'infeksi_usus' => $post['infeksi_usus'] ?? null,
          'bab_berubah' => $post['bab_berubah'] ?? null,
          'lama_bab_berubah' => $post['lama_bab_berubah'] ?? null,
          'bab_berdarah' => $post['bab_berdarah'] ?? null,
          'riwayat_kanker_keluarga' => $post['riwayat_kanker_keluarga'] ?? null,
          'pen_kanker' => $post['pen_kanker'] ?? null,
          'hpht' => $post['hpht'] ?? null,
          'hari_hpht' => $post['hari_hpht'] ?? null,
          'jenis_hpht' => $post['jenis_hpht'] ?? null,
          'kawin' => $post['kawin'] ?? null,
          'usia_menikah' => $post['usia_menikah'] ?? null,
          'keluhan_vagina' => $post['keluhan_vagina'] ?? null,
          'keputihan' => $post['keputihan'] ?? null,
          'pendarahan_spontan' => $post['pendarahan_spontan'] ?? null,
          'pendarahan_senggama' => $post['pendarahan_senggama'] ?? null,
          'paps_smear_tidak_normal' => $post['paps_smear_tidak_normal'] ?? null,
          'vagina_touch_check' => $post['vagina_touch_check'] ?? null,
          'vagina_touch_text' => $post['vagina_touch_text'] ?? null,
          'colok_dubur_check' => $post['colok_dubur_check'] ?? null,
          'colok_dubur_text' => $post['colok_dubur_text'] ?? null,
          'rpp' => isset($post['rpp']) ? json_encode($post['rpp']) : null,
          'ket_rpp' => $post['ket_rpp'] ?? null,
          'keadaan_umum' => $post['keadaan_umum'] ?? null,
          'keadaan_gizi' => $post['keadaan_gizi'] ?? null,
          'habitus' => $post['habitus'] ?? null,
          'tiroid' => $post['tiroid'] ?? null,
          'getah_bening' => $post['getah_bening'] ?? null,
          'jantung' => $post['jantung'] ?? null,
          'paru' => $post['paru'] ?? null,
          'inspeksi' => $post['inspeksi'] ?? null,
          'perut_palpasi' => $post['perut_palpasi'] ?? null,
          'perkusi' => $post['perkusi'] ?? null,
          'auskultasi' => $post['auskultasi'] ?? null,
          'reflek_fisiologis' => $post['reflek_fisiologis'] ?? null,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataUKDD);exit();
        $idUKDD = $this->UKDDModel->simpan($dataUKDD);
        // Akhir data uji kesehatan deteksi dini

        // Mulai data kanker payudara
        $dataKP = array(
          'id_ukdd' => $idUKDD,
          'nokun' => $nokun,
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'risk_1' => $post['risk_1'] ?? null,
          'risk_2' => $post['risk_2'] ?? null,
          'risk_3' => $post['risk_3'] ?? null,
          'risk_4' => $post['risk_4'] ?? null,
          'risk_5' => $post['risk_5'] ?? null,
          'risk_6' => $post['risk_6'] ?? null,
          'risk_7' => $post['risk_7'] ?? null,
          'risk_8' => $post['risk_8'] ?? null,
          'risk_9' => $post['risk_9'] ?? null,
          'risk_10' => $post['risk_10'] ?? null,
          'risk_11' => $post['risk_11'] ?? null,
          'risk_12' => $post['risk_12'] ?? null,
          'risk_13' => $post['risk_13'] ?? null,
          'risk_14' => $post['risk_14'] ?? null,
          'risk_15' => $post['risk_15'] ?? null,
          'ket_risk_15' => $post['ket_risk_15'] ?? null,
          'risk_16' => $post['risk_16'] ?? null,
          'nilai_risk' => $post['nilai_risk'] ?? null,
          'gejala_1' => $post['gejala_1'] ?? null,
          'gejala_2' => $post['gejala_2'] ?? null,
          'gejala_3' => $post['gejala_3'] ?? null,
          'gejala_4' => $post['gejala_4'] ?? null,
          'gejala_5' => $post['gejala_5'] ?? null,
          'gejala_6' => $post['gejala_6'] ?? null,
          'gejala_7' => $post['gejala_7'] ?? null,
          'gejala_8' => $post['gejala_8'] ?? null,
          'gejala_9' => $post['gejala_9'] ?? null,
          'gejala_10' => $post['gejala_10'] ?? null,
          'gejala_11' => $post['gejala_11'] ?? null,
          'gejala_12' => $post['gejala_12'] ?? null,
          'gejala_13' => $post['gejala_13'] ?? null,
          'gejala_14' => $post['gejala_14'] ?? null,
          'gejala_15' => $post['gejala_15'] ?? null,
          'gejala_16' => $post['gejala_16'] ?? null,
          'gejala_17' => $post['gejala_17'] ?? null,
          'gejala_18' => $post['gejala_18'] ?? null,
          'imt' => $tb != 0 || $bb != 0 ? round(($bb / ($tb / 100 * $tb / 100)), 2) : null,

          // Mulai bagian dokter
          'payudara_text' => $post['payudara_text'] ?? null,
          // 'simetris' => $post['simetris'] ?? null,
          // 'benjolan_terlihat' => $post['benjolan_terlihat'] ?? null,
          // 'lokasi_terlihat' => $post['lokasi_terlihat'] ?? null,
          // 'arah_terlihat' => $post['arah_terlihat'] ?? null,
          // 'kulit' => $post['kulit'] ?? null,
          // 'areola_papilla' => $post['areola_papilla'] ?? null,
          // 'benjolan_teraba' => $post['benjolan_teraba'] ?? null,
          // 'lokasi_teraba' => $post['lokasi_teraba'] ?? null,
          // 'arah_teraba' => $post['arah_teraba'] ?? null,
          // 'jumlah_benjolan_teraba' => $post['jumlah_benjolan_teraba'] ?? null,
          // 'panjang_benjolan' => $post['panjang_benjolan'] ?? null,
          // 'lebar_benjolan' => $post['lebar_benjolan'] ?? null,
          // 'tinggi_benjolan' => $post['tinggi_benjolan'] ?? null,
          // 'permukaan' => $post['permukaan'] ?? null,
          // 'konsistensi' => $post['konsistensi'] ?? null,
          // 'batas' => $post['batas'] ?? null,
          // 'mobilisasi' => $post['mobilisasi'] ?? null,
          // 'nyeri_tekan' => $post['nyeri_tekan'] ?? null,
          // 'kgb_aksila_teraba' => $post['kgb_aksila_teraba'] ?? null,
          // 'kgb_aksila_soliter_multipel' => $post['kgb_aksila_soliter_multipel'] ?? null,
          // 'kgb_supraklavikula_teraba' => $post['kgb_supraklavikula_teraba'] ?? null,
          // 'kgb_supraklavikula_soliter_multipel' => $post['kgb_supraklavikula_soliter_multipel'] ?? null,
          // Akhir bagian dokter

          'status' => $status,
        );

        // Mulai data pengisi
        if ($post['jenis_pengguna'] == 1) {
          $dataKP['dokter'] = $oleh;
        } elseif ($post['jenis_pengguna'] == 2) {
          $dataKP['perawat'] = $oleh;
        }
        // Akhir data pengisi

        // echo '<pre>';print_r($dataKP);exit();
        $this->KankerPayudaraModel->simpan($dataKP);
        // Akhir data kanker payudara

        // Mulai tinggi dan berat badan
        $dataTBB = array(
          'data_source' => $dataSource,
          'ref' => $idUKDD,
          'nomr' => $nomr,
          'nokun' => $nokun,
          'tb' => $tb,
          'bb' => $bb,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataTBB);exit();
        $this->TbBbModel->insert($dataTBB);
        // Akhir tinggi dan berat badan

        // Mulai tanda vital
        $dataTV = array(
          'data_source' => $dataSource,
          'ref' => $idUKDD,
          'nomr' => $nomr,
          'nokun' => $nokun,
          'td_sistolik' => $post['td_sistolik'] ?? null,
          'td_diastolik' => $post['td_diastolik'] ?? null,
          'nadi' => $post['nadi'] ?? null,
          'pernapasan' => $post['pernapasan'] ?? null,
          'suhu' => $post['suhu'] ?? null,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataTV);exit();
        $this->OTKeperawatanModel->simpanTandaVital($dataTV);
        // Akhir tanda vital

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array(
      'nomr' => $post['nomr'],
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/emr/deteksiDini/UKDD/history', $data);
  }

  public function historyUKDD()
  {
    $post = $this->input->post();
    $id = $post['id'];
    $namaJenisPengguna = null;
    $khususDokter = null;
    $ariaDokter = null;
    $khususPerawat = null;

    // Mulai jenis pengguna
    $jenisPengguna = $this->session->userdata('status');
    if ($jenisPengguna == 1) {
      $namaJenisPengguna = 'Dokter';
      $khususDokter = null;
      $ariaDokter = null;
      $khususPerawat = 'disabled';
    } elseif ($jenisPengguna == 2) {
      $namaJenisPengguna = 'Perawat';
      $khususDokter = 'disabled';
      $ariaDokter = "aria-disabled= 'true'";
      $khususPerawat = null;
    } else {
      $khususDokter = 'disabled';
      $ariaDokter = "aria-disabled= 'true'";
      $khususPerawat = 'disabled';
    }
    // Akhir jenis pengguna

    $data = [
      'jenisPengguna' => $jenisPengguna,
      'khususDokter' => $khususDokter,
      'ariaDokter' => $ariaDokter,
      'khususPerawat' => $khususPerawat,
      'idPengguna' => $this->session->userdata('id'),

      // Mulai riwayat penyakit keluarga
      'diabetesMellitus' => $this->masterModel->referensi(290),
      'hipertensi' => $this->masterModel->referensi(291),
      'jantung' => $this->masterModel->referensi(292),
      'merokok' => $this->masterModel->referensi(293),
      // Akhir riwayat penyakit keluarga

      // Mulai faktor risiko atau gejala kanker
      'perokokPasif' => $this->masterModel->referensi(294),
      'sukaAlkohol' => $this->masterModel->referensi(295),
      'riwayatHepatitis' => $this->masterModel->referensi(296),
      'riwayatHepatitisKeluarga' => $this->masterModel->referensi(297),
      'pernahOperasi' => $this->masterModel->referensi(298),
      'pernahTransfusi' => $this->masterModel->referensi(299),
      'penyalahgunaanObat' => $this->masterModel->referensi(300),
      'makanDaging' => $this->masterModel->referensi(301),
      'makanDiasap' => $this->masterModel->referensi(302),
      'polipUsus' => $this->masterModel->referensi(303),
      'infeksiUsus' => $this->masterModel->referensi(304),
      'BABBerubah' => $this->masterModel->referensi(305),
      'BABBerdarah' => $this->masterModel->referensi(306),
      'riwayatKankerKeluarga' => $this->masterModel->referensi(307),
      'HPHT' => $this->masterModel->referensi(1787),
      'karsinogen' => $this->masterModel->referensi(1822),
      'polusi' => $this->masterModel->referensi(1823),
      'rumahSehat' => $this->masterModel->referensi(1824),
      'overweight' => $this->masterModel->referensi(1825),
      'aktifitasFisik' => $this->masterModel->referensi(1826),
      'dietRendah' => $this->masterModel->referensi(1827),
      'sindromametabolik' => $this->masterModel->referensi(1828),
      // Akhir faktor risiko atau gejala kanker

      // Mulai khusus wanita
      'kawin' => $this->masterModel->referensi(308),
      'melahirkan' => $this->masterModel->referensi(309),
      'pernahMenyusui' => $this->masterModel->referensi(310),
      'kontrasepsiHormonal' => $this->masterModel->referensi(311),
      'hormonalLain' => $this->masterModel->referensi(312),
      'haid' => $this->masterModel->referensi(313),
      'menopause' => $this->masterModel->referensi(314),
      'keluhanVagina' => $this->masterModel->referensi(315),
      'keputihan' => $this->masterModel->referensi(363),
      'pendarahanSpontan' => $this->masterModel->referensi(364),
      'pendarahanSenggama' => $this->masterModel->referensi(316),
      'papsSmear' => $this->masterModel->referensi(317),
      // Akhir khusus wanita

      'rencanaPemeriksaanPenunjang' => $this->masterModel->referensi(1795),

      // Mulai faktor risiko
      'jenkelWanita' => $this->masterModel->referensi(1417),
      'lebih50' => $this->masterModel->referensi(1418),
      'kankerPayudaraKelTingkat1' => $this->masterModel->referensi(1419),
      'kankerPayudaraKelTingkat2' => $this->masterModel->referensi(1428),
      'radioterapiPanjang' => $this->masterModel->referensi(1420),
      'tumorPayudaraJinak' => $this->masterModel->referensi(1421),
      'kankerOvarium' => $this->masterModel->referensi(1422),
      'menarcheDini' => $this->masterModel->referensi(1423),
      'menopauseTerlambat' => $this->masterModel->referensi(1424),
      'menikah' => $this->masterModel->referensi(1785),
      'memilikiAnak' => $this->masterModel->referensi(1425),
      'melahirkan30' => $this->masterModel->referensi(1426),
      'riwayatMenyusui' => $this->masterModel->referensi(1427),
      'obatHormonal' => $this->masterModel->referensi(1429),
      // Akhir faktor risiko

      // Mulai gejala
      'menopauseGejala' => $this->masterModel->referensi(1440),
      'menstruasi' => $this->masterModel->referensi(1441),
      'menyusui' => $this->masterModel->referensi(1442),
      'benjolanPayudara' => $this->masterModel->referensi(1408),
      'nyeriPayudara' => $this->masterModel->referensi(1443),
      'bengkakPayudara' => $this->masterModel->referensi(1444),
      'benjolanKetiak' => $this->masterModel->referensi(1409),
      'benjolanLeher' => $this->masterModel->referensi(1445),
      'lukaPayudara' => $this->masterModel->referensi(1410),
      'putingTertarik' => $this->masterModel->referensi(1786),
      'kulitJeruk' => $this->masterModel->referensi(1411),
      'lesungPayudara' => $this->masterModel->referensi(1412),
      'keluarCairan' => $this->masterModel->referensi(1413),
      'nyeriTulang' => $this->masterModel->referensi(1414),
      'batukSesakNapas' => $this->masterModel->referensi(1415),
      'mualMuntah' => $this->masterModel->referensi(1416),
      // Akhir gejala

      // Mulai pemeriksaan fisik khusus
      'simetris' => $this->masterModel->referensi(1431),
      'benjolanTerlihat' => $this->masterModel->referensi(1788),
      'lokasi' => $this->masterModel->referensi(1789),
      'kulit' => $this->masterModel->referensi(1433),
      'areolaPapilla' => $this->masterModel->referensi(1434),
      'benjolanTeraba' => $this->masterModel->referensi(1435),
      'permukaan' => $this->masterModel->referensi(1790),
      'konsistensi' => $this->masterModel->referensi(1791),
      'batas' => $this->masterModel->referensi(1792),
      'mobilisasi' => $this->masterModel->referensi(1793),
      'nyeriTekan' => $this->masterModel->referensi(1794),
      'kgbAksilaTeraba' => $this->masterModel->referensi(1436),
      'kgbAksilaSoliterMultipel' => $this->masterModel->referensi(1437),
      'kgbSupraklavikulaTeraba' => $this->masterModel->referensi(1447),
      'kgbSupraklavikulaSoliterMultipel' => $this->masterModel->referensi(1448),
      // Akhir pemeriksaan fisik khusus

      // Mulai pemeriksaan fisik umum
      'keadaanUmum' => $this->masterModel->referensi(320),
      'keadaanGizi' => $this->masterModel->referensi(321),
      'habitus' => $this->masterModel->referensi(322),
      // Akhir pemeriksaan fisik umum
    ];
    if (isset($id)) {
      $detail = $this->UKDDModel->detail($id);
      $data['detail'] = $detail;
      $data['pasien'] = $this->pengkajianAwalModel->getNomr($detail['nokun']);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/emr/deteksiDini/UKDD/historyUKDD', $data);
    }
  }

  public function tabel()
  {
    $draw = intval($this->input->POST('draw'));
    $nomr = $this->input->POST('nomr');
    $history = $this->UKDDModel->history($nomr);
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status = 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no++,
        isset($h->perawat) ? $h->perawat : '-',
        isset($h->dokter) ? $h->dokter : '-',
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->waktu)),
        $h->pengirim,
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-ukdd' class='btn btn-sm btn-danger waves-effect tbl-batal-ukdd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-ukdd' class='btn btn-sm btn-warning waves-effect tbl-detail-ukdd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fas fa-pencil-alt'></i> Ubah
          </button>
          <button type='button' href='#modal-his-ukdd' class='btn btn-custom btn-sm waves-effect tbl-his-ukdd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-history'></i> History
          </button>
          <button type='button' href='#modal-gambar-ukdd' class='btn btn-sm btn-info waves-effect tbl-gambar-ukdd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fas fa-image'></i> Gambar
          </button>
        </div>",
      );
    }
    // echo '<pre>';print_r($data);exit();

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function gambar()
  {
    $post = $this->input->post();
    $idUKDD = $post['id'];
    $data = array(
      'idUKDD' => $idUKDD,
      'jenisPengguna' => $this->session->userdata('status'),
      'gambar' => $this->UKDDModel->gambar($idUKDD),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/emr/deteksiDini/UKDD/gambar', $data);
  }

  public function simpanGambar()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $idUKDD = $post['id'];
    // Mulai batal gambar lama
    $dataGambarLama = array('status' => 0);
    $this->UKDDModel->ubahGambar($idUKDD, $dataGambarLama);
    // Akhir batal gambar lama

    // Mulai simpan gambar baru
    $dataGambarBaru = array(
      'id_ukdd' => $post['id'],
      'pemeriksaan' => file_get_contents($post['pemeriksaan']),
      'oleh' => $this->session->userdata['id'],
      'status' => 1,
    );
    $this->UKDDModel->simpanGambar($dataGambarBaru);
    // Akhir simpan gambar baru

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = $post['id'];

    $data = array('status' => 0);
    $this->UKDDModel->ubah($id, $data);
    $this->KankerPayudaraModel->ubah($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function ubah()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->UKDDModel->rules;
      $this->form_validation->set_rules($rules);

      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $idUKDD = $post['id'] ?? null;
        $tanggal = $post['tanggal'] ?? null;
        $waktu = $post['waktu'] ?? null;
        $tb = isset($post['tb']) ? round($post['tb'], 2) : 0;
        $bb = isset($post['bb']) ? round($post['bb'], 2) : 0;
        $oleh = $this->session->userdata['id'];
        $status = 1;
        $dataSource = 23;

        // Mulai data uji kesehatan deteksi dini
        $dataUKDD = array(
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'keluhan' => $post['keluhan'] ?? null,
          'riwayat_penyakit' => $post['riwayat_penyakit'] ?? null,
          'diabetes_mellitus' => $post['diabetes_mellitus'] ?? null,
          'pen_diabetes_mellitus' => $post['pen_diabetes_mellitus'] ?? null,
          'hipertensi' => $post['hipertensi'] ?? null,
          'pen_hipertensi' => $post['pen_hipertensi'] ?? null,
          'penyakit_jantung' => $post['penyakit_jantung'] ?? null,
          'pen_jantung' => $post['pen_jantung'] ?? null,
          'merokok' => $post['merokok'] ?? null,
          'lama_merokok' => $post['lama_merokok'] ?? null,
          'mulai_merokok' => $post['mulai_merokok'] ?? null,
          'sampai_merokok' => $post['sampai_merokok'] ?? null,
          'perokok_pasif' => $post['perokok_pasif'] ?? null,
          'karsinogen' => $post['karsinogen'] ?? null,
          'polusi' => $post['polusi'] ?? null,
          'rumah_sehat' => $post['rumah_sehat'] ?? null,
          'suka_alkohol' => $post['suka_alkohol'] ?? null,
          'riwayat_hepatitis' => $post['riwayat_hepatitis'] ?? null,
          'riwayat_hepatitis_keluarga' => $post['riwayat_hepatitis_keluarga'] ?? null,
          'pen_hepatitis' => $post['pen_hepatitis'] ?? null,
          'pernah_operasi' => $post['pernah_operasi'] ?? null,
          'ket_pernah_operasi' => $post['ket_pernah_operasi'] ?? null,
          'pernah_transfusi' => $post['pernah_transfusi'] ?? null,
          'penyalahgunaan_obat' => $post['penyalahgunaan_obat'] ?? null,
          'overweight' => $post['overweight'] ?? null,
          'aktifitas_fisik' => $post['aktifitas_fisik'] ?? null,
          'diet_rendah' => $post['diet_rendah'] ?? null,
          'sindroma_metabolik' => $post['sindroma_metabolik'] ?? null,
          'makan_daging' => $post['makan_daging'] ?? null,
          'makan_diasap' => $post['makan_diasap'] ?? null,
          'polip_usus' => $post['polip_usus'] ?? null,
          'infeksi_usus' => $post['infeksi_usus'] ?? null,
          'bab_berubah' => $post['bab_berubah'] ?? null,
          'lama_bab_berubah' => $post['lama_bab_berubah'] ?? null,
          'bab_berdarah' => $post['bab_berdarah'] ?? null,
          'riwayat_kanker_keluarga' => $post['riwayat_kanker_keluarga'] ?? null,
          'pen_kanker' => $post['pen_kanker'] ?? null,
          'hpht' => $post['hpht'] ?? null,
          'hari_hpht' => $post['hari_hpht'] ?? null,
          'jenis_hpht' => $post['jenis_hpht'] ?? null,
          'kawin' => $post['kawin'] ?? null,
          'usia_menikah' => $post['usia_menikah'] ?? null,
          'keluhan_vagina' => $post['keluhan_vagina'] ?? null,
          'keputihan' => $post['keputihan'] ?? null,
          'pendarahan_spontan' => $post['pendarahan_spontan'] ?? null,
          'pendarahan_senggama' => $post['pendarahan_senggama'] ?? null,
          'paps_smear_tidak_normal' => $post['paps_smear_tidak_normal'] ?? null,
          'vagina_touch_check' => $post['vagina_touch_check'] ?? null,
          'vagina_touch_text' => $post['vagina_touch_text'] ?? null,
          'colok_dubur_check' => $post['colok_dubur_check'] ?? null,
          'colok_dubur_text' => $post['colok_dubur_text'] ?? null,
          'rpp' => isset($post['rpp']) ? json_encode($post['rpp']) : null,
          'ket_rpp' => $post['ket_rpp'] ?? null,
          'keadaan_umum' => $post['keadaan_umum'] ?? null,
          'keadaan_gizi' => $post['keadaan_gizi'] ?? null,
          'habitus' => $post['habitus'] ?? null,
          'tiroid' => $post['tiroid'] ?? null,
          'getah_bening' => $post['getah_bening'] ?? null,
          'jantung' => $post['jantung'] ?? null,
          'paru' => $post['paru'] ?? null,
          'inspeksi' => $post['inspeksi'] ?? null,
          'perut_palpasi' => $post['perut_palpasi'] ?? null,
          'perkusi' => $post['perkusi'] ?? null,
          'auskultasi' => $post['auskultasi'] ?? null,
          'reflek_fisiologis' => $post['reflek_fisiologis'] ?? null,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataUKDD);exit();
        $this->UKDDModel->ubah($idUKDD, $dataUKDD);
        // Akhir data uji kesehatan deteksi dini

        // Mulai data kanker payudara
        $dataKP = array(
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'risk_1' => $post['risk_1'] ?? null,
          'risk_2' => $post['risk_2'] ?? null,
          'risk_3' => $post['risk_3'] ?? null,
          'risk_4' => $post['risk_4'] ?? null,
          'risk_5' => $post['risk_5'] ?? null,
          'risk_6' => $post['risk_6'] ?? null,
          'risk_7' => $post['risk_7'] ?? null,
          'risk_8' => $post['risk_8'] ?? null,
          'risk_9' => $post['risk_9'] ?? null,
          'risk_10' => $post['risk_10'] ?? null,
          'risk_11' => $post['risk_11'] ?? null,
          'risk_12' => $post['risk_12'] ?? null,
          'risk_13' => $post['risk_13'] ?? null,
          'risk_14' => $post['risk_14'] ?? null,
          'risk_15' => $post['risk_15'] ?? null,
          'ket_risk_15' => $post['ket_risk_15'] ?? null,
          'risk_16' => $post['risk_16'] ?? null,
          'nilai_risk' => $post['nilai_risk'] ?? null,
          'gejala_1' => $post['gejala_1'] ?? null,
          'gejala_2' => $post['gejala_2'] ?? null,
          'gejala_3' => $post['gejala_3'] ?? null,
          'gejala_4' => $post['gejala_4'] ?? null,
          'gejala_5' => $post['gejala_5'] ?? null,
          'gejala_6' => $post['gejala_6'] ?? null,
          'gejala_7' => $post['gejala_7'] ?? null,
          'gejala_8' => $post['gejala_8'] ?? null,
          'gejala_9' => $post['gejala_9'] ?? null,
          'gejala_10' => $post['gejala_10'] ?? null,
          'gejala_11' => $post['gejala_11'] ?? null,
          'gejala_12' => $post['gejala_12'] ?? null,
          'gejala_13' => $post['gejala_13'] ?? null,
          'gejala_14' => $post['gejala_14'] ?? null,
          'gejala_15' => $post['gejala_15'] ?? null,
          'gejala_16' => $post['gejala_16'] ?? null,
          'gejala_17' => $post['gejala_17'] ?? null,
          'gejala_18' => $post['gejala_18'] ?? null,
          'imt' => $tb != 0 || $bb != 0 ? round(($bb / ($tb / 100 * $tb / 100)), 2) : null,

          // Mulai bagian dokter
          'simetris' => $post['simetris'] ?? null,
          'benjolan_terlihat' => $post['benjolan_terlihat'] ?? null,
          'lokasi_terlihat' => $post['lokasi_terlihat'] ?? null,
          'arah_terlihat' => $post['arah_terlihat'] ?? null,
          'kulit' => $post['kulit'] ?? null,
          'areola_papilla' => $post['areola_papilla'] ?? null,
          'benjolan_teraba' => $post['benjolan_teraba'] ?? null,
          'lokasi_teraba' => $post['lokasi_teraba'] ?? null,
          'arah_teraba' => $post['arah_teraba'] ?? null,
          'jumlah_benjolan_teraba' => $post['jumlah_benjolan_teraba'] ?? null,
          'panjang_benjolan' => $post['panjang_benjolan'] ?? null,
          'lebar_benjolan' => $post['lebar_benjolan'] ?? null,
          'tinggi_benjolan' => $post['tinggi_benjolan'] ?? null,
          'permukaan' => $post['permukaan'] ?? null,
          'konsistensi' => $post['konsistensi'] ?? null,
          'batas' => $post['batas'] ?? null,
          'mobilisasi' => $post['mobilisasi'] ?? null,
          'nyeri_tekan' => $post['nyeri_tekan'] ?? null,
          'kgb_aksila_teraba' => $post['kgb_aksila_teraba'] ?? null,
          'kgb_aksila_soliter_multipel' => $post['kgb_aksila_soliter_multipel'] ?? null,
          'kgb_supraklavikula_teraba' => $post['kgb_supraklavikula_teraba'] ?? null,
          'kgb_supraklavikula_soliter_multipel' => $post['kgb_supraklavikula_soliter_multipel'] ?? null,
          // Akhir bagian dokter

          'status' => $status,
        );

        // Mulai data pengisi
        if ($post['jenis_pengguna'] == 1) {
          $dataKP['dokter'] = $oleh;
        } elseif ($post['jenis_pengguna'] == 2) {
          $dataKP['perawat'] = $oleh;
        }
        // Akhir data pengisi

        // echo '<pre>';print_r($dataKP);exit();
        $this->KankerPayudaraModel->ubah_ukdd($idUKDD, $dataKP);
        // Akhir data kanker payudara

        // Mulai tinggi dan berat badan
        $dataTBB = array(
          'tb' => $tb,
          'bb' => $bb,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataTBB);exit();
        $this->OTKeperawatanModel->ubahTbBb($idUKDD, $dataSource, $dataTBB);
        // Akhir tinggi dan berat badan

        // Mulai tanda vital
        $dataTV = array(
          'td_sistolik' => $post['td_sistolik'] ?? null,
          'td_diastolik' => $post['td_diastolik'] ?? null,
          'nadi' => $post['nadi'] ?? null,
          'pernapasan' => $post['pernapasan'] ?? null,
          'suhu' => $post['suhu'] ?? null,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataTV);exit();
        $this->OTKeperawatanModel->ubahTandaVital($idUKDD, $dataSource, $dataTV);
        // Akhir tanda vital
      }

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
  public function ubahNew()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->UKDDModel->rules;
      $this->form_validation->set_rules($rules);

      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $idUKDD = $post['id'] ?? null;
        $tanggal = $post['tanggal'] ?? null;
        $waktu = $post['waktu'] ?? null;
        $tb = isset($post['tb']) ? round($post['tb'], 2) : 0;
        $bb = isset($post['bb']) ? round($post['bb'], 2) : 0;
        $oleh = $this->session->userdata['id'];
        $status = 1;
        $dataSource = 23;

        // Mulai data uji kesehatan deteksi dini
        $dataUKDD = array(
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'keluhan' => $post['keluhan'] ?? null,
          'riwayat_penyakit' => $post['riwayat_penyakit'] ?? null,
          'diabetes_mellitus' => $post['diabetes_mellitus'] ?? null,
          'pen_diabetes_mellitus' => $post['pen_diabetes_mellitus'] ?? null,
          'hipertensi' => $post['hipertensi'] ?? null,
          'pen_hipertensi' => $post['pen_hipertensi'] ?? null,
          'penyakit_jantung' => $post['penyakit_jantung'] ?? null,
          'pen_jantung' => $post['pen_jantung'] ?? null,
          'merokok' => $post['merokok'] ?? null,
          'lama_merokok' => $post['lama_merokok'] ?? null,
          'mulai_merokok' => $post['mulai_merokok'] ?? null,
          'sampai_merokok' => $post['sampai_merokok'] ?? null,
          'perokok_pasif' => $post['perokok_pasif'] ?? null,
          'karsinogen' => $post['karsinogen'] ?? null,
          'polusi' => $post['polusi'] ?? null,
          'rumah_sehat' => $post['rumah_sehat'] ?? null,
          'suka_alkohol' => $post['suka_alkohol'] ?? null,
          'riwayat_hepatitis' => $post['riwayat_hepatitis'] ?? null,
          'riwayat_hepatitis_keluarga' => $post['riwayat_hepatitis_keluarga'] ?? null,
          'pen_hepatitis' => $post['pen_hepatitis'] ?? null,
          'pernah_operasi' => $post['pernah_operasi'] ?? null,
          'ket_pernah_operasi' => $post['ket_pernah_operasi'] ?? null,
          'pernah_transfusi' => $post['pernah_transfusi'] ?? null,
          'penyalahgunaan_obat' => $post['penyalahgunaan_obat'] ?? null,
          'overweight' => $post['overweight'] ?? null,
          'aktifitas_fisik' => $post['aktifitas_fisik'] ?? null,
          'diet_rendah' => $post['diet_rendah'] ?? null,
          'sindroma_metabolik' => $post['sindroma_metabolik'] ?? null,
          'makan_daging' => $post['makan_daging'] ?? null,
          'makan_diasap' => $post['makan_diasap'] ?? null,
          'polip_usus' => $post['polip_usus'] ?? null,
          'infeksi_usus' => $post['infeksi_usus'] ?? null,
          'bab_berubah' => $post['bab_berubah'] ?? null,
          'lama_bab_berubah' => $post['lama_bab_berubah'] ?? null,
          'bab_berdarah' => $post['bab_berdarah'] ?? null,
          'riwayat_kanker_keluarga' => $post['riwayat_kanker_keluarga'] ?? null,
          'pen_kanker' => $post['pen_kanker'] ?? null,
          'hpht' => $post['hpht'] ?? null,
          'hari_hpht' => $post['hari_hpht'] ?? null,
          'jenis_hpht' => $post['jenis_hpht'] ?? null,
          'kawin' => $post['kawin'] ?? null,
          'usia_menikah' => $post['usia_menikah'] ?? null,
          'keluhan_vagina' => $post['keluhan_vagina'] ?? null,
          'keputihan' => $post['keputihan'] ?? null,
          'pendarahan_spontan' => $post['pendarahan_spontan'] ?? null,
          'pendarahan_senggama' => $post['pendarahan_senggama'] ?? null,
          'paps_smear_tidak_normal' => $post['paps_smear_tidak_normal'] ?? null,
          'vagina_touch_check' => $post['vagina_touch_check'] ?? null,
          'vagina_touch_text' => $post['vagina_touch_text'] ?? null,
          'colok_dubur_check' => $post['colok_dubur_check'] ?? null,
          'colok_dubur_text' => $post['colok_dubur_text'] ?? null,
          'rpp' => isset($post['rpp']) ? json_encode($post['rpp']) : null,
          'ket_rpp' => $post['ket_rpp'] ?? null,
          'keadaan_umum' => $post['keadaan_umum'] ?? null,
          'keadaan_gizi' => $post['keadaan_gizi'] ?? null,
          'habitus' => $post['habitus'] ?? null,
          'tiroid' => $post['tiroid'] ?? null,
          'getah_bening' => $post['getah_bening'] ?? null,
          'jantung' => $post['jantung'] ?? null,
          'paru' => $post['paru'] ?? null,
          'inspeksi' => $post['inspeksi'] ?? null,
          'perut_palpasi' => $post['perut_palpasi'] ?? null,
          'perkusi' => $post['perkusi'] ?? null,
          'auskultasi' => $post['auskultasi'] ?? null,
          'reflek_fisiologis' => $post['reflek_fisiologis'] ?? null,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataUKDD);exit();
        $this->UKDDModel->ubah($idUKDD, $dataUKDD);
        // Akhir data uji kesehatan deteksi dini

        // Mulai data kanker payudara
        $dataKP = array(
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'risk_1' => $post['risk_1'] ?? null,
          'risk_2' => $post['risk_2'] ?? null,
          'risk_3' => $post['risk_3'] ?? null,
          'risk_4' => $post['risk_4'] ?? null,
          'risk_5' => $post['risk_5'] ?? null,
          'risk_6' => $post['risk_6'] ?? null,
          'risk_7' => $post['risk_7'] ?? null,
          'risk_8' => $post['risk_8'] ?? null,
          'risk_9' => $post['risk_9'] ?? null,
          'risk_10' => $post['risk_10'] ?? null,
          'risk_11' => $post['risk_11'] ?? null,
          'risk_12' => $post['risk_12'] ?? null,
          'risk_13' => $post['risk_13'] ?? null,
          'risk_14' => $post['risk_14'] ?? null,
          'risk_15' => $post['risk_15'] ?? null,
          'ket_risk_15' => $post['ket_risk_15'] ?? null,
          'risk_16' => $post['risk_16'] ?? null,
          'nilai_risk' => $post['nilai_risk'] ?? null,
          'gejala_1' => $post['gejala_1'] ?? null,
          'gejala_2' => $post['gejala_2'] ?? null,
          'gejala_3' => $post['gejala_3'] ?? null,
          'gejala_4' => $post['gejala_4'] ?? null,
          'gejala_5' => $post['gejala_5'] ?? null,
          'gejala_6' => $post['gejala_6'] ?? null,
          'gejala_7' => $post['gejala_7'] ?? null,
          'gejala_8' => $post['gejala_8'] ?? null,
          'gejala_9' => $post['gejala_9'] ?? null,
          'gejala_10' => $post['gejala_10'] ?? null,
          'gejala_11' => $post['gejala_11'] ?? null,
          'gejala_12' => $post['gejala_12'] ?? null,
          'gejala_13' => $post['gejala_13'] ?? null,
          'gejala_14' => $post['gejala_14'] ?? null,
          'gejala_15' => $post['gejala_15'] ?? null,
          'gejala_16' => $post['gejala_16'] ?? null,
          'gejala_17' => $post['gejala_17'] ?? null,
          'gejala_18' => $post['gejala_18'] ?? null,
          'imt' => $tb != 0 || $bb != 0 ? round(($bb / ($tb / 100 * $tb / 100)), 2) : null,

          // Mulai bagian dokter
          'payudara_text' => $post['payudara_text'] ?? null,
          // Akhir bagian dokter

          'status' => $status,
        );

        // Mulai data pengisi
        if ($post['jenis_pengguna'] == 1) {
          $dataKP['dokter'] = $oleh;
        } elseif ($post['jenis_pengguna'] == 2) {
          $dataKP['perawat'] = $oleh;
        }
        // Akhir data pengisi

        // echo '<pre>';print_r($dataKP);exit();
        $this->KankerPayudaraModel->ubah_ukdd($idUKDD, $dataKP);
        // Akhir data kanker payudara

        // Mulai tinggi dan berat badan
        $dataTBB = array(
          'tb' => $tb,
          'bb' => $bb,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataTBB);exit();
        $this->OTKeperawatanModel->ubahTbBb($idUKDD, $dataSource, $dataTBB);
        // Akhir tinggi dan berat badan

        // Mulai tanda vital
        $dataTV = array(
          'td_sistolik' => $post['td_sistolik'] ?? null,
          'td_diastolik' => $post['td_diastolik'] ?? null,
          'nadi' => $post['nadi'] ?? null,
          'pernapasan' => $post['pernapasan'] ?? null,
          'suhu' => $post['suhu'] ?? null,
          'oleh' => $oleh,
          'status' => $status,
        );
        // echo '<pre>';print_r($dataTV);exit();
        $this->OTKeperawatanModel->ubahTandaVital($idUKDD, $dataSource, $dataTV);
        // Akhir tanda vital
      }

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
}

// End of file UKDD.php
// Location: ./application/controllers/emr/deteksiDini/UKDD.php