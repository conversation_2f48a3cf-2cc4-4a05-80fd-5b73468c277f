<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TandaVitalModel extends MY_Model
{
    protected $_table_name = 'db_pasien.tb_tanda_vital';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'nokun',
                'label' => 'Nomor kunjungan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'nomr',
                'label' => 'Nomor rekam medis',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'td_sistolik',
                'label' => 'Tekanan darah sistolik',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'td_diastolik',
                'label' => 'Tekanan darah diastolik',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'nadi',
                'label' => 'Nadi',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'pernapasan',
                'label' => 'Pernapasan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'suhu',
                'label' => 'Suhu',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
        ];
    }

    public function simpan($data)
    {
        $this->db->insert('db_pasien.tb_tanda_vital', $data);
        return $this->db->insert_id();
    }

    public function ubah($id, $data)
    {
        $this->db->where('db_pasien.tb_tanda_vital.id', $id);
        $this->db->update('db_pasien.tb_tanda_vital', $data);
    }

    function table_query()
    {
        $this->db->select(
            "t.id, t.data_source, t.ref,t.nomr, t.nokun, t.pukul, t.td_sistolik, t.td_diastolik, t.nadi, t.pernapasan,
            t.suhu, t.map, t.oleh, t.status,
            CONCAT(e.tanggal, ' ',IF(e.jam='00:00:00',DATE_FORMAT(t.created_at,'%H:%i:%s'),e.jam)) created_at,
            t.created_at waktu, ds.deskripsi, master.getNamaLengkapPegawai(ap.NIP) oleh_desc"
        );
        $this->db->from('db_pasien.tb_tanda_vital t');
        $this->db->join('db_master.tb_data_source ds', 't.data_source = ds.id', 'left');
        $this->db->join('aplikasi.pengguna ap', 't.oleh = ap.ID', 'left');
        $this->db->join('pendaftaran.kunjungan pk', 't.nokun = pk.NOMOR', 'left');
        $this->db->join('keperawatan.tb_ews e', 'e.id = t.ref AND t.data_source=3', 'left');
        $this->db->where('t.td_sistolik IS NOT NULL');
        $this->db->order_by('t.created_at', 'DESC');
        if ($this->input->post('nomr')) {
            $this->db->where('t.nomr', $this->input->post('nomr'));
            $this->db->limit('1');
        }

        if ($this->input->post('nokun')) {
            $this->db->where('t.nokun', $this->input->post('nokun'));
            $this->db->limit('1');
        }

        if ($this->input->post('nopen')) {
            $this->db->where('pk.NOPEN', $this->input->post('nopen'));
            $this->db->limit('1');
        }

        if ($this->input->post('id')) {
            $this->db->where('t.id', $this->input->post('id'));
        }
    }

    function get_table($single = true)
    {
        $this->db->where('t.status', 1);
        $this->table_query();
        $query = $this->db->get();
        if ($single == true) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}

// End of file TandaVitalModel.php
// Location: ./application/models/rekam_medis/TandaVitalModel.php