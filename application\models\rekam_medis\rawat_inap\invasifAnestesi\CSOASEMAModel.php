<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CSOASEMAModel extends MY_Model
{

  protected $_table_name = 'keperawatan.tb_csoasema';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nomr' => array(
      'field' => 'nomr',
      'label' => 'Nomor Rekam Medis',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib Ang<PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function replace($data)
  {
    $this->db->replace('keperawatan.tb_csoasema', $data);
  }

  public function history($nomr, $param)
  {
    if ($param == 'jumlah') {
      $this->db->select('c.id');
    } elseif ($param == 'tabel') {
      $this->db->select(
        'c.id, c.tanggal, c.jam, master.getNamaLengkapPegawai(peng.NIP) pengisi, c.updated_at, c.status'
      );
    }
    $this->db->from('keperawatan.tb_csoasema c');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = c.oleh', 'left');
    $this->db->where('c.nomr', $nomr);
    if ($param == 'jumlah') {
      $this->db->where('c.status', 1);
      $query = $this->db->get();
      return $query->num_rows();
    } elseif ($param == 'tabel') {
      $this->db->order_by('c.updated_at', 'DESC');
      $query = $this->db->get();
      return $query;
    } else {
      return null;
    }
  }

  public function ubah($id, $data)
  {
    $this->db->where('keperawatan.tb_csoasema.id', $id);
    $this->db->update('keperawatan.tb_csoasema', $data);
  }

  public function detail($id)
  {
    $this->db->select(
      'c.id, c.tanggal, c.jam, c.scope, c.tube, c.airway_device, c.tape, c.introducer, c.connection, c.suction,
      c.premedikasi_po, c.induksi_po, c.pelumpuh_otot_po, c.reverse_antidotum_po, c.emergensi_po, c.analgetik_po,
      c.premedikasi_es, c.suntik_lokal_es, c.spinal_es, c.emergensi_es, c.analgetik_es, c.ket_sambung_kabel,
      c.sambung_selang, c.ket_sambung_selang, c.bagging, c.ket_bagging, c.ventilator, c.ket_ventilator, c.vaporizer,
      c.ket_vaporizer, c.absorber, c.ket_absorber, c.corrigated, c.ket_corrigated, c.filter, c.ket_filter, c.facemask,
      c.ket_facemask,'
    );
    $this->db->from('keperawatan.tb_csoasema c');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = c.oleh', 'left');
    $this->db->where('c.id', $id);
    $query = $this->db->get();
    return $query->result_array();
  }
}
