<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PermintaanDirawat extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'PengkajianAwalModel',
        'masterModel',
        'permintaanDirawat/AdmModel'
      )
    );
  }

  public function index()
  {
    $data = array(
      'title' => 'Permintaan Dirawat',
      'isi' => 'PermintaanDirawat/index',
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
  }

  public function nonFinal()
  {
    $post = $this->input->post();
    $this->load->view('PermintaanDirawat/nonFinal/index');
  }

  public function tabelNonFinal()
  {
    $draw = intval($this->input->post('draw'));
    $tabel = $this->AdmModel->listPermintaanRawatNoFinal();
    $data = array();
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel as $t) {
      $data[] = array(
        $t['NORM'],
        $t['PASIEN'],
        $t['RUANGAN_ASAL'],
        $t['TUJUAN'],
        $t['TANGGAL'] == '0000-00-00' ? date('d/m/Y', strtotime($t['TANGGAL'])) : '-',
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-detail-non-final-pd' class='btn btn-sm btn-primary waves-effect tbl-detail-non-final-pd' data-toggle='modal' data-id='" . $t['ID_PERMINTAAN'] . "'>
            <i class='fa fa-eye'></i> Lihat
          </button>
          <button type='button' href='#modal-final-non-final-pd' class='btn btn-sm btn-success waves-effect tbl-final-non-final-pd' data-toggle='modal' data-id='" . $t['ID_PERMINTAAN'] . '-' . $t['PASIEN'] . "'>
            <i class='fas fa-flag-checkered'></i> Final
          </button>
          <a href='/reports/simrskd/PermintaanDirawat/PermintaanDirawat.php?format=pdf&NOID=" . $t['ID_PERMINTAAN'] . "' class='btn btn-sm btn-warning waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak
          </a>
        </div>",
      );
    }

    $output = array(
      'draw' => $draw,
      'data' => $data
    );
    echo json_encode($output);
  }

  public function final()
  {
    $post = $this->input->post();
    $this->load->view('PermintaanDirawat/final/index');
  }

  public function tabelFinal()
  {
    $draw = intval($this->input->post('draw'));
    $tabel = $this->AdmModel->listPermintaanRawatFinal();
    $data = array();
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel as $t) {
      $data[] = array(
        $t['NORM'],
        $t['PASIEN'],
        $t['RUANGAN_ASAL'],
        $t['TUJUAN'],
        $t['TANGGAL'] == '0000-00-00' ? date('d/m/Y', strtotime($t['TANGGAL'])) : '-',
        $t['TANGGAL'] == '0000-00-00' ? date('d/m/Y', strtotime($t['TANGGAL_FINAL'])) : '-',
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-detail-final-pd' class='btn btn-sm btn-primary waves-effect tbl-detail-final-pd' data-toggle='modal' data-id='" . $t['ID_PERMINTAAN'] . "'>
            <i class='fa fa-eye'></i> Lihat
          </button>
          <a href='/reports/simrskd/PermintaanDirawat/PermintaanDirawat.php?format=pdf&NOID=" . $t['ID_PERMINTAAN'] . "' class='btn btn-sm btn-warning waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak
          </a>
        </div>",
      );
    }

    $output = array(
      'draw' => $draw,
      'data' => $data
    );
    echo json_encode($output);
  }
}

/* End of file PermintaanDirawat.php */
/* Location: ./application/controllers/PermintaanDirawat/PermintaanDirawat.php */