<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormulirKriteriaRawatIntensif extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'igd/FKRIModel'));
  }

  

  public function index()
  {
    $id_pengguna = $this->session->userdata('id');
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $nopen = $getNomr['NOPEN'];
    $getIdEmrFkri = $this->pengkajianAwalModel->getIdEmrFkri($nokun);
    $id_emr = $getIdEmrFkri['id_fkri'];
    $history_fkri = $this->pengkajianAwalModel->history_fkri($nomr);

    $fkri_gangguan_kardiovaskular = $this->masterModel->referensi(811);
    $fkri_gangguan_pernapasan = $this->masterModel->referensi(812);
    $fkri_gangguan_neurologi = $this->masterModel->referensi(813);
    $fkri_gangguan_gastrointestinal = $this->masterModel->referensi(814);
    $fkri_gangguan_metabolik = $this->masterModel->referensi(815);
    $fkri_kegawatdaruratan_onkologi = $this->masterModel->referensi(817);
    $fkri_kriteria_eksklusi = $this->masterModel->referensi(818);
    $fkri_ruangan_masuk = $this->masterModel->referensi(829);
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
    if ($id_emr != "") {
      $id_fkri = $id_emr;
      $get_fkri = $this->pengkajianAwalModel->get_fkri($id_fkri);
    }

    $data = array(
      'id_emr' => isset($id_emr) ? $id_emr : "",
      'id_pengguna' => $id_pengguna,
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'nomr' => $nomr,
      'nopen' => $nopen,
      'history_fkri' => $history_fkri,
      'get_fkri' => $get_fkri,
      'fkri_gangguan_kardiovaskular' => $fkri_gangguan_kardiovaskular,
      'fkri_gangguan_pernapasan' => $fkri_gangguan_pernapasan,
      'fkri_gangguan_neurologi' => $fkri_gangguan_neurologi,
      'fkri_gangguan_gastrointestinal' => $fkri_gangguan_gastrointestinal,
      'fkri_gangguan_metabolik' => $fkri_gangguan_metabolik,
      'fkri_kegawatdaruratan_onkologi' => $fkri_kegawatdaruratan_onkologi,
      'fkri_kriteria_eksklusi' => $fkri_kriteria_eksklusi,
      'fkri_ruangan_masuk' => $fkri_ruangan_masuk,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap
    );

    $this->load->view('Pengkajian/igd/formulirKriteriaRawatIntensif/index', $data);
  }

  public function indexRawatInap()
  {
    $id_pengguna = $this->session->userdata('id');
    $nokun = $this->uri->segment(2);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $history_fkri = $this->pengkajianAwalModel->history_fkri($nomr);
  
    if ($this->uri->segment(3) != "") {
      $get_fkri = $this->pengkajianAwalModel->get_fkri($this->uri->segment(3));
    }else {
      $get_fkri = "";
    }

    $fkri_gangguan_kardiovaskular = $this->masterModel->referensi(811);
    $fkri_gangguan_pernapasan = $this->masterModel->referensi(812);
    $fkri_gangguan_neurologi = $this->masterModel->referensi(813);
    $fkri_gangguan_gastrointestinal = $this->masterModel->referensi(814);
    $fkri_gangguan_metabolik = $this->masterModel->referensi(815);
    $fkri_kegawatdaruratan_onkologi = $this->masterModel->referensi(817);
    $fkri_kriteria_eksklusi = $this->masterModel->referensi(818);
    $fkri_ruangan_masuk = $this->masterModel->referensi(829);
    $dokter = $this->masterModel->listDr();
    $perawat = $this->masterModel->listPerawat();
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();

    $data = array(
      'nokun' => $nokun,
      'get_fkri' => $get_fkri,
      'getNomr' => $getNomr,
      'history_fkri' => $history_fkri,
      'id_pengguna' => $id_pengguna,
      'fkri_gangguan_kardiovaskular' => $fkri_gangguan_kardiovaskular,
      'fkri_gangguan_pernapasan' => $fkri_gangguan_pernapasan,
      'fkri_gangguan_neurologi' => $fkri_gangguan_neurologi,
      'fkri_gangguan_gastrointestinal' => $fkri_gangguan_gastrointestinal,
      'fkri_gangguan_metabolik' => $fkri_gangguan_metabolik,
      'fkri_kegawatdaruratan_onkologi' => $fkri_kegawatdaruratan_onkologi,
      'fkri_kriteria_eksklusi' => $fkri_kriteria_eksklusi,
      'fkri_ruangan_masuk' => $fkri_ruangan_masuk,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
      'dokter' => $dokter,
      'perawat' => $perawat
    );

    $this->load->view('Pengkajian/igd/formulirKriteriaRawatIntensif/index', $data);
  }

  public function indexFKPKRI() {
    $id = $this->input->post('id');
    $nopen = $this->input->post('nopen');
    $history_fkpkri = $this->FKRIModel->historyFKPKRI($id);
    $getTekananDarah = $this->FKRIModel->getTekananDarah($nopen);

    $data = array(
      'id_fkri' => $id,
      'nopen' => $nopen,
      'listAlatBantuNapas' => $this->masterModel->referensi(1457),
      'listKondisiStabil' => $this->masterModel->referensi(1458),
      'listTerapiGagal' => $this->masterModel->referensi(1459),
      'listEndOfLife' => $this->masterModel->referensi(1460),
      'listMenolakDirawat' => $this->masterModel->referensi(1461),
      'ruanganIntensif' => $this->masterModel->ruanganIntensif(),
      'history_fkpkri' => $history_fkpkri,
      'getTekananDarah' => $getTekananDarah
    );
    $this->load->view('Pengkajian/igd/formulirKriteriaRawatIntensif/modalFKPKRI', $data);
  }

  public function action_fkri($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $get_id_fkri = !empty($post['id_fkri']) ? $post['id_fkri'] : $this->pengkajianAwalModel->getIdEmr();

        $data_fkri = array(
          'id_fkri'  => $get_id_fkri,
          'nokun'           => isset($post['nokun']) ? $post['nokun'] : null,
          'fkri_diagnosis'   => isset($post['fkri_diagnosis']) ? $post['fkri_diagnosis'] : null,
          'fkri_jenis_ruangan'   => isset($post['fkri_jenis_ruangan']) ? $post['fkri_jenis_ruangan'] : null,
          'fkri_ruangan' => isset($post['fkri_ruangan']) ? $post['fkri_ruangan'] : null,
          'dokter_pengirim' => isset($post['dokter_pengirim']) ? $post['dokter_pengirim'] : null,
          'dokter_penerima' => isset($post['dokter_penerima']) ? $post['dokter_penerima'] : null,
          'perawat_pengirim' => isset($post['perawat_pengirim']) ? $post['perawat_pengirim'] : null,
          'perawat_penerima' => isset($post['perawat_penerima']) ? $post['perawat_penerima'] : null,
          'fkri_gangguan_kardiovaskular' => isset($post['fkri_gangguan_kardiovaskular']) ? json_encode($post['fkri_gangguan_kardiovaskular']) : null,
          'fkri_gangguan_kardiovaskular_lainnya' => isset($post['fkri_gangguan_kardiovaskular_lainnya']) ? $post['fkri_gangguan_kardiovaskular_lainnya'] : null,

          'fkri_gangguan_pernapasan' => isset($post['fkri_gangguan_pernapasan']) ? json_encode($post['fkri_gangguan_pernapasan']) : null,
          'fkri_gangguan_pernapasan_lainnya' => isset($post['fkri_gangguan_pernapasan_lainnya']) ? $post['fkri_gangguan_pernapasan_lainnya'] : null,

          'fkri_gangguan_neurologi' => isset($post['fkri_gangguan_neurologi']) ? json_encode($post['fkri_gangguan_neurologi']) : null,
          'fkri_gangguan_neurologi_lainnya' => isset($post['fkri_gangguan_neurologi_lainnya']) ? $post['fkri_gangguan_neurologi_lainnya'] : null,

          'fkri_gangguan_gastrointestinal' => isset($post['fkri_gangguan_gastrointestinal']) ? json_encode($post['fkri_gangguan_gastrointestinal']) : null,
          'fkri_gangguan_gastrointestinal_lainnya' => isset($post['fkri_gangguan_gastrointestinal_lainnya']) ? $post['fkri_gangguan_gastrointestinal_lainnya'] : null,

          'fkri_gangguan_metabolik' => isset($post['fkri_gangguan_metabolik']) ? json_encode($post['fkri_gangguan_metabolik']) : null,
          'fkri_gangguan_metabolik_lainnya' => isset($post['fkri_gangguan_metabolik_lainnya']) ? $post['fkri_gangguan_metabolik_lainnya'] : null,

          'fkri_perawatan_perioperatif' => isset($post['fkri_perawatan_perioperatif']) ? $post['fkri_perawatan_perioperatif'] : null,

          'fkri_kegawatdaruratan_onkologi' => isset($post['fkri_kegawatdaruratan_onkologi']) ? json_encode($post['fkri_kegawatdaruratan_onkologi']) : null,

          'fkri_kriteria_eksklusi' => isset($post['fkri_kriteria_eksklusi']) ? json_encode($post['fkri_kriteria_eksklusi']) : null,

          'fkri_ruangan_masuk' => isset($post['fkri_ruangan_masuk']) ? $post['fkri_ruangan_masuk'] : null,
          'fkri_desk_ruangan_masuk' => isset($post['fkri_desk_ruangan_masuk']) ? $post['fkri_desk_ruangan_masuk'] : null,

          'created_at' => date('Y-m-d H:i:s'),
          'fkri_tanggal' =>  isset($post['tanggal']) ? date('Y-m-d', strtotime($post['tanggal'])) : null,
          'fkri_waktu' => isset($post['jam']) ? date('H:i', strtotime($post['jam'])) : null,
          'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

        // print_r($data_fkri);exit();

        if (!empty($post['id_fkri'])) {
          $this->db->replace('medis.tb_igd_fkri', $data_fkri);
          $result = array('status' => 'success', 'pesan' => 'ubah');
        }else {
          $this->db->insert('medis.tb_igd_fkri', $data_fkri);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }

  public function action_fkpkri($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();

        $data = array(
          'id' => isset($post['id_fkpkri']) ? $post['id_fkpkri'] : "",
          'id_fkri'  => isset($post['id_fkri']) ? $post['id_fkri'] : "",
          'tanggal' => isset($post['tanggal']) ? date('Y-m-d', strtotime($post['tanggal'])) : "",
          'jam' => isset($post['jam']) ? date('H:i', strtotime($post['jam'])) : "",
          'ruangan' => isset($post['ruangan']) ? $post['ruangan'] : "",
          'alat_bantu_napas' => isset($post['alat_bantu_napas']) ? $post['alat_bantu_napas'] : "",
          'kondisi_stabil' => isset($post['kondisi_stabil']) ? $post['kondisi_stabil'] : "",
          'sistolik' => isset($post['sistolik']) ? $post['sistolik'] : "",
          'diastolik' => isset($post['diastolik']) ? $post['diastolik'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'rr' => isset($post['rr']) ? $post['rr'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'gcs' => isset($post['gcs']) ? $post['gcs'] : "",
          'terapi_gagal' => isset($post['terapi_gagal']) ? $post['terapi_gagal'] : "",
          'end_of_life' => isset($post['end_of_life']) ? $post['end_of_life'] : "",
          'menolak_dirawat' => isset($post['menolak_dirawat']) ? $post['menolak_dirawat'] : ""
        );

        if (!empty($post['id_fkpkri'])) {
          $this->db->where('medis.tb_igd_fkpkri.id', $post['id_fkpkri']);
        $this->db->update('medis.tb_igd_fkpkri', $data);
          $result = array('status' => 'success');
        }else {
          $this->db->insert('medis.tb_igd_fkpkri', $data);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }

  public function lihatHistoryFKPKRI() {

    $data = array(
      'id_fkpkri' => $this->input->post('id'),
      'listAlatBantuNapas' => $this->masterModel->referensi(1457),
      'listKondisiStabil' => $this->masterModel->referensi(1458),
      'listTerapiGagal' => $this->masterModel->referensi(1459),
      'listEndOfLife' => $this->masterModel->referensi(1460),
      'listMenolakDirawat' => $this->masterModel->referensi(1461),
      'ruanganIntensif' => $this->masterModel->ruanganIntensif(),
      'detailFKPKRI' => $this->FKRIModel->detailFKPKRI($this->input->post('id'))
    );

    $this->load->view('Pengkajian/igd/formulirKriteriaRawatIntensif/modalViewEditFKPKRI', $data);
  }
}


/* End of file FormulirKriteriaRawatIntensif.php */
/* Location: ./application/controllers/igd/FormulirKriteriaRawatIntensif.php */
