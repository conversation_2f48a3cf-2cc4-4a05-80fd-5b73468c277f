<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ProtokolKemoModel extends CI_Model {

  public function namaPegawai($id)
  {
    $query  = $this->db->query("SELECT master.getNamaLengkapPegawai(mp.NIP) NAMAPEGAWAI
     FROM aplikasi.pengguna ap
     LEFT JOIN master.pegawai mp ON ap.NIP = mp.NIP
     WHERE ap.ID = '$id'");

    return $query->row_array();
  }

  public function simpan($data)
  {
    $this->db->insert_batch('keperawatan.tb_tbak_detail', $data);
  }

  public function dataDiriPasien($nomr)
  {
    $query = $this->db->query("SELECT mp.`*`, master.getNamaLengkap(mp.NORM)NAMAPASIEN
      , CONCAT(master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR), ' Tahun') UMUR
      , mk.NOMOR tlpn
      FROM master.pasien mp
      LEFT JOIN pendaftaran.pendaftaran pp ON pp.NORM = mp.NORM
      LEFT JOIN master.kontak_pasien mk ON mk.NORM = mp.NORM
      WHERE mp.NORM = '$nomr'
      ORDER BY pp.NOMOR DESC
      LIMIT 1");
    if ($query->num_rows() > 0) {
      return $query->row_array();
    }else{
      return $query->num_rows();
    }
  }

  public function cekAturanPakai($ket)
  {
    $query = "SELECT *
    FROM master.referensi r
    WHERE r.JENIS=? AND r.STATUS=?
    AND
    r.DESKRIPSI LIKE ?";
    $bind = $this->db->query($query, array(41,1,$ket));
    return $bind;
  }

  public function dpjpDiagnosaProKem($idfarmasi)
  {
    $query = "SELECT prtkl.DIAGNOSA DIAGNOSA, master.getNamaLengkapPegawai(dpjp.NIP) DPJP, prtkl.DPJP ID_DOKTER
    FROM medis.tb_prtkl_kemo prtkl
    LEFT JOIN master.dokter dpjp ON dpjp.ID = prtkl.DPJP
    WHERE prtkl.NOMOR=?";
    $bind = $this->db->query($query, array($idfarmasi));
    return $bind->row_array();
  }

  public function getHasilInputFarmasi($iddetail)
  {
    $query = $this->db->query("SELECT  pkf.id_detail_farmasi ID_DETAIL_FARMASI, pkf.id_barang_ruangan_obat ID_OBAT
      , ib.NAMA NAMA_OBAT
      , pkf.pilih_farmasi_tujuan ID_TUJUAN
      , pkf.id_barang_ruangan_pengenceran ID_OBAT_PENGENCERAN
      , ibd.NAMA NAMA_PENGENCERAN
      , pkf.dosis DOSIS_OBAT
      , pkf.dosis_pengenceran DOSIS_PENGENCERAN
      , pkf.jumlah JUMLAH_OBAT
      , pkf.jumlah_pengenceran JUMLAH_PENGENCERAN
      , pkf.jenis JENIS_DETAIL
      , pkf.pilih_farmasi_tujuan ID_FARMASI_TUJUAN
      , pkf.no_batch NO_BATCH
      , pkf.stabilitas STABILITAS
      FROM keperawatan.tb_prtkl_kemo_pemberian_encer pkf

      LEFT JOIN inventory.barang_ruangan ibr ON ibr.ID = pkf.id_barang_ruangan_obat
      LEFT JOIN inventory.barang ib ON ib.ID = ibr.BARANG
      LEFT JOIN inventory.barang_ruangan ibrd ON ibrd.ID = pkf.id_barang_ruangan_pengenceran
      LEFT JOIN inventory.barang ibd ON ibd.ID = ibrd.BARANG
      WHERE pkf.id_detail_farmasi='$iddetail'");
    if ($query->num_rows() > 0) {
      return $query->row_array();
    }else{
      return $query->num_rows();
    }
  }

  public function rekapHasilInputFarmasi($noorder)
  {
    $query = $this->db->query(
      "SELECT *
      FROM 
      ((
        SELECT pkd.NOMOR_PRTKL_MEDIS NO_ORDER, r.DESKRIPSI FARMASI_TUJUAN
        , r.ID ID_FARMASI_TUJUAN
        ,dos.NAMA_OBAT ID_BARANG_RUANGAN, b.NAMA NAMA_OBAT
        , SUM(dos.DOSIS) DOSIS, SUM(dos.JUMLAH) JUMLAH 
        , pkd.ID ID_PRTKL
        , br.BARANG BARANG_BR
        , br.ID ID_BR
        , pkd.KETERANGAN
        , dos.ID ID_RESEP
        , 1 STATUS_ID_RESEP
        , 'PEMBERIAN DOSIS' KET_STATUS_RESEP
        , dos.NO_BATCH NO_BATCH
        , dos.EXPDATE_REKONSITUSI EXPDATE_REKONSITUSI
        , dos.STABILITAS STABILITAS
        , dos.SUHU SUHU
        , dos.KONDISI_PENYIMPANAN KONDISI_PENYIMPANAN
        , dos.TANGGAL_PENYIAPAN TANGGAL_PENYIAPAN
        , dos.TANGGAL_KADALUARSA TANGGAL_KADALUARSA
        , dos.JALUR_PEMBERIAN JALUR_PEMBERIAN
        , dos.DALAM DALAM
        , dos.STATUS_CETAK_LABEL STATUS_CETAK_LABEL
        , dos.VOLUME VOLUME
        , dos.STATUS STATUS_OBAT
        FROM keperawatan.tb_prtkl_kemo_pemberian_dosis dos
        LEFT JOIN medis.tb_prtkl_kemo_detail pkd ON pkd.ID = dos.ID_PRTKL_DETAIL
        LEFT JOIN inventory.barang_ruangan br ON br.ID = dos.NAMA_OBAT
        LEFT JOIN inventory.barang b ON b.ID = br.BARANG
        LEFT JOIN master.ruangan r ON r.ID = dos.FARMASI_TUJUAN
        WHERE pkd.NOMOR_PRTKL_MEDIS='$noorder' AND dos.`STATUS`=1
        GROUP BY dos.NAMA_OBAT) UNION ALL
      (
        SELECT lpf.noorder NO_ORDER, r.DESKRIPSI RUANGAN_TUJUAN
        , r.ID ID_FARMASI_TUJUAN
        , lpf.id_barang_ruangan_pengenceran ID_BARANG_RUANGAN
        , b.NAMA NAMA_OBAT, SUM(lpf.dosis_pengenceran) DOSIS
        , SUM(lpf.jumlah_pengenceran) JUMLAH
        , lpf.id_detail_farmasi ID_PRTKL
        , br.BARANG BARANG_BR
        , br.ID ID_BR
        , NULL KETERANGAN
        , lpf.id_detail_farmasi ID_RESEP
        , 2 STATUS_ID_RESEP
        , 'PEMBERIAN ENCER' KET_STATUS_RESEP
        , lpf.no_batch NO_BATCH
        , lpf.expdate_rekonsitusi EXPDATE_REKONSITUSI
        , lpf.stabilitas STABILITAS
        , lpf.suhu SUHU
        , lpf.kondisi_penyimpanan KONDISI_PENYIMPANAN
        , lpf.tanggal_penyiapan TANGGAL_PENYIAPAN
        , lpf.tanggal_kadaluarsa TANGGAL_KADALUARSA
        , lpf.jalur_pemberian JALUR_PEMBERIAN
        , lpf.dalam DALAM
        , lpf.status_cetak_label STATUS_CETAK_LABEL
        , NULL VOLUME
        , lpf.status STATUS_OBAT
        FROM keperawatan.tb_prtkl_kemo_pemberian_encer lpf
        LEFT JOIN inventory.barang_ruangan br ON br.ID = lpf.id_barang_ruangan_pengenceran
        LEFT JOIN inventory.barang b ON b.ID = br.BARANG
        LEFT JOIN master.ruangan r ON r.ID = lpf.pilih_farmasi_tujuan
        WHERE lpf.noorder='$noorder' AND lpf.jenis='2' AND lpf.`status`=1 AND lpf.id_barang_ruangan_pengenceran IS NOT NULL
        GROUP BY lpf.id_barang_ruangan_obat)) a
      ORDER BY a.FARMASI_TUJUAN DESC"
    );
    return $query->result_array();
  }

  public function listPegawai()
  {
    $query = $this->db->query(
      "SELECT pengguna.ID, peg.NIP, master.getNamaLengkapPegawai(peg.NIP) NAMA_LENGKAP
      FROM aplikasi.pengguna pengguna
      LEFT JOIN master.pegawai peg ON pengguna.NIP = peg.NIP
      WHERE peg.NIP is NOT NULL"
    );
    return $query->result_array();
  }

  public function historyTbakFarmasiProKem($nomr)
  {
    $query = $this->db->query("CALL keperawatan.TBAKmenu($nomr)");
    $this->db->reconnect();
    return $query->result_array();
  }

  public function getArrayObat($iddetpk)
  {
    $query = $this->db->query(
      "SELECT tpkd.ID, tpkd.ID_PRTKL_DETAIL, tpkd.FARMASI_TUJUAN ID_TUJUAN, tpkd.NAMA_OBAT ID_OBAT
      , tpkd.DOSIS, tpkd.JUMLAH, r.DESKRIPSI NAMA_TUJUAN_FARMASI, b.NAMA NAMA_OBAT
      FROM keperawatan.tb_prtkl_kemo_pemberian_dosis tpkd
      LEFT JOIN master.ruangan r ON r.JENIS_KUNJUNGAN='11' AND r.ID = tpkd.FARMASI_TUJUAN
      LEFT JOIN inventory.barang_ruangan br ON br.ID = tpkd.NAMA_OBAT
      LEFT JOIN inventory.barang b ON b.ID = br.BARANG
      WHERE tpkd.ID_PRTKL_DETAIL='$iddetpk'"
    );
    return $query->result_array();
  }

  public function historyProtokolKemoFarmasi($nomr)
    {
        $query = $this->db->query(
            "SELECT p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , orp.NOMOR NO_ORDER, r.ID ID_RUANGAN,r.DESKRIPSI RUANGAN
            , pko.NAMA PAKET, orp.TANGGAL_KEMO TANGGAL_KEMO
            , orp.CREATED_AT TANGGAL_CREATE
            , orp.OLEH ID_USER, master.getNamaLengkapPegawai(peng.NIP) USER
            , orp.`STATUS`
            , IF(orp.`STATUS`=1,'Sudah tersimpan'
            , IF(orp.`STATUS`=2, 'Diterima Farmasi'
            , IF(orp.`STATUS`=3,'Final Farmasi'
            , IF(orp.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , orp.JENIS
            , orp.INSTRUKSI

            FROM medis.tb_prtkl_kemo orp

            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = orp.NOKUN
            LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            LEFT JOIN db_master.tb_protokol_kemo pko ON pko.ID = orp.PAKET
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN master.dokter dpjp ON dpjp.ID = orp.DPJP
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = orp.OLEH

            WHERE p.NORM='$nomr' AND orp.JENIS=1

            UNION

            SELECT p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , orp.NOMOR NO_ORDER, NULL ID_RUANGAN, 'Perjanjian' RUANGAN
            , pko.NAMA PAKET, orp.TANGGAL_KEMO TANGGAL_KEMO
            , orp.CREATED_AT TANGGAL_CREATE
            , orp.OLEH ID_USER, master.getNamaLengkapPegawai(peng.NIP) USER
            , orp.`STATUS`
            , IF(orp.`STATUS`=1,'Sudah tersimpan'
            , IF(orp.`STATUS`=2, 'Diterima Farmasi'
            , IF(orp.`STATUS`=3,'Final Farmasi'
            , IF(orp.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , orp.JENIS
            , orp.INSTRUKSI

            FROM medis.tb_prtkl_kemo orp

            LEFT JOIN master.pasien p ON p.NORM = orp.NOMR
            LEFT JOIN db_master.tb_protokol_kemo pko ON pko.ID = orp.PAKET
            LEFT JOIN master.dokter dpjp ON dpjp.ID = orp.DPJP
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = orp.OLEH

            WHERE p.NORM='$nomr' AND orp.JENIS=2

            GROUP BY NO_ORDER

            ORDER BY TANGGAL_CREATE DESC

            "
        );
        return $query->result_array();
    }

    public function cariPasien()
    {
      $query = $this->db->query(
        "SELECT tpk.NOMOR ID_PROTOKOL

        , tpk.NOMR
        , tpk.NOKUN
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , DATE_FORMAT(tpk.TANGGAL_KEMO,'%d-%m-%Y') TANGGAL_KEMO
        , tpv.NAMA NAMA_PAKET_PRTKLKEMO
        , tpk.INSTRUKSI
        , tpk.DIAGNOSA
        , tpk.SIKLUS
        , IF(tpk.`STATUS`=1,'Sudah tersimpan'
          , IF(tpk.`STATUS`=2, 'Diterima Farmasi'
            , IF(tpk.`STATUS`=3,'Final Farmasi'
              , IF(tpk.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO
        , (
          SELECT rua.DESKRIPSI
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          WHERE pend.`STATUS`!=0 AND penk.`STATUS`!=0 AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%') AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY) AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY) AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) RUANGAN_KEMO
        , (
          SELECT DATE_FORMAT(penk.MASUK,'%d-%m-%Y')
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          WHERE pend.`STATUS`!=0 AND penk.`STATUS`!=0 AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%') AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY) AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY) AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) TANGGAL_MSK_RUANGAN_KEMO
        , (
          SELECT IF(ore.NOMOR IS NULL, 'Belum Resep','Sudah Resep')
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          LEFT JOIN layanan.order_resep ore ON ore.KUNJUNGAN = penk.NOMOR AND ore.TUJUAN='105050105' AND ore.`STATUS`!=0
          WHERE pend.`STATUS`!=0 AND penk.`STATUS`!=0 AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%') AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY) AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY) AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) STATUS_RESEP_KEMO

        , (
          SELECT penk.NOMOR
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          WHERE pend.`STATUS`!=0 AND penk.`STATUS`!=0 AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%') AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY) AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY) AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) NOKUN_UNTUK_KEMO

        , IF(DATE(NOW())=tpk.TANGGAL_KEMO,1,2) TODAY
        , tpk.`STATUS`
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , DATE_FORMAT(tpk.CREATED_AT,'%d-%m-%Y') CREATED_AT
        FROM medis.tb_prtkl_kemo tpk
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tpk.NOKUN
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        LEFT JOIN db_pasien.tb_tb_bb tb ON tb.data_source = 30 AND tb.id = tpk.ID_TB_BB
        LEFT JOIN db_master.tb_protokol_kemo tpv ON tpv.ID = tpk.PAKET
        LEFT JOIN master.dokter dpjp ON dpjp.ID = tpk.DPJP
        WHERE tpk.`STATUS` NOT IN (3,4)
        ORDER BY TODAY ASC, tpk.TANGGAL_KEMO DESC"
      );
      return $query->result_array();
    }

    public function cariPasienFinal()
    {
      $query = $this->db->query(
        "SELECT tpk.NOMOR ID_PROTOKOL

        , tpk.NOMR
        , tpk.NOKUN
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , DATE_FORMAT(tpk.TANGGAL_KEMO,'%d-%m-%Y') TANGGAL_KEMO
        , tpv.NAMA NAMA_PAKET_PRTKLKEMO
        , tpk.INSTRUKSI
        , tpk.DIAGNOSA
        , tpk.SIKLUS
        , IF(tpk.`STATUS`=1,'Sudah tersimpan'
          , IF(tpk.`STATUS`=2, 'Diterima Farmasi'
            , IF(tpk.`STATUS`=3,'Final Farmasi'
              , IF(tpk.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO
        , (
          SELECT rua.DESKRIPSI
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          WHERE pend.`STATUS`=1 AND penk.`STATUS`!=0
AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%')
#AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY)
# AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY)
AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) RUANGAN_KEMO
        , (
          SELECT DATE_FORMAT(penk.MASUK,'%d-%m-%Y')
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          WHERE pend.`STATUS`=1 AND penk.`STATUS`!=0
AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%')
# AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY) AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY)
AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) TANGGAL_MSK_RUANGAN_KEMO
        , (
          SELECT IF(ore.NOMOR IS NULL, 'Belum Resep','Sudah Resep')
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          LEFT JOIN layanan.order_resep ore ON ore.KUNJUNGAN = penk.NOMOR AND ore.TUJUAN='105050105' AND ore.`STATUS`!=0
          WHERE pend.`STATUS`=1 AND penk.`STATUS`!=0
AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%')
#AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY) AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY)
AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) STATUS_RESEP_KEMO

        , (
          SELECT penk.NOMOR
          FROM pendaftaran.pendaftaran pend
          LEFT JOIN pendaftaran.kunjungan penk ON penk.NOPEN = pend.NOMOR
          LEFT JOIN master.ruangan rua ON rua.ID = penk.RUANGAN
          WHERE pend.`STATUS`=1 AND penk.`STATUS`!=0
AND (penk.RUANGAN LIKE '1050201%' OR penk.RUANGAN LIKE '10501%') #AND penk.MASUK BETWEEN DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL -3 DAY) AND DATE_ADD(tpk.TANGGAL_KEMO, INTERVAL +7 DAY)
AND pend.NORM=p.NORM
          ORDER BY penk.MASUK DESC
          LIMIT 1) NOKUN_UNTUK_KEMO

        , IF(DATE(NOW())=tpk.TANGGAL_KEMO,1,2) TODAY
        , tpk.`STATUS`
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , DATE_FORMAT(tpk.CREATED_AT,'%d-%m-%Y') CREATED_AT
        FROM medis.tb_prtkl_kemo tpk
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tpk.NOKUN
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        LEFT JOIN db_pasien.tb_tb_bb tb ON tb.data_source = 30 AND tb.id = tpk.ID_TB_BB
        LEFT JOIN db_master.tb_protokol_kemo tpv ON tpv.ID = tpk.PAKET
        LEFT JOIN master.dokter dpjp ON dpjp.ID = tpk.DPJP
        WHERE tpk.`STATUS`=3
        ORDER BY TODAY ASC, tpk.TANGGAL_KEMO DESC"
      );
      return $query->result_array();
    }

    public function paketProKem($nomr, $noorder)
    {
        $query = $this->db->query(
            "SELECT p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , orp.NOMOR NO_ORDER, r.ID ID_RUANGAN,r.DESKRIPSI RUANGAN
            , pko.NAMA PAKET, orp.TANGGAL_KEMO TANGGAL_KEMO
            , orp.CREATED_AT TANGGAL_CREATE
            , orp.OLEH ID_USER, master.getNamaLengkapPegawai(peng.NIP) USER
            , orp.`STATUS`
            , IF(orp.`STATUS`=1,'Sudah tersimpan'
            , IF(orp.`STATUS`=2, 'Diterima Farmasi'
            , IF(orp.`STATUS`=3,'Final Farmasi'
            , IF(orp.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , orp.JENIS
            , orp.INSTRUKSI
            , orp.DIAGNOSA, orp.SIKLUS, tbbt.tb TINGGI_BADAN, tbbt.bb BERAT_BADAN, tbbt.lpb LPB, tbbt.lpb_2 LPB_2
            , orp.PROTOKOL_TERIMA, orp.OBAT_SELESAI, orp.OBAT_DITERIMA, orp.FARMASI_1, orp.FARMASI_2, orp.PERAWAT, orp.VERIF_FARMASI, orp.TANGGAL_VERIF_FARMASI
            , orp.EGFR EGFR

            FROM medis.tb_prtkl_kemo orp

            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = orp.NOKUN
            LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            LEFT JOIN db_master.tb_protokol_kemo pko ON pko.ID = orp.PAKET
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN master.dokter dpjp ON dpjp.ID = orp.DPJP
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = orp.OLEH
            LEFT JOIN db_pasien.tb_tb_bb tbbt ON tbbt.ref = orp.NOMOR

            WHERE p.NORM='$nomr' AND orp.JENIS=1 AND orp.NOMOR='$noorder'

            UNION

            SELECT p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , orp.NOMOR NO_ORDER, NULL ID_RUANGAN, 'Perjanjian' RUANGAN
            , pko.NAMA PAKET, orp.TANGGAL_KEMO TANGGAL_KEMO
            , orp.CREATED_AT TANGGAL_CREATE
            , orp.OLEH ID_USER, master.getNamaLengkapPegawai(peng.NIP) USER
            , orp.`STATUS`
            , IF(orp.`STATUS`=1,'Sudah tersimpan'
            , IF(orp.`STATUS`=2, 'Diterima Farmasi'
            , IF(orp.`STATUS`=3,'Final Farmasi'
            , IF(orp.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , orp.JENIS
            , orp.INSTRUKSI
            , orp.DIAGNOSA, orp.SIKLUS, tbbt.tb TINGGI_BADAN, tbbt.bb BERAT_BADAN, tbbt.lpb LPB, tbbt.lpb_2 LPB_2
            , orp.PROTOKOL_TERIMA, orp.OBAT_SELESAI, orp.OBAT_DITERIMA, orp.FARMASI_1, orp.FARMASI_2, orp.PERAWAT, orp.VERIF_FARMASI, orp.TANGGAL_VERIF_FARMASI
            , orp.EGFR EGFR

            FROM medis.tb_prtkl_kemo orp

            LEFT JOIN master.pasien p ON p.NORM = orp.NOMR
            LEFT JOIN db_master.tb_protokol_kemo pko ON pko.ID = orp.PAKET
            LEFT JOIN master.dokter dpjp ON dpjp.ID = orp.DPJP
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = orp.OLEH
            LEFT JOIN db_pasien.tb_tb_bb tbbt ON tbbt.ref = orp.NOMOR

            WHERE p.NORM='$nomr' AND orp.JENIS=2 AND orp.NOMOR='$noorder'

            GROUP BY NO_ORDER

            ORDER BY TANGGAL_CREATE DESC

            "
        );
        return $query->row_array();
    }

  public function dataDetailProKemFarmasi($noorder)
  {
    $query = $this->db->query(
      "SELECT
      pas.NORM, master.getNamaLengkap(pas.NORM) NAMA_PASIEN
      , CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y')) TANGGAL_LAHIR
      , 'Perjanjian' RUANGAN_PERMINTAAN, master.getNamaLengkapPegawai(dpjp.NIP) DPJP
      , pk.NOMOR NOMOR_PROTOKOL, pk.TANGGAL_KEMO, pk.PAKET ID_PAKET, pro.NAMA NAMA_PAKET, pk.INSTRUKSI INSTRUKSI_DOKTER
      , pk.OLEH ID_USER, master.getNamaLengkapPegawai(peng.NIP) NAMA_USER, pk.CREATED_AT TANGGAL_PEMBUATAN
      , pk.`STATUS`
      , IF(pk.`STATUS`=1,'Sudah tersimpan'
      , IF(pk.`STATUS`=2, 'Diterima Farmasi'
      , IF(pk.`STATUS`=3,'Final Farmasi'
      , IF(pk.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO

      , pkd.ID ID_PRTKL_KEMO_DETAIL_MEDIS, pkd.ID_PROTOKOL_KEMO_DETAIL ID_MASTER_PROTOKOL
      , mpkd.OBAT_PROTOKOL ID_OBAT_PROTOKOL, op.NAMA_OBAT, IF(pkd.SEDIAAN = '', op.SEDIAAN, pkd.SEDIAAN) SEDIAAN
      , mpkd.DOSIS_PROTOKOL DOSIS_MASTER
      , pkd.DOSIS_PROTOKOL DOSIS_OLEH_DOKTER
      , mpkd.JENIS ID_JENIS
      , IF(mpkd.JENIS=1,'Default',IF(mpkd.JENIS=2,'Premedikasi','Bilas')) JENIS_DESK
      , mpkd.PENGENCERAN ID_OBAT_PENGENCERAN, opp.NAMA_OBAT OBAT_PENGENCERAN, opp.SEDIAAN SEDIAAN_PENGENCER
      , mpkd.DOSIS_PENGENCERAN DOSIS_PENGENCERAN_MASTER
      , pkd.DOSIS_PENGENCERAN DOSIS_PENGENCERAN_OLEH_DOKTER
      , aks.variabel AKSES_PEMBERIAN
      , mpkd.KECEPATAN
      , pkd.KETERANGAN KETERANGAN_DOKTER
      , pkd.`STATUS` STATUS_PRTKL_DETAIL_DOKTER
      , pkp.JAM_MULAI, pkp.JAM_SELESAI, pkp.KETERANGAN KETERANGAN_PERAWAT
      , pkp.OLEH ID_USER_PRWT, master.getNamaLengkapPegawai(pengp.NIP) NAMA_USER_PRWT
      , pkp.CREATED_AT TANGGAL_INPUT, pkp.`STATUS` STATUS_PRWT
      , IF(pk.`STATUS`=1, DATE(NOW()), plo.TANGGAL_KEMOTERAPI) TANGGAL_REAL_KEMO
      , IF(pkp.ID IS NULL, 0,1) STATUS_CHECKLIST_AFTERFINAL
      , tpkpe.no_batch NO_BATCH, tpkpe.stabilitas STABILITAS
      , pkd.KETERANGAN_FARMASI KETERANGAN_FARMASI

      FROM medis.tb_prtkl_kemo pk

      LEFT JOIN medis.tb_prtkl_kemo_detail pkd ON pkd.NOMOR_PRTKL_MEDIS = pk.NOMOR AND pkd.STATUS='1'
      LEFT JOIN db_master.tb_protokol_kemo pro ON pro.ID = pk.PAKET
      LEFT JOIN db_master.tb_protokol_kemo_detil mpkd ON mpkd.ID = pkd.ID_PROTOKOL_KEMO_DETAIL
      LEFT JOIN db_master.tb_obat_protokol op ON op.ID = mpkd.OBAT_PROTOKOL
      LEFT JOIN db_master.tb_obat_protokol opp ON opp.ID = mpkd.PENGENCERAN
      LEFT JOIN master.pasien pas ON pas.NORM = pk.NOMR
      LEFT JOIN master.dokter dpjp ON dpjp.ID = pk.DPJP
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = pk.OLEH
      LEFT JOIN db_master.variabel aks ON aks.id_variabel = mpkd.AKSES_PEMBERIAN
      LEFT JOIN keperawatan.tb_prtkl_kemo_pemberian pkp ON pkp.ID_PRTKL_DETAIL = pkd.ID
      LEFT JOIN aplikasi.pengguna pengp ON pengp.ID = pkp.OLEH
      LEFT JOIN keperawatan.tb_prtkl_kemo_log plo ON plo.NOMOR = pk.NOMOR
      LEFT JOIN keperawatan.tb_prtkl_kemo_pemberian_encer tpkpe ON tpkpe.id_detail_farmasi = pkd.ID

      WHERE pk.NOMOR='$noorder'

      GROUP BY pkd.ID

      ORDER BY mpkd.ID ASC
      "
    );
    return $query->result_array();
  }

  public function dataDetailInputDosisFarmasi($noorder, $iddetpk)
  {
    $query = $this->db->query(
      "SELECT
      pas.NORM, master.getNamaLengkap(pas.NORM) NAMA_PASIEN
      , CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y')) TANGGAL_LAHIR
      , 'Perjanjian' RUANGAN_PERMINTAAN, master.getNamaLengkapPegawai(dpjp.NIP) DPJP
      , pk.NOMOR NOMOR_PROTOKOL, pk.TANGGAL_KEMO, pk.PAKET ID_PAKET, pro.NAMA NAMA_PAKET, pk.INSTRUKSI INSTRUKSI_DOKTER
      , pk.OLEH ID_USER, master.getNamaLengkapPegawai(peng.NIP) NAMA_USER, pk.CREATED_AT TANGGAL_PEMBUATAN
      , pk.`STATUS`
      , IF(pk.`STATUS`=1,'Sudah tersimpan'
      , IF(pk.`STATUS`=2, 'Diterima Farmasi'
      , IF(pk.`STATUS`=3,'Final Farmasi'
      , IF(pk.`STATUS`=4,'Final Perawat','Di batalkan')))) STATUS_PROTO

      , pkd.ID ID_PRTKL_KEMO_DETAIL_MEDIS, pkd.ID_PROTOKOL_KEMO_DETAIL ID_MASTER_PROTOKOL
      , mpkd.OBAT_PROTOKOL ID_OBAT_PROTOKOL, op.NAMA_OBAT, op.SEDIAAN
      , mpkd.DOSIS_PROTOKOL DOSIS_MASTER
      , pkd.DOSIS_PROTOKOL DOSIS_OLEH_DOKTER
      , mpkd.JENIS ID_JENIS
      , IF(mpkd.JENIS=1,'Default',IF(mpkd.JENIS=2,'Premedikasi','Bilas')) JENIS_DESK
      , mpkd.PENGENCERAN ID_OBAT_PENGENCERAN, opp.NAMA_OBAT OBAT_PENGENCERAN, opp.SEDIAAN SEDIAAN_PENGENCER
      , mpkd.DOSIS_PENGENCERAN DOSIS_PENGENCERAN_MASTER
      , pkd.DOSIS_PENGENCERAN DOSIS_PENGENCERAN_OLEH_DOKTER
      , aks.variabel AKSES_PEMBERIAN
      , mpkd.KECEPATAN
      , pkd.KETERANGAN KETERANGAN_DOKTER
      , pkd.`STATUS` STATUS_PRTKL_DETAIL_DOKTER
      , pkp.JAM_MULAI, pkp.JAM_SELESAI, pkp.KETERANGAN KETERANGAN_PERAWAT
      , pkp.OLEH ID_USER_PRWT, master.getNamaLengkapPegawai(pengp.NIP) NAMA_USER_PRWT
      , pkp.CREATED_AT TANGGAL_INPUT, pkp.`STATUS` STATUS_PRWT
      , pk.`STATUS` STATUS_PROTO
      , IF(pk.`STATUS`=1, DATE(NOW()), plo.TANGGAL_KEMOTERAPI) TANGGAL_REAL_KEMO
      , IF(pkp.ID IS NULL, 0,1) STATUS_CHECKLIST_AFTERFINAL
      , IF(pkd.DOSIS_PENGENCERAN>0, 2,1) JENIS_DOSIS
      , pk.NOKUN


      FROM medis.tb_prtkl_kemo pk

      LEFT JOIN medis.tb_prtkl_kemo_detail pkd ON pkd.NOMOR_PRTKL_MEDIS = pk.NOMOR
      LEFT JOIN db_master.tb_protokol_kemo pro ON pro.ID = pk.PAKET
      LEFT JOIN db_master.tb_protokol_kemo_detil mpkd ON mpkd.ID = pkd.ID_PROTOKOL_KEMO_DETAIL
      LEFT JOIN db_master.tb_obat_protokol op ON op.ID = mpkd.OBAT_PROTOKOL
      LEFT JOIN db_master.tb_obat_protokol opp ON opp.ID = mpkd.PENGENCERAN
      LEFT JOIN master.pasien pas ON pas.NORM = pk.NOMR
      LEFT JOIN master.dokter dpjp ON dpjp.ID = pk.DPJP
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = pk.OLEH
      LEFT JOIN db_master.variabel aks ON aks.id_variabel = mpkd.AKSES_PEMBERIAN
      LEFT JOIN keperawatan.tb_prtkl_kemo_pemberian pkp ON pkp.ID_PRTKL_DETAIL = pkd.ID
      LEFT JOIN aplikasi.pengguna pengp ON pengp.ID = pkp.OLEH
      LEFT JOIN keperawatan.tb_prtkl_kemo_log plo ON plo.NOMOR = pk.NOMOR

      WHERE pk.NOMOR='$noorder' AND pkd.ID='$iddetpk'

      GROUP BY pkd.ID

      ORDER BY mpkd.ID ASC
      "
    );
    return $query->row_array();
  }

}

/* End of file ProfileModel.php */
/* Location: ./application/models/ProfileModel.php */
