<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PTKemoRhabdomiosarkoma extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','informedConsent/PTKemoRhabdomiosarkomaModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      //PTKemoRhabdomiosarkoma
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'DiagnosisWDDDPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(974),
      'DasarDiagnosisPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(975),
      'TindakanKedokteranPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(976),
      'IndikasiTindakanPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(977),
      'TataCaraPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(978),
      'TujuanPengobatanPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(979),
      'RisikoPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(980),
      'KomplikasiPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(981),
      'PrognosisPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(982),
      'AlternatifPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(983),
      'LainLainPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(984),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );

    // print_r($data);exit();
    $this->load->view('Pengkajian/informedConsent/PTKemoRhabdomiosarkoma/index', $data);
  }

  public function simpanPTKemoRhabdomiosarkoma()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $date = $this->input->post('datePickerDomioSarkoma');
    $tglPersetujuanDomioSarkoma = date('Y-m-d H:i', strtotime($date));

    $dataInformedConcent = array (
      'nokun'                  => $post['nokun'],
      'jenis_informed_consent' => 3032,
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanPTKemoRhabdomiosarkoma'],
      'penerima_informasi'     => $post['penerimaInformasiPTKemoRhabdomiosarkoma'],
      'oleh'                   => $this->session->userdata('id'),
    );

    $idInformedConcent = $this->PTKemoRhabdomiosarkomaModel->simpanInformedConcent($dataInformedConcent);

    $dataPTKemoRhabdomiosarkoma = array (
      'id_informed_consent'              => $idInformedConcent,
      'diagnosis_wd_dd'                  => implode(',',$post["DiagnosisWDDDPTKemoRhabdomiosarkoma"]),
      'dasar_diagnosis'                  => implode(',',$post["DasarDiagnosisPTKemoRhabdomiosarkoma"]),
      'tindakan_kedokteran'              => implode(',',$post["TindakanKedokteranPTKemoRhabdomiosarkoma"]),
      'indikasi_tindakan'                => implode(',',$post["IndikasiTindakanPTKemoRhabdomiosarkoma"]),
      'tata_cara'                        => implode(',',$post["TataCaraPTKemoRhabdomiosarkoma"]),
      'tujuan_tindakan'                  => $post["TujuanTindakanPTKemoRhabdomiosarkoma"],
      'tujuan_pengobatan'                => implode(',',$post["TujuanPengobatanPTKemoRhabdomiosarkoma"]),
      'risiko'                           => implode(',',$post["RisikoPTKemoRhabdomiosarkoma"]),
      'komplikasi'                       => implode(',',$post["KomplikasiPTKemoRhabdomiosarkoma"]),
      'prognosis'                        => implode(',',$post["PrognosisPTKemoRhabdomiosarkoma"]),
      'alternatif'                       => implode(',',$post["AlternatifPTKemoRhabdomiosarkoma"]),
      'lain_lain'                        => implode(',',$post["LainLainPTKemoRhabdomiosarkoma"]),
    );

    $this->PTKemoRhabdomiosarkomaModel->simpanPTKemoRhabdomiosarkoma($dataPTKemoRhabdomiosarkoma);

    $dataPersetujuanDomioSarkoma = array(
      'id_informed_consent'        => $idInformedConcent,
      'nama_keluarga'              => $post['namaDomioSarkoma'],
      'umur_keluarga'              => $post['umurDomioSarkoma'],
      'jk_keluarga'                => $post['jenis_kelaminDomioSarkoma'],
      'alamat_keluarga'            => $post['alamatDomioSarkoma'],
      'tindakan'                   => $post['tindakanDomioSarkoma'],
      'hub_keluarga_dgn_pasien'    => $post['hubunganDomioSarkoma'],
      'tanggal_persetujuan'        => $tglPersetujuanDomioSarkoma,
      'ttd_menyatakan'             => file_get_contents($this->input->post('signMenyatakanDomioSarkoma')),
      'ttd_saksi_keluarga'         => file_get_contents($this->input->post('signKeluargaDomioSarkoma')),
      'ttd_saksi_rumah_sakit'      => file_get_contents($this->input->post('signRumahSakitDomioSarkoma')),
      'saksi_keluarga'             => $post['nama_keluarga'],
      'saksi_rumah_sakit'          => $post['nama_saksi_rs']
    );

    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran',$dataPersetujuanDomioSarkoma);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyPTKemoRhabdomiosarkoma()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listPTKemoRhabdomiosarkoma = $this->PTKemoRhabdomiosarkomaModel->listHistoryInformedConsentPTKemoRhabdomiosarkoma($nomr);

    $data = array();
    $no = 1;
    foreach ($listPTKemoRhabdomiosarkoma->result() as $PTKemoRhabdomiosarkoma) {

      $data[] = array(
        $no,
        $PTKemoRhabdomiosarkoma->nokun,
        $PTKemoRhabdomiosarkoma->DOKTERPELAKSANA,
        $PTKemoRhabdomiosarkoma->OLEH,
        date("d-m-Y H:i:s",strtotime($PTKemoRhabdomiosarkoma->tanggal)),
        '<a href="#modalPTKemoRhabdomiosarkoma" class="btn btn-primary btn-block" data-id="'.$PTKemoRhabdomiosarkoma->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fas fa-edit"></i> Edit</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listPTKemoRhabdomiosarkoma->num_rows(),
      "recordsFiltered" => $listPTKemoRhabdomiosarkoma->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalPTKemoRhabdomiosarkoma()
  {
    $id = $this->input->post('id');
    $gptkr = $this->PTKemoRhabdomiosarkomaModel->getPTKemoRhabdomiosarkoma($id);
    $explode_diagnosis_wd_dd       = explode(',' , $gptkr['diagnosis_wd_dd']);
    $explode_dasar_diagnosis       = explode(',' , $gptkr['dasar_diagnosis']);
    $explode_tindakan_kedokteran   = explode(',' , $gptkr['tindakan_kedokteran']);
    $explode_indikasi_tindakan     = explode(',' , $gptkr['indikasi_tindakan']);
    $explode_tata_cara             = explode(',' , $gptkr['tata_cara']);
    $tujuan_tindakan               = $gptkr['tujuan_tindakan'];
    $explode_tujuan_pengobatan     = explode(',' , $gptkr['tujuan_pengobatan']);
    $explode_risiko                = explode(',' , $gptkr['risiko']);
    $explode_komplikasi            = explode(',' , $gptkr['komplikasi']);
    $explode_prognosis             = explode(',' , $gptkr['prognosis']);
    $explode_alternatif            = explode(',' , $gptkr['alternatif']);
    $explode_lain_lain             = explode(',' , $gptkr['lain_lain']);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'gptkr' => $gptkr,
      'explode_diagnosis_wd_dd'       => $explode_diagnosis_wd_dd,
      'explode_dasar_diagnosis'       => $explode_dasar_diagnosis,
      'explode_tindakan_kedokteran'   => $explode_tindakan_kedokteran,
      'explode_indikasi_tindakan'     => $explode_indikasi_tindakan,
      'explode_tata_cara'             => $explode_tata_cara,
      'tujuan_tindakan'               => $tujuan_tindakan,
      'explode_tujuan_pengobatan'     => $explode_tujuan_pengobatan,
      'explode_risiko'                => $explode_risiko,
      'explode_komplikasi'            => $explode_komplikasi,
      'explode_prognosis'             => $explode_prognosis,
      'explode_alternatif'           => $explode_alternatif,
      'explode_lain_lain'             => $explode_lain_lain,
      // Informed Consent
      'DiagnosisWDDDPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(974),
      'DasarDiagnosisPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(975),
      'TindakanKedokteranPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(976),
      'IndikasiTindakanPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(977),
      'TataCaraPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(978),
      'TujuanPengobatanPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(979),
      'RisikoPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(980),
      'KomplikasiPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(981),
      'PrognosisPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(982),
      'AlternatifPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(983),
      'LainLainPTKemoRhabdomiosarkoma' => $this->masterModel->referensi(984),
      'listDrUmum' => $this->masterModel->listDrUmum(),
    );

    $this->load->view('Pengkajian/informedConsent/PTKemoRhabdomiosarkoma/view_edit', $data);
  }

  public function updatePTKemoRhabdomiosarkoma()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    $idtkr = $this->input->post('idtkr');
    $post = $this->input->post();

    $dataInformedConcent = array (
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanPTKemoRhabdomiosarkoma_edit'],
      'penerima_informasi'     => $post['penerimaInformasiPTKemoRhabdomiosarkoma_edit'],
    );

    $this->PTKemoRhabdomiosarkomaModel->updateInformedConcent($dataInformedConcent,$id);

    $dataPTKemoRhabdomiosarkoma_edit = array (
      'diagnosis_wd_dd'                  => implode(',',$post["DiagnosisWDDDPTKemoRhabdomiosarkoma_edit"]),
      'dasar_diagnosis'                  => implode(',',$post["DasarDiagnosisPTKemoRhabdomiosarkoma_edit"]),
      'tindakan_kedokteran'              => implode(',',$post["TindakanKedokteranPTKemoRhabdomiosarkoma_edit"]),
      'indikasi_tindakan'                => implode(',',$post["IndikasiTindakanPTKemoRhabdomiosarkoma_edit"]),
      'tata_cara'                        => implode(',',$post["TataCaraPTKemoRhabdomiosarkoma_edit"]),
      'tujuan_tindakan'                  => $post["TujuanTindakanPTKemoRhabdomiosarkoma_edit"],
      'tujuan_pengobatan'                => implode(',',$post["TujuanPengobatanPTKemoRhabdomiosarkoma_edit"]),
      'risiko'                           => implode(',',$post["RisikoPTKemoRhabdomiosarkoma_edit"]),
      'komplikasi'                       => implode(',',$post["KomplikasiPTKemoRhabdomiosarkoma_edit"]),
      'prognosis'                        => implode(',',$post["PrognosisPTKemoRhabdomiosarkoma_edit"]),
      'alternatif'                       => implode(',',$post["AlternatifPTKemoRhabdomiosarkoma_edit"]),
      'lain_lain'                        => implode(',',$post["LainLainPTKemoRhabdomiosarkoma_edit"]),
    );

    // print_r($dataPTKemoRhabdomiosarkoma_edit);exit();

    $this->PTKemoRhabdomiosarkomaModel->updatePTKemoRhabdomiosarkoma($dataPTKemoRhabdomiosarkoma_edit,$idtkr);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}

/* End of file PTKemoRhabdomiosarkoma.php */
/* Location: ./application/controllers/informedConsent/PTKemoRhabdomiosarkoma.php */
