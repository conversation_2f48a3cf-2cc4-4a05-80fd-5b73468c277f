<?php
defined('BASEPATH') or exit('No direct script access allowed');
class UploadRM extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/scanberkas/ScanModel',
      'rekam_medis/uploadRMModel'
    ));
  }

  public function index()
  {
    $data = array(
      'title' => 'Halaman View All Pengkajian',
      'isi' => 'rekam_medis/uploadRM/index',
    );

    $this->load->view('layout/wrapper', $data);
  }

  function cekBerkas(){
    $post = $this->input->post();
    $norm = $post['NORM'];
    $tanggal = date("dmY",strtotime($post['TANGGAL']));
    $file = $post['JENIS'].".pdf";
    $cekBerkas = $this->uploadRMModel->cekBerkas($norm,$tanggal,$file);

    if($cekBerkas->num_rows() == 0){
      $result = array('status' => 'empty');
    }else{
      $result = array('status' => 'exist');
    }
    echo json_encode($result);
  }

    function getJenisBerkas()
  	{
      $result = $this->masterModel->jenisBerkasRM();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID'];
          $sub_array['text'] = $row['NAMA'];
          $data[] = $sub_array;
      }
      // $output = array(
      //     "item" -> $data
      // );
      echo json_encode($data);
  	}

    function getJenisBerkas2($jenis)
  	{
      $data = array();
      if($jenis){
        $char = range('A', 'Z');
        $result = $this->masterModel->jenisBerkasRM2($jenis);
        $childs = array();
        $id_kategori="";
        $no=0;
        foreach ($result as $row) {
          if($id_kategori==""){
            $id_kategori=$row['id_kategori'];
          }
          if($id_kategori!=$row['id_kategori']){
            $id_kategori=$row['id_kategori'];
            $no++;
          }
          $sub_array = array();
          $sub_array['text'] = $char[$no].'. '.$row['nama_kategori'];
          $child['id'] = $row['id'];
          $child['text'] = $row['nama_berkas'];
          $childs[$row['id_kategori']][] = $child;

          // if($id_kategori!=$row['id_kategori']){
            // $id_kategori=$row['id_kategori'];
          $sub_array['children'] = $childs[$row['id_kategori']];
            // $childs = array();

          $data[$row['id_kategori']] = $sub_array;
            // }
          
        }
      }
      $data = array_values($data);
      // $output = array(
      //     "item" -> $data
      // );
      echo json_encode($data);
  	}

  public function uploadFileRM()
  {
    $config['upload_path']="../../upload_emr/scan_rekam_medis2/".$this->input->post('norm')."/".date("dmY",strtotime($this->input->post('tanggalRm')));
    $config['allowed_types']='pdf';
    $config['file_name'] = $this->input->post('jenisBerkas').".pdf";

    $this->load->library('upload');
    $this->upload->initialize($config);

    if(!$this->upload->do_upload("uploadFileEMR")){
      $error = array('error' => $this->upload->display_errors(), 'path' => $this->upload->data());
      echo"<pre>";print_r($error);exit();
    //   redirect('rekam_medis/uploadRM/errorUploadFileRM','refresh');
    }else{

      $result = $this->upload->data();
      $data = array(
        'NOMR'              =>  $this->input->post('norm'),
        'LOKASI'            =>  '/upload_emr/scan_rekam_medis2/',
        'FOLDER'            => $this->input->post('norm'),
        'SUBFOLDER'         => date("dmY",strtotime($this->input->post('tanggalRm'))),
        'FILE'              => $result['file_name'],
        'STATUS'            => 1,
      );
      $this->db->insert('db_layanan.tb_scan_berkas', $data);
      $this->session->set_flashdata('success', 'Berhasil');
      redirect('rekam_medis/uploadRM');
    }
  }

    function simpanBerkas(){
      $post = $this->input->post();

      $this->db->trans_begin();
      $berkas = array();
		  $files = $_FILES;
    	$cpt = count($_FILES['uploadBerkas']['name']);

      for($i=0; $i<$cpt; $i++)
      {           
        $_FILES['uploadBerkas']['name']= $files['uploadBerkas']['name'][$i];
        $_FILES['uploadBerkas']['type']= $files['uploadBerkas']['type'][$i];
        $_FILES['uploadBerkas']['tmp_name']= $files['uploadBerkas']['tmp_name'][$i];
        $_FILES['uploadBerkas']['error']= $files['uploadBerkas']['error'][$i];
        $_FILES['uploadBerkas']['size']= $files['uploadBerkas']['size'][$i];   
      	

      $directory = "../upload_emr/scan_rekam_medis2/".$post['normBerkas'][$i]."/".date("dmY",strtotime($post['tanggalBerkas'][$i]));
      if (!file_exists($directory)) {
        mkdir($directory, 0777, true);
      }

      $config['upload_path'] = $directory."/";
      $config['allowed_types'] = 'pdf'; //extension yang diperbolehkan untuk diupload
      $config['file_name'] = $post['jenisBerkasEMR'][$i].".pdf";

      $this->upload->initialize($config); //meng set config yang sudah di atur
      
      if($this->upload->do_upload('uploadBerkas')){
        $berkas[] = $this->upload->data();
        $data = array(
          'NOMR'              =>  $post['normBerkas'][$i],
          'LOKASI'            =>  '/upload_emr/scan_rekam_medis2/',
          'FOLDER'            => $post['normBerkas'][$i],
          'SUBFOLDER'         => date("dmY",strtotime($post['tanggalBerkas'][$i])),
          'FILE'              => $berkas[$i]['file_name'],
          'STATUS'            => 1,
        );
        $this->db->insert('db_layanan.tb_scan_berkas', $data);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed', 'error' => $this->upload->display_errors(), 'info' => $this->upload->data());
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

      }else{
        $result = array('status' => 'noupload', 'error' => $this->upload->display_errors(), 'info' => $this->upload->data());
      }
    }
        echo json_encode($result);
    
	  }

  function formUpload(){
    $data = $this->input->post();
    $this->load->view('rekam_medis/uploadRM/berkaspasien', $data);
  }

  function formUploadPerJenis(){
    $data = $this->input->post();
    $this->load->view('rekam_medis/uploadRM/formUploadPerJenis', $data);
  }

  function formAddHistory(){
    $data = $this->input->post();
    // $$norm=$params['params']['nomr'];
    // $data['params'] = $this->ScanModel->getdokter($params);
    $data['namapasien'] = $this->masterModel->getPasien($data['nomr']);
    $data['dokter'] = $this->masterModel->listDr();
    // var_dump($data);exit;
    $this->load->view('rekam_medis/uploadRM/formAddHistory', $data);
  }

  function actAddHistory(){
    $result = array();
    $data = $this->input->post();
    $return = $this->ScanModel->actaddhistory($data);
    // var_dump($return);exit;
    if($return===true){
      $result["message"] = 'Data history berhasil ditambah';
      $result["success"] = '1';
    }
    else if($return=='ada'){
      $result["message"] = 'Data pendaftaran tanggal '.substr($data['tanggal'],0,10).' sudah ada';
      $result["success"] = '0';
    }else{
      $result["message"] = 'Gagal Tambah';
      $result["success"] = '0';
    }
    echo json_encode($result);
    // $this->load->view('rekam_medis/uploadRM/formAddHistory', $data);
  }

  function simpanBerkas2(){
    $iduser=$this->session->userdata("id");
  
    $jenisberkasid=isset($_POST['jenisberkasid'])?$_POST['jenisberkasid']:null;
    $jenisberkas=isset($_POST['jenisberkas'])?$_POST['jenisberkas']:null;
    $param=isset($_POST['param'])?(array)json_decode($_POST['param']):null;

    $result = array();
    if(isset($_FILES["filepasien"]) && $iduser && $jenisberkasid && $jenisberkas && $param){

        $error =$_FILES["filepasien"]["error"];
        $file_type = $_FILES['filepasien']['type'];
        $allowed = array("application/pdf");
        $maksimal=10485760 * 2;
        if(!in_array($file_type, $allowed)) {
            $result["message"] = 'hanya file dengan pdf yang boleh diupload.';
            $result["sukses"] = '0';

        }else if($_FILES['filepasien']['size'] > $maksimal) { //10 MB
          $result["message"] = 'Size File '.$this->formatBytes($_FILES['filepasien']['size']).', maksimal '.$this->formatBytes($maksimal);
          $result["sukses"] = '0';
         
        }else{

          $pattern = '#\(.*?\)#';

          $fileName = strtok($jenisberkas, ':');
          $fileName =  preg_replace('/[^a-zA-Z0-9_ -]/s','',preg_replace($pattern, '', $fileName));
          $fileName =  strtolower(str_replace(" ","_",trim($fileName)));
          // echo $fileName;exit;


          $lokasi = "upload_emr/scan_rekam_medis2/mr_".$param["nomr"]."/nopen_".$param["nopen"];
          $directory = "../".$lokasi;
          if (!file_exists($directory)) {
            mkdir($directory, 0777, true);
          }
          $nameori = $_FILES["filepasien"]["name"];
          $ext=pathinfo($nameori, PATHINFO_EXTENSION);
          $fileName = $param["nomr"]."_".$fileName.".".$ext;
          $lokasipath=$directory."/";

          $config['upload_path'] = $lokasipath;
          $config['allowed_types'] = 'pdf';
          $config['file_name'] = $fileName;
          $config['overwrite'] = true;

          $getdata = (array) $this->ScanModel->getberkas($param[nomr],$param[nopen],$fileName);
          if(count($getdata)>0 && $getdata["modif"]==0){
              $result["message"] = 'dokumen <b><u>'.$jenisberkas.'</u></b> sudah ada dan tidak bisa diubah';
              $result["sukses"] = '0';
          }else{

            $this->upload->initialize($config);
            
            if($this->upload->do_upload('filepasien')){
              $stquery ="INSERT INTO
                    db_layanan.tb_scan_berkas2 (nomr,nopen,lokasi,namafile,idjenis_berkas,created_by,deleted_at,deleted_by) 
                    VALUES ('$param[nomr]','$param[nopen]','$lokasipath','$fileName','$jenisberkasid','$iduser',NULL,NULL) 
                    ON DUPLICATE KEY UPDATE lokasi = VALUES(lokasi), namafile = VALUES(namafile), created_at = now(), updated_at = now(), updated_by = '$iduser', deleted_at = VALUES(deleted_at), deleted_by = VALUES(deleted_by)";
              // echo  $stquery;
              $this->db->query($stquery);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result["message"] = 'Gagal Insert';
                $result["sukses"] = '0';
                // $result = array('status' => 'failed', 'error' => $this->upload->display_errors(), 'info' => $this->upload->data());
              } else {
                $this->db->trans_commit();
                $result["file"]= $fileName;
                $result["sukses"]= '1';
                // $result = array('status' => 'success');
              }

            }else{
              $result["message"] = 'Gagal Upload';
              $result["sukses"] = '0';
              // $result = array('status' => 'noupload', 'error' => $this->upload->display_errors(), 'info' => $this->upload->data());
            }
        }

        }
      }
    echo json_encode($result);
    // var_dump($param);
    // var_dump($_POST);
  }

  private function formatBytes($value) {
    if($value < 1024) {
        return $value . " bytes";
    }
    else if($value < 1024000) {
        return round(($value / 1024 ), 1) . "k";
    }
    else {
        return round(($value / 1024000), 1) . "MB";
    }

}
  function get_histori_pasien(){
    $norm = $this->input->post('norm');
    $data['namapasien'] = $this->masterModel->getPasien($norm);
    $data['listscan'] = $this->ScanModel->gethistori_pasien($norm);
    $data['listscanold'] = $this->ScanModel->gethistori_pasienold($norm);
    echo json_encode($data);
  }

  function get_berkas_pasien($norm="",$nopen=""){
    $ret= array();
    $listScan = $this->ScanModel->getberkas($norm,$nopen);
    // while ($data = mysqli_fetch_array($qrar)) {
      foreach ($listScan as $key => $data) {
      $details['name']=$data["namafile"];
        $filePath=$data["lokasi"].$data["namafile"];
        $details['size']=@filesize($filePath);

      $details['jenis']=$data["jenis"];
      $details['modif']=$data["modif"];
      $ret[] = $details;
    }
    echo json_encode($ret);
  }

  function showfile($norm="",$nopen="",$nmfile=""){
    
    $getdata = (array) $this->ScanModel->getberkas($norm,$nopen,$nmfile);
    if(count($getdata)>0){
        $lokasi=$getdata["lokasi"];
        $getfile=$getdata["namafile"];
            $file = $lokasi.$getfile;

            // $fileName=$userid.substr($fileName, strpos($fileName,"_") );
            // $file = "../../berkas/".$fileName;
            if (file_exists($file)) {
              
                    header("Content-type: application/pdf");
               
                readfile($file);
                exit;
            }else{
                echo "File tidak ditemukan";
            }
          }
  }

  function del_berkas_pasien(){
    $ret= array();
    $iduser=$this->session->userdata("id");
    $filep = $this->input->post('filep');
    $norm = $this->input->post('nomr');
    $nopen = $this->input->post('nopen');

    // $getdata = (array) $this->ScanModel->getberkas($norm,$nopen,$filep);
    // if(count($getdata)>0){
    $delfile = $this->ScanModel->delberkas($filep,$norm,$nopen,$iduser);
    // var_dump($getdata);
      if($delfile>0){
        // $filePath = "../".$getdata["lokasi"]. $filep;
        // if (file_exists($filePath)) 
        // {
        //   unlink($filePath);
        // }
        $ret["sukses"]= '1';
      }else{
        $ret["message"] = 'Gagal Hapus';
        $ret["sukses"] = '0';
      }
    // }
    // $listScan = $this->ScanModel->delberkas($filep,$norm,$nopen,$iduser);
   
    echo json_encode($ret);
  }

  function get_data_berkas(){
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $norm = $this->input->post('norm');
    $listScan = $this->ScanModel->getScan($norm);

    $data = array();
    $no = 1;
    foreach ($listScan->result() as $ls) {
        if($ls->LOKASI == "/upload_emr/scan_rekam_medis2/"){
            $tgl = substr($ls->SUBFOLDER, 4)."-".substr($ls->SUBFOLDER, 2, 2)."-".substr($ls->SUBFOLDER, 0, 2);
              // switch (basename($ls->FILE, '.pdf')) {
              //   case '1':
              //     $nama = "CPPT (".date_indo($tgl).")";
              //     break;
              //   case '2':
              //     $nama = "Hasil Penunjang (".date_indo($tgl).")";
              //     break;
              //   case '3':
              //     $nama = "Resume Medis (".date_indo($tgl).")";
              //     break;
              //   case '4':
              //     $nama = "Ringkasan Masuk Keluar/Registrasi (".date_indo($tgl).")";
              //     break;
              //   case '5':
              //     $nama = "Summary List (".date_indo($tgl).")";
              //     break;
              //   case '6':
              //     $nama = "Assesmen Awal RJ/IGD (".date_indo($tgl).")";
              //     break;
              //   case '7':
              //     $nama = "Assesmen Awal RI (".date_indo($tgl).")";
              //     break;
              //   case '8':
              //     $nama = "Laporan Pembedahan (".date_indo($tgl).")";
              //     break;
              //   case '9':
              //     $nama = "Informed Consent (".date_indo($tgl).")";
              //     break;
              //   case '10':
              //     $nama = "Obat Kanker (".date_indo($tgl).")";
              //     break;
              //   case '11':
              //     $nama = "Laporan Operasi (".date_indo($tgl).")";
              //     break;
              //   case '12':
              //     $nama = "Sertifikat Kematian (".date_indo($tgl).")";
              //     break;
              //   // default:
              //   //   # code...
              //   //   break;
              // }

        }else{
            $nama = basename($ls->FILE, '.pdf');
        }
        $link = $ls->LOKASI.$ls->FOLDER."/".$ls->SUBFOLDER."/".$ls->FILE;

        $data[] = array(
            $no,
            $ls->NOMR.' - '.$ls->NAMA,
            $ls->JENIS_BERKAS.' ('.date_indo($tgl).')',
            '<a class="btn btn-success" href="'.$link.'" target="_blank" style="color:#ffffff; width:80px; margin-left:0px; font-size:13px; margin-bottom:5px;">Lihat</a>'
        );
        $no++;
    }

    $output = array(
        "draw"            => $draw,
        "recordsTotal"    => $listScan->num_rows(),
        "recordsFiltered" => $listScan->num_rows(),
        "data"            => $data
    );
    echo json_encode($output);
}

  public function historyRM($nomr=''){
      $data['history']=$this->ScanModel->BerkasScanPendaftaran($nomr);
      $data['nomr']=$nomr;
      $this->load->view('rekam_medis/uploadRM/historyBerkas', $data);
  }

  public function berkasRM($nomr='',$nopen='', $tanggal=''){
      $data['params']['nomr'] = $nomr;
      $data['params']['nopen'] = $nopen;
      // $data['params']['idjenis'] = $jenis;
      $data['berkas'] = $this->ScanModel->getberkasshow($nomr,$nopen,$tanggal);
      $this->load->view('rekam_medis/uploadRM/berkaspasienshow', $data);
  }

  function getkategoriberkasrm3($jenislayanan=''){
    $result = $this->ScanModel->kategoriberkasrm3($jenislayanan);
    // var_dump($result);exit;
    $data = array();
    $data[] = array("id"=>"","text"=>"-Pilih-");
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['id'];
        $sub_array['text'] = $row['nama_kategori'];
        $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  function getjenisberkasrm3($idkategori=''){
    $result = $this->ScanModel->jenisBerkasrm3($idkategori);
    $data = array();
    $data[] = array("id"=>"","text"=>"-Pilih-");
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['id'];
        $sub_array['text'] = $row['nama_berkas'];
        $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  function hapusberkasrm3(){
    $ret=array();
    $iduser=$this->session->userdata("id");
    $nomr = $this->input->post('nomr');
    $id = $this->input->post('id');
    $param['nomr']=$nomr;
    $param['id']=$id;
    $param['oleh']=$iduser;
    $result = $this->ScanModel->hapusBerkasrm3($param);
    
    if($result){
      $ret["sukses"]= '1';
      $ret["message"] = 'berhasil dihapus';
    }else{
      $ret["message"] = 'Gagal Hapus';
      $ret["sukses"] = '0';
    }
    echo json_encode($ret);
  }

  function simpanBerkas3(){
    $iduser=$this->session->userdata("id");
  
    $nomr=isset($_POST['nomr'])?$_POST['nomr']:null;
    $caraupload=isset($_POST['caraupload'])?$_POST['caraupload']:'';
    $jenislayanan=(isset($_POST['jenislayanan']) && $_POST['jenislayanan']>0)?$_POST['jenislayanan']:null;
    $jenisberkasid=(isset($_POST['jenisberkasid']) && $_POST['jenisberkasid']>0)?$_POST['jenisberkasid']:'';
    $jenisberkas=isset($_POST['jenisberkas'])?$_POST['jenisberkas']:null;
    $tglform1=isset($_POST['tglform1'])?$_POST['tglform1']:'';
    $tglform2=isset($_POST['tglform2'])?$_POST['tglform2']:'';
    $datasama=isset($_POST['datasama'])?$_POST['datasama']:'';
    // $param=isset($_POST['param'])?(array)json_decode($_POST['param']):null;

    $result = array();
    if(isset($_FILES["filepasien"]) && $iduser && $tglform1 && $tglform2 && $nomr){

        $error =$_FILES["filepasien"]["error"];
        $file_type = $_FILES['filepasien']['type'];
        $allowed = array("application/pdf");
        $maksimal=10485760 * 2;
        if(!in_array($file_type, $allowed)) {
            $result["message"] = 'hanya file dengan pdf yang boleh diupload.';
            $result["sukses"] = '0';

        }else if($_FILES['filepasien']['size'] > $maksimal) { //10 MB
          $result["message"] = 'Size File '.$this->formatBytes($_FILES['filepasien']['size']).', maksimal '.$this->formatBytes($maksimal);
          $result["sukses"] = '0';
         
        }else{

          $lokasi = "upload_emr/scan_rekam_medis3/mr_".$nomr;

          $directory = "../".$lokasi;
          if (!file_exists($directory)) {
            mkdir($directory, 0777, true);
          }
          $nameori = $_FILES["filepasien"]["name"];
          $ext=pathinfo($nameori, PATHINFO_EXTENSION);

          $pattern = '#\(.*?\)#';

          $flagtanggal = $tglform1.'_'.$tglform2.'_'.date('YmdHis');
          if($jenisberkas!=null){
            $fileName = strtok($jenisberkas, ':');
            $fileName =  preg_replace('/[^a-zA-Z0-9_ -]/s','',preg_replace($pattern, '', $fileName));
            $fileName =  strtolower(str_replace(" ","_",trim($fileName)));
            // echo $fileName;exit;
            $fileName = $nomr."_".$fileName."_".$flagtanggal.".".$ext;
          }else{
            $fileName = $nomr."_".$flagtanggal.".".$ext;
          }

          $lokasipath=$directory."/";

          $config['upload_path'] = $lokasipath;
          $config['allowed_types'] = 'pdf';
          $config['file_name'] = $fileName;
          $config['overwrite'] = true;

          $cekdata = $this->ScanModel->cekberkasrm3($nomr,$jenisberkasid,$tglform1,$tglform2);
          if($cekdata>0 && $datasama!='2'){
              $result["message"] = 'dokumen pada periode ini sudah diupload';
              $result["sukses"] = '2';
          }else{

            $this->upload->initialize($config);
            
            if($this->upload->do_upload('filepasien')){
              $stquery ="INSERT INTO
                    db_layanan.tb_scan_berkas3 (nomr,cara_upload,jenis_pelayanan,lokasi,namafile,idjenis_berkas,periode_awal,periode_akhir,created_by,deleted_at,deleted_by) 
                    VALUES ('$nomr','$caraupload',".($jenislayanan!=null?"'$jenislayanan'":'NULL').",'$lokasipath','$fileName','$jenisberkasid','$tglform1','$tglform2','$iduser',NULL,NULL) 
                    ON DUPLICATE KEY UPDATE lokasi = VALUES(lokasi), namafile = VALUES(namafile), created_at = now(), updated_at = now(), updated_by = '$iduser', deleted_at = VALUES(deleted_at), deleted_by = VALUES(deleted_by)";
              // echo  $stquery;exit;
              $this->db->query($stquery);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result["message"] = 'Gagal Insert';
                $result["sukses"] = '0';
                // $result = array('status' => 'failed', 'error' => $this->upload->display_errors(), 'info' => $this->upload->data());
              } else {
                $this->db->trans_commit();
                $result["file"]= $fileName;
                $result["sukses"]= '1';
                // $result = array('status' => 'success');
              }

            }else{
              $result["message"] = 'Gagal Upload';
              $result["sukses"] = '0';
              // $result = array('status' => 'noupload', 'error' => $this->upload->display_errors(), 'info' => $this->upload->data());
            }
        }

        }
      }
    echo json_encode($result);
  }

  public function tblberkasrm3(){

        $draw = intval($this->input->post("draw"));
        $start = intval($this->input->post("start"));
        $length = intval($this->input->post("length"));

        $nomr = $this->input->POST('nomr');
        $del = $this->input->POST('del');
        // echo $nomr;exit;
        // $jenisp = $this->input->POST('jenis');

        $respon = $this->ScanModel->listberkasrm3($nomr);
        // var_dump($respon );exit;
        $data = array();
        $no = 1;
        foreach ($respon as $rr) {
                $item=array();
                $item[] = $no;
                $item[] = $rr->cara_upload==1?'Per Jenis':'Per Episode';
                $item[] = $rr->jenis_pelayanan==2?'Rawat Inap':($rr->jenis_pelayanan==1?'Rawat Jalan':'');
                $item[] = $rr->nama_kategori;
                $item[] = $rr->nama_berkas;
                $item[] = $rr->periode_awal;
                $item[] = $rr->Periode_akhir;
                $item[] = $rr->created_at;
                $item[] = '<a href="#" onClick="showfilerm3(this)" data-url="'.$rr->lokasi.$rr->namafile.'" class="btn btn-sm btn-primary btn-block" data-toggle="modal" data-id="' . $rr->id . '" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i></a>';
                if($del=='1'){
                  $item[] = '<a href="#" onClick="deletefilerm3(this)"  data-id="'.$rr->id.'" class="btn btn-sm btn-danger btn-block" data-toggle="modal" data-id="' . $rr->id . '" data-backdrop="static" data-keyboard="false"><i class="fa fa-trash"></i></a>';
                }
                $data[] = $item;
            $no++;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => count($data),
            "recordsFiltered" => count($data),
            "data" => $data
        );
        echo json_encode($output);
    }
    

}