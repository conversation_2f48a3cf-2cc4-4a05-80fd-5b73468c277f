<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class JpokemModel extends CI_Model
{
    // Ambil list protokol kemoterapi untuk select2
    public function getProtokolKemoList($search = '')
    {
        $this->db->select('ID, NAMA');
        $this->db->from('db_master.tb_protokol_kemo');
        $this->db->where('STATUS', 1);
        if ($search) {
            $this->db->like('NAMA', $search);
        }
        $query = $this->db->get();
        return $query->result_array();
    }

    // Ambil detail protokol kemoterapi sesuai query
    public function getProtokolKemoDetail($id_protokol)
    {
        $sql = "SELECT kem.ID id_prokem
, kem.NAMA nama_protokol
, det.ID id_detil
, op.NAMA_OBAT nama_obat
, det.DOSIS_PROTOKOL dosis
, pen.NAMA_OBAT pengenceran
, det.DOSIS_PENGENCERAN dosis_pengenceran
, ap.variabel aks<PERSON>_pem<PERSON>
, det.KECEPATAN kecepatan
, det.KETERANGAN keterangan

from db_master.tb_protokol_kemo_detil det

LEFT JOIN db_master.tb_protokol_kemo kem ON kem.ID = det.PAKET AND kem.STATUS = 1
LEFT JOIN db_master.tb_obat_protokol op ON op.ID = det.OBAT_PROTOKOL AND op.STATUS = 1
LEFT JOIN db_master.tb_obat_protokol pen ON pen.ID = det.PENGENCERAN AND pen.STATUS = 1
LEFT JOIN db_master.variabel ap ON ap.id_variabel = det.AKSES_PEMBERIAN AND ap.id_referensi = 279

WHERE det.STATUS = 1 AND kem.ID = ? AND det.PENGENCERAN IS NOT NULL

ORDER BY kem.ID, det.ID ASC";
        $query = $this->db->query($sql, array($id_protokol));
        return $query->result_array();
    }

    // Simpan data JPOK Kemoterapi
    public function simpanJpok($data_jpok, $dosis, $dosis_pengenceran, $id_prokem, $keterangan = [])
    {
        $this->db->trans_start();

        try {
            // Insert ke tabel utama tb_kemo_jpok
            $insert_result = $this->db->insert('db_master.tb_kemo_jpok', $data_jpok);

            if (!$insert_result) {
                $error = $this->db->error();
                throw new Exception('Gagal menyimpan data utama JPOK: ' . $error['message']);
            }

            $id_jpok = $this->db->insert_id();

            if (!$id_jpok) {
                throw new Exception('Gagal mendapatkan ID JPOK setelah insert');
            }

            // Insert ke tabel detail tb_kemo_jpok_detail
            foreach ($dosis as $id_detil => $nilai_dosis) {
                if (!empty($nilai_dosis) && $nilai_dosis != '0') {
                    // Ambil data detail protokol untuk mendapatkan informasi lengkap
                    $detail_protokol = $this->getDetailProtokolById($id_detil);

                    // Pastikan id_prokem adalah nilai tunggal, bukan array
                    $id_prokem_value = null;
                    if (isset($id_prokem[$id_detil])) {
                        $id_prokem_value = is_array($id_prokem[$id_detil]) ? $id_prokem[$id_detil][0] : $id_prokem[$id_detil];
                    }

                    // Pastikan dosis_pengenceran adalah nilai tunggal, bukan array
                    $dosis_pengenceran_value = null;
                    if (isset($dosis_pengenceran[$id_detil])) {
                        $dosis_pengenceran_value = is_array($dosis_pengenceran[$id_detil]) ? $dosis_pengenceran[$id_detil][0] : $dosis_pengenceran[$id_detil];
                    }

                    // Ambil keterangan dari input user atau gunakan default dari protokol
                    $keterangan_value = '';
                    if (isset($keterangan[$id_detil]) && !empty($keterangan[$id_detil])) {
                        $keterangan_value = is_array($keterangan[$id_detil]) ? $keterangan[$id_detil][0] : $keterangan[$id_detil];
                    } else {
                        $keterangan_value = $detail_protokol['keterangan'];
                    }

                    $data_detail = array(
                        'ID_JPOK' => $id_jpok,
                        'ID_PROKEM' => $detail_protokol['id_detil'], // Gunakan id_detil dari query
                        'NAMA_OBAT' => $detail_protokol['nama_obat'],
                        'DOSIS' => $nilai_dosis,
                        'PENGENCERAN' => $detail_protokol['pengenceran'],
                        'DOSIS_PENGENCERAN' => $dosis_pengenceran_value,
                        'AKSES_PEMBERIAN' => $detail_protokol['akses_pemberian'],
                        'KECEPATAN' => $detail_protokol['kecepatan'],
                        'KETERANGAN' => $keterangan_value,
                        'STATUS' => 1
                    );

                    $insert_detail = $this->db->insert('db_master.tb_kemo_jpok_detail', $data_detail);
                    if (!$insert_detail) {
                        $error = $this->db->error();
                        throw new Exception('Gagal menyimpan detail obat ' . $detail_protokol['nama_obat'] . ': ' . $error['message']);
                    }
                }
            }
            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                return array('status' => false, 'message' => 'Gagal menyimpan data ke database');
            }

            return array('status' => true, 'id_jpok' => $id_jpok, 'message' => 'Data berhasil disimpan');

        } catch (Exception $e) {
            $this->db->trans_rollback();
            return array('status' => false, 'message' => $e->getMessage());
        }
    }

    // Helper method untuk mendapatkan detail protokol berdasarkan ID detail
    public function getDetailProtokolById($id_detil)
    {
        $sql = "SELECT kem.ID id_prokem
                , kem.NAMA nama_protokol
                , det.ID id_detil
                , op.NAMA_OBAT nama_obat
                , det.DOSIS_PROTOKOL dosis
                , pen.NAMA_OBAT pengenceran
                , det.DOSIS_PENGENCERAN dosis_pengenceran
                , ap.variabel akses_pemberian
                , det.KECEPATAN kecepatan
                , det.KETERANGAN keterangan

                from db_master.tb_protokol_kemo_detil det

                LEFT JOIN db_master.tb_protokol_kemo kem ON kem.ID = det.PAKET AND kem.STATUS = 1
                LEFT JOIN db_master.tb_obat_protokol op ON op.ID = det.OBAT_PROTOKOL AND op.STATUS = 1
                LEFT JOIN db_master.tb_obat_protokol pen ON pen.ID = det.PENGENCERAN AND pen.STATUS = 1
                LEFT JOIN db_master.variabel ap ON ap.id_variabel = det.AKSES_PEMBERIAN AND ap.id_referensi = 279

                WHERE det.STATUS = 1 AND det.ID = ? AND det.PENGENCERAN IS NOT NULL

                ORDER BY kem.ID, det.ID ASC";

        $query = $this->db->query($sql, array($id_detil));
        $result = $query->row_array();

        return $result ? $result : array(
            'id_prokem' => '',
            'nama_protokol' => '',
            'id_detil' => '',
            'nama_obat' => '',
            'dosis' => '',
            'pengenceran' => '',
            'dosis_pengenceran' => '',
            'akses_pemberian' => '',
            'kecepatan' => '',
            'keterangan' => ''
        );
    }

    // Ambil list JPOK yang sudah disimpan untuk select2
    public function getJpokList($nokun = '', $search = '')
    {
        $this->db->select('kj.ID, kj.NOKUN, kj.LINI, kj.JUMLAH_SIKLUS, kj.TANGGAL_INPUT, pk.NAMA');
        $this->db->from('db_master.tb_kemo_jpok kj');
        $this->db->join('db_master.tb_protokol_kemo pk', 'pk.ID = kj.ID_PROTOKOL_KEMO', 'left');
        $this->db->where('kj.STATUS', 1);

        if (!empty($nokun)) {
            $this->db->where('kj.NOKUN', $nokun);
        }

        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('kj.NOKUN', $search);
            $this->db->or_like('kj.LINI', $search);
            $this->db->or_like('kj.ID', $search);
            $this->db->or_like('pk.NAMA', $search); // sekalian cari di nama protokol, biar mantap
            $this->db->group_end();
        }

        $this->db->order_by('kj.TANGGAL_INPUT', 'DESC');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Ambil detail JPOK beserta jadwal untuk tabel pemberian (dikelompokkan per bulan)
    public function getJpokDetailWithJadwal($id_jpok)
    {
        // Ambil data obat JPOK
        $sql_obat = "SELECT
                        jd.ID as id_detail,
                        jd.NAMA_OBAT,
                        jd.DOSIS,
                        jd.KETERANGAN as pemberian
                    FROM db_master.tb_kemo_jpok_detail jd
                    WHERE jd.ID_JPOK = ? AND jd.STATUS = 1
                    ORDER BY jd.ID";

        $query_obat = $this->db->query($sql_obat, array($id_jpok));
        $obat_data = $query_obat->result_array();

        // Ambil data jadwal yang dikelompokkan per bulan/tahun
        $sql_jadwal = "SELECT DISTINCT 
                        js.BULAN, 
                        js.TAHUN,
                        CONCAT(js.BULAN, '-', js.TAHUN) as periode_key
                    FROM db_master.tb_kemo_jpok_detail_jadwal js
                    INNER JOIN db_master.tb_kemo_jpok_detail jd ON jd.ID = js.ID_JPOK_DETAIL
                    WHERE jd.ID_JPOK = ? AND js.STATUS = 1 AND jd.STATUS = 1
                    ORDER BY js.TAHUN, js.BULAN";

        $query_jadwal = $this->db->query($sql_jadwal, array($id_jpok));
        $periode_data = $query_jadwal->result_array();

        $result = array();

        foreach ($periode_data as $periode) {
            $bulan_nama = date('F Y', mktime(0, 0, 0, $periode['BULAN'], 1, $periode['TAHUN']));
            
            $result[] = array(
                'periode' => $periode['periode_key'],
                'bulan' => $periode['BULAN'],
                'tahun' => $periode['TAHUN'],
                'bulan_nama' => $bulan_nama,
                'obat_list' => $obat_data
            );
        }

        // Jika tidak ada jadwal, tetap return obat list untuk tabel kosong
        if (empty($result) && !empty($obat_data)) {
            $result[] = array(
                'periode' => 'no-schedule',
                'bulan' => null,
                'tahun' => null,
                'bulan_nama' => 'Belum ada jadwal',
                'obat_list' => $obat_data
            );
        }

        return $result;
    }

    // Ambil jadwal detail untuk modal
    public function getJadwalDetail($id_jpok_detail)
    {
        $this->db->select('*');
        $this->db->from('db_master.tb_kemo_jpok_detail_jadwal');
        $this->db->where('ID_JPOK_DETAIL', $id_jpok_detail);
        $this->db->where('STATUS', 1);
        $this->db->order_by('TAHUN, BULAN, MINGGU, SERI');
        $query = $this->db->get();
        return $query->result_array();
    }

    // Simpan jadwal minum obat
    public function simpanJadwal($id_jpok, $bulan, $tahun, $minggu, $seri, $tb, $bb, $lbb_m2, $obat_jadwal)
    {
        $this->db->trans_start();

        try {
            // Ambil semua detail obat dari JPOK
            $this->db->select('ID');
            $this->db->from('db_master.tb_kemo_jpok_detail');
            $this->db->where('ID_JPOK', $id_jpok);
            $this->db->where('STATUS', 1);
            $detail_obat = $this->db->get()->result_array();

            foreach ($detail_obat as $obat) {
                $id_detail = $obat['ID'];

                // Siapkan data jadwal
                $data_jadwal = array(
                    'ID_JPOK_DETAIL' => $id_detail,
                    'BULAN' => $bulan,
                    'TAHUN' => $tahun,
                    'MINGGU' => $minggu,
                    'SERI' => $seri,
                    'TB' => $tb,
                    'BB' => $bb,
                    'LBB_M2' => $lbb_m2,
                    'OLEH' => $this->session->userdata['id'],
                    'STATUS' => 1
                );

                // Set checkbox hari (default 0)
                for ($hari = 1; $hari <= 7; $hari++) {
                    $data_jadwal['HARI_' . $hari] = 0;
                    if (isset($obat_jadwal[$id_detail][$hari])) {
                        $data_jadwal['HARI_' . $hari] = 1;
                    }
                }

                // Cek apakah sudah ada data untuk kombinasi ini
                $this->db->where('ID_JPOK_DETAIL', $id_detail);
                $this->db->where('BULAN', $bulan);
                $this->db->where('TAHUN', $tahun);
                $this->db->where('MINGGU', $minggu);
                $this->db->where('SERI', $seri);
                $existing = $this->db->get('db_master.tb_kemo_jpok_detail_jadwal')->row();

                if ($existing) {
                    // Update data yang sudah ada
                    $this->db->where('ID', $existing->ID);
                    $this->db->update('db_master.tb_kemo_jpok_detail_jadwal', $data_jadwal);
                } else {
                    // Insert data baru
                    $this->db->insert('db_master.tb_kemo_jpok_detail_jadwal', $data_jadwal);
                }
            }

            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                return array('status' => false, 'message' => 'Gagal menyimpan jadwal ke database');
            }

            return array('status' => true, 'message' => 'Jadwal berhasil disimpan');

        } catch (Exception $e) {
            $this->db->trans_rollback();
            return array('status' => false, 'message' => $e->getMessage());
        }
    }

    // Ambil data jadwal berdasarkan ID JPOK
    public function getJadwalByJpok($id_jpok)
    {
        $sql = "SELECT
                    js.*,
                    jd.ID as ID_JPOK_DETAIL
                FROM db_master.tb_kemo_jpok_detail_jadwal js
                INNER JOIN db_master.tb_kemo_jpok_detail jd ON jd.ID = js.ID_JPOK_DETAIL
                WHERE jd.ID_JPOK = ? AND js.STATUS = 1 AND jd.STATUS = 1
                ORDER BY js.TAHUN, js.BULAN, js.MINGGU, js.SERI";

        $query = $this->db->query($sql, array($id_jpok));
        return $query->result_array();
    }

    // Method untuk cek jenis kelamin pasien
    public function getJenisKelaminPasien($nokun)
    {
        $sql = "SELECT IF(mp.JENIS_KELAMIN=1,'Laki-laki','Perempuan') as jenkel
                FROM pendaftaran.kunjungan pk
                INNER JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                INNER JOIN master.pasien mp ON mp.NORM = pp.NORM
                WHERE pk.NOMOR = ?
                LIMIT 1";

        $query = $this->db->query($sql, array($nokun));
        $result = $query->row_array();

        return $result ? $result : array('jenkel' => '');
    }

    // Method untuk mengambil history JPOK
    public function getHistoryJpok($nokun)
    {
        $sql = "SELECT
                    j.ID,
                    j.NOKUN,
                    j.JUMLAH_SIKLUS,
                    j.LINI,
                    j.ID_PROTOKOL_KEMO,
                    pk.NAMA AS PROTOKOL_KEMO,
                    j.CREATED_AT,
                    j.OLEH,
                    COUNT(jd.ID_JPOK) as JUMLAH_OBAT,
                    GROUP_CONCAT(jd.NAMA_OBAT SEPARATOR ', ') as DAFTAR_OBAT
                FROM db_master.tb_kemo_jpok j
                LEFT JOIN db_master.tb_kemo_jpok_detail jd ON j.ID = jd.ID_JPOK
                LEFT JOIN db_master.tb_protokol_kemo pk ON j.ID_PROTOKOL_KEMO = pk.ID
                GROUP BY j.ID
                ORDER BY j.CREATED_AT DESC";

        $query = $this->db->query($sql);
        return $query->result_array();
    }
}
