<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RAPOModel extends MY_Model
{
  protected $_table_name = 'medis.tb_rapo';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = [
    'nokun' => [
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s <PERSON>ajib <PERSON>.',
        'numeric' => '%s <PERSON>ajib <PERSON>',
      ]
    ],
  ];

  function __construct()
  {
    parent::__construct();
  }

  function simpanRAPO($data)
  {
    $this->db->insert('medis.tb_rapo', $data);
    return $this->db->insert_id();
  }

  function simpanObatRAPO($data)
  {
    $this->db->insert_batch('medis.tb_obat_rapo', $data);
  }

  function simpanObat($data)
  {
    $this->db->insert('medis.tb_obat_rapo', $data);
  }

  function cekNopen($nomr)
  {
    $this->db->select('k.NOPEN');
    $this->db->from('medis.tb_rapo r');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = r.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('p.NORM', $nomr);

    $query = $this->db->get();
    return $query->num_rows();
  }

  function jumlah($nomr)
  {
    $this->db->select('r.id');
    $this->db->from('medis.tb_rapo r');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = r.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('p.NORM', $nomr);
    $this->db->where('r.status', 1);

    $query = $this->db->get();
    return $query->num_rows();
  }

  function history($nomr)
  {
    $this->db->select('r.id, r.nokun, r.tanggal, master.getNamaLengkapPegawai(peng.NIP) pengisi, r.oleh, r.tgl_update');
    $this->db->from('medis.tb_rapo r');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = r.oleh', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = r.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('p.NORM', $nomr);
    $this->db->where('r.status', 1);
    $this->db->order_by('r.tanggal', 'DESC');

    $query = $this->db->get();
    return $query;
  }

  function detail($id)
  {
    $this->db->select(
      'r.id, r.nokun, r.tanggal, r.tindakan_operasi, r.diagnosis_pasca_operasi, r.ruang_rawat, r.nama_ruang_rawat,
      r.posisi_badan, r.ket_posisi_badan, r.posisi_ekstrimitas, r.ket_posisi_ekstrimitas, r.diet, r.ket_diet_1,
      r.ket_diet_2, r.infus, r.ket_infus, r.transfusi, r.ket_transfusi, r.drain, r.lokasi_drain, r.jenis_drain,
      r.observasi_drain, r.tampon, r.durasi_tampon, r.pelepasan_tampon, r.perawatan_luka, r.dauer_catheter, r.ukur_urin,
      r.ngt, r.ukur_produksi, r.periksa_lab, r.ket_periksa_lab, r.periksa_rontgen, r.ket_periksa_rontgen, r.observasi,
      r.ket_observasi, r.instruksi_lain, r.pendamping, r.ket_pendamping, r.kontak_darurat, r.ket_kontak_darurat,
      r.anjuran, r.ket_anjuran'
    );
    $this->db->from('medis.tb_rapo r');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = r.nokun', 'left');
    $this->db->where('r.id', $id);

    $query = $this->db->get();
    return $query->row_array();
  }

  function ubahRAPO($id, $data)
  {
    $this->db->where('medis.tb_rapo.id', $id);
    $this->db->update('medis.tb_rapo', $data);
  }

  function tabelObat($id)
  {
    $this->db->select('o.id, o.nama_obat, o.dosis_obat, o.pemberian_obat, o.ket_obat');
    $this->db->from('medis.tb_obat_rapo o');
    $this->db->join('medis.tb_rapo r', 'r.id = o.id_rapo', 'left');
    $this->db->where('o.id_rapo', $id);
    $this->db->where('o.status', 1);

    $query = $this->db->get();
    return $query->result_array();
  }

  function detailObat($id)
  {
    $this->db->select('o.id, o.nama_obat, o.dosis_obat, o.pemberian_obat, o.ket_obat');
    $this->db->from('medis.tb_obat_rapo o');
    $this->db->where('o.id', $id);

    $query = $this->db->get();
    return $query->row_array();
  }

  function ubahObat($id, $data)
  {
    $this->db->where('medis.tb_obat_rapo.id', $id);
    $this->db->update('medis.tb_obat_rapo', $data);
  }
}

/* End of file RAPOModel.php */
/* Location: ./application/models/rekam_medis/rawat_inapp/operasi/RAPOModel.php */