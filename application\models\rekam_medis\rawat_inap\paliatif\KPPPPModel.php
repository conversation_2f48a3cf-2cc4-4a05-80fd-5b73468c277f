<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KPPPPModel extends MY_Model
{

    public function getIDResep($nomr, $ruangan)
    {
        $query = $this->db->query("
            SELECT ore.NOMOR NOMOR_ORDER
            FROM layanan.order_resep ore
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ore.KUNJUNGAN
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            WHERE p.NORM = '$nomr' AND ore.TUJUAN = '$ruangan' AND ore.`STATUS`=2
            ORDER BY ore.TANGGAL DESC
            LIMIT 1
        ");
        return $query->row()->NOMOR_ORDER;
    }

    public function detailOrderResep($nomor)
    {
        $query  = $this->db->query("SELECT ore.KUNJUNGAN,ore.NOMOR NOMOR_ORDER, r.DESKRIPSI ASAL_UNIT
                                  , ore.TANGGAL TANGGAL_RESEP
                                  , ras.DESKRIPSI TUJUAN
                                  , ore.RESEP_CITO
                                  , if(ore.RESEP_CITO=0, 'Non-Cito', 'Cito') RESEP_CITO_DESK
                                  , ore.`STATUS`
                                  , IF(ore.`STATUS`=1, 'Order Belum Diterima', IF(ore.`STATUS`=2 AND kunfar.`STATUS`=1, 'Diterima/diproses', IF(ore.`STATUS`=2 AND kunfar.`STATUS`=2,'Resep Difinalkan', IF(ore.`STATUS`=0, 'Order Dibatalkan','Resep Dibatalkan')))) STATUS_RESEP
                                  , kunfar.`STATUS` STATUS_FARMASI
                                  , `master`.getNamaLengkapPegawai(ap.NIP) OLEH
                                  , `master`.getNamaLengkapPegawai(md.NIP) DPJP
                                FROM layanan.order_resep ore
                                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ore.KUNJUNGAN
                                LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
                                LEFT JOIN master.ruangan ras ON ras.ID = ore.TUJUAN
                                LEFT JOIN pendaftaran.kunjungan kunfar ON kunfar.REF = ore.NOMOR
                                LEFT JOIN aplikasi.pengguna ap ON ore.OLEH = ap.ID
                                LEFT JOIN `master`.dokter md ON ore.DOKTER_DPJP = md.ID
                                WHERE ore.KUNJUNGAN='$nomor'
                                ORDER BY ore.TANGGAL DESC");

        return $query->row();
    }

    public function listHistoryKPPPP($nomr)
    {
        $query = $this->db->query("SELECT
                                  kpp.*,
                                  master.getNamaLengkapPegawai (ap.NIP) OLEH,
                                  master.getNamaLengkapPegawai (ap1.NIP) OLEH_UPDATE                                 
                                FROM
                                  medis.tb_kppp_paliatif kpp
                                  LEFT JOIN aplikasi.pengguna ap ON ap.ID = kpp.OLEH 
                                  LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = kpp.EDIT_OLEH 
                                WHERE
                                kpp.STATUS = 1 AND kpp.norm = $nomr
                              ");
        return $query;
    }

    public function getDataKPPPP($id)
    {
        $query = $this->db->query("SELECT
                                kpppp.*
                            FROM
                                medis.tb_kppp_paliatif kpppp
                            WHERE kpppp.id = $id AND kpppp.status = 1");
        return $query->row_array();
    }

    public function riwayatResep($nomr)
    {
        $query = $this->db->query("
        SELECT ore.NOMOR NOMOR_ORDER, p.NORM, r.DESKRIPSI ASAL_UNIT, ore.TANGGAL TANGGAL_RESEP, 
               ras.DESKRIPSI TUJUAN, ore.RESEP_CITO, IF(ore.RESEP_CITO=0, 'Non-Cito', 'Cito') RESEP_CITO_DESK, 
               ore.STATUS, 
               IF(ore.STATUS=1, 'Order Belum Diterima', 
                  IF(ore.STATUS=2 AND kunfar.STATUS=1, 'Diterima/diproses', 
                     IF(ore.STATUS=2 AND kunfar.STATUS=2,'Resep Difinalkan', 
                        IF(ore.STATUS=0, 'Order Dibatalkan','Resep Dibatalkan')))) STATUS_RESEP, 
               `master`.getNamaLengkapPegawai(ap.NIP) OLEH, 
               ant.nomordisplay as noantrian, 
               IF(ore.RESEP_PASIEN_PULANG=1, 'Resep Pasien Pulang', 'Bukan Resep Pasien Pulang') RESEP_PASIEN_PULANG
        FROM layanan.order_resep ore
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ore.KUNJUNGAN
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
        LEFT JOIN master.ruangan ras ON ras.ID = ore.TUJUAN
        LEFT JOIN pendaftaran.kunjungan kunfar ON kunfar.REF = ore.NOMOR
        LEFT JOIN aplikasi.pengguna ap ON ore.OLEH = ap.ID
        LEFT JOIN antrianv2.antrian ant ON ore.KUNJUNGAN=ant.nokun AND ant.ref=ore.NOMOR
        WHERE p.NORM='$nomr' AND ore.RESEP_PASIEN_PULANG=1 AND ore.STATUS=2
        ORDER BY ore.TANGGAL DESC
        LIMIT 1;
        ");

        return $query->row_array();
    }
    public function detilResep($orderId)
    {
        $query = $this->db->query("SELECT ored.ORDER_ID NOMOR_RESEP
                , IF(ored.RACIKAN=0,'Non-racik', CONCAT('Racikan ', ored.GROUP_RACIKAN)) KLP_RACIKAN
                , IF(ored.GROUP_RACIKAN=0,'',ored.GROUP_RACIKAN) GROUP_RACIKAN
                , b.NAMA NAMA_OBAT, gen.DESKRIPSI GENERIK, ored.JUMLAH, IF(far.ID IS NULL, ap.DESKRIPSI, aps.DESKRIPSI) ATURAN_PAKAI, ored.KETERANGAN
                , IF(b.FORMULARIUM=1, 'Fornas', 'Non-fornas') FORMULARIUM
                , ored.KARDEX, ored.JALUR_PEMBERIAN
                , b.FORMULARIUM FORMULARIUMINT
                , b.KATEGORI
                , b.ID BARANG
                , ibr.ID IDIBR
                , ored.JAM
                , ored.ATURAN_PAKAI ATURAN_PAKAI_ID
                , ored.RACIKAN
                , ored.DOSIS
                , ored.JUMLAH_SIGNA sig_dosis
                , ored.SATUAN_SIGNA sig_satuan
                , ored.FREKUENSI_SIGNA sig_frek
                , ored.KETERANGAN_SIGNA sig_ket
                FROM layanan.order_detil_resep ored
                LEFT JOIN layanan.order_resep ore ON ore.NOMOR = ored.ORDER_ID
                LEFT JOIN inventory.barang b ON b.ID = ored.FARMASI
                LEFT JOIN master.referensi ap ON ap.ID = ored.ATURAN_PAKAI AND ap.JENIS=41
                LEFT JOIN master.referensi gen ON gen.ID = b.GENERIK AND gen.JENIS=42
                LEFT JOIN pendaftaran.kunjungan pk ON pk.REF = ore.NOMOR
                LEFT JOIN layanan.farmasi far ON far.KUNJUNGAN = pk.NOMOR AND ored.FARMASI = far.FARMASI AND ored.STOK = far.STOK
                LEFT JOIN master.referensi aps ON aps.ID = far.ATURAN_PAKAI AND aps.JENIS=41
                LEFT JOIN inventory.barang_ruangan ibr ON b.ID = ibr.BARANG AND ibr.RUANGAN=ore.TUJUAN AND ibr.STATUS=1
                WHERE ored.ORDER_ID='$orderId'
                GROUP BY b.ID
                ORDER BY ore.TANGGAL ASC");

        return $query->result_array();
    }
}
/* End of file HADSModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/paliatif/HADSModel.php */
