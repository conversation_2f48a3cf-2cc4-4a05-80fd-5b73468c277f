<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SkriningCovid19Model extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('sc.id, sc.nokun, sc.created_at tanggal, ru.DESKRIPSI ruangan, peng.NAMA user, sc.suhu
        , IF(sc.suhu > 37.4, "0", "1") status_suhu, IF(sc.pilih1 = 5128 || sc.pilih2 = 5130 || sc.pilih3 = 5132 || sc.pilih4 = 5134 || sc.pilih5 = 5136 || sc.pilih6 = 5147 || sc.pilih7 = 5138 || sc.pilih8 = 5140 || sc.pilih9 = 5142 || sc.pilih10 = 5144, "0", "1") status_suspek #status 1 hijau, 0 merah
        ');
        $this->db->from('db_layanan.tb_formulir_skrining sc');
        $this->db->join('pendaftaran.kunjungan kun','kun.NOMOR = sc.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','pen.NOMOR = kun.NOPEN','LEFT');
        $this->db->join('master.ruangan ru','kun.RUANGAN = ru.ID','LEFT');
        $this->db->join('aplikasi.pengguna peng','sc.oleh = peng.ID','LEFT');
        $this->db->where('pen.NORM',$this->input->post('nomr'));
        $this->db->order_by('sc.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id_sc)
    {
      $query = $this->db->query(
        'SELECT sc.`*` FROM db_layanan.tb_formulir_skrining sc
        WHERE sc.id = "'.$id_sc.'"'
      );
      return $query->row_array();
    }

}
