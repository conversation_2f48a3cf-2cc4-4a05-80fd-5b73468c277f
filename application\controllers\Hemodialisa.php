<?php
defined('BASEPATH') or exit('No direct script access allowed');

class He<PERSON><PERSON><PERSON> extends CI_Controller
{
  public function __construct()
  {
      parent::__construct();
      if ($this->session->userdata('logged_in') == false) {
          redirect('login');
      }

      $this->load->model(array('HemodialisaModel', 'pengkajianAwalModel'));
      
  }

  public function actionHD($param)
  {
      if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
          if ($param == 'tambah' || $param == 'ubah') {
              $post = $this->input->post();

              $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();


              $dataObservasiHd = array(
                  'id_emr' => $getIdEmr,
                  'nomr'=> isset($post['noRm']) ? $post['noRm'] : "", 
                  'nokun'=> isset($post['noKun']) ? $post['noKun'] : "", 
                  'tgl_hemodialisa'=> date('Y-m-d H:i:s'),
                  'hdke'=> isset($post['hd_ke']) ? $post['hd_ke'] : "", 
                  'mulaihd' => isset($post['mulai_hd']) ? $post['mulai_hd'] : "",
                  'selesaihd' => isset($post['selesai_hd']) ? $post['selesai_hd'] : "",
                  'lamahd'=> isset($post['lama_hd']) ? $post['lama_hd'] : "", 
                  'kesadaran_pre'=> isset($post['kesadaran_pre']) ? $post['kesadaran_pre'] : "",
                  'keluhan_pre'=> isset($post['keluhan_pre']) ? $post['keluhan_pre'] : "", 
                  'td_pre_sis'=> isset($post['td_pre_sis']) ? $post['td_pre_sis'] : "",
                  'td_pre_dis'=> isset($post['td_pre_dis']) ? $post['td_pre_dis'] : "",
                  'n_pre'=> isset($post['n_pre']) ? $post['n_pre'] : "",  
                  'p_pre'=> isset($post['p_pre']) ? $post['p_pre'] : "",  
                  's_pre'=> isset($post['s_pre']) ? $post['s_pre'] : "",  
                  'uncheck_pre'=> isset($post['uncheck_pre']) ? 1 : 0, 
                  'bb_pre'=> isset($post['bb_pre']) ? $post['bb_pre'] : "", 
                  'tb_pre'=> isset($post['tb_pre']) ? $post['tb_pre'] : "", 
                  'bb_kering_pre'=> isset($post['bb_kering_pre']) ? $post['bb_kering_pre'] : "",  
                  'bb_hd_berlaku_pre'=> isset($post['bb_hd_pre']) ? $post['bb_hd_pre'] : "",  
                  'kenaikan_bb'=> isset($post['naik_bb']) ? $post['naik_bb'] : "", 
                  'kesadaran_post'=> isset($post['kesadaran_post']) ? $post['kesadaran_post'] : "", 
                  'keluhan_post'=> isset($post['keluhan_post']) ? $post['keluhan_post'] : "", 
                  'td_post_sis' => isset($post['td_post_sis']) ? $post['td_post_sis'] : "", 
                  'td_post_dis' => isset($post['td_post_dis']) ? $post['td_post_dis'] : "", 
                  'n_post'=> isset($post['n_post']) ? $post['n_post'] : "", 
                  'p_post'=> isset($post['p_post']) ? $post['p_post'] : "", 
                  's_post'=> isset($post['s_post']) ? $post['s_post'] : "", 
                  'uncheck_post'=> isset($post['uncheck_post']) ? 1 : 0, 
                  'bb_post'=> isset($post['bb_post']) ? $post['bb_post'] : "", 
                  'tb_post'=> isset($post['tb_post']) ? $post['tb_post'] : "", 
                  'bb_kering_post'=> isset($post['bb_kering_post']) ? $post['bb_kering_post'] : "", 
                  'bb_post_hd'=> isset($post['bb_hd_post']) ? $post['bb_hd_post'] : "", 
                  'penurun_bb_post'=> isset($post['penurun_bb_post']) ? $post['penurun_bb_post'] : "",
                  'observasi_jam1'=> isset($post['jam_1']) ? $post['jam_1'] : "", 
                  'tb_1'=> isset($post['td_1']) ? $post['td_1'] : "", 
                  'n_1'=> isset($post['n_1']) ? $post['n_1'] : "", 
                  'p_1'=> isset($post['p_1']) ? $post['p_1'] : "", 
                  's_1'=> isset($post['s_1']) ? $post['s_1'] : "",
                  'ufg_1'=> isset($post['ufg_1']) ? $post['ufg_1'] : "",  
                  'ufr_1'=> isset($post['ufr_1']) ? $post['ufr_1'] : "",  
                  'uf_1'=> isset($post['uf_1']) ? $post['uf_1'] : "", 
                  'qb_1'=> isset($post['qb_1']) ? $post['qb_1'] : "", 
                  'qd_1'=> isset($post['qd_1']) ? $post['qd_1'] : "", 
                  'vp_1'=> isset($post['vp_1']) ? $post['vp_1'] : "", 
                  'ap_1'=> isset($post['ap_1']) ? $post['ap_1'] : "", 
                  'tmp_1'=> isset($post['tmp_1']) ? $post['tmp_1'] : "",  
                  'hep_1'=> isset($post['hep_1']) ? $post['hep_1'] : "",  
                  'observasi_jam2'=> isset($post['jam_2']) ? $post['jam_2'] : "",
                  'tb_2'=> isset($post['td_2']) ? $post['td_2'] : "", 
                  'n_2'=> isset($post['n_2']) ? $post['n_2'] : "", 
                  'p_2'=> isset($post['p_2']) ? $post['p_2'] : "", 
                  's_2'=> isset($post['s_2']) ? $post['s_2'] : "", 
                  'ufg_2'=> isset($post['ufg_2']) ? $post['ufg_2'] : "",  
                  'ufr_2'=> isset($post['ufr_2']) ? $post['ufr_2'] : "",  
                  'uf_2'=> isset($post['uf_2']) ? $post['uf_2'] : "", 
                  'qb_2'=> isset($post['qb_2']) ? $post['qb_2'] : "", 
                  'qd_2'=> isset($post['qd_2']) ? $post['qd_2'] : "", 
                  'vp_2'=> isset($post['vp_2']) ? $post['vp_2'] : "", 
                  'ap_2'=> isset($post['ap_2']) ? $post['ap_2'] : "", 
                  'tmp_2'=> isset($post['tmp_2']) ? $post['tmp_2'] : "",  
                  'hep_2'=> isset($post['hep_2']) ? $post['hep_2'] : "",  
                  'observasi_jam3'=> isset($post['jam_3']) ? $post['jam_3'] : "", 
                  'tb_3'=> isset($post['td_3']) ? $post['td_3'] : "",
                  'n_3'=> isset($post['tb_3']) ? $post['tb_3'] : "", 
                  'p_3'=> isset($post['p_3']) ? $post['p_3'] : "", 
                  's_3'=> isset($post['s_3']) ? $post['s_3'] : "", 
                  'ufg_3'=> isset($post['ufg_3']) ? $post['ufg_3'] : "",  
                  'ufr_3'=> isset($post['ufr_3']) ? $post['ufr_3'] : "",  
                  'uf_3'=> isset($post['uf_3']) ? $post['uf_3'] : "", 
                  'qb_3'=> isset($post['qb_3']) ? $post['qb_3'] : "", 
                  'qd_3'=> isset($post['qd_3']) ? $post['qd_3'] : "", 
                  'vp_3'=> isset($post['vp_3']) ? $post['vp_3'] : "", 
                  'ap_3'=> isset($post['ap_3']) ? $post['ap_3'] : "", 
                  'tmp_3' => isset($post['tmp_3']) ? $post['tmp_3'] : "", 
                  'hep_3'=> isset($post['hep_3']) ? $post['hep_3'] : "",  
                  'observasi_jam4'=> isset($post['jam_4']) ? $post['jam_4'] : "", 
                  'tb_4'=> isset($post['td_4']) ? $post['td_4'] : "", 
                  'n_4'=> isset($post['n_4']) ? $post['n_4'] : "", 
                  'p_4'=> isset($post['p_4']) ? $post['p_4'] : "", 
                  's_4'=> isset($post['s_4']) ? $post['s_4'] : "", 
                  'ufg_4'=> isset($post['ufg_4']) ? $post['ufg_4'] : "", 
                  'ufr_4'=> isset($post['ufr_4']) ? $post['ufr_4'] : "",  
                  'uf_4'=> isset($post['uf_4']) ? $post['uf_4'] : "", 
                  'qb_4'=> isset($post['qb_4']) ? $post['qb_4'] : "", 
                  'qd_4'=> isset($post['qd_4']) ? $post['qd_4'] : "", 
                  'vp_4'=> isset($post['vp_4']) ? $post['vp_4'] : "", 
                  'ap_4'=> isset($post['ap_4']) ? $post['ap_4'] : "", 
                  'tmp_4'=> isset($post['tmp_4']) ? $post['tmp_4'] : "", 
                  'hep_4'=> isset($post['hep_4']) ? $post['hep_4'] : "",  
                  'observasi_jam5'=> isset($post['jam_5']) ? $post['jam_5'] : "", 
                  'tb_5'=> isset($post['td_5']) ? $post['td_5'] : "", 
                  'n_5'=> isset($post['n_5']) ? $post['n_5'] : "", 
                  'p_5'=> isset($post['p_5']) ? $post['p_5'] : "", 
                  's_5'=> isset($post['s_5']) ? $post['s_5'] : "", 
                  'ufg_5' => isset($post['ufg_5']) ? $post['ufg_5'] : "", 
                  'ufr_5'=> isset($post['ufr_5']) ? $post['ufr_5'] : "",  
                  'uf_5'=> isset($post['uf_5']) ? $post['uf_5'] : "", 
                  'qb_5'=> isset($post['qb_5']) ? $post['qb_5'] : "", 
                  'qd_5'=> isset($post['qd_5']) ? $post['qd_5'] : "", 
                  'vp_5'=> isset($post['vp_5']) ? $post['vp_5'] : "", 
                  'ap_5'=> isset($post['ap_5']) ? $post['ap_5'] : "", 
                  'tmp_5'=> isset($post['tmp_5']) ? $post['tmp_5'] : "",  
                  'hep_5'=> isset($post['hep_5']) ? $post['hep_5'] : "",
                  'observasi_jam6'=> isset($post['jam_6']) ? $post['jam_6'] : "", 
                  'tb_6'=> isset($post['td_6']) ? $post['td_6'] : "", 
                  'n_6'=> isset($post['n_6']) ? $post['n_6'] : "", 
                  'p_6'=> isset($post['p_6']) ? $post['p_6'] : "", 
                  's_6'=> isset($post['s_6']) ? $post['s_6'] : "", 
                  'ufg_6' => isset($post['ufg_6']) ? $post['ufg_6'] : "", 
                  'ufr_6'=> isset($post['ufr_6']) ? $post['ufr_6'] : "",  
                  'uf_6'=> isset($post['uf_6']) ? $post['uf_6'] : "", 
                  'qb_6'=> isset($post['qb_6']) ? $post['qb_6'] : "", 
                  'qd_6'=> isset($post['qd_6']) ? $post['qd_6'] : "", 
                  'vp_6'=> isset($post['vp_6']) ? $post['vp_6'] : "", 
                  'ap_6'=> isset($post['ap_6']) ? $post['ap_6'] : "", 
                  'tmp_6'=> isset($post['tmp_6']) ? $post['tmp_6'] : "",  
                  'hep_6'=> isset($post['hep_6']) ? $post['hep_6'] : "", 
                  'observasi_jam7'=> isset($post['jam_7']) ? $post['jam_7'] : "", 
                  'tb_7'=> isset($post['td_7']) ? $post['td_7'] : "", 
                  'n_7'=> isset($post['n_7']) ? $post['n_7'] : "", 
                  'p_7'=> isset($post['p_7']) ? $post['p_7'] : "", 
                  's_7'=> isset($post['s_7']) ? $post['s_7'] : "", 
                  'ufg_7' => isset($post['ufg_7']) ? $post['ufg_7'] : "", 
                  'ufr_7'=> isset($post['ufr_7']) ? $post['ufr_7'] : "",  
                  'uf_7'=> isset($post['uf_7']) ? $post['uf_7'] : "", 
                  'qb_7'=> isset($post['qb_7']) ? $post['qb_7'] : "", 
                  'qd_7'=> isset($post['qd_7']) ? $post['qd_7'] : "", 
                  'vp_7'=> isset($post['vp_7']) ? $post['vp_7'] : "", 
                  'ap_7'=> isset($post['ap_7']) ? $post['ap_7'] : "", 
                  'tmp_7'=> isset($post['tmp_7']) ? $post['tmp_7'] : "",  
                  'hep_7'=> isset($post['hep_7']) ? $post['hep_7'] : "",  
                  'observasi_jam8'=> isset($post['jam_8']) ? $post['jam_8'] : "", 
                  'tb_8'=> isset($post['td_8']) ? $post['td_8'] : "", 
                  'n_8'=> isset($post['n_8']) ? $post['n_8'] : "", 
                  'p_8'=> isset($post['p_8']) ? $post['p_8'] : "", 
                  's_8'=> isset($post['s_8']) ? $post['s_8'] : "", 
                  'ufg_8' => isset($post['ufg_8']) ? $post['ufg_8'] : "", 
                  'ufr_8'=> isset($post['ufr_8']) ? $post['ufr_8'] : "",  
                  'uf_8'=> isset($post['uf_8']) ? $post['uf_8'] : "", 
                  'qb_8'=> isset($post['qb_8']) ? $post['qb_8'] : "", 
                  'qd_8'=> isset($post['qd_8']) ? $post['qd_8'] : "", 
                  'vp_8'=> isset($post['vp_8']) ? $post['vp_8'] : "", 
                  'ap_8'=> isset($post['ap_8']) ? $post['ap_8'] : "", 
                  'tmp_8'=> isset($post['tmp_8']) ? $post['tmp_8'] : "",  
                  'hep_8'=> isset($post['hep_8']) ? $post['hep_8'] : "", 
                  'oral_intake'=> isset($post['oral']) ? $post['oral'] : "", 
                  'iv_intake'=> isset($post['iv']) ? $post['iv'] : "",   
                  'priming_intake'=> isset($post['intakeprinting']) ? $post['intakeprinting'] : "",  
                  'wash_out'=> isset($post['washout']) ? $post['washout'] : "",    
                  'transfusi_intake'=> isset($post['transfusiintake']) ? $post['transfusiintake'] : "",    
                  'dll_itake'=> isset($post['dllintake']) ? $post['dllintake'] : "",   
                  'total_intake'=> isset($post['totalintake']) ? $post['totalintake'] : "", 
                  'uf_output'=> isset($post['uf_output']) ? $post['uf_output'] : "",   
                  'urine_output'=> isset($post['urine']) ? $post['urine'] : "",    
                  'muntah_output'=> isset($post['muntah']) ? $post['muntah'] : "",
                  'pendarahan_output'=> isset($post['darah']) ? $post['darah'] : "",   
                  'total_output'=> isset($post['totalout']) ? $post['totalout'] : "",   
                  'total_balace'=> isset($post['total_balace']) ? $post['total_balace'] : "",
                  'oleh'=> isset($post['oleh']) ? $post['oleh'] : "",
                  'dialiser' => isset($post['dialiser']) ? $post['dialiser'] : "",
                  'prim_vol' => isset($post['priming_volume']) ? $post['priming_volume'] : "",
                  'dialisat' => isset($post['dialisat']) ? $post['dialisat'] : "",
                  'akses_vaskular' => isset($post['akses_vaskuler']) ? $post['akses_vaskuler'] : "",
                  'heparawal' => isset($post['hep_lama']) ? $post['hep_lama'] : "",
                  'heparlanjutan' => isset($post['hep_lanjutan']) ? $post['hep_lanjutan'] : "",

                  //tindakan
                  'jam_posisi' => isset($post['jam_posisi']) ? $post['jam_posisi'] : "",
                  'supinasi' => isset($post['supinasi']) ? $post['supinasi'] : "",
                  'semifowler' => isset($post['semifowler']) ? $post['semifowler'] : "",
                  'fowler' => isset($post['fowler']) ? $post['fowler'] : "",
                  'jam_kemampuan' => isset($post['jam_kemampuan']) ? $post['jam_kemampuan'] : "",
                  'intakecairan' => isset($post['intakecairan']) ? $post['intakecairan'] : "",
                  'peningkatanbb' => isset($post['peningkatanbb']) ? $post['peningkatanbb'] : "",
                  'jam_ultrafiltrasi' => isset($post['jam_ultrafiltrasi']) ? $post['jam_ultrafiltrasi'] : "",
                  'avshunt' => isset($post['avshunt']) ? $post['avshunt'] : "",
                  'cdl' => isset($post['cdl']) ? $post['cdl'] : "",
                  'jam_insertasi' => isset($post['jam_insertasi']) ? $post['jam_insertasi'] : "",
                  'hipotensi' => isset($post['hipotensi']) ? $post['hipotensi'] : "",
                  'hipertensi' => isset($post['hipertensi']) ? $post['hipertensi'] : "",
                  'kramotot' => isset($post['kramotot']) ? $post['kramotot'] : "",
                  'mualmuntah' => isset($post['mualmuntah']) ? $post['mualmuntah'] : "",
                  'sakitkepala' => isset($post['sakitkepala']) ? $post['sakitkepala'] : "",
                  'terapiiv' => isset($post['terapiiv']) ? $post['terapiiv'] : "",
                  'terapisc' => isset($post['terapisc']) ? $post['terapisc'] : "",
                  'terapioral' => isset($post['terapioral']) ? $post['terapioral'] : "",
                  'goldar' => isset($post['goldar']) ? $post['goldar'] : "",
                  'nostok' => isset($post['nostok']) ? $post['nostok'] : "",
                  'jumlahtransfusi' => isset($post['jumlahtransfusi']) ? $post['jumlahtransfusi'] : "",
                  'jenistransfusi' => isset($post['jenistransfusi']) ? $post['jenistransfusi'] : "",
                  'keterangan' => isset($post['ket']) ? $post['ket'] : "",
                  'perawat1' => file_get_contents($post['perawat1']),
                  'perawat2' => file_get_contents($post['perawat2']),
              );

              $this->db->insert('keperawatan.tb_observasi_hemodialisa', $dataObservasiHd);
          }
      }
  }

  public function lihathistoryHd(){
      $id = $this->input->post('id'); 
      $historyHd = $this->HemodialisaModel->historyDetailHd($id);
      //echo "<pre>";
      //print_r($historyHd);
      //exit();
      //$nokun = 
      foreach ($historyHd as $Hd):
      ?>
      <div class="konsultasi">
          <div class="row form-group">
              <label for="dokter_pengirim" class="col-sm-12 col-md-3 col-form-label">
                  Dokter Pengirim
              </label>
              <div class="col-sm-12 col-md-9">
                  <input type="text" name="dokter" class="form-control" value="<?php echo $Hd['NAMADOKTER']; ?>">
              </div>
          </div>
          <div class="row form-group">
              <label for="ruang_rawat" class="col-sm-12 col-md-3 col-form-label">
                  Ruang Rawat
              </label>
              <div class="col-sm-12 col-md-9">
                  <input type="text" class="form-control" value="<?php echo $Hd['ruang_rawat']; ?>">
              </div>
          </div>
          <div class="row form-group">
              <label for="hd_ke" class="col-sm-12 col-md-3 col-form-label">
                  HD Ke
              </label>
              <div class="col-sm-12 col-md-9">
                  <input type="text" class="form-control" value="<?php echo $Hd['hdke']; ?>">
              </div>
          </div>
          <div class="row form-group">
              <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                  Mulai HD
              </label>
              <div class="col-sm-12 col-md-9">
                  <input type="text" class="form-control" value="<?php echo $Hd['mulaihd']; ?>">
              </div>
          </div>
          <div class="row form-group">
              <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                  Selesai HD
              </label>
              <div class="col-sm-12 col-md-9">
                  <input type="text" class="form-control" value="<?php echo $Hd['selesaihd']; ?>">
              </div>
          </div>
          <div class="row form-group">
              <label for="lama_hd" class="col-sm-12 col-md-3 col-form-label">
                  Lama HD
              </label>
              <div class="col-sm-12 col-md-9">
                  <input type="text" class="form-control" value="<?php echo $Hd['lamahd']; ?>">
              </div>
          </div>
          <div class="row">
              <div class="col-sm-6"> 
                  <div id="pre_hd" data-parent="#accordion">
                  <div class="card new-card">
                      <div class="card">
                      <div class="card-body">
                          <div class="row form-group">
                              <label for="dialiser" class="col-sm-12 col-md-3 col-form-label">
                                  Dialiser
                              </label>
                              <div class="col-sm-12 col-md-9">
                                  <input type="text" class="form-control" value="<?php echo $Hd['dialiser']; ?>">
                              </div>
                          </div>
                          <div class="row form-group">
                              <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                                  Priming Volume
                              </label>
                              <div class="col-sm-12 col-md-9">
                                  <input type="text" class="form-control" value="<?php echo $Hd['priming_volume']; ?>">
                              </div>
                          </div>
                          
                          <div class="row form-group">
                          <label for="dialisat" class="col-sm-12 col-md-3 col-form-label">
                              Dialisat
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['dialisat']; ?>">
                          </div>
                          </div>
                      </div>
                      </div>
                  </div>
                  </div>
              </div>
              <div class="col-sm-6"> 
                  <div id="pre_hd" data-parent="#accordion">
                  <div class="card new-card">
                      <div class="card">
                      <div class="card-body">
                          <div class="row form-group">
                          <label for="akses_vaskuler" class="col-sm-12 col-md-3 col-form-label">
                              Akses Vaskuler
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['akses_vaskuler']; ?>">
                          </div>
                          </div>
                          <div class="row form-group">
                          <label for="hep_lama" class="col-sm-12 col-md-3 col-form-label">
                              Heparin Dosis Awal
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['heparawal']; ?>">
                          </div>
                          </div>
                          
                          <div class="row form-group">
                          <label for="hep_lanjutan" class="col-sm-12 col-md-3 col-form-label">
                              Heparin Dosis Lanjutan
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['heparlanjutan']; ?>">
                          </div>
                          </div>
                      </div>
                      </div>
                  </div>
                  </div>
              </div>
              <div class="row">
              <div class="col-sm-6"> 
                  <!-- PRE HD -->
                  <div id="pre_hd" data-parent="#accordion">
                  <div class="card new-card">
                      <div class="card-header new-card-header" id="heading">
                      <h5 class="m-0">PRE HD</h5>
                      </div>
                      <div class="card">
                      <div class="card-body">
                          <div class="row form-group">
                              <label for="kesadaran_pre" class="col-sm-12 col-md-3 col-form-label">
                                  Kesadaran
                              </label>
                              <div class="col-sm-12 col-md-9">
                                  <input type="text" class="form-control" value="<?php echo $Hd['heparlanjutan']; ?>">
                              </div>
                          </div>
                          <div class="row form-group">
                              <label for="keluhan_pre" class="col-sm-12 col-md-3 col-form-label">
                                  Keluhan
                              </label>
                              <div class="col-sm-12 col-md-9">
                                  <input type="text" class="form-control" value="<?php echo $Hd['keluhan_pre']; ?>">
                              </div>
                          </div>
                          <div class="row form-group">
                          <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                              Tanda-tanda Vital
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <div class="card">
                              <div class="card-body">
                                  <div class="row form-group">
                                  <div class="col-sm-12 col-md-5">
                                      <label for="td_pre" class="col-sm-12 col-md-5 col-form-label">
                                          TD
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['td_pre_sis']; ?>">
                                      </div><br>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['td_pre_dis']; ?>">
                                      </div>
                                  </div>
                                  <div class="col-sm-12 col-md-5">
                                      <label for="n_pre" class="col-sm-12 col-md-4 col-form-label">
                                          N
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['n_pre']; ?>">
                                      </div>
                                  </div>
                                  <div class="col-sm-12 col-md-5">
                                      <label for="p_pre" class="col-sm-12 col-md-4 col-form-label">
                                          P
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['p_pre']; ?>">
                                      </div>
                                  </div>
                                  <div class="col-sm-12 col-md-5">
                                      <label for="s_pre" class="col-sm-12 col-md-3 col-form-label">
                                          S
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['s_pre']; ?>">
                                      </div>
                                  </div>
                                  </div>
                              </div>
                              </div>
                          </div>
                          </div>
                          <input type="checkbox" value="0" id="tb_bb_pre" name="tb_bb_pre" > &nbsp; &nbsp; BB/TB tidak dapat diukur
                          <br><br>
                          <div class="row form-group">
                          <label for="bb_pre" class="col-sm-12 col-md-3 col-form-label">
                              BB
                          </label>
                          <div class="col-sm-12 col-md-3">
                              <input type="text" class="form-control" value="<?php echo $Hd['bb_pre']; ?>">
                          </div>
                          <label for="tb_pre" class="col-sm-12 col-md-3 col-form-label" align="right">
                              TB
                          </label>
                          <div class="col-sm-12 col-md-3">
                              <input type="text" class="form-control" value="<?php echo $Hd['tb_pre']; ?>">
                          </div>
                          </div>
                          <div class="row form-group">
                          <label for="bb_kering_pre" class="col-sm-12 col-md-3 col-form-label">
                              BB Kering
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['bb_kering_pre']; ?>">
                          </div>
                          </div>
                          <div class="row form-group">
                          <label for="bb_hd_lama" class="col-sm-12 col-md-3 col-form-label">
                              BB HD yang lalu
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['bb_hd_berlaku_pre']; ?>">
                          </div>
                          </div>
                          <div class="row form-group">
                          <label for="naik_bb" class="col-sm-12 col-md-3 col-form-label">
                              Kenaikan BB
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['kenaikan_bb']; ?>">
                          </div>
                          </div>
                      </div>
                      </div>
                  </div>
                  </div>
              </div>
              <div class="col-sm-6"> 
                  <!-- POST HD -->
                  <div id="pre_hd" data-parent="#accordion">
                  <div class="card new-card">
                      <div class="card-header new-card-header" id="heading">
                      <h5 class="m-0">POST HD</h5>
                      </div>
                      <div class="card">
                      <div class="card-body">
                          <div class="row form-group">
                              <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                                  Kesadaran
                              </label>
                              <div class="col-sm-12 col-md-9">
                                  <input type="text" class="form-control" value="<?php echo $Hd['kesadaran_post']; ?>">
                              </div>
                          </div>
                          <div class="row form-group">
                              <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                                  Keluhan
                              </label>
                              <div class="col-sm-12 col-md-9">
                                  <input type="text" class="form-control" value="<?php echo $Hd['keluhan_post']; ?>">
                              </div>
                          </div>
                          <div class="row form-group">
                          <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                              Tanda-tanda Vital
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <div class="card">
                              <div class="card-body">
                                  <div class="row form-group">
                                  <div class="col-sm-12 col-md-5">
                                      <label for="td_post" class="col-sm-12 col-md-5 col-form-label">
                                          TD
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['td_post_sis']; ?>">
                                      </div><br>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['td_post_dis']; ?>">
                                      </div>
                                  </div>
                                  <div class="col-sm-12 col-md-5">
                                      <label for="n_post" class="col-sm-12 col-md-4 col-form-label">
                                          N
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['n_post']; ?>">
                                      </div>
                                  </div>
                                  <div class="col-sm-12 col-md-5">
                                      <label for="p_post" class="col-sm-12 col-md-4 col-form-label">
                                          P
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['p_post']; ?>">
                                      </div>
                                  </div>
                                  <div class="col-sm-12 col-md-5">
                                      <label for="s_post" class="col-sm-12 col-md-3 col-form-label">
                                          S
                                      </label>
                                      <div class="col-sm-12 col-md-12">
                                          <input type="text" class="form-control" value="<?php echo $Hd['s_post']; ?>">
                                      </div>
                                  </div>
                                  </div>
                              </div>
                              </div>
                          </div>
                          </div>
                          <input type="checkbox" value="0" id="tb_bb_post" name="tb_bb-post" > &nbsp; &nbsp; BB/TB tidak dapat diukur
                          <br><br>
                          <div class="row form-group">
                          <label for="bb_post" class="col-sm-12 col-md-3 col-form-label">
                              BB
                          </label>
                          <div class="col-sm-12 col-md-3">
                              <input type="text" class="form-control" value="<?php echo $Hd['bb_post']; ?>">
                          </div>
                          <label for="tb_post" class="col-sm-12 col-md-3 col-form-label" align="right">
                              TB
                          </label>
                          <div class="col-sm-12 col-md-3">
                              <input type="text" class="form-control" value="<?php echo $Hd['tb_post']; ?>">
                          </div>
                          </div>
                          <div class="row form-group">
                          <label for="bb_kering_post" class="col-sm-12 col-md-3 col-form-label">
                              BB Kering
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['bb_kering_post']; ?>">
                          </div>
                          </div>
                          <div class="row form-group">
                          <label for="bb_hd_post" class="col-sm-12 col-md-3 col-form-label">
                              BB HD yang lalu
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['bb_hd_post']; ?>">
                          </div>
                          </div>
                          <div class="row form-group">
                          <label for="penurunanbb" class="col-sm-12 col-md-3 col-form-label">
                              Penurunan BB
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" value="<?php echo $Hd['penurun_bb_post']; ?>">
                          </div>
                          </div>
                      </div>
                      </div>
                  </div>
                  </div>
              </div>
          </div>
          <div class="card new-card col-xl-12">
              <h5 class="m-0 card-header new-card-header">OBSERVASI</h5>
              <br>
              <caption>Observasi ke 1</caption>
              <br>
              <table border="1" style="padding: 5px;" id="observasi">
              <thead>
                  <tr>
                  <th>Jam</th>                    
                  <th>TD</th>                    
                  <th>N</th>                    
                  <th>P</th>                    
                  <th>S</th>                    
                  <th>UFG</th>                    
                  <th>UFR</th>                    
                  <th>UF</th>                    
                  <th>QB</th>                    
                  <th>QD</th>                    
                  <th>VP</th>                    
                  <th>AP</th>                    
                  <th>TMP</th>                    
                  <th>HEP</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                    <td width="80px;">
                      <input type="text" class="form-control" value="<?php echo $Hd['observasi_jam1']; ?>">
                    </td>                           
                    <td><input type="text" class="form-control" value="<?php echo $Hd['tb_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['n_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['p_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['s_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufg_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufr_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['uf_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qb_1']; ?>"></td> 
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qd_1']; ?>"></td>                     
                    <td><input type="text" class="form-control " value="<?php echo $Hd['vp_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ap_1']; ?>"></td>
                    <td><input type="text" class="form-control " value="<?php echo $Hd['tmp_1']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['hep_1']; ?>"></td>
                  </tr>
                </tbody>
              </tabel>
              <caption style="color:white">Observasi ke 2</caption>
              <table border="1" style="padding: 5px;" id="observasi">
              <thead>
                  <tr>
                  <th>Jam</th>                    
                  <th>TD</th>                    
                  <th>N</th>                    
                  <th>P</th>                    
                  <th>S</th>                    
                  <th>UFG</th>                    
                  <th>UFR</th>                    
                  <th>UF</th>                    
                  <th>QB</th>                    
                  <th>QD</th>                    
                  <th>VP</th>                    
                  <th>AP</th>                    
                  <th>TMP</th>                    
                  <th>HEP</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                    <td width="80px;">
                      <input type="text" class="form-control" value="<?php echo $Hd['observasi_jam2']; ?>">
                    </td>                           
                    <td><input type="text" class="form-control" value="<?php echo $Hd['tb_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['n_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['p_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['s_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufg_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufr_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['uf_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qb_2']; ?>"></td> 
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qd_2']; ?>"></td>                     
                    <td><input type="text" class="form-control " value="<?php echo $Hd['vp_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ap_2']; ?>"></td>
                    <td><input type="text" class="form-control " value="<?php echo $Hd['tmp_2']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['hep_2']; ?>"></td>
                  </tr>
                </tbody>
              </tabel>
              <caption style="color:white">Observasi ke 3</caption>
              <table border="1" style="padding: 5px;" id="observasi">
              <thead>
                  <tr>
                  <th>Jam</th>                    
                  <th>TD</th>                    
                  <th>N</th>                    
                  <th>P</th>                    
                  <th>S</th>                    
                  <th>UFG</th>                    
                  <th>UFR</th>                    
                  <th>UF</th>                    
                  <th>QB</th>                    
                  <th>QD</th>                    
                  <th>VP</th>                    
                  <th>AP</th>                    
                  <th>TMP</th>                    
                  <th>HEP</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                    <td width="80px;">
                      <input type="text" class="form-control" value="<?php echo $Hd['observasi_jam3']; ?>">
                    </td>                           
                    <td><input type="text" class="form-control" value="<?php echo $Hd['tb_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['n_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['p_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['s_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufg_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufr_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['uf_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qb_3']; ?>"></td> 
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qd_3']; ?>"></td>                     
                    <td><input type="text" class="form-control " value="<?php echo $Hd['vp_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ap_3']; ?>"></td>
                    <td><input type="text" class="form-control " value="<?php echo $Hd['tmp_3']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['hep_3']; ?>"></td>
                  </tr>
                </tbody>
              </tabel>
              <caption style="color:white">Observasi ke 4</caption>
              <table border="1" style="padding: 5px;" id="observasi">
              <thead>
                  <tr>
                  <th>Jam</th>                    
                  <th>TD</th>                    
                  <th>N</th>                    
                  <th>P</th>                    
                  <th>S</th>                    
                  <th>UFG</th>                    
                  <th>UFR</th>                    
                  <th>UF</th>                    
                  <th>QB</th>                    
                  <th>QD</th>                    
                  <th>VP</th>                    
                  <th>AP</th>                    
                  <th>TMP</th>                    
                  <th>HEP</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                    <td width="80px;">
                      <input type="text" class="form-control" value="<?php echo $Hd['observasi_jam4']; ?>">
                    </td>                           
                    <td><input type="text" class="form-control" value="<?php echo $Hd['tb_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['n_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['p_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['s_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufg_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufr_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['uf_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qb_4']; ?>"></td> 
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qd_4']; ?>"></td>                     
                    <td><input type="text" class="form-control " value="<?php echo $Hd['vp_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ap_4']; ?>"></td>
                    <td><input type="text" class="form-control " value="<?php echo $Hd['tmp_4']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['hep_4']; ?>"></td>
                  </tr>
                </tbody>
              </tabel>
              <caption style="color:white">Observasi ke 6</caption>
              <table border="1" style="padding: 5px;" id="observasi">
              <thead>
                  <tr>
                  <th>Jam</th>                    
                  <th>TD</th>                    
                  <th>N</th>                    
                  <th>P</th>                    
                  <th>S</th>                    
                  <th>UFG</th>                    
                  <th>UFR</th>                    
                  <th>UF</th>                    
                  <th>QB</th>                    
                  <th>QD</th>                    
                  <th>VP</th>                    
                  <th>AP</th>                    
                  <th>TMP</th>                    
                  <th>HEP</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                    <td width="80px;">
                      <input type="text" class="form-control" value="<?php echo $Hd['observasi_jam6']; ?>">
                    </td>                           
                    <td><input type="text" class="form-control" value="<?php echo $Hd['tb_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['n_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['p_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['s_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufg_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufr_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['uf_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qb_6']; ?>"></td> 
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qd_6']; ?>"></td>                     
                    <td><input type="text" class="form-control " value="<?php echo $Hd['vp_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ap_6']; ?>"></td>
                    <td><input type="text" class="form-control " value="<?php echo $Hd['tmp_6']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['hep_6']; ?>"></td>
                  </tr>
              </tbody>
              </tabel>
              <caption style="color:white">Observasi ke 7</caption>
              <table border="1" style="padding: 5px;" id="observasi">
              <thead>
                  <tr>
                  <th>Jam</th>                    
                  <th>TD</th>                    
                  <th>N</th>                    
                  <th>P</th>                    
                  <th>S</th>                    
                  <th>UFG</th>                    
                  <th>UFR</th>                    
                  <th>UF</th>                    
                  <th>QB</th>                    
                  <th>QD</th>                    
                  <th>VP</th>                    
                  <th>AP</th>                    
                  <th>TMP</th>                    
                  <th>HEP</th>
                  </tr>
              </thead>
              <tbody>
                  <tr>
                    <td width="80px;">
                      <input type="text" class="form-control" value="<?php echo $Hd['observasi_jam7']; ?>">
                    </td>                           
                    <td><input type="text" class="form-control" value="<?php echo $Hd['tb_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['n_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['p_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['s_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufg_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ufr_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['uf_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qb_7']; ?>"></td> 
                    <td><input type="text" class="form-control " value="<?php echo $Hd['qd_7']; ?>"></td>                     
                    <td><input type="text" class="form-control " value="<?php echo $Hd['vp_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['ap_7']; ?>"></td>
                    <td><input type="text" class="form-control " value="<?php echo $Hd['tmp_7']; ?>"></td>                    
                    <td><input type="text" class="form-control " value="<?php echo $Hd['hep_7']; ?>"></td>
                  </tr>
                </tbody>
              </tabel>
              <caption style="color:white">Observasi ke 8</caption>
              <table border="1" style="padding: 5px;" id="observasi">
                  <thead>
                      <tr>
                      <th>Jam</th>                    
                      <th>TD</th>                    
                      <th>N</th>                    
                      <th>P</th>                    
                      <th>S</th>                    
                      <th>UFG</th>                    
                      <th>UFR</th>                    
                      <th>UF</th>                    
                      <th>QB</th>                    
                      <th>QD</th>                    
                      <th>VP</th>                    
                      <th>AP</th>                    
                      <th>TMP</th>                    
                      <th>HEP</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr>
                      <td width="80px;">
                          <input type="text" class="form-control" value="<?php echo $Hd['observasi_jam8']; ?>">
                      </td>                           
                      <td><input type="text" class="form-control" value="<?php echo $Hd['tb_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['n_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['p_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['s_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['ufg_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['ufr_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['uf_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['qb_8']; ?>"></td> 
                      <td><input type="text" class="form-control " value="<?php echo $Hd['qd_8']; ?>"></td>                     
                      <td><input type="text" class="form-control " value="<?php echo $Hd['vp_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['ap_8']; ?>"></td>
                      <td><input type="text" class="form-control " value="<?php echo $Hd['tmp_8']; ?>"></td>                    
                      <td><input type="text" class="form-control " value="<?php echo $Hd['hep_8']; ?>"></td>
                      </tr>
                  </tbody>
              </tabel>
          </div>
          <caption>
          <!-- BALANCE CAIRAN -->
          <div class="card new-card">
            <div class="card-header new-card-header" id="heading">
              <h5 class="m-0" align="center">BALANCE CAIRAN</h5>
            </div>
            <div class="row">
            <div class="col-sm-6"> 
              <!-- INTAKE -->
              <div id="pre_hd" data-parent="#accordion">
                <div class="card new-card">
                  <div class="card-header new-card-header" id="heading">
                    <h5 class="m-0" align="center">INTAKE</h5>
                  </div>
                  <div class="card">
                    <div class="card-body">
                      <div class="row form-group">
                          <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                              Oral
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" id="oral" name="oral"
                                  autocomplete="off" value="<?php echo $Hd['oral_intake']; ?>">
                          </div>
                      </div>
                      <div class="row form-group">
                          <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                              IV
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" id="iv" name="iv"
                                  autocomplete="off" value="<?php echo $Hd['iv_intake']; ?>">
                          </div>
                      </div>
                      
                      <div class="row form-group">
                        <label for="bb_tb" class="col-sm-12 col-md-3 col-form-label">
                            Intake Priming
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="intakeprinting" name="intakeprinting"
                                autocomplete="off" value="<?php echo $Hd['primingprinting']; ?>">
                        </div>
                      </div>
                      <div class="row form-group">
                        <label for="bb_kering" class="col-sm-12 col-md-3 col-form-label">
                            Wash Out
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="washout" name="washout"
                                autocomplete="off" value="<?php echo $Hd['wash_out']; ?>">
                        </div>
                      </div>
                      <div class="row form-group">
                        <label for="transfusiintake" class="col-sm-12 col-md-3 col-form-label">
                            Transfusi
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="transfusiintake" name="transfusiintake"
                                autocomplete="off" value="<?php echo $Hd['transfusi_intake']; ?>">
                        </div>
                      </div>
                      <div class="row form-group">
                        <label for="kenaikan_bb" class="col-sm-12 col-md-3 col-form-label">
                            Dll
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="dllintake" name="dllintake"
                                autocomplete="off" value="<?php echo $Hd['dll_intake']; ?>">
                        </div>
                      </div>
                      <div class="row form-group">
                        <label for="totalintake" class="col-sm-12 col-md-3 col-form-label">
                            Total
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="totalintake" name="totalintake"
                                autocomplete="off" value="<?php echo $Hd['total_intake']; ?>">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-sm-6"> 
              <!-- OUTPUT -->
              <div id="pre_hd" data-parent="#accordion">
                <div class="card new-card">
                  <div class="card-header new-card-header" id="heading">
                    <h5 class="m-0" align="center">OUT PUT</h5>
                  </div>
                  <div class="card">
                    <div class="card-body">
                      <div class="row form-group">
                          <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                              UF
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" id="uf_output" name="uf_output"
                                  autocomplete="off" value="<?php echo $Hd['uf_output']; ?>">
                          </div>
                      </div>
                      <div class="row form-group">
                          <label for="diagnosis_kerja" class="col-sm-12 col-md-3 col-form-label">
                              Urine
                          </label>
                          <div class="col-sm-12 col-md-9">
                              <input type="text" class="form-control" id="urine" name="urine"
                                  autocomplete="off" value="<?php echo $Hd['urine_output']; ?>">
                          </div>
                      </div>
                      
                      <div class="row form-group">
                        <label for="bb_tb" class="col-sm-12 col-md-3 col-form-label">
                            Muntah
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="muntah" name="muntah"
                                autocomplete="off" value="<?php echo $Hd['muntah_output']; ?>">
                        </div>
                      </div>
                      <div class="row form-group">
                        <label for="bb_kering" class="col-sm-12 col-md-3 col-form-label">
                            Pendarahan
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="darah" name="darah"
                                autocomplete="off" value="<?php echo $Hd['pendarahan_output']; ?>">
                        </div>
                      </div>
                      
                      <div class="row form-group">
                        <label for="kenaikan_bb" class="col-sm-12 col-md-3 col-form-label">
                            Total
                        </label>
                        <div class="col-sm-12 col-md-9">
                            <input type="text" class="form-control" id="totalout" name="totalout"
                                autocomplete="off" value="<?php echo $Hd['total_output']; ?>">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card-header new-card-header" id="heading">
            <h5 class="m-0" align="center">TOTAL BALANCE : "<?php echo $Hd['balance']; ?>"</h5>
          </div>
          </div>
          </caption>
          <caption>
          <div class="card new-card">
              <div class="card-header new-card-header" id="heading">
              <h5 class="m-0" align="center">TINDAKAN HEMODIALISA</h5>
              </div>
              <div class="card">
              <div class="row card-body">
                <div class="col-sm-2">Jam</div>
                <div class="col-sm-10">Kegiatan</div>
              </div>
              <div class="row card-body">
                <div class="col-sm-2"><input type="text" class="form-control without_ampm" id="jam_posisi" name="jam_posisi" autocomplete="off" value="<?php echo $Hd['jam_posisi']; ?>"></div>
                <div class="col-sm-10">
                  <label>Mengatur posisi nyaman sesui kebutuhan pasien</label>
                  <div class="form-group">
                    <div class="checkbox checkbox-primary jarak2">
                      <input type="checkbox" name="supinasi" id="supinasi" 
                      value="1"
                      <?php
                      if($Hd['supinasi'] == 1){
                        echo "checked";
                      }
                      ?>
                      >
                      <label for="supinasi">Supinasi</label>
                      &nbsp;
                      <input type="checkbox" name="semifowler" id="semifowler" <?php
                      if($Hd['semifowler'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="semifowler">Semi Fowler</label>
                      &nbsp;
                      <input type="checkbox" name="fowler" id="fowler" <?php
                      if($Hd['fowler'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="fowler">Fowler</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row card-body">
                <div class="col-sm-2"><input type="text" class="form-control without_ampm" id="jam_kemampuan" name="jam_kemampuan" autocomplete="off" value="<?php echo $Hd['jam_kemampuan']; ?>"></div>
                <div class="col-sm-10">
                  <label>Mengaji kemampuan pasien mengelola cairan</label>
                  <div class="form-group">
                    <div class="checkbox checkbox-primary jarak2">
                      <input type="checkbox" name="intakecairan" id="intakecairan" <?php
                      if($Hd['intakecairan'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="intakecairan">Mengatur Intake cairan</label>
                      &nbsp;
                      <input type="checkbox" name="peningkatanbb" id="peningkatanbb" <?php
                      if($Hd['peningkatanbb'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="peningkatanbb">Mengatur peningkatan BB</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row card-body">
                <div class="col-sm-2"><input type="text" class="form-control without_ampm" id="jam_ultrafiltrasi" name="jam_ultrafiltrasi" autocomplete="off" value="<?php echo $Hd['jam_ultrafiltrasi']; ?>"></div>
                <div class="col-sm-10">
                  <label>Memprogram ultrafiltrasi goal dan lama HD</label>
                </div>
              </div>
              <div class="row card-body">
                <div class="col-sm-2"><input type="text" class="form-control without_ampm" id="jam_insertasi" name="jam_insertasi" autocomplete="off" value="<?php echo $Hd['jam_insertasi']; ?>"></div>
                <div class="col-sm-10">
                  <label>Melakukan insertasi</label>
                  <div class="form-group">
                    <div class="checkbox checkbox-primary jarak2">
                      <input type="checkbox" name="avshunt" id="avshunt" <?php
                      if($Hd['avshunt'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="avshunt">AV Shunt</label>
                      &nbsp;
                      <input type="checkbox" name="cdl" id="cdl" <?php
                      if($Hd['cdl'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="cdl">CDL</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row card-body">
                <div class="col-sm-2"><input type="text" class="form-control without_ampm" id="jam_komplikasi" name="jam_komplikasi" autocomplete="off" value="<?php echo $Hd['jam_komplikasi']; ?>"></div>
                <div class="col-sm-10">
                  <label>Managemen Komplikasi HD</label>
                  <div class="form-group">
                    <div class="jarak2">
                      <input type="checkbox" name="hipotensi" id="hipotensi" <?php
                      if($Hd['hipotensi'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="hipotensi">Hipotensi</label>
                      <div class="row card-body">
                        <label>- Mengobservasi tanda-tanda vital</label>
                        <label>- Menurunkan QB dan UFG sesuai kondisi pasien</label> 
                        <label>- Memberikan terapi oksigen</label>
                        <label>- Memberikan cairan NaCl 0.9% sesuai kebutuhan</label>
                        <label>- Mengatur tidur pasien pada posisi supine atau trendelenburg dan tidak menggunakan bantal</label> 
                      </div>
                      <input type="checkbox" name="hipertensi" id="hipertensi" <?php
                      if($Hd['hipertensi'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="hipertensi">Hipertensi</label>
                      <div class="row card-body">
                        <label>- Mengobservasi tanda-tanda vital</label>
                        <label>- Menurunkan QB dan UFG sesuai kondisi pasien</label> 
                        <label>- Memberikan terapi oksigen</label>
                        <label>- Memberikan cairan NaCl 0.9% sesuai kebutuhan</label>
                        <label>- Mengatur tidur pasien pada posisi supine atau trendelenburg dan tidak menggunakan bantal</label> 
                      </div>
                      <input type="checkbox" name="kramotot" id="kramotot" <?php
                      if($Hd['kramotot'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="kramotot">Kram Otot</label>
                      <div class="row card-body">
                        <label>- Menurunkan QB sesuai kondisi pasien</label> 
                        <label>- Mengobservasi tanda-tanda vital</label>
                        <label>- Membantu pasien berdiri (mengunjakkan kaki ke lantai) bila memungkinkan</label>
                        <label>- Memberikan cairan NaCl 0.9% sesuai kebutuhan</label>
                      </div>
                      <input type="checkbox" name="mualmuntah" id="mualmuntah" <?php
                      if($Hd['mualmuntah1'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="mualmuntah">Mual dan Muntah</label>
                      <div class="row card-body">
                        <label>- Menurunkan QB dan UFG sesuai kondisi pasien</label> 
                        <label>- Mengobservasi tanda-tanda vital</label>
                        <label>- Memberikan obat-obatan sesuai instruksi</label>
                        <label>- Memberikan cairan NaCl 0.9% sesuai kebutuhan</label>
                      </div>
                      <input type="checkbox" name="sakitkepala" id="sakitkepala"  <?php
                      if($Hd['sakitkepala'] == 1){
                        echo "checked";
                      }
                      ?>>
                      <label for="sakitkepala">Sakit Kepala</label>
                      <div class="row card-body">
                        <label>- Menurunkan QB dan UFG sesuai kondisi pasien</label> 
                        <label>- Mengobservasi tanda-tanda vital</label>
                        <label>- Memberikan terapi oksigen sesuai kebutuhan</label>
                        <label>- Mengatur tidur pasien pada posisi supine atau trendelenburg dan tidak menggunakan bantal</label> 
                        <label>- Memberikan obat-obatan sesuai instruksi</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row card-body">
                <div class="col-sm-2"><input type="text" class="form-control without_ampm" id="jam_terapi" name="jam_terapi" autocomplete="off" value="<?php echo $Hd['jam_terapi']; ?>"></div>
                <div class="col-sm-10">
                  <label>Mememberikan Terapi</label>
                  <div class="form-group">
                    <div class="row card-body">
                      <div class="col-md-3">
                        Terapi IV
                      </div>
                      <div class="col-sm-12 col-md-12">
                          <textarea class="form-control" id="terapiiv" name="terapiiv"><?php echo $Hd['terapiiv']; ?>'</textarea>
                      </div>
                    </div>
                    <div class="row card-body">
                      <div class="col-md-3">
                        Terapi SC
                      </div>
                      <div class="col-md-9">
                        <input type="text" class="form-control without_ampm" name="terapisc" id="terapisc" value="<?php echo $Hd['terapisc']; ?>">
                      </div>
                    </div>
                    <div class="row card-body">
                      <div class="col-md-3">
                        Terapi Oral
                      </div>
                      <div class="col-md-9">
                        <input type="text" class="form-control without_ampm" name="terapioral" id="terapioral" value="<?php echo $Hd['terapioral']; ?>">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- transfusi -->
              <div class="row card-body">
                <div class="col-sm-2"><input type="text" class="form-control without_ampm" id="jam_transfusi" name="jam_transfusi" autocomplete="off" value="<?php echo $Hd['jam_transfusi']; ?>"></div>
                <div class="col-sm-10">
                  <label>Transfusi</label>
                  <div class="form-group">
                    <div class="row card-body">
                      <div class="col-md-3">
                        Golongan Darah
                      </div>
                      <div class="col-md-9">
                        <input type="text" class="form-control without_ampm" name="goldar" id="goldar" value="<?php echo $Hd['goldar']; ?>">
                      </div>
                    </div>
                    <div class="row card-body">
                      <div class="col-md-3">
                        No. Stok
                      </div>
                      <div class="col-sm-12 col-md-12">
                          <textarea class="form-control" id="nostok" name="nostok" id="nostok"><?php echo $Hd['nostok']; ?>'</textarea>
                      </div>
                    </div>
                    <div class="row card-body">
                      <div class="col-md-3">
                        Jumlah
                      </div>
                      <div class="col-md-9">
                        <input type="text" class="form-control without_ampm" name="jumlahtransfusi" id="jumlahtransfusi" value="<?php echo $Hd['jumlahtransfusi']; ?>">
                      </div>
                    </div>
                    <div class="row card-body">
                      <div class="col-md-3">
                        Jenis
                      </div>
                      <div class="col-md-9">
                        <input type="text" class="form-control without_ampm" name="jenistransfusi" id="jenistransfusi" value="<?php echo $Hd['jenistransfusi']; ?>">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row card-body">
                <div class="col-md-3">
                  Keterangan :
                </div>
                <div class="col-sm-12 col-md-12">
                    <textarea class="form-control" id="ket" name="ket"
                    ><?php echo $Hd['ket']; ?>'</textarea>
                </div>
              </div>
              <div class="row card-body">
                <div class="col-md-3">
                  Double Check :
                </div>
                <div class="col-sm-12 col-md-12">
                  <div class="row card-body">
                    <div class="col-md-3">
                      Perawat 1
                    </div>
                    <div class="col-md-9">
                      <div class="form-group">
                        <?php
                        if(strlen($Hd['perawat1'])==1){ ?>
                          <canvas id="csPerawat1" width="160" height="200" style="background:url(<?=base_url();?>assets/admin/assets/images/crossword.png) no-repeat;"></canvas>
                        <?php
                        }else{ ?>
                          <img src="data:image/jpeg;base64,'".<?php echo base64_encode($Hd['perawat1'] ); ?>.'"/>
                        <?php } ?>
                          <input type="hidden" name="perawat1" id="perawat1" value="" />
                      </div>
                    </div>
                  </div>
                  <div class="row card-body">
                    <div class="col-md-3">
                      Perawat 2
                    </div>
                    <div class="col-md-9">
                      <div class="form-group">
                        <?php
                        if(strlen($Hd['perawat1'])==1){ ?>
                          <canvas id="csPerawat2" width="160" height="200" style="background:url(<?=base_url();?>assets/admin/assets/images/crossword.png) no-repeat;"></canvas>
                        <?php
                        }else{ ?>
                          <img src="data:image/jpeg;base64,'".<?php echo base64_encode($Hd['perawat2'] ); ?>.'"/>
                        <?php } ?>
                        <input type="hidden" name="perawat2" id="perawat2" value="" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </caption>
      </div>
      <?php          
      endforeach;
  }

  public function actionSetuju($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();
        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $dataSetujuHd = array(
          'id_emr' => $getIdEmr,
          'norm'=> isset($post['noRm']) ? $post['noRm'] : "", 
          'nokun'=> isset($post['noKun']) ? $post['noKun'] : "", 
          'namapasien'=> isset($post['nmPasien']) ? $post['nmPasien'] : "", 
          'tgllahir'=> isset($post['tglLahir']) ? $post['tglLahir'] : "", 
          'umur'=> isset($post['umur']) ? $post['umur'] : "", 
          'id_dokter'=> isset($post['dokter_pengirim']) ? $post['dokter_pengirim'] : "", 
          'namaperawat'=> isset($post['namaperawat']) ? $post['namaperawat'] : "", 
          'namapenerima'=> isset($post['penerima']) ? $post['penerima'] : "", 
          'anamnesis'=> isset($post['anamnesis']) ? $post['anamnesis'] : "", 
          'pfisik'=> isset($post['pfisik']) ? $post['pfisik'] : "", 
          'hasillab'=> isset($post['hasillab']) ? $post['hasillab'] : "", 
          'hasilrad'=> isset($post['hasilrad']) ? $post['hasilrad'] : "", 
          'lainlain'=> isset($post['lainlain']) ? $post['lainlainlainlain'] : "", 
          'tindakankd'=> isset($post['tindakankd']) ? $post['tindakankd'] : "", 
          'hasilup'=> isset($post['hasilup']) ? $post['hasilup'] : "", 
          'anuria'=> isset($post['anuria']) ? $post['anuria'] : "", 
          'usgabsnormal'=> isset($post['usgabsnormal']) ? $post['usgabsnormal'] : "", 
          'cairandialisat'=> isset($post['cairandialisat']) ? $post['cairandialisat'] : "", 
          'cimino'=> isset($post['cimino']) ? $post['cimino'] : "", 
          'aksescdl'=> isset($post['aksescdl']) ? $post['aksescdl'] : "", 
          'ureum'=> isset($post['ureum']) ? $post['ureum'] : "", 
          'cairantubuh'=> isset($post['cairantubuh']) ? $post['cairantubuh'] : "", 
          'sensitifalergi'=> isset($post['sensitifalergi']) ? $post['sensitifalergi'] : "", 
          'bbkeringnaik'=> isset($post['bbkeringnaik']) ? $post['bbkeringnaik'] : "", 
          'insertasi'=> isset($post['insertasi']) ? $post['insertasi'] : "", 
          'aksescdlancar'=> isset($post['aksescdlancar']) ? $post['aksescdlancar'] : "", 
          'insersi'=> isset($post['insersi']) ? $post['insersi'] : "", 
          'kondisi'=> isset($post['kondisi']) ? $post['kondisi'] : "", 
          'alternatif'=> isset($post['alternatif']) ? $post['alternatif'] : "", 
          'alternatifck'=> isset($post['alternatifck']) ? $post['alternatifck'] : "", 
          'lainlaintext'=> isset($post['lainlain1']) ? $post['lainlain1'] : "", 
          'lainlain1ck'=> isset($post['lainlain1ck']) ? $post['lainlain1ck'] : "", 
          'pernyataan'=> isset($post['pernyataan']) ? $post['pernyataan'] : "", 
          'informasi'=> isset($post['informasi']) ? $post['informasi'] : "", 
          'namattd'=> isset($post['namattd']) ? $post['namattd'] : "", 
          'jeniskelamin'=> isset($post['jk_1']) ? $post['jk_1'] : "", 
          'alamat'=> isset($post['alamat']) ? $post['alamat'] : "", 
          'terhadap'=> isset($post['terhadap']) ? $post['terhadap'] : "", 
          'nama2'=> isset($post['nama2']) ? $post['nama2'] : "", 
          'umur2'=> isset($post['umur2']) ? $post['umur2'] : "", 
          'jeniskelamin2'=> isset($post['jk']) ? $post['jk'] : "", 
          'alamat2'=> isset($post['alamat2']) ? $post['alamat2'] : "", 
          'ttd_pernyataan'=> isset($post['ttd_pernyataan']) ? $post['ttd_pernyataan'] : "", 
          'ttd_saksi1'=> isset($post['ttd_saksi1']) ? $post['ttd_saksi1'] : "", 
          'ttd_saksi2'=> isset($post['ttd_saksi2']) ? $post['ttd_saksi2'] : "", 
          'inputoleh'=> isset($post['inputOleh']) ? $post['inputOleh'] : "", 
          'tanggalinput'=> date("Y-m-d H:i:s"), 
        );
  
        var_dump($this->db->insert('keperawatan.tb_persetujuan_hd', $dataSetujuHd));
        exit();
      }
    }
  } 
  
  public function lihathistoryPrHd(){
      $id = $this->input->post('id'); 
      $historyPrHd = $this->HemodialisaModel->historyDetailPrHd($id);
      echo "<pre>";
      print_r($historyPrHd);
      exit();
    
  }
}
/* End of file Dashboard.php */
/* Location: ./application/controllers/Dashboard.php */
