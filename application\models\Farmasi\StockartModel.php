<?php
defined('BASEPATH') or exit('No direct script access allowed');

class StockartModel extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    public function simpan($data)
    {
        return $this->db->insert('inventory.request_item', $data);
    }

    public function getByNokun($nokun)
    {
        $this->db->where('nokun', $nokun);
        $this->db->where('status !=', 0); // Exclude deleted records
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function getById($id)
    {
        $this->db->where('id', $id);
        $this->db->where('status !=', 0); // Exclude deleted records
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->row();
    }

    public function getAll()
    {
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->update('inventory.request_item', $data);
    }

    public function hapus($id)
    {
        $this->db->where('id', $id);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->delete('inventory.request_item');
    }
    
    public function softDelete($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->update('inventory.request_item', $data);
    }

    public function getByStatus($status)
    {
        $this->db->where('status', $status);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function countByStatus($status)
    {
        $this->db->where('status', $status);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->count_all_results('inventory.request_item');
    }

    public function getByDateRange($start_date, $end_date)
    {
        $this->db->where('created_at >=', $start_date);
        $this->db->where('created_at <=', $end_date);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }

    public function getDataTable($nokun)
    {
        try {
            // Simplified query without problematic joins
            $this->db->select('ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at, COUNT(ri.id) as jumlah_item,
                              cb.nama as created_by_name, ub.nama as updated_by_name, ri.ref');
            $this->db->from('inventory.request_item ri');
            $this->db->join('aplikasi.pengguna cb', 'cb.id = ri.created_by', 'left');
            $this->db->join('aplikasi.pengguna ub', 'ub.id = ri.updated_by', 'left');

            // Filter by nokun if provided
            if (!empty($nokun)) {
                $this->db->where('ri.nokun', $nokun);
            }

            // Exclude deleted records (status = 0) and filter by category = 2
            $this->db->where('ri.status !=', 0);
            $this->db->where('ri.category', 2);
            $this->db->group_by('ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at, cb.nama, ub.nama, ri.ref');
            $this->db->order_by('ri.created_at', 'DESC');

            $query = $this->db->get();

            if ($this->db->error()['code'] != 0) {
                log_message('error', 'Database error in getDataTable: ' . $this->db->error()['message']);
                return [];
            }

            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Exception in getDataTable: ' . $e->getMessage());
            return [];
        }
    }
    
    public function hapusByPengajuan($id_pengajuan)
    {
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->delete('inventory.request_item');
    }
    
    public function softDeleteByPengajuan($id_pengajuan, $data)
    {
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->update('inventory.request_item', $data);
    }
    
    public function getDetailPengajuan($id_pengajuan)
    {
        $this->db->select('ri.*, ib.NAMA as nama_barang, cb.nama as created_by_name, ub.nama as updated_by_name, ibr.ID as id_barang_ruangan, ptp.DOKTER as id_dokter, ore.STATUS as status_resep');
        $this->db->from('inventory.request_item ri');
        $this->db->join('inventory.barang ib', 'ib.ID = ri.id_item', 'left');
        $this->db->join('inventory.barang_ruangan ibr', 'ibr.BARANG = ri.id_item AND ibr.STATUS = 1', 'left');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ri.nokun', 'left');
        $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pk.NOPEN', 'left');
        $this->db->join('layanan.order_resep ore', 'ore.NOMOR = ri.ref', 'left');
        $this->db->join('aplikasi.pengguna cb', 'cb.id = ri.created_by', 'left');
        $this->db->join('aplikasi.pengguna ub', 'ub.id = ri.updated_by', 'left');
        $this->db->where('ri.id_pengajuan', $id_pengajuan);
        // Exclude deleted records (status = 0) and filter by category = 2
        $this->db->where('ri.status !=', 0);
        $this->db->where('ri.category', 2);
        $this->db->order_by('ri.id', 'ASC');
        $this->db->group_by('ri.id');
        return $this->db->get()->result();
    }
    
    public function getByPengajuan($id_pengajuan)
    {
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('status !=', 0); // Exclude deleted records
        $this->db->where('category', 2); // Filter by category = 2
        return $this->db->get('inventory.request_item')->result();
    }
    
    public function getDataTableWithSearch($nokun, $start = 0, $length = 10, $search_value = '', $order_by = 'created_at', $order_dir = 'desc')
    {
        try {
            // Log parameter yang diterima
            log_message('debug', 'getDataTableWithSearch() called with params - nokun: ' . $nokun . ', start: ' . $start . ', length: ' . $length . ', search: ' . $search_value . ', order_by: ' . $order_by . ', order_dir: ' . $order_dir);

            // Validasi parameter input
            $start = max(0, intval($start));
            $length = max(1, intval($length));
            $search_value = trim($search_value);
            $order_dir = in_array(strtolower($order_dir), ['asc', 'desc']) ? $order_dir : 'desc';

            // Simplified total count query
            $total_query = "SELECT COUNT(DISTINCT ri.id_pengajuan) as total
                           FROM inventory.request_item ri
                           WHERE ri.status != 0 AND ri.category = 2";

            if (!empty($nokun)) {
                $total_query .= " AND ri.nokun = " . $this->db->escape($nokun);
            }

            log_message('debug', 'Total count query: ' . $total_query);

            $total_result = $this->db->query($total_query);
            if (!$total_result) {
                log_message('error', 'Failed to execute total count query: ' . $this->db->error()['message']);
                throw new Exception('Database error in total count query');
            }
            $total_records = $total_result->row()->total;

            // Build filtered count query
            $filtered_query = "SELECT COUNT(DISTINCT ri.id_pengajuan) as filtered
                              FROM inventory.request_item ri
                              LEFT JOIN aplikasi.pengguna cb ON cb.id = ri.created_by
                              LEFT JOIN aplikasi.pengguna ub ON ub.id = ri.updated_by
                              WHERE ri.status != 0 AND ri.category = 2";

            if (!empty($nokun)) {
                $filtered_query .= " AND ri.nokun = " . $this->db->escape($nokun);
            }

            // Apply search filter if search value is provided
            if (!empty($search_value)) {
                $escaped_search = $this->db->escape_like_str($search_value);
                $filtered_query .= " AND (ri.id_pengajuan LIKE '%$escaped_search%'
                                    OR ri.nokun LIKE '%$escaped_search%'
                                    OR cb.nama LIKE '%$escaped_search%'
                                    OR ub.nama LIKE '%$escaped_search%')";
            }

            log_message('debug', 'Filtered count query: ' . $filtered_query);

            $filtered_result = $this->db->query($filtered_query);
            if (!$filtered_result) {
                log_message('error', 'Failed to execute filtered count query: ' . $this->db->error()['message']);
                throw new Exception('Database error in filtered count query');
            }
            $filtered_records = $filtered_result->row()->filtered;

            // Build final data query with simplified approach
            $data_query = "SELECT ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at,
                                 COUNT(ri.id) as jumlah_item,
                                 ri.created_by,
                                 cb.nama as created_by_name,
                                 ub.nama as updated_by_name,
                                 ri.ref,
                                 ore.STATUS as status_resep,
                                 CASE
                                     WHEN ri.ref IS NOT NULL AND ore.STATUS = 2 THEN 'Diterima'
                                     WHEN ri.ref IS NOT NULL AND ore.STATUS = 0 THEN 'Ditolak Farmasi'
                                     WHEN ri.status = 2 THEN 'Diproses'
                                     WHEN ri.status = 1 THEN 'Diajukan'
                                     WHEN ri.status = 3 THEN 'Ditolak Dokter'
                                     ELSE '-'
                                 END as status_text
                          FROM inventory.request_item ri
                          LEFT JOIN aplikasi.pengguna cb ON cb.id = ri.created_by
                          LEFT JOIN aplikasi.pengguna ub ON ub.id = ri.updated_by
                          LEFT JOIN layanan.order_resep ore ON ore.NOMOR = ri.ref
                          WHERE ri.status != 0 AND ri.category = 2";

            if (!empty($nokun)) {
                $data_query .= " AND ri.nokun = " . $this->db->escape($nokun);
            }

            // Apply search filter if search value is provided
            if (!empty($search_value)) {
                $escaped_search = $this->db->escape_like_str($search_value);
                $data_query .= " AND (ri.id_pengajuan LIKE '%$escaped_search%'
                                OR ri.nokun LIKE '%$escaped_search%'
                                OR cb.nama LIKE '%$escaped_search%'
                                OR ub.nama LIKE '%$escaped_search%')";
            }

            $data_query .= " GROUP BY ri.id_pengajuan, ri.nokun, ri.status, ri.created_at, ri.updated_at, ri.created_by, cb.nama, ub.nama, ri.ref, ore.STATUS";

            // Apply sorting
            $valid_columns = [
                'id_pengajuan' => 'ri.id_pengajuan',
                'nokun' => 'ri.nokun',
                'jumlah_item' => 'jumlah_item',
                'status' => 'ri.status',
                'created_at' => 'ri.created_at',
                'updated_at' => 'ri.updated_at',
                'created_by_name' => 'cb.nama',
                'updated_by_name' => 'ub.nama'
            ];

            $sort_column = isset($valid_columns[$order_by]) ? $valid_columns[$order_by] : 'ri.created_at';
            $sort_direction = strtoupper($order_dir) === 'ASC' ? 'ASC' : 'DESC';

            $data_query .= " ORDER BY $sort_column $sort_direction";

            // Apply pagination
            if ($length != -1) {
                $data_query .= " LIMIT $start, $length";
            }

            log_message('debug', 'Final data query: ' . $data_query);

            $query = $this->db->query($data_query);

            if (!$query) {
                log_message('error', 'Database error in getDataTableWithSearch final query: ' . $this->db->error()['message']);
                throw new Exception('Database error in final data query: ' . $this->db->error()['message']);
            }

            $data = $query->result();

            log_message('debug', 'getDataTableWithSearch() result - total: ' . $total_records . ', filtered: ' . $filtered_records . ', data count: ' . count($data));

            return [
                'data' => $data,
                'total' => $total_records,
                'filtered' => $filtered_records
            ];

        } catch (Exception $e) {
            log_message('error', 'Exception in getDataTableWithSearch: ' . $e->getMessage() . ' - File: ' . $e->getFile() . ' - Line: ' . $e->getLine());
            return [
                'data' => [],
                'total' => 0,
                'filtered' => 0
            ];
        }
    }

    public function getBarang($search = null)
    {
        $this->db->select('ib.ID, ib.NAMA');
        $this->db->from('inventory.barang ib');
        $this->db->join('inventory.barang_ruangan ibr', 'ibr.BARANG = ib.ID');
        $this->db->where('ibr.RUANGAN', '105050156');
        $this->db->where('ibr.STATUS', 1);
        $this->db->where('ib.STATUS', 1);
        if ($search) {
            $this->db->like('ib.NAMA', $search);
        }
        $this->db->limit(20);
        $query = $this->db->get();
        $result = $query->result();
        
        $data = [];
        foreach ($result as $row) {
            $data[] = [
                'id' => $row->ID,
                'text' => $row->NAMA
            ];
        }
        
        return $data;
    }

    public function isRoomRegistered($id_room)
    {
        $this->db->select('id');
        $this->db->from('inventory.stockart_room');
        $this->db->where('id_room', $id_room);
        $this->db->where('status', 1);
        $query = $this->db->get();
        return $query->num_rows() > 0;
    }
    
    public function updateStatusPengajuan($id_pengajuan, $status, $updated_by)
    {
        $updateData = [
            'status' => $status,
            'updated_by' => $updated_by,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('category', 2);
        return $this->db->update('inventory.request_item', $updateData);
    }

    public function getNamaPegawai($nip)
    {
        $query = $this->db->query("SELECT master.getNamaLengkapPegawai('$nip') as nama_lengkap");
        $result = $query->row();
        return $result ? $result->nama_lengkap : null;
    }
    
    public function updateRefPengajuan($id_pengajuan, $ref)
    {
        $updateData = [
            'ref' => $ref,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('category', 2);
        return $this->db->update('inventory.request_item', $updateData);
    }
    
    public function checkOwnership($id, $user_id)
    {
        $this->db->select('created_by');
        $this->db->from('inventory.request_item');
        $this->db->where('id', $id);
        $this->db->where('category', 2);
        $this->db->where('status !=', 0);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $row = $query->row();
            return $row->created_by == $user_id;
        }
        
        return false;
    }
    
    public function checkOwnershipByPengajuan($id_pengajuan, $user_id)
    {
        $this->db->select('created_by');
        $this->db->from('inventory.request_item');
        $this->db->where('id_pengajuan', $id_pengajuan);
        $this->db->where('category', 2);
        $this->db->where('status !=', 0);
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $row = $query->row();
            return $row->created_by == $user_id;
        }
        
        return false;
    }
}