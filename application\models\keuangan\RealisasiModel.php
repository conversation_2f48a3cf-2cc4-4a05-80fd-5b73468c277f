<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RealisasiModel extends MY_Model {

  protected $_table_name = 'db_keuangan.realisasi_rkakl';
  protected $_primary_key = 'ID';
  protected $_order_by = 'ID';
  protected $_order_by_type = 'DESC';

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('rkakl.*, master.getNamaLengkapPegawai(apeng.NIP) NAMAPEGAWAI, mr.DESKRIPSI DESKRUANGAN, rk.URAIAN');
    $this->db->from('db_keuangan.realisasi_rkakl rkakl');
    $this->db->join('aplikasi.pengguna apeng', 'apeng.ID = rkakl.OLEH', 'left');
    // $this->db->join('db_keuangan.rkakl_instalasi ri', 'ri.ID = rkakl.RUANGAN', 'left');
    $this->db->join('db_keuangan.master_instalasi mr', 'mr.ID = rkakl.RUANGAN', 'left');
    $this->db->join('db_keuangan.rkakl rk', 'rk.ID = rkakl.ID_RKAKL', 'left');
    if($this->input->get('id')){
      $this->db->where('rr.ID',$this->input->get('id'));
    }
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }

}

/* End of file RealisasiModel.php */
/* Location: ./application/models/keuangan/RealisasiModel.php */