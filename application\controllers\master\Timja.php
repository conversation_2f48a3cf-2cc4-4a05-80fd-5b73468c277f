<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON> extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(5,$this->session->userdata('akses')) OR !in_array(6,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('masterModel');
  }

  ///////////////////////////////////////////////////////////// START TIMJA ////////////////////////////////////////////////////////////////////////
  public function index()
  {
    $pegawai = $this->masterModel->pegawai();

    $data = array(
      'title'   => 'Halaman Master',
      'isi'     => 'Master/timja/index',
      'pegawai' => $pegawai,
      'Timja' => $this->masterModel->referensiTimja(102, null),
      'JabatanTimja' => $this->masterModel->referensiTimja(103, null),
      'DokterTimja' => $this->masterModel->dokter_timja(),
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function listTimja()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listTimja = $this->masterModel->timja();

    // echo "<pre>";print_r($listTimja);exit();
    $data  = array();
    $no    =1;
    foreach($listTimja->result() as $lt) {
      if($lt->STATUS_TIMJA == 1){
        $check = "checked" ;
      }else{
        $check = "" ;
      }

      $data[] = array(
        $no,
        $lt->NIP,
        $lt->NAMADOKTER,
        $lt->TIMJA,
        $lt->JABATAN_TIMJA,
        $lt->OLEH,
        '<div class="checkbox checkbox-primary">
        <input type="checkbox" id="statusTimja'.$lt->ID.'" value="'.$lt->ID.'"  class="STimja" '.$check.'>
        <label for="statusTimja'.$lt->ID.'"></label>
        </div>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listTimja->num_rows(),
      "recordsFiltered" => $listTimja->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  function simpanTimja(){
    $this->db->trans_begin();

    $post = $this->input->post();
    $id_pengguna = $this->session->userdata('id');

    $dataTimja = array (
      'ID_DOKTER'                           => $post['DOKTER_TIMJA'],
      'ID_TIMJA'                            => $post['TIMJA'],
      'ID_JABATAN_TIMJA'                    => $post['JABATAN_TIMJA'],
      'OLEH'                                => $id_pengguna,
      'STATUS_TIMJA'                        => 1,
    );
    // echo "<pre>";print_r($dataTimja);echo "</pre>";

    $this->masterModel->simpanTimja($dataTimja);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
}

public function STimjaAktiv()
  {
    $id_timja = $this->input->post('id_timja');

    $data = array(
      'STATUS_TIMJA' => 1,
    );

    $this->masterModel->updateTimja($id_timja,$data);
  }

  public function STimjaNonAktiv()
  {
    $id_timja = $this->input->post('id_timja');

    $data = array(
      'STATUS_TIMJA' => 0,
    );

    $this->masterModel->updateTimja($id_timja,$data);
  }


}

/* End of file Timja.php */
/* Location: ./application/controllers/master/Timja.php */
