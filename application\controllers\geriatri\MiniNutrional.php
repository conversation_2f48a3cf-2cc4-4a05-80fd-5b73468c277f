<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MiniNutrional extends CI_Controller
{

	public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function simpanFormMiniNutrionalSkrining()
    {
        $kunjungan = $this->input->post("nokun");
        $id_pengguna = $this->session->userdata('id');
        $penurunanasupan = $this->input->post("penurunanasupan");
        $kehilanganbb = $this->input->post("kehilanganbb");
        $kemampuanmobilitas = $this->input->post("kemampuanmobilitas");
        $menderitastress = $this->input->post("menderitastress");
        $neuropsikologis = $this->input->post("neuropsikologis");
        $nilaiimt = $this->input->post("nilaiimt");
        
        $data = array(
            'nokun' => $kunjungan,
            'penurunan_asupan' => $penurunanasupan,
            'kehilangan_bb' => $kehilanganbb,
            'kemampuan_mobilitas' => $kemampuanmobilitas,
            'menderita_stress' => $menderitastress,
            'neuropsikologis' => $neuropsikologis,
            'nilai_imt' => $nilaiimt,
            'oleh' => $id_pengguna,
        );
        $this->db->trans_begin();

        $this->db->insert('db_layanan.tb_geriatri_mini_nutrional_skrining', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryMiniNutrionalSkrining()
    {
        $id = $this->input->post('id');
        $MiniNutrionalSkrining = $this->pengkajianAwalModel->HistoryDetailMiniNutrionalSkrining($id);
        
        $listPenurunanAsupan = $this->masterModel->referensi(918);
        $listKehilanganBB = $this->masterModel->referensi(919);
        $listKemampuanMobilitas = $this->masterModel->referensi(920);
        $listMenderitaStress = $this->masterModel->referensi(921);
        $listNeuropsikologis = $this->masterModel->referensi(922);
        $listNilaiIMT = $this->masterModel->referensi(923);

        foreach($MiniNutrionalSkrining as $mna):
        echo '      <form id="frmMiniNutrionalSkriningUpdate">
                        <input type="hidden" name="id_mna" value="'.$mna['id'].'">
                        <input type="hidden" name="nukun2" value="'.$mna['nokun'].'">
                        <table class="table table-bordered dt-responsive nowrap" cellspacing="0" width="100%">
                            <thead class="thead-light">
                                <th width="5%">&nbsp</th>
                                <th width="40%">&nbsp</th>
                                <th width="40%">Hasil Penilaian</th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>A.</td>
                                    <td>
                                        Apakah anda mengalami penurunan asupan makanan dalam 3 bulan terakhir disebabkan kehilangan nafsu makan, gangguan saluran cerna, kesulitan mengunyah atau menelan?</br>
                                        0 = kehilangan nafsu makan berat (severe)</br>
                                        1 = kehilangan nafsu makan sedang (moderate)</br>
                                        2 = tidak kehilangan nafsu makan
                                    </td>
                                    <td>
                                    <div style="margin-left:15px;" class="col form-check">';
                                            foreach ($listPenurunanAsupan as $list):
            echo '                          <div class="col-md-12 form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" class="col-md-1" name="penurunanasupan2" value="'.$list['id_variabel'].'" class="penurunanasupan2" id="penurunanasupan2'.$list['id_variabel'].'" ';
                                                    if($mna['penurunan_asupan']== $list['id_variabel']){
                                                        echo "checked";
                                                    }else{
                                                        echo "";
                                                    }
            echo '                                  >
                                                    <label for="penurunanasupan2'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                                </div>
                                            </div></br></br>';
                                            endforeach;
            echo '                  <div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>B.</td>
                                    <td>
                                        Kehilangan berat badan dalam tiga bulan terakhir ?</br>
                                        0 = kehilangan BB > 3 kg</br>
                                        1 = tidak tahu </br>
                                        2 = kehilangan BB antara 1-3 kg</br>
                                        3 = tidak mengalami kehilangan BB
                                    </td>
                                    <td>
                                    <div class="col form-check">';
                                        foreach ($listKehilanganBB as $list):
            echo '                          <div class="col-md-12 form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="kehilanganbb2" value="'.$list['id_variabel'].'" class="kehilanganbb2" id="kehilanganbb2'.$list['id_variabel'].'"';
                                                    if($mna['kehilangan_bb']== $list['id_variabel']){
                                                        echo "checked";
                                                    }else{
                                                        echo "";
                                                    }
            echo '                                  >
                                                    <label for="kehilanganbb2'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                                </div>
                                            </div></br></br>';
                                        endforeach;
            echo '                  <div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>C.</td>
                                    <td>
                                        Kemampuan melakukan mobilitas ?</br>
                                        0 = di ranjang saja atau di kursi roda</br>
                                        1 = dapat meninggalkan ranjang atau kursi roda namun tidak bisa pergi/jalan-jalan keluar </br>
                                        2 = dapat berjalan atau pergi dengan leluasa
                                    </td>
                                    <td>
                                        <div style="margin-top:-25px;margin-left:27px;">';
                                            foreach ($listKemampuanMobilitas as $list):
            echo '                              <div class="col-md-12 form-check">
                                                    <div class="radio radio-primary form-check-input jarak2">
                                                        <input type="radio" name="kemampuanmobilitas2" class="col-md-1" value="'.$list['id_variabel'].'" class="kemampuanmobilitas2" id="kemampuanmobilitas2'.$list['id_variabel'].'" ';
                                                        if($mna['kemampuan_mobilitas']== $list['id_variabel']){
                                                            echo "checked";
                                                        }else{
                                                            echo "";
                                                        }
            echo '                                      >
                                                        <label for="kemampuanmobilitas2'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                                    </div>
                                                </div></br></br>';
                                            endforeach;
            echo '                     </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>D.</td>
                                    <td>
                                        Menderita stress psikologis atau penyakit akut dalam tiga bulan terakhir ?</br>
                                        0 = ya</br>
                                        2 = tidak
                                    </td>
                                    <td>
                                    <div class="col form-check">';
                                        foreach ($listMenderitaStress as $list):
            echo '                                <div class="col-md-12 form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="menderitastress2" value="'.$list['id_variabel'].'" class="menderitastress2" id="menderitastress2'.$list['id_variabel'].'" ';
                                                    if($mna['menderita_stress']== $list['id_variabel']){
                                                        echo "checked";
                                                    }else{
                                                        echo "";
                                                    }
            echo '                                  >
                                                    <label for="menderitastress2'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                                </div>
                                            </div></br></br>';
                                        endforeach;
            echo '                  <div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>E.</td>
                                    <td>
                                        Mengalami masalah neuropsikologis ?</br>
                                        0 = dementia atau depresi berat</br>
                                        1 = demensia sedang (moderate)</br>
                                        2 = tidak ada masalah psikologis
                                    </td>
                                    <td>
                                    <div class="col form-check">';
                                        foreach ($listNeuropsikologis as $list):
            echo '                         <div class="col-md-12 form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="neuropsikologis2" value="'.$list['id_variabel'].'" class="neuropsikologis2" id="neuropsikologis2'.$list['id_variabel'].'"';
                                                    if($mna['neuropsikologis']== $list['id_variabel']){
                                                        echo "checked";
                                                    }else{
                                                        echo "";
                                                    }
            echo '                                  >
                                                    <label for="neuropsikologis2'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                                </div>
                                            </div></br></br>';
                                        endforeach;
            echo '                  </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>F.</td>
                                    <td>
                                        Nilai IMT (Indeks Massa Tubuh) ?</br>
                                        0 = IMT < 19 kg/m<sup>2</sup></br>
                                        1 = IMT 19-21</br>
                                        2 = IMT 21-23</br>
                                        3 = IMT > 23</br>
                                    </td>
                                    <td>
                                    <div class="col form-check">';
                                        foreach ($listNilaiIMT as $list):
            echo '                          <div class="col-md-12 form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="nilaiimt2" value="'.$list['id_variabel'].'" class="nilaiimt2" id="nilaiimt2'.$list['id_variabel'].'"';
                                                    if($mna['nilai_imt']== $list['id_variabel']){
                                                        echo "checked";
                                                    }else{
                                                        echo "";
                                                    }
            echo '                                  >
                                                    <label for="nilaiimt2'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                                </div>
                                            </div></br></br>';
                                        endforeach;
            echo '                  <div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2">SUB TOTAL</td>
                                    <td>'.$mna['total'].'</td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-lg-2 offset-lg-10">
                            <div class="form-group">
                                <button class="btn btn-primary waves-effect waves-light btn-block" type="submit">
                                <i class="fa fa-save"></i> Update
                                </button>
                            </div>
                            </div>
                        </div>
                    </form>
                    <script>
                        $("#frmMiniNutrionalSkriningUpdate").submit(function(event) {
                            
                            event.preventDefault();
                            dataMiniNutrionalSkriningUpdate = $("#frmMiniNutrionalSkriningUpdate").serializeArray();
                            alertify.confirm("Konfirmasi", "Pilih Ok, jika setuju untuk Di Rubah",
                            function(){
                              $.ajax({
                                  dataType:"json",
                                  url: "'.base_url("geriatri/MiniNutrional/updateFormMiniNutrionalSkrining").'",
                                  method: "POST",
                                  data: dataMiniNutrionalSkriningUpdate,
                                  success: function(data) {
                                    if(data.status == "success"){
                                      alertify.success("Data Terupdate");
                                      $("#lihathistoryMiniNutrionalSkrining").modal("hide");
                                    }else{
                                      alertify.warning("Internal Server Error");
                                    }
                                  }
                    
                              });
                            }, function(){ });
                        });
                    </script>
                    ';
        endforeach;
    }

    public function updateFormMiniNutrionalSkrining()
    {
        $kunjungan = $this->input->post("nukun2");
        $id_pengguna = $this->session->userdata('id');
        $penurunanasupan = $this->input->post("penurunanasupan2");
        $kehilanganbb = $this->input->post("kehilanganbb2");
        $kemampuanmobilitas = $this->input->post("kemampuanmobilitas2");
        $menderitastress = $this->input->post("menderitastress2");
        $neuropsikologis = $this->input->post("neuropsikologis2");
        $nilaiimt = $this->input->post("nilaiimt2");
        
        $dataUpdate = array(
            'nokun' => $kunjungan,
            'penurunan_asupan' => $penurunanasupan,
            'kehilangan_bb' => $kehilanganbb,
            'kemampuan_mobilitas' => $kemampuanmobilitas,
            'menderita_stress' => $menderitastress,
            'neuropsikologis' => $neuropsikologis,
            'nilai_imt' => $nilaiimt,
            'oleh' => $id_pengguna,
        );
        $this->db->trans_begin();

        
        $this->db->where('db_layanan.tb_geriatri_mini_nutrional_skrining.id', $this->input->post('id_mna'));
        $this->db->update('db_layanan.tb_geriatri_mini_nutrional_skrining', $dataUpdate);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

}

?>