<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class EwsRawatJalan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $nomr = $this->uri->segment(4);
    $id_pengguna = $this->session->userdata('id');
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $parameterEws = $this->masterModel->parameterEws();
    $scoreEws = array();
    foreach ($parameterEws as $parEws) {
      $dataScoreEws = array();
      $dataScoreEws['title'] = $parEws['referensi'];
      $dataScoreEws['id_referensi'] = $parEws['id_referensi'];
      $dataScoreEws['sub'] = $this->masterModel->referensi($parEws['id_referensi']);

      $scoreEws[] = $dataScoreEws;
    }

    $data = array(
      'nokun' => $nokun,
      'nomr' => $nomr,
      'id_pengguna' => $id_pengguna,
      'getNomr' => $getNomr,
      'scoreEws' => $scoreEws,
    );

    $this->load->view('Pengkajian/emr/ews/ews', $data);
  }

}

/* End of file PersetujuanTindakanKemoterapiLlaAnak.php */
/* Location: ./application/controllers/informedConsent/PersetujuanTindakanKemoterapiLlaAnak.php */
