<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ProfileModel extends CI_Model {

  public function namaPegawai($id)
  {
    $query  = $this->db->query("SELECT master.getNamaLengkapPegawai(mp.NIP) NAMAPEGAWAI
     FROM aplikasi.pengguna ap
     LEFT JOIN master.pegawai mp ON ap.NIP = mp.NIP
     WHERE ap.ID = '$id'");

    return $query->row_array();
  }

   public function detailListPasien($nomr)
  {
    $query = $this->db->query(
      "SELECT cov.id ID, IF(cov.jenis=1,'PASIEN', 'NON PASIEN') JENIS
      , cov.nomr NOMR
      , cov.nama NAMA
      , DATE_FORMAT(cov.tgllahir,'%d/%m/%Y') TANGGAL_LAHIR
      , IF(cov.jk=1,'PRIA', 'WANITA') JENIS_KELAMIN
      , IF(cov.warna=1,'HIJAU', IF(cov.warna=2,'MERAH', '-')) WARNA
      , concat((YEAR(CURDATE())-YEAR(cov.tgllahir)),' Th') UMUR
      , cov.created_at TANGGAL
      , IF(cov.hasil_pemeriksaan=1,'ODP', IF(cov.hasil_pemeriksaan=2,'PDP'
      , IF(cov.hasil_pemeriksaan=3,'Positif'
      , IF(cov.hasil_pemeriksaan=4,'Negatif', IF(cov.hasil_pemeriksaan=5,'OTG','-'))))) HASIL_PEMERIKSAAN
      , IF(cov.status_hidup=1,'Hidup', IF(cov.status_hidup=2,'Meninggal', '-')) STATUS_HIDUP
      , cov.keterangan KETERANGAN
      #, cov.created_at
      FROM db_layanan.tb_skrining_covid cov
      WHERE #cov.warna!=1 AND 
      cov.nomr = '$nomr'
      #GROUP BY cov.nomr 
      ORDER BY cov.created_at ASC
      #LIMIT 1"
    );
    return $query->result_array();
  }

}

/* End of file ProfileModel.php */
/* Location: ./application/models/ProfileModel.php */
