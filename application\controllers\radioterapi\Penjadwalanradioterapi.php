<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>jadwalanradioterapi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','radioterapi/PenjadwalanRadioterapiModel','rekam_medis/rawat_inap/keperawatan/Idomodel'));
  }

  public function index(){
    $norm = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    // var_dump($norm);exit;
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $dokter = $this->PenjadwalanRadioterapiModel->getDokter();
    $alat = $this->PenjadwalanRadioterapiModel->getAlat(2);
    $alatsimulator = $this->PenjadwalanRadioterapiModel->getAlat(1);
    // $alat = $this->PenjadwalanRadioterapiModel->alat();
    // var_dump($dokter);exit;
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'dokter' => $dokter,
      'alat' => $alat,
      'alatsimulator' => $alatsimulator,
      'getNomr' => $getNomr,
      'jenisMenu' => 'RJ',
    );
    // var_dump($data);exit;
    //  print_r($data['tanggalTD']);exit();
    $this->load->view('Pengkajian/radioTerapi/penjadwalan/penjadwalan_add', $data);
  }



  public function simpanRencanaPenjadwalan()
  {
    $post = $this->input->post();
    $data= $post;
    $data['oleh']= $this->session->userdata('id');
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->PenjadwalanRadioterapiModel->simpanRencanaRadioterapi($data);
  }


  public function simpancheckin()
  {
    $post = $this->input->post();
    $data= $post;
    $this->PenjadwalanRadioterapiModel->simpancheckin($data);
  }

  public function simpanstop()
  {
    $post = $this->input->post();
    $data= $post;
    $this->PenjadwalanRadioterapiModel->simpanstop($data);
  }

  public function simpanreschedule()
  {
    $post = $this->input->post();
    $data= $post;
    $data['oleh']= $this->session->userdata('id');
    $this->PenjadwalanRadioterapiModel->simpanreschedule($data);
  }


  public function getEvent()
  {
    // $data = $this->input->post();
    $data = $this->input->get();
    // var_dump($data['def']);exit;
    
    $ret= $this->PenjadwalanRadioterapiModel->getEvent($data);

    $event=array();
      // $event[]=array('id'=>$data['dfid'],'groupId'=>$data['dfgroupid'],'title'=>$data['dftitle'],'start'=>$data['dfstart'],'color'=> $data['dfbackgroundColor'],'flag'=> $data['dfflag']);
    foreach ($ret as $key => $value) {
      $event[]=array('title'=>((int)$value['TERPAKAI_REG']).'/'.((int)$value['KUOTA_REG']),'start'=>$value['TANGGAL'],'color'=> 'green','flag'=> '1');
      $event[]=array('title'=>((int)$value['TERPAKAI_CITO']).'/'.((int)$value['KUOTA_CITO']),'start'=>$value['TANGGAL'],'color'=> 'red','flag'=> '0');
      
    }
    // var_dump($ret);exit;
    // $output = array(
    //   "status"            => 1,
    //   "message"    => "success",
    //   "data"            => $ret
    // );
    echo json_encode($event);
  }

  public function getTglCTPlan()
  {
    $data = $this->input->get();
    
    $ret= $this->PenjadwalanRadioterapiModel->getTglCTPlan($data);

    $event=array();
    // $event[]=array('id'=>$data['dfid'],'title'=>$data['dftitle'],'start'=>$data['dfstart'],'color'=> $data['dfbackgroundColor'],'flag'=> $data['dfflag']);
    foreach ($ret as $key => $value) {
      $event[]=array('title'=>((int)$value['jml']),'start'=>$value['TGL_CT_PLAN'],'color'=> '#167ccb','flag'=> '1');
      
    }

    // $output = array(
    //   "status"            => 1,
    //   "message"    => "success",
    //   "data"            => $ret
    // );
    echo json_encode($event);
  }


  public function monitoringkedatangan()
  {
    // echo 'a';exit;
    $data=null;
    $dokter = $this->PenjadwalanRadioterapiModel->getDokter();
    $alat = $this->PenjadwalanRadioterapiModel->getAlat(2);
    // $statusPengguna = $_SESSION['status'];
    // $id_pengguna = $this->session->userdata('id');
    // $user = $statusPengguna == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    // $totalPasienRi = $this->MedisModel->total_pasienRi($user);
    // // echo "<pre>";print_r($statusPengguna);exit();
    $data = array(
      'title' => 'Monitoring Kedatangan Pasien Radioterapi',
      'isi' => 'Pengkajian/radioTerapi/penjadwalan/monitoringkedatangan',
      'dokter' => $dokter,
      'alat' => $alat,
      // 'totalPasienRi' => $totalPasienRi,
    );

    $this->load->view('layout/wrapper', $data);
    // $this->load->view('Pengkajian/radioTerapi/penjadwalan/penjadwalan_add', $data);
  }

  public function daftarpasieneksterna()
  {
    // echo 'a';exit;
    $data=null;
    $dokter = $this->PenjadwalanRadioterapiModel->getDokter();
    $alat = $this->PenjadwalanRadioterapiModel->getAlat(2);
    // $statusPengguna = $_SESSION['status'];
    // $id_pengguna = $this->session->userdata('id');
    // $user = $statusPengguna == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    // $totalPasienRi = $this->MedisModel->total_pasienRi($user);
    // // echo "<pre>";print_r($statusPengguna);exit();
    $data = array(
      'title' => 'Daftar Pasien Radiasi Eksterna',
      'isi' => 'Pengkajian/radioTerapi/penjadwalan/pasienradiasieksterna',
      'dokter' => $dokter,
      'alat' => $alat,
      // 'totalPasienRi' => $totalPasienRi,
    );

    $this->load->view('layout/wrapper', $data);
    // $this->load->view('Pengkajian/radioTerapi/penjadwalan/penjadwalan_add', $data);
  }

  public function tblmonitoringkedatangan()
  {
    
      $post = $this->input->post();
      $draw = intval($this->input->post("draw"));
      $start = intval($this->input->post("start"));
      $length = intval($this->input->post("length"));

      // $nokun = $this->input->POST('nokun');
// 
      $retData = $this->PenjadwalanRadioterapiModel->monitoringkedatangan($post);
// var_dump($retData->result());exit;
      $data = array();
      $no = 1;
      foreach ($retData->result() as $dor) {
          $data[] = array(
              $no,
              $dor->NAMA_PASIEN,
              $dor->NAMA_ALAT,
              $dor->SEVERITY,
              $dor->DPJP,
              $dor->TANGGAL,
              ($dor->statustt=='1'?'Aktif':($dor->statustt=='2'?'Checkin':'')),
              '<div class="btn-group btn-group-sm">
              <button type="button" data-id="' . $dor->idtt . '"'.($dor->statustt=='2'?' disabled ':'').' class="btn btn-success btcheckin">Checkin</button>
              <button type="button" data-id="' . $dor->idtt . '"'.($dor->statustt=='2'?' disabled ':'').' class="btn btn-primary btreschedule">Reschedule</button>
              <button type="button" data-id="' . $dor->ID . '"'.($dor->statustt=='2'?' disabled ':'').' class="btn btn-danger btstop">Stop</button>
            </div>'
              // '<a href="#viewDetilResep" class="btn btn-sm btn-primary btn-block" data-toggle="modal" data-id="' . $dor->ID . '" data-status="' . $dor->STATUS . '" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> View</a>'
          );
          $no++;
      }

      $output = array(
          "draw" => $draw,
          "recordsTotal" => $retData->num_rows(),
          "recordsFiltered" => $retData->num_rows(),
          "data" => $data
      );
      echo json_encode($output);
  }

  public function pasienradiasieksterna()
  {
    
      $post = $this->input->post();
      $draw = intval($this->input->post("draw"));
      $start = intval($this->input->post("start"));
      $length = intval($this->input->post("length"));

      // $nokun = $this->input->POST('nokun');
// 
      $retData = $this->PenjadwalanRadioterapiModel->pasienradiasieksterna($post);
// var_dump($retData->result());exit;
      $data = array();
      $no = 1;
      foreach ($retData->result() as $dor) {
          $data[] = array(
              $no,
              $dor->NOMR,
              $dor->NOMR,
              $dor->NAMA_PASIEN,
              $dor->NAMA_ALAT,
              $dor->NAMA_ALAT,
              $dor->TGL_CT_PLAN,
              $dor->NAMA_ALAT,
              $dor->tgl_mulai,
              $dor->JUMLAH_JADWAL,
              $dor->JUMLAH_JADWAL,
              $dor->tgl_akhir,
              $dor->DPJP,
              ($dor->statustt=='1'?'Aktif':($dor->statustt=='2'?'Stop':'')),
              '<div class="btn-group btn-group-sm">
             
            </div>'
              // '<a href="#viewDetilResep" class="btn btn-sm btn-primary btn-block" data-toggle="modal" data-id="' . $dor->ID . '" data-status="' . $dor->STATUS . '" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> View</a>'
          );
          $no++;
      }

      $output = array(
          "draw" => $draw,
          "recordsTotal" => $retData->num_rows(),
          "recordsFiltered" => $retData->num_rows(),
          "data" => $data
      );
      echo json_encode($output);
  }

}

/* End of file TreatmentDose.php */
/* Location: ./application/controllers/radioterapi/TreatmentDose.php */
