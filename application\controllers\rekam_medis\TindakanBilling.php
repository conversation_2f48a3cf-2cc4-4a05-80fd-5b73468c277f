<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TindakanBilling extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'TindakanBillingModel', 'TindakanModel'));
    }

    public function index()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(4));
        $nopen = $this->uri->segment(5);
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $smf = $this->session->userdata('smf');
        $data = array(
            'getNomr' => $getNomr,
            'pasien' => $pasien,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'smf' => $smf,
        );
        $this->load->view('Pengkajian/tindakanBilling', $data);
    }
    public function getTindakanList()
    {
        $ruangan = $this->input->post('ruangan');
        $smf = $this->input->post('smf');
        $search = $this->input->post('search');
        $tindakanList = $this->TindakanBillingModel->listTindakan($ruangan, $smf, $search);
        echo json_encode($tindakanList);
    }

    public function kirimOrder()
    {
        $post = $this->input->post();

        if (empty($post['kunjungan']) || empty($post['dokter']) || empty($post['dokter_asal']) || empty($post['ruangan']) || empty($post['id_tindakan'])) {
            $response = [
                'success' => false,
                'message' => 'Data yang diperlukan tidak lengkap.'
            ];
            echo json_encode($response);
            return;
        }

        $nokun = $post['kunjungan'];
        $oleh = $this->session->userdata('id');
        $olehDok = $this->session->userdata('iddokter');

        foreach ($post['id_tindakan'] as $tindakan) {
            $result = $this->TindakanBillingModel->simpanOrder($nokun, $tindakan, $oleh, $olehDok);

            if (!$result['success']) {
                $response = [
                    'success' => false,
                    'message' => 'Gagal menyimpan tindakan: ' . $tindakan
                ];
                echo json_encode($response);
                return;
            }
        }

        $response = [
            'success' => true,
            'message' => 'Order berhasil disimpan.'
        ];
        echo json_encode($response);
    }

    public function getTindakanTable()
    {
        // Ambil parameter draw, length, dan start dari POST
        $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
        $length = isset($_POST['length']) ? intval($_POST['length']) : 10; // Default 10 records per page
        $start = isset($_POST['start']) ? intval($_POST['start']) : 0;

        $nokun = $this->input->post('kunjungan');

        // Get data for each section
        $tindakanData = $this->TindakanBillingModel->getTindakanDatatable($nokun, $length, $start);
        $labData = $this->TindakanBillingModel->getLabDatatable($nokun, $length, $start);
        $radiologiData = $this->TindakanBillingModel->getRadiologiDatatable($nokun, $length, $start);
        // $radioterapiData = $this->TindakanBillingModel->getRadioterapiDatatable($nokun, $length, $start);
        $resepData = $this->TindakanBillingModel->getResepDatatable($nokun, $length, $start);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array();
        $no = 1;

        // Daftar Tindakan
        $data[] = array(
            'no' => '',
            'nama_tindakan' => 'Daftar Tindakan',
            'tarif' => '',
            'status' => '',
            'is_header' => true
        );
        foreach ($tindakanData as $tindakan) {
            $row = array();
            $row['no'] = $no++;
            $row['nama_tindakan'] = $tindakan->nama_tindakan;
            $row['tarif'] = (isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14) ? number_format($tindakan->tarif, 0, ',', '.') : '-';
            $row['status'] = $tindakan->status == 1 ? '<span class="text-warning">Belum difinalkan</span>' : ($tindakan->status == 0 ? '<span class="text-danger">Dibatalkan</span>' : '<span class="text-primary">Difinalkan</span>');
            $row['is_header'] = false;
            $data[] = $row;
        }

        // Lab
        $data[] = array(
            'no' => '',
            'nama_tindakan' => 'Lab',
            'tarif' => '',
            'status' => '',
            'is_header' => true
        );
        foreach ($labData as $lab) {
            $row = array();
            $row['no'] = $no++;
            $row['nama_tindakan'] = $lab->nama_tindakan;
            $row['tarif'] = (isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14) ? number_format($lab->tarif, 0, ',', '.') : '-';
            $row['status'] = $tindakan->status == 1 ? '<span class="text-warning">Belum difinalkan</span>' : ($tindakan->status == 0 ? '<span class="text-danger">Dibatalkan</span>' : '<span class="text-primary">Difinalkan</span>'); // No status for lab
            $row['is_header'] = false;
            $data[] = $row;
        }

        // Radiologi
        $data[] = array(
            'no' => '',
            'nama_tindakan' => 'Radiologi',
            'tarif' => '',
            'status' => '',
            'is_header' => true
        );
        foreach ($radiologiData as $radiologi) {
            $row = array();
            $row['no'] = $no++;
            $row['nama_tindakan'] = $radiologi->nama_tindakan;
            $row['tarif'] = (isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14) ? number_format($radiologi->tarif, 0, ',', '.') : '-';
            $row['status'] = $tindakan->status == 1 ? '<span class="text-warning">Belum difinalkan</span>' : ($tindakan->status == 0 ? '<span class="text-danger">Dibatalkan</span>' : '<span class="text-primary">Difinalkan</span>'); // No status for radiologi
            $row['is_header'] = false;
            $data[] = $row;
        }

        // Radioterapi
        // $data[] = array(
        //     'no' => '',
        //     'nama_tindakan' => 'Radioterapi',
        //     'tarif' => '',
        //     'status' => '',
        //     'is_header' => true
        // );
        // foreach ($radioterapiData as $radioterapi) {
        //     $row = array();
        //     $row['no'] = $no++;
        //     $row['nama_tindakan'] = $radioterapi->nama_tindakan;
        //     $row['tarif'] = number_format($radioterapi->tarif, 0, ',', '.');
        //     $row['status'] = $tindakan->status == 1 ? '<span class="text-warning">Belum difinalkan</span>' : ($tindakan->status == 0 ? '<span class="text-danger">Dibatalkan</span>' : '<span class="text-primary">Difinalkan</span>'); // No status for radioterapi
        //     $row['is_header'] = false;
        //     $data[] = $row;
        // }

        // Resep/Farmasi
        $data[] = array(
            'no' => '',
            'nama_tindakan' => 'Farmasi',
            'tarif' => '',
            'status' => '',
            'is_header' => true
        );
        foreach ($resepData as $resep) {
            $row = array();
            $row['no'] = $no++;
            $row['nama_tindakan'] = $resep->NAMA;
            $row['tarif'] = (isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14) ? number_format($resep->HARGA_JUAL, 0, ',', '.') : '-';
            $row['status'] = $tindakan->status == 1 ? '<span class="text-warning">Belum difinalkan</span>' : ($tindakan->status == 0 ? '<span class="text-danger">Dibatalkan</span>' : '<span class="text-primary">Difinalkan</span>'); // No status for resep
            $row['is_header'] = false;
            $data[] = $row;
        }

        // Return the combined result
        $output = array(
            "draw" => $draw,
            "recordsTotal" => $no - 1,
            "recordsFiltered" => $no - 1,
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function getDaftarTindakan()
    {
        $nokun = $this->input->post('kunjungan');
        $dokter = $this->session->userdata('id');
        $result = $this->TindakanBillingModel->getDaftarTindakan($nokun, $dokter);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array();
        $no = $_POST['start'];

        foreach ($result['data'] as $tindakan) {
            $no++;
            $row = array();
            $row['no'] = $no;
            $row['nama_tindakan'] = $tindakan->nama_tindakan;
            $row['tarif'] = (isset($getNomr['JENIS_KUNJUNGAN']) && $getNomr['JENIS_KUNJUNGAN'] == 14) ? number_format($tindakan->tarif, 0, ',', '.') : '-';
            $row['status'] = $tindakan->status == 1
                ? '<button class="btn btn-primary btn-batalkan" title="Batalkan" data-id="' . $tindakan->ID . '">
                        <i class="fa fa-trash"></i>
                    </button>'
                : '<button class="btn btn-secondary" title="Dibatalkan" disabled>
                        Dibatalkan
                    </button>';
            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $result['total_records'],
            "recordsFiltered" => $result['filtered_records'],
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function batalkanTindakan()
    {
        $tindakan_id = $this->input->post('tindakan_id');

        if (empty($tindakan_id)) {
            $response = array(
                'status' => 'error',
                'message' => 'ID tindakan tidak valid.'
            );
            echo json_encode($response);
            return;
        }

        $result = $this->TindakanBillingModel->batalkanTindakan($tindakan_id);

        if ($result) {
            $response = array(
                'status' => 'success',
                'message' => 'Tindakan berhasil dibatalkan.'
            );
        } else {
            $response = array(
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat membatalkan tindakan.'
            );
        }

        echo json_encode($response);
    }
}
