<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PenilaianStatusGiziModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.',
                        'numeric' => '%s Wajib <PERSON>ka.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('penkun.NOPEN, sg.created_at TANGGAL, sg.NOKUN
        , master.getNamaLengkapPegawai(peng.NIP) USER');
        $this->db->from('medis.tb_penilaian_status_gizi sg');
        $this->db->join('aplikasi.pengguna peng','peng.ID = sg.oleh','LEFT');
        $this->db->join('pendaftaran.kunjungan penkun','sg.nokun = penkun.NOMOR','LEFT');
        $this->db->join('pendaftaran.pendaftaran penpen','penkun.NOPEN = penpen.NOMOR','LEFT');
        $this->db->where('sg.STATUS !=','0');
        $this->db->where('penpen.NORM',$this->input->post('nomr'));
        $this->db->order_by('sg.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($nokun)
    {
      $query = $this->db->query(
        'SELECT pg.`*`, tbbb.tb, tbbb.bb bb_sekarang FROM medis.tb_penilaian_status_gizi pg
        left join db_pasien.tb_tb_bb tbbb ON pg.nokun = tbbb.nokun AND tbbb.data_source = 21
        where pg.nokun = "'.$nokun.'"
       	order by tbbb.created_at DESC limit 1'
      );
      return $query->row_array();
    }

    public function getTBBB($nopen)
    {
        // $query = $this->db->query(
        //     'SELECT * FROM db_pasien.tb_tb_bb tbbb
        //     WHERE tbbb.nokun = "'.$nokun.'"
        //     ORDER BY tbbb.created_at DESC limit 1'
        // );

        $query = $this->db->query(
                    'SELECT tb.*, pkun.NOPEN
                    FROM db_pasien.tb_tb_bb tb
                    LEFT JOIN pendaftaran.kunjungan pkun ON pkun.NOMOR = tb.nokun
                    WHERE pkun.NOPEN="'.$nopen.'" AND tb.`status`=1
                    ORDER BY tb.created_at DESC
                    LIMIT 1'
                );

        return $query->row_array();
    }

    public function getTotalSkorGizi($nokun)
    {
        $query = $this->db->query(
            'SELECT (penurbb.nilai+asumak.nilai) TOTAL_SKOR_GIZI FROM keperawatan.tb_keperawatan k 
            LEFT JOIN keperawatan.tb_skrining_gizi sg ON sg.id_emr = k.id_emr
            LEFT JOIN db_master.variabel penurbb ON penurbb.id_variabel = sg.penurunan_bb
            LEFT JOIN db_master.variabel asumak ON asumak.id_variabel = sg.asupan_bb
            WHERE k.nokun = "'.$nokun.'" AND k.`status` = 1
            ORDER	BY k.created_at DESC LIMIT 1'
        );

        return $query->row_array();
    }

    public function getDiagnosaMasuk($nopen)
    {
        $query = $this->db->query(
            "SELECT a.diagnosa FROM
            ((SELECT kp.diagnosa_masuk diagnosa, kp.created_at tgl
            FROM keperawatan.tb_keperawatan kp
            
            WHERE kp.`status`=1 AND kp.flag=1 AND kp.diagnosa_masuk IS NOT NULL 
                AND kp.nopen='$nopen'
            ORDER BY kp.created_at DESC)
            UNION ALL
            (SELECT mk.desk_diagnosa_medis diagnosa, md.created_at tgl FROM medis.tb_medis md
            
                LEFT JOIN medis.tb_masalah_medis_kep mk ON mk.id_emr = md.id_emr
            
            WHERE md.`status`=1 AND md.flag=1 AND mk.desk_diagnosa_medis IS NOT NULL
                AND md.nopen='$nopen'
            ORDER BY md.created_at DESC)) a
            
            ORDER BY a.tgl DESC
            
            LIMIT 1"
        );

        return $query->row_array();
    }

}
