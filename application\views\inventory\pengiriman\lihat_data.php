<div class="row">
	<div class="col-sm-12">
		<h4 class="page-title"><PERSON> Permintaan</h4>
	</div>
</div>  
<div class="row">
	<div class="col-12">
		<div class="card-box">		

			<select class="form-control select2" id="gudang" name="id_gudang" onchange="cek_data()">
				<option value=""></option>
				<?php foreach ($gudang as $k) {
					echo "<option value='$k->ID'>$k->DESKRIPSI</option>";
				} ?>
			</select>				
			<br><br>
			<div class="loading"></div>
			<div class="tampilkan_data"></div>                              		
			
		</div> 
	</div>
</div> <!-- end row -->

<script type="text/javascript">
  function cek_data()
  {
    sel_gudang = $('[name="id_gudang"]');
    $.ajax({
      type : 'POST',
      data: "cari="+1+"&id_gudang="+sel_gudang.val(),
      url  : 'pengiriman/view_data',
      cache: false,
      beforeSend: function() {
        sel_gudang.attr('disabled', true);
        // $('.loading').html('Loading...');
    },
    success: function(data){
        sel_gudang.attr('disabled', false);
        // $('.loading').html('');
        $('.tampilkan_data').html(data);
    }
});
    return false;
}
</script>