<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RkaklModel extends MY_Model{
	protected $_table_name = 'db_keuangan.rkakl';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'program' => array(
            'field' => 'program',
            'label' => 'Program',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.'
                ),
        ),

        'uraian' => array(
            'field' => 'uraian',
            'label' => 'Uraian',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        // 'volume' => array(
        //     'field' => 'volume',
        //     'label' => 'Volume',
        //     'rules' => 'trim|numeric|required',
        //     'errors' => array(
        //                 'required' => '%s Wajib <PERSON>isi.',
        //                 'numeric' => '%s <PERSON>ajib <PERSON>.'
        //         ),
        // ),

        // 'satuan' => array(
        //     'field' => 'satuan',
        //     'label' => 'Satuan',
        //     'rules' => 'trim|required',
        //     'errors' => array(
        //                 'required' => '%s Wajib Diisi.'
        //         ),
        // ),

        // 'harga_satuan' => array(
        //     'field' => 'harga_satuan',
        //     'label' => 'Harga Satuan',
        //     'rules' => 'trim|numeric|required',
        //     'errors' => array(
        //                 'required' => '%s Wajib Diisi.',
        //                 'numeric' => '%s Wajib Angka.'
        //         ),
        // ),

        'pagu_awal' => array(
            'field' => 'pagu_awal',
            'label' => 'Pagu Awal',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'sumber_dana' => array(
            'field' => 'sumber_dana',
            'label' => 'Sumber Dana',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'periode' => array(
            'field' => 'periode',
            'label' => 'Periode',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('*');
        $this->db->from('db_keuangan.rkakl r');
        if($this->input->post('search[value]')){
			$this->db->like('r.URAIAN',$this->input->post('search[value]'));	
		}
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
