<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PT_PengobatanKemoterapi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','informedConsent/PT_PengobatanKemoterapiModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $listDrUmum = $this->masterModel->listDrUmum();

    $data = array(
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'listDrUmum' => $listDrUmum,
      'dasarDiagnosisPKem' => $this->masterModel->referensi(847),
      'tindakanKedokteranPKem' => $this->masterModel->referensi(848),
      'indikasiTindakanPKem' => $this->masterModel->referensi(850),
      'tataCaraPKem' => $this->masterModel->referensi(851),
      'tujuanTindakanPKem' => $this->masterModel->referensi(852),
      'tujuanPengobatanPKem' => $this->masterModel->referensi(853),
      'risikoPKem' => $this->masterModel->referensi(854),
      'komplikasiPKem' => $this->masterModel->referensi(855),
      'prognosisPKem' => $this->masterModel->referensi(856),
      'alternatifDanResikoPKem' => $this->masterModel->referensi(859),
      'lainLainPKem' => $this->masterModel->referensi(860),
      'jenis_kelamin' => $this->masterModel->referensi(965),
    );

    $this->load->view('Pengkajian/informedConsent/persetujuanTindakanPengobatanKemoterapi/index', $data);
  }

  public function indexRawatInap()
  {
    $nokun = $this->uri->segment(2);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'getNomr' => $getNomr,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'dasarDiagnosisPKem' => $this->masterModel->referensi(847),
      'tindakanKedokteranPKem' => $this->masterModel->referensi(848),
      'indikasiTindakanPKem' => $this->masterModel->referensi(850),
      'tataCaraPKem' => $this->masterModel->referensi(851),
      'tujuanTindakanPKem' => $this->masterModel->referensi(852),
      'tujuanPengobatanPKem' => $this->masterModel->referensi(853),
      'risikoPKem' => $this->masterModel->referensi(854),
      'komplikasiPKem' => $this->masterModel->referensi(855),
      'prognosisPKem' => $this->masterModel->referensi(856),
      'alternatifDanResikoPKem' => $this->masterModel->referensi(859),
      'lainLainPKem' => $this->masterModel->referensi(860),
      'jenis_kelamin' => $this->masterModel->referensi(965),
    );
    $this->load->view('Pengkajian/informedConsent/persetujuanTindakanPengobatanKemoterapi/index', $data);
  }

  public function simpanPTKemoterapi()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $dataInformedConcent = array (
      'nokun'                  => $post['nokun'],
      'jenis_informed_consent' => 3031,
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakan'],
      'penerima_informasi'     => $post['penerimaInformasi'],
      'oleh'                   => $this->session->userdata('id'),
    );

    $idInformedConcent = $this->PT_PengobatanKemoterapiModel->simpanInformedConcent($dataInformedConcent);

    $dataKemo = array (
      'id_informed_consent'              => $idInformedConcent,
      'diagnosis_kerja'                  => $post["diagnosisKerja"],
      'dasar_diagnosis'                  => implode(',',$post["dasarDiagnosisPKem"]),
      'dasar_diagnosis_pemeriksaan_lain' => $post["deskDasarDiagnosisPKem"],
      'tindakan_kedokteran_kemo_desk'    => $post["tKKemoterapi"],
      'tindakan_kedokteran_jml_siklus'   => $post["tKJumlahSiklus"],
      'tindakan_kedokteran'              => implode(',',$post["tindakanKedokteranPKem"]),
      'tindakan_kedokteran_lain'         => $post["deskTindakanKedokteranPKem"],
      'indikasi_tindakan'                => implode(',',$post["indikasiTindakanPKem"]),
      'tata_cara'                        => implode(',',$post["tataCaraPKem"]),
      'tujuan_tindakan'                  => implode(',',$post["tujuanTindakanPKem"]),
      'tujuan_pengobatan'                => implode(',',$post["tujuanPengobatanPKem"]),
      'risiko'                           => implode(',',$post["risikoPKem"]),
      'risiko_lain'                      => $post["deskRisikoPKem"],
      'komplikasi'                       => implode(',',$post["komplikasiPKem"]),
      'komplikasi_lain'                  => $post["deskKomplikasiPKem"],
      'prognosis'                        => implode(',',$post["prognosisPKem"]),
      'prognosis_harapan_hidup'          => $post["prognosisHarapan"],
      'prognosis_tahun'                  => $post["prognosisTahun"],
      'prognosis_lain'                   => $post["deskPrognosisPKem"],
      'alternatif_dan_risiko'            => implode(',',$post["alternatifDanResikoPKem"]),
      'alternatif_diubah_menjadi'        => $post["alternatif_diubah_menjadi"],
      'alternatif_alasan'                => $post["alternatif_alasan"],
      'lain_lain'                        => implode(',',$post["lainLainPKem"]),
      'lain_lain_dipantau_selama'        => $post["lain_lain_dipantau_selama"],
    );

    $this->PT_PengobatanKemoterapiModel->simpanPengobatanKemo($dataKemo);

    $dataPersetujuanTidakanKedokteran = array(
      'id_informed_consent'     => $idInformedConcent,
      'nama_keluarga'           => $post['nama'],
      'umur_keluarga'           => $post['umur'],
      'jk_keluarga'             => $post['jenis_kelamin'],
      'alamat_keluarga'         => $post['alamat'],
      'tindakan'                => $post['tindakan'],
      'hub_keluarga_dgn_pasien' => $post['hubungan'],
      'tanggal_persetujuan'     => $post['tanggalPerstujuanKemo'].' '.$post['jamPerstujuanKemo'],
      'ttd_menyatakan'          => file_get_contents($this->input->post('signMenyatakanKemo')),
      'ttd_saksi_keluarga'      => file_get_contents($this->input->post('signKeluargaKemo')),
      'ttd_saksi_rumah_sakit'   => file_get_contents($this->input->post('signRumahSakitKemo')),
      'saksi_keluarga'          => $post['nama_keluarga'],
      'saksi_rumah_sakit'       => $post['nama_saksi_rs']
    );

    $this->PT_PengobatanKemoterapiModel->simpanPersetujuanTidakanKedokteran($dataPersetujuanTidakanKedokteran);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyPTKemoterapi()
  // 1412504274
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listPTKemoterapi = $this->PT_PengobatanKemoterapiModel->listHistoryInformedConsentKemo($nomr);

    $data = array();
    $no = 1;
    foreach ($listPTKemoterapi->result() as $PTKemoterapi) {

      $data[] = array(
        $no,
        $PTKemoterapi->nokun,
        $PTKemoterapi->DOKTERPELAKSANA,
        $PTKemoterapi->OLEH,
        date("d-m-Y H:i:s",strtotime($PTKemoterapi->tanggal)),
        '<a href="#modalPTKemoterapi" class="btn btn-primary btn-block" data-id="'.$PTKemoterapi->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> View</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listPTKemoterapi->num_rows(),
      "recordsFiltered" => $listPTKemoterapi->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalPTKemoterapi()
  {
    $id = $this->input->post('id');
    $gptk = $this->PT_PengobatanKemoterapiModel->getPTKemoterapi($id);
    $explode_dasar_diagnosis       = explode(',' , $gptk['dasar_diagnosis']);
    $explode_tindakan_kedokteran   = explode(',' , $gptk['tindakan_kedokteran']);
    $explode_indikasi_tindakan     = explode(',' , $gptk['indikasi_tindakan']);
    $explode_tata_cara             = explode(',' , $gptk['tata_cara']);
    $explode_tujuan_tindakan       = explode(',' , $gptk['tujuan_tindakan']);
    $explode_tujuan_pengobatan     = explode(',' , $gptk['tujuan_pengobatan']);
    $explode_risiko                = explode(',' , $gptk['risiko']);
    $explode_komplikasi            = explode(',' , $gptk['komplikasi']);
    $explode_prognosis             = explode(',' , $gptk['prognosis']);
    $explode_alternatif_dan_risiko = explode(',' , $gptk['alternatif_dan_risiko']);
    $explode_lain_lain             = explode(',' , $gptk['lain_lain']);
    // echo "<pre>";print_r($gptk);exit();

    $data = array(
      'id' => $id,
      'gptk' => $gptk,
      'explode_dasar_diagnosis'       => $explode_dasar_diagnosis,
      'explode_tindakan_kedokteran'   => $explode_tindakan_kedokteran,
      'explode_indikasi_tindakan'     => $explode_indikasi_tindakan,
      'explode_tata_cara'             => $explode_tata_cara,
      'explode_tujuan_tindakan'       => $explode_tujuan_tindakan,
      'explode_tujuan_pengobatan'     => $explode_tujuan_pengobatan,
      'explode_risiko'                => $explode_risiko,
      'explode_komplikasi'            => $explode_komplikasi,
      'explode_prognosis'             => $explode_prognosis,
      'explode_alternatif_dan_risiko' => $explode_alternatif_dan_risiko,
      'explode_lain_lain'             => $explode_lain_lain,
      // Informed Consent
      'dasarDiagnosisPKem' => $this->masterModel->referensi(847),
      'tindakanKedokteranPKem' => $this->masterModel->referensi(848),
      'indikasiTindakanPKem' => $this->masterModel->referensi(850),
      'tataCaraPKem' => $this->masterModel->referensi(851),
      'tujuanTindakanPKem' => $this->masterModel->referensi(852),
      'tujuanPengobatanPKem' => $this->masterModel->referensi(853),
      'risikoPKem' => $this->masterModel->referensi(854),
      'komplikasiPKem' => $this->masterModel->referensi(855),
      'prognosisPKem' => $this->masterModel->referensi(856),
      'alternatifDanResikoPKem' => $this->masterModel->referensi(859),
      'lainLainPKem' => $this->masterModel->referensi(860),
      'listDrUmum' => $this->masterModel->listDrUmum(),
       'jenis_kelamin' => $this->masterModel->referensi(965),
    );

    $this->load->view('Pengkajian/informedConsent/persetujuanTindakanPengobatanKemoterapi/edit', $data);
  }

  public function updatePTKemoterapi()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    $idtpk = $this->input->post('idtpk');
    $post = $this->input->post();

    $dataInformedConcent = array (
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanEdit'],
      'penerima_informasi'     => $post['penerimaInformasiEdit'],
    );

    $this->PT_PengobatanKemoterapiModel->updateInformedConcent($dataInformedConcent,$id);

    $dataKemo = array (
      'diagnosis_kerja'                  => $post["diagnosisKerjaEdit"],
      'dasar_diagnosis'                  => implode(',',$post["dasarDiagnosisPKemEdit"]),
      'dasar_diagnosis_pemeriksaan_lain' => $post["deskDasarDiagnosisPKemEdit"],
      'tindakan_kedokteran_kemo_desk'    => $post["tKKemoterapiEdit"],
      'tindakan_kedokteran_jml_siklus'   => $post["tKJumlahSiklusEdit"],
      'tindakan_kedokteran'              => implode(',',$post["tindakanKedokteranPKemEdit"]),
      'tindakan_kedokteran_lain'         => $post["deskTindakanKedokteranPKemEdit"],
      'indikasi_tindakan'                => implode(',',$post["indikasiTindakanPKemEdit"]),
      'tata_cara'                        => implode(',',$post["tataCaraPKemEdit"]),
      'tujuan_tindakan'                  => implode(',',$post["tujuanTindakanPKemEdit"]),
      'tujuan_pengobatan'                => implode(',',$post["tujuanPengobatanPKemEdit"]),
      'risiko'                           => implode(',',$post["risikoPKemEdit"]),
      'risiko_lain'                      => $post["deskRisikoPKemEdit"],
      'komplikasi'                       => implode(',',$post["komplikasiPKemEdit"]),
      'komplikasi_lain'                  => $post["deskKomplikasiPKemEdit"],
      'prognosis'                        => implode(',',$post["prognosisPKemEdit"]),
      'prognosis_harapan_hidup'          => $post["prognosisHarapanEdit"],
      'prognosis_tahun'                  => $post["prognosisTahunEdit"],
      'prognosis_lain'                   => $post["deskPrognosisPKemEdit"],
      'alternatif_dan_risiko'            => implode(',',$post["alternatifDanResikoPKemEdit"]),
      'alternatif_diubah_menjadi'        => $post["alternatif_diubah_menjadiEdit"],
      'alternatif_alasan'                => $post["alternatif_alasanEdit"],
      'lain_lain'                        => implode(',',$post["lainLainPKemEdit"]),
      'lain_lain_dipantau_selama'        => $post["lain_lain_dipantau_selamaEdit"],
    );

    $this->PT_PengobatanKemoterapiModel->updatePengobatanKemo($dataKemo,$idtpk);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}

/* End of file PT_PengobatanKemoterapi.php */
/* Location: ./application/controllers/informedConsent/PT_PengobatanKemoterapi.php */
