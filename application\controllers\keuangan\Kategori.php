<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>gori extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('keuangan/KategoriModel'));
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'get'){
                $kategori = $this->KategoriModel->get_table(FALSE);

                $data = array();
                foreach($kategori as $dataKategori){
                    $sub_array = array();
                    $sub_array['id'] = $dataKategori -> ID;
                    $sub_array['text'] = $dataKategori -> URAIAN;

                    $data[] = $sub_array;
                }

                echo json_encode($data);
            }
    	}
    }
}