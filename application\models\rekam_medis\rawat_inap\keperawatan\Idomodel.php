<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Idomodel extends CI_Model
{

  function __construct()
  {
    parent::__construct();
  }

  public function mdokter()
  {
    $squery =
      "SELECT dok.ID,
          master.getNamaLengkapPegawai(dok.NIP) DOKTER
      FROM master.dokter dok
          LEFT JOIN master.pegawai p ON p.NIP = dok.NIP
      WHERE p.STATUS = 1
          AND dok.STATUS = 1";

    $query = $this->db->query($squery);
    return $query->result_array();
  }

  public function mpegmedis()
  {
    $squery =
      "SELECT dok.NIP,
          master.getNamaLengkapPegawai(dok.NIP) NAMA
      FROM master.dokter dok
          LEFT JOIN master.pegawai p ON p.NIP = dok.NIP
      WHERE p.STATUS = 1
          AND dok.STATUS = 1
      UNION
      SELECT pr.NIP,
          master.getNamaLengkapPegawai(pr.NIP) NAMA
      FROM master.perawat pr
          LEFT JOIN master.pegawai p ON p.NIP = pr.NIP
      WHERE p.STATUS = 1
          AND pr.STATUS = 1";

    $query = $this->db->query($squery);
    return $query->result_array();
  }

  public function mperawat()
  {
    $squery =
      "SELECT pg.NIP,
          master.getNamaLengkapPegawai(pg.NIP) PERAWAT
      FROM master.pegawai pg
      WHERE pg.PROFESI = 6";

    $query = $this->db->query($squery);
    return $query->result_array();
  }

  public function mperawatanastesi()
  {
    $squery =
      "SELECT pg.NIP, master.getNamaLengkapPegawai(pg.NIP) PERAWAT
      FROM master.pegawai pg
      WHERE pg.PROFESI=19";

    $query = $this->db->query($squery);
    return $query->result_array();
  }

  public function getpasien($nokun)
  {
    $squery =
      "SELECT ruan.DESKRIPSI RUANGAN,
          ruan.ID ID_RUANGAN,
          ps.NORM nomr,
          ps.NAMA,
          IF(ps.JENIS_KELAMIN = 1, '52', '53') KODE_JENIS_KELAMIN,
          IF(ps.JENIS_KELAMIN = 1, 'Laki-Laki', 'Perempuan') JENIS_KELAMIN,
          ps.JENIS_KELAMIN ID_JK,
          DATE_FORMAT(ps.TANGGAL_LAHIR, '%d-%m-%Y') TANGGAL_LAHIR,
          p.TANGGAL TGL_MASUK,
          (YEAR(CURDATE()) - YEAR(ps.TANGGAL_LAHIR)) UMUR,
          concat((YEAR(CURDATE()) - YEAR(ps.TANGGAL_LAHIR)), ' Th') UMUR_T,
          ref.DESKRIPSI PENJAMIN,
          ref.ID ID_PENJAMIN,
          IF(ref.ID = 2, '56', IF(ref.ID = 62, '57', IF(1, '54', '55'))) PENANGGUNG,
          IF(
              ref.ID = 2,
              'BPJS',
              IF(
                  ref.ID = 62,
                  'Karyawan RSKD',
                  IF(1, 'Umum/Tanpa Asuransi', 'Jaminan Perusahaan')
              )
          ) NAMA_PENANGGUNG,
          neg.DESKRIPSI KEWARGANEGARAAN,
          pek.DESKRIPSI PEKERJAAN,
          kp.NOMOR TELPON,
          ps.ALAMAT,
          kip.NOMOR KTP,
          p.NOMOR NOPEN,
          dia.ICD,
          dia.DIAGNOSA,
          CONCAT(dia.ICD, '-', dia.DIAGNOSA) DIAGMASUK,
          kk.NOMOR TELPKEL,
          kel.NAMA NAMAKEL,
          (
              SELECT COUNT(id) + 1
              FROM db_ppi.tb_postido
              WHERE nomr = ps.NORM
          ) postke
      FROM pendaftaran.pendaftaran p
          LEFT JOIN pendaftaran.kunjungan pk ON p.NOMOR = pk.NOPEN
          LEFT JOIN master.ruangan ruan ON ruan.ID = pk.RUANGAN
          LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
          LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
          LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
          LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
          LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS
          AND ref.JENIS = 10
          left JOIN master.negara neg ON neg.ID = ps.KEWARGANEGARAAN
          LEFT JOIN master.referensi pek ON pek.ID = ps.PEKERJAAN
          and pek.JENIS = 4
          LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
          LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
          LEFT JOIN master.diagnosa_masuk dia ON dia.ID = p.DIAGNOSA_MASUK
          LEFT JOIN master.kontak_keluarga_pasien kk ON kk.NORM = p.NORM
          LEFT JOIN master.keluarga_pasien kel ON kel.NORM = p.NORM
      WHERE pk.NOMOR = '$nokun'
          AND p.STATUS != 0 #AND pk.STATUS=1
          #AND ruan.JENIS_KUNJUNGAN in (3)
      ORDER BY pk.MASUK desc
      LIMIT 1";
    // echo $squery;exit;
    $query = $this->db->query($squery);
    return $query->row_array();
  }

  public function simpanpreido($param = array())
  {

    if ($param['id'] > 0) {
      $squery =
        "UPDATE db_ppi.tb_survido
        SET nomr                          = '$param[nomr]',
            nokun                         = '$param[nokun]',
            tglsurveilan                  = '$param[tglsurveilan]',
            tb                            = '$param[tb]',
            bb                            = '$param[bb]',
            tindakan                      = '$param[tindakan]',
            tgl_rencana                   = '$param[tgl_rencana]',
            dokteroperator                = '$param[dokteroperator]',
            suhu                          = '$param[suhu]',
            lab_tanggal                   = '$param[lab_tanggal]',
            glukosa                       = '$param[glukosa]',
            hb                            = '$param[hb]',
            leukosit                      = '$param[leukosit]',
            albumin                       = '$param[albumin]',
            tgl_mandi1                    = '$param[tgl_mandi1]',
            tgl_mandi2                    = '$param[tgl_mandi2]',
            mandi_menggunakan             = '$param[mandi_menggunakan]',
            mandi_menggunakan_lain        = '$param[mandi_menggunakan_lain]',
            alat_cukur                    = '$param[alat_cukur]',
            lokasi_mandi                  = '$param[lokasi_mandi]',
            tgl_cukur                     = '$param[tgl_cukur]',
            asascore                      = '$param[asascore]',
            petugas_asa_score             = '$param[petugas_asa_score]',
            klasifikasi_lukaop            = '$param[klasifikasi_lukaop]',
            urgensi_op                    = '$param[urgensi_op]',
            ruang_operasi                 = '$param[ruang_operasi]',
            nama_operator                 = '$param[nama_operator]',
            nama_asisten                  = '$param[nama_asisten]',
            asisten_dokter                = '$param[asisten_dokter]',
            perawat_instrumen             = '$param[perawat_instrumen]',
            perawat_sirkuler              = '$param[perawat_sirkuler]',
            dokter_anastesi               = '$param[dokter_anastesi]',
            perawat_anastesi              = '$param[perawat_anastesi]',
            ab_dibutuhkan                 = '$param[ab_dibutuhkan]',
            ab_alasanlain                 = '$param[ab_alasanlain]',
            ab_profilaksis                = '$param[ab_profilaksis]',
            nama_ab_prof                  = '$param[nama_ab_prof]',
            dosis_ab_prof                 = '$param[dosis_ab_prof]',
            waktu_ab_prof                 = '$param[waktu_ab_prof]',
            ulang_ab_prof                 = '$param[ulang_ab_prof]',
            waktu_pemberian_ulang_ab_prof = '$param[waktu_pemberian_ulang_ab_prof]',
            antiseptik                    = '$param[antiseptik]',
            teknik_preparasi              = '$param[teknik_preparasi]',
            nama_hh_operator              = '$param[nama_hh_operator]',
            nama_hh_perawat               = '$param[nama_hh_perawat]',
            nama_hh_sirkuler              = '$param[nama_hh_sirkuler]',
            nama_hh_asisten               = '$param[nama_hh_asisten]',
            hh_perawat                    = '$param[hh_perawat]',
            hh_sirkuler                   = '$param[hh_sirkuler]',
            hh_operator                   = '$param[hh_operator]',
            hh_asisten                    = '$param[hh_asisten]',
            jml_petugas_op                = '$param[jml_petugas_op]',
            jml_selain_petugas_op         = '$param[jml_selain_petugas_op]',
            jml_pintu_dibuka              = '$param[jml_pintu_dibuka]',
            terpasang_drain               = '$param[terpasang_drain]',
            lokasi_drain                  = '$param[lokasi_drain]',
            tipe_drain                    = '$param[tipe_drain]',
            penggunaan_alat_reuse         = '$param[penggunaan_alat_reuse]',
            nama_alat_reuse1              = '$param[nama_alat_reuse1]',
            nama_alat_reuse2              = '$param[nama_alat_reuse2]',
            nama_alat_reuse3              = '$param[nama_alat_reuse3]',
            alat_reuse1                   = '$param[alat_reuse1]',
            alat_reuse2                   = '$param[alat_reuse2]',
            alat_reuse3                   = '$param[alat_reuse3]',
            penggunaan_implant            = '$param[penggunaan_implant]',
            jns_implant                   = '$param[jns_implant]',
            jns_implant_lain              = '$param[jns_implant_lain]',
            ab_post_op                    = '$param[ab_post_op]',
            nama_ab_op                    = '$param[nama_ab_op]',
            dosis_ab_op                   = '$param[dosis_ab_op]',
            selama_ab_op                  = '$param[selama_ab_op]',
            alasan_pos_op                 = '$param[alasan_pos_op]',
            alasan_lain_post_op           = '$param[alasan_lain_post_op]',
            hasil_lab_leukosit            = '$param[hasil_lab_leukosit]',
            hasil_lab_crcp                = '$param[hasil_lab_crcp]',
            hasil_lab_pct                 = '$param[hasil_lab_pct]',
            hasil_lab_gds                 = '$param[hasil_lab_gds]',
            isi_lab_leukosit              = '$param[isi_lab_leukosit]',
            isi_lab_crcp                  = '$param[isi_lab_crcp]',
            isi_lab_pct                   = '$param[isi_lab_pct]',
            isi_lab_gds                   = '$param[isi_lab_gds]',
            tgl_hasil_lab_op              = '$param[tgl_hasil_lab_op]',
            tgl_isi_form                  = '$param[tgl_isi_form]',
            tgl_selesai_isi               = '$param[tgl_selesai_isi]',
            nama_petugas                  = '$param[nama_petugas]',
            diubah_oleh                   = '$param[user]'
        WHERE id = '$param[id]'
          AND nomr = '$param[nomr]'";
    } else {
      $squery =
        "INSERT INTO db_ppi.tb_survido (
                nomr,
                nokun,
                tglsurveilan,
                tb,
                bb,
                tindakan,
                tgl_rencana,
                dokteroperator,
                suhu,
                lab_tanggal,
                glukosa,
                hb,
                leukosit,
                albumin,
                tgl_mandi1,
                tgl_mandi2,
                mandi_menggunakan,
                mandi_menggunakan_lain,
                alat_cukur,
                lokasi_mandi,
                tgl_cukur,
                asascore,
                petugas_asa_score,
                klasifikasi_lukaop,
                urgensi_op,
                ruang_operasi,
                nama_operator,
                nama_asisten,
                asisten_dokter,
                perawat_instrumen,
                perawat_sirkuler,
                dokter_anastesi,
                perawat_anastesi,
                ab_dibutuhkan,
                ab_alasanlain,
                ab_profilaksis,
                nama_ab_prof,
                dosis_ab_prof,
                waktu_ab_prof,
                ulang_ab_prof,
                waktu_pemberian_ulang_ab_prof,
                antiseptik,
                teknik_preparasi,
                nama_hh_operator,
                nama_hh_perawat,
                nama_hh_sirkuler,
                nama_hh_asisten,
                hh_perawat,
                hh_sirkuler,
                hh_operator,
                hh_asisten,
                jml_petugas_op,
                jml_selain_petugas_op,
                jml_pintu_dibuka,
                terpasang_drain,
                lokasi_drain,
                tipe_drain,
                penggunaan_alat_reuse,
                nama_alat_reuse1,
                nama_alat_reuse2,
                nama_alat_reuse3,
                alat_reuse1,
                alat_reuse2,
                alat_reuse3,
                penggunaan_implant,
                jns_implant,
                jns_implant_lain,
                ab_post_op,
                nama_ab_op,
                dosis_ab_op,
                selama_ab_op,
                alasan_pos_op,
                alasan_lain_post_op,
                hasil_lab_leukosit,
                hasil_lab_crcp,
                hasil_lab_pct,
                hasil_lab_gds,
                isi_lab_leukosit,
                isi_lab_crcp,
                isi_lab_pct,
                isi_lab_gds,
                tgl_hasil_lab_op,
                tgl_isi_form,
                tgl_selesai_isi,
                nama_petugas,
                user
            )
        VALUES (
                '$param[nomr]',
                '$param[nokun]',
                '$param[tglsurveilan]',
                '$param[tb]',
                '$param[bb]',
                '$param[tindakan]',
                '$param[tgl_rencana]',
                '$param[dokteroperator]',
                '$param[suhu]',
                '$param[lab_tanggal]',
                '$param[glukosa]',
                '$param[hb]',
                '$param[leukosit]',
                '$param[albumin]',
                '$param[tgl_mandi1]',
                '$param[tgl_mandi2]',
                '$param[mandi_menggunakan]',
                '$param[mandi_menggunakan_lain]',
                '$param[alat_cukur]',
                '$param[lokasi_mandi]',
                '$param[tgl_cukur]',
                '$param[asascore]',
                '$param[petugas_asa_score]',
                '$param[klasifikasi_lukaop]',
                '$param[urgensi_op]',
                '$param[ruang_operasi]',
                '$param[nama_operator]',
                '$param[nama_asisten]',
                '$param[asisten_dokter]',
                '$param[perawat_instrumen]',
                '$param[perawat_sirkuler]',
                '$param[dokter_anastesi]',
                '$param[perawat_anastesi]',
                '$param[ab_dibutuhkan]',
                '$param[ab_alasanlain]',
                '$param[ab_profilaksis]',
                '$param[nama_ab_prof]',
                '$param[dosis_ab_prof]',
                '$param[waktu_ab_prof]',
                '$param[ulang_ab_prof]',
                '$param[waktu_pemberian_ulang_ab_prof]',
                '$param[antiseptik]',
                '$param[teknik_preparasi]',
                '$param[nama_hh_operator]',
                '$param[nama_hh_perawat]',
                '$param[nama_hh_sirkuler]',
                '$param[nama_hh_asisten]',
                '$param[hh_perawat]',
                '$param[hh_sirkuler]',
                '$param[hh_operator]',
                '$param[hh_asisten]',
                '$param[jml_petugas_op]',
                '$param[jml_selain_petugas_op]',
                '$param[jml_pintu_dibuka]',
                '$param[terpasang_drain]',
                '$param[lokasi_drain]',
                '$param[tipe_drain]',
                '$param[penggunaan_alat_reuse]',
                '$param[nama_alat_reuse1]',
                '$param[nama_alat_reuse2]',
                '$param[nama_alat_reuse3]',
                '$param[alat_reuse1]',
                '$param[alat_reuse2]',
                '$param[alat_reuse3]',
                '$param[penggunaan_implant]',
                '$param[jns_implant]',
                '$param[jns_implant_lain]',
                '$param[ab_post_op]',
                '$param[nama_ab_op]',
                '$param[dosis_ab_op]',
                '$param[selama_ab_op]',
                '$param[alasan_pos_op]',
                '$param[alasan_lain_post_op]',
                '$param[hasil_lab_leukosit]',
                '$param[hasil_lab_crcp]',
                '$param[hasil_lab_pct]',
                '$param[hasil_lab_gds]',
                '$param[isi_lab_leukosit]',
                '$param[isi_lab_crcp]',
                '$param[isi_lab_pct]',
                '$param[isi_lab_gds]',
                '$param[tgl_hasil_lab_op]',
                '$param[tgl_isi_form]',
                '$param[tgl_selesai_isi]',
                '$param[nama_petugas]',
                '$param[user]'
            )";
    }
    // echo $squery;exit;
    $query = $this->db->query($squery);
    if ($query) {
      return true;
    } else {
      return false;
    }
  }

  public function simpanpostido($param = array())
  {

    if ($param['id'] > 0) {
      $squery =
        "UPDATE db_ppi.tb_postido
        SET nomr        = '$param[nomr]',
            nokun       = '$param[nokun]',
            tglmasuk    = '$param[tglmasuk]',
            tglkeluar   = '$param[tglkeluar]',
            postke      = '$param[postke]',
            tglsurvey   = '$param[tglsurvey]',
            event       = '$param[event]',
            antibiotik  = '$param[antibiotik]',
            gejala      = '$param[gejala]',
            petugas     = '$param[petugas]',
            diubah_oleh = '$param[user]'
        WHERE id = '$param[id]'
            AND nomr = '$param[nomr]'";
    } else {
      $squery =
        "INSERT INTO db_ppi.tb_postido (
                nomr,
                nokun,
                tglmasuk,
                tglkeluar,
                postke,
                tglsurvey,
                event,
                antibiotik,
                gejala,
                petugas,
                user
            )
        VALUES (
                '$param[nomr]',
                '$param[nokun]',
                '$param[tglmasuk]',
                '$param[tglkeluar]',
                '$param[postke]',
                '$param[tglsurvey]',
                '$param[event]',
                '$param[antibiotik]',
                '$param[gejala]',
                '$param[petugas]',
                '$param[user]'
            )";
    }
    $query = $this->db->query($squery);
    if ($query) {
      return true;
    } else {
      return false;
    }
  }

  public function histopreido($nokun = null)
  {
    if ($nokun) {
      $squery =
        "SELECT ps.NAMA,
            ps.TANGGAL_LAHIR,
            id.*
        FROM db_ppi.tb_survido id
            LEFT JOIN master.pasien ps ON ps.NORM = id.nomr
            LEFT JOIN pendaftaran.pendaftaran pp ON pp.NORM = ps.NORM
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
        WHERE id.STATUS = 1
            AND pk.NOMOR = '$nokun'
        ORDER BY id.tgl_input DESC";

      $query = $this->db->query($squery);
      return $query->result_array();
    }
  }

  public function histopreidoshow($id = null)
  {
    if ($id) {
      $squery =
        "SELECT ruan.DESKRIPSI RUANGAN,
            ruan.ID ID_RUANGAN,
            ps.NORM,
            ps.NAMA,
            IF(ps.JENIS_KELAMIN = 1, '52', '53') KODE_JENIS_KELAMIN,
            IF(ps.JENIS_KELAMIN = 1, 'Laki-Laki', 'Perempuan') JENIS_KELAMIN,
            ps.JENIS_KELAMIN ID_JK,
            DATE_FORMAT(ps.TANGGAL_LAHIR, '%d-%m-%Y') TANGGAL_LAHIR,
            p.TANGGAL TGL_MASUK,
            (YEAR(CURDATE()) - YEAR(ps.TANGGAL_LAHIR)) UMUR,
            concat(
                (YEAR(CURDATE()) - YEAR(ps.TANGGAL_LAHIR)),
                ' Th'
            ) UMUR_T,
            ref.DESKRIPSI PENJAMIN,
            ref.ID ID_PENJAMIN,
            IF(
                ref.ID = 2,
                '56',
                IF(ref.ID = 62, '57', IF(1, '54', '55'))
            ) PENANGGUNG,
            IF(
                ref.ID = 2,
                'BPJS',
                IF(
                    ref.ID = 62,
                    'Karyawan RSKD',
                    IF(1, 'Umum/Tanpa Asuransi', 'Jaminan Perusahaan')
                )
            ) NAMA_PENANGGUNG,
            neg.DESKRIPSI KEWARGANEGARAAN,
            pek.DESKRIPSI PEKERJAAN,
            kp.NOMOR TELPON,
            ps.ALAMAT,
            kip.NOMOR KTP,
            p.NOMOR NOPEN,
            dia.ICD,
            dia.DIAGNOSA,
            CONCAT(dia.ICD, '-', dia.DIAGNOSA) DIAGMASUK,
            ido.*
        FROM pendaftaran.pendaftaran p
            LEFT JOIN pendaftaran.kunjungan pk ON p.NOMOR = pk.NOPEN
            LEFT JOIN master.ruangan ruan ON ruan.ID = pk.RUANGAN
            LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
            LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
            LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS
            AND ref.JENIS = 10
            left JOIN master.negara neg ON neg.ID = ps.KEWARGANEGARAAN
            LEFT JOIN master.referensi pek ON pek.ID = ps.PEKERJAAN
            and pek.JENIS = 4
            LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
            LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
            LEFT JOIN master.diagnosa_masuk dia ON dia.ID = p.DIAGNOSA_MASUK
            left JOIN db_ppi.tb_survido ido ON ido.nomr = ps.NORM
        WHERE ido.id = '$id'
            AND p.STATUS != 0 #AND pk.STATUS=1
            AND ruan.JENIS_KUNJUNGAN in (2, 3)
        ORDER BY pk.MASUK DESC
        LIMIT 1";
      $query = $this->db->query($squery);
      return $query->row_array();
    }
  }

  public function histopostido($nokun = null)
  {
    if ($nokun) {
      $squery =
        "SELECT ps.NAMA,
            ps.TANGGAL_LAHIR,
            id.*
        FROM db_ppi.tb_postido id
            LEFT JOIN master.pasien ps ON ps.NORM = id.nomr
            LEFT JOIN pendaftaran.pendaftaran pp ON pp.NORM = ps.NORM
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
        WHERE id.STATUS = 1
            AND pk.NOMOR = '$nokun'
        ORDER BY id.tgl_input DESC";

      // echo $squery;exit;
      $query = $this->db->query($squery);
      return $query->result_array();
    }
  }

  public function histopostidoshow($id = null)
  {
    if ($id) {
      $squery =
        "SELECT ruan.DESKRIPSI RUANGAN,
            ruan.ID ID_RUANGAN,
            ps.NORM,
            ps.NAMA,
            IF(ps.JENIS_KELAMIN = 1, '52', '53') KODE_JENIS_KELAMIN,
            IF(ps.JENIS_KELAMIN = 1, 'Laki-Laki', 'Perempuan') JENIS_KELAMIN,
            ps.JENIS_KELAMIN ID_JK,
            DATE_FORMAT(ps.TANGGAL_LAHIR, '%d-%m-%Y') TANGGAL_LAHIR,
            p.TANGGAL TGL_MASUK,
            (YEAR(CURDATE()) - YEAR(ps.TANGGAL_LAHIR)) UMUR,
            concat((YEAR(CURDATE()) - YEAR(ps.TANGGAL_LAHIR)), ' Th') UMUR_T,
            ref.DESKRIPSI PENJAMIN,
            ref.ID ID_PENJAMIN,
            IF(ref.ID = 2, '56', IF(ref.ID = 62, '57', IF(1, '54', '55'))) PENANGGUNG,
            IF(
                ref.ID = 2,
                'BPJS',
                IF(
                    ref.ID = 62,
                    'Karyawan RSKD',
                    IF(1, 'Umum/Tanpa Asuransi', 'Jaminan Perusahaan')
                )
            ) NAMA_PENANGGUNG,
            neg.DESKRIPSI KEWARGANEGARAAN,
            pek.DESKRIPSI PEKERJAAN,
            kp.NOMOR TELPON,
            ps.ALAMAT,
            kip.NOMOR KTP,
            p.NOMOR NOPEN,
            dia.ICD,
            dia.DIAGNOSA,
            CONCAT(dia.ICD, '-', dia.DIAGNOSA) DIAGMASUK,
            ido.*,
            kk.NOMOR TELPKEL,
            kel.NAMA NAMAKEL
        FROM pendaftaran.pendaftaran p
            LEFT JOIN pendaftaran.kunjungan pk ON p.NOMOR = pk.NOPEN
            LEFT JOIN master.ruangan ruan ON ruan.ID = pk.RUANGAN
            LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
            LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
            LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS
            AND ref.JENIS = 10
            left JOIN master.negara neg ON neg.ID = ps.KEWARGANEGARAAN
            LEFT JOIN master.referensi pek ON pek.ID = ps.PEKERJAAN
            and pek.JENIS = 4
            LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
            LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
            LEFT JOIN master.diagnosa_masuk dia ON dia.ID = p.DIAGNOSA_MASUK
            left JOIN db_ppi.tb_postido ido ON ido.nomr = ps.NORM
            LEFT JOIN master.kontak_keluarga_pasien kk ON kk.NORM = p.NORM
            LEFT JOIN master.keluarga_pasien kel ON kel.NORM = p.NORM
        WHERE ido.id = '$id'
            AND p.STATUS != 0 #AND pk.STATUS=1
            AND ruan.JENIS_KUNJUNGAN in (2, 3)
        ORDER BY pk.MASUK DESC
        LIMIT 1";

      $query = $this->db->query($squery);
      return $query->row_array();
    }
  }

}