# Fix: Commands Out of Sync Error

## Ma<PERSON><PERSON>r "Commands out of sync; you can't run this command now" terjadi ketika menggunakan stored procedure dan function database secara bersamaan dalam satu koneksi MySQL.

## Penyebab
1. **Stored Procedure tidak dibersihkan**: Method `getKontakPasien()` menggunakan `CALL` stored procedure yang meninggalkan result set aktif
2. **Multiple result sets**: Stored procedure dapat menghasilkan multiple result sets yang tidak dibersihkan
3. **Koneksi database kotor**: Setelah stored procedure, koneksi masih dalam state yang tidak bersih

## Solusi yang Diterapkan

### 1. Perbaikan Method `getKontakPasien()`
```php
public function getKontakPasien($norm)
{
    $query = $this->db->query("CALL db_rekammedis.getKontakPasien(?)", array($norm));
    $result = $query->row();
    
    // Bersihkan hasil stored procedure untuk mencegah "Commands out of sync"
    if ($query) {
        $query->free_result();
        // Clear any additional result sets from stored procedure
        while ($this->db->conn_id->more_results()) {
            $this->db->conn_id->next_result();
            if ($res = $this->db->conn_id->store_result()) {
                $res->free();
            }
        }
    }
    
    return $result ? $result->NOMOR : null;
}
```

### 2. Perbaikan Method `generateNoKontrol()`
```php
public function generateNoKontrol($tanggal)
{
    // Gunakan query langsung dengan parameter binding
    $query = $this->db->query("SELECT generator.generateNoKontrol(?) AS KODE", array($tanggal));
    $result = $query->row();
    
    // Pastikan result di-free untuk membersihkan koneksi
    if ($query) {
        $query->free_result();
    }
    
    return $result ? $result->KODE : null;
}
```

### 3. Method Alternatif dengan Koneksi Terpisah
Jika masalah masih terjadi, gunakan method alternatif:

```php
// Untuk generate nomor kontrol
$noKontrol = $this->PengkajianDafOpeModel->generateNoKontrolSafe($post['tanggal_operasi']);

// Untuk mendapatkan kontak pasien
$nomorKontak = $this->PengkajianDafOpeModel->getKontakPasienSafe($post['norm']);
```

## Cara Penggunaan

### Di Controller
```php
// Ambil data dengan method yang sudah diperbaiki
$namaPasien = $this->PengkajianDafOpeModel->getNamaLengkapPasien($post['norm']);
$nomorKontak = $this->PengkajianDafOpeModel->getKontakPasien($post['norm']);
$noKontrol = $this->PengkajianDafOpeModel->generateNoKontrol($post['tanggal_operasi']);
```

## Testing
Untuk memastikan fix berfungsi:

1. **Test normal flow**: Jalankan proses pendaftaran operasi dengan ruang operasi ID=16
2. **Test error handling**: Pastikan tidak ada error "Commands out of sync"
3. **Test data integrity**: Pastikan semua data tersimpan dengan benar

## Monitoring
Monitor log aplikasi untuk memastikan tidak ada error:
```bash
tail -f application/logs/log-*.php
```

## Fallback Plan
Jika masalah masih terjadi, gunakan method alternatif dengan koneksi terpisah:

```php
// Ganti di controller
$nomorKontak = $this->PengkajianDafOpeModel->getKontakPasienSafe($post['norm']);
$noKontrol = $this->PengkajianDafOpeModel->generateNoKontrolSafe($post['tanggal_operasi']);
```

## Best Practices
1. **Selalu free result**: Setelah menggunakan query result
2. **Handle stored procedure**: Bersihkan multiple result sets
3. **Use parameter binding**: Untuk keamanan dan stabilitas
4. **Error logging**: Log semua error untuk debugging
5. **Separate connections**: Untuk operasi yang kompleks

## Referensi
- [MySQL Commands Out of Sync](https://dev.mysql.com/doc/refman/8.0/en/commands-out-of-sync.html)
- [CodeIgniter Database Reference](https://codeigniter.com/userguide3/database/index.html)
- [PHP MySQLi Multi Query](https://www.php.net/manual/en/mysqli.multi-query.php)
