<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Status_Anestesia extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {     
        $nomr = $this->uri->segment(4);
        $nopen = $this->uri->segment(5);
        $nokun = $this->uri->segment(6);
        $id_sa = $this->uri->segment(7);
        $history_sa = $this->pengkajianAwalModel->history_sa($nomr);
        $get_sa = $this->pengkajianAwalModel->get_sa($id_sa);
        $sa_riwayatpengobatan = $this->masterModel->referensi(529);
        $sa_alergi = $this->masterModel->referensi(530);
        $sa_riwayat_apk = $this->masterModel->referensi(531);
        $sa_riwayat_merokok_alkohol = $this->masterModel->referensi(532);
        $sa_wajah = $this->masterModel->referensi(585);
        $sa_thorax = $this->masterModel->referensi(586);
        $sa_kemampuan = $this->masterModel->referensi(587);
        $sa_jarak = $this->masterModel->referensi(588);
        $sa_mallampati = $this->masterModel->referensi(589);
        $sa_obstruksi = $this->masterModel->referensi(590);
        $sa_mobilitas_menggerakan = $this->masterModel->referensi(592);
        $sa_mobilitas_melakukan = $this->masterModel->referensi(593);
        $sa_mobilitas_pasien = $this->masterModel->referensi(594);
        $sa_sistem_respirasi = $this->masterModel->referensi(595);
        $sa_sistem_kardiovaskular = $this->masterModel->referensi(596);
        $sa_sistem_renal_endokrin = $this->masterModel->referensi(597);
        $sa_hepato = $this->masterModel->referensi(598);
        $sa_neuro = $this->masterModel->referensi(599);
        $sa_lain_lain = $this->masterModel->referensi(600);
        $sa_teknik_anestesi = $this->masterModel->referensi(745);
        $sa_monitoring = $this->masterModel->referensi(746);
        $sa_kontrol_nyeri = $this->masterModel->referensi(747);
        $sa_pemasangan_iv_line = $this->masterModel->referensi(748);
        $sa_kesiapan_mesin = $this->masterModel->referensi(749);
        $sa_sumber_gas = $this->masterModel->referensi(750);
        $sa_penyakit_yang_diderita = $this->masterModel->referensi(751);
        $sa_gigi_palsu = $this->masterModel->referensi(752);
        $sa_kontak_lens = $this->masterModel->referensi(753);
        $sa_penggunaan_obat = $this->masterModel->referensi(288);
        $sa_kesadaran = $this->masterModel->referensi(727);
        $sa_pernapasan = $this->masterModel->referensi(728);
        $sa_nyeri = $this->masterModel->referensi(729);
        $sa_risiko_jatuh = $this->masterModel->referensi(739);
        $sa_ruangan = $this->masterModel->referensi(736);
        $sa_nyeri_keluar = $this->masterModel->referensi(738);
        $sa_risiko_jatuh_keluar = $this->masterModel->referensi(739);
        $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
        $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
        $listDrAnestesi = $this->masterModel->listDrAnestesi();
        $listDr = $this->masterModel->listDrUmum();
        $skalaNyeriNRS = $this->masterModel->referensi(114);
        $skalaNyeriWBR = $this->masterModel->referensi(115);
        $skalaNyeriFLACC = $this->masterModel->referensi(123);
        $skalaNyeriBPS = $this->masterModel->referensi(133);
        $efeksampingNRS = $this->masterModel->referensi(118);
        $efeksampingWBR = $this->masterModel->referensi(119);
        $efeksampingFLACC = $this->masterModel->referensi(131);
        $efeksampingBPS = $this->masterModel->referensi(134);
        $statusnyeriNRS = $this->masterModel->referensi(136);
        $statusnyeriWBR = $this->masterModel->referensi(136);
        $statusnyeriFLACC = $this->masterModel->referensi(136);
        $statusnyeriBPS = $this->masterModel->referensi(136);
        $riwayatAlergi = $this->masterModel->referensi(2);
        $golonganDarah = $this->masterModel->referensi(756);
        $rhesus = $this->masterModel->referensi(882);
        $ruangan = 1;

    $data = array(
            'ruangan' => $ruangan,
            'nokun' => $nokun,
            'sa_riwayatpengobatan' => $sa_riwayatpengobatan,
            'golonganDarah' => $golonganDarah,
            'rhesus' => $rhesus,
            'sa_alergi' => $sa_alergi,
            'listPerawat' => $this->masterModel->listPegawai(),
            'sa_riwayat_apk' => $sa_riwayat_apk,
            'sa_riwayat_merokok_alkohol' => $sa_riwayat_merokok_alkohol,
            'sa_wajah' => $sa_wajah,
            'sa_thorax' => $sa_thorax,
            'sa_kemampuan' => $sa_kemampuan,
            'sa_jarak' => $sa_jarak,
            'sa_mallampati' => $sa_mallampati,
            'sa_obstruksi' => $sa_obstruksi,
            'sa_mobilitas_menggerakan' => $sa_mobilitas_menggerakan,
            'sa_mobilitas_melakukan' => $sa_mobilitas_melakukan,
            'sa_mobilitas_pasien' => $sa_mobilitas_pasien,
            'sa_sistem_respirasi' => $sa_sistem_respirasi,
            'sa_sistem_kardiovaskular' => $sa_sistem_kardiovaskular,
            'sa_sistem_renal_endokrin' => $sa_sistem_renal_endokrin,
            'sa_hepato' => $sa_hepato,
            'sa_neuro' => $sa_neuro,
            'sa_lain_lain' => $sa_lain_lain,
            'sa_teknik_anestesi' => $sa_teknik_anestesi,
            'sa_monitoring' => $sa_monitoring,
            'sa_kontrol_nyeri' => $sa_kontrol_nyeri,
            'sa_pemasangan_iv_line' => $sa_pemasangan_iv_line,
            'sa_kesiapan_mesin' => $sa_kesiapan_mesin,
            'sa_sumber_gas' => $sa_sumber_gas,
            'sa_penyakit_yang_diderita' => $sa_penyakit_yang_diderita,
            'sa_gigi_palsu' => $sa_gigi_palsu,
            'sa_kontak_lens' => $sa_kontak_lens,
            'sa_penggunaan_obat' => $sa_penggunaan_obat,
            'sa_kesadaran' => $sa_kesadaran,
            'sa_pernapasan' => $sa_pernapasan,
            'sa_nyeri' => $sa_nyeri,
            'sa_risiko_jatuh' => $sa_risiko_jatuh,
            'sa_ruangan' => $sa_ruangan,
            'sa_nyeri_keluar' => $sa_nyeri_keluar,
            'sa_risiko_jatuh_keluar' => $sa_risiko_jatuh_keluar,
            'ruanganRawatJalan' => $ruanganRawatJalan,
            'ruanganRawatInap' => $ruanganRawatInap,
            'history_sa' => $history_sa,
            'get_sa' => $get_sa,
            'listDrAnestesi' => $listDrAnestesi,
            'listDr' => $listDr,
            'riwayatAlergi' => $riwayatAlergi,
            'skalaNyeriNRS' => $skalaNyeriNRS,
            'skalaNyeriWBR' => $skalaNyeriWBR,
            'skalaNyeriFLACC' => $skalaNyeriFLACC,
            'skalaNyeriBPS' => $skalaNyeriBPS,
            'efeksampingNRS' => $efeksampingNRS,
            'efeksampingWBR' => $efeksampingWBR,
            'efeksampingFLACC' => $efeksampingFLACC,
            'efeksampingBPS' => $efeksampingBPS,
            'statusnyeriNRS' => $statusnyeriNRS,
            'statusnyeriWBR' => $statusnyeriWBR,
            'statusnyeriFLACC' => $statusnyeriFLACC,
            'statusnyeriBPS' => $statusnyeriBPS,
          );
        $this->load->view('Pengkajian/anestesia/status_anestesia/index', $data);

  }

  public function viewInputPascaAnestesia()
  {
    $id_sa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $get_sa = $this->pengkajianAwalModel->get_sa($id_sa);

    $data = array(
      'idStatusAnestesia' => $id_sa, 
      'norm' => $norm, 
      'nopen' => $nopen, 
      'nokun' => $nokun, 
      'get_sa' => $get_sa, 
      'sa_pernapasan' => $this->masterModel->referensi(728), 
      'listDrAnestesi' => $this->masterModel->listDrAnestesi(),
      'listPerawat' => $this->masterModel->listPerawatPenataAnestesi(), 
      'sa_nyeri' => $this->masterModel->referensi(729), 
      'sa_kesadaran' => $this->masterModel->referensi(727), 
      'sa_risiko_jatuh' => $this->masterModel->referensi(739), 
      'skalaNyeriNRS' => $this->masterModel->referensi(114), 
      'sa_ruangan' => $this->masterModel->referensi(736), 
      'sa_nyeri_keluar' => $this->masterModel->referensi(738), 
      'sa_risiko_jatuh_keluar' => $this->masterModel->referensi(739), 
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/pascaAnestesia/pascaView', $data);
  }

  public function viewTandaVitalPascaAnestesia()
  {
    $id_sa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $get_sa = $this->pengkajianAwalModel->get_sa($id_sa);

    $HistoryTandaVitalPasca = $this->pengkajianAwalModel->HistoryTandaVitalPascaAnestesia($id_sa);
    $HistoryInputTandaVitalPascaAnestesia = $this->pengkajianAwalModel->HistoryInputTandaVitalPascaAnestesia($id_sa);

    $waktu = array();
    $napas = array();
    $nadi = array();
    $sistolik = array();
    $diastolik = array();
    $map = array();
    foreach ($HistoryTandaVitalPasca as $HistoryTandaVitalPasca){
      array_push($waktu,$HistoryTandaVitalPasca['pukul']);
      array_push($napas,$HistoryTandaVitalPasca['pernapasan']);
      array_push($nadi,$HistoryTandaVitalPasca['nadi']);
      array_push($sistolik,$HistoryTandaVitalPasca['td_sistolik']);
      array_push($diastolik,$HistoryTandaVitalPasca['td_diastolik']);
      array_push($map,$HistoryTandaVitalPasca['map']);
    }

    $data = array(
      'idStatusAnestesia' => $id_sa, 
      'norm' => $norm, 
      'nopen' => $nopen, 
      'nokun' => $nokun, 
      'get_sa' => $get_sa, 
      'waktu'                             => $waktu,
      'napas'                             => $napas,
      'nadi'                              => $nadi,
      'sistolik'                          => $sistolik,
      'diastolik'                         => $diastolik,
      'map'                               => $map,
      'HistoryInputTandaVitalPascaAnestesia' => $HistoryInputTandaVitalPascaAnestesia, 
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/pascaAnestesia/tandaVital', $data);
  }

  public function viewInputIntraAnestesia()
  {
    $idsa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');

    $data = array(
      'idStatusAnestesia' => $idsa, 
      'norm' => $norm, 
      'nopen' => $nopen, 
      'nokun' => $nokun, 
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/intraView', $data);
  }

  public function viewTVitalAnestesia()
  {
    $idsa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');

    $HistoryTandaVital = $this->pengkajianAwalModel->HistoryTandaVitalAnestesia($idsa);
    $HistoryInputTandaVitalAnestesia = $this->pengkajianAwalModel->HistoryInputTandaVitalAnestesia($idsa);

    $waktu = array();
    $napas = array();
    $nadi = array();
    $sistolik = array();
    $diastolik = array();
    $map = array();
    foreach ($HistoryTandaVital as $HistoryTandaVital){
      array_push($waktu,$HistoryTandaVital['pukul']);
      array_push($napas,$HistoryTandaVital['pernapasan']);
      array_push($nadi,$HistoryTandaVital['nadi']);
      array_push($sistolik,$HistoryTandaVital['td_sistolik']);
      array_push($diastolik,$HistoryTandaVital['td_diastolik']);
      array_push($map,$HistoryTandaVital['map']);
    }

    $data = array(
      'idStatusAnestesia'                 => $idsa, 
      'norm'                              => $norm, 
      'nopen'                             => $nopen, 
      'nokun'                             => $nokun, 
      'waktu'                             => $waktu,
      'napas'                             => $napas,
      'nadi'                              => $nadi,
      'sistolik'                          => $sistolik,
      'diastolik'                         => $diastolik,
      'map'                               => $map,
      'HistoryInputTandaVitalAnestesia'   => $HistoryInputTandaVitalAnestesia,
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/tandaVital', $data);
  }

  public function viewPemantauanAnestesia()
  {
    $idsa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');

    $hisPemantauanAnestesia = $this->pengkajianAwalModel->hisPemantauanAnestesia($idsa);

    $data = array(
      'idStatusAnestesia'                 => $idsa, 
      'norm'                              => $norm, 
      'nopen'                             => $nopen, 
      'nokun'                             => $nokun, 
      'hisPemantauanAnestesia'            => $hisPemantauanAnestesia,
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/pemantauanAnestesia', $data);
  }

  public function viewObatInfusAnestesia()
  {
    $idsa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $jenis = $this->input->post('jenis');

    $nmObat = $this->pengkajianAwalModel->namaObatInfusAnestesia();
    $tableObatInfusAnestesia = $this->pengkajianAwalModel->tableObatInfusAnestesia($idsa, $jenis);
    $labelGrafikObatInfusAnestesia = $this->pengkajianAwalModel->labelGrafikObatInfusAnestesia($idsa);
    $labelGrafikObatInfusAnestesiaDetail = $this->pengkajianAwalModel->labelGrafikObatInfusAnestesiaDetail($idsa);
    $hisPemberianObatInfusAnestesia = $this->pengkajianAwalModel->hisPemberianObatInfusAnestesia($idsa);
    $ruanganRawatInap = $this->masterModel->listRawatInapPrtklKemo();
    $jalurPemberian = $this->masterModel->referensi(901);

    $data = array(
      'idStatusAnestesia'                 => $idsa, 
      'norm'                              => $norm, 
      'nopen'                             => $nopen, 
      'nokun'                             => $nokun, 
      'nmObat'                            => $nmObat,
      'jenis'                             => $jenis,
      'ruanganRawatInap'                  => $ruanganRawatInap,
      'jalurPemberian'                    => $jalurPemberian,
      'tableObatInfusAnestesia'           => $tableObatInfusAnestesia,
      'labelGrafikObatInfusAnestesia'           => $labelGrafikObatInfusAnestesia,
      'labelGrafikObatInfusAnestesiaDetail'           => $labelGrafikObatInfusAnestesiaDetail,
      'hisPemberian'                      => $hisPemberianObatInfusAnestesia,
    );

    if($jenis == 1){
      $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/obatInfus', $data);
    }elseif($jenis == 2){
      $this->load->view('Pengkajian/anestesia/status_anestesia/pascaAnestesia/obatInfus', $data);
    }
  }

  public function viewTambahObatInfus()
  {
    $idobt = $this->input->post('idobt');
    $norm = $this->input->post('norm');
    $nokun = $this->input->post('nokun');
    $idsa = $this->input->post('idsa');
    $jenis = $this->input->post('jenis');

    $getObt = $this->pengkajianAwalModel->tableObatInfusAnestesiaId($idobt);
    $pilihPerawat = $this->pengkajianAwalModel->pilihPerawatKardek();

    $data = array(
      'idobt'                             => $idobt, 
      'norm'                              => $norm, 
      'nokun'                             => $nokun, 
      'idsa'                              => $idsa, 
      'jenis'                             => $jenis, 
      'getObt'                            => $getObt, 
      'pilihPerawat'                      => $pilihPerawat, 
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/tambahObatInfus', $data);
  }

  public function viewStopObatInfus()
  {
    $idobt = $this->input->post('idobt');
    $norm = $this->input->post('norm');
    $nokun = $this->input->post('nokun');
    $idsa = $this->input->post('idsa');
    $jenis = $this->input->post('jenis');

    $getObt = $this->pengkajianAwalModel->tableObatInfusAnestesiaId($idobt);
    $listDpjp = $this->masterModel->listDrUmum();

    $data = array(
      'idobt'                             => $idobt, 
      'norm'                              => $norm, 
      'nokun'                             => $nokun, 
      'idsa'                              => $idsa, 
      'jenis'                             => $jenis, 
      'getObt'                            => $getObt, 
      'listDpjp'                          => $listDpjp, 
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/stopObatInfus', $data);
  }

  public function viewFinalInputAnestesia()
  {
    $idsa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');

    $n2o = $this->masterModel->referensi(1710);
    $gas = $this->masterModel->referensi(1711);
    $finalInputAnestesiaRi = $this->pengkajianAwalModel->finalInputAnestesiaRi($idsa);

    $data = array(
      'idStatusAnestesia'                 => $idsa, 
      'norm'                              => $norm, 
      'nopen'                             => $nopen, 
      'nokun'                             => $nokun, 
      'getFinalInput'                     => $finalInputAnestesiaRi, 
      'n2o'                               => $n2o, 
      'gas'                               => $gas, 
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/finalInput', $data);
  }

  public function viewFormIntraAnestesia()
  {
    $idsa = $this->input->post('idsa');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');

    $formIntraAnestesiaRi = $this->pengkajianAwalModel->formIntraAnestesiaRi($idsa);
    $posisi = $this->masterModel->referensi(1712);
    $posisiLateral = $this->masterModel->referensi(1713);
    $premedikasi = $this->masterModel->referensi(1714);
    $induksi = $this->masterModel->referensi(1715);
    $intubasi = $this->masterModel->referensi(1716);
    $intubasiOral = $this->masterModel->referensi(1717);
    $ventilasi = $this->masterModel->referensi(1718);
    $kateter = $this->masterModel->referensi(1719);
    $hasil = $this->masterModel->referensi(1720);
    $tindakanAnestesi = $this->masterModel->referensi(1721);

    $data = array(
      'idStatusAnestesia'                 => $idsa, 
      'norm'                              => $norm, 
      'nopen'                             => $nopen, 
      'nokun'                             => $nokun, 
      'getFormIntra'                      => $formIntraAnestesiaRi, 
      'posisi'                            => $posisi, 
      'posisiLateral'                     => $posisiLateral, 
      'premedikasi'                       => $premedikasi, 
      'induksi'                           => $induksi, 
      'intubasi'                          => $intubasi, 
      'intubasiOral'                      => $intubasiOral, 
      'ventilasi'                         => $ventilasi, 
      'kateter'                           => $kateter, 
      'hasil'                             => $hasil, 
      'tindakanAnestesi'                  => $tindakanAnestesi, 
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/intraAnestesia/formIntra', $data);
  }

  public function simpanFormIntra()
  {
    $id_sa = $this->input->post('id_sa');
    $idFinal = $this->input->post('idFinal');
    $oleh = $this->input->post('pengisi');
    $nokun = $this->input->post('nokun');
    $deskInfusPeriferIntraAnestesia = $this->input->post('deskInfusPeriferIntraAnestesia');
    $cvcInfusPeriferIntraAnestesia = $this->input->post('cvcInfusPeriferIntraAnestesia');
    $posisiFormIntraAnestesia = $this->input->post('posisiFormIntraAnestesia');
    $posisiLateralFormIntraAnestesia = $this->input->post('posisiLateralFormIntraAnestesia');
    $posisiLainFormIntraAnestesia = $this->input->post('posisiLainFormIntraAnestesia');
    $premedikasiFormIntraAnestesia = $this->input->post('premedikasiFormIntraAnestesia');
    $penjelasanPremedikasiFormIntraAnestesia = $this->input->post('penjelasanPremedikasiFormIntraAnestesia');
    $induksiFormIntraAnestesia = $this->input->post('induksiFormIntraAnestesia');
    $penjelasanInduksiFormIntraAnestesia = $this->input->post('penjelasanInduksiFormIntraAnestesia');
    $faskFormIntraAnestesia = $this->input->post('faskFormIntraAnestesia');
    $oroFormIntraAnestesia = $this->input->post('oroFormIntraAnestesia');
    $ettFormIntraAnestesia = $this->input->post('ettFormIntraAnestesia');
    $jenisEttFormIntraAnestesia = $this->input->post('jenisEttFormIntraAnestesia');
    $fiksasiFormIntraAnestesia = $this->input->post('fiksasiFormIntraAnestesia');
    $lmaFormIntraAnestesia = $this->input->post('lmaFormIntraAnestesia');
    $jenisLmaFormIntraAnestesia = $this->input->post('jenisLmaFormIntraAnestesia');
    $trakhesotomiFormIntraAnestesia = $this->input->post('trakhesotomiFormIntraAnestesia');
    $bronFibFormIntraAnestesia = $this->input->post('bronFibFormIntraAnestesia');
    $glidscopeFormIntraAnestesia = $this->input->post('glidscopeFormIntraAnestesia');
    $lainTataFormIntraAnestesia = $this->input->post('lainTataFormIntraAnestesia');
    $intubasiFormIntraAnestesia = $this->input->post('intubasiFormIntraAnestesia');
    $intubasiOralFormIntraAnestesia = $this->input->post('intubasiOralFormIntraAnestesia');
    $sulitVentilasiFormIntraAnestesia = $this->input->post('sulitVentilasiFormIntraAnestesia');
    $sulitIntubasiFormIntraAnestesia = $this->input->post('sulitIntubasiFormIntraAnestesia');
    $ventilasiFormIntraAnestesia = $this->input->post('ventilasiFormIntraAnestesia');
    $deskTvFormIntraAnestesia = $this->input->post('deskTvFormIntraAnestesia');
    $rrFormIntraAnestesia = $this->input->post('rrFormIntraAnestesia');
    $peepFormIntraAnestesia = $this->input->post('peepFormIntraAnestesia');
    $konversiFormIntraAnestesia = $this->input->post('konversiFormIntraAnestesia');
    $lainnyaFormIntraAnestesia = $this->input->post('lainnyaFormIntraAnestesia');
    $tindknAnestesiaFormIntraAnestesia = $this->input->post('tindknAnestesiaFormIntraAnestesia');
    $deskTindknAnestesiaFormIntraAnestesia = $this->input->post('deskTindknAnestesiaFormIntraAnestesia');
    $teknikJenisFormIntraAnestesia = $this->input->post('teknikJenisFormIntraAnestesia');
    $teknikLokasiFormIntraAnestesia = $this->input->post('teknikLokasiFormIntraAnestesia');
    $teknikJarumFormIntraAnestesia = $this->input->post('teknikJarumFormIntraAnestesia');
    $kateterFormIntraAnestesia = $this->input->post('kateterFormIntraAnestesia');
    $fiksasiKateterFormIntraAnestesia = $this->input->post('fiksasiKateterFormIntraAnestesia');
    $obatObatFormIntraAnestesia = $this->input->post('obatObatFormIntraAnestesia');
    $komplikasiFormIntraAnestesia = $this->input->post('komplikasiFormIntraAnestesia');
    $hasilFormIntraAnestesia = $this->input->post('hasilFormIntraAnestesia');
    $hasilDeskFormIntraAnestesia = $this->input->post('hasilDeskFormIntraAnestesia');

    $data = array(
      'id_sa'                             => $id_sa, 
      'nokun'                             => $nokun, 
      'desk_infus'                        => $deskInfusPeriferIntraAnestesia, 
      'cvc'                               => $cvcInfusPeriferIntraAnestesia, 
      'pilih_posisi'                      => json_encode($posisiFormIntraAnestesia),
      'lateral'                           => $posisiLateralFormIntraAnestesia, 
      'desk_posisi_lain'                  => $posisiLainFormIntraAnestesia, 
      'pilih_premedikasi'                 => json_encode($premedikasiFormIntraAnestesia), 
      'desk_premedikasi'                  => $penjelasanPremedikasiFormIntraAnestesia, 
      'pilih_induksi'                     => json_encode($induksiFormIntraAnestesia),
      'desk_induksi'                      => $penjelasanInduksiFormIntraAnestesia,
      'face_mask'                         => $faskFormIntraAnestesia,
      'oro_nasopharing'                   => $oroFormIntraAnestesia,
      'ett_no'                            => $ettFormIntraAnestesia,
      'jenis_ett'                         => $jenisEttFormIntraAnestesia,
      'fiksasi_tata_laksana'              => $fiksasiFormIntraAnestesia,
      'lma_no'                            => $lmaFormIntraAnestesia,
      'jenis_lma'                         => $jenisLmaFormIntraAnestesia,
      'trakhesotomi'                      => $trakhesotomiFormIntraAnestesia,
      'bronkoskopi_fiberoptik'            => $bronFibFormIntraAnestesia,
      'glidescope'                        => $glidscopeFormIntraAnestesia,
      'lain_tata_laksana'                 => $lainTataFormIntraAnestesia,
      'pilih_intubasi'                    => json_encode($intubasiFormIntraAnestesia),
      'pilih_oral'                        => $intubasiOralFormIntraAnestesia,
      'sulit_ventilasi'                   => $sulitVentilasiFormIntraAnestesia,
      'sulit_intubasi'                    => $sulitIntubasiFormIntraAnestesia,
      'pilih_ventilasi'                   => $ventilasiFormIntraAnestesia,
      'ventilator_tv'                     => $deskTvFormIntraAnestesia,
      'ventilator_rr'                     => $rrFormIntraAnestesia,
      'ventilator_peep'                   => $peepFormIntraAnestesia,
      'konversi_tindakan'                 => $konversiFormIntraAnestesia,
      'lainnya_tindakan'                  => $lainnyaFormIntraAnestesia,
      'pilih_tindakan_anestesi'           => $tindknAnestesiaFormIntraAnestesia,
      'pilih_tindakan_anestesi_lainnya'   => $deskTindknAnestesiaFormIntraAnestesia,
      'desk_jenis_teknik'                 => $teknikJenisFormIntraAnestesia,
      'lokasi_teknik'                     => $teknikLokasiFormIntraAnestesia,
      'jarum_teknik'                      => $teknikJarumFormIntraAnestesia,
      'pilih_kateter'                     => $kateterFormIntraAnestesia,
      'desk_kateter'                      => $fiksasiKateterFormIntraAnestesia,
      'desk_obat_obat'                    => $obatObatFormIntraAnestesia,
      'desk_komplikasi'                   => $komplikasiFormIntraAnestesia,
      'pilih_hasil_teknik'                => $hasilFormIntraAnestesia,
      'desk_hasil_teknik'                 => $hasilDeskFormIntraAnestesia,
      'oleh'                              => $oleh,
    );

    // echo "<pre>";print_r($data);echo "</pre>";exit();

    if($idFinal != ""){
      $this->db->where('tb_status_anestesia_form_intra.id', $idFinal);
      $this->db->update('medis.tb_status_anestesia_form_intra', $data);
    }elseif($idFinal == ""){
      $this->db->insert('medis.tb_status_anestesia_form_intra', $data);
    }
  }

  public function simpanFinalInput()
  {
    $id_sa = $this->input->post('id_sa');
    $idFinal = $this->input->post('idFinal');
    $dateMulai = $this->input->post('mulaiFinalInputAnestesia');
    // $dateSelesai = $this->input->post('selesaiFinalInputAnestesia');
    $lamaPembiusan = $this->input->post('lamaBiusFinalInputAnestesia');
    $lamaPembedahan = $this->input->post('lamaPembedahanFinalInputAnestesia');
    // $dateSelesaiBius = $this->input->post('selesaiBiusFinalInputAnestesia');
    $nokun = $this->input->post('nokun');
    $pengisi = $this->input->post('pengisi');
    $n2oFinalInputAnestesia = $this->input->post('n2oFinalInputAnestesia');
    $gasFinalInputAnestesia = $this->input->post('gasFinalInputAnestesia');
    $catatanFinalInputAnestesia = $this->input->post('catatanFinalInputAnestesia');

    $tglMulai = date('Y-m-d H:i:s', strtotime($dateMulai));
    // $tglSelesai = date('Y-m-d H:i:s', strtotime($dateSelesai));
    // $tglSelesaiBius = date('Y-m-d H:i:s', strtotime($dateSelesaiBius));

    $data = array(
      'id_sa'                             => $id_sa, 
      'nokun'                             => $nokun, 
      'n2o_new_array'                     => isset($n2oFinalInputAnestesia) ? json_encode($n2oFinalInputAnestesia) : null,
      'gas'                               => $gasFinalInputAnestesia, 
      'mulai_anestesia'                   => $tglMulai, 
      // 'selesai_anestesia'                 => $tglSelesai, 
      'lama_pembiusan'                    => $lamaPembiusan, 
      'lama_pembedahan'                   => $lamaPembedahan, 
      // 'selesai_pembiusan'                 => $tglSelesaiBius, 
      'catatan'                           => $catatanFinalInputAnestesia, 
      'oleh'                              => $pengisi 
    );

    // echo "<pre>";print_r($data);echo "</pre>";exit();

    // if($idFinal != ""){
    //   $this->db->where('tb_status_anestesia_final_input.id', $idFinal);
    //   $this->db->update('medis.tb_status_anestesia_final_input', $data);
    // }elseif($idFinal == ""){
      $this->db->insert('medis.tb_status_anestesia_final_input', $data);
    // }
  }

  public function hapusFinalInput()
  {
    $id = $this->input->post('id');
    $data = array(
      'status'    => 0
    );
    $this->db->where('tb_status_anestesia_final_input.id', $id);
    $this->db->update('medis.tb_status_anestesia_final_input', $data);
  }

  public function stopObatInfus()
  {
    $idobt = $this->input->post('idobt');
    $date = $this->input->post('tglStopAnestesiaRi');
    $dpjp = $this->input->post('stopPilihDokterAnestesiaRi');
    $pengisi = $this->input->post('pengisi');

    $tglStopPemberian = date('Y-m-d H:i:s', strtotime($date));

    $data = array(
      'id_obat_anestesia'                 => $idobt, 
      'tanggal_jam_berhenti'              => $tglStopPemberian, 
      'dpjp'                              => $dpjp, 
      'oleh'                              => $pengisi 
    );

    $this->db->insert('medis.tb_obat_stop_anestesia', $data);

    $dataStop = array(
      'status' => 2, 
    );

    $this->db->where('tb_obat_anestesia.id', $idobt);
    $this->db->update('medis.tb_obat_anestesia', $dataStop);
  }

  public function simpanTambahPemberianObatInfus()
  {
    $idobt = $this->input->post('idobt');
    $date = $this->input->post('tglPemberianAnestesiaRi');
    $jumlahBerikanAnestesiaRi = $this->input->post('jumlahBerikanAnestesiaRi');
    $pilihPerawatAnestesiaRi = $this->input->post('pilihPerawatAnestesiaRi');
    $sisaBerikan = $this->input->post('sisaBerikan');
    $dosisAnestesiaRi = $this->input->post('dosisAnestesiaRi');
    $oleh = $this->session->userdata("id");

    $tglPemberian = date('Y-m-d H:i:s', strtotime($date));

    $data = array(
      'id_obat_anestesia' => $idobt,  
      'tanggal_jam' => $tglPemberian,  
      'jumlah_pemberian' => $jumlahBerikanAnestesiaRi,    
      'dosis_pemberian' => $dosisAnestesiaRi,    
      'perawat_2' => $pilihPerawatAnestesiaRi,   
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->insert('medis.tb_obat_pemberian_anestesia', $data);

    $dataPemberian = array(
      'jumlah_obat' => $sisaBerikan,    
      'oleh' => $oleh, 
      'status' => 1, 
    );

    $this->db->where('tb_obat_jumlah_anestesia.id_obat_anestesia', $idobt);
    $this->db->update('medis.tb_obat_jumlah_anestesia', $dataPemberian);

  }

  public function simpanObatInfus()
  {
    $nokun = $this->input->post('nokun');
    $idsa = $this->input->post('idsa');
    $oleh = $this->input->post('pengisi');
    $jenis = $this->input->post('jenis');
    $pilihNamaObatInfusAnestesiaRi = $this->input->post('pilihNamaObatInfusAnestesiaRi');
    // $ruanganRiObatInfusAnestesiaRi = $this->input->post('ruanganRiObatInfusAnestesiaRi');
    $dosisObatInfusAnestesiaRi = $this->input->post('dosisObatInfusAnestesiaRi');
    $jamObatInfusAnestesiaRi = $this->input->post('jamObatInfusAnestesiaRi');
    $jalurPemObatInfusAnestesiaRi = $this->input->post('jalurPemObatInfusAnestesiaRi');
    $jumlahObatInfusAnestesiaRi = $this->input->post('jumlahObatInfusAnestesiaRi');
    $ketObatInfusAnestesiaRi = $this->input->post('ketObatInfusAnestesiaRi');

    $data = array(
      'id_sa' => $idsa, 
      'nokun' => $nokun, 
      // 'ruangan' => $ruanganRiObatInfusAnestesiaRi, 
      'nama_obat' => $pilihNamaObatInfusAnestesiaRi, 
      'dosis' => $dosisObatInfusAnestesiaRi, 
      'jam' => $jamObatInfusAnestesiaRi, 
      'jalur_pemberian' => $jalurPemObatInfusAnestesiaRi, 
      'jumlah' => $jumlahObatInfusAnestesiaRi, 
      'keterangan' => $ketObatInfusAnestesiaRi, 
      'jenis' => $jenis, 
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $getId = $this->pengkajianAwalModel->simpanObatInfusAnestesia($data);

    $dataAnestesiaJumlah = array(
      'id_obat_anestesia' => $getId, 
      'jumlah_obat' => $jumlahObatInfusAnestesiaRi, 
      'jalur_pemberian' => $jalurPemObatInfusAnestesiaRi, 
      'oleh' => $oleh, 
      'status' => 1, 
    );
    $this->db->insert('medis.tb_obat_jumlah_anestesia', $dataAnestesiaJumlah);
  }

  public function simpanPemantauan()
  {
    $idsa = $this->input->post('id_sa');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');
    $oleh = $this->input->post('pengisi');
    $pukulPemantauanAnestesiaRi = $this->input->post('pukulPemantauanAnestesiaRi');
    $spO2AnestesiaRi = $this->input->post('spO2AnestesiaRi');
    $peCo2AnestesiaRi = $this->input->post('peCo2AnestesiaRi');
    $fiO2AnestesiaRi = $this->input->post('fiO2AnestesiaRi');
    $cairanInfusAnestesiaRi = $this->input->post('cairanInfusAnestesiaRi');
    $darahAnestesiaRi = $this->input->post('darahAnestesiaRi');
    $urinAnestesiaRi = $this->input->post('urinAnestesiaRi');
    $perdarahanAnestesiaRi = $this->input->post('perdarahanAnestesiaRi');

    $data = array(
      'id_sa' => $idsa, 
      'nokun' => $nokun, 
      'spo2' => $spO2AnestesiaRi, 
      'peco2' => $peCo2AnestesiaRi, 
      'fio2' => $fiO2AnestesiaRi, 
      'cairan_infus' => $cairanInfusAnestesiaRi, 
      'darah' => $darahAnestesiaRi, 
      'urin' => $urinAnestesiaRi, 
      'perdarahan' => $perdarahanAnestesiaRi, 
      'waktu' => $pukulPemantauanAnestesiaRi, 
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->insert('medis.tb_status_anestesia_pemantauan', $data);
  }

  public function hapusPemantauan()
  {
    $id = $this->input->post('id');
    $this->db->where('tb_status_anestesia_pemantauan.id', $id);
    $this->db->update('medis.tb_status_anestesia_pemantauan', array('status' => 0));
  }

  public function simpanTandaVital()
  {
    $idsa = $this->input->post('id_sa');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');
    $pukulTandaVitalAnestesiaRi = $this->input->post('pukulTandaVitalAnestesiaRi');
    $sistolikAnestesiaRi = $this->input->post('sistolikAnestesiaRi');
    $diastolikAnestesiaRi = $this->input->post('diastolikAnestesiaRi');
    $mapAnestesiaRi = $this->input->post('mapAnestesiaRi');
    $nadiAnestesiaRi = $this->input->post('nadiAnestesiaRi');
    $pernapasanAnestesiaRi = $this->input->post('pernapasanAnestesiaRi');
    $oleh = $this->input->post('pengisi');

    $data = array(
      'data_source' => 36, 
      'ref' => $idsa, 
      'nomr' => $norm, 
      'nokun' => $nokun, 
      'pukul' => $pukulTandaVitalAnestesiaRi, 
      'td_sistolik' => $sistolikAnestesiaRi, 
      'td_diastolik' => $diastolikAnestesiaRi, 
      'nadi' => $nadiAnestesiaRi, 
      'pernapasan' => $pernapasanAnestesiaRi, 
      'map' => $mapAnestesiaRi, 
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->insert('db_pasien.tb_tanda_vital', $data);
  }

  public function simpanTandaVitalPasca()
  {
    $idsa = $this->input->post('id_sa');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');
    $pukulTandaVitalAnestesiaRi = $this->input->post('pukulTandaVitalPascaAnestesiaRi');
    $sistolikAnestesiaRi = $this->input->post('sistolikPascaAnestesiaRi');
    $diastolikAnestesiaRi = $this->input->post('diastolikPascaAnestesiaRi');
    $mapAnestesiaRi = $this->input->post('mapPascaAnestesiaRi');
    $nadiAnestesiaRi = $this->input->post('nadiPascaAnestesiaRi');
    $pernapasanAnestesiaRi = $this->input->post('pernapasanPascaAnestesiaRi');
    $oleh = $this->input->post('pengisi');

    $data = array(
      'data_source' => 38, 
      'ref' => $idsa, 
      'nomr' => $norm, 
      'nokun' => $nokun, 
      'pukul' => $pukulTandaVitalAnestesiaRi, 
      'td_sistolik' => $sistolikAnestesiaRi, 
      'td_diastolik' => $diastolikAnestesiaRi, 
      'nadi' => $nadiAnestesiaRi, 
      'pernapasan' => $pernapasanAnestesiaRi, 
      'map' => $mapAnestesiaRi, 
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->insert('db_pasien.tb_tanda_vital', $data);
  }

  public function nonAktifTandaVital()
  {
    $idsa = $this->input->post('id');
    $this->db->where('tb_tanda_vital.id', $idsa);
    $this->db->update('db_pasien.tb_tanda_vital', array('status' => 0));
  }

  public function nonAktifTandaVitalPasca()
  {
    $idsa = $this->input->post('id');
    $this->db->where('tb_tanda_vital.id', $idsa);
    $this->db->update('db_pasien.tb_tanda_vital', array('status' => 0));
  }

  public function action_sa($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $get_id_sa = !empty($post['id_sa']) ? $post['id_sa'] : $this->pengkajianAwalModel->getIdEmr();
        $jenisForm = $this->input->post('jenisForm');

        if($jenisForm == 1){
          $data_sa = array(
          'id_sa'  => $get_id_sa,
          'nokun'           => isset($post['nokun']) ? $post['nokun'] : null,
          //
          'sa_jenis_ruangan'   => isset($post['sa_jenis_ruangan']) ? $post['sa_jenis_ruangan'] : null,
          'sa_ruangan'   => isset($post['sa_ruangan']) ? $post['sa_ruangan'] : null,
          'sa_tanggal' => isset($post['sa_tanggal']) ? $post['sa_tanggal'] : null,
          'sa_waktu' => isset($post['sa_waktu']) ? $post['sa_waktu'] : null,
          'sa_dokter_anestesi' => isset($post['sa_dokter_anestesi']) ? $post['sa_dokter_anestesi'] : null,
          'sa_penata_anestesi' => isset($post['sa_penata_anestesi']) ? $post['sa_penata_anestesi'] : null,
          'sa_dokter_operator' => isset($post['sa_dokter_operator']) ? $post['sa_dokter_operator'] : null,
          'sa_diagnosis_pra_bedah' => isset($post['sa_diagnosis_pra_bedah']) ? $post['sa_diagnosis_pra_bedah'] : null,
          'sa_jenis_pembedahan' => isset($post['sa_jenis_pembedahan']) ? $post['sa_jenis_pembedahan'] : null,
          'sa_diagnosis_pasca_bedah' => isset($post['sa_diagnosis_pasca_bedah']) ? $post['sa_diagnosis_pasca_bedah'] : null,
          //
          'sa_tinggi_badan' => isset($post['sa_tinggi_badan']) ? $post['sa_tinggi_badan'] : null,
          'sa_berat_badan' => isset($post['sa_berat_badan']) ? $post['sa_berat_badan'] : null,
          'sa_golongan_darah' => isset($post['sa_golongan_darah']) ? $post['sa_golongan_darah'] : null,
          'sa_rhesus' => isset($post['sa_rhesus']) ? $post['sa_rhesus'] : null,
          'sa_tekanan_darah_1' => isset($post['sa_tekanan_darah_1']) ? $post['sa_tekanan_darah_1'] : null,
          'sa_tekanan_darah_2' => isset($post['sa_tekanan_darah_2']) ? $post['sa_tekanan_darah_2'] : null,
          'sa_pernapasan_awal' => isset($post['sa_pernapasan_awal']) ? $post['sa_pernapasan_awal'] : null,
          'sa_nadi' => isset($post['sa_nadi']) ? $post['sa_nadi'] : null,
          'sa_suhu' => isset($post['sa_suhu']) ? $post['sa_suhu'] : null,
          'sa_hemoglobin' => isset($post['sa_hemoglobin']) ? $post['sa_hemoglobin'] : null,
          'sa_hematokrit' => isset($post['sa_hematokrit']) ? $post['sa_hematokrit'] : null,
          //
          'sa_riwayatpengobatan' => isset($post['sa_riwayatpengobatan']) ? $post['sa_riwayatpengobatan'] : null,
          'sa_riwayatpengobatan_desk' => isset($post['saRiwayatPengobatanDesk']) ? $post['saRiwayatPengobatanDesk'] : null,
          'sa_alergi' => isset($post['sa_alergi']) ? $post['sa_alergi'] : null,
          'sa_alergi_desk' => isset($post['saAlergiDesk']) ? $post['saAlergiDesk'] : null,
          'sa_riwayat_apk' => isset($post['sa_riwayat_apk']) ? $post['sa_riwayat_apk'] : null,
          'sa_riwayat_apk_desk' => isset($post['saKomplikasiAnestesiDesk']) ? $post['saKomplikasiAnestesiDesk'] : null,
          'sa_riwayat_merokok_alkohol' => isset($post['sa_riwayat_merokok_alkohol']) ? $post['sa_riwayat_merokok_alkohol'] : null,
          //
          'sa_wajah' => isset($post['sa_wajah']) ? json_encode($post['sa_wajah']) : null,
          'sa_thorax' => isset($post['sa_thorax']) ? json_encode($post['sa_thorax']) : null,
          'sa_kemampuan' => isset($post['sa_kemampuan']) ? $post['sa_kemampuan'] : null,
          'sa_jarak' => isset($post['sa_jarak']) ? $post['sa_jarak'] : null,
          'sa_mallampati' => isset($post['sa_mallampati']) ? $post['sa_mallampati'] : null,
          'sa_obstruksi' => isset($post['sa_obstruksi']) ? json_encode($post['sa_obstruksi']) : null,
          'sa_mobilitas_menggerakan' => isset($post['sa_mobilitas_menggerakan']) ? $post['sa_mobilitas_menggerakan'] : null,
          'sa_mobilitas_melakukan' => isset($post['sa_mobilitas_melakukan']) ? $post['sa_mobilitas_melakukan'] : null,
          'sa_mobilitas_pasien' => isset($post['sa_mobilitas_pasien']) ? $post['sa_mobilitas_pasien'] : null,
          //
          'sa_sistem_respirasi' => isset($post['sa_sistem_respirasi']) ? json_encode($post['sa_sistem_respirasi']) : null,
          'sa_sistem_respirasi_lainnya' => isset($post['sa_sistem_respirasi_lainnya']) ? $post['sa_sistem_respirasi_lainnya'] : null,
          'sa_sistem_kardiovaskular' => isset($post['sa_sistem_kardiovaskular']) ? json_encode($post['sa_sistem_kardiovaskular']) : null,
          'sa_sistem_renal_endokrin' => isset($post['sa_sistem_renal_endokrin']) ? json_encode($post['sa_sistem_renal_endokrin']) : null,
          'sa_hepato' => isset($post['sa_hepato']) ? json_encode($post['sa_hepato']) : null,
          'sa_neuro' => isset($post['sa_neuro']) ? json_encode($post['sa_neuro']) : null,
          'sa_lain_lain' => isset($post['sa_lain_lain']) ? json_encode($post['sa_lain_lain']) : null,
          //
          'sa_ekg' => isset($post['sa_ekg']) ? $post['sa_ekg'] : null,
          'sa_pulmonarystudies' => isset($post['sa_pulmonarystudies']) ? $post['sa_pulmonarystudies'] : null,
          'sa_radiologi' => isset($post['sa_radiologi']) ? $post['sa_radiologi'] : null,
          'sa_diagnostiklainlain' => isset($post['sa_diagnostiklainlain']) ? $post['sa_diagnostiklainlain'] : null,
          'sa_hbht' => isset($post['sa_hbht']) ? $post['sa_hbht'] : null,
          'sa_urinalisis' => isset($post['sa_urinalisis']) ? $post['sa_urinalisis'] : null,
          'sa_elektrolit' => isset($post['sa_elektrolit']) ? $post['sa_elektrolit'] : null,
          'sa_lablainlain' => isset($post['sa_lablainlain']) ? $post['sa_lablainlain'] : null,
          //
          'sa_daftar_masalah_diagnosis' => isset($post['sa_daftar_masalah_diagnosis']) ? $post['sa_daftar_masalah_diagnosis'] : null,
          'sa_teknik_anestesi' => isset($post['sa_teknik_anestesi']) ? json_encode($post['sa_teknik_anestesi']) : null,
          'sa_teknik_anestesi_lainnya' => isset($post['sa_teknik_anestesi_lainnya']) ? $post['sa_teknik_anestesi_lainnya'] : null,
          'sa_monitoring' => isset($post['sa_monitoring']) ? json_encode($post['sa_monitoring']) : null,
          'sa_kontrol_nyeri' => isset($post['sa_kontrol_nyeri']) ? json_encode($post['sa_kontrol_nyeri']) : null,
          'sa_kontrol_nyeri_lainnya' => isset($post['sa_kontrol_nyeri_lainnya']) ? $post['sa_kontrol_nyeri_lainnya'] : null,
          'sa_kesadaran_pra_induksi' => isset($post['sa_kesadaran_pra_induksi']) ? $post['sa_kesadaran_pra_induksi'] : null,
          'sa_asa_pra_induksi' => isset($post['sa_asa_pra_induksi']) ? $post['sa_asa_pra_induksi'] : null,
          'sa_saturasi_pra_induksi' => isset($post['sa_saturasi_pra_induksi']) ? $post['sa_saturasi_pra_induksi'] : null,
          'sa_tekanan_darah_1_pra_induksi' => isset($post['sa_tekanan_darah_1_pra_induksi']) ? $post['sa_tekanan_darah_1_pra_induksi'] : null,
          'sa_tekanan_darah_2_pra_induksi' => isset($post['sa_tekanan_darah_2_pra_induksi']) ? $post['sa_tekanan_darah_2_pra_induksi'] : null,
          'sa_pernapasan_pra_induksi' => isset($post['sa_pernapasan_pra_induksi']) ? $post['sa_pernapasan_pra_induksi'] : null,
          'sa_nadi_pra_induksi' => isset($post['sa_nadi_pra_induksi']) ? $post['sa_nadi_pra_induksi'] : null,
          'sa_suhu_pra_induksi' => isset($post['sa_suhu_pra_induksi']) ? $post['sa_suhu_pra_induksi'] : null,
          'sa_saturasi_pra_induksi' => isset($post['sa_saturasi_pra_induksi']) ? $post['sa_saturasi_pra_induksi'] : null,
          'sa_gambaran_ekg_pra_induksi' => isset($post['sa_gambaran_ekg_pra_induksi']) ? $post['sa_gambaran_ekg_pra_induksi'] : null,
          'sa_pemasangan_iv_line' => isset($post['sa_pemasangan_iv_line']) ? $post['sa_pemasangan_iv_line'] : null,
          'sa_pemasangan_iv_line_lainnya' => isset($post['sa_pemasangan_iv_line_lainnya']) ? $post['sa_pemasangan_iv_line_lainnya'] : null,
          'sa_kesiapan_mesin' => isset($post['sa_kesiapan_mesin']) ? $post['sa_kesiapan_mesin'] : null,
          'sa_kesiapan_mesin_lainnya' => isset($post['sa_kesiapan_mesin_lainnya']) ? $post['sa_kesiapan_mesin_lainnya'] : null,
          'sa_sumber_gas' => isset($post['sa_sumber_gas']) ? $post['sa_sumber_gas'] : null,
          'sa_sumber_gas_lainnya' => isset($post['sa_sumber_gas_lainnya']) ? $post['sa_sumber_gas_lainnya'] : null,
          'sa_penyakit_yang_diderita' => isset($post['sa_penyakit_yang_diderita']) ? $post['sa_penyakit_yang_diderita'] : null,
          'sa_penyakit_yang_diderita_lainnya' => isset($post['sa_penyakit_yang_diderita_lainnya']) ? $post['sa_penyakit_yang_diderita_lainnya'] : null,
          'sa_alergi_pra_induksi' => isset($post['sa_alergi_pra_induksi']) ? $post['sa_alergi_pra_induksi'] : null,
          'sa_alergi_pra_induksi_lainnya' => isset($post['sa_alergi_pra_induksi_lainnya']) ? $post['sa_alergi_pra_induksi_lainnya'] : null,
          'sa_penggunaan_obat' => isset($post['sa_penggunaan_obat']) ? $post['sa_penggunaan_obat'] : null,
          'sa_penggunaan_obat_lainnya' => isset($post['sa_penggunaan_obat_lainnya']) ? $post['sa_penggunaan_obat_lainnya'] : null,
          'sa_gigi_palsu' => isset($post['sa_gigi_palsu']) ? $post['sa_gigi_palsu'] : null,
          'sa_kontak_lens' => isset($post['sa_kontak_lens']) ? $post['sa_kontak_lens'] : null,
          //
          // 'created_at' => date('Y-m-d H:i:s'),
          // 'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );
        }elseif ($jenisForm == 2) {
          $data_sa = array(
            'id_sa'  => $get_id_sa,
            'nokun'           => isset($post['nokun']) ? $post['nokun'] : null,
            'sa_pukul_masuk_kamar_pemulihan' => isset($post['sa_pukul_masuk_kamar_pemulihan']) ? $post['sa_pukul_masuk_kamar_pemulihan'] : null,
          'sa_penata_anestesi_pengirim' => isset($post['sa_penata_anestesi_pengirim']) ? $post['sa_penata_anestesi_pengirim'] : null,
          'sa_penata_anestesi_penerima' => isset($post['sa_penata_anestesi_penerima']) ? $post['sa_penata_anestesi_penerima'] : null,
          'sa_tekanan_darah_1_kamar_pemulihan' => isset($post['sa_tekanan_darah_1_kamar_pemulihan']) ? $post['sa_tekanan_darah_1_kamar_pemulihan'] : null,
          'sa_tekanan_darah_2_kamar_pemulihan' => isset($post['sa_tekanan_darah_2_kamar_pemulihan']) ? $post['sa_tekanan_darah_2_kamar_pemulihan'] : null,
          'sa_pernapasan_kamar_pemulihan' => isset($post['sa_pernapasan_kamar_pemulihan']) ? $post['sa_pernapasan_kamar_pemulihan'] : null,
          'sa_nadi_kamar_pemulihan' => isset($post['sa_nadi_kamar_pemulihan']) ? $post['sa_nadi_kamar_pemulihan'] : null,
          'sa_suhu_kamar_pemulihan' => isset($post['sa_suhu_kamar_pemulihan']) ? $post['sa_suhu_kamar_pemulihan'] : null,
          'sa_kesadaran' => isset($post['sa_kesadaran']) ? $post['sa_kesadaran'] : null,
          'sa_pernapasan' => isset($post['sa_pernapasan']) ? $post['sa_pernapasan'] : null,
          'sa_pernapasan_lainnya' => isset($post['sa_pernapasan_lainnya']) ? $post['sa_pernapasan_lainnya'] : null,
          'sa_nyeri' => isset($post['sa_nyeri']) ? $post['sa_nyeri'] : null,
          'sa_nyeri_lainnya' => isset($post['sa_nyeri_lainnya']) ? $post['sa_nyeri_lainnya'] : null,
          'sa_risiko_jatuh' => isset($post['sa_risiko_jatuh']) ? $post['sa_risiko_jatuh'] : null,
          'sa_penyulit_intra_operatif' => isset($post['sa_penyulit_intra_operatif']) ? $post['sa_penyulit_intra_operatif'] : null,
          'sa_instruksi_khusus' => isset($post['sa_instruksi_khusus']) ? $post['sa_instruksi_khusus'] : null,
          //
          'sa_skala_nyeri' => isset($post['sa_skala_nyeri']) ? $post['sa_skala_nyeri'] : null,
          'sa_saturasi_o2_aldrete' => isset($post['sa_saturasi_o2_aldrete']) ? $post['sa_saturasi_o2_aldrete'] : null,
          'sa_pernapasan_aldrete' => isset($post['sa_pernapasan_aldrete']) ? $post['sa_pernapasan_aldrete'] : null,
          'sa_sirkulasi_aldrete' => isset($post['sa_sirkulasi_aldrete']) ? $post['sa_sirkulasi_aldrete'] : null,
          'sa_aktifitas_motorik_aldrete' => isset($post['sa_aktifitas_motorik_aldrete']) ? $post['sa_aktifitas_motorik_aldrete'] : null,
          'sa_kesadaran_aldrete' => isset($post['sa_kesadaran_aldrete']) ? $post['sa_kesadaran_aldrete'] : null,
          'sa_pergerakan_steward' => isset($post['sa_pergerakan_steward']) ? $post['sa_pergerakan_steward'] : null,
          'sa_pernapasan_steward' => isset($post['sa_pernapasan_steward']) ? $post['sa_pernapasan_steward'] : null,
          'sa_kesadaran_steward' => isset($post['sa_kesadaran_steward']) ? $post['sa_kesadaran_steward'] : null,
          'sa_gpdt_bromage' => isset($post['sa_gpdt_bromage']) ? $post['sa_gpdt_bromage'] : null,
          'sa_tmet_bromage' => isset($post['sa_tmet_bromage']) ? $post['sa_tmet_bromage'] : null,
          'sa_tmfl_bromage' => isset($post['sa_tmfl_bromage']) ? $post['sa_tmfl_bromage'] : null,
          'sa_tmfpk_bromage' => isset($post['sa_tmfpk_bromage']) ? $post['sa_tmfpk_bromage'] : null,
          //
          'sa_jam_1' => isset($post['sa_jam_1']) ? $post['sa_jam_1'] : null,
          'sa_penerima_1' => isset($post['sa_penerima_1']) ? $post['sa_penerima_1'] : null,
          'sa_jam_2' => isset($post['sa_jam_2']) ? $post['sa_jam_2'] : null,
          'sa_penerima_2' => isset($post['sa_penerima_2']) ? $post['sa_penerima_2'] : null,
          'sa_jam_3' => isset($post['sa_jam_3']) ? $post['sa_jam_3'] : null,
          'sa_penerima_3' => isset($post['sa_penerima_3']) ? $post['sa_penerima_3'] : null,
          //
          'sa_pukul_keluar' => isset($post['sa_pukul_keluar']) ? $post['sa_pukul_keluar'] : null,
          'sa_ke_ruangan' => isset($post['sa_ke_ruangan']) ? $post['sa_ke_ruangan'] : null,
          'sa_ruangan_lainnya' => isset($post['sa_ruangan_lainnya']) ? $post['sa_ruangan_lainnya'] : null,
          'sa_aldrette_score' => isset($post['sa_aldrette_score']) ? $post['sa_aldrette_score'] : null,
          'sa_steward_score' => isset($post['sa_steward_score']) ? $post['sa_steward_score'] : null,
          'sa_bromage_score' => isset($post['sa_bromage_score']) ? $post['sa_bromage_score'] : null,
          'sa_padss_score' => isset($post['sa_padss_score']) ? $post['sa_padss_score'] : null,
          'sa_score_skala_nyeri' => isset($post['sa_score_skala_nyeri']) ? $post['sa_score_skala_nyeri'] : null,
          'sa_nyeri_keluar' => isset($post['sa_nyeri_keluar']) ? $post['sa_nyeri_keluar'] : null,
          'sa_risiko_jatuh_keluar' => isset($post['sa_risiko_jatuh_keluar']) ? $post['sa_risiko_jatuh_keluar'] : null,
          'sa_pengelolaan_nyeri' => isset($post['sa_pengelolaan_nyeri']) ? $post['sa_pengelolaan_nyeri'] : null,
          'sa_penanganan_mual_muntah' => isset($post['sa_penanganan_mual_muntah']) ? $post['sa_penanganan_mual_muntah'] : null,
          'sa_antibiotika' => isset($post['sa_antibiotika']) ? $post['sa_antibiotika'] : null,
          'sa_obat_obatan_lain' => isset($post['sa_obat_obatan_lain']) ? $post['sa_obat_obatan_lain'] : null,
          'sa_infus' => isset($post['sa_infus']) ? $post['sa_infus'] : null,
          'sa_diet_nutrisi' => isset($post['sa_diet_nutrisi']) ? $post['sa_diet_nutrisi'] : null,
          'sa_setiap' => isset($post['sa_setiap']) ? $post['sa_setiap'] : null,
          'sa_selama' => isset($post['sa_selama']) ? $post['sa_selama'] : null,
          'sa_lain_lain_pasca' => isset($post['sa_lain_lain_pasca']) ? $post['sa_lain_lain_pasca'] : null,
          'sa_hasil_pemeriksaan' => isset($post['sa_hasil_pemeriksaan']) ? $post['sa_hasil_pemeriksaan'] : null,
          'oleh_pasca' => isset($post['pengisi']) ? $post['pengisi'] : null,
          'sa_dokter_anestesi_pasca' => isset($post['dokterAnestesiPascaView']) ? $post['dokterAnestesiPascaView'] : null,
          'sa_penata_anestesi_pasca' => isset($post['penataAnestesiPascaView']) ? $post['penataAnestesiPascaView'] : null,
          'ttd_menyatakan'             => file_get_contents($this->input->post('signttdMenyatakanPasca')),
          'ttd_menyatakan_penata'             => file_get_contents($this->input->post('signttdMenyatakanPenataPasca')),
          );
        }

        // echo $post['signttdMenyatakanPasca'];exit();

        if (!empty($post['id_sa'])) {
          $this->db->where('id_sa', $post['id_sa']);
          $this->db->update('medis.tb_status_anestesia', $data_sa);
          // $this->db->replace('medis.tb_status_anestesia', $data_sa);
          $result = array('status' => 'success', 'pesan' => 'ubah');
        }else {
          $this->db->insert('medis.tb_status_anestesia', $data_sa);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }
}


/* End of file Status_Anestesia.php */
/* Location: ./application/controllers/igd/Status_Anestesia.php */
