<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemeriksaanSpirometri extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }


    public function simpanFormPemeriksaanSpirometri()
    {
        $kunjungan = $this->input->post("nokun");
        $tanggalpemeriksaan = $this->input->post("tanggalpemeriksaan");
        $waktupemeriksaan = $this->input->post("waktupemeriksaan");
        $tinggibadan = $this->input->post("tinggibadan");
        $beratbadan = $this->input->post("beratbadan");
        $kvpml = $this->input->post("kvpml");
        $kvppersen = $this->input->post("kvppersen");
        $vepml = $this->input->post("vepml");
        $veppersen = $this->input->post("veppersen");
        $vep1 = $this->input->post("vep1");
        $ape = $this->input->post("ape");
        $kesan = $this->input->post("kesan");
        $restriksi = $this->input->post("restriksi");
        $obstruksi = $this->input->post("obstruksi");

        if ($restriksi != null) {
            $data = array(
                'nokun' => $kunjungan,
                'tanggal_pemeriksaan' => $tanggalpemeriksaan,
                'waktu_pemeriksaan' => $waktupemeriksaan,
                'tinggi_badan' => $tinggibadan,
                'berat_badan' => $beratbadan,
                'kvp_ml' => $kvpml,
                'kvp_persen' => $kvppersen,
                'vep_ml' => $vepml,
                'vep_persen' => $veppersen,
                'vep1' => $vep1,
                'ape' => $ape,
                'kesan' => $kesan,
                'restriksi' => $restriksi,
            );
        } elseif ($obstruksi != null) {
            $data = array(
                'nokun' => $kunjungan,
                'tanggal_pemeriksaan' => $tanggalpemeriksaan,
                'waktu_pemeriksaan' => $waktupemeriksaan,
                'tinggi_badan' => $tinggibadan,
                'berat_badan' => $beratbadan,
                'kvp_ml' => $kvpml,
                'kvp_persen' => $kvppersen,
                'vep_ml' => $vepml,
                'vep_persen' => $veppersen,
                'vep1' => $vep1,
                'ape' => $ape,
                'kesan' => $kesan,
                'obstruksi' => $obstruksi,
            );
        } else {
            $data = array(
                'nokun' => $kunjungan,
                'tanggal_pemeriksaan' => $tanggalpemeriksaan,
                'waktu_pemeriksaan' => $waktupemeriksaan,
                'tinggi_badan' => $tinggibadan,
                'berat_badan' => $beratbadan,
                'kvp_ml' => $kvpml,
                'kvp_persen' => $kvppersen,
                'vep_ml' => $vepml,
                'vep_persen' => $veppersen,
                'vep1' => $vep1,
                'ape' => $ape,
                'kesan' => $kesan,
            );
        }
        $persiapanspirometri = $this->pengkajianAwalModel->insertFormPersiapanSpirometri($data);
    }

    public function lihatHistoryPersiapanSpirometri()
    {
        $id = $this->input->post('id');
        $HistoryPersiapanSpirometri = $this->pengkajianAwalModel->historyDetailPersiapanSpirometri($id);
        $listKesan = $this->masterModel->listKesan();
        $listRestriksi = $this->masterModel->listRestriksi();
        $listObstruksi = $this->masterModel->listObstruksi();

        foreach ($HistoryPersiapanSpirometri as $spirometri):
            echo '      <div class="row">
                            <div class="row form-group">
                                <label for="tinggibadan" class="col-md-3">
                                    Tinggi Badan
                                </label>	
                                <label for="beratbadan" class="col-md-2">
                                    Berat Badan
                                </label>
                                <label for="tanggalpemeriksaan" class="col-md-3">
                                    Tanggal Pemeriksaan
                                </label>
                                <label for="waktupemeriksaan" class="col-md-3">
                                    Waktu Pemeriksaan
                                </label>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="tinggibadan" name="tinggibadan"
                                    placeholder="[ TB ]" value="'.$spirometri['tinggi_badan'].'" autocomplete="off">
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" id="beratbadan" name="beratbadan"
                                    placeholder="[ BB ]" value="'.$spirometri['berat_badan'].'" autocomplete="off">
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" placeholder="[ Tanggal ]" name="tanggalpemeriksaan" value="'.$spirometri['tanggal_pemeriksaan'].'" autocomplete="off">
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" id="waktupemeriksaan" name="waktupemeriksaan"
                                    placeholder="[ 00:00 ]" value="'.$spirometri['waktu_pemeriksaan'].'" autocomplete="off">
                                </div>
                            </div>


                            <label for="kvp" class="col-sm-12 col-md-12 col-form-label">
                                Kapasitas Vital Paksa (KVP)
                            </label>
                            <div class="col-sm-12 col-md-6">
                                <div class="row form-group">  	
                                    <div class="col-sm-12 col-md-3">
                                        <input type="text" class="form-control" id="kvpml" name="kvpml"
                                            placeholder="[ ml/ ]" value="'.$spirometri['kvp_ml'].'" autocomplete="off">
                                    </div>
                                    <div class="col-sm-12 col-md-1">
                                    <label>=</label>
                                    </div>
                                    <div class="col-sm-12 col-md-3">
                                        <input type="text" class="form-control" id="kvppersen" name="kvppersen"
                                            placeholder="[ % ]" value="'.$spirometri['kvp_persen'].'" autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <label for="vep" class="col-sm-12 col-md-12 col-form-label">
                                Volume Ekspirasi Paksa detik Pertama (VEP)
                            </label>
                            <div class="col-sm-12 col-md-6">
                                <div class="row form-group">  	
                                <div class="col-sm-12 col-md-3">
                                        <input type="text" class="form-control" id="vepml" name="vepml"
                                            placeholder="[ ml/ ]" value="'.$spirometri['vep_ml'].'" autocomplete="off">
                                    </div>
                                    <div class="col-sm-12 col-md-1">
                                    <label>=</label>
                                    </div>
                                    <div class="col-sm-12 col-md-3">
                                        <input type="text" class="form-control" id="veppersen" name="veppersen"
                                            placeholder="[ % ]" value="'.$spirometri['vep_persen'].'"  autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <label for="vep1" class="col-sm-12 col-md-12 col-form-label">
                                VEP 1 / KVP
                            </label>
                            <div class="col-sm-12 col-md-9">
                                <div class="row form-group">  	
                                    <div class="col-sm-12 col-md-9">
                                        <input type="text" class="form-control" id="vep1" name="vep1"
                                            placeholder="[ %/ ]" value="'.$spirometri['vep1'].'" autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            <label for="ape" class="col-sm-12 col-md-12 col-form-label">
                                Arus Puncak Ekspirasi (APE)
                            </label>
                            <div class="col-sm-12 col-md-9">
                                <div class="row form-group">  	
                                    <div class="col-sm-12 col-md-9">
                                        <input type="text" class="form-control" id="ape" name="ape"
                                            placeholder="[ L/mnt ]" value="'.$spirometri['ape'].'" autocomplete="off">
                                    </div>
                                </div>
                            </div>

                            
                            <label for="ape" class="col-sm-12 col-md-12 col-form-label">
                                Kesan :
                            </label>                            
                            <div id="kesan" class="col-sm-12 col-md-9">
                                <div class="row">';
                                    foreach ($listKesan as $listKesan):
	        echo '                  <div class="col form-check">
                                        <div class="radio radio-primary form-check-input jarak2 jarak2">
                                            <input type="radio" name="kesan" value="'.$listKesan['id_variabel'].'" class="kesan" id="listkesan_'.$listKesan['id_variabel'].'" ';
                                            if($spirometri['kesan'] == $listKesan['id_variabel']){
                                                echo "checked";
                                            }else{
                                                echo "";
                                            }
            echo '                          >
                                            <label for="listkesan_'.$listKesan['id_variabel'].'">
                                            '.$listKesan['variabel'].'                          
                                            </label>
                                        </div>
                                    </div>';
                                    endforeach;
            echo '                  </br>	
                                    </br>				                           	
                                </div>
                            </div>

                            <div id="restriksi" class="col-sm-12 col-md-9" style="left: 25%;">
                                <div class="row">
                                    <div class="checkbox checkbox-primary jarak2">';
                                        foreach ($listRestriksi as $listRestriksi): 
            echo '                      <div class="radio radio-primary jarak2">
                                            <input type="radio" name="restriksi" class="restriksi" value="'.$listRestriksi['id_variabel'].'" id="listRestriksi_'.$listRestriksi['id_variabel'].'" ';
                                            if($spirometri['restriksi'] == $listRestriksi['id_variabel']){
                                                echo "checked";
                                            }else{
                                                echo "";
                                            }
            echo '                          >
                                            <label for="listRestriksi_'.$listRestriksi['id_variabel'].'">
                                            '.$listRestriksi['variabel'].'                            
                                            </label>
                                        </div>';
                                        endforeach; 
            echo '                  </div>
                                    </br>					                           	
                                </div>
                            </div>

                            <div id="obstruksi" class="col-sm-12 col-md-9" style="left: 50%;">
                                <div class="row">
                                    <div class="checkbox checkbox-primary jarak2">';
                                        foreach ($listObstruksi as $listObstruksi): 
            echo '                      <div class="radio radio-primary jarak2">
                                            <input type="radio" name="obstruksi" class="obstruksi" value="'.$listObstruksi['id_variabel'].'" id="listObstruksi_'.$listObstruksi['id_variabel'].'" ';
                                            if($spirometri['obstruksi'] == $listObstruksi['id_variabel']){
                                                echo "checked";
                                            }else{
                                                echo "";
                                            }
            echo '                          >
                                            <label for="listObstruksi_'.$listObstruksi['id_variabel'].'">
                                            '.$listObstruksi['variabel'].'                           
                                            </label>
                                        </div>';
                                        endforeach;
            echo '                  </div>
                                    </br>					                           	
                                </div>
                            </div>

                        </div>
            
            ';
        endforeach;
    }
}
?>