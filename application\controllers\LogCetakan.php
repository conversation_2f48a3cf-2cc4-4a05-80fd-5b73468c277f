<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Controller untuk menangani log cetakan
 *
 * Controller ini digunakan untuk mencatat dan menampilkan statistik
 * penggunaan cetakan dalam aplikasi SIMRSKD
 */
class LogCetakan extends CI_Controller {

    /**
     * Konstruktor
     */
    public function __construct() {
        parent::__construct();

        // Load library yang diperlukan
        $this->load->library('session');
        $this->load->library('form_validation');
        $this->load->database();

        if ($this->session->userdata('logged_in') == FALSE) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model('LogCetakanModel');
        $this->load->helper('url');
    }

    /**
     * API untuk menyimpan log cetakan
     */
    public function simpan() {
        // Cek apakah request adalah AJAX
        if (!$this->input->is_ajax_request()) {
            exit('No direct script access allowed');
        }

        $post = $this->input->post();

        // Validasi data
        if (!isset($post['url_cetakan']) || empty($post['url_cetakan'])) {
            echo json_encode(['status' => 'error', 'message' => 'URL cetakan tidak boleh kosong']);
            return;
        }

        // Ekstrak nokun dari URL jika tidak ada
        $nokun = isset($post['nokun']) ? $post['nokun'] : null;

        // Jika nokun masih kosong, coba cari dari id_ref
        if (empty($nokun) && !empty($post['id_ref'])) {
            $id_ref = $post['id_ref'];

            // Coba cari nokun dari berbagai tabel berdasarkan id_ref
            $this->load->database();

            // Coba cari di tabel kunjungan
            $query = $this->db->query("SELECT NOMOR FROM pendaftaran.kunjungan WHERE ID = ? OR NOPEN = ? LIMIT 1", array($id_ref, $id_ref));
            if ($query->num_rows() > 0) {
                $row = $query->row();
                $nokun = $row->NOMOR;
            }

            // Jika masih kosong, coba cari di tabel lain
            if (empty($nokun)) {
                // Coba cari di tabel pendaftaran
                $query = $this->db->query("SELECT k.NOMOR FROM pendaftaran.pendaftaran p
                                          JOIN pendaftaran.kunjungan k ON p.NOMOR = k.NOPEN
                                          WHERE p.NORM = ? OR p.NOMOR = ?
                                          ORDER BY k.TANGGAL DESC LIMIT 1", array($id_ref, $id_ref));
                if ($query->num_rows() > 0) {
                    $row = $query->row();
                    $nokun = $row->NOMOR;
                }
            }

            // Jika masih kosong, coba cari di tabel emr
            if (empty($nokun)) {
                $query = $this->db->query("SELECT NOKUN FROM emr.emr WHERE ID = ? LIMIT 1", array($id_ref));
                if ($query->num_rows() > 0) {
                    $row = $query->row();
                    $nokun = $row->NOKUN;
                }
            }
        }

        // Ekstrak id_ref dari URL jika tidak ada
        $id_ref = isset($post['id_ref']) ? $post['id_ref'] : null;

        // Tentukan jenis cetakan jika belum ada
        $jenis_cetakan = isset($post['jenis_cetakan']) ? $post['jenis_cetakan'] : null;
        if (empty($jenis_cetakan) && !empty($post['url_cetakan'])) {
            $url = $post['url_cetakan'];
            if (strpos($url, '/cppt/') !== false) {
                $jenis_cetakan = 'CPPT';
            } else if (strpos($url, '/penunjang/') !== false) {
                $jenis_cetakan = 'Penunjang';
            } else if (strpos($url, '/PersetujuanTindakan/') !== false) {
                $jenis_cetakan = 'Persetujuan Tindakan';
            } else if (strpos($url, '/BuktiPelayananRJ/') !== false) {
                $jenis_cetakan = 'Bukti Pelayanan RJ';
            } else if (strpos($url, '/triase/') !== false) {
                $jenis_cetakan = 'Triase';
            } else if (strpos($url, '/mendus/') !== false) {
                $jenis_cetakan = 'Mendus';
            } else if (strpos($url, '/cendana/') !== false) {
                $jenis_cetakan = 'Cendana';
            } else if (strpos($url, '/ctsimulator/') !== false) {
                $jenis_cetakan = 'CT Simulator';
            } else if (strpos($url, '/tertekan/') !== false) {
                $jenis_cetakan = 'Skrining Perasaan Tertekan';
            } else if (strpos($url, '/summarylist/') !== false) {
                $jenis_cetakan = 'Summary List';
            } else if (strpos($url, '/farmasi/') !== false) {
                $jenis_cetakan = 'Farmasi';
            } else if (strpos($url, '/inventory/') !== false) {
                $jenis_cetakan = 'Inventory';
            } else if (strpos($url, '/timja/') !== false) {
                $jenis_cetakan = 'Timja';
            } else if (strpos($url, '/konsul/') !== false) {
                $jenis_cetakan = 'Konsultasi';
            } else if (strpos($url, '/sistemik/') !== false) {
                $jenis_cetakan = 'Pengkajian Sistemik';
            } else if (strpos($url, '/resume/') !== false || strpos($url, '/resume_medis/') !== false) {
                $jenis_cetakan = 'Resume Medis';
            } else if (strpos($url, '/radiologi/') !== false) {
                $jenis_cetakan = 'Radiologi';
            } else if (strpos($url, '/laboratorium/') !== false) {
                $jenis_cetakan = 'Laboratorium';
            } else if (strpos($url, '/fisioterapi/') !== false) {
                $jenis_cetakan = 'Fisioterapi';
            } else if (strpos($url, '/gizi/') !== false) {
                $jenis_cetakan = 'Gizi';
            } else if (strpos($url, '/resep/') !== false) {
                $jenis_cetakan = 'Resep';
            } else if (strpos($url, '/surat/') !== false) {
                $jenis_cetakan = 'Surat';
            } else if (strpos($url, '/formulir/') !== false) {
                $jenis_cetakan = 'Formulir';
            } else if (strpos($url, '/laporan/') !== false) {
                $jenis_cetakan = 'Laporan';
            } else {
                // Jika tidak ada yang cocok, gunakan bagian terakhir dari URL sebagai jenis cetakan
                $urlParts = explode('/', $url);
                $lastPart = end($urlParts);
                if (strpos($lastPart, '.') !== false) {
                    $lastPart = substr($lastPart, 0, strpos($lastPart, '.'));
                }
                $jenis_cetakan = 'Lainnya: ' . $lastPart;
            }
        }

        // Bersihkan URL, potong hanya sampai .php
        $url_cetakan = $post['url_cetakan'];

        // Potong URL di tanda tanya (?)
        $url_parts = explode('?', $url_cetakan);
        $url_cetakan = $url_parts[0];

        // Siapkan data untuk disimpan
        $data = [
            'url_cetakan' => $url_cetakan,
            'nokun' => $nokun,
            'id_ref' => $id_ref,
            'jenis_cetakan' => $jenis_cetakan,
            'oleh' => $this->session->userdata('id'),
            'waktu_cetak' => date('Y-m-d H:i:s'),
            'ip_address' => $this->input->ip_address(),
            'user_agent' => $this->input->user_agent()
        ];

        // Simpan data
        $this->db->trans_begin();
        $id = $this->LogCetakanModel->simpan($data);

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan log cetakan']);
        } else {
            $this->db->trans_commit();
            echo json_encode(['status' => 'success', 'message' => 'Log cetakan berhasil disimpan', 'id' => $id]);
        }
    }
}
