<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class InstrumenMNA_Model extends CI_Model {

  public function simpanFSkriningMna($data)
  {
    $this->db->trans_begin();
    $this->db->insert('db_layanan.tb_geriatri_mna_skrining', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function simpanFPenilaianMna($data)
  {
    $this->db->trans_begin();
    $this->db->insert('db_layanan.tb_geriatri_mna_penilaian', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    // echo json_encode($result);
  }

  public function listInstrumenMna($nomr)
  {
    $query = $this->db->query("SELECT tgms.id IDSKRINING, tgmp.id IDPENILAIAN
                              , tgms.created_at tanggal, tgms.nokun
                              , (dv1.nilai+dv2.nilai+dv3.nilai+dv4.nilai+dv5.nilai+dv6.nilai)TOTAL_TGMS
                              , (dv7.nilai+dv8.nilai+dv9.nilai+dv10.nilai
                              +dv14.nilai+dv15.nilai+dv16.nilai+dv17.nilai
                              +dv18.nilai+dv19.nilai+dv20.nilai+dv21.nilai)
                              +(IF((dv11.nilai+dv12.nilai+dv13.nilai) IN (0,1),0.00
                              ,IF((dv11.nilai+dv12.nilai+dv13.nilai)=2,0.50,1.00)))TOTAL_TGMP
                              FROM db_layanan.tb_geriatri_mna_skrining tgms
                              LEFT JOIN db_layanan.tb_geriatri_mna_penilaian tgmp ON tgmp.nokun = tgms.nokun
                              LEFT JOIN db_master.variabel dv1 ON dv1.id_variabel = tgms.skrining_a
                              LEFT JOIN db_master.variabel dv2 ON dv2.id_variabel = tgms.skrining_b
                              LEFT JOIN db_master.variabel dv3 ON dv3.id_variabel = tgms.skrining_c
                              LEFT JOIN db_master.variabel dv4 ON dv4.id_variabel = tgms.skrining_d
                              LEFT JOIN db_master.variabel dv5 ON dv5.id_variabel = tgms.skrining_e
                              LEFT JOIN db_master.variabel dv6 ON dv6.id_variabel = tgms.skrining_f
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tgms.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN db_master.variabel dv7 ON dv7.id_variabel = tgmp.penilaian_g
                              LEFT JOIN db_master.variabel dv8 ON dv8.id_variabel = tgmp.penilaian_h
                              LEFT JOIN db_master.variabel dv9 ON dv9.id_variabel = tgmp.penilaian_i
                              LEFT JOIN db_master.variabel dv10 ON dv10.id_variabel = tgmp.penilaian_j
                              LEFT JOIN db_master.variabel dv11 ON dv11.id_variabel = tgmp.penilaian_k1
                              LEFT JOIN db_master.variabel dv12 ON dv12.id_variabel = tgmp.penilaian_k2
                              LEFT JOIN db_master.variabel dv13 ON dv13.id_variabel = tgmp.penilaian_k3
                              LEFT JOIN db_master.variabel dv14 ON dv14.id_variabel = tgmp.penilaian_l
                              LEFT JOIN db_master.variabel dv15 ON dv15.id_variabel = tgmp.penilaian_m
                              LEFT JOIN db_master.variabel dv16 ON dv16.id_variabel = tgmp.penilaian_n
                              LEFT JOIN db_master.variabel dv17 ON dv17.id_variabel = tgmp.penilaian_o
                              LEFT JOIN db_master.variabel dv18 ON dv18.id_variabel = tgmp.penilaian_p
                              LEFT JOIN db_master.variabel dv19 ON dv19.id_variabel = tgmp.penilaian_q
                              LEFT JOIN db_master.variabel dv20 ON dv20.id_variabel = tgmp.penilaian_r
                              LEFT JOIN db_master.variabel dv21 ON dv21.id_variabel = tgmp.penilaian_s
                              WHERE pp.NORM = '$nomr'
                              GROUP BY tgms.id
                              ORDER BY tgms.id DESC");
    return $query;
  }

  public function getSkriningMna($id)
  {
    $query = $this->db->query("SELECT tgms.`*`
                              ,(dv1.nilai+dv2.nilai+dv3.nilai+dv4.nilai+dv5.nilai+dv6.nilai)TOTAL_TGMS
                              ,HOUR(TIMEDIFF(NOW(),tgms.created_at)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tgms.created_at))<=24,1,0) STATUS_EDIT
                              FROM db_layanan.tb_geriatri_mna_skrining tgms
                              LEFT JOIN db_master.variabel dv1 ON dv1.id_variabel = tgms.skrining_a
                              LEFT JOIN db_master.variabel dv2 ON dv2.id_variabel = tgms.skrining_b
                              LEFT JOIN db_master.variabel dv3 ON dv3.id_variabel = tgms.skrining_c
                              LEFT JOIN db_master.variabel dv4 ON dv4.id_variabel = tgms.skrining_d
                              LEFT JOIN db_master.variabel dv5 ON dv5.id_variabel = tgms.skrining_e
                              LEFT JOIN db_master.variabel dv6 ON dv6.id_variabel = tgms.skrining_f
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tgms.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              WHERE tgms.id = '$id'");
    return $query->row_array();
  }

  public function getPenilaianMna($id)
  {
    $query = $this->db->query("SELECT tgmp.`*`
                              , (dv7.nilai+dv8.nilai+dv9.nilai+dv10.nilai
                              +dv14.nilai+dv15.nilai+dv16.nilai+dv17.nilai
                              +dv18.nilai+dv19.nilai+dv20.nilai+dv21.nilai)
                              +(IF((dv11.nilai+dv12.nilai+dv13.nilai) IN (0,1),0.00
                              ,IF((dv11.nilai+dv12.nilai+dv13.nilai)=2,0.50,1.00)))TOTAL_TGMP
                              ,HOUR(TIMEDIFF(NOW(),tgmp.created_at)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tgmp.created_at))<=24,1,0) STATUS_EDIT
                              FROM db_layanan.tb_geriatri_mna_penilaian tgmp
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tgmp.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN db_master.variabel dv7 ON dv7.id_variabel = tgmp.penilaian_g
                              LEFT JOIN db_master.variabel dv8 ON dv8.id_variabel = tgmp.penilaian_h
                              LEFT JOIN db_master.variabel dv9 ON dv9.id_variabel = tgmp.penilaian_i
                              LEFT JOIN db_master.variabel dv10 ON dv10.id_variabel = tgmp.penilaian_j
                              LEFT JOIN db_master.variabel dv11 ON dv11.id_variabel = tgmp.penilaian_k1
                              LEFT JOIN db_master.variabel dv12 ON dv12.id_variabel = tgmp.penilaian_k2
                              LEFT JOIN db_master.variabel dv13 ON dv13.id_variabel = tgmp.penilaian_k3
                              LEFT JOIN db_master.variabel dv14 ON dv14.id_variabel = tgmp.penilaian_l
                              LEFT JOIN db_master.variabel dv15 ON dv15.id_variabel = tgmp.penilaian_m
                              LEFT JOIN db_master.variabel dv16 ON dv16.id_variabel = tgmp.penilaian_n
                              LEFT JOIN db_master.variabel dv17 ON dv17.id_variabel = tgmp.penilaian_o
                              LEFT JOIN db_master.variabel dv18 ON dv18.id_variabel = tgmp.penilaian_p
                              LEFT JOIN db_master.variabel dv19 ON dv19.id_variabel = tgmp.penilaian_q
                              LEFT JOIN db_master.variabel dv20 ON dv20.id_variabel = tgmp.penilaian_r
                              LEFT JOIN db_master.variabel dv21 ON dv21.id_variabel = tgmp.penilaian_s
                              WHERE tgmp.id
                              = '$id'");
    return $query->row_array();
  }

  public function updateFSkriningMna($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('db_layanan.tb_geriatri_mna_skrining', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updateFPenilaianMna($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('db_layanan.tb_geriatri_mna_penilaian', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function cekSriningMna($nokun)
  {
    $query = $this->db->query("SELECT gms.`*`
                              ,(dv1.nilai+dv2.nilai+dv3.nilai+dv4.nilai+dv5.nilai+dv6.nilai)TOTAL_TGMS
                              FROM db_layanan.tb_geriatri_mna_skrining gms
                              LEFT JOIN db_master.variabel dv1 ON dv1.id_variabel = gms.skrining_a
                              LEFT JOIN db_master.variabel dv2 ON dv2.id_variabel = gms.skrining_b
                              LEFT JOIN db_master.variabel dv3 ON dv3.id_variabel = gms.skrining_c
                              LEFT JOIN db_master.variabel dv4 ON dv4.id_variabel = gms.skrining_d
                              LEFT JOIN db_master.variabel dv5 ON dv5.id_variabel = gms.skrining_e
                              LEFT JOIN db_master.variabel dv6 ON dv6.id_variabel = gms.skrining_f
                              WHERE gms.nokun = '$nokun'");
    return $query->row_array();
  }

}

/* End of file InstrumenMNA_Model.php */
/* Location: ./application/models/geriatri/InstrumenMNA_Model.php */
