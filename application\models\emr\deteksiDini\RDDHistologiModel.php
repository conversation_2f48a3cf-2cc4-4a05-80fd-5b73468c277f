<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RDDHistologiModel extends MY_Model
{
    protected $_table_name = 'medis.tb_rdd_histologi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }

    public function simpan($data)
    {
        $this->db->insert_batch($this->_table_name, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_rdd_histologi.id_rdd', $id);
        $this->db->update($this->_table_name, $data);
    }

    public function penunjang($id)
    {
        $this->db->select(
            "rh.id_histologi, DATE_FORMAT(hph.TANGGAL_LAB, '%d-%m-%Y, %H.%i.%s') tanggal,
            hph.NOMOR_LAB no_lab_histologi"
        );
        $this->db->from('medis.tb_rdd_histologi rh');
        $this->db->join('layanan.hasil_pa_histologi hph', 'hph.id = rh.id_histologi', 'left');
        $this->db->where('rh.id_rdd', $id);
        $this->db->where('rh.status', 1);
        $this->db->order_by('tanggal');
        return $this->db->get();
    }
}

// End of file RDDHistologiModel.php
// Location: ./application/models/emr/deteksiDini/RDDHistologiModel.php