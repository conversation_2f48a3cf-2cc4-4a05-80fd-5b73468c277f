<?php
defined('BASEPATH') or exit('No direct script access allowed');

class BerkasPasien extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(array('masterModel', 'uploadRmModel'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    // echo "<pre>";print_r($nomr);exit();
    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
    );

    $this->load->view('uploadRm/berkasPerPasien', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nomr = $this->input->POST('nomr');
    $list = $this->uploadRmModel->tabelVBPJS($nomr);
    $no = 1;
    $jenis = '';
    $data = array();
    // echo "<pre>";print_r($list);exit();

    foreach ($list->result() as $l) {
      if ($l->jenis_data == 0) {
        $jenis = 'Berkas PA, BMP, HCV+, CD4, atau lainnya';
      } elseif ($l->jenis_data == 1) {
        $jenis = 'Hasil penunjang eksternal dan internal';
      } elseif ($l->jenis_data == 2) {
        $jenis = 'JPOK';
      } elseif ($l->jenis_data == 3) {
        $jenis = '<i>Billing</i> layanan';
      } elseif ($l->jenis_data == 4) {
        $jenis = 'Laporan operasi';
      }

      $data[] = array(
        $no,
        $l->norm,
        $l->nama,
        $l->nobpjs,
        $jenis,
        // Real
        "<a href='http://192.168.7.10/vbpjs/uploads/" . $l->file . "' class='btn btn-primary' target='_blank'>Lihat</a>",
        // Dummy
        // "<a href='http://192.168.59.60/vbpjs_uploads/" . $l->file . "' class='btn btn-primary' target='_blank'>Lihat</a>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recorsTotal' => $list->num_rows(),
      'recordsFilteres' => $list->num_rows(),
      'data' => $data,
    );
    echo json_encode($output);
  }
}

/* End of file BerkasPasien.php */
/* Location: ./application/controllers/filePendukungPasien/BerkasPasien.php */