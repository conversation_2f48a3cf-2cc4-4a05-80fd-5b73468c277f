<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SimulatorInformationModel extends CI_Model
{

  public function simpanFotoSimulatorInformation($data)
  {
    $this->db->insert('medis.tb_foto_simulator_infotmation', $data);
  }

  public function listsimulator_infotmation($norm)
  {
    $query = $this->db->query("SELECT sm.`*`, master.getNamaLengkap(sm.nomr)NAMAPASIEN, master.getNamaLengkapPegawai(ap.NIP)OLEH
                              FROM medis.tb_foto_simulator_infotmation sm
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = sm.idPengguna
                              WHERE sm.nomr = '$norm' AND sm.status=1");

    return $query;
  }

  public function updateStatusSm($id, $data)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_foto_simulator_infotmation', $data);
  }

  public function hasilFotoSi($id)
  {
    $query = $this->db->query("SELECT sm.`*`, master.getNamaLengkap(sm.nomr)NAMAPASIEN, master.getNamaLengkapPegawai(ap.NIP)OLEH
                              FROM medis.tb_foto_simulator_infotmation sm
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = sm.idPengguna
                              WHERE sm.id = '$id'");

    return $query->row_array();
  }

  public function simpanSimulatorInformationDr($data)
  {
    $this->db->insert('medis.tb_simulatorInformationDr', $data);
    return $this->db->insert_id();
  }

  public function insertTakePhoto($data)
  {
    $this->db->insert('medis.tb_foto_simulator', $data);
  }

  public function simpanSimulatorInformationRadiografer($data)
  {
    $this->db->insert('medis.tb_simulatorInformationRadiografer', $data);
    return $this->db->insert_id();
  }

  public function simpanSimulatorInformationFisikaMedis($data)
  {
    $this->db->insert('medis.tb_simulatorInformationFisikaMedis', $data);
    return $this->db->insert_id();
  }

  public function historySimulatorInformation($nomr)
  {
    $query = $this->db->query("SELECT sid.id IDSIDR,sid.nokun,sid.oleh oleh_dr, master.getNamaLengkapPegawai(ap1.NIP)OLEHDR, sid.tanggal TANGGALSIDR,
                              sir.id IDSIRAD,sir.oleh oleh_rad,master.getNamaLengkapPegawai(ap2.NIP)OLEHRAD,sir.tanggal TANGGALSIRAD,
                              sif.id IDSIFIS, sif.oleh oleh_fis,master.getNamaLengkapPegawai(ap3.NIP)OLEHFIS ,sif.tanggal TANGGALSIFIS
                              ,HOUR(TIMEDIFF(NOW(),sid.tanggal)) DURASI,IF(HOUR(TIMEDIFF(NOW(),sid.tanggal))<=24,1,0) STATUS_EDIT, sid.status as STATUS
                              FROM medis.tb_simulatorInformationDr sid
                              LEFT JOIN medis.tb_simulatorInformationRadiografer sir ON sir.id_simulator_dokter = sid.id AND sir.`status` = 1
                              LEFT JOIN medis.tb_simulatorInformationFisikaMedis sif ON sif.id_simulator_dokter = sid.id AND sif.`status` = 1
                              LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = sid.oleh
                              LEFT JOIN aplikasi.pengguna ap2 ON ap2.ID = sir.oleh
                              LEFT JOIN aplikasi.pengguna ap3 ON ap3.ID = sif.oleh
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = sid.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              WHERE pp.NORM = '$nomr' AND sid.`status` != 0
                              ORDER BY sid.tanggal DESC");
    return $query;
  }

  public function getSimulatorInformationDr($id)
  {
    $query = $this->db->query("SELECT tsiDr.`*`, master.getNamaLengkap(pp.NORM)NAMAPASIEN,pp.NORM,pp.NOMOR NOPEN
                              ,master.getNamaLengkapPegawai(ap.NIP)OLEH, pp.TANGGAL TANGGALDAFTAR
                              ,HOUR(TIMEDIFF(NOW(),tsiDr.tanggal)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tsiDr.tanggal))<=24,1,0) STATUS_EDIT
                              FROM medis.tb_simulatorInformationDr tsiDr
                              LEFT JOIN pendaftaran.kunjungan pk ON tsiDr.nokun = pk.NOMOR
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tsiDr.oleh
                              WHERE tsiDr.id = '$id'");

    return $query->row_array();
  }

  public function getSimulatorInformationRad($id)
  {
    $this->db->select('tsiRad.*, master.getNamaLengkap(pp.NORM) as NAMAPASIEN, pp.NOMOR as NOPEN, pp.NORM, master.getNamaLengkapPegawai(ap.NIP) as OLEH, pp.TANGGAL as TANGGALDAFTAR, HOUR(TIMEDIFF(NOW(), tsiRad.tanggal)) as DURASI, IF(HOUR(TIMEDIFF(NOW(), tsiRad.tanggal)) <= 24, 1, 0) as STATUS_EDIT, tsiDr.status as STATUS_DOKTER');
    $this->db->from('medis.tb_simulatorInformationRadiografer tsiRad');
    $this->db->join('pendaftaran.kunjungan pk', 'tsiRad.nokun = pk.NOMOR', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = tsiRad.oleh', 'left');
    $this->db->join('medis.tb_simulatorInformationDr tsiDr', 'tsiDr.id = tsiRad.id_simulator_dokter', 'left');
    $this->db->where('tsiRad.id', $id);

    $query = $this->db->get();
    return $query->row_array();
  }

  public function getSimulatorInformationFis($id)
  {
    $this->db->select("tsiFis.*, master.getNamaLengkap(pp.NORM) AS NAMAPASIEN, 
                          pp.NOMOR AS NOPEN, pp.NORM, 
                          master.getNamaLengkapPegawai(ap.NIP) AS OLEH, 
                          pp.TANGGAL AS TANGGALDAFTAR, 
                          HOUR(TIMEDIFF(NOW(), tsiFis.tanggal)) AS DURASI, 
                          IF(HOUR(TIMEDIFF(NOW(), tsiFis.tanggal)) <= 24, 1, 0) AS STATUS_EDIT, tsiDr.status as STATUS_DOKTER");
    $this->db->from("medis.tb_simulatorInformationFisikaMedis tsiFis");
    $this->db->join("pendaftaran.kunjungan pk", "tsiFis.nokun = pk.NOMOR", "left");
    $this->db->join("pendaftaran.pendaftaran pp", "pp.NOMOR = pk.NOPEN", "left");
    $this->db->join("aplikasi.pengguna ap", "ap.ID = tsiFis.oleh", "left");
    $this->db->join('medis.tb_simulatorInformationDr tsiDr', 'tsiDr.id = tsiFis.id_simulator_dokter', 'left');

    $this->db->where("tsiFis.id", $id);

    $query = $this->db->get();
    return $query->row_array();
  }


  public function tb_simulatorInformationFisikaMedisDetail($id)
  {
    $query = $this->db->query("SELECT *
                              FROM medis.tb_simulatorInformationFisikaMedisDetail tsid
                              WHERE tsid.idTbSimulatorFisikaMedis = '$id'");

    return $query;
  }

  public function updateSimulatorInformationDr($data, $id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('medis.tb_simulatorInformationDr', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updateSimulatorInformationRadiografer($data, $id)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_simulatorInformationRadiografer', $data);
    return $this->db->affected_rows(); // Mengembalikan jumlah baris yang terpengaruh
  }


  public function updateSimulatorInformationFisikaMedis($data, $id)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_simulatorInformationFisikaMedis', $data);
  }

  public function historyUploadSimulatorInformation($nomr)
  {
    $query = $this->db->query("SELECT fs.`*`, master.getNamaLengkapPegawai(ap.NIP)OLEH
                              FROM medis.tb_foto_simulator fs
                              LEFT JOIN medis.tb_simulatorInformationDr sdr ON sdr.id = fs.IDSIMULATOR
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = sdr.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = sdr.oleh
                              WHERE pp.NORM = '$nomr' AND fs.`STATUS` = 1
                              ORDER BY fs.TANGGAL DESC");

    return $query;
  }

  public function downloadFileSimulator($id)
  {
    $query = $this->db->query("SELECT fs.`*`
                              FROM medis.tb_foto_simulator fs
                              WHERE fs.ID ='$id'");

    return $query->row_array();
  }

  public function simpanSimulatorInformationRadiograferDetail($data)
  {
    $this->db->insert('medis.tb_simulatorInformationRadiograferDetail', $data);
    return $this->db->insert_id();
  }

  public function getSiRadDetail($idRad)
  {
    $this->db->select('*');
    $this->db->from('medis.tb_simulatorInformationRadiograferDetail');
    $this->db->where('id_si_rad', $idRad); // Sesuaikan dengan nama kolom foreign key
    $this->db->where("status != 0");
    $query = $this->db->get();
    return $query->result_array(); // Mengembalikan banyak data dalam bentuk array
  }

  public function udtSiRadDetail($ids, $data)
  {
    if (!empty($ids) && is_array($ids)) {
      $this->db->where_in('id', $ids);
      $this->db->update('medis.tb_simulatorInformationRadiograferDetail', $data);
      return $this->db->affected_rows(); // Mengembalikan jumlah baris yang terpengaruh
    }
    return false; // Jika $ids kosong atau bukan array, return false
  }

  public function getSiDrByNokun($nokun) {
    $this->db->select('sidr.*');
    $this->db->from('medis.tb_simulatorInformationDr sidr');
    $this->db->where('sidr.nokun', $nokun);
    return $this->db->get()->row(); // Mengambil satu baris hasil
  }

}

/* End of file SimulatorInformationModel.php */
/* Location: ./application/models/radioterapi/SimulatorInformationModel.php */
