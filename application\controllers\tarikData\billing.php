<?php
defined('BASEPATH') or exit('No direct script access allowed');

class billing extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel'));
    }

   public function index()
  {
    $data = array(
      'title'         => 'Halaman Billing',
      'isi'           => 'tarikData/billing/index',
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function cariQuery()
  {
    $nomr = $this->input->post('nomr');
    $cariQuery = $this->pengkajianAwalModel->cariQuery($nomr);
    $data = array(
      'nomr' => $nomr,
      'cariQuery' => $cariQuery,
    );

    $this->load->view('tarikData/billing/tableHasil', $data);
  }

}