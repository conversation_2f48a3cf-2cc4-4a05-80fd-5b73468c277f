<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>laimRehab extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'masterModel',
                'pengkajianAwalModel',
                'rehabilitasiMedik/klaimRehab/KlaimRehabModel',
                'rehabilitasiMedik/klaimRehab/KlaimRehabDMModel',
                'rehabilitasiMedik/klaimRehab/KlaimRehabDFModel',
                'rehabilitasiMedik/klaimRehab/KlaimRehabTLModel',
                'rehabilitasiMedik/klaimRehab/KlaimRehabKunjunganModel'
            ]
        );
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);
        $cppt = $this->KlaimRehabModel->getCPPT($nokun);
        $data = [
            'nomr' => $this->uri->segment(4),
            'nokun' => $nokun,
            'pasien' => $pasien,
            'noSEP' => !empty($pasien['NO_SEP']) ? $pasien['NO_SEP'] : (!empty($pasien['NO_SEP_ALT']) ? $pasien['NO_SEP_ALT'] : 0),
            'cppt'  => $cppt
        ];
        // echo '<pre>';print_r($data);exit();

        $this->load->view('Pengkajian/rehabilitasiMedik/klaimRehab/index', $data);
    }

    public function simpan()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->KlaimRehabModel->rules());
            // $this->form_validation->set_rules($this->KlaimRehabDMModel->rules()); // diagnosis medis
            // $this->form_validation->set_rules($this->KlaimRehabDFModel->rules()); // diagnosis fungsi
            // $this->form_validation->set_rules($this->KlaimRehabTLModel->rules()); // tata laksana

            if (isset($post['akibat_kerja'])) {
                if ($post['akibat_kerja'] == 1) { // keterangan suspek penyakit akibat kerja
                    $this->form_validation->set_rules($this->KlaimRehabModel->rulesAkibatKerja());
                }
            }

            if ($this->form_validation->run() == true) {
                // mulai klaim rehab
                $dataKlaimRehab = [
                    'nomr' => $post['nomr'],
                    'nokun' => $post['nokun'],
                    'id_cppt' => $post['id_cppt'],
                    'tanggal' => $post['tanggal'],
                    'anamnesis' => $post['anamnesis'],
                    'pemeriksaan_fisik' => $post['pemeriksaan_fisik'],
                    'diagnosis' => $post['diagnosis'],
                    'tata_laksana' => $post['tata_laksana'],
                    'evaluasi' => $post['evaluasi'],
                    'akibat_kerja' => $post['akibat_kerja'],
                    'keterangan_akibat_kerja' => $post['keterangan_akibat_kerja'] ?? null,
                    'oleh' => $this->session->userdata['id'],
                    'status' => 1,
                ];
                $id = $this->KlaimRehabModel->simpan($dataKlaimRehab);
                // akhir klaim rehab

                // mulai diagnosis medis
                // $i = 0;
                // $dataDiagnosisMedis = [];
                // foreach ($post['diagnosis_medis'] as $dm) {
                //     $dataDiagnosisMedis[$i] = [
                //         'id_klaim_rehab' => $id,
                //         'diagnosis_medis' => $dm,
                //         'status' => 1
                //     ];
                //     $i++;
                // }
                // $this->KlaimRehabDMModel->simpan($dataDiagnosisMedis);
                // akhir diagnosis medis

                // mulai diagnosis fungsi
                // $j = 0;
                // $dataDiagnosisFungsi = [];
                // foreach ($post['diagnosis_fungsi'] as $df) {
                //     $dataDiagnosisFungsi[$j] = [
                //         'id_klaim_rehab' => $id,
                //         'diagnosis_fungsi' => $df,
                //         'status' => 1
                //     ];
                //     $j++;
                // }
                // $this->KlaimRehabDFModel->simpan($dataDiagnosisFungsi);
                // akhir diagnosis fungsi

                // mulai tata laksana
                // $k = 0;
                // $dataTataLaksana = [];
                // foreach ($post['tata_laksana'] as $tl) {
                //     $dataTataLaksana[$k] = [
                //         'id_klaim_rehab' => $id,
                //         'tata_laksana' => $tl,
                //         'status' => 1
                //     ];
                //     $k++;
                // }
                // $this->KlaimRehabTLModel->simpan($dataTataLaksana);
                // akhir tata laksana

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array()
                ];
            }

            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    public function tabelFormulir()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $nomr = $post['nomr'];
        $draw = intval($post['draw']);
        $no = $post['start'];
        $tabel = $this->KlaimRehabModel->ambil($nomr);

        foreach ($tabel as $t) {
            $data[] = [
                ++$no . '.',
                $t->tata_laksana,
                date('d-m-Y', strtotime($t->tgl_pertama)),
                isset($t->tgl_terakhir) ? date('d-m-Y', strtotime($t->tgl_terakhir)) : '-',
                "<div class='btn-group-vertical' role='group'>
                    <button type='button' class='btn btn-primary btn-sm waves-effect tbl-detail-klaim-rehab' data-toggle='collapse' data-target='#collapse-form-klaim-rehab' aria-expanded='false' aria-controls='collapse-form-klaim-rehab' data-id='" . $t->id . "'>
                        <i class='fa fa-star'></i> Pilih
                    </button>
                    <a href='/reports/simrskd/RehabMedik/klaimrehab.php?format=pdf&ID=" . $t->id . "' class='btn btn-warning btn-sm waves-effect' target='_blank'>
                        <i class='fa fa-print'></i> Cetak
                    </a>
                </div>",
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->KlaimRehabModel->hitung_semua($nomr),
            'recordsFiltered' => $this->KlaimRehabModel->hitung_tersaring($nomr),
            'data' => $data
        ];

        echo json_encode($output);
    }

    public function detail()
    {
        $id = $this->input->post('id');
        // $diagnosisMedis = [];
        // $diagnosisFungsi = [];
        // $tataLaksana = [];
        $detail = $this->KlaimRehabModel->detail($id);
        // echo '<pre>';print_r($detail);exit();

        // mulai diagnosis medis
        // $dataDM = $this->KlaimRehabDMModel->data_tersimpan($id);
        // foreach ($dataDM as $row) {
        //     $sub_array = [];
        //     $sub_array['id'] = $row->CODE;
        //     $sub_array['text'] = $row->CODE . ' - ' . $row->STR;
        //     $sub_array['selected'] = true;
        //     $diagnosisMedis[] = $sub_array;
        // }
        // echo '<pre>';print_r($diagnosisMedis);exit();
        // akhir diagnosis medis

        // mulai diagnosis fungsi
        // $dataDF = $this->KlaimRehabDFModel->data_tersimpan($id);
        // foreach ($dataDF as $row) {
        //     $sub_array = [];
        //     $sub_array['id'] = $row->CODE;
        //     $sub_array['text'] = $row->CODE . ' - ' . $row->STR;
        //     $sub_array['selected'] = true;
        //     $diagnosisFungsi[] = $sub_array;
        // }
        // echo '<pre>';print_r($diagnosisFungsi);exit();
        // akhir diagnosis fungsi

        // mulai tata laksana
        // $dataTL = $this->KlaimRehabTLModel->data_tersimpan($id);
        // foreach ($dataTL as $row) {
        //     $sub_array = [];
        //     $sub_array['id'] = $row->CODE;
        //     $sub_array['text'] = $row->CODE . ' - ' . $row->STR;
        //     $sub_array['selected'] = true;
        //     $tataLaksana[] = $sub_array;
        // }
        // echo '<pre>';print_r($tataLaksana);exit();
        // akhir tata laksana

        echo json_encode(
            [
                'status' => 'success',
                'detail' => $detail,
            ]
        );
    }

    public function kunjungan()
    {
        $post = $this->input->post();
        $data = [
            'id' => $post['id'],
            'noSEP' => $post['no_sep'] != 0 ? $post['no_sep'] : null,
            'nokun' => $post['nokun'],
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/rehabilitasiMedik/klaimRehab/kunjungan/tabel', $data);
    }

    public function formKunjungan()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $disableDokter = null;
        $disableTerapis = null;

        // mulai profesi
        if ($this->session->userdata('profesi') == 11) { // dokter
            $disableDokter = 'disabled';
        } elseif ($this->session->userdata('profesi') == 9) { // terapis
            $disableTerapis = 'disabled';
        }
        // akhir profesi

        $data = [
            'id' => $post['id'],
            'idPengguna' => $this->session->userdata('id'),
            'dokter' => $this->masterModel->listUserDr(),
            'terapis' => $this->KlaimRehabModel->listTerapis(),
            'disableDokter' => $disableDokter,
            'disableTerapis' => $disableTerapis,
            // 'noSEP' => $post['no_sep'] != 0 ? $post['no_sep'] : null,
        ];

        // mulai periksa id
        if (isset($post['id_kunjungan'])) {
            $detail = $this->KlaimRehabKunjunganModel->detail($post['id_kunjungan']);
            $data['id_kunjungan'] = $post['id_kunjungan'];
            $data['detail'] = $detail;
            $data['isiDokter'] = $detail['dokter'];
            $data['isiTerapis'] = $detail['terapis'];
            $data['pasien'] = $pasien;
        } else {
            if ($this->session->userdata('profesi') == 11) { // dokter
                $data['isiDokter'] = $this->session->userdata('iddokter') ?? 0;
                $data['isiTerapis'] = 0;
            } elseif ($this->session->userdata('profesi') == 9 || $this->session->userdata('profesi') == 15) { // terapis
                $data['isiDokter'] = 0;
                $data['isiTerapis'] = $this->session->userdata('id') ?? 0;
            } else { // lainnya
                $data['isiDokter'] = 0;
                $data['isiTerapis'] = 0;
            }
        }
        
        $data['pasien'] = $this->pengkajianAwalModel->getNomr($post['nokun']);
        // akhir periksa id

        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/rehabilitasiMedik/klaimRehab/kunjungan/form', $data);
    }

    public function simpanKunjungan()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->KlaimRehabKunjunganModel->rules());

            if ($this->form_validation->run() == true) {
                // mulai data kunjungan
                $getSEP = $this->KlaimRehabKunjunganModel->getSEP($post['nopen']);
                $data = [
                    'id_klaim_rehab' => $post['id_klaim_rehab'],
                    'nokun' => $post['nokun'],
                    'tanggal' => $post['tanggal'],
                    'program' => $post['program'] ?? null,
                    'no_sep' => $getSEP['NOMOR'],
                    'dokter' => $post['dokter'] ?? null,
                    'terapis' => $post['terapis'] ?? null,
                    'oleh' => $this->session->userdata['id'],
                ];

                if ($post['id_kunjungan']) {
                    $this->KlaimRehabKunjunganModel->ubah($post['id_kunjungan'], $data); // ubah
                } else {
                    $this->KlaimRehabKunjunganModel->simpan($data); // simpan
                }
                // akhir data kunjungan

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
            } else {
                $result = [
                    'status' => 'failed',
                    'errors' => $this->form_validation->error_array()
                ];
            }

            // echo '<pre>';print_r($result);exit();
            echo json_encode($result);
        }
    }

    public function tabelKunjungan()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'];
        $draw = intval($post['draw']);
        $no = $post['start'];
        $tabel = $this->KlaimRehabKunjunganModel->ambil($id);
        $disabled = null;
        $status = null;
        $smf = $this->session->userdata('smf');
        $tblVerifikasi = null;
        // echo '<pre>';print_r($tabel);exit();

        foreach ($tabel as $t) {
            // Mulai periksa status
            if ($t->status == 0) {
                $disabled = 'disabled';
                $status = '<p class="text-danger">Dibatalkan</p>';
            } elseif ($t->status == 1) {
                $disabled = null;
                $status = 'Diterima';
            } elseif ($t->status == 2) {
                $disabled = 'disabled';
                $status = '<p class="text-success">Diverifikasi</p>';
            }
            // Akhir periksa status

            // Mulai tombol verifikasi
            if ($smf == 29) {
                $tblVerifikasi = "<button type='button' class='btn btn-sm btn-primary waves-effect tbl-verifikasi-kunjungan-klaim-rehab' data-id='" . $t->id . "' $disabled>
                                            <i class='fa fa-check'></i> Verifikasi
                                        </button>";
            } else {
                $tblVerifikasi = null;
            }
            // Akhir tombol verifikasi

            $data[] = [
                ++$no . '.',
                $t->program,
                date('d-m-Y', strtotime($t->tanggal)),
                $t->dokter != '   ' ? $t->dokter : '-',
                $t->terapis != '   ' ? $t->terapis : '-',
                $status,
                "<div class='btn-group-vertical' role='group'>
                    " . $tblVerifikasi . "
                    <button type='button' href='#modal-kunjungan-klaim-rehab' class='btn btn-sm btn-primary waves-effect tbl-ubah-kunjungan-klaim-rehab' data-toggle='modal' data-id='" . $t->id . "' $disabled>
                        <i class='fa fa-eye'></i> Lihat
                    </button>
                    <button type='button' href='#modal-batal-kunjungan-klaim-rehab' class='btn btn-sm btn-danger waves-effect tbl-batal-kunjungan-klaim-rehab' data-toggle='modal' data-id='" . $t->id . "' $disabled>
                        <i class='fa fa-window-close'></i> Batal
                    </button>
                </div>",
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->KlaimRehabKunjunganModel->hitung_semua($id),
            'recordsFiltered' => $this->KlaimRehabKunjunganModel->hitung_tersaring($id),
            'data' => $data
        ];

        echo json_encode($output);
    }

    public function batalKunjungan()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        $id = $post['id'];
        // echo '<pre>';print_r($id);exit();

        $data = ['status' => 0];
        $this->KlaimRehabKunjunganModel->ubah($id, $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
        } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
        }
        echo json_encode($result);
    }

    public function verifikasiKunjungan()
    {
        $this->db->trans_begin();
        $post = $this->input->post();
        $id = $post['id'];
        // echo '<pre>';print_r($id);exit();

        $data = ['status' => 2];
        $this->KlaimRehabKunjunganModel->ubah($id, $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
        } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
        }
        echo json_encode($result);
    }
}

/* End of file KlaimRehab.php */
/* Location: ./application/controllers/rehabilitasiMedik/KlaimRehab.php */