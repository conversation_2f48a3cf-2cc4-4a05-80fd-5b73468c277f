<?php
defined('BASEPATH') or exit('No direct script access allowed');
class ResumeKematian extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','rekam_medis/rawat_inap/resumeKematian/ResumeKematianModel'));
  }

    public function index(){
      $nokun = $this->uri->segment(2);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $getDataPasien = $this->ResumeKematianModel->getDataPasien($nokun)->row_array();
  
      $data = array(
        'getNomr' => $getNomr,
        'getDataPasien' => $getDataPasien,
        'nokun' => $nokun,
        'listDrUmum' => $this->masterModel->listDrUmum(),
        'jenis_kelamin' => $this->masterModel->referensi(969),
        'listHubunganKepalaKeluarga' => $this->masterModel->referensi(1733),
        'listTempatMeninggals' => $this->masterModel->referensi(1734),
        'listStatusJenazah' => $this->masterModel->referensi(1735),
        'listKualifikasiPemeriksa' => $this->masterModel->referensi(1736),
        'listDasarDiagnosis' => $this->masterModel->referensi(1737),
        'listKelompokPenyebabKematian' => $this->masterModel->referensi(1738),
      );
        $this->load->view('rekam_medis/rawat_inap/resumeKematian/index', $data);
    }

    public function simpanResumKematian()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $dataResumeKematian = array (
      'nokun'                       => $post['nokun'],
      'no_urut'                     => $post['urutKematian'],
      'hub_kepala_rumahtangga'      => $post['hubResumeKematian'],
      'waktu_meninggal'             => $post['tanggalWaktuKematian'],
      'tempat_meninggal'            => $post['TempatMeninggalPasien'],
      'tempat_meninggal_isian'      => $post['TempatMeninggalPasienDeskripsi'],
      'status_jenazah'              => $post['statusJenazahResumeKematian'],
      'status_jenazah_waktu'        => $post['statusJenazahResumeKematianWaktu'],
      'nama_pemeriksa'              => $post['namaPemeriksaResumeKematian'],
      'kualifikasi_pemeriksa'       => $post['kualifikasiPemeriksaResumeKematian'],
      'waktu_pemeriksaan'           => $post['waktuPemeriksaan'],
      'dasar_diagnosis'             => implode(',',$post['dasarDiagnosisResumeKematian']),
      'dasar_diagnosis_isian'       => $post['dasarDiagnosisResumeKematianDeskripsi'],
      'kelompok_penyebab_kematian'  => $post['penyebabResumeKematian'],
      'nama_pihak_keluarga'         => $post['nama_pihakMenerima'],
      'ttd_pihak_keluarga'          => file_get_contents($this->input->post('sign_PihakMenerima')),
      'dokter'                      => $post['nama_DokterMenerangkan'],
      'ttd_dokter'                  => file_get_contents($this->input->post('sign_DokterMenerangkan')),
      'oleh'                        => $this->session->userdata('id'),
      'status'                      => 1,
    );
    // echo "<pre>";print_r($dataResumeKematian);echo "</pre>";

    $this->ResumeKematianModel->simpanResumeKematian($dataResumeKematian);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyResumeKematian()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listResumeKematian = $this->ResumeKematianModel->listResumeKematian($nomr);

    $data = array();
    $no = 1;
    foreach ($listResumeKematian->result() as $lrk) {
        if($lrk->status == 1){
            $status = 'Aktif';
        }else{
            $status = 'Non-Aktif';
        }
      $data[] = array(
        $no,
        $lrk->nokun,
        $lrk->DOKTERPELAKSANA,
        $lrk->OLEH_PEMBUAT,
        $lrk->created_at,
        // $status,
        '<a href="#modalresumeKematian" class="btn btn-danger btn-block" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$lrk->id.'"><i class="fas fa-edit"></i> Non-Aktif </a>
        <a href="#cetak" class="btn btn-success btn-block item_cetak" data-toggle="modal" data-backdrop="static" data-keyboard="false" id="item_cetak" data="'.$lrk->id.'"><i class="fas fa-file-alt"></i> Cetak </a>'
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listResumeKematian->num_rows(),
      "recordsFiltered" => $listResumeKematian->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalResumeKematian()
  {
    $id = $this->input->post('id');
    $gpResumeKematian = $this->ResumeKematianModel->getResumeKematian($id);
    $nokun = $gpResumeKematian['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $getDataPasien = $this->ResumeKematianModel->getDataPasien($nokun)->row_array();
    $explode_dasar_diagnosis = explode(',' , $gpResumeKematian['dasar_diagnosis']);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'explode_dasar_diagnosis'       => $explode_dasar_diagnosis,
      'gpResumeKematian' => $gpResumeKematian,
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
      'getDataPasien' => $getDataPasien,
      'listHubunganKepalaKeluarga' => $this->masterModel->referensi(1733),
      'listTempatMeninggals' => $this->masterModel->referensi(1734),
      'listStatusJenazah' => $this->masterModel->referensi(1735),
      'listKualifikasiPemeriksa' => $this->masterModel->referensi(1736),
      'listDasarDiagnosis' => $this->masterModel->referensi(1737),
      'listKelompokPenyebabKematian' => $this->masterModel->referensi(1738),
    );

    $this->load->view('rekam_medis/rawat_inap/resumeKematian/view_non', $data);
  }

  public function nonaktifResumeKematian()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    $post = $this->input->post();

    $dataResumeKematian_edit = array (
      'status'                => 0
    );

     // echo "<pre>";print_r($dataResumeKematian_edit);echo "</pre>";exit();

    $this->ResumeKematianModel->updateResumeKematian($dataResumeKematian_edit,$id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}
?>