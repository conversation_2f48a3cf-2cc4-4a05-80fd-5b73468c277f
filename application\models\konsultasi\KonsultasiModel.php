<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KonsultasiModel extends MY_Model
{
  protected $_table_name = 'medis.tb_konsul';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = [
    'nokun' => [
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Ang<PERSON>',
      ]
    ],

    'tujuan' => [
      'field' => 'tujuan[]',
      'label' => 'Pilih Tujuan',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s Wajib Diisi.',
      ]
    ],

    'jenis_konsultasi' => [
      'field' => 'jenis_konsultasi',
      'label' => '<PERSON>is Konsultasi',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>ka',
      ]
    ],

    'jenis_rawat' => [
      'field' => 'jenis_rawat',
      'label' => 'Jenis Rawat',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Angka',
      ]
    ],
  ];

  public function insertFormKonsul($data)
  {
    $this->db->insert('medis.tb_konsul', $data);
    return $this->db->insert_id();
  }

  public function tampilHistoryKonsul($nomr)
  {
    $this->db->select(
      "kon.id ID_KONSUL, master.getNamaLengkapPegawai(dok1.NIP) DOKTER_PENGIRIM, kon.tanggal, smf.DESKRIPSI SMF, p.*,
      kon.dokter_tujuan id_dokter_tujuan, master.getNamaLengkapPegawai(dok2.NIP) DOKTER_TUJUAN, r.DESKRIPSI RUANGAN,
      kon.status STATUS, jaw.dokter_jawab, dok3.ID DOKTER_JAWAB, master.getNamaLengkapPegawai(dok3.NIP) DOKTER_PENJAWAB,
      IF(kon.status = 1, 'Konsul Sudah Dikirim', IF(kon.status = 2, 'Konsul Sudah Dijawab', 'Konsul Dibatalkan')) STATUS_KONSUL"
    );
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('master.dokter dok1', 'dok1.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.JENIS = 26', 'left');
    $this->db->join('master.dokter dok2', 'dok2.ID = kon.dokter_tujuan', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan r', 'r.ID = kon.tujuan_ruangan', 'left');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = jaw.dokter_jawab', 'left');
    $this->db->join('master.dokter dok3', 'dok3.NIP = peng.NIP', 'left');
    $this->db->where('p.NORM', $nomr);
    $this->db->group_by('kon.id');
    $this->db->order_by('kon.tanggal', 'DESC');

    $query = $this->db->get();
    return $query->result_array();
  }

  public function modalHistoryKonsul($id_konsul)
  {
    $this->db->select(
      "jaw.id ID_JAWAB, kon.id ID_KONSUL, kon.kunjungan, kon.dokter_pengirim id_dokter_pengirim,
      master.getNamaLengkapPegawai(dok1.NIP) DOKTER_PENGIRIM, kon.tanggal,
      master.getNamaLengkapPegawai(dok2.NIP) DOKTER_TUJUAN, r.DESKRIPSI RUANGAN, kon.oleh id_pengirim,
      kon.dokter_tujuan id_dokter_tujuan, kon.smf, kon.sumber_sediaan, r.ID ID_RUANGAN, vcit.variabel CITO,
      kon.isian_lanjutan, kon.diagnosis_kerja, kon.ikhtisar_klinis, kon.konsul_yang_diminta, kon.penilaian_kasus,
      vpen.id_variabel penilaian_kasus_desc, smf.DESKRIPSI, kon.status, jaw.diagnosa, jaw.anjuran,
      jaw.persetujuan_kasus, jaw.dokter_jawab, jaw.tanggal_update tanggal_jawab, jaw.penemuan,
      IF(kon.status = 1, 'Konsul Sudah Dikirim', IF(kon.status = 2, 'Konsul Sudah Dijawab', 'Konsul Dibatalkan')) STATUS_KONSUL,
      master.getNamaLengkapPegawai(dok3.NIP) DOKTER_PENJAWAB"
    );
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('master.dokter dok1', 'dok1.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.JENIS = 26', 'left');
    $this->db->join('master.dokter dok2', 'dok2.ID = kon.dokter_tujuan', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan r', 'r.ID = kon.tujuan_ruangan', 'left');
    $this->db->join('db_master.variabel vcit', 'vcit.id_variabel = kon.cito', 'left');
    $this->db->join('db_master.variabel vpen', 'vpen.id_variabel = kon.penilaian_kasus', 'left');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = jaw.dokter_jawab', 'left');
    $this->db->join('master.dokter dok3', 'dok3.NIP = peng.NIP', 'left');
    $this->db->where('kon.id', $id_konsul);

    $query = $this->db->get();
    return $query->result_array();
  }

  public function updateHistoryKonsul($where, $data, $table)
  {
    $this->db->where($where);
    $this->db->update($table, $data);
  }

  public function tampilJawabKonsul()
  {
    $query = $this->db->query(
      "CALL medis.TampilJawabKonsul(" . $this->uri->segment(4) . ")"
    );
    mysqli_next_result($this->db->conn_id);
    return $query->result_array();
  }

  public function jumlahNotifikasiKonsul($id_pengguna)
  {
    $query = $this->db->query(
      "CALL medis.JumlahNotifikasiKonsul(" . $id_pengguna . ", " . $this->session->userdata('smf') . ")"
    );
    return $query->num_rows();
  }

  public function notifikasiKonsul($id_pengguna)
  {
    $query = $this->db->query(
      "CALL medis.NotifikasiKonsul(" . $id_pengguna . ", " . $this->session->userdata('smf') . ")"
    );
    return $query->result();
  }

  public function tabelVerifikasi($id_pengguna, $param)
  {
    if ($param == 'tabel') {
      $this->db->select(
        "peng1.ID ID_PENGGUNA, master.getNamaLengkapPegawai(doktu.NIP) NAMA_TUJUAN,
        master.getNamaLengkapPegawai(dokpe.NIP) NAMA_PENGIRIM,
        master.getNamaLengkapPegawai(dokja.NIP) NAMA_PENJAWAB, kon.id ID_KONSUL, p.NORM NORM,
        master.getNamaLengkap(p.NORM) PASIEN, kon.tanggal TANGGAL_KONSUL, smftuju.DESKRIPSI SMF,
        IF(kon.dokter_tujuan = 0, 'Tujuan ke SMF', 'Tujuan ke Dokter') JENIS_KONSUL,
        IF(kon.dokter_tujuan = 0, CONCAT('(SMF) ', smftuju.DESKRIPSI), master.getNamaLengkapPegawai(doktu.NIP)) TUJUAN"
      );
    } elseif ($param == 'jumlah') {
      $this->db->select('kon.id');
    }
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id');
    $this->db->join('aplikasi.pengguna peng1', 'peng1.ID = jaw.dokter_jawab', 'left');
    $this->db->join('master.dokter dokja', 'dokja.NIP = peng1.NIP', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.dokter doktu', 'doktu.ID = kon.dokter_tujuan', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = doktu.NIP', 'left');
    $this->db->join('aplikasi.pengguna peng2', 'peng2.NIP = doktu.NIP', 'left');
    $this->db->join('master.dokter dokpe', 'dokpe.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.referensi smftuju', 'smftuju.ID = kon.SMF AND smftuju.JENIS = 26', 'left');
    $this->db->where('jaw.status', 2);
    $this->db->where('jaw.verifikasi', 0);
    $this->db->where('jaw.atas_persetujuan !=', null);
    $this->db->where('dokja.ID != doktu.ID');
    $this->db->where('peng2.ID', $id_pengguna);
    $this->db->order_by('jaw.tanggal', 'DESC');

    $query = $this->db->get();
    if ($param == 'tabel') {
      return $query->result();
    } elseif ($param == 'jumlah') {
      return $query->num_rows();
    }
  }

  public function jumlahNotifPerPasien($id_pengguna, $nomr)
  {
    $this->db->select('kon.id');
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('master.dokter dok1', 'dok1.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.JENIS = 26', 'left');
    $this->db->join('master.dokter dok2', 'dok2.ID = kon.dokter_tujuan', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan r', 'r.ID = kon.tujuan_ruangan', 'left');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.NIP = dok2.NIP', 'left');
    $this->db->where('p.NORM', $nomr);
    $this->db->where('peng.ID', $id_pengguna);
    $this->db->where('kon.status', 1);
    $this->db->order_by('kon.tanggal', 'DESC');
    $query = $this->db->get();
    return $query->num_rows();
  }

  public function konsulTerjawab($id_pengguna)
  {
    $this->db->select(
      "IF(kon.dokter_tujuan = 0, 'Tujuan ke SMF', 'Tujuan ke Dokter') JENIS_KONSUL, peng.ID ID_PENGGUNA,
      master.getNamaLengkapPegawai(doktu.NIP) NAMA_TUJUAN, master.getNamaLengkapPegawai(dokpe.NIP) NAMA_PENGIRIM,
      kon.id ID_KONSUL, p.NORM, master.getNamaLengkap(p.NORM) PASIEN, kon.tanggal TANGGAL_KONSUL,
      jaw.tanggal TANGGAL_JAWAB,
      IF(kon.dokter_tujuan = 0, CONCAT('(SMF) ', smftuju.DESKRIPSI), master.getNamaLengkapPegawai(doktu.NIP)) TUJUAN,
      smftuju.DESKRIPSI SMF"
    );
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.dokter doktu', 'doktu.ID = kon.dokter_tujuan', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = jaw.dokter_jawab', 'left');
    $this->db->join('master.dokter dok3', 'dok3.NIP = peng.NIP', 'left');
    $this->db->join('master.dokter dokpe', 'dokpe.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = doktu.NIP', 'left');
    $this->db->join('master.referensi smftuju', 'smftuju.ID = kon.SMF AND smftuju.JENIS = 26', 'left');
    $this->db->where('peng.ID', $this->session->userdata('id'));
    $this->db->order_by('jaw.tanggal', 'DESC');

    $query = $this->db->get();
    return $query->result_array();
  }

  public function modalJawabKonsul($id_konsul)
  {
    $this->db->select(
      "jaw.id ID_JAWAB, kon.id ID_KONSUL, kon.kunjungan nokun, p.NORM NORM, master.getNamaLengkap(p.NORM) PASIEN,
      master.getNamaLengkapPegawai(dok1.NIP) DOKTER_PENGIRIM, kon.tanggal, kon.isian_lanjutan,
      master.getNamaLengkapPegawai(dok2.NIP) DOKTER_TUJUAN, master.getNamaLengkapPegawai(dok3.NIP) DOKTER_PENJAWAB,
      r.DESKRIPSI RUANGAN, kon.dokter_pengirim, kon.dokter_tujuan id_dokter_tujuan, kon.smf, kon.sumber_sediaan, r.ID ID_RUANGAN, vcit.variabel CITO, kon.diagnosis_kerja, kon.ikhtisar_klinis, kon.konsul_yang_diminta, kon.penilaian_kasus, vpen.variabel penilaian_kasus_desc, smf.DESKRIPSI, kon.status, jaw.backtime,
      IF(kon.status = 1, 'Konsul Sudah Dikirim', IF(kon.status = 2, 'Konsul Sudah Dijawab', 'Konsul Dibatalkan')) STATUS_KONSUL,
      jaw.tanggal_update tanggal_jawab, jaw.dokter_jawab, jaw.penemuan, jaw.diagnosa, jaw.anjuran, jaw.persetujuan_kasus, jaw.atas_persetujuan"
    );
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id', 'left');
    $this->db->join('master.dokter dok1', 'dok1.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.JENIS = 26', 'left');
    $this->db->join('master.dokter dok2', 'dok2.ID = kon.dokter_tujuan', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan r', 'r.ID = kon.tujuan_ruangan', 'left');
    $this->db->join('db_master.variabel vcit', 'vcit.id_variabel = kon.cito', 'left');
    $this->db->join('db_master.variabel vpen', 'vpen.id_variabel = kon.penilaian_kasus', 'left');
    $this->db->join('aplikasi.pengguna peng3', 'peng3.ID = jaw.dokter_jawab', 'left');
    $this->db->join('master.dokter dok3', 'dok3.NIP = peng3.NIP', 'left');
    $this->db->where('kon.id', $id_konsul);

    $query = $this->db->get();
    return $query->result_array();
  }

  public function formJawabKonsul($id_konsul)
  {
    $this->db->select(
      "jaw.id ID_JAWAB, kon.id ID_KONSUL, pdes.NORM NORM, master.getNamaLengkap(pdes.NORM) PASIEN,
      master.getNamaLengkapPegawai(dok1.NIP) DOKTER_PENGIRIM, kon.tanggal,
      master.getNamaLengkapPegawai(dok2.NIP) DOKTER_TUJUAN, r.DESKRIPSI RUANGAN, kon.dokter_pengirim,
      kon.isian_lanjutan, kon.dokter_tujuan id_dokter_tujuan, kon.smf, kon.sumber_sediaan, r.ID ID_RUANGAN,
      vcit.variabel CITO, kon.diagnosis_kerja, kon.ikhtisar_klinis, kon.konsul_yang_diminta, kon.penilaian_kasus,
      vpen.variabel penilaian_kasus_desc, smf.DESKRIPSI, kon.status, jaw.backtime, jaw.tanggal_update tanggal_jawab,
      jaw.dokter_jawab, jaw.penemuan, jaw.diagnosa, jaw.anjuran, jaw.persetujuan_kasus, jaw.verifikasi,
      master.getNamaLengkapPegawai(dok3.NIP) DOKTER_PENJAWAB,
      IF(kon.status = 1, 'Konsul Sudah Dikirim', IF(kon.status = 2, 'Konsul Sudah Dijawab', 'Konsul Dibatalkan')) STATUS_KONSUL,
      IF(IF(kon.tujuan = 1, medis.getNokunTerakhir(pdes.NORM, kon.dokter_tujuan, kon.tanggal_update),
      medis.getNokunTerakhirSMF(pdes.NORM, kon.smf, kon.tanggal_update)) IS NULL, kon.kunjungan,
      IF(kon.tujuan = 1, medis.getNokunTerakhir(pdes.NORM, kon.dokter_tujuan, kon.tanggal_update),
      medis.getNokunTerakhirSMF(pdes.NORM, kon.smf, kon.tanggal_update))) NOKUN_TERAKHIR, ruangan_aktif.DESKRIPSI RUANGAN_AKTIF,
      IF(IF(kon.tujuan = 1, medis.getNokunTerakhir(pdes.NORM, kon.dokter_tujuan, kon.tanggal_update),
      medis.getNokunTerakhirSMF(pdes.NORM, kon.smf, kon.tanggal_update)) IS NULL,
      'Konsultasi dari ruangan yang sama', 'Konsultasi dari ruangan yang berbeda') STATUS_KONSUL"
    );
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id', 'left');
    $this->db->join('master.dokter dok1', 'dok1.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.JENIS = 26', 'left');
    $this->db->join('master.dokter dok2', 'dok2.ID = kon.dokter_tujuan', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = jaw.dokter_jawab', 'left');
    $this->db->join('master.dokter dok3', 'dok3.NIP = peng.NIP', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran pdes', 'pdes.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan r', 'r.ID = kon.tujuan_ruangan', 'left');
    $this->db->join('db_master.variabel vcit', 'vcit.id_variabel = kon.cito', 'left');
    $this->db->join('db_master.variabel vpen', 'vpen.id_variabel = kon.penilaian_kasus', 'left');
    $this->db->join(
      'pendaftaran.kunjungan kunfur',
      'kunfur.NOMOR = IF(IF(kon.tujuan = 1, medis.getNokunTerakhir(pdes.NORM, kon.dokter_tujuan, kon.tanggal_update), medis.getNokunTerakhirSMF(pdes.NORM, kon.smf, kon.tanggal_update)) IS NULL, kon.kunjungan, IF(kon.tujuan=1, medis.getNokunTerakhir(pdes.NORM, kon.dokter_tujuan, kon.tanggal_update), medis.getNokunTerakhirSMF(pdes.NORM, kon.smf, kon.tanggal_update)))',
      'left'
    );
    $this->db->join('master.ruangan ruangan_aktif', 'ruangan_aktif.ID = kunfur.RUANGAN', 'left');
    $this->db->where('kon.id', $id_konsul);

    $query = $this->db->get();
    return $query->result_array();
  }

  public function insertJawabKonsul($data)
  {
    $this->db->insert('medis.tb_konsul_jawab', $data);
    return $this->db->insert_id();
  }

  public function updateJawabKonsul($where, $data, $table)
  {
    $this->db->where($where);
    $this->db->update($table, $data);
  }

  // Ambil ID dokter
  public function idDokter($dokter)
  {
    $this->db->select('d.ID, mp.SMF');
    $this->db->from('master.dokter d');
    $this->db->join('aplikasi.pengguna ap', 'ap.NIP = d.NIP');
    $this->db->join('master.pegawai mp', 'mp.NIP = d.NIP', 'left');
    $this->db->where('ap.ID', $dokter);
    $query = $this->db->get();
    return $query->row_array();
  }

  //  Ambil ID pengguna dokter
  public function idPenggunaDokter($dokter)
  {
    $this->db->select('p.ID');
    $this->db->from('aplikasi.pengguna p');
    $this->db->join('master.dokter d', 'd.NIP = p.NIP');
    $this->db->where('d.ID', $dokter);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function belumDijawab($smf)
  {
    $this->db->select(
      "kon.id ID_KONSUL, peng.LOGIN, peng.ID ID_PENGGUNA_DOKTER_PENGIRIM,
      master.getNamaLengkapPegawai(dok1.NIP) DOKTER_PENGIRIM, kon.tanggal, kon.smf ID_SMF_TUJUAN,
      smf.DESKRIPSI SMF_TUJUAN, peng2.ID ID_PENGGUNA_DOKTER_TUJUAN,
      master.getNamaLengkapPegawai(dok2.NIP) DOKTER_TUJUAN, peg2.SMF SMF_LOGIN, smftuju.DESKRIPSI SMF_LOGIN,
      r.DESKRIPSI RUANGAN, IF(kon.tujuan = 1, 'true', 'false')"
    );
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('master.dokter dok1', 'dok1.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.JENIS = 26', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.NIP = dok1.NIP', 'left');
    $this->db->join('master.dokter dok2', 'dok2.ID = kon.dokter_tujuan', 'left');
    $this->db->join('master.pegawai peg2', 'peg2.NIP = dok2.NIP', 'left');
    $this->db->join('master.referensi smftuju', 'smftuju.ID = peg2.SMF AND smftuju.JENIS = 26', 'left');
    $this->db->join('aplikasi.pengguna peng2', 'peng2.NIP = dok2.NIP', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan r', 'r.ID = kon.tujuan_ruangan', 'left');
    $this->db->where('kon.status', 1);
    $this->db->where('smf.ID', $smf);
    $this->db->order_by('kon.tanggal', 'DESC');

    $query = $this->db->get();
    return $query->result_array();
  }

  public function sudahDijawab($smf)
  {
    $this->db->select(
      "kon.id ID_KONSUL, master.getNamaLengkapPegawai(dok1.NIP) DOKTER_PENGIRIM, kon.tanggal, smf.DESKRIPSI SMF, p.NAMA,
      kon.dokter_tujuan id_dokter_tujuan, master.getNamaLengkapPegawai(dok2.NIP) DOKTER_TUJUAN, r.DESKRIPSI RUANGAN,
      kon.status STATUS, jaw.dokter_jawab, dok3.ID DOKTER_JAWAB, master.getNamaLengkapPegawai(dok3.NIP) DOKTER_PENJAWAB"
    );
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('master.dokter dok1', 'dok1.ID = kon.dokter_pengirim', 'left');
    $this->db->join('master.dokter dok2', 'dok2.ID = kon.dokter_tujuan', 'left');
    $this->db->join('master.pegawai p', 'p.NIP = dok2.NIP', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.ID = p.SMF AND smf.JENIS = 26', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan r', 'r.ID = kon.tujuan_ruangan', 'left');
    $this->db->join('medis.tb_konsul_jawab jaw', 'jaw.id_konsul = kon.id', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = jaw.dokter_jawab', 'left');
    $this->db->join('master.dokter dok3', 'dok3.NIP = ap.NIP', 'left');
    $this->db->where('kon.status', 2);
    $this->db->where('smf.ID', $smf);
    $this->db->order_by('kon.tanggal', 'DESC');

    $query = $this->db->get();
    return $query->result_array();
  }
}

/* End of file KonsultasiModel.php */
/* Location: ./application/models/konsultasi/KonsultasiModel.php */