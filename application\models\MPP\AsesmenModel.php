<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AsesmenModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_asesmen_mpp';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  var $tabel = 'keperawatan.tb_asesmen_mpp a';
  var $urutan_kolom = array(null, 'tanggal', 'waktu', 'catatan', 'pengisi', 'created_at', 'status', null);
  var $pencarian_kolom = array('tanggal', 'waktu', 'catatan', 'pengisi', 'created_at', 'status');
  var $urutan = array('tanggal' => 'desc', 'waktu', 'desc');

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s <PERSON>ajib <PERSON>.',
        'numeric' => '%s <PERSON>ajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_asesmen_mpp', $data);
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_asesmen_mpp.id', $id);
    $this->db->update('keperawatan.tb_asesmen_mpp', $data);
  }

  public function history($nomr)
  {
    $this->db->select(
      'a.id, a.tanggal, a.waktu, a.catatan, master.getNamaLengkapPegawai(ap.NIP) pengisi, a.created_at, a.status'
    );
    $this->db->from($this->tabel);
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = a.oleh', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = a.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('p.NORM', $nomr);

    $i = 0;
    foreach ($this->pencarian_kolom as $pk) { // Loop kolom
      $_POST['search']['value'] = (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) ? $_POST['search']['value'] : '';

      if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
        if ($i === 0) { // Loop pertama
          $this->db->group_start();
          $this->db->like($pk, $_POST['search']['value']);
        } else {
          $this->db->or_like($pk, $_POST['search']['value']);
        }

        if (count($this->pencarian_kolom) - 1 == $i) { // Loop terakhir
          $this->db->group_end(); // Tutup kurung
        }
      }
      $i++;
    }

    if (isset($_POST['order'])) { // Pemrosesan order
      $this->db->order_by($this->urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
    } elseif (isset($this->urutan)) {
      $urutan = $this->urutan;
      $this->db->order_by(key($urutan), $urutan[key($urutan)]);
    }
  }

  public function ambilTabel($nomr)
  {
    $this->history($nomr);
    $_POST['length'] = (isset($_POST['length']) && $_POST['length'] < 1) ? '10' : $_POST['length'];

    if (isset($_POST['start']) && $_POST['start'] > 1) {
      $_POST['start'] = $_POST['start'];
    }

    $this->db->limit($_POST['length'], $_POST['start']);
    // print_r($_POST);die;
    $query = $this->db->get();
    return $query->result();
  }

  public function hitungTersaring($nomr)
  {
    $this->history($nomr);
    $query = $this->db->get();
    return $query->num_rows();
  }

  public function hitungSemua($nomr)
  {
    $this->db->from($this->tabel);
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = a.oleh', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = a.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('p.NORM', $nomr);
    return $this->db->count_all_results();
  }

  public function detail($id)
  {
    $this->db->select('id, nokun, tanggal, waktu, catatan');
    $this->db->from('keperawatan.tb_asesmen_mpp');
    $this->db->where('id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file AsesmenModel.php */
/* Location: ./application/model/MPP/AsesmenModel.php */