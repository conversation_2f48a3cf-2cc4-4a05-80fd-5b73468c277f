<?php
defined('BASEPATH') or exit('No direct script access allowed');

class UKDDModel extends MY_Model
{
  protected $_table_name = 'medis.tb_ukdd';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('medis.tb_ukdd', $data);
    return $this->db->insert_id();
  }

  public function simpanGambar($data)
  {
    $this->db->insert('medis.tb_gbr_dd_payudara', $data);
  }

  public function ubah($id, $data)
  {
    $this->db->where('medis.tb_ukdd.id', $id);
    $this->db->update('medis.tb_ukdd', $data);
  }

  public function ubahGambar($id, $data)
  {
    $this->db->where('medis.tb_gbr_dd_payudara.id_ukdd', $id);
    $this->db->update('medis.tb_gbr_dd_payudara', $data);
  }

  public function jumlah($nomr)
  {
    $this->db->select('ukdd.id');
    $this->db->from('medis.tb_ukdd ukdd');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ukdd.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = ukdd.oleh', 'left');
    $this->db->join('medis.tb_dd_payudara dp', 'dp.id_ukdd = ukdd.id', 'left');
    $this->db->join('aplikasi.pengguna perawat', 'perawat.ID = dp.perawat', 'left');
    $this->db->join('aplikasi.pengguna dokter', 'dokter.ID = dp.dokter', 'left');
    $this->db->where('pp.NORM', $nomr);
    $query = $this->db->get();
    return $query->num_rows();
  }

  public function cekTerbaru($nokun)
  {
    $this->db->select('ukdd.id');
    $this->db->from('medis.tb_ukdd ukdd');
    $this->db->join('medis.tb_dd_payudara dp', 'dp.id_ukdd = ukdd.id', 'left');
    $this->db->where('dp.dokter IS NULL', null, false);
    $this->db->where('ukdd.status', 1);
    $this->db->where('dp.status', 1);
    $this->db->order_by('ukdd.id', 'DESC');
    $query = $this->db->get();
    return $query->row_array();
  }

  public function history($nomr)
  {
    $this->db->select(
      'ukdd.id, ukdd.tanggal, ukdd.waktu, master.getNamaLengkapPegawai(ap.NIP) pengirim,
      master.getNamaLengkapPegawai(perawat.NIP) perawat, master.getNamaLengkapPegawai(dokter.NIP) dokter, ukdd.status'
    );
    $this->db->from('medis.tb_ukdd ukdd');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ukdd.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = ukdd.oleh', 'left');
    $this->db->join('medis.tb_dd_payudara dp', 'dp.id_ukdd = ukdd.id', 'left');
    $this->db->join('aplikasi.pengguna perawat', 'perawat.ID = dp.perawat', 'left');
    $this->db->join('aplikasi.pengguna dokter', 'dokter.ID = dp.dokter', 'left');
    $this->db->where('pp.NORM', $nomr);
    $this->db->order_by('ukdd.id', 'DESC');
    return $this->db->get();
  }

  public function gambar($id)
  {
    $this->db->select('gdp.id, ukdd.nokun, gdp.pemeriksaan');
    $this->db->from('medis.tb_gbr_dd_payudara gdp');
    $this->db->join('medis.tb_ukdd ukdd', 'ukdd.id = gdp.id_ukdd', 'left');
    $this->db->where('gdp.status', 1);
    $this->db->where('ukdd.id', $id);
    $this->db->order_by('gdp.created_at', 'desc');
    $query = $this->db->get();
    return $query->row_array();
  }

  public function detail($id)
  {
    $this->db->select(
      'ukdd.id, ukdd.nokun, master.getNamaLengkapPegawai(perawat.NIP) perawat,
      master.getNamaLengkapPegawai(dokter.NIP) dokter, ukdd.tanggal, ukdd.waktu, ukdd.keluhan, ukdd.riwayat_penyakit,
      ukdd.diabetes_mellitus, ukdd.pen_diabetes_mellitus, ukdd.hipertensi, ukdd.pen_hipertensi, ukdd.penyakit_jantung,
      ukdd.pen_jantung, ukdd.merokok, ukdd.lama_merokok, ukdd.mulai_merokok, ukdd.sampai_merokok, ukdd.perokok_pasif,
      ukdd.karsinogen, ukdd.polusi, ukdd.rumah_sehat, ukdd.suka_alkohol, ukdd.riwayat_hepatitis,
      ukdd.riwayat_hepatitis_keluarga, ukdd.pen_hepatitis, ukdd.pernah_operasi, ukdd.ket_pernah_operasi,
      ukdd.pernah_transfusi, ukdd.penyalahgunaan_obat, ukdd.overweight, ukdd.aktifitas_fisik, ukdd.diet_rendah, 
      ukdd.makan_daging, ukdd.makan_diasap, ukdd.polip_usus, ukdd.infeksi_usus, ukdd.bab_berubah, ukdd.lama_bab_berubah,
      ukdd.bab_berdarah, ukdd.riwayat_kanker_keluarga, ukdd.sindroma_metabolik, ukdd.pen_kanker, ukdd.hpht, ukdd.hari_hpht,
      ukdd.jenis_hpht, ukdd.kawin, ukdd.usia_menikah, ukdd.keluhan_vagina, ukdd.keputihan, ukdd.pendarahan_spontan,
      ukdd.pendarahan_senggama, ukdd.paps_smear_tidak_normal, ukdd.vagina_touch_check, ukdd.vagina_touch_text,
      ukdd.colok_dubur_check, ukdd.colok_dubur_text, ukdd.rpp, ukdd.ket_rpp, dp.risk_1, dp.risk_2, dp.risk_3,
      dp.risk_4, dp.risk_5, dp.risk_6, dp.risk_7, dp.risk_8, dp.risk_9, dp.risk_10, dp.risk_11, dp.risk_12,
      dp.risk_13, dp.risk_14, dp.risk_15, dp.ket_risk_15, dp.risk_16, dp.nilai_risk, dp.gejala_1, dp.gejala_2,
      dp.gejala_3, dp.gejala_4, dp.gejala_5, dp.gejala_6, dp.gejala_7, dp.gejala_8, dp.gejala_9, dp.gejala_10,
      dp.gejala_10, dp.gejala_11, dp.gejala_12, dp.gejala_13, dp.gejala_14, dp.gejala_15, dp.gejala_16, dp.gejala_17,
      dp.gejala_18, dp.simetris, dp.benjolan_terlihat, dp.lokasi_terlihat, dp.arah_terlihat, dp.kulit, dp.areola_papilla,
      dp.benjolan_teraba, dp.lokasi_teraba, dp.arah_teraba, dp.jumlah_benjolan_teraba, dp.panjang_benjolan,
      dp.lebar_benjolan, dp.tinggi_benjolan, dp.permukaan, dp.konsistensi, dp.batas, dp.mobilisasi, dp.nyeri_tekan,
      dp.kgb_aksila_teraba, dp.kgb_aksila_soliter_multipel, dp.kgb_supraklavikula_teraba,
      dp.kgb_supraklavikula_soliter_multipel, ukdd.keadaan_umum, ukdd.keadaan_gizi, ukdd.habitus, tbb.tb, tbb.bb,
      dp.imt, tv.suhu, tv.nadi, tv.td_sistolik, tv.td_diastolik, tv.pernapasan, ukdd.tiroid, ukdd.getah_bening,
      ukdd.jantung, ukdd.paru, ukdd.inspeksi, ukdd.perut_palpasi, ukdd.perkusi, ukdd.auskultasi, ukdd.reflek_fisiologis,
      master.getNamaLengkapPegawai(ap.NIP) pengirim, ukdd.status, dp.payudara_text'
    );
    $this->db->from('medis.tb_ukdd ukdd');
    $this->db->join('medis.tb_dd_payudara dp', 'dp.id_ukdd = ukdd.id', 'left');
    $this->db->join('db_pasien.tb_tb_bb tbb', 'tbb.ref = ukdd.id AND tbb.data_source = 23', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'tv.ref = ukdd.id AND tv.data_source = 23', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ukdd.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = ukdd.oleh', 'left');
    $this->db->join('aplikasi.pengguna perawat', 'perawat.ID = dp.perawat', 'left');
    $this->db->join('aplikasi.pengguna dokter', 'dokter.ID = dp.dokter', 'left');
    $this->db->where('ukdd.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function tabelMenuDeteksiDini($mulai, $selesai)
  {
    $query = $this->db->query("CALL keperawatan.PasienDeteksiDini('" . $mulai . "', '" . $selesai . "')");
    return $query;
  }
}

/* End of file UKDDModel.php */
/* Location: ./application/models/emr/deteksiDini/UKDDModel.php */