<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianAferesis extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','bankdarah/PengkajianAferesisModel'));
    }

    public function index(){
        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(5)),
            'skriningResikoJatuhPusing' => $this->masterModel->referensi(120),
            'skriningResikoJatuhBerdiri' => $this->masterModel->referensi(121),
            'skriningResikoJatuh6Bulan' => $this->masterModel->referensi(122),
            'skriningNyeri' => $this->masterModel->referensi(7),
            'skalaNyeriNRS' => $this->masterModel->referensi(114),
            'skalaNyeriWBR' => $this->masterModel->referensi(115),
            'skalaNyeriFLACC' => $this->masterModel->referensi(123),
            'skalaNyeriBPS' => $this->masterModel->referensi(133),
            'jenis_pasien_aferesis' => $this->masterModel->referensi(775),
            'riwayat_alergi_aferesis' => $this->masterModel->referensi(774),
            'riwayat_penyakit_aferesis' => $this->masterModel->referensi(777),
            'golongan_darah' => $this->masterModel->referensi(756),
            'rh_darah' => $this->masterModel->referensi(757),
            'riwayat_donor' => $this->masterModel->referensi(759),
            'jenis_donor' => $this->masterModel->referensi(760),
            'kesadaran_aferesis' => $this->masterModel->referensi(762),
            'akses_veskuler' => $this->masterModel->referensi(763),
            'jenis_akses' => $this->masterModel->referensi(764),
            'pola_napas' => $this->masterModel->referensi(765),
            'suara_napas' => $this->masterModel->referensi(766),
            'nadi' => $this->masterModel->referensi(767),
            'ctr' => $this->masterModel->referensi(768),
            'akral' => $this->masterModel->referensi(769),
            'psikososial_aferesis' => $this->masterModel->referensi(770),
            'edukasi' => $this->masterModel->referensi(771),
            'masalah_keperawatan_aferesis' => $this->masterModel->referensi(772),
        );

        $this->load->view('Pengkajian/bankdarah/pengkajianAferesis',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->PengkajianAferesisModel->rules;
                $this->form_validation->set_rules($rules);

                if($this->input->post('jenis_pasien_aferesis') == 2701){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->pendonor);
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->riwayat_alergi_pendonor);
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->riwayat_penyakit_pendonor);
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->jenis_donor);
                }elseif($this->input->post('jenis_pasien_aferesis') == 2702){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->pasien);
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->riwayat_alergi_pasien);
                }

                if($this->input->post('kesadaran_aferesis') == 2664){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->kesadaran);
                }

                if(in_array('2669',$this->input->post('jenis_akses'))){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->perifer);
                }

                if($this->input->post('nadi_aferesis') == 2677){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->frekuensi);
                }

                if($this->input->post('skrining_nyeri') != 17){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->nyeri);
                }

                if(in_array('2689',$this->input->post('edukasi'))){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->edukasi);
                }

                if(in_array('2696',$this->input->post('masalah_keperawatan'))){
                    $this->form_validation->set_rules($this->PengkajianAferesisModel->masalah_keperawatan);
                }

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPengkajianAferesis = array(
                        'kunjungan' => $post['nokun'],
                        'jenis_pasien' => $post['jenis_pasien_aferesis'],

                        'golongan_darah' => $post['jenis_pasien_aferesis'] == 2701 ? $post['golongan_darah'] : $post['golongan_darah_pasien'],
                        'rh_darah' => $post['jenis_pasien_aferesis'] == 2701 ? $post['rh_darah'] : $post['rh_darah_pasien'],
                        'riwayat_alergi' => $post['jenis_pasien_aferesis'] == 2701 ? $post['riwayat_alergi_aferesis'] : $post['riwayat_alergi_aferesis_pasien'],
                        'jelaskan_riwayat_alergi' => $post['jenis_pasien_aferesis'] == 2701 ? $post['alergi_aferesis_desc'] : $post['alergi_aferesis_pasien_desc'],
                        'riwayat_penyakit' => isset($post['riwayat_penyakit_aferesis']) ? $post['riwayat_penyakit_aferesis']: null,
                        'jelaskan_riwayat_penyakit' => isset($post['alergi_penyakit_desc']) ? $post['alergi_penyakit_desc'] : null,
                        'riwayat_donor_darah' => isset($post['riwayat_donor']) ? $post['riwayat_donor']: null,
                        'jenis_donor_darah' => isset($post['jenis_donor']) ? $post['jenis_donor']: null,
                        'kapan_donor_darah' => isset($post['kapan_donor']) ? $post['kapan_donor']: null,
                        'riwayat_donor_aferesis' => isset($post['riwayat_aferesis']) ? $post['riwayat_aferesis']: null,

                        'keluhan_utama' => isset($post['keluhan_utama']) ? $post['keluhan_utama']: null,
                        'riwayat_penyakit_dahulu' => isset($post['riwayat_penyakit_dahulu']) ? $post['riwayat_penyakit_dahulu']: null,
                        'riwayat_penyakit_saat_ini' => isset($post['riwayat_penyakit_saat_ini']) ? $post['riwayat_penyakit_saat_ini']: null,
                        'obat_kemoterapi' => isset($post['obat_kemoterapi']) ? $post['obat_kemoterapi']: null,

                        'e' => $post['e_gcs'],
                        'm' => $post['m_gcs'],
                        'v' => $post['v_gcs'],
                        'gcs' => $post['total_gcs'],
                        'kesadaran' => $post['kesadaran_aferesis'],
                        'kesadaran_lainnya' => isset($post['kesadaran_aferesis_desc']) ? $post['kesadaran_aferesis_desc']: null,
                        'tekanan_darah' => $post['sistolik'],
                        'per_tekanan_darah' => $post['distolik'],
                        'pernapasan' => $post['pernapasan'],
                        'nadi' => $post['nadi'],
                        'suhu' => $post['suhu'],
                        'tinggi_badan' => $post['tb'],
                        'berat_badan' => $post['bb'],
                        'akses_vaskuler' => $post['akses_veskuler'],
                        'jenis_akses' => json_encode($post['jenis_akses']),
                        'lokasi' => isset($post['lokasi_akses']) ? $post['lokasi_akses']: null,
                        'no_jarum' => isset($post['no_jarum']) ? $post['no_jarum']: null,
                        
                        'pola_napas' => $post['pola_napas'],
                        'suara_napas' => $post['suara_napas'],
                        'sirkulasi_nadi' => $post['nadi_aferesis'],
                        'frekuensi' => isset($post['frekuensi_aferesis']) ? $post['frekuensi_aferesis'] : "" ,
                        'ctr' => $post['ctr'],
                        'akral' => $post['akral'],
                        'metode' => $post['skrining_nyeri'],
                        'skala' => $post['skrining_nyeri'] != 17 ? $post['skor_nyeri'] : "",

                        'hb' => $post['hb'],
                        'leko' => $post['leko'],
                        'tr' => $post['tr'],
                        'eri' => $post['eri'],
                        'ht' => $post['ht'],
                        'hbsag' => $post['hbsag'],
                        'anti_hcv' => $post['antihcv'],
                        'anti_hiv' => $post['antihiv'],
                        'vdrl' => $post['vdrl'],
                        'cd34+' => $post['cd34'],
                        'ureum' => $post['ureum'],
                        'creatinin' => $post['creatinin'],
                        'prototal' => $post['prototal'],
                        'albumin' => $post['albumin'],
                        'globulin' => $post['globulin'],
                        'sgot' => $post['sgot'],
                        'sgpt' => $post['sgpt'],
                        'na' => $post['na'],
                        'k' => $post['k'],
                        'ch' => $post['ch'],
                        'mg' => $post['mg'],
                        'ca' => $post['ca'],
                        'lain_lain' => $post['lain'],

                        'pusing' => $post['skrining_resiko_jatuh_pusing_aferesis'],
                        'berdiri' => $post['skrining_resiko_jatuh_berdiri_aferesis'],
                        'jatuh' => $post['skrining_resiko_jatuh_6bulan_aferesis'],
                        'psikososial' => json_encode($post['psikososial']),
                        'edukasi' => json_encode($post['edukasi']),
                        'edukasi_lain' => isset($post['edukasi_aferesis_desc']) ? $post['edukasi_aferesis_desc']: null,
                        'masalah_keperawatan' => json_encode($post['masalah_keperawatan']),
                        'masalah_keperawatan_lain' => isset($post['masalasah_keperawatan_aferesis_desc']) ? $post['masalasah_keperawatan_aferesis_desc']: null,

                        'oleh' => $this->session->userdata("id"),
                    );

                    $this->db->replace('keperawatan.tb_pengkajian_aferesis',$dataPengkajianAferesis);


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambilAferesis'){
    			$post = $this->input->post(NULL,TRUE);
                $dataPengkajianAferesis = $this->PengkajianAferesisModel->get($post['nokun'], true);
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataPengkajianAferesis
                ));
            }else if($param == 'count'){
                $result = $this->PengkajianAferesisModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->PengkajianAferesisModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_pengkajian_aferesis" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PengkajianAferesisModel->total_count(),
            "recordsFiltered"   => $this->PengkajianAferesisModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}