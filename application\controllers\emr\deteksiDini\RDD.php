<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RDD extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/TbBbModel',
        'rekam_medis/TandaVitalModel',
        'emr/deteksiDini/RDDModel',
        'emr/deteksiDini/RDDSitologiModel',
        'emr/deteksiDini/RDDHistologiModel',
        'emr/deteksiDini/RDDRadiologiModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel'
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(7);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $pasien['NORM'];

    // Mendapatkan data riwayat kesehatan
    $data_riwayat = $this->RDDModel->getRiwayatKesehatan($nokun);
    // echo '<pre>'; print_r($data_riwayat); exit();

    $riwayat_keluarga = [];
    $faktor_risiko = [];

    if (isset($data_riwayat)) {
      if ($data_riwayat->diabetes_mellitus == 1081) {
        $riwayat_keluarga[] = 'Diabetes Melitus' . (!empty($data_riwayat->pen_diabetes_mellitus) ? ' (' . $data_riwayat->pen_diabetes_mellitus . ')' : null);
      }
      if ($data_riwayat->hipertensi == 1082) {
        $riwayat_keluarga[] = 'Hipertensi' . (!empty($data_riwayat->pen_hipertensi) ? ' (' . $data_riwayat->pen_hipertensi . ')' : null);
      }
      if ($data_riwayat->penyakit_jantung == 1085) {
        $riwayat_keluarga[] = 'Jantung' . (!empty($data_riwayat->pen_jantung) ? ' (' . $data_riwayat->pen_jantung . ')' : null);
      }

      if ($data_riwayat->usia50 == 4686) $faktor_risiko[] = 'Usia lebih dari 50 tahun';
      if ($data_riwayat->merokok == 1087) {
        $faktor_risiko[] = 'Merokok' . (!empty($data_riwayat->lama_merokok) ? ' (' . $data_riwayat->lama_merokok . ' tahun, dari tahun ' . $data_riwayat->mulai_merokok . ' sampai ' . $data_riwayat->sampai_merokok . ')' : null);
      }
      if ($data_riwayat->perokok_pasif == 1089) $faktor_risiko[] = 'Perokok Pasif';
      if ($data_riwayat->karsinogen == 6109) $faktor_risiko[] = 'Terpapar Zat Karsinogen';
      if ($data_riwayat->polusi == 6111) $faktor_risiko[] = 'Tinggal di lingkungan dengan tingkat polusi yang tinggi';
      if ($data_riwayat->rumah_sehat == 6113) $faktor_risiko[] = 'Rumah tidak sehat';
      if ($data_riwayat->suka_alkohol == 1091) $faktor_risiko[] = 'Suka minum alkohol';
      if ($data_riwayat->riwayat_hepatitis == 1093) $faktor_risiko[] = 'Riwayat penyakit Hepatitis B/C';
      if ($data_riwayat->riwayat_hepatitis_keluarga == 1095) {
        $faktor_risiko[] = 'Riwayat penyakit Hepatitis B/C di keluarga' . (!empty($data_riwayat->pen_hepatitis) ? ' (' . $data_riwayat->pen_hepatitis . ')' : null);
      }
      if ($data_riwayat->pernah_operasi == 1097) {
        $faktor_risiko[] = 'Pernah operasi' . (!empty($data_riwayat->ket_pernah_operasi) ? ' (' . $data_riwayat->ket_pernah_operasi . ')' : null);
      }
      if ($data_riwayat->pernah_transfusi == 1099) $faktor_risiko[] = 'Pernah mendapatkan transfusi darah';
      if ($data_riwayat->penyalahgunaan_obat == 1101) $faktor_risiko[] = 'Pernah suntik atau menyalahgunakan obat';
      if ($data_riwayat->overweight == 6115) $faktor_risiko[] = 'Over Weight';
      if ($data_riwayat->aktifitas_fisik == 6117) $faktor_risiko[] = 'Kurang Beraktifitas Fisik';
      if ($data_riwayat->diet_rendah == 6119) $faktor_risiko[] = 'Diet rendah serat dan tinggi lemak';
      if ($data_riwayat->makan_daging == 1103) $faktor_risiko[] = 'Makan daging, susu, atau lemak lebih dari 2 hari/minggu';
      if ($data_riwayat->makan_diasap == 1105) $faktor_risiko[] = 'Makan makanan yang diasap atau dipanggang lebih dari 2 hari/minggu';
      if ($data_riwayat->polip_usus == 1107) $faktor_risiko[] = 'Pernah menderita polip usus';
      if ($data_riwayat->infeksi_usus == 1109) $faktor_risiko[] = 'Pernah menderita infeksi usus dalam waktu lama';
      if ($data_riwayat->bab_berubah == 1115) {
        $faktor_risiko[] = 'Pola BAB berubah (diare, susah BAB, konstipasi)' . (!empty($data_riwayat->lama_bab_berubah) ? ' (' . $data_riwayat->lama_bab_berubah . ')' : null);
      }
      if ($data_riwayat->bab_berdarah == 1117) $faktor_risiko[] = 'BAB bercampur darah';
      if ($data_riwayat->riwayat_kanker_keluarga == 1119) {
        $faktor_risiko[] = 'Riwayat penyakit kanker di keluarga' . (!empty($data_riwayat->pen_kanker) ? ' (' . $data_riwayat->pen_kanker . ')' : null);
      }
      if ($data_riwayat->sindroma_metabolik == 6121) $faktor_risiko[] = 'Riwayat penyakit sindroma metabolik';
    }

    $data = [
      'nomr' => $nomr,
      'nokun' => $nokun,
      'pasien' => $pasien,
      'jumlah' => $this->RDDModel->history($nokun, 'jumlah'),
      'pilihanCPPT' => $this->masterModel->referensi(1407),
      'anjuran' => $this->masterModel->referensi(1803),

      // Mulai pemeriksaan penunjang
      'sitologi' => $this->pengkajianAwalModel->sitologi($nomr),
      'histologi' => $this->pengkajianAwalModel->histologi($nomr),
      'tindakanRadiologi' => $this->pengkajianAwalModel->tindakan_rad($nomr),
      // Akhir pemeriksaan penunjang

      // Riwayat Kesehatan
      'keluhan' => $data_riwayat->keluhan ?? null,
      'riwayatPenyakit' => $data_riwayat->riwayat_penyakit ?? null,
      'penyakitKeluarga' => implode(", ", $riwayat_keluarga),
      'risikoKanker' => implode(", ", $faktor_risiko),


      //pemeriksaan khusus
      'payudara_text' => $data_riwayat->payudara_text ?? null,
      'vagina_text'   => $data_riwayat->vagina_touch_text ?? null

    ];

    // print_r($data_riwayat);
    
    // echo '<pre>'; print_r($data); exit();

    $this->load->view('Pengkajian/emr/deteksiDini/RDD/index', $data);
  }


  public function aksi($param)
  {
    // echo'<pre>';print_r($param);exit();
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $post = $this->input->post();
      // echo'<pre>';print_r($post);exit();
      $oleh = $this->session->userdata['id'];
      $status = 1;

      if ($param == 'simpan') {
        // Mulai simpan

        // Mulai rules
        $this->form_validation->set_rules($this->RDDModel->rules());
        $this->form_validation->set_rules($this->TbBbModel->rules());
        $this->form_validation->set_rules($this->TandaVitalModel->rules());
        // Akhir rules

        if ($this->form_validation->run() == true) {
          // Mulai resume hasil pemeriksaan deteksi dini kanker
          $data = [
            'tanggal' => $post['tanggal'] ?? null,
            'waktu' => $post['waktu'] ?? null,
            'keluhan' => $post['keluhan'] ?? null,
            'penyakit_dahulu' => $post['penyakit_dahulu'] ?? null,
            'penyakit_keluarga' => $post['penyakit_keluarga'] ?? null,
            'risiko_kanker' => $post['risiko_kanker'] ?? null,
            'leher' => $post['leher'] ?? null,
            'thorax' => $post['thorax'] ?? null,
            'payudara' => $post['payudara'] ?? null,
            'perut' => $post['perut'] ?? null,
            'ginekologi' => $post['ginekologi'] ?? null,
            'spekulo' => $post['spekulo'] ?? null,
            'vagina' => $post['vagina'] ?? null,
            'fisik_lainnya' => $post['fisik_lainnya'] ?? null,
            'mammografi' => $post['mammografi'] ?? null,
            'usg_payudara' => $post['usg_payudara'] ?? null,
            'pap_smear' => $post['pap_smear'] ?? null,
            'penunjang_lainnya' => $post['penunjang_lainnya'] ?? null,
            'kesimpulan' => $post['kesimpulan'] ?? null,
            'pilihan_anjuran' => isset($post['pilihan_anjuran']) ? json_encode($post['pilihan_anjuran']) : null,
            'konsultasi_1' => $post['konsultasi_1'] ?? null,
            'konsultasi_2' => $post['konsultasi_2'] ?? null,
            'anjuran' => $post['anjuran'] ?? null,
          ];
          // echo '<pre>';print_r($data);exit();
          // Akhir resume hasil pemeriksaan deteksi dini kanker

          // Mulai tinggi dan berat badan
          $dataTBB = [
            'tb' => isset($post['tb']) ? round($post['tb'], 2) : null,
            'bb' => isset($post['bb']) ? round($post['bb'], 2) : null,
          ];
          // echo '<pre>';print_r($dataTBB);exit();
          // Akhir tinggi dan berat badan

          // Mulai tanda vital
          $dataTV = [
            'td_sistolik' => isset($post['td_sistolik']) ? round($post['td_sistolik'], 2) : null,
            'td_diastolik' => isset($post['td_diastolik']) ? round($post['td_diastolik'], 2) : null,
            'nadi' => isset($post['nadi']) ? round($post['nadi'], 2) : null,
            'pernapasan' => isset($post['pernapasan']) ? round($post['pernapasan'], 2) : null,
            'suhu' => isset($post['suhu']) ? round($post['suhu'], 2) : null,
          ];
          // echo '<pre>';print_r($dataTV);exit();
          // Akhir tanda vital

          $dataSource = 42;
          if ($post['id'] != '') {
            // Mulai simpan perubahan
            $idRDD = $post['id'];
            $this->RDDModel->ubah($idRDD, $data);
            $this->OTKeperawatanModel->ubahTbBb($idRDD, $dataSource, $dataTBB);
            $this->OTKeperawatanModel->ubahTandaVital($idRDD, $dataSource, $dataTV);
            // Akhir simpan perubahan

            // Mulai hapus data sitologi, histologi, dan radiologi
            $data = ['status' => 0];
            $this->RDDSitologiModel->ubah($idRDD, $data);
            $this->RDDHistologiModel->ubah($idRDD, $data);
            $this->RDDRadiologiModel->ubah($idRDD, $data);
            // Akhir hapus data sitologi, histologi, dan radiologi
          } else {
            // Mulai simpan baru
            $nokun = isset($post['nokun']) ? $post['nokun'] : null;
            $nomr = isset($post['nomr']) ? $post['nomr'] : null;

            // Mulai simpan resume hasil pemeriksaan deteksi dini kanker
            $data['nokun'] = $nokun;
            $data['oleh'] = $oleh;
            $data['status'] = $status;
            // echo '<pre>';print_r($data);exit();
            $idRDD = $this->RDDModel->simpan($data);
            // Akhir simpan resume hasil pemeriksaan deteksi dini kanker

            // Mulai simpan tinggi dan berat badan
            $dataTBB['data_source'] = $dataSource;
            $dataTBB['ref'] = $idRDD;
            $dataTBB['nomr'] = $nomr;
            $dataTBB['nokun'] = $nokun;
            $dataTBB['oleh'] = $oleh;
            $dataTBB['status'] = $status;
            // echo '<pre>';print_r($dataTBB);exit();
            $this->TbBbModel->insert($dataTBB);
            // Akhir simpan tinggi dan berat badan

            // Mulai simpan tanda vital
            $dataTV['data_source'] = $dataSource;
            $dataTV['ref'] = $idRDD;
            $dataTV['nomr'] = $nomr;
            $dataTV['nokun'] = $nokun;
            $dataTV['oleh'] = $oleh;
            $dataTV['status'] = $status;
            // echo '<pre>';print_r($dataTV);exit();
            $this->TandaVitalModel->simpan($dataTV);
            // Akhir simpan tanda vital

            // Akhir simpan baru
          }

          // Mulai simpan sitologi
          $i = 0;
          $dataSitologi = [];
          if (isset($post['id_sitologi'])) {
            foreach ($post['id_sitologi'] as $s) {
              $dataSitologi[$i] = [
                'id_rdd' => $idRDD,
                'id_sitologi' => $s,
                'status' => $status,
              ];
              $i++;
            }
            // echo '<pre>';print_r($dataSitologi);exit();
            $this->RDDSitologiModel->simpan($dataSitologi);
          }
          // Akhir simpan sitologi

          // Mulai simpan histologi
          $j = 0;
          $dataHistologi = [];
          if (isset($post['id_histologi'])) {
            foreach ($post['id_histologi'] as $h) {
              $dataHistologi[$j] = [
                'id_rdd' => $idRDD,
                'id_histologi' => $h,
                'status' => $status,
              ];
              $j++;
            }
            // echo '<pre>';print_r($dataHistologi);exit();
            $this->RDDHistologiModel->simpan($dataHistologi);
          }
          // Akhir simpan histologi

          // Mulai simpan radiologi
          $k = 0;
          $dataRadiologi = [];
          if (isset($post['id_tindakan_medis'])) {
            foreach ($post['id_tindakan_medis'] as $r) {
              $dataRadiologi[$k] = [
                'id_rdd' => $idRDD,
                'id_tindakan_medis' => $r,
                'status' => $status,
              ];
              $k++;
            }
            // echo '<pre>';print_r($dataRadiologi);exit();
            $this->RDDRadiologiModel->simpan($dataRadiologi);
          }
          // Akhir simpan radiologi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
          } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
          }
        } else {
          $result = ['status' => 'failed', 'errors' => $this->form_validation->error_array()];
        }
        echo json_encode($result);
        // Akhir simpan
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = str_replace('`', '"', $this->RDDModel->detail($post['id'])); // Cari ` lalu ganti dengan " biar jadi JSON
        // echo '<pre>';print_r($data);exit();
        echo json_encode(
          [
            'status' => 'success',
            'data' => $data,
          ]
        );
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = [
      'nokun' => $post['nokun'] ?? null,
      'nomr' => $post['nomr'] ?? null,
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/emr/deteksiDini/RDD/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $nokun = $post['nokun'];
    $nomr = $post['nomr'];
    $history = $this->RDDModel->history($nokun, 'tabel');
    $data = [];
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status = 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = [
        $no++ . '.',
        date('d/m/Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->waktu)),
        $h->pengisi,
        date('d/m/Y, H.i.s', strtotime($h->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-rdd' class='btn btn-sm btn-danger waves-effect tbl-batal-rdd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-ubah-rdd' class='btn btn-sm btn-warning waves-effect tbl-detail-rdd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fas fa-pencil-alt'></i> Ubah
          </button>
          <a href='/reports/simrskd/deteksidini/redis_dedin.php?format=pdf&ID=" . $h->id . "' class='btn btn-sm btn-danger waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak PDF
          </a>
          <a href='/reports/simrskd/deteksidini/redis_dedin.php?format=docx&ID=" . $h->id . "' class='btn btn-sm btn-custom' target='_blank'>
            <i class='fa fa-file-word'></i> File Word
          </a>
        </div>
        <button type='button' href='#modal-penunjang-rdd' class='btn btn-sm btn-primary waves-effect tbl-penunjang-rdd' data-toggle='modal' data-id='" . $h->id . "' data-nomr='" . $nomr . "' >
          <i class='fa-regular fa-file-lines'></i> Penunjang
        </button>",
      ];
    }

    $output = [
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $ref = isset($post['id']) ? $post['id'] : null;
    $dataSource = 42;
    $data = ['status' => 0];

    $this->RDDModel->ubah($ref, $data);
    $this->OTKeperawatanModel->ubahTbBb($ref, $dataSource, $data);
    $this->OTKeperawatanModel->ubahTandaVital($ref, $dataSource, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function penunjang()
  {
    $post = $this->input->post();
    $data = [
      'id' => $post['id'] ?? null,
      'nomr' => $post['nomr'] ?? null,
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/emr/deteksiDini/RDD/penunjang', $data);
  }

  public function tabelPenunjangSitologi()
  {
    $draw = intval($this->input->post('draw'));
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $id = $post['id'];
    $tabel = $this->RDDSitologiModel->penunjang($id);
    $data = [];
    $no = 1;
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel->result() as $t) {
      $data[] = [
        $no++ . '.',
        $t->tanggal,
        $t->no_lab_sitologi,
        "<button class='btn btn-sm btn-custom waves-effect cetak-sitologi-rdd' data-id='" . $t->id_sitologi . "' data-nolab='" . $t->no_lab_sitologi . "'>
          <i class='fa fa-print'></i> Cetak
        </button>"
      ];
    }

    $output = [
      'draw' => $draw,
      'recordsTotal' => $tabel->num_rows(),
      'recordsFiltered' => $tabel->num_rows(),
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function tabelPenunjangHistologi()
  {
    $draw = intval($this->input->post('draw'));
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $id = $post['id'];
    $tabel = $this->RDDHistologiModel->penunjang($id);
    $data = [];
    $no = 1;
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel->result() as $t) {
      $data[] = [
        $no++ . '.',
        $t->tanggal,
        $t->no_lab_histologi,
        "<button class='btn btn-sm btn-custom waves-effect cetak-histologi-rdd' data-id='" . $t->id_histologi . "' data-nolab='" . $t->no_lab_histologi . "'>
          <i class='fa fa-print'></i> Cetak
        </button>"
      ];
    }

    $output = [
      'draw' => $draw,
      'recordsTotal' => $tabel->num_rows(),
      'recordsFiltered' => $tabel->num_rows(),
      'data' => $data
    ];
    echo json_encode($output);
  }
  public function tabelPenunjangRadiologi()
  {
    $draw = intval($this->input->post('draw'));
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $id = $post['id'];
    $tabel = $this->RDDRadiologiModel->penunjang($id);
    $data = [];
    $no = 1;
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel->result() as $t) {
      $data[] = [
        $no++ . '.',
        $t->tanggal,
        $t->nama_tindakan,
        "<button class='btn btn-sm btn-custom waves-effect cetak-radiologi-rdd' data-id='" . $t->id_tindakan_medis . "'>
          <i class='fa fa-print'></i> Cetak
        </button>"
      ];
    }

    $output = [
      'draw' => $draw,
      'recordsTotal' => $tabel->num_rows(),
      'recordsFiltered' => $tabel->num_rows(),
      'data' => $data
    ];
    echo json_encode($output);
  }
}

// End of file RDD.php
// Location: ./application/controllers/emr/deteksiDini/RDD.php