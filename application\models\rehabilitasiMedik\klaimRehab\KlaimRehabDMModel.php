<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KlaimRehabDMModel extends MY_Model
{
    protected $_table = 'medis.tb_klaim_rehab_diagnosis_medis';
    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'diagnosis_medis[]',
                'label' => 'Diagnosis medis (ICD-10)',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert_batch($this->_table, $data);
    }

    function data_tersimpan($id)
    {
        $this->db->select('icd10.CODE, icd10.STR');
        $this->db->from('medis.tb_klaim_rehab_diagnosis_medis krdm');
        $this->db->join(
            'master.mrconso icd10', "icd10.CODE = krdm.diagnosis_medis AND icd10.SAB = 'ICD10_1998'", 'left'
        );
        $this->db->group_by('icd10.CODE');
        $this->db->where('krdm.id_klaim_rehab', $id);
        $query = $this->db->get();
        return $query->result();
    }
}

/* End of file KlaimRehabDMModel.php */
/* Location: ./application/models/rehabilitasiMedik/klaimRehab/KlaimRehabDMModel.php */