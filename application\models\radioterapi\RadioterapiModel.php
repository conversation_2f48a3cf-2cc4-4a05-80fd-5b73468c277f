<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RadioterapiModel extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        // $this->db = $this->load->database('default', true);
    }
    public function listKunjunganRadioterapi($nomr)
    {
        $this->db->select('pk.NOMOR, pk.MASUK, mr.DESKRIPSI');
        $this->db->from('pendaftaran.pendaftaran pp');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOPEN = pp.NOMOR');
        $this->db->join('master.ruangan mr', 'mr.ID = pk.RUANGAN');
        $this->db->where('pp.NORM', $nomr);
        $this->db->where_not_in('mr.JENIS_KUNJUNGAN', [0,4,6,11]);

        return $this->db->get()->result_array(); // Ambil data sebagai array
    }
}
