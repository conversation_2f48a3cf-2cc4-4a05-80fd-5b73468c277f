<?php
class Model_permintaan extends ci_model
{

    public function __construct()
    {
        parent::__construct();
    }

    function Tampil_barang()
    {
        $this->db->select('tm.ID as ID, gu.GUDANG as GUDANG, br.NAMA as BARANG, tm.JUMLAH as JUMLAH');
        $this->db->from('invenumum.tmp_order as tm');
        $this->db->join('invenumum.barang as br', 'br.ID = tm.BARANG');
        $this->db->join('master.ruangan as ru', 'ru.ID = tm.RUANG_ASAL');
        $this->db->join('invenumum.gudang as gu', 'gu.ID_GUDANG = tm.GUDANG');
        $data = $this->db->get();
        return $data;
    }

    function Tampil_barang_detail()
    {

        $query  = "SELECT pd.ID, br.BARANG, pd.JUMLAH, sa.SATUAN
    from invenumum.tmp_order pd
    left join invenumum.barang_master br ON br.ID_BARANG=pd.BARANG
    left join invenumum.satuan_ sa ON sa.ID_SATUAN=br.SATUAN";
        return $this->db->query($query);
    }

    function tampilkan_ruangan()
    {
        $query  = "SELECT * from master.ruangan where JENIS IN (2,3,4,5) AND STATUS=1";
        return $this->db->query($query);
    }

    // SEMENTARA QUERY INI DITUTUP AGAR BAGIAN BISA TERBUKA SEMUA
    // function tampilkan_ruanganuser()
    // {
    //     $query  = "SELECT mi.ID, mi.DESKRIPSI
    // FROM invenumum.user_inventory ui
    // LEFT JOIN invenumum.master_instalasi mi on mi.ID=ui.UNIT
    // where ui.USER=" . $this->session->userdata('id') . "
    // ";
    //     return $this->db->query($query);
    // }

    // INI QUERY SEMENTARA UNTUK NAMA BAGIAN
    function tampilkan_ruanganuser()
    {
        $query  = "SELECT ui.ID, ui.DESKRIPSI
        FROM master.ruangan ui
        where ui.`STATUS`=1 AND JENIS IN(2,3,4,5)
        order by ui.DESKRIPSI ASC";
        return $this->db->query($query);
    }

    function tampilkan_gudang()
    {
        $query  = "SELECT * from master.ruangan where ID like '%1060602%'";
        return $this->db->query($query);
    }

    function hapusitem($id)
    {
        $this->db->where('t_detail_id', $id);
        $this->db->delete('transaksi_detail');
    }


    function Simpan_detail_barang($table, $data)
    {
        return $this->db->insert($table, $data);
    }

    function get_subkategori($id)
    {
        $hasil = $this->db->query("SELECT bg.ID_BARANG_GUDANG, bm.BARANG,IF(bg.STOK=0,CONCAT(' - ',bg.STOK),'') STOK
     FROM invenumum.barang_gudang bg
     LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
     WHERE bg.GUDANG='$id' AND bg.`STATUS`=1 AND bm.`STATUS`=1 ORDER BY bm.BARANG ASC");
        return $hasil->result();
    }

    public function getbaranggudang($id)
    {
        $this->db->select('bg.ID_BARANG_GUDANG, bm.BARANG,bg.STOK');
        $this->db->from('invenumum.barang_gudang bg');
        $this->db->join('invenumum.barang_master bm', 'bm.ID_BARANG=bg.BARANG', 'left');
        $this->db->where(array('bg.GUDANG' => $id, 'bg.STATUS' => 1, 'bm.STATUS' => 1));
        $this->db->order_by('bm.BARANG');
        $this->db->limit(20);
        if ($this->input->get('q')) {
            $this->db->like('bm.BARANG', $this->input->get('q'));
        }
        $query  = $this->db->get();
        return $query->result_array();
    }

    function get_subkategori_nama($id)
    {
        $hasil = $this->db->query("
        SELECT bg.ID_BARANG_GUDANG, bm.BARANG
        FROM invenumum.barang_gudang bg
        LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
        WHERE bg.ID_BARANG_GUDANG='$id' AND bg.`STATUS`=1 ORDER BY bm.BARANG ASC ");
        return $hasil->result();
    }

    function get_satuan($id)
    {
        $hasil = $this->db->query("SELECT bm.BARANG, sa.SATUAN
        from invenumum.barang_gudang bg
        left join invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
        left join invenumum.satuan_ sa ON sa.ID_SATUAN=bm.SATUAN
        where bg.ID_BARANG_GUDANG='$id'");
        return $hasil->result();
    }

    function get_id_gudang($id)
    {
        $hasil = $this->db->query("SELECT * FROM master.ruangan mr
        WHERE mr.ID=$id");
        return $hasil->result();
    }

    // public function datapermintaan()
    // {
    //     $query = $this->db->query("SELECT pr.NOMOR, pr.TANGGAL_PERMINTAAN TANGGAL, ru.DESKRIPSI RUANG_ASAL, rtu.DESKRIPSI TUJUAN, pr.STATUS
    //     FROM invenumum.permintaan pr
    //     left join invenumum.master_instalasi ru ON ru.ID=pr.ASAL
    //     left join master.ruangan rtu ON rtu.ID=pr.TUJUAN 
    //     where pr.OLEH=" . $this->session->userdata('id') . " AND pr.STATUS !=0
    //     order by pr.TANGGAL DESC");
    //     return $query;
    // }

    public function datapermintaan()
    {
        $query = $this->db->query("SELECT pr.NOMOR, pr.TANGGAL_PERMINTAAN TANGGAL, ru.DESKRIPSI RUANG_ASAL, rtu.DESKRIPSI TUJUAN, pr.STATUS
        FROM invenumum.permintaan pr
        left join master.ruangan ru ON ru.ID=pr.ASAL
        left join master.ruangan rtu ON rtu.ID=pr.TUJUAN 
       where pr.STATUS !=0
        order by pr.TANGGAL DESC");
        return $query;
    }

    public function detail_minta($id)
    {
        $query = "SELECT pr.NOMOR,bg.ID_BARANG_GUDANG, pd.ID ID_DETAIL, bm.BARANG NAMA, round(pd.JUMLAH,0) JUMLAH, sa.SATUAN, pr.TUJUAN, pr.ASAL, round(pgd.JUMLAH,0) REALISASI
        FROM invenumum.permintaan pr 
        left join invenumum.permintaan_detil pd ON pr.NOMOR=pd.PERMINTAAN
        left join master.ruangan mr ON mr.ID=pr.ASAL
        LEFT join invenumum.barang_gudang bg ON bg.ID_BARANG_GUDANG=pd.BARANG
        left join invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
        left join invenumum.satuan_ sa ON bm.SATUAN=sa.ID_SATUAN
        left join invenumum.pengiriman_detil pgd ON pgd.PERMINTAAN_BARANG_DETIL=pd.ID
    where pr.NOMOR=$id AND pr.STATUS !=0 AND pd.STATUS !=0";
        return $this->db->query($query);
    }

    public function stok_gudang($id)
    {
        $query = "SELECT bg.ID_BARANG_GUDANG, bm.BARANG,IF(bg.STOK=0,bg.STOK,'') STOK
    FROM invenumum.barang_gudang bg
    LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
    WHERE bg.ID_BARANG_GUDANG='13' AND bm.`STATUS`=1";
        return $this->db->query($query);
    }

    function get_no_permintaan()
    {
        $q = $this->db->query("SELECT MAX(RIGHT(NOMOR,4)) AS kd_max FROM invenumum.permintaan WHERE DATE(TANGGAL)=CURDATE()");
        $kd = "";
        if ($q->num_rows() > 0) {
            foreach ($q->result() as $k) {
                $tmp = ((int) $k->kd_max) + 1;
                $kd = sprintf("%04s", $tmp);
            }
        } else {
            $kd = "0001";
        }
        date_default_timezone_set('Asia/Jakarta');
        return date('Ymd') . $kd;
    }
}
