<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Tpn extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/TpnModel'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(5);
    $nopen = $this->uri->segment(6);
    $nokun = $this->uri->segment(7);
    $tpnList = $this->TpnModel->tpnList($nomr);

    $data = array(
      'nokun' => $nokun,
      'nopen' => $nopen,
      'nomr' => $nomr,
      'tpnList' => $tpnList,
    );

    $this->load->view('rekam_medis/rawat_inap/tpn/index', $data);
  }

  public function viewBuatOrderTpn()
  {
    $post = $this->input->post();
    $ingredients = $this->TpnModel->ingredients();

    $data = array(
      'nomr' => $post['nomr'],
      'nokun' => $post['nokun'],
      'ingredients' => $ingredients,
    );

    $this->load->view('rekam_medis/rawat_inap/tpn/viewBuatOrderTpn', $data);
  }

  function getIngredients()
  {
    $id=$this->input->post('id');
    $data=$this->TpnModel->namaIngredientsChild($id);
    echo json_encode($data);
  }

  public function getIngredientsChild()
  {
    $result = $this->TpnModel->getChildIng();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function simpanTpn()
  {
    $post = $this->input->post();
    $nomor = $this->pengkajianAwalModel->getTpn();

    $dataTbBb = array(
      'data_source'   => 50,
      'ref'           => $nomor,
      'nomr'          => isset($post['nomr']) ? $post['nomr'] : NULL,
      'nokun'         => isset($post['nokun']) ? $post['nokun'] : NULL,
      'tb'            => isset($post['tbBuatOrderTpn']) ? $post['tbBuatOrderTpn'] : NULL,
      'bb'            => isset($post['bbBuatOrderTpn']) ? $post['bbBuatOrderTpn'] : NULL,
      'lpb'           => isset($post['cetakHasilLpbMostellerVal']) ? $post['cetakHasilLpbMostellerVal'] : NULL,
      'oleh'          => $this->session->userdata('id'),
      'status'        => 1,
    );
    $getIdTbBb = $this->pengkajianAwalModel->simpanTbBb($dataTbBb);

    $data = array(
      'NOMOR'                   => $nomor,
      'NOKUN'                   => isset($post['nokun']) ? $post['nokun'] : NULL,
      'NOMR'                    => isset($post['nomr']) ? $post['nomr'] : NULL,
      'ID_TB_BB'                => $getIdTbBb,
      'KATEGORI'                => isset($post['katBuatOrderTpn']) ? $post['katBuatOrderTpn'] : NULL,
      'DIAGNOSIS'               => isset($post['diagBuatOrderTpn']) ? $post['diagBuatOrderTpn'] : NULL,
      'DURATION'                => isset($post['durBuatOrderTpn']) ? $post['durBuatOrderTpn'] : NULL,
      'ROUTE'                   => isset($post['ruteBuatOrderTpn']) ? $post['ruteBuatOrderTpn'] : NULL,
      'TOTAL_VOLUME'            => isset($post['tvBuatOrderTpn']) ? $post['tvBuatOrderTpn'] : NULL,
      'TOTAL_VOLUME_FOR_AMINO'  => isset($post['totalTpnTiapRowSumHasilVal']) ? $post['totalTpnTiapRowSumHasilVal'] : NULL,
      'TOTAL_VOLUME_FOR_LIPID'  => isset($post['totalLipidTpnTiapRowSumHasilVal']) ? $post['totalLipidTpnTiapRowSumHasilVal'] : NULL,
      'FLOWRATE'                => isset($post['hasilFlowRate']) ? $post['hasilFlowRate'] : NULL,
      'CREATED_BY'              => $this->session->userdata('id'),
      'STATUS'                  => 1,
    );
    $this->db->insert('medis.tb_tpn', $data);

    $dataChild = array();
    $indexChild = 0;
    if (isset($post['nmIngredients'])) {
      foreach ($post['nmIngredients'] as $input) {
        $dataChild = array(
          'NOMOR'                 => $nomor,
          'ID_INGREDIENTS'        => $post['nmIngredients'][$indexChild],
          'ID_INGREDIENTS_CHILD'  => $post['nmIngredientsChild'][$indexChild],
          'DOSE'                  => $post['satuanDose'][$indexChild],
          'SATUAN_DOSE'           => $post['pilihSatuanDose'][$indexChild],
          'TOTAL'                 => $post['totalTpnTiapRow'][$indexChild],
          'AMOUNT_TO_BE_ADDED'    => $post['amountToBeAdded'][$indexChild],
          'CREATED_BY'            => $this->session->userdata('id'),
          'STATUS'                => 1,
        );
        $this->db->insert('medis.tb_tpn_detail', $dataChild);
        $indexChild++;
      }
    }

  }

}