<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProtokolKemo extends CI_Controller
{

	public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        // if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
        //     redirect('login');
        // }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'Farmasi/ProtokolKemoModel'));
        $this->load->library('whatsapp');
    }

public function index()
{
    $cariPasien = $this->ProtokolKemoModel->cariPasien();
    $cariPasienFinal = $this->ProtokolKemoModel->cariPasienFinal();
    $data = array(
          'title' => 'Protokol Kemoterapi',
          'isi'   => 'Pengkajian/protokolKemoterapi/cariPasienProKem',
          'pilihPasien'   => $cariPasien,
          'pilihPasienFinal'   => $cariPasienFinal
      );
    $this->load->view('layout/wrapper',$data);
}

public function dataProkemPasien()
{
     // VARIABLE
    $nomr       = $this->uri->segment(3);
    // $nomr       = $this->input->post('nomr');
    $dataPasien = $this->ProtokolKemoModel->dataDiriPasien($nomr);
    $id_pengguna = $this->session->userdata('id');
    $hProtokolKemo = $this->ProtokolKemoModel->historyProtokolKemoFarmasi($nomr);

    $data = array(
      'title'      => 'Halaman View All Pengkajian',
      'isi' => 'Pengkajian/protokolKemoterapi/farmasi/index',
      'listDr' => $this->masterModel->listDrUmum(),
      'nomr'       => $nomr,
      'dataPasien' => $dataPasien,
      'hProtokolKemo' => $hProtokolKemo,
    );

    $this->load->view('layout/wrapper',$data);
}

public function historyResep()
{
     // VARIABLE
    $nomr       = $this->uri->segment(3);
    $nokun       = $this->uri->segment(4);
    $dataPasien = $this->ProtokolKemoModel->dataDiriPasien($nomr);
    $id_pengguna = $this->session->userdata('id');

    $data = array(
      'title'      => 'Halaman History Eresep',
      'isi' => 'Pengkajian/protokolKemoterapi/farmasi/historyResep',
      'nomr'       => $nomr,
      'nokun'       => $nokun,
      'dataPasien' => $dataPasien,
    );

    $this->load->view('layout/wrapper',$data);
}

public function viewProtokolKemoterapiFarmasi()
{
    $noorder   = $this->input->post('noorder');
    $nomr   = $this->input->post('nomr');
    $dataDetailProKemFarmasi = $this->ProtokolKemoModel->dataDetailProKemFarmasi($noorder);
    $historyTbakFarmasiProKem = $this->ProtokolKemoModel->historyTbakFarmasiProKem($nomr);
    $paketProKem = $this->ProtokolKemoModel->paketProKem($nomr, $noorder);
    $dataDiriPasien = $this->ProtokolKemoModel->dataDiriPasien($nomr);
    $rekapHasilInputFarmasi = $this->ProtokolKemoModel->rekapHasilInputFarmasi($noorder);

    $data = array(
      'noorder'   => $noorder,
      'nomr'   => $nomr,
      'paketProKem'   => $paketProKem,
      'historyTbakFarmasiProKem'   => $historyTbakFarmasiProKem,
      'rekapHasilInputFarmasi'   => $rekapHasilInputFarmasi,
      'dataDiriPasien'   => $dataDiriPasien,
      'listPerawat' => $this->masterModel->listPerawat(),
      'listPegawai' => $this->ProtokolKemoModel->listPegawai(),
      'dataProKem'   => $dataDetailProKemFarmasi
    );

    $this->load->view('Pengkajian/protokolKemoterapi/farmasi/viewProtokolKemoterapiFarmasi',$data);
}

public function rekapObatProKemFarmasi()
{
    $noorder   = $this->input->post('noorder');
    $nomr   = $this->input->post('nomr');
    $paketProKem = $this->ProtokolKemoModel->paketProKem($nomr, $noorder);
    $rekapHasilInputFarmasi = $this->ProtokolKemoModel->rekapHasilInputFarmasi($noorder);

    $data = array(
      'noorder'   => $noorder,
      'nomr'   => $nomr,
      'paketProKem'   => $paketProKem,
      'rekapHasilInputFarmasi'   => $rekapHasilInputFarmasi,
    );

    $this->load->view('Pengkajian/protokolKemoterapi/farmasi/loadDetail/rekapObatProKemFarmasi',$data);
}

public function hapusRekObtFarmasiProkem()
{
  $post = $this->input->post();
  if($post['statusresep'] == 1){
    $data = array(
      'STATUS' => 0,
    );
    $this->db->where('tb_prtkl_kemo_pemberian_dosis.ID', $post['idresep']);
    $this->db->update('keperawatan.tb_prtkl_kemo_pemberian_dosis', $data);
  }else{
    $data = array(
      'status' => 0,
    );
    $this->db->where('tb_prtkl_kemo_pemberian_encer.id_detail_farmasi', $post['idresep']);
    $this->db->update('keperawatan.tb_prtkl_kemo_pemberian_encer', $data);
  }
}

public function simpanResepObatPrtklKemo()
  {
    $post = $this->input->post();
    $kode = $this->pengkajianAwalModel->generateNoOrderResep();
    // $kode = 1;

    $dataOrderResep = array(
      'NOMOR' => $kode,
      'KUNJUNGAN' => $post["kunjungan"],
      'TANGGAL' => date("Y-m-d H:i:s"),
      'DOKTER_DPJP' => $post["dokter"],
      'PEMBERI_RESEP' => $post["pemberiFrmsObtPrtklKemo"],
      'BERAT_BADAN' => $post["bbFrmsObtPrtklKemo"],
      'TINGGI_BADAN' => $post["tbFrmsObtPrtklKemo"],
      'DIAGNOSA' => $post["diagnosaFrmsObtPrtklKemo"],
      'ALERGI_OBAT' => $post["alergiFrmsObtPrtklKemo"],
      'GANGGUAN_FUNGSI_GINJAL' => $post["gangguGjlFrmsObtPrtklKemo"],
      'MENYUSUI' => $post["mnysFrmsObtPrtklKemo"],
      'HAMIL' => $post["hamilFrmsObtPrtklKemo"],
      'RESEP_PASIEN_PULANG' => isset($post["resepFrmsObtPrtklKemo"]) ? $post["resepFrmsObtPrtklKemo"] : 0,
      'TUJUAN' => $post["farmasi_tujuan"],
      'RESEP_CITO' => isset($post['citoFrmsObtPrtklKemo']) ? $post['citoFrmsObtPrtklKemo'] : "0",
      'OLEH' => $this->session->userdata('id'),
    );
    // echo "<pre>"; print_r($dataOrderResep); echo "</pre>";
    $this->db->insert('layanan.order_resep', $dataOrderResep);

    $dataKardek = array();
    $indexKardek = 0;
    if (isset($post['idFrmsObtPrtklKemo'])) {
      foreach ($post['idFrmsObtPrtklKemo'] as $input) {
        if ($post['idFrmsObtPrtklKemo'][$indexKardek] != "") {
          $cekAturanPakai = $this->ProtokolKemoModel->cekAturanPakai($post['aturanPakaiDosisFrmsObtPrtklKemo'][$indexKardek]);
          $aturanPakai = '';
          if($cekAturanPakai->num_rows() > 0)
            {
              if($post['aturanPakaiDosisFrmsObtPrtklKemo'][$indexKardek] != ''){
                $aturanPakai = $post['aturanPakaiDosisFrmsObtPrtklKemo_Id'][$indexKardek];
              }else{
                $aturanPakai = '';
              }
            }else{
              $dataAturanPakai = array(
                'JENIS' => 41,
                'DESKRIPSI' => ltrim($post['aturanPakaiDosisFrmsObtPrtklKemo'][$indexKardek])
              );
              $this->db->insert('master.referensi', $dataAturanPakai);
              // echo "<pre>"; print_r($dataAturanPakai); echo "</pre>";
              $aturanPakai = $this->db->insert_id();
            }

            $dataDetailOrderObat = array(
              'ORDER_ID' => $kode,
              'FARMASI' => $post['brgBrFrmsObtPrtklKemo'][$indexKardek],
              'STOK' => $post['idStkBrFrmsObtPrtklKemo'][$indexKardek],
              'DOSIS' => $post['nmDosisFrmsObtPrtklKemo'][$indexKardek],
              'JUMLAH' => $post['jmlhFrmsObtPrtklKemo'][$indexKardek],
              'ATURAN_PAKAI' => $aturanPakai,
              'KETERANGAN' => $post['ketFrmsObtPrtklKemo'][$indexKardek],
              'KARDEX' => 0,
            );
            // echo "<pre>"; print_r($dataDetailOrderObat); echo "</pre>";
            $this->db->insert('layanan.order_detil_resep', $dataDetailOrderObat);
        }
        $indexKardek++;
      }
    }
  }

public function viewBuatResepPrtklKemo()
{
    $idfarmasi   = $this->input->post('idfarmasi');
    $nokun   = $this->input->post('nokun');
    $listObat = $this->ProtokolKemoModel->rekapHasilInputFarmasi($idfarmasi);
    $dpjpDiagnosaProKem = $this->ProtokolKemoModel->dpjpDiagnosaProKem($idfarmasi);
    $farmasi_eresep = $this->masterModel->farmasi();
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'idfarmasi'   => $idfarmasi,
      'nokun'   => $nokun,
      'pasien'   => $pasien,
      'listObat'   => $listObat,
      'dpjpDiagnosaProKem'   => $dpjpDiagnosaProKem,
      'farmasi_eresep'   => $farmasi_eresep,
      'getTbBbAlergi' => $this->pengkajianAwalModel->getTbBbAlergi($pasien['NORM']),
    );

    $this->load->view('Pengkajian/protokolKemoterapi/farmasi/viewBuatResepPrtklKemo',$data);
}

public function viewInputDosisFarmasiProKem()
{
    $noorder   = $this->input->post('noorder');
    $iddetpk   = $this->input->post('iddetpk');
    $dosis   = $this->input->post('dosis');
    $dataDetailInputDosisFarmasi = $this->ProtokolKemoModel->dataDetailInputDosisFarmasi($noorder, $iddetpk);
    $pasien = $this->pengkajianAwalModel->getNomr($dataDetailInputDosisFarmasi['NOKUN']);
    $getDataFarmasi = $this->ProtokolKemoModel->getHasilInputFarmasi($iddetpk);
    $getArrayObat = $this->ProtokolKemoModel->getArrayObat($iddetpk);
    $listDpjp = $this->masterModel->listDrUmum();

    $data = array(
      'iddetpk'   => $iddetpk,
      'noorder'   => $noorder,
      'dosis'   => $dosis,
      'pasien'   => $pasien,
      'listDpjp'   => $listDpjp,
      'getDataFarmasi'   => $getDataFarmasi,
      'getArrayObat'   => $getArrayObat,
      'prokemInput'   => $dataDetailInputDosisFarmasi,
      'farmasi_prtklkemo' => $this->masterModel->farmasi()
    );

    $this->load->view('Pengkajian/protokolKemoterapi/farmasi/viewInputDosisFarmasiProKem',$data);
}

public function viewUbahDosisObatFarmasiProKem()
{
    $id   = $this->input->post('id');
    $dosis   = $this->input->post('dosis');
    $nmobat   = $this->input->post('nmobat');
    $ketfarmasi   = $this->input->post('ketfarmasi');
    $noorder   = $this->input->post('noorder');

    $data = array(
      'dosis'   => $dosis,
      'nmobat'   => $nmobat,
      'ketfarmasi'   => $ketfarmasi,
      'noorder'   => $noorder,
      'id'   => $id,
    );

    $this->load->view('Pengkajian/protokolKemoterapi/farmasi/viewUbahDosisObatFarmasiProKem',$data);
}

public function viewInputBatchFarmasiProkem()
{
    $idresep   = $this->input->post('idresep');
    $statusresep   = $this->input->post('statusresep');
    $nobatch   = $this->input->post('nobatch');
    $stabilitas   = $this->input->post('stabilitas');
    $tglpenyiapan   = $this->input->post('tglpenyiapan');
    $tglkadaluarsa   = $this->input->post('tglkadaluarsa');
    $nmobat   = $this->input->post('nmobat');
    $getPemberian   = $this->input->post('jalurpemberian');
    $dalam   = $this->input->post('dalam');
    $expdateron   = $this->input->post('expdateron');
    $suhu   = $this->input->post('suhu');
    $kondisipenyimpanan   = $this->input->post('kondisipenyimpanan');
    $jalurPemberian = $this->masterModel->referensi(901);

    $data = array(
      'statusresep'   => $statusresep,
      'idresep'   => $idresep,
      'nobatch'   => $nobatch,
      'stabilitas'   => $stabilitas,
      'tglpenyiapan'   => $tglpenyiapan,
      'tglkadaluarsa'   => $tglkadaluarsa,
      'nmobat'   => $nmobat,
      'getPemberian'   => $getPemberian,
      'expdateron'   => $expdateron,
      'suhu'   => $suhu,
      'kondisipenyimpanan'   => $kondisipenyimpanan,
      'dalam'   => $dalam,
      'jalurPemberian'   => $jalurPemberian,
    );

    $this->load->view('Pengkajian/protokolKemoterapi/farmasi/viewInputBatchFarmasiProkem',$data);
}

public function simpanTbakFarmasiProKem($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $post = $this->input->post();
        $data = array();
        $i = 0;
        if (isset($post['dpjpTbakFarmasiProKem'])) {
          foreach ($post['dpjpTbakFarmasiProKem'] as $input) {
            if ($post['dpjpTbakFarmasiProKem'][$i] != '') {
              array_push(
                $data,
                array(
                  'nokun' => null,
                  'id_protokol_kemo' => $post['idPrtklKemoTbakFarmasiProKem'],
                  'dokter_tbak' => $post['dpjpTbakFarmasiProKem'][$i],
                  'instruksi_tbak' => $post['instruksiTbakFarmasiProKem'][$i],
                  'id_cppt' => null,
                  'created_at' => date('Y-m-d H:i:s'),
                  'status' => '1',
                  'oleh' => $this->session->userdata("id"),
                )
              );
            }
            $i++;
          }
          // echo '<pre>';print_r($data);//exit();
          $this->ProtokolKemoModel->simpan($data);
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');

          if (isset($post['dpjpTbakFarmasiProKem'])) {
            $index = 0;
            $jam = date('H');

            // Mulai menentukan waktu
            if ($jam >= '05' && $jam < '10') {
              $selamat = 'pagi';
            } elseif ($jam >= '10' && $jam < '15') {
              $selamat = 'siang';
            } elseif ($jam >= '15' && $jam < '19') {
              $selamat = 'sore';
            } else {
              $selamat = 'malam';
            }
            foreach ($post['dpjpTbakFarmasiProKem'] as $input) {
              if (isset($post['dpjpTbakFarmasiProKem'][$index])) {
                $dataNomor = $this->masterModel->nomorDokter($post['dpjpTbakFarmasiProKem'][$index]);
                $norm = $post['nomr'];
                $namaPasien = $this->masterModel->getPasien($norm);
                $namaDokter = $this->masterModel->getDokter($post['dpjpTbakFarmasiProKem'][$index]);
                $pengirim = $this->masterModel->getPenguna($this->session->userdata("id"));
                $instruksi = $post['instruksiTbakFarmasiProKem'][$index];
                if (!empty($dataNomor)) {
                  $nomor = '+62' . substr(trim($dataNomor->NOMOR), 1);
                  try {
                    $this->whatsapp->send($nomor, array($selamat . ' ' . $namaDokter, 'kami sampaikan', 'Anda mendapatkan TBAK untuk pasien *' . $norm . ' atas nama ' . $namaPasien . '* dengan isi *' . $instruksi . '* pada tanggal *' . date('d-m-Y') . '* dan pukul *' . date('H:i') . '* oleh *' . $pengirim . '*. Silakan mengecek aplikasi EMR dan klik notifikasi TBAK untuk info lebih lanjut dan verifikasi.'));
                  } catch (Exception $e) {
                                        //throw $th;
                  }
                }
              }
              $index++;
            }
          }

        }
        echo json_encode($result);
      }
    }
  }

public function simpanDosisProKem()
{
    $post = $this->input->post();
    // $pilihObat = explode('|', $this->input->post('pilihNamaObatProKemFarmasi'));
    // $pilihObatDosisPengenceran = explode('|', $this->input->post('pilihNamaObatProKemFarmasiDosis'));
    $jenis = $this->input->post('jenis');
    $farmasi_tujuan_prtklkemo = $this->input->post('farmasi_tujuan_prtklkemo');
    // $farmasi_tujuan_prtklkemodosis = $this->input->post('farmasi_tujuan_prtklkemodosis');
    $noorder = $this->input->post('noorderInputDosisFarmasiProkem');
    $dosisObat = $this->input->post('dosisProKemFarmasi');
    // $dosisObatDosis = $this->input->post('dosisProKemFarmasiDosis');
    $jumlahObat = $this->input->post('jumlahProKemFarmasi');
    $volumeObat = $this->input->post('volumeObatProKemFarmasi');
    // $jumlahObatDosis = $this->input->post('jumlahProKemFarmasiDosis');
    $keteranganPrtklKemoFarmasi = $this->input->post('keteranganPrtklKemoFarmasi');
    $pilihDpjpPrtklKemoFarmasi = $this->input->post('pilihDpjpPrtklKemoFarmasi');
    $iddetpk = $this->input->post('iddetpk');
    $IDPemberian = $this->input->post('IDPemberian');
    $getDataFarmasi = $this->ProtokolKemoModel->getHasilInputFarmasi($iddetpk);
    $oleh = $this->session->userdata("id");

    $dataPemberian = array();
    $indexPemberian = 0;
    if (isset($post['IDPemberian'])) {
      foreach ($post['IDPemberian'] as $input) {
        if ($post['IDPemberian'][$indexPemberian] != "") {
          $pilihObatDosis = explode("|", $post['pilihNamaObatProKemFarmasi'][$indexPemberian]);
          array_push(
            $dataPemberian, array(
              'ID_PRTKL_DETAIL' => $IDPemberian[$indexPemberian],
              'FARMASI_TUJUAN' => $farmasi_tujuan_prtklkemo[$indexPemberian],
              'NAMA_OBAT' => $pilihObatDosis[1],
              'DOSIS' => $dosisObat[$indexPemberian],
              'JUMLAH' => $jumlahObat[$indexPemberian],
              'VOLUME' => $volumeObat[$indexPemberian],
              'OLEH' => $oleh,
            )
          );
        }
        $indexPemberian++;
      }
    }

    $this->db->delete('keperawatan.tb_prtkl_kemo_pemberian_dosis', array('ID_PRTKL_DETAIL' => $post['iddetpk']));
    foreach ($dataPemberian as $key => $value) {
      // print_r($value);
      $this->db->replace('keperawatan.tb_prtkl_kemo_pemberian_dosis', $value, 'ID');
    }
    
    // if(isset($pilihObatDosisPengenceran[1])){
      // $data = array(
      //   'id_detail_farmasi'   => $iddetpk,
      //   'pilih_farmasi_tujuan'   => isset($farmasi_tujuan_prtklkemodosis) ? $farmasi_tujuan_prtklkemodosis : 0,
      //   'noorder'   => $noorder,
      //   'id_barang_ruangan_pengenceran'   => isset($pilihObatDosisPengenceran[1]) ? $pilihObatDosisPengenceran[1] : $getDataFarmasi['ID_OBAT_PENGENCERAN'],
      //   'dosis_pengenceran'   => isset($dosisObatDosis) ? $dosisObatDosis : 0,
      //   'jumlah_pengenceran'   => isset($jumlahObatDosis) ? $jumlahObatDosis : 0,
      //   'keterangan'   => isset($keteranganPrtklKemoFarmasi) ? $keteranganPrtklKemoFarmasi : "",
      //   'pilih_dpjp'   => isset($pilihDpjpPrtklKemoFarmasi) ? $pilihDpjpPrtklKemoFarmasi : 0,
      //   'jenis'   => $jenis,
      //   'oleh'   => $oleh,
      //   'status'   => 1
      // );

      // if($getDataFarmasi != 0){
      //   $this->db->where('id_detail_farmasi', $iddetpk);
      //   $this->db->update('keperawatan.tb_prtkl_kemo_pemberian_encer', $data);
      // }else if($getDataFarmasi == 0){
      //   $this->db->insert('keperawatan.tb_prtkl_kemo_pemberian_encer', $data);
      // }
      
    // }
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    
    
}

public function ubahStatusTerimaFarmasi()
{
    $noorder = $this->input->post('noorder');

    $data = array(
      'STATUS'   => 2
    );

    // echo "<pre>";print_r($data);echo "</pre>";
    $this->db->where('NOMOR', $noorder);
    $this->db->update('medis.tb_prtkl_kemo', $data);
}

public function simpanVerifPrtklKemo()
{
    $noorder = $this->input->post('noorder');
    $data = array(
      'VERIF_FARMASI'   => $this->session->userdata('id'),
      'TANGGAL_VERIF_FARMASI'   => date("Y-m-d H:i:s")
    );

    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->where('NOMOR', $noorder);
    $this->db->update('medis.tb_prtkl_kemo', $data);
}

public function nonAktifStatusObatFarmasiProKem()
{
    $id = $this->input->post('id');

    $data = array(
      'STATUS'   => 0
    );

    $this->db->where('tb_prtkl_kemo_detail.ID', $id);
    $this->db->update('medis.tb_prtkl_kemo_detail', $data);
}

public function batalStatusFarmasi()
{
    $noorder = $this->input->post('noorder');

    $data = array(
      'STATUS'   => 1
    );

    // echo "<pre>";print_r($data);echo "</pre>";
    $this->db->where('NOMOR', $noorder);
    $this->db->update('medis.tb_prtkl_kemo', $data);
}

public function ubahDosisObatFarmasiProKem()
{
    $ubahDosisObatFarmasiProKem = $this->input->post('ubahDosisObatFarmasiProKem');
    $ubahKeteranganObatFarmasiProKem = $this->input->post('ubahKeteranganObatFarmasiProKem');
    $dosis = $this->input->post('dosis');
    $id = $this->input->post('id');

    $data = array(
      'DOSIS_PROTOKOL'          => $ubahDosisObatFarmasiProKem,
      'DOSIS_PROTOKOL_BACKUP'   => $dosis,
      'KETERANGAN_FARMASI'      => $ubahKeteranganObatFarmasiProKem
    );

    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->where('tb_prtkl_kemo_detail.ID', $id);
    $this->db->update('medis.tb_prtkl_kemo_detail', $data);
}

public function ubahStatusFinalFarmasi()
{
    $noorder = $this->input->post('noorder');
    $getDataFarmasi = $this->ProtokolKemoModel->rekapHasilInputFarmasi($noorder);
    $oleh = $this->session->userdata("id");
    
    foreach($getDataFarmasi as $dataRekap)
    {
      $data = array(
        'noorder' => $dataRekap['NO_ORDER'],
        'id_barang_ruangan_obat' => $dataRekap['ID_BARANG_RUANGAN'],
        'dosis' => $dataRekap['DOSIS'],
        'jumlah' => $dataRekap['JUMLAH'],
        'oleh'   => $oleh,
        'status'   => 1
      );
      $this->db->insert('medis.protokolkemo_resep', $data);
    }

    // exit();

    $data = array(
      'STATUS'   => 3,
      'OLEH_FINAL'   => $this->session->userdata("id")
    );

    // echo "<pre>";print_r($data);echo "</pre>";
    $this->db->where('NOMOR', $noorder);
    $this->db->update('medis.tb_prtkl_kemo', $data);
}

public function simpanRiwayatPrtklKemo()
{
  $noorder = $this->input->post('noOrderInputanFarmasi');
  $dateTerima = $this->input->post('protokolTerimaProduksi');
  $dateSelesai = $this->input->post('obtSlesaiDirekPrktlKemo');
  $tglTerima = date('Y-m-d H:i:s', strtotime($dateTerima));
  $tglSelesai = date('Y-m-d H:i:s', strtotime($dateSelesai));
  
  $farmasi1 = $this->input->post('pilihFarmasiInputPrtklKemo1');
  $farmasi2 = $this->input->post('pilihFarmasiInputPrtklKemo2');

  $data = array(
    'PROTOKOL_TERIMA' => $tglTerima,
    'OBAT_SELESAI' => $tglSelesai,
    'FARMASI_1' => $farmasi1,
    'FARMASI_2' => $farmasi2
  );
  // echo "<pre>";print_r($data);echo "</pre>";exit();
  $this->db->where('NOMOR', $noorder);
  $this->db->update('medis.tb_prtkl_kemo', $data);
}

public function UbahStatusCekLabelProtokolKemo()
{
  $this->db->trans_begin();

    $post = $this->input->post();
    $cek = $this->input->post('cek');

    if($cek == "true"){
      $status = 1;
    }else{
      $status = 0;
    }

    if($post['statusresep'] == 1){
      $data = array(
        'STATUS_CETAK_LABEL'              => $status,
      );

      $this->db->where('tb_prtkl_kemo_pemberian_dosis.ID', $post['idresep']);
      $this->db->update('keperawatan.tb_prtkl_kemo_pemberian_dosis', $data);
    }elseif ($post['statusresep'] == 2) {
      $data = array(
        'status_cetak_label'              => $status,
      );

      $this->db->where('tb_prtkl_kemo_pemberian_encer.id_detail_farmasi', $post['idresep']);
      $this->db->update('keperawatan.tb_prtkl_kemo_pemberian_encer', $data);
    }

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success', 'cek' => $status);
    }

    echo json_encode($result);
}

public function simpanInputBatch()
{
  $post = $this->input->post();
  $idresep = $this->input->post('idresep');
  $statusresep = $this->input->post('statusresep');

  $datePenyiapan = $this->input->post('tglPenyiapanPrtklKemoFarmasi');
  $tglPenyiapan = date('Y-m-d H:i:s', strtotime($datePenyiapan));

  $dateKadaluarsa = $this->input->post('tglWaktuExpPrtklKemo');
  $tglKadaluarsa = date('Y-m-d H:i:s', strtotime($dateKadaluarsa));

  $dateExpRekonsitusi = $this->input->post('expDateRekonsitusi');
  $tglExpRekonsitusi = date('Y-m-d', strtotime($dateExpRekonsitusi));

  if($statusresep == 1){
    $data = array(
      'NO_BATCH' => isset($post['noBatchPrtklKemoFarmasi']) ? $post['noBatchPrtklKemoFarmasi'] : "",
      'EXPDATE_REKONSITUSI' => $tglExpRekonsitusi,
      'STABILITAS' => isset($post['stabilitasPrtklKemoFarmasi']) ? $post['stabilitasPrtklKemoFarmasi'] : "",
      'SUHU' => isset($post['suhuProKemFarmasi']) ? $post['suhuProKemFarmasi'] : "",
      'KONDISI_PENYIMPANAN' => isset($post['kondisiPenyimpanan']) ? $post['kondisiPenyimpanan'] : "",
      'TANGGAL_PENYIAPAN' => $tglPenyiapan,
      'TANGGAL_KADALUARSA' => $tglKadaluarsa,
      'JALUR_PEMBERIAN' => isset($post['jalurPemberianProKemFarmasi']) ? $post['jalurPemberianProKemFarmasi'] : null,
      'DALAM' => isset($post['dalamProKemFarmasi']) ? $post['dalamProKemFarmasi'] : '',
    );

    $this->db->where('ID', $idresep);
    $this->db->update('keperawatan.tb_prtkl_kemo_pemberian_dosis', $data);
  }elseif($statusresep == 2){
    $data = array(
      'no_batch' => isset($post['noBatchPrtklKemoFarmasi']) ? $post['noBatchPrtklKemoFarmasi'] : "",
      'expdate_rekonsitusi' => $tglExpRekonsitusi,
      'stabilitas' => isset($post['stabilitasPrtklKemoFarmasi']) ? $post['stabilitasPrtklKemoFarmasi'] : "",
      'suhu' => isset($post['suhuProKemFarmasi']) ? $post['suhuProKemFarmasi'] : "",
      'kondisi_penyimpanan' => isset($post['kondisiPenyimpanan']) ? $post['kondisiPenyimpanan'] : "",
      'tanggal_penyiapan' => $tglPenyiapan,
      'tanggal_kadaluarsa' => $tglKadaluarsa,
      'jalur_pemberian' => isset($post['jalurPemberianProKemFarmasi']) ? $post['jalurPemberianProKemFarmasi'] : null,
      'dalam' => isset($post['dalamProKemFarmasi']) ? $post['dalamProKemFarmasi'] : '',
    );

    $this->db->where('id_detail_farmasi', $idresep);
    $this->db->update('keperawatan.tb_prtkl_kemo_pemberian_encer', $data);
  }else{

  }

}

}