<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CpptTemplateModel extends MY_Model
{
    protected $_table_name = 'keperawatan.tb_cppt_template';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'deskripsi' => array(
            'field' => 'deskripsi',
            'label' => 'Deskripsi',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.'
            ),
        )
    );

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select(
            '*');
        $this->db->from('keperawatan.tb_cppt_template ktcp');

        $this->db->where('ktcp.oleh', $this->session->userdata("id"));
        $this->db->order_by('ktcp.id', 'DESC');

        if ($this->input->post('id')) {
          $this->db->where('ktcp.id', $this->input->post('id'));
      }
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}