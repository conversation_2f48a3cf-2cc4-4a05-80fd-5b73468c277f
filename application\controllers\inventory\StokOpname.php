<?php
defined('BASEPATH') or exit('No direct script access allowed');
class StokOpname extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(23, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('inventory/Model_so'));
    date_default_timezone_set("Asia/Bangkok");
  }

  function index()
  {
    $gudang     = $this->Model_so->tampilkan_gudang()->result();
    $dataso     = $this->Model_so->view();
    $data       = array(
      'title'         => 'Halaman Input Permintaan',
      'isi'           => 'inventory/stokopname/form_stokopname',
      'gudang'        => $gudang,
      'dataso'        => $dataso
    );
    $this->load->view('layout/wrapper', $data);
  }

  public function buat_so()
  {
    $this->form_validation->set_rules('RUANGAN', 'RUANGAN', 'required');
    if ($this->form_validation->run() == FALSE) {
      $this->session->set_flashdata('error', "Data Gagal Di Tambahkan");
      redirect('inventory/StokOpname');
    } else {
      $data = array(
        "TANGGAL" => $_POST['TANGGAL'],
        "GUDANG" => $_POST['RUANGAN'],
        "OLEH"   => $this->session->userdata("id"),
      );

      $id = $this->input->post('RUANGAN');
      $databarang = $this->Model_so->barang_gudang($id)->result_array();
      $idso = $this->Model_so->tambah_so($data);
      foreach ($databarang as $a)
        $data2[] = array(
          'BARANG_RUANGAN' => $a['ID_BARANG_GUDANG'],
          'AWAL' => $a['STOK'],
          'STOK_OPNAME' => $idso,
          'MANUAL' => 0,
        );
      //echo "<pre>";print_r($data2);exit();
      $this->Model_so->create_so($data, $data2);
      $this->session->set_flashdata('sukses', "Data Berhasil Disimpan");
      redirect('inventory/StokOpname');
    }
  }

  public function simpan_so()
  {
    $rules = $this->Model_so->rules;
    $this->form_validation->set_rules($rules);

    if ($this->form_validation->run() == TRUE) {
      $post = $this->input->post();
      $this->db->trans_begin();

      $id_so = $this->input->post('ID_SO');
      $ID_att = $this->input->post('ID');
      //$stok = $this->input->post('STOK');
      $harga  = $this->input->post('HARGA');
      $barang = $this->input->post('BARANG');
      $jumlah = $this->input->post('MANUAL');
      $tglso  = date('Y-m-d');
      $result = array();
      foreach ($barang as $key => $val) {
        $result[] = array(
          "ID" => $ID_att[$key],
          "MANUAL"  => $_POST['MANUAL'][$key],
        );

        $datatr = array(
          'BARANG_RUANGAN'    => $barang[$key],
          'JUMLAH'            => $jumlah[$key],
          'HARGA'             => $harga[$key],
          'STOK'              => $jumlah[$key],
          'JENIS'             => 3,
          'REF'               => $ID_att[$key],
          'TANGGAL_TRANSAKSI' => $tglso
        );
        // echo "<pre>";print_r($stokgudang); exit();
        $this->db->insert('invenumum.transaksi_stok_ruangan', $datatr);
        $insert_idtsr = $this->db->insert_id();
        $stokgudang[] = array(
          "ID_BARANG_GUDANG"        => $barang[$key],
          "STOK"                    => $_POST['MANUAL'][$key],
          "TRANSAKSI_STOK_RUANGAN"  => $insert_idtsr,
        );
      }
      $this->db->update_batch('invenumum.stok_opname_detil', $result, 'ID');
      $this->db->update_batch('invenumum.barang_gudang', $stokgudang, 'ID_BARANG_GUDANG');
      $this->Model_so->update_status($id_so);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
    } else {
      $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    }
    echo json_encode($result);
  }


  public function barang_so($data)
  {
    $id =  $this->uri->segment(4);
    $barang     = $this->Model_so->data($id);
    //$gudang     = $this->Model_so->tampilkan_gudang()->result();	
    $data       = array(
      'title'         => 'Halaman Input SO',
      'isi'           => 'inventory/stokopname/tabel_so',
      'barang'        => $barang
    );
    $this->load->view('layout/wrapper', $data);
  }

  public function batal($data)
  {
    $id =  $this->uri->segment(4);
    //echo "<pre>";print_r($id);exit();
    $this->Model_so->batal_status($id);
    redirect('inventory/StokOpname');
  }

  public function hapus($data)
  {
    $id =  $this->uri->segment(4);
    // echo "<pre>";print_r($id);exit();
    $this->Model_so->hapus_status($id);
    redirect('inventory/StokOpname');
  }

  public function datamodal()
  {
    $id       = $this->input->post('id');
    $barang     = $this->Model_so->data($id);
    $data     = array(
      'barang' => $barang,
    );
    $this->load->view('inventory/stokopname/ModalDetilSO', $data);
  }

  public function updatejumlah()
  {
    // POST values
    $id = $this->input->post('id');
    $field = $this->input->post('field');
    $value = $this->input->post('value');

    // Update records
    $this->Model_so->updatejumlah($id, $field, $value);

    echo 1;
    exit;
  }
}
