<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'login';
$route['404_override'] = '';
$route['translate_uri_dashes'] = false;

$route['pengkajianLuka/index/:num/:num/:num'] = 'luka/PengkajianLuka';
$route['pengkajianGigi/index/:num/:num/:num'] = 'gigi/Pengkajian';
$route['odontogram/index/:num/:num/:num'] = 'gigi/Odontogram';
$route['pemantauanAnastesiGigi/index/:num/:num/:num'] = 'gigi/PemantauanAnastesiGigi';
$route['pengkajianAferesis/index/:num/:num/:num'] = 'bankdarah/PengkajianAferesis';
$route['persetujuanTindakanTerapeutikAferesis/index/:num/:num/:num'] = 'bankdarah/PersetujuanTindakanTerapeutikAferesis';

$route['PengkajianAwalKeperawatan/index/:num/:num/:num/:any/:num'] = 'emr/PengkajianAwalKeperawatan';

$route['PengkajianRawatInap/:num'] = 'PengkajianRawatInap/index';

// Pengkajian
$route['PasienKritis/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PasienKritis';
$route['PengkajianRiim/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRiim';
$route['PengkajianRira/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengkajianRira';
$route['Dewasa/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/Dewasa';
$route['Anak/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/Anak';
$route['Remaja/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/Remaja';
$route['pengkajianRiDewasaMedis/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasa';
$route['PAKPerawat/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PAKPerawat';
$route['PAKMedis/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PAKMedis';
$route['pengkajianRiMedisKritis/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/MedisKritis';
$route['pengkajianRiMedisKritis/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/MedisKritis';
$route['RadioterapiRi/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/RadioterapiRi';
$route['pengkajianTerapiSistemikRJ/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/PengkajianTerapiSistemikRJ/index';
$route['pengkajianTerapiSistemikRJ/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/PengkajianTerapiSistemikRJ/index';

// Keperawatan
$route['PerencanaanAK/:num'] = 'rekam_medis/rawat_inap/keperawatan/perencanaanAK';
$route['OTKeperawatan/:num'] = 'rekam_medis/rawat_inap/keperawatan/OTKeperawatan';
$route['skalaBraden/:num'] = 'rekam_medis/rawat_inap/keperawatan/SkalaBraden';
$route['EWS/:num'] = 'rekam_medis/rawat_inap/keperawatan/EWS';
$route['PEWS/:num'] = 'rekam_medis/rawat_inap/keperawatan/PEWS';
$route['OTKI/:num'] = 'rekam_medis/rawat_inap/keperawatan/OTKI';
$route['ido/(:any)/(:any)'] = 'rekam_medis/rawat_inap/keperawatan/ido/$1/$2';
$route['SkalaOntarioMASS/:num'] = 'geriatri/SkalaOntarioMASS/index';
$route['SkalaOntarioMASS/:num/:num'] = 'geriatri/SkalaOntarioMASS/index';

// Gizi
$route['validasiMalnutrisiDewasa/:num'] = 'rekam_medis/rawat_inap/gizi/ValidasiMalnutrisiDewasa';
$route['penilaianStatusGizi/:num'] = 'rekam_medis/rawat_inap/gizi/PenilaianStatusGizi';
$route['PengkajianGiziLanjutan/:num'] = 'rekam_medis/rawat_inap/gizi/PengkajianGiziLanjutan';
$route['PengkajianGiziLanjutan/:num/:num'] = 'rekam_medis/rawat_inap/gizi/PengkajianGiziLanjutan';
$route['observasiIntakeOralPasien/:num'] = 'rekam_medis/rawat_inap/gizi/ObservasiIntakeOralPasien';
$route['observasiIntakeOralPasien/:num/:num'] = 'rekam_medis/rawat_inap/gizi/ObservasiIntakeOralPasien';

// CPIS
$route['cpis/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/CPIS';
$route['cpis/:num/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/CPIS';

// Kriteria Rawat Intensif
$route['formulirKriteriaRawatIntensif/:num'] = 'igd/FormulirKriteriaRawatIntensif/indexRawatInap';
$route['formulirKriteriaRawatIntensif/:num/:num'] = 'igd/FormulirKriteriaRawatIntensif/indexRawatInap';

// Status Sedasi
$route['statusSedasi/:num'] = 'sedasi/StatusSedasi/indexRawatInap';
$route['statusSedasi/:num/:num'] = 'sedasi/StatusSedasi/indexRawatInap';

// Medikasi Obat
$route['medikasiObat/:num'] = 'sedasi/MedikasiObat/indexRawatInap';
$route['medikasiObat/:num/:num'] = 'sedasi/MedikasiObat/indexRawatInap';

// Medikasi Napas
$route['medikasiNapas/:num'] = 'sedasi/MedikasiNapas/indexRawatInap';
$route['medikasiNapas/:num/:num'] = 'sedasi/MedikasiNapas/indexRawatInap';

// Pemulihan Sedasi
$route['sedasiPemulihan/:num'] = 'sedasi/SedasiPemulihan/indexRawatInap';
$route['sedasiPemulihan/:num/:num'] = 'sedasi/SedasiPemulihan/indexRawatInap';

// Pemulihan Napas
$route['pemulihanNapas/:num'] = 'sedasi/PemulihanNapas/indexRawatInap';
$route['pemulihanNapas/:num/:num'] = 'sedasi/PemulihanNapas/indexRawatInap';

// Pemberian Informasi dan Persetujuan Tindakan Kedokteran (Sedasi)
$route['piptks/:num'] = 'sedasi/PIPTKS/index';
$route['piptks/:num/:num'] = 'sedasi/PIPTKS/index';

// Pemberian Informasi dan Persetujuan Tindakan Kedokteran (Anestesi Umum)
$route['piptkau/:num'] = 'informedConsent/PIPTKAU/index';
$route['piptkau/:num/:num'] = 'informedConsent/PIPTKAU/index';

// Informed Consent
$route['ptk/:num'] = 'informedConsent/persetujuanTindakanKedokteran/indexRawatInap';
$route['ptk/:num/:num'] = 'informedConsent/persetujuanTindakanKedokteran/indexRawatInap';
$route['ptpk/:num'] = 'informedConsent/PT_PengobatanKemoterapi/indexRawatInap';
$route['pttd/:num'] = 'rekam_medis/rawat_inap/informedConsent/PTTD/index';
// Penolakan Tindakan Kendokteran
$route['penolakantindakankedokteran/:num'] = 'informedConsent/PenolakanTindakanKedokteran/index';
$route['penolakantindakankedokteran/:num/:num'] = 'informedConsent/PenolakanTindakanKedokteran/index';
$route['SpinalEpi/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/informedConsent/SpinalEpi';
$route['DNR/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/informedConsent/DNR';
$route['EdukasiTAS/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/informedConsent/EdukasiTAS';

$route['Scan/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/scanberkas/Scan';

//SOFA
$route['sofa/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/Sofa';

// Bundle VAP
$route['bundlevap/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/BundleVAP';
$route['bundlevap/:num/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/BundleVAP';

// Ceklis Edukasi dan Orientasi Pasien Baru di Ruang Perawatan Intensif
$route['ceri/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/CeklisEdukasiRuanganIntensif';
$route['ceri/:num/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/CeklisEdukasiRuanganIntensif';

// Ceklis Edukasi dan Orientasi Pasien Baru Rawat Inap
$route['ceori/:num'] = 'rekam_medis/rawat_inap/transferRuangan/CeklisEdukasiOrientasiRI';
$route['ceori/:num/:num'] = 'rekam_medis/rawat_inap/transferRuangan/CeklisEdukasiOrientasiRI';

// Surveilans Rumah Sakit
$route['surveilans/:num'] = 'rekam_medis/rawat_inap/transferRuangan/Surveilans';
$route['surveilans/:num/:num'] = 'rekam_medis/rawat_inap/transferRuangan/Surveilans';

// Persiapan Ekstubasi
$route['persiapanEkstubasi/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/PersiapanEkstubasi';
$route['persiapanEkstubasi/:num/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/PersiapanEkstubasi';

$route['fasthug/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/Fasthug';
$route['fasthug/:num/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/Fasthug';

// Medis
$route['medis'] = 'rekam_medis/medis/index';
$route['medis/:num'] = 'rekam_medis/medis/index';
$route['medis/dashboard'] = 'rekam_medis/medis/dashboard';

// E-Resep
$route['eresep/:num'] = 'rekam_medis/eresep/index';

// Penunjang
$route['patologiKlinik/:num'] = 'rekam_medis/penunjang/PatologiKlinik/index';
$route['patologiAnatomi/:num/:num/:num'] = 'rekam_medis/penunjang/PatologiAnatomi/index';
$route['patologiAnatomi/laporan/:num'] = 'rekam_medis/penunjang/PatologiAnatomi/laporan';
$route['radiologi/:num'] = 'rekam_medis/penunjang/radiologi/index';
$route['prosedurDiagnostik/:num'] = 'rekam_medis/penunjang/prosedurDiagnostik/index';
$route['reevaluasiLab/:num'] = 'reevaluasi/reevaluasiLAB/index';

// History Penunjang
$route['historyPenunjang'] = 'rekam_medis/penunjang/history/pasien/index';

// Catatan Terintegrasi
$route['PlanOfCare/:num'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/PlanOfCare';
$route['KardekRi/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/KardekRi';

// Flap
$route['Flap/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/Flap';
$route['Flap/:num/:num'] = 'rekam_medis/rawat_inap/ruanganIntensif/Flap';

// Skrining peraasan tertekan
$route['SkriningPerasaanTertekan/:num'] = 'rekam_medis/rawat_inap/pengkajian/SkriningPerasaanTertekan';

// Operasi
$route['SiteMarking/index/:num/:num/:num'] = 'operasi/SiteMarking';
$route['PengkajianDafOpe/index/:num/:num/:num'] = 'operasi/PengkajianDafOpe';
$route['PengkajianPraOperasi/index/:num/:num/:num'] = 'operasi/PengkajianPraOperasi';
$route['RAPO/:num'] = 'rekam_medis/rawat_inap/operasi/RAPO';
$route['IntOpeRi/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/operasi/IntOpeRi';
$route['AsPraAnes/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/operasi/AsPraAnes';
$route['KesOpeRi/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/operasi/KesOpeRi';
$route['AlatOperasi/:num'] = 'rekam_medis/rawat_inap/operasi/AlatOperasi';
$route['CPAO/:num'] = 'rekam_medis/rawat_inap/operasi/CPAO';

$route['HumptyDumptyScale/index/:num/:num/:num'] = 'igd/HumptyDumptyScale';

// Kemoterapi
$route['PengkajianTerapiSistemik/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/kemoterapi/PengkajianTerapiSistemik';
$route['ProtokolKemo/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/kemoterapi/ProtokolKemo';
$route['SerahTerimaRi/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/transferRuangan/SerahTerimaRi';
$route['KesInvRi/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/invasifAnestesi/KesInvRi';
$route['Anestesia/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/invasifAnestesi/Anestesia';
$route['TandaVitalAnes/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/invasifAnestesi/TandaVitalAnes';
$route['ProKemAnak/:num'] = 'kemoterapi/ProKemAnak';
$route['JPOKEM/:num'] = 'kemoterapi/JPOKEM';

// Kardek
$route['Kardek/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/kemoterapi/Kardek';
$route['Kardek/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/kemoterapi/Kardek';

// IGD
$route['DashboardIGD/:num'] = 'rekam_medis/rawat_inap/igd/Dashboard';
$route['DashboardIGD/lab'] = 'rekam_medis/rawat_inap/igd/Dashboard/lab';
$route['FormulirTriase/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/igd/FormulirTriase';
$route['PengkajianIgdRi/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/igd/PengkajianIgdRi';
$route['SurKetEm/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/igd/SurKetEm';
$route['LaporanDPJP/:num'] = 'rekam_medis/rawat_inap/igd/LaporanDPJP';
$route['LaporanDPJP/:num/:num'] = 'rekam_medis/rawat_inap/igd/LaporanDPJP';

// Farmasi
$route['RekonsiliasiObat/:num'] = 'igd/FormulirRekonsiliasiObat/indexRawatInap';
$route['Konseling/:num'] = 'rekam_medis/rawat_inap/farmasi/Konseling';
$route['Konseling/:num/:num'] = 'rekam_medis/rawat_inap/farmasi/Konseling';

// Covid-19
$route['SkriningCovid19/:num'] = 'rekam_medis/rawat_inap/covid19/SkriningCovid19/index';
$route['SkriningCovid19/:num/:num'] = 'rekam_medis/rawat_inap/covid19/SkriningCovid19/index';

$route['formulirG8Geriatri/:num'] = 'geriatri/FormulirG8Geriatri/indexRawatInap';
$route['formulirG8Geriatri/:num/:num'] = 'geriatri/FormulirG8Geriatri/indexRawatInap';

// Pemantauan Nyeri
$route['keperawatan/pemantauanNyeri/index/:num'] = 'rekam_medis/rawat_inap/keperawatan/PemantauanNyeri/index';
$route['keperawatan/serahTerima/index/:num'] = 'rekam_medis/rawat_inap/keperawatan/SerahTerima/index';

// CPPT
$route['cppt/:num'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/cppt/index';
$route['cpptList/:num'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/cppt/list';
$route['cpptList/:num/:any'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/cppt/list';
$route['cpptList/:num/:any/:any'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/cppt/list';
$route['cpptView/:num'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/cppt/history';
$route['cpptView/:num/:any'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/cppt/history';

// PPRA
$route['ppra/:num'] = 'rekam_medis/rawat_inap/catatanTerintegrasi/PPRA/index';

// Resume
$route['PemulanganPasienRi/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/resume/PemulanganPasienRi';

// Persetujuan dan Edukasi
$route['CatatanEdukasi/index/:num/:num/:num'] = 'CatatanEdukasi/index';

// Invasif dan Anestesi
$route['CSOASEMA/:num'] = 'rekam_medis/rawat_inap/invasifAnestesi/CSOASEMA';

// Brakhiterapi
$route['PengBrak/index/:num/:num/:num/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengBrak';
$route['pengkajianRiMedisBrakhiterapi/:num'] = 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/MedisBrakhiterapi';
$route['OTKB/:num'] = 'rekam_medis/rawat_inap/brakhiterapi/OTKB';
$route['LTBG/:num'] = 'rekam_medis/rawat_inap/brakhiterapi/LTBG';
$route['LTBG/:num/:num'] = 'rekam_medis/rawat_inap/brakhiterapi/LTBG';
$route['BrakNaso/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/brakhiterapi/BrakNaso';

// DASHBOARD
$route['dashboard/:num'] = 'rekam_medis/dashboard';

// Flosheet
$route['Flosheet/:num'] = 'rekam_medis/rawat_inap/flosheet/index';

// FormEpidCovid
$route['FormEpidCovid/:num'] = 'rekam_medis/rawat_inap/FormEpidCovid';

// Admission
$route['CekPIRI/index/:num/:num/:num'] = 'rekam_medis/rawat_inap/admission/CekPIRI';

// Pemberian darah
$route['pemberianDanPemantauanDarah/:num'] = 'rekam_medis/rawat_inap/pemberianDanPemantauanDarah';

// Deteksi dini
$route['deteksiDini'] = 'emr/deteksiDini/MenuDeteksiDini';

// eTimja
$route['Etimja'] = 'Etimja';
$route['Etimja/:num/:num'] = 'Etimja';

// Resume Kematian
$route['resumeKematian/:num'] = 'rekam_medis/rawat_inap/resumeKematian/ResumeKematian/index';

// TBAK
$route['TBAK/:num/:num/:num'] = 'rekam_medis/rawat_inap/TBAK/index';

// Permintaan Dirawat
$route['PermintaanDirawat'] = 'PermintaanDirawat/PermintaanDirawat/index';
$route['PermintaanDirawat/tabelNonFinal'] = 'PermintaanDirawat/PermintaanDirawat/tabelNonFinal';
$route['PermintaanDirawat/tabelFinal'] = 'PermintaanDirawat/PermintaanDirawat/tabelFinal';

// Manajer Pelayanan Pasien
$route['MPP/Skrining/:num'] = 'MPP/Skrining/index';
$route['MPP/Asesmen/:num'] = 'MPP/Asesmen/index';
$route['MPP/Implementasi/:num'] = 'MPP/Implementasi/index';

// HIV
$route['HIV/FollowUp/:num'] = 'HIV/FollowUp/index';
$route['HIV/Rujukan/:num'] = 'HIV/Rujukan/index';
// Echo Ekg
$route['Echoekg/:num'] = 'rekam_medis/rawat_inap/echoekg/Echoekg/index_rawat_inap';

$route['napak/:num'] = 'rekam_medis/napak';

// Karyawan
$route['karyawan/SuratSakit/:num'] = 'karyawan/SuratSakit/index';

// radioterapi
$route['penjadwalanradioterapi'] = 'radioterapi/Penjadwalanradioterapi';
$route['penjadwalanradioterapi/(:any)'] = 'radioterapi/Penjadwalanradioterapi/$1';

$route['sonka/login'] = 'api/pasien/get_pasien';
