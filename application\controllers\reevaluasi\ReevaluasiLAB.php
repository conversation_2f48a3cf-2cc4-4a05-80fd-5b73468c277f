<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ReevaluasiLAB extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','reevaluasi/ReevaluasiLABModel'));
    }

    public function index()
    {
        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
            'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
            'ketersediaan_jaringan' => $this->masterModel->referensi(718),
            'lokasi_jaringan' => $this->masterModel->referensi(719),
            'jenis_pemeriksaan' => $this->masterModel->referensi(720),
            'asal_spesimen' => $this->masterModel->referensi(721),
            'jenis_spesimen' => $this->masterModel->referensi(722),
            'status_spesimen' => $this->masterModel->referensi(726),

        );

        $this->load->view('Pengkajian/reevaluasi_lab/index', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->ReevaluasiLABModel->rules;
                $this->form_validation->set_rules($rules);
                if($this->input->post('ruangan') == 1){
                    $this->form_validation->set_rules($this->ReevaluasiLABModel->rules_rawat_jalan);
                }elseif($this->input->post('ruangan') == 2){
                    $this->form_validation->set_rules($this->ReevaluasiLABModel->rules_rawat_inap);
                }

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();

                    $data = array(
                        'kunjungan' => $post['nokun'],
                        'rawat' => isset($post['ruangan']) ? $post['ruangan'] : 0,
                        'ruangan' => $post['ruangan'] == 1 ? $post['reevaluasi_jalan_ruangan'] : $post['reevaluasi_inap_ruangan'],
                        'rs_lain' => $post['rs_lain'],
                        'pemeriksaan_pa' => isset($post['na_patologi_anatomi']) ? $post['na_patologi_anatomi'] : "",
                        'pemeriksaan_pk' => isset($post['na_patologi_klinik']) ? $post['na_patologi_klinik'] : "",
                        'ketersediaan_jaringan' => isset($post['ketersediaan_jaringan']) && !isset($post['na_patologi_anatomi']) ? $post['ketersediaan_jaringan'] : null,
                        'jelaskan_ketersediaan_jaringan' => isset($post['jelaskan_ketersediaan_jaringan']) && !isset($post['na_patologi_anatomi']) ? $post['jelaskan_ketersediaan_jaringan'] : null,
                        'lokasi_asal_jaringan' => isset($post['lokasi_jaringan']) && !isset($post['na_patologi_anatomi']) ? $post['lokasi_jaringan'] : null,
                        'lokasi' => isset($post['jelaskan_lokasi_jaringan']) && !isset($post['na_patologi_anatomi']) ? $post['jelaskan_lokasi_jaringan'] : null,
                        'jenis_pemeriksaan' => isset($post['jenis_pemeriksaan']) && !isset($post['na_patologi_anatomi']) ? json_encode($post['jenis_pemeriksaan']) : null,
                        'diagnosis_awal_pa' => isset($post['diagnosis_pa']) && !isset($post['na_patologi_anatomi']) ? $post['diagnosis_pa'] : null,
                        'indikasi_klinis_pa' => isset($post['indikasi_klinis_pa']) && !isset($post['na_patologi_anatomi']) ? $post['indikasi_klinis_pa'] : null,
                        'asal_spesimen' => isset($post['asal_spesimen']) && !isset($post['na_patologi_klinik']) ? $post['asal_spesimen'] : null,
                        'asal_spesimen_dari' => isset($post['asal_spesimen_dari']) && !isset($post['na_patologi_klinik']) ? $post['asal_spesimen_dari'] : null,
                        'status_spesimen' => isset($post['status_spesimen']) && !isset($post['na_patologi_klinik']) ? $post['status_spesimen'] : null,
                        'jenis_spesimen' => isset($post['jenis_spesimen']) && !isset($post['na_patologi_klinik']) ? json_encode($post['jenis_spesimen']) : null,
                        'diagnosis_awal_pk' => isset($post['diagnosis_pk']) && !isset($post['na_patologi_klinik']) ? $post['diagnosis_pk'] : null,
                        'indikasi_klinis_pk' => isset($post['indikasi_klinis_pk']) && !isset($post['na_patologi_klinik']) ? $post['indikasi_klinis_pk'] : null,
                        'oleh' => $this->session->userdata("id"),
                    );

    				if($this->db->replace('medis.tb_reevaluasi_lab',$data)){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataReevaluasi = $this->ReevaluasiLABModel->get($post['nokun'], true);
                $data = array();
    			if(!empty($dataReevaluasi)){
    				echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataReevaluasi
                    ));
    			}else{
                    echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataReevaluasi
                    ));
                }
            }else if($param == 'count'){
                $result = $this->ReevaluasiLABModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->ReevaluasiLABModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_reevaluasi_lab" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> TANGGAL_ODONTO;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->ReevaluasiLABModel->total_count(),
            "recordsFiltered"   => $this->ReevaluasiLABModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
