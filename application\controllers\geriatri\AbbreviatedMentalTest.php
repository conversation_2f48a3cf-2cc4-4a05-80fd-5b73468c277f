<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AbbreviatedMentalTest extends CI_Controller
{

	public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listUmur = $this->masterModel->referensi(891);
        $listJamBerapa = $this->masterModel->referensi(892);
        $listDimanaAlamat = $this->masterModel->referensi(893);
        $listTahunBerapa = $this->masterModel->referensi(894);
        $listKitaDimana = $this->masterModel->referensi(895);
        $listMengenaliDokter = $this->masterModel->referensi(896);
        $listIndonesiaMerdeka = $this->masterModel->referensi(897);
        $listPresidenRI = $this->masterModel->referensi(898);
        $listAndaLahir = $this->masterModel->referensi(899);
        $listMenghitungMundur = $this->masterModel->referensi(900);
        $listPerasaanHati = $this->masterModel->referensi(902);
        $historyAbbreviatedMentalTest = $this->pengkajianAwalModel->historyAbbreviatedMentalTest($getNomr['NORM']);

        $data = array(
            'getNomr' => $getNomr,
            'listUmur' => $listUmur,
            'listJamBerapa' => $listJamBerapa,
            'listDimanaAlamat' => $listDimanaAlamat,
            'listTahunBerapa' => $listTahunBerapa,
            'listKitaDimana' => $listKitaDimana,
            'listMengenaliDokter' => $listMengenaliDokter,
            'listIndonesiaMerdeka' => $listIndonesiaMerdeka,
            'listPresidenRI' => $listPresidenRI,
            'listAndaLahir' => $listAndaLahir,
            'listMenghitungMundur' => $listMenghitungMundur,
            'listPerasaanHati' => $listPerasaanHati,
            'historyAbbreviatedMentalTest' => $historyAbbreviatedMentalTest,
        );

        $this->load->view('Pengkajian/geriatri/abbreviatedMentalTest/index', $data);
    }

    public function simpanFormAbbreviatedMentalTest()
    {
        $kunjungan = $this->input->post("nokun");
        $id_pengguna = $this->session->userdata('id');
        $umur = $this->input->post("umur");
        $jamberapa = $this->input->post("jamberapa");
        $dimanaalamat = $this->input->post("dimanaalamat");
        $tahunberapa = $this->input->post("tahunberapa");
        $kitadimana = $this->input->post("kitadimana");
        $mengenalidokter = $this->input->post("mengenalidokter");
        $indonesiamerdeka = $this->input->post("indonesiamerdeka");
        $presidenri = $this->input->post("presidenri");
        $andalahir = $this->input->post("andalahir");
        $menghitungmundur = $this->input->post("menghitungmundur");
        $perasaanhati = $this->input->post("perasaanhati");

        $data = array(
            'nokun' => $kunjungan,
            'umur' => $umur,
            'jam_berapa' => $jamberapa,
            'dimana_alamat' => $dimanaalamat,
            'tahun_berapa' => $tahunberapa,
            'kita_dimana' => $kitadimana,
            'mengenali_dokter' => $mengenalidokter,
            'indonesia_merdeka' => $indonesiamerdeka,
            'presiden_ri' => $presidenri,
            'anda_lahir' => $andalahir,
            'menghitung_mundur' => $menghitungmundur,
            'perasaan_hati' => $perasaanhati,
            'oleh' => $id_pengguna,
        );

        $this->db->trans_begin();

        $this->db->insert('db_layanan.tb_abbreviated_mental_test', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryAbbreviatedMentalTest()
    {   
        $id = $this->input->post('id');
        $AbbreviatedMentalTest = $this->pengkajianAwalModel->HistoryDetailAbbreviatedMentalTest($id);

        $listUmur = $this->masterModel->referensi(891);
        $listJamBerapa = $this->masterModel->referensi(892);
        $listDimanaAlamat = $this->masterModel->referensi(893);
        $listTahunBerapa = $this->masterModel->referensi(894);
        $listKitaDimana = $this->masterModel->referensi(895);
        $listMengenaliDokter = $this->masterModel->referensi(896);
        $listIndonesiaMerdeka = $this->masterModel->referensi(897);
        $listPresidenRI = $this->masterModel->referensi(898);
        $listAndaLahir = $this->masterModel->referensi(899);
        $listMenghitungMundur  = $this->masterModel->referensi(900);
        $listPerasaanHati  = $this->masterModel->referensi(902);

        foreach($AbbreviatedMentalTest as $amt):
            echo '  <form id="frmAbbreviatedMentalTest_edit">
                        <input type="hidden" name="id_amt_edit" value="'.$amt['id'].'">
                        <input type="hidden" name="nokun_edit" value="'.$amt['nokun'].'">
                        <table class="table table-bordered dt-responsive nowrap" cellspacing="0" width="100%">
                            <thead class="thead-light">
                                <th width="5%">&nbsp</th>
                                <th width="50%">&nbsp</th>
                                <th width="20%">Salah = 0</th>
                                <th width="20%">Benar = 1</th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>A</td>
                                    <td>Berapakah umur anda ?</td>';
                                        foreach ($listUmur as $list):
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="umur_edit" value="'.$list['id_variabel'].'" class="umur_edit" id="umur_edit'.$list['id_variabel'].'" ';
                                                if($amt['umur'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="umur_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach;
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>B</td>
                                    <td>Jam berapa sekarang ?</td>';
                                        foreach ($listJamBerapa as $list):
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="jamberapa_edit" value="'.$list['id_variabel'].'" class="jamberapa_edit" id="jamberapa_edit'.$list['id_variabel'].'" ';
                                                if($amt['jam_berapa'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="jamberapa_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach;
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>C</td>
                                    <td>Dimana alamat rumah anda ?</td>';
                                        foreach ($listDimanaAlamat as $list):
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="dimanaalamat_edit" value="'.$list['id_variabel'].'" class="dimanaalamat_edit" id="dimanaalamat_edit'.$list['id_variabel'].'" ';
                                                if($amt['dimana_alamat'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="dimanaalamat_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach;
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>D</td>
                                    <td>Tahun berapa sekarang ?</td>';
                                        foreach ($listTahunBerapa as $list):
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="tahunberapa_edit" value="'.$list['id_variabel'].'" class="tahunberapa_edit" id="tahunberapa_edit'.$list['id_variabel'].'" ';
                                                if($amt['tahun_berapa'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="tahunberapa_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach; 
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>E</td>
                                    <td>Saat ini kita sedang berada dimana ?</td>';
                                        foreach ($listKitaDimana as $list):
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kitadimana_edit" value="'.$list['id_variabel'].'" class="kitadimana_edit" id="kitadimana_edit'.$list['id_variabel'].'" ';
                                                if($amt['kita_dimana'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="kitadimana_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach;
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>F</td>
                                    <td>Mampukah pasien mengenali dokter atau perawat ?</td>';
                                        foreach ($listMengenaliDokter as $list): 
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="mengenalidokter_edit" value="'.$list['id_variabel'].'" class="mengenalidokter_edit" id="mengenalidokter_edit'.$list['id_variabel'].'" ';
                                                if($amt['mengenali_dokter'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="mengenalidokter_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach;
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>G</td>
                                    <td>Tahun berapa Indonesia Merdeka ?</td>';
                                        foreach ($listIndonesiaMerdeka as $list):
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="indonesiamerdeka_edit" value="'.$list['id_variabel'].'" class="indonesiamerdeka_edit" id="indonesiamerdeka_edit'.$list['id_variabel'].'" ';
                                                if($amt['indonesia_merdeka'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="indonesiamerdeka_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach; 
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>H</td>
                                    <td>Siapa nama presiden RI sekarang ?</td>';
                                        foreach ($listPresidenRI as $list):
            echo '                  <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="presidenri_edit" value="'.$list['id_variabel'].'" class="presidenri_edit" id="presidenri_edit'.$list['id_variabel'].'" ';
                                                if($amt['presiden_ri'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="presidenri_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach;
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>I</td>
                                    <td>Tahun berapa anda lahir ?</td>';
                                        foreach ($listAndaLahir as $list):
            echo '                      <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="andalahir_edit" value="'.$list['id_variabel'].'" class="andalahir_edit" id="andalahir_edit'.$list['id_variabel'].'" ';
                                                if($amt['anda_lahir'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="andalahir_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach;
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>J</td>
                                    <td>Menghitung mundur dari 20 sampai 1</td>';
                                        foreach ($listMenghitungMundur as $list):
            echo '                  <td>
                                        <div style="margin-top:-25px;" class="col-md-2 form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="menghitungmundur_edit" value="'.$list['id_variabel'].'" class="menghitungmundur_edit" id="menghitungmundur_edit'.$list['id_variabel'].'" ';
                                                if($amt['menghitung_mundur'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="menghitungmundur_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>
                                        </div>
                                        </td>';
                                        endforeach; 
            echo '                  </td>
                                </tr>
                                <tr>
                                    <td>&nbsp</td>
                                    <td>Jumlah Skor:</td>
                                    <td colspan="2">'.$amt['total'].'</td>
                                </tr>
                                <tr>
                                    <td>K</td>
                                    <td colspan="3">Perasaan hati (efek): pilih yang sesuai dengan kondisi pasien</br></br>
                                    <div class="col md-10">';
                                        foreach ($listPerasaanHati as $list): 
            echo '                          <div class="radio radio-primary form-check-inline jarak2">
                                                <input type="radio" name="perasaanhati_edit" value="'.$list['id_variabel'].'" class="perasaanhati_edit" id="perasaanhati_edit'.$list['id_variabel'].'" ';
                                                if($amt['perasaan_hati'] == $list['id_variabel'])
                                                {
                                                    echo "checked";
                                                }else{
                                                    echo "";
                                                }
            echo '                              >
                                                <label for="perasaanhati_edit'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                            </div>';
                                        endforeach;
            echo '                 </td>
                                </tr>
                                <tr>
                                    <td colspan="2">Interpretasi</td>
                                    <td colspan="2">';
                                    if($amt['total'] > 7){
                                        echo "Normal";
                                    }else if($amt['total'] > 3 && $amt['total'] < 8){
                                        echo "Sedang";
                                    }else if($amt['total'] < 4){
                                        echo "Berat";
                                    }
            echo '                  </td>
                                </tr>
                            </tbody>
                        </table>
                        <p>
                            <b>Cara Pelaksanaan</b></br>
                            1. Minta pasien untuk menjawab pertanyaan tersebut, beri tanda centang (V) pada nilai nol (0) jika salah dan satu (1) jika benar </br>
                            2. Jumlahkan skor total A sampai J, item K tidak dijumlahkan, hanya sebagai keterangan. </br>
                            3. Interpretasi : </br>
                            - Skor 8-10 menunjukkan <b>normal</b>, </br>
                            - Skor 4-7 gangguan ingatan <b>sedang</b> dan </br>
                            - skor 0-3 gangguan ingatan <b>berat</b> 
                        </p>
                        <div class="row">
                            <div class="col-lg-2 offset-lg-10">
                            <div class="form-group">
                                <button class="btn btn-primary waves-effect waves-light btn-block" type="submit">
                                <i class="fa fa-save"></i> Ubah
                                </button>
                            </div>
                            </div>
                        </div>
                    </form>
                    <script>
                        $("#frmAbbreviatedMentalTest_edit").submit(function(event) {
                            
                            event.preventDefault();
                            dataAbbreviatedMentalTest_edit = $("#frmAbbreviatedMentalTest_edit").serializeArray();
                            alertify.confirm("Konfirmasi", "Pilih Ok, jika setuju untuk Di Rubah",
                            function(){
                                $.ajax({
                                        dataType:"json",
                                        url: "'.base_url("geriatri/AbbreviatedMentalTest/updateAbbreviatedMentalTest").'",
                                        method: "POST",
                                        data: dataAbbreviatedMentalTest_edit,
                                        success: function(data) {
                                            if(data.status == "success"){
                                                alertify.success("Data Berhasil Di Update");
                                                $("#lihathistoryAbbreviatedMentalTest").modal("hide");
                                            }else{
                                                alertify.warning("Internal Server Error");
                                            }
                                        }
                        
                                });
                            }, function(){ });
                        });
                    </script>';
    endforeach;
    }

    public function updateAbbreviatedMentalTest()
    {
        $kunjungan = $this->input->post("nokun_edit");
        $id_pengguna = $this->session->userdata('id');
        $umur = $this->input->post("umur_edit");
        $jamberapa = $this->input->post("jamberapa_edit");
        $dimanaalamat = $this->input->post("dimanaalamat_edit");
        $tahunberapa = $this->input->post("tahunberapa_edit");
        $kitadimana = $this->input->post("kitadimana_edit");
        $mengenalidokter = $this->input->post("mengenalidokter_edit");
        $indonesiamerdeka = $this->input->post("indonesiamerdeka_edit");
        $presidenri = $this->input->post("presidenri_edit");
        $andalahir = $this->input->post("andalahir_edit");
        $menghitungmundur = $this->input->post("menghitungmundur_edit");
        $perasaanhati = $this->input->post("perasaanhati_edit");

        $dataUpdate = array(
            'nokun' => $kunjungan,
            'umur' => $umur,
            'jam_berapa' => $jamberapa,
            'dimana_alamat' => $dimanaalamat,
            'tahun_berapa' => $tahunberapa,
            'kita_dimana' => $kitadimana,
            'mengenali_dokter' => $mengenalidokter,
            'indonesia_merdeka' => $indonesiamerdeka,
            'presiden_ri' => $presidenri,
            'anda_lahir' => $andalahir,
            'menghitung_mundur' => $menghitungmundur,
            'perasaan_hati' => $perasaanhati,
            'oleh' => $id_pengguna,
        );
        $this->db->trans_begin();

        
        $this->db->where('db_layanan.tb_abbreviated_mental_test.id', $this->input->post('id_amt_edit'));
        $this->db->update('db_layanan.tb_abbreviated_mental_test', $dataUpdate);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }
}
?>