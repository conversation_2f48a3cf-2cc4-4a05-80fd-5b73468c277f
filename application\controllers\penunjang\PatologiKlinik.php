<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PatologiKlinik extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'penunjang/PatologiKlinikModel'
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $data = [
      'nokun' => $nokun,
      'pasien' => $pasien,
      'jumlah' => $this->PatologiKlinikModel->history($pasien['NORM'], 'jumlah'),
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'ruangPK' => $this->masterModel->ruangPK(),
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/penunjang/patologiKlinik/index', $data);
  }

  public function tindakan()
  {
    $post = $this->input->post();
    $data = ['ruangTujuan' => $post['ruangTujuan'] ?? null];
    // echo '<pre>';print_r($ruangTujuan);exit();
    $this->load->view('Pengkajian/penunjang/patologiKlinik/tindakan', $data);
  }

  public function tabelTindakan()
  {
    $draw = intval($this->input->post('draw'));
    $ruangTujuan = $this->input->post('ruangTujuan');
    $tindakan = $this->PatologiKlinikModel->tindakan($ruangTujuan);
    $data = [];
    $no = 1;
    $fmt = numfmt_create('id_ID', NumberFormatter::CURRENCY);
    // echo '<pre>';print_r($tindakan);exit();

    foreach ($tindakan as $t) {
      $data[] = [
        $no++ . '.',
        $t->ID,
        $t->NAMA,
        numfmt_format($fmt, $t->TARIF)
      ];
    }

    $output = [
      'draw' => $draw,
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->PatologiKlinikModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();

          // Mulai simpan
          $tanggal = isset($post['tanggal']) ? $post['tanggal'] : date('Y-m-d H:i:s');
          $ruangAwal = $post['ruang_awal'] ?? null;
          $kode = $this->pengkajianAwalModel->generateNoOrderLab($ruangAwal, $tanggal);
          $dataOrderLab = [
            'NOMOR' => $kode,
            'KUNJUNGAN' => $post['nokun'] ?? null,
            'TANGGAL' => $tanggal,
            'DOKTER_ASAL' => $post['dokter_perujuk'] ?? null,
            'TUJUAN' => $post['tujuan'] ?? null,
            'CITO' => $post['cito'] ?? 0,
            'OLEH' => $this->session->userdata('id'),
            'ALASAN' => $post['alasan'] ?? null,
            'TANGGAL_RENCANA' => $post['tanggal_rencana'] ?? date('Y-m-d'),
            'JENIS' => 2,
          ];
          // echo '<pre>';print_r($dataOrderLab);exit();
          $this->PatologiKlinikModel->simpan($dataOrderLab);
          // Akhir simpan

          // Mulai simpan detail
          $dataDetailOrderLab = [];
          $index = 0;
          $id = [];
          if (isset($post['tindakan'])) {
            $arrayTindakan = array_values(array_diff($post['tindakan'], ['0-128', '0-129']));
            // echo '<pre>';print_r($arrayTindakan);exit();
            foreach ($arrayTindakan as $input) {
              if ($arrayTindakan[$index] != null && !in_array(($arrayTindakan[$index]), $id)) {
                $tindakan[$index] = explode('-', ($arrayTindakan[$index]));
                array_push($id, $arrayTindakan[$index]);
                array_push(
                  $dataDetailOrderLab,
                  [
                    'ORDER_ID' => $kode,
                    'TINDAKAN' => $tindakan[$index][0],
                    'REF' => null,
                    'REF_TINDAKAN_EMR' => null,
                    'DESKRIPSI' => null,
                  ]
                );
              }
              $index++;
            }
          }
          // echo '<pre>';print_r($dataDetailOrderLab);exit();
          $this->PatologiKlinikModel->simpanDetail($dataDetailOrderLab);
          // Akhir simpan detail

          switch ($this->db->trans_status()) {
            case false:
              $this->db->trans_rollback();
              $result = ['status' => 'failed'];
              break;
            default:
              $this->db->trans_commit();
              $result = ['status' => 'success'];
              break;
          }
        } else {
          $result = ['status' => 'failed', 'errors' => $this->form_validation->error_array()];
        }
        echo json_encode($result);
      }
    }
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $norm = $this->input->post('norm');
    // echo '<pre>';print_r($norm);exit();
    $history = $this->PatologiKlinikModel->history($norm, 'tabel');
    $data = [];
    $no = 1;
    $warna = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history->result() as $h) {
      switch ($h->status == 0) {
        case 0:
          $warna = 'text-danger';
          break;
        case 1:
          $warna = 'text-primary';
          break;
        case 2:
          $warna = 'text-success';
          break;
        default:
          $warna = null;
          break;
      }

      $data[] = [
        $no++ . '.',
        $h->no_lab,
        date('d/m/Y', strtotime($h->tanggal)),
        date('d/m/Y', strtotime($h->tanggal_rencana)),
        $h->ruang_awal,
        $h->ruang_tujuan,
        $h->dokter_perujuk,
        "<p class='" . $warna . "'>" . isset($h->ket_status) && $h->ket_status != '' ? $h->ket_status : '-' . "</p>",
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-detail-lpk' class='btn btn-sm btn-primary waves-effect tbl-detail-lpk' data-toggle='modal' data-id='" . $h->no_lab . "'>
            <i class='fa fa-eye'></i> Lihat
          </button>
          <a href='/reports/simrskd/penunjang/penunjangPK.php?format=pdf&id=" . $h->no_lab . "' class='btn btn-sm btn-success waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak
          </a>
        </div>"
      ];
    }

    $output = [
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function history()
  {
    $post = $this->input->post();
    $data = ['norm' => $post['norm']];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/penunjang/patologiKlinik/history/index', $data);
  }

  public function detail()
  {
    $post = $this->input->post();
    $nomor = $post['nomor'] ?? null;
    $detail = $this->PatologiKlinikModel->detail($nomor);
    $nokun = $this->uri->segment(6);
    print_r($nokun);
    $alert = null;

    switch ($detail['status']) {
      case 0:
        $alert = 'alert-danger';
        break;
      case 1:
        $alert = 'alert-primary';
        break;
      case 2:
        $alert = 'alert-success';
        break;
    }

    $data = [
      'detail' => $detail,
      'alert' => $alert,
      'nokun' => $nokun
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/penunjang/patologiKlinik/history/detail', $data);
  }

  public function tabelDetail()
  {
    $draw = intval($this->input->post('draw'));
    $id = $this->input->post('id') ?? null;
    // echo '<pre>';print_r($id);exit();
    $tabel = $this->PatologiKlinikModel->tabelDetail($id);
    $data = [];
    $no = 1;
    $fmt = numfmt_create('id_ID', NumberFormatter::CURRENCY);
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel as $t) {
      $data[] = [
        $no++,
        $t->NAMA,
        numfmt_format($fmt, $t->TARIF)
      ];
    }

    $output = [
      'draw' => $draw,
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $nomor = $this->input->post('nomor') ?? null;

    $data = ['status' => 0];
    $this->PatologiKlinikModel->ubah($data, $nomor);

    switch ($this->db->trans_status()) {
      case false:
        $this->db->trans_rollback();
        $result = ['status' => 'failed'];
        break;
      default:
        $this->db->trans_commit();
        $result = ['status' => 'success'];
        break;
    }
    echo json_encode($result);
  }
}

// End of file PatologiKlinik.php
// Location: ./application/controllers/penunjang/PatologiKlinik.php