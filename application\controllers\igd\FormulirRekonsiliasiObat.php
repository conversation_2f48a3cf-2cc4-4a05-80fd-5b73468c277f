<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormulirRekonsiliasiObat extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        // if (!in_array(8, $this->session->userdata('akses'))) {
        //     redirect('login');
        // }
        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function indexRawatInap()
    {
        $nokun = $this->uri->segment(2);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array(
            'getNomr' => $getNomr,
            'listDr' => $this->masterModel->listDr(),
            'pegawai' => $this->masterModel->listAllPegawai(),
            'listStatusPasienRekonsiliasi' => $this->masterModel->listStatusPasienRekonsiliasi(),
            'listRiwayatAlergiObat' => $this->masterModel->listRiwayatAlergiObat(),
            'listRuangan' => $this->masterModel->ruanganRskd(),
            'listKategoriRuangan' => $this->masterModel->referensi(1724),
            'listKategoriRuanganSaatIni' => $this->masterModel->referensi(1725),
            'historyRekonsiliasiObatIGD' => $this->pengkajianAwalModel->historyRekonsiliasiObatIGD($getNomr['NORM'])
        );

        $this->load->view('Pengkajian/igd/rekonsiliasiObat/index', $data);
    }

    public function history()
    {
        $data = array(
            'historyRekonsiliasiObatIGD' => $this->pengkajianAwalModel->historyRekonsiliasiObatIGD($this->input->post('nomr'))
        );

        $this->load->view('Pengkajian/igd/rekonsiliasiObat/history', $data);
    }

    public function count()
    {
        $result = $this->pengkajianAwalModel->countRekonsiliasiObat($this->input->post('nomr'));
    
        echo json_encode($result);

    }

    public function simpanFormRekonsiliasiObat()
    {

        $riwayatNamaObatOral = $this->input->post("riwayatNamaObatOral");
        $riwayatAturanPakaiOral = $this->input->post("riwayatAturanPakaiOral");
        $riwayatInstruksiOral = $this->input->post("riwayatInstruksiOral");
        $riwayatCatatanOral = $this->input->post("riwayatCatatanOral");

        $riwayatNamaObatInjeksi = $this->input->post("riwayatNamaObatInjeksi");
        $riwayatAturanPakaiInjeksi = $this->input->post("riwayatAturanPakaiInjeksi");
        $riwayatInstruksiInjeksi = $this->input->post("riwayatInstruksiInjeksi");
        $riwayatCatatanInjeksi = $this->input->post("riwayatCatatanInjeksi");

        $riwayatNamaObatInfus = $this->input->post("riwayatNamaObatInfus");
        $riwayatAturanPakaiInfus = $this->input->post("riwayatAturanPakaiInfus");
        $riwayatIntruksiInfus = $this->input->post("riwayatIntruksiInfus");
        $riwayatCatatanInfus = $this->input->post("riwayatCatatanInfus");

        $riwayatNamaObatObatLuar = $this->input->post("riwayatNamaObatObatLuar");
        $riwayatAturanPakaiObatLuar = $this->input->post("riwayatAturanPakaiObatLuar");
        $riwayatInstruksiObatLuar = $this->input->post("riwayatInstruksiObatLuar");
        $riwayatCatatanObatLuar = $this->input->post("riwayatCatatanObatLuar");

        $terapiNamaObatOral = $this->input->post("terapiNamaObatOral");
        $terapiAturanPakaiOral = $this->input->post("terapiAturanPakaiOral");
        $terapiCatatanOral = $this->input->post("terapiCatatanOral");

        $terapiNamaObatInjeksi = $this->input->post("terapiNamaObatInjeksi");
        $terapiAturanPakaiInjeksi = $this->input->post("terapiAturanPakaiInjeksi");
        $terapiCatatanInjeksi = $this->input->post("terapiCatatanInjeksi");

        $terapiNamaObatInfus = $this->input->post("terapiNamaObatInfus");
        $terapiAturanPakaiInfus = $this->input->post("terapiAturanPakaiInfus");
        $terapiCatatanInfus = $this->input->post("terapiCatatanInfus");

        $terapiNamaObatObatLuar = $this->input->post("terapiNamaObatObatLuar");
        $terapiAturanPakaiObatLuar = $this->input->post("terapiAturanPakaiObatLuar");
        $terapiCatatanObatLuar = $this->input->post("terapiCatatanObatLuar");

        $data = array(
            'nokun' => $this->input->post("nokun"),
            'dpjpRekon' => $this->input->post("dpjpRekon"),
            'diagnosis' => $this->input->post("diagnosis"),
            'tanggalMasuk' => $this->input->post("tanggalMasuk"),
            'statusPasien' => $this->input->post("statusPasien"),
            'riwayatAlergi' => $this->input->post("riwayatAlergi"),
            'AdaRiwayatAlergi' => $this->input->post("AdaRiwayatAlergi"),
            'asalRuanganRiwayat' => $this->input->post("asalRuanganRiwayat"),
            'kategoriRuangan' => $this->input->post("kategori_ruangan"),
            'asalRuanganTerapi' => $this->input->post("asalRuanganTerapi"),
            'kategoriRuanganSaatIni' => $this->input->post("kategori_ruangan_saat_ini"),
            'apotekerPertama' => $this->input->post("apotekerpertama"),
            'apotekerKedua' => $this->input->post("apotekerkedua"),
            'pengguna' => $this->input->post("pengguna"),
            'status' => 1,
        );

        // echo "<pre>".print_r($data)."</pre>";

        $idSimpan = $this->pengkajianAwalModel->insertRekonsiliasiObat($data);
       // echo $idSimpan;

        $dataPengguna = array(
            'id_rekonsiliasi' => $idSimpan,
            'pengguna' => $this->input->post("pengguna")
        );

        $this->pengkajianAwalModel->insertPenggunaRekonsiliasiObat($dataPengguna);

        $dataRiwayatOral = array();
        $indexRiwayatOral = 0;
        if (count($riwayatNamaObatOral) > 0) {
            foreach ($riwayatNamaObatOral as $inputDatai) {
                if ($riwayatNamaObatOral[$indexRiwayatOral] != "") {
                    $i = $indexRiwayatOral+1;
                    $r_namaobat_oral = $this->input->post("r_namaobat_oral$i");
                    $r_nie_oral = $this->input->post("r_nie_oral$i");
                    $r_tgled_oral = $this->input->post("r_tgled_oral$i");
                    $r_btk_fisik_oral = $this->input->post("r_btk_fisik_oral$i");
                    $r_suhu_oral = $this->input->post("r_suhu_oral$i");

                    array_push(
                        $dataRiwayatOral, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'riwayatNamaObatOral' => $riwayatNamaObatOral[$indexRiwayatOral],
                        'riwayatAturanPakaiOral' => $riwayatAturanPakaiOral[$indexRiwayatOral],
                        'riwayatInstruksiOral' => $riwayatInstruksiOral[$indexRiwayatOral],
                        'riwayatCatatanOral' => $riwayatCatatanOral[$indexRiwayatOral],
                        'r_namaobat_oral' => $r_namaobat_oral[0],
                        'r_nie_oral' => $r_nie_oral[0],        
                        'r_tgled_oral' => $r_tgled_oral[0],
                        'r_btk_fisik_oral' => $r_btk_fisik_oral[0],
                        'r_suhu_oral' => $r_suhu_oral[0],
                        )
                    );
                }
                $indexRiwayatOral++;
            }

            // echo "<pre>".print_r($dataRiwayatOral)."</pre>";
            // exit();
            $this->pengkajianAwalModel->insertRiwayatObatORal($dataRiwayatOral);
        }

        $dataRiwayatInjeksi = array();
        $indexRiwayatInjeksi = 0;
        if (count($riwayatNamaObatInjeksi) > 0) {
            foreach ($riwayatNamaObatInjeksi as $inputDataii) {
                if ($riwayatNamaObatInjeksi[$indexRiwayatInjeksi] != "") {
                    $i = $indexRiwayatInjeksi+1;
                    $r_namaobat_injeksi = $this->input->post("r_namaobat_injeksi$i");
                    $r_nie_injeksi = $this->input->post("r_nie_injeksi$i");
                    $r_tgled_injeksi = $this->input->post("r_tgled_injeksi$i");
                    $r_btk_fisik_injeksi = $this->input->post("r_btk_fisik_injeksi$i");
                    $r_suhu_injeksi = $this->input->post("r_suhu_injeksi$i");

                    array_push(
                        $dataRiwayatInjeksi, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'riwayatNamaObatInjeksi ' => $riwayatNamaObatInjeksi [$indexRiwayatInjeksi],
                        'riwayatAturanPakaiInjeksi' => $riwayatAturanPakaiInjeksi[$indexRiwayatInjeksi],
                        'riwayatInstruksiInjeksi' => $riwayatInstruksiInjeksi[$indexRiwayatInjeksi],
                        'riwayatCatatanInjeksi' => $riwayatCatatanInjeksi[$indexRiwayatInjeksi],
                        'r_namaobat_injeksi' => $r_namaobat_injeksi[0],
                        'r_nie_injeksi' => $r_nie_injeksi[0],        
                        'r_tgled_injeksi' => $r_tgled_injeksi[0],
                        'r_btk_fisik_injeksi' => $r_btk_fisik_injeksi[0],
                        'r_suhu_injeksi' => $r_suhu_injeksi[0]
                        )
                    );
                }
                $indexRiwayatInjeksi++;
            }
            $this->pengkajianAwalModel->insertRiwayatObatInjeksi($dataRiwayatInjeksi);
        }

        $dataRiwayatInfus = array();
        $indexRiwayatInfus = 0;
        if (count($riwayatNamaObatInfus) > 0) {
            foreach ($riwayatNamaObatInfus as $inputDataiii) {
                if ($riwayatNamaObatInfus[$indexRiwayatInfus] != "") {
                    $i = $indexRiwayatInfus+1;
                    $r_namaobat_infus = $this->input->post("r_namaobat_infus$i");
                    $r_nie_infus = $this->input->post("r_nie_infus$i");
                    $r_tgled_infus = $this->input->post("r_tgled_infus$i");
                    $r_btk_fisik_infus = $this->input->post("r_btk_fisik_infus$i");
                    $r_suhu_infus = $this->input->post("r_suhu_infus$i");
                    array_push(
                        $dataRiwayatInfus, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'riwayatNamaObatInfus ' => $riwayatNamaObatInfus [$indexRiwayatInfus],
                        'riwayatAturanPakaiInfus' => $riwayatAturanPakaiInfus[$indexRiwayatInfus],
                        'riwayatIntruksiInfus' => $riwayatIntruksiInfus[$indexRiwayatInfus],
                        'riwayatCatatanInfus' => $riwayatCatatanInfus[$indexRiwayatInfus],
                        'r_namaobat_infus' => $r_namaobat_infus[0],
                        'r_nie_infus' => $r_nie_infus[0],        
                        'r_tgled_infus' => $r_tgled_infus[0],
                        'r_btk_fisik_infus' => $r_btk_fisik_infus[0],
                        'r_suhu_infus' => $r_suhu_infus[0]
                        )
                    );
                }
                $indexRiwayatInfus++;
            }
            $this->pengkajianAwalModel->insertRiwayatObatInfus($dataRiwayatInfus);
        }

        $dataRiwayatObatLuar = array();
        $indexRiwayatObatLuar = 0;
        if (count($riwayatNamaObatObatLuar) > 0) {
            foreach ($riwayatNamaObatObatLuar as $inputDataiv) {
                if ($riwayatNamaObatObatLuar[$indexRiwayatObatLuar] != "") {
                    $i = $indexRiwayatObatLuar+1;
                    $r_namaobat_obatluar = $this->input->post("r_namaobat_obatluar$i");
                    $r_nie_obatluar = $this->input->post("r_nie_obatluar$i");
                    $r_tgled_obatluar = $this->input->post("r_tgled_obatluar$i");
                    $r_btk_fisik_obatluar = $this->input->post("r_btk_fisik_obatluar$i");
                    $r_suhu_obatluar = $this->input->post("r_suhu_obatluar$i");
                    array_push(
                        $dataRiwayatObatLuar, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'riwayatNamaObatObatLuar ' => $riwayatNamaObatObatLuar [$indexRiwayatObatLuar],
                        'riwayatAturanPakaiObatLuar' => $riwayatAturanPakaiObatLuar[$indexRiwayatObatLuar],
                        'riwayatInstruksiObatLuar ' => $riwayatInstruksiObatLuar[$indexRiwayatObatLuar],
                        'riwayatCatatanObatLuar' => $riwayatCatatanObatLuar[$indexRiwayatObatLuar],
                        'r_namaobat_obatluar' => $r_namaobat_obatluar[0],
                        'r_nie_obatluar' => $r_nie_obatluar[0],        
                        'r_tgled_obatluar' => $r_tgled_obatluar[0],
                        'r_btk_fisik_obatluar' => $r_btk_fisik_obatluar[0],
                        'r_suhu_obatluar' => $r_suhu_obatluar[0]
                        )
                    );
                }
                $indexRiwayatObatLuar++;
            }
            $this->pengkajianAwalModel->insertRiwayatObatLuar($dataRiwayatObatLuar);
        }

        $dataTerapiObatOral = array();
        $indexTerapiOral = 0;
        if (count($terapiNamaObatOral) > 0) {
            foreach ($terapiNamaObatOral as $inputDatav) {
                if ($terapiNamaObatOral[$indexTerapiOral] != "") {
                    array_push(
                        $dataTerapiObatOral, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'terapiNamaObatOral' => $terapiNamaObatOral[$indexTerapiOral],
                        'terapiAturanPakaiOral' => $terapiAturanPakaiOral[$indexTerapiOral],
                        'terapiCatatanOral ' => $terapiCatatanOral[$indexTerapiOral],
                        )
                    );
                }
                $indexTerapiOral++;
            }
            $this->pengkajianAwalModel->insertTerapiObatOral($dataTerapiObatOral);
        }

        $dataTerapiObatInjeksi = array();
        $indexTerapiInjeksi = 0;
        if (count($terapiNamaObatInjeksi) > 0) {
            foreach ($terapiNamaObatInjeksi as $inputDatavi) {
                if ($terapiNamaObatInjeksi[$indexTerapiInjeksi] != "") {
                    array_push(
                        $dataTerapiObatInjeksi, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'terapiNamaObatInjeksi' => $terapiNamaObatInjeksi[$indexTerapiInjeksi],
                        'terapiAturanPakaiInjeksi' => $terapiAturanPakaiInjeksi[$indexTerapiInjeksi],
                        'terapiCatatanInjeksi ' => $terapiCatatanInjeksi[$indexTerapiInjeksi],
                        )
                    );
                }
                $indexTerapiInjeksi++;
            }
            $this->pengkajianAwalModel->insertTerapiObatInjeksi($dataTerapiObatInjeksi);
        }

        $dataTerapiObatInfus = array();
        $indexTerapiInfus = 0;
        if (count($terapiNamaObatInfus) > 0) {
            foreach ($terapiNamaObatInfus as $inputDatavii) {
                if ($terapiNamaObatInfus[$indexTerapiInfus] != "") {
                    array_push(
                        $dataTerapiObatInfus, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'terapiNamaObatInfus' => $terapiNamaObatInfus[$indexTerapiInfus],
                        'terapiAturanPakaiInfus' => $terapiAturanPakaiInfus[$indexTerapiInfus],
                        'terapiCatatanInfus ' => $terapiCatatanInfus[$indexTerapiInfus],
                        )
                    );
                }
                $indexTerapiInfus++;
            }
            $this->pengkajianAwalModel->insertTerapiObatInfus($dataTerapiObatInfus);
        }

        $dataTerapiObatLuar = array();
        $indexTerapiObatLuar = 0;
        if (count($terapiNamaObatObatLuar) > 0) {
            foreach ($terapiNamaObatObatLuar as $inputDataviii) {
                if ($terapiNamaObatObatLuar[$indexTerapiObatLuar] != "") {
                    array_push(
                        $dataTerapiObatLuar, array(
                        'id_rekonsiliasi' => $idSimpan,
                        'terapiNamaObatObatLuar' => $terapiNamaObatObatLuar[$indexTerapiObatLuar],
                        'terapiAturanPakaiObatLuar' => $terapiAturanPakaiObatLuar[$indexTerapiObatLuar],
                        'terapiCatatanObatLuar ' => $terapiCatatanObatLuar[$indexTerapiObatLuar],
                        )
                    );
                }
                $indexTerapiObatLuar++;
            }
            $this->pengkajianAwalModel->insertTerapiObatLuar($dataTerapiObatLuar);
        }



        if ($idSimpan) {
            $result = array('status' => 'success');
        }

        return $result;
    }

    public function ubahFormRekonsiliasiObat()
    {

        $riwayatNamaObatOral = $this->input->post("riwayatNamaObatOral_edit");
        $riwayatAturanPakaiOral = $this->input->post("riwayatAturanPakaiOral_edit");
        $riwayatInstruksiOral = $this->input->post("riwayatInstruksiOral_edit");
        $riwayatCatatanOral = $this->input->post("riwayatCatatanOral_edit");

        $riwayatNamaObatInjeksi = $this->input->post("riwayatNamaObatInjeksi_edit");
        $riwayatAturanPakaiInjeksi = $this->input->post("riwayatAturanPakaiInjeksi_edit");
        $riwayatInstruksiInjeksi = $this->input->post("riwayatInstruksiInjeksi_edit");
        $riwayatCatatanInjeksi = $this->input->post("riwayatCatatanInjeksi_edit");

        $riwayatNamaObatInfus = $this->input->post("riwayatNamaObatInfus_edit");
        $riwayatAturanPakaiInfus = $this->input->post("riwayatAturanPakaiInfus_edit");
        $riwayatIntruksiInfus = $this->input->post("riwayatIntruksiInfus_edit");
        $riwayatCatatanInfus = $this->input->post("riwayatCatatanInfus_edit");

        $riwayatNamaObatObatLuar = $this->input->post("riwayatNamaObatObatLuar_edit");
        $riwayatAturanPakaiObatLuar = $this->input->post("riwayatAturanPakaiObatLuar_edit");
        $riwayatInstruksiObatLuar = $this->input->post("riwayatInstruksiObatLuar_edit");
        $riwayatCatatanObatLuar = $this->input->post("riwayatCatatanObatLuar_edit");

        $terapiNamaObatOral = $this->input->post("terapiNamaObatOral_edit");
        $terapiAturanPakaiOral = $this->input->post("terapiAturanPakaiOral_edit");
        $terapiCatatanOral = $this->input->post("terapiCatatanOral_edit");

        $terapiNamaObatInjeksi = $this->input->post("terapiNamaObatInjeksi_edit");
        $terapiAturanPakaiInjeksi = $this->input->post("terapiAturanPakaiInjeksi_edit");
        $terapiCatatanInjeksi = $this->input->post("terapiCatatanInjeksi_edit");

        $terapiNamaObatInfus = $this->input->post("terapiNamaObatInfus_edit");
        $terapiAturanPakaiInfus = $this->input->post("terapiAturanPakaiInfus_edit");
        $terapiCatatanInfus = $this->input->post("terapiCatatanInfus_edit");

        $terapiNamaObatObatLuar = $this->input->post("terapiNamaObatObatLuar_edit");
        $terapiAturanPakaiObatLuar = $this->input->post("terapiAturanPakaiObatLuar_edit");
        $terapiCatatanObatLuar = $this->input->post("terapiCatatanObatLuar_edit");

        $dataUbah = array(
            'dpjpRekon' => $this->input->post("dpjpRekon_edit"),
            'diagnosis' => $this->input->post("diagnosis_edit"),
            'tanggalMasuk' => $this->input->post("tanggalMasuk_edit"),
            'statusPasien' => $this->input->post("statusPasien_edit"),
            'riwayatAlergi' => $this->input->post("riwayatAlergi_edit"),
            'AdaRiwayatAlergi' => $this->input->post("AdaRiwayatAlergi_edit"),
            'kategoriRuangan' => $this->input->post("kategori_ruangan_edit"),
            'asalRuanganRiwayat' => $this->input->post("asalRuanganRiwayat_edit"),
            'asalRuanganTerapi' => $this->input->post("asalRuanganTerapi_edit"),
            'apotekerPertama' => $this->input->post("apotekerpertama_edit"),
            'apotekerKedua' => $this->input->post("apotekerkedua_edit"),
            'status' => 1,
        );




        $idRekon = $this->input->post('id_rekon');

        $dataPengguna = array(
            'id_rekonsiliasi' => $idRekon,
            'pengguna' => $this->session->userdata('id')
        );
        // echo "<pre>".print_r($dataUbah)."</pre>";
        $this->pengkajianAwalModel->insertPenggunaRekonsiliasiObat($dataPengguna);

        $this->db->where('keperawatan.tb_rekonsiliasi_obat.id', $idRekon); 
        $this->db->update('keperawatan.tb_rekonsiliasi_obat', $dataUbah); 
        $this->db->delete('keperawatan.tb_rekonsiliasi_riwayat_obat_oral', array('id_rekonsiliasi' => $idRekon));
        $this->db->delete('keperawatan.tb_rekonsiliasi_riwayat_obat_injeksi', array('id_rekonsiliasi' => $idRekon));
        $this->db->delete('keperawatan.tb_rekonsiliasi_riwayat_obat_infus', array('id_rekonsiliasi' => $idRekon));
        $this->db->delete('keperawatan.tb_rekonsiliasi_riwayat_obat_luar', array('id_rekonsiliasi' => $idRekon));
        $this->db->delete('keperawatan.tb_rekonsiliasi_terapi_obat_oral', array('id_rekonsiliasi' => $idRekon));
        $this->db->delete('keperawatan.tb_rekonsiliasi_terapi_obat_injeksi', array('id_rekonsiliasi' => $idRekon));
        $this->db->delete('keperawatan.tb_rekonsiliasi_terapi_obat_infus', array('id_rekonsiliasi' => $idRekon));
        $this->db->delete('keperawatan.tb_rekonsiliasi_terapi_obat_luar', array('id_rekonsiliasi' => $idRekon));

        $dataRiwayatOral = array();
        $indexRiwayatOral = 0;
        if (isset($riwayatNamaObatOral)) {
            foreach ($riwayatNamaObatOral as $inputDatai) {
                if ($riwayatNamaObatOral[$indexRiwayatOral] != "") {
                    array_push(
                        $dataRiwayatOral, array(
                        'id_rekonsiliasi' => $idRekon,
                        'riwayatNamaObatOral' => $riwayatNamaObatOral[$indexRiwayatOral],
                        'riwayatAturanPakaiOral' => $riwayatAturanPakaiOral[$indexRiwayatOral],
                        'riwayatInstruksiOral' => $riwayatInstruksiOral[$indexRiwayatOral],
                        'riwayatCatatanOral' => $riwayatCatatanOral[$indexRiwayatOral],
                        )
                    );
                }
                $indexRiwayatOral++;
            }
            $this->pengkajianAwalModel->insertRiwayatObatORal($dataRiwayatOral);
        }

        $dataRiwayatInjeksi = array();
        $indexRiwayatInjeksi = 0;
        if (isset($riwayatNamaObatInjeksi)) {
            foreach ($riwayatNamaObatInjeksi as $inputDataii) {
                if ($riwayatNamaObatInjeksi[$indexRiwayatInjeksi] != "") {
                    array_push(
                        $dataRiwayatInjeksi, array(
                        'id_rekonsiliasi' => $idRekon,
                        'riwayatNamaObatInjeksi ' => $riwayatNamaObatInjeksi [$indexRiwayatInjeksi],
                        'riwayatAturanPakaiInjeksi' => $riwayatAturanPakaiInjeksi[$indexRiwayatInjeksi],
                        'riwayatInstruksiInjeksi' => $riwayatInstruksiInjeksi[$indexRiwayatInjeksi],
                        'riwayatCatatanInjeksi' => $riwayatCatatanInjeksi[$indexRiwayatInjeksi],
                        )
                    );
                }
                $indexRiwayatInjeksi++;
            }
            $this->pengkajianAwalModel->insertRiwayatObatInjeksi($dataRiwayatInjeksi);
        }

        $dataRiwayatInfus = array();
        $indexRiwayatInfus = 0;
        if (isset($riwayatNamaObatInfus)) {
            foreach ($riwayatNamaObatInfus as $inputDataiii) {
                if ($riwayatNamaObatInfus[$indexRiwayatInfus] != "") {
                    array_push(
                        $dataRiwayatInfus, array(
                        'id_rekonsiliasi' => $idRekon,
                        'riwayatNamaObatInfus ' => $riwayatNamaObatInfus [$indexRiwayatInfus],
                        'riwayatAturanPakaiInfus' => $riwayatAturanPakaiInfus[$indexRiwayatInfus],
                        'riwayatIntruksiInfus' => $riwayatIntruksiInfus[$indexRiwayatInfus],
                        'riwayatCatatanInfus' => $riwayatCatatanInfus[$indexRiwayatInfus],
                        )
                    );
                }
                $indexRiwayatInfus++;
            }
            $this->pengkajianAwalModel->insertRiwayatObatInfus($dataRiwayatInfus);
        }

        $dataRiwayatObatLuar = array();
        $indexRiwayatObatLuar = 0;
        if (isset($riwayatNamaObatObatLuar)) {
            foreach ($riwayatNamaObatObatLuar as $inputDataiv) {
                if ($riwayatNamaObatObatLuar[$indexRiwayatObatLuar] != "") {
                    array_push(
                        $dataRiwayatObatLuar, array(
                        'id_rekonsiliasi' => $idRekon,
                        'riwayatNamaObatObatLuar ' => $riwayatNamaObatObatLuar [$indexRiwayatObatLuar],
                        'riwayatAturanPakaiObatLuar' => $riwayatAturanPakaiObatLuar[$indexRiwayatObatLuar],
                        'riwayatInstruksiObatLuar ' => $riwayatInstruksiObatLuar[$indexRiwayatObatLuar],
                        'riwayatCatatanObatLuar' => $riwayatCatatanObatLuar[$indexRiwayatObatLuar],
                        )
                    );
                }
                $indexRiwayatObatLuar++;
            }
            $this->pengkajianAwalModel->insertRiwayatObatLuar($dataRiwayatObatLuar);
        }

        $dataTerapiObatOral = array();
        $indexTerapiOral = 0;
        if (isset($terapiNamaObatOral)) {
            foreach ($terapiNamaObatOral as $inputDatav) {
                if ($terapiNamaObatOral[$indexTerapiOral] != "") {
                    array_push(
                        $dataTerapiObatOral, array(
                        'id_rekonsiliasi' => $idRekon,
                        'terapiNamaObatOral' => $terapiNamaObatOral[$indexTerapiOral],
                        'terapiAturanPakaiOral' => $terapiAturanPakaiOral[$indexTerapiOral],
                        'terapiCatatanOral ' => $terapiCatatanOral[$indexTerapiOral],
                        )
                    );
                }
                $indexTerapiOral++;
            }
            $this->pengkajianAwalModel->insertTerapiObatOral($dataTerapiObatOral);
        }

        $dataTerapiObatInjeksi = array();
        $indexTerapiInjeksi = 0;
        if (isset($terapiNamaObatInjeksi)) {
            foreach ($terapiNamaObatInjeksi as $inputDatavi) {
                if ($terapiNamaObatInjeksi[$indexTerapiInjeksi] != "") {
                    array_push(
                        $dataTerapiObatInjeksi, array(
                        'id_rekonsiliasi' => $idRekon,
                        'terapiNamaObatInjeksi' => $terapiNamaObatInjeksi[$indexTerapiInjeksi],
                        'terapiAturanPakaiInjeksi' => $terapiAturanPakaiInjeksi[$indexTerapiInjeksi],
                        'terapiCatatanInjeksi ' => $terapiCatatanInjeksi[$indexTerapiInjeksi],
                        )
                    );
                }
                $indexTerapiInjeksi++;
            }
            $this->pengkajianAwalModel->insertTerapiObatInjeksi($dataTerapiObatInjeksi);
        }

        $dataTerapiObatInfus = array();
        $indexTerapiInfus = 0;
        if (isset($terapiNamaObatInfus)) {
            foreach ($terapiNamaObatInfus as $inputDatavii) {
                if ($terapiNamaObatInfus[$indexTerapiInfus] != "") {
                    array_push(
                        $dataTerapiObatInfus, array(
                        'id_rekonsiliasi' => $idRekon,
                        'terapiNamaObatInfus' => $terapiNamaObatInfus[$indexTerapiInfus],
                        'terapiAturanPakaiInfus' => $terapiAturanPakaiInfus[$indexTerapiInfus],
                        'terapiCatatanInfus ' => $terapiCatatanInfus[$indexTerapiInfus],
                        )
                    );
                }
                $indexTerapiInfus++;
            }
            $this->pengkajianAwalModel->insertTerapiObatInfus($dataTerapiObatInfus);
        }

        $dataTerapiObatLuar = array();
        $indexTerapiObatLuar = 0;
        if (isset($terapiNamaObatObatLuar)) {
            foreach ($terapiNamaObatObatLuar as $inputDataviii) {
                if ($terapiNamaObatObatLuar[$indexTerapiObatLuar] != "") {
                    array_push(
                        $dataTerapiObatLuar, array(
                        'id_rekonsiliasi' => $idRekon,
                        'terapiNamaObatObatLuar' => $terapiNamaObatObatLuar[$indexTerapiObatLuar],
                        'terapiAturanPakaiObatLuar' => $terapiAturanPakaiObatLuar[$indexTerapiObatLuar],
                        'terapiCatatanObatLuar ' => $terapiCatatanObatLuar[$indexTerapiObatLuar],
                        )
                    );
                }
                $indexTerapiObatLuar++;
            }
            $this->pengkajianAwalModel->insertTerapiObatLuar($dataTerapiObatLuar);
        }



        if ($idRekon) {
            $result = array('status' => 'success');
        }

        return $result;
    }

    public function detailRekonsiliasiObat()
    {
        $id = $this->input->post("id");
        $DHistoryRekonsi = $this->pengkajianAwalModel->dHistoryRekonsiliasiObatIGD($id);
        $nokun = $this->uri->segment(2);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listDr = $this->masterModel->listDr();
        $listStatusPasienRekonsiliasi = $this->masterModel->listStatusPasienRekonsiliasi();
        $listRiwayatAlergiObat = $this->masterModel->listRiwayatAlergiObat();
        $listRuangan = $this->masterModel->ruanganRskd();
        $dHistoryRiwayatObatOralIGD = $this->pengkajianAwalModel->dHistoryRiwayatObatOralIGD($id);
        $dHistoryRiwayatObatInjeksiIGD = $this->pengkajianAwalModel->dHistoryRiwayatObatInjeksiIGD($id);
        $dHistoryRiwayatObatInfusIGD = $this->pengkajianAwalModel->dHistoryRiwayatObatInfusIGD($id);
        $dHistoryRiwayatObatLuarIGD = $this->pengkajianAwalModel->dHistoryRiwayatObatLuarIGD($id);
        $dHistoryTerapiObatOralIGD = $this->pengkajianAwalModel->dHistoryTerapiObatOralIGD($id);
        $dHistoryTerapiObatInjeksiIGD = $this->pengkajianAwalModel->dHistoryTerapiObatInjeksiIGD($id);
        $dHistoryTerapiObatInfusIGD = $this->pengkajianAwalModel->dHistoryTerapiObatInfusIGD($id);
        $dHistoryTerapiObatLuarIGD = $this->pengkajianAwalModel->dHistoryTerapiObatLuarIGD($id);
      //  echo "<pre>";print_r($dHistoryRiwayatObatOralIGD);exit();
        $data = array(
            'listRiwayatAlergiObat' => $listRiwayatAlergiObat,
            'listStatusPasienRekonsiliasi' => $listStatusPasienRekonsiliasi,
            'listDr' => $listDr,
            'listKategoriRuangan' => $this->masterModel->referensi(1724),
            'listKategoriRuanganSaatIni' => $this->masterModel->referensi(1725),
            'listRuangan' => $listRuangan,
            'DHistoryRekonsi' => $DHistoryRekonsi,
            'dHistoryRiwayatObatOralIGD' => $dHistoryRiwayatObatOralIGD,
            'dHistoryRiwayatObatInjeksiIGD' => $dHistoryRiwayatObatInjeksiIGD,
            'dHistoryRiwayatObatInfusIGD' => $dHistoryRiwayatObatInfusIGD,
            'dHistoryRiwayatObatLuarIGD' => $dHistoryRiwayatObatLuarIGD,
            'dHistoryTerapiObatOralIGD' => $dHistoryTerapiObatOralIGD,
            'dHistoryTerapiObatInjeksiIGD' => $dHistoryTerapiObatInjeksiIGD,
            'dHistoryTerapiObatInfusIGD' => $dHistoryTerapiObatInfusIGD,
            'dHistoryTerapiObatLuarIGD' => $dHistoryTerapiObatLuarIGD,
            'pegawai' => $this->masterModel->listAllPegawai(),
        );

        $this->load->view('Pengkajian/igd/rekonsiliasiObat/view', $data);
    }

    public function detailRekonsiliasiObatTerakhir()
    {
        $nokun = $this->input->post('nokun');
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $DHistoryRekonsiliasiTerakhir = $this->pengkajianAwalModel->dHistoryRekonsiliasiObatIGDTerakhir($getNomr['NORM']);
        $dHistoryRiwayatObatOralIGDTerakhir = $this->pengkajianAwalModel->dHistoryRiwayatObatOralIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);
        $dHistoryRiwayatObatInjeksiIGDTerakhir = $this->pengkajianAwalModel->dHistoryRiwayatObatInjeksiIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);
        $dHistoryRiwayatObatInfusIGDTerakhir = $this->pengkajianAwalModel->dHistoryRiwayatObatInfusIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);
        $dHistoryRiwayatObatLuarIGDTerakhir = $this->pengkajianAwalModel->dHistoryRiwayatObatLuarIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);
        $dHistoryTerapiObatOralIGDTerakhir = $this->pengkajianAwalModel->dHistoryTerapiObatOralIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);
        $dHistoryTerapiObatInjeksiIGDTerakhir = $this->pengkajianAwalModel->dHistoryTerapiObatInjeksiIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);
        $dHistoryTerapiObatInfusIGDTerakhir = $this->pengkajianAwalModel->dHistoryTerapiObatInfusIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);
        $dHistoryTerapiObatLuarIGDTerakhir = $this->pengkajianAwalModel->dHistoryTerapiObatLuarIGDTerakhir($DHistoryRekonsiliasiTerakhir['ID']);

        $data = array(
            'DHistoryRekonsiliasiTerakhir' => $DHistoryRekonsiliasiTerakhir,
            'dHistoryRiwayatObatOralIGDTerakhir' => $dHistoryRiwayatObatOralIGDTerakhir,
            'dHistoryRiwayatObatInjeksiIGDTerakhir' => $dHistoryRiwayatObatInjeksiIGDTerakhir,
            'dHistoryRiwayatObatInfusIGDTerakhir' => $dHistoryRiwayatObatInfusIGDTerakhir,
            'dHistoryRiwayatObatLuarIGDTerakhir' => $dHistoryRiwayatObatLuarIGDTerakhir,
            'dHistoryTerapiObatOralIGDTerakhir' => $dHistoryTerapiObatOralIGDTerakhir,
            'dHistoryTerapiObatInjeksiIGDTerakhir' => $dHistoryTerapiObatInjeksiIGDTerakhir,
            'dHistoryTerapiObatInfusIGDTerakhir' => $dHistoryTerapiObatInfusIGDTerakhir,
            'dHistoryTerapiObatLuarIGDTerakhir' => $dHistoryTerapiObatLuarIGDTerakhir
        );

        echo json_encode($data);

    }


}
?>