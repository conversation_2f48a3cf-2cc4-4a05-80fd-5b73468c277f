<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RujukanInternalModel extends MY_Model
{
  protected $_table_name = 'pendaftaran.konsul';
  protected $_primary_key = 'NOMOR';
  protected $_order_by = 'NOMOR';
  protected $_order_by_type = 'DESC';

  var $tabel = 'pendaftaran.konsul k';
  var $urutan_kolom = array(null, 'TANGGAL', 'DOKTER_PERUJUK', 'TUJUAN', 'CITO', 'PENGISI', 'STATUS', null);
  var $pencarian_kolom = array('TANGGAL', 'DOKTER_PERUJUK', 'TUJUAN', 'CITO', 'PENGISI', 'STATUS');
  var $urutan = array('TANGGAL' => 'DESC');

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      ),
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('pendaftaran.konsul', $data);
  }

  public function ubah($data, $id)
  {
    $this->db->where('pendaftaran.konsul.NOMOR', $id);
    $this->db->update('pendaftaran.konsul', $data);
  }

  public function history($nokun)
  {
    $this->db->select(
      'k.NOMOR, k.TANGGAL, master.getNamaLengkapPegawai(d.NIP) DOKTER_PERUJUK, k.ALASAN, k.PERMINTAAN_TINDAKAN,
      r.DESKRIPSI TUJUAN, k.CITO, master.getNamaLengkapPegawai(p.NIP) PENGISI, k.STATUS'
    );
    $this->db->from($this->tabel);
    $this->db->join('master.dokter d', 'd.ID = k.DOKTER_ASAL', 'left');
    $this->db->join('master.ruangan r', 'r.ID = k.TUJUAN', 'left');
    $this->db->join('aplikasi.pengguna p', 'p.ID = k.OLEH', 'left');
    $this->db->where('k.KUNJUNGAN', $nokun);

    $i = 0;
    foreach ($this->pencarian_kolom as $pk) {
      if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
        $_POST['search']['value'] = $_POST['search']['value'];
      } else {
        $_POST['search']['value'] = '';
      }

      if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
        if ($i === 0) { // Loop pertama
          $this->db->group_start();
          $this->db->like($pk, $_POST['search']['value']);
        } else {
          $this->db->or_like($pk, $_POST['search']['value']);
        }

        if (count($this->pencarian_kolom) - 1 == $i) { // Loop terakhir
          $this->db->group_end(); // Tutup kurung
        }
      }
      $i++;
    }

    if (isset($_POST['order'])) { // Pemrosesan order
      $this->db->order_by($this->urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
    } elseif (isset($this->urutan)) {
      $urutan = $this->urutan;
      $this->db->order_by(key($urutan), $urutan[key($urutan)]);
    }
  }

  public function ambilTabel($nokun)
  {
    $this->history($nokun);
    if (isset($_POST['length']) && $_POST['length'] < 1) {
      $_POST['length'] = '10';
    } else {
      $_POST['length'] = $_POST['length'];
    }

    if (isset($_POST['start']) && $_POST['start'] > 1) {
      $_POST['start'] = $_POST['start'];
    }

    $this->db->limit($_POST['length'], $_POST['start']);
    // print_r($_POST);die;
    $query = $this->db->get();
    return $query->result();
  }

  public function hitungTersaring($nokun)
  {
    $this->history($nokun);
    $query = $this->db->get();
    return $query->num_rows();
  }

  public function hitungSemua($nokun)
  {
    $this->db->from($this->tabel);
    $this->db->where('k.KUNJUNGAN', $nokun);
    return $this->db->count_all_results();
  }

  public function detail($id)
  {
    $this->db->select('NOMOR, KUNJUNGAN, TANGGAL, DOKTER_ASAL, ALASAN, PERMINTAAN_TINDAKAN, TUJUAN, CITO, STATUS');
    $this->db->from('pendaftaran.konsul');
    $this->db->where('NOMOR', $id);
    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file RujukanInternal.php */
/* Location: ./application/models/konsultasi/RujukanInternal.php */