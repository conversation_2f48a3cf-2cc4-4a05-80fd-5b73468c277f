<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PaketObatDetilModel extends MY_Model{
    protected $_table_name = 'master.paket_obat_detil';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'ASC';

    function __construct(){
        parent::__construct();
    }

    function table_query(){
        $this->db->select('po.PAKET,po.DOSIS,po.JUMLAH,po.ATURAN_PAKAI,po.KETERANGAN,ibr.*, ib.NAMA, ib.FORMULARIUM ID_FORMULARIUM, IF(SUBSTRING(ib.KATEGORI, 1, 3)="101",1,0) KATEGORI
        , IF(ib.FORMULARIUM=1,"Fornas","Non Fornas") FORMULARIUM, ro.PERINGATAN_SAAT_ERESEP');
        $this->db->from('master.paket_obat_detil po');
        $this->db->join('inventory.barang ib','po.FARMASI=ib.ID','left');
        $this->db->join('inventory.barang_ruangan ibr','ib.ID=ibr.BARANG','left');
        $this->db->join('inventory.restriksi_obat ro', 'ro.BARANG = ib.ID` AND ro.STATUS = 1', 'left');
        $this->db->where(array('po.STATUS !=' => 0,'ibr.RUANGAN' => $this->input->post('ruangan')));
        $this->db->order_by('po.ID','ASC');

        if ($this->input->post('paket')) {
            $this->db->where('po.PAKET', $this->input->post('paket'));
        }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }
}
