<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormulirG8GeriatriModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s <PERSON>ajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('g8.id, g8.nokun, g8.created_at tanggal, g8.total, ru.DESKRIPSI ruangan, peng.NAMA user');
        $this->db->from('keperawatan.tb_g8_geriatri g8');
        $this->db->join('pendaftaran.kunjungan kun','kun.NOMOR = g8.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','pen.NOMOR = kun.NOPEN','LEFT');
        $this->db->join('master.ruangan ru','kun.RUANGAN = ru.ID','LEFT');
        $this->db->join('aplikasi.pengguna peng','g8.oleh = peng.ID','LEFT');
        $this->db->where('g8.nomr',$this->input->post('nomr'));
        $this->db->where('g8.status', 1);
        $this->db->where('g8.nokun <>', '');
        $this->db->order_by('g8.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id_g8)
    {
      $query = $this->db->query(
        'SELECT g8.`*`, tbbb.tb, tbbb.bb FROM keperawatan.tb_g8_geriatri g8
            LEFT JOIN db_pasien.tb_tb_bb tbbb ON g8.id = tbbb.ref AND tbbb.data_source = 29
            WHERE g8.id = "'.$id_g8.'"
            ORDER BY tbbb.created_at DESC LIMIT 1'
      );
      return $query->row_array();
    }

}
