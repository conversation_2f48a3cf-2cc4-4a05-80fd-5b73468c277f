<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for %s</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="css/bootstrap.min.css" rel="stylesheet">
  <link href="css/style.css" rel="stylesheet">
  <!--[if lt IE 9]>
  <script src="js/html5shiv.min.js"></script>
  <script src="js/respond.min.js"></script>
  <![endif]-->
 </head>
 <body>
  <header>
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <ol class="breadcrumb">
        <li class="active">%s</li>
        <li>(<a href="dashboard.html">Dashboard</a>)</li>

      </ol>
     </div>
    </div>
   </div>
  </header>
  <div class="container">
   <table class="table table-bordered">
    <thead>
     <tr>
      <td>&nbsp;</td>
      <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
     </tr>
     <tr>
      <td>&nbsp;</td>
      <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
      <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
      <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td class="danger">Total</td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">50.00%</div></td>
      <td class="danger small"><div align="right">1&nbsp;/&nbsp;2</div></td>
      <td class=" big"></td>
      <td class=" small"><div align="right">n/a</div></td>
      <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      <td class=" big"></td>
      <td class=" small"><div align="right">n/a</div></td>
      <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
     </tr>

     <tr>
      <td class="danger"><span class="glyphicon glyphicon-file"></span> <a href="source_with_ignore.php.html">source_with_ignore.php</a></td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">50.00%</div></td>
      <td class="danger small"><div align="right">1&nbsp;/&nbsp;2</div></td>
      <td class=" big"></td>
      <td class=" small"><div align="right">n/a</div></td>
      <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      <td class=" big"></td>
      <td class=" small"><div align="right">n/a</div></td>
      <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
     </tr>


    </tbody>
   </table>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 50%</span>
     <span class="warning"><strong>Medium</strong>: 50% to 90%</span>
     <span class="success"><strong>High</strong>: 90% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage %s</a> using <a href="%s" target="_top">%s</a> at %s.</small>
    </p>
   </footer>
  </div>
  <script src="js/jquery.min.js" type="text/javascript"></script>
  <script src="js/bootstrap.min.js" type="text/javascript"></script>
  <script src="js/holder.min.js" type="text/javascript"></script>
 </body>
</html>
