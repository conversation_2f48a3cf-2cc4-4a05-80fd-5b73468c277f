<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Oksigen extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('rekam_medis/OksigenModel'));
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $result = $this->OksigenModel->get_table(true);

        echo json_encode(
          array(
            'status' => 'success',
            'data' => $result
          )
        );
      }
    }
  }

  public function grafik_saturasi()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $nopen = $this->input->post('nopen');
      $saturasi = array();
      $waktu = array();
      $grafik_saturasi = $this->OksigenModel->grafik_saturasi($nopen);

      foreach ($grafik_saturasi as $gs) {
        array_push($saturasi, $gs['saturasi_o2']);
        array_push($waktu, $gs['waktu']);
      }

      $data = array(
        'saturasi' => $saturasi,
        'waktu' => $waktu,
      );
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/grafik_saturasi', $data);
    }
  }
}

/* End of file Oksigen.php */
/* Location: ./application/controllers/rekam_medis/Oksigen.php */