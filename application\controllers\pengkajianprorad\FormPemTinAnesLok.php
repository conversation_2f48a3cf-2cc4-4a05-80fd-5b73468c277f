<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormPemTinAnesLok extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $historyPemTinAnesLok = $this->pengkajianAwalModel->historyPemTinAnesLok($nomr);
    $listDr = $this->masterModel->listDrUmum();

    $data = array(
      'getNomr' => $getNomr,
      'nokun' => $nokun,
      'listDr' => $listDr,
      'historyPemTinAnesLok' => $historyPemTinAnesLok,
      'kesadaran' => $this->masterModel->referensi(5),
      'nmObat' => $this->masterModel->referensi(396),
      'encerkan' => $this->masterModel->referensi(397),
      'adrenalin' => $this->masterModel->referensi(398),
      'konversi' => $this->masterModel->referensi(601),
      'konversiYa' => $this->masterModel->referensi(1739),
      'jmlhPerdarahan' => $this->masterModel->referensi(465),
      'riwayatAlergi' => $this->masterModel->referensi(2),
    );

    $this->load->view('Pengkajian/pemantauanTindakanAnestesiLokal/index', $data);
  }

  public function simpan()
  {
    $post = $this->input->post();

    $data = array(
      'nokun' => isset($post['nokun']) ? $post['nokun']: "",
      'dokter_operator' => isset($post['dokterOperatorPemTinAnesRj']) ? $post['dokterOperatorPemTinAnesRj']: "",
      'jenis_tindakan' => isset($post['jenTinPemTinAnesRj']) ? $post['jenTinPemTinAnesRj']: "",
      'bb' => isset($post['bbPemTinAnesRj']) ? $post['bbPemTinAnesRj']: "",
      'tb' => isset($post['tbPemTinAnesRj']) ? $post['tbPemTinAnesRj']: "",
      'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran']: "",
      'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi']: "",
      'desk_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']): "",
      'desk_reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi']: "",
      'pemeriksaan_hemo' => isset($post['hemoPemTinAnesLokRj']) ? $post['hemoPemTinAnesLokRj']: "",
      'pemeriksaan_trombosit' => isset($post['tromPemTinAnesLokRj']) ? $post['tromPemTinAnesLokRj']: "",
      'pemeriksaan_hema' => isset($post['hemaPemTinAnesLokRj']) ? $post['hemaPemTinAnesLokRj']: "",
      'pemeriksaan_lainnya' => isset($post['lainPemTinAnesLokRj']) ? $post['lainPemTinAnesLokRj']: "",
      'obatanes_nama_obat' => isset($post['nmObatPemTinAnesLokRj']) ? $post['nmObatPemTinAnesLokRj']: "",
      'obatanes_desk_nama_obat' => isset($post['deskNmObatPemTinAnesLokRj']) ? $post['deskNmObatPemTinAnesLokRj']: "",
      'obatanes_dosis' => isset($post['dosisjmlhPemTinAnesLokRj']) ? $post['dosisjmlhPemTinAnesLokRj']: "",
      'obatanes_encer' => isset($post['encerkanPemTinAnesLokRj']) ? $post['encerkanPemTinAnesLokRj']: "",
      'obatanes_desk_encer' => isset($post['deskEncerkanPemTinAnesLokRj']) ? $post['deskEncerkanPemTinAnesLokRj']: "",
      'obatanes_adrenalin' => isset($post['adrenalinPemTinAnesLokRj']) ? $post['adrenalinPemTinAnesLokRj']: "",
      'obatanes_desk_adrenalin' => isset($post['deskDosisPemTinAnesLokRj']) ? $post['deskDosisPemTinAnesLokRj']: "",
      'obatanes_lokasi' => isset($post['lokasiPemTinAnesLokRj']) ? $post['lokasiPemTinAnesLokRj']: "",
      'obatanes_jam_lokasi' => isset($post['lokasiJamPemTinAnesLokRj']) ? $post['lokasiJamPemTinAnesLokRj']: "",
      'konversi' => isset($post['konversiPemTinAnesLokRj']) ? $post['konversiPemTinAnesLokRj']: "",
      'konversi_ya' => isset($post['deskKonversiPemTinAnesLokRj']) ? $post['deskKonversiPemTinAnesLokRj']: "",
      'konversi_desk_ya' => isset($post['deskKonversiYaPemTinAnesLokRj']) ? $post['deskKonversiYaPemTinAnesLokRj']: "",
      'jumlah_perdarahan' => isset($post['jmlhPerdarahanPemTinAnesLokRj']) ? $post['jmlhPerdarahanPemTinAnesLokRj']: "",
      'jumlah_perdarahan_desk' => isset($post['deskJmlhPerdarahanPemTinAnesLokRj']) ? $post['deskJmlhPerdarahanPemTinAnesLokRj']: "",
      'selesai_tindakan_pukul' => isset($post['selJamPemTinAnesLokRj']) ? $post['selJamPemTinAnesLokRj']: "",
      'oleh' => $this->session->userdata("id"),
      'status' => 1,
    );

    if($post['id'] != ""){
      $this->db->where('tb_pem_tin_anes_lok.id', $post['id']);
      $this->db->update('keperawatan.tb_pem_tin_anes_lok', $data);
    }elseif($post['id'] == ""){
      $this->db->insert('keperawatan.tb_pem_tin_anes_lok', $data);
    }

  }

  public function viewHistoryPemTinAnesLok()
  {
    $post = $this->input->post();
    $listDr = $this->masterModel->listDrUmum();
    $getPemTinAnesLok = $this->pengkajianAwalModel->getPemTinAnesLok($post['id']);
    $getNomr = $this->pengkajianAwalModel->getNomr($getPemTinAnesLok['nokun']);

    $data = array(
      'id' => $post['id'],
      'getNomr' => $getNomr,
      'getPemTinAnesLok' => $getPemTinAnesLok,
      'listDr' => $listDr,
      'kesadaran' => $this->masterModel->referensi(5),
      'nmObat' => $this->masterModel->referensi(396),
      'encerkan' => $this->masterModel->referensi(397),
      'adrenalin' => $this->masterModel->referensi(398),
      'konversi' => $this->masterModel->referensi(601),
      'konversiYa' => $this->masterModel->referensi(1739),
      'jmlhPerdarahan' => $this->masterModel->referensi(465),
      'riwayatAlergi' => $this->masterModel->referensi(2),
    );

    $this->load->view('Pengkajian/pemantauanTindakanAnestesiLokal/modalViewPemTinAnesLokal', $data);
  }

  public function viewStatusFisiologis()
  {
    $post = $this->input->post();

    $historyStatusFisiologis = $this->pengkajianAwalModel->historyStatusFisiologis($post['id']);
    
    $data = array(
      'id'  => $post['id'],
      'nokun'  => $post['nokun'],
      'norm'  => $post['norm'],
      'historyStatusFisiologis'  => $historyStatusFisiologis,
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'reaksiObat' => $this->masterModel->referensi(1741),
    );
    $this->load->view('Pengkajian/pemantauanTindakanAnestesiLokal/modalStatusFisiologis', $data);
  }

  public function simpanStatusFisiologis()
  {
    $post = $this->input->post();

    $data = array(
      'id_pem' => isset($post['id']) ? $post['id']: "",
      'nokun' => isset($post['nokun']) ? $post['nokun']: "",
      'jam' => isset($post['jamStatusFisiologisPemTinAnesLok']) ? $post['jamStatusFisiologisPemTinAnesLok']: "",
      'reaksi_obat' => isset($post['reaksiObatRjPTSL']) ? $post['reaksiObatRjPTSL']: "",
      'oleh' => $this->session->userdata("id"),
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $getId = $this->pengkajianAwalModel->simpanPemTinAnesLok($data);

    $dataTandaVital = array(
      'data_source' => 46,
      'ref' => $getId,
      'nomr' => isset($post['norm']) ? $post['norm']: "",
      'nokun' => isset($post['nokun']) ? $post['nokun']: "",
      'pukul' => isset($post['jamStatusFisiologisPemTinAnesLok']) ? $post['jamStatusFisiologisPemTinAnesLok']: "",
      'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1']: "",
      'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2']: "",
      'nadi' => isset($post['nadi']) ? $post['nadi']: "",
      'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan']: "",
      'oleh' => $this->session->userdata("id"),
    );

    $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);

    $dataSkriningNyeri = array(
      'nokun' => isset($post['nokun']) ? $post['nokun']: "",
      'data_source' => 46,
      'ref' => $getId,
      'metode' => isset($post['skrining_nyeri_RjPTSL']) ? $post['skrining_nyeri_RjPTSL']: "",
      'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri']: "",
      'created_by' => $this->session->userdata("id"),
    );

    $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
  }

  public function hapusStatusFisiologis()
  {
    $post = $this->input->post();
    $data = array(
      'status' => 0,
    );
    $this->db->where('tb_pem_tin_stts_fisio.id', $post['id']);
    $this->db->update('keperawatan.tb_pem_tin_stts_fisio', $data);

    $this->db->where('tb_skrining_nyeri.data_source', 46);
    $this->db->where('tb_skrining_nyeri.ref', $post['id']);
    $this->db->update('keperawatan.tb_skrining_nyeri', $data);

    $this->db->where('tb_tanda_vital.data_source', 46);
    $this->db->where('tb_tanda_vital.ref', $post['id']);
    $this->db->update('db_pasien.tb_tanda_vital', $data);
  }

}


/* End of file FormPemantauanAnestesiLokal.php */
/* Location: ./application/controllers/pengkajianprorad/FormPemantauanAnestesiLokal.php */
