<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PasienModel extends MY_Model{

    public function get_pasien($filters = [])
	{
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            return array('status' => 'error', 'message' => 'Invalid request method');
        }

        if(!is_object($filters) || !isset($filters->patient) || !isset($filters->method)) {
            $this->output->set_status_header(400);
            return array('status' => 'error', 'message' => 'Parameter is required');
        }

        // if($filters->patient != '123456') {
        //     $this->output->set_status_header(404);
        //     return array('status' => 'error', 'message' => 'Resource not found');
        // }

        $this->db->where('NORM',$filters->patient);
        $query = $this->db->get('master.pasien');

        if(!$query->num_rows()) {
            $this->output->set_status_header(404);
            return array('status' => 'error', 'message' => 'Resource not found');
        }

        return $query->row();
    }
}
