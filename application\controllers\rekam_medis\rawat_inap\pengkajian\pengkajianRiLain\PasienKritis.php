<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PasienKritis extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/KritisModel', 'rekam_medis/MedisModel'));
  }

  public function index($idLoadNorm, $idLoadNopen, $idLoadNokun, $idLoad)
  {
    $norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->KritisModel->getNomrRawatInap($nopen, $nokun);
    $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
    if ($idLoad === "00000") {
      $getPengkajian = $this->KritisModel->getPengkajian($getIdEmr);
    } elseif ($idLoad === "000000") {
      $getPengkajian = $this->KritisModel->getPengkajian($idLoad);
    } else {
      $getPengkajian = $this->KritisModel->getPengkajian($idLoad);
    }

    // START INDEKS BARTHEL
    $indeksBarthel = $this->KritisModel->hasilIndeksBarthel($nokun);
    if (isset($indeksBarthel)) {
      $totalSkorIndeksBarthel = $indeksBarthel['rangsang_bab'] + $indeksBarthel['rangsang_berkemih'] + $indeksBarthel['bersihkan_diri'] + $indeksBarthel['penggunaan_kloset'] + $indeksBarthel['makan'] + $indeksBarthel['berubah_posisi'] + $indeksBarthel['berpindah'] + $indeksBarthel['memakai_baju'] + $indeksBarthel['naik_tangga'] + $indeksBarthel['mandi'];

      if ($totalSkorIndeksBarthel == 20) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 4) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 8) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 11) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 19) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
      }
    }

    $jumlahIndeksBarthel = $this->KritisModel->get_count_indeksBarthel($nokun);
    // END INDEKS BARTHEL

    // START SKALA MORSE
    $skalaMorse = $this->KritisModel->hasilSkalaMorse($nokun);
    if (isset($skalaMorse)) {
      $totalSkalaMorse = $skalaMorse['NILAI_1'] + $skalaMorse['NILAI_2'] + $skalaMorse['NILAI_3'] + $skalaMorse['NILAI_4'] + $skalaMorse['NILAI_5'] + $skalaMorse['NILAI_6'];

      if ($totalSkalaMorse < 25) {
        $hasilSkalaMorse = "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaMorse . "</b> = <b>Tidak risiko</b> ( Skala Morse )</div>";
      } elseif ($totalSkalaMorse < 45) {
        $hasilSkalaMorse = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko Rendah</b> ( Skala Morse )</div>";
      } elseif ($totalSkalaMorse >= 45) {
        $hasilSkalaMorse = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko tinggi</b> ( Skala Morse )</div>";
      }
    }

    $jumlahSkalaMorse = $this->KritisModel->get_count_skalaMorse($nokun);
    // END SKALA MORSE

    // START SKALA ONTARIO
    $skalaOntario = $this->KritisModel->hasilSkalaOntario($nokun);
    if (isset($skalaOntario)) {
      $totalSkalaOntario = $skalaOntario['total'];

      if ($totalSkalaOntario >= 0 && $totalSkalaOntario <= 5) {
        $hasilSkalaOntario = "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Rendah</b> ( Skala Ontario ) </div>";
      } elseif ($totalSkalaOntario >= 6 && $totalSkalaOntario <= 16) {
        $hasilSkalaOntario = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Sedang</b> ( Skala Ontario ) </div>";
      } elseif ($totalSkalaOntario >= 17) {
        $hasilSkalaOntario = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Tinggi</b> ( Skala Ontario ) </div>";
      }
    }

    $jumlahSkalaOntario = $this->KritisModel->get_count_skalaOntario($nokun);
    // END SKALA ONTARIO

    // START HUMPTY DUMPTY
    $humptyDumpty = $this->KritisModel->hasilHumptyDumpty($nokun);
    if (isset($humptyDumpty)) {
      $totalHumptyDumpty = $humptyDumpty['HASIL'];

      if ($totalHumptyDumpty <= 7) {
        $hasilHumptyDumpty = "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b> ( Humpty Dumpty )</div>";
      } elseif ($totalHumptyDumpty <= 11) {
        $hasilHumptyDumpty = "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b> ( Humpty Dumpty )</div>";
      } elseif ($totalHumptyDumpty >= 12) {
        $hasilHumptyDumpty = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko tinggi</b> ( Humpty Dumpty )</div>";
      }
    }

    $jumlahHumptyDumpty = $this->KritisModel->get_count_humptyDumpty($nokun);
    // END HUMPTY DUMPTY

    $data = array(
      'hasilSkorIndeksBarthel' => isset($hasilSkorIndeksBarthel) ? $hasilSkorIndeksBarthel : null,
      'hasilSkalaMorse' => isset($hasilSkalaMorse) ? $hasilSkalaMorse : null,
      'hasilHumptyDumpty' => isset($hasilHumptyDumpty) ? $hasilHumptyDumpty : null,
      'hasilSkalaOntario' => isset($hasilSkalaOntario) ? $hasilSkalaOntario : null,

      'jumlahIndeksBarthel' => $jumlahIndeksBarthel,
      'jumlahSkalaMorse' => $jumlahSkalaMorse,
      'jumlahHumptyDumpty' => $jumlahHumptyDumpty,
      'jumlahSkalaOntario' => $jumlahSkalaOntario,

      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'idLoad' => $idLoad,
      'pasien' => $getNomr,
      'getPengkajian' => $getPengkajian,
      'user_perawat' => $this->session->userdata("id"),
      'anamnesis' => $this->masterModel->referensi(54),
      'jalanNapasPernapasan' => $this->masterModel->referensi(1191),
      'sumbatanJlnNpsPernpsn' => $this->masterModel->referensi(1186),
      'sesakNapasKritis' => $this->masterModel->referensi(637),
      'batukNapasKritis' => $this->masterModel->referensi(1192),
      'bukaMata' => $this->masterModel->referensi(1565),
      'responsMotorik' => $this->masterModel->referensi(1566),
      'responsVerbal' => $this->masterModel->referensi(1567),
      'isiBatukNapasKritis' => $this->masterModel->referensi(1455),
      'terapiOksigenNapasKritis' => $this->masterModel->referensi(1187),
      'terpasangDrainaseDada' => $this->masterModel->referensi(1188),
      'undulasiKritis' => $this->masterModel->referensi(1189),
      'kesadaran' => $this->masterModel->referensi(5),
      'drainKepala' => $this->masterModel->referensi(1190),
      'sedasi' => $this->masterModel->referensi(1767),
      'sirkulasiCrt' => $this->masterModel->referensi(1120),
      'sirkulasiEdema' => $this->masterModel->referensi(1119),
      'gambaranEkg' => $this->masterModel->referensi(1193),
      'gambaranEkgRegular' => $this->masterModel->referensi(1194),
      'gambaranEkgIreguler' => $this->masterModel->referensi(1195),
      'aksesVaskuler' => $this->masterModel->referensi(1235),
      'aksesVaskulerLokasi' => $this->masterModel->referensi(1236),
      'aksesVaskulerNilai' => $this->masterModel->referensi(1237),
      'kekuatanEkstremitas' => $this->masterModel->referensi(1238),
      'esofagus' => $this->masterModel->referensi(1239),
      'tenggorokan' => $this->masterModel->referensi(1240),
      'diet' => $this->masterModel->referensi(1241),
      'feedingTube' => $this->masterModel->referensi(1242),
      'feedingTubeNgt' => $this->masterModel->referensi(1243),
      'produkResidu' => $this->masterModel->referensi(1244),
      'buangAirBesar' => $this->masterModel->referensi(1245),
      'buangAirBesarKarakteristik' => $this->masterModel->referensi(1246),
      'drainAbdomen' => $this->masterModel->referensi(1247),
      'keluhanSaluranCema' => $this->masterModel->referensi(1248),
      'buangAirKecil' => $this->masterModel->referensi(1249),
      'buangAirKecilNetrostomi' => $this->masterModel->referensi(1250),
      'warnaPerkemihan' => $this->masterModel->referensi(1251),
      'distensi' => $this->masterModel->referensi(1252),
      'spoolingCatheterUrine' => $this->masterModel->referensi(1253),
      'genetaliaExternal' => $this->masterModel->referensi(1254),
      'pengeluaranPervaginam' => $this->masterModel->referensi(1255),
      'turgorKulit' => $this->masterModel->referensi(1257),
      'dekubitus' => $this->masterModel->referensi(1258),
      'dekubitusAda' => $this->masterModel->referensi(1259),
      'dekubitusGrade' => $this->masterModel->referensi(1260),
      'dekubitusEksudat' => $this->masterModel->referensi(1261),
      'dekubitusWarna' => $this->masterModel->referensi(1262),
      'dekubitusPerdarahan' => $this->masterModel->referensi(1263),
      'fraktur' => $this->masterModel->referensi(1264),
      'mobilisasi' => $this->masterModel->referensi(1266),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'efeksampingNRS' => $this->masterModel->referensi(118),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'responTerhadapPenyakit' => $this->masterModel->referensi(1273),
      'komunikasi' => $this->masterModel->referensi(1274),
      'dukunganKeluarga' => $this->masterModel->referensi(1275),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'sosialDanEkonomiPencariNafkah' => $this->masterModel->referensi(15),
      'sosialDanEkonomiTinggalSerumah' => $this->masterModel->referensi(16),
      'privasiKhusus' => $this->masterModel->referensi(1110),
      'apakahPenting' => $this->masterModel->referensi(1286),
      'programPengobatanDenganKeyakinan' => $this->masterModel->referensi(17),
      'restrain' => $this->masterModel->referensi(1287),
      'restrainYa' => $this->masterModel->referensi(1288),
      'gelangIdentitasPasien' => $this->masterModel->referensi(1289),
      'riwayatAlergi' => $this->masterModel->referensi(774),
      'terpasangKlipAlergi' => $this->masterModel->referensi(1290),
      'topikPembelajaran' => $this->masterModel->referensi(1291),
      'mediaPembelajaran' => $this->masterModel->referensi(1292),
      'apakahKeluargaTahu' => $this->masterModel->referensi(1293),
      'P31' => $this->masterModel->referensi(1135),
      'P32' => $this->masterModel->referensi(1136),
      'P33' => $this->masterModel->referensi(1137),
      'P34' => $this->masterModel->referensi(1138),
      'P35' => $this->masterModel->referensi(1139),
      'P36' => $this->masterModel->referensi(1140),
      'P37' => $this->masterModel->referensi(1141),
      'P38' => $this->masterModel->referensi(1142),
      'P39' => $this->masterModel->referensi(1143),
      'P310' => $this->masterModel->referensi(1144),
      'listRangsangbab' => $this->masterModel->referensi(834),
      'listRangsangberkemih' => $this->masterModel->referensi(835),
      'listMembersihkandiri' => $this->masterModel->referensi(836),
      'listPenggunaankloset' => $this->masterModel->referensi(837),
      'listMakan' => $this->masterModel->referensi(838),
      'listBerubahposisi' => $this->masterModel->referensi(839),
      'listBerpindah' => $this->masterModel->referensi(840),
      'listMemakaibaju' => $this->masterModel->referensi(841),
      'listNaiktangga' => $this->masterModel->referensi(842),
      'listMandi' => $this->masterModel->referensi(843),
      'hdsUmur' => $this->masterModel->referensi(1014),
      'hdsJk'   => $this->masterModel->referensi(1015),
      'hdsDa'   => $this->masterModel->referensi(1016),
      'hdsGk'   => $this->masterModel->referensi(1017),
      'hdsFl'   => $this->masterModel->referensi(1018),
      'hdsOb'   => $this->masterModel->referensi(1019),
      'hdsPo'   => $this->masterModel->referensi(1020),
      'listRiwayatJatuh' => $this->masterModel->referensi(1006),
      'listDiagnosisSekunder' => $this->masterModel->referensi(1007),
      'listAlatBantu' => $this->masterModel->referensi(1008),
      'listMenggunakanInfus' => $this->masterModel->referensi(1009),
      'listCaraBerjalan' => $this->masterModel->referensi(1010),
      'listStatusMental' => $this->masterModel->referensi(1011),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'listKarenaJatuh' => $this->masterModel->referensi(1674),
      'listJatuh2Bln' => $this->masterModel->referensi(1675),
      'listPasienDilirium' => $this->masterModel->referensi(1676),
      'listPasienDisorientasi' => $this->masterModel->referensi(1677),
      'listPasienAgitasi' => $this->masterModel->referensi(1678),
      'listPakaiKacamata' => $this->masterModel->referensi(1679),
      'listPenglihatanBuram' => $this->masterModel->referensi(1680),
      'listPasienGlaukoma' => $this->masterModel->referensi(1681),
      'listPerilakuBerkemih' => $this->masterModel->referensi(1682),
    );
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/pengkajianRiKritis', $data);
  }

  public function yakinVerifPengkajianOlehPerawat()
  {
    $post = $this->input->post();
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif_perawat' => 1,
      'verif_oleh_perawat' => $oleh,
    );

    $this->db->where('tb_keperawatan.id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

  public function asuhanKeperawatan_edit()
  {
    $id = $this->input->post('id');
    $idemr = $this->input->post('idemr');

    $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
    $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
    $getPengkajian = $this->KritisModel->getPengkajian($idemr);

    $data = array(
      'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
      'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
      'getPengkajian' => $getPengkajian,
    );

    $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
  }

  public function simpanIndeksBarthel()
  {
    $nokun = $this->input->post('nokun');
    $total_barthel_1 = $this->input->post('total_barthel_1');
    $total_barthel_2 = $this->input->post('total_barthel_2');
    $total_barthel_3 = $this->input->post('total_barthel_3');
    $total_barthel_4 = $this->input->post('total_barthel_4');
    $total_barthel_5 = $this->input->post('total_barthel_5');
    $total_barthel_6 = $this->input->post('total_barthel_6');
    $total_barthel_7 = $this->input->post('total_barthel_7');
    $total_barthel_8 = $this->input->post('total_barthel_8');
    $total_barthel_9 = $this->input->post('total_barthel_9');
    $total_barthel_10 = $this->input->post('total_barthel_10');
    $oleh = $this->session->userdata("id");

    $totalSkorIndeksBarthel = $total_barthel_1 + $total_barthel_2 + $total_barthel_3 + $total_barthel_4 + $total_barthel_5 + $total_barthel_6 + $total_barthel_7 + $total_barthel_8 + $total_barthel_9 + $total_barthel_10;

    if ($totalSkorIndeksBarthel == 20) {
      echo "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 4) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 8) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 11) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 19) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 10,
      'rangsang_bab' => $total_barthel_1,
      'rangsang_berkemih' => $total_barthel_2,
      'bersihkan_diri' => $total_barthel_3,
      'penggunaan_kloset' => $total_barthel_4,
      'makan' => $total_barthel_5,
      'berubah_posisi' => $total_barthel_6,
      'berpindah' => $total_barthel_7,
      'memakai_baju' => $total_barthel_8,
      'naik_tangga' => $total_barthel_9,
      'mandi' => $total_barthel_10,
      'oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->KritisModel->get_count_indeksBarthel($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_barthel_indek', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 10);
      $this->db->update('keperawatan.tb_barthel_indek', $data);
    }
  }

  public function simpanSkalaOntario()
  {
    $nokun = $this->input->post('nokun');
    $post = $this->input->post();
    $totalSkalaOntario = $this->input->post('totalSkalaOntarioMASS');

    $oleh = $this->session->userdata("id");

    if ($totalSkalaOntario >= 0 && $totalSkalaOntario <= 5) {
      echo "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Rendah</b> ( Skala Ontario ) </div>";
    } elseif ($totalSkalaOntario >= 6 && $totalSkalaOntario <= 16) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Sedang</b> ( Skala Ontario ) </div>";
    } elseif ($totalSkalaOntario >= 17) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Tinggi</b> ( Skala Ontario ) </div>";
    }

    $data = array(
      'nokun' => $post['nokun'],
      'ref' => 10,
      // 'diagnosa_masuk' => isset($post['diagnosa_masuk']) ? $post['diagnosa_masuk']: "",
      'tanggal' => isset($post['tanggalRiKritis']) ? date('Y-m-d', strtotime($post['tanggalRiKritis'])) : "",
      'karena_jatuh' => isset($post['karena_jatuh']) ? $post['karena_jatuh'] : "",
      'jatuh_2_bln' => isset($post['jatuh_2_bln']) ? $post['jatuh_2_bln'] : "",
      'pasien_dirilium' => isset($post['pasien_dirilium']) ? $post['pasien_dirilium'] : "",
      'pasien_disorientasi' => isset($post['pasien_disorientasi']) ? $post['pasien_disorientasi'] : "",
      'pasien_agitasi' => isset($post['pasien_agitasi']) ? $post['pasien_agitasi'] : "",
      'pakai_kacamata' => isset($post['pakai_kacamata']) ? $post['pakai_kacamata'] : "",
      'penglihatan_buram' => isset($post['penglihatan_buram']) ? $post['penglihatan_buram'] : "",
      'pasien_glaukoma' => isset($post['pasien_glaukoma']) ? $post['pasien_glaukoma'] : "",
      'perilaku_berkemih' => isset($post['perilaku_berkemih']) ? $post['perilaku_berkemih'] : "",
      'transfer' => isset($post['transfer']) ? $post['transfer'] : "",
      'mobilitas' => isset($post['mobilitas']) ? $post['mobilitas'] : "",
      'total' => isset($post['totalSkalaOntarioMASS']) ? $post['totalSkalaOntarioMASS'] : "",
      'tingkat_risiko' => isset($post['tingkat_risiko']) ? $post['tingkat_risiko'] : "",
      'oleh' => $this->session->userdata('id')
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->KritisModel->get_count_skalaOntario($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_skala_ontario_mass', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 10);
      $this->db->update('keperawatan.tb_skala_ontario_mass', $data);
    }
  }

  public function simpanSkalaMorse()
  {
    $nokun = $this->input->post('nokun');
    $riwayatJatuhBaruIniSkalaMorse = $this->input->post('riwayatJatuhBaruIniSkalaMorse');
    $diagnosisSekunderSkalaMorse = $this->input->post('diagnosisSekunderSkalaMorse');
    $alatBantuJalanSkalaMorse = $this->input->post('alatBantuJalanSkalaMorse');
    $menggunakanInfusSkalaMorse = $this->input->post('menggunakanInfusSkalaMorse');
    $caraBerjalanSkalaMorse = $this->input->post('caraBerjalanSkalaMorse');
    $statusMentalSkalaMorse = $this->input->post('statusMentalSkalaMorse');
    $totalSkalaMorse = $this->input->post('totalSkalaMorse');
    $oleh = $this->session->userdata("id");

    if ($totalSkalaMorse < 25) {
      echo "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaMorse . "</b> = <b>Tidak risiko</b> ( Skala Morse )</div>";
    } elseif ($totalSkalaMorse < 45) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko Rendah</b> ( Skala Morse )</div>";
    } elseif ($totalSkalaMorse >= 45) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko tinggi</b> ( Skala Morse )</div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 10,
      'riwayat_jatuh' => $riwayatJatuhBaruIniSkalaMorse,
      'diagnosis_sekunder' => $diagnosisSekunderSkalaMorse,
      'alat_bantu' => $alatBantuJalanSkalaMorse,
      'infus' => $menggunakanInfusSkalaMorse,
      'cara_berjalan' => $caraBerjalanSkalaMorse,
      'status_mental' => $statusMentalSkalaMorse,
      'oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->KritisModel->get_count_skalaMorse($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 10);
      $this->db->update('keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa', $data);
    }
  }

  public function simpanHumptyDumptyRiKritis()
  {
    $nokun = $this->input->post('nokun');
    $hdsUmur = $this->input->post('hdsUmur');
    $hdsJk = $this->input->post('hdsJk');
    $hdsDa = $this->input->post('hdsDa');
    $hdsGk = $this->input->post('hdsGk');
    $hdsFl = $this->input->post('hdsFl');
    $hdsOb = $this->input->post('hdsOb');
    $hdsPo = $this->input->post('hdsPo');
    $oleh = $this->session->userdata("id");

    $hdsUmurNilai = $this->KritisModel->getNilaiHumptyDumpty($hdsUmur);
    $hdsJkNilai = $this->KritisModel->getNilaiHumptyDumpty($hdsJk);
    $hdsDaNilai = $this->KritisModel->getNilaiHumptyDumpty($hdsDa);
    $hdsGkNilai = $this->KritisModel->getNilaiHumptyDumpty($hdsGk);
    $hdsFlNilai = $this->KritisModel->getNilaiHumptyDumpty($hdsFl);
    $hdsObNilai = $this->KritisModel->getNilaiHumptyDumpty($hdsOb);
    $hdsPoNilai = $this->KritisModel->getNilaiHumptyDumpty($hdsPo);

    $totalHumptyDumpty = $hdsUmurNilai + $hdsJkNilai + $hdsDaNilai + $hdsGkNilai + $hdsFlNilai + $hdsObNilai + $hdsPoNilai;

    if ($totalHumptyDumpty <= 7) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b>(Humpty Dumpty)</div>";
    } elseif ($totalHumptyDumpty <= 11) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b>(Humpty Dumpty)</div>";
    } elseif ($totalHumptyDumpty >= 12) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko tinggi</b>(Humpty Dumpty)</div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 10,
      'umur' => $hdsUmur,
      'jenis_kelamin' => $hdsJk,
      'diagnosa' => $hdsDa,
      'gangguan_kognitif' => $hdsGk,
      'faktor_lingkungan' => $hdsFl,
      'respon_terhadap_operasi' => $hdsOb,
      'penggunaan_obat' => $hdsPo,
      'oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->KritisModel->get_count_humptyDumpty($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_penilaian_humptydumpty_igd', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 10);
      $this->db->update('keperawatan.tb_penilaian_humptydumpty_igd', $data);
    }
  }

  public function yakinVerifPengkajian()
  {
    $post = $this->input->post();
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif' => 1,
      'verif_oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $this->db->where('id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

  public function simpanPengkajianRiKritis($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $dateTglMasukRiKritis = $this->input->post('tglMasukRiKritis');
        $tglMasukRiKritis = date('Y-m-d', strtotime($dateTglMasukRiKritis));

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'tanggal_masuk' => $tglMasukRiKritis,
          'pukul_masuk' => $post['pklMasukRiKritis'],
          'jenis' => 10,
          // 'rujukan' => $post['rujukanRiKritis'],
          // 'diagnosa_masuk' => $post['rujukanRiKritis'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 14,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0,
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataJalanPernapasan = array(
          'id_emr' => $getIdEmr,
          'jalan_napas' => isset($post['jalanNapasPernapasan']) ? json_encode($post['jalanNapasPernapasan']) : "",
          'jalan_napas_ett_ukuran' => isset($post['jalanNapasEttUkuran']) ? $post['jalanNapasEttUkuran'] : "",
          'jalan_napas_ett_bibir' => isset($post['jalanNapasEttBibir']) ? $post['jalanNapasEttBibir'] : "",
          'jalan_napas_ett_tekanan' => isset($post['jalanNapasEttTekanan']) ? $post['jalanNapasEttTekanan'] : "",
          'jalan_napas_trakeostomi_ukuran' => isset($post['jalanNapasTrakeostomiUkuran']) ? $post['jalanNapasTrakeostomiUkuran'] : "",
          'jalan_napas_sumbatan_ya' => isset($post['sumbatanNapasPernapasan']) ? $post['sumbatanNapasPernapasan'] : "",
          'jalan_napas_sumbatan_ya_ada' => isset($post['sumbatanYa']) ? json_encode($post['sumbatanYa']) : "",
          'jalan_napas_sesak' => isset($post['sesakNapasKritis']) ? $post['sesakNapasKritis'] : "",
          'jalan_napas_batuk' => isset($post['batukNapasKritis']) ? $post['batukNapasKritis'] : "",
          'isi_jalan_napas_batuk' => isset($post['isiBatukNapasKritis']) ? $post['isiBatukNapasKritis'] : "",
          'jalan_napas_terapi_oksigen' => isset($post['terapiOksigenNapas']) ? $post['terapiOksigenNapas'] : "",
          'jalan_napas_terpasang' => isset($post['terpasangDrainaseDada']) ? $post['terpasangDrainaseDada'] : "",
          'jalan_napas_undulasi' => isset($post['undulasiKritis']) ? $post['undulasiKritis'] : "",
          'jalan_napas_agd_ph' => isset($post['hasilLabPhRiKritis']) ? $post['hasilLabPhRiKritis'] : "",
          'jalan_napas_agd_p02' => isset($post['hasilLabP02RiKritis']) ? $post['hasilLabP02RiKritis'] : "",
          'jalan_napas_agd_pco2' => isset($post['hasilLabPco2RiKritis']) ? $post['hasilLabPco2RiKritis'] : "",
          'jalan_napas_agd_hco3' => isset($post['hasilLabHco3RiKritis']) ? $post['hasilLabHco3RiKritis'] : "",
          'jalan_napas_agd_be' => isset($post['hasilLabBeRiKritis']) ? $post['hasilLabBeRiKritis'] : "",
          'jalan_napas_agd_sa' => isset($post['hasilLabSao2RiKritis']) ? $post['hasilLabSao2RiKritis'] : "",
          'jalan_napas_hasil_foto_thorax' => isset($post['hasilFotoThoraxRiKritis']) ? $post['hasilFotoThoraxRiKritis'] : "",
        );

        // echo "<pre>data jalan pernapasan ";print_r($dataJalanPernapasan);echo "</pre>";

        $dataTandaVital = array(
          'data_source' => 14,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'map' => isset($post['MAP']) ? $post['MAP'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data tanda vital ";print_r($dataTandaVital);echo "</pre>";

        $dataSirkulasi = array(
          'id_emr' => $getIdEmr,
          'sirkulasi_crt' => isset($post['sirkulasi_Crt']) ? $post['sirkulasi_Crt'] : "",
          'sirkulasi_edema' => isset($post['sirkulasiEdemaRiKritis']) ? json_encode($post['sirkulasiEdemaRiKritis']) : "",
          'sirkulasi_edema_lainnya' => isset($post['edemaLainnya']) ? $post['edemaLainnya'] : "",
          'sirkulasi_gambaran_ekg' => isset($post['gambaranEkgRiKritis']) ? $post['gambaranEkgRiKritis'] : "",
          'sirkulasi_gambaran_ekg_lainnya' => isset($post['gambaranEkgLainnya']) ? $post['gambaranEkgLainnya'] : "",
          'sirkulasi_gambaran_regular' => isset($post['gambaranEkgRegular']) ? json_encode($post['gambaranEkgRegular']) : "",
          'sirkulasi_gambaran_iregular' => isset($post['gambaranEkgIreguler']) ? json_encode($post['gambaranEkgIreguler']) : "",
          'sirkulasi_akses' => isset($post['aksesVaskulerRiKritis']) ? json_encode($post['aksesVaskulerRiKritis']) : "",
          'sirkulasi_akses_lainnya' => isset($post['aksesLainnya']) ? $post['aksesLainnya'] : "",
          'sirkulasi_akses_central' => isset($post['aksesVaskulerJenisRiKritis']) ? $post['aksesVaskulerJenisRiKritis'] : "",
          'sirkulasi_akses_lokasi' => isset($post['aksesVaskulerLokasiRiKritis']) ? json_encode($post['aksesVaskulerLokasiRiKritis']) : "",
          'sirkulasi_akses_nilai' => isset($post['aksesVaskulerNilaiCvpRiKritis']) ? $post['aksesVaskulerNilaiCvpRiKritis'] : "",
          'sirkulasi_hasil_lab_hb' => isset($post['hasilLabHbRiKritis']) ? $post['hasilLabHbRiKritis'] : "",
          'sirkulasi_hasil_lab_leu' => isset($post['hasilLabLeuRiKritis']) ? $post['hasilLabLeuRiKritis'] : "",
          'sirkulasi_hasil_lab_tr' => isset($post['hasilLabTrRiKritis']) ? $post['hasilLabTrRiKritis'] : "",
          'sirkulasi_hasil_lab_ht' => isset($post['hasilLabHtRiKritis']) ? $post['hasilLabHtRiKritis'] : "",
          'sirkulasi_hasil_echo' => isset($post['hasilEchoRiKritis']) ? $post['hasilEchoRiKritis'] : "",
        );

        // echo "<pre>data Sirkulasi ";print_r($dataSirkulasi);echo "</pre>";

        $dataKesadaran = array(
          'data_source' => 14,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data kesadaran ";print_r($dataKesadaran);echo "</pre>";

        $dataNeurologi = array(
          'id_emr' => $getIdEmr,
          'sedasi_tidak' => isset($post['sedasiTidak']) ? $post['sedasiTidak'] : "",
          'sedasi_tidak_desk' => isset($post['deskSedasiTidak']) ? $post['deskSedasiTidak'] : "",
          'neurologi_drain_kepala' => isset($post['drainKepala']) ? $post['drainKepala'] : "",
          'neurologi_drain_kepala_desk' => isset($post['deskDrainKepala']) ? $post['deskDrainKepala'] : "",
          'glass_buka_mata' => isset($post['buka_mataKritis']) ? $post['buka_mataKritis'] : "",
          'glass_respon_motorik' => isset($post['respons_motorikKritis']) ? $post['respons_motorikKritis'] : "",
          'glass_respon_verbal' => isset($post['respons_verbalKritis']) ? $post['respons_verbalKritis'] : "",
          'neurologi_kekuatan' => isset($post['kekuatanEkstremitasRiKritis']) ? json_encode($post['kekuatanEkstremitasRiKritis']) : "",
          'neurologi_kekuatan_atas_kanan' => isset($post['isiKekuatanAtasKanan']) ? $post['isiKekuatanAtasKanan'] : "",
          'neurologi_kekuatan_atas_kiri' => isset($post['isiKekuatanAtasKiri']) ? $post['isiKekuatanAtasKiri'] : "",
          'neurologi_kekuatan_bawah_kanan' => isset($post['isiKekuatanBawahKanan']) ? $post['isiKekuatanBawahKanan'] : "",
          'neurologi_kekuatan_bawah_kiri' => isset($post['isiKekuatanBawahKiri']) ? $post['isiKekuatanBawahKiri'] : "",
          'neurologi_elektrolit_na' => isset($post['elektrolitNaRiKritis']) ? $post['elektrolitNaRiKritis'] : "",
          'neurologi_elektrolit_k' => isset($post['elektrolitKRiKritis']) ? $post['elektrolitKRiKritis'] : "",
          'neurologi_elektrolit_cl' => isset($post['elektrolitClRiKritis']) ? $post['elektrolitClRiKritis'] : "",
          'neurologi_elektrolit_ca' => isset($post['elektrolitCaRiKritis']) ? $post['elektrolitCaRiKritis'] : "",
          'neurologi_hasil_mri' => isset($post['hasilMriCtScanRiKritis']) ? $post['hasilMriCtScanRiKritis'] : "",
        );

        // echo "<pre>data Neurologi ";print_r($dataNeurologi);echo "</pre>";

        $dataPencernaan = array(
          'id_emr' => $getIdEmr,
          'pencernaan_reflek' => isset($post['esofagusRiKritis']) ? $post['esofagusRiKritis'] : "",
          'pencernaan_tenggorokan' => isset($post['tenggorokanRiKritis']) ? $post['tenggorokanRiKritis'] : "",
          'pencernaan_diet' => isset($post['dietRiKritis']) ? $post['dietRiKritis'] : "",
          'pencernaan_feeding' => isset($post['feedingTubeRiKritis']) ? $post['feedingTubeRiKritis'] : "",
          'pencernaan_feeding_pasang' => isset($post['feedingTubeNgtRiKritis']) ? $post['feedingTubeNgtRiKritis'] : "",
          'pencernaan_produk' => isset($post['produkResiduRiKritis']) ? $post['produkResiduRiKritis'] : "",
          'pencernaan_buang_air_besar' => isset($post['buangAirBesarRiKritis']) ? $post['buangAirBesarRiKritis'] : "",
          'pencernaan_warna' => isset($post['buangAirBesarWarna']) ? $post['buangAirBesarWarna'] : "",
          'pencernaan_karakter' => isset($post['buangAirBesarKarakteristikRiKritis']) ? $post['buangAirBesarKarakteristikRiKritis'] : "",
          'pencernaan_drain_abdomen' => isset($post['drainAbdomenRiKritis']) ? $post['drainAbdomenRiKritis'] : "",
          'pencernaan_keluhan' => isset($post['keluhanSaluranCemaRiKritis']) ? $post['keluhanSaluranCemaRiKritis'] : "",
          'pencernaan_hasil_usg' => isset($post['hasilUsgAbdomenRiKritis']) ? $post['hasilUsgAbdomenRiKritis'] : "",
        );

        // echo "<pre>data Pencernaan ";print_r($dataPencernaan);echo "</pre>";

        $dataPerkemihan = array(
          'id_emr' => $getIdEmr,
          'perkemihan_air_kecil' => isset($post['buangAirKecilRiKritis']) ? json_encode($post['buangAirKecilRiKritis']) : "",
          'perkemihan_netrostomi' => isset($post['buangAirKecilNetrostomiRiKritis']) ? $post['buangAirKecilNetrostomiRiKritis'] : "",
          'perkemihan_warna' => isset($post['warnaPerkemihanRiKritis']) ? $post['warnaPerkemihanRiKritis'] : "",
          'perkemihan_distensi' => isset($post['distensiRiKritis']) ? $post['distensiRiKritis'] : "",
          'perkemihan_spooling' => isset($post['spoolingCatheterUrineRiKritis']) ? $post['spoolingCatheterUrineRiKritis'] : "",
          'perkemihan_jumlah_cc' => isset($post['jumlahUrinCcRiKritis']) ? $post['jumlahUrinCcRiKritis'] : "",
          'perkemihan_jumlah_jam' => isset($post['jumlahUrinJamRiKritis']) ? $post['jumlahUrinJamRiKritis'] : "",
          'perkemihan_genetalia' => isset($post['genetaliaExternalRiKritis']) ? $post['genetaliaExternalRiKritis'] : "",
          'perkemihan_pengeluaran' => isset($post['pengeluaranPervaginamRiKritis']) ? $post['pengeluaranPervaginamRiKritis'] : "",
          'perkemihan_hasil_lab' => isset($post['hasilLaboratoriumFungsiGinjalRiKritis']) ? $post['hasilLaboratoriumFungsiGinjalRiKritis'] : "",
          'perkemihan_hasil_usg' => isset($post['hasilUsgGinialRiKritis']) ? $post['hasilUsgGinialRiKritis'] : "",
        );

        // echo "<pre>data Perkemihan ";print_r($dataPerkemihan);echo "</pre>";

        $dataIntegumen = array(
          'id_emr' => $getIdEmr,
          'inte_turgor' => isset($post['turgorKulitRiKritis']) ? $post['turgorKulitRiKritis'] : "",
          'inte_dekubitus' => isset($post['dekubitusRiKritis']) ? $post['dekubitusRiKritis'] : "",
          'inte_dek_grade' => isset($post['dekubitusGradeRiKritis']) ? $post['dekubitusGradeRiKritis'] : "",
          'inte_dek_eksudat' => isset($post['dekubitusEksudatRiKritis']) ? $post['dekubitusEksudatRiKritis'] : "",
          'inte_dek_warna' => isset($post['dekubitusWarnaRiKritis']) ? $post['dekubitusWarnaRiKritis'] : "",
          'inte_dek_perdarahan' => isset($post['dekubitusPerdarahanRiKritis']) ? $post['dekubitusPerdarahanRiKritis'] : "",
          'inte_fraktur' => isset($post['frakturRiKritis']) ? $post['frakturRiKritis'] : "",
          'inte_fisiologis' => isset($post['fisiologisLokasiRiKritis']) ? json_encode($post['fisiologisLokasiRiKritis']) : "",
          'inte_patologis' => isset($post['patologisLokasiRiKritis']) ? json_encode($post['patologisLokasiRiKritis']) : "",
          'inte_mobilisasi' => isset($post['mobilisasiRiKritis']) ? $post['mobilisasiRiKritis'] : "",
        );

        // echo "<pre>data Integumen ";print_r($dataIntegumen);echo "</pre>";

        $dataSkriningNyeri = array(
          'nokun' => $post['nokun'],
          'data_source' => 14,
          'ref' => $getIdEmr,
          'metode' => isset($post['skrining_nyeri_RiKritis']) ? $post['skrining_nyeri_RiKritis'] : "",
          'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'provokative' => isset($post['propocative_RiKritis']) ? $post['propocative_RiKritis'] : "",
          'quality' => isset($post['quality_RiKritis']) ? $post['quality_RiKritis'] : "",
          'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
          'regio' => isset($post['regio_RiKritis']) ? $post['regio_RiKritis'] : "",
          'severity' => isset($post['severity_RiKritis']) ? $post['severity_RiKritis'] : "",
          'time' => isset($post['time_RiKritis']) ? $post['time_RiKritis'] : "",
          'ket_time' => isset($post['durasi_nyeri_RiKritis']) ? $post['durasi_nyeri_RiKritis'] : "",
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining nyeri ";print_r($dataSkriningNyeri);echo "</pre>";

        $dataPsikoSosial = array(
          'id_emr' => $getIdEmr,
          'psikologis_respon' => isset($post['responTerhadapPenyakitRiKritis']) ? $post['responTerhadapPenyakitRiKritis'] : "",
          'psikologis_komunikasi' => isset($post['komunikasiRiKritis']) ? $post['komunikasiRiKritis'] : "",
          'psikologis_dukungan' => isset($post['dukunganKeluargaRiKritis']) ? $post['dukunganKeluargaRiKritis'] : "",
          'sosial_hubungan' => isset($post['sdehRiKritis']) ? $post['sdehRiKritis'] : "",
          'sosial_pencari_nafkah' => isset($post['sdepnRiKritis']) ? $post['sdepnRiKritis'] : "",
          'sosial_tinggal_serumah' => isset($post['sosialDanEkonomiTinggalSerumahRiKritis']) ? $post['sosialDanEkonomiTinggalSerumahRiKritis'] : "",
          'sosial_kebutuhan' => isset($post['privasiKhususRiKritis']) ? $post['privasiKhususRiKritis'] : "",
          'sosial_agama' => isset($post['apakahPentingRiKritis']) ? $post['apakahPentingRiKritis'] : "",
          'sosial_pengobatan' => isset($post['programPengobatanDenganKeyakinanRiKritis']) ? $post['programPengobatanDenganKeyakinanRiKritis'] : "",
        );

        // echo "<pre>data Psikologis Sosial ";print_r($dataPsikoSosial);echo "</pre>";

        $dataKesKeb = array(
          'id_emr' => $getIdEmr,
          'keselamatan_restrain' => isset($post['restrainRiKritis']) ? $post['restrainRiKritis'] : "",
          'keselamatan_restrain_ya' => isset($post['restrainYaRiKritis']) ? json_encode($post['restrainYaRiKritis']) : "",
          'keselamatan_gelang' => isset($post['gelangIdentitasPasienRiKritis']) ? $post['gelangIdentitasPasienRiKritis'] : "",
          'keselamatan_alergi' => isset($post['riwayatAlergiRiKritis']) ? $post['riwayatAlergiRiKritis'] : "",
          'keselamatan_alergi_jenis' => isset($post['jenisAlergiRiKritis']) ? json_encode($post['jenisAlergiRiKritis']) : "",
          'keselamatan_alergi_klip' => isset($post['terpasangKlipAlergiRiKritis']) ? $post['terpasangKlipAlergiRiKritis'] : "",
          'kebutuhan_topik' => isset($post['topikPembelajaranRiKritis']) ? json_encode($post['topikPembelajaranRiKritis']) : "",
          'kebutuhan_media' => isset($post['mediaPembelajaranRiKritis']) ? $post['mediaPembelajaranRiKritis'] : "",
        );

        // echo "<pre>data Keselamatan Kebutuhan ";print_r($dataKesKeb);echo "</pre>";

        $dataP3 = array(
          'id_emr' => $getIdEmr,
          'memerlukan_p3' => isset($post['apakahKeluargaTahuRiKritis']) ? $post['apakahKeluargaTahuRiKritis'] : "",
          'umur_65' => isset($post['pasien_perlu_p31RiKritis']) ? $post['pasien_perlu_p31RiKritis'] : 3788,
          'ket_umur_65' => isset($post['ketP31RiKritis']) ? $post['ketP31RiKritis'] : "",
          'keterbatasan_mobilitas' => isset($post['pasien_perlu_p32RiKritis']) ? $post['pasien_perlu_p32RiKritis'] : 3790,
          'ket_keterbatasan_mobilitas' => isset($post['ketP32RiKritis']) ? $post['ketP32RiKritis'] : "",
          'perawatan' => isset($post['pasien_perlu_p33RiKritis']) ? $post['pasien_perlu_p33RiKritis'] : 3792,
          'ket_perawatan' => isset($post['ketP33RiKritis']) ? $post['ketP33RiKritis'] : "",
          'pasien_tinggal_sendiri' => isset($post['pasien_perlu_p34RiKritis']) ? $post['pasien_perlu_p34RiKritis'] : 3794,
          'ket_pasien_tinggal_sendiri' => isset($post['ketP34RiKritis']) ? $post['ketP34RiKritis'] : "",
          'adakah_keluarga' => isset($post['pasien_perlu_p35RiKritis']) ? $post['pasien_perlu_p35RiKritis'] : 3796,
          'ket_adakah_keluarga' => isset($post['ketP35RiKritis']) ? $post['ketP35RiKritis'] : "",
          'pasien_tanggung_jawab_anak' => isset($post['pasien_perlu_p36RiKritis']) ? $post['pasien_perlu_p36RiKritis'] : 3798,
          'ket_pasien_tanggung_jawab_anak' => isset($post['ketP36RiKritis']) ? $post['ketP36RiKritis'] : "",
          'pasien_pulang_bawa_obat' => isset($post['pasien_perlu_p37RiKritis']) ? $post['pasien_perlu_p37RiKritis'] : 3800,
          'ket_pasien_pulang_bawa_obat' => isset($post['ketP37RiKritis']) ? $post['ketP37RiKritis'] : "",
          'risiko_infeksi' => isset($post['pasien_perlu_p38RiKritis']) ? $post['pasien_perlu_p38RiKritis'] : 3802,
          'ket_risiko_infeksi' => isset($post['ketP38RiKritis']) ? $post['ketP38RiKritis'] : "",
          'efek_samping' => isset($post['pasien_perlu_p39RiKritis']) ? $post['pasien_perlu_p39RiKritis'] : 3804,
          'ket_efek_samping' => isset($post['ketP39RiKritis']) ? $post['ketP39RiKritis'] : "",
          'masalah_untuk_transportasi' => isset($post['pasien_perlu_p310RiKritis']) ? $post['pasien_perlu_p310RiKritis'] : 3806,
          'ket_masalah_untuk_transportasi' => isset($post['ketP310RiKritis']) ? $post['ketP310RiKritis'] : "",
          'jenis' => 1,
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data perencanaan pemulangan pasien ";print_r($dataP3);echo "</pre>";
        // exit();

        $this->db->trans_begin();
        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 14);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          // $this->db->replace('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->replace('keperawatan.tb_pernapasan_kritis', $dataJalanPernapasan);
          $this->db->replace('keperawatan.tb_sirkulasi_kritis', $dataSirkulasi);
          $this->db->replace('keperawatan.tb_neurologi_kritis', $dataNeurologi);
          $this->db->replace('keperawatan.tb_pencernaan_kritis', $dataPencernaan);
          $this->db->replace('keperawatan.tb_perkemihan_kritis', $dataPerkemihan);
          $this->db->replace('keperawatan.tb_integumen_kritis', $dataIntegumen);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 14);
          $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
          // $this->db->replace('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 14);
          $this->db->update('db_pasien.tb_kesadaran', $dataKesadaran);
          // $this->db->replace('db_pasien.tb_kesadaran', $dataKesadaran);
          $this->db->where('nokun', $post['nokun']);
          $this->db->where('data_source', 14);
          $this->db->update('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          // $this->db->replace('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->replace('keperawatan.tb_psikologis_sosial_budaya_kritis', $dataPsikoSosial);
          $this->db->replace('keperawatan.tb_keselamatan_kebutuhan_kritis', $dataKesKeb);
          $this->db->replace('keperawatan.tb_perencanaan_pemulangan_pasien', $dataP3);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
          $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $post['idemr']));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->insert('keperawatan.tb_pernapasan_kritis', $dataJalanPernapasan);
          $this->db->insert('keperawatan.tb_sirkulasi_kritis', $dataSirkulasi);
          $this->db->insert('keperawatan.tb_neurologi_kritis', $dataNeurologi);
          $this->db->insert('keperawatan.tb_pencernaan_kritis', $dataPencernaan);
          $this->db->insert('keperawatan.tb_perkemihan_kritis', $dataPerkemihan);
          $this->db->insert('keperawatan.tb_integumen_kritis', $dataIntegumen);
          $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->insert('db_pasien.tb_kesadaran', $dataKesadaran);
          $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->insert('keperawatan.tb_psikologis_sosial_budaya_kritis', $dataPsikoSosial);
          $this->db->insert('keperawatan.tb_keselamatan_kebutuhan_kritis', $dataKesKeb);
          $this->db->insert('keperawatan.tb_perencanaan_pemulangan_pasien', $dataP3);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      } else if ($param == 'count') {
        $result = $this->KritisModel->get_count();
        echo json_encode($result);
      } else if ($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataKritisModel = $this->KritisModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataKritisModel
        ));
      }
    }
  }
}

/* End of file PasienKritis.php */
/* Location: ./application/controllers/rekam_medis/pengkajian/pengkajianRiLain/PasienKritis.php */