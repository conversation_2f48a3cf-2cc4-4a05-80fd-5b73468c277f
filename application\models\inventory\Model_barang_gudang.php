<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Model_barang_gudang extends CI_Model {

	var $table = 'invenumum.v_barang';
	var $column_order = array(null, 'BARANG','SATUAN','STOK'); //set column field database for datatable orderable
	var $column_search = array('BARANG','SATUAN','STOK'); //set column field database for datatable searchable 
	var $order = array('ID_BARANG_GUDANG' => 'asc'); // default order 

	public function __construct()
	{
		parent::__construct();
		$this->load->database();
	}

	public function barangGudang()
	{
		$query  = $this->db->query("SELECT bg.ID_BARANG_GUDANG ID, bm.ID_BARANG, bg.STOK, ru.DESKRIPSI GUDANG, bm.HARGA, bg.`STATUS`, sa.SATUAN, bm.BARANG NAMA
			FROM invenumum.barang_gudang bg
			left join invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
			left join master.ruangan ru ON ru.ID=bg.GUDANG
			LEFT JOIN invenumum.satuan_ sa ON sa.ID_SATUAN=bm.SATUAN
			ORDER BY ru.DESKRIPSI DESC");
		return $query;
	}

	private function _get_datatables_query()
	{
		
		//add custom filter here
		if($this->input->post('GUDANG'))
		{
			$this->db->where('GUDANG', $this->input->post('GUDANG'));
		}

		$this->db->from($this->table);
		
		$i = 0;

		foreach ($this->column_search as $item) // loop column 
		{
			if($_POST['search']['value']) // if datatable send POST for search
			{
				
				if($i===0) // first loop
				{
					$this->db->group_start(); // open bracket. query Where with OR clause better with bracket. because maybe can combine with other WHERE with AND.
					$this->db->like($item, $_POST['search']['value']);
				}
				else
				{
					$this->db->or_like($item, $_POST['search']['value']);
				}

				if(count($this->column_search) - 1 == $i) //last loop
					$this->db->group_end(); //close bracket
				}
				$i++;
			}

		if(isset($_POST['order'])) // here order processing
		{
			$this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->db->order_by(key($order), $order[key($order)]);
		}
	}

	public function get_datatables()
	{
		$this->_get_datatables_query();
		if($_POST['length'] != -1)
			$this->db->limit($_POST['length'], $_POST['start']);
		$query = $this->db->get();
		return $query->result();
	}

	public function count_filtered()
	{
		$this->_get_datatables_query();
		$query = $this->db->get();
		return $query->num_rows();
	}

	public function count_all()
	{
		$this->db->from($this->table);
		return $this->db->count_all_results();
	}

	public function get_list_gudang()
	{
		$this->db->select('ID,DESKRIPSI');
		$this->db->from("master.ruangan where ID like '%1060602%'");
		$this->db->order_by('ID','asc');
		$query = $this->db->get();
		$result = $query->result();

		$gdng = array();
		foreach ($result as $row) 
		{
			$gdng[] = $row->DESKRIPSI;
		}
		return $gdng;

	}

	function tampilkan_gudang()
	{
		$query  ="SELECT * from master.ruangan where ID like '%1060602%'";
		return $this->db->query($query);
	}

	function tampl_barang()
	{
		$query  ="SELECT * from invenumum.barang_master";
		return $this->db->query($query);
	}

	function tampil_barang_gudang()
	{
		$query  ="SELECT bg.ID_BARANG_GUDANG, bm.ID_BARANG, bm.BARANG, bg.GUDANG ID_GUDANG, r.DESKRIPSI GUDANG  
		from invenumum.barang_gudang bg
		LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
		LEFT JOIN master.ruangan r ON r.ID=bg.GUDANG";
		return $this->db->query($query);
	}

	public function modaldatabaranggudang($id)
	{
		$query = $this->db->query(
			"SELECT bg.ID_BARANG_GUDANG, bm.ID_BARANG, bm.BARANG, bg.GUDANG ID_GUDANG, r.DESKRIPSI GUDANG  
			from invenumum.barang_gudang bg
			LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
			LEFT JOIN master.ruangan r ON r.ID=bg.GUDANG 
			WHERE bg.ID_BARANG_GUDANG='$id'");
		return $query->result_array();
	}

	public function update($id, $data)
	{
		$this->db->where('ID_BARANG_GUDANG', $id);
		$this->db->update('invenumum.barang_gudang', $data);
	}

	function post($data)
	{
		$this->db->insert('invenumum.barang_gudang',$data);
	}

	public function sBarangAktif($id, $data)
	{
		$this->db->where('ID_BARANG_GUDANG', $id);
		$this->db->update('invenumum.barang_gudang', $data);
	}

	public function sBarangNonAktif($id, $data)
	{
		$this->db->where('ID_BARANG_GUDANG', $id);
		$this->db->update('invenumum.barang_gudang', $data);
	}

}
