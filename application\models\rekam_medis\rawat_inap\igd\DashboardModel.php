<?php
defined('BASEPATH') or exit('No direct script access allowed');

class DashboardModel extends MY_Model
{
    function __construct()
    {
        parent::__construct();
    }

    public function tglHasilLab($nopen)
    {
        $this->db->select('ol.NOMOR no_order, ol.TANGGAL tanggal_order');
        $this->db->from('layanan.order_lab ol');
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = ol.KUNJUNGAN', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
        $this->db->join('pendaftaran.kunjungan k1', 'k1.REF = ol.NOMOR AND p.NOMOR = k.NOPEN'); // Kunjungan lab
        $this->db->join('master.ruangan r', 'r.ID = k.RUANGAN', 'left');
        $this->db->where('ol.STATUS', 2);
        $this->db->where('p.NOMOR', $nopen);
        $this->db->group_by('ol.NOMOR');
        $this->db->order_by('ol.TANGGAL', 'desc');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function tabelLab($nopen, $no_order = null)
    {
        $this->db->select(
            'p.NORM nomr, r.DESKRIPSI ruang_order, ol.NOMOR no_order, ol.TANGGAL tanggal_order, k.NOMOR nokun_order,
            k1.MASUK masuk_lab, k1.NOMOR nokun_lab'
        );
        $this->db->from('layanan.order_lab ol');
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = ol.KUNJUNGAN', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
        $this->db->join('pendaftaran.kunjungan k1', 'k1.REF = ol.NOMOR AND p.NOMOR = k.NOPEN'); // Kunjungan lab
        $this->db->join('master.ruangan r', 'r.ID = k.RUANGAN', 'left');
        $this->db->where('ol.STATUS', 2);
        $this->db->where('p.NOMOR', $nopen);
        if ($no_order != null) { // Jika pilihan tanggal tidak kosong
            $this->db->where('ol.NOMOR', $no_order);
        }
        $this->db->group_by('ol.NOMOR');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function ambilPemeriksaanLab($nopen, $no_order = null)
    {
        $this->db->select(
            "ol.kunjungan nokun_order, p.NORM nomr, CONCAT(t.NAMA, ' - ', hl.LIS_NAMA_TEST) parameter,
            hl.LIS_NAMA_TEST desk_parameter, p.NOMOR nopen, t.ID id_tindakan_simpel, t.NAMA tindakan_simpel"
        );
        $this->db->from('lis.hasil_log hl');
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = hl.HIS_NO_LAB', 'left');
        $this->db->join('master.tindakan t', 't.ID = hl.HIS_KODE_TEST', 'left');
        $this->db->join('layanan.order_lab ol', 'ol.NOMOR = k.REF', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
        $this->db->where('k.NOPEN', $nopen);
        if ($no_order != null) { // Jika pilihan tanggal tidak kosong
            $this->db->where('ol.NOMOR', $no_order);
        }
        $this->db->group_by('t.ID');
        $this->db->group_by('hl.LIS_NAMA_TEST');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function ambilHasilLab($nopen, $no_order = null)
    {
        if ($no_order == null) {
            $query = $this->db->query(
                "SELECT
                a.nomr, master.getNamaLengkap(a.nomr) nama, a.nopen, ol2.nokun_order, ol2.ruang_order, ol2.no_order,
                ol2.tanggal_order, ol2.nokun_lab, ol2.masuk_lab, a.tindakan_simpel, a.id_tindakan_simpel,
                CONCAT(a.tindakan_simpel, ' - ', a.parameter) parameter, (
                    SELECT hl.LIS_HASIL
                    FROM lis.hasil_log hl
                        LEFT JOIN pendaftaran.kunjungan k ON k.NOMOR = hl.HIS_NO_LAB
                        LEFT JOIN master.tindakan t ON t.ID = hl.HIS_KODE_TEST
                    WHERE hl.HIS_NO_LAB = ol2.nokun_lab
                        AND hl.LIS_NAMA_TEST = a.parameter
                        AND hl.HIS_KODE_TEST = a.id_tindakan_simpel
                ) hasil
            FROM (
                SELECT
                    ol.KUNJUNGAN nokun_order, p.NORM nomr, hl.LIS_NAMA_TEST parameter, p.NOMOR nopen,
                    t.ID id_tindakan_simpel, t.NAMA tindakan_simpel
                FROM lis.hasil_log hl
                    LEFT JOIN pendaftaran.kunjungan k ON k.NOMOR = hl.HIS_NO_LAB
                    LEFT JOIN master.tindakan t ON t.ID = hl.HIS_KODE_TEST
                    LEFT JOIN layanan.order_lab ol ON ol.NOMOR = k.REF
                    LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = k.nopen
                WHERE p.NOMOR = '$nopen'
                GROUP BY
                    t.ID,
                    hl.LIS_NAMA_TEST
            ) a
            LEFT JOIN (
                SELECT
                    p1.NORM nomr1, ol1.NOMOR no_order, k1.NOMOR nokun_order, k2.NOMOR nokun_lab,
                    k2.MASUK masuk_lab, ol1.TANGGAL tanggal_order, r.DESKRIPSI ruang_order
                FROM layanan.order_lab ol1
                    LEFT JOIN pendaftaran.kunjungan k1 ON k1.NOMOR = ol1.KUNJUNGAN
                    LEFT JOIN pendaftaran.pendaftaran p1 ON p1.NOMOR = k1.nopen
                    LEFT JOIN pendaftaran.kunjungan k2 ON k2.REF = ol1.NOMOR AND p1.NOMOR = k1.nopen
                    LEFT JOIN master.ruangan r ON r.ID = k1.RUANGAN
                WHERE ol1.STATUS = 2
                    AND p1.NOMOR = '$nopen'
                GROUP BY ol1.NOMOR
            ) ol2 ON ol2.nomr1 = a.nomr
            ORDER BY
                ol2.tanggal_order ASC,
                a.parameter ASC"
            );
        } else {
            $query = $this->db->query(
                "SELECT
                a.nomr, master.getNamaLengkap(a.nomr) nama, a.nopen, ol2.nokun_order, ol2.ruang_order, ol2.no_order,
                ol2.tanggal_order, ol2.nokun_lab, ol2.masuk_lab, a.tindakan_simpel, a.id_tindakan_simpel,
                CONCAT(a.tindakan_simpel, ' - ', a.parameter) parameter, (
                    SELECT hl.LIS_HASIL
                    FROM lis.hasil_log hl
                        LEFT JOIN pendaftaran.kunjungan k ON k.NOMOR = hl.HIS_NO_LAB
                        LEFT JOIN master.tindakan t ON t.ID = hl.HIS_KODE_TEST
                    WHERE hl.HIS_NO_LAB = ol2.nokun_lab
                        AND hl.LIS_NAMA_TEST = a.parameter
                        AND hl.HIS_KODE_TEST = a.id_tindakan_simpel
                ) hasil
            FROM (
                SELECT
                    ol.KUNJUNGAN nokun_order, p.NORM nomr, hl.LIS_NAMA_TEST parameter, p.NOMOR nopen,
                    t.ID id_tindakan_simpel, t.NAMA tindakan_simpel
                FROM lis.hasil_log hl
                    LEFT JOIN pendaftaran.kunjungan k ON k.NOMOR = hl.HIS_NO_LAB
                    LEFT JOIN master.tindakan t ON t.ID = hl.HIS_KODE_TEST
                    LEFT JOIN layanan.order_lab ol ON ol.NOMOR = k.REF
                    LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = k.nopen
                WHERE p.NOMOR = '$nopen'
                    AND ol.NOMOR = '$no_order'
                GROUP BY
                    t.ID,
                    hl.LIS_NAMA_TEST
            ) a
            LEFT JOIN (
                SELECT
                    p1.NORM nomr1, ol1.NOMOR no_order, k1.NOMOR nokun_order, k2.NOMOR nokun_lab,
                    k2.MASUK masuk_lab, ol1.TANGGAL tanggal_order, r.DESKRIPSI ruang_order
                FROM layanan.order_lab ol1
                    LEFT JOIN pendaftaran.kunjungan k1 ON k1.NOMOR = ol1.KUNJUNGAN
                    LEFT JOIN pendaftaran.pendaftaran p1 ON p1.NOMOR = k1.nopen
                    LEFT JOIN pendaftaran.kunjungan k2 ON k2.REF = ol1.NOMOR AND p1.NOMOR = k1.nopen
                    LEFT JOIN master.ruangan r ON r.ID = k1.RUANGAN
                WHERE ol1.STATUS = 2
                    AND ol1.NOMOR = '$no_order'
                    AND p1.NOMOR = '$nopen'
                GROUP BY ol1.NOMOR
            ) ol2 ON ol2.nomr1 = a.nomr
            ORDER BY
                ol2.tanggal_order ASC,
                a.parameter ASC"
            );
        }
        return $query->result_array();
    }
}

/* End of file Dashboard.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/Dashboard.php */