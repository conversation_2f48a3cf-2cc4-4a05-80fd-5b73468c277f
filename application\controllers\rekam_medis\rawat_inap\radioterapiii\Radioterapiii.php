<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Radioterapiii extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'radioterapi/TreatmentDoseModel',
      'radioterapi/Ct_simulatorModel',
      'radioterapi/RadioterapiModel'
    ));
  }

  public function index(){
    // $norm = $this->uri->segment(6);
    // $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $hPengkajianRater = $this->pengkajianAwalModel->historyPengkajianRaterRi();
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      'nokun' => $nokun,
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'efeksampingNRS' => $this->masterModel->referensi(118),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'RisikoJatuhTR' => $this->masterModel->referensi(259),
      'kesadaran' => $this->masterModel->referensi(5),
      'hPengkajianRater' => $hPengkajianRater,
      'getNomr' => $getNomr,
    );
     // print_r($data);exit();
    $this->load->view('Pengkajian/radioTerapi/index', $data);
  }

  public function indexCT(){
    // $norm = $this->uri->segment(6);
    // $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $hPengkajianRater = $this->pengkajianAwalModel->historyPengkajianRaterRi();
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      'nokun' => $nokun,
      'In_Case_Of_Pelvic_Treatment_bladeer' => $this->masterModel->referensi(693),
      'Position_of_Patient' => $this->masterModel->referensi(678),
      'Mask' => $this->masterModel->referensi(1856),
      'Contrast' => $this->masterModel->referensi(680),
      'Slicethickness' => $this->Ct_simulatorModel->referensi(681),
      'Base_Plate' => $this->masterModel->referensi(682),
      'Fixation' => $this->masterModel->referensi(683),
      'Neck_Extention' => $this->masterModel->referensi(684),
      'Mouth_Bite' => $this->masterModel->referensi(685),
      'Scar_Marking' => $this->masterModel->referensi(686),
      'Arm_Pos' => $this->masterModel->referensi(687),
      'Breastboard' => $this->masterModel->referensi(688),
      'Knee_Rest' => $this->masterModel->referensi(689),
      'Bolus' => $this->masterModel->referensi(690),
      'Vac_Lock' => $this->masterModel->referensi(691),
      'Matras' => $this->masterModel->referensi(692),
      'referenceOfLaser' => $this->masterModel->referensi(700),
      'linacMachine' => $this->masterModel->referensi(696),
      'imagefusion' => $this->masterModel->referensi(1852),
      'face' => $this->masterModel->referensi(1853),
      'supportingpatientimage' => $this->masterModel->referensi(1854),
      'bladder' => $this->masterModel->referensi(1855),
      'radiografer' => $this->masterModel->radiografer(),
      'fismed' => $this->masterModel->fismed(),
      'initialLat' => $this->masterModel->referensi(701),
      'initialVrt' => $this->masterModel->referensi(702),
      'initialLong' => $this->masterModel->referensi(703),
      'FootRate' => $this->masterModel->referensi(999),
      'referenceOfLaser' => $this->masterModel->referensi(700),
      'linacMachine' => $this->masterModel->referensi(696),
      'SignX' => $this->masterModel->referensi(707),
      'SignY' => $this->masterModel->referensi(708),
      'SignZ' => $this->masterModel->referensi(709),
      'CouchMovementX' => $this->masterModel->referensi(710),
      'CouchMovementY' => $this->masterModel->referensi(711),
      'CouchMovementZ' => $this->masterModel->referensi(712),
      'coordinatesX' => $this->masterModel->referensi(704),
      'coordinatesY' => $this->masterModel->referensi(705),
      'coordinatesZ' => $this->masterModel->referensi(706),
      'Treatment_Field' => $this->masterModel->referensi(713),
      'Direction_Block' => $this->masterModel->referensi(714),
      'Set_Up' => $this->masterModel->referensi(716),
      'getNomr' => $getNomr,
      'jenisMenu' => 'RI',
      'cekDokter' => $this->Ct_simulatorModel->getIsianCtDokter($nokun)->num_rows(),
      'cekRadiografer' => $this->Ct_simulatorModel->getIsianCtRadiografer($nokun)->num_rows(),
      'cekFisikaMedis' => $this->Ct_simulatorModel->getIsianCtFisikaMedis($nokun)->num_rows(),
      'cekSetupNote' => $this->Ct_simulatorModel->getIsianCtSetupNote($nokun)->num_rows(),
      'cekIsoCenter' => $this->Ct_simulatorModel->getIsianIsoCenter($nokun)->num_rows(),
      'isianDokter' => $this->Ct_simulatorModel->getIsianCtDokter($nokun)->row_array(),
      'isianRadiografer' => $this->Ct_simulatorModel->getIsianCtRadiografer($nokun)->row_array(),
      'isianFisikaMedis' => $this->Ct_simulatorModel->getIsianCtFisikaMedis($nokun)->row_array(),
      'isianSetupNote' => $this->Ct_simulatorModel->getIsianCtSetupNote($nokun)->result_array(),
      'isianIsoCenter' => $this->Ct_simulatorModel->getIsianIsoCenter($nokun)->result_array(),
    );
     // print_r($data);exit();
    $this->load->view('Pengkajian/radioTerapi/ct_simulator', $data);
  }

  public function indexSimu(){
    $norm = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $hPengkajianRater = $this->pengkajianAwalModel->historyPengkajianRaterRi();
    $listKunjungan = $this->RadioterapiModel->listKunjunganRadioterapi($norm);
    $data = array(
      'nopen' => $nopen,
      'nomr' => $norm,
      'nokun' => $nokun,
      // 'listDrUmum' => $this->masterModel->listDrUmum(),
      'Direction_Block' => $this->masterModel->referensi(714),
      'Treatment_Field' => $this->masterModel->referensi(713),
      'Position_of_Patient' => $this->masterModel->referensi(678),
      'Mask' => $this->masterModel->referensi(679),
      'Neck_Extention' => $this->masterModel->referensi(684),
      'Mouth_Bite' => $this->masterModel->referensi(685),
      'Knee_Rest' => $this->masterModel->referensi(689),
      'Matras' => $this->masterModel->referensi(692),
      'Arm_Pos' => $this->masterModel->referensi(687),
      'Set_Up' => $this->masterModel->referensi(716),
      'linacMachine' => $this->masterModel->referensi(696),
      'getNomr' => $getNomr,
      'listKunjungan' => $listKunjungan,
      'jenisMenu' => 'RI'
    );
     // print_r($data);exit();
    $this->load->view('Pengkajian/radioTerapi/simulatorInformation', $data);
  }

}
  ?>