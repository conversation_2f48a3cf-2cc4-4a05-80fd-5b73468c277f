<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>_<PERSON>giriman extends CI_Model
{
	public function __construct()
	{
		parent::__construct();
	}

	public function view_data($id)
	{
		$query = "SELECT pr.NOMOR, rutuj.DESKRIPSI TUJUAN, pr.TANGGAL_PERMINTAAN TANGGAL , ruas.DESKRIPSI UNIT_MINTA, pr.STATUS
		FROM invenumum.permintaan pr 
		left join master.ruangan ruas ON pr.ASAL=ruas.ID
		left join master.ruangan rutuj ON rutuj.ID=pr.TUJUAN where pr.TUJUAN=$id AND pr.`STATUS`=1 order by pr.TANGGAL DESC";
		return $this->db->query($query);
	}

	public function detail_kirim($id)
	{
		$query = "SELECT pr.NOMOR,bg.ID_BARANG_GUDANG, pd.ID ID_DETAIL, bm.BARANG NAMA, pd.J<PERSON>, sa.<PERSON>U<PERSON>, pr.<PERSON>U<PERSON><PERSON><PERSON>, pr.ASAL, bg.STOK, bm.HARGA, bg.ID_BARANG_GUDANG
		FROM invenumum.permintaan pr 
		left join invenumum.permintaan_detil pd ON pr.NOMOR=pd.PERMINTAAN
		left join master.ruangan mr ON mr.ID=pr.ASAL
		LEFT join invenumum.barang_gudang bg ON bg.ID_BARANG_GUDANG=pd.BARANG
		left join invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
		left join invenumum.satuan_ sa ON bm.SATUAN=sa.ID_SATUAN
		where pr.NOMOR=$id";
		return $this->db->query($query);
	}

	function get_no_pengiriman()
	{
		$q = $this->db->query("SELECT MAX(RIGHT(NOMOR,4)) AS kd_max FROM invenumum.pengiriman WHERE DATE(TANGGAL)=CURDATE()");
		$kd = "";
		if ($q->num_rows() > 0) {
			foreach ($q->result() as $k) {
				$tmp = ((int) $k->kd_max) + 1;
				$kd = sprintf("%04s", $tmp);
			}
		} else {
			$kd = "0001";
		}
		date_default_timezone_set('Asia/Jakarta');
		return date('Ymd') . $kd;
	}

	function simpan_kirim($data)
	{
		$this->db->insert('invenumum.pengiriman', $data);
	}

	function dataAll()
	{
		$query = "SELECT pr.NOMOR, rutuj.DESKRIPSI TUJUAN, pr.TANGGAL_KIRIM TANGGAL , ruas.DESKRIPSI UNIT_MINTA, pr.STATUS
		FROM invenumum.pengiriman pr 
		left join master.ruangan ruas ON pr.ASAL=ruas.ID
		left join master.ruangan rutuj ON rutuj.ID=pr.TUJUAN
		WHERE pr.`STATUS` !=0
		order by pr.TANGGAL DESC";
		return $this->db->query($query);
	}

	public function detail_modal($id)
	{
		$query = "SELECT png.NOMOR, png.TANGGAL TANGGAL_KIRIM, pd.JUMLAH JML_KIRIM, pmd.JUMLAH JML_MINTA, rm.DESKRIPSI RUANG_MINTA, png.`STATUS`, bm.BARANG, pd.KETERANGAN
		FROM invenumum.pengiriman png
		LEFT JOIN invenumum.pengiriman_detil pd ON png.NOMOR=pd.PENGIRIMAN
		LEFT JOIN invenumum.permintaan_detil pmd ON pmd.ID=pd.PERMINTAAN_BARANG_DETIL
		LEFT JOIN master.ruangan rm ON rm.ID=png.ASAL
		LEFT JOIN invenumum.barang_gudang bg ON bg.ID_BARANG_GUDANG=pmd.BARANG
		LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
		where png.NOMOR=$id AND pd.STATUS=1";
		return $this->db->query($query);
	}

	public function listPegawai()
	{
		$query = "select mp.NIP, mp.NAMA from master.pegawai mp";
		return $this->db->query($query);
	}

	function getDataPegawai($peg)
	{
		$this->db->select("pg.NIP,CONCAT(pg.NAMA,' / ',DATE_FORMAT(pg.TANGGAL_LAHIR,'%d-%m-%Y')) NAMA");
		//$this->db->limit(10);
		$this->db->from('master.pegawai pg');
		$this->db->like('pg.NAMA', $peg);
		return $this->db->get()->result_array();
	}
}
