<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemulihanNapas extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index(){
        
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listNapas = $this->masterModel->referensi(723);
        $listNadi = $this->masterModel->referensi(724);
        $listTekananDarah = $this->masterModel->referensi(725);
        $historyPemulihanNapas = $this->pengkajianAwalModel->historyPemulihanNapas($getNomr['NORM']);

        $data = array(
            'getNomr' => $getNomr,
            'listNapas' => $listNapas,
            'listNadi' => $listNadi,
            'listTekananDarah' => $listTekananDarah,
            'historyPemulihanNapas' => $historyPemulihanNapas,
        );

        $this->load->view('Pengkajian/sedasi/pemulihanNapas/index', $data);
    }

    public function indexRawatInap(){
        
        $nokun = $this->uri->segment(2);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listNapas = $this->masterModel->referensi(723);
        $listNadi = $this->masterModel->referensi(724);
        $listTekananDarah = $this->masterModel->referensi(725);
        $historyPemulihanNapas = $this->pengkajianAwalModel->historyPemulihanNapas($getNomr['NORM']);

        $data = array(
            'getNomr' => $getNomr,
            'listNapas' => $listNapas,
            'listNadi' => $listNadi,
            'listTekananDarah' => $listTekananDarah,
            'historyPemulihanNapas' => $historyPemulihanNapas,
        );

        $this->load->view('Pengkajian/sedasi/pemulihanNapas/index', $data);
    }

    public function simpanFormPemulihanNapas()
    {
        $kunjungan = $this->input->post("nokun");
        $napas = $this->input->post("napas");
        $nadi = $this->input->post("nadi");
        $sistolik = $this->input->post("sistolik");
        $diastolik = $this->input->post("diastolik");
        $waktu = $this->input->post("waktu");

        $data = array(
            'nokun' => $kunjungan,
            'napas' => $napas,
            'nadi' => $nadi,
            'sistolik' => $sistolik,
            'diastolik' => $diastolik,
            'waktu' => $waktu,
        );

        $pemulihannapas = $this->pengkajianAwalModel->insertFormSedasiPemulihanArray($data);
    }

    public function lihatHistoryPemulihanNapas()
    {
        $id = $this->input->post('id');

        $HistoryPemulihanNapas = $this->pengkajianAwalModel->HistoryDetailPemulihanNapas($id);
        $waktu = array();
        $napas = array();
        $nadi = array();
        $sistolik = array();
        $diastolik = array();
        foreach ($HistoryPemulihanNapas as $pemulihannapas):
            array_push($waktu,$pemulihannapas['waktu']);
            array_push($napas,$pemulihannapas['napas']);
            array_push($nadi,$pemulihannapas['nadi']);
            array_push($sistolik,$pemulihannapas['sistolik']);
            array_push($diastolik,$pemulihannapas['diastolik']);

        endforeach;
        echo '<div id="chartMedikasi2"></div>';
        echo "<script>

                $('#chartMedikasi2').highcharts({
                    chart: {
                        type: 'spline'
                    },
                
                    legend: {
                        symbolWidth: 40
                    },
                
                    title: {
                        text: 'Monitoring Kamar Pemulihan'
                    },
                
                    yAxis: {
                        title: {
                            text: 'Nilai'
                        }
                    },
                
                    xAxis: {
                        title: {
                            text: 'Waktu'
                        },
                        accessibility: {
                            description: 'Waktu'
                        },
                        categories: ".json_encode($waktu).",
                    },
                
                    tooltip: {
                        split: true
                    },
                
                    plotOptions: {
                        series: {
                            point: {
                                events: {
                                    click: function () {
                                        window.location.href = this.series.options.website;
                                    }
                                }
                            },
                            cursor: 'pointer'
                        }
                    },
                
                    series: [
                        {
                            name: 'Napas',
                            data: ".json_encode($napas, JSON_NUMERIC_CHECK).",
                            marker: {
                                symbol: 'triangle'
                            }
                        }, {
                            name: 'Nadi',
                            data: ".json_encode($nadi, JSON_NUMERIC_CHECK).",
                            color: Highcharts.getOptions().colors[6],
                            marker: {
                                symbol: 'circle'
                            }

                        }, {
                            name: 'Sistolik',                            
                            data: ".json_encode($sistolik, JSON_NUMERIC_CHECK).",
                            website: 'http://www.apple.com/accessibility/osx/voiceover',
                            color: Highcharts.getOptions().colors[2],
                            marker: {
                                symbol: 'url(".base_url()."/assets/admin/assets/images/sistolik.png)'
                            }

                        }, {
                            name: 'Diastolik',                            
                            data: ".json_encode($diastolik, JSON_NUMERIC_CHECK).",
                            website: 'http://www.apple.com/accessibility/osx/voiceover',
                            color: Highcharts.getOptions().colors[2],
                            marker: {
                                symbol: 'url(".base_url()."/assets/admin/assets/images/diastolik.png)'
                            }
                        }
                    ],
                
                    responsive: {
                        rules: [{
                            condition: {
                                maxWidth: 500
                            },
                            chartOptions: {
                                legend: {
                                    itemWidth: 150
                                }
                            }
                        }]
                    }
                });
            </script>";
    }

}

?>