<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SiteMarkingModel extends CI_Model {

  public function simpanFotoSiteMarking($data)
  {
    $this->db->insert('medis.tb_foto_siteMarking', $data);
  }

  public function listSiteMarking($norm)
  {
    $query = $this->db->query("SELECT sm.`*`, master.getNamaLengkap(sm.nomr)NAMAPASIEN, master.getNamaLengkapPegawai(ap.NIP)OLEH
                              FROM medis.tb_foto_siteMarking sm
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = sm.idPengguna
                              WHERE sm.nomr = '$norm' AND sm.status=1");

    return $query;
  }

  public function updateStatusSm($id,$data)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_foto_siteMarking', $data);
  }

  public function hasilFotoSm($id)
  {
    $query = $this->db->query("SELECT sm.`*`, master.getNamaLengkap(sm.nomr)NAMAPASIEN, master.getNamaLengkapPegawai(ap.NIP)OLEH
                              FROM medis.tb_foto_siteMarking sm
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = sm.idPengguna
                              WHERE sm.id = '$id'");

    return $query->row_array();
  }



}

/* End of file SiteMarkingModel.php */
/* Location: ./application/models/SiteMarkingModel.php */
