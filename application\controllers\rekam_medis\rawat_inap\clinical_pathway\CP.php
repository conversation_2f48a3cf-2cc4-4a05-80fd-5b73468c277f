<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class CP extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','rekam_medis/rawat_inap/clinicalpathway/CPModel'));
  }

 function modalDetailCP()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    // $gpDetailPerawat = $this->ListPerawat_model->getDetail($id)->row_array();
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
    //   'gpDetailPerawat' => $gpDetailPerawat,
    );

    $this->load->view('rekam_medis/rawat_inap/clinical_pathway/cp', $data);
  }

  public function getDataCP()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $listCP = $this->CPModel->listCP();

    $data = array();
    $no = 1;
    foreach ($listCP->result() as $LC) {
      $data[] = array(
        $no,
        $LC->SUB_JUDUL,
        $LC->ICD_JUDUL,
        '<a class="btn btn-success" href="'.$LC->LOKASI_FILE.'" target="_blank" style="color:#ffffff; width:80px; margin-left:0px; font-size:13px; margin-bottom:5px;">Lihat</a>'
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listCP->num_rows(),
      "recordsFiltered" => $listCP->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

}
