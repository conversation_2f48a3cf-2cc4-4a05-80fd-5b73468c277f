<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormulirPemberianIntravena extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'igd/PemberianIntravenaModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listDr = $this->masterModel->listDr();
        $listCairan = $this->masterModel->listCairan();
        $listTransfusi = $this->masterModel->listTransfusi();
        $listObat = $this->masterModel->listObat();
        $riwayatPemberianIntravena = $this->PemberianIntravenaModel->riwayatPemberianIntravena($getNomr['NORM']);
        $historyPemberianIntravena = $this->PemberianIntravenaModel->historyPemberianIntravena($getNomr['NORM']);


        $data = array(
            'getNomr' => $getNomr,
            'listDr' => $listDr,
            'listCairan' => $listCairan,
            'listObat' => $listObat,
            'riwayatPemberianIntravena' => $riwayatPemberianIntravena,
            'historyPemberianIntravena' => $historyPemberianIntravena
        );

        $this->load->view('Pengkajian/igd/pemberianIntravena/index', $data);
    }

    public function simpanFormPemberianIntravena()
    {
        $post = $this->input->post();

        $dataIntravena = array();
        $index = 0;
        $urutan = 1;
        $this->db->trans_begin();
        if (isset($post['kecepatan_dosis'])) {
            foreach ($post['kecepatan_dosis'] as $input) {
                if ($post['kecepatan_dosis'][$index] != "") {
                    $dataIntravena = array(
                        'nokun' => $post['nokun'],
                        'dokter' => $post['dokter'],
                        'nama_cairan' => $post['cairan'][$index],
                        'nama_transfusi' => $post['transfusi'][$index],
                        'kecepatan_dosis' => $post['kecepatan_dosis'][$index],
                        'kecepatan_jam' => $post['kecepatan_jam'][$index],
                        'group_intravena' => $post['group_intravena'][$index],
                        'oleh' => isset($post['pengguna'])
                    );


                    $this->db->insert('keperawatan.tb_pemberian_cairan_intravena', $dataIntravena);
                    // );


                    $getIdIntravena = $this->db->insert_id();

                    if (!empty($post['nama_obat_' . $urutan])) {

                        $dataIntravenaArray = array();
                        $index_arr = 0;

                        foreach ($post['nama_obat_' . $urutan] as $input) {
                            if ($post['nama_obat_' . $urutan][$index_arr] != "") {
                                $dataIntravenaArray = array(
                                    'id_intravena' => $getIdIntravena,
                                    'nokun' => $post['nokun'],
                                    'nama_obat_tambahan' => $post['nama_obat_' . $urutan][$index_arr],
                                    'dosis_obat_tambahan' => $post['dosis_obat_' . $urutan][$index_arr],
                                    'group_intravena' => $post['group_obat_tambahan'][$index_arr],
                                    'oleh' => $post['pengguna']
                                );
                                $this->db->insert('keperawatan.tb_pemberian_cairan_intravena_array', $dataIntravenaArray);
                            }
                            $index_arr++;
                        }
                    }
                }

                $urutan++;
                $index++;
            }
        }

        $result = array('status' => 'failed');

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryPemberianIntravena()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post("nokun");
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $HistoryPemberianIntravena = $this->PemberianIntravenaModel->historyDetailPemberianIntravena($id);
        $HistoryPemberianIntravenaArr = $this->PemberianIntravenaModel->historyDetailPemberianIntravenaArr($HistoryPemberianIntravena['id']);
        $listDr = $this->masterModel->listDr();
        $listCairanInfus = $this->masterModel->referensi(1163);
        $listCairan = $this->masterModel->listCairan();
        $listTransfusi = $this->masterModel->listTransfusi();
        $listObat = $this->masterModel->listObat();

        $data = array(
            'id' => $id,
            'getNomr' => $getNomr,
            'listCairanInfus' => $listCairanInfus,
            'intravena' => $HistoryPemberianIntravena,
            'intravena_arr' => $HistoryPemberianIntravenaArr,
            'listDr' => $listDr,
            'listCairan' => $listCairan,
            'listTransfusi' => $listTransfusi,
            'listObat' => $listObat
        );

        $this->load->view('Pengkajian/igd/pemberianIntravena/modalViewEditPemberianIntravena', $data);
    }

    public function ubahFormPemberianIntravena()
    {
        $post = $this->input->post();

        $id_intravena = $this->input->post("id_intravena");
        $nokun = $this->input->post("nokun_edit");
        $dokter = $this->input->post("dokterPemberianIntravena_edit");
        $nama_cairan = $this->input->post("cairanPemberianIntravena_edit");
        $nama_transfusi = $this->input->post("transfusiPemberianIntravena_edit");
        $kecepatan_dosis = $this->input->post("kecepatan_dosis_edit");
        $kecepatan_jam = $this->input->post("kecepatan_jam_edit");

        $dataUbah = array(
            'id' => $id_intravena,
            'nokun' => $nokun,
            'dokter' => $dokter,
            'nama_cairan' => $nama_cairan,
            'nama_transfusi' => $nama_transfusi,
            'kecepatan_dosis' => $kecepatan_dosis,
            'kecepatan_jam' => $kecepatan_jam
        );

        $this->db->where('keperawatan.tb_pemberian_cairan_intravena.id', $id_intravena);
        $this->db->update('keperawatan.tb_pemberian_cairan_intravena', $dataUbah);
        $this->db->delete('keperawatan.tb_pemberian_cairan_intravena_array', array('id_intravena' => $id_intravena));

        $dataIntravenaArrayUbah = array();
        $indexUbah = 0;
        if (isset($post['namaObatPemberianIntravena_edit'])) {
            foreach ($post['namaObatPemberianIntravena_edit'] as $input) {
                if ($post['namaObatPemberianIntravena_edit'][$indexUbah] != "") {
                    array_push(
                        $dataIntravenaArrayUbah,
                        array(
                            'id_intravena' => $id_intravena,
                            'nokun' => $post['nokun_edit'],
                            'nama_obat_tambahan' => $post['namaObatPemberianIntravena_edit'][$indexUbah],
                            'dosis_obat_tambahan' => $post['dosisPemberianIntravena_edit'][$indexUbah],
                            'oleh' => $post['pengguna_edit']
                        )
                    );
                }
                $indexUbah++;
            }

            $this->db->insert_batch('keperawatan.tb_pemberian_cairan_intravena_array', $dataIntravenaArrayUbah);
        }

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryPemberianIntravenaIsi()
    {
        $id = $this->input->post('id');
        $listPerawat = $this->masterModel->listPerawat();
        $historyPemberianIntravenaIsi = $this->PemberianIntravenaModel->historyDetailPemberianIntravenaIsi($id);
        $listCairanInfus = $this->masterModel->referensi(1163);
        $data = array(
            'id' => $id,
            'listPerawat' => $listPerawat,
            'listCairanInfus' => $listCairanInfus,
            'intravenaisi' => $historyPemberianIntravenaIsi
        );
        $this->load->view('Pengkajian/igd/pemberianIntravena/modalViewEditPemberian', $data);
    }


    public function modalPemberian()
    {
        $id = $this->input->post('id');
        $listPerawat = $this->masterModel->listPerawat();
        $listCairanInfus = $this->masterModel->referensi(1163);
        $historyPemberianIntravenaIsi = $this->PemberianIntravenaModel->historyPemberianIntravenaIsi($id);
        $historyBatalPemberianIntravena = $this->PemberianIntravenaModel->historyBatalPemberianIntravena($id);
        $getLabelBlue = $this->PemberianIntravenaModel->getLabelBlue($id);
        $getLabelYellow = $this->PemberianIntravenaModel->getLabelYellow($id);
        $data = array(
            'id' => $id,
            'listPerawat' => $listPerawat,
            'listCairanInfus' => $listCairanInfus,
            'historyPemberianIntravenaIsi' => $historyPemberianIntravenaIsi,
            'historyBatal' => $historyBatalPemberianIntravena,
            'getLabelBlue' => $getLabelBlue,
            'getLabelYellow' => $getLabelYellow
        );
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/igd/pemberianIntravena/modalPemberian', $data);
    }

    public function simpanFormPemberianIsi()
    {
        $id_intravena = $this->input->post("id_intravena");
        $pengguna = $this->input->post("pengguna");
        $perawat1 = $this->input->post("perawat1");
        $perawat2 = $this->input->post("perawat2");
        $tanggal = $this->input->post("tanggal");
        $dimulai = $this->input->post("dimulai");
        $keterangan = $this->input->post("keterangan");
        $jumlah_cairan = $this->input->post("jumlah_cairan");
        $golongan_darah = $this->input->post("golongan_darah");
        $no_stok = $this->input->post("no_stok");
        $tanggal_kadaluarsa = $this->input->post("tanggal_kadaluarsa");
        $volume = $this->input->post("volume");

        $data = array(
            'id_intravena' => $id_intravena,
            'perawat1' => $perawat1,
            'perawat2' => $perawat2,
            'tanggal' => $tanggal,
            'dimulai' => $dimulai,
            'keterangan' => $keterangan,
            'jumlah_cairan' => $jumlah_cairan,
            'golongan_darah' => isset($golongan_darah) ? $golongan_darah : "",
            'no_stok' => isset($no_stok) ? $no_stok : "",
            'tanggal_kadaluarsa' => isset($tanggal_kadaluarsa) ? $tanggal_kadaluarsa : "",
            'volume' => isset($volume) ? $volume : "",
            'oleh' => $pengguna,
        );

        $this->db->trans_begin();
        $this->db->insert('keperawatan.tb_pemberian_cairan_intravena_isi', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function ubahFormPemberianIntravenaIsi()
    {
        $id_intravena_isi = $this->input->post("id_intravena_isi");
        $perawat1_edit = $this->input->post("perawat1_edit");
        $perawat2_edit = $this->input->post("perawat2_edit");
        $tanggal_edit = $this->input->post("tanggal_edit");
        $dimulai_edit = $this->input->post("dimulai_edit");
        $keterangan_edit = $this->input->post("keterangan_edit");
        $jumlah_cairan_edit = $this->input->post("jumlah_cairan_edit");
        $golongan_darah_edit = $this->input->post("golongan_darah_edit");
        $no_stok_edit = $this->input->post("no_stok_edit");
        $tanggal_kadaluarsa_edit = $this->input->post("tanggal_kadaluarsa_edit");
        $volume_edit = $this->input->post("volume_edit");

        $dataUbah = array(
            'perawat1' => $perawat1_edit,
            'perawat2' => $perawat2_edit,
            'tanggal' => $tanggal_edit,
            'dimulai' => $dimulai_edit,
            'keterangan' => $keterangan_edit,
            'jumlah_cairan' => $jumlah_cairan_edit,
            'golongan_darah' => isset($golongan_darah_edit) ? $golongan_darah_edit : "",
            'no_stok' => isset($no_stok_edit) ? $no_stok_edit : "",
            'tanggal_kadaluarsa' => isset($tanggal_kadaluarsa_edit) ? $tanggal_kadaluarsa_edit : "",
            'volume' => isset($volume_edit) ? $volume_edit : "",
        );

        $this->db->trans_begin();

        $this->db->where('keperawatan.tb_pemberian_cairan_intravena_isi.id', $id_intravena_isi);
        $this->db->update('keperawatan.tb_pemberian_cairan_intravena_isi', $dataUbah);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }


    public function modalStopPemberian()
    {
        $id = $this->input->post('id');
        $data = array(
            'id' => $id
        );
        $this->load->view('Pengkajian/igd/pemberianIntravena/modalStopPemberian', $data);
    }

    public function simpanStopPemberian()
    {
        $id_intravena = $this->input->post("id_intravena");
        $keterangan = $this->input->post("keterangan");

        $data = array(
            'keterangan_stop_pemberian' => $keterangan,
            'status_stop' => '0'
        );

        $this->db->trans_begin();

        $this->db->where('keperawatan.tb_pemberian_cairan_intravena.id', $id_intravena);
        $this->db->update('keperawatan.tb_pemberian_cairan_intravena', $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function listDr()
    {
        $result = $this->masterModel->listDr();
        $json = array();
        foreach ($result as $row) {
            $json[] = array('id' => $row['ID_DOKTER'], 'text' => $row['DOKTER']);
        }
        echo json_encode($json);
    }

    public function listCairan()
    {
        $result = $this->masterModel->listCairan();
        $json = array();
        foreach ($result as $row) {
            $json[] = array('id' => $row['ID'], 'text' => $row['NAMA']);
        }
        echo json_encode($json);
    }

    public function listTransfusi()
    {
        $result = $this->masterModel->listTransfusi();
        $json = array();
        foreach ($result as $row) {
            $json[] = array('id' => $row['ID'], 'text' => $row['NAMA']);
        }
        echo json_encode($json);
    }

    public function listObat()
    {
        $result = $this->masterModel->listObat();
        $json = array();
        foreach ($result as $row) {
            $json[] = array('id' => $row['ID'], 'text' => $row['NAMA']);
        }
        echo json_encode($json);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'ambilParenteralTerakhir') {
                $result = $this->PemberianIntravenaModel->ambilIntravenaTerakhir('parenteral');
                // echo '<pre>';print_r($result);exit();

                echo json_encode(
                    array(
                        'status' => 'success',
                        'data'   => $result
                    )
                );
            } elseif ($param == 'ambilTransfusiTerakhir') {
                $result = $this->PemberianIntravenaModel->ambilIntravenaTerakhir('transfusi');
                // echo '<pre>';print_r($result);exit();

                echo json_encode(
                    array(
                        'status' => 'success',
                        'data'   => $result
                    )
                );
            }
        }
    }
}
