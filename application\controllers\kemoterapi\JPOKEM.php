<?php
defined('BASEPATH') or exit('No direct script access allowed');

class JPOKEM extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(array(
            'masterModel',
            'pengkajianAwalModel',
            'kemoterapi/JpokemModel',
        ));
    }

    public function index()
    {
        $nokun = $this->uri->segment(2);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $data = array(
            'nokun' => $nokun,
            'nomr' => $getNomr
        );
        $this->load->view('Pengkajian/kemoterapi/JPOKEM/index', $data);
    }

    // Endpoint untuk select2 protokol kemoterapi
    public function get_protokol_kemo()
    {
        $search = $this->input->get('q');
        $result = $this->JpokemModel->getProtokolKemoList($search);
        $data = array();
        foreach ($result as $row) {
            $data[] = array(
                'id' => $row['ID'],
                'text' => $row['NAMA']
            );
        }
        echo json_encode(['results' => $data]);
    }

    // Endpoint untuk load tabel detail protokol kemoterapi
    public function get_protokol_kemo_detail()
    {
        $id = $this->input->post('id');
        $detail = $this->JpokemModel->getProtokolKemoDetail($id);
        // Render partial view tabel detail
        $html = $this->load->view('Pengkajian/kemoterapi/JPOKEM/tabelDetail', ['detail' => $detail], true);
        echo $html;
    }

    // Simpan data JPOK Kemoterapi
    public function simpan_jpok()
    {
        // Set content type untuk JSON response
        header('Content-Type: application/json');

        try {
            // Debug: Output raw POST data
            if (isset($_GET['debug'])) {
                echo json_encode([
                    'debug' => $_POST,
                    'analysis' => [
                        'nokun' => $_POST['nokun'] ?? 'tidak ada',
                        'jumlah_siklus' => $_POST['jumlah_siklus'] ?? 'tidak ada',
                        'lini' => $_POST['lini'] ?? 'tidak ada',
                        'protokol_kemo' => $_POST['protokol_kemo'] ?? 'tidak ada',
                        'dosis' => $_POST['dosis'] ?? 'tidak ada',
                        'dosis_pengenceran' => $_POST['dosis_pengenceran'] ?? 'tidak ada',
                        'id_prokem' => $_POST['id_prokem'] ?? 'tidak ada'
                    ]
                ], JSON_PRETTY_PRINT);
                return;
            }

            // Ambil data langsung dari $_POST untuk menghindari masalah CodeIgniter
            // Pastikan data scalar bukan array
            $nokun = is_array($_POST['nokun'] ?? '') ? '' : ($_POST['nokun'] ?? '');
            $jumlah_siklus = is_array($_POST['jumlah_siklus'] ?? '') ? '' : ($_POST['jumlah_siklus'] ?? '');
            $lini = is_array($_POST['lini'] ?? '') ? '' : ($_POST['lini'] ?? '');
            $menopause = is_array($_POST['menopause'] ?? null) ? null : ($_POST['menopause'] ?? null);
            $protokol_kemo = is_array($_POST['protokol_kemo'] ?? '') ? '' : ($_POST['protokol_kemo'] ?? '');
            $dosis = $_POST['dosis'] ?? [];
            $dosis_pengenceran = $_POST['dosis_pengenceran'] ?? [];
            $id_prokem = $_POST['id_prokem'] ?? [];

            // Ambil data keterangan juga
            $keterangan = $_POST['keterangan'] ?? [];

            // Validasi data dasar - gunakan $_POST langsung untuk menghindari masalah CI
            // Pastikan data tidak berupa array yang tidak diharapkan
            if (is_array($nokun) || is_array($jumlah_siklus) || is_array($lini) || is_array($protokol_kemo)) {
                echo json_encode(['status' => 'error', 'message' => 'Format data tidak valid - ada data array yang tidak diharapkan']);
                return;
            }

            // Validasi input
            if (empty($nokun)) {
                echo json_encode(['status' => 'error', 'message' => 'NOKUN tidak valid']);
                return;
            }

            $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
            if (empty($getNomr)) {
                echo json_encode(['status' => 'error', 'message' => 'NOMR tidak ditemukan untuk NOKUN: ' . $nokun]);
                return;
            }

            if (empty($protokol_kemo)) {
                echo json_encode(['status' => 'error', 'message' => 'Protokol kemoterapi harus dipilih']);
                return;
            }

            if (empty($dosis) || !is_array($dosis)) {
                echo json_encode(['status' => 'error', 'message' => 'Data dosis tidak valid']);
                return;
            }

            // Cek apakah ada dosis yang diisi (tidak kosong dan tidak 0)
            $ada_dosis_valid = false;
            foreach ($dosis as $nilai) {
                if (!empty($nilai) && $nilai != '0') {
                    $ada_dosis_valid = true;
                    break;
                }
            }

            if (!$ada_dosis_valid) {
                echo json_encode(['status' => 'error', 'message' => 'Minimal satu dosis harus diisi']);
                return;
            }

            // Ambil user ID dari session dengan aman
            $user_id = 1; // Default fallback
            if (isset($_SESSION['userdata']['id'])) {
                $user_id = (int)$_SESSION['userdata']['id'];
            } elseif (isset($this->session->userdata['id'])) {
                $user_id = (int)$this->session->userdata['id'];
            }

            // Data untuk tabel utama - pastikan semua data bukan array
            $data_jpok = array(
                'NOKUN' => is_array($nokun) ? '' : $nokun,
                'NOMR' => is_array($getNomr) ? '' : $getNomr,
                'JUMLAH_SIKLUS' => !empty($jumlah_siklus) ? (int)$jumlah_siklus : null,
                'LINI' => is_array($lini) ? '' : $lini,
                'MENOPAUSE' => is_array($menopause) ? null : $menopause,
                'ID_PROTOKOL_KEMO' => (int)$protokol_kemo,
                'OLEH' => $user_id,
                'STATUS' => 1
            );

            // Debug: Check untuk data array sebelum insert
            foreach ($data_jpok as $field => $value) {
                if (is_array($value)) {
                    echo json_encode(['status' => 'error', 'message' => "Field '$field' masih berupa array: " . print_r($value, true)]);
                    return;
                }
            }

            // Simpan ke database
            $result = $this->JpokemModel->simpanJpok($data_jpok, $dosis, $dosis_pengenceran, $id_prokem, $keterangan);

            if ($result['status']) {
                echo json_encode(['status' => 'success', 'message' => 'Data JPOK berhasil disimpan', 'id_jpok' => $result['id_jpok']]);
            } else {
                echo json_encode(['status' => 'error', 'message' => $result['message']]);
            }
        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    // Endpoint untuk select2 JPOK yang sudah disimpan
    public function get_jpok_list()
    {
        header('Content-Type: application/json');

        try {
            $nokun = $_GET['nokun'] ?? '';
            $search = $_GET['q'] ?? '';

            // Load model
            $this->load->model('kemoterapi/JpokemModel');

            $result = $this->JpokemModel->getJpokList($nokun, $search);
            $data = array();

            foreach ($result as $row) {
                $data[] = array(
                    'id' => $row['ID'],
                    'text' => 'JPOK - ' . $row['NAMA'] . ' - ' . $row['LINI'] . ' (' . $row['JUMLAH_SIKLUS'] . ' siklus) - ' . date('d/m/Y H:i:s', strtotime($row['TANGGAL_INPUT']))
                );
            }

            echo json_encode(['results' => $data]);

        } catch (Exception $e) {
            echo json_encode(['results' => [], 'error' => $e->getMessage()]);
        }
    }

    // Endpoint untuk load detail JPOK dan tabel pemberian
    public function get_jpok_detail()
    {
        $id_jpok = $_POST['id_jpok'] ?? 0;
        $nokun = $_POST['nokun'] ?? ($_GET['nokun'] ?? '');

        // Load model
        $this->load->model('kemoterapi/JpokemModel');

        $detail = $this->JpokemModel->getJpokDetailWithJadwal($id_jpok);

        $data = array(
            'detail' => $detail,
            'id_jpok' => $id_jpok,
            'nokun' => $nokun  // Tambahkan nokun ke data
        );

        $html = $this->load->view('Pengkajian/kemoterapi/JPOKEM/detailPemberian', $data, true);
        echo $html;
    }

    // Simpan jadwal minum obat
    public function simpan_jadwal()
    {
        try {
            $bulan = $this->input->post('bulan');
            $tahun = $this->input->post('tahun');
            $minggu = $this->input->post('minggu');
            $seri = $this->input->post('seri');
            $tb = $this->input->post('tb');
            $bb = $this->input->post('bb');
            $lbb_m2 = $this->input->post('lbb_m2');
            $id_jpok = $this->input->post('id_jpok');

            // Validasi input
            if (empty($bulan) || empty($tahun) || empty($minggu) || empty($seri)) {
                echo json_encode(['status' => 'error', 'message' => 'Bulan, Tahun, Minggu, dan Seri harus diisi']);
                return;
            }

            // Ambil semua checkbox obat
            $post_data = $this->input->post();
            $obat_jadwal = array();

            foreach ($post_data as $key => $value) {
                if (strpos($key, 'obat_') === 0 && strpos($key, '_hari_') !== false) {
                    // Parse key: obat_{id_detail}_hari_{hari}
                    preg_match('/obat_(\d+)_hari_(\d+)/', $key, $matches);
                    if (count($matches) == 3) {
                        $id_detail = $matches[1];
                        $hari = $matches[2];

                        if (!isset($obat_jadwal[$id_detail])) {
                            $obat_jadwal[$id_detail] = array();
                        }
                        $obat_jadwal[$id_detail][$hari] = 1;
                    }
                }
            }

            // Simpan ke database
            $result = $this->JpokemModel->simpanJadwal($id_jpok, $bulan, $tahun, $minggu, $seri, $tb, $bb, $lbb_m2, $obat_jadwal);

            if ($result['status']) {
                echo json_encode(['status' => 'success', 'message' => 'Jadwal berhasil disimpan']);
            } else {
                echo json_encode(['status' => 'error', 'message' => $result['message']]);
            }
        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    // Ambil data jadwal untuk update tabel pemberian
    public function get_jadwal_data()
    {
        try {
            $id_jpok = $this->input->post('id_jpok');
            $jadwal_data = $this->JpokemModel->getJadwalByJpok($id_jpok);
            echo json_encode(['status' => 'success', 'data' => $jadwal_data]);
        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    // Method untuk cek jenis kelamin pasien
    public function getJenisKelaminPasien()
    {
        $nokun = $this->input->post('nokun');
        $result = $this->JpokemModel->getJenisKelaminPasien($nokun);
        echo json_encode($result);
    }

    // Method untuk mengambil data TB, BB, LBB terbaru dari pasien
    public function get_patient_vitals()
    {
        header('Content-Type: application/json');

        try {
            $nokun = $_GET['nokun'] ?? $_POST['nokun'] ?? '';

            if (empty($nokun)) {
                echo json_encode(['status' => 'error', 'message' => 'NOKUN tidak valid']);
                return;
            }

            // Query untuk mengambil data TB, BB, LBB terbaru
            $sql = "SELECT tb, bb, lpb_2 as lbb_m2, created_at
                    FROM db_pasien.tb_tb_bb
                    WHERE nokun = ?
                    ORDER BY created_at DESC
                    LIMIT 1";

            $query = $this->db->query($sql, array($nokun));
            $result = $query->row_array();

            if ($result) {
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'tb' => $result['tb'],
                        'bb' => $result['bb'],
                        'lbb_m2' => $result['lbb_m2'],
                        'created_at' => $result['created_at']
                    ]
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Data TB/BB/LBB tidak ditemukan untuk NOKUN: ' . $nokun
                ]);
            }

        } catch (Exception $e) {
            echo json_encode(['status' => 'error', 'message' => 'Error: ' . $e->getMessage()]);
        }
    }

    // Method untuk menampilkan history JPOK
    public function get_history()
    {
        try {
            $nokun = $_POST['nokun'] ?? '';

            if (empty($nokun)) {
                echo '<div class="alert alert-warning">NOKUN tidak valid</div>';
                return;
            }

            // Load model
            $this->load->model('kemoterapi/JpokemModel');

            // Ambil data history JPOK
            $history = $this->JpokemModel->getHistoryJpok($nokun);

            // Load view history
            $data = [
                'history' => $history,
                'nokun' => $nokun
            ];

            $this->load->view('Pengkajian/kemoterapi/JPOKEM/historyJpok', $data);

        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error: ' . $e->getMessage() . '</div>';
        }
    }
}
