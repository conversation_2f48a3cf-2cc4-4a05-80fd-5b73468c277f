<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class HumptyDumptyScale extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'formulirTriaseModel', 'igd/HumptyDumptyScaleModel'));
  }

  public function index()
  {
    $data = array(
      'hdsUmur' => $this->masterModel->referensi(1014),
      'hdsJk'   => $this->masterModel->referensi(1015),
      'hdsDa'   => $this->masterModel->referensi(1016),
      'hdsGk'   => $this->masterModel->referensi(1017),
      'hdsFl'   => $this->masterModel->referensi(1018),
      'hdsOb'   => $this->masterModel->referensi(1019),
      'hdsPo'   => $this->masterModel->referensi(1020),
    );
    $this->load->view('Pengkajian/igd/humptyDumptyScale/humptyDumptyScale',$data);
  }

  public function simpanHumptyDumptyScale()
  {

    $data = array(
      'nokun'                   => $this->input->post("nokun"),
      'umur'                    => $this->input->post("hdsUmur"),
      'jenis_kelamin'           => $this->input->post("hdsJk"),
      'diagnosa'                => $this->input->post("hdsDa"),
      'gangguan_kognitif'       => $this->input->post("hdsGk"),
      'faktor_lingkungan'       => $this->input->post("hdsFl"),
      'respon_terhadap_operasi' => $this->input->post("hdsOb"),
      'penggunaan_obat'         => $this->input->post("hdsPo"),
      'oleh'                    => $this->session->userdata('id'),
    );

    $this->HumptyDumptyScaleModel->simpanHumptyDumptyScale($data);

  }

  public function tblHistoryHumptyDumpty()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listHumptyDumpty = $this->HumptyDumptyScaleModel->tbl_HumptyDumptyScale($nomr);

    $data = array();
    $no = 1;
    foreach ($listHumptyDumpty->result() as $historyHd) {
      $btnPencegahan = $historyHd->IDPENCEGAHAN == '' ? $historyHd->TOTALNILAI >= 12 ? '<a href="#modalPencegahanHd" class="btn btn-sm btn-block btn-warning" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$historyHd->id.'"><i class="fas fa-pencil-alt"></i> Form Pencegahan</a>' : '<a href="#modalPencegahanHd" class="btn btn-sm btn-block btn-warning disabled" ><i class="fas fa-pencil-alt"></i> Form Pencegahan</a>' : '<a href="#modalPencegahanHd" class="btn btn-sm btn-block btn-warning disabled" ><i class="fas fa-pencil-alt"></i> Form Pencegahan</a>';
      $data[] = array(
        $no,
        $historyHd->nokun,
        $historyHd->OLEH,
        $historyHd->TOTALNILAI>= 12 ? "<b style='color:#fc5c65'>".$historyHd->TOTALNILAI."</b>": "<b>".$historyHd->TOTALNILAI."</b>",
        $historyHd->TOTALNILAI >= 12 ? "<p style='color:#fc5c65'>Resiko Tinggi</p>": "<p style='color:#26de81'>Resiko Rendah</p>",
        date("d-m-Y",strtotime($historyHd->tanggal)),
        '<a href="#modalHistoryHd" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$historyHd->id.'"><i class="fas fa-edit"></i> Edit</a><br>'.$btnPencegahan.'
        '
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listHumptyDumpty->num_rows(),
      "recordsFiltered" => $listHumptyDumpty->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalPelaksanaanPencegahanRj()
  {
    $data = array(
      'id'              => $this->input->POST("id"),
      'hdsRisikoRendah' => $this->masterModel->referensi(1021),
      'hdsRisikoTinggi' => $this->masterModel->referensi(1022),
    );
    $this->load->view('Pengkajian/igd/humptyDumptyScale/pelaksanaanPencegahanRj',$data);
  }

  public function simpanfrmintervensiRendah()
  {

    $data = array(
      'id_pengkajian'    => $this->input->post("id"),
      'jenis_pengkajian' => 3,
      'resiko_rendah'    => $this->input->post("hdsRisikoRendah"),
      'oleh'             => $this->session->userdata('id'),
    );

    $this->HumptyDumptyScaleModel->simpanfrmintervensiRendah($data);

  }

  public function simpanfrmintervensiTinggi()
  {

    $data = array(
      'id_pengkajian'    => $this->input->post("id"),
      'jenis_pengkajian' => 3,
      'resiko_tinggi'    => $this->input->post("hdsRisikoTinggi"),
      'oleh'             => $this->session->userdata('id'),
    );

    $this->HumptyDumptyScaleModel->simpanfrmintervensiTinggi($data);

  }

  public function modalHistoryHd()
  {
    $id   = $this->input->POST("id");
    $ghds = $this->HumptyDumptyScaleModel->get_HumptyDumptyScale($id);
    $gppj = $this->HumptyDumptyScaleModel->get_pencegahanPasienJatuh($id);
    // echo "<pre>";print_r($gppj);exit();

    $data = array(
      'id'              => $id,
      'ghds'            => $ghds,
      'gppj'            => $gppj,
      'hdsUmur'         => $this->masterModel->referensi(1014),
      'hdsJk'           => $this->masterModel->referensi(1015),
      'hdsDa'           => $this->masterModel->referensi(1016),
      'hdsGk'           => $this->masterModel->referensi(1017),
      'hdsFl'           => $this->masterModel->referensi(1018),
      'hdsOb'           => $this->masterModel->referensi(1019),
      'hdsPo'           => $this->masterModel->referensi(1020),
      'hdsRisikoRendah' => $this->masterModel->referensi(1021),
      'hdsRisikoTinggi' => $this->masterModel->referensi(1022),
    );
    $this->load->view('Pengkajian/igd/humptyDumptyScale/edit',$data);
  }

  public function updateHumptyDumptyScale()
  {

    $id   = $this->input->POST("id");

    $data = array(
      'umur'                    => $this->input->post("hdsUmurEdit"),
      'jenis_kelamin'           => $this->input->post("hdsJkEdit"),
      'diagnosa'                => $this->input->post("hdsDaEdit"),
      'gangguan_kognitif'       => $this->input->post("hdsGkEdit"),
      'faktor_lingkungan'       => $this->input->post("hdsFlEdit"),
      'respon_terhadap_operasi' => $this->input->post("hdsObEdit"),
      'penggunaan_obat'         => $this->input->post("hdsPoEdit"),
    );

    $this->HumptyDumptyScaleModel->updateHumptyDumptyScale($data,$id);

  }

  public function updatefrmintervensiRendah()
  {
    $id   = $this->input->POST("idRendah");

    $data = array(
      'resiko_rendah' => $this->input->post("hdsRisikoRendahEdit"),
    );

    $this->HumptyDumptyScaleModel->updatefrmintervensiRendah($data,$id);

  }

  public function updatefrmintervensiTinggi()
  {
    $id   = $this->input->POST("idTinggi");

    $data = array(
      'resiko_tinggi' => $this->input->post("hdsRisikoTinggiEdit"),
    );

    $this->HumptyDumptyScaleModel->updatefrmintervensiTinggi($data,$id);

  }



}

/* End of file HumptyDumptyScale.php */
/* Location: ./application/controllers/igd/HumptyDumptyScale.php */
