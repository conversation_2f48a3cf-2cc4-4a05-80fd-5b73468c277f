<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TindakanBillingModel extends CI_Model
{

  public function __construct()
  {
    parent::__construct();
  }

  public function obat($nopen)
  {
    $query = $this->db->query("SELECT * FROM pendaftaran.penjamin p WHERE p.NOPEN = $nopen");
  }

  public function listTindakan($ruangan, $smf = null, $search = null)
  {
    $this->db->select('tr.ID, td.ID AS ID_TINDAKAN, tr.RUANGAN, td.NAMA, tt.TARIF, tt.DOKTER_OPERATOR TARIF_DOKTER');
    $this->db->from('master.tindakan_ruangan tr');
    $this->db->join('master.tindakan td', 'td.ID = tr.TINDAKAN');

    // Cek jika ada SMF dan tidak ada search, maka cek apakah SMF ada di mapping
    if (!empty($smf) && empty($search)) {
      // Cek apakah SMF ada di tindakan_mapping
      $check_mapping = $this->db->query("SELECT COUNT(*) as count FROM master.tindakan_mapping tm 
                                         JOIN master.pegawai peg ON peg.SMF = tm.SMF 
                                         WHERE peg.SMF = ?", [$smf])->row();
      
      if ($check_mapping->count > 0) {
        // Jika SMF ada di mapping, tambahkan JOIN untuk filter berdasarkan SMF
        $this->db->join('master.tindakan_mapping tm', 'tm.ID_TINDAKAN = td.ID');
        $this->db->join('master.pegawai peg', 'peg.SMF = tm.SMF');
        $this->db->where('peg.SMF', $smf);
      }
    }

    if (in_array($ruangan, ['105020201', '105020202', '105020204'])) {
      $this->db->join('master.tarif_tindakan tt', 'td.ID = tt.TINDAKAN AND tt.KELAS=56 AND tt.STATUS = 1');
    } else {
      $this->db->join('master.tarif_tindakan tt', 'td.ID = tt.TINDAKAN AND tt.STATUS = 1');
    }

    $this->db->where('tr.RUANGAN', $ruangan);
    // $this->db->where('td.STATUS', 1);
    $this->db->where('tr.STATUS', 1);
    
    // Jika ada search, filter berdasarkan nama tindakan
    if (!empty($search)) {
      $this->db->like('td.NAMA', $search);
    }
    
    $this->db->group_by('td.ID');
    $this->db->order_by('(CASE WHEN td.NAMA LIKE "Konsultasi%" THEN 0 ELSE 1 END)', 'ASC');
    $this->db->order_by('td.NAMA', 'ASC');

    $query = $this->db->get();
    return $query->result();
  }

  public function cekBilling($nokun, $olehDok)
  {
    $this->db->select('ptm.*, tm.TINDAKAN, tm.TANGGAL');
    $this->db->from('layanan.tindakan_medis tm');
    $this->db->join('layanan.petugas_tindakan_medis ptm', 'ptm.TINDAKAN_MEDIS = tm.ID', 'left');
    $this->db->where('tm.KUNJUNGAN', $nokun);
    $this->db->where('ptm.MEDIS', $olehDok);
    $this->db->where('tm.STATUS', 1);

    $query = $this->db->get();
    return $query->result();
  }

  public function simpanOrder($nokun, $tindakan, $oleh, $olehDok)
  {
    $id_tindakan_medis = $this->db->query("SELECT generator.generateIdTindakanMedis(NOW()) as ID")->row()->ID;

    if (!$id_tindakan_medis) {
      return ['success' => false, 'message' => 'Gagal mendapatkan ID tindakan medis.'];
    }

    $tanggal = date("Y-m-d H:i:s");

    $query = "
        INSERT IGNORE INTO layanan.tindakan_medis (ID, KUNJUNGAN, TINDAKAN, TANGGAL, OLEH)
        VALUES (?, ?, ?, ?, ?)";

    $this->db->query($query, [$id_tindakan_medis, $nokun, $tindakan, $tanggal, $oleh]);

    if ($this->db->affected_rows() > 0) {
      $data_petugas = [
        'TINDAKAN_MEDIS' => $id_tindakan_medis,
        'JENIS' => 1,
        'MEDIS' => $olehDok,
      ];

      $this->db->insert('layanan.petugas_tindakan_medis', $data_petugas);

      return ['success' => true, 'ID' => $id_tindakan_medis];
    } else {
      return ['success' => false, 'message' => 'Tidak ada data yang disimpan di tindakan medis.'];
    }
  }
  // Query builder untuk Daftar Tindakan
  public function getTindakanDatatable($nokun, $length, $start)
  {
    $this->db->select('mtd.NAMA AS nama_tindakan, mtt.TARIF AS tarif, ltm.STATUS AS status, pk.NOMOR AS nomor_kunjungan')
      ->from('master.tindakan mtd')
      ->join('layanan.tindakan_medis ltm', 'ltm.TINDAKAN = mtd.ID')
      ->join('master.tarif_tindakan mtt', 'mtd.ID = mtt.TINDAKAN AND mtt.KELAS = 56 AND mtt.STATUS = 1')
      ->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ltm.KUNJUNGAN', 'left')
      #->where('pk.RUANGAN LIKE', '1050202%')
      ->where('pk.NOMOR', $nokun)
      ->where('ltm.STATUS !=', 0)
      ->limit($length, $start); // Implement limit and offset for pagination

    return $this->db->get()->result();
  }


  // Query builder untuk Order Lab
  public function getLabDatatable($nokun, $length, $start)
  {
    $this->db->select('mtd.NAMA AS nama_tindakan, mtt.TARIF AS tarif, pk.NOMOR AS nomor_kunjungan')
      ->from('layanan.order_lab ol')
      ->join('layanan.order_detil_lab odl', 'ol.NOMOR = odl.ORDER_ID')
      ->join('master.tindakan mtd', 'mtd.ID = odl.TINDAKAN')
      ->join('master.tarif_tindakan mtt', 'mtd.ID = mtt.TINDAKAN AND mtt.KELAS = 56 AND mtt.STATUS = 1')
      ->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ol.KUNJUNGAN', 'left')
      // ->where('pk.RUANGAN LIKE', '1050202%')
      ->where('pk.NOMOR', $nokun)
      ->where('ol.STATUS !=', 0)
      ->limit($length, $start); // Add limit for pagination

    return $this->db->get()->result();
  }

  // Query builder untuk Radiologi
  public function getRadiologiDatatable($nokun, $length, $start)
  {
    $this->db->select('mtd.NAMA AS nama_tindakan, mtt.TARIF AS tarif, pk.NOMOR AS nomor_kunjungan')
      ->from('layanan.order_rad ora')
      ->join('layanan.order_detil_rad odr', 'ora.NOMOR = odr.ORDER_ID')
      ->join('master.tindakan mtd', 'mtd.ID = odr.TINDAKAN')
      ->join('master.tarif_tindakan mtt', 'mtd.ID = mtt.TINDAKAN AND mtt.KELAS = 56 AND mtt.STATUS = 1')
      ->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ora.KUNJUNGAN', 'left')
      // ->where('pk.RUANGAN LIKE', '1050202%')
      ->where('pk.NOMOR', $nokun)
      ->where('ora.STATUS !=', 0)
      ->limit($length, $start);

    return $this->db->get()->result();
  }

  // Query builder untuk Radioterapi
  public function getRadioterapiDatatable($nokun, $length, $start)
  {
    $this->db->select('mtd.NAMA AS nama_tindakan, mtt.TARIF AS tarif, pk.NOMOR AS nomor_kunjungan')
      ->from('layanan.order_radioterapi ora')
      ->join('layanan.order_detil_radioterapi odr', 'ora.NOMOR = odr.ORDER_ID')
      ->join('master.tindakan mtd', 'mtd.ID = odr.TINDAKAN')
      ->join('master.tarif_tindakan mtt', 'mtd.ID = mtt.TINDAKAN AND mtt.KELAS = 56 AND mtt.STATUS = 1')
      ->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ora.KUNJUNGAN', 'left')
      // ->where('pk.RUANGAN LIKE', '1050202%')
      ->where('pk.NOMOR', $nokun)
      ->where('ora.STATUS !=', 0)
      ->limit($length, $start);

    return $this->db->get()->result();
  }

  // Query builder untuk Order Resep
  public function getResepDatatable($nokun, $length, $start)
  {
    $this->db->select('pk.NOMOR, ib.ID, ib.NAMA, odr.JUMLAH, IF(hb.HARGA_JUAL < 1000000,ROUND(odr.JUMLAH * (hb.HARGA_JUAL + ((hb.HARGA_JUAL*22.5)/100))), ROUND(odr.JUMLAH * (hb.HARGA_JUAL + ((hb.HARGA_JUAL*17.5)/100)))) AS HARGA_JUAL')
      ->from('layanan.order_resep ores')
      ->join('layanan.order_detil_resep odr', 'odr.ORDER_ID = ores.NOMOR', 'left')
      ->join('inventory.barang ib', 'ib.ID = odr.FARMASI AND ib.STATUS = 1', 'left')
      ->join('inventory.harga_barang hb', 'hb.BARANG = ib.ID', 'left')
      ->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ores.KUNJUNGAN', 'left')
      // ->where('pk.RUANGAN LIKE', '1050202%')
      ->where('pk.NOMOR', $nokun)
      ->where('hb.STATUS', 1)
      ->where('hb.HARGA_JUAL !=', 0)
      ->where('ores.STATUS !=', 0)
      ->limit($length, $start);

    return $this->db->get()->result();
  }

  public function getDaftarTindakan($nokun, $dokter)
  {
    // Select and join necessary tables
    $this->db->select('ltm.ID, mtd.NAMA as nama_tindakan, mtt.TARIF as tarif, ltm.STATUS as status');
    $this->db->from('master.tindakan mtd');
    $this->db->join('layanan.tindakan_medis ltm', 'ltm.TINDAKAN = mtd.ID');
    $this->db->join('master.tarif_tindakan mtt', 'mtd.ID = mtt.TINDAKAN and mtt.KELAS=56 and mtt.STATUS = 1');
    $this->db->where('ltm.KUNJUNGAN', $nokun);
    $this->db->where('ltm.OLEH', $dokter);
    $this->db->order_by('ltm.STATUS', 'DESC');

    // Search filter
    if (!empty($_POST['search']['value'])) {
      $this->db->like('mtd.NAMA', $_POST['search']['value']);
    }

    // Get total count before applying limit (for pagination)
    $total_records = $this->db->count_all_results('', false);

    // Ordering
    if (isset($_POST['order'])) {
      $this->db->order_by($_POST['order']['0']['column'], $_POST['order']['0']['dir']);
    }

    // Limit for pagination
    if ($_POST['length'] != -1) {
      $this->db->limit($_POST['length'], $_POST['start']);
    }

    // Fetch the filtered records
    $query = $this->db->get();
    $filtered_records = $query->num_rows();
    $data = $query->result();

    // Return both the data and counts
    return array(
      'data' => $data,
      'total_records' => $total_records,
      'filtered_records' => $filtered_records,
    );
  }

  public function batalkanTindakan($tindakan_id)
  {
    $data = array(
      'status' => 0,
    );

    $this->db->where('ID', $tindakan_id);
    $this->db->update('layanan.tindakan_medis', $data);

    return $this->db->affected_rows() > 0;
  }
}
