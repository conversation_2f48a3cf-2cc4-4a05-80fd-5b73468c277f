<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PersetujuanTindakanTransfusi extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function simpanForm()
    {
        $post = $this->input->post();
        echo $post['nokun'];
      $dataInformedConsent = array(
            'nokun' => $post['nokun'],
            'jenis_informed_consent' => '3027',
            'dokter_pelaksana' => $post['dokterPelaksana'],
            'pemberi_informasi' => $post['PemberiInformasi'],
            'penerima_informasi' => $post['PenerimaInformasi'],
            'oleh' => $this->session->userdata("id"),
        );
        $idSimpan = $this->pengkajianAwalModel->insertPersetujuanTindakanTD($dataInformedConsent);

       $dataTindakanTD = array(
            'id_informed_consent' => $idSimpan,
            'diagnosis_kerja' => $post['diagnosisKerja'],
            'dasar_diagnosis' => isset($post['dasarDiagnosis']) ? implode(",",$post['dasarDiagnosis']) : NULL,
            'tindakan_kedokteran' => isset($post['transfusiDarahTTD']) ? implode(",",$post['transfusiDarahTTD']) : NULL,
            'indikasi_tindakan' => isset($post['indikasiTindakan']) ? implode(",",$post['indikasiTindakan']) : NULL,
            'indikasi_tindakan_lain' => "",
            'tata_cara' => "",
            'tujuan_tindakan' => isset($post['tujuanTindakan']) ? implode(",",$post['tujuanTindakan']) : NULL,
            'tujuan_pengobatan' => isset($post['tujuanPengobatan']) ? implode(",",$post['tujuanPengobatan']) : NULL,
            'resiko' => isset($post['risikoTTD']) ? implode(",",$post['risikoTTD']) : NULL,
            'komplikasi' => isset($post['komplikasiTTD']) ? implode(",",$post['komplikasiTTD']) : NULL,
            'prognosis' => isset($post['prognosisTTD']) ? implode(",",$post['prognosisTTD']) : NULL,
            'prognosis_lain' => "",
            'alternatif' => isset($post['alternatifRisikoTTD']) ? implode(",",$post['alternatifRisikoTTD']) : NULL,
            'resiko2' => "",
            'resiko2_lain' => "",
            'lain_lain' => $post['lainLain'],
        );
        $this->db->insert('db_informed_consent.tb_terapeutik_aferesis',$dataTindakanTD);
 
        $dataPersetujuanTidakanKedokteranTD = array(
            'id_informed_consent' => $idSimpan,
            'nama_keluarga' => $post['nama'],
            'umur_keluarga' => $post['umur'],
            'jk_keluarga' => $post['jenis_kelamin'],
            'alamat_keluarga' => $post['alamat'],
            'tindakan' => $post['tindakan'],
            'hub_keluarga_dgn_pasien' => $post['hubungan'],
            'ttd_menyatakan' => file_get_contents($this->input->post('signMenyatakan')),
            'ttd_saksi_keluarga' => file_get_contents($this->input->post('signKeluarga')),
            'ttd_saksi_rumah_sakit' => file_get_contents($this->input->post('signRumahsakitTD')),
            'saksi_keluarga' => $post['nama_keluarga'],
            'saksi_rumah_sakit' => $post['nama_saksi_rs'],
        );
        $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran',$dataPersetujuanTidakanKedokteranTD);
        
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        } /* */
    }

    public function detailTindakanTD()
    {
        $id = $this->input->post("id");
        $dHistoryTromboferesis = $this->pengkajianAwalModel->dHistoryTromboferesis($id);
        $listDr = $this->masterModel->listDr();
      /*  $dHistoryTromboferesisDetail = $this->pengkajianAwalModel->dHistoryTromboferesisDetail($id);*/
       // echo "<pre>";print_r($dHistoryTromboferesis);exit();
        $data = array(
            'listDr' => $listDr,
           // 'dHistoryTromboferesis' => $dHistoryTromboferesis,
            //'dHistoryTromboferesisDetail' => $dHistoryTromboferesisDetail,
        );

        $this->load->view('Pengkajian/bankdarah/persetujuanTindakanTD/view', $data);
    }
}

?>