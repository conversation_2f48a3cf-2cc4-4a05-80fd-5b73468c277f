<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PPRA extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/catatanTerintegrasi/PPRAModel','konsultasi/KonsultasiModel'));
      
        $this->load->library('whatsapp');
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_kirim_ppra = $this->uri->segment(3);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      
      $data = array(
        'nokun' => $nokun,
        'nomr' => $getNomr['NORM'],
        'id_kirim_ppra' => $id_kirim_ppra,
        'listRuangan' => $this->masterModel->ruanganRskd(),
        'jawabPPRA' => $this->PPRAModel->jawab_ppra($getNomr['NORM']),
        'historyPPRA' => $this->PPRAModel->history_ppra($getNomr['NORM']),
        'listDarahLengkap' => $this->masterModel->listDarahLengkap($getNomr['NORM']),
        'listFungsiHati' => $this->masterModel->listFungsiHati($getNomr['NORM']),
        'listMarkerInfeksi' => $this->masterModel->listMarkerInfeksi($getNomr['NORM']),
        'listFungsiGinjal' => $this->masterModel->listFungsiGinjal($getNomr['NORM']),
        'listTargetTerapi' => $this->masterModel->referensi(1707),
        'listKondisi' => $this->masterModel->referensi(1708),
        'listOksigenasi' => $this->masterModel->referensi(1771),
        'listDr' => $this->masterModel->listDr(),
        'listKultur' => $this->masterModel->listKultur($getNomr['NORM']),
        'pilihanCPPT' => $this->masterModel->referensi(1407),
        'kesadaran' => $this->masterModel->referensi(5)
      );
      $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/ppra/kirimPPRA', $data);
    }

    public function action_kirim_ppra($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
          
          $getNomr = $this->pengkajianAwalModel->getNomr($post['nokun']);

          $dataKirimPPRA = array(
            'id' => isset($post['id_kirim_ppra']) ? $post['id_kirim_ppra']: "",
            'nokun' => $post['nokun'],
            'tanggal' => isset($post['tanggal']) ? date('Y-m-d',strtotime($post['tanggal'])) : "",
            'jam' => isset($post['jam']) ? $post['jam'] : "",
            'ruangan' => isset($post['ruangan']) ? $post['ruangan'] : "",
            'konsul_dokter' => isset($post['konsul_dokter']) ? $post['konsul_dokter'] : "",
            'ringkasan_perawatan' => isset($post['ringkasan_perawatan']) ? $post['ringkasan_perawatan'] : "",
            'diagnosa_utama' => isset($post['diagnosa_utama']) ? $post['diagnosa_utama'] : "",
            'diagnosa_infeksi' => isset($post['diagnosa_infeksi']) ? $post['diagnosa_infeksi'] : "",
            'target_terapi' => isset($post['target_terapi']) ? $post['target_terapi'] : "",
            'darah_lengkap' => isset($post['darah_lengkap']) ? json_encode($post['darah_lengkap']) : "",
            'fungsi_hati' => isset($post['fungsi_hati']) ? json_encode($post['fungsi_hati']) : "",
            'fungsi_ginjal' => isset($post['fungsi_ginjal']) ? json_encode($post['fungsi_ginjal']) : "",
            'marker_infeksi' => isset($post['marker_infeksi']) ? json_encode(array_values($post['marker_infeksi'])) : "",
            'check_kultur' => isset($post['check_kultur']) ? json_encode(array_values($post['check_kultur'])) : "",
            'lokasi' => isset($post['lokasi']) ? $post['lokasi'] : "",
            'kondisi' => isset($post['kondisi']) ? $post['kondisi'] : "",
            'mikroorganisme' => isset($post['mikroorganisme']) ? $post['mikroorganisme'] : "",
            'lini_pertama' => isset($post['lini_pertama']) ? $post['lini_pertama'] : "",
            'lini_kedua' => isset($post['lini_kedua']) ? $post['lini_kedua'] : "",
            'lini_ketiga' => isset($post['lini_ketiga']) ? $post['lini_ketiga'] : "",
            'dpjp' =>isset($post['dpjp']) ? $post['dpjp'] : "",
            'antimikroba' => isset($post['antimikroba']) ? $post['antimikroba']: "",
            'dosis' => isset($post['dosis']) ? $post['dosis']: "",
            'kondisi_saat_ini' => isset($post['kondisi_saat_ini']) ? $post['kondisi_saat_ini']: "",
            'oksigenasi' => isset($post['oksigenasi']) ? $post['oksigenasi']: "",
            'saturasi' => isset($post['saturasi']) ? $post['saturasi']: "",
            'oleh' => $this->session->userdata('id')
          );

          $selamat = (date('H') >= '05' && date('H') < '10' ? 'Pagi' : (date('H') >= '10' && date('H') < '15' ? 'Siang' : (date('H') >= '15' && date('H') < '19' ? 'Sore' : 'Malam')));

          $getIdDokter = $this->PPRAModel->getIdDokter($this->session->userdata('id'));

          $dokter_pengirim = $this->PPRAModel->getDataDokter($getIdDokter['ID']);
          
          $dokter_tujuan_konsul = $this->PPRAModel->getDataDokter($post['konsul_dokter']);

          $isi_pesan_dokter_pengirim = "Selamat ".$selamat.', '.$dokter_pengirim['NAMA'].", Berikut ini kami menyampaikan:
                                        Bahwa pasien yang bernama ". $getNomr['NAMA_PASIEN'] ."[". $getNomr['NORM'] ."] berhasil dikonsultasikan ke ".$dokter_tujuan_konsul['NAMA']." Terimakasih";

          $isi_pesan_dokter_tujuan_konsul = "Selamat " .$selamat.', '.$dokter_tujuan_konsul['NAMA'].", Berikut ini kami menyampaikan:
                                            Anda menerima konsultasi ppra untuk pasien [".$getNomr['NORM']."], atas nama ".$getNomr['NAMA_PASIEN']." pada tanggal ". date("d M Y", strtotime($post['tanggal'])) ." dari ".$dokter_pengirim['NAMA'].". Mohon untuk mengecek aplikasi EMR dan mengeklik Notifikasi Konsultasi PPRA untuk mengetahui info lebih lanjut dan menjawab konsultasi PPRA. Terimakasih";

          // echo "<pre>".print_r($isi_pesan_dokter_tujuan_konsul)."</pre>";

          $kesadaran = $this->input->post("kesadaran");

          // echo '<pre>';print_r($dataKesadaran);exit();

          $this->db->trans_begin();
        
          if (!empty($post['id_kirim_ppra'])) {
            $this->db->where('keperawatan.tb_ppra_kirim.id', $post['id_kirim_ppra']);
            $this->db->update('keperawatan.tb_ppra_kirim', $dataKirimPPRA);
            $idkirimppra=$this->input->post("id_kirim_ppra");
          }else{
              $this->db->insert('keperawatan.tb_ppra_kirim', $dataKirimPPRA);
              $getIdKirimPPRA = $this->db->insert_id();
              $idkirimppra=$getIdKirimPPRA;

              if (!empty($dokter_pengirim['NOMOR'])) {
                  $nomor = '+62' . substr(trim($dokter_pengirim['NOMOR']), 1);
                  try {
                      $this->whatsapp->send($nomor, array($selamat.', '.$dokter_pengirim['NAMA'], 'ini kami menyampaikan', 'Bahwa pasien yang bernama '. $getNomr['NAMA_PASIEN'] .'['. $getNomr['NORM'] .'] berhasil mengirim konsultasi ppra ke '.$dokter_tujuan_konsul['NAMA']));
                  } catch (Exception $e) {
                  }
              }
    
              if(!empty($dokter_tujuan_konsul['NOMOR'])){
                  $nomor = '+62' . substr(trim($dokter_tujuan_konsul['NOMOR']), 1);
                  try {
                      $this->whatsapp->send($nomor, array($selamat.', '.$dokter_tujuan_konsul['NAMA'], 'ini kami menyampaikan', 'Anda menerima konsultasi ppra untuk pasien ['.$getNomr['NORM'].'], atas nama '.$getNomr['NAMA_PASIEN'].' pada tanggal '. date("d M Y", strtotime($post['tanggal'])) .' dari '.$dokter_pengirim['NAMA'].'. Mohon untuk mengecek aplikasi EMR dan mengeklik Notifikasi Konsultasi PPRA untuk mengetahui info lebih lanjut dan menjawab konsultasi PPRA.'));
                  } catch (Exception $e) {
                  }
              }
          }

          $dataKesadaran = array(
            'data_source' => 37,
            'ref' => $getIdKirimPPRA,
            'nokun' => $post['nokun'],
            'nomr' => $getNomr['NORM'],
            'kesadaran' => $kesadaran,
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
          );

          $dataTandaVital = array(
            'data_source' => 37,
            'ref' => $getIdKirimPPRA,
            'nomr' => $getNomr['NORM'],
            'nokun' => $post['nokun'],
            'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
            'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
            'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
            'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
            'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
          );

          $marker_infeksi_id = $this->input->post("marker_infeksi");
          $marker_infeksi_param = $this->input->post("marker_infeksi_param");
          $marker_infeksi_val = $this->input->post("marker_infeksi_val");
          $marker_infeksi_flag = $this->input->post("marker_infeksi_flag");
          $dataOtoJawab = array(
            'idkirimppra' => $idkirimppra,
            'dataTandaVital' => $dataTandaVital,
            'marker_infeksi_id' => $marker_infeksi_id,
            'marker_infeksi_param' => $marker_infeksi_param,
            'marker_infeksi_val' => $marker_infeksi_val,
            'marker_infeksi_flag' => $marker_infeksi_flag,
            'kesadaran' => $kesadaran,
            'antimikroba' => isset($post['antimikroba']) ? $post['antimikroba'] : "",
            'check_kultur_id' => isset($post['check_kultur']) ? $post['check_kultur'] : "",
            'check_kultur_param' => isset($post['check_kultur_param']) ? $post['check_kultur_param'] : "",
            'check_kultur_val' => isset($post['check_kultur_val']) ? $post['check_kultur_val'] : "",
          );

          if(!empty($post['id_kirim_ppra'])) {
            $this->db->where('tb_tanda_vital.data_source', 37);
            $this->db->where('tb_tanda_vital.ref', $post['id_kirim_ppra']);
            $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
            
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
              // $this->jawab_otomatis($dataOtoJawab);
            }
            
            echo json_encode($result);
          } else {
            
            $this->db->insert('db_pasien.tb_kesadaran', $dataKesadaran);
            $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $this->jawab_otomatis($dataOtoJawab);
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }

        }
      }
    }

    private function jawab_otomatis($dataOtoJawab=''){

      $idkirimppra=$dataOtoJawab['idkirimppra'];
      $dataTandaVital=$dataOtoJawab['dataTandaVital'];
      $kesadaran=$dataOtoJawab['kesadaran'];
      $marker_infeksi_id=$dataOtoJawab['marker_infeksi_id'];
      $marker_infeksi_param=$dataOtoJawab['marker_infeksi_param'];
      $marker_infeksi_val=$dataOtoJawab['marker_infeksi_val'];
      $marker_infeksi_flag=$dataOtoJawab['marker_infeksi_flag'];
      $antimikroba=strtolower(trim($dataOtoJawab['antimikroba']));
      $check_kultur_id=$dataOtoJawab['check_kultur_id'];
      $check_kultur_param=$dataOtoJawab['check_kultur_param'];
      $check_kultur_val=$dataOtoJawab['check_kultur_val'];

      $hasil_procalcitonin="";
      $flag_reaksi_protein="";
      $check_kultur_cefepime="";
      if($marker_infeksi_id!=""){
        foreach ($marker_infeksi_id as $key => $value) {
          if($marker_infeksi_param[$key]=="Procalcitonin"){
            $hasil_procalcitonin=$marker_infeksi_val[$key];
          }
          if($marker_infeksi_param[$key]=="C-Reaktif Protein"){
            $flag_reaksi_protein=$marker_infeksi_flag[$key];
          }

        }
      }
      if($check_kultur_id!=""){
        foreach ($check_kultur_id as $keyk => $valuek) {
          if($check_kultur_param[$keyk]=="Cefepime"){
            $check_kultur_cefepime=$check_kultur_val[$keyk];
          }

        }
      }

      // $findkey = array_search('Procalcitonin', $marker_infeksi_param); 
      // $hasil_procalcitonin=($findkey >-1)?$marker_infeksi_val[$findkey]:'';

      // $findkeycefepime = array_search('Cefepime', $check_kultur_param); 
      // $check_kultur_cefepime=($findkeycefepime >-1)?$check_kultur_val[$findkeycefepime]:'';

      // $findkeyreaksiprotein = array_search('C-Reaktif Protein', $check_kultur_param); 
      // $flag_reaksi_protein=($findkeyreaksiprotein >-1)?$marker_infeksi_flag[$findkeyreaksiprotein]:'';

      // file_put_contents('logppra3.txt', "1:".$antimikroba."-2:".$dataTandaVital['pernapasan']."-3:".$dataTandaVital['td_sistolik']."-4:".$hasil_procalcitonin."-5:".$kesadaran."-6:".$check_kultur_cefepime."-7:".$flag_reaksi_protein , FILE_APPEND | LOCK_EX);
      if((stripos($antimikroba,"cefepim") >-1 || stripos($antimikroba,"cefepime") >-1) && $dataTandaVital['pernapasan'] >= 22 && $dataTandaVital['td_sistolik'] <= 100 
      && $hasil_procalcitonin > 1 && $kesadaran!=9 && $check_kultur_cefepime=='S' && $flag_reaksi_protein=='H'){

        $updateDataStatusPPRA = array(
          'status' => 2
        );
        
        $dataJawabPPRA = array(
          'id_kirim_ppra' => $idkirimppra,
          'acc' => "1",
          'rekomendasi' => "Acc",
          'disetujui_dgn_catatan' => "Ambil Kultur Ulang",
          'evaluasi' => "6013",
          'nama_kpra' => '95',
          'oleh' => '347' //dr.MUHAMAD ALFIN HANIF
        );

        $this->db->trans_begin();
        $this->db->where('keperawatan.tb_ppra_kirim.id', $idkirimppra);
        $this->db->update('keperawatan.tb_ppra_kirim', $updateDataStatusPPRA);
        
        // $this->db->where('keperawatan.tb_ppra_jawab.id_kirim_ppra',$idkirimppra);
        // $q = $this->db->get('keperawatan.tb_ppra_jawab');
        // if ( $q->num_rows() > 0 ){
            // $this->db->where('keperawatan.tb_ppra_jawab.id_kirim_ppra', $idkirimppra);
            // $this->db->update('keperawatan.tb_ppra_jawab',$dataJawabPPRA);
        // } else {
            $this->db->insert('keperawatan.tb_ppra_jawab', $dataJawabPPRA);
        // }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
        } else {
          $this->db->trans_commit();
        }

      }
      
    }

    public function view_kirim_ppra()
    {
      $id_kirim_ppra = $this->input->post('id');
      $history_kirim = $this->PPRAModel->history_kirim($id_kirim_ppra);

      $id_lab_dl = isset($history_kirim['darah_lengkap']) ? json_decode($history_kirim['darah_lengkap']) : "";
      $id_lab_fh = isset($history_kirim['fungsi_hati']) ? json_decode($history_kirim['fungsi_hati']) : "";
      $id_lab_fg = isset($history_kirim['fungsi_ginjal']) ? json_decode($history_kirim['fungsi_ginjal']) : "";
      $id_lab_mi = isset($history_kirim['marker_infeksi']) ? json_decode($history_kirim['marker_infeksi']) : "";
      $id_lab_k = isset($history_kirim['check_kultur']) ? json_decode($history_kirim['check_kultur']) : "";


      if(!empty($id_lab_dl)) { 
        $getDarahLengkap = $this->PPRAModel->getDarahLengkap($id_lab_dl);
      }
      if(!empty($id_lab_fh)) { 
        $getFungsiHati = $this->PPRAModel->getFungsiHati($id_lab_fh);
      }
      if(!empty($id_lab_fg)) {
        $getFungsiGinjal = $this->PPRAModel->getFungsiGinjal($id_lab_fg);
      }
      if(!empty($id_lab_mi)) {
        $getMarkerInfeksi = $this->PPRAModel->getMarkerInfeksi($id_lab_mi);
      }
      if(!empty($id_lab_k)) {
        $getKultur = $this->PPRAModel->getKultur($id_lab_k);
      }

      $getNomr = $this->pengkajianAwalModel->getNomr($history_kirim['nokun']);

      $data = array(
        'id_kirim_ppra' => isset($id_kirim_ppra) ? $id_kirim_ppra : "",
        'history_kirim' => isset($history_kirim) ? $history_kirim : "",
        'getDarahLengkap' => isset($getDarahLengkap) ? $getDarahLengkap : "",
        'getFungsiHati' => isset($getFungsiHati) ? $getFungsiHati : "",
        'getFungsiGinjal' => isset($getFungsiGinjal) ? $getFungsiGinjal : "",
        'getMarkerInfeksi' => isset($getMarkerInfeksi) ? $getMarkerInfeksi : "",
        'getKultur' => isset($getKultur) ? $getKultur : "",
        'listDr' => $this->masterModel->listDr(),
        'listRuangan' => $this->masterModel->ruanganRskd(),
        'listTargetTerapi' => $this->masterModel->referensi(1707),
        'listKondisi' => $this->masterModel->referensi(1708),
        'listDarahLengkap' => $this->masterModel->listDarahLengkap($getNomr['NORM']),
        'listFungsiHati' => $this->masterModel->listFungsiHati($getNomr['NORM']),
        'listMarkerInfeksi' => $this->masterModel->listMarkerInfeksi($getNomr['NORM']),
        'listFungsiGinjal' => $this->masterModel->listFungsiGinjal($getNomr['NORM']),
        'listKultur' => $this->masterModel->listKultur($getNomr['NORM']),
        'listOksigenasi' => $this->masterModel->referensi(1771),
        'kesadaran' => $this->masterModel->referensi(5)
      );

      $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/ppra/editKirimPPRA', $data);
    }

    public function edit_kirim_ppra()
    {
      $post = $this->input->post();

      $dataEditKirimPPRA = array(
        'tanggal' => isset($post['tanggal']) ? date('Y-m-d',strtotime($post['tanggal'])) : "",
        'jam' => isset($post['jam']) ? $post['jam'] : "",
        'ruangan' => isset($post['ruangan']) ? $post['ruangan'] : "",
        'konsul_dokter' => isset($post['konsul_dokter']) ? $post['konsul_dokter'] : "",
        'ringkasan_perawatan' => isset($post['ringkasan_perawatan']) ? $post['ringkasan_perawatan'] : "",
        'diagnosa_utama' => isset($post['diagnosa_utama']) ? $post['diagnosa_utama'] : "",
        'diagnosa_infeksi' => isset($post['diagnosa_infeksi']) ? $post['diagnosa_infeksi'] : "",
        'target_terapi' => isset($post['target_terapi']) ? $post['target_terapi'] : "",
        'darah_lengkap' => isset($post['darah_lengkap']) ? json_encode($post['darah_lengkap']) : "",
        'fungsi_hati' => isset($post['fungsi_hati']) ? json_encode($post['fungsi_hati']) : "",
        'fungsi_ginjal' => isset($post['fungsi_ginjal']) ? json_encode($post['fungsi_ginjal']) : "",
        'marker_infeksi' => isset($post['marker_infeksi']) ? json_encode($post['marker_infeksi']) : "",
        'check_kultur' => isset($post['check_kultur']) ? json_encode($post['check_kultur']) : "",
        'lokasi' => isset($post['lokasi']) ? $post['lokasi'] : "",
        'kondisi' => isset($post['kondisi']) ? $post['kondisi'] : "",
        'mikroorganisme' => isset($post['mikroorganisme']) ? $post['mikroorganisme'] : "",
        'lini_pertama' => isset($post['lini_pertama']) ? $post['lini_pertama'] : "",
        'lini_kedua' => isset($post['lini_kedua']) ? $post['lini_kedua'] : "",
        'lini_ketiga' => isset($post['lini_ketiga']) ? $post['lini_ketiga'] : "",
        'dpjp' =>isset($post['dpjp']) ? $post['dpjp'] : "",
        'antimikroba' => isset($post['antimikroba']) ? $post['antimikroba']: "",
        'dosis' => isset($post['dosis']) ? $post['dosis']: "",
        'kondisi_saat_ini' => isset($post['kondisi_saat_ini']) ? $post['kondisi_saat_ini']: "",
        'oksigenasi' => isset($post['oksigenasi']) ? $post['oksigenasi']: "",
        'saturasi' => isset($post['saturasi']) ? $post['saturasi']: "",
      );

      $dataEditTandaVital = array(
        'data_source' => 37,
        'ref' => $post['id_kirim_ppra'],
        'nomr' => $post['nomr'],
        'nokun' => $post['nokun'],
        'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
        'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
        'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
        'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
        'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
        'oleh' => $this->session->userdata('id'),
        'status' => 1,
      );

      $dataEditKesadaran = array(
        'data_source' => 37,
        'ref' => $post['id_kirim_ppra'],
        'nokun' => $post['nokun'],
        'nomr' => $post['nomr'],
        'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
        'oleh' => $this->session->userdata('id'),
        'status' => 1,
      );

      $this->db->trans_begin();
        
      if (!empty($post['id_kirim_ppra'])) {
        $this->db->where('keperawatan.tb_ppra_kirim.id', $post['id_kirim_ppra']);
        $this->db->update('keperawatan.tb_ppra_kirim', $dataEditKirimPPRA);

        $this->db->where('db_pasien.tb_tanda_vital.data_source', 37);
        $this->db->where('db_pasien.tb_tanda_vital.ref', $post['id_kirim_ppra']);
        $this->db->update('db_pasien.tb_tanda_vital', $dataEditTandaVital);

        $this->db->where('db_pasien.tb_kesadaran.data_source', 37);
        $this->db->where('db_pasien.tb_kesadaran.ref', $post['id_kirim_ppra']);
        $this->db->where('db_pasien.tb_kesadaran.nomr', $post['nomr']);
        $qk = $this->db->get('db_pasien.tb_kesadaran');
     
        if ( $qk->num_rows() > 0 ){
          $this->db->where('db_pasien.tb_kesadaran.data_source', 37);
          $this->db->where('db_pasien.tb_kesadaran.ref', $post['id_kirim_ppra']);
          $this->db->where('db_pasien.tb_kesadaran.nomr', $post['nomr']);
          $this->db->update('db_pasien.tb_kesadaran', $dataEditKesadaran);
        } else {
           $this->db->insert('db_pasien.tb_kesadaran', $dataEditKesadaran);
        }
      }

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success_ubah');
        $this->jawab_otomatis($dataEditTandaVital);
      }
      
      echo json_encode($result);
    }

    public function index_jawab_ppra()
    {
      $id_kirim_ppra = $this->input->post('id');

      $history_kirim = $this->PPRAModel->history_kirim($id_kirim_ppra);

      $id_lab_dl = isset($history_kirim['darah_lengkap']) ? json_decode($history_kirim['darah_lengkap']) : "";
      $id_lab_fh = isset($history_kirim['fungsi_hati']) ? json_decode($history_kirim['fungsi_hati']) : "";
      $id_lab_fg = isset($history_kirim['fungsi_ginjal']) ? json_decode($history_kirim['fungsi_ginjal']) : "";
      $id_lab_mi = isset($history_kirim['marker_infeksi']) ? json_decode($history_kirim['marker_infeksi']) : "";
      $id_lab_k = isset($history_kirim['check_kultur']) ? json_decode($history_kirim['check_kultur']) : "";


      if(!empty($id_lab_dl)) { 
        $getDarahLengkap = $this->PPRAModel->getDarahLengkap($id_lab_dl);
      }
      if(!empty($id_lab_fh)) { 
        $getFungsiHati = $this->PPRAModel->getFungsiHati($id_lab_fh);
      }
      if(!empty($id_lab_fg)) {
        $getFungsiGinjal = $this->PPRAModel->getFungsiGinjal($id_lab_fg);
      }
      if(!empty($id_lab_mi)) {
        $getMarkerInfeksi = $this->PPRAModel->getMarkerInfeksi($id_lab_mi);
      }
      if(!empty($id_lab_k)) {
        $getKultur = $this->PPRAModel->getKultur($id_lab_k);
      }


      $dataJawabPPRA = array(
        'id_kirim_ppra' => isset($id_kirim_ppra) ? $id_kirim_ppra: "",
        'listEvaluasi' => $this->masterModel->referensi(1709),
        'listDr' => $this->masterModel->listDr(),
        'listRuangan' => $this->masterModel->ruanganRskd(),
        'history_kirim' => $history_kirim,
        'getDarahLengkap' => isset($getDarahLengkap) ? $getDarahLengkap : "",
        'getFungsiHati' => isset($getFungsiHati) ? $getFungsiHati : "",
        'getFungsiGinjal' => isset($getFungsiGinjal) ? $getFungsiGinjal : "",
        'getMarkerInfeksi' => isset($getMarkerInfeksi) ? $getMarkerInfeksi : "",
        'getKultur' => isset($getKultur) ? $getKultur : "",
        'listTargetTerapi' => $this->masterModel->referensi(1707),
        'listKondisi' => $this->masterModel->referensi(1708),
        'listOksigenasi' => $this->masterModel->referensi(1771)
      );

      $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/ppra/jawabPPRA', $dataJawabPPRA);
    }

    public function action_jawab_ppra($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $updateDataStatusPPRA = array(
            'status' => 2
          );
          
          $dataJawabPPRA = array(
            'id' => isset($post['id_jawab_ppra']) ? $post['id_jawab_ppra']: "",
            'id_kirim_ppra' => isset($post['id_kirim_ppra']) ? $post['id_kirim_ppra']: "",
            'acc' => isset($post['acc']) ? $post['acc']: "",
            'rekomendasi' => isset($post['rekomendasi']) ? $post['rekomendasi']: "",
            'disetujui_dgn_catatan' => isset($post['disetujui_dgn_catatan']) ? $post['disetujui_dgn_catatan']: "",
            'evaluasi' => isset($post['evaluasi']) ? $post['evaluasi']: "",
            'nama_kpra' => isset($post['nama_kpra']) ? $post['nama_kpra']: "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
          $this->db->where('keperawatan.tb_ppra_kirim.id', $post['id_kirim_ppra']);
          $this->db->update('keperawatan.tb_ppra_kirim', $updateDataStatusPPRA);

          $this->db->insert('keperawatan.tb_ppra_jawab', $dataJawabPPRA);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success_simpan');
          }
  
          echo json_encode($result);
        }
      }
    }

    public function view_jawab_ppra()
    {
      $id_jawab_ppra = $this->input->post('id');
      $data = array(
        'id_jawab_ppra' => $id_jawab_ppra,
        'history_jawab' => $this->PPRAModel->history_jawab($id_jawab_ppra),
        'listEvaluasi' => $this->masterModel->referensi(1709),
        'listDr' => $this->masterModel->listDr(),
      );
      $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/ppra/editJawabPPRA', $data);
    }

    public function edit_jawab_ppra()
    {
      $post = $this->input->post();

      $dataEditJawabPPRA = array(
        'acc' => isset($post['acc']) ? $post['acc']: "",
        'rekomendasi' => isset($post['rekomendasi']) ? $post['rekomendasi']: "",
        'disetujui_dgn_catatan' => isset($post['disetujui_dgn_catatan']) ? $post['disetujui_dgn_catatan']: "",
        'evaluasi' => isset($post['evaluasi']) ? $post['evaluasi']: "",
        'nama_kpra' => isset($post['nama_kpra']) ? $post['nama_kpra']: ""
      );

      
      $this->db->trans_begin();

      if (!empty($post['id_jawab_ppra'])) {
        $this->db->where('keperawatan.tb_ppra_jawab.id', $post['id_jawab_ppra']);
        $this->db->update('keperawatan.tb_ppra_jawab', $dataEditJawabPPRA);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success_ubah');
        }

        echo json_encode($result);
      }


    }

    public function history_ppra()
    {
      $id_jawab = $this->input->post('id');
      // $nokun = $this->uri->segment(2);
      // var_dump($nokun);exit;
      $getPengkajian = $this->PPRAModel->getPengkajian($id_jawab);
      $getNomr = $this->pengkajianAwalModel->getNomr($getPengkajian['nokun']);
      $id_lab_dl = isset($getPengkajian['darah_lengkap']) ? json_decode($getPengkajian['darah_lengkap']) : "";
      $id_lab_fh = isset($getPengkajian['fungsi_hati']) ? json_decode($getPengkajian['fungsi_hati']) : "";
      $id_lab_fg = isset($getPengkajian['fungsi_ginjal']) ? json_decode($getPengkajian['fungsi_ginjal']) : "";
      $id_lab_mi = isset($getPengkajian['marker_infeksi']) ? json_decode($getPengkajian['marker_infeksi']) : "";
      $id_lab_k = isset($getPengkajian['check_kultur']) ? json_decode($getPengkajian['check_kultur']) : "";
      
      if(!empty($id_lab_dl)) { 
        $getDarahLengkap = $this->PPRAModel->getDarahLengkap($id_lab_dl);
      }
      if(!empty($id_lab_fh)) { 
        $getFungsiHati = $this->PPRAModel->getFungsiHati($id_lab_fh);
      }
      if(!empty($id_lab_fg)) {
        $getFungsiGinjal = $this->PPRAModel->getFungsiGinjal($id_lab_fg);
      }
      if(!empty($id_lab_mi)) {
        $getMarkerInfeksi = $this->PPRAModel->getMarkerInfeksi($id_lab_mi);
      }
      if(!empty($id_lab_k)) {
        $getKultur = $this->PPRAModel->getKultur($id_lab_k);
      }
      
      $data = array(
        'id_jawab' => $id_jawab,
        'listRuangan' => $this->masterModel->ruanganRskd(),
        'listTargetTerapi' => $this->masterModel->referensi(1707),
        'listKondisi' => $this->masterModel->referensi(1708),
        'listDr' => $this->masterModel->listDr(),
        'listEvaluasi' => $this->masterModel->referensi(1709),
        'getPengkajian' => $getPengkajian,
        'listDarahLengkap' => $this->masterModel->listDarahLengkap($getNomr['NORM']),
        'getDarahLengkap' => isset($getDarahLengkap) ? $getDarahLengkap : "",
        'getFungsiHati' => isset($getFungsiHati) ? $getFungsiHati : "",
        'getFungsiGinjal' => isset($getFungsiGinjal) ? $getFungsiGinjal : "",
        'getMarkerInfeksi' => isset($getMarkerInfeksi) ? $getMarkerInfeksi : "",
        'getKultur' => isset($getKultur) ? $getKultur : "",
        'listOksigenasi' => $this->masterModel->referensi(1771),
      );
      
      $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/ppra/historyPPRA', $data);
    }

    public function lihatJawabNotifikasi()
    {
      $idKonsul = $this->uri->segment(6);

      $isiModal = $this->ambilFormJawab($idKonsul);
      $nokun = $isiModal[0]['nokun'];
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $nomr = $getNomr['NORM'];
      $history_kirim = $this->PPRAModel->history_kirim($idKonsul);

      $id_lab_dl = isset($history_kirim['darah_lengkap']) ? json_decode($history_kirim['darah_lengkap']) : "";
      $id_lab_fh = isset($history_kirim['fungsi_hati']) ? json_decode($history_kirim['fungsi_hati']) : "";
      $id_lab_fg = isset($history_kirim['fungsi_ginjal']) ? json_decode($history_kirim['fungsi_ginjal']) : "";
      $id_lab_mi = isset($history_kirim['marker_infeksi']) ? json_decode($history_kirim['marker_infeksi']) : "";
      $id_lab_k = isset($history_kirim['check_kultur']) ? json_decode($history_kirim['check_kultur']) : "";

      if(!empty($id_lab_dl)) { 
        $getDarahLengkap = $this->PPRAModel->getDarahLengkap($id_lab_dl);
      }
      if(!empty($id_lab_fh)) { 
        $getFungsiHati = $this->PPRAModel->getFungsiHati($id_lab_fh);
      }
      if(!empty($id_lab_fg)) {
        $getFungsiGinjal = $this->PPRAModel->getFungsiGinjal($id_lab_fg);
      }
      if(!empty($id_lab_mi)) {
        $getMarkerInfeksi = $this->PPRAModel->getMarkerInfeksi($id_lab_mi);
      }
      if(!empty($id_lab_k)) {
        $getKultur = $this->PPRAModel->getKultur($id_lab_k);
      }

      $data = array(
        'title' => 'Jawab Konsul',
        'isi' => 'Pengkajian/jawabPPRA/index',
        'isiModal' => $isiModal,
        'nokun' => $nokun,
        'idKonsul' => $idKonsul,
        'history_kirim' => $history_kirim,
        'listRuangan' => $this->masterModel->ruanganRskd(),
        'listDr' => $this->masterModel->listDr(),
        'getDarahLengkap' => isset($getDarahLengkap) ? $getDarahLengkap : "",
        'getFungsiHati' => isset($getFungsiHati) ? $getFungsiHati : "",
        'getFungsiGinjal' => isset($getFungsiGinjal) ? $getFungsiGinjal : "",
        'getMarkerInfeksi' => isset($getMarkerInfeksi) ? $getMarkerInfeksi : "",
        'getKultur' => isset($getKultur) ? $getKultur : "",
        'listTargetTerapi' => $this->masterModel->referensi(1707),
        'listKondisi' => $this->masterModel->referensi(1708),
        'listEvaluasi' => $this->masterModel->referensi(1709),
        'historyPPRA' => $this->PPRAModel->history_ppra($nomr),
      );
      $this->load->view('layout/wrapper', $data);
    }

    public function ambilFormJawab($idKonsul)
    {
      $detailHistory = $this->PPRAModel->formJawabKonsul($idKonsul);
      $id_kirim_ppra = $this->input->post('id');

      $data = array();
      foreach ($detailHistory as $main) {
        $isi_array = array();
        $isi_array['id_konsul'] = $main['id'];
        $isi_array['pasien'] = $main['pasien'];
        $isi_array['norm'] = $main['norm'];
        $isi_array['nokun'] = $main['nokun'];

        $data[] = $isi_array;
      }

      // echo "<pre>".print_r($data)."</pre>";
      // exit();

      return $data;
    }

    public function ppra_tvterakhir()
    {
      if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
   
          $nomr = $this->input->post('nomr');
          $result = $this->PPRAModel->ppra_tvterakhir($nomr);
          // $result = $this->TandaVitalModel->get_table(true);
  
          echo json_encode(array(
              'status' => 'success',
              'data' => $result
            )
          );
      }
    }

}