<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ResikoJatuhLanjutUsia_Model extends CI_Model {

    public function simpanFRisikoJatuhLanjutUsia($data)
    {
      $this->db->trans_begin();
      $this->db->insert('db_layanan.tb_geriatri_resikojatuhlanjutusia', $data);
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }

      echo json_encode($result);
    }

    public function getResikoJatuhLanjutUsia($id)
    {
      $query = $this->db->query("SELECT
                  tgr.*
                FROM
                  db_layanan.tb_geriatri_resikojatuhlanjutusia tgr 
                WHERE
                  tgr.id = $id");

      return $query->row_array();
    }

    public function listResikoJatuhLanjutUsia($nomr)
    {
      $query = $this->db->query("SELECT tgr.*
        FROM db_layanan.tb_geriatri_resikojatuhlanjutusia tgr
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tgr.nokun
        LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
        WHERE pp.NORM = '$nomr'
        ORDER BY tgr.id DESC");

      return $query;
    }

    public function updateFResikoJatuhLanjutUsia($data, $id)
    {
      $this->db->trans_begin();
      $this->db->where('id', $id);
      $this->db->update('db_layanan.tb_geriatri_resikojatuhlanjutusia', $data);
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }

      echo json_encode($result);
    }
    
}

/* End of file ResikoJatuhLanjutUsia_Model.php */
/* Location: ./application/models/geriatri/ResikoJatuhLanjutUsia_Model.php */
