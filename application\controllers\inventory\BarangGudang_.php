<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BarangGudang extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(24, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
		$this->load->model('inventory/Model_barang_gudang');
	}

	public function index()
	{
		$this->load->helper('url');
		$this->load->helper('form');
		
		$gudang = $this->Model_barang_gudang->get_list_gudang();

		$opt = array('' => 'Pilih gudang');
		foreach ($gudang as $gdg) {
			$opt[$gdg] = $gdg;
		}
		$form_gudang = form_dropdown('',$opt,'','id="GUDANG" class="form-control"');
		$data = array(
			'title'         => 'Halaman Barang Gudang',
			'isi'           => 'inventory/barang_gudang/barang_gudang',
			'form_gudang'   => $form_gudang,
		);  
		$this->load->view('layout/wrapper',$data);   

	}

	public function ajax_list()
	{
		$list = $this->Model_barang_gudang->get_datatables();
		$data = array();
		$no = $_POST['start'];
		foreach ($list as $brg) {
			$no++;
			$row = array();
			$row[] = $no;
			$row[] = $brg->BARANG;
			$row[] = $brg->SATUAN;
			$row[] = $brg->STOK;
			$row[] = $brg->HARGA;
			$data[] = $row;
		}

		$output = array(
			"draw" => $_POST['draw'],
			"recordsTotal" => $this->Model_barang_gudang->count_all(),
			"recordsFiltered" => $this->Model_barang_gudang->count_filtered(),
			"data" => $data,
		);
		//output to json format
		echo json_encode($output);
	}

}
