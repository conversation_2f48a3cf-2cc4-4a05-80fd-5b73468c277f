<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Barang extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(20, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('inventory/Model_barang', 'inventory/Model_kategori', 'inventory/Model_satuan', 'inventory/Model_penyedia'));
  }

  public function index()
  {
    $datakategori   =  $this->Model_kategori->tampilkan_data()->result();
    $datasatuan     =  $this->Model_satuan->tampilkan_data()->result();
    $datapenyedia   =  $this->Model_penyedia->tampilkan_data()->result();
    $barang         =  $this->Model_barang->tampilkan_data()->result();
    $data = array(
      'title'         => 'Halaman Master Barang',
      'isi'           => 'inventory/barang/data_barang',
      'datakategori'  => $datakategori,
      'datasatuan'    => $datasatuan,
      'datapenyedia'  => $datapenyedia,
      'barang'        => $barang,
    );
    $this->load->view('layout/wrapper', $data);
  }

  public function datatable()
  {
    $builder = $this->datatables
      ->select('br.ID,br.NAMA as nama,ka.NAMA as kategori,pe.NAMA as penyedia,sa.DESKRIPSI as satuan, br.MIN_STOK as minstok')
      ->from('invenumum.barang as br')
      ->join('invenumum.kategori as ka', 'br.KATEGORI = ka.ID')
      ->join('invenumum.satuan as sa', 'sa.ID = br.SATUAN')
      ->join('invenumum.penyedia as pe', 'pe.ID = br.PENYEDIA');
    $builder->add_column('edit', 'Edit', 'guid')
      ->add_column('soft', 'Hapus', 'guid')
      ->add_column('restore', 'Purge/Restore', 'guid');
    $this->output->set_content_type('application/json')->set_output($this->datatables->generate('json'));
  }

  public function insert()
  {
    if (isset($_POST['submit'])) {
      $NAMA               =   $this->input->post('NAMA');
      $KATEGORI           =   $this->input->post('KATEGORI');
      $KODE_BMN           =   $this->input->post('KODE_BMN');
      $SATUAN             =   $this->input->post('SATUAN');
      $PENYEDIA           =   $this->input->post('PENYEDIA');
      // $SATUAN_KONVERSI    =   $this->input->post('SATUAN_KONVERSI');
      // $JUMLAH_KONVERSI    =   $this->input->post('JUMLAH_KONVERSI');
      $HARGA              =   $this->input->post('HARGA');
      //echo "<pre>";print_r($_POST);exit();
      $sql = $this->db->query("SELECT BARANG FROM invenumum.barang_master where BARANG='$NAMA'");
      $cek_nama = $sql->num_rows();
      if ($cek_nama > 0) {
        $this->session->set_flashdata('warning', 'Nama barang sudah ada...!');
        redirect('inventory/Barang');
      } else {
        $data           =   array(
          'BARANG'          => $NAMA,
          'KODE_BMN'        => $KODE_BMN,
          'SATUAN'          => $SATUAN,
          'PENYEDIA'        => $PENYEDIA,
          'HARGA'           => $HARGA
          // 'SATUAN_KONVERSI' => $SATUAN_KONVERSI,
          // 'JUMLAH_KONVERSI' => $JUMLAH_KONVERSI
        );
        $this->Model_barang->post($data);
        redirect('inventory/Barang');
      }
    }
  }

  function get_autocomplete()
  {
    if (isset($_GET['term'])) {
      $result = $this->Model_barang->cari_barang($_GET['term']);
      if (count($result) > 0) {
        foreach ($result as $row)
          $arr_result[] = array(
            'label'     => $row->BARANG,
            'idbarang'   => $row->ID_BARANG,
          );
        echo json_encode($arr_result);
      }
    }
  }


  public function listBarang()
  {
    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));
    $listBarang = $this->Model_barang->barang();
    // echo "<pre>";print_r($listPegawai);exit();
    $data  = array();
    $no    = 1;
    foreach ($listBarang->result() as $lp) {
      if ($lp->STATUS == 1) {
        $check = "checked";
      } else {
        $check = "";
      }
      $data[] = array(
        $no,
        $lp->NAMA,
        $lp->SATUAN,
        $lp->HARGA,
        $lp->KODE_BMN,
        '<div class="checkbox checkbox-primary">
        <input type="checkbox" id="statusBarang' . $lp->ID . '" value="' . $lp->ID . '"  class="Dbarang" ' . $check . '>
        <label for="statusBarang' . $lp->ID . '"></label>
        </div>',

        '<a href="#formedit" class="btn btn-md btn-block btn-info" data-toggle="modal" data-id="' . $lp->ID . '"><i class="fas fa-edit"></i> Ubah</a>',

      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listBarang->num_rows(),
      "recordsFiltered" => $listBarang->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function sBarangAktif()
  {
    $id = $this->input->post('id');

    $data = array(
      'STATUS' => 1,
    );

    $this->Model_barang->sBarangAktif($id, $data);
  }

  public function sBarangNonAktif()
  {
    $id = $this->input->post('id');

    $data = array(
      'STATUS' => 0,
    );

    $this->Model_barang->sBarangNonAktif($id, $data);
  }

  public function databarang()
  {
    $id       = $this->input->post('id');
    $isiModal = $this->ambildatabarang($id);
    $satuan   =  $this->Model_satuan->tampilkan_data()->result();
    $penyedia =  $this->Model_penyedia->tampilkan_data()->result();
    $data     = array(
      'isiModal' => $isiModal,
      'penyedia' => $penyedia,
      'satuan' => $satuan,
    );
    $this->load->view('inventory/barang/modalEditBarang', $data);
  }

  public function ambildatabarang($id)
  {
    $databarang = $this->Model_barang->modaldatabarang($id);
    $data = array();
    foreach ($databarang as $main) {
      $isi_array = array();
      $isi_array['ID_BARANG'] = $main['ID_BARANG'];
      $isi_array['BARANG'] = $main['BARANG'];
      $isi_array['SATUAN'] = $main['SATUAN'];
      // $isi_array['SATUAN_KONVERSI'] = $main['SATUAN_KONVERSI'];
      // $isi_array['JUMLAH_KONVERSI'] = $main['JUMLAH_KONVERSI'];
      $isi_array['KODE_BMN'] = $main['KODE_BMN'];
      $isi_array['PENYEDIA'] = $main['PENYEDIA'];
      $isi_array['HARGA']   = $main['HARGA'];
      $isi_array['STATUS'] = $main['STATUS'];
      $data[] = $isi_array;
    }
    return $data;
  }

  public function update()
  {
    $id = $this->input->post('ID');
    $data = array(
      'BARANG'            => $this->input->post('NAMA'),
      'KODE_BMN'          => $this->input->post('KODE_BMN'),
      'SATUAN'            => $this->input->post('SATUAN'),
      'PENYEDIA'          => $this->input->post('PENYEDIA'),
      'HARGA'             => $this->input->post('HARGA')
      // 'SATUAN_KONVERSI'   => $this->input->post('SATUAN_KONVERSI'),
      // 'JUMLAH_KONVERSI'   => $this->input->post('JUMLAH_KONVERSI')
    );
    // echo "<pre>";print_r($data);exit();
    $this->Model_barang->update($id, $data);
    redirect('inventory/Barang');
  }
}
