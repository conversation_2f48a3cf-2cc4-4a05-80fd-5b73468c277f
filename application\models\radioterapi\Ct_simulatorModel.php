<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ct_simulatorModel extends CI_Model {

  public function __construct()
  {
      parent::__construct();
      // $this->db = $this->load->database('default', true);
  }

  public function simpanCt_simulatorDr($data)
  {
    $this->db->trans_begin();
    $this->db->insert('medis.tb_ctSimulatorDokter', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function simpanCt_simulatorRadiografer($data)
  {
    if ($this->db->insert('medis.tb_ctSimulatorRadiografer', $data)) {
        return $this->db->insert_id();
    } else {
        $error = $this->db->error();
      // echo "Error Code: " . $error['code'] . "<br>";
      // echo "Error Message: " . $error['message'];
    }
    
  }

  public function simpanCt_simulatorRadiograferImage($data)
  {
    if ($this->db->insert('medis.tb_ctSimulatorSetupNote', $data)) {
      return $this->db->insert_id();
    } else {
        $error = $this->db->error(); // Ambil informasi error
    }
  }

  public function update_ctsimulatorsetupnote($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_ctSimulatorSetupNote', $data);
  }
  public function del_ctsimulatorsetupnote($data,$id)
  {
    $this->db->where_not_in('id', $data);
    $this->db->where('idradiografi', $id);
    $this->db->delete('medis.tb_ctSimulatorSetupNote');
  }


  // public function simpanTreatment_radiografer($data)
  // {
  //   $this->db->insert('medis.tb_treatment_radiografer', $data);
  // }

  public function simpanCt_simulatorFisikaMedis($data)
  {
    $this->db->insert('medis.tb_ctSimulatorFisikaMedis', $data);
    return $this->db->insert_id();
  }

  public function simpanIsocenter_FisikaMedis($data)
  {
    $this->db->insert('medis.tb_isocenter_fisikamedis', $data);
  }

  public function cekisiCtSimulator($nomr='',$nokun='')
  {
    $stquery="SELECT 
    MAX(CASE WHEN source = 'dokter' THEN id END) AS dokter_id,
    MAX(CASE WHEN source = 'radiografer' THEN id END) AS radiografer_id,
    MAX(CASE WHEN source = 'fisikamedis' THEN id END) AS fisikamedis_id
    FROM (
        SELECT tcd.id, 'dokter' AS source
        FROM medis.tb_ctSimulatorDokter tcd
        WHERE tcd.nomr = '$nomr' 
        AND tcd.nokun='$nokun'
        AND tcd.status = 1

        UNION 

        SELECT rad.id, 'radiografer' AS source
        FROM medis.tb_ctSimulatorRadiografer rad
        WHERE rad.nomr = '$nomr' 
        AND rad.nokun='$nokun'
        AND rad.status = 1

        UNION 

        SELECT med.id, 'fisikamedis' AS source
        FROM medis.tb_ctSimulatorFisikaMedis med
        WHERE med.nomr = '$nomr' 
        AND med.nokun='$nokun'
        AND med.status = 1
    ) a";
    $query = $this->db->query($stquery);
    return $query->row_array();
  }

  public function listHistoryCtSimulator($nomr='')
  {
    $stquery="SELECT 
              MAX(CASE WHEN source = 'dokter' THEN id END) AS dokter_id,
              MAX(CASE WHEN source = 'radiografer' THEN id END) AS radiografer_id,
              MAX(CASE WHEN source = 'fisikamedis' THEN id END) AS fisikamedis_id,
              MAX(CASE WHEN source = 'dokter' THEN tanggal END) AS dokter_tanggal,
              MAX(CASE WHEN source = 'radiografer' THEN tanggal END) AS radiografer_tanggal,
              MAX(CASE WHEN source = 'fisikamedis' THEN tanggal END) AS fisikamedis_tanggal,
              MAX(CASE WHEN source = 'dokter' THEN verifdokter END) AS verifdokter,
              MAX(CASE WHEN source = 'dokter' THEN nama_verif END) AS dokter_verif,
              MAX(CASE WHEN source = 'radiografer' THEN nama_verif END) AS radiografer_verif,
              MAX(CASE WHEN source = 'fisikamedis' THEN nama_verif END) AS fisikamedis_verif,
              MAX(CASE WHEN source = 'dokter' THEN tglverif END) AS dokter_tglverif,
              MAX(CASE WHEN source = 'radiografer' THEN tglverif END) AS radiografer_tglverif,
              MAX(CASE WHEN source = 'fisikamedis' THEN tglverif END) AS fisikamedis_tglverif,
              max(nokun) nokun

          FROM (
              SELECT tcd.id, 'dokter' AS source, tcd.nokun,tcd.tanggal,tcd.verifdokter,master.getNamaLengkapPegawai(peng.NIP) nama_verif, tcd.tglverif
              FROM medis.tb_ctSimulatorDokter tcd
              LEFT JOIN aplikasi.pengguna as peng on peng.ID = tcd.olehverif
              WHERE tcd.nomr = '$nomr'
              AND tcd.status = 1

              UNION 

              SELECT rad.id, 'radiografer' AS source, rad.nokun,rad.tanggal, NULL verifdokter, NULL nama_verif, NULL tglverif
              FROM medis.tb_ctSimulatorRadiografer rad
              WHERE rad.nomr = '$nomr'
              AND rad.status = 1

              UNION 

              SELECT med.id, 'fisikamedis' AS source, med.nokun,med.tanggal, NULL verifdokter, NULL nama_verif, NULL tglverif
              FROM medis.tb_ctSimulatorFisikaMedis med
              WHERE med.nomr = '$nomr'
              AND med.status = 1
          ) a";
          // echo "<pre>".$stquery;
    $query = $this->db->query($stquery);
    return $query->row_array();
  }

  public function listHistoryCtSimulatorNew($nomr)
  {
      $this->db->select([
          'pp.NORM', 'pk.NOMOR as nokun',
          'ctd.id as dokter_id', 'master.getNamaLengkapPegawai(dr.NIP) as oleh_dr', 'ctd.tanggal as dokter_tanggal',
          'ctr.id as radiografer_id', 'master.getNamaLengkapPegawai(rad.NIP) as oleh_rad', 'master.getNamaLengkapPegawai(rad2.NIP) as oleh_rad2', 'ctr.tanggal as radiografer_tanggal',
          'ctf.id as fisikamedis_id', 'master.getNamaLengkapPegawai(fis.NIP) as oleh_fis', 'master.getNamaLengkapPegawai(fis2.NIP) as oleh_fis2', 'ctf.tanggal as fisikamedis_tanggal',
          "IF(IFNULL(ctd.verifdokter,0)=1,1,0) as verifdokter", 'master.getNamaLengkapPegawai(drv.NIP) as dokter_verif', 'ctd.tglverif as dokter_tglverif', 'ctr.idCtSimDr', 'ctd.oleh as oleh_id'
      ]);
      
      $this->db->from('pendaftaran.pendaftaran pp');
      $this->db->join('pendaftaran.kunjungan pk', 'pk.NOPEN = pp.NOMOR', 'left');
      $this->db->join('medis.tb_ctSimulatorDokter ctd', 'ctd.nokun = pk.NOMOR AND ctd.status != 0', 'left');
      $this->db->join('medis.tb_ctSimulatorRadiografer ctr', 'ctr.idCtSimDr = ctd.id AND ctr.status != 0', 'left');
      $this->db->join('medis.tb_ctSimulatorFisikaMedis ctf', 'ctf.idCtSimDr = ctd.id AND ctf.status != 0', 'left');
      
      $this->db->join('aplikasi.pengguna dr', 'dr.ID = ctd.oleh', 'left');
      $this->db->join('aplikasi.pengguna drv', 'drv.ID = ctd.olehverif', 'left');
      $this->db->join('aplikasi.pengguna rad', 'rad.ID = ctr.oleh', 'left');
      $this->db->join('aplikasi.pengguna rad2', 'rad2.ID = ctr.oleh2', 'left');
      $this->db->join('aplikasi.pengguna fis', 'fis.ID = ctf.oleh', 'left');
      $this->db->join('aplikasi.pengguna fis2', 'fis2.ID = ctf.oleh2', 'left');
      
      $this->db->where('pp.NORM', $nomr);
      $this->db->group_start();
      $this->db->where('ctd.id IS NOT NULL');
      $this->db->or_where('ctr.id IS NOT NULL');
      $this->db->or_where('ctf.id IS NOT NULL');
      $this->db->group_end();
      
      $this->db->order_by('pp.TANGGAL', 'ASC');
      $this->db->order_by('pk.MASUK', 'ASC');
      
      return $this->db->get();
  }


  public function getCtSimulatorDr($id)
  {
    $query = $this->db->query("SELECT ctDr.`*`, master.getNamaLengkap(pp.NORM)NAMAPASIEN,pp.NOMOR NOPEN,pp.NORM
                              ,master.getNamaLengkapPegawai(ap.NIP)OLEH, pp.TANGGAL TANGGALDAFTAR
                              ,HOUR(TIMEDIFF(NOW(),ctDr.tanggal)) DURASI,IF(IFNULL(ctDr.verifdokter,1)=1,1,0) STATUS_EDIT
                              FROM medis.tb_ctSimulatorDokter ctDr
                              LEFT JOIN pendaftaran.kunjungan pk ON ctDr.nokun = pk.NOMOR
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = ctDr.oleh
                              WHERE 
                              ctDr.id = '$id'
                              AND ctDr.status = 1
                              ");

    return $query->row_array();
  }

  public function getCtSimulatorRad($id)
  {
    $stquery="SELECT ctRad.`*`, master.getNamaLengkap(pp.NORM)NAMAPASIEN,pp.NOMOR NOPEN,pp.NORM
                              ,master.getNamaLengkapPegawai(ap.NIP) OLEH, pp.TANGGAL TANGGALDAFTAR
                              ,HOUR(TIMEDIFF(NOW(),ctRad.tanggal)) DURASI
                              ,GROUP_CONCAT(note.id,'@_@',note.note,'@_@',IFNULL(note.gambar,'') SEPARATOR '#_#') setnote
                              ,IF(IFNULL(ctDr.verifdokter,1)=1,1,0) STATUS_EDIT
                              FROM medis.tb_ctSimulatorRadiografer ctRad
                              LEFT JOIN medis.tb_ctSimulatorSetupNote note ON ctRad.id=note.idradiografi
                              LEFT JOIN medis.tb_ctSimulatorDokter ctDr ON ctRad.idCtSimDr=ctDr.id
                              LEFT JOIN pendaftaran.kunjungan pk ON ctRad.nokun = pk.NOMOR
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = ctRad.oleh
                              
                              WHERE ctRad.id = '$id'
                              AND ctRad.status = 1
                              GROUP BY ctRad.id";
                              // echo '<pre>'.$stquery;exit;
    $query = $this->db->query($stquery);

    return $query->row_array();
  }

  public function getCtSimulatorFis($id)
  {
    $query = $this->db->query("SELECT ctFis.`*`, master.getNamaLengkap(pp.NORM)NAMAPASIEN,pp.NOMOR NOPEN,pp.NORM
                              ,master.getNamaLengkapPegawai(ap.NIP) OLEH, pp.TANGGAL TANGGALDAFTAR
                              ,HOUR(TIMEDIFF(NOW(),ctFis.tanggal)) DURASI
                              ,IF(IFNULL(ctDr.verifdokter,1)=1,1,0) STATUS_EDIT
                              FROM medis.tb_ctSimulatorFisikaMedis ctFis
                              LEFT JOIN medis.tb_ctSimulatorDokter ctDr ON ctFis.idCtSimDr=ctDr.id
                              LEFT JOIN pendaftaran.kunjungan pk ON ctFis.nokun = pk.NOMOR
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = ctFis.oleh
                              WHERE 
                              ctFis.id = '$id'
                              AND ctFis.status = 1
                              ");

    return $query->row_array();
  }

  public function getTreatmentRadiografer($id)
  {
    $query = $this->db->query("SELECT *
                              FROM medis.tb_treatment_radiografer tr
                              WHERE tr.id_ctSimulatorRadiografer = '$id'");

    return $query->result_array();
  }

  public function getIsocenterFisikaMedis($id)
  {
    $query = $this->db->query("SELECT * FROM medis.tb_isocenter_fisikamedis isf
                              WHERE isf.idCtSimulatorFisikaMedis = '$id'");

    return $query->result_array();
  }

  public function getIsianCtDokter($nokun)
  {
    $query = $this->db->query("SELECT
                                csd.* 
                              FROM
                                medis.tb_ctSimulatorDokter csd 
                              WHERE
                                csd.nokun = '$nokun' AND csd.status = 1
                              ORDER BY csd.id DESC");

    return $query;
  }

  public function getIsianCtRadiografer($nokun)
  {
    $query = $this->db->query("SELECT
                                csr.*,
                                peng1.NAMA OLEH_PERTAMA,
	                              peng2.NAMA OLEH_KEDUA
                              FROM
                                medis.tb_ctSimulatorRadiografer csr
                                LEFT JOIN aplikasi.pengguna peng1 ON peng1.ID = csr.oleh AND peng1.STATUS = 1
                                LEFT JOIN aplikasi.pengguna peng2 ON peng2.ID = csr.oleh2 AND peng2.STATUS = 1
                              WHERE
                                csr.nokun = '$nokun' AND csr.status = 1
                              ORDER BY csr.id DESC");

    return $query;
  }

  public function getIsianCtFisikaMedis($nokun)
  {
    $query = $this->db->query("SELECT
                                csf.*,
                                peng1.NAMA OLEH_PERTAMA,
                                peng2.NAMA OLEH_KEDUA
                              FROM
                                medis.tb_ctSimulatorFisikaMedis csf 
                                LEFT JOIN aplikasi.pengguna peng1 ON peng1.ID = csf.oleh AND peng1.STATUS = 1
                                LEFT JOIN aplikasi.pengguna peng2 ON peng2.ID = csf.oleh2 AND peng2.STATUS = 1
                              WHERE
                                csf.nokun = '$nokun' AND csf.status = 1
                              ORDER BY csf.id DESC");
    return $query;
  }

  public function getIsianCtSetupNote($nokun)
  {
    $query = $this->db->query("SELECT
                                cssn.* 
                              FROM
                                medis.tb_ctSimulatorSetupNote cssn 
                                LEFT JOIN medis.tb_ctSimulatorRadiografer csr ON csr.id = cssn.idradiografi
                              WHERE
                                csr.nokun = '$nokun' AND cssn.status = 1
                              ORDER BY cssn.id ASC");
    return $query;
  }

  public function getIsianIsoCenter($nokun)
  {
    $query = $this->db->query("SELECT
                                tif.* 
                              FROM
                                medis.tb_isocenter_fisikamedis tif 
                                LEFT JOIN medis.tb_ctSimulatorFisikaMedis csf ON csf.id = tif.idCtSimulatorFisikaMedis
                              WHERE
                                csf.nokun = '$nokun' AND tif.status = 1
                              ORDER BY tif.id ASC");
    return $query;
  }

  public function updateCt_simulatorDr($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('medis.tb_ctSimulatorDokter', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }
  
  public function verifCt_simulator($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('medis.tb_ctSimulatorDokter', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updateCt_simulatorRadiografer($data,$id)
  {
    // var_dump($data);
    $this->db->where('id', $id);
    if($this->db->update('medis.tb_ctSimulatorRadiografer', $data)){

    }  else {
        // $error = $this->db->error();
      // echo "Error Code: " . $error['code'] . "<br>";
      // echo "Error Message: " . $error['message'];
    }
  }

  // public function updateTreatment_radiografer($data,$idTreatment)
  // {
  //   $this->db->where('id', $idTreatment);
  //   $this->db->update('medis.tb_treatment_radiografer', $data);
  // }

  public function updateCt_simulatorFisikaMedis($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_ctSimulatorFisikaMedis', $data);
  }

  public function updateIsocenter_FisikaMedis($data,$idIso)
  {
    $this->db->where('id', $idIso);
    $this->db->update('medis.tb_isocenter_fisikamedis', $data);
  }

  public function referensi($id)
  { 
      $query = $this->db->query("SELECT *
                              FROM db_master.variabel dv
                              WHERE dv.id_referensi = '$id' AND dv.status='1' ORDER BY dv.seq ASC");

      return $query->result_array();
  }
  public function getCTByNokun($nokun) {
    $this->db->select('ctd.*');
    $this->db->from('medis.tb_ctSimulatorDokter ctd');
    $this->db->where('ctd.nokun', $nokun);
    return $this->db->get()->row(); // Mengambil satu baris hasil
  }

}

/* End of file Ct_simulatorModel.php */
/* Location: ./application/models/radioterapi/Ct_simulatorModel.php */
