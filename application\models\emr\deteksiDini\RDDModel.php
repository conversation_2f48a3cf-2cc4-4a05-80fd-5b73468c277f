<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RDDModel extends MY_Model
{
  protected $_table_name = 'medis.tb_rdd';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public function __construct()
  {
    parent::__construct();
  }

  public function rules()
  {
    return [
      [
        'field' => 'tanggal',
        'label' => 'Tanggal',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
      [
        'field' => 'waktu',
        'label' => 'Waktu',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
    ];
  }

  public function simpan($data)
  {
    $this->db->insert($this->_table_name, $data);
    return $this->db->insert_id();
  }

  public function ubah($id, $data)
  {
    $this->db->where('medis.tb_rdd.id', $id);
    $this->db->update($this->_table_name, $data);
  }

  public function history($nokun, $jenis)
  {
    if (isset($jenis)) {
      if ($jenis == 'jumlah') {
        $this->db->select('r.id');
      } elseif ($jenis == 'tabel') {
        $this->db->select(
          'r.id, r.tanggal, r.waktu, master.getNamaLengkapPegawai(p.NIP) pengisi, r.created_at, r.status'
        );
      }
    }
    $this->db->from('medis.tb_rdd r');
    $this->db->join('aplikasi.pengguna p', 'p.ID = r.oleh', 'left');
    $this->db->where('r.nokun', $nokun);
    if (isset($jenis)) {
      if ($jenis == 'jumlah') {
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($jenis == 'tabel') {
        $this->db->order_by('r.tanggal', 'desc');
        $this->db->order_by('r.waktu', 'desc');
        return $this->db->get();
      }
    }
  }

  public function detail($id)
  {
    $this->db->select(
      "r.id, r.nokun, r.tanggal, r.waktu, r.keluhan, r.penyakit_dahulu, r.penyakit_keluarga, r.risiko_kanker,
      tbb.tb, tbb.bb, tv.td_sistolik, tv.td_diastolik, tv.nadi, tv.pernapasan, tv.suhu, r.leher, r.thorax, r.payudara,
      r.perut, r.ginekologi, r.spekulo, r.vagina, r.fisik_lainnya, r.mammografi, r.usg_payudara, r.pap_smear,
      r.penunjang_lainnya, r.kesimpulan, r.pilihan_anjuran, r.konsultasi_1, r.konsultasi_2, r.anjuran,
      CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('`', rs.id_sitologi, '`')), ']') sitologi,
      CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('`', rh.id_histologi, '`')), ']') histologi,
      CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('`', rr.id_tindakan_medis, '`')), ']') radiologi"
    );
    $this->db->from('medis.tb_rdd r');
    $this->db->join('medis.tb_rdd_sitologi rs', 'rs.id_rdd = r.id AND rs.status = 1', 'left');
    $this->db->join('medis.tb_rdd_histologi rh', 'rh.id_rdd = r.id AND rh.status = 1', 'left');
    $this->db->join('medis.tb_rdd_radiologi rr', 'rr.id_rdd = r.id AND rr.status = 1', 'left');
    $this->db->join('db_pasien.tb_tb_bb tbb', 'tbb.ref = r.id AND tbb.data_source = 42', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'tv.ref = r.id AND tv.data_source = 42', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = r.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->where('r.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function getRiwayatKesehatan($nokun)
  {
    $this->db->select('ukdd.tanggal, ukdd.keluhan, ukdd.riwayat_penyakit,
    ukdd.diabetes_mellitus, ukdd.pen_diabetes_mellitus, ukdd.hipertensi, ukdd.pen_hipertensi,
    ukdd.penyakit_jantung, ukdd.pen_jantung, ukdd.merokok, ukdd.lama_merokok, ukdd.mulai_merokok, ukdd.sampai_merokok,
    ukdd.perokok_pasif, ukdd.karsinogen, ukdd.polusi, ukdd.rumah_sehat, ukdd.suka_alkohol,
    ukdd.riwayat_hepatitis, ukdd.pen_hepatitis, ukdd.riwayat_hepatitis_keluarga, ukdd.pernah_operasi, ukdd.ket_pernah_operasi,
    ukdd.pernah_transfusi, ukdd.penyalahgunaan_obat, ukdd.overweight, ukdd.aktifitas_fisik, ukdd.diet_rendah, ukdd.makan_daging,
    ukdd.makan_diasap, ukdd.polip_usus, ukdd.infeksi_usus, ukdd.bab_berubah, ukdd.lama_bab_berubah, ukdd.bab_berdarah,
    ukdd.riwayat_kanker_keluarga, ukdd.pen_kanker, ukdd.sindroma_metabolik, pay.risk_2 usia50, pay.payudara_text, ukdd.vagina_touch_text');
    $this->db->from('medis.tb_ukdd ukdd');
    $this->db->join('medis.tb_dd_payudara pay', 'pay.nokun = ukdd.nokun', 'left');
    $this->db->where('ukdd.nokun', $nokun);
    $this->db->order_by('ukdd.tanggal, pay.tanggal', 'desc');
    $this->db->limit(1);
    $query = $this->db->get();
    return $query->row();
  }
}

// End of file RDDModel.php
// Location: ./application/models/emr/deteksiDini/RDDModel.php