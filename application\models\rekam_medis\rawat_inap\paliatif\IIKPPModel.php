<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class IIKPPModel extends MY_Model {

  public function listHistoryIIKPP($nomr)
  {
    $query = $this->db->query("SELECT
                                  iikpp.*,
                                  master.getNamaLengkapPegawai (ap.NIP) OLEH,
                                  master.getNamaLengkapPegawai (ap1.NIP) OLEH_UPDATE                                 
                                FROM
                                  medis.tb_instrumen_keperawatan_paliatif iikpp
                                  LEFT JOIN aplikasi.pengguna ap ON ap.ID = iikpp.created_by 
                                  LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = iikpp.updated_by 
                                WHERE
                                  iikpp.STATUS = 1 AND iikpp.norm = $nomr
                              ");
    return $query;
  }

  public function getIIKPP($id)
  {
    $query = $this->db->query("SELECT
                                  iikpp.*
                                FROM
                                  medis.tb_instrumen_keperawatan_paliatif iikpp
                                WHERE iikpp.id = $id AND iikpp.status = 1
                              ");
    return $query->row_array();
  }

//   public function getFammeetFamily($id)
//   {
//     $query = $this->db->query("SELECT
//                                   * 
//                                 FROM
//                                   medis.tb_family_meeting_keluarga fmk
//                                 WHERE fmk.id_fammeet = $id AND fmk.status = 1
//                               ");
//     return $query->result_array();
//   }

//   public function getFammeetAspek($id)
//   {
//     $query = $this->db->query("SELECT
//                                   * 
//                                 FROM
//                                   medis.tb_family_meeting_aspek_medis fmk
//                                 WHERE fmk.id_fammeet = $id AND fmk.status = 1
//                               ");
//     return $query->row_array();
//   }

//   public function cekAspek($id)
//   {
//     $query = $this->db->query("SELECT
//     (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 1 AND fmam.status = 1) ASPEK_MEDIS,
//     (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 2 AND fmam.status = 1) ASPEK_KEPERAWATAN,
//     (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 3 AND fmam.status = 1) ASPEK_PSIKOLOGI,
//     (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 4 AND fmam.status = 1) ASPEK_SOSIAL,
//     (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 5 AND fmam.status = 1) ASPEK_SPIRITUAL
//                           ");
//     return $query->row_array();
//   }

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
?>
