<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MedisDewasaModel extends MY_Model {
  protected $_table_name = 'medis.tb_medis';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor Ku<PERSON>',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>ka.'
      ),
    ),
  );

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('pk.NOMOR NOKUN
                      , rk.ID ID_RUANGAN
                      , rk.DESKRIPSI RUANGAN
                      , pk.MASUK TANGGAL_KUNJUNGAN
                      , kp.id_emr ID_EMR_PERAWAT
                      , kp.created_at TANGGAL_PENGKAJIAN_PERAWAT
                      , master.getNamaLengkapPegawai(peng.NIP) USER_PERAWAT
                      , md.id_emr ID_EMR_MEDIS
                      , md.created_at TANGGAL_PENGKAJIAN_MEDIS
                      , master.getNamaLengkapPegawai(pemed.NIP) USER_MEDIS
                      , p.NOMOR NOPEN
                      , p.NORM
                      , master.getNamaLengkap(p.NORM) NAMA_PASIEN
                      , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
                      , kp.created_by ID_USER_PERAWAT
                      , md.created_by ID_USER_MEDIS
                      , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
                      , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
                      , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18, 2, 1) USIA
                      , kp.jenis JENIS_PENGKAJIAN_PERAWAT
                      , md.jenis JENIS_PENGKAJIAN_MEDIS
                      ,HOUR(TIMEDIFF(NOW(),md.created_at)) DURASI_MEDIS,IF(HOUR(TIMEDIFF(NOW(),md.created_at))<=24,1,0) STATUS_EDIT_MEDIS
                      ,HOUR(TIMEDIFF(NOW(),kp.created_at)) DURASI_PERAWAT,IF(HOUR(TIMEDIFF(NOW(),kp.created_at))<=24,1,0) STATUS_EDIT_PERAWAT');
    $this->db->from('pendaftaran.kunjungan pk');
    $this->db->join('keperawatan.tb_keperawatan kp','pk.NOMOR = kp.nokun AND kp.flag=1 AND kp.`status`=1','LEFT');
    $this->db->join('medis.tb_medis md','pk.NOMOR = md.nokun AND md.flag=1 AND md.`status`=1','LEFT');
    $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
    $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
    $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
    $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
    $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
    $this->db->join('master.pasien pas','pas.NORM = p.NORM','LEFT');
    $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
    $this->db->join('master.ruangan rp','rp.ID = tp.RUANGAN','LEFT');
    $this->db->join('master.referensi refpj','refpj.ID = pj.JENIS AND refpj.JENIS=10','LEFT');
    $this->db->join('aplikasi.pengguna peng','peng.ID = kp.created_by','LEFT');
    $this->db->join('aplikasi.pengguna pemed','pemed.ID = md.created_by','LEFT');

    $this->db->where("(kp.id_emr IS NOT NULL OR md.id_emr IS NOT NULL)");
    $this->db->where('p.NORM',$this->input->post('nomr'));
    $this->db->group_by('pk.NOMOR');
    $this->db->order_by('pk.MASUK ', 'DESC');
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }


}
