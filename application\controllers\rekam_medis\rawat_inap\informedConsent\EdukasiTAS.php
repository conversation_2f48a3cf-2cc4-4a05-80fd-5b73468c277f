<?php
defined('BASEPATH') or exit('No direct script access allowed');
class EdukasiTAS extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/informedConsent/EdukasiTASModel'
    ));
  }

  public function index(){
    // $norm = $this->uri->segment(6);
    // $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'listEdukasiTAS' => $this->masterModel->referensi(1612),
      // 'hubunganSaksiDNR' => $this->masterModel->referensi(1610),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );
     // print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/informedConsent/EdukasiTAS/index', $data);
  }

  public function simpanEdukasiTAS()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $date = $this->input->post('datePickerEdukasiTAS');
    $tglEdukasiTAS = date('Y-m-d h:i', strtotime($date));

    $dataInformedConcent = array (
      'nokun'                  => $post['nokun'],
      'jenis_informed_consent' => 3032,
      'dokter_pelaksana'       => $post['dokterEdukasiTAS'],
      'penerima_informasi'     => "-",
      'pemberi_informasi'      => "-",
      'oleh'                   => $this->session->userdata('id'),
    );
    // echo "<pre>";print_r($dataInformedConcent);echo "</pre>";

    $idInformedConcent = $this->EdukasiTASModel->simpanInformedConcent($dataInformedConcent);

    $dataEdukasiTAS = array(
      // 'id_informed_consent'                    => 1,
      'id_informed_consent'                    => $idInformedConcent,
      'edukasi_tas'                            => implode(',',$post["EdukasiTAS"]),
      'nama_pihak'                             => $post['namaEdukasiTAS'],
      'umur_pihak'                             => $post['umurEdukasiTAS'],
      'jenis_kelamin_pihak'                    => $post['jenis_kelaminEdukasiTAS'],
      'alamat_pihak'                           => $post['alamatEdukasiTAS'],
      'no_telp_pihak'                          => $post['telpEdukasiTAS'],
      'tgl_edukasi'                            => $tglEdukasiTAS,
      'nama_ttd_pihak'                         => $post['nama_ttd_pihak'],
      'ttd_dokter_edukasi'                     => file_get_contents($this->input->post('sign_Dokter_EdukasiTAS')),
      'ttd_pihak_edukasi'                      => file_get_contents($this->input->post('sign_PihakEdukasiTAS')),
      'status_edukasi'                         => 1
    );
    // echo "<pre>";print_r($dataEdukasiTAS);echo "</pre>";exit();

    $this->db->insert('db_informed_consent.tb_edukasi_anestesi_sedasi',$dataEdukasiTAS);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyEdukasiTAS()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    // $nomr = $this->uri->segment(6);
    $listEdukasiTAS = $this->EdukasiTASModel->listHistoryInformedConsentEdukasiTAS($nomr);

    $data = array();
    $no = 1;
    foreach ($listEdukasiTAS->result() as $EdukasiTAS) {
      $stat = $EdukasiTAS->status_edukasi;
      if($EdukasiTAS->status_edukasi == 1){
        $status = 'Aktif';
        $button = '<a href="#modalEdukasiTAS" class="btn btn-primary btn-block" data-id="'.$EdukasiTAS->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fas fa-edit"></i>Non-Aktif</a>';
      }else{
        $status = 'Non-Aktif';
        $button = '';
      }
      $data[] = array(
        $no,
        $EdukasiTAS->nokun,
        $EdukasiTAS->DOKTERPELAKSANA,
        $EdukasiTAS->OLEH,
        date("d-m-Y H:i:s",strtotime($EdukasiTAS->tanggal)),
        $status,
        $button,
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listEdukasiTAS->num_rows(),
      "recordsFiltered" => $listEdukasiTAS->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalEdukasiTAS()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    $gpEdukasiTAS = $this->EdukasiTASModel->getEdukasiTAS($id);
    $nokun = $gpEdukasiTAS['nokun'];
    // $saksi = $gpDNR['saksi_rumah_sakit'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $explode_edukasi       = explode(',' , $gpEdukasiTAS['edukasi_tas']);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'explode_edukasi'       => $explode_edukasi,
      'gpEdukasiTAS' => $gpEdukasiTAS,
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'listEdukasiTAS_edit' => $this->masterModel->referensi(1612),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );

    $this->load->view('rekam_medis/rawat_inap/informedConsent/EdukasiTAS/view_edit', $data);
  }

  public function updateEdukasiTAS()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    $idEdukasiTAS = $this->input->post('idEdukasiTAS');
    $post = $this->input->post();

    $dataEdukasiTAS_edit = array (
      'status_edukasi'                => 0
    );

     // echo "<pre>";print_r($dataEdukasiTAS_edit);echo "</pre>";exit();

    $this->EdukasiTASModel->updateEdukasiTAS($dataEdukasiTAS_edit,$idEdukasiTAS);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  }
  ?>