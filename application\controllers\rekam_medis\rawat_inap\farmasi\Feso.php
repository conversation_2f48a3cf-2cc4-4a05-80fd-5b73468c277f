<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Feso extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/farmasi/FesoModel'
    ));
  }

  public function index(){
  	// $norm = $this->uri->segment(6);
   //  $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      'nokun' => $nokun,
      // 'listObat' => $this->FesoModel->listObat(),
      'getNomr' => $getNomr,
    );
     // print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/farmasi/feso/index', $data);
  }

  function getObatDetil(){
		$id = $this->input->post('id',TRUE);
    $nopen = $this->input->post('nopen',TRUE);
		$data = $this->FesoModel->listObatPasienDetail($nopen,$id)->row_array();
		echo json_encode($data);
	}

    function simpanFeso()
  	{
      $this->db->trans_begin();

      $post = $this->input->post();

      if($post['par'] == 'eso'){
        $dataESO = array (
          'norm'     					    => $post['norm'],
          'nopen'     		        => $post['nopen'],
          'nokun'  					      => $post['nokun'],
          'kemoterapi'					  => $post['kemoterapi'],
          'protokol_kemoterapi'		=> $post['protokolFeso'],
          'riwayat_covid'     		=> $post['covid'],
          'kondisi_penyerta'     	=> $post['kondisiFeso'],
          'kondisi_lainnya'     	=> $post['kondisiLainnya'],
          'pelapor'  					    => $post['namaPelapor'],
          'profesi_pelapor'				=> $post['profesiPelapor'],
          'oleh'							    => $this->session->userdata('id'),
          );
          // echo "<pre>";print_r($dataESO);echo "</pre>";
    
          $this->FesoModel->simpanFeso($dataESO);
      }elseif($post['par'] == 'esoObat'){
        $dataESOObat = array (
          // 'norm'     					    => $post['norm'],
          // 'nopen'     		        => $post['nopen'],
          // 'nokun'  					      => $post['nokun'],
          // 'kemoterapi'					  => $post['kemoterapi'],
          // 'protokol_kemoterapi'		=> $post['protokolFeso'],
          // 'riwayat_covid'     		=> $post['covid'],
          // 'kondisi_penyerta'     	=> $post['kondisiFeso'],
          // 'kondisi_lainnya'     	=> $post['kondisiLainnya'],
          'id_eso'  					    => $post['idEso'],
          'jenis'  					      => $post['jenisObatKenaEfekSamping'],
          'obat'  					      => isset($post['obatKenaEfekSamping']) ? $post['obatKenaEfekSamping'] : '0',
          'obat_luar'  					  => $post['obatKenaEfekSampingLuar'],
          'dosis'					        => $post['dosisObatSamping'],
          'aturan_pakai'					=> $post['aturanObatSamping'],
          'no_batch'     					=> $post['batchObatSamping'],
          'tanggal_mulai'     		=> $post['mulaiObatSamping'],
          'tanggal_stop'  				=> $post['stopObatSamping'],
          'indikasi'					    => $post['indikasiObatSamping'],
          // 'dicurigai'							=> $post[''],
          // 'pelapor'  					    => $post['namaPelapor'],
          // 'profesi_pelapor'				=> $post['profesiPelapor'],
          'oleh'							    => $this->session->userdata('id'),
          );
          // echo "<pre>";print_r($dataESOObat);echo "</pre>";
    
          $this->FesoModel->simpanFesoObat($dataESOObat);
      }elseif($post['par'] == 'esoForm'){

        $dataUpdateESOObat = array (
          'dicurigai'		=> 1
        );
        $this->db->where('id', $post['idObatEso']);
        $this->db->update('db_layanan.tb_eso_obat', $dataUpdateESOObat);

        $dataESOForm = array (
          'id_eso_obat'  					=> $post['idObatEso'],
          'efek_samping'  			  => implode(',',$post["terjadiEfekSamping"]),
          'manifestasi'					  => $post['manifesESO'],
          'tanggal_kejadian'			=> $post['tanggalESO'],
          'kecepatan_timbul'     	=> $post['iamSpeed'],
          'data_lab'              => $post['dataLabESO'],
          'tindakan_dilakukan'  	=> $post['tindakanESO'],
          'kesudahan'					    => $post['sudahESO'],
          'obat_sebelumnya'				=> $post['pernahESO'],
          'deskripsi_obat_sebelumnya'				=> $post['desPernahDptObat'],
          'reaksi'					      => $post['adaReaksiESO'],
          'deskripsi_reaksi'					      => $post['desReaksiDptObat'],
          'oleh'							    => $this->session->userdata('id'),
          );
          // echo "<pre>";print_r($dataESOForm);echo "</pre>";
    
          $this->FesoModel->simpanFesoForm($dataESOForm);
          $idForm = $this->db->insert_id();

          $dataCeklist = array();
          $tes = $post['terjadiEfekSamping'];
      
          $index = 0; // Set index array awal dengan 0
          foreach($tes as $datates){ // Kita buat perulangan berdasarkan nis sampai data terakhir
            array_push($dataCeklist, array(
                  'id_eso_form'            => $idForm,
                  'terjadi_eso'            => $tes[$index],
                  'oleh'                   => $this->session->userdata('id'),
              ));
              
              $index++;
          }
      
          // echo "<pre>";print_r($dataCeklist);echo "</pre>";
          $this->FesoModel->batch_form($dataCeklist);

      }elseif($post['par'] == 'esoFormEdit'){

        $dataESOFormEdit = array (
          'efek_samping'  			  => implode(',',$post["terjadiEfekSampingEdit"]),
          'manifestasi'					  => $post['manifesESOEdit'],
          'tanggal_kejadian'			=> $post['tanggalESOEdit'],
          'kecepatan_timbul'     	=> $post['iamSpeedEdit'],
          'data_lab'              => $post['dataLabESOEdit'],
          'tindakan_dilakukan'  	=> $post['tindakanESOEdit'],
          'kesudahan'					    => $post['sudahESOEdit'],
          'obat_sebelumnya'				=> $post['pernahESOEdit'],
          'deskripsi_obat_sebelumnya'				=> $post['desPernahDptObatEdit'],
          'reaksi'					      => $post['adaReaksiESOEdit'],
          'deskripsi_reaksi'					      => $post['desReaksiDptObatEdit'],
          'oleh'							    => $this->session->userdata('id'),
          );
          // echo "<pre>";print_r($dataESOFormEdit);echo "</pre>";

          $this->db->where('id', $post['idFormEsoEdit']);
          $this->db->update('db_layanan.tb_eso_form', $dataESOFormEdit);
          //--------------------------------------------------------------
          $dataCeklistEdit = array();
          $tesEdit = $post['terjadiEfekSampingEdit'];
      
          $index = 0; // Set index array awal dengan 0
          foreach($tesEdit as $datatesedit){ // Kita buat perulangan berdasarkan nis sampai data terakhir
            $this->FesoModel->batch_delete_form($post['idFormEsoEdit']);
            array_push($dataCeklistEdit, array(
                  'id_eso_form'            => $post['idFormEsoEdit'],
                  'terjadi_eso'            => $tesEdit[$index],
                  'oleh'                   => $this->session->userdata('id'),
              ));
              
              $index++;
          }
      
          // echo "<pre>";print_r($dataCeklistEdit);echo "</pre>";
          $this->FesoModel->batch_form($dataCeklistEdit);

      }elseif($post['par'] == 'esoObatNonaktif'){
        $dataESOObatNonaktif = array (
          'status'  			  => '0',
          );
          // echo "<pre>";print_r($dataESOObatNonaktif);echo "</pre>";

          $this->db->where('id', $post['ID']);
          $this->db->update('db_layanan.tb_eso_obat', $dataESOObatNonaktif);
      }
      
     
      if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
      //   $this->session->set_flashdata('error', "Gagal Simpan");
      } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
      //   $this->session->set_flashdata('success', "Berhasil Simpan"); 
      }
    	echo json_encode($result);
  	}

  function getObat()
  {
    $result = $this->FesoModel->listObatPasien()->result_array();
    // $data = array();
    $data = '';
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['ID_OBAT'];
        $sub_array['text'] = $row['NAMA_OBAT']." @(".$row['TANGGAL'].")";
        $data .= '<option value="'.$sub_array['id'].'">'.$sub_array['text'].'</option>';
        // $data[] = $sub_array;
    }
    // $output = array(
    //     "item" -> $data
    // );
    echo ($data);
  }

  function getKondisi()
  {
    $result = $this->FesoModel->listKondisi()->result_array();
    $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['id'];
        $sub_array['text'] = $row['deskripsi'];
        $data[] = $sub_array;
    }
    // $output = array(
    //     "item" -> $data
    // );
    echo json_encode($data);
  }

  function getProfesi()
  {
    $result = $this->FesoModel->listProfesi()->result_array();
    $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['id'];
        $sub_array['text'] = $row['deskripsi'];
        $data[] = $sub_array;
    }
    // $output = array(
    //     "item" -> $data
    // );
    echo json_encode($data);
  }

  function getEfekSamping()
  {
    $id = $this->input->post('id');
    if(isset($id)){
      // $id = $this->input->post('id');
      $getDataEfekSamping = $this->FesoModel->getDataEfekSamping($id);
      $result = array(
        'id_efek' => $getDataEfekSamping['id'], 
        'nama_efek' => $getDataEfekSamping['deskripsi']
      );
      echo json_encode($result);
      
    }else{
      $result = $this->FesoModel->listEfekSamping()->result_array();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['id'];
          $sub_array['text'] = $row['deskripsi'];
          $data[] = $sub_array;
      }
      echo json_encode($data);
    }
    
    // $output = array(
    //     "item" -> $data
    // );
  }

  function getKecepatan()
  {
    $result = $this->FesoModel->listKecepatan()->result_array();
    $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['id'];
        $sub_array['text'] = $row['deskripsi'];
        $data[] = $sub_array;
    }
    // $output = array(
    //     "item" -> $data
    // );
    echo json_encode($data);
  }

  function getKesudahan()
  {
    $result = $this->FesoModel->listKesudahan()->result_array();
    $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['id'];
        $sub_array['text'] = $row['deskripsi'];
        $data[] = $sub_array;
    }
    // $output = array(
    //     "item" -> $data
    // );
    echo json_encode($data);
  }

  public function modalFeso()
  {
    $id = $this->input->post('id');
    $getEsoObat = $this->FesoModel->getEsoObat($id);
    // $nokun = $this->uri->segment(8);
    // $nokun = $gpDNR['nokun'];
    // $saksi = $gpDNR['saksi_rumah_sakit'];
    // $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      // 'nokun' => $nokun,
      'getEsoObat' => $getEsoObat,
    );

    $this->load->view('rekam_medis/rawat_inap/farmasi/feso/modal_feso', $data);
  }

  public function modalFesoDetil()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    // $nokun = $gpDNR['nokun'];
    // $saksi = $gpDNR['saksi_rumah_sakit'];
    $getEsoForm = $this->FesoModel->getEsoForm($id);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      // 'nokun' => $nokun,
      'getEsoForm' => $getEsoForm,
    );

    $this->load->view('rekam_medis/rawat_inap/farmasi/feso/modal_feso_detil', $data);
  }

  public function modalFesoEdit()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    // $nokun = $gpDNR['nokun'];
    // $saksi = $gpDNR['saksi_rumah_sakit'];
    $getEsoForm = $this->FesoModel->getEsoForm($id);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      // 'nokun' => $nokun,
      'getEsoForm' => $getEsoForm,
    );

    $this->load->view('rekam_medis/rawat_inap/farmasi/feso/modal_feso_edit', $data);
  }

  function get_data_eso(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
    $norm = $this->input->post("norm");
		$listEso = $this->FesoModel->datatablesEso($norm);
	
		$data = array();
		$no = $_POST['start'];
		foreach ($listEso as $LE) {
		$no++;

		// $button = '<a class="btn btn-danger nonaktifBarang" href="javascript:;" data="'.$LE->ID_BARANG.'"><i class="fas fa-times"></i> Non Aktif</a>';

    $button = '<button dataid="'.$LE->id.'" class="btn btn-primary waves-effect waves-light btnEsoObat" type="button" data-toggle="collapse" id="btnEsoObat"
    data-target="#collapseExample1" aria-expanded="false"
    aria-controls="collapseExample1"><i class="fa fa-plus" id="iconEsoObat"></i> Tambah/Lihat Obat
    </button>';

		  $data[] = array(
      $no,
			$LE->nokun,
			$LE->oleh_pengisi,
			$LE->created_at,
			$button,
			
		  );
		  
		}
	
		$output = array(
		  "draw"            => $draw,
		  "recordsTotal"    => $this->FesoModel->total_count_eso($norm),
		  "recordsFiltered" => $this->FesoModel->filter_count_eso($norm),
		  "data"            => $data
		);
		echo json_encode($output);
	}

  function get_data_eso_obat(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
    $id = $this->input->post("id");
		$listEsoObat = $this->FesoModel->datatablesEsoObat($id);
	
		$data = array();
		$no = $_POST['start'];
		foreach ($listEsoObat as $LEO) {
		$no++;

    // $button = '<a href="#modalCurigaiObatEdit" data-id="'.$LEO->id_eso_form.'" class="btn btn-warning btn-block modalCurigaiObatEdit" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-target="#modalCurigaiObatEdit">Edit</a>';

    if($LEO->dicurigai == 1){
      // $button = '<a href="#modalCurigaiObatEdit" data-id="'.$LEO->id_eso_form.'" class="btn btn-primary btn-block modalCurigaiObatEdit" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-target="#modalCurigaiObatEdit">Edit</a><a href="#modalCurigaiObatDetil" data-id="'.$LEO->id_eso_form.'" class="btn btn-warning btn-block modalCurigaiObatDetil" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-target="#modalCurigaiObatDetil">Detail</a><a href="#" data="'.$LEO->id.'" class="btn btn-danger btn-block nonaktifObat">Non-Aktif</a>';
      $button = '<a href="#modalCurigaiObat" data-id="'.$LEO->id_eso_form.'" data-kd="edit" class="btn btn-primary btn-block modalCurigaiObat" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-target="#modalCurigaiObat">Edit</a><a href="#modalCurigaiObat" data-id="'.$LEO->id_eso_form.'"  data-kd="detil" class="btn btn-warning btn-block modalCurigaiObat" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-target="#modalCurigaiObat">Detail</a><a href="#" data="'.$LEO->id.'" class="btn btn-danger btn-block nonaktifObat">Non-Aktif</a>';
    }else{
      $button = '<a href="#modalCurigaiObat" data-id="'.$LEO->id.'" data-kd="add" class="btn btn-primary btn-block modalCurigaiObat" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-target="#modalCurigaiObat">Dicurigai</a>
      <a href="#" data="'.$LEO->id.'" class="btn btn-danger btn-block nonaktifObat">Non-Aktif</a>';
    }

		  $data[] = array(
      // $LEO->nama_obat,
      ($LEO->jenis == 1) ? $LEO->nama_obat : $LEO->obat_luar,
      ($LEO->jenis == 1) ? $LEO->nama_generik : '-',
      ($LEO->jenis == 1) ? $LEO->nama_sediaan : '-',
			$LEO->dosis,
			$LEO->aturan_pakai,
      $LEO->no_batch,
      $LEO->tanggal_mulai,
      $LEO->tanggal_stop,
      $LEO->indikasi,
			$button,
			
		  );
		  
		}
	
		$output = array(
		  "draw"            => $draw,
		  "recordsTotal"    => $this->FesoModel->total_count_esoObat($id),
		  "recordsFiltered" => $this->FesoModel->filter_count_esoObat($id),
		  "data"            => $data
		);
		echo json_encode($output);
	}


}
?>