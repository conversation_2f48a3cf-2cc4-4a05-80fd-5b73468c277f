<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PersetujuanTindakanTerapeutikAferesis extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','bankdarah/PersetujuanTindakanTerapeutikAferesisModel'));
    }

    public function index(){
        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(5)),
            'listDrUmum' => $this->masterModel->listDrUmum(),
            'dasar_diagnosis_terapeutik' => $this->masterModel->referensi(942),
            'tindakan_kedokteran_terapeutik' => $this->masterModel->referensi(943),
            'indikasi_tindakan_terapeutik' => $this->masterModel->referensi(944),
            'tata_cara_terapeutik' => $this->masterModel->referensi(945),
            'tujuan_tindakan_terapeutik' => $this->masterModel->referensi(946),
            'tujuan_pengobatan_terapeutik' => $this->masterModel->referensi(947),
            'resiko_terapeutik' => $this->masterModel->referensi(948),
            'komplikasi_terapeutik' => $this->masterModel->referensi(949),
            'prognosis_terapeutik' => $this->masterModel->referensi(950),
            'alternatif_terapeutik' => $this->masterModel->referensi(951),
            'resiko_2_terapeutik' => $this->masterModel->referensi(952),
            'jenis_kelamin' => $this->masterModel->referensi(965),
        );

        $this->load->view('Pengkajian/bankdarah/persetujuanTindakanTerapeutikAferesis',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->PersetujuanTindakanTerapeutikAferesisModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataInformedConsent = array(
                        'nokun' => $post['nokun'],
                        'jenis_informed_consent' => '3028',
                        'dokter_pelaksana' => $post['dokter_pelaksana_tindakan'],
                        'pemberi_informasi' => $post['pemberi_informasi'],
                        'penerima_informasi' => $post['penerima_informasi'],
                        'oleh' => $this->session->userdata("id"),
                    );
                    $this->db->insert('db_informed_consent.tb_informed_consent',$dataInformedConsent);

                    $idInformedConsent = $this->db->insert_id();

                    $dataTerapeutikAferesis = array(
                        'id_informed_consent' => $idInformedConsent,
                        'diagnosis_kerja' => $post['diagnosa_kerja'],
                        'dasar_diagnosis' => isset($post['dasar_diagnosis_terapeutik']) ? implode(",",$post['dasar_diagnosis_terapeutik']) : NULL,
                        'tindakan_kedokteran' => isset($post['tindakan_kedokteran_terapeutik']) ? implode(",",$post['tindakan_kedokteran_terapeutik']) : NULL,
                        'indikasi_tindakan' => isset($post['indikasi_tindakan_terapeutik']) ? implode(",",$post['indikasi_tindakan_terapeutik']) : NULL,
                        'indikasi_tindakan_lain' => $post['indikasi_tindakan_lain'],
                        'tata_cara' => isset($post['tata_cara_terapeutik']) ? implode(",",$post['tata_cara_terapeutik']) : NULL,
                        'tujuan_tindakan' => isset($post['tujuan_tindakan_terapeutik']) ? implode(",",$post['tujuan_tindakan_terapeutik']) : NULL,
                        'tujuan_pengobatan' => isset($post['tujuan_pengobatan_terapeutik']) ? implode(",",$post['tujuan_pengobatan_terapeutik']) : NULL,
                        'resiko' => isset($post['resiko_terapeutik']) ? implode(",",$post['resiko_terapeutik']) : NULL,
                        'komplikasi' => isset($post['komplikasi_terapeutik']) ? implode(",",$post['komplikasi_terapeutik']) : NULL,
                        'prognosis' => isset($post['prognosis_terapeutik']) ? implode(",",$post['prognosis_terapeutik']) : NULL,
                        'prognosis_lain' => $post['prognosis_lain'],
                        'alternatif' => isset($post['alternatif_terapeutik']) ? implode(",",$post['alternatif_terapeutik']) : NULL,
                        'resiko2' => isset($post['resiko_2_terapeutik']) ? implode(",",$post['resiko_2_terapeutik']) : NULL,
                        'resiko2_lain' => $post['resiko2_lain'],
                        'lain_lain' => $post['lain_lain'],
                    );

                    $dataPersetujuanTidakanKedokteran = array(
                        'id_informed_consent' => $idInformedConsent,
                        'nama_keluarga' => $post['nama'],
                        'umur_keluarga' => $post['umur'],
                        'jk_keluarga' => isset($post['jenis_kelamin']) ? $post['jenis_kelamin'] : null,
                        'alamat_keluarga' => $post['alamat'],
                        'tindakan' => $post['tindakan'],
                        'hub_keluarga_dgn_pasien' => $post['hubungan'],                        'tanggal_persetujuan' => $post['tanggal_persetujuan_terapeutik'].' '.$post['jam_persetujuan_terapeutik'],
                        'ttd_menyatakan' => file_get_contents($this->input->post('signMenyatakan')),
                        'ttd_saksi_keluarga' => file_get_contents($this->input->post('signKeluarga')),
                        'ttd_saksi_rumah_sakit' => file_get_contents($this->input->post('signRumahSakit')),
                        'saksi_keluarga' => $post['nama_keluarga'],
                        'saksi_rumah_sakit' => $post['nama_saksi_rs']
                    );

                    $this->db->insert('db_informed_consent.tb_terapeutik_aferesis',$dataTerapeutikAferesis);
                    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran',$dataPersetujuanTidakanKedokteran);

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambilAferesis'){
    			$post = $this->input->post(NULL,TRUE);
                $dataTerapeutikAferesis = $this->PersetujuanTindakanTerapeutikAferesisModel->get($post['nokun'], true);
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataTerapeutikAferesis
                ));
            }else if($param == 'count'){
                $result = $this->PersetujuanTindakanTerapeutikAferesisModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->PersetujuanTindakanTerapeutikAferesisModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm cetak_persetujuan_aferesis" data-id="'.$row -> ID.'"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PersetujuanTindakanTerapeutikAferesisModel->total_count(),
            "recordsFiltered"   => $this->PersetujuanTindakanTerapeutikAferesisModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}