<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ResikoJatuhLanjutUsia extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','geriatri/ResikoJatuhLanjutUsia_Model'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'getNomr' => $getNomr,
      'nokun' => $nokun,
    );
    $this->load->view('Pengkajian/geriatri/resikoJatuhLanjutUsia/index', $data);
  }

  public function historyResikoJatuhLanjutUsia()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listResikoJatuhLanjutUsia = $this->ResikoJatuhLanjutUsia_Model->listResikoJatuhLanjutUsia($nomr);

    $data = array();
    $no = 1;
    foreach ($listResikoJatuhLanjutUsia->result() as $lrjlu) {
      $data[] = array(
        $no,
        $lrjlu->nokun,
        $lrjlu->jumlah_skala,
        $lrjlu->created_at,
        '<a href="#editModalRisikoJatuhLanjutUsia" class="btn btn-custom btn-block" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$lrjlu->id.'"><i class="fas fa-edit"></i> Edit </a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listResikoJatuhLanjutUsia->num_rows(),
      "recordsFiltered" => $listResikoJatuhLanjutUsia->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function simpanFResikoJatuhLanjutUsia()
  {
    $data= array(
      'nokun'              => $this->input->post('nokun'),
      'skala_1'             => $this->input->post('risiko1'),
      'skala_2'             => $this->input->post('risiko2'),
      'skala_3'             => $this->input->post('risiko3'),
      'skala_4'             => $this->input->post('risiko4'),
      'skala_5'             => $this->input->post('risiko5'),
      'skala_6'             => $this->input->post('risiko6'),
      'skala_7'             => $this->input->post('risiko7'),
      'skala_8'             => $this->input->post('risiko8'),
      'skala_9'             => $this->input->post('risiko9'),
      'skala_10'            => $this->input->post('risiko10'),
      'skala_11'            => $this->input->post('risiko11'),
      'jumlah_skala'        => $this->input->post('risikototal'),
      'oleh'                => $this->session->userdata('id'),
      'status'              => 1,
    );
    //  echo "<pre>";print_r($data);exit();
    $this->ResikoJatuhLanjutUsia_Model->simpanFRisikoJatuhLanjutUsia($data);
  }

  public function modalRisikoJatuh()
  {
    $id = $this->input->post('id');
    $dataResikoJatuhLanjutUsia = $this->ResikoJatuhLanjutUsia_Model->getResikoJatuhLanjutUsia($id);
    $nokun = $dataResikoJatuhLanjutUsia['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'id'                 => $id,
      'dataResikoJatuhLanjutUsia'   => $dataResikoJatuhLanjutUsia,
      // 'dominisasiHemisfer' => $this->masterModel->referensi(940),
      // 'kesadaranResponden' => $this->masterModel->referensi(941),
      'getNomr' => $getNomr,
      'nokun' => $nokun,
    );

    $this->load->view('Pengkajian/geriatri/resikoJatuhLanjutUsia/editResikoJatuh', $data);
  }

  public function editFResikoJatuhLanjutUsia()
  {
    $id = $this->input->post('id');

    $data= array(
      'skala_1'             => $this->input->post('risiko1_edit'),
      'skala_2'             => $this->input->post('risiko2_edit'),
      'skala_3'             => $this->input->post('risiko3_edit'),
      'skala_4'             => $this->input->post('risiko4_edit'),
      'skala_5'             => $this->input->post('risiko5_edit'),
      'skala_6'             => $this->input->post('risiko6_edit'),
      'skala_7'             => $this->input->post('risiko7_edit'),
      'skala_8'             => $this->input->post('risiko8_edit'),
      'skala_9'             => $this->input->post('risiko9_edit'),
      'skala_10'            => $this->input->post('risiko10_edit'),
      'skala_11'            => $this->input->post('risiko11_edit'),
      'jumlah_skala'        => $this->input->post('risikototal_edit'),
      'oleh'                => $this->session->userdata('id'),
    );
    // echo "<pre>";print_r($data);exit();
    $this->ResikoJatuhLanjutUsia_Model->updateFResikoJatuhLanjutUsia($data, $id);
  }
}

/* End of file ResikoJatuhLanjutUsia.php */
/* Location: ./application/controllers/geriatri/ResikoJatuhLanjutUsia.php */
