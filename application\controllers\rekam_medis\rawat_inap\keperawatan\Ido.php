<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Ido extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'MasterModel',
        'rekam_medis/rawat_inap/keperawatan/Idomodel'
      ]
    );
    // $this->load->model('pengkajianAwalModel');
  }

  public function pre($nokun = null)
  {
    $data['nokun'] = $nokun;
    $data['mdokter'] = $this->Idomodel->mdokter();
    $data['mpegmedis'] = $this->Idomodel->mpegmedis();
    $data['mperawat'] = $this->MasterModel->listPerawatPenataAnestesi();
    $data['mperawatanastesi'] = $this->Idomodel->mperawatanastesi();
    $pasien = $this->Idomodel->getpasien($nokun);
    $data['dt'] = $pasien;
    // echo '<pre>';print_r($data);exit();

    $view['idoisi'] = $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/form_survido_isi', $data, true);
    $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/form_ido', $view);

  }

  // public function gepasien($nomr=null)
  // {

  //     $data= $this->Idomodel->getpasien($nomr);
  //     $row = array();
  //     if($data){
  //       $row = array(
  //         'norm' 				=> $data['NORM'],
  //         'nama' 				=> $data['NAMA'],
  //         'jk'   				=> $data['JENIS_KELAMIN'],
  //         'kode_jenis_kelamin'=> $data['KODE_JENIS_KELAMIN'],
  //         'id_jenis_kelamin' 	=> $data['ID_JK'],
  //         'ruangan'			=> $data['RUANGAN'],
  //         'id_ruangan'		=> $data['ID_RUANGAN'],
  //         'penjamin'			=> $data['PENJAMIN'],
  //         'id_penjamin'       => $data['ID_PENJAMIN'],
  //         'penanggung'        => $data['PENANGGUNG'],
  //         'nama_penanggung'   => $data['NAMA_PENANGGUNG'],
  //         'umur'          	=> $data['UMUR'],
  //         'umur_t'          	=> $data['UMUR_T'],
  //         'tgl_masuk'         => $data['TGL_MASUK'],
  //         'tgl_lahir'         => $data['TANGGAL_LAHIR'],
  //         'ktp'         		=> $data['KTP'],
  //         'warganegara'       => $data['KEWARGANEGARAAN'],
  //         'pekerjaan'         => $data['PEKERJAAN'],
  //         'telpon'         	=> $data['TELPON'],
  //         'alamat'         	=> $data['ALAMAT'],
  //         'nopen'         	=> $data['NOPEN'],
  //         'icd'         		=> $data['ICD'],
  //         'diagnosa'         	=> $data['DIAGNOSA'],
  //         'diagmasuk'         => $data['DIAGMASUK'],
  //         'tglusia'         	=> $data['TANGGAL_LAHIR'].' / '.$data['UMUR'].'Thn',
  //         'telpkel'         	=> $data['TELPKEL'],
  //         'namakel'         	=> $data['NAMAKEL'],
  //         'postid'         	=> $data['POSTID'],
  //       );
  //     }
  //     echo json_encode($row);
  // }

  public function pospreido($no = null)
  {
    // echo '<pre>';print_r($_POST);exit();
    $iduser = $this->session->userdata("id");
    if ($iduser && isset($_POST['btnsimpan']) && isset($_POST['nomr']) && $_POST['nomr']) {
      $param['id'] = isset($_POST['ID']) ? $_POST['ID'] : null;
      $param['nomr'] = isset($_POST['nomr']) ? $_POST['nomr'] : null;
      $param['nokun'] = isset($_POST['nokun']) ? $_POST['nokun'] : null;
      $param['nama'] = isset($_POST['nama']) ? $_POST['nama'] : null;
      $param['nopen'] = isset($_POST['nopen']) ? $_POST['nopen'] : null;
      $param['tglmasukruangan'] = isset($_POST['tglmasukruangan']) ? $_POST['tglmasukruangan'] : null;
      $param['asalruangan'] = isset($_POST['asalruangan']) ? $_POST['asalruangan'] : null;
      $param['dxmedis'] = isset($_POST['dxmedis']) ? $_POST['dxmedis'] : null;
      $param['tglsurveilan'] = isset($_POST['tglsurveilan']) ? $_POST['tglsurveilan'] : null;
      $param['tb'] = isset($_POST['tb']) ? $_POST['tb'] : null;
      $param['bb'] = isset($_POST['bb']) ? $_POST['bb'] : null;
      $param['tindakan'] = isset($_POST['tindakan']) ? $_POST['tindakan'] : null;
      $param['tgl_rencana'] = isset($_POST['tgl_rencana']) ? $_POST['tgl_rencana'] : null;
      $param['dokteroperator'] = "";
      if (isset($_POST['dokteroperator'])) {
        $param['dokteroperator'] = implode(',', $_POST['dokteroperator']);
      }
      $param['suhu'] = isset($_POST['suhu']) ? $_POST['suhu'] : null;
      $param['lab_tanggal'] = isset($_POST['lab_tanggal']) ? $_POST['lab_tanggal'] : null;
      $param['glukosa'] = isset($_POST['glukosa']) ? $_POST['glukosa'] : null;
      $param['hb'] = isset($_POST['hb']) ? $_POST['hb'] : null;
      $param['leukosit'] = isset($_POST['leukosit']) ? $_POST['leukosit'] : null;
      $param['albumin'] = isset($_POST['albumin']) ? $_POST['albumin'] : null;
      $param['tgl_mandi1'] = isset($_POST['tgl_mandi1']) ? $_POST['tgl_mandi1'] : null;
      $param['tgl_mandi2'] = isset($_POST['tgl_mandi2']) ? $_POST['tgl_mandi2'] : null;
      $param['mandi_menggunakan'] = isset($_POST['mandi_menggunakan']) ? $_POST['mandi_menggunakan'] : null;
      $param['mandi_menggunakan_lain'] = isset($_POST['mandi_menggunakan_lain']) ? $_POST['mandi_menggunakan_lain'] : null;
      $param['alat_cukur'] = isset($_POST['alat_cukur']) ? $_POST['alat_cukur'] : null;
      $param['lokasi_mandi'] = isset($_POST['lokasi_mandi']) ? $_POST['lokasi_mandi'] : null;
      $param['tgl_cukur'] = isset($_POST['tgl_cukur']) ? $_POST['tgl_cukur'] : null;
      $param['asascore'] = isset($_POST['asascore']) ? $_POST['asascore'] : null;
      $param['petugas_asa_score'] = isset($_POST['petugas_asa_score']) ? $_POST['petugas_asa_score'] : null;
      $param['klasifikasi_lukaop'] = isset($_POST['klasifikasi_lukaop']) ? $_POST['klasifikasi_lukaop'] : null;
      $param['urgensi_op'] = isset($_POST['urgensi_op']) ? $_POST['urgensi_op'] : null;
      $param['ruang_operasi'] = isset($_POST['ruang_operasi']) ? $_POST['ruang_operasi'] : null;
      $param['nama_operator'] = "";
      if (isset($_POST['nama_operator'])) {
        $param['nama_operator'] = implode(',', $_POST['nama_operator']);
      }
      $param['nama_asisten'] = isset($_POST['nama_asisten']) ? $_POST['nama_asisten'] : null;
      $param['asisten_dokter'] = isset($_POST['asisten_dokter']) ? $_POST['asisten_dokter'] : null;
      $param['perawat_instrumen'] = isset($_POST['perawat_instrumen']) ? json_encode($_POST['perawat_instrumen']) : null;
      $param['perawat_sirkuler'] = isset($_POST['perawat_sirkuler']) ? json_encode($_POST['perawat_sirkuler']) : null;
      $param['dokter_anastesi'] = isset($_POST['dokter_anastesi']) ? $_POST['dokter_anastesi'] : null;
      $param['perawat_anastesi'] = isset($_POST['perawat_anastesi']) ? $_POST['perawat_anastesi'] : null;
      $param['ab_dibutuhkan'] = isset($_POST['ab_dibutuhkan']) ? $_POST['ab_dibutuhkan'] : null;
      $param['ab_alasanlain'] = isset($_POST['ab_alasanlain']) ? $_POST['ab_alasanlain'] : null;
      $param['ab_profilaksis'] = isset($_POST['ab_profilaksis']) ? $_POST['ab_profilaksis'] : null;
      $param['nama_ab_prof'] = isset($_POST['nama_ab_prof']) ? $_POST['nama_ab_prof'] : null;
      $param['dosis_ab_prof'] = isset($_POST['dosis_ab_prof']) ? $_POST['dosis_ab_prof'] : null;
      $param['waktu_ab_prof'] = isset($_POST['waktu_ab_prof']) ? $_POST['waktu_ab_prof'] : null;
      $param['ulang_ab_prof'] = isset($_POST['ulang_ab_prof']) ? $_POST['ulang_ab_prof'] : null;
      $param['waktu_pemberian_ulang_ab_prof'] = isset($_POST['waktu_pemberian_ulang_ab_prof']) ? $_POST['waktu_pemberian_ulang_ab_prof'] : null;
      $param['antiseptik'] = isset($_POST['antiseptik']) ? $_POST['antiseptik'] : null;
      $param['teknik_preparasi'] = isset($_POST['teknik_preparasi']) ? $_POST['teknik_preparasi'] : null;
      $param['nama_hh_operator'] = "";
      if (isset($_POST['nama_hh_operator'])) {
        $param['nama_hh_operator'] = implode(',', $_POST['nama_hh_operator']);
      }

      $param['nama_hh_sirkuler'] = isset($_POST['nama_hh_sirkuler']) ? $_POST['nama_hh_sirkuler'] : null;
      $param['nama_hh_perawat'] = isset($_POST['nama_hh_perawat']) ? $_POST['nama_hh_perawat'] : null;
      $param['nama_hh_asisten'] = isset($_POST['nama_hh_asisten']) ? $_POST['nama_hh_asisten'] : null;
      $param['hh_perawat'] = isset($_POST['hh_perawat']) ? $_POST['hh_perawat'] : null;
      $param['hh_sirkuler'] = isset($_POST['hh_sirkuler']) ? $_POST['hh_sirkuler'] : null;
      $param['hh_operator'] = isset($_POST['hh_operator']) ? $_POST['hh_operator'] : null;
      $param['hh_asisten'] = isset($_POST['hh_asisten']) ? $_POST['hh_asisten'] : null;
      $param['jml_petugas_op'] = isset($_POST['jml_petugas_op']) ? $_POST['jml_petugas_op'] : null;
      $param['jml_selain_petugas_op'] = isset($_POST['jml_selain_petugas_op']) ? $_POST['jml_selain_petugas_op'] : null;
      $param['jml_pintu_dibuka'] = isset($_POST['jml_pintu_dibuka']) ? $_POST['jml_pintu_dibuka'] : null;
      $param['terpasang_drain'] = isset($_POST['terpasang_drain']) ? $_POST['terpasang_drain'] : null;
      $param['lokasi_drain'] = isset($_POST['lokasi_drain']) ? $_POST['lokasi_drain'] : null;
      $param['tipe_drain'] = isset($_POST['tipe_drain']) ? $_POST['tipe_drain'] : null;
      $param['penggunaan_alat_reuse'] = isset($_POST['penggunaan_alat_reuse']) ? $_POST['penggunaan_alat_reuse'] : null;
      $param['nama_alat_reuse1'] = isset($_POST['nama_alat_reuse1']) ? $_POST['nama_alat_reuse1'] : null;
      $param['nama_alat_reuse2'] = isset($_POST['nama_alat_reuse2']) ? $_POST['nama_alat_reuse2'] : null;
      $param['nama_alat_reuse3'] = isset($_POST['nama_alat_reuse3']) ? $_POST['nama_alat_reuse3'] : null;
      $param['alat_reuse1'] = isset($_POST['alat_reuse1']) ? $_POST['alat_reuse1'] : null;
      $param['alat_reuse2'] = isset($_POST['alat_reuse2']) ? $_POST['alat_reuse2'] : null;
      $param['alat_reuse3'] = isset($_POST['alat_reuse3']) ? $_POST['alat_reuse3'] : null;
      $param['penggunaan_implant'] = isset($_POST['penggunaan_implant']) ? $_POST['penggunaan_implant'] : null;
      $param['jns_implant'] = isset($_POST['jns_implant']) ? $_POST['jns_implant'] : null;
      $param['jns_implant_lain'] = isset($_POST['jns_implant_lain']) ? $_POST['jns_implant_lain'] : null;
      $param['ab_post_op'] = isset($_POST['ab_post_op']) ? $_POST['ab_post_op'] : null;
      $param['nama_ab_op'] = isset($_POST['nama_ab_op']) ? $_POST['nama_ab_op'] : null;
      $param['dosis_ab_op'] = isset($_POST['dosis_ab_op']) ? $_POST['dosis_ab_op'] : null;
      $param['selama_ab_op'] = isset($_POST['selama_ab_op']) ? $_POST['selama_ab_op'] : null;
      $param['alasan_pos_op'] = isset($_POST['alasan_pos_op']) ? $_POST['alasan_pos_op'] : null;
      $param['alasan_lain_post_op'] = isset($_POST['alasan_lain_post_op']) ? $_POST['alasan_lain_post_op'] : null;
      $param['hasil_lab_leukosit'] = isset($_POST['hasil_lab_leukosit']) ? $_POST['hasil_lab_leukosit'] : null;
      $param['hasil_lab_crcp'] = isset($_POST['hasil_lab_crcp']) ? $_POST['hasil_lab_crcp'] : null;
      $param['hasil_lab_pct'] = isset($_POST['hasil_lab_pct']) ? $_POST['hasil_lab_pct'] : null;
      $param['hasil_lab_gds'] = isset($_POST['hasil_lab_gds']) ? $_POST['hasil_lab_gds'] : null;
      $param['isi_lab_leukosit'] = isset($_POST['isi_lab_leukosit']) ? $_POST['isi_lab_leukosit'] : null;
      $param['isi_lab_crcp'] = isset($_POST['isi_lab_crcp']) ? $_POST['isi_lab_crcp'] : null;
      $param['isi_lab_pct'] = isset($_POST['isi_lab_pct']) ? $_POST['isi_lab_pct'] : null;
      $param['isi_lab_gds'] = isset($_POST['isi_lab_gds']) ? $_POST['isi_lab_gds'] : null;
      $param['tgl_hasil_lab_op'] = isset($_POST['tgl_hasil_lab_op']) ? $_POST['tgl_hasil_lab_op'] : null;
      $param['tgl_isi_form'] = isset($_POST['tgl_isi_form']) ? $_POST['tgl_isi_form'] : null;
      $param['tgl_selesai_isi'] = isset($_POST['tgl_selesai_isi']) ? $_POST['tgl_selesai_isi'] : null;
      $param['nama_petugas'] = isset($_POST['nama_petugas']) ? $_POST['nama_petugas'] : null;

      $param['user'] = $iduser;
      // echo '<pre>';print_r($param);exit();
      $response = $this->Idomodel->simpanpreido($param);
      $res['success'] = "0";
      if ($response) {
        if ($param['id'] > 0) {
          $res['success'] = "2";
        } else {
          $res['success'] = "1";
        }
      }
      echo json_encode($res);

    }

  }

  public function tabelpreido($nokun = null)
  {
    $data['data'] = $this->Idomodel->histopreido($nokun);
    $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/tabelsurvido', $data);
  }

  public function tabelpreidoshow($nokunid = null)
  {
    $nokid = explode("_", $nokunid);
    $data['dt'] = $this->Idomodel->histopreidoshow($nokid[1]);
    $data['idpreido'] = $nokid[1];
    // var_dump($data['id']);exit;
    $data['nokun'] = $nokid[0];
    $data['mdokter'] = $this->Idomodel->mdokter();
    $data['mpegmedis'] = $this->Idomodel->mpegmedis();
    $data['mperawat'] = $this->MasterModel->listPerawatPenataAnestesi();
    $data['mperawatanastesi'] = $this->Idomodel->mperawatanastesi();
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/form_survido_isi', $data);
  }

  public function post($nokun = null)
  {
    $data['nokun'] = $nokun;
    $data['dt'] = $this->Idomodel->getpasien($nokun);
    $view['idoisi'] = $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/form_postido_isi', $data, true);
    $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/form_ido', $view);
  }

  public function pospostido($no = null)
  {
    $iduser = $this->session->userdata("id");
    if ($iduser && isset($_POST['btnsimpan']) && isset($_POST['nomr']) && $_POST['nomr']) {
      $param['id'] = isset($_POST['ID']) ? $_POST['ID'] : null;
      $param['nomr'] = isset($_POST['nomr']) ? $_POST['nomr'] : null;
      $param['nokun'] = isset($_POST['nokun']) ? $_POST['nokun'] : null;
      $param['nopen'] = isset($_POST['nopen']) ? $_POST['nopen'] : null;
      $param['tglmasuk'] = isset($_POST['tglmasuk']) ? $_POST['tglmasuk'] : null;
      $param['tglkeluar'] = isset($_POST['tglkeluar']) ? $_POST['tglkeluar'] : null;
      $param['postke'] = isset($_POST['postke']) ? $_POST['postke'] : null;
      $param['tglsurvey'] = isset($_POST['tglsurvey']) ? $_POST['tglsurvey'] : null;
      $param['event'] = isset($_POST['event']) ? $_POST['event'] : null;
      $param['antibiotik'] = isset($_POST['antibiotik']) ? $_POST['antibiotik'] : null;
      $param['gejala'] = isset($_POST['gejala']) ? $_POST['gejala'] : null;
      $param['petugas'] = isset($_POST['petugas']) ? $_POST['petugas'] : null;


      $param['user'] = $iduser;
      $response = $this->Idomodel->simpanpostido($param);
      $res['success'] = "0";
      if ($response) {
        if ($param['id'] > 0) {
          $res['success'] = "2";
        } else {
          $res['success'] = "1";
        }
      }
      echo json_encode($res);

    }

  }

  public function tabelpostido($nokun = null)
  {
    $data['data'] = $this->Idomodel->histopostido($nokun);
    $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/tabelpostido', $data);
  }

  public function tabelpostidoshow($nokunid = null)
  {
    $nokid = explode("_", $nokunid);
    $data['dt'] = $this->Idomodel->histopostidoshow($nokid[1]);
    $data['idpostido'] = $nokid[1];
    $data['nokun'] = $nokid[0];
    $this->load->view('rekam_medis/rawat_inap/keperawatan/ido/form_postido_isi', $data);
  }

}