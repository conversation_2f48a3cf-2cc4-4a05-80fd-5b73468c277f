<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SiteMarking extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'siteMarkingModel'));
  }

  public function index()
  {
    $data = array(
      'nomr' => $this->uri->segment(3),
      'nopen' => $this->uri->segment(4),
      'nokun' => $this->uri->segment(5),
      'siteMarking' => $this->masterModel->siteMarking(),
    );
    
    $this->load->view('Pengkajian/operasi/siteMarking', $data);
  }

  public function tampilGambar()
  {
    $idGambar = $this->input->post('idGambar');

        // echo "<pre>";print_r($nokun);exit();
    $smGb = $this->masterModel->getPictSiteMarking($idGambar);

    $gb = base_url("assets/admin/assets/images/siteMarking/" . $smGb['file']);

    echo '
    <div class="demoSiteMarking" id="colors_demoSiteMarking">
    <div class="toolsSiteMarking">
    <a href="#my_canvasSiteMarking" data-tool="marker">Marker</a>
    <a href="#my_canvasSiteMarking" data-tool="eraser">Eraser</a>
    </div>';
    echo "
    <div class='form-group'>
    <canvas id='my_canvasSiteMarking' width='450' height='450' style='background:url($gb) no-repeat;'></canvas>
    <input type='hidden' name='img_valSiteMarking' id='img_valSiteMarking' value='' />
    </div></div>";
        // echo"<pre>";print_r($soapGb);exit();
  }

  public function simpanSm()
  {
    $nomr       = $this->input->post('nomr');
    $nopen      = $this->input->post('nopen');
    $nokun      = $this->input->post('nokun');
    $idPengguna = $this->input->post('idPengguna');

    $data = array(
      'nomr'       => $nomr,
      'nopen'      => $nopen,
      'nokun'      => $nokun,
      'idPengguna' => $idPengguna,
      'judul'      => $this->input->post('judulSiteMarking'),
      'data'       => file_get_contents($this->input->post('img_valSiteMarking')),
      'catatan'    => $this->input->post('catatanSiteMarking'),
    );
    // $this->siteMarkingModel->simpanFotoSiteMarking($data);
    if($this->db->insert('medis.tb_foto_siteMarking', $data)){
      $result = array('status' => 'success');
    }else{
        $result = array('status' => 'failed');
    }
    echo json_encode($result);
    // redirect(base_url("pengkajianAwal/index/" . $nomr . "/" . $nopen . "/" . $nokun."/ews"));
  }

  public function tblHistorySiteMarking()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listSiteMarking = $this->siteMarkingModel->listSiteMarking($nomr);

    $data = array();
    $no = 1;
    foreach ($listSiteMarking->result() as $historySm) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s",strtotime($historySm->tanggal)),
        $historySm->judul,
        '<input type="checkbox" class="cekstatusSiteMarking" value="'.$historySm->id.'">',
        '<a href="#" class="clickSm btn-sm btn-block btn-primary" data-id="'.$historySm->id.'" ><i class="fa fa-eye"></i> View</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listSiteMarking->num_rows(),
      "recordsFiltered" => $listSiteMarking->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function updateStatusSiteMarking()
  {
    $id = $this->input->post('id');
    $data = array(
      'status' => 0,
    );
    $this->siteMarkingModel->updateStatusSm($id, $data);
  }

  public function keteranganSm()
  {
    $id = $this->input->post('id');
    $hasilFotoSm = $this->siteMarkingModel->hasilFotoSm($id);

    echo '
    <div class="row">
    <div class="col-md-12">
    <div class="form-group">
    <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . date("d-m-Y H:i:s",strtotime($hasilFotoSm['tanggal'])) . '</span> ]</h4>
    </div>
    </div>
    </div>

    <div class="row">
    <div class="col-md-12">
    <div class="form-group">
    <label for="judulSiteMarking">Judul Site Marking</label>
    <input type="text" class="form-control" placeholder="[ Judul Site Marking ]" value="' . $hasilFotoSm['judul'] . '" readonly>
    </div>
    </div>
    </div>

    <div class="row">
    <div class="col-md-6">
    <div class="form-group">
    <label>Hasil Foto Site Marking</label><br>
    <img src="data:image;base64,' . base64_encode($hasilFotoSm['data']) . '">
    </div>
    </div>
    <div class="col-md-6">
    <div class="form-group">
    <label for="catatan">Catatan</label>
    <textarea class="form-control" cols="15" rows="10" placeholder="[Catatan ]"  readonly>' . $hasilFotoSm['catatan'] . '</textarea>
    </div>
    </div>
    </div>';
  }

}

/* End of file SiteMarking.php */
/* Location: ./application/controllers/operasi/SiteMarking.php */
