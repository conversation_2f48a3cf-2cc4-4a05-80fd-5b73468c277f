<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ResumeKematianModel extends MY_Model {

    public function simpanResumeKematian($data)
  {
    $this->db->insert('db_layanan.tb_resume_kematian', $data);
  }

  public function updateResumeKematian($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('db_layanan.tb_resume_kematian', $data);
  }

    public function getDataPasien($nokun){
        // $ins = $this->session->userdata('ses_ins');
        $hasil= "SELECT pepeka.NAMA, pepeka.KODE KODE_RS, p.NORM	
        , master.getNamaLengkap(p.NORM) NAMA_LENGKAP, kip.NOMOR NIK #, pepeka.*
        , IF(p.JENIS_KELAMIN=1,'La<PERSON>-Laki','Perempuan') JK
        , wil.DESKRIPSI
        , DATE_FORMAT(p.TANGGAL_LAHIR,'%d-%m-%Y') TGL_LAHIR
        , ag.DESKRIPSI AGAMA
        , p.ALAMAT, p.RT, p.RW, mkp.NOMOR NO_TELP
        , pro.DESKRIPSI PROVINSI
        , kot.DESKRIPSI KOTA
        , kec.DESKRIPSI KECAMATAN
        , kel.DESKRIPSI KELURAHAN
        , IF(wil.ID NOT LIKE '31%','Bukan Penduduk','Penduduk') STATUS_KEPENDUDUKAN
        , DATE_FORMAT(pp.TANGGAL,'%d-%m-%Y') TGL_KEMATIAN
        , DATE_FORMAT(pp.TANGGAL, '%H:%i') JAM_KEMATIAN
        , '2021-06-28 14:59:00' NTAH
        , TIME(pp.TANGGAL) JAM_KEMATIAN_DETAIL
        , master.getCariUmurTahun(pp.TANGGAL, p.TANGGAL_LAHIR) UMUR
    
    FROM pendaftaran.kunjungan pk
        
        LEFT JOIN pendaftaran.pendaftaran pd ON pd.NOMOR = pk.NOPEN
        LEFT JOIN master.pasien p ON p.NORM = pd.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = p.NORM
        LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = p.NORM AND kip.JENIS=1
        LEFT JOIN master.wilayah wil ON wil.ID = p.TEMPAT_LAHIR
        LEFT JOIN master.referensi ag ON ag.ID = p.AGAMA AND ag.JENIS=1
        LEFT JOIN master.wilayah kel ON kel.ID = p.WILAYAH AND kel.JENIS=4
        LEFT JOIN master.wilayah kec ON kec.ID = SUBSTR(p.WILAYAH,1,6) AND kec.JENIS=3
        LEFT JOIN master.wilayah kot ON kot.ID = SUBSTR(p.WILAYAH,1,4) AND kot.JENIS=2
        LEFT JOIN master.wilayah pro ON pro.ID = SUBSTR(p.WILAYAH,1,2) AND pro.JENIS=1
        LEFT JOIN layanan.pasien_pulang pp ON pp.NOPEN = pd.NOMOR AND pp.`STATUS`!=0 AND pp.CARA=6
        
        , (SELECT * FROM master.ppk ppk
            WHERE ppk.ID=?) pepeka
    
    WHERE pk.NOMOR=?";

        $bind = $this->db->query($hasil, array(13747,$nokun));
        return $bind;
    }

    public function listResumeKematian($nomr)
    {
      $query = $this->db->query("SELECT trk.*, master.getNamaLengkapPegawai(ap.NIP) OLEH_PEMBUAT,
      master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA 
      FROM db_layanan.tb_resume_kematian trk
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = trk.nokun
      LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN 
      LEFT JOIN aplikasi.pengguna ap ON ap.ID = trk.oleh
      LEFT JOIN master.dokter md ON md.ID = trk.dokter
      WHERE pp.NORM = '$nomr' AND trk.status = 1
      ORDER BY trk.id DESC");

      return $query;
    }

    public function getResumeKematian($id)
  {
    $query = $this->db->query("SELECT
                            trk.*,
                            master.getNamaLengkapPegawai ( md.NIP ) DOKTERPELAKSANA,
                            master.getNamaLengkapPegawai(ap.NIP) OLEH_PEMBUAT
                        FROM
                            db_layanan.tb_resume_kematian trk
                            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = trk.nokun
                            LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                            LEFT JOIN aplikasi.pengguna ap ON ap.ID = trk.oleh
                            LEFT JOIN master.dokter md ON md.ID = trk.dokter 
                        WHERE
                            trk.id = $id
                            AND trk.`status` = 1");
    return $query->row_array();
  }
}
?>