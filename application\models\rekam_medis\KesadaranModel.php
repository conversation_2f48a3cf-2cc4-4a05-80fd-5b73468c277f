<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KesadaranModel extends MY_Model
{
    protected $_table_name = 'db_pasien.tb_kesadaran';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>',
                'numeric' => '%s Wajib Angka',
            )
        ),

        'nomr' => array(
            'field' => 'nomr',
            'label' => 'Nomor rekam medis',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi',
                'numeric' => '%s Wajib Angka',
            )
        ),

        'kesadaran' => array(
            'field' => 'kesadaran',
            'label' => 'Kesadaran',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s <PERSON>ajib <PERSON>',
            )
        ),
    );

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select(
            't.id, t.data_source, t.ref, t.id_otk, t.nokun, t.nomr, t.kesadaran, t.oleh, t.status, t.created_at,
            ds.deskripsi, master.getNamaLengkapPegawai(ap.NIP) oleh_desc'
        );
        $this->db->from('db_pasien.tb_kesadaran t');
        $this->db->join('db_master.tb_data_source ds', 't.data_source = ds.id', 'left');
        $this->db->join('aplikasi.pengguna ap', 't.oleh = ap.ID', 'left');
        $this->db->where('t.kesadaran !=', null);
        $this->db->order_by('t.created_at', 'DESC');
        if ($this->input->post('nomr')) {
            $this->db->where('t.nomr', $this->input->post('nomr'));
            $this->db->limit('1');
        }

        if ($this->input->post('nokun')) {
            $this->db->where('t.nokun', $this->input->post('nokun'));
            $this->db->limit('1');
        }

        if ($this->input->post('id')) {
            $this->db->where('t.id', $this->input->post('id'));
        }
    }

    function get_table($single = TRUE)
    {
        $this->db->where('t.status', 1);
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}

/* End of file KesadaranModel.php */
/* Location: ./application/models/rekam_medis/KesadaranModel.php */