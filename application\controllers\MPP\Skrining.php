<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Skrining extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'MPP/SkriningModel'
      )
    );
  }

  public function index($id = null)
  {
    $nokun = $this->uri->segment(3);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'id' => $id,
      'nokun' => $nokun,
      'pasien' => $pasien,
      'jumlah' => $this->SkriningModel->history($pasien['NORM'], 'jumlah'),
    );
    if (isset($id)) {
      $data['detail'] = $this->SkriningModel->detail($id);
    }
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/MPP/Skrining/index', $data);
  }

  public function simpan()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->SkriningModel->rules;
      $this->form_validation->set_rules($rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'] ?? null;
        $perluMPP = 0;
        $tanggal = $post['tanggal'] ?? null;
        $waktu = $post['waktu'] ?? null;
        $tanggalWaktu = date('Y-m-d H:i:s', strtotime("$tanggal $waktu"));
        // echo '<pre>';print_r($tanggalWaktu);exit();

        // Mulai kriteria penilaian mayor
        $rawatInapPenuh = $post['rawat_inap_penuh'] ?? 0;
        $isuSosial = $post['isu_sosial'] ?? 0;
        $bunuhDiri = $post['bunuh_diri'] ?? 0;
        $potensiKomplain = $post['potensi_komplain'] ?? 0;
        // Akhir kriteria penilaian mayor

        // Mulai kriteria penilaian minor
        $usia = $post['usia'] ?? 0;
        $kognitifRendah = $post['kognitif_rendah'] ?? 0;
        $penyakitKronis = $post['penyakit_kronis'] ?? 0;
        $fungsionalRendah = $post['fungsional_rendah'] ?? 0;
        $riwayatPeralatanMedis = $post['riwayat_peralatan_medis'] ?? 0;
        $gangguanMental = $post['gangguan_mental'] ?? 0;
        $seringMasuk = $post['sering_masuk'] ?? 0;
        $biayaTinggi = $post['biaya_tinggi'] ?? 0;
        $pembiayaanKompleks = $post['pembiayaan_kompleks'] ?? 0;
        $melebihiLamaRawat = $post['melebihi_lama_rawat'] ?? 0;
        // Akhir kriteria penilaian minor

        // Mulai periksa kriteria
        if ($rawatInapPenuh == 1 || $isuSosial == 1 || $bunuhDiri == 1 || $potensiKomplain == 1) {
          $perluMPP = 1;
        } else {
          $minor = $usia + $kognitifRendah + $penyakitKronis + $fungsionalRendah + $riwayatPeralatanMedis + $gangguanMental + $seringMasuk + $biayaTinggi + $pembiayaanKompleks + $melebihiLamaRawat;
          if ($minor > 3) {
            $perluMPP = 1;
          } else {
            $perluMPP = 0;
          }
        }
        // echo '<pre>';print_r($perluMPP);exit();
        // Akhir periksa kriteria

        // Mulai data
        $data = array(
          'nokun' => $post['nokun'] ?? null,
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'rawat_inap_penuh' => $rawatInapPenuh,
          'isu_sosial' => $isuSosial,
          'bunuh_diri' => $bunuhDiri,
          'potensi_komplain' => $potensiKomplain,
          'usia' => $usia,
          'kognitif_rendah' => $kognitifRendah,
          'penyakit_kronis' => $penyakitKronis,
          'fungsional_rendah' => $fungsionalRendah,
          'riwayat_peralatan_medis' => $riwayatPeralatanMedis,
          'gangguan_mental' => $gangguanMental,
          'sering_masuk' => $seringMasuk,
          'biaya_tinggi' => $biayaTinggi,
          'pembiayaan_kompleks' => $pembiayaanKompleks,
          'melebihi_lama_rawat' => $melebihiLamaRawat,
          'perlu_mpp' => $perluMPP,
          'oleh' => $this->session->userdata['id'],
          'created_at' => $tanggalWaktu,
          'status' => 1,
        );
        // Akhir data

        // Mulai simpan
        // echo '<pre>';print_r($data);exit();
        if (!empty($id)) {
          $this->SkriningModel->ubah($data, $id);
        } else {
          $this->SkriningModel->simpan($data);
        }
        // Akhir simpan

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array(
      'nomr' => $post['nomr'],
      'nokun' => $post['nokun'],
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/MPP/Skrining/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nomr = $this->input->post('nomr');
    $history = $this->SkriningModel->history($nomr, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    $perluMPP = null;
    // echo '<pre>';print_r($nomr);exit();

    foreach ($history->result() as $h) {
      // Mulai periksa status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }
      // Akhir periksa status

      // Mulai periksa keperluan MPP
      if ($h->perlu_mpp == 0) {
        $perluMPP = '<p class="text-success">Tidak perlu</p>';
      } elseif ($h->perlu_mpp == 1) {
        $perluMPP = '<p class="text-warning">Perlu</p>';
      }
      // Akhir periksa keperluan MPP

      // Mulai data
      $data[] = array(
        $no++,
        date('d/m/Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->waktu)),
        $perluMPP,
        $h->pengisi,
        date('d/m/Y, H:i', strtotime("$h->tanggal $h->waktu")),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-smpp' class='btn btn-sm btn-danger waves-effect tbl-batal-smpp' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' class='btn btn-sm btn-primary waves-effect tbl-detail-smpp' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      // Akhir data
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = $post['id'] ?? null;
    $data = array('status' => 0);
    $this->SkriningModel->ubah($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detail()
  {
    $post = $this->input->post(null, true);
    $detail = $this->SkriningModel->detail($post['id']);
    // echo '<pre>';print_r($detail);exit();
    echo json_encode(
      array(
        'status' => 'succes',
        'data' => $detail,
      )
    );
  }
}

/* End of file Skrining.php */
/* Location: ./application/controllers/MPP/Skrining.php */