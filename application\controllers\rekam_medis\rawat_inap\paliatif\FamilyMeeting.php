<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FamilyMeeting extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/paliatif/FamilyMeetingModel'));
    }

    public function index()
    {
        $norm                   = $this->uri->segment(6);
        $nopen                  = $this->uri->segment(7);
        $nokun                  = $this->uri->segment(8);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        // $id_ruangan             = substr($nokun, 0, 9);
        // $hPaliatif              = $this->pengkajianAwalModel->historyPaliatifRI();
        // $getNomr                = $this->PengkajianPaliatifRIModel->getNomrPalRI($nopen);
        // $getIdEmr               = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
        // $getPengkajianPaliatif  = $this->PengkajianPaliatifRIModel->getPengkajianPaliatif($getIdEmr);
        $data = array(
            'norm'                          => $this->uri->segment(6),
            'nopen'                         => $this->uri->segment(7),
            'nokun'                         => $this->uri->segment(8),
            'HubunganPasien'                => $this->masterModel->referensi(1610),
            // 'getPengkajianPaliatif'         => $getPengkajianPaliatif,
            'getNomr'                       => $getNomr,
            // 'getIdEmr'                      => $getIdEmr,
            // 'kunjungan_pk'                  => $this->pengkajianAwalModel->kunjungan_pk($norm),
            // 'listVBPJS'                     => $this->pengkajianAwalModel->listVBPJS($norm),
            // 'id_ruangan'                    => $id_ruangan,
            // 'tindakan_rad'                  => $this->pengkajianAwalModel->tindakan_rad($norm),
            // 'hPaliatif'                     => $hPaliatif,
        );
        $this->load->view('rekam_medis/rawat_inap/paliatif/familymeeting/index', $data);
    }

    public function simpanFamilyMeeting()
    {
        $this->db->trans_begin();
        date_default_timezone_set('Asia/Jakarta');

        $post = $this->input->post();

        if($post['PAR'] == 'inputFammeet'){
            $date = $this->input->post('hariTglFM');
            $tgl = date('Y-m-d', strtotime($date));

            $dataFamilyMeeting = array (
            'norm'                 => $post['norm'],
            'nopen'                => $post['nopen'],
            'nokun'                => $post['nokun'],
            'diagnosis'            => $post['diagnosisFM'],
            'tanggal_form'         => $tgl,
            'dihadiri_pasien'      => $post['pasienFM'],
            'pemberian_informasi'  => $post['infoPasienFM'],
            'kondisi_pasien'       => $post['kondisiPenyakitFM'],
            'kondisi_pasien_desk'  => $post['kondisiYaFM'],
            'prognosis'            => $post['prognosisFM'],
            'prognosis_desk'       => $post['prognosisYaFM'],
            'created_at'           => date('Y-m-d H:i:s'),
            'created_by'           => $this->session->userdata('id'),
            );
            // echo "<pre>";print_r($dataFamilyMeeting);echo "</pre>";
            $this->db->insert('medis.tb_family_meeting', $dataFamilyMeeting);
            $idFM = $this->db->insert_id();

            $dataAspek = array ('id_fammeet' => $idFM);
            $this->db->insert('medis.tb_family_meeting_aspek_medis', $dataAspek);

            $dataFMKeluarga = array();
            $index = 0; // Set index array awal dengan 0
            foreach($post['namaKeluargaFM'] as $u){ // Kita buat perulangan berdasarkan nis sampai data terakhir
                if($post['namaKeluargaFM'][$index] != NULL || $post['namaKeluargaFM'][$index] != ""){
                    array_push($dataFMKeluarga, array(
                            'id_fammeet'           => $idFM,
                            'nama_keluarga'        => $post['namaKeluargaFM'][$index],
                            'posisi_keluarga'      => $post['keluargaFMLainnya'][$index],
                            'created_at'           => date('Y-m-d H:i:s'),
                            'created_by'           => $this->session->userdata('id'),
                        ));
                        $index++;
                }
            }
            $this->db->insert_batch('medis.tb_family_meeting_keluarga', $dataFMKeluarga);
            // echo "<pre>";print_r($dataFMKeluarga);echo "</pre>";exit();

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }

        }elseif($post['PAR'] == 'inputAspekMedis'){

            if($this->session->userdata('profesi') == 11){
                $dataAspekMedis = array (
                    // 'id_fammeet'                 => $post['idFammeet'],
                    // 'jenis_aspek'                => 1,
                    'aspek_medis'                => $post['aspekFMDokter'],
                    'id_pengisi_aspek_medis'     => $this->session->userdata('id'),
                    'nama_pengisi_aspek_medis'   => $this->session->userdata('nama'),
                    'tgl_aspek_medis'            => date('Y-m-d H:i:s')
                );
                $this->db->where('id', $post['idAspek']);
                $this->db->update('medis.tb_family_meeting_aspek_medis', $dataAspekMedis);

            }elseif($this->session->userdata('profesi') == 6){
                $dataAspekPerawat = array (
                    // 'id_fammeet'                 => $post['idFammeet'],
                    // 'jenis_aspek'                => 2,
                    'aspek_keperawatan'                => $post['aspekFMPerawat'],
                    'id_pengisi_aspek_keperawatan'     => $this->session->userdata('id'),
                    'nama_pengisi_aspek_keperawatan'   => $this->session->userdata('nama'),
                    'tgl_aspek_keperawatan'            => date('Y-m-d H:i:s'),
                    'aspek_sosial'                     => ($post['aspekFMSosial'] != NULL || $post['aspekFMSosial'] != "") ? $post['aspekFMSosial'] : NULL,
                    // 'id_pengisi_aspek_sosial'     => $this->session->userdata('id'),
                    'nama_pengisi_aspek_sosial'      => ($post['timPaliatif4'] != NULL || $post['timPaliatif4'] != "") ? $post['timPaliatif4'] : NULL,
                    'tgl_aspek_sosial'               => ($post['aspekFMSosial'] != NULL || $post['aspekFMSosial'] != "") ? date('Y-m-d H:i:s') : NULL,
                    'aspek_spiritual'                => ($post['aspekFMSpirit'] != NULL || $post['aspekFMSpirit'] != "") ? $post['aspekFMSpirit'] : NULL,
                    // 'id_pengisi_aspek_spiritual'     => $this->session->userdata('id'),
                    'nama_pengisi_aspek_spiritual'   => ($post['timPaliatif5'] != NULL || $post['timPaliatif5'] != "") ? $post['timPaliatif5'] : NULL,
                    'tgl_aspek_spiritual'            => ($post['aspekFMSpirit'] != NULL || $post['aspekFMSpirit'] != "") ? date('Y-m-d H:i:s') : NULL
                );
                $this->db->where('id', $post['idAspek']);
                $this->db->update('medis.tb_family_meeting_aspek_medis', $dataAspekPerawat);

                if($post['tindakLanjutFM'] != NULL || $post['tindakLanjutFM'] != ""){
                    $dataTindakLanjut = array (
                        'tindak_lanjut'                => $post['tindakLanjutFM'],
                    );
                    $this->db->where('id', $post['idFammeet']);
                    $this->db->update('medis.tb_family_meeting', $dataTindakLanjut);
                }

            }elseif($this->session->userdata('profesi') == 13){
                $dataAspekPsikologi = array (
                    // 'id_fammeet'                 => $post['idFammeet'],
                    // 'jenis_aspek'                => 3,
                    'aspek_psikologi'                => $post['aspekFMPsikolog'],
                    'id_pengisi_aspek_psikologi'     => $this->session->userdata('id'),
                    'nama_pengisi_aspek_psikologi'   => $this->session->userdata('nama'),
                    'tgl_aspek_psikologi'            => date('Y-m-d H:i:s')
                );
                $this->db->where('id', $post['idAspek']);
                $this->db->update('medis.tb_family_meeting_aspek_medis', $dataAspekPsikologi);
            }
            
            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }

        }elseif($post['PAR'] == 'inputTindakLanjut'){

        }

        echo json_encode($result);
    }


    public function historyFammeet()
    {
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));

        $nomr = $this->input->post('nomr');
        // $nomr = $this->uri->segment(6);
        $listFM = $this->FamilyMeetingModel->listHistoryFM($nomr);

        $data = array();
        $no = 1;
        foreach ($listFM->result() as $FM) {
            $cekAspek = $this->FamilyMeetingModel->cekAspek($FM->id);
            // if($FM->ASPEK_MEDIS > 0 && $FM->ASPEK_KEPERAWATAN > 0 && $FM->ASPEK_PSIKOLOGI > 0 && $FM->ASPEK_SOSIAL > 0 && $FM->ASPEK_ROHANIAWAN > 0){
            //     $btn = 'btn-primary';
            //     $icn = 'fa fa-eye';
            //     $nmbtn = 'Lihat';
            //     $sts = 'done';
            // }else{
                $btn = 'btn-warning';
                $icn = 'fa fa-pencil-square-o';
                $nmbtn = 'Form';
                $sts = 'progress';
            // }

            $button = '<button type="button" href="#modalFammeet" class="btn '.$btn.' btn-block" data-id="'.$FM->id.'" data-sts="'.$sts.'" data-toggle="modal" data-backdrop="static" data-keyboard="false" ><i class="'.$icn.'"></i> '.$nmbtn.'</button>';
            
            $data[] = array(
            $no,
            $FM->nokun,
            $FM->created_at,
            $FM->diagnosis,
            $FM->OLEH,
            $button,

            );
            $no++;
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listFM->num_rows(),
            "recordsFiltered" => $listFM->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }

    public function modalFammeet()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        // $gpFammeet = $this->FamilyMeetingModel->getFammeet($id);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array(
            'id' => $id,
            'gpFammeet' => $this->FamilyMeetingModel->getFammeet($id),
            'gpFammeetFamily' => $this->FamilyMeetingModel->getFammeetFamily($id),
            'gpFammeetAspek' => $this->FamilyMeetingModel->getFammeetAspek($id),
            'getNomr' => $getNomr,
        );

        $this->load->view('rekam_medis/rawat_inap/paliatif/familymeeting/view', $data);
    }

}
