<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormTriase extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/triase/FormTriaseModel'));
  }


    public function index()
    {
        // $norm = $this->uri->segment(3);
        // $nopen = $this->uri->segment(4);
        // $nokun = $this->uri->segment(5);
        // $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $jenisKunjunganTriase = $this->masterModel->referensi(485);
        $PENGGUNAAN_O2 = $this->masterModel->referensi(129);
        $atsTriase1 = $this->masterModel->referensi(479);
        $atsTriase2 = $this->masterModel->referensi(480);
        $atsTriase3 = $this->masterModel->referensi(481);
        $atsTriase4 = $this->masterModel->referensi(482);
        $atsTriase5 = $this->masterModel->referensi(483);
        $triaseIgd = $this->masterModel->referensi(486);
        $data = array(
            // 'getNomr' => $getNomr,
            // 'nokun' => $nokun,
            // 'norm' => $norm,
            // 'nopen' => $nopen,
            'jenisKunjunganTriase' => $jenisKunjunganTriase,
            'PENGGUNAAN_O2' => $PENGGUNAAN_O2,
            'atsTriase1' => $atsTriase1,
            'atsTriase2' => $atsTriase2,
            'atsTriase3' => $atsTriase3,
            'atsTriase4' => $atsTriase4,
            'atsTriase5' => $atsTriase5,
            'triaseIgd' => $triaseIgd,
            'apakahInginMasukEws' => $this->masterModel->referensi(1162),
            'kesadaran' => $this->masterModel->referensi(5),
            'skriningNyeri' => $this->FormTriaseModel->referensi_nyeri(7),
            'skalaNyeriNRS' => $this->masterModel->referensi(114),
            'efeksampingNRS' => $this->masterModel->referensi(118),
            'isi' => 'rekam_medis/triase/index',
        );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
    }

    public function atsTriase()
  {
    $id = $this->input->post("id");

    $atsTriase = $this->masterModel->referensi($id);

    foreach ($atsTriase as $atsTriase) {
      echo '<div class="checkbox checkbox-primary">';
      echo '<input type="checkbox" name="atsTriaseBaru[]" id="atsTriaseBaru'.$atsTriase['id_variabel'].'" value="'.$atsTriase['id_variabel'].'">';
      echo '<label for="atsTriaseBaru'.$atsTriase['id_variabel'].'">'.$atsTriase['variabel'].'</label>';
      echo '</div>';
    }
  }

  function cekRM(){
		$norm = $this->input->post('norm',TRUE);
		$cek = $this->FormTriaseModel->ceknorm($norm);
		if($cek->num_rows() > 0){
		  $result = array('status' => 'exist', 'data' => $cek->row_array());
		}else{
		  $result = array('status' => 'empty');
		}
		echo json_encode($result);
	}

  function cekKTP(){
		$ktp = $this->input->post('ktp',TRUE);
		$cek = $this->FormTriaseModel->cekktp($ktp);
		if($cek->num_rows() > 0){
		  $result = array('status' => 'exist', 'data' => $cek->row_array());
		}else{
		  $result = array('status' => 'empty');
		}
		echo json_encode($result);
	}

    public function simpanTriase()
  {
     $post = $this->input->post();
    //  $nomr = $this->input->post('nomr');
    $nomr = $this->input->post('nomrPasienTriaseBaru');
    $identitas = $this->input->post('noIdentitasTriaseBaru');
    $jenisidentitas = $this->input->post('jenisIdTriaseBaru');

    // if (strlen($identitas) < 16) {
    //     $post['noIdentitasTriaseBaru'] = $nomr;
    //     $post['mapping_nomr'] = $nomr;
    // } else if (strlen($identitas) >= 16 && $nomr == '') {
    //     $post['noIdentitasTriaseBaru'] = $identitas;
    //     $post['mapping_nomr'] = ""; 
    // }else if (strlen($identitas) >= 16 && $nomr != '') {
    //     $post['noIdentitasTriaseBaru'] = $identitas;
    //     $post['mapping_nomr'] = $nomr; 
    // }else{
    //     $post['noIdentitasTriaseBaru'] = $identitas;
    // }

    if ($jenisidentitas == 1) {
        $post['noIdentitasTriaseBaru'] = $identitas;
        $post['mapping_nomr'] = $identitas;
    } else if ($jenisidentitas == 2 && $nomr == '') {
        $post['noIdentitasTriaseBaru'] = $identitas;
        $post['mapping_nomr'] = ""; 
    }else if ($jenisidentitas == 2 && $nomr != '') {
        $post['noIdentitasTriaseBaru'] = $identitas;
        $post['mapping_nomr'] = $nomr; 
    }

     $dataTriase = array(
      'jenis_identitas'   => $post['jenisIdTriaseBaru'],
      'no_identitas'      => $post['noIdentitasTriaseBaru'],
      'mapping_nomr'      => $post['mapping_nomr'],
      'nama_lengkap'      => $post['namaPasienTriaseBaru'],
      'tanggal_masuk'     => $post['tanggalMasukBaru'],
      'jam'               => $post['jamMasukBaru'],
      'jenis_kunjungan'   => $post['jenisKunjunganBaru'],
      'rujukan_dari'      => isset($post['deskRujukanDariTriaseBaru']) ? $post['deskRujukanDariTriaseBaru'] : "",
      'tekanan_darah'     => $post['tekanan_darah_1TriaseBaru'],
      'per_tekanan_darah' => $post['tekanan_darah_2TriaseBaru'],
      'pernapasan'        => $post['pernapasanTriaseBaru'],
      'nadi'              => $post['nadiTriaseBaru'],
      'suhu'              => $post['suhuTriaseBaru'],
      'kesadaran'         => isset($post['kesadaranTriaseRiBaru']) ? $post['kesadaranTriaseRiBaru'] : "",
      'saturasiTriase'    => $post['saturasiTriaseBaru'],
      'penggunaanO2'      => $post['penggunaanO2Baru'],
      'data_source'       => 4,
      'metode'            => $post['skrining_nyeri_triase'],
      'skor'              => $post['skrining_nyeri_triase'] != 17 ? $post['skor_nyeri_triase'] : "",
      'farmakologi'       => $post['skrining_nyeri_triase'] != 17 ? $post['farmakologi_triase'] : null,
      'non_farmakologi'   => $post['skrining_nyeri_triase'] != 17 ? $post['non_farmakologi_triase'] : null,
      'efek_samping'      => $post['skrining_nyeri_triase'] != 17 ? $post['efek_samping_triase'] : "",
      'ket_efek_samping'  => $post['skrining_nyeri_triase'] != 17 && $post['efek_samping_triase'] == 338 ? $post['efek_samping_lain_triase'] : "",
      'jenis_ats'         => $post['atsTriaseBaru'],
      'cat_ats'           => $post['catATSBaru'],
      'diterima_igd'      => $post['igdBukan'],
      'ket_diterima_igd'  => $post['ketIgdBukan'],
      'oleh'              => $this->session->userdata('id'),
    );
    //  echo "<pre>";print_r($dataTriase);echo "</pre>";
    $getIdTriase = $this->FormTriaseModel->simpanTriase($dataTriase);

    // $atsTriase = array();
    // $indexAtsTriase = 0;
    // if (isset($post['atsTriaseBaru'])) {
      // foreach ($post['atsTriaseBaru'] as $input) {
      //   if ($post['atsTriaseBaru'][$indexAtsTriase] != "") {
      //     array_push(
      //       $atsTriase, array(
      //         'id_triase'   => $getIdTriase,
      //         'ats_detail' => $post['atsTriaseBaru'][$indexAtsTriase],
      //       )
      //     );
      //   }
      //   $indexAtsTriase++;
      // }
      // echo "<pre>";print_r($atsTriase);echo "</pre>";
      // $this->db->insert_batch('keperawatan.tb_triase_ats_nonpasien', $atsTriase);
    // }
  }

  public function historyTriaseBaru()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $listHistoryBaru = $this->FormTriaseModel->listTriase();

    $data = array();
    $no = 1;
    foreach ($listHistoryBaru->result() as $hb) {
      if($hb->jenis_identitas == 1){
        $jenisId = 'NORM';
      }else if($hb->jenis_identitas == 2){
        $jenisId = 'KTP';
      }else if($hb->jenis_identitas == 3){
        $jenisId = 'SIM';
      }else{
        $jenisId = 'Pasport';
      }

      if($hb->diterima_igd == 1){
        // $igd = '✔';
        // $igd = '<i class="fa fa-check" style="color:green"></i>';
        $igd = '<span class="badge" style="background-color: green">Diterima</span>';
      }else if($hb->diterima_igd == 0){
        // $igd = '✖';
        // $igd = '<i class="fa fa-times" style="color:red"></i>';
        $igd = '<span class="badge" style="background-color: red">Tidak</span>';
      }
      

      $button = '<a href="#modalHistoryTsBaru" class="btn btn-primary btn-small" data-id="'.$hb->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-edit"></i></a>
        <a class="btn btn-warning btn-small cetakTriaseNonPasien" data="'.$hb->id.'"><i class="fas fa-print"></i></a>';
      
        $data[] = array(
        $no,
        $jenisId,
        $hb->no_identitas,
        $hb->mapping_nomr,
        $hb->nama_lengkap,
        $hb->DOKTER,
        $hb->tanggal_masuk,
        $igd,
        $hb->ket_diterima_igd,
        $button
        // '<center><a class="btn btn-warning btn-small cetakTriaseNonPasien" data="'.$hb->id.'"><i class="fas fa-print"></i></a></center>',

      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listHistoryBaru->num_rows(),
      "recordsFiltered" => $listHistoryBaru->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  function getPasien(){
		$mr = $this->input->post('mr',TRUE);
		$data = $this->FormTriaseModel->cariMappingMR($mr);
		echo json_encode($data);
	}

  public function simpanMappingMr()
	{
		$this->db->trans_begin();

		$post = $this->input->post();
		$id = $this->input->post('idMapping');
		$mr = $this->input->post('nomrTriaseBaru');
    $user = $this->session->userdata('id');
		$data = array(
      'jenis_identitas'   => $post['jenisIdTriaseBaru_edit'],
      'no_identitas'      => $post['noIdentitasTriaseBaru_edit'],
      'nama_lengkap'      => $post['namaPasienTriaseBaru_edit'],
      'tanggal_masuk'     => $post['tanggalMasukBaru_edit'],
      'jam'               => $post['jamMasukBaru_edit'],
      'jenis_kunjungan'   => $post['jenisKunjunganBaru_edit'],
      'rujukan_dari'      => isset($post['jenisKunjunganBaru_edit']) != 1653 ? $post['deskRujukanDariTriaseBaru_edit'] : "",
      'tekanan_darah'     => $post['tekanan_darah_1TriaseBaru_edit'],
      'per_tekanan_darah' => $post['tekanan_darah_2TriaseBaru_edit'],
      'pernapasan'        => $post['pernapasanTriaseBaru_edit'],
      'nadi'              => $post['nadiTriaseBaru_edit'],
      'suhu'              => $post['suhuTriaseBaru_edit'],
      'kesadaran'         => isset($post['kesadaranTriaseRiBaru_edit']) ? $post['kesadaranTriaseRiBaru_edit'] : "",
      'saturasiTriase'    => $post['saturasiTriaseBaru_edit'],
      'penggunaanO2'      => $post['penggunaanO2Baru_edit'],
      'data_source'       => 4,
      'metode'            => $post['skrining_nyeri_triase_edit'],
      'skor'              => $post['skrining_nyeri_triase_edit'] != 17 ? $post['skor_nyeri_triase_edit'] : "",
      'farmakologi'       => $post['skrining_nyeri_triase_edit'] != 17 ? $post['farmakologi_triase_edit'] : null,
      'non_farmakologi'   => $post['skrining_nyeri_triase_edit'] != 17 ? $post['non_farmakologi_triase_edit'] : null,
      'efek_samping'      => $post['skrining_nyeri_triase_edit'] != 17 ? $post['efek_samping_triase_edit'] : "",
      'ket_efek_samping'  => $post['skrining_nyeri_triase_edit'] != 17 && $post['efek_samping_triase_edit'] == 338 ? $post['efek_samping_lain_triase_edit'] : "",
      'jenis_ats'         => $post['atsTriaseBaru_edit'],
      'cat_ats'           => $post['catATSBaru_edit'],
      'diterima_igd'      => $post['igdBukan_edit'],
      'ket_diterima_igd'  => (isset($post['igdBukan_edit']) && $post['igdBukan_edit'] == 0) ? $post['ketIgdBukan_edit'] : '',
      'oleh'              => $this->session->userdata('id'),
			'mapping_nomr'      => $mr,
      'mapping_oleh'      => $user
		);

    // echo "<pre>";print_r($data);echo "</pre>";
    $this->db->where('tb_triase_nonpasien.id', $id);
    $this->db->update('keperawatan.tb_triase_nonpasien', $data);

		if ($this->db->trans_status() === false) {
			$this->db->trans_rollback();
			$result = array('status' => 'failed');
		} else {
			$this->db->trans_commit();
			$result = array('status' => 'success');
		}

		echo json_encode($result);
	}

  public function modalHistoryTs()
  {
    $id = $this->input->post('id');
    $jenisKunjunganTriase = $this->masterModel->referensi(485);
    $PENGGUNAAN_O2 = $this->masterModel->referensi(129);
    $atsTriase1 = $this->masterModel->referensi(479);
    $atsTriase2 = $this->masterModel->referensi(480);
    $atsTriase3 = $this->masterModel->referensi(481);
    $atsTriase4 = $this->masterModel->referensi(482);
    $atsTriase5 = $this->masterModel->referensi(483);
    $triaseIgd = $this->masterModel->referensi(486);
    $dataTriase = $this->FormTriaseModel->detailTriasebaru($id)->row_array();

    $data = array(
      'id' => $id,            
      'jenisKunjunganTriase' => $jenisKunjunganTriase,
      'PENGGUNAAN_O2' => $PENGGUNAAN_O2,
      'atsTriase1' => $atsTriase1,
      'atsTriase2' => $atsTriase2,
      'atsTriase3' => $atsTriase3,
      'atsTriase4' => $atsTriase4,
      'atsTriase5' => $atsTriase5,
      'triaseIgd' => $triaseIgd,
      'apakahInginMasukEws' => $this->masterModel->referensi(1162),
      'kesadaran' => $this->masterModel->referensi(5),
      'skriningNyeri' => $this->FormTriaseModel->referensi_nyeri(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'efeksampingNRS' => $this->masterModel->referensi(118),
      'dataTriase' => $dataTriase
    );

    $this->load->view('rekam_medis/triase/modalPreview', $data);
  }

}