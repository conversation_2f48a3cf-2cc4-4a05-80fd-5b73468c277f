<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TbBb extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    $this->load->model(array('rekam_medis/TbBbModel'));
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $result = $this->TbBbModel->get_table(true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $result
        )
        );
      }
    }
  }
}

/* End of file TbBb */
/* Location: ./application/controllers/rekam_medis/TbBb */