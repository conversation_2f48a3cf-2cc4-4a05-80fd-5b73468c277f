<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPraSedasiModel extends MY_Model
{
  protected $_table_name = 'medis.tb_pengkajian_pra_sedasi';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s <PERSON>ajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('medis.tb_pengkajian_pra_sedasi', $data);
  }

  public function ubah($id, $data)
  {
    $this->db->where('tb_pengkajian_pra_sedasi.id_pra', $id);
    $this->db->update('medis.tb_pengkajian_pra_sedasi', $data);
  }

  public function jumlah($nomr)
  {
    $this->db->select('r.id');
    $this->db->from('keperawatan.tb_riwayat_kesehatan_sebelumnya r');
    $this->db->join('pendaftaran.kunjungan k', 'r.nokun = k.NOMOR', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'k.NOPEN = pp.NOMOR', 'left');
    $this->db->where('r.status', 1);
    $this->db->where('pp.NORM', $nomr);
    $query = $this->db->get();
    return $query->num_rows();
  }

  public function history($nomr)
  {
    $this->db->select('rks.id, rks.created_at, rks.nokun, rks.jenis');
    $this->db->from('keperawatan.tb_riwayat_kesehatan_sebelumnya rks');
    $this->db->join('pendaftaran.kunjungan k', 'rks.nokun = k.NOMOR', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'k.NOPEN = pp.NOMOR', 'left');
    $this->db->join('master.pasien mp', 'pp.NORM = mp.NORM', 'left');
    $this->db->where('pp.NORM', $nomr);
    $this->db->where('rks.status', 1);
    $this->db->order_by('rks.created_at', 'DESC');

    $query = $this->db->get();
    return $query;
  }

  public function detail($id, $dataSource)
  {
    $this->db->select(
      'ppsa.id, ppsa.id_pra, ppsa.nokun, ppsa.hilangnya_gigi, ppsa.masalah_leher, ppsa.denyut_jantung, ppsa.batuk,
      ppsa.sesak, ppsa.menderita_infeksi, ppsa.saluran_napas, ppsa.sakit_dada, ppsa.muntah, ppsa.pingsan, ppsa.stroke,
      ppsa.kejang, ppsa.sedang_hamil, ppsa.kelainan_tulang, ppsa.obesitas, ppsa.keterangan_kajian_sistem,
      ppsa.skor_mallampati, ppsa.gigi_palsu, ppsa.jantung, ppsa.jantung_jelaskan, ppsa.paru_paru,
      ppsa.paru_paru_jelaskan, ppsa.abdomen, ppsa.abdomen_jelaskan, ppsa.tulang_belakang,
      ppsa.tulang_belakang_jelaskan, ppsa.ekstremitas, ppsa.ekstremitas_jelaskan, ppsa.neurologi,
      ppsa.neurologi_jelaskan, ppsa.keterangan_pemeriksaan_fisik, ppsa.hbht, ppsa.glukosa, ppsa.naci, ppsa.leukosit,
      ppsa.tes_kehamilan, ppsa.ureum, ppsa.pt, ppsa.ekg, ppsa.kreatinin, ppsa.trombosit, ppsa.kallium, ppsa.rothorax,
      ppsa.keterangan_pemeriksaan_penunjang, ppsa.diagnosis, ppsa.klasifikasi_asa, ppsa.penyulit_sedasi,
      ppsa.penyulit_sedasi_lain, ppsa.tindak_lanjut, ppsa.tindak_lanjut_jelaskan, ppsa.teknik_sedasi, ppsa.teknik_ga,
      ppsa.teknik_regional, ppsa.teknik_lain_lain, ppsa.teknik_hipotensi, ppsa.teknik_khusus,
      ppsa.keterangan_teknik_khusus, ppsa.monitoring, ppsa.ekg_lead_ket, ppsa.cvp_ket, ppsa.arteri_line_ket,
      ppsa.monitoring_lain, ppsa.alat_khusus, ppsa.pilihan_alat_khusus, ppsa.alat_khusus_sebutkan, ppsa.pasca_sedasi,
      ppsa.pasca_sedasi_lain, ppsa.puasa_mulai, ppsa.pre_medikasi, ppsa.transportasi, ppsa.rencana,
      ppsa.catatan_persiapan, ppsa.oleh, tv.td_sistolik, tv.td_diastolik, tv.nadi, tv.pernapasan, tv.suhu, rks.jenis'
    );
    $this->db->from('medis.tb_pengkajian_pra_sedasi ppsa');
    $this->db->join('keperawatan.tb_riwayat_kesehatan_sebelumnya rks', 'rks.id = ppsa.id_pra', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'tv.ref = rks.id');
    $this->db->where('rks.id', $id);
    $this->db->where('tv.data_source', $dataSource);

    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file PengkajianPraSedasiModel.php */
/* Location: ./application/models/sedasi/PengkajianPraSedasiModel.php */