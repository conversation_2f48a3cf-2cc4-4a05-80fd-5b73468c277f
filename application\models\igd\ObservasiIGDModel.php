<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ObservasiIGDModel extends CI_Model
{
  public function simpanObservasiIGD($data)
  {
    $this->db->insert('keperawatan.tb_observasi_tindakan', $data);
    return $this->db->insert_id();
  }

  public function tampilObservasiIGD($nomr)
  {
    $this->db->select(
      'o.id, o.tanggal, o.jam, o.td_sistolik, o.td_diastolik, o.nadi, o.pernapasan, o.suhu, o.cvp, o.wsd,
      v.variabel kesadaran, o.perifer, o.oral, o.parenteral, o.muntah, o.ngt_pengeluaran, o.bak, o.bab, o.wsd_drain,
      o.tindakan_keperawatan, o.bd_urine, o.iwl, o.balance, o.saturasi, o.oksigen,
      master.getNamaLengkapPegawai(peng.NIP) perawat'
    );
    $this->db->from('keperawatan.tb_observasi_tindakan o');
    $this->db->join('db_master.variabel v', 'v.id_variabel = o.kesadaran', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = o.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = o.oleh', 'left');
    $this->db->where('o.status', 1);
    $this->db->where('p.NORM', $nomr);
    $this->db->order_by('o.updated_at', 'ASC');

    $query = $this->db->get();
    return $query->result_array();
  }
}