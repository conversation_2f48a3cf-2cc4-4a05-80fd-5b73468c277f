<!-- Page-Title -->
<div class="row">
	<div class="col-sm-12">
		<h4 class="page-title">Master Penyedia</h4>
	</div>
</div>
<!-- end page title -->
<div class="row">
	<div class="col-12">
		<div class="card-box">
			<div class="table-responsive">
				<p class="text-muted font-14 m-b-30">
					<button type="button" id="btn-tambah" data-toggle="modal" data-target="#tambahpenyedia" class="btn btn-info">
						<span class="fas fa-user-plus"></span>  Tambah Penyedia
					</button>
				</p>
				<table class="table table-striped table-bordered table-hover" id="penyedia">
					<thead>
						<tr>
							<th>No</th>
							<th>Nama Penyedia</th>
							<th>Alamat</th>
							<th>Telepon</th>
							<th>Fax</th>
							<th width="10%">Aksi</th>
						</tr>
					</thead>
					<tbody>
						<?php $no=0; foreach ($penyedia as $dt){ ?>
							<tr>
								<td><?php echo ++$no;?></td>
								<td><?php echo $dt['NAMA'];?></td>
								<td><?php echo $dt['ALAMAT'];?></td>
								<td><?php echo $dt['TELEPON'];?></td>
								<td><?php echo $dt['FAX'];?></td>
								<td>
									<a
									href="javascript:;"
									data-id="<?php echo $dt['ID'] ?>"
									data-nama="<?php echo $dt['NAMA'] ?>"
									data-alamat="<?php echo $dt['ALAMAT'] ?>"
									data-telepon="<?php echo $dt['TELEPON'] ?>"
									data-fax="<?php echo $dt['FAX'] ?>"
									data-toggle="modal" data-target="#edit-data">
									<button  data-toggle="modal" data-target="#ubah-data" class="btn btn-info"><span class='fas fa-edit'></span> Ubah</button>
								</a>
								<!-- <a href="#" class="btn btn-danger">Hapus</a> -->
							</td>
						</tr>
					<?php } ?>
				</tbody>
			</table>
		</div>
		<!-- Modal Tambah -->
		<div id="tambahpenyedia" class="modal fade">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<span>Form tambah penyedia</span>
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
						<h4 class="modal-title">
							<span id="modal-title"></span>
						</h4>
					</div>
					<div class="modal-body">
						<?php echo form_open('inventory/Penyedia/tambah'); ?>
						<div class="form-group row">
							<label class="col-sm-2  col-form-label" for="simpleinput">Nama Penyedia</label>
							<div class="col-sm-10">
								<input type="text" id="simpleinput" name="NAMA" class="form-control" placeholder="Nama Penyedia">
							</div>
						</div>
						<div class="form-group row">
							<label class="col-sm-2  col-form-label" for="simpleinput">Alamat</label>
							<div class="col-sm-10">
								<textarea class="form-control" name="ALAMAT" placeholder="Penyedia" rows="3" id="example-textarea"></textarea>
							</div>
						</div>
						<div class="form-group row">
							<label class="col-sm-2  col-form-label" for="simpleinput">Telepon</label>
							<div class="col-sm-10">
								<input type="text" id="simpleinput" name="TELEPON" class="form-control" placeholder="Telepon">
							</div>
						</div>
						<div class="form-group row">
							<label class="col-sm-2  col-form-label" for="simpleinput">FAX</label>
							<div class="col-sm-10">
								<input type="text" id="simpleinput" name="FAX" class="form-control" placeholder="Fax">
							</div>
						</div>
						<button type="submit" name="submit" class="btn btn-primary"><span class='fas fa-save'></span> Simpan</button>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- END Modal Tambah -->
<!-- Modal Ubah -->
<div aria-hidden="true" aria-labelledby="myModalLabel" role="dialog" tabindex="-1" id="edit-data" class="modal fade">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<span>Form Update penyedia</span>
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">
					<span id="modal-title"></span>
				</h4>
			</div>
			<div class="modal-body">
				<form action="<?php echo base_url('inventory/Penyedia/ubah')?>" method="post">
					<div class="form-group row">
						<input type="hidden" id="id" name="ID">
						<label class="col-sm-2  col-form-label" for="simpleinput">Nama Penyedia</label>
						<div class="col-sm-10">
							<input type="text" id="nama" name="NAMA" class="form-control" placeholder="Nama Penyedia">
						</div>
					</div>
					<div class="form-group row">
						<label class="col-sm-2  col-form-label" for="simpleinput">Alamat</label>
						<div class="col-sm-10">
							<textarea class="form-control" name="ALAMAT" placeholder="Penyedia" rows="3" id="alamat"></textarea>
						</div>
					</div>
					<div class="form-group row">
						<label class="col-sm-2  col-form-label" for="simpleinput">Telepon</label>
						<div class="col-sm-10">
							<input type="text" id="telepon" name="TELEPON" class="form-control" placeholder="Telepon">
						</div>
					</div>
					<div class="form-group row">
						<label class="col-sm-2  col-form-label" for="simpleinput">FAX</label>
						<div class="col-sm-10">
							<input type="text" id="fax" name="FAX" class="form-control" placeholder="Fax">
						</div>
					</div>

					<div class="modal-footer">
						<button class="btn btn-info" type="submit"><span class='fas fa-save'></span>  Simpan</button>
						<button type="button" class="btn btn-warning" data-dismiss="modal"> Batal</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
</div>
<!-- END Modal Ubah -->
<script>
	$(document).ready(function() {
		$('#edit-data').on('show.bs.modal', function (event) {
var div = $(event.relatedTarget) // Tombol dimana modal di tampilkan
var modal          = $(this)

// Isi nilai pada field
modal.find('#id').attr("value",div.data('id'));
modal.find('#nama').attr("value",div.data('nama'));
modal.find('#alamat').html(div.data('alamat'));
modal.find('#telepon').attr("value",div.data('telepon'));
modal.find('#fax').attr("value",div.data('fax'));
});
	});
</script>
<script>
	$(document).ready(function() {
		$('#penyedia').DataTable({
			responsive: true
		});
	});
</script>
