<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DashboardRealisasi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('keuanganModel','masterModel'));
  }

  public function index()
  {
    $dashboardRealisasi = $this->keuanganModel->dashboardRealisasi();
    
    // echo "<pre>";print_r($dPerRuangan);exit();
    $data = array(
      'title'              => 'Halaman Sistem Informasi Manajemen Anggaran',
      'isi'                => 'Keuangan/dashboard/dashboardRealisasi',
      'dashboardRealisasi' => $dashboardRealisasi,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function pilihanBulan()
  {
    $bln1 = date("m",strtotime($this->input->post('bln1')));
    $bln2 = date("m",strtotime($this->input->post('bln2')));

    $dPilihRealisasi = $this->keuanganModel->dashboardPilihRealisasi($bln1,$bln2);
// echo "<pre>";print_r($dPilihRealisasi);exit();
    
    $data = array(
      'dPilihRealisasi' => $dPilihRealisasi,
      'bln1' => $bln1,
      'bln2' => $bln2,
    );

    $this->load->view('Keuangan/dashboard/pilihDashboardRealisasi',$data);
  }

}

/* End of file DashboardRealisasi.php */
/* Location: ./application/controllers/keuangan/DashboardRealisasi.php */