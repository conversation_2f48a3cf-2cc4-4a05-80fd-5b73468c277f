<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SuratSakit extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }
        date_default_timezone_set('Asia/Jakarta');

        $this->load->model(
            array(
                'pengkajianAwalModel',
                'karyawan/SuratSakitModel',
            )
        );
    }

    public function index()
    {
        $nokun = $this->uri->segment(3);
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);
        $data = array(
            'nokun' => $nokun,
            'pasien' => $pasien,
            'jumlah' => $this->SuratSakitModel->hitungSemua($pasien['NORM']),
        );
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/karyawan/suratSakit/index', $data);
    }

    public function ajaxGetUnit()
    {
        $search = $this->input->get('q');
		$results = $this->SuratSakitModel->getUnit($search);

		echo json_encode(['results' => $results]);
    }

    public function laporan()
    {
        $this->load->view('Pengkajian/karyawan/suratSakit/laporan', $data);
    }

    public function simpan()
    {
        $bulan = null;
        $bulan_romawi = null;
        $tahun = null;
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $rules = $this->SuratSakitModel->rules;
            $this->form_validation->set_rules($rules);
            if ($this->form_validation->run() == true) {
                $post = $this->input->post();
                // echo '<pre>';print_r($post);exit();

                // Mulai urutan surat
                $surat_bulan_ini = $this->SuratSakitModel->jumlahSuratPerBulan();
                $no_surat_bulan_ini = ++$surat_bulan_ini;
                // echo '<pre>';print_r($no_surat_bulan_ini);exit();
                // Akhir urutan surat

                // Mulai bulan
                $bulan = date('m');
                if ($bulan == '1') {
                    $bulan_romawi = 'I';
                } elseif ($bulan == '2') {
                    $bulan_romawi = 'II';
                } elseif ($bulan == '3') {
                    $bulan_romawi = 'III';
                } elseif ($bulan == '4') {
                    $bulan_romawi = 'IV';
                } elseif ($bulan == '5') {
                    $bulan_romawi = 'V';
                } elseif ($bulan == '6') {
                    $bulan_romawi = 'VI';
                } elseif ($bulan == '7') {
                    $bulan_romawi = 'VII';
                } elseif ($bulan == '8') {
                    $bulan_romawi = 'VIII';
                } elseif ($bulan == '9') {
                    $bulan_romawi = 'IX';
                } elseif ($bulan == '10') {
                    $bulan_romawi = 'X';
                } elseif ($bulan == '11') {
                    $bulan_romawi = 'XI';
                } elseif ($bulan == '12') {
                    $bulan_romawi = 'XII';
                }
                // Akhir bulan

                $tahun = date('Y');

                // Mulai simpan ke surat sakit
                $data = array(
                    'ID_PEGAWAI' => $post['id_pegawai'],
                    'TGL_MULAI_SAKIT' => $post['mulai'],
                    'TGL_SELESAI_SAKIT' => $post['selesai'],
                    'LAMA_SAKIT' => $post['lama_sakit'],
                    'FILE_SCAN' => null,
                    'PARENT' => $post['id_jabatan'],
                    'STATUS' => 1,
                );
                // echo '<pre>';print_r($data);exit();
                $id = $this->SuratSakitModel->simpan($data);
                // Akhir simpan ke surat sakit

                // Mulai simpan ke log surat sakit
                $data_log = array(
                    'ID_PEGAWAI' => $post['id_pegawai'],
                    'UNIT' => $post['id_unit'],
                    'AKSI' => 1,
                );
                // echo '<pre>';print_r($data_log);exit();
                $id_log = $this->SuratSakitModel->simpanLog($data_log);
                // Akhir simpan ke log surat sakit

                // Mulai simpan diagnosis
                // if (isset($post['diagnosis'])) {
                //     $i = 0;
                //     $data_diagnosis = array();
                //     foreach ($post['diagnosis'] as $d) {
                //         $data_diagnosis[$i] = array(
                //             'id_sakit' => $id,
                //             'diagnosis_icd10' => $d,
                //             'status' => 1,
                //         );
                //         $i++;
                //     }
                //     // echo '<pre>';print_r($data_diagnosis);exit();
                //     $this->SuratSakitModel->simpanDiagnosis($data_diagnosis);
                // }

                if (!empty($post['diagnosis']) || !empty($post['lainnya'])) {
                    $data_diagnosis = [];
                    
                    // Simpan diagnosis ICD-10 jika ada
                    if (!empty($post['diagnosis']) && is_array($post['diagnosis'])) {
                        foreach ($post['diagnosis'] as $d) {
                            $data_diagnosis[] = [
                                'id_sakit' => $id,
                                'diagnosis_icd10' => $d,
                                'status' => 1,
                            ];
                        }
                    }

                    // Simpan diagnosis lainnya jika ada
                    if (!empty($post['lainnya'])) {
                        $data_diagnosis[] = [
                            'id_sakit' => $id,
                            'diagnosis_lain' => $post['lainnya'],
                            'status' => 1,
                        ];
                    }
                    // echo '<pre>';print_r($data_diagnosis);exit();
                    $this->SuratSakitModel->simpanDiagnosis($data_diagnosis);
                }
                // Akhir simpan diagnosis

                // Mulai simpan ke SIMRSKD
                $data_integrasi = array(
                    'nokun' => $post['nokun'],
                    'id_sakit' => $id,
                    'id_log' => $id_log,
                    'no_absen' => $post['no_absen'],
                    'no_surat' => 'LKK/' . $no_surat_bulan_ini . '/SKT/' . $bulan_romawi . '/' . $tahun,
                    'mulai_sakit' => $post['mulai'],
                    'selesai_sakit' => $post['selesai'],
                    'lama_sakit' => $post['lama_sakit'],
                    'sumber_surat' => $post['sumber_surat'],
                    'oleh' => $this->session->userdata['id'],
                    'status' => 1,
                );
                // echo '<pre>';print_r($data_integrasi);exit();
                $this->SuratSakitModel->simpanIntegrasi($data_integrasi);
                // Akhir simpan ke SIMRSKD
            }

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }
            echo json_encode($result);
        }
    }

    public function history()
    {
        $post = $this->input->post();
        $data = array('nomr' => $post['nomr']);
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/karyawan/suratSakit/history', $data);
    }

    public function tabel()
    {
        $nomr = $this->input->post('nomr');
        $history = $this->SuratSakitModel->ambilTabel($nomr);
        $data = array();
        $no = $_POST['start'];
        $cetak = null;
        // echo '<pre>';print_r($history);exit();

        foreach ($history as $h) {
            // Mulai cetak
            if ($h->sumber_surat == 1) {
                $cetak = "<a href='/reports/simrskd/suratsakit/surat_sakit.php?format=pdf&ID=" . $h->id_sakit . "' class='btn btn-sm btn-warning waves-effect' target='_blank'>
                                    <i class='fa fa-print'></i> Cetak
                                </a>";
            } elseif ($h->sumber_surat == 2) {
                $cetak = "<a href='#' class='btn btn-sm btn-secondary waves-effect' data-toggle='tooltip' data-placement='top' title='Surat yang diunggah sendiri untuk sementara hanya bisa diakses lewat Simpeg'>
                            <i class='fa fa-print'></i> Cetak
                        </a>";
            }
            // Akhir cetak

            // Mulai data
            $row = array();
            $row[] = ++$no . '.';
            $row[] = $h->no_surat;
            $row[] = date('d/m/Y, H.i.s', strtotime($h->created_at));
            $row[] = date('d/m/Y', strtotime($h->mulai_sakit));
            $row[] = date('d/m/Y', strtotime($h->selesai_sakit));
            $row[] = $h->pengisi;
            $row[] = $cetak;
            $data[] = $row;
            // Akhir data
        }

        $output = array(
            'draw' => $_POST['draw'],
            'recordsTotal' => $this->SuratSakitModel->hitungSemua($nomr),
            'recordsFiltered' => $this->SuratSakitModel->hitungTersaring($nomr),
            'data' => $data
        );
        echo json_encode($output);
    }
}

/* End of file SuratSakit.php */
/* Location: ./application/controllers/karyawan/SuratSakit.php */