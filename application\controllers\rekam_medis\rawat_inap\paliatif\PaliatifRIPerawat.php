<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PaliatifRIPerawat extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/PengkajianPaliatifRI/PengkajianPaliatifRIModel'));
    }

    public function index()
    {
        $norm                   = $this->uri->segment(6);
        $nopen                  = $this->uri->segment(7);
        $nokun                  = $this->uri->segment(8);
        $id_ruangan             = substr($nokun, 0, 9);
        $hPaliatif              = $this->pengkajianAwalModel->historyPaliatifRI();
        $getNomr                = $this->PengkajianPaliatifRIModel->getNomrPalRI($nopen);
        $getIdEmr               = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
        $getPengkajianPaliatif  = $this->PengkajianPaliatifRIModel->getPengkajianPaliatif($getIdEmr);
        $data = array(
            'norm'                          => $this->uri->segment(6),
            'nopen'                         => $this->uri->segment(7),
            'nokun'                         => $this->uri->segment(8),
            'getPengkajianPaliatif'         => $getPengkajianPaliatif,
            'getNomr'                       => $getNomr,
            'getIdEmr'                      => $getIdEmr,
            'kunjungan_pk'                  => $this->pengkajianAwalModel->kunjungan_pk($norm),
            'listVBPJS'                     => $this->pengkajianAwalModel->listVBPJS($norm),
            'id_ruangan'                    => $id_ruangan,
            'tujuanpalitaif'                => $this->masterModel->referensi(346),
            'riwayatsakitpaliatif'          => $this->masterModel->referensi(347),
            'riwayatpengobatanpaliatif'     => $this->masterModel->referensi(348),
            'riwayatalergipaliatif'         => $this->masterModel->referensi(71),
            'topikbelajarpaliatif'          => $this->masterModel->referensi(349),
            'mediabelajarpaliatif'          => $this->masterModel->referensi(350),
            'kolaborasipaliatif'            => $this->masterModel->referensi(393),
            'klasifikasi'                   => $this->masterModel->referensi(152),
            'stadiumPaliataif'              => $this->masterModel->referensi(153),
            'operasiPali'                   => $this->masterModel->referensi(370),
            'radiasiPali'                   => $this->masterModel->referensi(372),
            'terapiPali'                    => $this->masterModel->referensi(371),
            'kesadaran'                     => $this->masterModel->referensi(5),
            'ESASnyeri'                     => $this->masterModel->referensi(170),
            'ESASlelah'                     => $this->masterModel->referensi(171),
            'ESASmual'                      => $this->masterModel->referensi(172),
            'ESASdepresi'                   => $this->masterModel->referensi(173),
            'ESAScemas'                     => $this->masterModel->referensi(174),
            'ESASmengantuk'                 => $this->masterModel->referensi(175),
            'ESASnafsuMakan'                => $this->masterModel->referensi(176),
            'ESASsehat'                     => $this->masterModel->referensi(177),
            'ESASsesakNapas'                => $this->masterModel->referensi(178),
            'ESASmasalah'                   => $this->masterModel->referensi(179),
            'skriningNyeri'                 => $this->masterModel->referensi(7),
            'statusFungsional'              => $this->masterModel->referensi(18),
            'RisikoJatuhTR'                 => $this->masterModel->referensi(259),
            'skriningResikoJatuhPusing'     => $this->masterModel->referensi(120),
            'skriningResikoJatuhBerdiri'    => $this->masterModel->referensi(121),
            'skriningResikoJatuh6Bulan'     => $this->masterModel->referensi(122),
            'formAsuhanKeperawatan'         => $this->masterModel->referensi(148),
            'tindakan_rad'                  => $this->pengkajianAwalModel->tindakan_rad($norm),
            'efeksampingNRS'                => $this->masterModel->referensi(118),
            'pengkajianNyeriProvocative'    => $this->masterModel->referensi(8),
            'pengkajianNyeriQuality'        => $this->masterModel->referensi(9),
            'pengkajianNyeriTime'           => $this->masterModel->referensi(12),
            'skalaNyeriNRS'                 => $this->masterModel->referensi(114),
            'skalaNyeriWBR'                 => $this->masterModel->referensi(115),
            'skalaNyeriFLACC'               => $this->masterModel->referensi(123),
            'skalaNyeriBPS'                 => $this->masterModel->referensi(133),
            'hPaliatif'                     => $hPaliatif,
        );
        $this->load->view('rekam_medis/rawat_inap/paliatif/FormPaliatifPerawat', $data);
    }
}
