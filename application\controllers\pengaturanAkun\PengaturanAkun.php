<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>gaturanAkun extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengaturanAkun/PengaturanAkunModel'));
  }

  public function index()
  {
    $oleh = $this->session->userdata('id');
    $namaPegawai = $this->PengaturanAkunModel->namaPegawai($oleh);
    $cekKait = $this->PengaturanAkunModel->cekSudahKaitBelum($oleh);
    $listKinerjaPengkajian = $this->PengaturanAkunModel->listKinerjaPengkajian($namaPegawai['id_pegawai_simpeg']);
    $jenisIsiKinerja = $this->PengaturanAkunModel->jenisIsiKinerja($oleh);

    $data = array(
      'title'       => 'Pengaturan Akun',
      'isi'         => 'PengaturanAkun/index',
      'namaPegawai' => $namaPegawai,
      'cekKait' => $cekKait['JUMLAH'],
      'cekKaitStatus' => $cekKait['STATUS'],
      'dataLink' => $cekKait,
      'listKinerjaPengkajian' => $listKinerjaPengkajian,
      'jenisIsiKinerja' => $jenisIsiKinerja,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function cekData()
  {
    $post = $this->input->post();
    $username = $this->input->post('username');
    $password = $this->input->post('password');
    $idLinkBind = $this->input->post('idLinkBind');
    $oleh = $this->session->userdata('id');
    $cekUser = $this->PengaturanAkunModel->cekData($username, $password);
    $jumlahUser = $cekUser['JUMLAH'];
    if($jumlahUser != 0)
    {
      if(password_verify($password, $cekUser['PASSWORD'])){
        $data = array(
          'id_pengguna'           => $oleh,
          'id_pegawai_simpeg'     => $cekUser['ID_PEGAWAI'],
          'no_absen'              => $username,
          'status'                => 1,
        );
        // echo "<pre>";print_r($data);exit();
        if($idLinkBind != "")
        {
          $simpanData = $this->PengaturanAkunModel->simpanUnlinkSimpeg($oleh, $data);
        }else{
          $simpanData = $this->PengaturanAkunModel->simpanLinkSimpeg($data);
        }
      }else{
        $jumlahUser = 0;
      }
    }
    echo json_encode($jumlahUser);
  }

  public function cekDataUnbind()
  {
    $post = $this->input->post();
    $idLinkUnbind = $this->input->post('idLinkUnbind');
    $oleh = $this->session->userdata('id');
    $cekUser = $this->PengaturanAkunModel->cekSudahKaitBelum($oleh);
    $data = array(
      'status'    => 0
    );
    // echo "<pre>";print_r($data);exit();
    $simpanData = $this->PengaturanAkunModel->simpanUnlinkSimpeg($oleh, $data);
  }

  public function simpanMappingKinerja()
  {
    $post = $this->input->post();
    $oleh = $this->session->userdata('id');
    $cekMapping = $this->PengaturanAkunModel->cekMapping($oleh);

    $dataKinerja = array();
    $indexKinerja = 0;
    if (isset($post['pilihKinerjaMappingAkun'])) {
      foreach ($post['pilihKinerjaMappingAkun'] as $input) {
        if ($post['pilihKinerjaMappingAkun'][$indexKinerja] != "") {
          $idPilih = explode("^", $post['pilihKinerjaMappingAkun'][$indexKinerja]);
          array_push(
            $dataKinerja, array(
              'id_pengguna_simrskd' => $oleh,
              'jenis' => $idPilih[1],
              'kinerja' => $idPilih[0],
              'status' => 1,
            )
          );
        }
        $indexKinerja++;
      }
    }
    // echo "<pre>";print_r($dataKinerja);echo "</pre>";exit();
    if($cekMapping['JUMLAH'] > 0)
    {
      $this->db->delete('akses_simrskd.tb_mapping', array('id_pengguna_simrskd' => $oleh));
      foreach ($dataKinerja as $key => $value) {
        $this->db->replace('akses_simrskd.tb_mapping', $value, 'id_pengguna_simrskd');
      }
    }else{
      if (isset($post['pilihKinerjaMappingAkun'])) {
        $this->db->insert_batch('akses_simrskd.tb_mapping', $dataKinerja);
      }
    }
    
  }

}

/* End of file Profile.php */
/* Location: ./application/controllers/Profile.php */
