<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PenolakanTindakanKedokteranModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s <PERSON>ajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('pp.NORM, tic.id, tic.nokun, master.getNamaLengkapPegawai(ap.NIP) oleh, ptk.status,
        master.getNamaLengkapPegawai(md.NIP) do<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>, tic.created_at tanggal, tic.jenis_informed_consent');
        $this->db->from('db_informed_consent.tb_informed_consent tic');
        $this->db->join('db_informed_consent.tb_form_penolakan_tk ptk','ptk.id_informed_consent = tic.id','LEFT');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = tic.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pp','pp.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('aplikasi.pengguna ap','ap.ID = tic.oleh','LEFT');
        $this->db->join('master.dokter md','md.ID = tic.dokter_pelaksana','LEFT');
        $this->db->where('tic.jenis_informed_consent',4924);
        $this->db->where('pp.NORM',$this->input->post('nomr'));
        $this->db->order_by('tic.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function simpanInformedConsent($data)
    {
      $this->db->insert('db_informed_consent.tb_informed_consent', $data);
      return $this->db->insert_id();
    }

    public function simpanPenolakanTK($data)
    {
      $this->db->insert('db_informed_consent.tb_form_penolakan_tk', $data);
    }

    public function simpanPenolakanTidakanKedokteran($data)
    {
      $this->db->insert('db_informed_consent.tb_penolakan_tindakan_kedokteran', $data);
    }

    public function ubahInformedConcent($id, $dataInformedConsent)
    {
      $this->db->where($id);
      $this->db->update('db_informed_consent.tb_informed_consent', $dataInformedConsent);
    }

    public function ubahPenolakanTK($id, $dataPenolakanTK)
    {
      $this->db->where($id);
      $this->db->update('db_informed_consent.tb_form_penolakan_tk', $dataPenolakanTK);
    }

    public function ubahPenolakanTidakanKedokteran($id, $dataPenolakanTindakanKedokteran)
    {
      $this->db->where($id);
      $this->db->update('db_informed_consent.tb_penolakan_tindakan_kedokteran', $dataPenolakanTindakanKedokteran);
    }

    public function getPengkajian($id)
    {
      $query = $this->db->query(
        'SELECT tic.id id_tic, tic.nokun, tic.jenis_informed_consent, tic.penerima_informasi, tic.dokter_pelaksana, tic.status,
        ptk.id id_ptk, ptk.kunjungan, ptk.tanggal, ptk.diagnosis, ptk.dasar_diagnosis, ptk.tindakan_kedokteran,
        ptk.indikasi_tindakan, ptk.tata_cara, ptk.tujuan_tindakan, ptk.tujuan_pengobatan, ptk.risiko, ptk.komplikasi,
        ptk.prognosis, ptk.alternatif_risiko, ptk.lainnya, ptk.ttd_menerangkan, ptk.ttd_menerima, tptk.id id_tptk,
        tptk.nama_keluarga, tptk.umur_keluarga, tptk.jk_keluarga, tptk.alamat_keluarga, tptk.tindakan,
        tptk.hub_keluarga_dgn_pasien, tptk.tanggal_penolakan, tptk.ttd_menyatakan, tptk.ttd_saksi_keluarga,
        tptk.ttd_saksi_rumah_sakit, tptk.saksi_keluarga, tptk.saksi_rumah_sakit, tptk.status_penolakan,
        master.getNamaLengkap(pp.NORM) nama_pasien, master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR) umur,
        mp.JENIS_KELAMIN jk, master.getNamaLengkapPegawai(ap2.NIP) saksi_rs
        
        FROM db_informed_consent.tb_informed_consent tic
        LEFT JOIN db_informed_consent.tb_form_penolakan_tk ptk on ptk.id_informed_consent = tic.id
        LEFT JOIN pendaftaran.kunjungan pk on pk.NOMOR = tic.nokun
        LEFT JOIN pendaftaran.pendaftaran pp on pp.NOMOR = pk.NOPEN
        LEFT JOIN aplikasi.pengguna ap on ap.ID = tic.oleh
        LEFT JOIN master.dokter md on md.ID = tic.dokter_pelaksana
        LEFT JOIN db_informed_consent.tb_penolakan_tindakan_kedokteran tptk on tptk.id_informed_consent = tic.id
        LEFT JOIN master.pasien mp on mp.NORM = pp.NORM
        LEFT JOIN aplikasi.pengguna ap2 on ap2.ID = tptk.saksi_rumah_sakit
        where tic.id ="'.$id.'" '
      );
      return $query->row_array();
    }


}
