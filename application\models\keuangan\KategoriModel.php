<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class KategoriModel extends MY_Model{
	protected $_table_name = 'db_keuangan.kategori';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
    protected $_order_by_type = 'DESC';


	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('*');
        $this->db->from('db_keuangan.kategori k');

        if($this->input->get('q')){
			$this->db->like('k.URAIAN',$this->input->get('q'));	
		}
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
