<?php
defined('BASEPATH') or exit('No direct script access allowed');

class HADS extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/paliatif/HADSModel'));
    }

    public function index()
    {
        $norm = $this->uri->segment(6);
        $nopen = $this->uri->segment(7);
        $nokun = $this->uri->segment(8);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = [
            'norm' => $norm,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'getNomr' => $getNomr,

            // Mu<PERSON>tanyaan
            // 'tegang' => $this->HADSModel->getHADS(1831),
            // 'menikmati' => $this->HADSModel->getHADS(1832),
            // 'perasaanTakut' => $this->HADSModel->getHADS(1833),
            // 'tertawa' => $this->HADSModel->getHADS(1834),
            // 'khawatir' => $this->HADSModel->getHADS(1835),
            // 'gembira' => $this->HADSModel->getHADS(1836),
            // 'dudukTenang' => $this->HADSModel->getHADS(1837),
            // 'merasaLambat' => $this->HADSModel->getHADS(1838),
            // 'menakutkan' => $this->HADSModel->getHADS(1839),
            // 'kehilanganMinat' => $this->HADSModel->getHADS(1840),
            // 'merasaLelah' => $this->HADSModel->getHADS(1841),
            // 'senangHati' => $this->HADSModel->getHADS(1842),
            // 'panik' => $this->HADSModel->getHADS(1843),
            // 'tdkMenikmati' => $this->HADSModel->getHADS(1844),
            // Akhir Pertanyaan

            // Mulai Pilihan Jawaban
            'tegang' => $this->masterModel->referensi(1831),
            'menikmati' => $this->masterModel->referensi(1832),
            'perasaanTakut' => $this->masterModel->referensi(1833),
            'tertawa' => $this->masterModel->referensi(1834),
            'khawatir' => $this->masterModel->referensi(1835),
            'gembira' => $this->masterModel->referensi(1836),
            'dudukTenang' => $this->masterModel->referensi(1837),
            'merasaLambat' => $this->masterModel->referensi(1838),
            'menakutkan' => $this->masterModel->referensi(1839),
            'kehilanganMinat' => $this->masterModel->referensi(1840),
            'merasaLelah' => $this->masterModel->referensi(1841),
            'senangHati' => $this->masterModel->referensi(1842),
            'panik' => $this->masterModel->referensi(1843),
            'tdkMenikmati' => $this->masterModel->referensi(1844),
            // Akhir Pilihan Jawaban

            // Mulai Nilai Anxiety & Depression
            'nama_anxiety' => $this->HADSModel->getHADS(1845),
            'nilai_anxiety' => $this->HADSModel->referensi(1845),
            'nama_depression' => $this->HADSModel->getHADS(1846),
            'nilai_depression' => $this->HADSModel->referensi(1846),
            'nilai_anxietyR' => $this->HADSModel->referensi(1847),
            // Akhir Nilai Anxiety & Depression
        ];

        $this->load->view('rekam_medis/rawat_inap/paliatif/HADS/index', $data);
    }

    public function simpanHADS()
    {
        $this->db->trans_begin();
        date_default_timezone_set('Asia/Jakarta');

        $post = $this->input->post();

        if ($post['method'] == 'insert') {
            $dataHADS = array(
                'norm'           => $post['norm'],
                'nopen'          => $post['nopen'],
                'nokun'          => $post['nokun'],
                'a_1'            => $post['tegang'],
                'd_1'            => $post['menikmati'],
                'a_2'            => $post['perasaanTakut'],
                'd_2'            => $post['tertawa'],
                'a_3'            => $post['khawatir'],
                'd_3'            => $post['gembira'],
                'a_4'            => $post['dudukTenang'],
                'd_4'            => $post['merasaLambat'],
                'a_5'            => $post['menakutkan'],
                'd_5'            => $post['kehilanganMinat'],
                'a_6'            => $post['merasaLelah'],
                'd_6'            => $post['senangHati'],
                'a_7'            => $post['panik'],
                'd_7'            => $post['tdkMenikmati'],
                'total_anxiety'     => $post['total_anxiety'],
                'total_depression'  => $post['total_depression'],
                'kesimpulan'     => $post['kesimpulan'],
                'created_at'     => date('Y-m-d H:i:s'),
                'created_by'     => $this->session->userdata('id'),
            );

            // echo "<pre>";print_r($dataHADS);echo "</pre>";
            $this->db->insert('medis.tb_hospital_anxiety_and_depression_scale', $dataHADS);

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }
        } elseif ($post['method'] == 'update') {
            $dataHADSedit = array(
                'norm'           => $post['norm'],
                'nopen'          => $post['nopen'],
                'nokun'          => $post['nokun'],
                'a_1'            => $post['view_tegang'],
                'd_1'            => $post['view_menikmati'],
                'a_2'            => $post['view_perasaanTakut'],
                'd_2'            => $post['view_tertawa'],
                'a_3'            => $post['view_khawatir'],
                'd_3'            => $post['view_gembira'],
                'a_4'            => $post['view_dudukTenang'],
                'd_4'            => $post['view_merasaLambat'],
                'a_5'            => $post['view_menakutkan'],
                'd_5'            => $post['view_kehilanganMinat'],
                'a_6'            => $post['view_merasaLelah'],
                'd_6'            => $post['view_senangHati'],
                'a_7'            => $post['view_panik'],
                'd_7'            => $post['view_tdkMenikmati'],
                'total_anxiety'     => $post['vtotal_anxiety'],
                'total_depression'  => $post['vtotal_depression'],
                'kesimpulan'     => $post['vkesimpulan'],
                'updated_at'     => date('Y-m-d H:i:s'),
                'updated_by'     => $this->session->userdata('id'),
            );
            $this->db->where('id', $post['idHADS']);
            $this->db->update('medis.tb_hospital_anxiety_and_depression_scale', $dataHADSedit);

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }
        }

        echo json_encode($result);
    }


    public function historyHADS()
    {
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));

        $nomr = $this->input->post('nomr');
        // $nomr = $this->uri->segment(6);
        $listHADS = $this->HADSModel->listHistoryHADS($nomr);

        $data = array();
        $no = 1;
        foreach ($listHADS->result() as $HADS) {

            $button = '<button type="button" href="#modalHADS" class="btn btn-primary btn-block" data-id="' . $HADS->id . '" data-toggle="modal" data-backdrop="static" data-keyboard="false" ><i class="fa fa-pencil-square-o"></i> View</button>';

            $data[] = array(
                $no,
                $HADS->nokun,
                $HADS->created_at,
                ($HADS->updated_at != NULL || $HADS->updated_at != "") ? $HADS->updated_at : '-',
                $HADS->total_anxiety,
                $HADS->total_depression,
                $HADS->total_depression + $HADS->total_anxiety,
                $HADS->kesimpulan,
                $HADS->OLEH,
                ($HADS->updated_by != NULL || $HADS->updated_by != "") ? $HADS->OLEH_UPDATE : '-',
                $button,

            );
            $no++;
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listHADS->num_rows(),
            "recordsFiltered" => $listHADS->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }

    public function modalHADS()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array(
            'id' => $id,
            'hads' => $this->HADSModel->getDataHADS($id),
            'getNomr' => $getNomr,
            // Jawaban
            'tegang' => $this->masterModel->referensi(1831),
            'menikmati' => $this->masterModel->referensi(1832),
            'perasaanTakut' => $this->masterModel->referensi(1833),
            'tertawa' => $this->masterModel->referensi(1834),
            'khawatir' => $this->masterModel->referensi(1835),
            'gembira' => $this->masterModel->referensi(1836),
            'dudukTenang' => $this->masterModel->referensi(1837),
            'merasaLambat' => $this->masterModel->referensi(1838),
            'menakutkan' => $this->masterModel->referensi(1839),
            'kehilanganMinat' => $this->masterModel->referensi(1840),
            'merasaLelah' => $this->masterModel->referensi(1841),
            'senangHati' => $this->masterModel->referensi(1842),
            'panik' => $this->masterModel->referensi(1843),
            'tdkMenikmati' => $this->masterModel->referensi(1844),
            // Nilai
            'nama_anxiety' => $this->HADSModel->getHADS(1845),
            'nilai_anxiety' => $this->HADSModel->referensi(1845),
            'nama_depression' => $this->HADSModel->getHADS(1846),
            'nilai_depression' => $this->HADSModel->referensi(1846),
            'nilai_anxietyR' => $this->HADSModel->referensi(1847),
        );

        $this->load->view('rekam_medis/rawat_inap/paliatif/HADS/view', $data);
    }
}
/* End of file HADS.php */
