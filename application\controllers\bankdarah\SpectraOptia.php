<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SpectraOptia extends CI_Controller
{
 public function __construct()
 {
  parent::__construct();
  if ($this->session->userdata('logged_in') == false) {
    redirect('login');
  }

  if (!in_array(8, $this->session->userdata('akses'))) {
    redirect('login');
  }

  date_default_timezone_set("Asia/Bangkok");
  $this->load->model(array('masterModel', 'pengkajianAwalModel'));
}

public function simpanSpectraOptia()
{
  $post = $this->input->post();
  $nokun = $this->input->post('nokun');
  $oleh = $this->session->userdata("id");

  // tanggal spectra optia
  $date = $this->input->post('tanggalSpectraOptia');
  $tglSpectraOptia = date('Y-m-d', strtotime($date));
  echo $tglSpectraOptia;

  // tanggal tubing set
  $date2 = $this->input->post('expDateTubingSpectraOptia');
  $tglTubingSet = date('Y-m-d', strtotime($date2));
  echo $tglTubingSet;

  // tanggal ACDA
  $date3 = $this->input->post('expDateAcdaSpectraOptia');
  $tglAcda = date('Y-m-d', strtotime($date3));
  echo $tglAcda;

  $dataSpactraOptiaSimpan = array( 
    'nokun'=> $nokun, 
    'tanggal' => $tglSpectraOptia,
    'nama' => isset($post['namaDonorSpectraOptia']) ? $post['namaDonorSpectraOptia'] : "",
    'jenkel' => isset($post['gender']) ? $post['gender'] : "",
    'tinggi' => isset($post['tinggiSpectraOptia']) ? $post['tinggiSpectraOptia'] : "",
    'berat_badan' => isset($post['beratBadanSpectraOptia']) ? $post['beratBadanSpectraOptia'] : "",
    'gol_darah' => isset($post['golDarahSpectraOptia']) ? $post['golDarahSpectraOptia'] : "",
    'tbv' => isset($post['tbvSpectraOptia']) ? $post['tbvSpectraOptia'] : "",
    'ht' => isset($post['htSpectraOptia']) ? $post['htSpectraOptia'] : "",
    'platelet' => isset($post['plateletSpectraOptia']) ? $post['plateletSpectraOptia'] : "",
    'tubing_set_kode' => isset($post['tubingKodeSpectraOptia']) ? $post['tubingKodeSpectraOptia'] : "",
    'tubing_set_tanggal' => $tglTubingSet,
    'acda_kode' => isset($post['acdaKodeSpectraOptia']) ? $post['acdaKodeSpectraOptia'] : "",
    'acda_tanggal' => $tglAcda,
    'mulai' => isset($post['mulaiSpectraOptia']) ? $post['mulaiSpectraOptia'] : "",
    'yield' => isset($post['yieldSpectraOptia']) ? $post['yieldSpectraOptia'] : "",
    'waktu' => isset($post['waktuMenitSpectraOptia']) ? $post['waktuMenitSpectraOptia'] : "",
    'akhir' => isset($post['AkhirSpectraOptia']) ? $post['AkhirSpectraOptia'] : "",
    'total_ac_user' => isset($post['totalAcUsedSpectraOptia']) ? $post['totalAcUsedSpectraOptia'] : "",
    'eor_time' => isset($post['eorTimeSpectraOptia']) ? $post['eorTimeSpectraOptia'] : "",
    'lor_time' => isset($post['lorSpectraOptia']) ? $post['lorSpectraOptia'] : "",
    'post_platelete_count' => isset($post['postPleteleteCountSpectraOptia']) ? $post['postPleteleteCountSpectraOptia'] : "",
    'post_hct' => isset($post['postHctSpectraOptia']) ? $post['postHctSpectraOptia'] : "",
    'actual_ac_donor' => isset($post['actualAcDonorSpectraOptia']) ? $post['actualAcDonorSpectraOptia'] : "",
    'blood_volume_process' => isset($post['bloodVolumeProcessSpectraOptia']) ? $post['bloodVolumeProcessSpectraOptia'] : "",
    'volume' => isset($post['volumeSpectraOptia']) ? $post['volumeSpectraOptia'] : "",
    'yield_of_platelets' => isset($post['yopSpectraOptia']) ? $post['yopSpectraOptia'] : "",
    'volume_of_ac_in_platelets' => isset($post['voAcInPlateletsSpectraOptia']) ? $post['voAcInPlateletsSpectraOptia'] : "",
    'nama_operator' => isset($post['operator']) ? $post['operator'] : "",
    'tanda_tangan' => file_get_contents($post['tandaTanganSpectraOptia']),
    'oleh' => $oleh,
  );
  // echo "<pre>";print_r($dataSpactraOptiaSimpan);exit();
  // $this->db->insert('keperawatan.tb_spectraoptia', $dataSpactraOptiaSimpan);

  if (!empty($post['id_spectra_optia'])) {
    $this->db->replace('keperawatan.tb_spectraoptia', $dataSpactraOptiaSimpan);
    $result = array('status' => 'success', 'pesan' => 'ubah');
  }else {
    $this->db->insert('keperawatan.tb_spectraoptia', $dataSpactraOptiaSimpan);
    $result = array('status' => 'success');
  }
  echo json_encode($result);

  $dataCatatanSpectraOptia = array();
  $indexCatatanSpectraOptia = 0;
  if (isset($post['catatanSpectraOptia'])) {
    foreach ($post['catatanSpectraOptia'] as $input) {
      if ($post['catatanSpectraOptia'][$indexCatatanSpectraOptia] != "") {
        array_push(
          $dataCatatanSpectraOptia, array(
            'nokun' => $nokun,
            'value' => $post['catatanSpectraOptia'][$indexCatatanSpectraOptia],
          )
        );
      }
      $indexCatatanSpectraOptia++;
    }
  }

  $this->db->insert_batch('keperawatan.tb_catatan_spectraoptia', $dataCatatanSpectraOptia);

}
}
/* End of file Dashboard.php */
/* Location: ./application/controllers/Dashboard.php */
