<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemberianDanPemantauanDarah extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('pengkajianAwalModel', 'masterModel', 'bankdarah/PemberianDanPemantauanModel', 'bankdarah/KesesuaianKantongModel', 'bankdarah/ReaksiTransfusiModel', 'bankdarah/PemantauanPemberianDarahModel'));
        $this->load->library('whatsapp');

    }

    public function index()
    {
        $norm = $this->uri->segment(6);
        $nopen = $this->uri->segment(7);
        $nokun = $this->uri->segment(8);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $data = array(
            'norm' => $norm,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'getNomr' => $getNomr,
            'surat_izin' => $this->masterModel->referensi(879),
            'kesesuaian_instruksi' => $this->masterModel->referensi(880),
            'jenis_darah' => $this->masterModel->referensi(881),
            'jenis_darah_sesuai' => $this->masterModel->referensi(883),
            'rhesus' => $this->masterModel->referensi(882),
            'golongan_darah' => $this->masterModel->referensi(756),
            'golongan_darah_sesuai' => $this->masterModel->referensi(884),
            'rhesus_sesuai' => $this->masterModel->referensi(885),
            'formulir_pmi' => $this->masterModel->referensi(888),
            'label_darah' => $this->masterModel->referensi(886),
            'identitas_pasien' => $this->masterModel->referensi(887),
            'listPegawai' => $this->masterModel->listPegawai(),
            'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
            'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
            'listPerawat' => $this->masterModel->listPerawat(),
        );
        $this->load->view('rekam_medis/bankDarah/PemberianDanPemantauanDarah', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah') {
                $rules = $this->PemberianDanPemantauanModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPemberianDanPemantauan = array(
                        'kunjungan' => $post['nokun'],
                        'tanggal' => $post['tanggal'],
                        'rawat' => $post['ruangan'],
                        'ruangan' => $post['ruangan'] == 1 ? $post['pemberian_jalan_ruangan'] : $post['pemberian_inap_ruangan'],
                        'surat_ijin' => $post['surat_ijin'],
                        'kesesuaian_intruksi' => $post['kesesuaian_intruksi'],
                        'jenis_darah' => $post['jenis_darah'],
                        'jenis_darah_lain' => $post['jenis_darah'] == '3057' ? $post['jenis_darah_lainnya'] : null,

                        'volume' => $post['volume'],
                        'jenis_darah_sesuai' => $post['jenis_darah_sesuai'],
                        'golongan_darah' => $post['golongan_darah_pemberian'],
                        'golongan_darah_sesuai' => $post['golongan_darah_sesuai'],
                        'rhesus' => $post['rhesus'],
                        'rhesus_sesuai' => $post['rhesus_sesuai'],
                        'formulir_pmi' => $post['formulir_pmi'],
                        'petugas_bank_darah' => $post['petugas_bank_darah'],
                        'petugas_ruangan' => $post['petugas_ruangan'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    if (!empty($post['id'])) {
                        $this->PemberianDanPemantauanModel->update($dataPemberianDanPemantauan, array('id' => $post['id']));
                    } else {
                        $this->PemberianDanPemantauanModel->insert($dataPemberianDanPemantauan);
                    }


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'tambahKesesuaianNomor') {
                $rules = $this->KesesuaianKantongModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPemberianDanPemantauan = array(
                        'kunjungan' => $post['nokun'],
                        'nomor_kantong' => $post['nomor_kantong'],
                        'golongan_darah' => $post['golongan_darah_kesesuaian'],
                        'label_darah' => $post['label_darah'],
                        'identitas_pasien' => $post['identitas_pasien'],
                        'perawat_1' => $post['perawat1'],
                        'perawat_2' => $post['perawat2'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    if (!empty($post['id'])) {
                        $this->KesesuaianKantongModel->update($dataPemberianDanPemantauan, array('id' => $post['id']));
                    } else {
                        $this->KesesuaianKantongModel->insert($dataPemberianDanPemantauan);
                    }


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'tambahReakasiTransfusi') {
                $rules = $this->ReaksiTransfusiModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataReaksiTransfusi = array(
                        'kunjungan' => $post['nokun'],
                        'nomor_kantong' => $post['nomor_kantong'],
                        'reaksi_tindakan' => $post['reaksi_tindakan'],
                        'tindakan' => $post['tindakan'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    $this->db->replace('keperawatan.tb_reaksi_transfusi', $dataReaksiTransfusi);


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                        $selamat = (date('H') >= '05' && date('H') < '10' ? 'Pagi' : (date('H') >= '10' && date('H') < '15' ? 'Siang' : (date('H') >= '15' && date('H') < '19' ? 'Sore' : 'Malam')));
                        $norm = $post['nomr'];
                        $ruangan = $post['ruangan'];
                        $noKantong = $post['nomor_kantong'];
                        $reaksiTindakan = $post['reaksi_tindakan'];
                        $tindakan = $post['tindakan'];
                        $tanggal = date('d-m-Y H:i:s');
                        $namaPasien = $this->masterModel->getPasien($norm);
                        $pengirim = $this->masterModel->getPenguna($this->session->userdata('id'));
                        $nomor = '+6285157010183';
                        try {
                            $this->whatsapp->send($nomor, array($selamat, "kami sampaikan", "Pasien dengan no mr *$norm* atas nama *$namaPasien* di ruangan *$ruangan*, mempunyai reaksi transfusi. No Kantong: *$noKantong* , Reaksi Transfusi: *$reaksiTindakan*, Tindakan yang dilakukan: *$tindakan* pada tanggal *$tanggal* oleh *$pengirim*"));
                        } catch (Exception $e) {
                        }
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'tambahDaftarPemantauan') {
                $rules = $this->PemantauanPemberianDarahModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPemantauanDarah = array(
                        'kunjungan' => $post['nokun'],
                        'bag_ke' => $post['bag_ke'],
                        'tanggal' => $post['tanggal'],
                        'jam' => $post['jam'],
                        'jenis_tranfusi' => $post['jenis_tranfusi'],
                        'sistolik' => $post['jenis_tranfusi'] == 1 ? $post['sistolik'] : 0,
                        'diastolik' => $post['jenis_tranfusi'] == 1 ? $post['diastolik'] : 0,
                        'n' => $post['jenis_tranfusi'] == 1 ? $post['n'] : 0,
                        'rr' => $post['jenis_tranfusi'] == 1 ? $post['rr'] : 0,
                        's' => $post['jenis_tranfusi'] == 1 ? $post['s'] : 0,

                        'sistolik15' => $post['jenis_tranfusi'] == 2 ? $post['sistolik15'] : 0,
                        'diastolik15' => $post['jenis_tranfusi'] == 2 ? $post['diastolik15'] : 0,
                        'n15' => $post['jenis_tranfusi'] == 2 ? $post['n15'] : 0,
                        'rr15' => $post['jenis_tranfusi'] == 2 ? $post['rr15'] : 0,
                        's15' => $post['jenis_tranfusi'] == 2 ? $post['s15'] : 0,

                        'sistolik30' => $post['jenis_tranfusi'] == 3 ? $post['sistolik30'] : 0,
                        'diastolik30' => $post['jenis_tranfusi'] == 3 ? $post['diastolik30'] : 0,
                        'n30' => $post['jenis_tranfusi'] == 3 ? $post['n30'] : 0,
                        'rr30' => $post['jenis_tranfusi'] == 3 ? $post['rr30'] : 0,
                        's30' => $post['jenis_tranfusi'] == 3 ? $post['s30'] : 0,
                        'oleh' => $this->session->userdata("id"),
                    );

                    if (!empty($post['id'])) {
                        $this->PemantauanPemberianDarahModel->update($dataPemantauanDarah, array('id' => $post['id']));
                    } else {
                        $this->PemantauanPemberianDarahModel->insert($dataPemantauanDarah);
                    }


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'ambilKesesuaianFormulir') {
                $post = $this->input->post(NULL, TRUE);
                $dataPemberianDanPemantauan = $this->PemberianDanPemantauanModel->get($post['id'], true);
                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $dataPemberianDanPemantauan
                    )
                );
            } else if ($param == 'count') {
                $result = $this->PemberianDanPemantauanModel->get_count();
                echo json_encode($result);
            } else if ($param == 'hapusKesesuaianNomor') {
                $post = $this->input->post(NULL, TRUE);
                if (!empty($post['id'])) {
                    $data = array(
                        'status' => '0',
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'deleted_by' => $this->session->userdata('id'),
                    );
                    $this->KesesuaianKantongModel->update($data, array('id' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            } else if ($param == 'hapusPemantauanDarah') {
                $post = $this->input->post(NULL, TRUE);
                if (!empty($post['id'])) {
                    $data = array(
                        'status' => '0',
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'deleted_by' => $this->session->userdata('id'),
                    );
                    $this->PemantauanPemberianDarahModel->update($data, array('id' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            } else if ($param == 'hapusReaksiTranfusi') {
                $post = $this->input->post(NULL, TRUE);
                if (!empty($post['id'])) {
                    $data = array(
                        'status' => '0',
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'deleted_by' => $this->session->userdata('id'),
                    );
                    $this->ReaksiTransfusiModel->update($data, array('id' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            } else if ($param == 'cekPemantauanDarah') {
                $post = $this->input->post(NULL, TRUE);
                $dataPemantauanDarah = $this->PemberianDanPemantauanModel->cekPemantauanDarah();
                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $dataPemantauanDarah
                    )
                );
            }  else if ($param == 'getPemantauanDarah') {
                $post = $this->input->post(NULL, TRUE);
                $dataPemberianDanPemantauan = $this->PemantauanPemberianDarahModel->get($post['id'], true);
                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $dataPemberianDanPemantauan
                    )
                );
            }
        }
    }

    public function datatables()
    {
        $result = $this->PemberianDanPemantauanModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_kesesuaian_form_Darah" data-id="' . $row->ID . '"><i class="fa fa-eye"></i> Lihat</a><a href="/reports/simrskd/PemantauanTransfusi/transfusi.php?format=pdf&ID=' .$row->ID . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row->TANGGAL;
            $sub_array[] = $row->RUANGAN_KUNJUNGAN;
            $sub_array[] = $row->DPJP;
            $sub_array[] = $row->USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw" => intval($_POST["draw"]),
            "recordsTotal" => $this->PemberianDanPemantauanModel->total_count(),
            "recordsFiltered" => $this->PemberianDanPemantauanModel->filter_count(),
            "data" => $data
        );
        echo json_encode($output);
    }

    public function datatablesKesesuaianKantong()
    {
        $result = $this->KesesuaianKantongModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-danger btn-block btn-sm hapus_kesesuaian_kantong" data-id="' . $row->id . '"><i class="fa fa-times"></i> Hapus</a>';
            $sub_array[] = $row->nomor_kantong;
            $sub_array[] = $row->golongan_darah;
            $sub_array[] = $row->label_darah;
            $sub_array[] = $row->identitas_pasien;
            $sub_array[] = $row->perawat_1;
            $sub_array[] = $row->perawat_2;


            $data[] = $sub_array;
        }

        $output = array(
            "draw" => intval($_POST["draw"]),
            "recordsTotal" => $this->KesesuaianKantongModel->total_count(),
            "recordsFiltered" => $this->KesesuaianKantongModel->filter_count(),
            "data" => $data
        );
        echo json_encode($output);
    }

    public function datatablesReaksiTransfusi()
    {
        $result = $this->ReaksiTransfusiModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-danger btn-block btn-sm hapus_reaksi_tranfusi" data-id="' . $row->id . '"><i class="fa fa-times"></i> Hapus</a>';
            $sub_array[] = $row->nomor_kantong;
            $sub_array[] = $row->reaksi_tindakan;
            $sub_array[] = $row->tindakan;
            $sub_array[] = $row->oleh;

            $data[] = $sub_array;
        }

        $output = array(
            "draw" => intval($_POST["draw"]),
            "recordsTotal" => $this->ReaksiTransfusiModel->total_count(),
            "recordsFiltered" => $this->ReaksiTransfusiModel->filter_count(),
            "data" => $data
        );
        echo json_encode($output);
    }

    public function datatablesPemantauanPemberianDarah()
    {
        $result = $this->PemantauanPemberianDarahModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-danger btn-block btn-sm hapus_pemantauan_darah" data-id="' . $row->id . '"><i class="fa fa-times"></i> Hapus</a><a class="btn btn-primary btn-block btn-sm ubah_pemantauan_darah" data-id="' . $row->id . '"><i class="fa fa-pencil"></i> Ubah</a>';
            $sub_array[] = $row->bag_ke;
            $sub_array[] = $row->tanggal;
            $sub_array[] = $row->jam;
            $sub_array[] = $row->jenis_tranfusi == 1 ? 'Sebelum Transfusi' : ($row->jenis_tranfusi == 2 ? '15 menit pertama, <br/>selanjutnya 1 jam' : ($row->jenis_tranfusi == 3 ? 'Setelah Transfusi' : '-'));
            $sub_array[] = $row->jenis_tranfusi == 1 ? $row->sistolik : ($row->jenis_tranfusi == 2 ? $row->sistolik15 : ($row->jenis_tranfusi == 3 ? $row->sistolik30 : 'Sebelum : ' . $row->sistolik . '<br/>' . '15 menit : ' . $row->sistolik15 . '<br/>' . 'Sesudah : ' . $row->sistolik30 . '<br/>'));
            $sub_array[] = $row->jenis_tranfusi == 1 ? $row->diastolik : ($row->jenis_tranfusi == 2 ? $row->diastolik15 : ($row->jenis_tranfusi == 3 ? $row->diastolik30 : 'Sebelum : ' . $row->diastolik . '<br/>' . '15 menit : ' . $row->diastolik15 . '<br/>' . 'Sesudah : ' . $row->diastolik30 . '<br/>'));
            $sub_array[] = $row->jenis_tranfusi == 1 ? $row->n : ($row->jenis_tranfusi == 2 ? $row->n15 : ($row->jenis_tranfusi == 3 ? $row->n30 : 'Sebelum : ' . $row->n . '<br/>' . '15 menit : ' . $row->n15 . '<br/>' . 'Sesudah : ' . $row->n30 . '<br/>'));
            $sub_array[] = $row->jenis_tranfusi == 1 ? $row->rr : ($row->jenis_tranfusi == 2 ? $row->rr15 : ($row->jenis_tranfusi == 3 ? $row->rr30 : 'Sebelum : ' . $row->rr . '<br/>' . '15 menit : ' . $row->rr15 . '<br/>' . 'Sesudah : ' . $row->rr30 . '<br/>'));
            $sub_array[] = $row->jenis_tranfusi == 1 ? $row->s : ($row->jenis_tranfusi == 2 ? $row->s15 : ($row->jenis_tranfusi == 3 ? $row->s30 : 'Sebelum : ' . $row->s . '<br/>' . '15 menit : ' . $row->s15 . '<br/>' . 'Sesudah : ' . $row->s30 . '<br/>'));
            $sub_array[] = $row->oleh;

            $data[] = $sub_array;
        }

        $output = array(
            "draw" => intval($_POST["draw"]),
            "recordsTotal" => $this->PemantauanPemberianDarahModel->total_count(),
            "recordsFiltered" => $this->PemantauanPemberianDarahModel->filter_count(),
            "data" => $data
        );
        echo json_encode($output);
    }
}

/* End of file PemberianDanPemantauanDarah.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/bankDarah/PemberianDanPemantauanDarah.php */