<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TrombaferesisHaemo extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $post = $this->input->post();
    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $id = $this->uri->segment(8);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $historyTrombaferesisHaemonetics = $this->pengkajianAwalModel->historyTrombaferesisHaemonetics($nomr);
    if ($id != "") {
      $getTrombaferesisHaemonetics = $this->pengkajianAwalModel->getTrombaferesisHaemonetics($id);
    }

    $data = array
    (
      'getNomr' => $getNomr,
      'historyTrombaferesisHaemonetics' => $historyTrombaferesisHaemonetics,
      'getTrombaferesisHaemonetics' => $id != "" ? $getTrombaferesisHaemonetics : ""
    );
    $this->load->view('Pengkajian/bankdarah/TrombaferesisHaemo/index', $data);
  }

  public function simpanTromHae()
  {
    $post = $this->input->post();
    $id = $this->input->post('idTromHae');
    $nokun = $this->input->post('nokun');
    $date = $this->input->post('tanggalTromHae');
    $tglTromHae = date('Y-m-d', strtotime($date));
    $td_sistolik_tromHae = $this->input->post('td_sistolik_tromHae');
    $td_diastolik_tromHae = $this->input->post('td_diastolik_tromHae');
    $no_donat_tromHae = $this->input->post('no_donat_tromHae');
    $nadi_tromHae = $this->input->post('nadi_tromHae');
    $blood_group_tromHae = $this->input->post('blood_group_tromHae');
    $no_disposable_tromHae = $this->input->post('no_disposable_tromHae');
    $no_seri_tromHae = $this->input->post('no_seri_tromHae');
    $no_lot_tromHae = $this->input->post('no_lot_tromHae');
    $no_batch_tromHae = $this->input->post('no_batch_tromHae');
    $tinggi_cm_tromHae = $this->input->post('tinggi_cm_tromHae');
    $berat_kg_tromHae = $this->input->post('berat_kg_tromHae');
    $volume_darah_tromHae = $this->input->post('volume_darah_tromHae');
    $hct_tromHae = $this->input->post('hct_tromHae');
    $plt_pre_tromHae = $this->input->post('plt_pre_tromHae');
    $targetVol_plasma_tromHae = $this->input->post('targetVol_plasma_tromHae');
    $targetPlt_yield_tromHae = $this->input->post('targetPlt_yield_tromHae');
    $process_vol_tromHae = $this->input->post('process_vol_tromHae');
    $target_siklus_tromHae = $this->input->post('target_siklus_tromHae');
    $target_durasi_tromHae = $this->input->post('target_durasi_tromHae');
    $volDarah_akhir_tromHae = $this->input->post('volDarah_akhir_tromHae');
    $acTerpakai_tromHae = $this->input->post('acTerpakai_tromHae');
    $lamaProsedur_tromHae = $this->input->post('lamaProsedur_tromHae');
    $jumlahSiklus_tromHae = $this->input->post('jumlahSiklus_tromHae');
    $naciTerpakai_tromHae = $this->input->post('naciTerpakai_tromHae');
    $plasmaVol_tromHae = $this->input->post('plasmaVol_tromHae');
    $acPlasmaBag_tromHae = $this->input->post('acPlasmaBag_tromHae');
    $plateletVol_tromHae = $this->input->post('plateletVol_tromHae');
    $acDalamPlt_tromHae = $this->input->post('acDalamPlt_tromHae');
    $estPltYield_tromHae = $this->input->post('estPltYield_tromHae');
    $targetPltYield_tromHae = $this->input->post('targetPltYield_tromHae');
    $jumlahNyata_tromHae = $this->input->post('jumlahNyata_tromHae');
    $jumlahNyata_tromHae_1 = $this->input->post('jumlahNyata_tromHae_1');
    $jumlahNyata_tromHae_2 = $this->input->post('jumlahNyata_tromHae_2');
    $jumlahNyata_tromHae_3 = $this->input->post('jumlahNyata_tromHae_3');
    $komentar_tromHae = $this->input->post('komentar_tromHae');
    $oleh = $this->input->post('pengguna');


    $data = array(
      'nokun'                            => $nokun,
      'tanggal'                          => $tglTromHae,
      'td_sistolik'                          => $td_sistolik_tromHae,
      'td_diastolik'                          => $td_diastolik_tromHae,
      'nadi'                          => $nadi_tromHae,
      'no_disposable'                          => $no_disposable_tromHae,
      'no_lot'                          => $no_lot_tromHae,
      'no_batch'                          => $no_batch_tromHae,
      'no_donation'                          => $no_donat_tromHae,
      'blood_group'                          => $blood_group_tromHae,
      'mechine_noseri'                          => $no_seri_tromHae,
      'tinggi'                          => $tinggi_cm_tromHae,
      'berat'                          => $berat_kg_tromHae,
      'volume_darah'                          => $volume_darah_tromHae,
      'hct'                          => $hct_tromHae,
      'plt_pre_count'                          => $plt_pre_tromHae,
      'target_vol_plasma'                          => $targetVol_plasma_tromHae,
      'target_plt_yield'                          => $targetPlt_yield_tromHae,
      'process_vol'                          => $process_vol_tromHae,
      'target_siklus'                          => $target_siklus_tromHae,
      'target_durasi'                          => $target_durasi_tromHae,
      'vol_darah_akhir'                          => $volDarah_akhir_tromHae,
      'ac_terpakai'                          => $acTerpakai_tromHae,
      'lama_prosedur'                          => $lamaProsedur_tromHae,
      'jumlah_siklus'                          => $jumlahSiklus_tromHae,
      'naci_terpakai'                          => $naciTerpakai_tromHae,
      'plasma_vol'                          => $plasmaVol_tromHae,
      'ac_dalam_plasma'                          => $acPlasmaBag_tromHae,
      'platelet_vol'                          => $plateletVol_tromHae,
      'ac_dalam_plt'                          => $acDalamPlt_tromHae,
      'est_plt_yield'                          => $estPltYield_tromHae,
      'target_plt_x10'                          => $targetPltYield_tromHae,
      'jumlah_nyata'                          => $jumlahNyata_tromHae,
      'jumlah_konsentrat_1'                          => $jumlahNyata_tromHae_1,
      'jumlah_konsentrat_2'                          => $jumlahNyata_tromHae_2,
      'jumlah_konsentrat_3'                          => $jumlahNyata_tromHae_3,
      'komentar'                          => $komentar_tromHae,
      'oleh'                             => $oleh,
      'status'                           => 1
    );
    // echo'<pre>';print_r($data);exit();
    
    if (!empty($id)) {
      $this->db->where('id', $id);
      $this->db->update('keperawatan.tb_trom_heo', $data);
    }else{
      $this->db->insert('keperawatan.tb_trom_heo', $data);
      $idTromHae = $this->db->insert_id();
    }
  }

   ///////////// Modal view modal trom hae ////////////////
  public function viewModalTromHae()
  {
    $nokun = $this->input->post('nokun');
    $idview = $this->input->post('idview');
    $norm = $this->input->post('norm');
    $DetailHisTrombaferesisHaemonetics = $this->pengkajianAwalModel->DetailHisTrombaferesisHaemonetics($idview);

    $data = array(
      'idview' => $idview,
      'DetailHisTrombaferesisHaemonetics' => $DetailHisTrombaferesisHaemonetics,
      'nokun' => $nokun
    );

    $this->load->view('Pengkajian/bankdarah/TrombaferesisHaemo/viewModalLembarProsedur', $data);
  }

  public function simpanLembarProsedurTromHae()
  {
    $post = $this->input->post();
    $id = $this->input->post('idViewTromHae');
    $oleh = $this->session->userdata('id');
    $waktuLembarProsedurTromHae = $this->input->post('waktuLembarProsedurTromHae');
    $drawReturnTromHaeModal = $this->input->post('drawReturnTromHaeModal');
    $plasmaVolTromHaeModal = $this->input->post('plasmaVolTromHaeModal');
    $tinggi_cm_tromHae = $this->input->post('tinggi_cm_tromHae');
    $processVolTromHaeModal = $this->input->post('processVolTromHaeModal');
    $nuciMlSiklusTromHaeModal = $this->input->post('nuciMlSiklusTromHaeModal');
    $catatanTromHaeModal = $this->input->post('catatanTromHaeModal');
    $plateletVolTromHaeModal = $this->input->post('plateletVolTromHaeModal');

    $data = array(
      'id_tromhae'     => $id,
      'waktu'      => $waktuLembarProsedurTromHae,
      'draw_atau_return'      => $drawReturnTromHaeModal,
      'plasma_vol'      => $plasmaVolTromHaeModal,
      'platelet_vol'      => $plateletVolTromHaeModal,
      'process_vol'      => $processVolTromHaeModal,
      'nuci_ml_siklus'      => $nuciMlSiklusTromHaeModal,
      'catatan'      => $catatanTromHaeModal,
      'oleh'      => $oleh,
      'status'    => 1
    );

    $this->db->insert('keperawatan.tb_lembar_prosedur_tromhae', $data);
  }

}


/* End of file Pra_anestesi.php */
/* Location: ./application/controllers/anestesi/evaluasikemampuanfungsionalmobilisasi/FormEKFM.php */
