<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CeklisEdukasiOrientasiRI extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/transferRuangan/CEORIModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_ceori = $this->uri->segment(3);
      
      $data = array(
        'nokun' => $nokun,
        'id_ceori' => isset($id_ceori) ? $id_ceori : "",
        'getPengkajian' => $this->CEORIModel->getPengkajian($id_ceori),
        'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
        'listKepada' => $this->masterModel->referensi(1548),
        'listFasilitasRuangan' => $this->masterModel->referensi(1554),
        'listKebijakan' => $this->masterModel->referensi(1555),
        'listRuangRI' => $this->masterModel->referensi(1556),
        'listRIIM' => $this->masterModel->referensi(1557),
        'listRIRA' => $this->masterModel->referensi(1558),
        'listRITN' => $this->masterModel->referensi(1559),
        'listRINAD' => $this->masterModel->referensi(1560),
        'listManKes' => $this->masterModel->referensi(1561),
        'listPegawai' => $this->masterModel->listAllPegawai()
      );
      $this->load->view('rekam_medis/rawat_inap/transferRuangan/ceklisEdukasiOrientasiRI',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
          
          $data = array(
            'nokun' => $post['nokun'],
            'kepada' => isset($post['kepada']) ? $post['kepada']: "",
            'kepada_lain' => isset($post['kepada_lain']) ? $post['kepada_lain']: "",
            'fasilitas_ruangan' => isset($post['fasilitas_ruangan']) ? json_encode($post['fasilitas_ruangan']): "",
            'kebijakan_tatatertib' => isset($post['kebijakan_tatatertib']) ? json_encode($post['kebijakan_tatatertib']): "",
            'ruang_ri' => isset($post['ruang_ri']) ? json_encode($post['ruang_ri']): "",
            'riim' => isset($post['riim']) ? json_encode($post['riim']): "",
            'rira' => isset($post['rira']) ? json_encode($post['rira']): "",
            'ritn' => isset($post['ritn']) ? json_encode($post['ritn']): "",
            'rinad' => isset($post['rinad']) ? json_encode($post['rinad']): "",
            'manajemen_keselamatan' => isset($post['manajemen_keselamatan']) ? json_encode($post['manajemen_keselamatan']): "",
            'ttd_menjelaskan' => file_get_contents($this->input->post('ttd_menjelaskan')),
            'ttd_menerima' => file_get_contents($this->input->post('ttd_menerima')),
            'nama_menjelaskan' => isset($post['nama_menjelaskan']) ? $post['nama_menjelaskan']: "",
            'nama_penerima' => isset($post['nama_penerima']) ? $post['nama_penerima']: "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_ceori'])) {
            $this->db->where('keperawatan.tb_ceori.id', $post['id_ceori']);
            $this->db->update('keperawatan.tb_ceori', $data);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_ceori', $data);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->CEORIModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->CEORIModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editCeklisEdukasiOrientasiRI" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->CEORIModel->total_count(),
            "recordsFiltered"   => $this->CEORIModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}