<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TrombaferesisAmicus extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function simpanForm()
    {

        $time = $this->input->post("time");
        $runTime = $this->input->post("runTime");
        $wbProses = $this->input->post("wbProses");
        $draw = $this->input->post("draw");
        $ppp = $this->input->post('ppp');
        $ket = $this->input->post('ket');

        $data = array(
            'nokun' => $this->input->post("nokun"),
            'dokterMinta' => $this->input->post("dokterMinta"),
            'KondisiVena' => $this->input->post("KondisiVena"),
            'TekDarah' => $this->input->post("TekDarah"),
            'Nadi' => $this->input->post("Nadi"),
            'Disposableset' => $this->input->post("Disposableset"),
            'NoLot' => $this->input->post("NoLot"),
            'ED' => $this->input->post("ED"),
            'WaktuMulaiAferesis' => $this->input->post("WaktuMulaiAferesis"),
            'WaktuSeleseiAferesis' => $this->input->post("WaktuSeleseiAferesis"),
            'Sex' => $this->input->post("Sex"),
            'TB' => $this->input->post("TB"),
            'BB' => $this->input->post("BB"),
            'TotalDarahdiproses' => $this->input->post("TotalDarahdiproses"),
            'PLTPrecount' => $this->input->post("PLTPrecount"),
            'PLTPostcount' => $this->input->post("PLTPostcount"),
            'Yieldtarget' => $this->input->post("Yieldtarget"),
            'VolumeTrombosit' => $this->input->post("VolumeTrombosit"),
            'TargetWaktu' => $this->input->post("TargetWaktu"),
            'HTPrecount' => $this->input->post("HTPrecount"),
            'HTPostcount' => $this->input->post("HTPostcount"),
            'Cuff' => $this->input->post("Cuff"),
            'DrawSpeed' => $this->input->post("DrawSpeed"),
            'ReurnSpeed' => $this->input->post("ReurnSpeed"),
            'CirateInfusionRate' => $this->input->post("CirateInfusionRate"),
            'TotalWBvolProcessed' => $this->input->post("TotalWBvolProcessed"),
            'ACD' => $this->input->post("ACD"),
            'Nacl' => $this->input->post("Nacl"),
            'Waktuproses' => $this->input->post("Waktuproses"),
            'Yield' => $this->input->post("Yield"),
            'PlasmaProd' => $this->input->post("PlasmaProd"),
            'RBCVol' => $this->input->post("RBCVol"),
            'VolumeTC' => $this->input->post("VolumeTC"),
            'ACDinstorage' => $this->input->post("ACDinstorage"),
            'komentar' => $this->input->post("komentar"),
            'oleh' =>  $this->session->userdata("id"),
            'status' => 1,
        );

        $idSimpan = $this->pengkajianAwalModel->insertTromboferesis($data);
       // echo $idSimpan;

        $dataDetail = array();
        $indexdataDetail = 0;
        if (count($time) > 0) {
            foreach ($time as $inputDatai) {
                if ($time[$indexdataDetail] != "") {
                    array_push(
                        $dataDetail, array(
                        'id_tromboferesis' => $idSimpan,
                        'time' => $time[$indexdataDetail],
                        'runTime' => $runTime[$indexdataDetail],
                        'wbProses' => $wbProses[$indexdataDetail],
                        'draw' => $draw[$indexdataDetail],
                        'ppp' => $ppp[$indexdataDetail],
                        'ket' => $ket[$indexdataDetail],
                        )
                    );
                }
                $indexdataDetail++;
            }
            $this->pengkajianAwalModel->insertTromboferesisDetail($dataDetail);
        }

        if ($idSimpan) {
            $result = array('status' => 'success');
        }

        return $result;
    }

    public function detailTromboferesis()
    {
        $id = $this->input->post("id");
        $dHistoryTromboferesis = $this->pengkajianAwalModel->dHistoryTromboferesis($id);
        $listDr = $this->masterModel->listDr();
        $dHistoryTromboferesisDetail = $this->pengkajianAwalModel->dHistoryTromboferesisDetail($id);
       // echo "<pre>";print_r($dHistoryTromboferesis);exit();
        $data = array(
            'listDr' => $listDr,
            'dHistoryTromboferesis' => $dHistoryTromboferesis,
            'dHistoryTromboferesisDetail' => $dHistoryTromboferesisDetail,
        );

        $this->load->view('Pengkajian/bankdarah/trombaferesisMesinAmicus/view', $data);
    }

   

   

}
?>