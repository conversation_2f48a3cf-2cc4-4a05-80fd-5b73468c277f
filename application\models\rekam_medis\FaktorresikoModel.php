<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FaktorresikoModel extends CI_Model
{
    function pertanyaanNjawab($nomr=''){
        $string = "SELECT dr.id_referensi,dr.referensi, GROUP_CONCAT(dv.id_variabel,'#:#',
        (case 
        when dv.variabel='Nevirapin' then 'Ya, mengandung Nevirapin/Efavirenz'
        when dv.variabel='NonNevirapin' then ' Ya, tidak mengandung Nevirapin/Efavirenz'
        ELSE dv.variabel
         END ) 
        SEPARATOR '#@#') opsi, fr.idjawaban jawab, fr.id idjawab
        FROM db_master.referensi dr
        LEFT JOIN db_master.variabel dv ON dr.id_referensi=dv.id_referensi and dv.status=1
        LEFT JOIN db_pasien.tb_faktorresiko fr ON dr.id_referensi=fr.idfaktor AND fr.nomr='$nomr'
        WHERE dr.jenis_kategori=1
        AND dr.status=1
        GROUP BY dr.id_referensi";
        $query = $this->db->query($string);

        return $query->result_array();
    }


    function saveJawaban($param=array()){
        // var_dump($param);exit;
        $pertanyaan=$param['idpertanyaan'];
        $jawaban=$param['jawaban'];
        $idjawab=$param['idjawab'];
        $data=array();
        $dataupdate=array();
        foreach ($pertanyaan as $key => $item) {
            if(isset($jawaban[$item]) && $jawaban[$item]!="" && $idjawab[$key]<1){
                $dataitem=array('nomr'=>$param['nomr'],'idfaktor'=>$item,'idjawaban'=>$jawaban[$item],'oleh'=>$param['oleh']);
                array_push($data,$dataitem);
            }
            else if(isset($jawaban[$item]) && $jawaban[$item]!="" && $idjawab[$key]>0){
                $dataitem=array('id'=>$idjawab[$key],'nomr'=>$param['nomr'],'idfaktor'=>$item,'idjawaban'=>$jawaban[$item],'oleh'=>$param['oleh']);
                array_push($dataupdate,$dataitem);
            }
            // var_dump($pertanyaan);
        }

        if(count($data)>0){
        $query =$this->db->insert_batch('db_pasien.tb_faktorresiko', $data); 
        }
        if(count($dataupdate)>0){
        $queryup =$this->db->update_batch('db_pasien.tb_faktorresiko',$dataupdate, 'id'); 
        }

        if($query || $queryup){
            return true;
        }else{
            return false;
        }
    }

}
