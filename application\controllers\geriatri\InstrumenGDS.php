<?php
defined('BASEPATH') or exit('No direct script access allowed');

class InstrumenGDS extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'geriatri/InstrumenGDSModel'
      )
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array(
      'igds1' => $this->masterModel->referensi(903),
      'igds2' => $this->masterModel->referensi(904),
      'igds3' => $this->masterModel->referensi(905),
      'igds4' => $this->masterModel->referensi(906),
      'igds5' => $this->masterModel->referensi(907),
      'igds6' => $this->masterModel->referensi(908),
      'igds7' => $this->masterModel->referensi(909),
      'igds8' => $this->masterModel->referensi(910),
      'igds9' => $this->masterModel->referensi(911),
      'igds10' => $this->masterModel->referensi(912),
      'igds11' => $this->masterModel->referensi(913),
      'igds12' => $this->masterModel->referensi(914),
      'igds13' => $this->masterModel->referensi(915),
      'igds14' => $this->masterModel->referensi(916),
      'igds15' => $this->masterModel->referensi(917),
    );
    if (isset($id)) {
      // Form ubah
      $data['id'] = $id;
      $data['detail'] = $this->InstrumenGDSModel->history(null, $id, 'detail');
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/geriatri/instrumenGDS/detail', $data);
    } else {
      // Form tambah
      $segment6 = $this->uri->segment(6);
      $segment2 = $this->uri->segment(2);
      $nokun = isset($segment6) ? $segment6 : $segment2;
      $data['nokun'] = $nokun;
      $data['jumlah'] = $this->InstrumenGDSModel->history($nokun, null, 'jumlah');
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/geriatri/instrumenGDS/index', $data);
    }
  }

  // Instrumen Geriatric Depression Scale
  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->InstrumenGDSModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();
          $id = isset($post['id']) ? isset($post['id']) : null;
          $data = array(
            'kunjungan' => isset($post['kunjungan']) ? $post['kunjungan'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'igds1' => isset($post['igds1']) ? $post['igds1'] : null,
            'igds2' => isset($post['igds2']) ? $post['igds2'] : null,
            'igds3' => isset($post['igds3']) ? $post['igds3'] : null,
            'igds4' => isset($post['igds4']) ? $post['igds4'] : null,
            'igds5' => isset($post['igds5']) ? $post['igds5'] : null,
            'igds6' => isset($post['igds6']) ? $post['igds6'] : null,
            'igds7' => isset($post['igds7']) ? $post['igds7'] : null,
            'igds8' => isset($post['igds8']) ? $post['igds8'] : null,
            'igds9' => isset($post['igds9']) ? $post['igds9'] : null,
            'igds10' => isset($post['igds10']) ? $post['igds10'] : null,
            'igds11' => isset($post['igds11']) ? $post['igds11'] : null,
            'igds12' => isset($post['igds12']) ? $post['igds12'] : null,
            'igds13' => isset($post['igds13']) ? $post['igds13'] : null,
            'igds14' => isset($post['igds14']) ? $post['igds14'] : null,
            'igds15' => isset($post['igds15']) ? $post['igds15'] : null,
            'score' => isset($post['skor']) ? $post['skor'] : null,
            'oleh' => $this->session->userdata['id'],
          );
          // echo '<pre>';print_r($data);exit();
          if (!empty($id)) {
            // Ubah data
            $this->InstrumenGDSModel->ubah($data, $id);
          } else {
            // Simpan data
            $this->InstrumenGDSModel->simpan($data);
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/geriatri/instrumenGDS/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->InstrumenGDSModel->history($nokun, null, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        $h->score,
        $h->pengisi,
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-igds' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-igds' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-igds' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-igds' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-edit'></i> Ubah
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->InstrumenGDSModel->rules;
      $this->form_validation->set_rules($rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        $id = isset($post['id']) ? $post['id'] : null;
        // echo '<pre>';print_r($id);exit();

        $data = array('status' => 0);
        $this->InstrumenGDSModel->ubah($data, $id);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }
}

/* End of file InstrumenGDS.php */
/* Location: ./application/models/geriatri/InstrumenGDS.php */