<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pasien extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    // if ($this->session->userdata('logged_in') == false) {
    //   redirect('login');
    // }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('api/PasienModel'));
  }

  public function get_pasien()
  {
    header('Content-type: application/json');
    $filters = json_decode(file_get_contents('php://input'));
    log_message('debug', json_encode($filters), false);

    $data = $this->PasienModel->get_pasien($filters);
    if($data->NORM) {
      // $data = array(
      //   'status' => 'success',
      //   'data' => array(
      //     'nomr' => '123456',
      //     'name' => 'Patient Name',
      //     'gender' => "male",
      //     'birthDate' => "1970-01-01",
      //   )
      // );
      $data = array(
        'status' => 'success',
        'data' => array(
          'nomr' => $data->NORM,
          'name' => $data->NAMA,
          'gender' => $data->JENIS_KELAMIN == 1 ? "male" : ($data->JENIS_KELAMIN == 2 ? "female" : "other"),
          'birthDate' => date_format(date_create($data->TANGGAL_LAHIR),"Y-m-d"),
        )
      );
    }
    log_message('debug', json_encode($data), false);
    echo json_encode($data);
  }

}