<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KankerPayudaraModel extends MY_Model
{
  protected $_table_name = 'medis.tb_dd_payudara';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),

    'risk_1' => array(
      'field' => 'risk_1',
      'label' => 'Jenis kelamin wanita',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_2' => array(
      'field' => 'risk_2',
      'label' => 'Usia lebih dari 50 tahun',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_3' => array(
      'field' => 'risk_3',
      'label' => 'Riwayat kanker payudara di keluarga tingkat pertama (ibu, saudara perempuan, anak perempuan)',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_4' => array(
      'field' => 'risk_4',
      'label' => 'Riwayat radioterapi jangka panjang (lebih dari satu bulan) karena limfoma maligma',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_5' => array(
      'field' => 'risk_5',
      'label' => 'Riwayat tumor jinak di payudara',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_6' => array(
      'field' => 'risk_6',
      'label' => 'Riwayat kanker ovarium',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_7' => array(
      'field' => 'risk_7',
      'label' => 'Riwayat menarche dini (kurang dari 12 tahun)',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_8' => array(
      'field' => 'risk_8',
      'label' => 'Riwayat <i>menopause</i> terlambat (lebih dari 55 tahun)',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_9' => array(
      'field' => 'risk_9',
      'label' => 'Memiliki anak',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_10' => array(
      'field' => 'risk_10',
      'label' => 'Usia saat melahirkan anak pertama lebih dari 30 tahun',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_11' => array(
      'field' => 'risk_11',
      'label' => 'Riwayat menyusui',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_13' => array(
      'field' => 'risk_13',
      'label' => 'Riwayat kanker payudara di keluarga tingkat kedua (nenek, tante, sepupu, dsb.)',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_14' => array(
      'field' => 'risk_14',
      'label' => 'Riwayat konsumsi obat hormonal (pil KB, dsb.) jangka panjang (lebih dari sepuluh tahun)',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'risk_15' => array(
      'field' => 'risk_15',
      'label' => 'Alasan datang ke Deteksi Dini Dharmais',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'nilai_risk' => array(
      'field' => 'nilai_risk',
      'label' => 'Skor risiko',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),

    'gejala_1' => array(
      'field' => 'gejala_1',
      'label' => 'Menopause (tidak menstruasi dalam satu tahun)',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_2' => array(
      'field' => 'gejala_2',
      'label' => 'Sedang menstruasi',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_4' => array(
      'field' => 'gejala_4',
      'label' => 'Sedang menyusui',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_5' => array(
      'field' => 'gejala_5',
      'label' => 'Keluhan benjolan di payudara',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_6' => array(
      'field' => 'gejala_6',
      'label' => 'Nyeri di payudara',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_7' => array(
      'field' => 'gejala_7',
      'label' => 'Keluhan bengkak kemerahan di payudara',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_8' => array(
      'field' => 'gejala_8',
      'label' => 'Keluhan benjolan di ketiak',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_9' => array(
      'field' => 'gejala_9',
      'label' => 'Keluhan benjolan di leher',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_10' => array(
      'field' => 'gejala_10',
      'label' => 'Luka di kulit payudara',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_11' => array(
      'field' => 'gejala_11',
      'label' => 'Gambaran kulit payudara seperti kulit jeruk',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_12' => array(
      'field' => 'gejala_12',
      'label' => 'Gambaran lesung kulit payudara',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_13' => array(
      'field' => 'gejala_13',
      'label' => 'Keluhan keluar cairan dari puting',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_14' => array(
      'field' => 'gejala_14',
      'label' => 'Keluhan nyeri tulang',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_15' => array(
      'field' => 'gejala_15',
      'label' => 'Keluhan batuk lama atau sesak napas',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_16' => array(
      'field' => 'gejala_16',
      'label' => 'Keluhan mual-muntah',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'gejala_17' => array(
      'field' => 'gejala_17',
      'label' => 'Keluhan lain',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),

    'imt' => array(
      'field' => 'imt',
      'label' => 'Index massa tubuh',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),

    'kategori_tubuh' => array(
      'field' => 'kategori_tubuh',
      'label' => 'Kategori tubuh',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  // Mulai rules tanggal
  public $rulesTanggal = array(
    'tanggal' => array(
      'field' => 'tanggal',
      'label' => 'Tanggal',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );
  // Akhri rules tanggal

  public $rulesWaktu = array(
    'waktu' => array(
      'field' => 'waktu',
      'label' => 'Waktu',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );
  // Akhri rules waktu

  // Mulai rules riwayat menyusui
  public $rulesRiwayatMenyusui = array(
    'risk_12' => array(
      'field' => 'risk_12',
      'label' => 'Lama menyusui',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );
  // Akhir rules riwayat menyusui

  // Mulai rules alasan datang ke Deteksi Dini Kanker Dharmais
  public $rulesAlasanDatang = array(
    'ket_risk_15' => array(
      'field' => 'ket_risk_15',
      'label' => 'Keterangan alasan datang ke Deteksi Dini Dharmais',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );
  // Akhir rules alasan datang ke Deteksi Dini Kanker Dharmais

  // Mulai rules sedang menstruasi
  public $rulesSedangMenyusui = array(
    'gejala_3' => array(
      'field' => 'gejala_3',
      'label' => 'Menstruasi sudah berapa hari',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );
  // Akhir rules sedang menstruasi

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('medis.tb_dd_payudara', $data);
    return $this->db->insert_id();
  }

  public function replace($data)
  {
    $this->db->replace('medis.tb_dd_payudara', $data);
  }

  public function ubah($id, $data)
  {
    $this->db->where('medis.tb_dd_payudara.id', $id);
    $this->db->update('medis.tb_dd_payudara', $data);
  }

  public function ubah_ukdd($id, $data)
  {
    $this->db->where('medis.tb_dd_payudara.id_ukdd', $id);
    $this->db->update('medis.tb_dd_payudara', $data);
  }

  public function simpanGambar($data)
  {
    $this->db->insert('medis.tb_gbr_dd_payudara', $data);
    return $this->db->insert_id();
  }

  public function jumlah($nomr)
  {
    $this->db->select('dd.id');
    $this->db->from('medis.tb_dd_payudara dd');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = dd.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->where('pp.NORM', $nomr);
    $this->db->order_by('dd.id', 'DESC');
    $query = $this->db->get();
    return $query->num_rows();
  }

  public function history($nomr)
  {
    $this->db->select(
      'dd.id, dd.nokun, dd.tanggal, dd.waktu, dd.nilai_risk, master.getNamaLengkapPegawai(perawat.NIP) perawat,
      master.getNamaLengkapPegawai(dokter.NIP) dokter, dd.status'
    );
    $this->db->from('medis.tb_dd_payudara dd');
    $this->db->join('db_pasien.tb_tb_bb tbb', 'tbb.ref = dd.id', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'tv.ref = dd.id', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = dd.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna perawat', 'perawat.ID = dd.perawat', 'left');
    $this->db->join('aplikasi.pengguna dokter', 'dokter.ID = dd.dokter', 'left');
    $this->db->where('tbb.data_source', '22');
    $this->db->where('tv.data_source', '22');
    $this->db->where('pp.NORM', $nomr);
    $this->db->order_by('dd.id', 'DESC');
    return $this->db->get();
  }

  public function detail($id)
  {
    $this->db->select(
      'dd.id, dd.nokun, dd.id_pemeriksaan, dd.tanggal, dd.waktu, dd.risk_1, dd.risk_2, dd.risk_3, dd.risk_4, dd.risk_5,
      dd.risk_6, dd.risk_7, dd.risk_8, dd.risk_9, dd.risk_10, dd.risk_11, dd.risk_12, dd.risk_13, dd.risk_14,
      dd.risk_15, dd.ket_risk_15, dd.nilai_risk, dd.gejala_1, dd.gejala_2, dd.gejala_3, dd.gejala_4, dd.gejala_5,
      dd.gejala_6, dd.gejala_7, dd.gejala_8, dd.gejala_9, dd.gejala_10, dd.gejala_11, dd.gejala_12, dd.gejala_13,
      dd.gejala_14, dd.gejala_15, dd.gejala_16, dd.gejala_17, tbb.id tbb, tbb.tb, tbb.bb, dd.imt, dd.kategori_tubuh,
      tv.id tv, tv.td_sistolik, tv.td_diastolik, tv.nadi, tv.pernapasan, tv.suhu, dd.simetri, dd.bentuk, dd.kulit,
      dd.areola_papilla, dd.benjolan_teraba, dd.panjang_benjolan, dd.lebar_benjolan, dd.tinggi_benjolan,
      dd.konsistensi, dd.batas, dd.mobilisasi, dd.warna, dd.nyeri, dd.kgb_aksila_teraba kat,
      dd.kgb_aksila_soliter_multipel kasm, dd.kgb_supraklavikula_teraba kst,
      dd.kgb_supraklavikula_soliter_multipel kssm, dd.keterangan_lain, dd.rencana_pemeriksaan_penunjang rpp,
      dd.perawat, dd.dokter, dd.status'
    );
    $this->db->from('medis.tb_dd_payudara dd');
    $this->db->join('db_pasien.tb_tb_bb tbb', 'tbb.ref = dd.id', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'tv.ref = dd.id', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = dd.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->where('tbb.data_source', '22');
    $this->db->where('tv.data_source', '22');
    $this->db->where('dd.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function gambar($id)
  {
    $this->db->select('dd.id, dd.nokun, ddp.pemeriksaan, dd.perawat, dd.dokter');
    $this->db->from('medis.tb_dd_payudara dd');
    $this->db->join('medis.tb_gbr_dd_payudara ddp', 'ddp.id = dd.id_pemeriksaan', 'left');
    $this->db->where('dd.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file KankerPayudaraModel.php */
/* Location: ./application/models/emr/deteksiDini/KankerPayudaraModel.php */