<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TpnModel extends MY_Model {

  function __construct(){
    parent::__construct();
  }

  function namaIngredientsChild($id)
  {
    $query = $this->db->query("SELECT * FROM medis.tb_ingredients_child WHERE id_ingredients='$id' AND status=1 ORDER BY preparation_used ASC");

    return $query->result_array();
  }

  public function ingredients()
  {
    $query = $this->db->query(
      "SELECT *
      FROM medis.tb_ingredients ing
      WHERE ing.status='1'
      ORDER BY ing.ingredients ASC"
    );
    return $query->result_array();
  }

  public function tpnList($nomr)
  {
    $query = $this->db->query(
      "SELECT tpn.*, master.getNamaLengkapPegawai(peng.NIP) OLEH, ruan.DESKRIPSI NAMA_RUANGAN
      FROM medis.tb_tpn tpn
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = tpn.CREATED_BY
      LEFT JOIN pendaftaran.kunjungan pkunz ON pkunz.NOMOR = tpn.NOKUN
      LEFT JOIN pendaftaran.tujuan_pasien tpuj ON tpuj.NOPEN = pkunz.NOPEN
      LEFT JOIN master.ruangan ruan ON ruan.ID = tpuj.RUANGAN
      WHERE tpn.NOMR='$nomr'"
    );
    return $query->result_array();
  }

  public function getChildIng()
  {
    $id = $this->input->post('id');
    $query = $this->db->query(
      "SELECT igc.*, ig.solution_bags
      FROM medis.tb_ingredients_child igc
      LEFT JOIN medis.tb_ingredients ig ON ig.id = igc.id_ingredients
      WHERE igc.id='$id'"
    );
    return $query->row_array();
  }

}
