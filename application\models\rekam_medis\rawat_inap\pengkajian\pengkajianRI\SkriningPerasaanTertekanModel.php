<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SkriningPerasaanTertekanModel extends MY_Model
{
    protected $_table_name = 'db_pasien.tb_skrining_perasaan_tertekan';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(
		'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib <PERSON>ka.'
                ),
        ),	
        
        'thermometer' => array(
            'field' => 'thermometer',
            'label' => 'thermometer',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),	
    );



    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select('spt.id ID,spt.nokun NOKUN, spt.created TANGGA<PERSON>
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('db_pasien.tb_skrining_perasaan_tertekan spt');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = spt.nokun', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('master.diagnosa_masuk dm', 'dm.ID = p.DIAGNOSA_MASUK', 'LEFT');
        $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'LEFT');
        $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'LEFT');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = spt.oleh', 'LEFT');

        $this->db->where('spt.STATUS !=', '0');
        $this->db->where('p.NORM', $this->input->post('nomr'));
        $this->db->order_by('spt.id', 'DESC');
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}
