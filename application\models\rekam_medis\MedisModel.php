<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MedisModel extends CI_Model
{
  public function __construct()
  {
    parent::__construct();
    $this->dblis = $this->load->database('237', true);
  }

  public function total_pasienRi($user)
  {
    if (in_array(1050114, $this->session->userdata('akses_ruangan'))) {
      $query = $this->db->query("CALL keperawatan.DashboardRI_RSHaji($user)");
    } else {
      $query = $this->db->query("CALL keperawatan.DashboardRI($user)");
    }
    return $query->result_array();
  }

  public function datatables_lp_ruangan($id = 0, $status = 0, $user = 0, $norm = '0', $limit = 0, $offset = 0)
  {
    $query = $this->db->query("CALL keperawatan.listPasienRI($user, $id, $status, '" . $norm . "' ,$limit, $offset)");
    return $query->result();
  }

  public function historyPeng<PERSON>jian()
  {
    $norm = $this->input->post("nomr");
    $query = $this->db->query("CALL keperawatan.HistoryRawatInap($norm)");
    return $query->result();
  }

  public function kunjungan_pk()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT penPEN.NORM, penK.NOPEN NOPEN, penK.NOMOR NOKUN,
        IF(olab.TANGGAL IS NULL, penK.MASUK, olab.TANGGAL) TGLMASUK, masR.DESKRIPSI RUANGAN
        FROM pendaftaran.pendaftaran penPEN
        LEFT JOIN pendaftaran.kunjungan penK ON penPEN.NOMOR = penK.NOPEN
        LEFT JOIN layanan.order_lab olab ON olab.NOMOR=penK.REF
        LEFT JOIN master.ruangan masR ON penK.RUANGAN = masR.ID
        LEFT JOIN layanan.tindakan_medis ltm ON penK.NOMOR = ltm.KUNJUNGAN
        LEFT JOIN layanan.hasil_lab lhl ON ltm.ID = lhl.TINDAKAN_MEDIS
        WHERE masR.ID IN ('105070101','105070102') AND penK.STATUS = 2 AND penPEN.NORM = $norm
        GROUP BY NOKUN
        ORDER BY TGLMASUK DESC"
    );
    return $query->result();
  }

  public function kunjungan_pk_baru()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT penPEN.NORM, penK.NOPEN NOPEN, penK.NOMOR NOKUN,
      IF(olab.TANGGAL IS NULL, penK.MASUK, olab.TANGGAL) TGLMASUK, masR.DESKRIPSI RUANGAN
      , (SELECT GROUP_CONCAT(t.NAMA SEPARATOR ', ')
      FROM layanan.tindakan_medis tm
      , master.tindakan t
      , pendaftaran.kunjungan pku
      WHERE tm.KUNJUNGAN=penK.NOMOR AND pku.`STATUS`!=0 AND tm.TINDAKAN=t.ID AND pku.NOMOR=penK.NOMOR
      ORDER BY tm.ID) TINDAKAN
      FROM pendaftaran.pendaftaran penPEN
      LEFT JOIN pendaftaran.kunjungan penK ON penPEN.NOMOR = penK.NOPEN
      LEFT JOIN layanan.order_lab olab ON olab.NOMOR=penK.REF
      LEFT JOIN master.ruangan masR ON penK.RUANGAN = masR.ID
      LEFT JOIN layanan.tindakan_medis ltm ON penK.NOMOR = ltm.KUNJUNGAN
      LEFT JOIN layanan.hasil_lab lhl ON ltm.ID = lhl.TINDAKAN_MEDIS
      WHERE masR.ID IN ('105070101','105070102') AND penK.STATUS = 2 AND penPEN.NORM = '$norm'
      GROUP BY NOKUN
      ORDER BY TGLMASUK DESC"
    );
    return $query->result();
  }

  public function detail_kunjungan_pk()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query(
      "SELECT pk.NOMOR, pk.MASUK TGL, ptm.TINDAKAN_MEDIS,
      CONCAT(peg.GELAR_DEPAN,' ',peg.NAMA,' ',peg.GELAR_BELAKANG) AS NMDA,
      CONCAT(peg1.GELAR_DEPAN,' ',peg1.NAMA,' ',peg1.GELAR_BELAKANG) AS NMDL
      , r.deskripsi RUANGASAL, pk.NOPEN

      FROM pendaftaran.kunjungan pk
      LEFT JOIN layanan.tindakan_medis tmi ON tmi.KUNJUNGAN=pk.NOMOR
      LEFT JOIN master.tindakan mt ON mt.ID=tmi.TINDAKAN
      LEFT JOIN layanan.order_detil_lab odl ON odl.REF=tmi.ID
      LEFT JOIN layanan.order_lab orlab ON orlab.NOMOR=odl.ORDER_ID
      LEFT JOIN master.ruangan r ON substr(pk.REF,3,9)=r.ID AND r.JENIS=5 AND pk.REF=orlab.NOMOR
      LEFT JOIN master.dokter d ON orlab.DOKTER_ASAL = d.ID
      LEFT JOIN master.pegawai peg ON d.NIP = peg.NIP
      LEFT JOIN layanan.petugas_tindakan_medis ptm ON tmi.ID = ptm.TINDAKAN_MEDIS
      LEFT JOIN master.dokter d1 ON ptm.MEDIS = d1.ID
      LEFT JOIN master.pegawai peg1 ON d1.NIP = peg1.NIP
      WHERE pk.NOMOR = '$nokun'
      ORDER BY ptm.TINDAKAN_MEDIS DESC"
    );
    return $query->row();
  }

  public function tanggal_sampling()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->dblis->query(
      "SELECT r.retrieved_dt, rb.authorization_date
      FROM lis_bridging.registration r
      LEFT JOIN lis_bridging.result_bridge_lis rb ON r.order_number = rb.his_reg_no
      WHERE r.order_number = '$nokun'"
    );
    return $query->row();
  }

  public function pemeriksaan_radiologi()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT pp.NOMOR NOPEN, pk.NOMOR KUNJUNGAN, pk.MASUK TANGGAL_KUNJUNGAN
      ,   (SELECT
      GROUP_CONCAT(t.NAMA SEPARATOR ', ')
      FROM layanan.tindakan_medis tm
      , master.tindakan t
      , pendaftaran.kunjungan pku
      WHERE  tm.KUNJUNGAN=pk.NOMOR
      AND pku.`STATUS`!=0
      AND tm.TINDAKAN=t.ID
      AND pku.NOMOR=pk.NOMOR
      ORDER BY tm.ID) TINDAKAN
      , master.getNamaLengkap(pp.NORM) NAMA_PASIEN,
      pp.NORM, mp.TANGGAL_LAHIR, master.getCariUmur(pp.TANGGAL, mp.TANGGAL_LAHIR) UMUR,
      ref.DESKRIPSI

      FROM pendaftaran.pendaftaran pp
      LEFT JOIN pendaftaran.kunjungan pk ON pp.NOMOR = pk.NOPEN
      LEFT JOIN master.pasien mp ON mp.NORM = pp.NORM
      LEFT JOIN master.ruangan mr ON pk.RUANGAN = mr.ID
      LEFT JOIN master.referensi ref ON ref.ID = mp.JENIS_KELAMIN AND ref.JENIS=2
      WHERE pp.NORM = '$norm' AND pk.RUANGAN = '105100101'
      ORDER BY pk.MASUK DESC"
    );
    return $query->result();
  }

  public function pemeriksaan_radiologi_gabung()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT DATE_FORMAT(pk.MASUK,'%d-%m-%Y') TGL_PEMERIKSAAN, ltm.ID, mt.NAMA TINDAKAN
      , DATE_FORMAT(hrad.TANGGAL,'%d-%m-%Y') TANGGALHASIL,
      hrad.KLINIS, hrad.KESAN, hrad.USUL, hrad.HASIL,
      master.getNamaLengkapPegawai(dok1.NIP) DOKTER_SATU,
      master.getNamaLengkapPegawai(dok2.NIP) DOKTER_DUA
      , dok1.ID ID_DOK1,
      dok2.ID ID_DOK2,
      ltm.KUNJUNGAN,
      ltm.ID TINDAKAN_MEDIS,
      IF(hrad.TANGGAL < '2019-09-02 00:00:00', 2, IF(hrad.`STATUS`=2,2,1)) STATUS_EXPERTISE,
      IF(hrad.TANGGAL < '2019-09-02 00:00:00', 'Expertise Final', IF(hrad.`STATUS`=2,'Expertise Final','Expertise Belum Final')) STATUS_DESKRIPSI
      FROM layanan.tindakan_medis ltm
      LEFT JOIN master.tindakan mt ON mt.ID = ltm.TINDAKAN
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ltm.KUNJUNGAN
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN layanan.hasil_rad hrad ON ltm.ID = hrad.TINDAKAN_MEDIS
      LEFT JOIN master.dokter dok1 ON dok1.ID = hrad.DOKTER_SATU
      LEFT JOIN master.dokter dok2 ON dok2.ID = hrad.DOKTER_DUA
      WHERE p.NORM = '$norm' AND pk.RUANGAN IN ('105100101','105100102') #AND pk.STATUS != 0
      AND IF(hrad.TANGGAL < '2019-09-02 00:00:00', hrad.`STATUS`=1, IF(hrad.`STATUS`=2, hrad.`STATUS`=2, hrad.`STATUS`=3))
      ORDER BY pk.MASUK DESC"
    );
    return $query->result();
  }

  public function pemeriksaan_pt_gabung()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT pt.*
      , IF(pt.JENIS_PEMERIKSAAN=1, 'EGFR'
      , IF(pt.JENIS_PEMERIKSAAN=2, 'HPV-GENOTYPING'
      , IF(pt.JENIS_PEMERIKSAAN=3, 'HPV Hybrid Captured'
      , IF(pt.JENIS_PEMERIKSAAN=4, 'BRAF'
      , IF(pt.JENIS_PEMERIKSAAN=5, 'KRAS'
      , IF(pt.JENIS_PEMERIKSAAN=6, 'ALL RAS'
      , IF(pt.JENIS_PEMERIKSAAN=7, 'IDH 1/2'
      , IF(pt.JENIS_PEMERIKSAAN=8, 'CYTO DNA'
      , IF(pt.JENIS_PEMERIKSAAN=9, 'DISH', '-'))))))))) DESK_JENIS_PEMERIKSAAN
      , DATE_FORMAT(pt.TANGGAL_LAB,'%d-%m-%Y') TANGGAL_PATMOL_FORMAT
      FROM layanan.hasil_pa_patmol pt
      WHERE pt.STATUS='1' AND (pt.NORM='$norm' OR pt.MR_INTERNAL='$norm')"
    );
    return $query->result();
  }

  public function pemeriksaan_pa_gabung()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT a.STATUS STATUS_PA, a.JENIZ JENIS_PEMERIKSAAN, a.NOPA NO_LAB#, a.MASUK_KUNJUNGAN TGL
, a.MASUK_SAMPEL TGL_TERIMA, a.DOKPENGIRIM
, a.DSPA DOKTER_PA
, a.RUANG_ASAL
, a.PENJAMIN
, a.MASUK_SAMPEL
, a.TANGGAL_HASIL
, a.LOKASI
, a.DIDAPAT_DENGAN
, a.CAIRAN_FIKSASI
, a.DIAGNOSA_KLINIK
, a.KETERANGAN_KLINIK
, a.MAKROSKOPIK
, a.MIKROSKOPIK
, a.KESIMPULAN
, a.IMUNO_HISTOKIMIA
, a.REEVALUASI

FROM
(SELECT

pa.NOMOR_LAB NOPA
, pk.NOMOR KUNJUNGAN
, pk.NOPEN
, pa.NORM NORM
, ref.DESKRIPSI PENJAMIN
, master.getNamaLengkap(pa.NORM) NAMA_PASIEN
, master.getNamaLengkapPegawai(mp.NIP) DSPA
, mr2.DESKRIPSI RUANG_ASAL
, master.getNamaTindakan2 (pk.NOMOR) TINDAKAN
, pk.MASUK MASUK_KUNJUNGAN
, pa.TANGGAL_LAB MASUK_SAMPEL, pa.TANGGAL TANGGAL_HASIL
, IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)<0,0
, (master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)) LAMA_PEMERIKSAAN
, SUBSTR(pa.TANGGAL_LAB,9,2) TANGGALSAMPLE, SUBSTR(pa.TANGGAL_LAB,6,2) BULANSAMPEL
, SUBSTR(pa.TANGGAL,9,2) TANGGALJAWAB, SUBSTR(pa.TANGGAL,6,2) BULANJAWAB
, INST.NAMAINST, INST.ALAMATINST

, master.getHeaderLaporan('1') INSTALASI
, IF(0=0,'Semua', master.getNamaLengkapPegawai(mp.NIP)) DOKTERHEADER
, IF(0=0,'Semua',t.NAMA) TINDAKANHEADER
, 'Histologi' JENIZ
, '' NOLABSLMBNYA
#, pa.DIAGNOSA_KLINIK
#, pa.KETERANGAN_KLINIK
#, pa.MIKROSKOPIK
#, pa.MAKROSKOPIK
#, pa.IMUNO_HISTOKIMIA IHK
#, pa.KESIMPULAN
, layanan.getKodeHisto(pa.ID) KODE
, layanan.getSkorHisto(pa.ID) SKOR
, (SELECT SUM(klh.SKOR)
FROM layanan.scor_pa_histo sch
LEFT JOIN master.jenis_layanan_pa jlh ON jlh.ID=sch.SCOR_ID
LEFT JOIN master.kode_layanan_pa klh ON jlh.KODE_LAYANAN=klh.ID
WHERE sch.HASIL=pa.ID AND sch.`STATUS`=1) total_score
,( SELECT
        IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=1,'I',IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=2,'II'
,  IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=3,'III',IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=4,'IV',IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=5,'V',IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)>5,'VI',' ')))))) LEVELL
        FROM layanan.scor_level_histologi slh
LEFT JOIN master.level_histologi_detail lhd ON slh.ID_LEVEL_DETAIL=lhd.ID
LEFT JOIN master.level_histologi lh ON lhd.ID_LEVEL=lh.ID

WHERE slh.`STATUS`=1
AND slh.KUNJUNGAN=pk.NOMOR
GROUP BY slh.KUNJUNGAN) LEVELLL
,( SELECT
         IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=1,'1',IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=2,'3'
,  IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=3,'5',IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=4,'10',IF(IF(COUNT(slh.KUNJUNGAN) >1,max(lh.ID+1),lh.ID)=5,'15',' 20'))))) SCOREE
        FROM layanan.scor_level_histologi slh
LEFT JOIN master.level_histologi_detail lhd ON slh.ID_LEVEL_DETAIL=lhd.ID
LEFT JOIN master.level_histologi lh ON lhd.ID_LEVEL=lh.ID

WHERE slh.`STATUS`=1
AND slh.KUNJUNGAN=pk.NOMOR
GROUP BY slh.KUNJUNGAN) SCOREE
, master.getNamaLengkapPegawai(dokpeng.NIP) DOKPENGIRIM
, pa.`STATUS`
, pa.LOKASI
, pa.DIDAPAT_DENGAN
, pa.CAIRAN_FIKSASI
, pa.DIAGNOSA_KLINIK
, pa.KETERANGAN_KLINIK
, pa.MAKROSKOPIK
, pa.MIKROSKOPIK
, pa.KESIMPULAN
, pa.IMUNO_HISTOKIMIA
, pa.REEVALUASI

FROM layanan.hasil_pa_histologi pa

LEFT JOIN master.dokter md ON pa.DOKTER = md.ID
LEFT JOIN master.pegawai mp ON md.NIP = mp.NIP
LEFT JOIN pendaftaran.kunjungan pk ON pa.KUNJUNGAN = pk.NOMOR
LEFT JOIN master.ruangan mr ON pk.RUANGAN = mr.ID
LEFT JOIN layanan.tindakan_medis tm ON pa.KUNJUNGAN = tm.KUNJUNGAN
LEFT JOIN master.tindakan t ON tm.TINDAKAN=t.ID
LEFT JOIN layanan.order_lab ol ON ol.NOMOR = pk.REF
LEFT JOIN pendaftaran.kunjungan pk2 ON pk2.NOMOR = ol.KUNJUNGAN
LEFT JOIN master.ruangan mr2 ON pk2.RUANGAN = mr2.ID
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
LEFT JOIN master.dokter dokpeng ON dokpeng.ID = ol.DOKTER_ASAL

, (SELECT p.NAMA NAMAINST, p.ALAMAT ALAMATINST
FROM aplikasi.instansi ai
, master.ppk p
WHERE ai.PPK=p.ID) INST

WHERE pa.NORM='$norm'

AND pk.NOPEN IS NOT NULL

GROUP BY pa.NOMOR_LAB

UNION

SELECT

pa.NOMOR_LAB NOPA
, pk.NOMOR KUNJUNGAN
, pk.NOPEN
, pa.NORM NORM
, ref.DESKRIPSI PENJAMIN
, master.getNamaLengkap(pa.NORM) NAMA_PASIEN
, master.getNamaLengkapPegawai(mp.NIP) DSPA
, mr2.DESKRIPSI RUANG_ASAL
, master.getNamaTindakan2 (pk.NOMOR) TINDAKAN
, pk.MASUK MASUK_KUNJUNGAN
, pa.TANGGAL_LAB MASUK_SAMPEL, pa.TANGGAL TANGGAL_HASIL
, IF((master.getJumlahHariKerja(pa.TANGGAL_LAB, pa.TANGGAL)-1)<0,0
, (master.getJumlahHariKerja(pa.TANGGAL_LAB, pa.TANGGAL)-1)) LAMA_PEMERIKSAAN

, SUBSTR(pa.TANGGAL_LAB,9,2) TANGGALSAMPLE, SUBSTR(pa.TANGGAL_LAB,6,2) BULANSAMPEL
, SUBSTR(pa.TANGGAL,9,2) TANGGALJAWAB, SUBSTR(pa.TANGGAL,6,2) BULANJAWAB
, INST.NAMAINST, INST.ALAMATINST
, master.getHeaderLaporan('1') INSTALASI
, IF(0=0,'Semua', master.getNamaLengkapPegawai(mp.NIP)) DOKTERHEADER
, IF(0=0,'Semua',t.NAMA) TINDAKANHEADER
, 'Sitologi' JENIZ
, '' NOLABSLMBNYA
#, pa.DIAGNOSA_KLINIK
#, pa.KETERANGAN_KLINIK
#, pa.MIKROSKOPIK
#, pa.MAKROSKOPIK
#, pa.IMUNO_HISTOKIMIA IHK
#, pa.KESIMPULAN
, layanan.getKodeSito(pa.ID) KODE
, layanan.getSkorSito(pa.ID) SKORE
, (SELECT SUM(klh.SKOR)
FROM layanan.scor_pa_sito sch
LEFT JOIN master.jenis_layanan_pa jlh ON jlh.ID=sch.SCOR_ID
LEFT JOIN master.kode_layanan_pa klh ON jlh.KODE_LAYANAN=klh.ID
WHERE sch.HASIL=pa.ID AND sch.`STATUS`=1) total_score
,(SELECT mks.KATEGORI
    from layanan.hasil_pa_sitologi lhs
left join layanan.scor_level_sitologi lsl on lsl.ID_HASIL = lhs.ID
left join master.level_slide_sitologi mls on lsl.ID_KATEGORI_LEVEL = mls.ID
left join master.level_kategori_sitologi mks on mks.ID = mls.KATEGORI_SITOLOGI
where lhs.KUNJUNGAN = pk.NOMOR and lsl.`STATUS` = 1 GROUP BY lhs.KUNJUNGAN) LEVELLL
,(SELECT mks.SKORING
    from layanan.hasil_pa_sitologi lhs
left join layanan.scor_level_sitologi lsl on lsl.ID_HASIL = lhs.ID
left join master.level_slide_sitologi mls on lsl.ID_KATEGORI_LEVEL = mls.ID
left join master.level_kategori_sitologi mks on mks.ID = mls.KATEGORI_SITOLOGI
where lhs.KUNJUNGAN = pk.NOMOR and lsl.`STATUS` = 1 GROUP BY lhs.KUNJUNGAN) SCOREE

, master.getNamaLengkapPegawai(dokpeng.NIP) DOKPENGIRIM
, pa.`STATUS`
, pa.LOKASI
, pa.DIDAPAT_DENGAN
, pa.CAIRAN_FIKSASI
, pa.DIAGNOSA_KLINIK
, pa.KETERANGAN_KLINIK
, pa.MAKROSKOPIK
, pa.MIKROSKOPIK
, pa.KESIMPULAN
, pa.IMUNO_HISTOKIMIA
, pa.REEVALUASI
FROM layanan.hasil_pa_sitologi pa

LEFT JOIN master.dokter md ON pa.DOKTER = md.ID
LEFT JOIN master.pegawai mp ON md.NIP = mp.NIP
LEFT JOIN pendaftaran.kunjungan pk ON pa.KUNJUNGAN = pk.NOMOR
LEFT JOIN master.ruangan mr ON pk.RUANGAN = mr.ID
LEFT JOIN layanan.tindakan_medis tm ON pa.KUNJUNGAN = tm.KUNJUNGAN
LEFT JOIN master.tindakan t ON tm.TINDAKAN=t.ID
LEFT JOIN layanan.order_lab ol ON ol.NOMOR = pk.REF
LEFT JOIN pendaftaran.kunjungan pk2 ON pk2.NOMOR = ol.KUNJUNGAN
LEFT JOIN master.ruangan mr2 ON pk2.RUANGAN = mr2.ID
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
LEFT JOIN master.dokter dokpeng ON dokpeng.ID = ol.DOKTER_ASAL
, (SELECT p.NAMA NAMAINST, p.ALAMAT ALAMATINST
FROM aplikasi.instansi ai
, master.ppk p
WHERE ai.PPK=p.ID) INST

WHERE pa.NORM='$norm'

AND pk.NOPEN IS NOT NULL

GROUP BY pa.NOMOR_LAB

UNION

SELECT

pa.NOMOR_IMUNO NOPA
, pk.NOMOR KUNJUNGAN
, pk.NOPEN
, pa.NORM NORM
, ref.DESKRIPSI PENJAMIN
, master.getNamaLengkap(pa.NORM) NAMA_PASIEN
, master.getNamaLengkapPegawai(mp.NIP) DSPA
, mr2.DESKRIPSI RUANG_ASAL
, master.getNamaTindakan2 (pk.NOMOR) TINDAKAN
, pk.MASUK MASUK_KUNJUNGAN
, pa.TANGGAL_IMUNO MASUK_SAMPEL, pa.TANGGAL_HASIL TANGGAL_HASIL
, IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_IMUNO), DATE(pa.TANGGAL_HASIL))-1)<0,0
, (master.getJumlahHariKerja(DATE(pa.TANGGAL_IMUNO), DATE(pa.TANGGAL_HASIL))-1)) LAMA_PEMERIKSAAN
, SUBSTR(pa.TANGGAL_IMUNO,9,2) TANGGALSAMPLE, SUBSTR(pa.TANGGAL_IMUNO,6,2) BULANSAMPEL
, SUBSTR(pa.TANGGAL_HASIL,9,2) TANGGALJAWAB, SUBSTR(pa.TANGGAL_HASIL,6,2) BULANJAWAB
, INST.NAMAINST, INST.ALAMATINST
, master.getHeaderLaporan('1') INSTALASI
, IF(0=0,'Semua', master.getNamaLengkapPegawai(mp.NIP)) DOKTERHEADER
, IF(0=0,'Semua',t.NAMA) TINDAKANHEADER
, 'Imuno Histokimia' JENIZ
, pa.NOMOR_LAB NOLABSLMBNYA
#, IF(pa.JENIS_HASIL=1, pahis.DIAGNOSA_KLINIK, pasit.DIAGNOSA_KLINIK) DIAGNOSA_KLINIK
#, IF(pa.JENIS_HASIL=1, pahis.KETERANGAN_KLINIK, pasit.KETERANGAN_KLINIK) KETERANGAN_KLINIK
#, IF(pa.JENIS_HASIL=1, pahis.MIKROSKOPIK, pasit.MIKROSKOPIK) KETERANGAN_KLINIK
#, CONCAT('Hasil Imuno Histokimia: ',pa.IMUNO_HISTOKIMIA) MAKROSKOPIK
#, pa.IMUNO_HISTOKIMIA IHK
#, pa.KESIMPULAN
, layanan.getKodeImuno(pa.ID) KODE
, layanan.getSkoreImuno(pa.ID) SKORE
, (SELECT SUM(klh.SKOR)
FROM layanan.scor_pa_imuno sch
LEFT JOIN master.jenis_layanan_pa jlh ON jlh.ID=sch.SCOR_ID
LEFT JOIN master.kode_layanan_pa klh ON jlh.KODE_LAYANAN=klh.ID
WHERE sch.HASIL=pa.ID AND sch.`STATUS`=1) total_score
,(SELECT mks.KATEGORI
    from layanan.hasil_pa_imunohistokimia lhs
left join layanan.scor_level_imuno lsl on lsl.ID_HASIL = lhs.ID
left join master.level_slide_imuno mls on lsl.ID_KATEGORI_LEVEL = mls.ID
left join master.level_kategori_imuno mks on mks.ID = mls.LEVEL_KATEGORI
where lhs.KUNJUNGAN = pk.NOMOR and lsl.`STATUS` = 1 GROUP BY lhs.KUNJUNGAN) LEVELLL


   ,(SELECT mks.SKORING
    from layanan.hasil_pa_imunohistokimia lhs
left join layanan.scor_level_imuno lsl on lsl.ID_HASIL = lhs.ID
left join master.level_slide_imuno mls on lsl.ID_KATEGORI_LEVEL = mls.ID
left join master.level_kategori_imuno mks on mks.ID = mls.LEVEL_KATEGORI
where lhs.KUNJUNGAN = pk.NOMOR and lsl.`STATUS` = 1 GROUP BY lhs.KUNJUNGAN
) SCOREE

, master.getNamaLengkapPegawai(dokpeng.NIP) DOKPENGIRIM
, pa.`STATUS`
, IF(pa.JENIS_HASIL=1, pahis.LOKASI, pasit.LOKASI) LOKASI
, IF(pa.JENIS_HASIL=1, pahis.DIDAPAT_DENGAN, pasit.DIDAPAT_DENGAN) DIDAPAT_DENGAN
, IF(pa.JENIS_HASIL=1, pahis.CAIRAN_FIKSASI, pasit.CAIRAN_FIKSASI) CAIRAN_FIKSASI
, IF(pa.JENIS_HASIL=1, pahis.DIAGNOSA_KLINIK, pasit.DIAGNOSA_KLINIK) DIAGNOSA_KLINIK
  , IF(pa.JENIS_HASIL=1, pahis.KETERANGAN_KLINIK, pasit.KETERANGAN_KLINIK) KETERANGAN_KLINIK
, IF(pa.JENIS_HASIL=1, pahis.MAKROSKOPIK, pasit.MAKROSKOPIK) MAKROSKOPIK
  , IF(pa.JENIS_HASIL=1, pahis.MIKROSKOPIK, pasit.MIKROSKOPIK) MIKROSKOPIK
  , pa.KESIMPULAN
  , pa.IMUNO_HISTOKIMIA
, '' REEVALUASI


FROM layanan.hasil_pa_imunohistokimia pa

LEFT JOIN master.dokter md ON pa.DOKTER = md.ID
LEFT JOIN master.pegawai mp ON md.NIP = mp.NIP
LEFT JOIN pendaftaran.kunjungan pk ON pa.KUNJUNGAN = pk.NOMOR
LEFT JOIN master.ruangan mr ON pk.RUANGAN = mr.ID
LEFT JOIN layanan.tindakan_medis tm ON pa.KUNJUNGAN = tm.KUNJUNGAN
LEFT JOIN master.tindakan t ON tm.TINDAKAN=t.ID
LEFT JOIN layanan.order_lab ol ON ol.NOMOR = pk.REF
LEFT JOIN pendaftaran.kunjungan pk2 ON pk2.NOMOR = ol.KUNJUNGAN
LEFT JOIN master.ruangan mr2 ON pk2.RUANGAN = mr2.ID
LEFT JOIN layanan.hasil_pa_histologi pahis ON pahis.NOMOR_LAB = pa.NOMOR_LAB
LEFT JOIN layanan.hasil_pa_sitologi pasit ON pasit.NOMOR_LAB = pa.NOMOR_LAB
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
#LEFT JOIN layanan.order_lab ol ON ol.NOMOR = pk.REF
LEFT JOIN master.dokter dokpeng ON dokpeng.ID = ol.DOKTER_ASAL
, (SELECT p.NAMA NAMAINST, p.ALAMAT ALAMATINST
FROM aplikasi.instansi ai
, master.ppk p
WHERE ai.PPK=p.ID) INST
WHERE pa.NORM='$norm' AND pa.IMUNO_HISTOKIMIA!=''

AND pk.NOPEN IS NOT NULL

GROUP BY pa.NOMOR_IMUNO)a

ORDER BY a.MASUK_SAMPEL DESC"
    );
    return $query->result();
  }

  public function tindakan_radiologi()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query(
      "SELECT pk.NOMOR NOKUN, p.NORM NORM, master.getNamaLengkap(p.NORM) NAMAPASIEN,
      DATE(ps.TANGGAL_LAHIR) TANGGALLAHIR, IF(ps.JENIS_KELAMIN=1,'L','P') JK,
      tm.ID IDTM,
      tin.ID KDTINDAKAN, tin.NAMA NMTINDAKAN,
      master.getNamaLengkapPegawai(doktin.NIP) DOKTERTINDAKAN,
      DATE_FORMAT(tm.TANGGAL,'%d-%m-%Y %H:%i:%s') TANGGALTINDAKAN,
      master.getCariUmur(p.TANGGAL, ps.TANGGAL_LAHIR) UMUR,
      crbyr.DESKRIPSI CARABAYAR,
      DATE_FORMAT(tm.TANGGAL,'%Y-%m-%d') TANGGALC

      FROM pendaftaran.kunjungan pk
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
      LEFT JOIN layanan.tindakan_medis tm ON tm.KUNJUNGAN = pk.NOMOR AND tm.STATUS!=0
      LEFT JOIN master.tindakan tin ON tin.ID = tm.TINDAKAN
      LEFT JOIN layanan.petugas_tindakan_medis ptm ON tm.ID=ptm.TINDAKAN_MEDIS AND ptm.JENIS=1 AND KE=1
      LEFT JOIN master.dokter doktin ON ptm.MEDIS=doktin.ID
      LEFT JOIN pendaftaran.penjamin pj ON p.NOMOR=pj.NOPEN
      LEFT JOIN master.referensi crbyr ON pj.JENIS=crbyr.ID AND crbyr.JENIS=10

      WHERE pk.NOMOR = '$nokun' AND pk.RUANGAN IN ('105100101','105100102') AND pk.STATUS != 0"
    );
    return $query->result();
  }

  public function expertise()
  {
    $id = $this->input->post('id');
    $query = $this->db->query(
      "SELECT ltm.ID,DATE_FORMAT(hrad.TANGGAL,'%d-%m-%Y') TANGGALHASIL,
      hrad.KLINIS, hrad.KESAN, hrad.USUL, hrad.HASIL,
      master.getNamaLengkapPegawai(dok1.NIP) DOKTER_SATU,
      master.getNamaLengkapPegawai(dok2.NIP) DOKTER_DUA,
      mt.NAMA,
      DATE_FORMAT(hrad.TANGGAL,'%H:%i:%s') WAKTUHASIL,
      DATE_FORMAT(hrad.TANGGAL,'%Y-%m-%d'),
      dok1.ID ID_DOK1,
      dok2.ID ID_DOK2,
      ltm.KUNJUNGAN,
      ltm.TANGGAL TANGGAL_INPUT_TINDAKAN,
      IF(hrad.TANGGAL < '2019-09-02 00:00:00', 2, IF(hrad.`STATUS`=2,2,1)) STATUS_EXPERTISE,
      IF(hrad.TANGGAL < '2019-09-02 00:00:00', 'Expertise Final', IF(hrad.`STATUS`=2,'Expertise Final','Expertise Belum Final')) STATUS_DESKRIPSI

      FROM layanan.tindakan_medis ltm

      LEFT JOIN master.tindakan mt ON mt.ID = ltm.TINDAKAN
      LEFT JOIN layanan.hasil_rad hrad ON ltm.ID = hrad.TINDAKAN_MEDIS
      LEFT JOIN master.dokter dok1 ON dok1.ID = hrad.DOKTER_SATU
      LEFT JOIN master.dokter dok2 ON dok2.ID = hrad.DOKTER_DUA

      WHERE ltm.ID = '$id' AND IF(hrad.TANGGAL < '2019-09-02 00:00:00', hrad.`STATUS`=1, IF(hrad.`STATUS`=2, hrad.`STATUS`=2, hrad.`STATUS`=3))"
    );
    return $query->row();
  }

  public function sitologi()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT hps.ID, hps.NOMOR_LAB,lol.TANGGAL TANGGAL_ORDER,hps.TANGGAL_LAB,hps.LOKASI
        ,if(mhs.NOMOR_LAB is not null,1,0) STATUS
        FROM layanan.hasil_pa_sitologi hps
        LEFT JOIN master.dokter md ON hps.DOKTER = md.ID
        LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = hps.KUNJUNGAN
        LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR
        LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID
        LEFT JOIN medis.hasil_sito mhs ON mhs.NOMOR_LAB = hps.NOMOR_LAB

        WHERE hps.NORM = '$norm'
        ORDER BY hps.TANGGAL_LAB DESC"
    );
    return $query->result();
  }

  public function histologi()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT hph.ID, hph.NOMOR_LAB,lol.TANGGAL TANGGAL_ORDER, hph.TANGGAL_LAB, hph.LOKASI
        , master.getNamaLengkapPegawai(md.NIP) PEMERIKSA, master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM
        ,if(mhh.NOMOR_LAB is not null,1,0) STATUS
        FROM layanan.hasil_pa_histologi hph
        LEFT JOIN master.dokter md ON hph.DOKTER = md.ID
        LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = hph.KUNJUNGAN
        LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR
        LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID
        LEFT JOIN medis.hasil_histo mhh ON mhh.NOMOR_LAB = hph.NOMOR_LAB

        WHERE hph.NORM = '$norm'
        ORDER BY hph.TANGGAL_LAB DESC"
    );
    return $query->result();
  }

  public function imuno()
  {
    $norm = $this->input->post('norm');
    $query = $this->db->query(
      "SELECT hph.ID, hph.NOMOR_LAB NOLABSBLMNYA,hph.NOMOR_IMUNO NOMOR_LAB,lol.TANGGAL TANGGAL_ORDER, hph.TANGGAL_IMUNO TANGGAL_LAB
      , master.getNamaLengkapPegawai(md.NIP) PEMERIKSA, master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM
      FROM layanan.hasil_pa_imunohistokimia hph
      LEFT join master.dokter md ON hph.DOKTER = md.ID
      LEFT join pendaftaran.kunjungan kun1 ON kun1.NOMOR = hph.KUNJUNGAN
      LEFT join layanan.order_lab lol ON kun1.REF = lol.NOMOR
      LEFT join master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID

      WHERE hph.NORM = '$norm'
      order by hph.ID desc"
    );
    return $query->result_array();
  }

  public function pilihTandaVitalMon($norm)
  {
    $query = $this->db->query(
      "SELECT tv.id ID_TANDA_VITAL, rk.DESKRIPSI RUANGAN, tv.nomr NORM
      , tv.nokun NOKUN, DATE_FORMAT(pk.MASUK,'%d-%m-%Y %H:%i:%s') TANGGAL
      FROM db_pasien.tb_tanda_vital tv
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tv.nokun
      LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
      WHERE tv.nomr='$norm' AND tv.`status`='1' AND tv.nokun != ''
      AND tv.nokun!='0'
      GROUP BY tv.nokun
      ORDER BY tv.created_at DESC"
    );
    return $query->result_array();
  }

  public function hasil_sitologi()
  {
    $nolab = $this->input->post('nolab');
    $query = $this->db->query(
      "SELECT hps.*
      ,master.getNamaLengkapPegawai(md.NIP) PEMERIKSA, master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM, kun1.MASUK TERIMA
      , mru.DESKRIPSI RUANGAN
      ,(SELECT REPLACE(GROUP_CONCAT(tin.NAMA SEPARATOR ';'),';',', ') TINDAKAN
				FROM layanan.tindakan_medis tm
					LEFT JOIN master.tindakan tin ON tin.ID = tm.TINDAKAN
			  WHERE tm.`STATUS`!=0 AND tm.KUNJUNGAN=kun1.NOMOR) TIND
      , maref.DESKRIPSI PENJAMIN
      FROM layanan.hasil_pa_sitologi hps
      LEFT JOIN master.dokter md ON hps.DOKTER = md.ID
      LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = hps.KUNJUNGAN
      LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR
      LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID
      LEFT JOIN pendaftaran.kunjungan kun2 ON kun2.NOMOR = lol.KUNJUNGAN
      LEFT JOIN master.ruangan mru ON kun2.RUANGAN = mru.ID
      LEFT JOIN pendaftaran.penjamin ppj ON kun1.NOPEN = ppj.NOPEN
      LEFT JOIN master.referensi maref ON ppj.JENIS = maref.ID AND maref.JENIS = 10

      WHERE hps.NOMOR_LAB = '$nolab' AND IF(hps.TANGGAL_LAB > '2018-08-23 00:00:00'
      , hps.`STATUS`=2 , hps.`STATUS` in (1,2))"
    );
    return $query->row_array();
  }

  public function hasil_patmol()
  {
    $nolab = $this->input->post('nolab');
    $query = $this->db->query(
      "SELECT pt.*
      , pt.ID
      , pt.KUNJUNGAN
      , IF(pt.MR_INTERNAL=0, pt.NORM, pt.MR_INTERNAL) NORM
      , pt.NOMOR_PATMOL NOMOR_LAB
      , IF(pt.JENIS_PEMERIKSAAN=1, 'EGFR'
      , IF(pt.JENIS_PEMERIKSAAN=2, 'HPV-GENOTYPING'
      , IF(pt.JENIS_PEMERIKSAAN=3, 'HPV Hybrid Captured'
      , IF(pt.JENIS_PEMERIKSAAN=4, 'BRAF'
      , IF(pt.JENIS_PEMERIKSAAN=5, 'KRAS'
      , IF(pt.JENIS_PEMERIKSAAN=6, 'ALL RAS'
      , IF(pt.JENIS_PEMERIKSAAN=7, 'IDH 1/2'
      , IF(pt.JENIS_PEMERIKSAAN=8, 'CYTO DNA'
      , IF(pt.JENIS_PEMERIKSAAN=9, 'DISH', '-'))))))))) JENIS_PEMERIKSAAN
      , master.getNamaLengkapPegawai(d.NIP) PEMERIKSA
      , DATE_FORMAT(pt.TANGGAL_LAB,'%d-%m-%Y %H:%i:%s') TANGGAL_PATMOL_FORMAT
      , DATE_FORMAT(pt.TANGGAL_HASIL,'%d-%m-%Y %H:%i:%s') TANGGAL_HASIL_FORMAT
      , master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM
      , DATE_FORMAT(kun1.MASUK,'%d-%m-%Y %H:%i:%s') TERIMA
      , mru.DESKRIPSI RUANGAN, kun1.NOMOR TIND, maref.DESKRIPSI PENJAMIN

      FROM layanan.hasil_pa_patmol pt
      LEFT JOIN master.dokter d ON pt.DOKTER = d.ID

      LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = pt.KUNJUNGAN
      LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR
      LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID

      LEFT JOIN pendaftaran.kunjungan kun2 ON kun2.NOMOR = lol.KUNJUNGAN
      LEFT JOIN master.ruangan mru ON kun2.RUANGAN = mru.ID
      LEFT JOIN pendaftaran.penjamin ppj ON kun1.NOPEN = ppj.NOPEN
      LEFT JOIN master.referensi maref ON ppj.JENIS = maref.ID AND maref.JENIS = 10

      WHERE pt.NOMOR_PATMOL='$nolab'"
    );
    return $query->row_array();
  }

  public function hasil_histo()
  {
    $nolab = $this->input->post('nolab');
    $query = $this->db->query(
      "SELECT hph.* ,master.getNamaLengkapPegawai(md.NIP) PEMERIKSA
        , master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM, kun1.MASUK TERIMA
        , CONCAT(mio.KODE,'-',mio.DESKRIPSI) ICDOMOR , CONCAT(mit.KODE,'-',mit.DESKRIPSI) ICDOMT
        , mru.DESKRIPSI RUANGAN
        ,(SELECT REPLACE(GROUP_CONCAT(tin.NAMA SEPARATOR ';'),';',', ') TINDAKAN
				FROM layanan.tindakan_medis tm
					LEFT JOIN master.tindakan tin ON tin.ID = tm.TINDAKAN
			  WHERE tm.`STATUS`!=0 AND tm.KUNJUNGAN=kun1.NOMOR) TIND
        , maref.DESKRIPSI PENJAMIN
        FROM layanan.hasil_pa_histologi hph
        LEFT JOIN master.dokter md ON hph.DOKTER = md.ID
        LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = hph.KUNJUNGAN
        LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR
        LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID
        LEFT JOIN master.icd_o_morphology mio ON hph.ICDOMORPHOLOGY = mio.ID
        LEFT JOIN master.icd_o_topography mit ON hph.ICDOTOPOGRAPHY = mit.ID
        LEFT JOIN pendaftaran.kunjungan kun2 ON kun2.NOMOR = lol.KUNJUNGAN
        LEFT JOIN master.ruangan mru ON kun2.RUANGAN = mru.ID
        LEFT JOIN pendaftaran.penjamin ppj ON kun1.NOPEN = ppj.NOPEN
        LEFT JOIN master.referensi maref ON ppj.JENIS = maref.ID AND maref.JENIS = 10

        WHERE hph.NOMOR_LAB = '$nolab' AND IF(hph.TANGGAL_LAB > '2018-08-23 00:00:00'
        , hph.`STATUS`=2 , hph.`STATUS` in (1,2))"
    );
    return $query->row_array();
  }

  public function hasil_imunohistokimia()
  {
    $nolab = $this->input->post('nolab');
    $query = $this->db->query(
      "SELECT IF(hph.JENIS_HASIL=1, pahis.NOMOR_LAB, pasit.NOMOR_LAB) NOLAB_SBLMNYA, hph.NOMOR_IMUNO NOMOR_LAB
        , IF(hph.JENIS_HASIL=1, pahis.LOKASI, pasit.LOKASI) LOKASI
        , IF(hph.JENIS_HASIL=1, pahis.DIDAPAT_DENGAN, pasit.DIDAPAT_DENGAN) DIDAPAT_DENGAN
        , IF(hph.JENIS_HASIL=1, pahis.CAIRAN_FIKSASI, pasit.CAIRAN_FIKSASI) CAIRAN_FIKSASI
        , IF(hph.JENIS_HASIL=1, pahis.DIAGNOSA_KLINIK, pasit.DIAGNOSA_KLINIK) DIAGNOSA_KLINIK
        , IF(hph.JENIS_HASIL=1, pahis.KETERANGAN_KLINIK, pasit.KETERANGAN_KLINIK) KETERANGAN_KLINIK
        , IF(hph.JENIS_HASIL=1, pahis.KESIMPULAN, pasit.KESIMPULAN) KESIMPULAN_LAB_SBLMYA
        , hph.ID, hph.KUNJUNGAN, hph.NORM, hph.JENIS_HASIL
        , hph.IMUNO_HISTOKIMIA, hph.KESIMPULAN, hph.DOKTER, hph.TANGGAL_IMUNO TANGGAL_LAB, hph.TANGGAL_HASIL TANGGAL
        , hph.OLEH, hph.`STATUS`
        , master.getNamaLengkapPegawai(md.NIP) PEMERIKSA
        , master.getNamaLengkapPegawai(mdp.NIP) PENGIRIM, kun1.MASUK TERIMA
        , mru.DESKRIPSI RUANGAN
        ,(SELECT REPLACE(GROUP_CONCAT(tin.NAMA SEPARATOR ';'),';',', ') TINDAKAN
				FROM layanan.tindakan_medis tm
					LEFT JOIN master.tindakan tin ON tin.ID = tm.TINDAKAN
			  WHERE tm.`STATUS`!=0 AND tm.KUNJUNGAN=kun1.NOMOR) TIND
        , maref.DESKRIPSI PENJAMIN
        FROM layanan.hasil_pa_imunohistokimia hph
        LEFT JOIN master.dokter md ON hph.DOKTER = md.ID
        LEFT JOIN pendaftaran.kunjungan kun1 ON kun1.NOMOR = hph.KUNJUNGAN
        LEFT JOIN layanan.order_lab lol ON kun1.REF = lol.NOMOR
        LEFT JOIN master.dokter mdp ON lol.DOKTER_ASAL = mdp.ID
        LEFT JOIN layanan.hasil_pa_histologi pahis ON pahis.NOMOR_LAB = hph.NOMOR_LAB
        LEFT JOIN layanan.hasil_pa_sitologi pasit ON pasit.NOMOR_LAB = hph.NOMOR_LAB
        LEFT JOIN pendaftaran.kunjungan kun2 ON kun2.NOMOR = lol.KUNJUNGAN
        LEFT JOIN master.ruangan mru ON kun2.RUANGAN = mru.ID
        LEFT JOIN pendaftaran.penjamin ppj ON kun1.NOPEN = ppj.NOPEN
        LEFT JOIN master.referensi maref ON ppj.JENIS = maref.ID AND maref.JENIS = 10
        WHERE hph.`STATUS`=2
        and hph.NOMOR_IMUNO='$nolab'"
    );
    return $query->row_array();
  }

  public function medis()
  {
    $this->db->select('m.`*, IF(HOUR(TIMEDIFF(NOW(),MAX(m.created_at)))<=24=1,1,IF(m.flag=1,0,1)) STATUS_EDIT ');
    $this->db->from('medis.tb_medis m');
    $this->db->where('m.id_emr', $this->input->post('id'));
    $query = $this->db->get();
    return $query->row();
  }

  public function asuhanKeperawatanCpptRI()
  {
    $this->db->select('GROUP_CONCAT(t.id_asuhan_keperawatan_detil)id_asuhan, db_master.getIDAsuhanKeperawatan_CPPT(t.id_cppt) parent');
    $this->db->from('keperawatan.tb_cppt_perencanaan_asuhan_keperawatan t');
    $this->db->where('t.id_cppt', $this->input->post('id'));
    $query = $this->db->get();
    return $query->row();
  }

  public function asuhanKeperawatanPengkajianRI()
  {
    $this->db->select('GROUP_CONCAT(t.id_asuhan_keperawatan_detil)id_asuhan, db_master.getIDAsuhanKepereawatanPengkajian(t.id_emr) parent');
    $this->db->from('keperawatan.tb_perencanaan_asuhan_keperawatan t');
    $this->db->where('t.id_emr', $this->input->post('id'));
    $query = $this->db->get();
    return $query->row();
  }

  public function obeservasi_keperawatan()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query(
      "SELECT ot.id, ru.DESKRIPSI RUANGAN,ot.tanggal, ot.jam
       , master.getNamaLengkapPegawai(peng.NIP) perawat
       , db_master.getIDAsuhanKeperawatan_CPPT_RI(ot.id) id_parent
       , (SELECT GROUP_CONCAT(akd.ID,'' SEPARATOR ',')
       FROM keperawatan.tb_intervensi_observasi_tindakan otd
       LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan pak ON pak.id = otd.id_intervensi
       LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON akd.ID = pak.id
       WHERE otd.id_observasi_tindakan = ot.id) id_perencanaan_asuhan_keperawatan
       , (SELECT CONCAT(GROUP_CONCAT('• ',akd.DESKRIPSI,'' SEPARATOR '<br>'))
       FROM keperawatan.tb_intervensi_observasi_tindakan otd
       LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan pak ON pak.id = otd.id_intervensi
       LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON akd.ID = pak.id
       WHERE otd.id_observasi_tindakan = ot.id)pak
       FROM keperawatan.tb_observasi_tindakan ot
       LEFT JOIN db_pasien.tb_kesadaran k ON ot.id = k.ref
       LEFT JOIN db_pasien.tb_tanda_vital tv ON ot.id = tv.ref
       LEFT JOIN db_pasien.tb_o2 o2 ON ot.id = o2.ref
       LEFT JOIN db_master.variabel v ON v.id_variabel = k.kesadaran
       LEFT JOIN aplikasi.pengguna peng ON peng.ID = ot.oleh
       LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ot.nokun
       LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
       WHERE ot.nokun = '$nokun' AND ot.`status`=1
       ORDER BY ot.updated_at DESC"
    );
    return $query->result();
  }

  public function pengkajianDashboard()
  {
    $nopen = $this->input->post('nopen');
    $query = $this->db->query("CALL keperawatan.DashboardPengkajian($nopen)");
    return $query->result();
  }

  public function ewsDashboard()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query("CALL keperawatan.DashboardEWS($nokun)");
    return $query->row();
  }

  public function pewsDashboard()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query("CALL keperawatan.DashboardPEWS($nokun)");
    return $query->row();
  }

  public function observasiKeperawatanDashboard()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query("CALL keperawatan.DashboardObservasi($nokun)");
    return $query->result();
  }

  public function pemberianCairanIntravenaDashboard()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query("CALL keperawatan.DashboardIntravena($nokun)");
    return $query->result();
  }

  public function resikoJatuhDashboard()
  {
    $nopen = $this->input->post('nopen');
    $query = $this->db->query("CALL keperawatan.DashboardResikoJatuh($nopen)");
    return $query->row();
  }

  public function keluhanUtamaDashboard()
  {
    $nopen = $this->input->post('nopen');
    $query = $this->db->query("CALL keperawatan.DashboardSubyektif($nopen)");
    return $query->result();
  }

  public function terapiPasienDashboard()
  {
    $nopen = $this->input->post('nopen');
    $query = $this->db->query("CALL keperawatan.DashboardTerapiPasien($nopen)");
    return $query->result();
  }

  public function instruksiDokterDashboard()
  {
    $nopen = $this->input->post('nopen');
    $query = $this->db->query("CALL keperawatan.DashboardInstruksiDokter($nopen)");
    return $query->result();
  }

  public function saturasiOksigenDashboard()
  {
    $nopen = $this->input->post('nopen');
    $query = $this->db->query("CALL keperawatan.DashboardSaturasi($nopen)");
    return $query->result();
  }

  public function tbakDashboard($nopen = null)
  {
    if ($nopen == null) {
      $nopen = $this->input->post('nopen');
    }
    $query = $this->db->query("CALL keperawatan.DashboardTBAK($nopen)");
    if ($nopen == null) {
      return $query->result_array();
    } else {
      return $query->result();
    }
  }

  public function getNomrRawatInap($nokun)
  {
    $jenis = $this->input->post('jenis') ? $this->input->post('jenis') : 0;
    $idemr = $this->input->post('idemr') ? $this->input->post('idemr') : 0;
    $query = $this->db->query(
      "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
        , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , pas.JENIS_KELAMIN ID_JK
        , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
        , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
        , p.TANGGAL TANGGAL_DAFTAR
        , r.JENIS_KUNJUNGAN
        , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
        , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
        ) DESKRIPSI_DIAGNOSA_MASUK
        , ref.ID IDPENJAMIN
        , ref.DESKRIPSI PENJAMIN
        , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
        dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
        )))) STATUS_KUNJUNGAN
        , penggu.ID ID_USER
        , dok.ID ID_DOKTER
        , pas.TANGGAL_LAHIR
        , dtt.NAME_PIC
        , pk.REF
        , mkp.NOMOR NOTLPN
        ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) ID_TUJUAN_FARMASI

        , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) TUJUAN_FARMASI
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
        , ppk.NAMA RUJUKAN_DARI
        , refdar.DESKRIPSI GOL_DARAH
        , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND IF(rk.ID='105140101',kepe.jenis=9, IF(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,kepe.jenis=5,kepe.jenis=6))
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC
                LIMIT 1) ID_EMR_KEPERAWATAN_DEWASA_RI
        , (SELECT id_emr FROM medis.tb_medis kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND IF((rk.ID='105030201' OR rk.ID='105030101') OR $jenis = 10,kepe.jenis=10 AND kepe.id_emr=$idemr ,IF($jenis=999,' ',IF((rk.ID='105140101' OR rk.ID='105140102'),kepe.jenis=9, IF(rk.ID='105020101',kepe.jenis=17,IF(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,kepe.jenis=5,kepe.jenis=6)))))
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC
                LIMIT 1) ID_EMR_MEDIS_DEWASA_RI

        FROM pendaftaran.pendaftaran p
        LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
        LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.`STATUS` != 0
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
        LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND refdar.JENIS=6
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
        LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
        LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM

        WHERE tp.`STATUS` not in (0,1)
        AND p.`STATUS`!= 0
        #AND pk.`STATUS` != 0
        AND pk.NOMOR = '$nokun'
        GROUP BY dtt.ID #DESC"
    );
    return $query->row_array();
  }

  public function getBrakhiterapi($nokun)
  {
    $query = $this->db->query(
      "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
        , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , pas.JENIS_KELAMIN ID_JK
        , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
        , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
        , p.TANGGAL TANGGAL_DAFTAR
        , r.JENIS_KUNJUNGAN
        , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
        , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
        ) DESKRIPSI_DIAGNOSA_MASUK
        , ref.ID IDPENJAMIN
        , ref.DESKRIPSI PENJAMIN
        , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
        dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
        )))) STATUS_KUNJUNGAN
        , penggu.ID ID_USER
        , dok.ID ID_DOKTER
        , pas.TANGGAL_LAHIR
        , dtt.NAME_PIC
        , pk.REF
        , mkp.NOMOR NOTLPN
        ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) ID_TUJUAN_FARMASI

        , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) TUJUAN_FARMASI
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
        , ppk.NAMA RUJUKAN_DARI
        , refdar.DESKRIPSI GOL_DARAH
        , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                WHERE kepe.nokun=pk.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=15
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC
                LIMIT 1) ID_EMR_KEPERAWATAN_DEWASA_RI
        , (SELECT id_emr FROM medis.tb_medis kepe
                WHERE kepe.nokun=pk.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=15
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC
                LIMIT 1) ID_EMR_MEDIS_DEWASA_RI

        FROM pendaftaran.pendaftaran p
        LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
        LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
        LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND ref.JENIS=6
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
        LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
        LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM

        WHERE tp.`STATUS` not in (0,1)
        AND p.`STATUS`!= 0
        AND pk.`STATUS` != 0 AND pk.NOMOR = '$nokun'
        GROUP BY dtt.ID #DESC"
    );
    return $query->row_array();
  }

  public function cekNokunHasilLabPk($norm)
  {
    $query = $this->db->query(
      "SELECT * FROM ((SELECT lhl.TANGGAL TANGGAL_HASIL, lhl.HASIL, pk.NOMOR NOKUN
        FROM layanan.hasil_lab lhl
        LEFT JOIN layanan.tindakan_medis tm ON tm.ID = lhl.TINDAKAN_MEDIS
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tm.KUNJUNGAN
        LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
        WHERE pp.NORM = '$norm'
        GROUP BY pk.NOMOR
        ORDER BY lhl.TANGGAL DESC)

        UNION

        (SELECT hl.LIS_TANGGAL, hl.LIS_HASIL, pk.NOMOR NOPEN
        FROM lis.hasil_log hl

        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = hl.HIS_NO_LAB
        LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
        WHERE pp.NORM = '$norm'
        GROUP BY pk.NOMOR
        ORDER BY hl.LIS_TANGGAL DESC)) a
        ORDER BY a.TANGGAL_HASIL DESC
        LIMIT 1"
    );
    return $query;
  }

  public function cekHasilLabPk($nokun)
  {
    $query = $this->db->query(
      "SELECT 
      master.getNamaLengkap(mp.NORM) NAMALENGKAP,
      LPAD(mp.NORM,8,'0') NORM, a.* 
      FROM (
          (SELECT 
              1 AS urutan, hlo.ID, pendK.NOMOR AS NOKUN, pendK.MASUK, hlo.LIS_TANGGAL, mt.NAMA, 
              hlo.LIS_NAMA_TEST AS PARAMETER, hlo.LIS_HASIL AS HASIL, hlo.LIS_KODE_TEST, ms.SKALA_BAWAH, ms.SKALA_ATAS, hlo.LIS_NILAI_NORMAL AS NILAI, 
              hlo.LIS_SATUAN AS SATUAN, hlo.LIS_CATATAN AS KETERANGAN, hlo.LIS_FLAG, 
              IF(hlo.LIS_FLAG = 'L' OR hlo.LIS_FLAG = 'VL' OR hlo.LIS_FLAG = 'H' OR hlo.LIS_FLAG = 'VH', 1, 0) AS NILAI_KRITIS
          FROM lis.hasil_log hlo
          LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
          LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pendK.NOPEN
          LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
          LEFT JOIN data_ihs.data_master_skala_hasil_lab ms ON ms.LIS_KODE_TEST LIKE hlo.LIS_KODE_TEST
          WHERE hlo.HIS_NO_LAB = '$nokun' AND 
          hlo.LIS_KODE_TEST IN ('00000019','00000034','00000013','00000019','00000034','00000096','00000097','00000104','00000105','00000251','00000013','00000105','00000719','00000013','00000019','00000034','00000252','00000505','00000681','00000762','00000099') AND hlo.LIS_HASIL < ms.SKALA_BAWAH AND hlo.LIS_HASIL > ms.SKALA_ATAS
          #mt.ID IN (1430,1435,1439,1593,1576,1526,1530,1534,1683,1540,1820)
          GROUP BY hlo.LIS_KODE_TEST
          ORDER BY pp.TANGGAL DESC)
          
          UNION ALL
          
          (SELECT 
              3 AS urutan, lhl.ID, pendK.NOMOR AS NOKUN, pendK.MASUK, lhl.TANGGAL, mt.NAMA, 
              mptl.PARAMETER, lhl.HASIL, lhl.PARAMETER_TINDAKAN, ms.SKALA_BAWAH, ms.SKALA_ATAS, lhl.NILAI, lhl.SATUAN, lhl.KETERANGAN, lhl.LIS_FLAG, 
              IF(lhl.LIS_FLAG = 'L' OR lhl.LIS_FLAG = 'VL' OR lhl.LIS_FLAG = 'H' OR lhl.LIS_FLAG = 'VH', 1, 0) AS NILAI_KRITIS
          FROM pendaftaran.kunjungan pendK
          LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pendK.NOPEN
          LEFT JOIN layanan.tindakan_medis ltm ON ltm.KUNJUNGAN = pendK.NOMOR
          LEFT JOIN master.tindakan mt ON mt.ID = ltm.TINDAKAN
          LEFT JOIN master.parameter_tindakan_lab mptl ON mptl.TINDAKAN = mt.ID AND mptl.STATUS != 0
          LEFT JOIN layanan.hasil_lab lhl ON lhl.TINDAKAN_MEDIS = ltm.ID AND lhl.PARAMETER_TINDAKAN = mptl.ID
          LEFT JOIN data_ihs.data_master_skala_hasil_lab ms ON ms.PARAMETER_TINDAKAN = lhl.PARAMETER_TINDAKAN
          WHERE pendK.NOMOR = '$nokun' AND 
          ltm.STATUS = 1 AND lhl.PARAMETER_TINDAKAN IN (1430004,1430005,1435006,1435007,1435015,1530001,1534001,1540001,1526001,1820001,1430003,1526001,1576009,1439006,1439008,1439017,1494001,1593008,1593010,1593014,1683001) AND lhl.HASIL < ms.SKALA_BAWAH AND lhl.HASIL > ms.SKALA_ATAS
          #mt.ID IN (1430,1435,1439,1593,1576,1526,1530,1534,1683,1540,1820)
          GROUP BY mptl.ID
          ORDER BY pp.TANGGAL DESC)
          
          UNION ALL
          
          (SELECT 
              2 AS urutan, lhl.ID, pendK.NOMOR AS NOKUN, pendK.MASUK, lhl.TANGGAL, mt.NAMA, 
              mptl.PARAMETER, lhl.HASIL, lhl.PARAMETER_TINDAKAN, ms.SKALA_BAWAH, ms.SKALA_ATAS, lhl.NILAI, lhl.SATUAN, lhl.KETERANGAN, lhl.LIS_FLAG, 
              IF(lhl.LIS_FLAG = 'L' OR lhl.LIS_FLAG = 'VL' OR lhl.LIS_FLAG = 'H' OR lhl.LIS_FLAG = 'VH', 1, 0) AS NILAI_KRITIS
          FROM pendaftaran.kunjungan pendK
          LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pendK.NOPEN
          LEFT JOIN layanan.tindakan_medis ltm ON ltm.KUNJUNGAN = pendK.NOMOR
          LEFT JOIN master.tindakan mt ON mt.ID = ltm.TINDAKAN
          LEFT JOIN master.parameter_tindakan_lab mptl ON mptl.TINDAKAN = mt.ID AND mptl.STATUS != 0
          LEFT JOIN layanan.hasil_lab lhl ON lhl.TINDAKAN_MEDIS = ltm.ID AND lhl.PARAMETER_TINDAKAN = mptl.ID
          LEFT JOIN data_ihs.data_master_skala_hasil_lab ms ON ms.PARAMETER_TINDAKAN = lhl.PARAMETER_TINDAKAN
          WHERE pendK.NOMOR = '$nokun' AND 
          ltm.STATUS = 1 AND lhl.HASIL IS NULL AND lhl.PARAMETER_TINDAKAN IN (1430004,1430005,1435006,1435007,1435015,1530001,1534001,1540001,1526001,1820001,1430003,1526001,1576009,1439006,1439008,1439017,1494001,1593008,1593010,1593014,1683001) AND lhl.HASIL < ms.SKALA_BAWAH AND lhl.HASIL > ms.SKALA_ATAS
          #mt.ID IN (1430,1435,1439,1593,1576,1526,1530,1534,1683,1540,1820)
          GROUP BY mptl.ID
          ORDER BY pp.TANGGAL DESC)
      ) a

      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = a.NOKUN
      LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
      LEFT JOIN master.pasien mp ON mp.NORM = pp.NORM

      WHERE pk.NOMOR = '$nokun' AND a.HASIL IS NOT NULL

      GROUP BY a.NAMA, a.PARAMETER
      ORDER BY a.NAMA ASC"
    );
    return $query;
  }

  public function detail_kunjungan_pk_kritis($nokun)
  {
    // $nokun = $this->input->post('nokun');
    $query = $this->db->query(
      "SELECT pk.NOMOR, pk.MASUK TGL, ptm.TINDAKAN_MEDIS,
      CONCAT(peg.GELAR_DEPAN,' ',peg.NAMA,' ',peg.GELAR_BELAKANG) AS NMDA,
      CONCAT(peg1.GELAR_DEPAN,' ',peg1.NAMA,' ',peg1.GELAR_BELAKANG) AS NMDL
      , r.deskripsi RUANGASAL, pk.NOPEN

      FROM pendaftaran.kunjungan pk
      LEFT JOIN layanan.tindakan_medis tmi ON tmi.KUNJUNGAN=pk.NOMOR
      LEFT JOIN master.tindakan mt ON mt.ID=tmi.TINDAKAN
      LEFT JOIN layanan.order_detil_lab odl ON odl.REF=tmi.ID
      LEFT JOIN layanan.order_lab orlab ON orlab.NOMOR=odl.ORDER_ID
      LEFT JOIN master.ruangan r ON substr(pk.REF,3,9)=r.ID AND r.JENIS=5 AND pk.REF=orlab.NOMOR
      LEFT JOIN master.dokter d ON orlab.DOKTER_ASAL = d.ID
      LEFT JOIN master.pegawai peg ON d.NIP = peg.NIP
      LEFT JOIN layanan.petugas_tindakan_medis ptm ON tmi.ID = ptm.TINDAKAN_MEDIS
      LEFT JOIN master.dokter d1 ON ptm.MEDIS = d1.ID
      LEFT JOIN master.pegawai peg1 ON d1.NIP = peg1.NIP
      WHERE pk.NOMOR = '$nokun'
      ORDER BY ptm.TINDAKAN_MEDIS DESC"
    );
    return $query->row_array();
  }

  public function tanggal_sampling_kritis($nokun)
  {
    // $nokun = $this->input->post('nokun');
    $query = $this->dblis->query(
      "SELECT r.retrieved_dt, rb.authorization_date
      FROM lis_bridging.registration r
      LEFT JOIN lis_bridging.result_bridge_lis rb ON r.order_number = rb.his_reg_no
      WHERE r.order_number = '$nokun'"
    );
    return $query->row_array();
  }

  public function log_kritis($nokun)
  {
    // $nokun = $this->input->post('nokun');
    $query = $this->db->query(
      "SELECT * FROM log.log_hasil_lab_kritis hlk WHERE hlk.nokun = '$nokun'"
    );
    return $query;
  }
}

/* End of file MedisModel.php */
/* Location: ./application/models/rekam_medis/MedisModel.php */