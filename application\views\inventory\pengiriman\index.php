<div class="row">
    <div class="col-12">
        <div class="card-box table-responsive">
            <ul class="nav nav-tabs nav-justified" style="color:white;">
                <li class="nav-item">
                    <a href="#formpengiriman_tab" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        Form pengiriman barang
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#historypengiriman_tab" data-toggle="tab" aria-expanded="true" class="nav-link">
                        History pengiriman barang
                    </a>
                </li>
            </ul>
            <div class="tab-content" style="border-color:#40739e">
                <div role="tabpanel" class="tab-pane fade show active" id="formpengiriman_tab">
                    <div class="row">
                        <div class="col-12">
                            <select class="form-control select2" id="gudang" name="id_gudang" onchange="cek_data()">
                                <option value=""></option>
                                <?php foreach ($gudang as $k) {
                                    echo "<option value='$k->ID'>$k->DESKRIPSI</option>";
                                } ?>
                            </select>
                            <br><br>
                            <div class="loading"></div>
                            <div class="tampilkan_data"></div>
                        </div>
                    </div> <!-- end row -->
                </div>
                <div role="tabpanel" class="tab-pane fade" id="historypengiriman_tab">
                    <div class="table-responsive">
                        <table id="tabeldatapengiriman" class="table table-bordered table-bordered dt-responsive " cellspacing="0" width="100%">
                            <thead style="color:#fff;">
                                <tr>
                                    <th>#</th>
                                    <th>Unit peminta</th>
                                    <th>Gudang</th>
                                    <th>Tanggal pengiriman</th>
                                    <!--                                     <th>Status</th> -->
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody style="color:#fff;"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div> <!-- end row -->
</div>
<!-- Modal Start -->
<div class="modal fade" id="detailpengiriman" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0" id="mySmallModalLabel">Detail Pengiriman</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div id="viewdetailpengiriman"></div>
            </div>
        </div>
    </div>
</div>


<link rel="stylesheet" type="text/css" href="<?= base_url() ?>assets/admin/assets/css/jquery-ui.css" />
<script src="<?= base_url() ?>assets/admin/assets/js/jquery-ui.js"></script>
<script type="text/javascript">
    function cek_data() {
        sel_gudang = $('[name="id_gudang"]');
        $.ajax({
            type: 'POST',
            data: "cari=" + 1 + "&id_gudang=" + sel_gudang.val(),
            url: 'pengiriman/view_data',
            cache: false,
            beforeSend: function() {
                sel_gudang.attr('disabled', true);
                // $('.loading').html('Loading...');
            },
            success: function(data) {
                sel_gudang.attr('disabled', false);
                // $('.loading').html('');
                $('.tampilkan_data').html(data);
            }
        });
        return false;
    }

    $('#tabeldatapengiriman').DataTable({
        "ajax": {
            url: '<?php echo base_url() ?>inventory/Pengiriman/datapengiriman',
            type: 'GET'
        }
    });
</script>
<script type="text/javascript">
    $('#detailpengiriman').on('show.bs.modal', function(e) {
        var id = $(e.relatedTarget).data('id');
        $.ajax({
            type: 'POST',
            url: '<?php echo base_url() ?>inventory/Pengiriman/datamodalkirim',
            data: {
                id: id
            },
            success: function(data) {
                $('#viewdetailpengiriman').html(data);
            }
        });
    });
</script>