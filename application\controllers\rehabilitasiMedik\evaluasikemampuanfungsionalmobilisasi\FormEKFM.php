<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormEKFM extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function action_ekfm($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $get_id_ekfm = !empty($post['id_ekfm']) ? $post['id_ekfm'] : $this->pengkajianAwalModel->getIdEmr();

        $dataekfm= array(
          'id_ekfm' => $get_id_ekfm,
          'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
          'barthel_index' => isset($post['barthel_index']) ? $post['barthel_index'] : null,
          'diagnosis_medik' => isset($post['diagnosis_medik']) ? $post['diagnosis_medik'] : null,
          'diagnosis_fungsi' => isset($post['diagnosis_fungsi']) ? $post['diagnosis_fungsi'] : null,
          'jenis_rawat_ekfm' => isset($post['jenis_rawat_ekfm']) ? $post['jenis_rawat_ekfm'] : null,
          'ruangan_ekfm' => isset($post['ruangan_ekfm']) ? $post['ruangan_ekfm'] : null,
          'tanggal_kunjungan_ekfm' => isset($post['tanggal_kunjungan_ekfm']) ? $post['tanggal_kunjungan_ekfm'] : null,
          'shifting_bantuan_penuh' => isset($post['shifting_bantuan_penuh']) ? $post['shifting_bantuan_penuh'] : null,
          'shifting_bantuan_sebagian' => isset($post['shifting_bantuan_sebagian']) ? $post['shifting_bantuan_sebagian'] : null,
          'shifting_tanpa_bantuan' => isset($post['shifting_tanpa_bantuan']) ? $post['shifting_tanpa_bantuan'] : null,
          'baring_bantuan_penuh' => isset($post['baring_bantuan_penuh']) ? $post['baring_bantuan_penuh'] : null,
          'baring_bantuan_sebagian' => isset($post['baring_bantuan_sebagian']) ? $post['baring_bantuan_sebagian'] : null,
          'baring_tanpa_bantuan' => isset($post['baring_tanpa_bantuan']) ? $post['baring_tanpa_bantuan'] : null,
          'duduk_dengan_sandaran' => isset($post['duduk_dengan_sandaran']) ? $post['duduk_dengan_sandaran'] : null,
          'duduk_tanpa_sandaran' => isset($post['duduk_tanpa_sandaran']) ? $post['duduk_tanpa_sandaran'] : null,
          'transfer_bantuan_penuh' => isset($post['transfer_bantuan_penuh']) ? $post['transfer_bantuan_penuh'] : null,
          'transfer_bantuan_sebagian' => isset($post['transfer_bantuan_sebagian']) ? $post['transfer_bantuan_sebagian'] : null,
          'transfer_tanpa_bantuan' => isset($post['transfer_tanpa_bantuan']) ? $post['transfer_tanpa_bantuan'] : null,
          'berdiri_bantuan_penuh' => isset($post['berdiri_bantuan_penuh']) ? $post['berdiri_bantuan_penuh'] : null,
          'berdiri_bantuan_sebagian' => isset($post['berdiri_bantuan_sebagian']) ? $post['berdiri_bantuan_sebagian'] : null,
          'berdiri_dengan_alat_bantu' => isset($post['berdiri_dengan_alat_bantu']) ? $post['berdiri_dengan_alat_bantu'] : null,
          'berdiri_tanpa_bantuan' => isset($post['berdiri_tanpa_bantuan']) ? $post['berdiri_tanpa_bantuan'] : null,
          'ambulasi_kursi_roda' => isset($post['ambulasi_kursi_roda']) ? $post['ambulasi_kursi_roda'] : null,
          'ambulasi_dengan_alat_bantu_jalan' => isset($post['ambulasi_dengan_alat_bantu_jalan']) ? $post['ambulasi_dengan_alat_bantu_jalan'] : null,
          'ambulasi_dengan_bantuan_orang' => isset($post['ambulasi_dengan_bantuan_orang']) ? $post['ambulasi_dengan_bantuan_orang'] : null,
          'ambulasi_mandiri_dalam_supervisi' => isset($post['ambulasi_mandiri_dalam_supervisi']) ? $post['ambulasi_mandiri_dalam_supervisi'] : null,
          'ambulasi_mandiri_penuh' => isset($post['ambulasi_mandiri_penuh']) ? $post['ambulasi_mandiri_penuh'] : null,
          'pemberdayaan_keluarga_minus' => isset($post['pemberdayaan_keluarga_minus']) ? $post['pemberdayaan_keluarga_minus'] : null,
          'pemberdayaan_keluarga_plus' => isset($post['pemberdayaan_keluarga_plus']) ? $post['pemberdayaan_keluarga_plus'] : null,
          'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

        // print_r($dataekfm);exit();

        if (!empty($post['id_ekfm'])) {
          $this->db->replace('keperawatan.tb_evaluasi_kemampuan_fungsional_mobilisasi', $dataekfm);
          $result = array('status' => 'success', 'pesan' => 'ubah');
        }else {
          $this->db->insert('keperawatan.tb_evaluasi_kemampuan_fungsional_mobilisasi', $dataekfm);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }
}


/* End of file FormEKFM.php */
/* Location: ./application/controllers/rehabilitasiMedik/evaluasikemampuanfungsionalmobilisasi/FormEKFM.php */
