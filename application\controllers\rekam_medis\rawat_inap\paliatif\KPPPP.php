<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KPPPP extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/paliatif/KPPPPModel', 'EresepModel'));
    }

    public function index()
    {
        $norm = $this->uri->segment(6);
        $nopen = $this->uri->segment(7);
        $nokun = $this->uri->segment(8);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $resep = $this->KPPPPModel->riwayatResep($norm);
        $resepPilih = $this->EresepModel->riwayatResepPilih($norm);

        $data = [
            'norm' => $norm,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'getNomr' => $getNomr,
            'resep' => $resep,
            'resepPilih' => $resepPilih,
        ];

        $this->load->view('rekam_medis/rawat_inap/paliatif/KPPPP/index', $data);
    }


    public function detilResep()
    {
        $id = $this->input->post('id');

        $dResepModel = $this->KPPPPModel->detilResep($id);
        $dOResep = $this->EresepModel->detailOrderResep($id);

        $no = 1;
        foreach ($dResepModel as $dResep) :
            $generik = $dResep['GENERIK'] == "" ? "" : " (" . $dResep['GENERIK'] . ")";

            echo "<tr>";
            echo "<td><input type='hidden' name='ID_RESEP' value='$id'>" . $no . "</td>";
            echo "<td>" . $dResep['NAMA_OBAT'] . $generik . "</td>";
            echo "<td>" . $dResep['ATURAN_PAKAI'] . "</td>";
            echo "<td>" . $dResep['JUMLAH'] . "</td>";
            echo "</tr>";

            $no++;
        endforeach;
    }


    public function simpanKPPPP()
    {
        $this->db->trans_begin();
        date_default_timezone_set('Asia/Jakarta');

        $post = $this->input->post();

        if ($post['method'] == 'insert') {
            $dataKPPPP = array(
                'NORM'                      => $post['norm'],
                'NOPEN'                     => $post['nopen'],
                'NOKUN'                     => $post['nokun'],
                'TGL_INPUT'                 => date('Y-m-d H:i:s'),
                'statusKesehatan'           => $post['statusKesehatan'],
                'rencanaPerawatan'          => $post['rencanaPerawatan'],
                'keteranganRencanaLain'     => $post['keteranganRencanaLain'],
                'mengertiMenerima'          => $post['mengertiMenerima'],
                'mempersiapkanDiri'         => $post['mempersiapkanDiri'],
                'memahamiPerawatan'         => $post['memahamiPerawatan'],
                'oralHygiene'               => $post['oralHygiene'],
                'perawatanKulit'            => $post['perawatanKulit'],
                'memandikanPasien'          => $post['memandikanPasien'],
                'caraMobilisasi'            => $post['caraMobilisasi'],
                'perawatanKateter'          => $post['perawatanKateter'],
                'perawatanNGT'              => $post['perawatanNGT'],
                'gantiBalutan'              => $post['gantiBalutan'],
                'faktorKeluargaLain'        => $post['faktorKeluargaLain'],
                'caregiver1'                => $post['caregiver1'],
                'caregiver2'                => $post['caregiver2'],
                'caregiver3'                => $post['caregiver3'],
                'gejalaPaliatif'            => $post['gejalaPaliatif'],
                'pengertianPasien'          => $post['pengertianPasien'],
                'intakeOral'                => $post['intakeOral'],
                'intakeOral_ket'            => $post['intakeOral_ket'],
                'intakeNGT'                 => $post['intakeNGT'],
                'intakeNGT_ket'             => $post['intakeNGT_ket'],
                'intakeSCInfus'             => $post['intakeSCInfus'],
                'intakeSCInfus_ket'         => $post['intakeSCInfus_ket'],
                'intakeIVInfus'             => $post['intakeIVInfus'],
                'intakeIVInfus_ket'         => $post['intakeIVInfus_ket'],
                'eliminasiSpontan'          => $post['eliminasiSpontan'],
                'eliminasiSpontan_ket'      => $post['eliminasiSpontan_ket'],
                'eliminasiKateter'          => $post['eliminasiKateter'],
                'eliminasiKateter_ket'      => $post['eliminasiKateter_ket'],
                'eliminasiNefrostomy'       => $post['eliminasiNefrostomy'],
                'eliminasiNefrostomy_ket'   => $post['eliminasiNefrostomy_ket'],
                'eliminasiColostomy'        => $post['eliminasiColostomy'],
                'eliminasiColostomy_ket'    => $post['eliminasiColostomy_ket'],
                'respirasiOksigen'          => $post['respirasiOksigen'],
                'respirasiOksigen_ket'      => $post['respirasiOksigen_ket'],
                'respirasiTracheostomy'     => $post['respirasiTracheostomy'],
                'respirasiTracheostomy_ket' => $post['respirasiTracheostomy_ket'],
                'respirasiSuction'          => $post['respirasiSuction'],
                'respirasiSuction_ket'      => $post['respirasiSuction_ket'],
                'respirasiNebulizer'        => $post['respirasiNebulizer'],
                'respirasiNebulizer_ket'    => $post['respirasiNebulizer_ket'],
                'skincareDekubitus'         => $post['skincareDekubitus'],
                'skincareDekubitus_ket'     => $post['skincareDekubitus_ket'],
                'skincareLuka'              => $post['skincareLuka'],
                'skincareLuka_ket'          => $post['skincareLuka_ket'],
                'ID_RESEP'                  => $post['ID_RESEP'],
                'tglKontrolPaliatif'        => $post['tglKontrolPaliatif'],
                'tglKontrolDPJP'            => $post['tglKontrolDPJP'],
                'rencanaKontrolObat'        => $post['rencanaKontrolObat'],
                'rencanaKontrolGejala'      => $post['rencanaKontrolGejala'],
                'rencanaKontrolTindakan'    => $post['rencanaKontrolTindakan'],
                'OLEH'                      => $this->session->userdata('id'),
                'STATUS'                    => 1,
            );

            // echo "<pre>";
            // print_r($dataKPPPP);
            // echo "</pre>";
            $this->db->insert('medis.tb_kppp_paliatif', $dataKPPPP);

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }
        } elseif ($post['method'] == 'update') {
            $dataKPPPPEdit = array(
                'NORM'                      => $post['norm'],
                'NOPEN'                     => $post['nopen'],
                'NOKUN'                     => $post['nokun'],
                'TGL_EDIT'                  => date('Y-m-d H:i:s'),
                'statusKesehatan'           => $post['view_statusKesehatan'],
                'rencanaPerawatan'          => $post['view_rencanaPerawatan'],
                'keteranganRencanaLain'     => $post['view_keteranganRencanaLain'],
                'mengertiMenerima'          => $post['view_mengertiMenerima'],
                'mempersiapkanDiri'         => $post['view_mempersiapkanDiri'],
                'memahamiPerawatan'         => $post['view_memahamiPerawatan'],
                'oralHygiene'               => $post['view_oralHygiene'],
                'perawatanKulit'            => $post['view_perawatanKulit'],
                'memandikanPasien'          => $post['view_memandikanPasien'],
                'caraMobilisasi'            => $post['view_caraMobilisasi'],
                'perawatanKateter'          => $post['view_perawatanKateter'],
                'perawatanNGT'              => $post['view_perawatanNGT'],
                'gantiBalutan'              => $post['view_gantiBalutan'],
                'faktorKeluargaLain'        => $post['view_faktorKeluargaLain'],
                'caregiver1'                => $post['view_caregiver1'],
                'caregiver2'                => $post['view_caregiver2'],
                'caregiver3'                => $post['view_caregiver3'],
                'gejalaPaliatif'            => $post['view_gejalaPaliatif'],
                'pengertianPasien'          => $post['view_pengertianPasien'],
                'intakeOral'                => $post['view_intakeOral'],
                'intakeOral_ket'            => $post['view_intakeOral_ket'],
                'intakeNGT'                 => $post['view_intakeNGT'],
                'intakeNGT_ket'             => $post['view_intakeNGT_ket'],
                'intakeSCInfus'             => $post['view_intakeSCInfus'],
                'intakeSCInfus_ket'         => $post['view_intakeSCInfus_ket'],
                'intakeIVInfus'             => $post['view_intakeIVInfus'],
                'intakeIVInfus_ket'         => $post['view_intakeIVInfus_ket'],
                'eliminasiSpontan'          => $post['view_eliminasiSpontan'],
                'eliminasiSpontan_ket'      => $post['view_eliminasiSpontan_ket'],
                'eliminasiKateter'          => $post['view_eliminasiKateter'],
                'eliminasiKateter_ket'      => $post['view_eliminasiKateter_ket'],
                'eliminasiNefrostomy'       => $post['view_eliminasiNefrostomy'],
                'eliminasiNefrostomy_ket'   => $post['view_eliminasiNefrostomy_ket'],
                'eliminasiColostomy'        => $post['view_eliminasiColostomy'],
                'eliminasiColostomy_ket'    => $post['view_eliminasiColostomy_ket'],
                'respirasiOksigen'          => $post['view_respirasiOksigen'],
                'respirasiOksigen_ket'      => $post['view_respirasiOksigen_ket'],
                'respirasiTracheostomy'     => $post['view_respirasiTracheostomy'],
                'respirasiTracheostomy_ket' => $post['view_respirasiTracheostomy_ket'],
                'respirasiSuction'          => $post['view_respirasiSuction'],
                'respirasiSuction_ket'      => $post['view_respirasiSuction_ket'],
                'respirasiNebulizer'        => $post['view_respirasiNebulizer'],
                'respirasiNebulizer_ket'    => $post['view_respirasiNebulizer_ket'],
                'skincareDekubitus'         => $post['view_skincareDekubitus'],
                'skincareDekubitus_ket'     => $post['view_skincareDekubitus_ket'],
                'skincareLuka'              => $post['view_skincareLuka'],
                'skincareLuka_ket'          => $post['view_skincareLuka_ket'],
                'tglKontrolPaliatif'        => $post['view_tglKontrolPaliatif'],
                'tglKontrolDPJP'            => $post['view_tglKontrolDPJP'],
                'rencanaKontrolObat'        => $post['view_rencanaKontrolObat'],
                'rencanaKontrolGejala'      => $post['view_rencanaKontrolGejala'],
                'rencanaKontrolTindakan'    => $post['view_rencanaKontrolTindakan'],
                'EDIT_OLEH'                 => $this->session->userdata('id'),
            );
            $this->db->where('id', $post['idKPPPP']);
            $this->db->update('medis.tb_kppp_paliatif', $dataKPPPPEdit);

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }
        }

        echo json_encode($result);
    }
    public function modalKPPPP()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        $norm = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $resep = $this->EresepModel->riwayatResep($norm);
        $resepPilih = $this->EresepModel->riwayatResepPilih($norm);

        $data = array(
            'id' => $id,
            'norm' => $norm,
            'getNomr' => $getNomr,
            'kpppp' => $this->KPPPPModel->getDataKPPPP($id),
            'resep' => $resep,
            'resepPilih' => $resepPilih,
        );

        $this->load->view('rekam_medis/rawat_inap/paliatif/KPPPP/view', $data);
    }

    public function historyKPPPP()
    {
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));

        $nomr = $this->input->post('nomr');
        // $nomr = $this->uri->segment(6);
        $listKPP = $this->KPPPPModel->listHistoryKPPPP($nomr);

        $data = array();
        $no = 1;
        foreach ($listKPP->result() as $KPPPP) {

            $button = '<button type="button" href="#modalKPPPP" class="btn btn-primary btn-block" data-id="' . $KPPPP->ID . '" data-toggle="modal" data-backdrop="static" data-keyboard="false" ><i class="fa fa-pencil-square-o"></i> View</button>';

            $data[] = array(
                $no,
                $KPPPP->NOKUN,
                $KPPPP->TGL_INPUT,
                ($KPPPP->TGL_EDIT != NULL || $KPPPP->TGL_EDIT != "") ? $KPPPP->TGL_EDIT : '-',
                $KPPPP->OLEH,
                ($KPPPP->OLEH_UPDATE != NULL || $KPPPP->OLEH_UPDATE != "") ? $KPPPP->OLEH_UPDATE : '-',
                $button,

            );
            $no++;
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listKPP->num_rows(),
            "recordsFiltered" => $listKPP->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }
}
/* End of file HADS.php */
