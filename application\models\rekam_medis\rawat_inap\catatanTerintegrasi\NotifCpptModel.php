<?php
class NotifCpptModel extends CI_Model{
    
    function dataNotifCppt($id_pengguna){
        $query="SELECT z.*, c.id id_cppt, c.oleh c_user, master.getNamaLengkapPegawai(ap.NIP) nama_user,mp.SMF smf_dr_cppt
		, md2.ID dr_input
		, c.tanggal c_tgl_input, c.status_verif, c.verif_oleh, c.tanggal_verif
		, c.jenis c_jenis, c.pemberi_cppt

                FROM 
                (
                    SELECT ap2.ID id_pgn_dpjp, ptp.DOKTER id_dpjp, ptp.SMF smf_dpjp,md.NIP nip_dpjp, master.getNamaLengkapPegawai(md.NIP) nama_dpjp
                            , pp.NOMOR nopen1, pp.NORM, master.getNamaLengkap(pp.NORM) nama_pasien
                            , CONCAT((date_format(mps.TANGGAL_LAHIR,'%d-%m-%Y')),' (',(master.getCariUmurTahun(CURDATE(), DATE(mps.TANGGAL_LAHIR))),')') tgl_lahir_umur
                            , pp.TANGGAL tgl_daftar, ref.DESKRIPSI penjamin
                            , rmp.RENCANA id_rencana, rmr.DESKRIPSI rencana_desk
                            , pk.NOMOR nokun1
                            , mr.DESKRIPSI nama_ruang, mr.JENIS, mr.JENIS_KUNJUNGAN
                    FROM pendaftaran.tujuan_pasien ptp 
                    
                    LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = ptp.NOPEN
                    LEFT JOIN remun_medis.perjanjian rmp ON rmp.NOMR = pp.NORM AND rmp.TANGGAL = DATE(pp.TANGGAL) AND rmp.STATUS!=0
                    LEFT JOIN remun_medis.rencana rmr ON rmr.ID = rmp.RENCANA
                    LEFT JOIN master.dokter md ON md.ID = ptp.DOKTER
                    LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
                    LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
                    LEFT JOIN aplikasi.pengguna ap2 ON ap2.NIP = md.NIP
                    LEFT JOIN pendaftaran.penjamin pjmn ON pjmn.NOPEN = pp.NOMOR
                    LEFT JOIN master.referensi ref ON ref.ID = pjmn.JENIS AND ref.JENIS=10
                    LEFT JOIN master.pasien mps ON mps.NORM = pp.NORM
                    
                
                    WHERE pp.STATUS!=?
                    AND date(pp.TANGGAL) >= date_sub(CURDATE(), INTERVAL 30 DAY)
                    AND ap2.ID =?
                    AND rmp.RENCANA IN (2,3,4,5)
                    
                )z 

                left join keperawatan.tb_cppt c ON z.nokun1 = c.nokun
                LEFT JOIN aplikasi.pengguna ap ON ap.ID = c.oleh
                LEFT JOIN master.dokter md2 ON md2.NIP = ap.NIP
                lEFT JOIN master.pegawai mp ON mp.NIP = ap.NIP

                WHERE c.status != ?
                and c.id IS NOT NULL
                AND c.jenis=?
                AND c.pemberi_cppt=?
                AND c.status_verif =?
                AND md2.ID != z.id_dpjp
                AND mp.SMF = ?
                AND DATE(c.tanggal) >= date_sub(CURDATE(), INTERVAL 7 DAY)

                GROUP BY c.id
                ORDER BY c.tanggal DESC";

        $bind = $this->db->query($query, array(0,$id_pengguna,0,1,1,0,31)); 
        return $bind;
    }
}
?>