<?php
defined('BASEPATH') or exit('No direct script access allowed');

class DiagnosisDpjp extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'pengkajianAwalModel',
                'DiagnosisDpjpModel'
            ]
        );
    }

    public function index()
    {
        $post = $this->input->post();
        $nomr = $post['nomr'];

        $data = [
            'nomr' => $nomr,
            'nopen' => $post['nopen'],
            'nokun' => $post['nokun'],
            'jumlah' => $this->DiagnosisDpjpModel->cekMr($nomr, 1),
            'data_pendaftaran'  => $this->DiagnosisDpjpModel->getPendaftaran($post['nopen'])
        ];
        //echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/viewInputDiagnosaDpjp', $data);
    }

    public function cek()
    {
        $post = $this->input->post();
        //echo '<pre>';print_r($post);exit();
        $nomr = $post['nomr'];
        $kategori = $post['kategori'] ?? null;
        echo json_encode($this->DiagnosisDpjpModel->cekMr($nomr, $kategori));
    }

    public function simpan()
    {
        $post = $this->input->post();
        $data = [
            'NOMR' => $post['nomr'],
            'NOPEN' => $post['nopen'],
            'KUNJUNGAN' => $post['nokun'],
            'DIAGNOSA' => $post['nmdiagnosa'],
            'KATEGORI_DIAGNOSA' => null,
            'OLEH' => $this->session->userdata('id'),
            'STATUS' => 1,
        ];

        // Panggil metode simpan() di model
        $result = $this->DiagnosisDpjpModel->simpan($data);

        if (!$result) {
            // Jika gagal, kirim respon gagal
            echo json_encode(['status' => 'error', 'message' => 'Diagnosis sudah ada untuk NOPEN dan KUNJUNGAN yang sama']);
        } else {
            // Jika berhasil, kirim respon sukses
            echo json_encode(['status' => 'success', 'message' => 'Diagnosis berhasil disimpan']);
        }
    }


    // Controller method untuk mengambil data TANGGAL - NOPEN
    public function getListNopen($nomr)
    {
        $nopens = $this->DiagnosisDpjpModel->listNopen($nomr);
        print_r($this->db->last_query());

        // Kirim data ke view dalam format JSON
        echo json_encode($nopens);
    }


    public function tabel()
    {
        $data = [];
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $nomr = $post['nomr'];
        $draw = intval($post['draw']);
        $no = $post['start'];
        
        $tabel = $this->DiagnosisDpjpModel->ambil($nomr);
        $kategori_diagnosis = null;
        $tgl_diubah = null;

        foreach ($tabel as $t) {
            // mulai kategori
            if ($t->kategori == 1) {
                $kategori_diagnosis = 'Primer';
            } elseif ($t->kategori == 2) {
                $kategori_diagnosis = 'Sekunder';
            }
            // akhir kategori

            // mulai tanggal diubah
            if (isset($t->tgl_diubah) && $t->tgl_diubah != $t->tgl_dibuat) {
                $tgl_diubah = date('d/m/Y, H.i.s', strtotime($t->tgl_diubah));
            } else {
                $tgl_diubah = '-';
            }
            // akhir tanggal diubah

            // mulai status
            if ($t->status == 1) {
                $checked = 'checked';
            } else {
                $checked = null;
            }
            // akhir status

            $data[] = [
                ++$no . '.',
                $t->diagnosis,
                "<div class='checkbox checkbox-primary'>
                    <input type='checkbox' name='kategoriDiagnosa' id='kategoriDiagnosisDpjp" . $t->id_pdd . "' data-id='" . $t->id_pdd . "' class='kategori-diagnosis-dpjp' " . $checked . ">
                    <label for='kategoriDiagnosisDpjp" . $t->id_pdd . "'></label>
                </div>"
            ];
        }
        // echo '<pre>';print_r($data);exit();

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->DiagnosisDpjpModel->hitung_semua($nomr),
            'recordsFiltered' => $this->DiagnosisDpjpModel->hitung_tersaring($nomr),
            'data' => $data
        ];

        // if ($draw == 1) {
        echo json_encode($output);
        // }
    }

    public function tableNew()
    {
        $data = [];
        $post = $this->input->post();
        $nomr = $post['nomr'];
        $draw = intval($post['draw']);
        $no = $post['start'];
        $nopen = isset($post['nopen']) ? $post['nopen'] : null;
        $source = isset($post['source']) ? $post['source'] : 'Default'; // Get source from POST

        $tabel = $this->DiagnosisDpjpModel->ambil($nomr, $nopen);

        $dataTemp = [];

        foreach ($tabel as $t) {
            // Determine diagnosis category
            $kategori_diagnosis = ($t->kategori == 1) ? 'Primer' : 
                                (($t->kategori == 2) ? 'Sekunder' : null);
            
            // Determine modification date
            $tgl_diubah = (isset($t->tgl_diubah) && $t->tgl_diubah != $t->tgl_dibuat) 
                ? date('d/m/Y, H.i.s', strtotime($t->tgl_diubah)) 
                : '-';
            
            // Determine checkbox status
            $checked = ($t->status == 1 && $t->NOPEN == $nopen) ? 'checked' : '';
            $disabled = ($t->NOPEN == $nopen && $t->status == 1 && $this->session->userdata('id') != $t->created_by) ? 'disabled' : '';
            
            // Generate unique checkbox ID and class based on source from POST
            $checkboxId = "kategoriDiagnosis{$source}" . $t->id_pdd;
            $checkboxClass = "kategori-diagnosis-" . strtolower($source);
            
            $dataTemp[] = [
                'checked' => $checked ? 1 : 0, // Add a flag for checked
                'row' => [
                    ++$no . '.',
                    $t->diagnosis,
                    "<div class='checkbox checkbox-primary'>
                        <input type='checkbox' name='kategoriDiagnosa' id='{$checkboxId}' 
                            data-id='" . $t->id_pdd . "' 
                            data-diagnosis='" . htmlspecialchars($t->diagnosis) . "' 
                            class='{$checkboxClass}' {$checked}>
                        <label for='{$checkboxId}'></label>
                    </div>"
                ]
            ];
        }

        // Sort the dataTemp array so that checked items appear first
        usort($dataTemp, function ($a, $b) {
            return $b['checked'] <=> $a['checked'];
        });

        // Extract sorted rows
        foreach ($dataTemp as $item) {
            $data[] = $item['row'];
        }

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->DiagnosisDpjpModel->hitung_semua($nomr, $nopen),
            'recordsFiltered' => $this->DiagnosisDpjpModel->hitung_tersaring($nomr, $nopen),
            'data' => $data
        ];

        echo json_encode($output);
    }


    public function ubah()
    {
        $post = $this->input->post();
        $data = [
            'UPDATE_OLEH' => $this->session->userdata('id'),
            'STATUS' => $post['cek'],
        ];

        $this->DiagnosisDpjpModel->ubah($post['id'], $data);
    }
    
    public function ubahNew()
    {
        $post = $this->input->post();
        $id = $post['id'];
        $cek = $post['cek']; // Menentukan status berdasarkan cek
        $nomr = $post['nomr'];
        $nopen = $post['nopen'];
        $nokun = $post['nokun'];
        $diagnosis = $post['diagnosis'];

        // Mengecek apakah ada data yang sudah ada dengan kondisi yang sama (nomr, nopen, diagnosis)
        $existingData = $this->DiagnosisDpjpModel->getDiagnosisByNopenNomr($nomr, $nopen, $diagnosis);

        if ($existingData) {
            // Jika ada data yang sama, update status saja
            $data = [
                'UPDATE_OLEH' => $this->session->userdata('id'),
                'STATUS' => $cek,
            ];
            $this->DiagnosisDpjpModel->ubah($id, $data);
        } else {
            // Jika tidak ada data yang sama, insert data baru dengan status default 1
            $data = [
                'NOMR' => $nomr,
                'NOPEN' => $nopen,
                'KUNJUNGAN' => $nokun,
                'DIAGNOSA' => $diagnosis,
                'STATUS' => 1, // Default status 1
                'OLEH' => $this->session->userdata('id'),
            ];
            $this->DiagnosisDpjpModel->tambah($data);
        }

        echo json_encode(['status' => 'success']);
    }

}

// End of File DiagnosisDpjp.php
// Location: ./application/controllers/DiagnosisDpjp.php