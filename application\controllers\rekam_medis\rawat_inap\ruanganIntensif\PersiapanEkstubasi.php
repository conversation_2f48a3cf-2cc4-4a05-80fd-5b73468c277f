<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PersiapanEkstubasi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/ruanganIntensif/PersiapanEkstubasiModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_ekstubasi = $this->uri->segment(3);
      $getEkstubasi = $this->PersiapanEkstubasiModel->getEkstubasi($id_ekstubasi);
      
      $data = array(
        'id_ekstubasi' => $id_ekstubasi,
        'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
        'listAirway' => $this->masterModel->referensi(1388),
        'listKriteria' => $this->masterModel->referensi(1389),
        'getEkstubasi' => $getEkstubasi,
      );
      $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/persiapanEkstubasi', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
         //echo "<pre>";print_r($post['id_cpis']);echo "</pre>";
          
          $dataEkstubasi = array(
            'id' => isset($post['id_ekstubasi']) ? $post['id_ekstubasi']: "",
            'nokun' => $post['nokun'],
            'airway' => isset($post['airway']) ? $post['airway'] : "",
            'diagnosis' => isset($post['diagnosis']) ? $post['diagnosis'] : "",
            'tgl_tindakan' => isset($post['tgl_tindakan']) ? date('Y-m-d', strtotime($post['tgl_tindakan'])) : "",
            'jam' => isset($post['jam']) ? $post['jam'] : "",
            'hari' => isset($post['hari']) ? $post['hari'] : "",
            'kriteria' => isset($post['kriteria']) ? json_encode($post['kriteria']) : "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_ekstubasi'])) {
            $this->db->replace('keperawatan.tb_persiapan_ekstubasi', $dataEkstubasi);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_persiapan_ekstubasi', $dataEkstubasi);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->PersiapanEkstubasiModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->PersiapanEkstubasiModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editPersiapanEkstubasi" data-toggle="modal" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> created_at;
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PersiapanEkstubasiModel->total_count(),
            "recordsFiltered"   => $this->PersiapanEkstubasiModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}