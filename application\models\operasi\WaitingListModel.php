<?php
defined('BASEPATH') or exit('No direct script access allowed');

class WaitingListModel extends CI_Model
{
    protected $_table = 'medis.tb_waiting_list_operasi wlo';
    protected $_primary_key = 'wlo.id';

    public function __construct()
    {
        parent::__construct();
    }

    public function ambil($nomr, $baru = false)
    {
        $this->db->select('wlo.id, wlo.tanggal, master.getNamaLengkapPegawai(d.NIP) dokter');
        $this->db->from($this->_table);
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->where('wlo.norm', $nomr);
        if ($baru == true) {
            $this->db->where_in('wlo.status', [1, 2]);
        }
        $this->db->order_by('wlo.tanggal', 'desc');
        $this->db->limit(1);
        $query = $this->db->get();
        return $query->result_array();
    }
    public function ambilDaftar($nomr)
    {
        $this->db->select('wlo.id, wlo.tanggal, master.getNamaLengkapPegawai(d.NIP) dokter');
        $this->db->from($this->_table);
        $this->db->join('master.dokter d', 'd.ID = wlo.id_dokter', 'left');
        $this->db->where('wlo.norm', $nomr);
        $this->db->where_in('wlo.status', [1, 2]);
        $this->db->order_by('wlo.tanggal', 'desc');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function simpan($data)
    {
        $this->db->insert('medis.tb_waiting_list_operasi', $data);
        return $this->db->insert_id();
    }

    public function simpanRencana($data)
    {
        $this->db->insert('medis.tb_waiting_list_operasi_rencana', $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_waiting_list_operasi.id', $id);
        $this->db->update('medis.tb_waiting_list_operasi', $data);
    }

    public function ubahRencana($id, $data)
    {
        $this->db->where('medis.tb_waiting_list_operasi_rencana.id_wlo', $id);
        $this->db->update('medis.tb_waiting_list_operasi_rencana', $data);
    }
}

// End of File WaitingListModel.php
// Location: ./application/models/operasi/WaitingListModel.php
