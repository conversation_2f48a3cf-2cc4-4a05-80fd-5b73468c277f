<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KlaimRehabKunjunganModel extends MY_Model
{
    protected $_table = 'medis.tb_klaim_rehab_kunjungan';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'desc';
    protected $_urutan_kolom = [null, 'program', 'tanggal', 'dokter', 'terapis', null];
    protected $_pencarian_kolom = [
        'krk.program', 'krk.tanggal', 'krk.no_sep', 'master.getNamaLengkapPegawai(dok.NIP) dokter',
        'master.getNamaLengkapPegawai(p.NIP) terapis'
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'id_klaim_rehab' => 'id_klaim_rehab',
                'label' => 'Klaim rehab',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s tidak terkirim',
                ]
            ],
            [
                'field' => 'tanggal',
                'label' => 'Tanggal kunjungan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'program',
                'label' => 'Program',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
    }

    public function tabel($id)
    {
        $this->db->select(
            'krk.id, krk.program, krk.tanggal, krk.no_sep, master.getNamaLengkapPegawai(dok.NIP) dokter,
            master.getNamaLengkapPegawai(ap.NIP) terapis, krk.status'
        );
        $this->db->from('medis.tb_klaim_rehab_kunjungan krk');
        $this->db->join('medis.tb_klaim_rehab kr', 'kr.id = krk.id_klaim_rehab', 'left');
        $this->db->join('master.dokter dok', 'dok.ID = krk.dokter', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = krk.terapis', 'left');
        $this->db->where('krk.id_klaim_rehab', $id);

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (!isset($_POST['search']['value']) && empty($_POST['search']['value'])) {
                $_POST['search']['value'] = null;
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil($nomr)
    {
        $this->tabel($nomr);
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring($nomr)
    {
        $this->tabel($nomr);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua($nomr)
    {
        $this->tabel($nomr);
        return $this->db->count_all_results();
    }

    public function ubah($id, $data)
    {
        $this->db->where($this->_primary_key, $id);
        $this->db->update($this->_table, $data);
    }

    public function detail($id)
    {
        $this->db->select('tanggal, program, no_sep, dokter, terapis');
        $this->db->from($this->_table);
        $this->db->where($this->_primary_key, $id);
        $query = $this->db->get();
        return $query->row_array();
    }

    public function getSEP($id)
    {
        $this->db->select('*');
        $this->db->from('pendaftaran.penjamin penj');
        $this->db->where('penj.NOPEN', $id);
        $this->db->where('penj.JENIS', 2);
        $query = $this->db->get();
        return $query->row_array();
    }
}

/* End of file KlaimRehabKunjunganModel.php */
/* Location: ./application/models/rehabilitasiMedik/klaimRehab/KlaimRehabKunjunganModel.php */