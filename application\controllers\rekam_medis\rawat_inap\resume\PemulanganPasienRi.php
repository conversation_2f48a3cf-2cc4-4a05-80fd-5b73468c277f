<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemulanganPasienRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/resume/PemulanganPasienRiModel'));
  }

  public function index()
  {
    $norm = $this->uri->segment(6);
    $nokun = $this->uri->segment(8);
    $getNomr = $this->PemulanganPasienRiModel->getNomrRawatInap($nokun);
    $getPengkajian = $this->PemulanganPasienRiModel->getPengkajian($nokun);
    $data = array(
      'nokun' => $nokun,
      'getPengkajian' => $getPengkajian,
      'pasien' => $getNomr,
      'listRambut' => $this->masterModel->referensi(1167),
      'listJerawat' => $this->masterModel->referensi(1168),
      'listPerubahanSuara' => $this->masterModel->referensi(1169),
      'listPertumbuhanPayudara' => $this->masterModel->referensi(1170),
      'listRiwayatHaid' => $this->masterModel->referensi(1171),
      'listRiwayatSirkumsisi' => $this->masterModel->referensi(1172),
      'listSiklus' => $this->masterModel->referensi(1173),
      'listKeluhan' => $this->masterModel->referensi(1174),
      'listPendidikan' => $this->masterModel->referensi(24),
      'listPendidikanDilanjutkan' => $this->masterModel->referensi(1175),
      'listSahabat' => $this->masterModel->referensi(1176),
      'listKlpremaja' => $this->masterModel->referensi(1177),
      'listPanutan' => $this->masterModel->referensi(1178),
      'listMemberidukungan' => $this->masterModel->referensi(1179),
      'listMelakukanibadah' => $this->masterModel->referensi(1180),
      'listBercerita' => $this->masterModel->referensi(1181),
      'listStress' => $this->masterModel->referensi(1182),
      'listDepresi' => $this->masterModel->referensi(1183),
      'listBunuhdiri' => $this->masterModel->referensi(1184),
      'listNarkotika' => $this->masterModel->referensi(1185),
      'listPerawat' => $this->masterModel->listPerawat(),
      'anamnesis' => $this->masterModel->referensi(54)
    );
    $this->load->view('rekam_medis/rawat_inap/resume/pemulanganPasienRi', $data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();
        $data = array(
          'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
          'diag_masuk' => isset($post['diag_masuk']) ? $post['diag_masuk'] : "",
          'tgl_keluar' => isset($post['tgl_keluar']) ? date('Y-m-d',strtotime($post['tgl_keluar'])) : "",
          'jenis_aktivitas' => isset($post['jenis_aktivitas']) ? $post['jenis_aktivitas'] : "",
          'alat_bantu' => isset($post['alat_bantu']) ? $post['alat_bantu'] : "",
          'aktivitas_lainnya' => isset($post['aktivitas_lainnya']) ? $post['aktivitas_lainnya'] : "",
          'uraian_aktivitas_lainnya' => isset($post['uraian_aktivitas_lainnya']) ? $post['uraian_aktivitas_lainnya'] : "",
          'efek_samping' => isset($post['efek_samping']) ? $post['efek_samping'] : "",
          'obat_alternatif' => isset($post['obat_alternatif']) ? $post['obat_alternatif'] : "",
          'pencegahan_kekambuhan' => isset($post['pencegahan_kekambuhan']) ? $post['pencegahan_kekambuhan'] : "",
          'edukasi_lainnya' => isset($post['edukasi_lainnya']) ? $post['edukasi_lainnya'] : "",
          'uraian_edukasi_lainnya' => isset($post['uraian_edukasi_lainnya']) ? $post['uraian_edukasi_lainnya'] : "",
          'kenali_tanda' => isset($post['kenali_tanda']) ? $post['kenali_tanda'] : "",
          'pengobatan_dirumah' => isset($post['pengobatan_dirumah']) ? $post['pengobatan_dirumah'] : "",
          'dilanjutkan_dirumah' => isset($post['dilanjutkan_dirumah']) ? $post['dilanjutkan_dirumah'] : "",
          'perawatan_lainnya' => isset($post['perawatan_lainnya']) ? $post['perawatan_lainnya'] : "",
          'uraian_perawatan_lainnya' => isset($post['uraian_perawatan_lainnya']) ? $post['uraian_perawatan_lainnya'] : "",
          'pola_makan' => isset($post['pola_makan']) ? $post['pola_makan'] : "",
          'pola_diet' => isset($post['pola_diet']) ? $post['pola_diet'] : "",
          'diet_lainnya' => isset($post['diet_lainnya']) ? $post['diet_lainnya'] : "",
          'uraian_diet_lainnya' => isset($post['uraian_diet_lainnya']) ? $post['uraian_diet_lainnya'] : "",
          'kunsul_spiritual' => isset($post['kunsul_spiritual']) ? $post['kunsul_spiritual'] : "",
          'konsul_psikologis' => isset($post['konsul_psikologis']) ? $post['konsul_psikologis'] : "",
          'kegiatan_keagamaan' => isset($post['kegiatan_keagamaan']) ? $post['kegiatan_keagamaan'] : "",
          'spiritual_lainnya' => isset($post['spiritual_lainnya']) ? $post['spiritual_lainnya'] : "",
          'uraian_spiritual_lainnya' => isset($post['uraian_spiritual_lainnya']) ? $post['uraian_spiritual_lainnya'] : "",
          'tanggal_pemulangan' => isset($post['tanggal_pemulangan']) ? date('Y-m-d', strtotime($post['tanggal_pemulangan'])) : "",
          'pendamping' => isset($post['pendamping']) ? $post['pendamping'] : "",
          'tempat_pemulangan' => isset($post['tempat_pemulangan']) ? $post['tempat_pemulangan'] : "",
          'transportasi' => isset($post['transportasi']) ? $post['transportasi'] : "",
          'pemulangan_lainnya' => isset($post['pemulangan_lainnya']) ? $post['pemulangan_lainnya'] : "",
          'uraian_pemulangan_lainnya' => isset($post['uraian_pemulangan_lainnya']) ? $post['uraian_pemulangan_lainnya'] : "",
          'tanggal_kontrol' => isset($post['tanggal_kontrol']) ? date('Y-m-d', strtotime($post['tanggal_kontrol'])) : "",
          'kontrol_dokter_ket' => isset($post['kontrol_dokter_ket']) ? $post['kontrol_dokter_ket'] : "",
          'tanggal_pemeriksaan_lab' => isset($post['tanggal_pemeriksaan_lab']) ? date('Y-m-d', strtotime($post['tanggal_pemeriksaan_lab'])) : "",
          'pemeriksaan_lab_ket' => isset($post['pemeriksaan_lab_ket']) ? $post['pemeriksaan_lab_ket'] : "",
          'tanggal_rad' => isset($post['tanggal_rad']) ? date('Y-m-d', strtotime($post['tanggal_rad'])) : "",
          'pemeriksaan_rad_ket' => isset($post['pemeriksaan_rad_ket']) ? $post['pemeriksaan_rad_ket'] : "",
          'perjanjian_lainnya' => isset($post['perjanjian_lainnya']) ? $post['perjanjian_lainnya'] : "",
          'tanggal_perjanjian_lainnya' => isset($post['tanggal_perjanjian_lainnya']) ? date('Y-m-d', strtotime($post['tanggal_perjanjian_lainnya'])) : "",
          'pemeriksaan_perjanjian_lainnya_ket' => isset($post['pemeriksaan_perjanjian_lainnya_ket']) ? $post['pemeriksaan_perjanjian_lainnya_ket'] : "",
          'ttd_perawat' => file_get_contents($this->input->post('ttd_perawat')),
          'ttd_pasien' => file_get_contents($this->input->post('ttd_pasien')),
          'perawat' => isset($post['perawat']) ? $post['perawat'] : "",
          'keluarga_pasien' => isset($post['keluarga_pasien']) ? $post['keluarga_pasien'] : "",
          'oleh' => $this->session->userdata('id')
        );

        $this->db->trans_begin();
        
        if (!empty($post['nokun'])) {
          $this->db->replace('keperawatan.tb_discharge_planning', $data);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success_ubah');
          }
  
          echo json_encode($result);
        }else{
            $this->db->insert('keperawatan.tb_discharge_planning', $data);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
        }
        // echo "<pre>data kesadaran ";print_r($dataKesedaran);echo "</pre>";
      }else if($param == 'count'){
        $result = $this->PemulanganPasienRiModel->get_count();
        echo json_encode($result);
      }
    }
  }

  public function datatables(){
      
    $result = $this->PemulanganPasienRiModel->datatables();

    $data = array();
    $urut = 1;
    foreach ($result as $row){
        $sub_array = array();
        $sub_array[] = $urut;
        $sub_array[] = $row -> nokun;
        $sub_array[] = date('d-m-Y H:i:s', strtotime($row -> created_at));
        $sub_array[] = $row -> oleh_nama;    
        $sub_array[] = '<button type="button" class="btn btn-primary btn-block btn-sm editPemulanganPasienRi" data-id="'.$row -> nokun.'"><i class="fa fa-eye"></i> lihat</button>';
    
        $data[] = $sub_array;
        $urut ++;
    }

    $output = array(
        "draw"              => intval($_POST["draw"]),  
        "data"              => $data
    );
    echo json_encode($output);
  }

}