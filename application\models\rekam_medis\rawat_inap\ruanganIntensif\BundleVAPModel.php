<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BundleVAPModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('b.id, w.variabel waktu, b.created_at tanggal, master.getNamaLengkapPegawai(peng.NIP) oleh
        , (kt.nilai+poskep.nilai+mm.nilai+oh.nilai+m.nilai+s.nilai+sg.nilai+pu.nilai+prof.nilai+rs.nilai+cc.nilai) total_score
        , ((kt.nilai+poskep.nilai+mm.nilai+oh.nilai+m.nilai+s.nilai+sg.nilai+pu.nilai+prof.nilai+rs.nilai+cc.nilai)/11)*100 total_presentase');
        $this->db->from('keperawatan.tb_bundle_vap b');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = b.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','pk.NOPEN = p.NOMOR');
        $this->db->join('db_master.variabel kt','b.kebersihan_tangan = kt.id_variabel');
        $this->db->join('db_master.variabel poskep','b.posisi_kepala = poskep.id_variabel');
        $this->db->join('db_master.variabel mm','b.mobilisasi_miring = mm.id_variabel');
        $this->db->join('db_master.variabel oh','b.oral_hygiene = oh.id_variabel');
        $this->db->join('db_master.variabel m','b.makanan = m.id_variabel');
        $this->db->join('db_master.variabel s','b.suction = s.id_variabel');
        $this->db->join('db_master.variabel sg','b.sikat_gigi = sg.id_variabel');
        $this->db->join('db_master.variabel pu','b.peptic_ulcer = pu.id_variabel');
        $this->db->join('db_master.variabel prof','b.profilaksis = prof.id_variabel');
        $this->db->join('db_master.variabel rs','b.review_sedasi = rs.id_variabel');
        $this->db->join('db_master.variabel cc','b.control_cuff = cc.id_variabel');
        $this->db->join('db_master.variabel w','b.waktu = w.id_variabel');
        $this->db->join('aplikasi.pengguna peng','b.oleh = peng.ID');

        $this->db->where('b.status !=','0');
        $this->db->where('p.NORM',$this->input->post('nomr'));
        $this->db->order_by('b.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id)
    {
      $query = $this->db->query(
        'SELECT * FROM keperawatan.tb_bundle_vap b WHERE b.id = "'.$id.'" '
      );
      return $query->row_array();
    }

    public function getTBBB($nokun)
    {
        $query = $this->db->query(
            'SELECT * FROM db_pasien.tb_tb_bb tbbb
            WHERE tbbb.nokun = "'.$nokun.'"
            ORDER BY tbbb.created_at DESC limit 1'
        );
        return $query->row_array();
    }

}
