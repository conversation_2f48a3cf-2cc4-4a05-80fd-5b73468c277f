<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengaturanAkunModel extends CI_Model 
{
    private $db4;
    private $db5;

    public function __construct()
    {
        parent::__construct();
        $this->db4 = $this->load->database('196', true);
        $this->db5 = $this->load->database('238', true);
    }

    protected $_table_name    = 'aplikasi.pengguna';
    protected $_primary_key   = 'ID';
    protected $_order_by      = 'ID';
    protected $_order_by_type = 'DESC';

  public function namaPegawai($id)
  {
    $query  = $this->db->query("SELECT master.getNamaLengkapPegawai(mp.NIP) NAMAPEGAWAI, ap.LOGIN USER_MASUK, ap.ID, sp.id_pegawai_simpeg
     FROM aplikasi.pengguna ap
     LEFT JOIN master.pegawai mp ON ap.NIP = mp.NIP
     LEFT JOIN akses_simrskd.link_simpeg sp ON ap.ID = sp.id_pengguna
     WHERE ap.ID = '$id'");

    return $query->row_array();
  }

  public function cekSudahKaitBelum($oleh){
    $query = $this->db5->query(
      "SELECT COUNT(akun.id_pengguna) JUMLAH
      , akun.id_pengguna ID_PENGGUNA
      , akun.status STATUS
      , akun.no_absen NO_ABSEN
      FROM dbsdm.link_simpeg akun
      WHERE akun.id_pengguna = '$oleh'"
    );
    return $query->row_array();
  }

  public function cekData($username, $password){
    $query = $this->db5->query(
      "SELECT COUNT(akun.USERNAME) JUMLAH, akun.PEGAWAI ID_PEGAWAI, akun.password PASSWORD
    FROM dbsdm.account akun
    WHERE akun.USERNAME = '$username' #AND akun.PASSWORD = '$password'
    AND akun.STATUS='1'"
    );
    return $query->row_array();
 }

 public function cekMapping($oleh){
  $query = $this->db->query(
    "SELECT COUNT(map.id_pengguna_simrskd) JUMLAH
    FROM akses_simrskd.tb_mapping map
    WHERE map.id_pengguna_simrskd='$oleh'"
  );
  return $query->row_array();
}

public function listKinerjaPengkajian($oleh)
{
  $query  = $this->db5->query("SELECT *
    FROM dbsdm.kuantitas k
    WHERE k.PEGAWAI = '$oleh' AND k.STATUS = 1");

  return $query->result_array();
}

public function jenisIsiKinerja($oleh)
{
  $query = $this->db->query("SELECT jns.id ID_JENIS, jns.isi ISI, mp.id ID_MAPPING
    , mp.id_pengguna_simrskd OLEH
    , mp.kinerja KINERJA
    FROM akses_simrskd.tb_jenis_isi_kinerja jns
    LEFT JOIN akses_simrskd.tb_mapping mp ON mp.jenis = jns.id AND mp.id_pengguna_simrskd='$oleh' AND mp.`status`=1
    GROUP BY jns.id
    ORDER BY jns.id ASC
    ");

  return $query->result_array();
}

 public function simpanLinkSimpeg($data)
 {
  $this->db5->insert('dbsdm.link_simpeg', $data);
  $this->db->insert('akses_simrskd.link_simpeg', $data);
  // return $this->db->insert_id();
}

 public function simpanUnlinkSimpeg($oleh, $data)
 {
  $this->db5->where('id_pengguna', $oleh);
  $this->db5->update('dbsdm.link_simpeg', $data);

  $this->db->where('id_pengguna', $oleh);
  $this->db->update('akses_simrskd.link_simpeg', $data);
}

}

/* End of file ProfileModel.php */
/* Location: ./application/models/ProfileModel.php */
