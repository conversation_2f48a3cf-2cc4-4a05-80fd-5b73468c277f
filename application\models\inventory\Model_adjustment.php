<?php
class Model_adjustment extends ci_model
{ 

  public function __construct()
  {
    parent::__construct();
  }

  function get_no_adjust(){
    $q = $this->db->query("SELECT MAX(RIGHT(NOMOR,4)) AS kd_max FROM invenumum.permintaan_perubahan_stok WHERE DATE(TANGGAL)=CURDATE()");
    $kd = "";
    if($q->num_rows()>0){
      foreach($q->result() as $k){
        $tmp = ((int)$k->kd_max)+1;
        $kd = sprintf("%04s", $tmp);
      }
    }else{
      $kd = "0001";
    }
    date_default_timezone_set('Asia/Jakarta');
    return date('ymd').$kd;
  }

  public function dataAdjustment()
  {
    $query = $this->db->query("SELECT pp.NOMOR, pp.ASAL, pp.TANGGAL,ppd.BARANG, ppd.STOK_AWAL,ppd.STOK_AKHIR, ppd.JUMLAH_RETUR, ppd.KETERANGAN
      , bm.BARANG
      FROM invenumum.permintaan_perubahan_stok pp
      left join invenumum.permintaan_perubahan_stok_detil ppd ON pp.NOMOR=ppd.NOMOR_PERMINTAAN
      left join invenumum.barang_gudang bg ON bg.ID_BARANG_GUDANG=ppd.BARANG
      left join invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG");
    return $query;
  }

  function get_subkategori_nama($id){
    $hasil=$this->db->query("
      SELECT bg.ID_BARANG_GUDANG, bm.BARANG, bg.STOK, bm.HARGA
      FROM invenumum.barang_gudang bg
      LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
      WHERE bg.ID_BARANG_GUDANG='$id' AND bm.`STATUS`=1");
    return $hasil->result();
  }

  function get_subkategori($id){
    $hasil=$this->db->query("SELECT bg.ID_BARANG_GUDANG, bm.BARANG,IF(bg.STOK=0,CONCAT(' - ',bg.STOK),'') STOK
     FROM invenumum.barang_gudang bg
     LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
     WHERE bg.GUDANG='$id' AND bm.`STATUS`=1");
    return $hasil->result();
  }

}