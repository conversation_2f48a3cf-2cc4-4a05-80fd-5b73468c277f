<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>ahKesehatan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
        redirect('login');
    }

    $this->load->model(array('rekam_medis/MasalahKesehatanModel'));
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if($param == 'ambil'){
          $post = $this->input->post(NULL,TRUE);
          $dataMasalahKesehatan = $this->MasalahKesehatanModel->get($post['id'], true);

          echo json_encode(array(
          'status' => 'success',
          'data'   => $dataMasalahKesehatan
          ));
      }
    }
  }

}
