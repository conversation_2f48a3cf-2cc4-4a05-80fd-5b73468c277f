<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TreatmentDose extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','radioterapi/TreatmentDoseModel','rekam_medis/rawat_inap/keperawatan/Idomodel'));
  }

  public function index(){
    $norm = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    // var_dump($norm);exit;
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'tanggalTD' => $this->TreatmentDoseModel->tanggalTD($norm),
      'simulationTm' => $this->masterModel->referensi(805),
      'TPSTm' => $this->masterModel->referensi(806),
      'CtSimulatorTm' => $this->masterModel->referensi(807),
      'initialLat' => $this->masterModel->referensi(701),
      'initialVrt' => $this->masterModel->referensi(702),
      'initialLong' => $this->masterModel->referensi(703),
      // 'Knee_Rest' => $this->masterModel->referensi(689),
      // 'Matras' => $this->masterModel->referensi(692),
      // 'Arm_Pos' => $this->masterModel->referensi(687),
      // 'Set_Up' => $this->masterModel->referensi(716),
      // 'linacMachine' => $this->masterModel->referensi(696),
      // 'hPengkajianRater' => $hPengkajianRater,
      'getNomr' => $getNomr,
      'jenisMenu' => 'RI',
    );
    //  print_r($data['tanggalTD']);exit();
    $this->load->view('Pengkajian/radioTerapi/treatmentDose', $data);
  }

  public function tblTreatmentDose()
  {

    $nomr = $this->input->post('nomr');

    $tblTd = $this->TreatmentDoseModel->tblTreatmentDose($nomr);

    echo json_encode($tblTd);
  }

  public function treatmentDoseDr(){
    $nokun = trim($this->input->get('nokun'));
    $nomr = trim($this->input->get('nomr'));
    $id = trim($this->input->get('id'));
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $tddr = $this->TreatmentDoseModel->treatmentDoseDr($id);
    // var_dump($tddr);exit;
    $data = array(
      'nokun' => $nokun,
      'data' => $tddr,
      'tanggalTD' => $this->TreatmentDoseModel->tanggalTD($pasien['NORM']),
      'simulationTm' => $this->masterModel->referensi(805),
      'TPSTm' => $this->masterModel->referensi(806),
      'CtSimulatorTm' => $this->masterModel->referensi(807),
      'initialLat' => $this->masterModel->referensi(701),
      'initialVrt' => $this->masterModel->referensi(702),
      'initialLong' => $this->masterModel->referensi(703),
      // 'Knee_Rest' => $this->masterModel->referensi(689),
      // 'Matras' => $this->masterModel->referensi(692),
      // 'Arm_Pos' => $this->masterModel->referensi(687),
      // 'Set_Up' => $this->masterModel->referensi(716),
      // 'linacMachine' => $this->masterModel->referensi(696),
      // 'hPengkajianRater' => $hPengkajianRater,
      'getNomr' => $getNomr,
      'jenisMenu' => 'RI',
    );
     // print_r($data);exit();
    $this->load->view('Pengkajian/radioTerapi/treatmentdose/treatmentdosedr_show', $data);
  }

  public function treatmentDoseDrhsitory(){
    $id = trim($this->input->post('id'));
    $tddrh = $this->TreatmentDoseModel->treatmentDoseDrhistory($id);
    // var_dump($tddrh);exit;
    $data = array(
      'data' => $tddrh,
    );
     // print_r($data);exit();
    $this->load->view('Pengkajian/radioTerapi/treatmentdose/treatmentdosedr_history', $data);
  }

  public function treatmentDoseFmhsitory(){
    $id = trim($this->input->post('id'));
    $tddrh = $this->TreatmentDoseModel->treatmentDoseFmhsitory($id);
    // var_dump($tddrh);exit;
    $data = array(
      'data' => $tddrh,
    );
     // print_r($data);exit();
    $this->load->view('Pengkajian/radioTerapi/treatmentdose/treatmentdosefm_history', $data);
  }

  public function treatmentDoseFmhsitorydetil(){
    $id = trim($this->input->post('id'));
    $revisi = trim($this->input->post('revisi'));
    $tddrh = $this->TreatmentDoseModel->treatmentDoseFm($id,$revisi);
    // var_dump($tddrh);exit;
    $data = array(
      'data' => $tddrh,
    );
     // print_r($data);exit();
    $this->load->view('Pengkajian/radioTerapi/treatmentdose/treatmentdosefm_historydetil', $data);
  }

  public function treatmentDoseFm()
  {

    $idtd = $this->input->post('id');
    $nokun = $this->input->post('nokun');
    $nomr = $this->input->post('nomr');

    $tblTd = $this->TreatmentDoseModel->treatmentDoseFm($idtd);
    $data = array(
      'idtd' => $idtd,
      'nokun' => $nokun,
      'nomr' => $nomr,
      'data' => $tblTd
    );

    $this->load->view('Pengkajian/radioTerapi/treatmentdose/treatmentdosefisikamedis', $data);
  }

  public function treatmentDoseradiog()
  {

    $idtd = $this->input->post('id');
    $nokun = $this->input->post('nokun');
    $nomr = $this->input->post('nomr');

    $remarks = $this->TreatmentDoseModel->treatmentDoseradiogRemarks($idtd);
    $tblTd = $this->TreatmentDoseModel->treatmentDoseradiog($idtd);
    $tblTddetil = $this->TreatmentDoseModel->treatmentDoseradiogDetil($idtd);
    $mdokter = $this->Idomodel->mdokter();
    // var_dump($tblTddetil);exit;
    $raddetil=array();
    foreach ($tblTddetil as $key => $value) {
      $raddetil[$value['idTDRd']][]=$value;
    }
    // var_dump($raddetil);exit;
    $data = array(
      'idtd' => $idtd,
      'nokun' => $nokun,
      'nomr' => $nomr,
      'remarks' => $remarks,
      'data' => $tblTd,
      'datadetil' => $raddetil,
      'mdokter' => $mdokter
    );

    $this->load->view('Pengkajian/radioTerapi/treatmentdose/treatmentdoseradiog', $data);
  }

  public function simpanTreatmentDose()
  {
    $post = $this->input->post();

    $data= array(
      'nokun'    => $post["nokun"],
      'dateTm'   => $post["dateTm"],
      'energy'   => $post["energy"],
      'fsdFid'   => $post["fsdFid"],
      'rttSign'  => $post["rttSign"],
      'fild1Mu1' => $post["fild1Mu1"],
      'fild1Mu2' => $post["fild1Mu2"],
      'fild2Mu1' => $post["fild2Mu1"],
      'fild2Mu2' => $post["fild2Mu2"],
      'fild3Mu1' => $post["fild3Mu1"],
      'fild3Mu2' => $post["fild3Mu2"],
      'fild4Mu1' => $post["fild4Mu1"],
      'fild4Mu2' => $post["fild4Mu2"],
      'fild5Mu1' => $post["fild5Mu1"],
      'fild5Mu2' => $post["fild5Mu2"],
      'fild6Mu1' => $post["fild6Mu1"],
      'fild6Mu2' => $post["fild6Mu2"],
      'cumDose1' => $post["cumDose1"],
      'cumDose2' => $post["cumDose2"],
      'remarks1' => $post["remarks1"],
      'remarks2' => $post["remarks2"],
      'remarks3' => $post["remarks3"],
      'oleh'     => $this->session->userdata('id'),
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->TreatmentDoseModel->simpanTreatmentDose($data);
  }

  public function tblApproving()
  {

    $nomr = $this->input->post('nomr');
    $nokun = $this->input->post('nokun');

    $tblTd = $this->TreatmentDoseModel->tblApproving($nomr,$nokun);

    echo json_encode($tblTd);
  }

  public function simpanApproving()
  {
    $post = $this->input->post();

    $data= array(
      'nokun'           => $post["nokun"],
      'field'           => $post["fildApproving"],
      'energy'          => $post["energyApproving"],
      'fsdFid'          => $post["fsdFid"],
      'XxY'             => $post["xXyAngle"],
      'gantryAngle'     => $post["gantryAngle"],
      'collAngle'       => $post["collAngle"],
      'coughAngle'      => $post["coughAngle"],
      'pddTmr'          => $post["pddTmr"],
      'dosePerFraction' => $post["desePerFraction"],
      'treaetmentMu1'    => $post["treatmentApprove1"],
      'treaetmentMu2'    => $post["treatmentApprove2"],
      'treaetmentMu3'    => $post["treatmentApprove3"],
      'treaetmentMu4'    => $post["treatmentApprove4"],
      'treaetmentMu5'    => $post["treatmentApprove5"],
      'treaetmentMu6'    => $post["treatmentApprove6"],
      'treaetmentMu7'    => $post["treatmentApprove7"],
      'oleh'            => $this->session->userdata('id'),
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->TreatmentDoseModel->simpanApproving($data);
  }

  public function simpanTreatmentDoseDr()
  {
    $post = $this->input->post();
    $data= array(
      'nokun'              => $post["nokun"],
      'nomr'               => $post["nomr"],
      'typeOfTreatment'    => $post["typeOfTreatmentTm"],
      'fraction'           => $post["fractionTm"],
      'totalDose'          => $post["totalDoseTm"],
      'simulation'         => isset($post["simulationTm"]) ? $post["simulationTm"] : 0,
      'tps'                => isset($post["TPSTm"]) ? $post["TPSTm"] : 0,
      'ctSimulator'        => isset($post["CtSimulatorTm"]) ? $post["CtSimulatorTm"] : 0,
      'revisi'             => isset($post["revisi"]) ? $post["revisi"] : 0,
      'idubah'             => isset($post["idtdubah"]) ? $post["idtdubah"] : null,
      'oleh'               => $this->session->userdata('id'),
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->TreatmentDoseModel->simpanTreatmentDoseDr($data);
  }

  public function simpanTreatmentDoseFm()
  {
    $post = $this->input->post();
    $field=$post["field"];
    
    $dataxist=array();
    $data=array();
    foreach ($field as $key => $vfield) {
      if($vfield!=""){
        $idself=$post["idself"][$key];
        $approvedby=$post["approvedby"][$key];
          $item= array(
            'idTD'            => $post["idtd"],
            'nokun'           => $post["nokun"],
            'field'           => $vfield,
            'energy'          => $post["energy"][$key],
            'fsdFid'          => $post["fsdFid"][$key],
            'XxY'             => $post["XxY"][$key],
            'gantryAngle'     => $post["gantryAngle"][$key],
            'collAngle'       => $post["collAngle"][$key],
            'coughAngle'      => $post["coughAngle"][$key],
            'pddTmr'          => $post["pddTmr"][$key],
            'dosePerFraction' => $post["dosePerFraction"][$key],
            'treatmentMu'     => $post["treatmentmu"][$key],
            'idubah'          => (isset($post["idtdubah"][$key]) && $post["idtdubah"][$key]>0)?($post["idtdubah"][$key]):null,
            'revisi'          => isset($post["revisi"])?$post["revisi"]:null,
            'oleh'            => $this->session->userdata('id')
          );
        if($idself>0 && $approvedby < 1){
          $item['id']=$post["idself"][$key];
          $dataxist[]=$item;
        }else{
          $data[]=$item;
        }
        // var_dump($data);exit;
        // echo "<pre>"; print_r($data); echo "</pre>"; exit();
      }
    }
    // var_dump($data);exit;
        $this->TreatmentDoseModel->simpanTreatmentDoseFm($dataxist,$data,$post["idtd"]);
  }

  public function approvetdfm()
  {
    $post   = $this->input->post();
    
    $data= array(
      'idTD'            => $post["idtd"],
      'revisi'          => isset($post["revisicurrent"])?$post["revisicurrent"]:null,
      'oleh'            => $this->session->userdata('id')
    );
        
    // var_dump($data);exit;
        $this->TreatmentDoseModel->approvetdfm($data);
  }

  
  public function simpanTreatmentDoseRemarks()
  {
    $post = $this->input->post();
    $data= array(
      'nokun'              => $post["nokun"],
      // 'nomr'               => $post["nomr"],
      'remarks'            => $post["statustd"],
      'remarks_ket'        => $post["keterangan"],
      'idtd'               => $post["idtd"],
      'remarksBy'          => $this->session->userdata('id'),
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->TreatmentDoseModel->simpanTreatmentDoseRemarks($data);
  }


  public function simpanTreatmentDoseRad()
  {
    $post = $this->input->post();
    $mu1  =$post["mu1"];
    $mu2  =$post["mu2"];

    $data= array(
      'id'              => $post["idtdrd"],
      'nokun'              => $post["nokun"],
      'idTD'               => $post["idtd"],
      'dateAt'             => $post["dateat"],
      'energy'              => $post["energy"],
      'fsdFid'              => $post["fsdfid"],
      'rttsign'             => isset($post['rttsign'])?implode(',',$post['rttsign']):'',
      'cumdose_mu1'        => $post["cumdose_mu1"],
      'cumdose_mu2'        => $post["cumdose_mu2"],
      'oleh'               => $this->session->userdata('id')
    );

    $datamu=array();
    foreach ($mu1 as $key => $value) {
      $item= array(
        'idTD'            => $post["idtd"],
        'idTDRd'          => $post["idtdrd"],
        'nofield'         => $key+1,
        'nokun'           => $post["nokun"],
        'mu1'             => $mu1[$key],
        'mu2'             => $mu2[$key],
        'oleh'            => $this->session->userdata('id')
      );
      $datamu[]=$item;
    }

    // echo "<pre>"; print_r($post); echo "</pre>"; exit();
    $this->TreatmentDoseModel->simpanTreatmentDoseRad($data,$datamu,$post["idtd"]);
    // $this->TreatmentDoseModel->simpanTreatmentDoseRemarks($data);
  }


  //old version--------------//

  public function historyTreatmentDose()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $tblTreatmentDose = $this->TreatmentDoseModel->tblHistoryTreatmentDose($nomr);

    $data = array();
    $no = 1;
    foreach ($tblTreatmentDose->result() as $ttd) {
      $btnDr = $ttd->STATUS_EDIT == 1 ? '<a href="#modalTreatmentDoesDr" class="btn btn-sm btn-block btn-purple" data-id="'.$ttd->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>': '<button class="btn btn-sm btn-block btn-purple disabled"><i class="fa fa-eye"></i> Edit</button>';
      $btnRad = $ttd->STATUS_EDIT == 1 ? $ttd->idRad != null ? '<a href="#modalTreatmentDoesRad" class="btn btn-sm btn-block btn-success" data-id="'.$ttd->nokun.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>' : '<button class="btn btn-sm btn-block btn-success disabled"><i class="fa fa-eye"></i> Edit</button>' : '<button class="btn btn-sm btn-block btn-success disabled"><i class="fa fa-eye"></i> Edit</button>';
      $btnVer = $ttd->STATUS_EDIT == 1 ? $ttd->idVer != null ? '<a href="#modalTreatmentDoesVer" class="btn btn-sm btn-block btn-success" data-id="'.$ttd->nokun.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>' : '<button class="btn btn-sm btn-block btn-success disabled"><i class="fa fa-eye"></i> Edit</button>' : '<button class="btn btn-sm btn-block btn-success disabled"><i class="fa fa-eye"></i> Edit</button>';
      $btnFis = $ttd->STATUS_EDIT == 1 ? $ttd->idFis != null ? '<a href="#modalTreatmentDoesFis" class="btn btn-sm btn-block btn-info" data-id="'.$ttd->nokun.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>' : '<button class="btn btn-sm btn-block btn-info" disabled><i class="fa fa-eye"></i> Edit</button>' : '<button class="btn btn-sm btn-block btn-info" disabled><i class="fa fa-eye"></i> Edit</button>';
      $data[] = array(
        $no,
        $ttd->nokun,
        date("d-m-Y H:i:s",strtotime($ttd->tanggal)),
        $btnDr,
        $btnRad,
        $btnVer,
        $btnFis,
        '<a href="/reports/simrskd/mendus/treatmentdose.php?format=pdf&nokun='.$ttd->nokun.'&nomr='.$ttd->NORM.'" class="btn btn-sm btn-block btn-warning" target="_blank"><i class="fa fa-print"></i> Cetak</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $tblTreatmentDose->num_rows(),
      "recordsFiltered" => $tblTreatmentDose->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalTmDr()
  {
    $id = $this->input->post('id');
    $gTmDr = $this->TreatmentDoseModel->getTmDr($id);

     $data = array(
       'id'     => $id,
       'gTmDr' => $gTmDr,
       'simulationTm'  => $this->masterModel->referensi(805),
       'TPSTm'         => $this->masterModel->referensi(806),
       'CtSimulatorTm' => $this->masterModel->referensi(807),
    );


    $this->load->view('Pengkajian/radioTerapi/editTreatmentDoseDr', $data);

  }

  public function modalTmVer()
  {
    $id = $this->input->post('id');
    $nokun = $this->input->post('nokun');
    $gTmVer = $this->TreatmentDoseModel->getTmVer($nokun);
    $totalTreatment = count($gTmVer);

    $gCtid = array_column($gTmVer, 'id');
    $gCtinitialLat = array_column($gTmVer, 'initialLat');
    $gCtvalueTreatmentLat = array_column($gTmVer, 'valueTreatmentLat');
    $gCtfinalCouchTreatmentLat = array_column($gTmVer, 'finalCouchTreatmentLat');
    $gCtinitialVrt = array_column($gTmVer, 'initialVrt');
    $gCtvalueTreatmentVrt = array_column($gTmVer, 'valueTreatmentVrt');
    $gCtfinalCouchTreatmentVrt = array_column($gTmVer, 'finalCouchTreatmentVrt');
    $gCtinitialLong = array_column($gTmVer, 'initialLong');
    $gCtvalueTreatmentLong = array_column($gTmVer, 'valueTreatmentLong');
    $gCtfinalCouchTreatmentLong = array_column($gTmVer, 'finalCouchTreatmentLong');

    $data = array(
      'id'          => $id,
      'nokun'      => $nokun,
      'gCtid'      => $gCtid,
      'gTmVer'     => $gTmVer,
      'namaPasien' => $gTmVer[0]['NAMAPASIEN'],
      'tanggalIsi' => $gTmVer[0]['tanggal'],
      'norm'       => $gTmVer[0]['NORM'],
      'totalTreatment'                      => $totalTreatment,
      'gCtinitialLat'                       => $gCtinitialLat,
      'gCtvalueTreatmentLat'                => $gCtvalueTreatmentLat,
      'gCtfinalCouchTreatmentLat'           => $gCtfinalCouchTreatmentLat,
      'gCtinitialVrt'                       => $gCtinitialVrt,
      'gCtvalueTreatmentVrt'                => $gCtvalueTreatmentVrt,
      'gCtfinalCouchTreatmentVrt'           => $gCtfinalCouchTreatmentVrt,
      'gCtinitialLong'                      => $gCtinitialLong,
      'gCtvalueTreatmentLong'               => $gCtvalueTreatmentLong,
      'gCtfinalCouchTreatmentLong'          => $gCtfinalCouchTreatmentLong,
      'initialLat'                          => $this->masterModel->referensi(701),
      'initialVrt'                          => $this->masterModel->referensi(702),
      'initialLong'                         => $this->masterModel->referensi(703),
      'initialLat3'                         => $this->masterModel->referensi(701),
      'initialVrt3'                         => $this->masterModel->referensi(702),
      'initialLong3'                        => $this->masterModel->referensi(703),
      'initialLat3a'                        => $this->masterModel->referensi(701),
      'initialVrt3a'                        => $this->masterModel->referensi(702),
      'initialLong3a'                       => $this->masterModel->referensi(703),
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->load->view('Pengkajian/radioTerapi/editTreatmentDoseVer', $data);
  }

  public function modalTmRad()
  {
    $nokun = $this->input->post('nokun');
    $gTmRad = $this->TreatmentDoseModel->getTmRad($nokun);

    $data = array(
      'nokun'      => $nokun,
      'gTmRad'     => $gTmRad,
      'namaPasien' => $gTmRad[0]['NAMAPASIEN'],
      'tanggalIsi' => $gTmRad[0]['tanggal'],
      'norm'       => $gTmRad[0]['NORM'],
    );

    $this->load->view('Pengkajian/radioTerapi/editTreatmentDoseRad', $data);
  }

  public function modalTmRadIsi()
  {
    $id = $this->input->post('id');
    $gTmRadDetail = $this->TreatmentDoseModel->getTmRadDetail($id);
    echo ' <form id="modalTreatmentDoesEdit">
        <input type="hidden" name="id" value="'.$gTmRadDetail['id'].'">
        <div class="modal-body">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>DATE</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="date" class="form-control" name="dateTmEdit" value="'.$gTmRadDetail['dateTm'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>ENERGY</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="energyEdit" placeholder="[ Energy ]" value="'.$gTmRadDetail['energy'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FSD / FID</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="fsdFidEdit" placeholder="[ Fsd/Fid ]" value="'.$gTmRadDetail['fsdFid'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>RTT. SIGN</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="rttSignEdit" placeholder="[ Rtt. Sign ]" value="'.$gTmRadDetail['rttSign'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FILD 1 / SIGN</label>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild1Mu1Edit" placeholder="MU" value="'.$gTmRadDetail['fild1Mu1'].'" autocomplete="off">
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild1Mu2Edit" placeholder="MU" value="'.$gTmRadDetail['fild1Mu2'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FILD 2 / SIGN</label>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild2Mu1Edit" placeholder="MU" value="'.$gTmRadDetail['fild2Mu1'].'" autocomplete="off">
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild2Mu2Edit" placeholder="MU" value="'.$gTmRadDetail['fild2Mu2'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FILD 3 / SIGN</label>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild3Mu1Edit" placeholder="MU" value="'.$gTmRadDetail['fild3Mu1'].'" autocomplete="off">
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild3Mu2Edit" placeholder="MU" value="'.$gTmRadDetail['fild3Mu2'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FILD 4 / SIGN</label>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild4Mu1Edit" placeholder="MU" value="'.$gTmRadDetail['fild4Mu1'].'" autocomplete="off">
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild4Mu2Edit" placeholder="MU" value="'.$gTmRadDetail['fild4Mu2'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FILD 5 / SIGN</label>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild5Mu1Edit" placeholder="MU" value="'.$gTmRadDetail['fild5Mu1'].'" autocomplete="off">
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild5Mu2Edit" placeholder="MU" value="'.$gTmRadDetail['fild5Mu2'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FILD 6 / SIGN</label>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild6Mu1Edit" placeholder="MU" value="'.$gTmRadDetail['fild6Mu1'].'" autocomplete="off">
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="fild6Mu2Edit" placeholder="MU" value="'.$gTmRadDetail['fild6Mu2'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>CUM DOSE</label>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="cumDose1Edit" placeholder="MU" value="'.$gTmRadDetail['cumDose1'].'" autocomplete="off">
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <input type="text" class="form-control" name="cumDose2Edit" placeholder="MU" value="'.$gTmRadDetail['cumDose2'].'" autocomplete="off">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>REMARKS</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <input type="text" class="form-control" name="remarks1Edit" placeholder="[ Remarks 1 ]" value="'.$gTmRadDetail['remarks1'].'" autocomplete="off">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <input type="text" class="form-control" name="remarks2Edit" placeholder="[ Remarks 2 ]" value="'.$gTmRadDetail['remarks2'].'" autocomplete="off">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <input type="text" class="form-control" name="remarks3Edit" placeholder="[ Remarks 3 ]" value="'.$gTmRadDetail['remarks3'].'" autocomplete="off">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-warning" data-dismiss="modal"><i class="fa fa-refresh"></i> Close</button>
          <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> Update</button>
        </div>
      </form>';
  }

  public function modalTmFis()
  {
    $nokun = $this->input->post('nokun');
    $gTmFis = $this->TreatmentDoseModel->getTmFis($nokun);

    $data = array(
      'nokun'      => $nokun,
      'gTmFis'     => $gTmFis,
      'namaPasien' => $gTmFis[0]['NAMAPASIEN'],
      'tanggalIsi' => $gTmFis[0]['tanggal'],
      'norm'       => $gTmFis[0]['NORM'],
    );

    $this->load->view('Pengkajian/radioTerapi/editTreatmentDoseFis', $data);
  }

  public function modalTmFisIsi()
  {
    $id = $this->input->post('id');
    $gTmFisDetail = $this->TreatmentDoseModel->getTmFisDetail($id);
    echo '<form id="formApprovingEdit">
        <input type="hidden" name="id" value="'.$gTmFisDetail['id'].'">
        <div class="modal-body">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FIELD</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="fildApprovingEdit" autocomplete="off" value="'.$gTmFisDetail['field'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>ENERGY</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="energyApprovingEdit" autocomplete="off" value="'.$gTmFisDetail['energy'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>FSD/FID (Cm)</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="fsdFidEdit" autocomplete="off" value="'.$gTmFisDetail['fsdFid'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>X x Y</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="xXyAngleEdit" autocomplete="off" value="'.$gTmFisDetail['XxY'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>GANTRY ANGLE</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="gantryAngleEdit" autocomplete="off" value="'.$gTmFisDetail['gantryAngle'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>COLL ANGLE</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="collAngleEdit" autocomplete="off" value="'.$gTmFisDetail['collAngle'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>COUGH ANGKE</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="coughAngleEdit" autocomplete="off" value="'.$gTmFisDetail['coughAngle'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>PDD / TMR</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="pddTmrEdit" autocomplete="off" value="'.$gTmFisDetail['pddTmr'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>DOSE PER FRACTION</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="desePerFractionEdit" autocomplete="off" value="'.$gTmFisDetail['dosePerFraction'].'">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label>TREATMENT MU</label>
              </div>
            </div>
            <div class="col-md-10">
              <div class="form-group">
                <input type="text" class="form-control" name="treatmentApproveEdit1" autocomplete="off" value="'.$gTmFisDetail['treaetmentMu1'].'">
              </div>
            </div>
          </div>
          <div class="row">
          <div class="col-md-2">
          </div>
          <div class="col-md-10">
            <div class="form-group">
              <input type="text" class="form-control" name="treatmentApproveEdit2" autocomplete="off" value="'.$gTmFisDetail['treaetmentMu2'].'">
            </div>
          </div>
        </div>
        <div class="row">
        <div class="col-md-2">
        </div>
        <div class="col-md-10">
          <div class="form-group">
            <input type="text" class="form-control" name="treatmentApproveEdit3" autocomplete="off" value="'.$gTmFisDetail['treaetmentMu3'].'">
          </div>
        </div>
      </div>
      <div class="row">
      <div class="col-md-2">
      </div>
      <div class="col-md-10">
        <div class="form-group">
          <input type="text" class="form-control" name="treatmentApproveEdit4" autocomplete="off" value="'.$gTmFisDetail['treaetmentMu4'].'">
        </div>
      </div>
    </div>
    <div class="row">
    <div class="col-md-2">
    </div>
    <div class="col-md-10">
      <div class="form-group">
        <input type="text" class="form-control" name="treatmentApproveEdit5" autocomplete="off" value="'.$gTmFisDetail['treaetmentMu5'].'">
      </div>
    </div>
  </div>
  <div class="row">
  <div class="col-md-2">
  </div>
  <div class="col-md-10">
    <div class="form-group">
      <input type="text" class="form-control" name="treatmentApproveEdit6" autocomplete="off" value="'.$gTmFisDetail['treaetmentMu6'].'">
    </div>
  </div>
</div>
<div class="row">
<div class="col-md-2">
</div>
<div class="col-md-10">
  <div class="form-group">
    <input type="text" class="form-control" name="treatmentApproveEdit7" autocomplete="off" value="'.$gTmFisDetail['treaetmentMu7'].'">
  </div>
</div>
</div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-warning" data-dismiss="modal"><i class="fa fa-refresh"></i> Close</button>
          <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> Update</button>
        </div>
      </form>';
  }

  public function updateTmDr()
  {
    $post = $this->input->post();
    $id = $this->input->post('id');

    if(isset($post["approvingEdit"]) == "on"){
      $acc = 1 ;
    }else{
      $acc = 0 ;
    }

    $data= array(
      'typeOfTreatment'    => $post["typeOfTreatmentTmEdit"],
      'fraction'           => $post["fractionTmEdit"],
      'totalDose'          => $post["totalDoseTmEdit"],
      'simulation'         => $post["simulationTmEdit"],
      'tps'                => $post["TPSTmEdit"],
      'ctSimulator'        => $post["CtSimulatorTmEdit"],
      'approvingRadiation' => $acc,
    );
    $this->TreatmentDoseModel->updateTmDr($data,$id);
  }

  public function updateTmRad()
  {
    $post = $this->input->post();
    $id = $this->input->post('id');

    $data= array(
      'dateTm'   => $post["dateTmEdit"],
      'energy'   => $post["energyEdit"],
      'fsdFid'   => $post["fsdFidEdit"],
      'rttSign'  => $post["rttSignEdit"],
      'fild1Mu1' => $post["fild1Mu1Edit"],
      'fild1Mu2' => $post["fild1Mu2Edit"],
      'fild2Mu1' => $post["fild2Mu1Edit"],
      'fild2Mu2' => $post["fild2Mu2Edit"],
      'fild3Mu1' => $post["fild3Mu1Edit"],
      'fild3Mu2' => $post["fild3Mu2Edit"],
      'fild4Mu1' => $post["fild4Mu1Edit"],
      'fild4Mu2' => $post["fild4Mu2Edit"],
      'fild5Mu1' => $post["fild5Mu1Edit"],
      'fild5Mu2' => $post["fild5Mu2Edit"],
      'fild6Mu1' => $post["fild6Mu1Edit"],
      'fild6Mu2' => $post["fild6Mu2Edit"],
      'cumDose1' => $post["cumDose1Edit"],
      'cumDose2' => $post["cumDose2Edit"],
      'remarks1' => $post["remarks1Edit"],
      'remarks2' => $post["remarks2Edit"],
      'remarks3' => $post["remarks3Edit"],
    );
    $this->TreatmentDoseModel->updateTmRad($data,$id);
  }

  public function updateTmFis()
  {
    $post = $this->input->post();
    $id = $this->input->post('id');

    $data= array(
      'field'           => $post["fildApprovingEdit"],
      'energy'          => $post["energyApprovingEdit"],
      'fsdFid'          => $post["fsdFidEdit"],
      'XxY'             => $post["xXyAngleEdit"],
      'gantryAngle'     => $post["gantryAngleEdit"],
      'collAngle'       => $post["collAngleEdit"],
      'coughAngle'      => $post["coughAngleEdit"],
      'pddTmr'          => $post["pddTmrEdit"],
      'dosePerFraction' => $post["desePerFractionEdit"],
      // 'treaetmentMu'    => $post["treatmentApproveEdit"],
      'treaetmentMu1'    => $post["treatmentApproveEdit1"],
      'treaetmentMu2'    => $post["treatmentApproveEdit2"],
      'treaetmentMu3'    => $post["treatmentApproveEdit3"],
      'treaetmentMu4'    => $post["treatmentApproveEdit4"],
      'treaetmentMu5'    => $post["treatmentApproveEdit5"],
      'treaetmentMu6'    => $post["treatmentApproveEdit6"],
      'treaetmentMu7'    => $post["treatmentApproveEdit7"],
    );
    $this->TreatmentDoseModel->updateTmFis($data,$id);
  }

  public function simpanFTreatmentDoseVer()
  {
    $this->db->trans_begin();
    $nokun = $this->input->post('nokun');
    $post = $this->input->post();
    $table1 = $this->input->post('table1');
    
    $dataTreatmentTable = array(
      'nokun'                     => $nokun,
      'initialLat'                => isset($post["initialLat"]) ? $post["initialLat"] : 0,
      'valueTreatmentLat'         => $post['valueTreatmentLat'],
      'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLat'],
      'initialVrt'                => isset($post["initialVrt"]) ? $post["initialVrt"] : 0,
      'valueTreatmentVrt'         => $post['valueTreatmentVrt'],
      'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrt'],
      'initialLong'               => isset($post["initialLong"]) ? $post["initialLong"] : 0,
      'valueTreatmentLong'        => $post['valueTreatmentLong'],
      'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLong'],
      'oleh'                      => $this->session->userdata('id'),
    );
    // echo "<pre>"; print_r($dataTreatmentTable); echo "</pre>"; exit();
    $this->TreatmentDoseModel->simpanTreatment_radiografer($dataTreatmentTable);

    // if (isset($post['initialLat2']) && isset($post['initialVrt2']) && isset($post['initialLong2'])) {
    if (isset($post['table1'])) {
      
      $dataTreatmentTable2 = array(
        // 'table1'                    => $post['table1'],
        'nokun'                     => $nokun,
        'initialLat'                => isset($post["initialLat2"]) ? $post["initialLat2"] : 0,
        'valueTreatmentLat'         => $post['valueTreatmentLat2'],
        'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLat2'],
        'initialVrt'                => isset($post["initialVrt2"]) ? $post["initialVrt2"] : 0,
        'valueTreatmentVrt'         => $post['valueTreatmentVrt2'],
        'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrt2'],
        'initialLong'               => isset($post["initialLong2"]) ? $post["initialLong2"] : 0,
        'valueTreatmentLong'        => $post['valueTreatmentLong2'],
        'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLong2'],
        'oleh'                      => $this->session->userdata('id'),
      );
      // echo "<pre>"; print_r($dataTreatmentTable2); echo "</pre>"; exit();
      $this->TreatmentDoseModel->simpanTreatment_radiografer($dataTreatmentTable2);
    }
    
    // if (isset($post['initialLat2a']) && isset($post['initialVrt2a']) && isset($post['initialLong2a'])) {
    if (isset($post['table1'])) {
      $dataTreatmentTable2a = array(
        // 'table1'                    => $table1,
        'nokun'                     => $nokun,
        'initialLat'                => isset($post["initialLat2a"]) ? $post["initialLat2a"] : 0,
        'valueTreatmentLat'         => $post['valueTreatmentLat2a'],
        'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLat2a'],
        'initialVrt'                => isset($post["initialVrt2a"]) ? $post["initialVrt2a"] : 0,
        'valueTreatmentVrt'         => $post['valueTreatmentVrt2a'],
        'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrt2a'],
        'initialLong'               => isset($post["initialLong2a"]) ? $post["initialLong2a"] : 0,
        'valueTreatmentLong'        => $post['valueTreatmentLong2a'],
        'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLong2a'],
        'oleh'                      => $this->session->userdata('id'),
      );
      // echo "<pre>"; print_r($dataTreatmentTable2a); echo "</pre>"; exit();
      $this->TreatmentDoseModel->simpanTreatment_radiografer($dataTreatmentTable2a);
    }


    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updateTmVer()  {
    $this->db->trans_begin();
    $id           = $this->input->post('id');
    $idTreatment  = $this->input->post('idTreatment');
    $idTreatment2 = $this->input->post('idTreatment2');
    $idTreatment2a = $this->input->post('idTreatment2a');
    $post = $this->input->post();

    $dataTreatmentTable = array(
      'id'                        => $idTreatment,
      'initialLat'                => $post['initialLatEdit'],
      'valueTreatmentLat'         => $post['valueTreatmentLatEdit'],
      'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLatEdit'],
      'initialVrt'                => $post['initialVrtEdit'],
      'valueTreatmentVrt'         => $post['valueTreatmentVrtEdit'],
      'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrtEdit'],
      'initialLong'               => $post['initialLongEdit'],
      'valueTreatmentLong'        => $post['valueTreatmentLongEdit'],
      'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLongEdit'],
      'oleh'                      => $this->session->userdata('id'),
    );
    // echo "<pre>"; print_r($dataTreatmentTable); echo "</pre>"; exit();
    $this->TreatmentDoseModel->updateTreatment_radiografer($dataTreatmentTable,$idTreatment);

    if (isset($post['initialLatEdit2']) && isset($post['initialVrtEdit2']) && isset($post['initialLongEdit2'])) {
      $dataTreatmentTable2 = array(
        'id'                        => $idTreatment2,
        // 'nokun'                     => $nokun,
        'initialLat'                => $post['initialLatEdit2'],
        'valueTreatmentLat'         => $post['valueTreatmentLatEdit2'],
        'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLatEdit2'],
        'initialVrt'                => $post['initialVrtEdit2'],
        'valueTreatmentVrt'         => $post['valueTreatmentVrtEdit2'],
        'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrtEdit2'],
        'initialLong'               => $post['initialLongEdit2'],
        'valueTreatmentLong'        => $post['valueTreatmentLongEdit2'],
        'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLongEdit2'],
        'oleh'                      => $this->session->userdata('id'),
      );
      // echo "<pre>"; print_r($dataTreatmentTable2); echo "</pre>"; exit();
      $this->TreatmentDoseModel->updateTreatment_radiografer($dataTreatmentTable2,$idTreatment2);
    }

    if (isset($post['initialLatEdit2a']) && isset($post['initialVrtEdit2a']) && isset($post['initialLongEdit2a'])) {
      $dataTreatmentTable2a = array(
        'id'                        => $idTreatment2a,
        // 'nokun'                     => $nokun,
        'initialLat'                => $post['initialLatEdit2a'],
        'valueTreatmentLat'         => $post['valueTreatmentLatEdit2a'],
        'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLatEdit2a'],
        'initialVrt'                => $post['initialVrtEdit2a'],
        'valueTreatmentVrt'         => $post['valueTreatmentVrtEdit2a'],
        'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrtEdit2a'],
        'initialLong'               => $post['initialLongEdit2a'],
        'valueTreatmentLong'        => $post['valueTreatmentLongEdit2a'],
        'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLongEdit2a'],
        'oleh'                      => $this->session->userdata('id'),
      );
      // echo "<pre>"; print_r($dataTreatmentTable2a); echo "</pre>"; exit();
      $this->TreatmentDoseModel->updateTreatment_radiografer($dataTreatmentTable2a,$idTreatment2a);
    }

    if (isset($post['initialLatEdit3']) && isset($post['initialVrtEdit3']) && isset($post['initialLongEdit3'])) {
      $dataTreatmentTable3 = array(
        // 'nokun'                     => $nokun,
        'initialLat'                => $post['initialLatEdit3'],
        'valueTreatmentLat'         => $post['valueTreatmentLatEdit3'],
        'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLatEdit3'],
        'initialVrt'                => $post['initialVrtEdit3'],
        'valueTreatmentVrt'         => $post['valueTreatmentVrtEdit3'],
        'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrtEdit3'],
        'initialLong'               => $post['initialLongEdit3'],
        'valueTreatmentLong'        => $post['valueTreatmentLongEdit3'],
        'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLongEdit3'],
        'oleh'                      => $this->session->userdata('id'),
      );
      // echo "<pre>"; print_r($dataTreatmentTable3); echo "</pre>"; exit();
      $this->TreatmentDoseModel->simpanTreatment_radiografer($dataTreatmentTable3);
    }

    if (isset($post['initialLatEdit3a']) && isset($post['initialVrtEdit3a']) && isset($post['initialLongEdit3a'])) {
      $dataTreatmentTable3a = array(
        // 'nokun'                     => $nokun,
        'initialLat'                => $post['initialLatEdit3a'],
        'valueTreatmentLat'         => $post['valueTreatmentLatEdit3a'],
        'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLatEdit3a'],
        'initialVrt'                => $post['initialVrtEdit3a'],
        'valueTreatmentVrt'         => $post['valueTreatmentVrtEdit3a'],
        'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrtEdit3a'],
        'initialLong'               => $post['initialLongEdit3a'],
        'valueTreatmentLong'        => $post['valueTreatmentLongEdit3a'],
        'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLongEdit3a'],
        'oleh'                      => $this->session->userdata('id'),
      );
      // echo "<pre>"; print_r($dataTreatmentTable3a); echo "</pre>"; exit();
      $this->TreatmentDoseModel->simpanTreatment_radiografer($dataTreatmentTable3a);
    }


    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }
}

/* End of file TreatmentDose.php */
/* Location: ./application/controllers/radioterapi/TreatmentDose.php */
