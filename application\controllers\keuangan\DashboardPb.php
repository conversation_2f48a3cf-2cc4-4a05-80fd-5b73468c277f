<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DashboardPb extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('keuanganModel','masterModel'));
  }

  public function index()
  {
    $pendapatanBiaya = $this->keuanganModel->pendapatanBiaya();
    $surplusAll = $this->keuanganModel->surplusAll();
    
    $data = array(
      'title'           => 'Halaman Sistem Informasi Manajemen Anggaran',
      'isi'             => 'Keuangan/dashboard/dashboardPendapatanBiaya',
      'pendapatanBiaya' => $pendapatanBiaya,
      'surplusAll' => $surplusAll,
    );
    // echo "<pre>";print_r($data);exit();
    $this->load->view('layout/wrapper',$data);
  }

}

/* End of file DashboardPb.php */
/* Location: ./application/controllers/keuangan/DashboardPb.php */