<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AntrianModel extends CI_Model
{
	public function __construct()
	{
		parent::__construct();
	}


	function getNoAntrianFarmasi($noorder = '', $id_obat = '', $oleh = '', $penunjang = '0')
	{
		if ($noorder) {
			// $string="SELECT layanan.fnoAntrianSet($nomr, '$nokun','$ruangan')";
			$string1 = "SELECT
									MAX(IF(IFNULL(ib.MAPPING_EKLAIM, 0) = 2, 1, 0)) mappingeklaim,
    								MAX(IFNULL(ib.KATEGORI_VERIFIKASI, 0)) verifikasi
								FROM inventory.barang ib
								WHERE ib.ID IN($id_obat)";

			$query1 = $this->db->query($string1);
			$datacek = $query1->row_array();
			$verfikasi = ($datacek['mappingeklaim'] + $datacek['verifikasi']) > 0 ? 1 : 0;
			// $verfikasi=1;

			if ($verfikasi > 0) {
				$string1 = "UPDATE layanan.order_resep SET VERIFIKASI = 1 WHERE NOMOR = '$noorder'";
				$this->db->query($string1);
			}

      $string="";
      try {
        $string="INSERT INTO antrianv2.antrian (nomr,iddokter,nokun,ref,id_jenis_antrian,nomor,nomordisplay,oleh,via_app)
        SELECT pp.NORM nomr, 
        lor.DOKTER_DPJP iddokter, pk.NOMOR nokun,lor.NOMOR noorder
        ,(
        CASE
        WHEN '$verfikasi'='1' OR lor.TUJUAN='105050105' THEN
            3
        WHEN (SELECT max(detil.RACIKAN) FROM layanan.order_detil_resep detil where detil.ORDER_ID=lor.NOMOR )>0 THEN
            15
        WHEN (lor.TUJUAN='105050101' AND ((IF(orlab.NOMOR IS NULL, 0,1)+IF(orrad.NOMOR IS NULL, 0,1)+ IF(orrapi.NOMOR IS NULL, 0,1)+ IF(pkon.NOMOR IS NULL, 0,1))>0 OR '$penunjang'='1')) THEN
            2
        ELSE
            1
        END
        ) AS idjenis
        ,(COALESCE(
          CASE 
          WHEN '$verfikasi'='1' OR lor.TUJUAN='105050105' THEN
              (SELECT MAX(ant.nomor)
              FROM  
              antrianv2.m_jenis_antrian ajr 
              LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian
              WHERE ajr.id=3 AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            )
          WHEN (SELECT max(detil.RACIKAN) FROM layanan.order_detil_resep detil where detil.ORDER_ID=lor.NOMOR )>0 THEN
              (SELECT MAX(ant.nomor)
              FROM  
              antrianv2.m_jenis_antrian ajr 
              LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian
              WHERE ajr.id=15 AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            )
        WHEN (lor.TUJUAN='105050101' AND ((IF(orlab.NOMOR IS NULL, 0,1)+IF(orrad.NOMOR IS NULL, 0,1)+ IF(orrapi.NOMOR IS NULL, 0,1)+ IF(pkon.NOMOR IS NULL, 0,1))>0 OR '$penunjang'='1')) THEN
            (SELECT MAX(ant.nomor)
              FROM  
              antrianv2.m_jenis_antrian ajr 
              LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian
              WHERE ajr.id=2 AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            )
          ELSE
          (SELECT MAX(ant.nomor)
              FROM  
              antrianv2.m_jenis_antrian ajr 
              LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian
              WHERE ajr.id=1 AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            )
          END,0)+1)  AS nomor
        ,(CASE 
          WHEN '$verfikasi'='1' OR lor.TUJUAN='105050105' THEN
              (SELECT concat(ajr.prefix ,LPAD((COALESCE(MAX(ant.nomor),0)+1), GREATEST(LENGTH(COALESCE(MAX(ant.nomor),0)+1), 3), 0))
            FROM  
            antrianv2.m_jenis_antrian ajr 
            LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian  AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            WHERE ajr.id=3
            )
          WHEN (SELECT max(detil.RACIKAN) FROM layanan.order_detil_resep detil where detil.ORDER_ID=lor.NOMOR )>0 THEN
            (SELECT concat(ajr.prefix ,LPAD((COALESCE(MAX(ant.nomor),0)+1), GREATEST(LENGTH(COALESCE(MAX(ant.nomor),0)+1), 3), 0))
            FROM  
            antrianv2.m_jenis_antrian ajr 
            LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian  AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            WHERE ajr.id=15
            )
          
          WHEN (lor.TUJUAN='105050101' AND ((IF(orlab.NOMOR IS NULL, 0,1)+IF(orrad.NOMOR IS NULL, 0,1)+ IF(orrapi.NOMOR IS NULL, 0,1)+ IF(pkon.NOMOR IS NULL, 0,1))>0 OR '$penunjang'='1')) THEN
            (SELECT concat(ajr.prefix ,LPAD((COALESCE(MAX(ant.nomor),0)+1), GREATEST(LENGTH(COALESCE(MAX(ant.nomor),0)+1), 3), 0))
            FROM  
            antrianv2.m_jenis_antrian ajr 
            LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian  AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            WHERE ajr.id=2
            )
          ELSE 
            (SELECT concat(ajr.prefix ,LPAD((COALESCE(MAX(ant.nomor),0)+1), GREATEST(LENGTH(COALESCE(MAX(ant.nomor),0)+1), 3), 0))
            FROM  
            antrianv2.m_jenis_antrian ajr 
            LEFT JOIN antrianv2.antrian ant  ON ajr.id=ant.id_jenis_antrian  AND ant.created_at BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND
            WHERE ajr.id=1
            ) 
          END)  AS nodisplay
        ,lor.OLEH oleh,1 jalur

        FROM pendaftaran.kunjungan pk
        LEFT JOIN layanan.order_lab orlab ON orlab.KUNJUNGAN = pk.NOMOR 
        LEFT JOIN layanan.order_rad orrad ON orrad.KUNJUNGAN = pk.NOMOR 
        LEFT JOIN layanan.order_radioterapi orrapi ON orrapi.KUNJUNGAN = pk.NOMOR 
        LEFT JOIN pendaftaran.konsul pkon ON pkon.KUNJUNGAN = pk.NOMOR 
        LEFT JOIN master.ruangan mru ON pk.RUANGAN=mru.ID  

        LEFT JOIN pendaftaran.pendaftaran pp ON pk.NOPEN=pp.NOMOR
        LEFT JOIN layanan.order_resep lor ON lor.KUNJUNGAN = pk.NOMOR
        LEFT JOIN pendaftaran.tujuan_pasien ptp ON ptp.NOPEN=pp.NOMOR
        LEFT JOIN master.ruangan mrucek ON mrucek.ID=ptp.RUANGAN  
    
        LEFT JOIN antrianv2.antrian acek ON acek.ref='$noorder'
        WHERE lor.NOMOR='$noorder'
        AND lor.TUJUAN IN('105050101','105050105')
        AND mru.ANTRIAN=1
        AND mrucek.JENIS_KUNJUNGAN!=3
        AND acek.id IS NULL
        AND pk.NOMOR IS NOT NULL 
        GROUP BY pp.NORM
        LIMIT 1";
        $query  = $this->db->query($string);
        if($query){
          return true;
        }else{
          return false;
        }
        
      } catch (\Throwable $th) {
        $data = [
          'noorder' => $noorder,
          'kode' => '0',
          'str_query' => $string,
          'str_error' => json_encode($th, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
        ];
        $this->db->insert('antrianv2.log_antrian', $data);
        return false;
      }
		}
	}

	public function cekGanda($display = null, $posisi = null) // periksa jika pasien terdaftar di lebih dari satu dokter
	{
		$this->db->select('id');
		$this->db->from('antrianv2.antrian');
		$this->db->where('nomordisplay', $display);
		$this->db->where('posisi', $posisi);
		$this->db->where('DATE(created_at) = CURDATE()');
		$this->db->where_in('status', [1, 3]);
		$query = $this->db->get();
		return $query->row_array();
	}

	public function simpan($data)
	{
		$this->db->insert('antrianv2.antrian', $data);
		return $this->db->insert_id();
	}

	public function ubah($id, $data)
	{
		$this->db->where('antrianv2.antrian.id', $id);
		$this->db->update('antrianv2.antrian', $data);
	}

	public function ambilNomor($posisi)
	{
		$this->db->select('(IFNULL(MAX(nomor), 0) + 1) AS nomor_baru');
		$this->db->from('antrianv2.antrian');
		$this->db->where('DATE(created_at) = CURDATE()');
		$this->db->where('posisi', $posisi);
		$query = $this->db->get();
		return $query->row_array();
	}
}

// BEGIN
// DECLARE nomor INT;
// DECLARE ruangasal CHAR(19);

// SELECT a.noantrian
// FROM antrian a
// WHERE a.nomr=PNORM AND a.noorder=PNOORDER AND a.ruangan=PRUANGAN INTO nomor;

// IF(nomor>0) THEN RETURN nomor; ELSE
// SELECT IFNULL((
// SELECT MAX(a.noantrian)+1 nomor
// FROM antrian a
// WHERE a.ruangan=PRUANGAN AND date(a.created_at)=CURDATE()
// ORDER BY a.noantrian DESC
// LIMIT 1
// ),1) INTO nomor;

// SELECT pk.RUANGAN INTO ruangasal
// FROM pendaftaran.kunjungan pk
// LEFT JOIN pendaftaran.pendaftaran pp ON pk.NOPEN=pp.NOMOR
// LEFT JOIN layanan.order_resep lor ON pk.NOMOR=lor.KUNJUNGAN
// WHERE lor.NOMOR=PNOORDER;
// IF(PRUANGAN='105050101' AND ruangasal NOT IN('105020201','105020202')) THEN
// INSERT INTO antrian (nomr,ruangan,noorder,ruangasal,noantrian) VALUES(PNORM, PRUANGAN, PNOORDER ,ruangasal,nomor); RETURN nomor;
// ELSE RETURN NULL;
// END IF;
// END IF;
// END

// End of file AntrianModel.php
// Location: ./application/models/antrian/AntrianModel.php