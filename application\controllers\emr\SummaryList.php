<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SummaryList extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'EresepModel',
        'hemodialisaModel',
        'geriatri/InstrumenMNA_Model',
        'laporanModel',
        'rekam_medis/rawat_inap/catatanTerintegrasi/CpptModel'
      ]
    );
  }

  public function index()
  {
    $nomr = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);

    $data = [
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'dataCPPT' => $this->CpptModel->ambilPerNopen($nopen)
    ];
    // echo'<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/summaryList/index', $data);
  }

  public function pilihRangeSummary()
  {
    $post = $this->input->post('tgl');
    $nomr = $this->input->post('nomr');
    $pisah = explode('-', $post);
    $tgl1 = date('Y-m-d', strtotime($pisah[0]));
    $tgl2 = date('Y-m-d', strtotime($pisah[1]));

    $dataSummaryList = $this->pengkajianAwalModel->dSummaryList($tgl1, $tgl2, $nomr);
    // echo'<pre>';print_r($dataSummaryList);exit();

    $data = array(
      'tgl1' => $tgl1,
      'tgl2' => $tgl2,
      'nomr' => $nomr,
      'dataSummaryList' => $dataSummaryList,
    );
    $this->load->view('Pengkajian/summaryList/dataPilihSummary', $data);
  }

  public function simpanSLP()
  {
    $data = array(
      'nomr' => $this->input->post('nomr'),
      'nokun' => $this->input->post('nokun'),
      'diagnosis' => $this->input->post('diagnosis'),
      'tata_laksana' => $this->input->post('tatalaksana'),
      'catatan_penting' => $this->input->post('catatanPenting'),
      'jenis' => 2,
      'oleh' => $this->session->userdata('id'),
    );

    $this->pengkajianAwalModel->simpanSummaryL($data);
  }

  public function simpanSummaryList()
  {
    $data = array(
      'nomr' => $this->input->post('nomr'),
      'nokun' => $this->input->post('nokun'),
      'diagnosis' => $this->input->post('diagnosis'),
      'operasi' => $this->input->post('operasi'),
      'kemoterapi' => $this->input->post('kemoterapi'),
      'radiasi' => $this->input->post('radiasi'),
      'pengobatan' => $this->input->post('pengobatan'),
      'oleh' => $this->session->userdata('id'),
    );

    $this->pengkajianAwalModel->simpanSummaryL($data);
  }

}

/* End of file SummaryList.php */
/* Location: ./application/controllers/emr/SummaryList.php */