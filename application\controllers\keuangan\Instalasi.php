<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Instalasi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('keuangan/InstalasiModel'));
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'get'){
                $instalasi = $this->InstalasiModel->get_table(FALSE);

                $data = array();
                foreach($instalasi as $dataInstalasi){
                    $sub_array = array();
                    $sub_array['id'] = $dataInstalasi -> ID;
                    $sub_array['text'] = $dataInstalasi -> DESKRIPSI;

                    $data[] = $sub_array;
                }

                echo json_encode($data);
            }
    	}
    }
}