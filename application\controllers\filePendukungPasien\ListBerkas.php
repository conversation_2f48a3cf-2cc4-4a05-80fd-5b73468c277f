<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ListBerkas extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(2,$this->session->userdata('akses')) OR !in_array(4,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('uploadRmModel','masterModel'));
  }

  public function index()
  {
    $listVBPJS = $this->uploadRmModel->listVBPJS();

    $data = array(
      'title'      => 'Halaman Data Upload Berkas VBPJS',
      'isi'        => 'uploadRm/tableDataVBPJS',
      'listVBPJS' => $listVBPJS,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function getFileEmr ()
  {
    $id = $this->input->post('id');
    $embed = $this->uploadRmModel->downloadFileVBPJS($id);
    ?>
      <embed class="media" src="http://************/vbpjs/uploads/<?php echo $embed['file'] ?>" width="100%;" height="700px;"></embed>
    <?php
  }

}

/* End of file ListBerkas.php */
/* Location: ./application/controllers/filePendukungPasien/ListBerkas.php */
