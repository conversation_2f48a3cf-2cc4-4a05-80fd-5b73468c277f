<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormLaporanOperasi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'operasi/LaporanOperasiModel',
        'operasi/LaporanOperasiDetailModel',
        'operasi/WaitingListModel',
        'rekam_medis/rawat_inap/igd/SurKetEmModel'
      ]
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $nokun = isset($post['nokun']) ? $post['nokun'] : $this->uri->segment(6);
    $data = [
      'listDr' => $this->masterModel->listDrUmum(),
      'listPerawat' => $this->masterModel->listPerawat(105090101, 5),
      'listDrAnestesi' => $this->masterModel->listDrAnestesi(),
      'jenisAnestesi' => $this->masterModel->referensi(584),
      'kategoriOperasi' => $this->masterModel->referensi(578),
      'sifatOperasi' => $this->masterModel->referensi(580),
      'tujuanOperasi' => $this->masterModel->referensi(1456),
      'jenisPembedahan' => $this->masterModel->referensi(581),
      'antibiotikPropilaksis' => $this->masterModel->referensi(582),
      'teknikAnestesiLokal' => $this->masterModel->referensi(623),
      'responHipersensitivitas' => $this->masterModel->referensi(624),
      'kejadianToksikasi' => $this->masterModel->referensi(625),
      'komplikasi' => $this->masterModel->referensi(626),
      'jumlahKehilanganDarah' => $this->masterModel->referensi(627),
      'transfusi' => $this->masterModel->referensi(628),
      'spesimen' => $this->masterModel->referensi(629),
      'pemasanganImplan' => $this->masterModel->referensi(630),
      'pilihanCPPT' => $this->masterModel->referensi(1407),
    ];

    if (isset($id)) {
      // Form ubah
      $detail = $this->LaporanOperasiModel->history(null, null, null, $id);
      $data['id'] = $id;
      $data['detail'] = $detail;
      $nomr = $detail['NORM'];
      $data['isiPraBedah'] = $this->LaporanOperasiDetailModel->praBedah($id);
      $data['isiPascaBedah'] = $this->LaporanOperasiDetailModel->pascaBedah($id);
      $data['isiTindakan'] = $this->LaporanOperasiDetailModel->tindakan($id);
      $data['jenis'] = $post['jenis'] ?? null;
      $data['historyTransfusi'] = $this->LaporanOperasiModel->historyTransfusi($id);
      $data['daftarTunggu'] = $this->WaitingListModel->ambilDaftar($nomr, null);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/operasi/laporanOperasi/detail', $data);
    } else {
      // Form tambah
      $pasien = $this->pengkajianAwalModel->getNomr($nokun);
      $nomr = $pasien['NORM'];
      $data['nokun'] = $nokun;
      $data['nomr'] = $nomr;
      $data['pasien'] = $pasien;
      $data['nopen'] = $pasien['NOPEN'];
      $data['jumlah'] = $this->LaporanOperasiModel->history($nomr, null, 'jumlah', null);
      $data['daftarTunggu'] = $this->WaitingListModel->ambil($nomr, true);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/operasi/laporanOperasi/index', $data);
    }
  }

  public function statusLokalis()
  {
    $SoapCppt = $this->masterModel->soap();
    $idLapOperasi = $this->uri->segment(5);

    $data = [
      'title' => 'Bagian Tubuh Status Lokalis',
      'isi' => 'Pengkajian/emr/statusLokalis/statusLokalisLapOperasi',
      'nokun' => $this->uri->segment(4),
      'idLapOperasi' => $idLapOperasi,
      'SoapCppt' => $SoapCppt,
      'historyLokalis' => $this->LaporanOperasiModel->historyLokalis($idLapOperasi),
    ];
    $this->load->view('layout/wrapper', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->LaporanOperasiModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $status = 1;
          // echo '<pre>';print_r($post);exit();

          // Data Laporan Operasi
          $data = [
            'id' => $post['id'] ?? null,
            'nokun' => $post['nokun'] ?? null,
            'id_waiting_list' => $post['id_waiting_list'] ?? null,
            'dokter_bedah' => isset($post['dokter_bedah']) ? json_encode($post['dokter_bedah']) : null,
            'asisten_operator' => isset($post['asisten_operator']) ? json_encode($post['asisten_operator']) : null,
            'asisten_operator_lainnya' => $post['asisten_operator_lainnya'] ?? null,
            'perawat_instrumentator' => isset($post['perawat_instrumentator']) ? json_encode($post['perawat_instrumentator']) : null,
            'perawat_sirkuler' => $post['perawat_sirkuler'] ?? null,
            'dokter_anestesi' => isset($post['dokter_anestesi']) ? json_encode($post['dokter_anestesi']) : null,
            'jenis_anestesi' => $post['jenis_anestesi'] ?? null,
            // Mulai Keterangan Anestesi Lokal
            'teknik_anestesi_lokal' => $post['teknik_anestesi_lokal'] ?? null,
            'keterangan_teknik' => $post['keterangan_teknik'] ?? null,
            'lokasi' => $post['lokasi'] ?? null,
            'obat_anestesi' => $post['obat_anestesi'] ?? null,
            'respon_hipersensitivitas' => $post['respon_hipersensitivitas'] ?? null,
            'isi_respon_hipersensitivitas' => $post['isi_respon_hipersensitivitas'] ?? null,
            'kejadian_toksikasi' => $post['kejadian_toksikasi'] ?? null,
            'isi_kejadian_toksikasi' => $post['isi_kejadian_toksikasi'] ?? null,
            // Selesai Keterangan Anestesi Lokal
            'kategori_operasi' => $post['kategori_operasi'] ?? null,
            'lok_pengambilan_sampel' => $post['lok_pengambilan_sampel'] ?? null,
            'diagnosis_pra_bedah' => null,
            'diagnosis_pra_bedah_lainnya' => $post['diagnosis_pra_bedah_lainnya'] ?? null,
            'tgl_operasi' => $post['tgl_operasi'] ?? null,
            'jam_mulai' => $post['jam_mulai'] ?? null,
            'jam_selesai' => $post['jam_selesai'] ?? null,
            'diagnosis_pasca_bedah' => null,
            'diagnosis_pasca_bedah_lainnya' => $post['diagnosis_pasca_bedah_lainnya'] ?? null,
            'letak_tumor_primer' => $post['letak_tumor_primer'] ?? null,
            'sifat_operasi' => $post['sifat_operasi'] ?? null,
            'tujuan_operasi' => $post['tujuan_operasi'] ?? null,
            'jenis_pembedahan' => $post['jenis_pembedahan'] ?? null,
            'antibiotik_propilaksis' => $post['antibiotik_propilaksis'] ?? null,
            // Mulai Keterangan Antibiotik Propilaksis
            'jenis_antibiotik_propilaksis' => $post['jenis_antibiotik_propilaksis'] ?? null,
            'waktu_antibiotik_propilaksis' => $post['waktu_antibiotik_propilaksis'] ?? null,
            // Selesai Keterangan Antibiotik Propilaksis
            'tindakan_operasi_lainnya' => $post['tindakan_operasi_lainnya'] ?? null,
            'deskripsi_operasi_1' => $post['deskripsi_operasi_1'] ?? null,
            'deskripsi_operasi_2' => $post['deskripsi_operasi_2'] ?? null,
            'deskripsi_operasi_3' => $post['deskripsi_operasi_3'] ?? null,
            'komplikasi' => $post['komplikasi'] ?? null,
            'isi_komplikasi' => $post['isi_komplikasi'] ?? null,
            'jml_kehilangan_darah' => $post['jml_kehilangan_darah'] ?? null,
            'spesimen' => $post['spesimen'] ?? null,
            'isi_spesimen' => $post['isi_spesimen'] ?? null,
            'pemasangan_implan' => $post['pemasangan_implan'] ?? null,
            'nama_implan' => $post['nama_implan'] ?? null,
            'seri_implan' => $post['seri_implan'] ?? null,
            'ket_riwayat_penyakit_sekarang' => $post['ket_riwayat_penyakit_sekarang'] ?? null,
            'status' => $status,
            'oleh' => $this->session->userdata['id'],
          ];
          // echo '<pre>';print_r($data);exit();

          if (isset($post['id'])) {
            // Ubah Laporan Operasi
            $this->LaporanOperasiModel->ubah($data, $post['id']);

            // Mulai Daftar Tunggu
            if (isset($post['id_waiting_list']) && $post['id_waiting_list'] != $post['id_waiting_list_lama']) {
              $dataWlBaru['status'] = 3; // 3 indicates completed status
              $this->WaitingListModel->ubah($post['id_waiting_list'], $dataWlBaru);

              $dataWlLama['status'] = 1; // 1 indicates waiting status
              $this->WaitingListModel->ubah($post['id_waiting_list_lama'], $dataWlLama);
            }
            // Akhir Daftar Tunggu

            // Ambil Diagnosis Pra Bedah
            $data = ['diagnosis_pra_bedah' => $this->LaporanOperasiModel->praBedah($post['id'])];

            if (isset($post['diagnosis_pra_bedah'])) {
              $data = ['status' => 0];
              // Batal Diagnosis Pra Bedah Sebelumnya
              $this->LaporanOperasiModel->ubahPraBedah($data, $post['id']);

              // Simpan Diagnosis Pra Bedah
              $i = 0;
              $dataPraBedah = [];
              foreach ($post['diagnosis_pra_bedah'] as $dpr) {
                $dataPraBedah[$i] = [
                  'id_laporan_operasi' => $post['id'],
                  'diagnosis_pra_bedah_multiple' => $dpr,
                  'status' => $status,
                ];
                $i++;
              }
              $this->LaporanOperasiModel->simpanPraBedah($dataPraBedah);
            }

            // Ambil Diagnosis Pasca Bedah
            $data = ['diagnosis_pasca_bedah' => $this->LaporanOperasiModel->pascaBedah($post['id'])];

            if (isset($post['diagnosis_pasca_bedah'])) {
              $data = ['status' => 0];
              // Batal Diagnosis Pasca Bedah Sebelumnya
              $this->LaporanOperasiModel->ubahPascaBedah($data, $post['id']);

              // Simpan Diagnosis Pasca Bedah
              $i = 0;
              $dataPascaBedah = [];
              foreach ($post['diagnosis_pasca_bedah'] as $dpc) {
                $dataPascaBedah[$i] = [
                  'id_laporan_operasi' => $post['id'],
                  'diagnosis_pasca_bedah_multiple' => $dpc,
                  'status' => $status,
                ];
                $i++;
              }
              $this->LaporanOperasiModel->simpanPascaBedah($dataPascaBedah);
            }

            // Ambil Tindakan Operasi
            $data = ['tindakan' => $this->LaporanOperasiModel->tindakan($post['id'])];

            if (isset($post['tindakan_operasi'])) {
              $data = ['status' => 0];
              // Batal Tindakan Operasi Sebelumnya
              $this->LaporanOperasiModel->ubahDetail($data, $post['id']);

              // Simpan Tindakan Operasi
              $i = 0;
              $dataTindakan = [];
              foreach ($post['tindakan_operasi'] as $to) {
                $dataTindakan[$i] = [
                  'id_laporan_operasi' => $post['id'],
                  'tindakan_operasi' => $to,
                  'status' => $status,
                ];
                $i++;
              }
              $this->LaporanOperasiModel->simpanTindakan($dataTindakan);
            }

            // Simpan Transfusi Darah Laporan Operasi
            if (isset($post['jenis_transfusi']) && isset($post['volume_transfusi'])) {
              $data = ['status' => 0];
              // Batal Transfusi Darah Laporan Operasi
              $this->LaporanOperasiModel->ubahTransfusi($data, $post['id']);

              $j = 0;
              $dataTransfusi = [];
              foreach ($post['jenis_transfusi'] as $jt) {
                $dataTransfusi[$j] = [
                  'id_laporan_operasi' => $post['id'],
                  'jenis_transfusi' => $jt,
                  'volume_transfusi' => $post['volume_transfusi'][$j],
                ];
                $j++;
              }
              // echo '<pre>';print_r($dataTransfusi);exit();
              $this->LaporanOperasiModel->simpanTransfusi($dataTransfusi);
            }
          } else {
            // Simpan Laporan Operasi
            $id = $this->LaporanOperasiModel->simpan($data);

            // Mulai Daftar Tunggu
            if (isset($post['id_waiting_list'])) {
              $dataWl['status'] = 3; // 3 indicates completed status
              $this->WaitingListModel->ubah($post['id_waiting_list'], $dataWl);
            }
            // Akhir Daftar Tunggu

            // Simpan Diagnosis Pra Bedah
            if (isset($post['diagnosis_pra_bedah'])) {
              $i = 0;
              $dataPraBedah = [];
              foreach ($post['diagnosis_pra_bedah'] as $dpr) {
                $dataPraBedah[$i] = [
                  'id_laporan_operasi' => $id,
                  'diagnosis_pra_bedah_multiple' => $dpr,
                  'status' => $status,
                ];
                $i++;
              }
              $this->LaporanOperasiModel->simpanPraBedah($dataPraBedah);
            }

            // Simpan Diagnosis Pasca Bedah
            if (isset($post['diagnosis_pasca_bedah'])) {
              $i = 0;
              $dataPascaBedah = [];
              foreach ($post['diagnosis_pasca_bedah'] as $dpc) {
                $dataPascaBedah[$i] = [
                  'id_laporan_operasi' => $id,
                  'diagnosis_pasca_bedah_multiple' => $dpc,
                  'status' => $status,
                ];
                $i++;
              }
              $this->LaporanOperasiModel->simpanPascaBedah($dataPascaBedah);
            }

            // Simpan Tindakan Laporan Operasi
            if (isset($post['tindakan_operasi'])) {
              $i = 0;
              $dataTindakan = [];
              foreach ($post['tindakan_operasi'] as $to) {
                $dataTindakan[$i] = [
                  'id_laporan_operasi' => $id,
                  'tindakan_operasi' => $to,
                  'status' => $status,
                ];
                $i++;
              }
              // echo '<pre>';print_r($dataTindakan);exit();
              $this->LaporanOperasiModel->simpanTindakan($dataTindakan);
            }

            // Simpan Transfusi Darah Laporan Operasi
            if (isset($post['jenis_transfusi']) && isset($post['volume_transfusi'])) {
              $j = 0;
              $dataTransfusi = [];
              foreach ($post['jenis_transfusi'] as $jt) {
                $dataTransfusi[$j] = [
                  'id_laporan_operasi' => $id,
                  'jenis_transfusi' => $jt,
                  'volume_transfusi' => $post['volume_transfusi'][$j],
                ];
                $j++;
              }
              // echo '<pre>';print_r($dataTransfusi);exit();
              $this->LaporanOperasiModel->simpanTransfusi($dataTransfusi);
            }
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
          } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
          }
        } else {
          $result = [
            'status' => 'failed',
            'errors' => $this->form_validation->error_array()
          ];
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->LaporanOperasiModel->get($post['id'], true);
        // echo '<pre>';print_r($data);exit();
        echo json_encode(
          [
            'status' => 'success',
            'data' => $data,
          ]
        );
      } elseif ($param == 'simpanLokalis') {
        $rules = $this->LaporanOperasiModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $status = 1;
          // echo '<pre>';print_r($post);exit();

          // Data Laporan Operasi
          $data = [
            'id_laporan_operasi' => isset($post['id_laporan_operasi']) ? $post['id_laporan_operasi'] : null,
            'judul' => isset($post['judul']) ? $post['judul'] : null,
            'data' => isset($post['img_val']) ? file_get_contents($post['img_val']) : null,
            'catatan' => isset($post['catatan']) ? $post['catatan'] : null,
            'status' => $status,
            'oleh' => $this->session->userdata['id'],
          ];
          // echo '<pre>';print_r($data);exit();
          $this->LaporanOperasiModel->simpanLokalis($data);

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
          } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
          }
        } else {
          $result = [
            'status' => 'failed',
            'errors' => $this->form_validation->error_array()
          ];
        }
        echo json_encode($result);
      }
    }
  }

  public function icd9()
  {
    $result = $this->SurKetEmModel->icd9();
    $data = [];
    foreach ($result as $row) {
      $sub_array = [];
      $sub_array['id'] = $row->CODE;
      $sub_array['text'] = $row->CODE . ' - ' . $row->STR;
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function icd10()
  {
    $result = $this->SurKetEmModel->icd10();
    $data = [];
    foreach ($result as $row) {
      $sub_array = [];
      $sub_array['id'] = $row->CODE;
      $sub_array['text'] = $row->CODE . ' - ' . $row->STR;
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function tabel()
  {
    $draw = intval($this->input->POST('draw'));
    $nomr = $this->input->POST('nomr');
    $history = $this->LaporanOperasiModel->history($nomr, null, 'tabel', null);
    $data = [];
    $no = 0;
    $pra_bedah = null;
    $pasca_bedah = null;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history->result() as $h) {
      // Mulai diagnosis pra bedah
      if (!empty($h->diagnosis_pra_bedah_multiple)) {
        $pra_bedah = $h->diagnosis_pra_bedah_multiple;
      } elseif (!empty($h->diagnosis_pra_bedah_lainnya)) {
        $pra_bedah = $h->diagnosis_pra_bedah_lainnya;
      } else {
        $pra_bedah = '-';
      }
      // Akhir diagnosis pra bedah

      // Mulai diagnosis pasca bedah
      if (!empty($h->diagnosis_pasca_bedah_multiple)) {
        $pasca_bedah = $h->diagnosis_pasca_bedah_multiple;
      } elseif (!empty($h->diagnosis_pasca_bedah_lainnya)) {
        $pasca_bedah = $h->diagnosis_pasca_bedah_lainnya;
      } else {
        $pasca_bedah = '-';
      }
      // Akhir diagnosis pasca bedah

      // Mulai periksa status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }
      // Akhir periksa status

      $data[] = [
        ++$no . '.',
        date('d/m/Y', strtotime($h->tgl_operasi)),
        $h->ruang,
        $h->DPJP,
        $pra_bedah,
        $pasca_bedah,
        $h->pengisi,
        date('d/m/Y, H.i.s', strtotime($h->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-lap-operasi' class='btn btn-sm btn-danger waves-effect tbl-batal-lap-operasi' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-lap-operasi' class='btn btn-sm btn-primary waves-effect tbl-detail-lap-operasi' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
          <a href='" . base_url("operasi/FormLaporanOperasi/statusLokalis/" . $h->nokun . "/" . $h->id) . "' class='btn btn-sm btn-purple waves-effect' target='_blank' rel='tag'>
            <i class='fa fa-image'></i> Status Lokalis
          </a>
          <a href='/reports/simrskd/ok/LaporanOperasi.php?format=pdf&id=" . $h->id . "' class='btn btn-sm btn-warning waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak
          </a>
        </div>",
      ];
    }

    $output = [
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function history()
  {
    $post = $this->input->post();
    $data = ['nomr' => $post['nomr']];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/operasi/laporanOperasi/history', $data);
  }

  public function keteranganLokalis()
  {
    $id = $this->input->post('id');
    $hasilFotoLokalis = $this->LaporanOperasiModel->hasilFotoLokalis($id);
    echo
      "<h4 class='card-title'>Tanggal Pengisian: <span class='text-custom'>" . date('d/m/Y, H.i.s', strtotime($hasilFotoLokalis["created_at"])) . "</span></h4>
      <div class='row'>
        <div class='col-lg-12'>
          <div class='form-group'>
            <label for='judul-keterangan-lokalis-lap-operasi'>Judul Lokalis</label>
            <input type='text' name='judul id='judul-keterangan-lokalis-lap-operasi class='form-control' value='" . $hasilFotoLokalis["judul"] . "' readonly>
          </div>
        </div>
      </div>
      <div class='row'>
        <div class='col-lg-6'>
          <label for='gambar-keterangan-lokalis-lap-operasi'>Hasil Foto</label><br>
          <img src='data:image;base64," . base64_encode($hasilFotoLokalis["data"]) . "' >
        </div>
        <div class='col-lg-6'>
          <label for='catatan-keterangan-lokalis-lap-operasi' class='col-form-label'>Catatan</label>
          <textarea class='form-control' id='catatan-keterangan-lokalis-lap-operasi' name='catatan' readonly>" . $hasilFotoLokalis["catatan"] . "</textarea>
        </div>
      </div>";
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    // echo '<pre>';print_r($id);exit();

    $data = ['status' => 0];
    $this->LaporanOperasiModel->ubah($data, $id);
    $this->LaporanOperasiModel->ubahPraBedah($data, $id);
    $this->LaporanOperasiModel->ubahPascaBedah($data, $id);
    $this->LaporanOperasiModel->ubahDetail($data, $id);
    $this->LaporanOperasiModel->ubahTransfusi($data, $id);
    $this->LaporanOperasiModel->ubahLokalisIdLapOperasi($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function nonaktifkanLokalis()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    // echo '<pre>';print_r($id);exit();

    $data = ['status' => 0];
    $this->LaporanOperasiModel->ubahLokalis($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }
}

// End of file FormLaporanOperasi.php
// Location: ./application/controllers/operasi/FormLaporanOperasi.php