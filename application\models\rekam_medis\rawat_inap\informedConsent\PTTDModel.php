<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PTTDModel extends MY_Model
{
  protected $_table_name = 'db_informed_consent.tb_informed_consent';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('db_informed_consent.tb_pttd', $data);
  }

  public function history($nomr, $param, $id)
  {
    if (isset($id)) {
      // Detail PTTD
      $this->db->select(
        'tic.id id_tic, tic.nokun, tic.jenis_informed_consent, tic.penerima_informasi, tic.dokter_pelaksana, tic.status,
        pttd.id id_pttd, pttd.tanggal, pttd.diagnosis, pttd.dasar_diagnosis, pttd.tindakan_kedokteran,
        pttd.indikasi_tindakan, pttd.indikasi_tindakan_lainnya, pttd.tata_cara, pttd.tujuan_tindakan,
        pttd.tujuan_tindakan_lainnya, pttd.tujuan_pengobatan, pttd.risiko, pttd.komplikasi, pttd.prognosis,
        pttd.prognosis_lainnya, pttd.alternatif_risiko, pttd.alternatif_risiko_lainnya, pttd.lainnya,
        pttd.ttd_menerangkan, pttd.ttd_menerima, tptk.id id_tptk, tptk.nama_keluarga, tptk.umur_keluarga,
        tptk.jk_keluarga, tptk.alamat_keluarga, tptk.tindakan, tptk.hub_keluarga_dgn_pasien, tptk.tanggal_persetujuan,
        tptk.ttd_menyatakan, tptk.ttd_saksi_keluarga, tptk.ttd_saksi_rumah_sakit, tptk.saksi_keluarga,
        tptk.saksi_rumah_sakit, tptk.status_persetujuan, master.getNamaLengkapPegawai(ap2.NIP) saksi_rs'
      );
    } elseif (isset($param)) {
      if ($param == 'jumlah') {
        // Jumlah
        $this->db->select('tic.id');
      } elseif ($param == 'tabel') {
        $this->db->select(
          // Tabel
          'pp.NORM, tic.id, tic.nokun, master.getNamaLengkapPegawai(ap.NIP) oleh, pttd.status,
          master.getNamaLengkapPegawai(md.NIP) dokter_pelaksana, tic.created_at tanggal, tic.jenis_informed_consent'
        );
      }
    }
    $this->db->from('db_informed_consent.tb_informed_consent tic');
    $this->db->join('db_informed_consent.tb_pttd pttd', 'pttd.id_informed_consent = tic.id', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = tic.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = tic.oleh', 'left');
    $this->db->join('master.dokter md', 'md.ID = tic.dokter_pelaksana', 'left');
    if (isset($id)) {
      // Detail
      $this->db->join('db_informed_consent.tb_persetujuan_tindakan_kedokteran tptk', 'tptk.id_informed_consent = tic.id', 'left');
      $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM', 'left');
      $this->db->join('aplikasi.pengguna ap2', 'ap2.ID = tptk.saksi_rumah_sakit', 'left');
      $this->db->where('tic.id', $id);
    } elseif (isset($param)) {
      // Jumlah dan tabel
      $this->db->where('pp.NORM', $nomr);
      $this->db->where('tic.jenis_informed_consent', 3027);
      if ($param == 'tabel') {
        // Tabel
        $this->db->order_by('tic.created_at', 'desc');
      }
    }

    $query = $this->db->get();
    if (isset($id)) {
      return $query->row_array(); // Detail
    } elseif (isset($param)) {
      if ($param == 'jumlah') {
        return $query->num_rows(); // Jumlah
      } elseif ($param == 'tabel') {
        return $query; // Tabel
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  public function ubah($id, $data)
  {
    $this->db->where($id);
    $this->db->update('db_informed_consent.tb_pttd', $data);
  }
}

/* End of file PTTDModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/informedConsent/PTTDModel.php */