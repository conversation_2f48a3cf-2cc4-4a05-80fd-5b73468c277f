<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ReevaluasiRadModel extends MY_Model{
	protected $_table_name = 'medis.tb_reevaluasi_rad';
	protected $_primary_key = 'kunjungan';
	protected $_order_by = 'kunjungan';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'ruangan' => array(
            'field' => 'ruangan',
            'label' => 'Ruangan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),		
    );
    
    public $rules_rawat_jalan = array(
		'reevaluasi_rad_jalan_ruangan' => array(
            'field' => 'reevaluasi_rad_jalan_ruangan',
            'label' => 'Ruangan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
    );
    
    public $rules_rawat_inap = array(
		'reevaluasi_rad_inap_ruangan' => array(
            'field' => 'reevaluasi_rad_inap_ruangan',
            'label' => 'Ruangan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('kp.kunjungan NOKUN, kp.tanggal TANGGAL_ODONTO
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('medis.tb_reevaluasi_rad kp');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = kp.kunjungan','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = kp.oleh','LEFT');

        $this->db->where('kp.STATUS !=','0');
        $this->db->where('p.NORM',$this->input->post('nomr'));
        $this->db->order_by('kp.TANGGAL', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
