<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ObservasiIGD extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Bangkok');
    $this->load->model(array('masterModel', 'FormulirTriaseModel', 'pengkajianAwalModel', 'igd/ObservasiIGDModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $idemr = $this->uri->segment(7);
    $nomr = $getNomr['NORM'];
    $observasiIGD = $this->ObservasiIGDModel->tampilObservasiIGD($nomr);

    if ($observasiIGD != null) {
      $vitalTriaseTerbaru = null;
    } else {
      $vitalTriaseTerbaru = $this->FormulirTriaseModel->vitalTriaseTerbaru($nokun);
    }

    $data = array(
      'getNomr' => $getNomr,
      'nokun' => $nokun,
      'vitalTriaseTerbaru' => $vitalTriaseTerbaru,
      'kesadaran' => $this->masterModel->referensi(5),
      'getPengkajian' => $this->pengkajianAwalModel->getPengkajian($idemr),
      'observasiIGD' => $observasiIGD,
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/igd/observasiIGD/index', $data);
  }

  public function simpanObservasiIGD()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $data = array(
      'nokun' => $post['nokun'],
      'tanggal' => $post['tanggal'],
      'jam' => $post['jam'],
      'oleh' => $this->session->userdata('id'),
      'td_sistolik' => $post['td_sistolik'],
      'td_diastolik' => $post['td_diastolik'],
      'nadi' => $post['nadi'],
      'pernapasan' => $post['pernapasan'],
      'suhu' => $post['suhu'],
      'cvp' => $post['cvp'],
      'wsd' => $post['wsd'],
      'kesadaran' => $post['kesadaran'],
      'perifer' => $post['perifer'],
      'oral' => $post['oral'],
      'parenteral' => $post['parenteral'],
      'muntah' => $post['muntah'],
      'ngt_pengeluaran' => $post['ngt_pengeluaran'],
      'bak' => $post['bak'],
      'bab' => $post['bab'],
      'wsd_drain' => $post['wsd_drain'],
      'tindakan_keperawatan' => $post['tindakan_keperawatan'],
      'bd_urine' => $post['bd_urine'],
      'iwl' => $post['iwl'],
      'balance' => $post['balance'],
      'saturasi' => $post['saturasi'],
      'oksigen' => $post['oksigen'],
      'updated_at' => date('Y-m-d H:i:s'),
    );
    $simpanFormObservasiIGD = $this->ObservasiIGDModel->simpanObservasiIGD($data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}