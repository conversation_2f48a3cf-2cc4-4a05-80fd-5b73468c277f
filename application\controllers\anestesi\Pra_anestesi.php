<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pra_anestesi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {

    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $id_praAnestesi = $this->uri->segment(8);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
    $hilangnya_gigi = $this->masterModel->referensi(633);
    $masalah_leher = $this->masterModel->referensi(664);
    $denyut_jantung = $this->masterModel->referensi(665);
    $batuk = $this->masterModel->referensi(666);
    $sesak_napas = $this->masterModel->referensi(667);
    $baru_saja_menderita_infeksi = $this->masterModel->referensi(668);
    $saluran_napas_atas = $this->masterModel->referensi(669);
    $sakit_dada = $this->masterModel->referensi(670);
    $muntah = $this->masterModel->referensi(671);
    $pingsan = $this->masterModel->referensi(672);
    $stroke = $this->masterModel->referensi(673);
    $kejang = $this->masterModel->referensi(674);
    $sedang_hamil = $this->masterModel->referensi(675);
    $kelainan_tulang_belakang = $this->masterModel->referensi(676);
    $obesitas = $this->masterModel->referensi(677);
    $klasifikasi_asa = $this->masterModel->referensi(655);
    $teknik_anestesia = $this->masterModel->referensi(740);
    $teknik_khusus = $this->masterModel->referensi(741);
    $monitoring = $this->masterModel->referensi(742);
    $alat_khusus = $this->masterModel->referensi(743);
    $perawatan_pasca_anestesi = $this->masterModel->referensi(744);
    $historyPraPasien = $this->pengkajianAwalModel->historyPasienPraAnestesi($nomr);
    $getPasien = $this->pengkajianAwalModel->historyDetailPasienPraSedasi($id_praAnestesi);

    // pra anestesi pasien
    $listKajianSistem = $this->masterModel->listKajianSistem();
    $listKajianSistem1 = $this->masterModel->listKajianSistem1();
    $listKajianSistem2 = $this->masterModel->listKajianSistem2();
    $listKajianSistem3 = $this->masterModel->listKajianSistem3();
    $listKajianSistem4 = $this->masterModel->listKajianSistem4();
    $listKajianSistem5 = $this->masterModel->listKajianSistem5();
    $listKajianSistem6 = $this->masterModel->listKajianSistem6();
    $listKajianSistem7 = $this->masterModel->listKajianSistem7();
    $listKajianSistem8 = $this->masterModel->listKajianSistem8();
    $listKajianSistem9 = $this->masterModel->listKajianSistem9();
    $listKajianSistem10 = $this->masterModel->listKajianSistem10();
    $listKajianSistem11 = $this->masterModel->listKajianSistem11();
    $listKajianSistem12 = $this->masterModel->listKajianSistem12();
    $listKajianSistem13 = $this->masterModel->listKajianSistem13();
    $listKajianSistem14 = $this->masterModel->listKajianSistem14();
    $listKajianSistem15 = $this->masterModel->listKajianSistem15();
    $listPemriksaanFisik = $this->masterModel->listPemriksaanFisik();
    $listPemriksaanFisik1 = $this->masterModel->listPemriksaanFisik1();
    $listPemriksaanFisik2 = $this->masterModel->listPemriksaanFisik2();
    $listPemriksaanFisik3 = $this->masterModel->listPemriksaanFisik3();
    $listPemriksaanFisik4 = $this->masterModel->listPemriksaanFisik4();
    $listPemriksaanFisik5 = $this->masterModel->listPemriksaanFisik5();
    $listPemriksaanFisik6 = $this->masterModel->listPemriksaanFisik6();
    $listPemriksaanPenunjang = $this->masterModel->listPemriksaanPenunjang();
    $listKlasifikasiAsa = $this->masterModel->listKlasifikasiAsa();
    $listMonitoring = $this->masterModel->listMonitoring();
    $listPerawatanPascaSedasi = $this->masterModel->listPerawatanPascaSedasi();
    $listAlergi = $this->masterModel->referensi(1023);
    $listMerokok = $this->masterModel->referensi(1024);
    $listAlkohol = $this->masterModel->referensi(1025);
    $listKopitehsoda = $this->masterModel->referensi(1026);
    $listOlahraga = $this->masterModel->referensi(1027);
    $listAspirin = $this->masterModel->referensi(1028);
    $listObatantisakit = $this->masterModel->referensi(1029);
    $listPerdarahan = $this->masterModel->referensi(1030);
    $listJantung = $this->masterModel->referensi(1031);
    $listPembekuan = $this->masterModel->referensi(1032);
    $listIramajantung = $this->masterModel->referensi(1033);
    $listPembiusan = $this->masterModel->referensi(1034);
    $listHipertensi = $this->masterModel->referensi(1035);
    $listDemamtinggi = $this->masterModel->referensi(1036);
    $listTuberkulosis = $this->masterModel->referensi(1037);
    $listDiabetes = $this->masterModel->referensi(1038);
    $listPenyakitberat = $this->masterModel->referensi(1039);
    $listPerdarahan_e = $this->masterModel->referensi(1040);
    $listJantung_e = $this->masterModel->referensi(1041);
    $listPembekuan_e = $this->masterModel->referensi(1042);
    $listIramajantung_e = $this->masterModel->referensi(1043);
    $listMaag_e = $this->masterModel->referensi(1044);
    $listAnemia_e = $this->masterModel->referensi(1045);
    $listAsma_e = $this->masterModel->referensi(1046);
    $listPingsan_e = $this->masterModel->referensi(1047);
    $listMengorok_e = $this->masterModel->referensi(1048);
    $listPembiusan_e = $this->masterModel->referensi(1049);
    $listHipertensi_e = $this->masterModel->referensi(1050);
    $listHepatitis_e = $this->masterModel->referensi(1051);
    $listKejang_e = $this->masterModel->referensi(1052);
    $listDemamtinggi_e = $this->masterModel->referensi(1053);
    $listPenyakitbawaan_e = $this->masterModel->referensi(1054);
    $listDiabetes_e = $this->masterModel->referensi(1055);
    $listPenyakitberat_e = $this->masterModel->referensi(1056);
    $listTransfusiDarah = $this->masterModel->referensi(1057);
    $listHiv = $this->masterModel->referensi(1058);
    $listPasienmemakai = $this->masterModel->referensi(1059);
    $listMenyusui = $this->masterModel->referensi(1060);

    $historypraanestesi = $this->pengkajianAwalModel->historypraanestesi($nomr);
    if ($id_praAnestesi != "") {
      $getpra_anestesi = $this->pengkajianAwalModel->getpra_anestesi($id_praAnestesi);
    }

    $data = array
    (
      'getNomr' => $getNomr,
      'historypraanestesi' => $historypraanestesi,
      'getpra_anestesi' => $id_praAnestesi != "" ? $getpra_anestesi : "",
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
      'hilangnya_gigi' => $hilangnya_gigi,
      'masalah_leher' => $masalah_leher,
      'getPasien' => $getPasien,
      'denyut_jantung' => $denyut_jantung,
      'batuk' => $batuk,
      'sesak_napas' => $sesak_napas,
      'baru_saja_menderita_infeksi' => $baru_saja_menderita_infeksi,
      'saluran_napas_atas' => $saluran_napas_atas,
      'sakit_dada' => $sakit_dada,
      'muntah' => $muntah,
      'historyPraPasien' => $historyPraPasien,
      'pingsan' => $pingsan,
      'stroke' => $stroke,
      'kejang' => $kejang,
      'sedang_hamil' => $sedang_hamil,
      'kelainan_tulang_belakang' => $kelainan_tulang_belakang,
      'obesitas' => $obesitas,
      'klasifikasi_asa' => $klasifikasi_asa,
      'teknik_anestesia' => $teknik_anestesia,
      'teknik_khusus' => $teknik_khusus,
      'monitoring' => $monitoring,
      'alat_khusus' => $alat_khusus,
      'perawatan_pasca_anestesi' => $perawatan_pasca_anestesi,
      'listKajianSistem' => $listKajianSistem,
      'listKajianSistem1' => $listKajianSistem1,
      'listKajianSistem2' => $listKajianSistem2,
      'listKajianSistem3' => $listKajianSistem3,
      'listKajianSistem4' => $listKajianSistem4,
      'listKajianSistem5' => $listKajianSistem5,
      'listKajianSistem6' => $listKajianSistem6,
      'listKajianSistem7' => $listKajianSistem7,
      'listKajianSistem8' => $listKajianSistem8,
      'listKajianSistem9' => $listKajianSistem9,
      'listKajianSistem10' => $listKajianSistem10,
      'listKajianSistem11' => $listKajianSistem11,
      'listKajianSistem12' => $listKajianSistem12,
      'listKajianSistem13' => $listKajianSistem13,
      'listKajianSistem14' => $listKajianSistem14,
      'listKajianSistem15' => $listKajianSistem15,
      'listPemriksaanFisik' => $listPemriksaanFisik,
      'listPemriksaanFisik1' => $listPemriksaanFisik1,
      'listPemriksaanFisik2' => $listPemriksaanFisik2,
      'listPemriksaanFisik3' => $listPemriksaanFisik3,
      'listPemriksaanFisik4' => $listPemriksaanFisik4,
      'listPemriksaanFisik5' => $listPemriksaanFisik5,
      'listPemriksaanFisik6' => $listPemriksaanFisik6,
      'listPemriksaanPenunjang' => $listPemriksaanPenunjang,
      'listKlasifikasiAsa' => $listKlasifikasiAsa,
      'listMonitoring' => $listMonitoring,
      'listPerawatanPascaSedasi' => $listPerawatanPascaSedasi,
            //For Pasien
      'listAlergi' => $listAlergi,
      'listMerokok' => $listMerokok,
      'listAlkohol' => $listAlkohol,
      'listKopitehsoda' => $listKopitehsoda,
      'listOlahraga' => $listOlahraga,
      'listAspirin' => $listAspirin,
      'listObatantisakit' => $listObatantisakit,
      'listPerdarahan' => $listPerdarahan,
      'listJantung' => $listJantung,
      'listPembekuan' => $listPembekuan,
      'listIramajantung' => $listIramajantung,
      'listPembiusan' => $listPembiusan,
      'listHipertensi' => $listHipertensi,
      'listDemamtinggi' => $listDemamtinggi,
      'listTuberkulosis' => $listTuberkulosis,
      'listDiabetes' => $listDiabetes,
      'listPenyakitberat' => $listPenyakitberat,
      'listPerdarahan_e' => $listPerdarahan_e,
      'listJantung_e' => $listJantung_e,
      'listPembekuan_e' => $listPembekuan_e, 
      'listIramajantung_e' => $listIramajantung_e, 
      'listMaag_e' => $listMaag_e, 
      'listAnemia_e' => $listAnemia_e,
      'listAsma_e' => $listAsma_e,
      'listPingsan_e' => $listPingsan_e,
      'listMengorok_e' => $listMengorok_e,
      'listPembiusan_e' => $listPembiusan_e,
      'listHipertensi_e' => $listHipertensi_e,
      'listHepatitis_e' => $listHepatitis_e,
      'listKejang_e' => $listKejang_e,
      'listDemamtinggi_e' => $listDemamtinggi_e,
      'listPenyakitbawaan_e' => $listPenyakitbawaan_e,
      'listDiabetes_e' => $listDiabetes_e,
      'listPenyakitberat_e' => $listPenyakitberat_e,
      'listTransfusiDarah' => $listTransfusiDarah,
      'listHiv' => $listHiv,
      'listPasienmemakai' => $listPasienmemakai,
      'listMenyusui' => $listMenyusui,
    );
$this->load->view('Pengkajian/anestesia/pra_anestesi/index', $data);
}

public function simpanPengkajianPraAnestesiPasien()
{
  $kunjungan = $this->input->post('nokun');
  $id = $this->input->post('id');
  $pengguna = $this->input->post("pengguna");
  $alergi = $this->input->post("alergi");
  $sebutkanalergi = $this->input->post("sebutkanalergi");
  $merokok = $this->input->post("merokok");
  $jmlmerokok = $this->input->post("jmlmerokok");
  $alkohol = $this->input->post("alkohol");
  $jmlalkohol = $this->input->post("jmlalkohol");
  $kopiteh = $this->input->post("kopiteh");
  $jmlkopiteh = $this->input->post("jmlkopiteh");
  $olahraga = $this->input->post("olahraga");
  $jmlolahraga = $this->input->post("jmlolahraga");
  $obatresep = $this->input->post("obatresep");
  $obatbebas = $this->input->post("obatbebas");
  $aspirin = $this->input->post("aspirin");
  $dosisaspirin = $this->input->post("dosisaspirin");
  $obatantisakit = $this->input->post("obatantisakit");
  $dosisobatantisakit = $this->input->post("dosisobatantisakit");
  $perdarahan = $this->input->post("perdarahan");
  $jantung = $this->input->post("jantung");
  $pembekuan = $this->input->post("pembekuan");
  $iramajantung = $this->input->post("iramajantung");
  $pembiusan = $this->input->post("pembiusan");
  $hipertensi = $this->input->post("hipertensi");
  $demamtinggi = $this->input->post("demamtinggi");
  $tuberkulosis = $this->input->post("tuberkulosis");
  $diabetes = $this->input->post("diabetes");
  $penyakitberat = $this->input->post("penyakitberat");
  $jelaskanpenyakitkeluarga = $this->input->post("jelaskanpenyakitkeluarga");
  $perdarahan_e = $this->input->post("perdarahan_e");
  $jantung_e = $this->input->post("jantung_e");
  $pembekuan_e = $this->input->post("pembekuan_e");
  $iramajantung_e = $this->input->post("iramajantung_e");
  $maag_e = $this->input->post("maag_e");
  $anemia_e = $this->input->post("anemia_e");
  $asma_e = $this->input->post("asma_e");
  $pingsan_e = $this->input->post("pingsan_e");
  $mengorok_e = $this->input->post("mengorok_e");
  $pembiusan_e = $this->input->post("pembiusan_e");
  $hipertensi_e = $this->input->post("hipertensi_e");
  $hepatitis_e = $this->input->post("hepatitis_e");
  $kejang_e = $this->input->post("kejang_e");
  $demamtinggi_e = $this->input->post("demamtinggi_e");
  $penyakitbawaan_e = $this->input->post("penyakitbawaan_e");
  $diabetes_e = $this->input->post("diabetes_e");
  $penyakitberat_e = $this->input->post("penyakitberat_e");
  $jelaskanpenyakitkeluarga_e = $this->input->post("jelaskanpenyakitkeluarga_e");
  $transfusidarah = $this->input->post("transfusidarah");
  $transfusidarah_thn = $this->input->post("transfusidarah_thn");
  $hiv = $this->input->post("hiv");
  $hiv_thn = $this->input->post("hiv_thn");
  $hiv_hasil = $this->input->post("hiv_hasil");
  $riwayatoperasi = $this->input->post("riwayatoperasi");
  $reaksianestesilokal = $this->input->post("reaksianestesilokal");
  $reaksianestesiregional = $this->input->post("reaksianestesiregional");
  $reaksianestesiumum = $this->input->post("reaksianestesiumum");
  $tanggalperiksaterakhir = $this->input->post("tanggalperiksaterakhir");
  $dimana = $this->input->post("dimana");
  $untukpenyakit = $this->input->post("untukpenyakit");
  $pasienmemakai = $this->input->post("pasienmemakai");
  $pasienmemakailain = $this->input->post("pasienmemakailain");
  $jmlkehamilan = $this->input->post("jmlkehamilan");
  $jmlanak = $this->input->post("jmlanak");
  $mensterakhir = $this->input->post("mensterakhir");
  $menyusui = $this->input->post("menyusui");

  $dataPasien = array(
    'nokun' => $kunjungan,
    'alergi' => $alergi,
    'sebutkan_alergi' => $sebutkanalergi,
    'merokok' => $merokok,
    'jmlmerokok' => $jmlmerokok,
    'alkohol' => $alkohol,
    'jmlalkohol' => $jmlalkohol,
    'kopiteh' => $kopiteh,
    'jmlkopiteh' => $jmlkopiteh,
    'olahraga' => $olahraga,
    'jmlolahraga' => $jmlolahraga,
    'obatresep' => $obatresep,
    'obatbebas' => $obatbebas,
    'aspirin' => $aspirin,
    'dosisaspirin' => $dosisaspirin,
    'obatantisakit' => $obatantisakit,
    'dosisobatantisakit' => $dosisobatantisakit,
    'perdarahan' => $perdarahan,
    'jantung' => $jantung,
    'pembekuan' => $pembekuan,
    'iramajantung' => $iramajantung,
    'pembiusan' => $pembiusan,
    'hipertensi' => $hipertensi,
    'demamtinggi' => $demamtinggi,
    'tuberkulosis' => $tuberkulosis,
    'diabetes' => $diabetes,
    'penyakitberat' => $penyakitberat,
    'jelaskanpenyakitkeluarga' => $jelaskanpenyakitkeluarga,
    'perdarahan_e' => $perdarahan_e,
    'jantung_e' => $jantung_e,
    'pembekuan_e' => $pembekuan_e,
    'iramajantung_e' => $iramajantung_e,
    'maag_e' => $maag_e,
    'anemia_e' => $anemia_e,
    'asma_e' => $asma_e,
    'pingsan_e' => $pingsan_e,
    'mengorok_e' => $mengorok_e,
    'pembiusan_e' => $pembiusan_e,
    'hipertensi_e' => $hipertensi_e,
    'hepatitis_e' => $hepatitis_e,
    'kejang_e' => $kejang_e,
    'demamtinggi_e' => $demamtinggi_e,
    'penyakitbawaan_e' => $penyakitbawaan_e,
    'diabetes_e' => $diabetes_e,
    'penyakitberat_e' => $penyakitberat_e,
    'jelaskanpenyakitkeluarga_e' => $jelaskanpenyakitkeluarga_e,
    'transfusidarah' => $transfusidarah,
    'transfusidarah_thn' => $transfusidarah_thn,
    'hiv' => $hiv,
    'hiv_thn' => $hiv_thn,
    'hiv_hasil' => $hiv_hasil,
    'riwayatoperasi' => $riwayatoperasi,
    'reaksianestesilokal' => $reaksianestesilokal,
    'reaksianestesiregional' => $reaksianestesiregional,
    'reaksianestesiumum' => $reaksianestesiumum,
    'tanggalperiksaterakhir' => $tanggalperiksaterakhir,
    'dimana' => $dimana,
    'untukpenyakit' => $untukpenyakit,
    'pasienmemakai' => isset($pasienmemakai) ? implode($pasienmemakai,',') : "",
    'pasienmemakailain' => $pasienmemakailain,
    'jmlkehamilan' => $jmlkehamilan,
    'jmlanak' => $jmlanak,
    'mensterakhir' => $mensterakhir,
    'menyusui' => $menyusui,
    'jenis' => "2",
    'oleh' => $pengguna,
  );

  // echo "<pre>";print_r($dataPasien);exit();
  // $this->db->insert('keperawatan.tb_riwayat_kesehatan_sebelumnya', $dataPasien);

   if (!empty($id)) {
      $this->db->where('id', $id);
      $this->db->where('nokun', $kunjungan);
      $this->db->update('keperawatan.tb_riwayat_kesehatan_sebelumnya', $dataPasien);
    }else{
      $this->db->insert('keperawatan.tb_riwayat_kesehatan_sebelumnya', $dataPasien);
    }

  echo json_encode($result);
}

public function action_pra_anestesi($param){
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    if ($param == 'tambah' || $param == 'ubah'){
      $post = $this->input->post();
      $get_idpra_anestesi = !empty($post['idpra_anestesi']) ? $post['idpra_anestesi'] : $this->pengkajianAwalModel->getIdEmr();

      $datapra_anestesi = array(
        'idpra_anestesi'  => $get_idpra_anestesi,
        'nokun'           => isset($post['nokun']) ? $post['nokun'] : null,
        'jenis_ruangan'   => isset($post['jenis_rawat_pra_anestesi']) ? $post['jenis_rawat_pra_anestesi'] : null,
        'ruangan_pra_anestesi' => isset($post['ruangan_pra_anestesi']) ? $post['ruangan_pra_anestesi'] : null,
        'tgl_masuk' => isset($post['tanggal_masuk_pra_anestesi']) ? $post['tanggal_masuk_pra_anestesi'] : null,
        'diagnosis_masuk' => isset($post['diagnosis_masuk']) ? $post['diagnosis_masuk'] : null,
        'tinggi_badan' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : null,
        'berat_badan' => isset($post['berat_badan']) ? $post['berat_badan'] : null,
        'ks_hilangnya_gigi' => isset($post['hilangnya_gigi']) ? $post['hilangnya_gigi'] : null,
        'ks_masalah_leher_pendek' => isset($post['masalah_leher_pendek']) ? $post['masalah_leher_pendek'] : null,
        'ks_denyut_jantung_tidak_normal' => isset($post['denyut_jantung_tidak_normal']) ? $post['denyut_jantung_tidak_normal'] : null,
        'ks_batuk' => isset($post['batuk']) ? $post['batuk'] : null,
        'ks_sesak_napas' => isset($post['sesak_napas']) ? $post['sesak_napas'] : null,
        'ks_baru_saja_menderita_infeksi' => isset($post['baru_saja_menderita_infeksi']) ? $post['baru_saja_menderita_infeksi'] : null,
        'ks_saluran_napas_atas' => isset($post['saluran_napas_atas']) ? $post['saluran_napas_atas'] : null,
        'ks_sakit_dada' => isset($post['sakit_dada']) ? $post['sakit_dada'] : null,
        'ks_muntah' => isset($post['muntah']) ? $post['muntah'] : null,
        'ks_pingsan' => isset($post['pingsan']) ? $post['pingsan'] : null,
        'ks_stroke' => isset($post['stroke']) ? $post['stroke'] : null,
        'ks_kejang' => isset($post['kejang']) ? $post['kejang'] : null,
        'ks_sedang_hamil' => isset($post['sedang_hamil']) ? $post['sedang_hamil'] : null,
        'ks_kelainan_tulang_belakang' => isset($post['kelainan_tulang_belakang']) ? $post['kelainan_tulang_belakang'] : null,
        'ks_obesitas' => isset($post['obesitas']) ? $post['obesitas'] : null,
        'ks_keterangan' => isset($post['ks_keterangan']) ? $post['ks_keterangan'] : null,
        'pf_tekanan_darah_1' => isset($post['pf_tekanan_darah_1']) ? $post['pf_tekanan_darah_1'] : null,
        'pf_tekanan_darah_2' => isset($post['pf_tekanan_darah_2']) ? $post['pf_tekanan_darah_2'] : null,
        'pf_pernapasan' => isset($post['pf_pernapasan']) ? $post['pf_pernapasan'] : null,
        'pf_nadi' => isset($post['pf_nadi']) ? $post['pf_nadi'] : null,
        'pf_suhu' => isset($post['pf_suhu']) ? $post['pf_suhu'] : null,
        'pf_skor_mallampati' => isset($post['pf_skor_mallampati']) ? $post['pf_skor_mallampati'] : null,
        'pf_gigi_palsu' => isset($post['pf_gigi_palsu']) ? $post['pf_gigi_palsu'] : null,
        'pf_jantung' => isset($post['pf_jantung']) ? $post['pf_jantung'] : null,
        'pf_paru_paru' => isset($post['pf_paru_paru']) ? $post['pf_paru_paru'] : null,
        'pf_abdomen' => isset($post['pf_abdomen']) ? $post['pf_abdomen'] : null,
        'pf_tulang_belakang' => isset($post['pf_tulang_belakang']) ? $post['pf_tulang_belakang'] : null,
        'pf_ekstremitas' => isset($post['pf_ekstremitas']) ? $post['pf_ekstremitas'] : null,
        'pf_neurologi' => isset($post['pf_neurologi']) ? $post['pf_neurologi'] : null,
        'pf_keterangan' => isset($post['pf_keterangan']) ? $post['pf_keterangan'] : null,
        'pp_hb_ht' => isset($post['pp_hb_ht']) ? $post['pp_hb_ht'] : null,
        'pp_leukosit' => isset($post['pp_leukosit']) ? $post['pp_leukosit'] : null,
        'pp_pt' => isset($post['pp_pt']) ? $post['pp_pt'] : null,
        'pp_trombosit' => isset($post['pp_trombosit']) ? $post['pp_trombosit'] : null,
        'pp_glukosa_darah' => isset($post['pp_glukosa_darah']) ? $post['pp_glukosa_darah'] : null,
        'pp_tes_kehamilan' => isset($post['pp_tes_kehamilan']) ? $post['pp_tes_kehamilan'] : null,
        'pp_ekg' => isset($post['pp_ekg']) ? $post['pp_ekg'] : null,
        'pp_kalium' => isset($post['pp_kalium']) ? $post['pp_kalium'] : null,
        'pp_na_cl' => isset($post['pp_na_cl']) ? $post['pp_na_cl'] : null,
        'pp_ureum' => isset($post['pp_ureum']) ? $post['pp_ureum'] : null,
        'pp_kreatinin' => isset($post['pp_kreatinin']) ? $post['pp_kreatinin'] : null,
        'pp_ro_thorax' => isset($post['pp_ro_thorax']) ? $post['pp_ro_thorax'] : null,
        'diagnosis_icd_x' => isset($post['diagnosis_icd_x']) ? $post['diagnosis_icd_x'] : null,
        'klasifikasi_asa' => isset($post['klasifikasi_asa']) ? json_encode($post['klasifikasi_asa']) : null,
        'penyulit_anestesi_lain' => isset($post['penyulit_anestesi_lain']) ? $post['penyulit_anestesi_lain'] : null,
        'catatan_tindak_lanjut' => isset($post['catatan_tindak_lanjut']) ? $post['catatan_tindak_lanjut'] : null,
        'teknik_anestesia' => isset($post['teknik_anestesia']) ? json_encode($post['teknik_anestesia']) : null,
        'ta_sedasi' => isset($post['ta_sedasi']) ? $post['ta_sedasi'] : null,
        'ta_ga' => isset($post['ta_ga']) ? $post['ta_ga'] : null,
        'ta_regional' => isset($post['ta_regional']) ? $post['ta_regional'] : null,
        'ta_lain_lain' => isset($post['ta_lain_lain']) ? $post['ta_lain_lain'] : null,
        'teknik_khusus' => isset($post['teknik_khusus']) ? json_encode($post['teknik_khusus']) : null,
        'tk_lain_lain' => isset($post['tk_lain_lain']) ? $post['tk_lain_lain'] : null,
        'monitoring' => isset($post['monitoring']) ? json_encode($post['monitoring']) : null,
        'm_ekg_lead' => isset($post['m_ekg_lead']) ? $post['m_ekg_lead'] : null,
        'm_cvp' => isset($post['m_cvp']) ? $post['m_cvp'] : null,
        'm_spo2' => isset($post['m_spo2']) ? $post['m_spo2'] : null,
        'm_arteri_line' => isset($post['m_arteri_line']) ? $post['m_arteri_line'] : null,
        'm_lain_lain' => isset($post['m_lain_lain']) ? $post['m_lain_lain'] : null,
        'alat_khusus' => isset($post['alat_khusus']) ? json_encode($post['alat_khusus']) : null,
        'ak_lain_lain' => isset($post['ak_lain_lain']) ? $post['ak_lain_lain'] : null,
        'perawatan_pasca_anestesi' => isset($post['perawatan_pasca_anestesi']) ? json_encode($post['perawatan_pasca_anestesi']) : null,
        'ppa_rawat_khusus' => isset($post['ppa_rawat_khusus']) ? $post['ppa_rawat_khusus'] : null,
        'ppa_keterangan_hcu' => isset($post['ppa_keterangan_hcu']) ? $post['ppa_keterangan_hcu'] : null,
        'ppa_waktu_puasa_mulai' => isset($post['ppa_waktu_puasa_mulai']) ? $post['ppa_waktu_puasa_mulai'] : null,
        'ppa_tanggal_puasa_mulai' => isset($post['ppa_tanggal_puasa_mulai']) ? $post['ppa_tanggal_puasa_mulai'] : null,
        'ppa_waktu_pre_medikasi' => isset($post['ppa_waktu_pre_medikasi']) ? $post['ppa_waktu_pre_medikasi'] : null,
        'ppa_tanggal_pre_medikasi' => isset($post['ppa_tanggal_pre_medikasi']) ? $post['ppa_tanggal_pre_medikasi'] : null,
        'ppa_waktu_tkb' => isset($post['ppa_waktu_tkb']) ? $post['ppa_waktu_tkb'] : null,
        'ppa_tanggal_tkb' => isset($post['ppa_tanggal_tkb']) ? $post['ppa_tanggal_tkb'] : null,
        'ppa_waktu_rencana_operasi' => isset($post['ppa_waktu_rencana_operasi']) ? $post['ppa_waktu_rencana_operasi'] : null,
        'ppa_tanggal_rencana_operasi' => isset($post['ppa_tanggal_rencana_operasi']) ? $post['ppa_tanggal_rencana_operasi'] : null,
        'catatan_persiapan_pra_anestesi' => isset($post['catatan_persiapan_pra_anestesi']) ? $post['catatan_persiapan_pra_anestesi'] : null,
        'created_at' => date('Y-m-d H:i:s'),
        'status' => '1',
        'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
      );

        //print_r($datapra_anestesi);exit();

if (!empty($post['idpra_anestesi'])) {
  $this->db->replace('medis.tb_pra_anestesi', $datapra_anestesi);
  $result = array('status' => 'success', 'pesan' => 'ubah');
}else {
  $this->db->insert('medis.tb_pra_anestesi', $datapra_anestesi);
  $result = array('status' => 'success');
}
echo json_encode($result);
}
}
}
}


/* End of file Pra_anestesi.php */
/* Location: ./application/controllers/anestesi/evaluasikemampuanfungsionalmobilisasi/FormEKFM.php */
