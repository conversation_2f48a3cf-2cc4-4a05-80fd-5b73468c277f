<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormulirTriase extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'formulirTriaseModel'));
  }

  public function index()
  {
    $norm = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $jenisKunjunganTriase = $this->masterModel->referensi(485);
    $PENGGUNAAN_O2 = $this->masterModel->referensi(129);
    $atsTriase1 = $this->masterModel->referensi(479);
    $atsTriase2 = $this->masterModel->referensi(480);
    $atsTriase3 = $this->masterModel->referensi(481);
    $atsTriase4 = $this->masterModel->referensi(482);
    $atsTriase5 = $this->masterModel->referensi(483);
    $triaseIgd = $this->masterModel->referensi(486);

    $data = array(
      'getNomr' => $getNomr,
      'nokun' => $nokun,
      'norm' => $norm,
      'nopen' => $nopen,
      'jenisKunjunganTriase' => $jenisKunjunganTriase,
      'PENGGUNAAN_O2' => $PENGGUNAAN_O2,
      'atsTriase1' => $atsTriase1,
      'atsTriase2' => $atsTriase2,
      'atsTriase3' => $atsTriase3,
      'atsTriase4' => $atsTriase4,
      'atsTriase5' => $atsTriase5,
      'triaseIgd' => $triaseIgd,
      'apakahInginMasukEws' => $this->masterModel->referensi(1162),
      'kesadaran' => $this->masterModel->referensi(5),
    );

    $this->load->view('Pengkajian/igd/formulirTriase/index', $data);
  }

}