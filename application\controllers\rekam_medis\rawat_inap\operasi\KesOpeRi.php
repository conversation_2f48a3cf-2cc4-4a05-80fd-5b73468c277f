<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KesOpeRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/operasi/KesOpeRiModel'
    ));
  }

  public function index(){
  	$norm = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    $cekDataAkhir = $this->KesOpeRiModel->cekDataAkhir($nokun);
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'cekDataAkhir' => $cekDataAkhir,
      'listDrPelaksana' => $this->masterModel->listDrPelaksana(),
      'historyKesOpeRi' => $this->KesOpeRiModel->historyKesOpeRi($norm),
      'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
      'penandaanSisi' => $this->masterModel->referensi(1517),
      'pemPenFoto' => $this->masterModel->referensi(1728),
      'pemPenCtScan' => $this->masterModel->referensi(1729),
      'pemPenMri' => $this->masterModel->referensi(1730),
      'pemPenLainnya' => $this->masterModel->referensi(1731),
      'persediaanDarah' => $this->masterModel->referensi(1518),
      'sitTransfusi' => $this->masterModel->referensi(1519),
      'renPemAlat' => $this->masterModel->referensi(1520),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/kesOpeRi/index', $data);
  }

  public function viewEditRi($idNorm, $idNopen, $idNokun, $idLoad){
    $norm = $idNorm;
    $nopen = $idNopen;
    $nokun = $idNokun;

    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'listDrPelaksana' => $this->masterModel->listDrPelaksana(),
      'historyKesOpeRi' => $this->KesOpeRiModel->historyKesOpeRi($norm),
      'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
      'getKesOpeRi' => $this->KesOpeRiModel->getKesOpeRi($idLoad),
      'penandaanSisi' => $this->masterModel->referensi(1517),
      'pemPenFoto' => $this->masterModel->referensi(1728),
      'pemPenCtScan' => $this->masterModel->referensi(1729),
      'pemPenMri' => $this->masterModel->referensi(1730),
      'pemPenLainnya' => $this->masterModel->referensi(1731),
      'persediaanDarah' => $this->masterModel->referensi(1518),
      'sitTransfusi' => $this->masterModel->referensi(1519),
      'renPemAlat' => $this->masterModel->referensi(1520),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/kesOpeRi/index', $data);
  }

  public function simpanKesOpeRi()
  {
    $post = $this->input->post();
    $date = $this->input->post('tglPukulKesOpeRi');
    $tglPukulKesOpeRi = date('Y-m-d H:i:s', strtotime($date));

    $data = array(
      'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
      'diagnosis' => isset($post['diagnosisKesOpeRi']) ? $post['diagnosisKesOpeRi'] : null,
      'tindakan_operasi' => isset($post['tindakanOperasiKesOpeRi']) ? $post['tindakanOperasiKesOpeRi'] : null,
      'dokter_operator' => isset($post['dokterOpeKesOpeRi']) ? $post['dokterOpeKesOpeRi'] : null,
      'waktu_dikirim' => $tglPukulKesOpeRi,
      'asal_ruangan' => isset($post['asalRuanganKesOpeRi']) ? $post['asalRuanganKesOpeRi'] : null,
      'identitas_pasien_sesuai' => isset($post['identitasPasienCek']) ? 1 : 0,
      'rencana_tindakan' => isset($post['rencanaTindakanKesOpeRi']) ? $post['rencanaTindakanKesOpeRi'] : null,
      'rencana_tindakan_sesuai' => isset($post['rencanaTindakanCek']) ? 1 : 0,
      'penandaan_lokasi' => isset($post['penandaanSisiKesOpeRi']) ? $post['penandaanSisiKesOpeRi'] : null,
      'penandaan_loaksi_sesuai' => isset($post['penandaanSisiCekKesOpeRi']) ? $post['penandaanSisiCekKesOpeRi'] : null,
      'tindakan_bedah' => isset($post['iCTinBedahCek']) ? 1 : 0,
      'tindakan_anestesi' => isset($post['iCTinAnesCek']) ? 1 : 0,
      'pemeriksaan_foto' => isset($post['pemPenFotoKesOpeRi']) ? $post['pemPenFotoKesOpeRi'] : null,
      'pemeriksaan_foto_sesuai' => isset($post['pemeriksaanPenunjangFotoCek']) ? $post['pemeriksaanPenunjangFotoCek'] : null,
      'pemeriksaan_ct_scan' => isset($post['pemPenCtScanKesOpeRi']) ? $post['pemPenCtScanKesOpeRi'] : null,
      'pemeriksaan_ct_scan_sesuai' => isset($post['pemeriksaanPenunjangCtScanCek']) ? $post['pemeriksaanPenunjangCtScanCek'] : null,
      'pemeriksaan_mri' => isset($post['pemPenMriKesOpeRi']) ? $post['pemPenMriKesOpeRi'] : null,
      'pemeriksaan_mri_sesuai' => isset($post['pemeriksaanPenunjangMriCek']) ? $post['pemeriksaanPenunjangMriCek'] : null,
      'pemeriksaan_lainnya' => isset($post['pemPenLainnyaKesOpeRi']) ? $post['pemPenLainnyaKesOpeRi'] : null,
      'pemeriksaan_lainnya_sesuai' => isset($post['pemeriksaanPenunjangLainnyaCek']) ? $post['pemeriksaanPenunjangLainnyaCek'] : null,
      'persediaan_darah_tersedia' => isset($post['PersediaanDarahCek']) ? $post['PersediaanDarahCek'] : null,
      'persediaan_darah_gol' => isset($post['golKesOpeRi']) ? $post['golKesOpeRi'] : null,
      'persediaan_darah_prc' => isset($post['prcKesOpeRi']) ? $post['prcKesOpeRi'] : null,
      'persediaan_darah_ffp' => isset($post['ffpUnitKesOpeRi']) ? $post['ffpUnitKesOpeRi'] : null,
      'persediaan_darah_lain' => isset($post['lainKesOpeRi']) ? $post['lainKesOpeRi'] : null,
      'persediaan_darah_sit' => isset($post['sitTransfusiCek']) ? $post['sitTransfusiCek'] : null,
      'rencana_pemasangan_alat' => isset($post['renPemAlatCek']) ? $post['renPemAlatCek'] : null,
      'rencana_pemasangan_alat_desk' => isset($post['renPemAlat']) ? $post['renPemAlat'] : null,
      'oleh' => isset($post['oleh']) ? $post['oleh'] : null,
      'status' => 1,
    );

    // echo "<pre>data keselamatan operasi ";print_r($data);echo "</pre>";exit();
    if (!empty($post['id_kesope'])) {
      $this->db->where('tb_keselamatan_tindakan_operasi.id', $post['id_kesope']);
      $this->db->update('keperawatan.tb_keselamatan_tindakan_operasi', $data);
      $getIdOpe = $post['id_kesope'];
    }else{
      $getIdOpe = $this->KesOpeRiModel->simpanKesOpe($data);
    }

    $dataTandaVital = array(
      'data_source' => 27,
      'ref' => $getIdOpe,
      'nomr' => isset($post['norm']) ? $post['norm'] : "",
      'nokun' => $post['nokun'],
      'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
      'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
      'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
      'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
      'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
      'oleh' => isset($post['oleh']) ? $post['oleh'] : null,
      'status' => 1,
    );

    $dataTbBb = array(
      'data_source' => 27,
      'ref' => $getIdOpe,
      'nomr' => isset($post['norm']) ? $post['norm'] : "",
      'nokun' => $post['nokun'],
      'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0 ,
      'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
      'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
      'oleh' => isset($post['oleh']) ? $post['oleh'] : null,
      'status' => 1,
    );

    if (!empty($post['id_kesope'])) {
      $this->db->where('tb_tanda_vital.ref', $post['id_kesope']);
      $this->db->where('tb_tanda_vital.data_source', 27);
      $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);

      $this->db->where('tb_tb_bb.ref', $post['id_kesope']);
      $this->db->where('tb_tb_bb.data_source', 27);
      $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
    }else{
      $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
      $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
    }
  }

  public function viewInOutKesOpeRi()
  {
    $idkto = $this->input->post('idkto');

    $data = array(
      'idkto' => $idkto,
      'sudahDiberi' => $this->masterModel->referensi(1521),
      'punyaAlergi' => $this->masterModel->referensi(1522),
      'gangguanNafas' => $this->masterModel->referensi(1523),
      'resikoPerdarahan' => $this->masterModel->referensi(1524),
      'apakahAntibiotik' => $this->masterModel->referensi(1541),
      'adakahMasalah' => $this->masterModel->referensi(1542),
      'pemakaianImplan' => $this->masterModel->referensi(1543),
      'tayangRadiologi' => $this->masterModel->referensi(1544),
      'masalahPeralatan' => $this->masterModel->referensi(1545),
      'diperhatikanSelama' => $this->masterModel->referensi(1546),
      'listDrAnestesi' => $this->masterModel->listDrAnestesi(),
      'listPerawat' => $this->masterModel->listPerawatPenataAnestesi(),
      'listPerawatSirkuler' => $this->masterModel->listPegawai(),
      'listDrPelaksana' => $this->masterModel->listDrPelaksana(),
      'getInOUtKesOpeRi' => $this->KesOpeRiModel->getInOUtKesOpeRi($idkto),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/kesOpeRi/inOutKesOpeRi', $data);
  }

  public function simpanSignIn()
  {
    $post = $this->input->post();
    $date = $this->input->post('tglSignInKesOpeRi');
    $tglSignInKesOpeRi = date('Y-m-d H:i:s', strtotime($date));

    $data = array(
      'idkto' => isset($post['idkto']) ? $post['idkto'] : null,
      'tgl_pukul_signin' => $tglSignInKesOpeRi,
      'identitas_pasien_signin' => isset($post['identitasPasienSignIn']) ? 1 : 0,
      'identitas_pasien_desk_signin' => isset($post['identitasDeskSignIn']) ? $post['identitasDeskSignIn'] : null,
      'tindakan_operasi_signin' => isset($post['tindakanOperasiSignIn']) ? 1 : 0,
      'tindakan_operasi_desk_signin' => isset($post['tindakanOperasiDeskSignIn']) ? $post['tindakanOperasiDeskSignIn'] : null,
      'sisi_operasi_signin' => isset($post['sisiOperasiSignIn']) ? 1 : 0,
      'sisi_operasi_desk_signin' => isset($post['sisiOperasiDeskSignIn']) ? $post['sisiOperasiDeskSignIn'] : null,
      'surat_izin_signin' => isset($post['suratIzinOpeSignIn']) ? 1 : 0,
      'surat_izin_desk_signin' => isset($post['suratIzinOpeDeskSignIn']) ? $post['suratIzinOpeDeskSignIn'] : null,
      'sudah_beri_signin' => isset($post['sudahDiberiCekSignInKesOpeRi']) ? $post['sudahDiberiCekSignInKesOpeRi'] : null,
      'mesin_obat_signin' => isset($post['mesinObatSignIn']) ? 1 : 0,
      'sudah_pasang_signin' => isset($post['sudahPasangSignIn']) ? 1 : 0,
      'punya_alergi_signin' => isset($post['punyaAlergiCekSignInKesOpeRi']) ? $post['punyaAlergiCekSignInKesOpeRi'] : null,
      'gangguan_nafas_signin' => isset($post['gangguanNafasCekSignInKesOpeRi']) ? $post['gangguanNafasCekSignInKesOpeRi'] : null,
      'resiko_perdarahan_signin' => isset($post['resikoPerdarahanCekSignInKesOpeRi']) ? $post['resikoPerdarahanCekSignInKesOpeRi'] : null,
      'dokter_anestesi' => isset($post['dokterAnestesiSignInKesOpeRi']) ? $post['dokterAnestesiSignInKesOpeRi'] : null,
      'penata_anestesi' => isset($post['penataAnestesiSignInKesOpeRi']) ? $post['penataAnestesiSignInKesOpeRi'] : null,
      'oleh' => isset($post['oleh']) ? $post['oleh'] : null,
      'status' => 1,
    );

    // echo "<pre>data sign in ";print_r($data);echo "</pre>";exit();
     $this->db->replace('keperawatan.tb_kesope_signin', $data);
  }

  public function simpanTimeOut()
  {
    $post = $this->input->post();
    $date = $this->input->post('tglTimeOutKesOpeRi');
    $tglTimeOutKesOpeRi = date('Y-m-d H:i:s', strtotime($date));

    $data = array(
      'idkto' => isset($post['idkto']) ? $post['idkto'] : null,
      'tgl_pukul_timeout' => $tglTimeOutKesOpeRi,
      'sebutkan_nama_timeout' => isset($post['sebutNamaTimeOut']) ? 1 : 0,
      'identitas_pasien_timeout' => isset($post['identitasPasienTimeOut']) ? 1 : 0,
      'identitas_pasien_desk_timeout' => isset($post['identitasDeskTimeOut']) ? $post['identitasDeskTimeOut'] : null,
      'tindakan_operasi_timeout' => isset($post['tindakanOperasiTimeOut']) ? 1 : 0,
      'tindakan_operasi_desk_timeout' => isset($post['tindakanOperasiDeskTimeOut']) ? $post['tindakanOperasiDeskTimeOut'] : null,
      'sisi_operasi_timeout' => isset($post['sisiOperasiTimeOut']) ? 1 : 0,
      'sisi_operasi_desk_timeout' => isset($post['sisiOperasiDeskTimeOut']) ? $post['sisiOperasiDeskTimeOut'] : null,
      'surat_izin_timeout' => isset($post['suratIzinOpeTimeOut']) ? 1 : 0,
      'surat_izin_desk_timeout' => isset($post['suratIzinOpeDeskTimeOut']) ? $post['suratIzinOpeDeskTimeOut'] : null,
      'berikan_antibiotik_timeout' => isset($post['apakahAntibiotikCekTimeOutKesOpeRi']) ? $post['apakahAntibiotikCekTimeOutKesOpeRi'] : null,
      'jam_pemberian' => isset($post['apakahAntibiotikDeskTimeOutKesOpeRi']) ? $post['apakahAntibiotikDeskTimeOutKesOpeRi'] : null,
      'antisipasi_dokter_bedah' => isset($post['antisipasiKondisiLangkahTimeOutKesOpeRi']) ? $post['antisipasiKondisiLangkahTimeOutKesOpeRi'] : null,
      'antisipasi_dokter_anestesi' => isset($post['antisipasiKondisiAdakahTimeOutKesOpeRi']) ? $post['antisipasiKondisiAdakahTimeOutKesOpeRi'] : null,
      'persiapan_instrumen' => isset($post['persiapanInstrumenTimeOut']) ? 1 : 0,
      'persiapan_masalah' => isset($post['adakahMasalahCekTimeOutKesOpeRi']) ? $post['adakahMasalahCekTimeOutKesOpeRi'] : null,
      'persiapan_sebutkan' => isset($post['adakahMasalahDeskTimeOutKesOpeRi']) ? json_encode($post['adakahMasalahDeskTimeOutKesOpeRi']) : null,
      'persiapan_implan' => isset($post['pemakaianImplanCekTimeOutKesOpeRi']) ? $post['pemakaianImplanCekTimeOutKesOpeRi'] : null,
      'persiapan_foto_radiologi' => isset($post['tayangRadiologiCekTimeOutKesOpeRi']) ? $post['tayangRadiologiCekTimeOutKesOpeRi'] : null,
      'perawat_sirkuler' => isset($post['perawatSirkulerTimeOutKesOpeRi']) ? $post['perawatSirkulerTimeOutKesOpeRi'] : null,
      'oleh' => isset($post['oleh']) ? $post['oleh'] : null,
      'status' => 1,
    );

    // echo "<pre>data time out ";print_r($data);echo "</pre>";exit();
     $this->db->replace('keperawatan.tb_kesope_timeout', $data);
  }

  public function simpanSignOut()
  {
    $post = $this->input->post();
    $date = $this->input->post('tglSignOutKesOpeRi');
    $tglSignOutKesOpeRi = date('Y-m-d H:i:s', strtotime($date));

    $data = array(
      'idkto' => isset($post['idkto']) ? $post['idkto'] : null,
      'tgl_pukul_signout' => $tglSignOutKesOpeRi,
      'konfirmasi_nama_tindakan' => isset($post['namaTindakanSignOut']) ? 1 : 0,
      'konfirmasi_kelengkapan' => isset($post['lengkapJarungSignOut']) ? 1 : 0,
      'konfirmasi_spesimen' => isset($post['labelNamaSignOut']) ? 1 : 0,
      'konfirmasi_spesimen_desk' => isset($post['labelNamaDeskSignOutKesOpeRi']) ? $post['labelNamaDeskSignOutKesOpeRi'] : null,
      'konfirmasi_masalah' => isset($post['masalahPeralatanCekSignOutKesOpeRi']) ? $post['masalahPeralatanCekSignOutKesOpeRi'] : null,
      'konfirmasi_masalah_desk' => isset($post['masalahPeralatanSignOutKesOpeRi']) ? $post['masalahPeralatanSignOutKesOpeRi'] : null,
      'adakah_masalah' => isset($post['diperhatikanSelamaCekSignOutKesOpeRi']) ? $post['diperhatikanSelamaCekSignOutKesOpeRi'] : null,
      'adakah_masalah_desk' => isset($post['diperhatikanSelamaSignOutKesOpeRi']) ? $post['diperhatikanSelamaSignOutKesOpeRi'] : null,
      'dokter_anestesi' => isset($post['dokterAnestesiSignOutKesOpeRi']) ? $post['dokterAnestesiSignOutKesOpeRi'] : "",
      'dokter_operator' => isset($post['dokterOperatorSignOutKesOpeRi']) ? $post['dokterOperatorSignOutKesOpeRi'] : "",
      'perawat_sirkuler' => isset($post['perawatSirkulerSignOutKesOpeRi']) ? $post['perawatSirkulerSignOutKesOpeRi'] : "",
      'oleh' => isset($post['oleh']) ? $post['oleh'] : null,
      'status' => 1,
    );

    // echo "<pre>data time out ";print_r($data);echo "</pre>";exit();
     $this->db->replace('keperawatan.tb_kesope_signout', $data);
  }

}