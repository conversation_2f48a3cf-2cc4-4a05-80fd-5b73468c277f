<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PT_PengobatanKemoterapiModel extends CI_Model {

  public function simpanInformedConcent($data)
  {
    $this->db->insert('db_informed_consent.tb_informed_consent', $data);
    return $this->db->insert_id();
  }

  public function simpanPengobatanKemo($data)
  {
    $this->db->insert('db_informed_consent.tb_pengobatan_kemo_dewasa', $data);
  }

  public function simpanPersetujuanTidakanKedokteran($data)
  {
    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran', $data);
  }

  public function listHistoryInformedConsentKemo($nomr)
  {
    $query = $this->db->query("SELECT pp.NORM, tic.id, tic.nokun,
                              master.getNamaLengkapPegawai(ap.NIP)OLEH,
                              master.getNamaLengkapPegawai(md.NIP)DOKTERPELAKSANA,
                              tic.created_at tanggal
                              FROM db_informed_consent.tb_informed_consent tic
                              LEFT JOIN db_informed_consent.tb_pengobatan_kemo_dewasa tpk ON tpk.id_informed_consent = tic.id
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE pp.NORM = '$nomr' AND tic.`status` = 1
                              ");
    return $query;
  }

  public function getPTKemoterapi($id)
  {
    $query = $this->db->query("SELECT tic.`*`, tpk.`*`,tptk.`*`,tic.id idtic,tpk.id idtpk,master.getNamaLengkap(pp.NORM)NAMAPASIEN
                              ,master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR)UMUR
                              ,master.getNamaLengkapPegawai(md.NIP)DOKTERPELAKSANA,tic.created_at tanggal
                              ,mp.JENIS_KELAMIN
                              ,master.getNamaLengkapPegawai(ap2.NIP)SAKSIRS
                              FROM db_informed_consent.tb_informed_consent tic
                              LEFT JOIN db_informed_consent.tb_pengobatan_kemo_dewasa tpk ON tpk.id_informed_consent = tic.id
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              LEFT JOIN db_informed_consent.tb_persetujuan_tindakan_kedokteran tptk ON tptk.id_informed_consent = tic.id
                              LEFT JOIN master.pasien mp ON mp.NORM = pp.NORM
                              LEFT JOIN aplikasi.pengguna ap2 ON ap2.ID = tptk.saksi_rumah_sakit
                              WHERE tic.id = $id AND tic.`status` = 1
                              ");
    return $query->row_array();
  }

  public function updateInformedConcent($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('db_informed_consent.tb_informed_consent', $data);
  }

  public function updatePengobatanKemo($data,$id)
  {
    $this->db->where('id_informed_consent', $id);
    $this->db->update('db_informed_consent.tb_pengobatan_kemo_dewasa', $data);
  }

}

/* End of file PT_PengobatanKemoterapiModel.php */
/* Location: ./application/models/informedConsent/PT_PengobatanKemoterapiModel.php */
