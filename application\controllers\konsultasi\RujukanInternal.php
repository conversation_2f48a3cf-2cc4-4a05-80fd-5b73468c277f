<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RujukanInternal extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'konsultasi/RujukanInternalModel',
      )
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(4);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'ruang' => $this->masterModel->ruanganRskd(),
      'pasien' => $pasien,
      'drPerujuk' => $this->masterModel->listDrUmum(null, null, null),
      'jumlah' => $this->RujukanInternalModel->hitungSemua($pasien['NOKUN']),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/rujukanInternal/index', $data);
  }

  public function simpan()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->RujukanInternalModel->rules;
      $this->form_validation->set_rules($rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = isset($post['id']) ? $post['id'] : null;
        $tanggal = isset($post['waktu']) ? date('Y-m-d H:i:s', strtotime($post['waktu'])) : date('Y-m-d H:i:s');

        // Mulai data
        $data = array(
          'KUNJUNGAN' => isset($post['nokun']) ? $post['nokun'] : null,
          'CITO' => isset($post['cito']) ? $post['cito'] : 0,
          'TANGGAL' => $tanggal,
          'TUJUAN' => isset($post['tujuan']) ? $post['tujuan'] : null,
          'DOKTER_ASAL' => isset($post['perujuk']) ? $post['perujuk'] : null,
          'ALASAN' => isset($post['alasan']) ? $post['alasan'] : null,
          'PERMINTAAN_TINDAKAN' => isset($post['konsul']) ? $post['konsul'] : null,
          'OLEH' => $this->session->userdata('id'),
          'STATUS' => 1,
        );
        // Akhir data

        // Mulai simpan
        if (!empty($id)) {
          // echo '<pre>';print_r($data);exit();
          $this->RujukanInternalModel->ubah($data, $id);
        } else {
          $id = $this->pengkajianAwalModel->generateNoKonsul($post['ruang'], $tanggal);
          // echo '<pre>';print_r($id);exit();
          $data['NOMOR'] = $id;
          // echo '<pre>';print_r($data);exit();
          $this->RujukanInternalModel->simpan($data);
        }
        // Akhir simpan

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/rujukanInternal/history', $data);
  }

  public function tabel()
  {
    $nokun = $this->input->post('nokun');
    $history = $this->RujukanInternalModel->ambilTabel($nokun);
    $data = array();
    $no = $_POST['start'];
    $cito = null;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history as $h) {
      // Mulai periksa CITO
      if ($h->CITO == 0) {
        $cito = 'Tidak';
      } elseif ($h->CITO == 1) {
        $cito = "<p class='text-danger'>Ya</p>";
      }
      // Akhir periksa CITO

      // Mulai periksa status
      if ($h->STATUS == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->STATUS == 1) {
        $disabled = null;
        $status = '<p class="text-warning">Terkirim</p>';
      } elseif ($h->STATUS == 2) {
        $disabled = 'disabled';
        $status = '<p class="text-success">Disetujui</p>';
      }
      // Akhir periksa status

      // Mulai data
      $row = array();
      $row[] = ++$no . '.';
      $row[] = date('d-m-Y, H.i.s', strtotime($h->TANGGAL));
      $row[] = $h->DOKTER_PERUJUK;
      $row[] = $h->TUJUAN;
      $row[] = $cito;
      $row[] = $h->ALASAN;
      $row[] = $h->PERMINTAAN_TINDAKAN;
      $row[] = $h->PENGISI;
      $row[] = $status;
      $row[] = "<button type='button' href='#modal-batal-ruj-in' class='btn btn-sm btn-danger waves-effect tbl-batal-ruj-in' data-toggle='modal' data-id='" . $h->NOMOR . "' $disabled>
                  <i class='fa fa-window-close'></i> Batal
                </button>";
      $data[] = $row;
      // Akhir data
    }

    $output = array(
      'draw' => $_POST['draw'],
      'recordsTotal' => $this->RujukanInternalModel->hitungSemua($nokun),
      'recordsFiltered' => $this->RujukanInternalModel->hitungTersaring($nokun),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array('status' => 0);
    $this->RujukanInternalModel->ubah($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detail()
  {
    $post = $this->input->post(null, true);
    $detail = $this->RujukanInternalModel->detail($post['id']);
    // echo '<pre>';print_r($detail);exit();
    echo json_encode(
      array(
        'status' => 'succes',
        'data' => $detail,
      )
    );
  }
}

/* End of file RujukanInternal.php */
/* Location: ./application/controllers/konsultasi/RujukanInternal.php */