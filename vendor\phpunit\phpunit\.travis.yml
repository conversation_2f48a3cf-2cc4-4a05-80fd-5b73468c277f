language: php

sudo: false

addons:
  apt:
    packages:
      - libxml2-utils

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.2
  - master

matrix:
  allow_failures:
    - php: 7.2
    - php: master
  fast_finish: true

env:
  matrix:
    - DEPENDENCIES="high"
    - DEPENDENCIES="low"
  global:
    - DEFAULT_COMPOSER_FLAGS="--no-interaction --no-ansi --no-progress --no-suggest"

before_install:
  - composer self-update
  - composer clear-cache

install:
  - if [[ "$DEPENDENCIES" = 'high' ]]; then travis_retry composer update $DEFAULT_COMPOSER_FLAGS; fi
  - if [[ "$DEPENDENCIES" = 'low' ]]; then travis_retry composer update $DEFAULT_COMPOSER_FLAGS --prefer-lowest; fi

before_script:
  - echo 'zend.assertions=1' >> ~/.phpenv/versions/$(phpenv version-name)/etc/conf.d/travis.ini
  - echo 'assert.exception=On' >> ~/.phpenv/versions/$(phpenv version-name)/etc/conf.d/travis.ini

script:
  - ./phpunit --coverage-clover=coverage.xml
  - ./phpunit --configuration ./build/travis-ci-fail.xml > /dev/null; if [ $? -eq 0 ]; then echo "SHOULD FAIL"; false; else echo "fail checked"; fi;
  - xmllint --noout --schema phpunit.xsd phpunit.xml
  - xmllint --noout --schema phpunit.xsd tests/_files/configuration.xml
  - xmllint --noout --schema phpunit.xsd tests/_files/configuration_empty.xml
  - xmllint --noout --schema phpunit.xsd tests/_files/configuration_xinclude.xml -xinclude

after_success:
  - bash <(curl -s https://codecov.io/bash)

notifications:
  email: false

