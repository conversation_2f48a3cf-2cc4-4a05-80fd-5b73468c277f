<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pasien extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            array(
                'masterModel',
                'pengkajianAwalModel',
                'rekam_medis/penunjang/patologi_klinik/PatologiKlinikModel'
            )
        );
    }

    public function index()
    {
      $data = array(
        'title' => 'Halaman History Penunjang',
        'isi' => 'rekam_medis/penunjang/history/index',
      );
  
      $this->load->view('layout/wrapper', $data);
    }
}
