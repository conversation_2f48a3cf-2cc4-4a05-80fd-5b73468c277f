<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>timja extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'EtimjaModel',
        'rekam_medis/MedisModel',
      )
    );
  }

  public function index()
  {
    $data = array(
      'title' => 'eTimja',
      'isi' => 'Etimja/index/wrapper',
      'timjaPasien' => $this->masterModel->referensiTimja(102, $this->session->userdata['id']),
      'id' => $this->uri->segment(2),
      'nomr' => $this->uri->segment(3),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
  }

  public function dataPasien($id, $nomr)
  {
    $detail = $this->EtimjaModel->detail($id);
    $nokun = $detail['nokun'];
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'nokun' => $nokun,
      'id' => $id,
      'pasien' => $pasien,
      'nomr' => $nomr,
      'jumlah' => $this->EtimjaModel->history($nomr, 'jumlah'),
      'kasus' => $this->masterModel->referensi(1770),
      'pilihTandaVitalMon' => $this->MedisModel->pilihTandaVitalMon($nomr),
      'listDr' => $this->masterModel->listDrUmum(),
      'timja' => $this->masterModel->referensiTimja(102, null),
      'detail' => $detail,
      'disabled' => $detail['status'] != 3 ? 'disabled' : null,
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Etimja/index/index', $data);
  }

  public function form()
  {
    $nokun = $this->uri->segment(3);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $pasien['NORM'];
    $detail = null;
    $disabled = null;
    $data = array(
      'nomr' => $nomr,
      'nokun' => $nokun,
      'pasien' => $pasien,
      'jumlah' => $this->EtimjaModel->history($nomr, 'jumlah'),
      'kasus' => $this->masterModel->referensi(1770),
      'listDr' => $this->masterModel->listDrUmum(),
      'timja' => $this->masterModel->referensiTimja(102, null),
      'detail' => $detail,
      'disabled' => $disabled,
    );

    // echo '<pre>';print_r($data);exit();
    $this->load->view('Etimja/form', $data);
  }

  public function listPasien()
  {
    $post = $this->input->post();
    $nomr = null;
    $nama = null;
    $timja = $post['timja'] ?? null;
    $status = $post['status'] ?? null;

    // Mulai cek isi filter
    if (isset($post['filter'])) {
      if (is_numeric($post['filter'])) {
        $nomr = $post['filter'];
      } else {
        $nama = $post['filter'];
      }
    }
    // Akhir cek isi filter

    $data = array(
      'pasien' => $this->EtimjaModel->listPasien($timja, $status, $nomr, $nama),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Etimja/index/pasien', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $this->form_validation->set_rules($this->EtimjaModel->rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();

          // Mulai ambil data
          $data = array(
            'nokun' => $post['nokun'] ?? null,
            'tanggal' => $post['tanggal'] ?? null,
            'waktu' => $post['waktu'] ?? null,
            'dpjp' => $post['dpjp'] ?? null,
            'timja' => $post['timja'] ?? null,
            'kasus' => $post['kasus'] ?? null,
            'diagnosis' => $post['diagnosis'] ?? null,
            'keluhan' => $post['keluhan'] ?? null,
            'klinis' => $post['klinis'] ?? null,
            'masalah' => $post['masalah'] ?? null,
            'pertanyaan' => $post['pertanyaan'] ?? null,
          );
          // Akhir ambil data

          // Mulai proses
          if (!empty($post['id'])) {
            // Mulai ubah
            $id = $post['id'];
            // echo '<pre>';print_r($data);exit();
            $this->EtimjaModel->ubah($id, $data);
            $this->EtimjaModel->hapusDokterUndangan($id);
            // Mulai simpan dokter undangan
            if (isset($post['dokter_undangan'])) {
              $this->simpanDokterUndangan($id);
            }
            // Akhir simpan dokter undangan
            // Akhir ubah
          } else {
            // Mulai simpan
            $data['status'] = 1;
            $data['oleh'] = $this->session->userdata['id'];
            // echo '<pre>';print_r($data);exit();
            $id = $this->EtimjaModel->simpan($data);

            // Mulai simpan dokter undangan
            if (isset($post['dokter_undangan'])) {
              $this->simpanDokterUndangan($id);
            }
            // Akhir simpan dokter undangan
            // Akhir simpan
          }
          // Akhir proses

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'final') {
        $this->form_validation->set_rules($this->EtimjaModel->rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();

          // Mulai proses
          if (!empty($post['id'])) {
            $id = $post['id'];
            $data = array(
              'waktu_final' => date('Y-m-d H:i:s'),
              'pemfinal' => $this->session->userdata['id'],
              'kesimpulan' => $post['kesimpulan'] ?? null,
              'status' => 2,
            );
            // echo '<pre>';print_r($data);exit();
            $this->EtimjaModel->ubah($id, $data);
          }
          // Akhir proses

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'retimja') {
        $this->form_validation->set_rules($this->EtimjaModel->rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();

          // Mulai proses
          if (!empty($post['id'])) {
            $id = $post['id'];
            $data = array(
              'retimja' => $post['id'],
              'oleh' => $this->session->userdata['id'],
              'status' => 3,
            );
            $idBaru = $this->EtimjaModel->retimja($id, $data);
          }
          // Akhir proses

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array(
              'status' => 'success',
              'id_baru' => $idBaru
            );
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->EtimjaModel->detail($post['id']);
        // echo '<pre>';print_r($data);exit();
        echo json_encode(
          array(
            'status' => 'success',
            'detail' => $data,
          )
        );
      }
    }
  }

  public function simpanDokterUndangan($id)
  {
    $i = 0;
    $post = $this->input->post();
    foreach ($post['dokter_undangan'] as $du) {
      $dataDokterUndangan[$i] = array(
        'id_etimja' => $id,
        'dokter_undangan' => $du,
        'status' => 1,
      );
      $i++;
    }
    $this->EtimjaModel->simpanDokterUndangan($dataDokterUndangan);
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array(
      'nokun' => $post['nokun'] ?? null,
      'nomr' => $post['nomr'] ?? null,
    );
    // echo '<pre>';print_r($data);exit();

    // Mulai periksa info
    if ($post['keterangan'] == 'info') {
      $this->load->view('Etimja/historyInfo', $data);
    } else {
      $this->load->view('Etimja/history', $data);
    }
    // Akhir periksa info

  }

  public function tabel()
  {
    $post = $this->input->post();
    $draw = intval($this->input->post('draw'));
    $nomr = $post['nomr'];
    $history = $this->EtimjaModel->history($nomr, 'tabel');
    $data = array();
    $no = 1;
    $lihat = null;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history->result() as $h) {
      // Mulai periksa status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-primary">Diterima</p>';
      } elseif ($h->status == 2) {
        $disabled = 'disabled';
        $status = '<p class="text-success">Final</p>';
      } elseif ($h->status == 3) {
        $disabled = null;
        $status = '<p class="text-warning">Re-timja</p>';
      }
      // Akhir periksa status

      // Mulai periksa keterangan
      if ($post['keterangan'] == 'info') {
        $lihat = "<button type='button' class='btn btn-sm btn-primary waves-effect buka-etimja' data-id='" . $h->id . "'>
                    <i class='fa fa-eye'></i> Lihat
                  </button>";
      } else {
        $lihat = "<button type='button' class='btn btn-sm btn-primary waves-effect tbl-detail-etimja' data-id='" . $h->id . "'>
                    <i class='fa fa-eye'></i> Lihat
                  </button>";
      }
      // Akhir periksa keterangan
      $cetak = "<button type='submit' class='btn btn-sm btn-warning waves-effect cetakTimja' id='cetakTimja' data-id='" . $h->id . "'>
                    <i class='fa fa-print'></i> Cetak
                </button>";

      $data[] = array(
        $no,
        date('d/m/Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->waktu)),
        $h->tujuan,
        $h->pengisi,
        date('d/m/Y, H.i.s', strtotime($h->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' class='btn btn-sm btn-danger waves-effect tbl-batal-etimja' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          " . $lihat . "
          <a href='" . base_url('Etimja/' . $h->id . '/' . $nomr) . "'' class='btn btn-sm btn-custom waves-effect' target='_blank'>
            <i class='fas fa-users'></i> eTimja
          </a>
        </div>" . $cetak ,
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $this->form_validation->set_rules($this->EtimjaModel->rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'];
        $data = array('status' => 0);
        // echo '<pre>';print_r($data);exit();
        $this->EtimjaModel->ubah($id, $data);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function kirimDiskusi()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $this->form_validation->set_rules($this->EtimjaModel->rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();

        // Mulai simpan data
        $data = array(
          'id_etimja' => $post['id'] ?? null,
          'diskusi' => $post['diskusi'] ?? null,
          'balas' => $post['balas'] ?? null,
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );
        // echo '<pre>';print_r($data);exit();
        $this->EtimjaModel->simpanDiskusi($data);
        // Akhir simpan data

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function ambilDiskusi($idEtimja)
  {
    if (isset($idEtimja)) {
      $data = array(
        'ambilDiskusi' => $this->EtimjaModel->ambilDiskusi($idEtimja),
        'pengirim' => $this->session->userdata('id'),
      );
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Etimja/index/diskusi', $data);
    }
  }

  public function menuHistory()
  {
    $data = array(
      'title' => 'Tabel Pasien eTimja',
      'isi' => 'Etimja/MenuHistory/index',
    );
    $this->load->view('layout/wrapper', $data);
  }

  public function tabelMenuHistory()
  {
    $post = $this->input->post();
    $tanggalMulai = $post['tanggal_mulai'] ?? null;
    $tanggalSelesai = $post['tanggal_selesai'] ?? null;
    $mulai = strtotime($tanggalMulai);
    $selesai = strtotime($tanggalSelesai);
    $data = array(
      'tanggalMulai' => $tanggalMulai,
      'tanggalSelesai' => $tanggalSelesai,
      'mulai' => date('Y-m-d', $mulai),
      'selesai' => date('Y-m-d', $selesai),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Etimja/MenuHistory/tabel', $data);
  }

  public function isiTabelMenuHistory()
  {
    $draw = intval($this->input->post('draw'));
    $post = $this->input->post();
    $mulai = $post['mulai'] ?? null;
    $selesai = $post['selesai'] ?? null;
    $tabel = $this->EtimjaModel->tabelMenuHistory($mulai, $selesai);
    $data = array();
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel->result() as $t) {
      $data[] = array(
        "<a href='" . base_url('Etimja/' . $t->nokun . '/' . $t->id) . "'' class='btn btn-sm btn-custom waves-effect'>
          <i class='fas fa-users'></i> eTimja
        </a>",
        $t->norm,
        $t->nokun,
        $t->nama,
        $t->jk,
        $t->masalah,
        date('d/m/Y', strtotime($t->waktu_final)),
        date('H.i', strtotime($t->waktu_final)),
        $t->pemfinal,
        $t->kesimpulan,
      );
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $tabel->num_rows(),
      'recordsFiltered' => $tabel->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function jumlah()
  {
    $post = $this->input->post();
    $jumlah = $this->EtimjaModel->history($post['nomr'], 'jumlah');
    echo json_encode($jumlah);
  }
}

/* End of file Etimja.php */
/* Location: ./application/controllers/Etimja.php */