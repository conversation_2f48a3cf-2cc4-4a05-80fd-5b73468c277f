<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class UploadRm extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(2,$this->session->userdata('akses')) OR !in_array(3,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('uploadRmModel','masterModel'));
  }

  public function index()
  {
    $id_pengguna = $this->session->userdata('id');
    $jenisBerkas = $this->masterModel->referensi(70);

    $data = array(
      'title'       => 'Halaman Upload Rm',
      'isi'         => 'uploadRm/formUpload',
      'id_pengguna' => $id_pengguna,
      'jenisBerkas' => $jenis<PERSON>erkas,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function namaPasien()
  {
    $nomr = $this->input->post("nomr");
    $namaPasien = $this->masterModel->dataDiriPasien($nomr);

    echo '<input type="text" class="form-control" value="'.$namaPasien['NAMAPASIEN'].'"  disabled>';
  }

  public function uploadFileRM()
  {
    $config['upload_path']="../../../../mnt/upload_emr/uploadRM";
    // $config['upload_path']="./bankdata";
    // $config['allowed_types']='pdf|jpg|png|xls|xlsx|doc|docx';
    $config['allowed_types']='pdf|jpg|png';
    $config['encrypt_name'] = TRUE;

    $this->load->library('upload',$config);

    if(!$this->upload->do_upload("uploadFileEMR")){
      $error = array('error' => $this->upload->display_errors());
      // echo"<pre>";print_r($error);exit();
      redirect('filePendukungPasien/uploadRm/errorUploadFileRM','refresh');
    }else{

      $result = $this->upload->data();
      $data = array(
        'NORM'          =>  $this->input->post('nomr'),
        'JENIS_BERKAS'  =>  $this->input->post('jenisBerkas'),
        'TANGGAL_RM'    => date("Y-m-d",strtotime($this->input->post('tanggalRm'))),
        // 'TANGGAL_RM'    => date("Y-m-d"),
        'NAME'          => $result['file_name'],
        'TYPE'          => $result['file_ext'],
        'SIZE_UPLOAD'   => $result['file_size'],
        'ORIGINAL_NAME' => $result['client_name'],
        'OLEH'          =>  $this->input->post('id_pengguna'),
      );
      $this->uploadRmModel->insertRm($data);
      $this->session->set_flashdata('success', 'Berhasil');
      redirect('filePendukungPasien/uploadRm');
    }
  }
  public function errorUploadFileRM()
  {
    echo '<script>
            if (confirm("Periksa kembali! file upload gagal")) {
              window.location.href="'.base_url('filePendukungPasien/uploadRm').'";
            }else{
              window.location.href="'.base_url('filePendukungPasien/uploadRm').'";
            }
          </script>';
  }


}

/* End of file UploadRm.php */
/* Location: ./application/controllers/UploadRm.php */
