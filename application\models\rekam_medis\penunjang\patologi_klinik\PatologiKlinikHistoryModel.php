<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PatologiKlinikHistoryModel extends MY_Model
{
    protected $_table_name = '';
    protected $_primary_key = '';
    protected $_order_by = '';
    protected $_order_by_type = '';

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $norm = $this->input->post('norm');
        $this->db->select(
            "ol.NOMOR NOMOR_LAB, ol.TANGGAL TANGGAL_ORDER, r.DESKRIPSI RUANG_ASAL, rut.ID ID_TUJUAN,
            rut.DESKRIPSI RUANG_TUJUAN, ol.TANGGAL_RENCANA, master.getNamaLengkapPegawai(d.NIP) DOKTER_ASAL,
            ol.ALASAN, ol.STATUS, master.getNamaLengkapPegawai(ap.NIP) USER,
            IF(
                ol.STATUS = 1, 'Order Terkirim',
                IF(
                    ol.STATUS = 2, 'Diterima/Proses',
                    CONCAT(' Order Dibatalkan Oleh: ', master.getNamaLengkapPegawai(ap.NIP))
                )
            ) STATUS_ORDER"
        );
        $this->db->from('layanan.order_lab ol');
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = ol.KUNJUNGAN', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'LEFT');
        $this->db->join('master.ruangan r', 'r.ID = k.RUANGAN', 'LEFT');
        $this->db->join('master.dokter d', 'd.ID = ol.DOKTER_ASAL', 'LEFT');
        $this->db->join('pendaftaran.kunjungan kut', 'kut.REF = ol.NOMOR', 'LEFT');
        $this->db->join('master.ruangan rut', 'rut.ID = ol.TUJUAN', 'LEFT');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = ol.OLEH', 'LEFT');
        $this->db->where('p.NORM', $norm);
        $this->db->where('ol.TUJUAN', '105070101');
        $this->db->order_by('ol.TANGGAL DESC');

        if (isset($_POST['status'])) {
            $this->db->where('ol.STATUS', $this->input->post('status'));
        }
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}

/* End of file PatologiKlinikHistoryModel.php */
/* Location: ./application/models/rekam_medis/penunjang/PatologiKlinikHistoryModel.php */