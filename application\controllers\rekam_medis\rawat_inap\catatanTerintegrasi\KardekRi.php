<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KardekRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/catatanTerintegrasi/KardekRiModel', 'EresepModel'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    $detailKardek = $this->KardekRiModel->detailKardek($nopen);
    $pendaftaran = $this->KardekRiModel->getAll($nomr);

    $detailKardekPemberian = $this->KardekRiModel->detailHistoryKardekViewPemberian($nopen);

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'pendaftaran' => $pendaftaran,
      'detailKardek' => $detailKardek,
      'pemberian' => $detailKardekPemberian,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/kardekRiView', $data);
  }

  public function hasilDariNopen()
  {
    $nomr = $this->uri->segment(6);
    $nopen = $this->input->post('nopen');
    $nokun = $this->uri->segment(8);
    $detailKardek = $this->KardekRiModel->detailKardek($nopen);
    $pendaftaran = $this->KardekRiModel->getAll($nomr);

    $detailKardekPemberian = $this->KardekRiModel->detailHistoryKardekViewPemberian($nopen);

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'pendaftaran' => $pendaftaran,
      'detailKardek' => $detailKardek,
      'pemberian' => $detailKardekPemberian,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/detail/hasilDariNopen', $data);
  }

  public function hasilDariNopenHis()
  {
    $nomr = $this->uri->segment(6);
    $nopen = $this->input->post('nopen');
    $nokun = $this->uri->segment(8);
    $detailKardek = $this->KardekRiModel->detailKardek($nopen);
    $pendaftaran = $this->KardekRiModel->getAll($nomr);

    $detailKardekPemberian = $this->KardekRiModel->detailHistoryKardekViewPemberian($nopen);

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'pendaftaran' => $pendaftaran,
      'detailKardek' => $detailKardek,
      'pemberian' => $detailKardekPemberian,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/detail/hasilDariNopenHis', $data);
  }

  public function indexFlosheet()
  {
    $nomr = $this->uri->segment(6);
    $nopen = $this->input->post('nopen');
    $nokun = $this->uri->segment(8);
    $detailKardek = $this->KardekRiModel->detailKardek($nopen);

    $detailKardekPemberian = $this->KardekRiModel->detailHistoryKardekViewPemberian($nopen);

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'detailKardek' => $detailKardek,
      'pemberian' => $detailKardekPemberian,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/kardekRiView', $data);
  }

  public function viewDetailIsiKardek()
  {
    $idfarmasi = $this->input->post('idfarmasi');
    $norm = $this->input->post('norm');
    $jenisobat = $this->input->post('jenisobat');
    if($jenisobat == 1 || $jenisobat == 2){
    $detailKardekView = $this->KardekRiModel->detailKardekView($idfarmasi);
    }else if($jenisobat == 3){
    $detailKardekView = $this->KardekRiModel->detailKardekViewLuar($idfarmasi);
    }

    $data = array(
      'idfarmasi' => $idfarmasi,
      'dataDetail' => $detailKardekView,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewDetailIsiKardek', $data);
  }

  public function viewTambahJamRencanaKardekRi()
  {
    $idfarmasi = $this->input->post('id');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $jenisobat = $this->input->post('jenisobat');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $nokun = $this->input->post('nokun');
    $groupracikan = $this->input->post('groupracikan');
    if($jenisobat == 1){
    $detailKardekView = $this->KardekRiModel->detailKardekView($nopen, $idgenerik, $katgenerik);
    }else if($jenisobat == 3){
    $detailKardekView = $this->KardekRiModel->detailKardekViewLuar($idfarmasi);
    }else if($jenisobat == 2){
    $detailKardekView = $this->KardekRiModel->detailKardekViewRacikan($nokun, $groupracikan);
    }

    $data = array(
      'idfarmasi' => $idfarmasi,
      'jenisobat' => $jenisobat,
      'dataDetail' => $detailKardekView,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewTambahJamRencanaKardekRi', $data);
  }

  public function viewUbahAturanPakaiKardekRi()
  {
    $idfarmasi = $this->input->post('id');
    $norm = $this->input->post('norm');
    $nopen = $this->input->post('nopen');
    $jenisobat = $this->input->post('jenisobat');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $nokun = $this->input->post('nokun');
    $groupracikan = $this->input->post('groupracikan');
    $aturanpakai = $this->input->post('aturanpakai');
    if($jenisobat == 1){
    $detailKardekView = $this->KardekRiModel->detailKardekView($nopen, $idgenerik, $katgenerik);
    }else if($jenisobat == 3){
    $detailKardekView = $this->KardekRiModel->detailKardekViewLuar($idfarmasi);
    }else if($jenisobat == 2){
    $detailKardekView = $this->KardekRiModel->detailKardekViewRacikan($nokun, $groupracikan);
    }

    $historyAturanPakai = $this->KardekRiModel->historyAturanPakai($nopen, $idfarmasi, $idgenerik, $katgenerik);

    $data = array(
      'idfarmasi' => $idfarmasi,
      'jenisobat' => $jenisobat,
      'aturanpakai' => $aturanpakai,
      'dataDetail' => $detailKardekView,
      'historyAturanPakai' => $historyAturanPakai,
      'jalurPemberian' => $this->masterModel->referensi(901),
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewUbahAturanPakaiKardekRi', $data);
  }

  public function viewTambahKeteranganInsKardekRi()
  {
    $idfarmasi = $this->input->post('id');
    $nopen = $this->input->post('nopen');
    $jenisobat = $this->input->post('jenisobat');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $groupracikan = $this->input->post('groupracikan');
    if($jenisobat == 1){
    $detailKardekView = $this->KardekRiModel->detailKardekView($nopen, $idgenerik, $katgenerik);
    }else if($jenisobat == 3){
    $detailKardekView = $this->KardekRiModel->detailKardekViewLuar($idfarmasi);
    }else if($jenisobat == 2){
    $detailKardekView = $this->KardekRiModel->detailKardekViewRacikan($nokun, $groupracikan);
    }

    $historyKeterangan = $this->KardekRiModel->historyKeterangan($idfarmasi, $jenisobat);

    $data = array(
      'idfarmasi' => $idfarmasi,
      'idgenerik' => $idgenerik,
      'nopen' => $nopen,
      'jenisobat' => $jenisobat,
      'dataDetail' => $detailKardekView,
      'historyKeterangan' => $historyKeterangan,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewTambahKeteranganInsKardekRi', $data);
  }

  public function viewDetailRacikanObatKardekRi()
  {
    $nokun = $this->input->post('nokun');
    $grpracikan = $this->input->post('grpracikan');
    $idfarmasi = $this->input->post('idfarmasi');
    $nmobat = $this->input->post('nmobat');
    $detailKardekView = $this->KardekRiModel->detailKardekViewOld($idfarmasi);
    $detailIsiNamaObatRacikan = $this->KardekRiModel->detailIsiNamaObatRacikan($nokun, $grpracikan);

    $data = array(
      'nokun' => $nokun,
      'nmobat' => $nmobat,
      'grpracikan' => $grpracikan,
      'dataDetail' => $detailKardekView,
      'dataIsiObat' => $detailIsiNamaObatRacikan,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewDetailNamaObat', $data);
  }

  public function viewHisPemKardekRi()
  {
    $idfarmasi = $this->input->post('idfarmasi');
    $kunjungan = $this->input->post('kunjungan');
    $grpracikan = $this->input->post('grpracikan');
    $jenisobat = $this->input->post('jenisobat');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $nopen = $this->input->post('nopen');
    if($jenisobat == 1 || $jenisobat == 2){
      $detailKardekView = $this->KardekRiModel->detailKardekView($idfarmasi);
      $detailKardekPemberian = $this->KardekRiModel->detailKardekViewPemberian($nopen, $idgenerik, $katgenerik);
    }else if($jenisobat == 3){
      $detailKardekView = $this->KardekRiModel->detailKardekViewLuar($idfarmasi);
      $detailKardekPemberian = $this->KardekRiModel->detailKardekViewLuarResult($idfarmasi);
    }
    $isiNamaObatRacikan = $this->KardekRiModel->isiNamaObatRacikan($kunjungan, $grpracikan);

    $data = array(
      'idfarmasi' => $idfarmasi,
      'grpracikan' => $grpracikan,
      'isiNamaObatRacikan' => $isiNamaObatRacikan,
      'dataDetail' => $detailKardekView,
      'pemberian' => $detailKardekPemberian,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewHisPemKardekRi', $data);
  }

  public function simpanNonAktifKeterangan()
  {
    $id = $this->input->post('id');
    $data = array(
      'status' => 0, 
    );
    $this->db->where('tb_kardek_keterangan.id_farmasi', $id);
    $this->db->update('db_layanan.tb_kardek_keterangan', $data);
  }

  public function viewTambahDetailIsiKardek()
  {
    $nopen = $this->input->post('nopen');
    $idfarmasi = $this->input->post('idfarmasi');
    $norm = $this->input->post('norm');
    $kunjungan = $this->input->post('kunjungan');
    $grpracikan = $this->input->post('grpracikan');
    $jenisobat = $this->input->post('jenisobat');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $nmdsr = $this->input->post('nmdsr');

    $detailObatGenNonGen = $this->KardekRiModel->detailObatGenNonGen($nopen, $idgenerik, $katgenerik);

    if($jenisobat == 1){
    $detailKardekView = $this->KardekRiModel->detailKardekView($nopen, $idgenerik, $katgenerik);
    }else if($jenisobat == 2){
    $detailKardekView = $this->KardekRiModel->detailKardekViewOld($idfarmasi);
    }else if($jenisobat == 3){
    $detailKardekView = $this->KardekRiModel->detailKardekLuarView($idfarmasi);
    }
    $jalurPemberian = $this->masterModel->referensi(901);
    $pilihPerawat = $this->pengkajianAwalModel->pilihPerawatKardek($nopen);
    $detailIsiNamaObatRacikan = $this->KardekRiModel->detailIsiNamaObatRacikan($kunjungan, $grpracikan);
    $isiNamaObatRacikan = $this->KardekRiModel->isiNamaObatRacikan($kunjungan, $grpracikan);

    $data = array(
      'idfarmasi' => $idfarmasi,
      'jenisobat' => $jenisobat,
      'nopen' => $nopen,
      'detailIsiNamaObatRacikan' => $detailIsiNamaObatRacikan,
      'grpracikan' => $grpracikan,
      'isiNamaObatRacikan' => $isiNamaObatRacikan,
      'dataDetail' => $detailKardekView,
      'jalurPemberian' => $jalurPemberian,
      'pilihPerawat' => $pilihPerawat,
      'detailObatGenNonGen' => $detailObatGenNonGen,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewTambahDetailIsiKardek', $data);
  }

  public function viewTambahGenNonGen()
  {
    $idobatfarmasi = $this->input->post('idobatfarmasi');
    $nopen = $this->input->post('nopen');
    $jenisobat = $this->input->post('jenisobat');
    $detailKardekView = $this->KardekRiModel->detailKardekViewOld($idobatfarmasi);
    $pilihPerawat = $this->pengkajianAwalModel->pilihPerawatKardek($nopen);

    $data = array(
      'idobatfarmasi' => $idobatfarmasi,
      'jenisobat' => $jenisobat,
      'dataDetail' => $detailKardekView,
      'pilihPerawat' => $pilihPerawat,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewTambahGenNonGen', $data);
  }

  public function viewJalurPemberianKardekRi()
  {
    $idfarmasi = $this->input->post('idfarmasi');
    $jenisobat = $this->input->post('jenisobat');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $nopen = $this->input->post('nopen');
    $nmdsr = $this->input->post('nmdsr');
    if($jenisobat == 1 || $jenisobat == 2){
    $detailKardekView = $this->KardekRiModel->detailKardekView($nopen, $idgenerik, $katgenerik);
    }else if($jenisobat == 3){
    $detailKardekView = $this->KardekRiModel->detailKardekLuarView($idfarmasi);
    }

    $data = array(
      'idfarmasi' => $idfarmasi,
      'dataDetail' => $detailKardekView,
      'jalurPemberian' => $this->masterModel->referensi(901),
      'jenisobat' => $jenisobat
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewJalurPemberianKardekRi', $data);
  }

  public function simpanUbahJalurPemberianKardekRi()
  {
    $idFarmasi = $this->input->post('idfarmasi');
    $jenisobat = $this->input->post('jenisobat');
    $idJalurPemberian = $this->input->post('jlrPemberianKardekRi');

    $data = array(
      'jalur_pemberian'    => $idJalurPemberian
    );

    if($jenisobat == 1 || $jenisobat == 2){
      $this->db->where('tb_kardek_rawatinap.id_farmasi', $idFarmasi);
      $this->db->update('db_layanan.tb_kardek_rawatinap', $data);
    }else if($jenisobat == 3){
      $this->db->where('tb_kardek_obat_luar.id', $idFarmasi);
      $this->db->update('db_layanan.tb_kardek_obat_luar', $data);
    }
  }

  public function simpanAturanPakai()
  {
    $post = $this->input->post();
    $data = array(
      'id_farmasi' => isset($post['idfarmasi']) ? $post['idfarmasi'] : NULL,
      'dosis' => isset($post['simpanDosisKardekRi']) ? $post['simpanDosisKardekRi'] : NULL,
      'jam' => isset($post['simpanJamKardekRi']) ? $post['simpanJamKardekRi'] : NULL,
      'jalur_pemberian' => isset($post['simpanJalurKardekRi']) ? $post['simpanJalurKardekRi'] : NULL,
      'oleh' => $this->session->userdata('id'),
    );
    $this->db->insert('db_layanan.tb_kardek_aturan_pakai', $data);
  }

  public function simpanKeteranganIns()
  {
    $post = $this->input->post();
    $jenisobat = $this->input->post('jenisobat');
    $data = array(
      'id_farmasi' => isset($post['idfarmasi']) ? $post['idfarmasi'] : NULL,
      'id_generik' => isset($post['idgenerik']) ? $post['idgenerik'] : NULL,
      'nopen' => isset($post['nopen']) ? $post['nopen'] : NULL,
      'keterangan' => isset($post['simpanKeteranganInsRi']) ? $post['simpanKeteranganInsRi'] : NULL,
      'jenis' => isset($jenisobat) ? $jenisobat : NULL,
      'oleh' => $this->session->userdata('id'),
    );
    $this->db->insert('db_layanan.tb_kardek_keterangan', $data);
  }

public function simpanRencanaJam()
  {
    $post = $this->input->post();
    $data = array(
      'rencana_jam' => isset($post['pilihJamRencanaKardekRi']) ? json_encode($post['pilihJamRencanaKardekRi']) : "",
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    if($post['jenisobat'] == 1 || $post['jenisobat'] == 2){
      $this->db->where('tb_kardek_rawatinap.id_farmasi', $post['idfarmasi']);
      $this->db->update('db_layanan.tb_kardek_rawatinap', $data);
    }else if($post['jenisobat'] == 3){
      $this->db->where('tb_kardek_obat_luar.id', $post['idfarmasi']);
      $this->db->update('db_layanan.tb_kardek_obat_luar', $data);
    }
  }

  public function viewTambahObatKardekLuar()
  {
    $nomr = $this->input->post('nomr');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $jalurPemberian = $this->masterModel->referensi(901);
    $ruanganRawatInap = $this->masterModel->listRawatInapPrtklKemo();

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'jalurPemberian' => $jalurPemberian,
      'ruanganRawatInap' => $ruanganRawatInap,
      'nokun' => $nokun
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewTambahObatKardekLuar', $data);
  }

  public function viewTambahObatJumlah()
  {
    $nomr = $this->input->post('nomr');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $listObatPilih = $this->KardekRiModel->listObatPilih($nopen);
    $detailKardek = $this->KardekRiModel->detailKardek($nopen);
    $jalurPemberian = $this->masterModel->referensi(901);
    $farmasi_eresep = $this->masterModel->farmasi();
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $getTbBbAlergi = $this->pengkajianAwalModel->getTbBbAlergi($nomr);

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'listObatPilih' => $listObatPilih,
      'detailKardek' => $detailKardek,
      'jalurPemberian' => $jalurPemberian,
      'farmasi_eresep' => $farmasi_eresep,
      'pasien' => $pasien,
      'getTbBbAlergi' => $getTbBbAlergi,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewTambahJumlahObat', $data);
  }

  public function hasilObatPerGenerik()
  {
    $id = $this->input->post('id');
    $nopen = $this->input->post('nopen');

    $listObat = $this->KardekRiModel->detailObatGenNonGen($nopen, $id);

    $data = array(
      'listObat' => $listObat,
    );
    echo json_encode($data);
  }

  public function simpanTambahJumlahObat()
  {
    $post = $this->input->post();
    $kode = $this->pengkajianAwalModel->generateNoOrderResep();

    $dataOrderResep = array(
      'NOMOR' => $kode,
      'KUNJUNGAN' => $post["kunjungan"],
      'TANGGAL' => date("Y-m-d H:i:s"),
      'DOKTER_DPJP' => $post["dokter"],
      'PEMBERI_RESEP' => $post["pemberiFrmsObt"],
      'BERAT_BADAN' => $post["bbFrmsObt"],
      'TINGGI_BADAN' => $post["tbFrmsObt"],
      'DIAGNOSA' => $post["diagnosaFrmsObt"],
      'ALERGI_OBAT' => $post["alergiFrmsObt"],
      'GANGGUAN_FUNGSI_GINJAL' => $post["gangguGjlFrmsObt"],
      'MENYUSUI' => $post["mnysFrmsObt"],
      'HAMIL' => $post["hamilFrmsObt"],
      'RESEP_PASIEN_PULANG' => isset($post["resepFrmsObt"]) ? $post["resepFrmsObt"] : 0,
      'TUJUAN' => $post["farmasi_tujuan"],
      'RESEP_CITO' => isset($post['citoFrmsObt']) ? $post['citoFrmsObt'] : "0",
      'OLEH' => $this->session->userdata('id'),
    );

    $this->db->insert('layanan.order_resep', $dataOrderResep);

    $dataKardek = array();
    $indexKardek = 0;
    if (isset($post['idFrmsObt'])) {
      foreach ($post['idFrmsObt'] as $input) {
        if ($post['idFrmsObt'][$indexKardek] != "") {
          $dataKardek = array(
              'JENIS' => 41,
              'DESKRIPSI' => $post['nmDosisFrmsObt'][$indexKardek] ."/". $post['jmFrmsObt'][$indexKardek] ." Jam/". $this->EresepModel->getJalurPemberian($post['jlrPemFrmsObt'][$indexKardek]),
            );
          $this->db->insert('master.referensi', $dataKardek);
          $aturanPakai = $this->db->insert_id();

          // array_push(
            $dataDetailOrderObat = array(
              'ORDER_ID' => $kode,
              'FARMASI' => $post['brgBrFrmsObt'][$indexKardek],
              'STOK' => $post['idStkBrFrmsObt'][$indexKardek],
              'DOSIS' => $post['nmDosisFrmsObt'][$indexKardek],
              'JUMLAH' => $post['jmlhFrmsObt'][$indexKardek],
              'ATURAN_PAKAI' => $aturanPakai,
              'KETERANGAN' => $post['ketFrmsObt'][$indexKardek],
              'RACIKAN' => 0,
              'GROUP_RACIKAN' => 0,
              'KARDEX' => 1,
              'JAM' => isset($post['jmFrmsObt']) ? $post['jmFrmsObt'][$indexKardek] : null,
              'JALUR_PEMBERIAN' => isset($post['jlrPemFrmsObt']) ? $post['jlrPemFrmsObt'][$indexKardek] : null,
            );
            $this->db->insert('layanan.order_detil_resep', $dataDetailOrderObat);
          // );
        }
        $indexKardek++;
      }
    }

    // foreach ($dataDetailOrderObat as $key => $value) {
    //   $this->db->insert('layanan.order_detil_resep', $value, 'ORDER_ID');
    // }

  }

  public function simpanListObatKardekLuar()
  {
    $post = $this->input->post();
    $nokun = $this->input->post('nokun');
    $ruanganObatKardekLuarRi = $this->input->post('ruanganObatKardekLuarRi');
    $nmObatKardekLuarRi = $this->input->post('nmObatKardekLuarRi');
    $dosisObatKardekLuarRi = $this->input->post('dosisObatKardekLuarRi');
    $jamObatKardekLuarRi = $this->input->post('jamObatKardekLuarRi');
    $jalurPemberianObatKardekLuarRi = $this->input->post('pilihJalurPemberianKardekRi');
    $jumlahObatKardekLuarRi = $this->input->post('jumlahObatKardekLuarRi');
    $keteranganObatKardekLuarRi = $this->input->post('keteranganObatKardekLuarRi');
    $oleh = $this->session->userdata("id");

    $data = array(
      'nokun' => $nokun,
      'ruangan' => $ruanganObatKardekLuarRi,
      'nama_obat' => $nmObatKardekLuarRi,
      'dosis' => $dosisObatKardekLuarRi,
      'jam' => $jamObatKardekLuarRi,
      'jalur_pemberian' => $jalurPemberianObatKardekLuarRi,
      'jumlah' => $jumlahObatKardekLuarRi,
      'keterangan' => $keteranganObatKardekLuarRi,
      'oleh' => $oleh,
      'status' => 1,
    );

    $getIdKardekLuar = $this->KardekRiModel->simpanKardekLuar($data);

    $dataKardekLuarDetail = array(
      'id_kardek_obat_luar' => $getIdKardekLuar,
      'jumlah_obat' => $jumlahObatKardekLuarRi,
      'jalur_pemberian' => $jalurPemberianObatKardekLuarRi,
      'oleh' => $oleh,
      'status' => 1,
    );

    $this->db->insert('db_layanan.tb_kardek_rawatinap_luar', $dataKardekLuarDetail);

  }

  public function viewStopDetailIsiKardek()
  {
    // $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    $idfarmasi = $this->input->post('idfarmasi');
    $norm = $this->input->post('norm');
    $kunjungan = $this->input->post('kunjungan');
    $grpracikan = $this->input->post('grpracikan');
    $jenisobat = $this->input->post('jenisobat');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $nopen = $this->input->post('nopen');
    $nmdsr = $this->input->post('nmdsr');
    $golongan = $this->input->post('golongan');
    if($jenisobat == 1){
    $detailKardekView = $this->KardekRiModel->detailKardekView($nopen, $idgenerik, $katgenerik);
    $listObat = $this->KardekRiModel->detailObatGenNonGen($nopen, $idgenerik, $katgenerik);
    }else if($jenisobat == 2){
    $detailKardekView = $this->KardekRiModel->detailKardekViewOld($idfarmasi);
    $listObat = "";
    }else if($jenisobat == 3){
    $detailKardekView = $this->KardekRiModel->detailKardekLuarView($idfarmasi);
    $listObat = "";
    }
    $listDpjp = $this->masterModel->listDrUmum();
    $getDataPasien = $this->pengkajianAwalModel->getNomr($nokun);
    $isiNamaObatRacikan = $this->KardekRiModel->isiNamaObatRacikan($kunjungan, $grpracikan);

    $data = array(
      'nopen' => $nopen,
      'idgenerik' => $idgenerik,
      'katgenerik' => $katgenerik,
      'idfarmasi' => $idfarmasi,
      'jenisobat' => $jenisobat,
      'grpracikan' => $grpracikan,
      'dataDetail' => $detailKardekView,
      'listDpjp' => $listDpjp,
      'listObat' => $listObat,
      'isiNamaObatRacikan' => $isiNamaObatRacikan,
      'getPasien' => $getDataPasien,
      'golongan' => $golongan,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewStopDetailIsiKardek', $data);
  }

  public function viewUbahTanggalResepKardekRi()
  {
    $id = $this->input->post('id');
    $nopen = $this->input->post('nopen');
    $tgl = $this->input->post('tgl');
    $jenisobat = $this->input->post('jenisobat');

    $data = array(
      'id' => $id,
      'nopen' => $nopen,
      'tgl' => $tgl,
      'jenisobat' => $jenisobat,
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/kardek/viewUbahTglKardek', $data);
  }

  public function inputUbahPemberianKardek()
  {
    $post = $this->input->post();
    $nopen = $this->input->post('nopen');
    $oleh = $this->session->userdata("id");
    $id = $this->input->post('id');
    $jenisObat = $this->input->post('jenisobat');
    $date = $this->input->post('tglPemberianKardekRi');

    $tglPemberian = date('Y-m-d H:i:s', strtotime($date));

    if($jenisObat == 1 || $jenisObat == 2){
      $data = array(
      'tanggal_jam' => $tglPemberian,
      'oleh' => $oleh,
    );

    // echo "<pre>";print_r($data);echo "</pre>";exit();

    $this->db->where('tb_kardek_pemberian_rawatinap.id', $id);
    $this->db->update('db_layanan.tb_kardek_pemberian_rawatinap', $data);

    }else if($jenisObat == 3){
      $data = array(
      'tanggal_jam' => $tglPemberian,
      'oleh' => $oleh,
    );

      $this->db->where('tb_kardek_pemberian_luar.id', $id);
      $this->db->update('db_layanan.tb_kardek_pemberian_luar', $data);
    }
  }

  public function inputPemberianKardek()
  {
    $post = $this->input->post();
    $idFarmasiKardek = $this->input->post('idFarmasi');
    $date = $this->input->post('tglPemberianKardekRi');
    $pilihPerawat = $this->input->post('pilihPerawatKardekRi');
    $jumlahPemb = $this->input->post('jumlahBerikanKardekRi');
    $dosisBerikanKardek = $this->input->post('dosisKardekRi');
    $pilihJalurPemberianKardek = $this->input->post('pilihJalurPemberianKardekRi');
    // $pilihWaktuPemberianKardek = $this->input->post('pilihWaktuPemberianKardekRi');
    $sisa = $this->input->post('sisaBerikan');
    $pasienKeluargaKardek = $this->input->post('pasienKeluargaKardekRi');
    $jenisObat = $this->input->post('jenisObat');
    $oleh = $this->session->userdata("id");

    $tglPemberian = date('Y-m-d H:i:s', strtotime($date));

    if($jenisObat == 1 || $jenisObat == 2){
      $data = array(
      'id_farmasi' => $idFarmasiKardek,
      'tanggal_jam' => $tglPemberian,
      'jumlah_pemberian' => $jumlahPemb,
      'dosis' => $dosisBerikanKardek,
      'jalur_pemberian_input' => $pilihJalurPemberianKardek,
      // 'waktu_pemberian_input' => $pilihWaktuPemberianKardek,
      'perawat_2' => $pilihPerawat,
      'pasien_keluarga' => $pasienKeluargaKardek,
      'oleh' => $oleh,
      'status' => 1
    );

    // echo "<pre>";print_r($data);echo "</pre>";

    $this->db->insert('db_layanan.tb_kardek_pemberian_rawatinap', $data);

    $jumlahObat = array(
      'jumlah_obat' => $sisa
    );
    // echo "<pre>";print_r($jumlahObat);echo "</pre>";exit();
    $this->db->where('id_farmasi', $idFarmasiKardek);
    $this->db->update('db_layanan.tb_kardek_rawatinap', $jumlahObat);
    }else if($jenisObat == 3){
      $data = array(
      'id_kardek_obat_luar' => $idFarmasiKardek,
      'tanggal_jam' => $tglPemberian,
      'jumlah_pemberian' => $jumlahPemb,
      'dosis' => $dosisBerikanKardek,
      'jalur_pemberian_input' => $pilihJalurPemberianKardek,
      'perawat_2' => $pilihPerawat,
      'pasien_keluarga' => $pasienKeluargaKardek,
      'oleh' => $oleh,
      'status' => 1
    );

      $this->db->insert('db_layanan.tb_kardek_pemberian_luar', $data);

      $jumlahObat = array(
      'jumlah_obat' => $sisa
    );

    $this->db->where('tb_kardek_rawatinap_luar.id_kardek_obat_luar', $idFarmasiKardek);
    $this->db->update('db_layanan.tb_kardek_rawatinap_luar', $jumlahObat);

    }
  }

  public function hentikanPemberianKardek()
  {
    $post = $this->input->post();
    $idFarmasiKardek = $this->input->post('idFarmasi');
    $dateKardekRi = $this->input->post('tglPemberianKardekRiStop');
    $dpjpPilih = $this->input->post('stopPilihDokterPemberianKardekRi');
    $jenisObat = $this->input->post('jenisObat');
    $nopen = $this->input->post('nopen');
    $idgenerik = $this->input->post('idgenerik');
    $katgenerik = $this->input->post('katgenerik');
    $oleh = $this->session->userdata("id");

    $tglPemberian = date('Y-m-d H:i:s', strtotime($dateKardekRi));

    if($jenisObat == 1){
      $dataBahan = array();
    $indexBahan = 0;
    if (isset($post['idFrmsiGenNonGenKardekStop'])) {
     foreach ($post['idFrmsiGenNonGenKardekStop'] as $input) {
       if ($post['idFrmsiGenNonGenKardekStop'][$indexBahan] != "") {
         array_push(
           $dataBahan, array(
            'id_farmasi' => $post['idFrmsiGenNonGenKardekStop'][$indexBahan],
            'tanggal_jam_berhenti' => $tglPemberian,
            'dpjp' => $dpjpPilih,
            'oleh' => $oleh,
            'status' => 1
          )
         );
         
         $sttsObatKardek = array(
          'oleh' => $oleh,
          'status' => 2
        );
         $this->db->where('id_farmasi', $post['idFrmsiGenNonGenKardekStop'][$indexBahan]);
         $this->db->update('db_layanan.tb_kardek_rawatinap', $sttsObatKardek);
       }
       $indexBahan++;
     }
    }

    // echo "<pre>"; print_r($dataBahan); echo "</pre>"; exit();
    // $this->db->delete('db_pesan_makan.tb_master_makanan_detail', array('id_makanan' => $getIdBahan));
    foreach ($dataBahan as $key => $value) {
      $this->db->insert('db_layanan.tb_kardek_stop_rawatinap', $value, 'id_farmasi');
    }
    }else if($jenisObat == 2){
      $data = array(
      'id_farmasi' => $idFarmasiKardek,
      'tanggal_jam_berhenti' => $tglPemberian,
      'dpjp' => $dpjpPilih,
      'oleh' => $oleh,
      'status' => 1
    );

    // echo "<pre>";print_r($data);echo "</pre>";

    $this->db->insert('db_layanan.tb_kardek_stop_rawatinap', $data);

    $sttsObatKardek = array(
      'oleh' => $oleh,
      'status' => 2
    );
    $this->db->where('id_farmasi', $idFarmasiKardek);
    $this->db->update('db_layanan.tb_kardek_rawatinap', $sttsObatKardek);

    }else if($jenisObat == 3){
      $data = array(
      'id_farmasi' => $idFarmasiKardek,
      'tanggal_jam_berhenti' => $tglPemberian,
      'dpjp' => $dpjpPilih,
      'oleh' => $oleh,
      'status' => 1
    );

      $this->db->insert('db_layanan.tb_kardek_stop_luar', $data);

      $sttsObatKardek = array(
      'oleh' => $oleh,
      'status' => 2
    );
    $this->db->where('tb_kardek_rawatinap_luar.id_kardek_obat_luar', $idFarmasiKardek);
    $this->db->update('db_layanan.tb_kardek_rawatinap_luar', $sttsObatKardek);

    }
  }

}