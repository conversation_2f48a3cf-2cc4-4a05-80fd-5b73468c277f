<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProKemAnakModel extends MY_Model
{
  protected $_table_name = 'medis.tb_form_pro_kem_anak';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function tabelProKem($nokun)
  {
    $this->db->select(
      'fpa.id, fpa.tgl_diagnosis, pa.ID id_pro_kem, pa.NAMA prokem, master.getNamaLengkapPegawai(dpjp.NIP) DPJP,
      master.getNamaLengkapPegawai(peng.NIP) pengisi, fpa.created_at, fpa.status'
    );
    $this->db->from('medis.tb_form_pro_kem_anak fpa');
    $this->db->join('db_master.tb_pro_kem_anak pa', 'pa.ID = fpa.pro_kem', 'left');
    $this->db->join('master.dokter dpjp', 'dpjp.ID = fpa.dpjp', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = fpa.oleh', 'left');
    $this->db->where('fpa.nokun', $nokun);
    $query = $this->db->get();
    return $query;
  }

  public function pilihProKem()
  {
    $this->db->select('ID, NAMA');
    $this->db->from('db_master.tb_pro_kem_anak');
    $this->db->where('status', 1);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function tabelObat($id)
  {
    $this->db->select(
      'pad.ID, op.ID ID_OBAT_PEMBERIAN, op.NAMA_OBAT, pad.DOSIS_PROTOKOL, op.SEDIAAN, v.variabel AKSES'
    );
    $this->db->from('db_master.tb_pro_kem_anak_detil pad');
    $this->db->join('db_master.tb_obat_protokol op', 'op.ID = pad.OBAT_PROTOKOL', 'left');
    $this->db->join('db_master.variabel v', 'v.id_variabel = pad.AKSES_PEMBERIAN', 'left');
    $this->db->where('pad.ID_PROTOKOL', $id);
    $this->db->where('pad.status', 1);
    $query = $this->db->get();
    return $query->result();
  }

  public function siklus($idProKem, $id, $param)
  {
    if ($param == 'jenis') {
      $this->db->select(
        "pap.JENIS,
          CASE
              WHEN pap.JENIS = 1 THEN 'Induksi'
              WHEN pap.JENIS = 2 THEN 'Konsolidasi'
              WHEN pap.JENIS = 3 THEN 'Intensifikasi'
          END AS NM_JENIS",
        false
      );
      $this->db->distinct();
    } else {
      $this->db->select(
        "pap.ID, pap.JENIS, pap.MINGGU,
        CASE
            WHEN pap.JENIS = 1 THEN 'Induksi'
            WHEN pap.JENIS = 2 THEN 'Konsolidasi'
            WHEN pap.JENIS = 3 THEN 'Intensifikasi'
        END AS NM_JENIS",
        false
      );
    }
    $this->db->from('db_master.tb_pro_kem_anak_pemberian pap');
    if (isset($param)) {
      if ($param == 'tersimpan') {
        // Pilih siklus untuk history
        $this->db->join('medis.tb_form_pro_kem_anak_terapi pat', 'pat.id_pro_kem_anak_pemberian = pap.id', 'left');
        $this->db->join('medis.tb_form_pro_kem_anak fpa', 'fpa.id = pat.id_form_pro_kem_anak', 'left');
        $this->db->where('fpa.id', $id);
        $this->db->where('pat.status', 1);
      }
    }
    $this->db->where('pap.PROTOKOL_KEMO_ANAK', $idProKem);
    $this->db->where('pap.status', 1);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function tabelDosis($idSiklus, $idProKem)
  {
    $this->db->select(
      'pao.ID, op.NAMA_OBAT, op.SEDIAAN, v.variabel AKSES, pad.DOSIS_PROTOKOL, pao.keterangan KETERANGAN'
    );
    $this->db->from('db_master.tb_pro_kem_anak_pemberian pap');
    $this->db->join('db_master.tb_pro_kem_anak_pemberian_detil ppd', 'ppd.JENIS_PEMBERIAN = pap.ID', 'left');
    $this->db->join('db_master.tb_pro_kem_anak_detil pad', 'pad.ID = ppd.OBAT_PROKEM_ANAK', 'left');
    $this->db->join('db_master.tb_obat_protokol op', 'op.ID = pad.OBAT_PROTOKOL', 'left');
    $this->db->join('db_master.variabel v', 'v.id_variabel = pad.AKSES_PEMBERIAN', 'left');
    $this->db->join('db_master.tb_pro_kem_anak pa', 'pa.ID = pap.PROTOKOL_KEMO_ANAK', 'left');
    $this->db->join('medis.tb_form_pro_kem_anak fpa', 'fpa.pro_kem = pa.ID', 'left');
    $this->db->join(
      'medis.tb_form_pro_kem_anak_obat pao',
      'pao.id_form_pro_kem_anak = fpa.id AND pao.id_pro_kem_anak_detail = pad.ID',
      'left'
    );
    $this->db->where('fpa.id', $idSiklus);
    $this->db->where('pap.ID', $idProKem);
    $this->db->where('pap.status', 1);
    $query = $this->db->get();
    return $query->result();
  }

  public function historyTerapi($idSiklus, $idProKem, $param)
  {
    if (isset($param)) {
      if ($param == 'tabel') {
        $this->db->select(
          'op.ID ID_OBAT_PEMBERIAN, pas.ID ID_SIKLUS, pap.MINGGU, pas.dosis DOSIS_SEKARANG,
          papt.tgl_rencana_pemberian TGL_RENCANA_PEMBERIAN, master.getNamaLengkapPegawai(p1.NIP) PERAWAT_1,
          master.getNamaLengkapPegawai(p2.NIP) PERAWAT_2'
        );
      }
    } else {
      $this->db->select(
        'fpa.nokun NOKUN, pat.id ID_TERAPI, pat.tgl_terapi TGL_TERAPI, tbb.bb BB, tbb.tb TB, tbb.lpb LPB,
        master.getNamaLengkapPegawai(peng.NIP) PENGISI, pao.ID ID_OBAT, op.NAMA_OBAT, op.SEDIAAN, v.variabel AKSES,
        pap.JENIS, pap.MINGGU, pad.DOSIS_PROTOKOL, pao.dosis DOSIS, pao.keterangan KETERANGAN, pas.ID ID_SIKLUS,
        pas.dosis DOSIS_SEKARANG, pas.keterangan KETERANGAN_SEKARANG'
      );
    }
    $this->db->from('medis.tb_form_pro_kem_anak_terapi pat');
    $this->db->join('db_pasien.tb_tb_bb tbb', 'tbb.ref = pat.id', 'left');
    $this->db->join('db_master.tb_pro_kem_anak_pemberian pap', 'pap.id = pat.id_pro_kem_anak_pemberian', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = pat.oleh', 'left');
    $this->db->join('db_master.tb_pro_kem_anak_pemberian_detil ppd', 'ppd.JENIS_PEMBERIAN = pap.ID', 'left');
    $this->db->join('db_master.tb_pro_kem_anak_detil pad', 'pad.ID = ppd.OBAT_PROKEM_ANAK', 'left');
    $this->db->join('db_master.tb_obat_protokol op', 'op.ID = pad.OBAT_PROTOKOL', 'left');
    $this->db->join('db_master.variabel v', 'v.id_variabel = pad.AKSES_PEMBERIAN', 'left');
    $this->db->join('db_master.tb_pro_kem_anak pa', 'pa.ID = pap.PROTOKOL_KEMO_ANAK', 'left');
    $this->db->join(
      'medis.tb_form_pro_kem_anak fpa',
      'fpa.pro_kem = pa.ID AND fpa.ID = pat.id_form_pro_kem_anak',
      'left'
    );
    $this->db->join(
      'medis.tb_form_pro_kem_anak_obat pao',
      'pao.id_form_pro_kem_anak = fpa.id AND pao.id_pro_kem_anak_detail = pad.ID',
      'left'
    );
    $this->db->join(
      'medis.tb_form_pro_kem_anak_siklus pas',
      'pas.id_form_pro_kem_anak_terapi = pat.id AND pas.id_form_pro_kem_anak_obat = pao.id',
      'left'
    );
    if (isset($param)) {
      if ($param == 'tabel') {
        $this->db->join(
          'medis.tb_form_pro_kem_anak_pemberian_terapi papt',
          'papt.id_form_pro_kem_anak_siklus = pas.id',
          'left'
        );
        $this->db->join('aplikasi.pengguna p1', 'p1.ID = papt.perawat_1', 'left');
        $this->db->join('aplikasi.pengguna p2', 'p2.ID = papt.perawat_2', 'left');
      }
    }
    $this->db->where('pap.ID', $idProKem);
    $this->db->where('pap.status', 1);
    $this->db->where('pat.status', 1);
    $this->db->where('pas.status', 1);
    $this->db->where('fpa.id', $idSiklus);
    $this->db->where('tbb.data_source', 32);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function pemberianTerapi($id)
  {
    $this->db->select(
      'pat.id_form_pro_kem_anak ID, pa.nokun NOKUN, op.NAMA_OBAT, op.SEDIAAN, pad.DOSIS_PROTOKOL DOSIS_AWAL,
      v.variabel AKSES, pas.dosis DOSIS, pas.keterangan KETERANGAN, papt.keterangan_pemberian KETERANGAN_PEMBERIAN,
      papt.id ID_PEMBERIAN_TERAPI, papt.tgl_rencana_pemberian TGL_RENCANA_PEMBERIAN, papt.mulai MULAI,
      papt.selesai SELESAI, papt.perawat_1 PERAWAT_1, papt.perawat_2 PERAWAT_2'
    );
    $this->db->from('medis.tb_form_pro_kem_anak_pemberian_terapi papt');
    $this->db->join('medis.tb_form_pro_kem_anak_siklus pas', 'pas.id = papt.id_form_pro_kem_anak_siklus');
    $this->db->join('medis.tb_form_pro_kem_anak_terapi pat', 'pat.id = pas.id_form_pro_kem_anak_terapi', 'left');
    $this->db->join('medis.tb_form_pro_kem_anak pa', 'pa.id = pat.id_form_pro_kem_anak', 'left');
    $this->db->join('medis.tb_form_pro_kem_anak_obat pao', 'pao.id = pas.id_form_pro_kem_anak_obat', 'left');
    $this->db->join('db_master.tb_pro_kem_anak_detil pad', 'pad.id = pao.id_pro_kem_anak_detail', 'left');
    $this->db->join('db_master.tb_obat_protokol op', 'op.ID = pad.OBAT_PROTOKOL', 'left');
    $this->db->join('db_master.variabel v', 'v.id_variabel = pad.AKSES_PEMBERIAN', 'left');
    $this->db->where('pas.id', $id);
    $this->db->where('papt.status', 1);
    $this->db->where('pas.status', 1);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function keteranganPemberian($id)
  {
    $this->db->select(
      'papt.keterangan_pemberian KETERANGAN_PEMBERIAN, papt.tgl_rencana_pemberian TGL_RENCANA_PEMBERIAN,
      papt.mulai MULAI, papt.selesai SELESAI, master.getNamaLengkapPegawai(p1.NIP) PERAWAT_1,
      master.getNamaLengkapPegawai(p2.NIP) PERAWAT_2'
    );
    $this->db->from('medis.tb_form_pro_kem_anak_pemberian_terapi papt');
    $this->db->join('medis.tb_form_pro_kem_anak_siklus pas', 'pas.id = papt.id_form_pro_kem_anak_siklus');
    $this->db->join('aplikasi.pengguna p1', 'p1.ID = papt.perawat_1', 'left');
    $this->db->join('aplikasi.pengguna p2', 'p2.ID = papt.perawat_2', 'left');
    $this->db->where('pas.id', $id);
    $this->db->where('papt.status', 1);
    $this->db->where('pas.status', 1);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function simpanProKem($data)
  {
    $this->db->insert('medis.tb_form_pro_kem_anak', $data);
    return $this->db->insert_id();
  }

  public function ubahProKem($data, $id)
  {
    $this->db->where('medis.tb_form_pro_kem_anak.id', $id);
    $this->db->update('medis.tb_form_pro_kem_anak', $data);
  }

  public function simpanObat($data)
  {
    $this->db->insert_batch('medis.tb_form_pro_kem_anak_obat', $data);
  }

  public function simpanTerapi($data)
  {
    $this->db->insert('medis.tb_form_pro_kem_anak_terapi', $data);
    return $this->db->insert_id();
  }

  public function simpanSiklus($data)
  {
    $this->db->insert('medis.tb_form_pro_kem_anak_siklus', $data);
    return $this->db->insert_id();
  }

  public function simpanPemberianTerapi($data)
  {
    $this->db->insert('medis.tb_form_pro_kem_anak_pemberian_terapi', $data);
  }

  public function ubahTerapi($data, $id)
  {
    $this->db->where('medis.tb_form_pro_kem_anak_terapi.id', $id);
    $this->db->update('medis.tb_form_pro_kem_anak_terapi', $data);
  }

  public function ubahSiklus($data, $id)
  {
    $this->db->where('medis.tb_form_pro_kem_anak_siklus.id_form_pro_kem_anak_terapi', $id);
    $this->db->update('medis.tb_form_pro_kem_anak_siklus', $data);
  }

  public function ubahPemberianTerapi($data, $id)
  {
    $this->db->where('medis.tb_form_pro_kem_anak_pemberian_terapi.id', $id);
    $this->db->update('medis.tb_form_pro_kem_anak_pemberian_terapi', $data);
  }
}

/* End of file ProKemAnakModel.php */
/* Location: ./application/models/kemoterapi/ProKemAnakModel.php */