<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TindakanRadterModel extends CI_Model {

  public function pasientindkradter()
  {
    $query = $this->db->query("SELECT trt.id ID, trt.tanggal TGL_KUNJUNGAN,trt.kunjungan NOKUN, pp.NORM, master.getNamaLengkap(pp.NORM)NAMAPASIEN, master.getNamaLengkapPegawai(ap.NIP) OLEH,
    mr.DESKRIPSI RUANGAN, master.getNamaLengkapPegawai(md.NIP) DOKTERDPJP, trm.`status` STATUS, IF(trm.`status` IS NULL,0,trm.`status`) STATUS_OR
    FROM medis.tb_tindakan_radioterapi trt
    left join pendaftaran.kunjungan pk ON pk.NOMOR=trt.kunjungan
    LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
    LEFT JOIN aplikasi.pengguna ap ON ap.ID = trt.oleh
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pk.NOPEN
    LEFT JOIN master.ruangan mr ON mr.ID = tp.RUANGAN
    LEFT JOIN master.dokter md ON md.ID = tp.DOKTER
    LEFT JOIN medis.tb_tindakan_radioterapi_medis trm ON trm.kunjungan=trt.kunjungan
    left join aplikasi.pengguna pe ON pe.NIP=md.NIP
    where pe.ID=" . $this->session->userdata('id') . " AND trt.tanggal >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) order by STATUS_OR ASC, trm.tanggal desc");
      return $query;

  }

  public function getNokunVerif($nokun)
  {
      $query = $this->db->query('SELECT tbm.kunjungan 
      FROM medis.tb_tindakan_radioterapi_medis tbm 
      where tbm.kunjungan = "' . $nokun . '"'
      );
      return $query->row_array();
  }
}