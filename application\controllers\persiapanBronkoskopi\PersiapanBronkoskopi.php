<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PersiapanBronkoskopi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $post = $this->input->post();
    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $id_bron = $this->uri->segment(7);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $listPersiapanBronkoskopi1 = $this->masterModel->listPersiapanBronkoskopi1();
    $listPersiapanBronkoskopi2 = $this->masterModel->listPersiapanBronkoskopi2();
    $listPersiapanBronkoskopi3 = $this->masterModel->listPersiapanBronkoskopi3();
    $listPersiapanBronkoskopi4 = $this->masterModel->listPersiapanBronkoskopi4();
    $listPersiapanBronkoskopi5 = $this->masterModel->listPersiapanBronkoskopi5();
    $listPersiapanBronkoskopi6 = $this->masterModel->listPersiapanBronkoskopi6();
    $listPersiapanBronkoskopi7 = $this->masterModel->listPersiapanBronkoskopi7();
    $historyPersiapanBronkoskopi = $this->pengkajianAwalModel->historyPersiapanBronkoskopi();

    $data = array(
      'listPersiapanBronkoskopi1'    => $listPersiapanBronkoskopi1,
      'listPersiapanBronkoskopi2'    => $listPersiapanBronkoskopi2,
      'listPersiapanBronkoskopi3'    => $listPersiapanBronkoskopi3,
      'listPersiapanBronkoskopi4'    => $listPersiapanBronkoskopi4,
      'listPersiapanBronkoskopi5'    => $listPersiapanBronkoskopi5,
      'listPersiapanBronkoskopi6'    => $listPersiapanBronkoskopi6,
      'listPersiapanBronkoskopi7'    => $listPersiapanBronkoskopi7,
      'historyPersiapanBronkoskopi'    => $historyPersiapanBronkoskopi,
      'getNomr'    => $getNomr,
    );

    $this->load->view('Pengkajian/persiapanBronkoskopi/index', $data);
  }

}

/* End of file Pews.php */
/* Location: ./application/controllers/igd/Pews.php */
