<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SedasiPemulihan extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $id_sedasi_pemulihan = $this->uri->segment(8);
        $getPengkajian = $this->pengkajianAwalModel->historyDetailPemulihanSedasi($id_sedasi_pemulihan);

        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listPerawat = $this->masterModel->listPerawat();
        $listSkalaNyeriSedasi = $this->masterModel->listSkalaNyeriSedasi();
        $listRisikoJatuhSedasi = $this->masterModel->listRisikoJatuhSedasi();
        $listKesadaranSedasi = $this->masterModel->listKesadaranSedasi();
        $listPernapasanSedasi = $this->masterModel->listPernapasanSedasi();
        $listNyeriSedasi = $this->masterModel->listNyeriSedasi();
        $listFrekuensiNapasSedasi = $this->masterModel->listFrekuensiNapasSedasi();
        $listFrekuensiNadiSedasi = $this->masterModel->listFrekuensiNadiSedasi();
        $listTekananDarahSedasi = $this->masterModel->listTekananDarahSedasi();
        $listRuangPemulihan = $this->masterModel->listRuangPemulihan();
        $listScorePadss = $this->masterModel->listScorePadss();
        $listKeluarNyeri = $this->masterModel->listKeluarNyeri();
        $listPemulihanRisiko = $this->masterModel->listPemulihanRisiko();
        $historyPemulihanSedasi = $this->pengkajianAwalModel->historyPemulihanSedasi($getNomr['NORM']);
        
        $data = array(
            'id_sedasi_pemulihan' => $id_sedasi_pemulihan,
            'getNomr' => $getNomr,
            'listPerawat' => $listPerawat,
            'listSkalaNyeriSedasi' => $listSkalaNyeriSedasi,
            'listRisikoJatuhSedasi' => $listRisikoJatuhSedasi,
            'listKesadaranSedasi' => $listKesadaranSedasi,
            'listPernapasanSedasi' => $listPernapasanSedasi,
            'listNyeriSedasi' => $listNyeriSedasi,
            'listFrekuensiNapasSedasi' => $listFrekuensiNapasSedasi,
            'listFrekuensiNadiSedasi' => $listFrekuensiNadiSedasi,
            'listTekananDarahSedasi' => $listTekananDarahSedasi,
            'listRuangPemulihan' => $listRuangPemulihan,
            'listScorePadss' => $listScorePadss,
            'listKeluarNyeri' => $listKeluarNyeri,
            'listPemulihanRisiko' => $listPemulihanRisiko,
            'historyPemulihanSedasi' => $historyPemulihanSedasi,
            'getPengkajian' => $getPengkajian,
            'category' => 1
        );

        $this->load->view('Pengkajian/sedasi/sedasiPemulihan/index', $data);
    }

    public function indexRawatInap()
    {
        $nokun = $this->uri->segment(2);
        $id_sedasi_pemulihan = $this->uri->segment(3);
        $getPengkajian = $this->pengkajianAwalModel->historyDetailPemulihanSedasi($id_sedasi_pemulihan);

        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listPerawat = $this->masterModel->listPerawat();
        $listSkalaNyeriSedasi = $this->masterModel->listSkalaNyeriSedasi();
        $listRisikoJatuhSedasi = $this->masterModel->listRisikoJatuhSedasi();
        $listKesadaranSedasi = $this->masterModel->listKesadaranSedasi();
        $listPernapasanSedasi = $this->masterModel->listPernapasanSedasi();
        $listNyeriSedasi = $this->masterModel->listNyeriSedasi();
        $listFrekuensiNapasSedasi = $this->masterModel->listFrekuensiNapasSedasi();
        $listFrekuensiNadiSedasi = $this->masterModel->listFrekuensiNadiSedasi();
        $listTekananDarahSedasi = $this->masterModel->listTekananDarahSedasi();
        $listRuangPemulihan = $this->masterModel->listRuangPemulihan();
        $listScorePadss = $this->masterModel->listScorePadss();
        $listKeluarNyeri = $this->masterModel->listKeluarNyeri();
        $listPemulihanRisiko = $this->masterModel->listPemulihanRisiko();
        $historyPemulihanSedasi = $this->pengkajianAwalModel->historyPemulihanSedasi($getNomr['NORM']);
        
        $data = array(
            'id_sedasi_pemulihan' => $id_sedasi_pemulihan,
            'getNomr' => $getNomr,
            'listPerawat' => $listPerawat,
            'listSkalaNyeriSedasi' => $listSkalaNyeriSedasi,
            'listRisikoJatuhSedasi' => $listRisikoJatuhSedasi,
            'listKesadaranSedasi' => $listKesadaranSedasi,
            'listPernapasanSedasi' => $listPernapasanSedasi,
            'listNyeriSedasi' => $listNyeriSedasi,
            'listFrekuensiNapasSedasi' => $listFrekuensiNapasSedasi,
            'listFrekuensiNadiSedasi' => $listFrekuensiNadiSedasi,
            'listTekananDarahSedasi' => $listTekananDarahSedasi,
            'listRuangPemulihan' => $listRuangPemulihan,
            'listScorePadss' => $listScorePadss,
            'listKeluarNyeri' => $listKeluarNyeri,
            'listPemulihanRisiko' => $listPemulihanRisiko,
            'historyPemulihanSedasi' => $historyPemulihanSedasi,
            'getPengkajian' => $getPengkajian,
            'category' => 2
        );

        $this->load->view('Pengkajian/sedasi/sedasiPemulihan/index', $data);
    }

    public function action($param){
      if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
        if($param == 'tambah' || $param == 'ubah'){
          $kunjungan = $this->input->post("nokun");
          $pukulmasuk = $this->input->post("pukulmasuk");
          $anastesipengirim = $this->input->post("anastesipengirim");
          $anastesipenerima = $this->input->post("anastesipenerima");
          $tekanandarah = $this->input->post("tekanandarah");
          $nadi = $this->input->post("nadi");
          $frekuensinapas = $this->input->post("frekuensinapas");
          $suhu = $this->input->post("suhu");
          $kesadaran = $this->input->post("kesadaran");
          $pernapasan = $this->input->post("pernapasan");
          $dibantu = $this->input->post("dibantu");
          $nyeri = $this->input->post("nyeri");
          $skornyeri = $this->input->post("skornyeri");
          $risikojatuh = $this->input->post("risikojatuh");
          $penyulitintra = $this->input->post("penyulitintra");
          $instruksikhusus = $this->input->post("instruksikhusus");
          $frekuensinapasnow = $this->input->post("frekuensinapasnow");
          $frekuensinadinow = $this->input->post("frekuensinadinow");
          $tekanandarahnow = $this->input->post("tekanandarahnow");
          $skalanyeri = $this->input->post("skalanyeri");
          $saturasialdretescore = $this->input->post("saturasialdretescore");
          $pernapasanaldretescore = $this->input->post("pernapasanaldretescore");
          $sirkulasialdretescore = $this->input->post("sirkulasialdretescore");
          $aktifitasmotorikaldretescore = $this->input->post("aktifitasmotorikaldretescore");
          $kesadaranaldretescore = $this->input->post("kesadaranaldretescore");
          $pergerakanstewardscore = $this->input->post("pergerakanstewardscore");
          $pernapasanstewardscore = $this->input->post("pernapasanstewardscore");
          $kesadaranstewardscore = $this->input->post("kesadaranstewardscore");
          $tungkaibromagescore = $this->input->post("tungkaibromagescore");
          $ekstensibromagescore = $this->input->post("ekstensibromagescore");
          $lututbromagescore = $this->input->post("lututbromagescore");
          $pergelanganbromagescore = $this->input->post("pergelanganbromagescore");
          $jam1 = $this->input->post("jam1");
          $penerima1 = $this->input->post("penerima1");
          $jam2 = $this->input->post("jam2");
          $penerima2 = $this->input->post("penerima2");
          $jam3 = $this->input->post("jam3");
          $penerima3 = $this->input->post("penerima3");
          $jamkeluar = $this->input->post("jamkeluar");
          $ruangkeluarpemulihan = $this->input->post("ruangkeluarpemulihan");
          $ruanganlain = $this->input->post("ruanganlain");
          $scorepadss = $this->input->post("scorepadss");
          $scorepadssapp = $this->input->post("scorepadssapp");
          $keluarnyeri = $this->input->post("keluarnyeri");
          $pemulihanrisiko = $this->input->post("pemulihanrisiko");
          $pengelolaannyeri = $this->input->post("pengelolaannyeri");
          $penangannanmual = $this->input->post("penangannanmual");
          $antibiotik = $this->input->post("antibiotik");
          $obatlain = $this->input->post("obatlain");
          $infus = $this->input->post("infus");
          $nutrisi = $this->input->post("nutrisi");
          $vitalsetiap = $this->input->post("vitalsetiap");
          $vitalselama = $this->input->post("vitalselama");
          $tandavitallain = $this->input->post("tandavitallain");
          $hasilpemeriksaan1 = $this->input->post("hasilpemeriksaan1");
          $hasilpemeriksaan2 = $this->input->post("hasilpemeriksaan2");
          $hasilpemeriksaan3 = $this->input->post("hasilpemeriksaan3");
          $hasilpemeriksaan4 = $this->input->post("hasilpemeriksaan4");
          $id_sedasi_pemulihan = $this->input->post("id_sedasi_pemulihan");

          $data = array(
              'id' => isset($id_sedasi_pemulihan) ? $id_sedasi_pemulihan : "",
              'nokun' => $kunjungan,
              'pukul_masuk' => $pukulmasuk,
              'penata_anestesi_pengirim' => $anastesipengirim,
              'penata_anestesi_penerima' => $anastesipenerima,
              'tekanan_darah' => $tekanandarah,
              'nadi' => $nadi,
              'frekuensi_napas' => $frekuensinapas,
              'suhu' => $suhu,
              'kesadaran' => $kesadaran,
              'pernapasan' => $pernapasan,
              'pernapasan_dibantu' => $dibantu,
              'nyeri' => $nyeri,
              'nyeri_skor' => $skornyeri,
              'risiko_jatuh' => $risikojatuh,
              'penyulit_intra' => $penyulitintra,
              'instruksi_khusus' => $instruksikhusus,
              'skala_nyeri' => $skalanyeri,
              'saturasi_aldrete_score' => $saturasialdretescore,
              'pernapasan_aldrete_score' => $pernapasanaldretescore,
              'sirkulasi_aldrete_score' => $sirkulasialdretescore,
              'aktifitas_aldrete_score' => $aktifitasmotorikaldretescore,
              'kesadaran_aldrete_score' => $kesadaranaldretescore,
              'pergerakan_steward_score' => $pergerakanstewardscore,
              'pernapasan_steward_score' => $pernapasanstewardscore,
              'kesadaran_steward_score' => $kesadaranstewardscore,
              'tungkai_bromage_score' => $tungkaibromagescore,
              'ekstensi_bromage_score' => $ekstensibromagescore,
              'lutut_bromage_score' => $lututbromagescore,
              'kaki_bromage_score' => $pergelanganbromagescore,
              'jam1' => $jam1,
              'penerima1' => $penerima1,
              'jam2' => $jam2,
              'penerima2' => $penerima2,
              'jam3' => $jam3,
              'penerima3' => $penerima3,
              'pukul_keluar' => $jamkeluar,
              'ke_ruang' => $ruangkeluarpemulihan,
              'ke_ruang_lain' => $ruanganlain,
              'score_padss' => $scorepadss,
              'score_padss_app' => $scorepadssapp,
              'keluar_nyeri' => $keluarnyeri,
              'pemulihan_risiko' => $pemulihanrisiko,
              'pengelolaan_nyeri' => $pengelolaannyeri,
              'penanganan_mual' => $penangannanmual,
              'antibiotik' => $antibiotik,
              'obat_lain' => $obatlain,
              'infus' => $infus,
              'nutrisi' => $nutrisi,
              'vital_setiap' => $vitalsetiap,
              'vital_selama' => $vitalselama,
              'vital_lain' => $tandavitallain,
              'hasil_pemeriksaan1' => $hasilpemeriksaan1,
              'hasil_pemeriksaan2' => $hasilpemeriksaan2,
              'hasil_pemeriksaan3' => $hasilpemeriksaan3,
              'hasil_pemeriksaan4' => $hasilpemeriksaan4,
          );

          $this->db->trans_begin();
        
          if (!empty($id_sedasi_pemulihan)) {
            $this->db->replace('keperawatan.tb_kamar_pemulihan_sedasi', $data);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          }else{
            $this->db->insert('keperawatan.tb_kamar_pemulihan_sedasi', $data);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }
          
        }
      }
    }
}
?>