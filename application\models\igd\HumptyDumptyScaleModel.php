<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class HumptyDumptyScaleModel extends CI_Model {

  public function simpanHumptyDumptyScale($data)
  {
    $this->db->trans_begin();
    $this->db->insert('keperawatan.tb_penilaian_humptyDumpty_igd', $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function tbl_HumptyDumptyScale($nomr)
  {
    $query = $this->db->query(
      "SELECT tphi.`*`, master.getNamaLengkapPegawai(ap.NIP)OLEH
      ,(dv1.nilai + dv2.nilai + dv3.nilai + dv4.nilai + dv5.nilai + dv6.nilai + dv7.nilai) TOTALNILAI
      , tppj.id IDPENCEGAHAN
      FROM keperawatan.tb_penilaian_humptyDumpty_igd tphi
      LEFT JOIN db_master.variabel dv1 ON dv1.id_variabel = tphi.umur
      LEFT JOIN db_master.variabel dv2 ON dv2.id_variabel = tphi.jenis_kelamin
      LEFT JOIN db_master.variabel dv3 ON dv3.id_variabel = tphi.diagnosa
      LEFT JOIN db_master.variabel dv4 ON dv4.id_variabel = tphi.gangguan_kognitif
      LEFT JOIN db_master.variabel dv5 ON dv5.id_variabel = tphi.faktor_lingkungan
      LEFT JOIN db_master.variabel dv6 ON dv6.id_variabel = tphi.respon_terhadap_operasi
      LEFT JOIN db_master.variabel dv7 ON dv7.id_variabel = tphi.penggunaan_obat
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tphi.nokun
      LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
      LEFT JOIN aplikasi.pengguna ap ON ap.ID = tphi.oleh
      LEFT JOIN keperawatan.tb_pencegahan_pasien_jatuh tppj ON tppj.id_pengkajian = tphi.id AND tppj.jenis_pengkajian = 3

      WHERE pp.NORM = '$nomr'
      ORDER BY tphi.id DESC"
    );
    return $query;
  }

  public function simpanfrmintervensiRendah($data)
  {
    $this->db->trans_begin();
    $this->db->insert('keperawatan.tb_pencegahan_pasien_jatuh', $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function simpanfrmintervensiTinggi($data)
  {
    $this->db->trans_begin();
    $this->db->insert('keperawatan.tb_pencegahan_pasien_jatuh', $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function get_HumptyDumptyScale($id)
  {
    $query = $this->db->query(
      "SELECT tphi.`*`, master.getNamaLengkapPegawai(ap.NIP)OLEH
      ,(dv1.nilai + dv2.nilai + dv3.nilai + dv4.nilai + dv5.nilai + dv6.nilai + dv7.nilai) TOTALNILAI
      ,HOUR(TIMEDIFF(NOW(),tphi.tanggal)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tphi.tanggal))<=24,1,0) STATUS_EDIT

      FROM keperawatan.tb_penilaian_humptyDumpty_igd tphi
      LEFT JOIN db_master.variabel dv1 ON dv1.id_variabel = tphi.umur
      LEFT JOIN db_master.variabel dv2 ON dv2.id_variabel = tphi.jenis_kelamin
      LEFT JOIN db_master.variabel dv3 ON dv3.id_variabel = tphi.diagnosa
      LEFT JOIN db_master.variabel dv4 ON dv4.id_variabel = tphi.gangguan_kognitif
      LEFT JOIN db_master.variabel dv5 ON dv5.id_variabel = tphi.faktor_lingkungan
      LEFT JOIN db_master.variabel dv6 ON dv6.id_variabel = tphi.respon_terhadap_operasi
      LEFT JOIN db_master.variabel dv7 ON dv7.id_variabel = tphi.penggunaan_obat
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tphi.nokun
      LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
      LEFT JOIN aplikasi.pengguna ap ON ap.ID = tphi.oleh

      WHERE tphi.id = '$id'"
    );
    return $query->row_array();
  }

  public function get_pencegahanPasienJatuh($id)
  {
    $query = $this->db->query(
      "SELECT tppj.`*`
      ,HOUR(TIMEDIFF(NOW(),tppj.created_at)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tppj.created_at))<=24,1,0) STATUS_EDIT
      FROM keperawatan.tb_pencegahan_pasien_jatuh tppj
      WHERE tppj.id_pengkajian = '$id' AND tppj.jenis_pengkajian = 3"
    );
    return $query->row_array();
  }

  public function updateHumptyDumptyScale($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('keperawatan.tb_penilaian_humptyDumpty_igd', $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updatefrmintervensiRendah($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('keperawatan.tb_pencegahan_pasien_jatuh', $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updatefrmintervensiTinggi($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('keperawatan.tb_pencegahan_pasien_jatuh', $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}

/* End of file HumptyDumptyScaleModel.php */
/* Location: ./application/models/igd/HumptyDumptyScaleModel.php */
