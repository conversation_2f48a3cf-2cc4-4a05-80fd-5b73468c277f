<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SpinalEpiModel extends MY_Model {

  public function simpanInformedConcent($data)
  {
    $this->db->insert('db_informed_consent.tb_informed_consent', $data);
    return $this->db->insert_id();
  }

  public function simpanSpinalEpi($data)
  {
    $this->db->insert('db_informed_consent.tb_form_ptk_spinalepi', $data);
  }

  public function simpanPersetujuanTidakanKedokteranSpinalEpi($data)
  {
    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran', $data);
  }

  public function listHistoryInformedConsentSpinalEpi($nomr)
  {
    $query = $this->db->query("SELECT pp.NORM, tic.id, tic.nokun,
                              master.getNamaLengkapPegawai(ap.NIP) OLEH,
                              master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA,
                              tic.created_at tanggal
                              FROM db_informed_consent.tb_form_ptk_spinalepi fps
                              LEFT JOIN db_informed_consent.tb_informed_consent tic ON tic.id = fps.id_informed_consent
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE pp.NORM = '$nomr' AND tic.`status` = 1
                              ");
    return $query;
  }

  public function getSpinalEpi($id)
  {
    $query = $this->db->query("SELECT tic.*, fps.*, ptk.*, tic.id idtic, fps.id idSpinalEpi
                              , master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA, tic.created_at tanggal
                              FROM db_informed_consent.tb_form_ptk_spinalepi fps
                              LEFT JOIN db_informed_consent.tb_informed_consent tic ON tic.id = fps.id_informed_consent
                              LEFT JOIN db_informed_consent.tb_persetujuan_tindakan_kedokteran ptk ON ptk.id_informed_consent = fps.id_informed_consent
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE tic.id = $id AND tic.`status` = 1
                              ");
    return $query->row_array();
  }

  public function updateInformedConcent($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('db_informed_consent.tb_informed_consent', $data);
  }

  public function updateSpinalEpi($data,$idSpinalEpi)
  {
    $this->db->where('id', $idSpinalEpi);
    $this->db->update('db_informed_consent.tb_form_ptk_spinalepi', $data);
  }

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
