<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Belanja extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('keuanganModel','masterModel'));
  }

  public function index()
  {
    $datRealisasi = $this->keuanganModel->dataRealisasi();
    $ruanganRealisasi = $this->keuanganModel->ruanganRealisasi();

    $listTableRkakl = $this->keuanganModel->listTableRkakl();

    $data = array(
      'title' => 'Halaman Realisasi Sistem Informasi Manajemen Anggaran',
      'isi'   => 'Keuangan/realisasi/index',
      'datRealisasi'      => $datRealisasi,
      'ruanganRealisasi'      => $ruanganRealisasi,
      'listTableRkakl'      => $listTableRkakl,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function simpanRealisasi()
  {
    $id = $this->input->post("uraian");

    $searchMak = $this->keuanganModel->getIdMakNew($id);
    $jmlrkakl    = $searchMak['PAGU_TRANSAKSI'];
    
    // $volume = $this->input->post("volume");
    $biayarealisai = $this->input->post("biaya");
    $sisa_saldo = $jmlrkakl - $biayarealisai;
    // $jmlvlmrkakl = $searchMak['VOLUME'];
  
    // $hasilvolume = $jmlvlmrkakl - $volume;
    $hasiljumlah = $jmlrkakl - $biayarealisai;

    $data = array(
      'ID_RKAKL'      => $id,
      'TOTAL_BAYAR' => $biayarealisai,
      'SISA_SALDO'  => $sisa_saldo,
      'RUANGAN'     => $this->input->post("ruangan"),
      'VOLUME'      => $this->input->post("volume"),
      'TANGGAL'     => $this->input->post("tanggal"),
      'OLEH'        => $this->session->userdata('id'),
      'STATUS'      => '1',
    );
    // print_r($data);exit();
    $simpan_realiasi = $this->keuanganModel->insertRealisasi($data);

    $data2 = array(
      // 'VOLUME' => $hasilvolume,
      'PAGU_TRANSAKSI' => $hasiljumlah,
    );

    $this->keuanganModel->updateRkaklJmlVlm($id, $data2);

  }

  public function periode()
  {
    $periode = $this->input->post("periode");
    $year = date('Y', strtotime($periode));
    $uraianRkakl = $this->keuanganModel->getUraianRkakl($year);

    foreach($uraianRkakl as $uRkakl):
      echo '<option value="'.$uRkakl["ID"].'">'.$uRkakl["URAIAN"]."-".$uRkakl["MAK"].'</option>';
    endforeach;

  }

  public function rModalPilih()
  {
    $mak = $this->input->post("mak");

    $ruanganRealisasi = $this->keuanganModel->getRuanganRealisasi($mak);

    
    foreach($ruanganRealisasi as $rr):
      echo '<option value="'.$rr["ID_INSTALASI"].'">'.$rr["DESKRIPSI"].'</option>';
    endforeach;
  }

  public function vModalPilih()
  {
    $mak = $this->input->post("mak");

    $pilih = $this->keuanganModel->getMakRealisasi($mak);

    echo '<input type="number" class="form-control" name="volume" value="'.$pilih['VOLUME'].'" readonly>';

  }

  public function pAwalModalPilih()
  {
    $mak = $this->input->post("mak");

    $pilih = $this->keuanganModel->getMakRealisasi($mak);

    echo '<input type="text" class="form-control" value="Rp '. number_format((double)$pilih['PAGU_AWAL'],2,",",".").'" readonly>';


  }

  public function tModalPilih()
  {
    $mak = $this->input->post("mak");

    $pilih = $this->keuanganModel->getMakRealisasi($mak);

    echo '<input type="text" class="form-control" value="Rp '. number_format((double)$pilih['PAGU_TRANSAKSI'],2,",",".").'" readonly>';


  }

}

/* End of file Realisasi.php */
/* Location: ./application/controllers/keuangan/Realisasi.php */