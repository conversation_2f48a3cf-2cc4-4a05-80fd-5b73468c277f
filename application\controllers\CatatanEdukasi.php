<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class CatatanEdukasi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'CatatanEdukasiModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(3);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $data = array(
      'getNomr' => $getNomr,
      'nokun' => $nokun,
      'listKebutuhanEdukasi' => $this->CatatanEdukasiModel->listKebutuhanEdukasi(),
      'gembira' => $this->masterModel->referensi(1062),
      'sedih' => $this->masterModel->referensi(1063),
      'motivasi' => $this->masterModel->referensi(1064),
      'kesediaanPasien' => $this->masterModel->referensi(1065),
      'history' => $this->CatatanEdukasiModel->history($nomr),
      'medis' => $this->masterModel->referensi(1066),
      'keperawatan' => $this->masterModel->referensi(1067),
      'obat' => $this->masterModel->referensi(1068),
      'peralatanMedis' => $this->masterModel->referensi(1069),
      'nutrisi' => $this->masterModel->referensi(1070),
      'makananLuar' => $this->masterModel->referensi(1071),
      'manajemenNyeri' => $this->masterModel->referensi(1072),
      'aspekPsikologis' => $this->masterModel->referensi(1073),
      'teknikRehabilitasi' => $this->masterModel->referensi(1074),
      'perawatanRIRA' => $this->masterModel->referensi(1075),
      'perawatanRIIM' => $this->masterModel->referensi(1076),
      'perawatanKemo' => $this->masterModel->referensi(1077),
      'penundaan' => $this->masterModel->referensi(1078),
      'alasanPenundaan' => $this->masterModel->referensi(1079),
      'alternatifPelayanan' => $this->masterModel->referensi(1080),
      'metode' => $this->masterModel->referensi(1081),
      'evaluasi' => $this->masterModel->referensi(1082),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/catatanEdukasi/index', $data);
  }

  public function simpan()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    //Simpan ke Catatan Edukasi
    $data = array(
      'nokun' => $post['nokun'],
      'gembira' => $post['gembira'],
      'sedih' => $post['sedih'],
      'motivasi' => $post['motivasi'],
      'kesediaan_pasien' => $post['kesediaan_pasien'],
      'edukasi' => $post['edukasi'],
      'paraf_pasien_keluarga' => file_get_contents($post['paraf_pasien_keluarga']),
      'nama_keluarga' => $post['nama_keluarga'],
      'paraf_petugas' => file_get_contents($post['paraf_petugas']),
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
      'waktu' => date('Y-m-d H:i:s'),
    );
    // echo '<pre>';print_r($data);exit();
    $idCatatanEdukasi = $this->CatatanEdukasiModel->simpanCatatanEdukasi($data);

    // Simpan ke Detail Catatan Edukasi
    $data = array();
    $i = 0;
    if (isset($post['detail_edukasi'])) {
      foreach ($post['detail_edukasi'] as $dataDetail) {
        if ($post['detail_edukasi'][$i] != null) {
          array_push(
            $data, array(
              'id_catatan_edukasi' => $idCatatanEdukasi,
              'detail_edukasi' => $post['detail_edukasi'][$i],
              'keterangan' => $post['keterangan'][$i] ?? null,
              'metode' => $post['metode'][$i] ?? null,
              'evaluasi' => $post['evaluasi'][$i] ?? null,
              'durasi' => $post['durasi'][$i] ?? null,
              'oleh' => $this->session->userdata('id'),
              'status' => 1,
              'waktu' => date('Y-m-d H:i:s'),
            )
          );
        }
        $i++;
      }
    }
    // echo '<pre>';print_r($data);exit();
    $this->CatatanEdukasiModel->simpanDetailCatatanEdukasi($data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detail()
  {
    $id = $this->input->post('id');
    $data = array(
      'edukasi' => $this->input->post('edukasi'),
      'detail' => $this->CatatanEdukasiModel->detail($id),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/catatanEdukasi/history/modal', $data);
  }

  public function batalCatatanEdukasi()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $data = array(
      'status' => 0,
    );
    $id = array('id' => $post['id']);
    /*echo '<pre>';print_r($id);exit();*/
    $this->CatatanEdukasiModel->ubah($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}