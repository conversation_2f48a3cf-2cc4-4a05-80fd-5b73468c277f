<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>rdek extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    $hKardekObat = $this->pengkajianAwalModel->historyKardekObat($nomr);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'hKardekObat' => $hKardekObat,
      'getNomr' => $getNomr,
    );

    $this->load->view('Pengkajian/igd/kardek/index', $data);
  }

}