<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CatatanModel extends CI_Model
{
    public function getListDoctor()
    {
        $this->db->select('mp.nip, master.getNamaLengkapPegawai(mp.NIP) as nama_dokter');
        $this->db->from('master.dokter md');
        $this->db->join('master.pegawai mp', 'md.NIP = mp.NIP');
        $this->db->join('master.referensi mr', 'mr.ID = mp.SMF AND mr.JENIS = 26');
        $this->db->join('master.dokter_ruangan mdr', 'mdr.DOKTER = md.ID');
        $this->db->where('md.STATUS', 1);
        $this->db->where('mdr.RUANGAN', '105120101');
        $this->db->where_in('mp.SMF', [34, 51]);
        $this->db->group_by('md.ID');
        $this->db->order_by('mp.NAMA', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function getListPerawat()
    {
        $this->db->select('mp.nip, master.getNamaLengkapPegawai(mp.NIP) as nama_perawat');
        $this->db->from('master.perawat_ruangan pr');
        $this->db->join('master.perawat p', 'p.ID = pr.PERAWAT', 'left');
        $this->db->join('master.pegawai mp', 'mp.NIP = p.NIP', 'left');
        $this->db->where('pr.RUANGAN', '105120101'); // RUANG RADIOTERAPI
        $this->db->where('pr.STATUS', 1);
        $this->db->where('p.STATUS', 1);
        $this->db->where('mp.PROFESI', 6); // PROFESI = 6 (PERAWAT)
        $this->db->group_by('p.ID');
        $this->db->order_by('mp.NAMA', 'ASC');
        $query = $this->db->get();
        return $query->result_array();
    }


    public function getListRadiografer()
    {
        $this->db->select('mp.nip, master.getNamaLengkapPegawai(mp.NIP) as nama_radiografer');
        $this->db->from('master.perawat_ruangan pr');
        $this->db->join('master.perawat p', 'p.ID = pr.PERAWAT', 'left');
        $this->db->join('master.pegawai mp', 'mp.NIP = p.NIP', 'left');  // Keep using mp for pegawai
        $this->db->where('pr.RUANGAN', '105120101'); // RUANG RADIOTERAPI
        $this->db->where('pr.STATUS', 1);
        $this->db->where('p.STATUS', 1);
        $this->db->where('mp.PROFESI', 8); // 8 for RADIOGRAFER
        $this->db->group_by('p.ID');
        $this->db->order_by('mp.NAMA', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function getListFisikawan()
    {
        $this->db->select('sr.*, master.getNamaLengkapPegawai(peg.NIP) nama_lengkap, ap.ID AS ID_PENGGUNA, peg.nip
        ');
        $this->db->from('master.staff_ruangan sr');
        $this->db->join('master.staff st', 'st.ID = sr.STAFF', 'left');
        $this->db->join('master.pegawai peg', 'peg.NIP = st.NIP', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.NIP = st.NIP', 'left');
        $this->db->where('sr.RUANGAN', '105120101'); // RUANG RADIOTERAPI
        $this->db->where('sr.STATUS', 1);
        $this->db->where('st.STATUS', 1);
        $this->db->where('peg.PROFESI', 17); // PROFESI FISIKAWAN MEDIS
        $this->db->group_by('st.ID');
        $this->db->order_by('peg.NAMA', 'ASC');

        $query = $this->db->get();
        $result = $query->result_array();
        return $result;
    }

    public function getCatatanData($start, $length, $search, $order_column, $order_dir, $nomr)
    {
        // Base query for counting the total records
        $this->db->select('mcr.id');
        $this->db->from('medis.tb_catatanradioterapi mcr');
        $this->db->where('mcr.STATUS !=', 0);
        if ($nomr) {
            $this->db->where('mcr.nomr', $nomr);
        }
        $total_count = $this->db->count_all_results(); // Get total count of records

        // Reset query and apply filters for filtered results
        $this->db->select('mcr.id, 
                        CONCAT(master.getNamaLengkapPegawai(mp.NIP), " (", mr.DESKRIPSI, ")") as tujuan,
                        apc.NAMA as oleh_catatan,
                        mcr.created_by, 
                        mcr.catatan, 
                        mcr.created_at as catatan_time, 
                        COUNT(mjr.id) as jumlah_jawaban');
        $this->db->from('medis.tb_catatanradioterapi mcr');
        $this->db->join('medis.tb_jawabanradioterapi mjr', 'mjr.id_catatan = mcr.id', 'left');
        $this->db->join('master.pegawai mp', 'mp.NIP = mcr.id_pegawai', 'left');
        $this->db->join('aplikasi.pengguna apc', 'apc.ID = mcr.created_by', 'left');
        $this->db->join('master.referensi mr', 'mr.ID = mcr.id_profesi AND mr.JENIS = 36', 'left'); // Corrected the alias here
        $this->db->where('mcr.STATUS !=', 0);
        if ($nomr) {
            $this->db->where('mcr.nomr', $nomr);
        }

        // Apply search filter if provided
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('mcr.catatan', $search);
            $this->db->or_like('apc.NAMA', $search);
            $this->db->or_like('master.getNamaLengkapPegawai(mp.NIP)', $search);
            $this->db->or_like('mr.DESKRIPSI', $search);
            $this->db->group_end();
        }

        // Get the count of filtered results
        $this->db->group_by('mcr.id');
        $filtered_count_query = clone $this->db; // Clone the query to get filtered count before applying pagination
        $filtered_count = $filtered_count_query->count_all_results();

        // Apply ordering, pagination
        $this->db->order_by($order_column, $order_dir);
        $this->db->limit($length, $start);

        $query = $this->db->get();
        $data = $query->result_array(); // Fetch paginated and ordered data

        // Return total, filtered counts, and data
        return [
            'total_count' => $total_count,
            'filtered_count' => $filtered_count,
            'data' => $data
        ];
    }


    public function getCatatanDataModal($start, $length, $search, $order_column, $order_dir, $user_id, $profesi_id, $status)
    {
        // 1. Menghitung Total Record dengan Kondisi Awal
        $this->db->select('mcr.id');
        $this->db->from('medis.tb_catatanradioterapi mcr');
        $this->db->join('aplikasi.pengguna apc', 'apc.NIP = mcr.id_pegawai', 'left');
        $this->db->join('medis.tb_jawabanradioterapi mjr', 'mjr.id_catatan = mcr.id', 'left');

        // Grup kondisi dengan (A OR B)
        $this->db->group_start()
            ->group_start()
            ->where('mcr.id_pegawai IS NOT NULL', null, false)
            ->where('apc.ID', $user_id)
            ->where('mcr.status', $status)  // Kondisi ketika apc.ID sama dengan user_id
            ->group_end()
            ->or_group_start()
            ->where('mcr.id_pegawai IS NULL', null, false)
            ->where('mcr.id_profesi', $profesi_id)
            ->where('mcr.status', $status)
            ->group_end()
            ->or_group_start()
            ->where('mcr.created_by', $user_id)
            ->where('mjr.created_by!=', $user_id)
            ->where('mjr.status', $status)  // Kondisi ketika salah satu mjr.status = 1
            ->group_end()
            ->group_end();
        // Grup berdasarkan ID catatan
        $this->db->group_by('mcr.id');
        // Hitung total record
        $total_count = $this->db->count_all_results();

        // 2. Mengambil Data dengan Kondisi dan Filter Tambahan
        $this->db->select('
            mcr.id, 
            CONCAT(master.getNamaLengkapPegawai(mp.NIP), " (", mr.DESKRIPSI, ")") as tujuan,
            ap.NAMA as oleh_catatan,
            mcr.created_by,
            mpas.norm,
            mpas.NAMA as nama_pasien, 
            mcr.catatan, 
            mcr.created_at as catatan_time, 
            (
                SELECT COUNT(*) 
                FROM medis.tb_jawabanradioterapi mjr_sub
                WHERE mjr_sub.id_catatan = mcr.id
            ) as jumlah_jawaban
        ');
        $this->db->from('medis.tb_catatanradioterapi mcr');
        $this->db->join('master.pasien mpas', 'mpas.NORM = mcr.nomr');
        $this->db->join('medis.tb_jawabanradioterapi mjr', 'mjr.id_catatan = mcr.id', 'left');
        $this->db->join('master.pegawai mp', 'mp.NIP = mcr.id_pegawai', 'left');
        $this->db->join('aplikasi.pengguna apc', 'apc.NIP = mcr.id_pegawai', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = mcr.created_by', 'left');
        $this->db->join('master.referensi mr', 'mr.ID = mcr.id_profesi AND mr.JENIS = 36', 'left');

        // Grup kondisi dengan (A OR B)
        $this->db->group_start()
            ->group_start()
            ->where('mcr.id_pegawai IS NOT NULL', null, false)
            ->where('apc.ID', $user_id)
            ->where('mcr.status', $status)  // Kondisi ketika apc.ID sama dengan user_id
            ->group_end()
            ->or_group_start()
            ->where('mcr.id_pegawai IS NULL', null, false)
            ->where('mcr.id_profesi', $profesi_id)
            ->where('mcr.status', $status)
            ->group_end()
            ->or_group_start()
            ->where('mcr.created_by', $user_id)
            ->where('mjr.created_by!=', $user_id)
            ->where('mjr.status', $status)  // Kondisi ketika salah satu mjr.status = 1
            ->group_end()
            ->group_end();

        // Terapkan filter pencarian jika ada
        if (!empty($search)) {
            $this->db->group_start()
                ->like('mcr.catatan', $search)
                ->or_like('apc.NAMA', $search)
                ->or_like('mpas.NAMA', $search)
                ->or_like('master.getNamaLengkapPegawai(mp.NIP)', $search)
                ->or_like('mr.DESKRIPSI', $search)
                ->group_end();
        }

        // Grup berdasarkan ID catatan
        $this->db->group_by('mcr.id');

        // Clone query untuk menghitung filtered_count sebelum menambahkan ORDER BY dan LIMIT
        $filtered_count_query = clone $this->db;
        $filtered_count = $filtered_count_query->count_all_results();

        // Terapkan ORDER BY dan LIMIT untuk pagination
        $this->db->order_by($order_column, $order_dir);
        $this->db->limit($length, $start);

        // Eksekusi query dan ambil data
        $query = $this->db->get();
        $data = $query->result_array();

        // Kembalikan hasil
        return [
            'total_count' => $total_count,
            'filtered_count' => $filtered_count,
            'data' => $data
        ];
    }

    public function saveCatatan($data)
    {
        return $this->db->insert('medis.tb_catatanradioterapi', $data); // Assuming your table is 'catatan_radioterapi'
    }

    public function saveJawaban($data)
    {
        return $this->db->insert('medis.tb_jawabanradioterapi', $data); // Assuming your table is 'catatan_radioterapi'
    }

    public function getJawabanByCatatan($id_catatan)
    {
        $this->db->select('mjr.jawaban, 
                           apj.NAMA as oleh_jawaban, 
                           mjr.created_at as jawaban_time,
                           mjr.file_path, mjr.file_name');
        $this->db->from('medis.tb_jawabanradioterapi mjr');
        $this->db->join('aplikasi.pengguna apj', 'apj.ID = mjr.created_by', 'left');
        $this->db->where('mjr.id_catatan', $id_catatan);
        $this->db->where('mjr.status!=', 0);
        $this->db->order_by('mjr.created_at', 'ASC'); // To show jawaban in chronological order
        $query = $this->db->get();
        return $query->result_array();
    }

    public function updateCatatan($id_catatan, $data)
    {
        $this->db->where('id', $id_catatan); // Assuming 'id' is the primary key for catatan
        return $this->db->update('medis.tb_catatanradioterapi', $data); // Replace 'your_table_name' with the actual table name
    }
    public function updateJawaban($id_catatan, $data)
    {
        $this->db->where('id_catatan', $id_catatan); // Assuming 'id' is the primary key for catatan
        return $this->db->update('medis.tb_jawabanradioterapi', $data); // Replace 'your_table_name' with the actual table name
    }

    public function getCatatanById($id_catatan)
    {
        $this->db->select('
        mcr.*, 
        apc.ID as pengguna_id,
        ap.NAMA as created_by_nama,
        mr.DESKRIPSI as profesi_deskripsi
    ');
        $this->db->from('medis.tb_catatanradioterapi mcr');
        $this->db->join('aplikasi.pengguna apc', 'apc.NIP = mcr.id_pegawai', 'left'); // Join untuk pengecekan apc.ID
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = mcr.created_by', 'left'); // Join untuk nama created_by
        $this->db->join('master.referensi mr', 'mr.ID = mcr.id_profesi AND mr.JENIS = 36', 'left'); // Join profesi deskripsi
        $this->db->where('mcr.id', $id_catatan);
        $this->db->group_by('mcr.id');

        return $this->db->get()->row_array(); // Mengembalikan satu hasil
    }
    public function checkUser($user_id = null, $profesi_id = null)
    {
        // Return false if profesi_id is not 8, 6, 11, or 17
        if (!in_array($profesi_id, [8, 6, 11, 17])) {
            return false;
        }

        if ($profesi_id == 11) {
            $this->db->join('master.dokter md', 'ap.NIP = md.NIP');
            $this->db->join('master.dokter_ruangan mdr', 'mdr.DOKTER = md.ID AND mdr.RUANGAN = 105120101 and mdr.STATUS = 1');
        } else if ($profesi_id == 8 || $profesi_id == 6) {
            $this->db->join('master.perawat mp', 'ap.NIP = mp.NIP');
            $this->db->join('master.perawat_ruangan mpr', 'mpr.PERAWAT = mp.ID AND mpr.RUANGAN = 105120101 and mpr.STATUS = 1');
        } else if ($profesi_id == 17) {
            $this->db->join('master.staff ms', 'ms.NIP = ap.NIP');
            $this->db->join('master.staff_ruangan msr', 'msr.STAFF = ms.ID and msr.RUANGAN = 105120101 and msr.status = 1');
        }

        $this->db->where('ap.ID', $user_id);
        $this->db->from('aplikasi.pengguna ap');
        $query = $this->db->get();

        // If any row is returned, then profession and join condition are met
        if ($query->num_rows() > 0) {
            return true;
        }
        return false;
    }
}
