<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RDDSitologiModel extends MY_Model
{
    protected $_table_name = 'medis.tb_rdd_sitologi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }

    public function simpan($data)
    {
        $this->db->insert_batch($this->_table_name, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_rdd_sitologi.id_rdd', $id);
        $this->db->update($this->_table_name, $data);
    }

    public function penunjang($id)
    {
        $this->db->select(
            "rs.id_sitologi, DATE_FORMAT(hps.TANGGAL_LAB, '%d-%m-%Y, %H.%i.%s') tanggal, hps.NOMOR_LAB no_lab_sitologi"
        );
        $this->db->from('medis.tb_rdd_sitologi rs');
        $this->db->join('layanan.hasil_pa_sitologi hps', 'hps.id = rs.id_sitologi', 'left');
        $this->db->where('rs.id_rdd', $id);
        $this->db->where('rs.status', 1);
        $this->db->order_by('tanggal');
        return $this->db->get();
    }
}

// End of file RDDSitologiModel.php
// Location: ./application/models/emr/deteksiDini/RDDSitologiModel.php