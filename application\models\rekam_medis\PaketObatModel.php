<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PaketObatModel extends MY_Model{
    protected $_table_name = 'master.paket_obat';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'ASC';

    function __construct(){
        parent::__construct();
    }

    function table_query(){
        $this->db->select('*');
        $this->db->from('master.paket_obat po');
        $this->db->where('po.STATUS != 0');
        $this->db->order_by('ID','ASC');
        
        if ($this->input->post('ruangan')) {
            $this->db->where('po.RUANGAN', $this->input->post('ruangan'));
        }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }
}
