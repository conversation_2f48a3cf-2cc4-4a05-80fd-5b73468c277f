<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MCUDDModel extends MY_Model
{
    protected $_table_name = 'medis.tb_mcudd';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s wajib diisi',
                'numeric' => '%s wajib angka',
            )
        ),

        'tanggal' => array(
            'field' => 'tanggal',
            'label' => 'Tanggal',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s wajib diisi',
            )
        ),
    );

    public function __construct()
    {
        parent::__construct();
    }

    public function simpan($data)
    {
        $this->db->insert('medis.tb_mcudd', $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_mcudd.id', $id);
        $this->db->update('medis.tb_mcudd', $data);
    }

    public function jumlah($nomr)
    {
        $this->db->select('m.id');
        $this->db->from('medis.tb_mcudd m');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = m.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = m.oleh', 'left');
        $this->db->where('m.status', 1);
        $this->db->where('pp.NORM', $nomr);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function history($nomr)
    {
        $this->db->select('m.id, m.tanggal, m.no_mcu, master.getNamaLengkapPegawai(ap.NIP) pengirim, m.status');
        $this->db->from('medis.tb_mcudd m');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = m.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = m.oleh', 'left');
        $this->db->where('pp.NORM', $nomr);
        $this->db->order_by('m.id', 'desc');
        return $this->db->get();
    }

    public function detail($id)
    {
        $this->db->select('m.id, m.nokun, m.tanggal, m.no_mcu, m.penilaian, m.kesimpulan, m.saran, m.catatan');
        $this->db->from('medis.tb_mcudd m');
        $this->db->where('m.id', $id);
        $query = $this->db->get();
        return $query->row_array();
    }
}

/* End of file MCUDDModel.php */
/* Location: ./application/models/emr/deteksiDini/MCUDDModel.php */