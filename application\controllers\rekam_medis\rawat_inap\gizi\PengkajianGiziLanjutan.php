<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianGiziLanjutan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/gizi/PengkajianGiziLanjutanModel'));
        date_default_timezone_set("Asia/Bangkok");
    }

    public function index()
    {
        $nokun = $this->uri->segment(2);
        $id_gizi = $this->uri->segment(3);
        $getPengkajian = $this->PengkajianGiziLanjutanModel->getPengkajian($id_gizi);
        $idRefEmr = $this->input->post('id');

        $data = array(
            'nokun' => $nokun,
            'id_gizi' => $id_gizi,
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'getTbbb' => $this->PengkajianGiziLanjutanModel->getTbbb($this->uri->segment(2)),
            'getDiagnosa' => $this->PengkajianGiziLanjutanModel->getDiagnosa($this->uri->segment(2)),
            'getPersen' => $this->PengkajianGiziLanjutanModel->getPersen($this->uri->segment(2)),
            'asupanEnergi' => $this->masterModel->referensi(1202),
            'penurunanLemak' => $this->masterModel->referensi(1204),
            'penurunanOtot' => $this->masterModel->referensi(1205),
            'akumulasiCairan' => $this->masterModel->referensi(1206),
            'diagnosis' => $this->masterModel->referensi(1207),
            'jalur' => $this->masterModel->referensi(1208),
            'enteral' => $this->masterModel->referensi(1209),
            'fisik' => $this->masterModel->referensi(1226),
            'domainintake' => $this->masterModel->referensi(1227),
            'domainklinis' => $this->masterModel->referensi(1228),
            'domainbehavior' => $this->masterModel->referensi(1229),
            'dietintervensi' => $this->masterModel->referensi(1230),
            'getPengkajian' => $getPengkajian,
        );

        $this->load->view('rekam_medis/rawat_inap/gizi/pengkajianGiziLanjutan', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();

                $getID = !empty($post['id_gizi']) ? $post['id_gizi'] : $this->pengkajianAwalModel->getIdEmr();
                $dataTbBb = array(
                    'data_source'   => 20,
                    'ref'           => $getID,
                    'nomr'          => isset($post['nomr']) ? $post['nomr'] : "",
                    'nokun'         => $post['nokun'],
                    'jenis'         => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0,
                    'tb'            => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
                    'bb'            => isset($post['berat_badan']) ? $post['berat_badan'] : "",
                    'oleh'          => $this->session->userdata('id'),
                    'status'        => 1,
                );

                if (isset($post['diet_nd1']) == "on") {
                    $diet_nd1 = 4078;
                } else {
                    $diet_nd1 = '';
                }

                if (isset($post['diet_nd2']) == "on") {
                    $diet_nd2 = 4079;
                } else {
                    $diet_nd2 = '';
                }

                $dataPengkajianGiziLanjutan = array(
                    'id' => $getID,
                    'nokun' => $post['nokun'],
                    // 'berat_badan'           => $post['berat_badan'],
                    // 'tinggi_badan'          => $post['tinggi_badan'],
                    'imt'                   => $post['imt'],
                    'lila'                  => $post['lila'],
                    'tilut'                 => $post['tilut'],
                    'perubahan_bb'          => $post['perubahan_bb'],
                    'bbu'                   => $post['bbu'],
                    'tbu'                   => $post['tbu'],
                    'bbtb'                  => $post['bbtb'],
                    'diagnosis_penyakit'    => $post['diagnosis_penyakit'],
                    'riwayat_penyakit'      => $post['riwayat_penyakit'],
                    'hasil_lab'             => $post['hasil_lab'],
                    'fisik'                 => json_encode($post['fisik']),
                    'fisik_lain'            => $post['fisik_lain'],
                    'alergi_makan'          => $post['alergi_makan'],
                    'pantangan_makan'       => $post['pantangan_makan'],
                    'makanan_disukai'       => $post['makanan_disukai'],
                    'makan_tidak_disukai'   => $post['makan_tidak_disukai'],
                    'pengetahuan_diet'      => $post['pengetahuan_diet'],
                    'asupan_makanan'        => $post['asupan_makanan'],
                    'asupan_makanan_deskripsi' => $post['asupan_makanan_deskripsi'],
                    'energi_assesmen'       => $post['energi_assesmen'],
                    'protein_assesmen'      => $post['protein_assesmen'],
                    'lemak_assesmen'        => $post['lemak_assesmen'],
                    'kh_assesmen'           => $post['kh_assesmen'],
                    'domainintake'          => $post['domainintake'],
                    'domainintake_lain'     => $post['domainintake_lain'],
                    'domainklinis'          => $post['domainklinis'],
                    'domainklinis_lain'     => $post['domainklinis_lain'],
                    'domainbehavior'        => $post['domainbehavior'],
                    'etiologi_intake'       => $post['etiologi_intake'],
                    'sign_symptoms_intake'  => $post['sign_symptoms_intake'],
                    'etiologi_klinis'       => $post['etiologi_klinis'],
                    'sign_symptoms_klinis'  => $post['sign_symptoms_klinis'],
                    'etiologi_behavior'     => $post['etiologi_behavior'],
                    'sign_symptoms_behavior' => $post['sign_symptoms_behavior'],
                    'diet_nd1'              => $diet_nd1,
                    'diet_nd2'              => $diet_nd2,
                    'dietintervensi_nd1'    => $post['dietintervensi_nd1'],
                    'dietintervensi_nd2'    => $post['dietintervensi_nd2'],
                    'bentuk'                => $post['bentuk'],
                    'route'                 => $post['route'],
                    'energi_intervensi'     => $post['energi_intervensi'],
                    'protein_intervensi'    => $post['protein_intervensi'],
                    'lemak_intervensi'      => $post['lemak_intervensi'],
                    'kh_intervensi'         => $post['kh_intervensi'],
                    'edukasi_gizi'          => $post['edukasi_gizi'],
                    'monev_gizi'            => $post['monev_gizi'],
                    'oleh'                  => $this->session->userdata("id"),
                );

                $this->db->trans_begin();

                if (!empty($post['id_gizi'])) {
                    $this->db->replace('medis.tb_pengkajian_gizi_lanjutan', $dataPengkajianGiziLanjutan);
                    $this->db->where('tb_tb_bb.ref', $getID);
                    $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success_ubah');
                    }

                    echo json_encode($result);
                } else {
                    $this->db->insert('medis.tb_pengkajian_gizi_lanjutan', $dataPengkajianGiziLanjutan);
                    $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success_simpan');
                    }

                    echo json_encode($result);
                }
            } else if ($param == 'count') {
                $result = $this->PengkajianGiziLanjutanModel->get_count();;
                echo json_encode($result);
            }
        }
    }

    public function datatables()
    {
        $result = $this->PengkajianGiziLanjutanModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editpengkajianGiziLanjutan" data-toggle="modal" data-id="' . $row->id . '"><i class="fa fa-eye"></i> Lihat</a><a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/Rawatinap/gizi/PengkajianGiziLanjut.php?format=pdf&id=' . $row->id . '" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row->tanggal;
            $sub_array[] = $row->ruangan;
            $sub_array[] = $row->user;
            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->PengkajianGiziLanjutanModel->total_count(),
            "recordsFiltered"   => $this->PengkajianGiziLanjutanModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
