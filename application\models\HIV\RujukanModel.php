<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RujukanModel extends MY_Model
{
  protected $_table_name = 'keperawatan.rujukan_hiv';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib <PERSON>ka',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function ambilRegNas($nomr)
  {
    $this->db->select('id id_reg_nas, no_reg_nas');
    $this->db->from('db_pasien.tb_regnas');
    $this->db->where('nomr', $nomr);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function simpanRegNas($data)
  {
    $this->db->insert('db_pasien.tb_regnas', $data);
    return $this->db->insert_id();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_rujukan_hiv', $data);
    return $this->db->insert_id();
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_rujukan_hiv.id', $id);
    $this->db->update('keperawatan.tb_rujukan_hiv', $data);
  }

  public function history($nokun, $param)
  {
    if (isset($param)) {
      if ($param == 'jumlah') {
        $this->db->select('rh.id');
      } elseif ($param == 'tabel') {
        $this->db->select(
          'rh.id, rh.tanggal, rh.waktu, master.getNamaLengkapPegawai(p.NIP) pengisi, rh.created_at, rh.status'
        );
      }
    }
    $this->db->from('keperawatan.tb_rujukan_hiv rh');
    $this->db->join('db_pasien.tb_regnas rn', 'rn.id = rh.id_reg_nas', 'left');
    $this->db->join('aplikasi.pengguna p', 'p.ID = rh.oleh', 'left');
    $this->db->where('rh.nokun', $nokun);
    if (isset($param)) {
      if ($param == 'jumlah') {
        $this->db->where('rh.status', 1);
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        $this->db->order_by('rh.tanggal', 'desc');
        $this->db->order_by('rh.waktu', 'desc');
        return $this->db->get();
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
  public function detail($id)
  {
    $this->db->select(
      'rh.id, rh.id_reg_nas, rn.no_reg_nas, rh.nokun, rh.tanggal, rh.waktu, rh.tanggal_tes_hiv, rh.tempat_tes_hiv,
      rh.stadium_klinis, tbb.nomr, tbb.tb, tbb.bb, rh.status_fungsional, rh.rejimen, rh.io, rh.profilaksis_io,
      rh.status_tb, rh.dokter, rh.no_register, rh.tanggal_melapor, rh.tempat_berobat_baru, rh.tanggal_pengembalian,
      rh.pengisi_pengembalian'
    );
    $this->db->from('keperawatan.tb_rujukan_hiv rh');
    $this->db->join('db_pasien.tb_regnas rn', 'rn.id = rh.id_reg_nas', 'left');
    $this->db->join('db_pasien.tb_tb_bb tbb', 'tbb.ref = rh.id', 'left');
    $this->db->where('tbb.data_source', 48);
    $this->db->where('rh.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file RujukanModel.php */
/* Location: ./application/models/HIV/RujukanModel.php */