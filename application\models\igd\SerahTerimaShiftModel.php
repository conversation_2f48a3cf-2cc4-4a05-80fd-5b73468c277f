<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SerahTerimaShiftModel extends CI_Model
{
  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_serah_terima_shift_igd', $data);
    return $this->db->insert_id();
  }

  public function history($nomr)
  {
    $this->db->select(
      'kstsi.ID,dv.variabel, kstsi.tanggal, kstsi.nokun, master.getNamaLengkapPegawai(peng.NIP) perawat,
      peg.NAMA penerima'
    );
    $this->db->from('keperawatan.tb_serah_terima_shift_igd kstsi');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kstsi.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('db_master.variabel dv', 'dv.id_variabel = kstsi.shiftKe', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = kstsi.yangMenerima', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = kstsi.oleh', 'left');
    $this->db->order_by('kstsi.tanggal', 'DESC');
    $this->db->where('pp.NORM', $nomr);

    $query = $this->db->get();
    return $query->result_array();
  }

  public function detailHistory($id)
  {
    $this->db->select(
      'kstsi.ID, kstsi.nokun, master.getNamaLengkapPegawai(peng.NIP) oleh, kstsi.shiftKe, shift.variabel varshift,
      kstsi.datapasiensrigd, kstsi.kesadaran, kesda.variabel varkesadaran, kstsi.skornyeri, kstsi.skorewspws,
      kstsi.resikojatuh, kstsi.tandaVital, kstsi.balance, kstsi.alatterpasang, kstsi.oksigenIGD,
      kstsi.lainAlatTerpasang, kstsi.pemeriksaanPenunjang, kstsi.tindakanObat, kstsi.masalahasuhan, kstsi.perencanaan,
      kstsi.evaluasiMasalah, kstsi.yangMenerima, kstsi.tanggal, peg.NAMA penerima'
    );
    $this->db->from('keperawatan.tb_serah_terima_shift_igd kstsi');
    $this->db->join('db_master.variabel shift', 'shift.id_variabel = kstsi.shiftKe', 'left');
    $this->db->join('db_master.variabel kesda', 'kesda.id_variabel = kstsi.kesadaran', 'left');
    $this->db->join('db_master.variabel reskJa', 'reskJa.id_variabel = kstsi.resikojatuh', 'left');
    $this->db->join('db_master.variabel oksgienId', 'oksgienId.id_variabel = kstsi.oksigenIGD', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = kstsi.yangMenerima', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = kstsi.oleh', 'left');
    $this->db->where('kstsi.ID', $id);

    $query = $this->db->get();
    return $query->row_array();
  }
}