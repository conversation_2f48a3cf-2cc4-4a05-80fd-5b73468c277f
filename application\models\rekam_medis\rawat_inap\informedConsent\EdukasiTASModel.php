<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class EdukasiTASModel extends MY_Model {

	 public function simpanInformedConcent($data)
  {
    $this->db->insert('db_informed_consent.tb_informed_consent', $data);
    return $this->db->insert_id();
  }

  public function simpanEdukasi($data)
  {
    $this->db->insert('db_informed_consent.tb_edukasi_anestesi_sedasi', $data);
  }

  public function listHistoryInformedConsentEdukasiTAS($nomr)
  {
    $query = $this->db->query("SELECT pp.NORM, tic.id, tic.nokun,
                              master.getNamaLengkapPegawai(ap.NIP) OLEH,
                              master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA,
                              tic.created_at tanggal, teas.status_edukasi
                              FROM db_informed_consent.tb_edukasi_anestesi_sedasi teas
                              LEFT JOIN db_informed_consent.tb_informed_consent tic ON tic.id = teas.id_informed_consent
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE pp.NORM = '$nomr' AND tic.`status` = 1
                              ");
    return $query;
  }

  public function getEdukasiTAS($id)
  {
    $query = $this->db->query("SELECT tic.*, teas.*, tic.id idtic, teas.id idEdukasiTAS
                              , master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA, tic.created_at tanggal
                              FROM db_informed_consent.tb_edukasi_anestesi_sedasi teas
                              LEFT JOIN db_informed_consent.tb_informed_consent tic ON tic.id = teas.id_informed_consent
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE tic.id = $id AND tic.`status` = 1
                              ");
    return $query->row_array();
  }

  public function updateEdukasiTAS($data,$idEdukasiTAS)
  {
    $this->db->where('id', $idEdukasiTAS);
    $this->db->update('db_informed_consent.tb_edukasi_anestesi_sedasi', $data);
  }

}
?>