<?php
class Model_penerimaan_gudang extends ci_model
{

  public function __construct()
  {
    parent::__construct();
  }

  function cari_barang($title)
  {
    $this->db->like('BARANG', $title, 'both');
    $this->db->order_by('BARANG', 'ASC');
    $this->db->limit(10);
    return $this->db->get('invenumum.v_barang_master')->result();
  }

  function get_no_penerimaan()
  {
    $q = $this->db->query("SELECT MAX(RIGHT(ID_PENERIMAAN,4)) AS kd_max FROM invenumum.penerimaan_gudang WHERE DATE(TGL_INPUT)=CURDATE()");
    $kd = "";
    if ($q->num_rows() > 0) {
      foreach ($q->result() as $k) {
        $tmp = ((int) $k->kd_max) + 1;
        $kd = sprintf("%04s", $tmp);
      }
    } else {
      $kd = "0001";
    }
    date_default_timezone_set('Asia/Jakarta');
    return date('Ymd') . $kd;
  }

  public function datapenerimaan()
  {
    $query = $this->db->query("SELECT pg.ID_PENERIMAAN, pg.NOSURAT_JALAN, pg.TGL_INPUT, pg.TGL_MASUK_BARANG, ru.DESKRIPSI RUANGAN
      FROM invenumum.penerimaan_gudang pg
      left JOIN invenumum.penerimaan_gudang_detil pgd ON pgd.ID_PENERIMAAN=pg.ID_PENERIMAAN
      LEFT JOIN master.ruangan ru ON ru.ID=pgd.GUDANG 
      where pg.`STATUS`=1 GROUP BY pg.ID_PENERIMAAN ORDER BY pg.TGL_INPUT DESC");
    return $query;
  }

  public function detail_penerimaan($id)
  {
    $query = "SELECT pg.ID_PENERIMAAN, pg.NOSURAT_JALAN, pg.TGL_INPUT, pg.TGL_MASUK_BARANG, ru.DESKRIPSI RUANGAN, bm.BARANG, pgd.HARGA, pgd.JUMLAH, sa.SATUAN
    FROM invenumum.penerimaan_gudang pg
    left JOIN invenumum.penerimaan_gudang_detil pgd ON pgd.ID_PENERIMAAN=pg.ID_PENERIMAAN
    LEFT JOIN master.ruangan ru ON ru.ID=pgd.GUDANG 
    LEFT JOIN invenumum.barang_gudang bg ON bg.ID_BARANG_GUDANG=pgd.BARANG
    LEFT JOIN invenumum.barang_master bm ON bg.BARANG=bm.ID_BARANG
    LEFT join invenumum.satuan_ sa ON bm.SATUAN=sa.ID_SATUAN
    where pg.ID_PENERIMAAN='$id' AND pgd.STATUS=1";
    return $this->db->query($query);
  }

  // public function get_id_barang($id){
  //       $query = $this->db->query('SELECT * FROM invenumum.barang_gudang bg WHERE bg.BARANG='$id'');
  //       return $query->row_array();
  //   }

  public function tampilkan_gudang()
  {
    $query  = "SELECT ru.ID, ru.DESKRIPSI from master.ruangan ru
    LEFT JOIN akses_simrskd.USER_RUANGAN ur ON ur.ID_RUANGAN=ru.ID
    where ur.ID_USER=" . $this->session->userdata('id') . "";
    return $this->db->query($query);
  }

  public function get_id_barang($id)
  {
    $query = $this->db->query("SELECT * FROM invenumum.barang_gudang bg where bg.BARANG='$id'");
    return $query->row_array();
  }
}
