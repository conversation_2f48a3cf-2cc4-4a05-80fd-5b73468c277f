  <?php
  defined('BASEPATH') OR exit('No direct script access allowed');

  class InstrumenMmse_Model extends CI_Model {

    public function simpanFInstrumenMmse($data)
    {
      $this->db->trans_begin();
      $this->db->insert('db_layanan.tb_geriatri_mmse', $data);
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }

      echo json_encode($result);
    }

    public function listInstrumenEvaluasi($nomr)
    {
      $query = $this->db->query("SELECT tgm.*
        FROM db_layanan.tb_geriatri_mmse tgm
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tgm.nokun
        LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
        WHERE pp.NORM = '$nomr'
        ORDER BY tgm.id DESC");

      return $query;
    }

    public function getInstrumenMmse($id)
    {
      $query = $this->db->query("SELECT tgm.*
        ,HOUR(TIMEDIFF(NOW(),tgm.tanggal)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tgm.tanggal))<=24,1,0) STATUS_EDIT
        ,(mmse_1+mmse_2+mmse_3+mmse_4+mmse_5+mmse_6+mmse_7+mmse_8+mmse_9+mmse_10+mmse_11)TotalEvaluasi
        FROM db_layanan.tb_geriatri_mmse tgm
        WHERE tgm.id = '$id'");

      return $query->row_array();
    }

    public function updateFInstrumenMmse($data, $id)
    {
      $this->db->trans_begin();
      $this->db->where('id', $id);
      $this->db->update('db_layanan.tb_geriatri_mmse', $data);
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }

      echo json_encode($result);
    }

  }

  /* End of file InstrumenMmse_Model.php */
  /* Location: ./application/models/geriatri/InstrumenMmse_Model.php */
