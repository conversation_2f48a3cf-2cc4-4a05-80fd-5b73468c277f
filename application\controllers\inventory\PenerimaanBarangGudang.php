<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PenerimaanBarangGudang extends ci_controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(34, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->library('cart');
    $this->load->model(array('inventory/Model_barang', 'inventory/Model_penerimaan_gudang', 'inventory/Model_penyedia', 'inventory/Model_permintaan', 'inventory/Model_satuan'));
    date_default_timezone_set("Asia/Bangkok");
  }


  function index()
  {
    $barang     =  $this->Model_barang->tampilkan_data();
    $penyedia   =  $this->Model_penyedia->tampilkan_data()->result();
    $gudang     =  $this->Model_penerimaan_gudang->tampilkan_gudang()->result();
    $satuan     =  $this->Model_satuan->tampilkan_data()->result();
    $data       = array(
      'title'         => 'Halaman Input Penerimaan',
      'isi'           => 'inventory/PenerimaanBarangGudang/index',
      'barang'        => $barang,
      'penyedia'      => $penyedia,
      'gudang'        => $gudang,
      'satuan'        => $satuan
    );
    $this->load->view('layout/wrapper', $data);
  }

  function input_barang()
  {
    $data = array(
      'id'                => $this->input->post('ID_BARANG_GUDANG'),
      'name'              => $this->input->post('NAMA'),
      'qty'               => $this->input->post('JUMLAH_MASUK'),
      'tgl_masuk'         => $this->input->post('TGL_MASUK'),
      'penyedia'          => $this->input->post('PENYEDIA'),
      'price'             => $this->input->post('HARGA'),
      'options'           => array(
        'satu'            => $this->input->post('SATUAN'),
        'nosurat_jalan'   => $this->input->post('NOSURAT_JALAN'),
        'gudang'          => $this->input->post('GUDANG'),
        'stok'            => $this->input->post('STOK'),
        'idbarangmaster'  => $this->input->post('ID_BARANG')
      )
    );
    //echo "<pre>";print_r($data);exit();
    $this->cart->insert($data);
    //$item = $this->cart->contents();
    //echo "<pre>";print_r($item);exit();
    echo $this->tampil_barang();
  }

  function tampil_barang()
  {
    $output = '';
    $no = 0;
    $data_barang = $this->cart->contents();
    foreach ($data_barang as $barang) {
      $no++;
      $output .= '
      <tr>
      <td>' . $no . '</td>
      <td>' . $barang['name'] . '</td>
      <td>' . $barang['price'] . '</td>
      <td>' . $barang['options']['stok'] . '</td>
      <td>' . $barang['qty'] . '</td>
      <td>' . $barang['options']['satu'] . '</td>
      <td><button type="button" id="' . $barang['rowid'] . '" class="hapus_terima btn btn-danger btn-sm"><i class="fas fa-ban"></i></button></td>
      </tr>
      ';
    }
    return $output;
  }

  function list_penerimaan()
  {
    echo $this->tampil_barang();
  }

  function get_autocomplete()
  {
    if (isset($_GET['term'])) {
      $result = $this->Model_penerimaan_gudang->cari_barang($_GET['term']);
      if (count($result) > 0) {
        foreach ($result as $row)
          $arr_result[] = array(
            'label'             => $row->BARANG,
            'idbarang'          => $row->ID_BARANG,
            'idsatuan'          => $row->SATUAN,
            'idstok'            => $row->STOK,
            'idbaranggudang'    => $row->ID_BARANG_GUDANG,
          );

        echo json_encode($arr_result);
      }
    }
  }

  function hapus_barang()
  { //fungsi untuk menghapus item cart
    $data = array(
      'rowid' => $this->input->post('row_id'),
      'qty' => 0,
    );
    $this->cart->update($data);
    echo $this->tampil_barang();
  }

  function simpan_penerimaan()
  {
    $idterima = $this->Model_penerimaan_gudang->get_no_penerimaan();
    foreach (array_slice($this->cart->contents(), 0, 1) as $item) {
      $oleh    = $this->session->userdata("id");
      $data = array(
        'ID_PENERIMAAN'     => $idterima,
        'NOSURAT_JALAN'     => $item['options']['nosurat_jalan'],
        'TGL_MASUK_BARANG'  => $item['tgl_masuk'],
        'PENYEDIA'          => $item['penyedia'],
        'OLEH'              => $oleh,
      );
      $this->db->insert('invenumum.penerimaan_gudang', $data);
    }
    foreach ($this->cart->contents() as $detil) {
      $data_detail = array(
        'ID_PENERIMAAN' => $idterima,
        'BARANG'       => $detil['id'],
        'JUMLAH'       => $detil['qty'],
        'HARGA'        => $detil['price'],
        'GUDANG'       => $item['options']['gudang'],
        'STOK'         => $detil['options']['stok'],
        'STOKUPDATE'   => $detil['qty'] + $detil['options']['stok'],
      );
      // echo "<pre>";print_r($data_detail);exit();
      $this->db->insert('invenumum.penerimaan_gudang_detil', $data_detail);
      $insert_id = $this->db->insert_id();
      $datatr = array(
        'BARANG_RUANGAN'    => $detil['id'],
        'JUMLAH'            => $detil['qty'],
        'HARGA'             => $detil['price'],
        'STOK'              => $detil['options']['stok'] + $detil['qty'],
        'JENIS'             => 1,
        'REF'               => $insert_id,
        'TANGGAL_TRANSAKSI' => $item['tgl_masuk'],
      );
      //echo "<pre>";print_r($datatr);exit();
      $this->db->insert('invenumum.transaksi_stok_ruangan', $datatr);
      $insert_idtsr = $this->db->insert_id();
      $stoknew = $detil['options']['stok'] + $detil['qty'];
      $this->db->query('update invenumum.barang_gudang SET STOK=' . $stoknew . ', TRANSAKSI_STOK_RUANGAN=' . $insert_idtsr . ' WHERE ID_BARANG_GUDANG=' . $detil['id'] . '');
      $id =  $detil['id'];
      $id_barang = $this->Model_penerimaan_gudang->get_id_barang($id);
      if (empty($id_barang)) {
        $data_gudang = array(
          'BARANG'                  => $detil['id'],
          'STOK'                    => $detil['options']['stok'] + $detil['qty'],
          'GUDANG'                  => $item['options']['gudang'],
        );
        // echo "<pre>";print_r($data_gudang);exit();
        $this->db->insert('invenumum.barang_gudang', $data_gudang);
      }

      $dataharga = array(
        'ID_BARANG_GUDANG'  => $detil['id'],
        'JML_MASUK'         => $detil['qty'],
        'HARGA'             => $detil['price'],
        'SISA'              => $detil['qty'],
        // 'STOK'             => $detil['options']['stok'],
        // 'JENIS'            => 1,
        // 'REF'              => $insert_id
      );
      //echo "<pre>";print_r($datatr);exit();
      $this->db->insert('invenumum.harga_barang', $dataharga);
      $idbarangmaster = $detil['options']['idbarangmaster'];
      $this->db->query('update invenumum.barang_master SET HARGA=' . $detil['price'] . ' WHERE ID_BARANG=' . $idbarangmaster . '');
    }
    $this->cart->destroy();
    redirect('inventory/PenerimaanBarangGudang');
  }

  public function datapenerimaan()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listpermintaan = $this->Model_penerimaan_gudang->datapenerimaan();
    $data  = array();
    $no    = 1;
    foreach ($listpermintaan->result() as $lp) {
      $data[] = array(
        $no,
        $lp->ID_PENERIMAAN,
        date("d-m-Y", strtotime($lp->TGL_MASUK_BARANG)),
        $lp->NOSURAT_JALAN,
        $lp->RUANGAN,
        '<a href="#detailpenerimaan" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-id="' . $lp->ID_PENERIMAAN . '"><i class="fas fa-search"></i> Lihat</a>',

      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listpermintaan->num_rows(),
      "recordsFiltered" => $listpermintaan->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function datamodal()
  {
    $id       = $this->input->post('id');
    $record =  $this->Model_penerimaan_gudang->detail_penerimaan($id)->result_array();
    $data     = array(

      'record' => $record,
    );
    $this->load->view('inventory/PenerimaanBarangGudang/ModalDetilPenerimaan', $data);
  }
}
