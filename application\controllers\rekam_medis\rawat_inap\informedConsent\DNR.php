<?php
defined('BASEPATH') or exit('No direct script access allowed');
class DNR extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/informedConsent/DNRModel'
    ));
  }

  public function index(){
  	// $norm = $this->uri->segment(6);
   //  $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'HubunganPasienDNR' => $this->masterModel->referensi(1610),
      'hubunganSaksiDNR' => $this->masterModel->referensi(1610),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
      'SaksiDNR' => $this->masterModel->referensi(1695),
    );
     // print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/informedConsent/DNR/index', $data);
  }

  public function simpanPersetujuanDNR()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $date = $this->input->post('datePickerDNR');
    $date1 = $this->input->post('datePickerDokterDNR');
    $tglPersetujuanKeluarga = date('Y-m-d h:i', strtotime($date));
    $tglPersetujuanDokter = date('Y-m-d h:i', strtotime($date1));

    $dataInformedConcent = array (
      'nokun'                  => $post['nokun'],
      'jenis_informed_consent' => 3032,
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanDNR'],
      'penerima_informasi'     => "-",
      'pemberi_informasi'      => "-",
      'oleh'                   => $this->session->userdata('id'),
    );
    // echo "<pre>";print_r($dataInformedConcent);echo "</pre>";

    $idInformedConcent = $this->DNRModel->simpanInformedConcent($dataInformedConcent);

    $dataPersetujuanDNR = array(
      'id_informed_consent'        			       => $idInformedConcent,
      'diagnosa'              	   			       => $post['diagnosa'],
      'penanggung_jawab'              		     => $post['penanggungDNR'],
      'hubungan_penanggung_jawab'              => $post['HubunganPasienDNR'],
      'hubungan_penanggung_jawab_lain'         => $post['HubunganPasienDNR1'],
      'waktu_penanggung_ttd'                   => $tglPersetujuanKeluarga,
      'ttd_penanggung'    					           => file_get_contents($this->input->post('sign_PenanggungDNR')),
      'ada_saksi_pasien'        				       => $post['AdaSaksiDNR'],
      'saksi_pasien'        				           => $post['saksiDNR'],
      'hubungan_saksi'             			       => $post['hubunganSaksiDNR'],
      'hubungan_saksi_lain'         		       => $post['hubunganSaksiDNR1'],
      'ttd_saksi'      						             => file_get_contents($this->input->post('sign_KeluargaDNR')),
      'dokter'             					           => $post['dokterPelaksanaTindakanDNR'],
      'waktu_dokter_ttd'          			       => $tglPersetujuanDokter,
      'ttd_dokter'          				           => file_get_contents($this->input->post('sign_DokterDNR')),
      'saksi_rumah_sakit'          			       => $post['nama_saksi_rs_DNR'],
      'ttd_saksi_rumah_sakit'          		     => file_get_contents($this->input->post('sign_RumahSakitDNR')),
      'status_persetujuan' 					           => 1
    );
    // echo "<pre>";print_r($dataPersetujuanDNR);echo "</pre>";exit();

    $this->db->insert('db_informed_consent.tb_persetujuan_dnr',$dataPersetujuanDNR);
    // $idInformedConcent = $this->DNRModel->simpanDNR($dataPersetujuanDNR);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyDNR()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    // $nomr = $this->uri->segment(6);
    $listDNR = $this->DNRModel->listHistoryInformedConsentDNR($nomr);

    $data = array();
    $no = 1;
    foreach ($listDNR->result() as $DNR) {
      $stat = $DNR->status_persetujuan;
      if($DNR->status_persetujuan == 1){
        $status = 'Setuju';
        $button = '<a href="#modalDNR" class="btn btn-primary btn-block" data-id="'.$DNR->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>
        <a href="#modalTolakDNR" class="btn btn-danger btn-block" data-id="'.$DNR->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fas fa-edit"></i> Tolak</a>';
      }else{
        $status = 'Batal Setuju';
         $button = '<a href="#modalDNR" class="btn btn-primary btn-block" data-id="'.$DNR->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>';
      }
      $data[] = array(
        $no,
        $DNR->nokun,
        $DNR->DOKTERPELAKSANA,
        $DNR->OLEH,
        date("d-m-Y H:i:s",strtotime($DNR->tanggal)),
        $status,
        $button,

      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listDNR->num_rows(),
      "recordsFiltered" => $listDNR->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalDNR()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    $gpDNR = $this->DNRModel->getDNR($id);
    $nokun = $gpDNR['nokun'];
    $saksi = $gpDNR['saksi_rumah_sakit'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'gpDNR' => $gpDNR,
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'saksiRS' => $this->DNRModel->listPegawai($saksi),
      'HubunganPasienDNR' => $this->masterModel->referensi(1610),
      'hubunganSaksiDNR' => $this->masterModel->referensi(1610),
      'HubunganPenolakDNR' => $this->masterModel->referensi(1610),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );

    $this->load->view('rekam_medis/rawat_inap/informedConsent/DNR/view_edit', $data);
  }

  public function modalTolakDNR()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    $gpDNR = $this->DNRModel->getDNR($id);
    $nokun = $gpDNR['nokun'];
    $saksi = $gpDNR['saksi_rumah_sakit'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'gpDNR' => $gpDNR,
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'saksiRS' => $this->DNRModel->listPegawai($saksi),
      'HubunganPenolakDNR' => $this->masterModel->referensi(1610),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );

    $this->load->view('rekam_medis/rawat_inap/informedConsent/DNR/tolak', $data);
  }

  public function batalDNR()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    $idDNR = $this->input->post('idDNR');
    $post = $this->input->post();
    $dates = $this->input->post('datePickerDNR1');
    $tglPersetujuanBatal = date('Y-m-d h:i', strtotime($dates));

    // $dataInformedConcent = array (
    //   'dokter_pelaksana'       => $post['dokterPelaksanaTindakanDNR'],
    //   // 'penerima_informasi'     => $post['penerimaInformasiDNR'],
    //   // 'pemberi_informasi'      => $post['pemberiInformasiDNR'],
    // );

    // $this->DNRModel->updateInformedConcent($dataInformedConcent,$id);

    $dataDNR_edit = array (
      'alasan_pembatalan'                 => $post['alasanTolakDNR'],
      'penanggung_jawab_batal'            => $post['penanggungTolakDNR'],
      'hub_penanggung_jawab_batal'        => $post['HubunganPenolakDNR'],
      'hub_penanggung_jawab_lain_batal'   => $post['HubunganPenolakDNR1'],
      'waktu_penanggung_ttd_batal'        => $tglPersetujuanBatal,
      'ttd_penanggung_batal'              => file_get_contents($this->input->post('sign_PenanggungTolakDNR')),
      'status_persetujuan'                => 0
    );

     // echo "<pre>";print_r($dataDNR_edit);echo "</pre>";exit();

    $this->DNRModel->updateDNR($dataDNR_edit,$idDNR);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function getPegawaiForUpdate()
    {
        $result = $this->DNRModel->listPegawai();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['text'] = $row['NAMA_LENGKAP'];
            $data[] = $sub_array;
        }
        // $output = array(
        //     "item" -> $data
        // );
        echo json_encode($data);
    }
}
?>