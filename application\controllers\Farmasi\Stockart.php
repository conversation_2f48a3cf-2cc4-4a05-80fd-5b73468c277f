<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Stockart extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model('Farmasi/StockartModel', 'StockartModel');
        $this->load->library('form_validation');
        
        // Set timezone to Asia/Jakarta
        date_default_timezone_set('Asia/Jakarta');
    }

    public function index($nomr = null, $nopen = null, $nokun = null)
    {
        $data['nomr'] = $nomr;
        $data['nopen'] = $nopen;
        $data['nokun'] = $nokun;
        $data['title'] = 'Permintaan Stockart';
        $this->load->view('Farmasi/Stockart', $data);
    }

    public function simpan()
    {
        $this->form_validation->set_rules('nokun', 'No. Kunjungan', 'required');
        $items = $this->input->post('items');
        
        if (empty($items) || !is_array($items)) {
            echo json_encode(['status' => 'error', 'message' => 'Minimal harus ada 1 item']);
            return;
        }

        if ($this->form_validation->run() == FALSE) {
            echo json_encode(['status' => 'error', 'message' => validation_errors()]);
        } else {
            // Generate unique id_pengajuan
            $id_pengajuan = $this->generateIdPengajuan();
            
            $nokun = $this->input->post('nokun');
            $created_by = $this->session->userdata('id');
            $created_at = date('Y-m-d H:i:s');
            
            // Start transaction
            $this->db->trans_start();
            
            // Insert each item with the same id_pengajuan
            foreach ($items as $item) {
                $data = [
                    'id_pengajuan' => $id_pengajuan,
                    'nokun' => $nokun,
                    'id_item' => $item['id_item'],
                    'quantity' => $item['quantity'],
                    'note' => $item['note_item'],
                    'status' => 1, // 1 = diajukan, 2 = diterima, 3 = ditolak, 0 = deleted
                    'category' => 2, // Set category = 2 for stockart
                    'created_by' => $created_by,
                    'created_at' => $created_at,
                    'updated_by' => $created_by,
                    'updated_at' => $created_at
                ];
                
                $this->StockartModel->simpan($data);
            }
            
            // Complete transaction
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan pengajuan']);
            } else {
                echo json_encode(['status' => 'success', 'message' => 'Pengajuan berhasil disimpan', 'id_pengajuan' => $id_pengajuan]);
            }
        }
    }
    
    private function generateIdPengajuan()
    {
        // Generate unique ID with format: PGJ + YYYYMMDD + sequential number
        $date = date('Ymd');
        $prefix = 'PST' . $date;
        
        // Get the last sequence for today
        $this->db->select('id_pengajuan');
        $this->db->from('inventory.request_item');
        $this->db->like('id_pengajuan', $prefix, 'after');
        $this->db->where('category', 2); // Filter by category = 2
        $this->db->order_by('id_pengajuan', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_id = $query->row()->id_pengajuan;
            $last_sequence = intval(substr($last_id, -4));
            $new_sequence = $last_sequence + 1;
        } else {
            $new_sequence = 1;
        }
        
        return $prefix . str_pad($new_sequence, 4, '0', STR_PAD_LEFT);
    }

    public function getData()
    {
        try {
            // Log untuk debugging
            log_message('debug', 'getData() called - POST data: ' . json_encode($this->input->post()));

            $nokun = $this->input->post('nokun');

            // DataTables parameters dengan validasi
            $draw = intval($this->input->post('draw')) ?: 1;
            $start = intval($this->input->post('start')) ?: 0;
            $length = intval($this->input->post('length')) ?: 10;

            // Validasi search parameter
            $search_data = $this->input->post('search');
            $search_value = '';
            if (is_array($search_data) && isset($search_data['value'])) {
                $search_value = $search_data['value'];
            }

            // Validasi order parameter
            $order_data = $this->input->post('order');
            $order_column = 4; // default ke created_at
            $order_dir = 'desc';

            if (is_array($order_data) && isset($order_data[0])) {
                $order_column = intval($order_data[0]['column']) ?: 5;
                $order_dir = (isset($order_data[0]['dir']) && in_array($order_data[0]['dir'], ['asc', 'desc'])) ? $order_data[0]['dir'] : 'desc';
            }

            // Map column index to actual column names
            $column_map = [
                0 => null, // No column (row number)
                1 => 'id_pengajuan',
                2 => 'jumlah_item',
                3 => 'status',
                4 => 'created_at',
                5 => 'updated_at',
                6 => null // No column (actions)
            ];

            $order_by = isset($column_map[$order_column]) && $column_map[$order_column] !== null ? $column_map[$order_column] : 'created_at';

            // Log parameter yang akan dikirim ke model
            log_message('debug', 'getData() parameters - nokun: ' . $nokun . ', start: ' . $start . ', length: ' . $length . ', search: ' . $search_value . ', order_by: ' . $order_by . ', order_dir: ' . $order_dir);

            // Get data with search, pagination and sorting
            $result = $this->StockartModel->getDataTableWithSearch($nokun, $start, $length, $search_value, $order_by, $order_dir);

            // Validasi hasil dari model
            if (!is_array($result)) {
                log_message('error', 'getData() - Model returned non-array result: ' . json_encode($result));
                throw new Exception('Model returned invalid data format');
            }

            // Ensure data is an array
            if (!isset($result['data']) || !is_array($result['data'])) {
                $result['data'] = [];
            }

            // Validasi total dan filtered
            $total = isset($result['total']) ? intval($result['total']) : 0;
            $filtered = isset($result['filtered']) ? intval($result['filtered']) : 0;

            // Format response for DataTables
            $response = [
                'draw' => $draw,
                'recordsTotal' => $total,
                'recordsFiltered' => $filtered,
                'data' => $result['data']
            ];

            // Log response untuk debugging
            log_message('debug', 'getData() response - total: ' . $total . ', filtered: ' . $filtered . ', data count: ' . count($result['data']));

            header('Content-Type: application/json');
            echo json_encode($response);

        } catch (Exception $e) {
            // Log error detail
            log_message('error', 'getData() Exception: ' . $e->getMessage() . ' - File: ' . $e->getFile() . ' - Line: ' . $e->getLine());

            $response = [
                'draw' => intval($this->input->post('draw')) ?: 1,
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => 'Terjadi kesalahan saat memuat data: ' . $e->getMessage()
            ];

            header('Content-Type: application/json');
            echo json_encode($response);
        }
    }

    public function hapus()
    {
        $id = $this->input->post('id');
        $user_id = $this->session->userdata('id');
        
        // Cek kepemilikan stockart
        if (!$this->StockartModel->checkOwnership($id, $user_id)) {
            echo json_encode(['status' => 'error', 'message' => 'Anda tidak memiliki izin untuk menghapus stockart ini. Hanya user yang membuat stockart yang dapat menghapusnya.']);
            return;
        }
        
        $data = [
            'status' => 0,
            'updated_by' => $user_id,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->StockartModel->softDelete($id, $data)) {
            echo json_encode(['status' => 'success', 'message' => 'Data berhasil dihapus']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus data']);
        }
    }
    
    public function hapusPengajuan()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');
        $user_id = $this->session->userdata('id');
        
        // Cek kepemilikan pengajuan stockart
        if (!$this->StockartModel->checkOwnershipByPengajuan($id_pengajuan, $user_id)) {
            echo json_encode(['status' => 'error', 'message' => 'Anda tidak memiliki izin untuk menghapus pengajuan ini. Hanya user yang membuat pengajuan yang dapat menghapusnya.']);
            return;
        }
        
        $data = [
            'status' => 0,
            'updated_by' => $user_id,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->StockartModel->softDeleteByPengajuan($id_pengajuan, $data)) {
            echo json_encode(['status' => 'success', 'message' => 'Pengajuan berhasil dihapus']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus pengajuan']);
        }
    }
    
    public function getDetailPengajuan()
    {
        $id_pengajuan = $this->input->get('id_pengajuan');
        $data = $this->StockartModel->getDetailPengajuan($id_pengajuan);
        
        if ($data && count($data) > 0) {
            // Determine status text based on conditions
            $status_text = '';
            $status_resep = $data[0]->status_resep;
            $status_request = $data[0]->status;
            
            if ($status_resep !== null) {
                // If order_resep exists
                if ($status_resep == 2) {
                    $status_text = 'Diterima';
                } elseif ($status_resep == 0) {
                    $status_text = 'Ditolak Farmasi';
                } else {
                    $status_text = 'Diproses';
                }
            } else {
                // If no order_resep, use request_item status
                if ($status_request == 2) {
                    $status_text = 'Diproses';
                } elseif ($status_request == 1) {
                    $status_text = 'Diajukan';
                } elseif ($status_request == 3) {
                    $status_text = 'Ditolak Dokter';
                } else {
                    $status_text = 'Unknown';
                }
            }
            
            $response = [
                'id_pengajuan' => $data[0]->id_pengajuan,
                'nokun' => $data[0]->nokun,
                'status' => $data[0]->status,
                'status_text' => $status_text,
                'ref' => $data[0]->ref ?: '-',
                'created_at' => date('d/m/Y H:i:s', strtotime($data[0]->created_at)),
                'updated_at' => date('d/m/Y H:i:s', strtotime($data[0]->updated_at)),
                'created_by_name' => $data[0]->created_by_name ?: '-',
                'updated_by_name' => $data[0]->updated_by_name ?: '-',
                'items' => []
            ];
            
            foreach ($data as $item) {
                $response['items'][] = [
                    'nama_barang' => $item->nama_barang,
                    'quantity' => $item->quantity,
                    'note_item' => $item->note
                ];
            }
            
            echo json_encode(['status' => 'success', 'data' => $response]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Data tidak ditemukan']);
        }
    }

    public function update()
    {
        $id = $this->input->post('id');
        $data = [
            'id_item' => $this->input->post('id_item'),
            'quantity' => $this->input->post('quantity'),
            'note' => $this->input->post('note'),
            'updated_by' => $this->session->userdata('id'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($this->StockartModel->update($id, $data)) {
            echo json_encode(['status' => 'success', 'message' => 'Data berhasil diupdate']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Gagal mengupdate data']);
        }
    }

    public function getById()
    {
        $id = $this->input->post('id');
        $data = $this->StockartModel->getById($id);
        if ($data) {
            // Get item name
            $this->db->select('NAMA');
            $this->db->from('inventory.barang');
            $this->db->where('ID', $data->id_item);
            $item = $this->db->get()->row();
            $data->nama_barang = $item ? $item->NAMA : '';
            echo json_encode(['status' => 'success', 'data' => $data]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Data tidak ditemukan']);
        }
    }

    public function getBarang()
    {
        $search = $this->input->get('q');
        $data = $this->StockartModel->getBarang($search);
        echo json_encode($data); // Return direct array, not wrapped in 'results'
    }

    public function isRoomRegistered($id_room)
    {
        return $this->StockartModel->isRoomRegistered($id_room);
    }
    
    public function orderResep()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');
        $nokun = $this->input->post('nokun');
        
        // Cek apakah user adalah dokter
        if ($this->session->userdata('status') != 1) {
            echo json_encode(['status' => 'error', 'message' => 'Hanya dokter yang dapat membuat order resep']);
            return;
        }
        
        // Get detail pengajuan stockart
        $stockartItems = $this->StockartModel->getDetailPengajuan($id_pengajuan);
        
        if (!$stockartItems || count($stockartItems) == 0) {
            echo json_encode(['status' => 'error', 'message' => 'Data pengajuan tidak ditemukan']);
            return;
        }
        
        // Cek status pengajuan harus 1 (diajukan)
        if ($stockartItems[0]->status != 1) {
            echo json_encode(['status' => 'error', 'message' => 'Hanya pengajuan dengan status "Diajukan" yang dapat diorder resep']);
            return;
        }
        
        // Generate kode order resep
        $this->load->model('PengkajianAwalModel', 'pengkajianAwalModel');
        // Set farmasi_tujuan untuk generateNoOrderResep
        $_POST['farmasi_tujuan'] = '105050156';
        $kode = $this->pengkajianAwalModel->generateNoOrderResep();
        
        // Get user info
        $user_id = $this->session->userdata('id');
        $user_nip = $this->session->userdata('nip');
        
        // Get dokter DPJP ID from stockart data (tujuan pasien)
        $id_dokter = $stockartItems[0]->id_dokter;
        
        // Set farmasi tujuan
        $farmasi_tujuan = '105050156';
        
        // Get nama pegawai using model method
        $nama_pegawai = $this->StockartModel->getNamaPegawai($user_nip);
        
        // Prepare data order resep
        $dataOrderResep = [
            'NOMOR' => $kode,
            'KUNJUNGAN' => $nokun,
            'TANGGAL' => date("Y-m-d H:i:s"),
            'DOKTER_DPJP' => $id_dokter,
            'TUJUAN' => $farmasi_tujuan,
            'PEMBERI_RESEP' => $nama_pegawai,
            'OLEH' => $user_id,
        ];
        
        // Prepare data order resep detail
        $dataOrderResepDetail = [];
        
        foreach ($stockartItems as $item) {
            // id_barang_ruangan sudah tersedia dari getDetailPengajuan
            if (!$item->id_barang_ruangan) {
                echo json_encode(['status' => 'error', 'message' => 'Barang ruangan tidak ditemukan untuk item: ' . $item->id_item]);
                return;
            }
            
            $dataOrderResepDetail[] = [
                'ORDER_ID' => $kode,
                'FARMASI' => $item->id_item,
                'STOK' => $item->id_barang_ruangan,
                'JUMLAH' => $item->quantity,
                'KETERANGAN' => 'PENGGANTIAN STOCKART',
            ];
        }
        
        // Start transaction
        $this->db->trans_start();
        
        // Insert order resep
        $this->db->insert('layanan.order_resep', $dataOrderResep);
        
        // Insert order resep detail
        $this->db->insert_batch('layanan.order_detil_resep', $dataOrderResepDetail);
        
        // Update status pengajuan stockart menjadi diterima (status = 2)
        $this->StockartModel->updateStatusPengajuan($id_pengajuan, 2, $user_id);
        
        // Update kolom ref dengan nomor order resep
        $this->StockartModel->updateRefPengajuan($id_pengajuan, $kode);
        
        // Complete transaction
        $this->db->trans_complete();
        
        if ($this->db->trans_status() === FALSE) {
            echo json_encode(['status' => 'error', 'message' => 'Gagal membuat order resep']);
        } else {
            echo json_encode(['status' => 'success', 'message' => 'Order resep berhasil dibuat dengan nomor: ' . $kode, 'nomor_order' => $kode]);
        }
    }
    
    public function rejectResep()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');
        
        // Cek apakah user adalah dokter
        if ($this->session->userdata('status') != 1) {
            echo json_encode(['status' => 'error', 'message' => 'Hanya dokter yang dapat menolak resep']);
            return;
        }
        
        // Get detail pengajuan stockart
        $stockartItems = $this->StockartModel->getDetailPengajuan($id_pengajuan);
        
        if (!$stockartItems || count($stockartItems) == 0) {
            echo json_encode(['status' => 'error', 'message' => 'Data pengajuan tidak ditemukan']);
            return;
        }
        
        // Cek status pengajuan harus 1 (diajukan)
        if ($stockartItems[0]->status != 1) {
            echo json_encode(['status' => 'error', 'message' => 'Hanya pengajuan dengan status "Diajukan" yang dapat ditolak']);
            return;
        }
        
        $this->db->trans_start();
        
        try {
            // Update status semua item dalam pengajuan menjadi 3 (ditolak)
            $this->db->where('id_pengajuan', $id_pengajuan);
            $this->db->update('inventory.request_item', ['status' => 3]);
            
            $this->db->trans_complete();
            
            if ($this->db->trans_status() === FALSE) {
                echo json_encode(['status' => 'error', 'message' => 'Gagal menolak resep']);
            } else {
                echo json_encode(['status' => 'success', 'message' => 'Resep berhasil ditolak']);
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            echo json_encode(['status' => 'error', 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }
}