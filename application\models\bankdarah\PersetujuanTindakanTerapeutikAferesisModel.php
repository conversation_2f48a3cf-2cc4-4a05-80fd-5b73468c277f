<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PersetujuanTindakanTerapeutikAferesisModel extends MY_Model{
	protected $_table_name = 'db_informed_consent.tb_terapeutik_aferesis';
	protected $_primary_key = 'id';
	protected $_order_by = 'id';
	protected $_order_by_type = 'DESC';

	public $rules = array(
		'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
		),
    );
    
    function table_query()
    {
        $this->db->select('kp.id ID, kp.nokun NOKUN, kp.created_at TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGA<PERSON>
        , p.NOR<PERSON>, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('db_informed_consent.tb_informed_consent kp');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = kp.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = kp.oleh','LEFT');

        $this->db->where('kp.STATUS !=','0');
        $this->db->where('p.NORM',$this->input->post('nomr'));
        $this->db->where('kp.jenis_informed_consent =','3028');
        $this->db->order_by('TANGGAL', 'DESC');

        // if($this->input->post('id')){
        // 	$this->db->where('hph.ID', $this->input->post('id'));
        // }

        // if($this->input->post('status')){
        //     $this->db->where_in('hph.STATUS_LIS',$this->input->post('status'),FALSE);
        // }
        // else{
        //     $this->db->where('his.STATUS IS NULL');
        // }

        // if($this->input->post('search[value]')){
        //     $this->db->group_start();
        //     $this->db->like('hph.NORM', $this->input->post('search[value]'));
        //     $this->db->or_like('hph.NOMOR_LAB', $this->input->post('search[value]'));
        //     $this->db->group_end();
        //     // $this->db->where_in('his.STATUS',$this->input->post('status'));            
        // }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
