<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class WhatsappPasienModel extends MY_Model{
	protected $_table_name = 'layanan.wa_pasien';
	protected $_primary_key = 'ID';
	protected $_order_by = 'TANGGAL';
	protected $_order_by_type = 'DESC';

	public $rules = array(
        'pasien' => array(
            'field' => 'pasien',
            'label' => 'Pasien',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'nomor' => array(
            'field' => 'nomor',
            'label' => 'Nomor',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
		'pesan' => array(
            'field' => 'pesan',
            'label' => 'Pesan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.'
                ),
		),	
    );

	function __construct(){
		parent::__construct();
    }
    
    function table_query()
    {
        $this->db->select('`master`.getNamaLengkap(lw.NORM) PASIEN
        , lw.PESAN
        , `master`.getNamaLengkapPegawai(ap.NIP) OLEH
        , lw.TANGGAL');
        $this->db->from('layanan.wa_pasien lw');
        $this->db->join('aplikasi.pengguna ap','lw.OLEH = ap.ID','LEFT');
        $this->db->where('DATE(lw.TANGGAL) = CURDATE()');

        $this->db->order_by('lw.TANGGAL', 'DESC');

        if($this->input->post('search[value]')){
            $this->db->group_start();
            $this->db->like('lw.NORM', $this->input->post('search[value]'));
            // $this->db->or_like('`master`.getNamaLengkap(lw.NORM)', $this->input->post('search[value]'));
            $this->db->group_end();
            // $this->db->where_in('his.STATUS',$this->input->post('status'));            
        }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
