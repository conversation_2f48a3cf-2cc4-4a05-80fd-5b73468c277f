<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Fasthug extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/ruanganIntensif/FasthugModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(2);
        $id_fasthug = $this->uri->segment(3);
        $getFasthug = $this->FasthugModel->getFasthug($id_fasthug);

        $data = array(
            'id_fasthug' => $id_fasthug,
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'getFasthug' => $getFasthug,
        );
        $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/fasthug', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();
                // echo "<pre>";
                // print_r($post['id_fasthug']);
                // echo "</pre>";
                $dataFasthug = array(
                    'id'            => isset($post['id_fasthug']) ? $post['id_fasthug'] : "",
                    'nopen'         => $post['nopen'],
                    'feeding'       => isset($post['feeding']) ? $post['feeding'] : "",
                    'analgesia'     => isset($post['analgesia']) ? $post['analgesia'] : "",
                    'sedation'      => isset($post['sedation']) ? $post['sedation'] : "",
                    'trombolytic'   => isset($post['trombolytic']) ? $post['trombolytic'] : "",
                    'head'          => isset($post['head']) ? $post['head'] : "",
                    'stress'        => isset($post['stress']) ? $post['stress'] : "",
                    'glucose'       => isset($post['glucose']) ? $post['glucose'] : "",
                    'keterangan'    => isset($post['keterangan']) ? $post['keterangan'] : "",
                    'oleh'          => $this->session->userdata('id')
                );

                // echo "<pre>";
                // print_r($dataFasthug);
                // echo "</pre>";

                $this->db->trans_begin();

                if (!empty($post['id_fasthug'])) {
                    $this->db->replace('keperawatan.tb_fasthug', $dataFasthug);
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success_ubah');
                    }

                    echo json_encode($result);
                } else {
                    $this->db->insert('keperawatan.tb_fasthug', $dataFasthug);
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success_simpan');
                    }

                    echo json_encode($result);
                }
            } else if ($param == 'count') {
                $result = $this->FasthugModel->get_count();;
                echo json_encode($result);
            }
        }
    }

    public function datatables()
    {
        $result = $this->FasthugModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm edit_Fasthug" data-toggle="modal" data-id="' . $row->id . '"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row->created_at;
            $sub_array[] = $row->feeding;
            $sub_array[] = $row->analgesia;
            $sub_array[] = $row->sedation;
            $sub_array[] = $row->trombolytic;
            $sub_array[] = $row->head;
            $sub_array[] = $row->stress;
            $sub_array[] = $row->glucose;
            $sub_array[] = $row->USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->FasthugModel->total_count(),
            "recordsFiltered"   => $this->FasthugModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
