<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormulirG8Geriatri extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','geriatri/FormulirG8GeriatriModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(6);
      $id_g8 = $this->uri->segment(8);
      
      $data = array(
        'nokun' => $nokun,
        'id_g8' => isset($id_g8) ? $id_g8 : "",
        'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
        'getPengkajian' => $this->FormulirG8GeriatriModel->getPengkajian($id_g8),
        'listNafsuMakan' => $this->masterModel->referensi(1585),
        'listKehilanganBB' => $this->masterModel->referensi(1586),
        'listMobilitas' => $this->masterModel->referensi(1587),
        'listGangguanMemori' => $this->masterModel->referensi(1588),
        'listMassaTubuh' => $this->masterModel->referensi(1589),
        'listObatDiminum' => $this->masterModel->referensi(1590),
        'listKondisiKesehatan' => $this->masterModel->referensi(1591),
        'listUsia' => $this->masterModel->referensi(1592),
      );
      $this->load->view('Pengkajian/geriatri/FormulirG8Geriatri', $data);
    }

    public function indexRawatInap() {
      $nokun = $this->uri->segment(2);
      $id_g8 = $this->uri->segment(3);
      
      $data = array(
        'id_g8' => isset($id_g8) ? $id_g8 : "",
        'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
        'getPengkajian' => $this->FormulirG8GeriatriModel->getPengkajian($id_g8),
        'listNafsuMakan' => $this->masterModel->referensi(1585),
        'listKehilanganBB' => $this->masterModel->referensi(1586),
        'listMobilitas' => $this->masterModel->referensi(1587),
        'listGangguanMemori' => $this->masterModel->referensi(1588),
        'listMassaTubuh' => $this->masterModel->referensi(1589),
        'listObatDiminum' => $this->masterModel->referensi(1590),
        'listKondisiKesehatan' => $this->masterModel->referensi(1591),
        'listUsia' => $this->masterModel->referensi(1592),
      );
      $this->load->view('Pengkajian/geriatri/FormulirG8Geriatri', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $data = array(
            'nokun' => $post['nokun'],
            'nomr' => $post['nomr'],
            'nafsu_makan' => isset($post['nafsu_makan']) ? $post['nafsu_makan']: "",
            'kehilangan_bb' => isset($post['kehilangan_bb']) ? $post['kehilangan_bb']: "",
            'mobilitas' => isset($post['mobilitas']) ? $post['mobilitas']: "",
            'gangguan_memori' => isset($post['gangguan_memori']) ? $post['gangguan_memori']: "",
            'massa_tubuh' => isset($post['massa_tubuh']) ? $post['massa_tubuh']: "",
            'obat_diminum' => isset($post['obat_diminum']) ? $post['obat_diminum']: "",
            'kondisi_kesehatan' => isset($post['kondisi_kesehatan']) ? $post['kondisi_kesehatan']: "",
            'usia' => isset($post['usia']) ? $post['usia']: "",
            'total' => isset($post['totalG8Geriatri']) ? $post['totalG8Geriatri']: "",
            'imt' => isset($post['imt']) ? $post['imt']: "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_g8'])) {
            $dataTBBB = array(
              'data_source' => 29,
              'ref' => $post['id_g8'],
              'nomr' => $post['nomr'],
              'nokun' => $post['nokun'],
              'tb' => $post['tb'],
              'bb' => $post['bb'],
              'oleh' => $this->session->userdata('id')
            );
            
            $this->db->where('keperawatan.tb_g8_geriatri.id', $post['id_g8']);
            $this->db->update('keperawatan.tb_g8_geriatri', $data);
            $this->db->insert('db_pasien.tb_tb_bb', $dataTBBB);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_g8_geriatri', $data);
              $getIdG8 = $this->db->insert_id();
              
              $dataTBBB = array(
                'data_source' => 29,
                'ref' => $getIdG8,
                'nomr' => $post['nomr'],
                'nokun' => $post['nokun'],
                'tb' => $post['tb'],
                'bb' => $post['bb'],
                'oleh' => $this->session->userdata('id')
              );

              $this->db->insert('db_pasien.tb_tb_bb', $dataTBBB);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->FormulirG8GeriatriModel->get_count();
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->FormulirG8GeriatriModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editG8Geriatri" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
            $sub_array[] = $row -> total;
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->FormulirG8GeriatriModel->total_count(),
            "recordsFiltered"   => $this->FormulirG8GeriatriModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}