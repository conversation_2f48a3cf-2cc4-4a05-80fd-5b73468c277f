<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class LogbookModel extends CI_Model 
{
    private $db4;
    private $db5;

    public function __construct()
    {
        parent::__construct();
        $this->db4 = $this->load->database('196', true);
        $this->db5 = $this->load->database('238', true);
    }

    protected $_table_name    = 'aplikasi.pengguna';
    protected $_primary_key   = 'ID';
    protected $_order_by      = 'ID';
    protected $_order_by_type = 'DESC';

    // ------------------------ QUERY UNTUK CEK DATA / PILIH KINERJA ------------------------
    public function namaPegawai($id)
  {
    $query  = $this->db->query("SELECT master.getNamaLengkapPegawai(mp.NIP) NAMAPEGAWAI, ap.LOGIN USER_MASUK, ap.ID, sp.id_pegawai_simpeg
     FROM aplikasi.pengguna ap
     LEFT JOIN master.pegawai mp ON ap.NIP = mp.NIP
     LEFT JOIN akses_simrskd.link_simpeg sp ON ap.ID = sp.id_pengguna
     WHERE ap.ID = '$id'");

    return $query->row_array();
  }

  public function pilihListPengkajian($oleh)
    {
      $query  = $this->db5->query("SELECT *
        FROM dbsdm.kuantitas k
        WHERE k.PEGAWAI = '$oleh' AND k.STATUS = 1");

      return $query->result_array();
    }

    public function listKinerja($oleh, $id)
    {
      $query  = $this->db5->query("SELECT *
        FROM dbsdm.kuantitas k
        WHERE k.PEGAWAI = '$oleh' AND k.STATUS = 1 AND k.ID='$id'");

      return $query->row_array();
    }

    public function jumlahKinerjaJenis($kinerja)
    {
      $query = $this->db->query("SELECT *, COUNT(mp.kinerja) JUMLAH
        FROM akses_simrskd.tb_mapping mp
        WHERE mp.kinerja='$kinerja'");

      return $query->row_array();
    }

    public function mappingKinerjaJenis($kinerja)
    {
      $query = $this->db->query("SELECT tm.id ID_MAPPING, tm.id_pengguna_simrskd ID_PENGGUNA, tm.jenis JENIS
        , tm.kinerja KINERJA, tm.status STATUS_MAPPING, tj.id ID_JENIS
        , tj.isi ISI_JENIS, tj.status STATUS_JENIS
        FROM akses_simrskd.tb_mapping tm
        LEFT JOIN akses_simrskd.tb_jenis_isi_kinerja tj ON tj.id = tm.jenis
        WHERE tm.kinerja='$kinerja'");

      return $query->row_array();
    }

    public function cekDataLinkSimpeg($oleh)
    {
      $query = $this->db->query("SELECT COUNT(akun.id_pengguna) JUMLAH
        FROM akses_simrskd.link_simpeg akun
        WHERE akun.id_pengguna = '$oleh' AND akun.status=1");

      return $query->row_array();
    }

    public function cekIdLogbook()
    {
      $query = $this->db5->query("SELECT MAX(lb.ID) + 1 ID_LOGBOOK
        FROM dbsdm.logbook lb");

      return $query->row_array();
    }

    public function simpanLogKinerja($data)
    {
      $this->db->insert('keperawatan.tb_log_kinerja', $data);
      return $this->db->insert_id();
    }

    public function simpanGabungLogObservasiTindakan($data)
    {
      // $this->db->insert('keperawatan.coba', $data);
      $this->db5->insert('dbsdm.logbook', $data);
    }

    public function simpanSimpeg($dataSimpeg)
    {
      $this->db5->insert('dbsdm.logbook', $dataSimpeg);
    }

    // ------------------------ QUERY UNTUK PENGKAJIAN AWAL ------------------------

    public function kinerjaListLogbookJenisSatu($kinerja)
    {
     $query  = $this->db->query("SELECT kp.id_emr, kp.jenis,jp.jenis_pengkajian, ru.DESKRIPSI RUANGAN, p.NORM NORM
      , master.getNamaLengkap(p.NORM) PASIEN
      , kp.created_at TANGGAL_PEMBUATAN, map.kinerja ID_KINERJA_SIMPEG, map.jenis JENIS_MAPPING
      , jns.id JENIS_LOGBOOK_KINERJA
      FROM akses_simrskd.tb_mapping map
      LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
      LEFT JOIN keperawatan.tb_keperawatan kp ON kp.created_by = sim.id_pengguna
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
      LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
      LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = kp.id_emr AND lkd.jenis='1'
      LEFT JOIN db_master.tb_jenis_pengkajian jp ON jp.id = kp.jenis
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by
      LEFT JOIN akses_simrskd.tb_jenis_isi_kinerja jns ON jns.id = map.jenis
      WHERE map.kinerja='$kinerja' AND map.jenis='1' AND kp.`status`=1 AND kp.flag=1 AND YEAR(kp.created_at)= YEAR(CURDATE()) AND MONTH(kp.created_at)= MONTH(CURDATE())
      /*INI DIAKTIFIN KALO SUDAH BISA INSERT KE keperawatan.tb_log_kinerja_detail*/ AND lkd.id_log IS NULL
      ORDER BY kp.created_at DESC");

     return $query->result_array();
   }

  public function cekJenisSatu($kinerja)
  {
    $query  = $this->db->query("SELECT COUNT(kp.id_emr) JUMLAH, jnsisi.isi ISI

      FROM akses_simrskd.tb_mapping map

      LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
      LEFT JOIN keperawatan.tb_keperawatan kp ON kp.created_by = sim.id_pengguna
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
      LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
      LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = kp.id_emr AND lkd.jenis='1'
      LEFT JOIN db_master.tb_jenis_pengkajian jp ON jp.id = kp.jenis
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by
      LEFT JOIN akses_simrskd.tb_jenis_isi_kinerja jnsisi ON jnsisi.id = map.jenis

      WHERE map.kinerja='$kinerja' AND map.jenis='1'
      AND kp.`status`=1 
      AND kp.flag=1 AND YEAR(kp.created_at)=YEAR(CURDATE()) AND month(kp.created_at)=MONTH(CURDATE())
      AND lkd.id_log IS NULL");

    return $query->row_array();
  }

  public function historyPengisianLogbookPengkajian($oleh)
    {
      $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
        FROM keperawatan.tb_log_kinerja lg
        LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
        WHERE lg.oleh='$oleh' AND lg.status='1' AND month(lg.created_at)=MONTH(CURDATE())
        AND lgd.jenis='1'
        GROUP BY lg.id");

      return $query->result_array();
    }

    public function historyDetailKinPengisianLogbookPengkajian($idlog)
    {
      $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
        , jp.jenis_pengkajian JENIS_PENGKAJIAN
        , ru.DESKRIPSI RUANGAN
        , p.NORM NORM
        , kp.created_at TANGGAL_PENGKAJIAN
        FROM keperawatan.tb_log_kinerja lg
        LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
        LEFT JOIN keperawatan.tb_keperawatan kp ON lgd.ref = kp.id_emr
        LEFT JOIN db_master.tb_jenis_pengkajian jp ON jp.id = kp.jenis
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        WHERE lgd.id_log='$idlog' AND lg.status='1'");

      return $query->result_array();
    }

    // ------------------------ QUERY UNTUK PERENCANAAN ASUHAN KEPERAWATAN ------------------------

    public function kinerjaListLogbookJenisDua($kinerja)
    {
      $query  = $this->db->query("SELECT papa.id ID_ASU, 2 JENIS
        , akde.DESKRIPSI PERENCANAAN_ASUHAN_KEPERAWATAN
        , ru.DESKRIPSI RUANGAN, p.NORM NORM
        , master.getNamaLengkap(p.NORM) PASIEN
        , kp.created_at TANGGAL_PEMBUATAN, map.kinerja ID_KINERJA_SIMPEG, map.jenis JENIS_MAPPING
        FROM akses_simrskd.tb_mapping map
        LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_keperawatan kp ON kp.created_by = sim.id_pengguna
        RIGHT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan papa ON papa.id_emr = kp.id_emr
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akde ON akde.ID = papa.id_asuhan_keperawatan_detil
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = papa.id AND lkd.jenis=2
        LEFT JOIN db_master.tb_jenis_pengkajian jp ON jp.id = kp.jenis
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by
        WHERE map.kinerja='$kinerja' AND map.jenis=2 AND akde.JENIS=3 AND kp.`status`=1 AND lkd.id_log IS NULL AND kp.flag=1 AND YEAR(kp.created_at)= YEAR(CURDATE()) AND MONTH(kp.created_at)= MONTH(CURDATE())
        ORDER BY kp.created_at DESC
        ");

      return $query->result_array();
    }

    public function cekJenisDua($kinerja)
    {
      $query  = $this->db->query("SELECT COUNT(papa.id) JUMLAH

        FROM akses_simrskd.tb_mapping map

        LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_keperawatan kp ON kp.created_by = sim.id_pengguna
        RIGHT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan papa ON papa.id_emr = kp.id_emr
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akde ON akde.ID = papa.id_asuhan_keperawatan_detil
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = papa.id AND lkd.jenis=2
        LEFT JOIN db_master.tb_jenis_pengkajian jp ON jp.id = kp.jenis
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by

        WHERE map.kinerja='$kinerja' AND map.jenis=2 
        AND akde.JENIS=3
        AND kp.`status`=1 
        AND lkd.id_log IS NULL
        AND kp.flag=1 AND YEAR(kp.created_at)=YEAR(CURDATE()) AND month(kp.created_at)=MONTH(CURDATE())");

      return $query->row_array();
    }

  public function historyPengisianLogbookAsuhan($oleh)
    {
      $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
        FROM keperawatan.tb_log_kinerja lg
        LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
        WHERE lg.oleh='$oleh' AND lg.status='1' AND month(lg.created_at)=MONTH(CURDATE())
        AND lgd.jenis='2'
        GROUP BY lg.id");

      return $query->result_array();
    }

    public function historyDetailKinPengisianLogbookAsuhan($idlog)
    {
      $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
        , akde.DESKRIPSI PERENCANAAN_ASUHAN_KEPERAWATAN
        , ru.DESKRIPSI RUANGAN
        , p.NORM NORM
        , papa.created_at TANGGAL_PERENCANAAN
        FROM keperawatan.tb_log_kinerja lg
        LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
        LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan papa ON papa.id = lgd.ref AND lgd.jenis=2
        LEFT JOIN keperawatan.tb_keperawatan kp ON kp.id_emr = papa.id_emr AND kp.flag=1 AND kp.`status`=1
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akde ON akde.ID = papa.id_asuhan_keperawatan_detil AND akde.JENIS=3
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        WHERE lg.status='1' 
        /*ini tar jadi parameter*/
        AND lgd.id_log='$idlog' ");

      return $query->result_array();
    }

    // ------------------------ QUERY UNTUK OBSERVASI TINDAKAN ------------------------

    public function kinerjaListLogbookJenisTiga($kinerja)
    {
      $query  = $this->db->query("SELECT oted.id ID_OBSERVASI
        , 3 jenis
        , tikep.TINDAKAN_KEPERAWATAN
        , ru.DESKRIPSI RUANGAN, p.NORM NORM
        , master.getNamaLengkap(p.NORM) PASIEN
        , CONCAT(oti.tanggal, ' ',oti.jam) TANGGAL_PEMBUATAN, map.kinerja ID_KINERJA_SIMPEG, map.jenis JENIS_MAPPING
        , lkd.`*`

        FROM akses_simrskd.tb_mapping map

        LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_observasi_tindakan oti ON oti.oleh = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_observasi_tindakan_detail oted ON oted.id_observasi_tindakan = oti.id
        LEFT JOIN db_master.tb_tindakan_keperawatan tikep ON tikep.ID = oted.id_pak
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = oti.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.nopen
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = oted.id AND lkd.jenis=3
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = oti.oleh

        WHERE map.kinerja='$kinerja' AND map.jenis=3
        AND oti.`status`!=0 
        AND YEAR(oti.tanggal)=YEAR(CURDATE()) AND month(oti.tanggal)=MONTH(CURDATE())
        AND lkd.id_log IS NULL
        AND oted.id IS NOT NULL

        ORDER BY oti.tanggal DESC
        ");

      return $query->result_array();
    }

    public function cekJenisTiga($kinerja)
    {
      $query  = $this->db->query("SELECT COUNT(oted.id) JUMLAH

        FROM akses_simrskd.tb_mapping map

        LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_observasi_tindakan oti ON oti.oleh = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_observasi_tindakan_detail oted ON oted.id_observasi_tindakan = oti.id
        LEFT JOIN db_master.tb_tindakan_keperawatan tikep ON tikep.ID = oted.id_pak
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = oti.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.nopen
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = oted.id AND lkd.jenis=3
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = oti.oleh

        WHERE map.kinerja='$kinerja' AND map.jenis=3
        AND oti.`status`!=0
        AND YEAR(oti.tanggal)=YEAR(CURDATE()) 
        AND month(oti.tanggal)=MONTH(CURDATE())
        AND lkd.id_log IS NULL
        AND oted.id IS NOT NULL
        -- AND oti.tanggal=DATE(NOW()) 

        ORDER BY oti.tanggal ASC

        ");

      return $query->row_array();
    }

    public function queryGabunganInsertObservasi($kinerja)
    {
      $query  = $this->db->query("SELECT 
        map.kinerja ID_KINERJA_SIMPEG,
        tikep.TINDAKAN_KEPERAWATAN ISI_LOGBOOK, COUNT(oted.id_pak) JUMLAH


        FROM akses_simrskd.tb_mapping map

        LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_observasi_tindakan oti ON oti.oleh = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_observasi_tindakan_detail oted ON oted.id_observasi_tindakan = oti.id
        LEFT JOIN db_master.tb_tindakan_keperawatan tikep ON tikep.ID = oted.id_pak
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = oti.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.nopen
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = oted.id AND lkd.jenis=3
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = oti.oleh

        WHERE map.kinerja='$kinerja' AND map.jenis=3
        AND oti.`status`!=0 
        AND month(oti.tanggal)=MONTH(CURDATE())
        AND lkd.id_log IS NULL
        AND oted.id IS NOT NULL
        -- AND oti.tanggal=DATE(NOW()) 

        GROUP BY tikep.TINDAKAN_KEPERAWATAN
        ");

      return $query->result_array();
    }

  public function historyPengisianLogbookObservasi($oleh)
    {
      $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
        FROM keperawatan.tb_log_kinerja lg
        LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
        WHERE lg.oleh='$oleh' AND lg.status='1' AND month(lg.created_at)=MONTH(CURDATE())
        AND lgd.jenis='3'
        GROUP BY lg.id");

      return $query->result_array();
    }

    public function historyDetailKinPengisianLogbookObservasi($idlog)
    {
      $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
        , tikep.TINDAKAN_KEPERAWATAN TINDAKAN_KEPERAWATAN
        , ru.DESKRIPSI RUANGAN
        , p.NORM NORM
        , CONCAT(oti.tanggal, ' ',oti.jam) TANGGAL_TINDAKAN
        FROM keperawatan.tb_log_kinerja lg
        LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
        LEFT JOIN keperawatan.tb_observasi_tindakan_detail oted ON oted.id = lgd.ref AND lgd.jenis=3
        LEFT JOIN keperawatan.tb_observasi_tindakan oti ON oti.id = oted.id_observasi_tindakan
        LEFT JOIN db_master.tb_tindakan_keperawatan tikep ON tikep.ID = oted.id_pak
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = oti.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.nopen
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        WHERE lg.status='1'
        AND lgd.id_log='$idlog' ");

      return $query->result_array();
    }

  // ------------------------ QUERY UNTUK CPPT ------------------------

  public function kinerjaListLogbookJenisEmpat($kinerja)
  {
    $query  = $this->db->query("SELECT cp.id ID_CPPT, 4 JENIS
      , IF(cp.jenis=1, 'CPPT RJ','CPPT RI/IGD') JENIS_CPPT
      , ru.DESKRIPSI RUANGAN, p.NORM NORM
      , master.getNamaLengkap(p.NORM) PASIEN
      , cp.tanggal TANGGAL_PEMBUATAN, map.kinerja ID_KINERJA_SIMPEG, map.jenis JENIS_MAPPING

      FROM akses_simrskd.tb_mapping map

      LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
      LEFT JOIN keperawatan.tb_cppt cp ON cp.oleh = sim.id_pengguna
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = cp.nokun
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
      LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = cp.id AND lkd.jenis=4
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = cp.oleh

      WHERE map.kinerja='$kinerja' AND map.jenis=4 
      AND cp.`status`=1
      AND YEAR(cp.tanggal)=YEAR(CURDATE()) AND month(cp.tanggal)=MONTH(CURDATE())
      AND lkd.id_log IS NULL
      ORDER BY cp.tanggal DESC
      ");

    return $query->result_array();
  }

  public function cekJenisEmpat($kinerja)
  {
    $query  = $this->db->query("SELECT COUNT(cp.id) JUMLAH

      FROM akses_simrskd.tb_mapping map

      LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
      LEFT JOIN keperawatan.tb_cppt cp ON cp.oleh = sim.id_pengguna
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = cp.nokun
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
      LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = cp.id AND lkd.jenis=4
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = cp.oleh

      WHERE map.kinerja='$kinerja' AND map.jenis=4 
      AND cp.`status`=1
      AND YEAR(cp.tanggal)=YEAR(CURDATE())
      AND month(cp.tanggal)=MONTH(CURDATE())
      AND lkd.id_log IS NULL
      ");

    return $query->row_array();
  }

  public function historyPengisianLogbookCppt($oleh)
    {
      $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
        FROM keperawatan.tb_log_kinerja lg
        LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
        WHERE lg.oleh='$oleh' AND lg.status='1' AND month(lg.created_at)=MONTH(CURDATE())
        AND lgd.jenis='4'
        GROUP BY lg.id");

      return $query->result_array();
    }

    public function historyDetailKinPengisianLogbookCppt($idlog)
    {
      $query = $this->db->query("SELECT cp.id ID_CPPT, 4 JENIS
      , IF(cp.jenis=1, 'CPPT RJ','CPPT RI/IGD') JENIS_CPPT
      , ru.DESKRIPSI RUANGAN, p.NORM NORM
      , master.getNamaLengkap(p.NORM) PASIEN
      , cp.tanggal TANGGAL_PEMBUATAN, map.kinerja ID_KINERJA_SIMPEG, map.jenis JENIS_MAPPING

      FROM akses_simrskd.tb_mapping map

      LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
      LEFT JOIN keperawatan.tb_cppt cp ON cp.oleh = sim.id_pengguna
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = cp.nokun
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
      LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = cp.id AND lkd.jenis=4
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = cp.oleh

      WHERE lkd.id_log='$idlog' AND map.jenis=4 
      AND cp.`status`=1
      AND month(cp.tanggal)=MONTH(CURDATE())
      AND lkd.id_log IS NOT NULL");

      return $query->result_array();
    }

  // ------------------------ QUERY UNTUK DIAGNOSA KEPERAWATAN ------------------------

    public function kinerjaListLogbookJenisLima($kinerja)
    {
      $query  = $this->db->query("SELECT kp.id_emr
        , kp.jenis
        , tk.DESKRIPSI, tk.ID_VARIABEL ID
        , master.getNamaLengkap(p.NORM) PASIEN
        , p.NORM NORM
        , papa.created_at TANGGAL_PEMBUATAN, map.kinerja ID_KINERJA_SIMPEG, map.jenis JENIS_MAPPING
        , IF(DATE(papa.created_at)= DATE(loki.created_at),0,1) stat
        , lkd.id_log
        FROM akses_simrskd.tb_mapping map
        LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_keperawatan kp ON kp.created_by = sim.id_pengguna
        RIGHT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan papa ON papa.id_emr = kp.id_emr
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil tak ON papa.id_asuhan_keperawatan_detil = tak.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan tk ON tk.ID = tak.ID_ASUHAN
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = papa.id_emr AND lkd.jenis=5
        LEFT JOIN keperawatan.tb_log_kinerja loki ON loki.id = lkd.id_log
        LEFT JOIN db_master.tb_jenis_pengkajian jp ON jp.id = kp.jenis
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by
        WHERE map.kinerja='$kinerja' AND map.jenis=5 AND kp.`status`=1 AND kp.flag=1 AND YEAR(kp.created_at)= YEAR(CURDATE()) AND MONTH(kp.created_at)= MONTH(CURDATE()) AND papa.created_at IS NOT NULL AND lkd.id_log IS NULL
        GROUP BY kp.id_emr, tk.ID_VARIABEL
        ORDER BY papa.created_at DESC
        ");

      return $query->result_array();
    }

    public function cekJenisLima($kinerja)
    {
      $query  = $this->db->query("SELECT SUM(a.JUML) JUMLAH FROM
        (SELECT kp.id_emr
        , kp.jenis
        , tk.DESKRIPSI, tk.ID_VARIABEL ID
        , master.getNamaLengkap(p.NORM) PASIEN
        , papa.created_at TANGGAL_PEMBUATAN, map.kinerja ID_KINERJA_SIMPEG, map.jenis JENIS_MAPPING
        , 1 JUML
        FROM akses_simrskd.tb_mapping map

        LEFT JOIN akses_simrskd.link_simpeg sim ON sim.id_pengguna = map.id_pengguna_simrskd
        LEFT JOIN keperawatan.tb_keperawatan kp ON kp.created_by = sim.id_pengguna
        RIGHT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan papa ON papa.id_emr = kp.id_emr
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil tak ON papa.id_asuhan_keperawatan_detil = tak.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan tk ON tk.ID = tak.ID_ASUHAN
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
        LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
        LEFT JOIN keperawatan.tb_log_kinerja_detail lkd ON lkd.ref = papa.id_emr AND lkd.jenis=5 
        LEFT JOIN keperawatan.tb_log_kinerja loki ON loki.id = lkd.id_log 
        LEFT JOIN db_master.tb_jenis_pengkajian jp ON jp.id = kp.jenis
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by

        WHERE map.kinerja='$kinerja' AND map.jenis=5
        AND kp.`status`=1 
        AND kp.flag=1 AND YEAR(kp.created_at)=YEAR(CURDATE()) AND month(kp.created_at)=MONTH(CURDATE())
        AND papa.created_at IS NOT NULL
        AND lkd.id_log IS NULL
        GROUP BY kp.id_emr, tk.ID_VARIABEL) a
        ");

    return $query->row_array();
  }

  public function historyPengisianLogbookDiagnosa($oleh)
  {
    $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
      FROM keperawatan.tb_log_kinerja lg
      LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
      WHERE lg.oleh='$oleh' AND lg.status='1' AND month(lg.created_at)=MONTH(CURDATE())
      AND lgd.jenis='5'
      GROUP BY lg.id");

    return $query->result_array();
  }

  public function historyDetailKinPengisianLogbookDiagnosa($idlog)
  {
    $query = $this->db->query("SELECT lg.id ID, lg.created_at TANGGAL_JAM, lg.keterangan_kinerja INDIKATOR
      , papa.id_emr
      , tk.DESKRIPSI DIAGNOSA
      , ru.DESKRIPSI RUANGAN
      , p.NORM NORM
      , papa.created_at TANGGAL_PENGKAJIAN
      FROM keperawatan.tb_log_kinerja lg
      LEFT JOIN keperawatan.tb_log_kinerja_detail lgd ON lgd.id_log = lg.id
      RIGHT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan papa ON papa.id_emr = lgd.ref AND lgd.jenis=5
      LEFT JOIN db_master.tb_asuhan_keperawatan_detil tak ON papa.id_asuhan_keperawatan_detil = tak.ID
      LEFT JOIN db_master.tb_asuhan_keperawatan tk ON tk.ID = tak.ID_ASUHAN
      LEFT JOIN keperawatan.tb_keperawatan kp ON kp.id_emr = papa.id_emr AND kp.flag=1 AND kp.`status`=1
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN master.ruangan ru ON ru.ID = pk.RUANGAN
      WHERE 
      lg.status='1' AND 
      DATE(lg.created_at)!=DATE(papa.created_at)
      AND lgd.id_log='$idlog' 

      GROUP BY kp.id_emr, tk.ID_VARIABEL");

    return $query->result_array();
  }

}

/* End of file ProfileModel.php */
/* Location: ./application/models/ProfileModel.php */
