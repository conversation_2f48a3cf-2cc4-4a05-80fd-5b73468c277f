<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ObservasiIntakeOralPasienModel extends MY_Model {

  public function listHistoryOIOP($nomr)
  {
    $query = $this->db->query("SELECT
                                  pp.NORM,
                                  oiop.id,
                                  oiop.nokun,
                                  oiop.created_at tanggal,
                                  oiop.status_observasi,
                                  ap.NAMA,
                                  v.variabel diet  
                                FROM
                                  keperawatan.tb_observasi_intake_oral_pasien oiop
                                  LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = oiop.nokun
                                  LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                                  LEFT JOIN aplikasi.pengguna ap ON ap.ID = oiop.oleh
                                  LEFT JOIN db_master.variabel v on v.id_variabel = oiop.diet
                                WHERE
                                  pp.NORM = '$nomr' 
                                  AND oiop.`status_observasi` = 1
                                ORDER BY oiop.created_at DESC
                              ");
    return $query;
  }

    public function getPengkajian($id_intake)
    {
      $query = $this->db->query(
        'SELECT oiop.`*` FROM keperawatan.tb_observasi_intake_oral_pasien oiop
        WHERE oiop.id = "'.$id_intake.'"'
      );
      return $query->row_array();
    }

}
?>
