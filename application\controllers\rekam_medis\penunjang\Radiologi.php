<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Radiologi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();
                $kode = $this->pengkajianAwalModel->generateNoOrderRad();

                $dataOrderRadiologi = array(
                    'NOMOR'       => $kode,
                    'KUNJUNGAN'   => $post["kunjungan"],
                    'TANGGAL'     => isset($post['tglRadiologiSimpelRi']) ? date('Y-m-d H:i:s',strtotime($post['tglRadiologiSimpelRi'])): date("Y-m-d H:i:s"),
                    // 'TANGGAL'     => date("Y-m-d H:i:s"),
                    'DOKTER_ASAL'            => isset($post['dokter']) ? $post['dokter'] : null,
                    'TUJUAN'                 => isset($post['tujuan']) ? $post['tujuan'] : "",
                    'CITO'                   => isset($post['citoKlik']) ? $post['citoKlik'] : "0",
                    'OLEH'                   => $this->session->userdata('id'),
                    // 'ALASAN'                 => $post["alasan"],
                    'DIAGNOSA_KLINIS'        => isset($post['diagnosaKlinis']) ? $post['diagnosaKlinis'] : "0",
                    'TANGGAL_KEMO'           => isset($post['tanggalKemoDiagnosaKlinisInput']) && $post['tanggalKemoDiagnosaKlinisInput'] != "" ? date('Y-m-d',strtotime($post['tanggalKemoDiagnosaKlinisInput'])): null,
                    'TANGGAL_OPERASI'        => isset($post['tanggalOperasiDiagnosaKlinisInput']) && $post['tanggalOperasiDiagnosaKlinisInput'] != "" ? date('Y-m-d',strtotime($post['tanggalOperasiDiagnosaKlinisInput'])): null,
                    'TANGGAL_ULANG'          => isset($post['tanggalUlangDiagnosaKlinisInput']) && $post['tanggalUlangDiagnosaKlinisInput'] != "" ? date('Y-m-d',strtotime($post['tanggalUlangDiagnosaKlinisInput'])): null,
                    'TANGGAL_KONTROL'        => isset($post['tanggalKontrolDiagnosaKlinisInput']) && $post['tanggalKontrolDiagnosaKlinisInput'] != "" ? date('Y-m-d',strtotime($post['tanggalKontrolDiagnosaKlinisInput'])): null,
                    'TANGGAL_RECIST'        => isset($post['tanggalRecistDiagnosaKlinisInput']) && $post['tanggalRecistDiagnosaKlinisInput'] != "" ? date('Y-m-d',strtotime($post['tanggalRecistDiagnosaKlinisInput'])): null,
                    'TANGGAL_LAINNYA'        => isset($post['tanggalLainnyaDiagnosaKlinisInput']) && $post['tanggalLainnyaDiagnosaKlinisInput'] != "" ? date('Y-m-d',strtotime($post['tanggalLainnyaDiagnosaKlinisInput'])): null,
                    'ISIAN_LAINNYA' => $post["keteranganDiagnosaKlinisInput"],
                    'KLINISI'                => $post["klinisi"],
                    'TB'                     => $post["tinggiBadanRadiologiOrder"],
                    'BB'                     => $post["beratBadanRadiologiOrder"],
                    'UREUM'                  => $post["ureumRadiologiOrder"],
                    'KREATININ'              => $post["kreatininRadiologiOrder"],
                    'DOSIS'                  => $post["dosisRadiologiOrder"],
                    'DOSIS_FDG'              => $post["dosisFdgRadiologiOrder"],
                    'TANGGAL_RENCANA'        => isset($post['tglRencanaRadiologiRi']) ? $post['tglRencanaRadiologiRi'] : date('Y-m-d'),
                    'JENIS'       => 2,
                );
                // echo "<pre>"; print_r($dataOrderRadiologi); echo "</pre>"; exit();

                $dataDetailOrderRadiologi = array();
                $index = 0;
                $id = array();

                if (isset($post['tindakanRadiologi'])) {
                    foreach ($post['tindakanRadiologi'] as $input) {
                        if ($post['tindakanRadiologi'][$index] != "" && !in_array(($post['tindakanRadiologi'][$index]), $id)) {
                            array_push($id, $post['tindakanRadiologi'][$index]);
                            array_push(
                                $dataDetailOrderRadiologi,
                                array(
                                    'ORDER_ID' => $kode,
                                    'TINDAKAN' => $post['tindakanRadiologi'][$index],
                                )
                            );
                        }
                        $index++;
                    }
                }

                $this->db->trans_begin();
                $this->db->insert('layanan.order_rad', $dataOrderRadiologi);
                $this->db->insert_batch('layanan.order_detil_rad', $dataDetailOrderRadiologi);
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success', 'kode' => $kode);
                }

                echo json_encode($result);
            } elseif ($param == 'batal') {
                $data = $this->pengkajianAwalModel->cekStatusRadiologi();
                if ($data['STATUS'] == 1) {
                    $this->db->set('STATUS', 0);
                    $this->db->where('NOMOR', $this->input->post('id'));
                    $this->db->update('layanan.order_rad');
                    $result = array('status' => 'success', 'pesan' => 'Berhasil Di Batalkan');
                } elseif ($data['STATUS'] == 2) {
                    $result = array('status' => 'failed', 'pesan' => 'Status Sudah Di Terima');
                } elseif ($data['STATUS'] == 0) {
                    $result = array('status' => 'failed', 'pesan' => 'Status Di Batalkan');
                }

                echo json_encode($result);
            }
        }
    }

    public function simpanRadDetailView()
    {
        $post = $this->input->post();
        $data = array(
            'ORDER_ID' => $post['kode'], 
            'TINDAKAN' => $post['idsimpel'], 
            'REF' => $post['id'], 
        );
        $this->db->insert('layanan.order_detil_rad_view', $data);
    }

    public function index()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
        $resultTindakanRadiologi = $this->masterModel->tindakanPenunjang('105100101');
        $dataTindakanRadiologi = array();
        foreach ($resultTindakanRadiologi->result() as $tindakanRadiologi) {
            $resultTindakanRadiologiSub = $this->masterModel->tindakanPenunjang('105100101', $tindakanRadiologi->id);
            $jenisTindakan = array();
            if ($resultTindakanRadiologiSub->num_rows() > 0) {
                $jenisTindakan['id'] = $tindakanRadiologi->id;
                $jenisTindakan['tindakan'] = $tindakanRadiologi->tindakan;
                $jenisTindakan['jenis'] = $tindakanRadiologi->deskripsi;
                $subJenisTindakan = array();
                foreach ($resultTindakanRadiologiSub->result() as $subTindakanRadiologi) {
                    $subSubJenisTindakan = array();
                    $resultTindakanRadiologiSubSub = $this->masterModel->tindakanPenunjang('105100101', $subTindakanRadiologi->id);
                    if ($resultTindakanRadiologiSubSub->num_rows() > 0) {
                        $subSubSubJenisTindakan = array();
                        $subSubJenisTindakan['id'] = $subTindakanRadiologi->id;
                        $subSubJenisTindakan['tindakan'] = $subTindakanRadiologi->tindakan;
                        $subSubJenisTindakan['jenis'] = $subTindakanRadiologi->deskripsi;
                        foreach ($resultTindakanRadiologiSubSub->result() as $subSubTindakanRadiologi) {
                            $subSubSubSubJenisTindakan = array();
                            $subSubSubSubJenisTindakan['id'] = $subSubTindakanRadiologi->id;
                            $subSubSubSubJenisTindakan['tindakan'] = $subSubTindakanRadiologi->tindakan;
                            $subSubSubSubJenisTindakan['jenis'] = $subSubTindakanRadiologi->deskripsi;
                            $subSubSubSubJenisTindakan['subJenis'] = $subSubTindakanRadiologi->deskripsi;
                            $subSubSubJenisTindakan[] = $subSubSubSubJenisTindakan;
                        }
                        $subSubJenisTindakan['subJenis'] = $subSubSubJenisTindakan;
                    } else {
                        $subSubJenisTindakan['id'] = $subTindakanRadiologi->id;
                        $subSubJenisTindakan['tindakan'] = $subTindakanRadiologi->tindakan;
                        $subSubJenisTindakan['jenis'] = $subTindakanRadiologi->deskripsi;
                    }
                    $subJenisTindakan[] = $subSubJenisTindakan;
                }
                $jenisTindakan['subJenis'] = $subJenisTindakan;
            } else {
                $jenisTindakan['id'] = $tindakanRadiologi->id;
                $jenisTindakan['tindakan'] = $tindakanRadiologi->tindakan;
                $jenisTindakan['jenis'] = $tindakanRadiologi->deskripsi;
            }
            $dataTindakanRadiologi[] = $jenisTindakan;
        }
        
        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'hOrderRadiologi' => $this->pengkajianAwalModel->historyOrderRadiologi($pasien['NORM']),
            'dupHisRadiologi' => $this->pengkajianAwalModel->dupHisRadiologi($pasien['NORM']),
            'dataTindakanRadiologi' => $dataTindakanRadiologi,
            'ketersediaan_foto' => $this->masterModel->referensi(735),
            'diagnosaKlinis' => $this->masterModel->referensi(1769),
            'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
            'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
            'listDrUmum' => $this->masterModel->listDrUmum(),
        );

        $this->load->view('rekam_medis/penunjang/radiologi/index', $data);
    }
    public function getTindakanTerakhir()
    {
        $id = $this->input->post('id');
        $norm = $this->input->post('norm');
        $getTindakanTerakhir = $this->pengkajianAwalModel->cekTinRadTerakhir($norm, $id);
        $result = array(
            'id_tindakan' => $getTindakanTerakhir['ID_TINDAKAN'], 
            'tindakan' => $getTindakanTerakhir['NAMA_TINDAKAN_EMR'], 
            'tanggal' => $getTindakanTerakhir['TGL_TINDAKAN_TERAKHIR'], 
            'dokter' => $getTindakanTerakhir['NAMA_DOKTER_ORDER']
        );
        echo json_encode($result);
    }
}
