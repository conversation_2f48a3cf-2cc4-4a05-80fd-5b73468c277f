<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TreatmentDoseModel extends CI_Model {

  public function tanggalTD($norm)
  {
  	$query = $this->db->query("
    SELECT a.id idori, a.nokun NOKUN, DATE_FORMAT(a.tanggal,'%d/%m/%Y %H:%i:%s') TANGGAL
    ,IFNULL((
    SELECT m1.id
              FROM medis.tb_treatmentDoseDr m1 
              LEFT JOIN medis.tb_treatmentDoseDr m2
              ON (m1.idubah = m2.idubah AND m1.id < m2.id) 
              AND m2.nomr ='$norm' 
              AND m2.status =1
              WHERE m2.id IS NULL
              AND m1.nomr ='$norm' 
              AND m1.status =1
              AND m1.idubah=a.id
              AND m1.idubah IS NOT NULL
              ORDER BY m1.idubah asc
        ),a.id) id
    FROM 
    medis.tb_treatmentDoseDr a
    WHERE a.nomr='$norm'
    and a.idubah IS NULL
    AND a.status=1
    ORDER BY a.id desc
  	");
  	return $query->result_array();
  }

  public function tblTreatmentDose($nomr)
  {
    $query  = $this->db->query("SELECT td.`*`, DATE_FORMAT(td.dateTm,'%d-%m-%Y') TGLTM
                               FROM medis.tb_treatmentDose td
                               LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = td.nokun
                               LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                               WHERE pp.NORM ='$nomr' #AND MONTH(td.dateTm) =  MONTH(NOW())
                               ORDER BY td.dateTm ASC");

    return $query->result();
  }

  public function treatmentDoseDr($id)
  {
    $str="SELECT tb.*
            FROM medis.tb_treatmentDoseDr tb
            WHERE tb.id='$id' 
          AND tb.status='1'";
    $query  = $this->db->query($str);
    // echo $str;exit;
   return $query->row_array();
  }

  public function treatmentDoseDrhistory($id)
  {
    $str="SELECT *
    FROM 
    (
      SELECT *,'1' AS unisort
      FROM 
      (SELECT tb2.*,mv1.variabel vsimulation
        ,mv2.variabel vtps
        ,mv3.variabel vctsimulator
        ,ap.NAMA voleh
              FROM medis.tb_treatmentDoseDr tb
              LEFT JOIN medis.tb_treatmentDoseDr tb2 ON tb.idubah=tb2.id AND tb.nomr=tb2.nomr
       
              LEFT JOIN db_master.variabel mv1 ON tb2.simulation=mv1.id_variabel AND mv1.id_referensi=805
              LEFT JOIN db_master.variabel mv2 ON tb2.tps=mv2.id_variabel AND mv2.id_referensi=806
              LEFT JOIN db_master.variabel mv3 ON tb2.ctSimulator=mv3.id_variabel AND mv3.id_referensi=807
              LEFT JOIN aplikasi.pengguna ap ON tb2.oleh=ap.ID
              WHERE tb.id='$id' 
            AND tb2.status='1'
            ORDER BY tb2.revisi asc, tb2.tanggal asc
    ) aa
    UNION
    SELECT *,'1' AS unisort
    FROM 
    (SELECT tb.*,mv1.variabel vsimulation
      ,mv2.variabel vtps
      ,mv3.variabel vctsimulator
      ,ap.NAMA voleh
                  FROM medis.tb_treatmentDoseDr tb
          
                  LEFT JOIN db_master.variabel mv1 ON tb.simulation=mv1.id_variabel AND mv1.id_referensi=805
                  LEFT JOIN db_master.variabel mv2 ON tb.tps=mv2.id_variabel AND mv2.id_referensi=806
                  LEFT JOIN db_master.variabel mv3 ON tb.ctSimulator=mv3.id_variabel AND mv3.id_referensi=807
                  LEFT JOIN aplikasi.pengguna ap ON tb.oleh=ap.ID
                  WHERE tb.id='$id' 
                  AND tb.idubah IS null
                  AND tb.status='1'
                  ORDER BY tb.revisi asc, tb.tanggal asc
    ) ab        
                
            
      UNION
    
    SELECT *,'2' AS unisort
    FROM 
    (SELECT tb2.*,mv1.variabel vsimulation
        ,mv2.variabel vtps
        ,mv3.variabel vctsimulator
        ,ap.NAMA voleh
                    FROM medis.tb_treatmentDoseDr tb
                    LEFT JOIN medis.tb_treatmentDoseDr tb2 ON tb.idubah=tb2.idubah AND tb.nomr=tb2.nomr
             
                    LEFT JOIN db_master.variabel mv1 ON tb2.simulation=mv1.id_variabel AND mv1.id_referensi=805
                    LEFT JOIN db_master.variabel mv2 ON tb2.tps=mv2.id_variabel AND mv2.id_referensi=806
                    LEFT JOIN db_master.variabel mv3 ON tb2.ctSimulator=mv3.id_variabel AND mv3.id_referensi=807
                    LEFT JOIN aplikasi.pengguna ap ON tb2.oleh=ap.ID
                    WHERE tb.id='$id' 
                  AND tb2.status='1'
                  ORDER BY tb2.revisi asc, tb2.tanggal asc
       ) b
       ) tbu 
       ORDER BY tbu.unisort ASC,tbu.revisi asc";
    $query  = $this->db->query($str);
    // echo "<pre>".$str;exit;
   return $query->result_array();
  }

  public function treatmentDoseFmhsitory($idtd)
  {
    $str="SELECT tdf.tanggal,tdf.revisi, tdf.approvedAt,tdf.approvedBy idapprovedby, master.getNamaLengkapPegawai(ap.NIP) dibuatby, master.getNamaLengkapPegawai(ap1.NIP) approvedby, $idtd as idTd
    FROM 
    
       medis.tb_treatmentdoseFisikamedis tdf 
        LEFT JOIN aplikasi.pengguna ap ON tdf.oleh=ap.ID
        LEFT JOIN aplikasi.pengguna ap1 ON tdf.approvedBy=ap1.ID
        WHERE tdf.idTD='$idtd'
        AND tdf.status='1'
        GROUP BY tdf.revisi
      ORDER BY tdf.revisi ASC, tdf.id ASC";
    $query  = $this->db->query($str);
    // echo $str;exit;
   return $query->result_array();
  }


  public function simpanTreatmentDose($data)
  {
    $this->db->trans_begin();
    $this->db->insert('medis.tb_treatmentDose', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function approvetdfm($data)
  {
    if($data['idTD']){
      $this->db->trans_begin();
        $str="UPDATE  medis.tb_treatmentdoseFisikamedis SET approvedAt=now(),approvedBy='".$data['oleh']."'
        WHERE idTD='".$data['idTD']."'";
        $str.=$data['revisi']>0?(" AND revisi='".$data['revisi']."'"):' AND revisi IS NULL';
        // echo $str;exit;
        $this->db->query($str);
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $str1="select master.getNamaLengkapPegawai(ap1.NIP) nama
        from medis.tb_treatmentdoseFisikamedis rev 
        LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = rev.approvedBy
          WHERE rev.idTD='".$data['idTD']."'";
        $str1.=$data['revisi']>0?(" AND rev.revisi='".$data['revisi']."'"):' AND rev.revisi IS NULL';
        // echo $str1;exit;
        $query= $this->db->query($str1);
        $ret= $query->row_array();

        $result = array('status' => 'success','oleh'=>$ret['nama']);
      }
    }else{
      $result = array('status' => 'tidak ada ID');
    }
    echo json_encode($result);
  }

  public function treatmentDoseFm($idtd,$revisi=null)
  {
    $revisi =($revisi !== null)?(($revisi === "")?"":$revisi+1):null;
    $strxx="SELECT 
    tbu.*
    ,app.vrevisi
    ,app.vapprovedBy
    ,app.approvename
    FROM 
    (SELECT *,IFNULL(idubah,id) AS unisort, 1 AS lem FROM
        (
          SELECT m1.*
          FROM medis.tb_treatmentdoseFisikamedis m1 
          LEFT JOIN medis.tb_treatmentdoseFisikamedis m2
          ON (m1.idubah = m2.idubah AND m1.id < m2.id) 
          AND m2.idTD ='$idtd' 
		        ".($revisi>0?"AND m2.revisi < $revisi":"")."
		        ".($revisi ===""?"AND m2.revisi IS NULL":"")."
          AND m2.status =1
          WHERE m2.id IS NULL
          AND m1.idTD ='$idtd' 
          AND m1.status =1
          ".($revisi>0?"AND m1.revisi < $revisi":"")."
          ".($revisi ===""?"AND m1.revisi IS NULL":"")."
          AND m1.idubah IS NOT NULL
          ORDER BY m1.idubah asc
        ) b 
        
        UNION all
        SELECT *, IFNULL(idubah,id) AS unisort, 1 AS lem
        FROM 
        ( SELECT tdf.* 
          from medis.tb_treatmentdoseFisikamedis tdf  
          LEFT join
          (
            SELECT m1.*
            FROM medis.tb_treatmentdoseFisikamedis m1 
            LEFT JOIN medis.tb_treatmentdoseFisikamedis m2
            ON (m1.idubah = m2.idubah AND m1.id < m2.id) 
            AND m2.idTD ='$idtd' 
		        ".($revisi>0?"AND m2.revisi < $revisi":"")."
		        ".($revisi ===""?"AND m2.revisi IS NULL":"")."
            AND m2.status =1
            WHERE m2.id IS NULL
            AND m1.idTD ='$idtd' 
            AND m1.status =1
		        ".($revisi>0?"AND m1.revisi < $revisi":"")."
		        ".($revisi ===""?"AND m1.revisi IS NULL":"")."
            AND m1.idubah IS NOT NULL
          ) tada ON tdf.id=tada.idubah
          WHERE tdf.idTD ='$idtd' 
          AND tdf.status =1 
          AND tdf.idubah IS NULL 
          ".($revisi>0?"AND (tdf.revisi < $revisi OR tdf.revisi IS NULL)":"")."
          ".($revisi ===""?"AND tdf.revisi IS NULL":"")."
          AND tada.id IS null
          ORDER BY tdf.id asc
        ) a 
           
       ) tbu 
       LEFT join
    		(SELECT 1 AS lem, rev.revisi vrevisi, rev.approvedBy vapprovedBy, trim(master.getNamaLengkapPegawai(ap1.NIP)) approvename
	      	from medis.tb_treatmentdoseFisikamedis rev 
	      	LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = rev.approvedBy
	      	WHERE rev.idTD='$idtd' 
	      	ORDER BY rev.revisi desc
	      	LIMIT 1
		  ) app ON app.lem=tbu.lem
       
       ORDER BY tbu.unisort ASC,tbu.idubah ASC,tbu.revisi,tbu.id asc        
     ";

     $str="SELECT 
          tbu.*
          ,app.vrevisi
          ,app.vapprovedBy
          ,app.approvename
          FROM 
          (
            SELECT m1.*, ifnull(m1.idubah,m1.id) urut, ifnull(m1.revisi,0) rev, 1 AS lem
            FROM medis.tb_treatmentdoseFisikamedis m1 
            LEFT JOIN medis.tb_treatmentdoseFisikamedis m2
            ON ( ifnull(m1.idubah,m1.id) = ifnull(m2.idubah,m2.id) AND  m1.id < m2.id) 
            AND m2.idTD ='$idtd' 
            AND m2.status =1
           ".($revisi>0?" AND IFNULL(m2.revisi,0) < $revisi":"")."
		        ".($revisi ===""?"AND m2.revisi IS NULL":"")."
           
           WHERE 1 
           AND m2.id IS NULL
           AND m1.idTD ='$idtd' 
           AND m1.status =1
           ".($revisi>0?" HAVING rev < $revisi":"")."
		        ".($revisi ===""?"AND m1.revisi IS NULL":"")."
           #HAVING rev<=1
           ORDER BY urut asc
           ) tbu 
            LEFT join
         (SELECT 1 AS lem, rev.revisi vrevisi, rev.approvedBy vapprovedBy, trim(master.getNamaLengkapPegawai(ap1.NIP)) approvename
           from medis.tb_treatmentdoseFisikamedis rev 
           LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = rev.approvedBy
           WHERE rev.idTD='$idtd' 
           ORDER BY rev.revisi desc
           LIMIT 1
       ) app ON app.lem=tbu.lem
       
       ORDER BY tbu.urut ASC,tbu.revisi,tbu.id asc ";

      $query  = $this->db->query($str);
    //  echo "<pre>".$str;exit;
    return $query->result_array();
  }

  public function treatmentDoseradiogRemarks($idtd){

    $str="SELECT a.jfield,b.approvedBy

          FROM 
          (
              SELECT 1 AS lem,COUNT(1) jfield
              FROM medis.tb_treatmentdoseFisikamedis m1 
              LEFT JOIN medis.tb_treatmentdoseFisikamedis m2
              ON (ifnull(m1.idubah,m1.id) = ifnull(m2.idubah,m2.id) AND m1.id < m2.id) 
              AND m2.idTD ='$idtd'
              AND m2.status =1
              WHERE 1 
              and m2.id IS NULL
              AND m1.idTD ='$idtd'
              AND m1.status =1
          ) a
          LEFT JOIN 
          (
            SELECT 1 AS lem,
            m1.approvedBy, IFNULL(m1.revisi,0) rev
            FROM medis.tb_treatmentdoseFisikamedis m1 
            LEFT JOIN medis.tb_treatmentdoseFisikamedis m2
            ON (ifnull(m1.idubah,m1.id) = ifnull(m2.idubah,m2.id) AND  m1.id < m2.id) 
            AND m2.idTD ='$idtd'
            AND m2.status =1
            WHERE 1 
            and m2.id IS NULL
            AND m1.idTD ='$idtd'
            AND m1.status =1
            GROUP BY rev
            ORDER BY rev desc
            LIMIT 1
            ) b ON a.lem=b.lem 
            ";
      $query  = $this->db->query($str);
    //  echo "<pre>".$str;exit;
    return $query->row_array();
  }

  public function treatmentDoseradiog($idtd){

    $str="SELECT tdr.*, IF(remarks=0,'Stop Treatment',IF(remarks=1,'Tunda Treatment',IF(remarks=2,'Lanjutkan  Treatment',''))) vremarks
          #,b.j jfield, b.approvedBy
          ,GROUP_CONCAT(master.getNamaLengkapPegawai(b.NIP) SEPARATOR '<br>') vrttsign
          FROM medis.tb_treatmentdoseRadiog tdr
        	INNER JOIN master.dokter b ON FIND_IN_SET(b.ID, tdr.rttsign) > 0
            
          WHERE tdr.idTD ='$idtd' 
          AND tdr.status =1 
         
          ORDER BY tdr.id asc
           
     ";
      $query  = $this->db->query($str);
    //  echo "<pre>".$str;exit;
    return $query->result_array();
  }

  public function treatmentDoseradiogDetil($idtd){
    $str="SELECT A.* FROM
          medis.tb_treatmentdoseRadiogDetil A INNER JOIN (
              SELECT nofield, MAX(id) id
              FROM medis.tb_treatmentdoseRadiogDetil
              WHERE idTD ='$idtd' 
              GROUP BY nofield
          ) B
          ON A.nofield = B.nofield 
          AND A.id = B.id
          WHERE idTD ='$idtd' 
          ORDER BY A.nofield asc";
   
      $query  = $this->db->query($str);
    //  echo "<pre>".$str;exit;
    return $query->result_array();
  }

  public function simpanApproving($data)
  {
    $this->db->trans_begin();
    $this->db->insert('medis.tb_treatmendoseApproving', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function simpanTreatmentDoseDr($data)
  {
    $this->db->trans_begin();
    $this->db->insert('medis.tb_treatmentDoseDr', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }


  public function simpanTreatmentDoseFm($dataxist,$data,$idtd)
  {
    $this->db->trans_begin();

    $str="SELECT approvedBy
    FROM medis.tb_treatmentdoseFisikamedis tb
    WHERE tb.idTD='$idtd' 
    AND tb.status='1'
    ORDER BY tb.revisi DESC
    LIMIT 1";
    $query  = $this->db->query($str);
  // var_dump($ret);exit;
  // echo $str;exit;
    $ret=$query->row_array();
    $dataxistset=$dataxist;
    $dataset=$data;
    if($ret['approvedBy']<1){
      // echo "A";exit;
      if(count($dataxist)>0){
        foreach($dataxist as $key => $value) {
          $value['revisi'] = ($value['revisi']-1)>0?($value['revisi']-1):null;
          $value['idubah'] = null;
          $dataxistset[$key]=$value;
        }
        // var_dump($data);
        // var_dump($dataxistset);exit;
        $this->db->where('approvedBy IS NULL');
        $this->db->update_batch('medis.tb_treatmentdoseFisikamedis', $dataxistset,'id');
      }
      
      if(count($data)>0){
        foreach($data as $keydata => $valuedata) {
          if($valuedata['revisi']==""){
            $valuedata['revisi'] =null;
          }else{
            $valuedata['revisi'] = ($valuedata['revisi']-1)>0?($valuedata['revisi']-1):null;
          }
          $dataset[$keydata]=$valuedata;
        }
      }
      // var_dump($dataset);exit;
    }else{
      if(count($dataxist)>0){
        foreach($dataxist as $key => $value) {
          unset($value['id']);
          $dataxistset[$key]=$value;
        }
      }
      
      $dataset = array_merge($dataxistset, $dataset);
    }
    $this->db->insert_batch('medis.tb_treatmentdoseFisikamedis', $dataset);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    // echo ($this->db->last_query());exit;
    echo json_encode($result);
  }

  

  public function simpanTreatmentDoseRad($data,$datamu,$idtd)
  {
    $str="SELECT oleh, remarks, remarksBy
    FROM medis.tb_treatmentdoseRadiog tb
    WHERE tb.idTD='".$idtd."' 
    AND tb.status='1'
    AND date(tb.dateAt)=CURDATE() 
    ORDER BY tb.dateAt DESC
    LIMIT 1";

    $query  = $this->db->query($str);
  // echo $str;exit;
  
    $ret=$query->row_array();
  // var_dump($ret);exit;
    if(!$ret){
      $result = array('status' => 'failed','ket'=>'Tidak ada Remarks pada tanggal '.date('Y-m-d').'');

    }else if($ret && $ret['oleh']>0){
      $result = array('status' => 'failed','ket'=>'Data tanggal '.date('Y-m-d').' sudah ada');

    }else{

      $this->db->trans_begin();   
      // var_dump($datamu);exit;
      // $this->db->where('approvedBy IS NULL');
      $this->db->where('id', $data['id']);
      $this->db->update('medis.tb_treatmentdoseRadiog', $data);
      $this->db->insert_batch('medis.tb_treatmentdoseRadiogDetil', $datamu);
      // echo ($this->db->last_query());exit;
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
    }
    // echo ($this->db->last_query());exit;
    echo json_encode($result);
  }

  
  public function simpanTreatmentDoseRemarks($data)
  {
    $str="SELECT 1
    FROM medis.tb_treatmentdoseRadiog tb
    WHERE tb.idTD='".$data['idtd']."' 
    AND tb.status='1'
    AND date(tb.remarksAt)=CURDATE() 
    ORDER BY tb.remarksAt DESC
    LIMIT 1";
    $query  = $this->db->query($str);
  // echo $str;exit;
  
    $ret=$query->num_rows();
  // var_dump($ret);exit;
    if($ret>0){
      $result = array('status' => 'failed','ket'=>'Remarks tanggal '.date('Y-m-d').' sudah ada');

    }else{

      $this->db->trans_begin();
      $this->db->insert('medis.tb_treatmentdoseRadiog', $data);
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed','ket'=>'');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }

    }
      // echo ($this->db->last_query());exit;
      echo json_encode($result);
  }

  // SELECT tmdr.`*`
  // ,HOUR(TIMEDIFF(NOW(),tmdr.tanggal)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tmdr.tanggal))<=24,1,0) STATUS_EDIT
  // ,tmrad.id idRad,tmrfis.id idFis, pp.NORM
  // ,master.getNamaLengkapPegawai(ap1.NIP)OLEHDR
  // ,master.getNamaLengkapPegawai(ap2.NIP)OLEHRAD
  // ,master.getNamaLengkapPegawai(ap3.NIP)OLEHFIS
  // FROM medis.tb_treatmentDoseDr tmdr
  // LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tmdr.nokun
  // LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
  // LEFT JOIN master.pasien mp ON mp.NORM = pp.NORM
  // LEFT JOIN medis.tb_treatmentDose tmrad ON tmrad.nokun = tmdr.nokun
  // LEFT JOIN medis.tb_treatmendoseApproving tmrfis ON tmrfis.nokun = tmdr.nokun
  // LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = tmdr.oleh
  // LEFT JOIN aplikasi.pengguna ap2 ON ap2.ID = tmrad.oleh
  // LEFT JOIN aplikasi.pengguna ap3 ON ap3.ID = tmrfis.oleh
  // WHERE pp.NORM = '$nomr' AND tmdr.`status` = 1
  // GROUP BY tmdr.nokun
  // ORDER BY tmdr.tanggal DESC
  
  public function tblHistoryTreatmentDose($nomr)
  {
    $query  = $this->db->query("SELECT tmdr.`*`
                                ,HOUR(TIMEDIFF(NOW(),tmdr.tanggal)) DURASI,IF(HOUR(TIMEDIFF(NOW(),tmdr.tanggal))<=24,1,0) STATUS_EDIT
                                ,tmrad.id idRad,tmrfis.id idFis, tmver.id idVer, pp.NORM
                                ,master.getNamaLengkapPegawai(ap1.NIP)OLEHDR
                                ,master.getNamaLengkapPegawai(ap2.NIP)OLEHRAD
                                ,master.getNamaLengkapPegawai(ap3.NIP)OLEHFIS
                                ,master.getNamaLengkapPegawai(ap4.NIP)OLEHVER
                                FROM medis.tb_treatmentDoseDr tmdr
                                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tmdr.nokun
                                LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                                LEFT JOIN master.pasien mp ON mp.NORM = pp.NORM
                                LEFT JOIN medis.tb_treatmentDose tmrad ON tmrad.nokun = tmdr.nokun
                                LEFT JOIN medis.tb_treatmendoseApproving tmrfis ON tmrfis.nokun = tmdr.nokun
                                LEFT JOIN medis.tb_treatment_radiografer_dose tmver ON tmver.nokun = tmdr.nokun 
                                
                                LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = tmdr.oleh
                                LEFT JOIN aplikasi.pengguna ap2 ON ap2.ID = tmrad.oleh
                                LEFT JOIN aplikasi.pengguna ap3 ON ap3.ID = tmrfis.oleh
                                LEFT JOIN aplikasi.pengguna ap4 ON ap4.ID = tmver.oleh
                                WHERE pp.NORM = '$nomr' AND tmdr.`status` = 1
                                GROUP BY tmdr.nokun
                                ORDER BY tmdr.tanggal DESC");

    return $query;
  }

  public function getTmDr($id)
  {
    $query  = $this->db->query("SELECT  tm.`*`,master.getNamaLengkap(pp.NORM)NAMAPASIEN, pp.NORM
                               FROM medis.tb_treatmentDoseDr tm
                               LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tm.nokun
                               LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                               WHERE tm.id = '$id'");

    return $query->row_array();
  }

  public function getTmRad($nokun)
  {
    $query  = $this->db->query("SELECT tm.`*`,master.getNamaLengkap(pp.NORM)NAMAPASIEN, pp.NORM
                               FROM medis.tb_treatmentDose tm
                               LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tm.nokun
                               LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                               WHERE tm.nokun = '$nokun'");

    return $query->result_array();
  }

  public function getTmRadDetail($id)
  {
    $query  = $this->db->query("SELECT *
                               FROM medis.tb_treatmentDose tm
                               WHERE tm.id = '$id'");

    return $query->row_array();
  }
// ----------------------------------------------------baru----------------------------------------------------------------
  public function getTmVer($nokun)
  {
    $query  = $this->db->query("SELECT tm.`*`,master.getNamaLengkap(pp.NORM)NAMAPASIEN, pp.NORM
                               FROM medis.tb_treatment_radiografer_dose tm
                               LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tm.nokun
                               LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                               WHERE tm.nokun = '$nokun'");

    return $query->result_array();
  }
  
// ----------------------------------------------------Endbaru----------------------------------------------------------------

  public function getTmFis($nokun)
  {
    $query  = $this->db->query("SELECT tma.`*`,master.getNamaLengkap(pp.NORM)NAMAPASIEN, pp.NORM
                               FROM medis.tb_treatmendoseApproving tma
                               LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tma.nokun
                               LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                               WHERE tma.nokun = '$nokun'");

    return $query->result_array();
  }

  public function getTmFisDetail($id)
  {
    $query  = $this->db->query("SELECT *
                               FROM medis.tb_treatmendoseApproving tma
                               WHERE tma.id = '$id'");

    return $query->row_array();
  }

  public function updateTmDr($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('medis.tb_treatmentDoseDr', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updateTmRad($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('medis.tb_treatmentDose', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updateTmFis($data,$id)
  {
    $this->db->trans_begin();
    $this->db->where('id', $id);
    $this->db->update('medis.tb_treatmendoseApproving', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function simpanTreatment_radiografer($data)
  {
    $this->db->insert('medis.tb_treatment_radiografer_dose', $data);
  }
  
  public function updateTreatment_radiografer($data,$idTreatment)
  {
    $this->db->where('id', $idTreatment);
    $this->db->update('medis.tb_treatment_radiografer_dose', $data);
  }


}

/* End of file TreatmentDoseModel.php */
/* Location: ./application/models/radioterapi/TreatmentDoseModel.php */
