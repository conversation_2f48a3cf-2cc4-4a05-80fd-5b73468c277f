<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class LaporanEmr extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('laporanModel','masterModel'));
  }

  public function index()
  {
    $ruangan = $this->masterModel->ruanganRskd();
    $data = array(
      'title' => 'Halaman Laporan Emr',
      'isi'   => 'Laporan/LaporanEmr/index',
      'ruangan'   => $ruangan,
    );
    $this->load->view('layout/wrapper',$data);
  }

  public function laporanPengkajian()
  {
    $tgl1     = $this->input->post("tgl1");
    $tgl2     = $this->input->post("tgl2");
    $pengguna = $this->input->post("pengguna");
    $ruangan  = $this->input->post("ruangan");


    if(empty($ruangan)){
      $listLaporanPengkajian =  $this->laporanModel->listLaporanPengkajian($pengguna, $tgl1, $tgl2);
    }else{
      $listLaporanPengkajian =  $this->laporanModel->listLaporanPengkajianRuangan($pengguna, $tgl1, $tgl2, $ruangan);
    }

    // echo"<pre>";print_r($listLaporanPengkajian);exit();

    if(!empty($listLaporanPengkajian)){
      echo "<br>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='form-group'>";
      echo "<h4 class='m-t-0 header-title'>Table Pengkajian</h4>";
      echo "<a href=".base_url('laporan/LaporanEmr/cetakExcelPengkajian/').$tgl1.'/'.$tgl2.'/'.$pengguna.'/'.$ruangan." class='text-muted font-14 m-b-20' target='_blank'><i class='fa fa-file-excel-o'></i> Excel Pengkajian</a>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblListPengkajian" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Nama</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Jumlah Pengkajian</th>";
      echo "<th width='15%'>Detail</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($listLaporanPengkajian as $llp):
      if(empty($ruangan)){
        $ru = " - ";
      }else{
        $ru = $llp['RUANGAN'];
      }
      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$llp['USER']."</td>";
      echo "<td>".$ru."</td>";
      echo "<td>".$llp['JUMLAH_PENGKAJIAN']."</td>";
      echo "<td><a href='#' data-id='".$llp['ID_USER']."' data-tgl1='".$tgl1."' data-tgl2='".$tgl2."' class='btn btn-success btn-block viewDetailPengkajian' data-toggle='modal' data-ruangan='".$ruangan."'><i class='fa fa-check'></i> Pilih</a></td>";
      echo "</tr>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "<tfoot>";
      echo "<tr>";
      echo "<th colspan='3' style='text-align:right'>Total:</th>";
      echo "<th colspan='2'></th>";
      echo "</tr>";
      echo "</tfoot>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
    }

  }
  public function cetakExcelPengkajian()
  {
    $tgl1     = $this->uri->segment(4);
    $tgl2     = $this->uri->segment(5);
    $pengguna = $this->uri->segment(6);
    $ruangan  = $this->uri->segment(7);

    header("Content-type=application/vnd.ms-excel");
    header("Content-disposition:attechment;filename=LaporanPengkajian.xls");

    if(empty($ruangan)){
      $listLaporanPengkajian =  $this->laporanModel->listLaporanPengkajian($pengguna, $tgl1, $tgl2);
    }else{
      $listLaporanPengkajian =  $this->laporanModel->listLaporanPengkajianRuangan($pengguna, $tgl1, $tgl2, $ruangan);
    }

    $data = array(
      'tgl1'                  => $tgl1,
      'tgl2'                  => $tgl2,
      'pengguna'              => $pengguna,
      'ruangan'               => $ruangan,
      'listLaporanPengkajian' => $listLaporanPengkajian,
    );
    $this->load->view('Laporan/LaporanEmr/excelPengkajian',$data);

  }

  public function detailPengkajian()
  {
    $id      = $this->input->post("id");
    $tgl1    = $this->input->post("tgl1");
    $tgl2    = $this->input->post("tgl2");
    $ruangan = $this->input->post("ruangan");
    if(empty($ruangan)){
      $detailPengkajian =  $this->laporanModel->detailLaporanPengkajian($id, $tgl1, $tgl2);
    }else{
      $detailPengkajian =  $this->laporanModel->detailLaporanPengkajianRuangan($id, $tgl1, $tgl2,$ruangan);
    }

    if(!empty($detailPengkajian)){
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail Pengkajian</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblDetailPengkajian" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Norm</th>";
      echo "<th>Nama Pasien</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Diagnosa Masuk</th>";
      echo "<th>Tanggal Pengkajian</th>";
      echo "<th>#</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($detailPengkajian as $dp):
      $tombolCetak = '';
      $tombolCetakMedis = '';
      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$dp['NORM']."</td>";
      echo "<td>".$dp['NAMA_PASIEN']."</td>";
      echo "<td>".$dp['RUANGAN']."</td>";
      echo "<td>".$dp['DIAGNOSA_MASUK']."</td>";
      echo "<td>".date("d-m-Y H:i:s",strtotime($dp['TANGGAL_PEMBUATAN_EMR']))."</td>";
      echo "<td>";
      if($dp['ID_EMR_MEDIS'] != null){
        // echo $dp['USIA'] == 1 ? '<a href="/reports/simrskd/keperawatananak/pengkajianRJMedisAnak.php?format=pdf&id=' . $dp['ID_EMR_MEDIS'] . '" class="btn btn-primary btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> Medis</a>' : '<a href="/reports/simrskd/pengkajian/pengkajian_medis.php?format=pdf&id=' . $dp['ID_EMR_MEDIS'] . '" class="btn btn-primary btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> Medis</a>';
        $jenisPengkajianMedis = $dp['JENIS_PENGKAJIAN_MEDIS'];
        
        if($jenisPengkajianMedis == 1){
          if ($dp['USIA'] == 1) {
            $tombolCetakMedis = '<a href="/reports/simrskd/keperawatananak/pengkajianRJMedisAnak.php?format=pdf&id=' . $dp['ID_EMR_MEDIS'] . '" class="btn btn-primary btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Medis</a>';
          }else{
            $tombolCetakMedis = '<button type="button" data-name="emr.pengkajian.pengkajian_medis" data-parameter=\'{"ID_EMR":"' . $dp['ID_EMR_MEDIS'] . '"}\' class="btn btn-primary btn-block btn-sm tombolCetakan" target="_blank"><i class="fa fa-print"></i> Medis</button>';
          }

          if ($dp['ID_RUANGAN'] == '105120101') {
            $tombolCetakMedis = '<a href="/reports/simrskd/radioterapi/pengkajian_medis_radio_terapi.php?format=pdf&id=' . $dp['ID_EMR_MEDIS'] . '" class="btn btn-primary btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Medis</a>';
          }
        }

        if($jenisPengkajianMedis == 5){
          $tombolCetakMedis = '<a href="/reports/simrskd/Rawatinap/medis/PengkajianRIMedisDewasa.php?format=pdf&id='.$dp['ID_EMR_MEDIS'].'" target="_blank" class="btn btn-primary btn-block btn-sm"><i class="fa fa-print"></i> Medis</a>';
        }elseif($jenisPengkajianMedis == 8){
          $tombolCetakMedis = '<a href="/reports/simrskd/sistemik/PengkajianSistemikRiMedis.php?format=pdf&idEmr='.$dp['ID_EMR_MEDIS'].'" target="_blank" class="btn btn-primary btn-block btn-sm"><i class="fa fa-print"></i> Medis</a>';
        }elseif($jenisPengkajianMedis == 9){
          $tombolCetakMedis = '<a href="/reports/simrskd/igd/pengkajian_medis_igd.php?format=pdf&id='.$dp['ID_EMR_MEDIS'].'" target="_blank" class="btn btn-primary btn-block btn-sm"><i class="fa fa-print"></i> Medis</a>';
        }elseif($jenisPengkajianMedis == 10){
          $tombolCetakMedis = '<a href="/reports/simrskd/pengkajiankritis/PengkajianKritisMedis.php?format=pdf&id='.$dp['ID_EMR_MEDIS'].'" target="_blank" class="btn btn-primary btn-block btn-sm"><i class="fa fa-print"></i> Medis</a>';
        }elseif($jenisPengkajianMedis == 15){
          $tombolCetakMedis = '<a href="/reports/simrskd/Brakhiterapi/MedisRIKeperawatanBrakhiterapi.php?format=pdf&id='.$dp['ID_EMR_MEDIS'].'" target="_blank" class="btn btn-primary btn-block btn-sm"><i class="fa fa-print"></i> Medis</a>';
        }

        echo $tombolCetakMedis;
      }

      if($dp['ID_EMR_KEPERAWATAN'] != null){
        // echo $dp['USIA'] == 1 ? '<a href="/reports/simrskd/keperawatananak/pengkajianRJKeperawatanAnak.php?format=pdf&id=' . $dp['ID_EMR_KEPERAWATAN'] . '" class="btn btn-info btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> Perawat</a>' : '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $dp['ID_EMR_KEPERAWATAN'] . '" class="btn btn-info btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> Perawat</a>';
          $jenisPengkajianPerawat = $dp['JENIS_PENGKAJIAN_KEPERAWATAN'];

          if($jenisPengkajianPerawat == 1){
            $tombolCetak = '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $dp['ID_EMR_KEPERAWATAN'] . '" class="btn btn-info btn-block btn-sm" target="_blank"><i class="fa fa-print"></i>Perawat</a>';

            if ($dp['USIA'] == 1) {
              $tombolCetak = '<a href="/reports/simrskd/keperawatananak/pengkajianRJKeperawatanAnak.php?format=pdf&id=' . $dp['ID_EMR_KEPERAWATAN'] . '" class="btn btn-info btn-block btn-sm" target="_blank"><i class="fa fa-print"></i>Perawat</a>';
            }

            if ($dp['ID_RUANGAN'] == '105120101') {
              $tombolCetak = '<a href="/reports/simrskd/radioterapi/pengkajian_keperawatan_radio_terapi.php?format=pdf&id=' . $dp['ID_EMR_KEPERAWATAN'] . '" class="btn btn-info btn-block btn-sm" target="_blank"><i class="fa fa-print"></i>Perawat</a>';
            }
          }

          if($jenisPengkajianPerawat == 5){
            $tombolCetak = '<a href="/reports/simrskd/Rawatinap/dewasa/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr='.$dp['ID_EMR_KEPERAWATAN'].'" target="_blank" class="btn btn-info btn-block btn-sm"><i class="fa fa-print"></i> Perawat</a>';
          }elseif($jenisPengkajianPerawat == 8){
            $tombolCetak = '<a href="/reports/simrskd/sistemik/PengkajianSistemikRi.php?format=pdf&idEmr='.$dp['ID_EMR_KEPERAWATAN'].'" target="_blank" class="btn btn-info btn-block btn-sm"><i class="fa fa-print"></i> Perawat</a>';
          }elseif($jenisPengkajianPerawat == 9){
            $tombolCetak = '<a href="/reports/simrskd/igd/pengkajian_AwalIgd.php?format=pdf&id='.$dp['ID_EMR_KEPERAWATAN'].'" target="_blank" class="btn btn-info btn-block btn-sm"><i class="fa fa-print"></i> Perawat</a>';
          }elseif($jenisPengkajianPerawat == 10){
            $tombolCetak = '<a href="/reports/simrskd/pengkajiankritis/pengkajianKritisPerawat.php?format=pdf&id='.$dp['ID_EMR_KEPERAWATAN'].'" target="_blank" class="btn btn-info btn-block btn-sm"><i class="fa fa-print"></i> Perawat</a>';
          }elseif($jenisPengkajianPerawat == 15){
            $tombolCetak = '<a href="/reports/simrskd/Brakhiterapi/PengkajianRIKeperawatanBrakhiterapi.php?format=pdf&id='.$dp['ID_EMR_KEPERAWATAN'].'" target="_blank" class="btn btn-info btn-block btn-sm"><i class="fa fa-print"></i> Perawat</a>';
          }

          echo $tombolCetak;
      }
      echo "</td>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo '<div class="modal-header">';
      echo '<h4 class="modal-title mt-0" id="mySmallModalLabel">Detail Pasien</h4>';
      echo '<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>';
      echo '</div>';
      echo '<div class="modal-body">';
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
      echo '</div>';
    }
  }

  public function laporanCppt()
  {
    $tgl1     = $this->input->post("tgl1");
    $tgl2     = $this->input->post("tgl2");
    $pengguna = $this->input->post("pengguna");
    $ruangan  = $this->input->post("ruangan");

    if(empty($ruangan)){
      $listLaporanCpp =  $this->laporanModel->listLaporanCppt($pengguna, $tgl1, $tgl2);
    }else{
      $listLaporanCpp =  $this->laporanModel->listLaporanCpptRuangan($pengguna, $tgl1, $tgl2,$ruangan);
    }

    // echo"<pre>";print_r($listLaporanCpp);exit();

    if(!empty($listLaporanCpp)){
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='form-group'>";
      echo "<h4 class='m-t-0 header-title'>Table CPPT</h4>";
      echo "<a href=".base_url('laporan/LaporanEmr/cetakExcelCppt/').$tgl1.'/'.$tgl2.'/'.$pengguna.'/'.$ruangan." class='text-muted font-14 m-b-20' target='_blank'><i class='fa fa-file-excel-o'></i> Excel CPPT</a>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblListCppt" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Nama</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Jumlah CPPT</th>";
      echo "<th width='15%'>Detail</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($listLaporanCpp as $llc):

      if(empty($ruangan)){
        $ruc = '-';
      }else{
        $ruc = $llc['RUANGAN'];
        $ruc = $llc['RUANGAN'];
      }

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$llc['USER']."</td>";
      echo "<td>".$ruc."</td>";
      echo "<td>".$llc['JUMLAH_CPPT']."</td>";
      echo "<td><a href='#' data-id='".$llc['ID_USER']."' data-tgl1='".$tgl1."' data-tgl2='".$tgl2."' data-jenisCppt='".$llc['JENIS_CPPT']."' class='btn btn-primary btn-block viewDetailCppt' data-toggle='modal' data-ruangan='".$ruangan ."'><i class='fa fa-check'></i> Pilih</a></td>";
      echo "</tr>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "<tfoot>";
      echo "<tr>";
      echo "<th colspan='3' style='text-align:right'>Total:</th>";
      echo "<th colspan='2'></th>";
      echo "</tr>";
      echo "</tfoot>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
    }
  }

  public function cetakExcelCppt()
  {
    $tgl1     = $this->uri->segment(4);
    $tgl2     = $this->uri->segment(5);
    $pengguna = $this->uri->segment(6);
    $ruangan  = $this->uri->segment(7);

    header("Content-type=application/vnd.ms-excel");
    header("Content-disposition:attechment;filename=LaporanCPPT.xls");

    if(empty($ruangan)){
      $listLaporanCpp =  $this->laporanModel->listLaporanCppt($pengguna, $tgl1, $tgl2);
    }else{
      $listLaporanCpp =  $this->laporanModel->listLaporanCpptRuangan($pengguna, $tgl1, $tgl2,$ruangan);
    }

    $data = array(
      'tgl1'           => $tgl1,
      'tgl2'           => $tgl2,
      'pengguna'       => $pengguna,
      'ruangan'        => $ruangan,
      'listLaporanCpp' => $listLaporanCpp,
    );
    $this->load->view('Laporan/LaporanEmr/excelCppt',$data);

  }

  public function detailCppt()
  {
    $id        = $this->input->post("id");
    $tgl1      = $this->input->post("tgl1");
    $tgl2      = $this->input->post("tgl2");
    $ruangan   = $this->input->post("ruangan");
    $jenisCppt = $this->input->post("jenisCppt");


    if(empty($ruangan)){
      $detailCppt =  $this->laporanModel->detailLaporanCppt($id, $tgl1, $tgl2);
    }else{
      $detailCppt =  $this->laporanModel->detailLaporanCpptRuangan($id, $tgl1, $tgl2,$ruangan);
    }

    if(!empty($detailCppt)){
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail Pengkajian</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblDetailCppt" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Norm</th>";
      echo "<th>Nama Pasien</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Tanggal Pembuatan Cppt</th>";
      echo "<th>Cetak</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($detailCppt as $dc):

      // $cetak = '<a href="/reports/simrskd/cppt/cpptnew.php?format=pdf&id='.$dc['ID_CPPT'].'" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';

      // if($dc['IDRUANGAN'] == 105120101){
      //   $cetak = '<a href="/reports/simrskd/cppt/cpptRadioterapinew.php?format=pdf&id='.$dc['ID_CPPT'].'" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
      // }

      if ($dc['PEMBERI_CPPT'] == 3) {
        $cetak = '<a id="view-cppt" target="_blank" href="/reports/simrskd/cppt/cpptgizi.php?format=pdf&id=' . $dc['ID_CPPT'] . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
      } else {
          if ($dc['JENIS'] == 1) {
              $cetak = '<a id="view-cppt" target="_blank" href="/reports/simrskd/cppt/cpptnew.php?format=pdf&id=' . $dc['ID_CPPT'] . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
              if ($dc['IDRUANGAN'] == 105120101) {
                  $cetak = '<a id="view-cppt" target="_blank" href="/reports/simrskd/cppt/cpptRadioterapinew.php?format=pdf&id=' . $dc['ID_CPPT'] . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
              } elseif ($dc['IDRUANGAN'] == 105110101) {
                  $cetak = '<a id="view-cppt" target="_blank" href="/reports/simrskd/cppt/cpptrehab.php?format=pdf&nokun=' . $dc['NOKUN'] . '&jenis=' . $dc['JENIS_CPPT'] . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
              }
          } else if ($dc['JENIS'] == 2) {
              $cetak = '<a id="view-cppt" target="_blank" href="/reports/simrskd/cppt/cpptRawatInap.php?format=pdf&id=' . $dc['ID_CPPT'] . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
          }
      }

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$dc['NORM']."</td>";
      echo "<td>".$dc['NAMA_PASIEN']."</td>";
      echo "<td>".$dc['RUANGAN']."</td>";
      echo "<td>".date("d-m-Y H:i:s",strtotime($dc['TANGGAL_PEMBUATAN_CPPT']))."</td>";
      echo "<td>".$cetak."</td>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
    }
  }

  public function laporanKonsul()
  {
    $tgl1     = $this->input->post("tgl1");
    $tgl2     = $this->input->post("tgl2");
    $ruangan  = $this->input->post("ruangan");

    if(empty($ruangan)){
      $listLaporanKonsul =  $this->laporanModel->listLaporanKonsul($tgl1, $tgl2);
    }else{
      $listLaporanKonsul =  $this->laporanModel->listLaporanKonsulRuangan($tgl1, $tgl2,$ruangan);
    }

    // echo"<pre>";print_r($pengguna);exit();

    if(!empty($listLaporanKonsul)){
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='form-group'>";
      echo "<h4 class='m-t-0 header-title'>Table Konsul</h4>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblListKonsul" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Nama</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Konsul Dikirim</th>";
      echo "<th>Konsul Dijawab</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($listLaporanKonsul as $llk):

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$llk['DOKTER_PENGIRIM']."</td>";
      echo "<td>".$llk['RUANGAN']."</td>";
      echo "<td><a href='#' data-id='".$llk['ID_DOKTER_PENGIRIM']."' data-ruangan='".$llk['ID_RUANGAN']."' class='btn btn-primary btn-block viewDetailKonsulKirim' data-toggle='modal' data-tgl1='".$tgl1."' data-tgl2='".$tgl2."'>".$llk['KONSUL_YANG_DIKIRIM']." Pasien</a></td>";
      echo "<td><a href='#' data-id='".$llk['ID_DOKTER_PENGIRIM']."' data-ruangan='".$llk['ID_RUANGAN']."' class='btn btn-success btn-block viewDetailKonsulJawab' data-toggle='modal' data-tgl1='".$tgl1."' data-tgl2='".$tgl2."'>".$llk['KONSUL_YANG_DIJAWAB']." Pasien</a></td>";
      echo "</tr>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
    }
  }

  public function detailKirimKonsul()
  {
    $id      = $this->input->post("id");
    $ruangan = $this->input->post("ruangan");
    $tgl1    = $this->input->post("tgl1");
    $tgl2    = $this->input->post("tgl2");

    $dKirimKonsul =  $this->laporanModel->detailLaporanKirimKonsul($id,$ruangan,$tgl1,$tgl2);
    // echo"<pre>";print_r($dKirimKonsul);exit();

    if(!empty($dKirimKonsul)){
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail konsul yang di kirim</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblDetailKirimKonsul" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Norm</th>";
      echo "<th>Nama Pasien</th>";
      echo "<th>Tanggal Konsul</th>";
      echo "<th>Cetak</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($dKirimKonsul as $dkk):

      $cetak = "<button type='button' data-name='emr.konsul.konsul' data-parameter='{\"ID\":\"" . $dkk['ID_KONSUL'] . "\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'><i class='fa fa-print'></i> Cetak</button>";

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$dkk['NORM']."</td>";
      echo "<td>".$dkk['NAMA_PASIEN']."</td>";
      echo "<td>".date("d-m-Y H:i:s",strtotime($dkk['TANGGAL']))."</td>";
      echo "<td>".$cetak."</td>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail konsul yang di kirim</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
      echo "</div>";

    }
  }

  public function detailJawabKonsul()
  {
    $id      = $this->input->post("id");
    $ruangan = $this->input->post("ruangan");
    $tgl1    = $this->input->post("tgl1");
    $tgl2    = $this->input->post("tgl2");

    $dJawabKonsul =  $this->laporanModel->detailLaporanJawabKonsul($id,$ruangan,$tgl1,$tgl2);


    if(!empty($dJawabKonsul)){
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail konsul yang di kirim</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblDetailJawabKonsul" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Norm</th>";
      echo "<th>Nama Pasien</th>";
      echo "<th>Tanggal Konsul</th>";
      echo "<th>Cetak</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($dJawabKonsul as $djk):

      $cetak = "<button type='button' data-name='emr.konsul.konsul' data-parameter='{\"ID\":\"" . $djk['ID_KONSUL'] . "\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'><i class='fa fa-print'></i> Cetak</button>";

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$djk['NORM']."</td>";
      echo "<td>".$djk['NAMA_PASIEN']."</td>";
      echo "<td>".date("d-m-Y H:i:s",strtotime($djk['TAMGGAL_JAWAB']))."</td>";
      echo "<td>".$cetak."</td>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail konsul yang di kirim</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
      echo "</div>";

    }
  }

  public function laporanTbak()
  {
    $tgl1     = $this->input->post("tgl1");
    $tgl2     = $this->input->post("tgl2");
    $ruangan  = $this->input->post("ruangan");

    if(empty($ruangan)){
      $listLaporanTbak =  $this->laporanModel->listLaporanTbak($tgl1, $tgl2);
    }else{
      $listLaporanTbak =  $this->laporanModel->listLaporanTbakRuangan($tgl1, $tgl2, $ruangan);
    }

    if(!empty($listLaporanTbak)){
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='form-group'>";
      echo "<h4 class='m-t-0 header-title'>Table Tbak</h4>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblListTbak" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Nama Dokter</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Jumlah All Tbak</th>";
      echo "<th>Jumlah Tbak Verif</th>";
      echo "<th>Jumlah Tbak Belum Verif</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($listLaporanTbak as $lTbak):

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$lTbak['DOKTER']."</td>";
      echo  $ruangan == "" ? "<td> - </td>" :"<td>".$lTbak['RUANGAN']."</td>";
      echo "<td><a href='#' data-id='".$lTbak['ID_DOKTER']."' class='btn btn-primary btn-block viewDetailAllTbak' data-toggle='modal' data-tgl1='".$tgl1."' data-tgl2='".$tgl2."' data-ruangan='".$ruangan."'>".$lTbak['JUMLAH_TBAK']." Pasien</a></td>";
      echo "<td><a href='#' data-id='".$lTbak['ID_DOKTER']."' class='btn btn-success btn-block viewDetailVerifTbak' data-toggle='modal' data-tgl1='".$tgl1."' data-tgl2='".$tgl2."' data-ruangan='".$ruangan."'>".$lTbak['TBAK_SDH_VERIF']." Pasien</a></td>";
      echo "<td><a href='#' data-id='".$lTbak['ID_DOKTER']."' class='btn btn-warning btn-block viewDetailBlmVerifTbak' data-toggle='modal' data-tgl1='".$tgl1."' data-tgl2='".$tgl2."' data-ruangan='".$ruangan."'>".$lTbak['TBAK_BLM_VERIF']." Pasien</a></td>";
      echo "</tr>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
    }
  }

  public function detailAllTbak()
  {
    $id      = $this->input->post("id");
    $tgl1    = $this->input->post("tgl1");
    $tgl2    = $this->input->post("tgl2");

    $dAllTbak =  $this->laporanModel->tbakAll($tgl1,$tgl2,$id);


    if(!empty($dAllTbak)){
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail All Tbak</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblDetailAllTbak" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Norm</th>";
      echo "<th>Nama Pasien</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Tanggal CPPT</th>";
      echo "<th>Status</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($dAllTbak as $dATbak):

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$dATbak['NORM']."</td>";
      echo "<td>".$dATbak['NAMA_PASIEN']."</td>";
      echo "<td>".$dATbak['RUANGAN']."</td>";
      echo "<td>".date("d-m-Y H:i:s",strtotime($dATbak['TANGGAL_CPPT']))."</td>";
      echo $dATbak['TGL_VERIF_TBAK'] == null ? '<td><p style="color:#eb3b5a;">Belum Verifikasi</p></td>' : '<td><p style="color:#20bf6b;">Sudah Verifikasi</p></td>';
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail konsul yang di kirim</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
      echo "</div>";

    }
  }

  public function detailVerifTbak()
  {
    $id      = $this->input->post("id");
    $tgl1    = $this->input->post("tgl1");
    $tgl2    = $this->input->post("tgl2");

    $dVerifTbak =  $this->laporanModel->tbakVerif($tgl1,$tgl2,$id);


    if(!empty($dVerifTbak)){
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail Verifikasi Tbak</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblDetailVerifTbak" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Norm</th>";
      echo "<th>Nama Pasien</th>";
      echo "<th>Ruangan</th>";
      echo "<th>Tanggal Verifikasi</p></td</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($dVerifTbak as $dvt):

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$dvt['NORM']."</td>";
      echo "<td>".$dvt['NAMA_PASIEN']."</td>";
      echo "<td>".$dvt['RUANGAN']."</td>";
      echo "<td>".date("d-m-Y H:i:s",strtotime($dvt['TANGGAL_VERIFIKASI']))."</td>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail konsul yang di kirim</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
      echo "</div>";

    }
  }

  public function detailBlmVerifTbak()
  {
    $id   = $this->input->post("id");
    $tgl1 = $this->input->post("tgl1");
    $tgl2 = $this->input->post("tgl2");

    $dBlmVerifTbak =  $this->laporanModel->tbakBlmVerif($tgl1,$tgl2,$id);


    if(!empty($dBlmVerifTbak)){
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail Belum Verifikasi Tbak</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo "<div class='row'>";
      echo "<div class='col-md-12'>";
      echo "<div class='table-responsive'>";
      echo '<table id="tblDetailBlmVerifTbak" class="table table-bordered table-bordered dt-responsive" cellspacing="0" width="100%">';
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Norm</th>";
      echo "<th>Nama Pasien</th>";
      echo "<th>Ruangan</th>";
      echo "</tr>";
      echo "</thead>";
      echo "<tbody>";
      $no=1; foreach($dBlmVerifTbak as $dBvt):

      echo "<tr>";
      echo "<td>".$no."</td>";
      echo "<td>".$dBvt['NORM']."</td>";
      echo "<td>".$dBvt['NAMA_PASIEN']."</td>";
      echo "<td>".$dBvt['RUANGAN']."</td>";
      $no++; endforeach ;
      echo "</tbody>";
      echo "</table>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
      echo "</div>";
    }else{
      echo "<div class='modal-header'>";
      echo "<h4 class='modal-title mt-0' id='mySmallModalLabel'>Detail konsul yang di kirim</h4>";
      echo "<button type='button' class='close' data-dismiss='modal' aria-hidden='true'>×</button>";
      echo "</div>";
      echo "<div class='modal-body'>";
      echo '<div class="alert alert-warning">
      <strong>Kosong!</strong> Data Tidak Di Temukan
      </div>';
      echo "</div>";

    }
  }

}

/* End of file LaporanEmr.php */
/* Location: ./application/controllers/laporan/LaporanEmr.php */
