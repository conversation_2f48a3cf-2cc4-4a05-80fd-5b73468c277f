<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PerencanaanAK extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/rawat_inap/gizi/validasiMalnutrisiDewasaModel',
        'rekam_medis/rawat_inap/keperawatan/PerencanaanAKModel'
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $idEMR = $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr ?? 0;
    $getPengkajianRawatInap = $this->pengkajianAwalModel->getPengkajianRawatInap($idEMR);
    $isiPAK = $this->PerencanaanAKModel->ambil($nokun);
    $dataAsuhanKeperawatan = null;

    // Mulai isi perencanaan asuhan keperawatan
    if ($isiPAK['id_diagnosis'] != null || $isiPAK['id_diagnosis'] != '' || !empty($isiPAK['id_diagnosis'])) {
      $dataAsuhanKeperawatan = json_decode(str_replace("'", '"', $isiPAK['id_diagnosis']));
    } else {
      $dataAsuhanKeperawatan = json_decode($getPengkajianRawatInap['ID_ASUHAN_KEPERAWATAN']);
    }
    // Akhir isi perencanaan asuhan keperawatan

    $data = [
      'pasien' => $pasien,
      'idEMR' => $idEMR,
      'nokun' => $nokun,
      'getPengkajianRawatInap' => $getPengkajianRawatInap,
      'dataAsuhanKeperawatan' => $dataAsuhanKeperawatan,
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'diagnosisKeperawatan' => $this->pengkajianAwalModel->historyPAK($pasien['NORM']),
      'jumlah' => $this->pengkajianAwalModel->historyPAK($pasien['NORM'], 1),
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/PerencanaanAK/index', $data);
  }

  public function baru()
  {
    $post = $this->input->post();
    $nokun = $post['nokun'] ?? null;
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $idEMR = $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr ?? 0;
    $data = [
      'pasien' => $pasien,
      'idEMR' => $idEMR,
      'nokun' => $nokun,
      'getPengkajianRawatInap' => $this->pengkajianAwalModel->getPengkajianRawatInap($idEMR),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'diagnosisKeperawatan' => $this->pengkajianAwalModel->historyPAK($pasien['NORM']),
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/PerencanaanAK/baru/index', $data);
  }

  public function history()
  {
    $norm = $this->input->post('norm');
    $data = [
      'norm' => $norm,
      'nokun' => $this->input->post('nokun'),
      'history' => $this->pengkajianAwalModel->historyPAK($norm),
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/PerencanaanAK/history', $data);
  }

  public function simpan($param)
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $data = [];
    $index = 0;
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        if (isset($post['asuhanKeperawatan'])) {
          foreach ($post['asuhanKeperawatan'] as $ak) {
            $id = 'asuhanLainya' . $post['asuhanKeperawatan'][$index];
            $data[$index] = [
              'nokun' => $post['nokun'] ?? null,
              'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
              'lain_lain' => $post[$id] ?? null,
              'oleh' => $this->session->userdata['id'],
            ];
            $index++;
          }
        } else {
          $data[0] = [
            'nokun' => $post['nokun'] ?? null,
            'id_asuhan_keperawatan_detil' => null,
            'lain_lain' => null,
            'oleh' => $this->session->userdata['id'],
          ];
        }
        // echo '<pre>';print_r($data);exit();
        $this->PerencanaanAKModel->simpan($data);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = ['status' => 'failed'];
        } else {
          $this->db->trans_commit();
          $result = ['status' => 'success', 'pesan' => 'ubah'];
        }
        echo json_encode($result);
      } else {
        if (isset($post['asuhanKeperawatan'])) {
          foreach ($post['asuhanKeperawatan'] as $ak) {
            $id = 'asuhanLainya' . $post['asuhanKeperawatan'][$index];
            $data[$index] = [
              'nokun' => $post['nokun'] ?? null,
              'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
              'lain_lain' => $post[$id] ?? null,
              'oleh' => $this->session->userdata['id'],
            ];
            $index++;
          }
        } else {
          $data[0] = [
            'nokun' => $post['nokun'] ?? null,
            'id_asuhan_keperawatan_detil' => null,
            'lain_lain' => null,
            'oleh' => $this->session->userdata['id'],
          ];
        }
        // echo '<pre>';print_r($data);exit();
        $this->PerencanaanAKModel->simpan($data);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = ['status' => 'failed'];
        } else {
          $this->db->trans_commit();
          $result = ['status' => 'success', 'pesan' => 'ubah'];
        }
        echo json_encode($result);
      }
    }
  }

  public function verifikasi()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $jenis = !empty($post['jenis']) ? $post['jenis'] : null;

    $data = [
      'id_asuhan' => !empty($post['id_asuhan']) ? $post['id_asuhan'] : null,
      'status_verif' => 1,
      'pemverifikasi' => $this->session->userdata['id'],
    ];

    // Mulai pembagian jenis
    if ($jenis == 1) {
      $data['id_emr'] = !empty($post['id_emr']) ? $post['id_emr'] : null;
    } elseif ($jenis == 2) {
      $data['nokun'] = !empty($post['nokun']) ? $post['nokun'] : null;
    }
    //  Akhir pembagian jenis

    // echo '<pre>';print_r($data);exit();
    $this->PerencanaanAKModel->verifikasi($data, $jenis);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }
}

/* End of file PerencanaanAK.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/keperawatan/PerencanaanAK.php */