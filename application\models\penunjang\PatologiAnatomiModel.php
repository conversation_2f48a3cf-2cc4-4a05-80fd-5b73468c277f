<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PatologiAnatomiModel extends MY_Model
{
  protected $_table_name = 'layanan.order_lab';
  protected $_primary_key = 'NOMOR';
  protected $_order_by = 'NOMOR';
  protected $_order_by_type = 'DESC';

  var $tabel = 'layanan.order_lab orla';
  var $urutan_kolom = array(
    null, 'NOMOR_ORDER', 'TANGGAL_ORDER', 'RUANG_AWAL', 'RUANG_TUJUAN', 'STATUS_ORDER', 'JENIS_HASIL', null
  );
  var $urutan_kolom_semua_order = array(
    null, 'NORM', 'NAMA_PASIEN', 'NOMOR_ORDER', 'TANGGAL_ORDER', 'RUANG_AWAL', 'STATUS_ORDER', 'JENIS_HASIL',
    'SUMBER_SEDIAAN', null
  );
  var $pencarian_kolom = array('orla.NOMOR', 'orla.TANGGAL', 'ras.DESKRIPSI', 'rut.DESKRIPSI');
  var $pencarian_kolom_semua_order = array(
    'p.NORM', 'master.getNamaLengkap(p.NORM)', 'orla.NOMOR', 'orla.TANGGAL', 'ras.DESKRIPSI', 'kon.sumber_sediaan'
  );
  var $urutan = array('TANGGAL_ORDER' => 'desc');

  public $rules = array(
    'KUNJUNGAN' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Angka',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function tampilHistoryLabPA($nomr)
  {
    $this->db->select(
      "orla.NOMOR NOMOR_ORDER, orla.KUNJUNGAN, p.NORM NORM, orla.TANGGAL TANGGAL_ORDER, ras.DESKRIPSI RUANG_ASAL,
      rut.ID ID_TUJUAN, rut.DESKRIPSI RUANG_TUJUAN, IF(rut.ID = '105070101', 1, 2) JENIS_LAB, orla.STATUS STATUS,
      IF(orla.STATUS = 1, 'Diterima/proses', IF(orla.STATUS = 2, 'Final', 'Dibatalkan')) STATUS_ORDER,
      CASE
        WHEN pah.id IS NOT NULL THEN 'Histopatologi'
        WHEN pas.id IS NOT NULL THEN 'Sitologi'
        WHEN kon.id IS NOT NULL THEN 'Konstultasi/review'
        ELSE 'Order Via SIMPEL'
      END AS JENIS_HASIL,
      CASE
        WHEN pah.id IS NOT NULL THEN 1
        WHEN pas.id IS NOT NULL THEN 2
        WHEN kon.id IS NOT NULL THEN 3
        ELSE 4
      END AS JENIS,
      CASE
        WHEN pah.id IS NOT NULL THEN pah.id
        WHEN pas.id IS NOT NULL THEN pas.id
        WHEN kon.id IS NOT NULL THEN kon.id
        ELSE NULL
      END AS ID_ORDER_SIMRSKD",
      FALSE
    );
    $this->db->from($this->tabel);
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = orla.KUNJUNGAN', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan ras', 'ras.ID = pk.RUANGAN', 'left');
    $this->db->join('master.dokter dok', 'dok.ID = orla.DOKTER_ASAL', 'left');
    $this->db->join('pendaftaran.kunjungan pkut', 'pkut.REF = orla.NOMOR', 'left');
    $this->db->join('master.ruangan rut', 'rut.ID = orla.TUJUAN', 'left');
    $this->db->join(
      'medis.tb_order_pa_histologi pah',
      'pah.kunjungan = orla.KUNJUNGAN AND pah.tanggal_order = orla.TANGGAL',
      'left'
    );
    $this->db->join(
      'medis.tb_order_pa_sitologi pas',
      'pas.kunjungan = orla.KUNJUNGAN AND pas.tanggal_order = orla.TANGGAL',
      'left'
    );
    $this->db->join(
      'medis.tb_konsul kon',
      'kon.kunjungan = orla.KUNJUNGAN AND kon.tujuan = 3 AND kon.tanggal = orla.TANGGAL',
      'left'
    );
    $this->db->where('p.NORM', $nomr);
    $this->db->where('orla.TUJUAN', '105080101');
    $this->db->order_by('orla.TANGGAL', 'DESC');

    $query = $this->db->get();
    return $query->result_array();
  }

  public function history($nomr = null, $status = null, $jenis = null)
  {
    $pencarianKolom = null;
    $urutanKolom = null;

    // Mulai periksa jenis
    if ($jenis == 'semua pasien') {
      $pencarianKolom = $this->pencarian_kolom_semua_order;
      $urutanKolom = $this->urutan_kolom_semua_order;

      $this->db->select(
        "orla.NOMOR NOMOR_ORDER, orla.KUNJUNGAN, p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN,
        orla.TANGGAL TANGGAL_ORDER, ras.DESKRIPSI RUANG_AWAL, rut.ID ID_TUJUAN, rut.DESKRIPSI RUANG_TUJUAN,
        IF(rut.ID = '105070101', 1, 2) JENIS_LAB, orla.STATUS STATUS, kon.sumber_sediaan,
        IF(kon.sumber_sediaan = 1, 'Internal RSKD', IF(kon.sumber_sediaan = 2, 'Rumah Sakit atau Laboratorium Luar', '-')) SUMBER_SEDIAAN,
        IF(orla.STATUS = 1, 'Belum Diterima', IF(orla.STATUS = 2, 'Sudah Diterima', 'Dibatalkan')) STATUS_ORDER,
        CASE
          WHEN pah.id IS NOT NULL THEN 'Histopatologi'
          WHEN pas.id IS NOT NULL THEN 'Sitologi'
          WHEN kon.id IS NOT NULL THEN 'Konstultasi/review'
          ELSE 'Order Via SIMPEL'
        END AS JENIS_HASIL,
        CASE
          WHEN pah.id IS NOT NULL THEN 1
          WHEN pas.id IS NOT NULL THEN 2
          WHEN kon.id IS NOT NULL THEN 3
          ELSE 4
        END AS JENIS,
        CASE
          WHEN pah.id IS NOT NULL THEN pah.id
          WHEN pas.id IS NOT NULL THEN pas.id
          WHEN kon.id IS NOT NULL THEN kon.id
          ELSE NULL
        END AS ID_ORDER_SIMRSKD",
        FALSE
      );
    } else {
      $pencarianKolom = $this->pencarian_kolom;
      $urutanKolom = $this->urutan_kolom;

      $this->db->select(
        "orla.NOMOR NOMOR_ORDER, orla.KUNJUNGAN, p.NORM NORM, orla.TANGGAL TANGGAL_ORDER, ras.DESKRIPSI RUANG_AWAL,
        rut.ID ID_TUJUAN, rut.DESKRIPSI RUANG_TUJUAN, IF(rut.ID = '105070101', 1, 2) JENIS_LAB, orla.STATUS STATUS,
        IF(orla.STATUS = 1, 'Diterima/proses', IF(orla.STATUS = 2, 'Final', 'Dibatalkan')) STATUS_ORDER,
        CASE
          WHEN pah.id IS NOT NULL THEN 'Histopatologi'
          WHEN pas.id IS NOT NULL THEN 'Sitologi'
          WHEN kon.id IS NOT NULL THEN 'Konstultasi/review'
          ELSE 'Order Via SIMPEL'
        END AS JENIS_HASIL,
        CASE
          WHEN pah.id IS NOT NULL THEN 1
          WHEN pas.id IS NOT NULL THEN 2
          WHEN kon.id IS NOT NULL THEN 3
          ELSE 4
        END AS JENIS,
        CASE
          WHEN pah.id IS NOT NULL THEN pah.id
          WHEN pas.id IS NOT NULL THEN pas.id
          WHEN kon.id IS NOT NULL THEN kon.id
          ELSE NULL
        END AS ID_ORDER_SIMRSKD",
        FALSE
      );
    }
    // Akhir periksa jenis

    $this->db->from($this->tabel);
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = orla.KUNJUNGAN', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan ras', 'ras.ID = pk.RUANGAN', 'left');
    $this->db->join('master.dokter dok', 'dok.ID = orla.DOKTER_ASAL', 'left');
    $this->db->join('pendaftaran.kunjungan pkut', 'pkut.REF = orla.NOMOR', 'left');
    $this->db->join('master.ruangan rut', 'rut.ID = orla.TUJUAN', 'left');
    $this->db->join(
      'medis.tb_order_pa_histologi pah',
      'pah.kunjungan = orla.KUNJUNGAN AND pah.tanggal_order = orla.TANGGAL',
      'left'
    );
    $this->db->join(
      'medis.tb_order_pa_sitologi pas',
      'pas.kunjungan = orla.KUNJUNGAN AND pas.tanggal_order = orla.TANGGAL',
      'left'
    );
    $this->db->join(
      'medis.tb_konsul kon',
      'kon.kunjungan = orla.KUNJUNGAN AND kon.tujuan = 3 AND kon.tanggal = orla.TANGGAL',
      'left'
    );

    $this->db->where('orla.TUJUAN', '105080101');

    // Mulai cek nomor MR
    if ($nomr != null) {
      $this->db->where('p.NORM', $nomr);
    }
    // Akhir cek nomor MR

    // Mulai cek status
    if ($status != null) {
      if ($status == 'diterima' || $status == 'belum selesai') { // Jika status diterima atau belum selesai
        $this->db->where('orla.STATUS', 1);
      } elseif ($status == 'history') { //Jika status history
        $this->db->where('orla.STATUS !=', 1);
      } elseif ($status == 'sudah selesai') { // JIka status sudah selesai
        $this->db->where('orla.STATUS !=', 1);
      }
    }
    // Akhir cek status

    // Mulai cek jenis
    if ($jenis != null) {
      if ($jenis == 'SIMRSKD' || $jenis == 'semua pasien') { // Jika jenis SIMRSKD dan semua pasien
        $this->db->group_start();
        $this->db->where('pah.id !=', null);
        $this->db->or_where('pas.id !=', null);
        $this->db->or_where('kon.id !=', null);
        $this->db->group_end();
      }
    }
    // Akhir cek jenis

    $i = 0;
    foreach ($pencarianKolom as $pk) { // Loop kolom
      if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
        $_POST['search']['value'] = $_POST['search']['value'];
      } else {
        $_POST['search']['value'] = '';
      }

      if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
        if ($i === 0) { // Loop pertama
          $this->db->group_start();
          $this->db->like($pk, $_POST['search']['value']);
        } else {
          $this->db->or_like($pk, $_POST['search']['value']);
        }

        if (count($pencarianKolom) - 1 == $i) { // Loop terakhir
          $this->db->group_end(); // Tutup kurung
        }
      }
      $i++;
    }

    if (isset($_POST['order'])) { // Pemrosesan order
      $this->db->order_by($urutanKolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
    } elseif (isset($this->urutan)) {
      $urutan = $this->urutan;
      $this->db->order_by(key($urutan), $urutan[key($urutan)]);
    }
  }

  public function ambilTabel($nomr = null, $status = null, $jenis = null)
  {
    $this->history($nomr, $status, $jenis);
    if (isset($_POST['length']) && $_POST['length'] < 1) {
      $_POST['lenght'] = '10';
    } else {
      $_POST['length'] = $_POST['length'];
    }

    if (isset($_POST['start']) && $_POST['start'] > 1) {
      $_POST['start'] = $_POST['start'];
    }

    $this->db->limit($_POST['length'], $_POST['start']);
    // print_r($_POST); die;
    $query = $this->db->get();
    return $query->result();
  }

  public function hitungTersaring($nomr = null, $status = null, $jenis = null)
  {
    $this->history($nomr);

    // Mulai cek nomor MR
    if ($nomr != null) {
      $this->db->where('p.NORM', $nomr);
    }
    // Akhir cek nomor MR

    // Mulai cek status
    if ($status != null) {
      if ($status == 'diterima' || $status == 'belum selesai') { // Jika status diterima atau belum selesai
        $this->db->where('orla.STATUS', 1);
      } elseif ($status == 'history') { //Jika status history
        $this->db->where('orla.STATUS !=', 1);
      } elseif ($status == 'sudah selesai') { // JIka status sudah selesai
        $this->db->where('orla.STATUS !=', 1);
      }
    }
    // Akhir cek status

    // Mulai cek jenis
    if ($jenis != null) {
      if ($jenis == 'SIMRSKD' || $jenis == 'semua pasien') { // Jika jenis SIMRSKD dan semua pasien
        $this->db->group_start();
        $this->db->where('pah.id !=', null);
        $this->db->or_where('pas.id !=', null);
        $this->db->or_where('kon.id !=', null);
        $this->db->group_end();
      }
    }
    // Akhir cek jenis

    $query = $this->db->get();
    return $query->num_rows();
  }

  public function hitungSemua($nomr = null, $status = null, $jenis = null)
  {
    $this->db->from($this->tabel);
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = orla.KUNJUNGAN', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('master.ruangan ras', 'ras.ID = pk.RUANGAN', 'left');
    $this->db->join('master.dokter dok', 'dok.ID = orla.DOKTER_ASAL', 'left');
    $this->db->join('pendaftaran.kunjungan pkut', 'pkut.REF = orla.NOMOR', 'left');
    $this->db->join('master.ruangan rut', 'rut.ID = orla.TUJUAN', 'left');
    $this->db->join(
      'medis.tb_order_pa_histologi pah',
      'pah.kunjungan = orla.KUNJUNGAN AND pah.tanggal_order = orla.TANGGAL',
      'left'
    );
    $this->db->join(
      'medis.tb_order_pa_sitologi pas',
      'pas.kunjungan = orla.KUNJUNGAN AND pas.tanggal_order = orla.TANGGAL',
      'left'
    );
    $this->db->join(
      'medis.tb_konsul kon',
      'kon.kunjungan = orla.KUNJUNGAN AND kon.tujuan = 3 AND kon.tanggal = orla.TANGGAL',
      'left'
    );
    $this->db->where('orla.TUJUAN', '105080101');
    $this->db->where('p.NORM', $nomr);

    // Mulai cek nomor MR
    if ($nomr != null) {
      $this->db->where('p.NORM', $nomr);
    }
    // Akhir cek nomor MR

    // Mulai cek status
    if ($status != null) {
      if ($status == 'diterima' || $status == 'belum selesai') { // Jika status diterima atau belum selesai
        $this->db->where('orla.STATUS', 1);
      } elseif ($status == 'history') { //Jika status history
        $this->db->where('orla.STATUS !=', 1);
      } elseif ($status == 'sudah selesai') { // JIka status sudah selesai
        $this->db->where('orla.STATUS !=', 1);
      }
    }
    // Akhir cek status

    // Mulai cek jenis
    if ($jenis != null) {
      if ($jenis == 'SIMRSKD' || $jenis == 'semua pasien') { // Jika jenis SIMRSKD dan semua pasien
        $this->db->group_start();
        $this->db->where('pah.id !=', null);
        $this->db->or_where('pas.id !=', null);
        $this->db->or_where('kon.id !=', null);
        $this->db->group_end();
      }
    }
    // Akhir cek jenis

    return $this->db->count_all_results();
  }
}

// End of file PatologiAnatomiModel.php
// Location: ./application/models/penunjang/PatologiAnatomiModel.php