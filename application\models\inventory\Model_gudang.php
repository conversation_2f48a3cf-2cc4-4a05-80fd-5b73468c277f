<?php
class Model_gudang extends ci_model{

    function tampilkan_gudang()
    {
        $query  ="SELECT * from master.ruangan where ID like '%1060602%'";
        return $this->db->query($query);
    }

    public function get_default($id){
        $sql = $this->db->query("SELECT * FROM invenumum.gudang WHERE ID_GUDANG = ".intval($id));
        if($sql->num_rows() > 0)
            return $sql->row_array();
        return false;
    }

    function post($data)
    {
        $this->db->insert('invenumum.gudang',$data);
    }

    function get_one($id)
    {
        $param  =   array('ID_GUDANG'=>$id);
        return $this->db->get_where('invenumum.gudang',$param);
    }

    function edit($data,$id)
    {
        $ID  =   $this->input->post('ID');
        $GUDANG     =   $this->input->post('GUDANG');
        //echo "<pre>";print_r($_POST);exit();
        $data       =   array('GUDANG'=>$GUDANG);

        $this->db->where('ID_GUDANG',$this->input->post('ID'));
        $this->db->update('invenumum.gudang',$data);
    }


    function delete($id)
    {
        $this->db->where('barang_id',$id);
        $this->db->delete('barang');
    }
}