<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormulirPindahRuanganModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_pindah_ruangan';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'kunjungan' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'tujuan' => array(
      'field' => 'tujuan',
      'label' => 'Tujuan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib Angka',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_pindah_ruangan', $data);
    return $this->db->insert_id();
  }

  public function ubah($id, $data)
  {
    $this->db->where('keperawatan.tb_pindah_ruangan.id', $id);
    $this->db->update('keperawatan.tb_pindah_ruangan', $data);
  }

  public function history($nomr, $param, $id)
  {
    if (isset($id)) {
      // Detail
      $this->db->select(
        'pr.id, pp.NORM, pr.kunjungan, pr.tujuan, pr.tanggal, pr.jam, pr.dpjp, pr.diagnosis, pr.tanggal_operasi,
        pr.waktu_operasi, pr.alasan_pindah, tbb.tb, tbb.bb, pr.masalah_utama, pr.kondisi_saat_ini,
        pr.ket_kondisi_saat_ini, pr.riwayat_alergi, pr.ket_riwayat_alergi, pr.riwayat_penyakit_menular,
        pr.ket_riwayat_penyakit_menular, pr.riwayat_pengobatan, pr.ket_riwayat_pengobatan, k.kesadaran, tv.td_sistolik,
        tv.td_diastolik, tv.nadi, tv.pernapasan, tv.suhu, pr.skor, pr.ket_skor, sn.metode skrining_nyeri,
        sn.skor skala_nyeri, sn.farmakologi, sn.non_farmakologi, sn.efek_samping, sn.ket_efek_samping, sn.provokative,
        sn.quality, sn.quality_lainnya, sn.regio, sn.severity, sn.time, sn.ket_time, pr.risiko_jatuh, pr.periksa_lab,
        pr.periksa_radiologi, pr.input, pr.output, pr.alat_bantu, pr.ket_alat_bantu, pr.prosedur_cairan_parenteral,
        pr.prosedur_transfusi, pr.prosedur_oral, pr.prosedur_injeksi,  pr.prosedur_oksigenisasi, pr.prosedur_tindakan,
        pr.masalah, pr.pemeriksaan_penunjang, pr.ket_pemeriksaan_penunjang,  pr.cairan_parenteral, pr.transfusi,
        pr.oral, pr.injeksi, pr.oksigenisasi, pr.tindakan, pr.nutrisi_diet, pr.nutrisi_parenteral, pr.perawatan,
        pr.tanggal_perawatan_cvc, pr.tanggal_perawatan_luka, pr.tanggal_perawatan_stoma, pr.tanggal_perawatan_ngt,
        pr.tanggal_perawatan_dc, pr.tanggal_perawatan_drain, pr.tanggal_perawatan_lainnya, pr.ket_perawatan,
        pr.konsultasi, pr.lain, pr.ket_lain, pr.dokter_penyetuju, pr.penerima,
        master.getNamaLengkapPegawai(ap.NIP) pengirim'
      );
    } elseif (isset($param)) {
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->select('pr.id');
      } elseif ($param == 'tabel') {
        $this->db->select(
          "pr.id, pp.NORM, pr.tanggal, pr.jam, mr.DESKRIPSI ruang_awal, mr2.DESKRIPSI ruang_tujuan,
          master.getNamaLengkapPegawai(ap.NIP) pengirim, pr.status,
          IF(master.getNamaLengkapPegawai(md.NIP) = '',
            master.getNamaLengkapPegawai(md2.NIP),
            master.getNamaLengkapPegawai(md.NIP)
          ) dpjp"
        );
      }
    }
    $this->db->from('keperawatan.tb_pindah_ruangan pr');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = pr.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = pr.pengirim', 'left');
    $this->db->join('master.ruangan mr', 'mr.ID = pk.RUANGAN', 'left');
    $this->db->join('master.ruangan mr2', 'mr2.ID = pr.tujuan', 'left');
    $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pk.NOPEN', 'left');
    $this->db->join('master.dokter md', 'md.ID = pr.dpjp', 'left');
    $this->db->join('master.dokter md2', 'md2.ID = ptp.DOKTER', 'left');
    if (isset($id)) {
      // Detail
      $this->db->join('db_pasien.tb_tb_bb tbb', 'pr.id = tbb.ref AND tbb.data_source = 19', 'left');
      $this->db->join('db_pasien.tb_kesadaran k', 'pr.id = k.ref AND k.data_source = 19', 'left');
      $this->db->join('db_pasien.tb_tanda_vital tv', 'pr.id = tv.ref AND tv.data_source = 19', 'left');
      $this->db->join('keperawatan.tb_skrining_nyeri sn', 'pr.id = sn.ref AND sn.data_source = 19', 'left');
      $this->db->where('pr.id', $id);
      $query = $this->db->get();
      return $query->result_array();
    } elseif (isset($param)) {
      $this->db->where('pp.NORM', $nomr);
      if ($param == 'jumlah') {
        // Jumlah
        $this->db->where('pr.status', 1);
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        // Tabel
        $this->db->order_by('pr.id', 'DESC');
        return $this->db->get();
      }
    }
  }
}

/* End of file FormulirPindahRuanganModel */
/* Location: ./application/models/transferRuangan/FormulirPindahRuanganModel.php */