<?php
defined('BASEPATH') or exit('No direct script access allowed');

class UploadRMModel extends CI_Model
{
    function cekBerkas($norm,$tanggal,$file){
        $query = "SELECT
                    * 
                FROM
                    db_layanan.tb_scan_berkas tsb
                WHERE tsb.NOMR = ? AND tsb.SUBFOLDER = ? AND tsb.FILE = ?";

        $bind = $this->db->query($query, array($norm,$tanggal,$file));
        return $bind;
    }

}

/* End of file UploadRmModel.php */
/* Location: ./application/models/UploadRmModel.php */