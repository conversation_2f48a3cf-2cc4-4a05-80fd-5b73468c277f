<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CekPIRI extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/admission/CekPIRIModel'
    ));
  }

  public function index(){
  	// $norm = $this->uri->segment(6);
   //  $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'TujuanRICekPIRI' => $this->masterModel->referensi(1614),
      'KemoterapiCekPIRI' => $this->masterModel->referensi(1615),
      'KemoradiasiCekPIRI' => $this->masterModel->referensi(1616),
      'RadioterapiCekPIRI' => $this->masterModel->referensi(1617),
      'OperasiCekPIRI' => $this->masterModel->referensi(1618),
      'PKUCekPIRI' => $this->masterModel->referensi(1619),
      'LabKemoterapiCekPIRI' => $this->masterModel->referensi(1620),
      'HasilPeriksaKemoterapiCekPIRI' => $this->masterModel->referensi(1621),
      'ResepObatKemoterapiCekPIRI' => $this->masterModel->referensi(1622),
      'LabKemoradiasiCekPIRI' => $this->masterModel->referensi(1623),
      'HasilPeriksaKemoradiasiCekPIRI' => $this->masterModel->referensi(1624),
      'ResepObatKemoradiasiCekPIRI' => $this->masterModel->referensi(1625),
      'KonsultasiKemoradiasiCekPIRI' => $this->masterModel->referensi(1626),
      'LabRadioterapiCekPIRI' => $this->masterModel->referensi(1627),
      'HasilPeriksaRadioterapiCekPIRI' => $this->masterModel->referensi(1628),
      'KonsultasiRadioterapiCekPIRI' => $this->masterModel->referensi(1629),
      'LabOperasiCekPIRI' => $this->masterModel->referensi(1630),
      'HasilPeriksaOperasiCekPIRI' => $this->masterModel->referensi(1631),
      'KonsultasiOperasiCekPIRI' => $this->masterModel->referensi(1632),
      'LabPKUCekPIRI' => $this->masterModel->referensi(1633),
      'HasilPeriksaPKUCekPIRI' => $this->masterModel->referensi(1634),
      'KonsultasiPKUCekPIRI' => $this->masterModel->referensi(1635),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );
     // print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/admission/CekPIRI/index', $data);
  }

  public function simpanCekPIRI()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $date = $this->input->post('tgl_CekPIRI');
    $tglCekPIRI = date('Y-m-d h:i', strtotime($date));

    $dataCekPIRI = array (
      'nokun'                            => $post['nokun'],
      'tujuan_rawat_inap'                => implode(',',$post["TujuanRICekPIRI"]),

      //KEMOTERAPI
      'tujuan_kemoterapi'                => implode(',',$post["KemoterapiCekPIRI"]),
      'tujuan_lab_kemoterapi'            => $post["labKemo"],
      'lab_kemoterapi'                   => implode(',',$post["LabKemoterapiCekPIRI"]),
      'lain_lab_kemoterapi'              => $post['labKemo2'],
      'tujuan_hasil_kemoterapi'          => $post["hasilKemo"],
      'hasil_periksa_kemoterapi'         => implode(',',$post["HasilPeriksaKemoterapiCekPIRI"]),
      'lain_hasil_kemoterapi'            => $post['hasilKemo2'],
      'tujuan_resep_kemoterapi'          => $post["resepKemo"],
      'resep_obat_kemoterapi'            => implode(',',$post["ResepObatKemoterapiCekPIRI"]),
      //KEMORADIASI
      'tujuan_kemoradiasi'               => implode(',',$post["KemoradiasiCekPIRI"]),
      'tujuan_lab_kemoradiasi'           => $post["labKemoRa"],
      'lab_kemoradiasi'                  => implode(',',$post["LabKemoradiasiCekPIRI"]),
      'lain_lab_kemoradiasi'             => $post['labKemoRa2'],
      'tujuan_hasil_kemoradiasi'         => $post["hasilKemoRa"],
      'hasil_periksa_kemoradiasi'        => implode(',',$post["HasilPeriksaKemoradiasiCekPIRI"]),
      'lain_hasil_kemoradiasi'           => $post['hasilKemoRa2'],
      'tujuan_resep_kemoradiasi'         => $post["resepKemoRa"],
      'resep_obat_kemoradiasi'           => implode(',',$post["ResepObatKemoradiasiCekPIRI"]),
      'tujuan_konsultasi_kemoradiasi'    => $post["konsulKemoRa"],
      'konsultasi_kemoradiasi'           => implode(',',$post["KonsultasiKemoradiasiCekPIRI"]),
      'lain_konsultasi_kemoradiasi'      => $post['konsulKemoRa2'],
      //RADIOTERAPI
      'tujuan_radioterapi'               => implode(',',$post["RadioterapiCekPIRI"]),
      'tujuan_lab_radioterapi'           => $post["labRadio"],
      'lab_radioterapi'                  => implode(',',$post["LabRadioterapiCekPIRI"]),
      'lain_lab_radioterapi'             => $post['labRadio2'],
      'tujuan_hasil_radioterapi'         => $post["hasilRadio"],
      'hasil_periksa_radioterapi'        => implode(',',$post["HasilPeriksaRadioterapiCekPIRI"]),
      'lain_hasil_radioterapi'           => $post['hasilRadio2'],
      'resep_obat_radioterapi'           => implode(',',$post["ResepObatKemoradiasiCekPIRI"]),
      'tujuan_konsultasi_radioterapi'    => $post["konsulRadio"],
      'konsultasi_radioterapi'           => implode(',',$post["KonsultasiRadioterapiCekPIRI"]),
      //OPERASI
      'tujuan_operasi'                   => implode(',',$post["OperasiCekPIRI"]),
      'golongan_darah_operasi'           => $post["golDarah"],
      'tujuan_lab_operasi'               => $post["labOperasi"],
      'lab_operasi'                      => implode(',',$post["LabOperasiCekPIRI"]),
      'lain_lab_operasi'                 => $post['labOperasi2'],
      'tujuan_hasil_operasi'             => $post["hasilOperasi"],
      'hasil_periksa_operasi'            => implode(',',$post["HasilPeriksaOperasiCekPIRI"]),
      'tujuan_konsultasi_operasi    '    => $post["konsulOperasi"],
      'konsultasi_operasi'               => implode(',',$post["KonsultasiOperasiCekPIRI"]),
      'lain_konsultasi_operasi'          => $post['konsulOperasi2'],
      //PKU
      'tujuan_pku'                       => implode(',',$post["PKUCekPIRI"]),
      'tujuan_lab_pku'                   => $post["labPku"],
      'lab_pku'                          => implode(',',$post["LabPKUCekPIRI"]),
      'lain_lab_pku'                     => $post['labPku2'],
      'tujuan_hasil_pku'                 => $post["hasilPku"],
      'hasil_periksa_pku'                => implode(',',$post["HasilPeriksaPKUCekPIRI"]),
      'lain_hasil_pku'                   => $post['hasilPku2'],
      'tujuan_konsultasi_pku'            => $post["konsulPku"],
      'konsultasi_pku'                   => implode(',',$post["KonsultasiPKUCekPIRI"]),
      'lain_konsultasi_pku'              => $post['konsulPku2'],

      'petugas_admisi_igd'               => $post['ttd_AdmisiIGD'],
      'petugas_ruangan_ri'               => $post['ttd_PetugasRuangan'],
      'ttd_petugas_admisi_igd'           => file_get_contents($this->input->post('sign_AdmisiIGD')),
      'ttd_petugas_ruangan_ri'           => file_get_contents($this->input->post('sign_PetugasRuangan')),
      'oleh'                             => $this->session->userdata('id'),
      'status_ri_igd'                    => 1,
    );
    // echo "<pre>";print_r($dataCekPIRI);echo "</pre>";

    $this->CekPIRIModel->simpanCekPIRI($dataCekPIRI);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyCekPIRI()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    // $nomr = $this->uri->segment(6);
    $listCekPIRI = $this->CekPIRIModel->listHistoryCekPIRI($nomr);

    $data = array();
    $no = 1;
    foreach ($listCekPIRI->result() as $CekPIRI) {
       $stat = $CekPIRI->status_ri_igd;
       if($CekPIRI->status_ri_igd == 1){
        $status = 'Aktif';
        $button = '<a href="#modalCekPIRI" class="btn btn-primary btn-block" data-id="'.$CekPIRI->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Non-Aktif</a>';
      }else{
        $status = 'Tidak Aktif';
         $button = '';
      }
      $data[] = array(
        $no,
        $CekPIRI->nokun,
        $CekPIRI->PETUGAS_IGD,
        $CekPIRI->PETUGAS_RUANGAN,
        $CekPIRI->NAMA,
        date("d-m-Y H:i:s",strtotime($CekPIRI->tanggal)),
        $status,
        $button,
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listCekPIRI->num_rows(),
      "recordsFiltered" => $listCekPIRI->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalCekPIRI()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    $gpCekPIRI = $this->CekPIRIModel->getCekPIRI($id);
    $nokun = $gpCekPIRI['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $explode_tujuan_rawat_inap            = explode(',' , $gpCekPIRI['tujuan_rawat_inap']);
    $explode_tujuan_kemoterapi            = explode(',' , $gpCekPIRI['tujuan_kemoterapi']);
    $explode_lab_kemoterapi               = explode(',' , $gpCekPIRI['lab_kemoterapi']);
    $explode_hasil_periksa_kemoterapi     = explode(',' , $gpCekPIRI['hasil_periksa_kemoterapi']);
    $explode_resep_obat_kemoterapi        = explode(',' , $gpCekPIRI['resep_obat_kemoterapi']);
    $explode_tujuan_kemoradiasi           = explode(',' , $gpCekPIRI['tujuan_kemoradiasi']);
    $explode_lab_kemoradiasi              = explode(',' , $gpCekPIRI['lab_kemoradiasi']);
    $explode_hasil_periksa_kemoradiasi    = explode(',' , $gpCekPIRI['hasil_periksa_kemoradiasi']);
    $explode_resep_obat_kemoradiasi       = explode(',' , $gpCekPIRI['resep_obat_kemoradiasi']);
    $explode_konsultasi_kemoradiasi       = explode(',' , $gpCekPIRI['konsultasi_kemoradiasi']);
    $explode_tujuan_radioterapi           = explode(',' , $gpCekPIRI['tujuan_radioterapi']);
    $explode_lab_radioterapi              = explode(',' , $gpCekPIRI['lab_radioterapi']);
    $explode_hasil_periksa_radioterapi    = explode(',' , $gpCekPIRI['hasil_periksa_radioterapi']);
    $explode_resep_obat_radioterapi       = explode(',' , $gpCekPIRI['resep_obat_radioterapi']);
    $explode_konsultasi_radioterapi       = explode(',' , $gpCekPIRI['konsultasi_radioterapi']);
    $explode_tujuan_operasi               = explode(',' , $gpCekPIRI['tujuan_operasi']);
    $explode_lab_operasi                  = explode(',' , $gpCekPIRI['lab_operasi']);
    $explode_hasil_periksa_operasi        = explode(',' , $gpCekPIRI['hasil_periksa_operasi']);
    $explode_konsultasi_operasi           = explode(',' , $gpCekPIRI['konsultasi_operasi']);
    $explode_tujuan_pku                   = explode(',' , $gpCekPIRI['tujuan_pku']);
    $explode_lab_pku                      = explode(',' , $gpCekPIRI['lab_pku']);
    $explode_hasil_periksa_pku            = explode(',' , $gpCekPIRI['hasil_periksa_pku']);
    $explode_konsultasi_pku               = explode(',' , $gpCekPIRI['konsultasi_pku']);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'gpCekPIRI' => $gpCekPIRI,
      'explode_tujuan_rawat_inap'         => $explode_tujuan_rawat_inap,
      'explode_tujuan_kemoterapi'         => $explode_tujuan_kemoterapi,
      'explode_lab_kemoterapi'            => $explode_lab_kemoterapi,
      'explode_hasil_periksa_kemoterapi'  => $explode_hasil_periksa_kemoterapi,
      'explode_resep_obat_kemoterapi'     => $explode_resep_obat_kemoterapi,
      'explode_tujuan_kemoradiasi'        => $explode_tujuan_kemoradiasi,
      'explode_lab_kemoradiasi'           => $explode_lab_kemoradiasi,
      'explode_hasil_periksa_kemoradiasi' => $explode_hasil_periksa_kemoradiasi,
      'explode_resep_obat_kemoradiasi'    => $explode_resep_obat_kemoradiasi,
      'explode_konsultasi_kemoradiasi'    => $explode_konsultasi_kemoradiasi,
      'explode_tujuan_radioterapi'        => $explode_tujuan_radioterapi,
      'explode_lab_radioterapi'           => $explode_lab_radioterapi,
      'explode_hasil_periksa_radioterapi' => $explode_hasil_periksa_radioterapi,
      'explode_resep_obat_radioterapi'    => $explode_resep_obat_radioterapi,
      'explode_konsultasi_radioterapi'    => $explode_konsultasi_radioterapi,
      'explode_tujuan_operasi'            => $explode_tujuan_operasi,
      'explode_lab_operasi'               => $explode_lab_operasi,
      'explode_hasil_periksa_operasi'     => $explode_hasil_periksa_operasi,
      'explode_konsultasi_operasi'        => $explode_konsultasi_operasi,
      'explode_tujuan_pku'                => $explode_tujuan_pku,
      'explode_lab_pku'                   => $explode_lab_pku,
      'explode_hasil_periksa_pku'         => $explode_hasil_periksa_pku,
      'explode_konsultasi_pku'            => $explode_konsultasi_pku,
      //
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'TujuanRICekPIRI' => $this->masterModel->referensi(1614),
      'KemoterapiCekPIRI' => $this->masterModel->referensi(1615),
      'KemoradiasiCekPIRI' => $this->masterModel->referensi(1616),
      'RadioterapiCekPIRI' => $this->masterModel->referensi(1617),
      'OperasiCekPIRI' => $this->masterModel->referensi(1618),
      'PKUCekPIRI' => $this->masterModel->referensi(1619),
      'LabKemoterapiCekPIRI' => $this->masterModel->referensi(1620),
      'HasilPeriksaKemoterapiCekPIRI' => $this->masterModel->referensi(1621),
      'ResepObatKemoterapiCekPIRI' => $this->masterModel->referensi(1622),
      'LabKemoradiasiCekPIRI' => $this->masterModel->referensi(1623),
      'HasilPeriksaKemoradiasiCekPIRI' => $this->masterModel->referensi(1624),
      'ResepObatKemoradiasiCekPIRI' => $this->masterModel->referensi(1625),
      'KonsultasiKemoradiasiCekPIRI' => $this->masterModel->referensi(1626),
      'LabRadioterapiCekPIRI' => $this->masterModel->referensi(1627),
      'HasilPeriksaRadioterapiCekPIRI' => $this->masterModel->referensi(1628),
      'KonsultasiRadioterapiCekPIRI' => $this->masterModel->referensi(1629),
      'LabOperasiCekPIRI' => $this->masterModel->referensi(1630),
      'HasilPeriksaOperasiCekPIRI' => $this->masterModel->referensi(1631),
      'KonsultasiOperasiCekPIRI' => $this->masterModel->referensi(1632),
      'LabPKUCekPIRI' => $this->masterModel->referensi(1633),
      'HasilPeriksaPKUCekPIRI' => $this->masterModel->referensi(1634),
      'KonsultasiPKUCekPIRI' => $this->masterModel->referensi(1635),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );

    $this->load->view('rekam_medis/rawat_inap/admission/CekPIRI/view_edit', $data);
  }

  public function updateCekPIRI()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    // $idCekPIRI = $this->input->post('idCekPIRI');
    $post = $this->input->post();

    $dataCekPIRI_edit = array (
      'status_ri_igd'            => 0
    );

     // echo "<pre>";print_r($dataCekPIRI_edit);echo "</pre>";exit();

    $this->CekPIRIModel->updateCekPIRI($dataCekPIRI_edit,$id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}
?>