<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Realisasi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('keuangan/RealisasiModel','keuanganModel'));
  }

  public function index() {

    $data = array(
      'title'       => 'Halaman Master Realisasi',
      'isi'         => 'Keuangan_new/realisasi/index'
    );

    $this->load->view('layout/wrapper',$data);
  }


  public function action($param){
    if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest')
    {
      if($param == 'tambah' || $param == 'ubah'){
        // $rules = $this->keuanganModel->rules;
        // $this->form_validation->set_rules($rules);

        // if($this->form_validation->run() == TRUE){
          $post = $this->input->post();
          $this->db->trans_begin();

          $id_rkakl      = $this->input->post("uraian");
          $biayarealisai = $this->input->post("biaya");
          
          $searchMak     = $this->keuanganModel->getIdMakNew($id_rkakl);
          $jmlrkakl = $searchMak['PAGU_TRANSAKSI'];
          // $sisa_saldo  = $jmlrkakl - $biayarealisai;
          $hasiljumlah   = $jmlrkakl + $biayarealisai;

          $dataRealisasi = array(
            'ID_RKAKL'    => $id_rkakl,
            'TOTAL_BAYAR' => $biayarealisai,
            // 'SISA_SALDO'  => $sisa_saldo,
            'VOLUME'      => $this->input->post("volume"),
            'TANGGAL'     => $this->input->post("tanggal"),
            'RUANGAN'     => $this->input->post("ruangan"),
            'OLEH'        => $this->session->userdata('id'),
            'STATUS'      => '1',
          );

          $this->db->insert('db_keuangan.realisasi_rkakl', $dataRealisasi);

          $data2 = array(
            'PAGU_TRANSAKSI' => $hasiljumlah,
          );

          $this->db->where('ID', $id_rkakl);
          $this->db->update('db_keuangan.rkakl', $data2);

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        // }else{
        //   $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        // }
        echo json_encode($result);
      }
    }
  }

  public function datatables(){
        $result = $this->RealisasiModel->datatables();
        $no=1;
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> URAIAN;      
            $sub_array[] = $row -> DESKRUANGAN;
            $sub_array[] = "Rp ".number_format((double)$row -> TOTAL_BAYAR,2,',','.');
            $sub_array[] = $row -> NAMAPEGAWAI;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->RealisasiModel->total_count(),
            "recordsFiltered"   => $this->RealisasiModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

}

/* End of file Realisasi.php */
/* Location: ./application/controllers/keuangan/Realisasi.php */