<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CpptModel extends MY_Model
{
    protected $_table_name = 'keperawatan.tb_cppt';
    protected $_primary_key = 'id';
    protected $_order_by = 'tanggal';
    protected $_order_by_type = 'DESC';

    public $rules = [
        'nokun' => [
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'numeric' => '%s Wajib Angka'
            ],
        ],

        'subyektif' => [
            'field' => 'subyektif',
            'label' => 'Subyektif',
            'rules' => 'trim|required|min_length[10]',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'min_length' => '{field} minimal berisi {param} karakter'
            ],
        ],

        'obyektif' => [
            'field' => 'obyektif',
            'label' => 'Obyektif',
            'rules' => 'trim|required|min_length[10]',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'min_length' => '{field} minimal berisi {param} karakter'
            ],
        ],

        'instruksi' => [
            'field' => 'instruksi',
            'label' => 'Instruksi',
            'rules' => 'trim|required|min_length[10]',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'min_length' => '{field} minimal berisi {param} karakter'
            ],
        ],
    ];

    public $rules_ap = [
        'analisis' => [
            'field' => 'analisis',
            'label' => 'Analisis',
            'rules' => 'trim|required|min_length[10]',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'min_length' => '{field} minimal berisi {param} karakter'
            ],
        ],

        'perencanaan' => [
            'field' => 'perencanaan',
            'label' => 'Perencanaan',
            'rules' => 'trim|required|min_length[10]',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'min_length' => '{field} minimal berisi {param} karakter'
            ],
        ],
    ];

    public $rules_adime = [
        'nokun' => [
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'numeric' => '%s Wajib Angka'
            ],
        ],

        'asesmen' => [
            'field' => 'asesmen',
            'label' => 'Asesmen',
            'rules' => 'trim|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
            ],
        ],

        'diagnosa' => [
            'field' => 'diagnosa',
            'label' => 'Diagnosa',
            'rules' => 'trim|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
            ],
        ],

        'intervensi' => [
            'field' => 'intervensi',
            'label' => 'Intervensi',
            'rules' => 'trim|required',
            'errors' => [
                'required' => '%s Wajib Diisi'
            ],
        ],

        'monitoring' => [
            'field' => 'monitoring',
            'label' => 'Monitoring',
            'rules' => 'trim|required',
            'errors' => [
                'required' => '%s Wajib Diisi'
            ],
        ],

        'evaluasi' => [
            'field' => 'evaluasi',
            'label' => 'Evaluasi',
            'rules' => 'trim|required',
            'errors' => [
                'required' => '%s Wajib Diisi'
            ],
        ],
    ];

    public $rules_skrining_nyeri = [
        'skrining_nyeri' => [
            'field' => 'skrining_nyeri',
            'label' => 'Skrining Nyeri',
            'rules' => 'trim|numeric|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'numeric' => '%s Wajib Angka'
            ],
        ],
    ];

    public $rules_nyeri = [
        'skor_nyeri' => [
            'field' => 'skor_nyeri',
            'label' => 'Skala Nyeri',
            'rules' => 'trim|numeric|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'numeric' => '%s Wajib Angka'
            ],
        ],
    ];

    public $kesadaran = [
        'kesadaran' => [
            'field' => 'kesadaran',
            'label' => 'Kesadaran',
            'rules' => 'trim|numeric|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'numeric' => '%s Wajib Angka'
            ],
        ],
    ];

    public $rules_asuhan_keperawatan = [
        'asuhankeperawatancppt[]' => [
            'field' => 'asuhankeperawatancppt[]',
            'label' => 'Asuhan Keperawatan',
            'rules' => 'trim|numeric|required',
            'errors' => [
                'required' => '%s Wajib Diisi',
                'numeric' => '%s Wajib Angka'
            ],
        ],
    ];

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select(
            'ktcp.id IDCPPT, ktcp.jenis, ktcp.pemberi_cppt, ktcp.status_verif, ktcp.nokun, ktcp.status_backdate,
            ktcp.tanggal, ktcp.subyektif, ktcp.obyektif, ktcp.analisis, ktcp.perencanaan, ktcp.instruksi,
            ktcp.asesmen_gizi, ktcp.diagnosa_gizi, ktcp.status_gizi, ktcp.intervensi, ktcp.diet, ktcp.bentuk,
            ktcp.route, ktcp.energi, ktcp.protein, ktcp.lemak, ktcp.kh, ktcp.energi_desc, ktcp.protein_desc,
            ktcp.lemak_desc, ktcp.kh_desc, ktcp.monitoring, ktcp.evaluasi, ktcp.kesadaran, ktcp.tb_bb, ktcp.tanda_vital,
            ktcp.skrining_nyeri, master.getNamaLengkap(pp.NORM) NAMAPASIEN,
            master.getNamaLengkapPegawai(ap.NIP)NAMAPEGAWAI, pro.DESKRIPSI PROFESI, mru.ID IDRUANGAN,
            mru.DESKRIPSI RUANGAN, mru.JENIS_KUNJUNGAN, master.getNamaLengkapPegawai(md.NIP) DOKTERDPJP,
            master.getNamaLengkapPegawai(mdtbak.NIP) DOKTERTBAK, ktcp.jenis_cppt JENIS_CPPT,
            master.getNamaLengkapPegawai(apv.NIP) VERIFOLEH, HOUR(TIMEDIFF(NOW(), ktcp.tanggal)) DURASI_CPPT,
            IF(HOUR(TIMEDIFF(NOW(), ktcp.tanggal)) <= 24, 1, 0) STATUS_EDIT_CPPT,
            (SELECT IF(COUNT(*) > 0, 1, 0) FROM keperawatan.tb_tbak_detail td WHERE td.id_cppt = ktcp.id AND td.status != 0) TBAK, ktcp.oleh'
        );
        $this->db->from('keperawatan.tb_cppt ktcp');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ktcp.nokun', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'LEFT');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = ktcp.oleh', 'LEFT');
        $this->db->join('master.pegawai peg', 'peg.NIP = ap.NIP', 'LEFT');
        $this->db->join('master.referensi pro', 'pro.ID = peg.PROFESI AND pro.JENIS = 36', 'LEFT');
        $this->db->join('master.ruangan mru', 'mru.ID = pk.RUANGAN', 'LEFT');
        $this->db->join('pendaftaran.tujuan_pasien ptp', 'ptp.NOPEN = pk.NOPEN', 'LEFT');
        $this->db->join('master.dokter md', 'md.ID = ptp.DOKTER', 'LEFT');
        $this->db->join('master.dokter mdtbak', 'mdtbak.ID = ktcp.dokter_tbak', 'LEFT');
        $this->db->join('aplikasi.pengguna apv', 'apv.ID = ktcp.verif_oleh', 'LEFT');

        $this->db->where('ktcp.STATUS !=', '0');
        $this->db->order_by('ktcp.id', 'DESC');

        if ($this->input->post('id')) {
            $this->db->where('ktcp.id', $this->input->post('id'));
        }

        if ($this->input->post('nomr')) {
            $this->db->where('pp.NORM', $this->input->post('nomr'));
        }

        if ($this->input->post('nopen')) {
            $this->db->where('pk.NOPEN', $this->input->post('nopen'));
        }

        if ($this->input->post('oleh')) {
            $this->db->where('ktcp.oleh', $this->input->post('oleh'));
        }

        if ($this->input->post('search[value]')) {
            $this->db->group_start();
            $this->db->like('ktcp.tanggal', $this->input->post('search[value]'));
            $this->db->or_like('pro.DESKRIPSI', $this->input->post('search[value]'));
            $this->db->or_like('mru.DESKRIPSI', $this->input->post('search[value]'));
            $this->db->or_like('master.getNamaLengkapPegawai(ap.NIP)', $this->input->post('search[value]'));
            $this->db->or_like('master.getNamaLengkapPegawai(md.NIP)', $this->input->post('search[value]'));
            $this->db->group_end();
        }
    }

    function count_history()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'] ?? 0;
        $nomr = $post['nomr'] ?? 0;
        $nopen = $post['nopen'] ?? 0;
        $oleh = $post['oleh'] ?? 0;

        $query = $this->db->query("CALL keperawatan.jumlahCpptList('$nomr', '$id', '$nopen', '$oleh')");
        $this->db->next_result();
        return $query->row_array();
    }

    function get_history($single = false)
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'] ?? 0;
        $nomr = $post['nomr'] ?? 0;
        $nopen = $post['nopen'] ?? 0;
        $oleh = $post['oleh'] ?? 0;
        $limit = $post['limit'] ?? 0;
        $offset = $post['offset'] ?? 0;

        $query = $this->db->query(
            "CALL keperawatan.cpptList3('$nomr', '$id', '$nopen', '$oleh', '$limit', '$offset')"
        );

        if ($single == true) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        $this->db->next_result();
        return $query->$method();
    }

    function get_history_tanggal($single = false)
    {
        $post = $this->input->post();
        $id = $post['id'] ?? 0;
        $nomr = $post['nomr'] ?? 0;
        $nopen = $post['nopen'] ?? 0;
        $oleh = $post['oleh'] ?? 0;
        $tanggal = $post['tanggal'] ?? 0;

        $query = $this->db->query("CALL keperawatan.cpptList2('$nomr', '$id', '$nopen', '$oleh', '$tanggal')");

        if ($single == true) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        $this->db->next_result();
        return $query->$method();
    }

    function get_table($single = true)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == true) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

    // function get_history($single = true)
    // {
    //     $this->history_query();
    //     $query = $this;
    //     if ($single == true) {
    //         $method = 'row';
    //     } else {
    //         $method = 'result';
    //     }
    //     return $query->$method();
    // }

    public function simpanCatatan($data)
    {
        $this->db->insert('keperawatan.tb_cppt_catatan', $data);
    }

    public function ubahCatatan($id, $data)
    {
        $this->db->where('keperawatan.tb_cppt_catatan.id', $id);
        $this->db->update('keperawatan.tb_cppt_catatan', $data);
    }

    public function ambilTanggal($nomr)
    {
        $this->db->select('date(c.tanggal) tanggal');
        $this->db->from('keperawatan.tb_cppt c');
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = c.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
        $this->db->where('p.NORM', $nomr);
        $this->db->group_by('tanggal');
        $this->db->order_by('tanggal', 'DESC');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function ubahBanyak($data, $id)
    {
        $this->db->update_batch('keperawatan.tb_cppt', $data, $id);
    }

    public function ambilPerNopen($nopen)
    {
        $this->db->select('analisis, instruksi');
        $this->db->from('keperawatan.tb_cppt c');
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = c.nokun', 'left');
        $this->db->where('k.NOPEN', $nopen);
        $this->db->where('c.pemberi_cppt', 1);
        $this->db->order_by('c.tanggal', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();
        return $query->row_array();
    }

    public function ambilIDTagihan($nopen)
    {
        $this->db->select('tp.TAGIHAN, tp.PENDAFTARAN');
        $this->db->from('pendaftaran.pendaftaran pen');
        $this->db->join('pembayaran.tagihan_pendaftaran tp', 'tp.PENDAFTARAN = pen.NOMOR', 'left');
        $this->db->where('pen.NOMOR', $nopen);
        $this->db->where('tp.STATUS', 1);

        $query = $this->db->get();
        return $query->row_array();
    }

    public function listTindakan($ruangan)
    {
        $this->db->select('tr.ID, td.ID AS ID_TINDAKAN, tr.RUANGAN, td.NAMA, tt.TARIF');
        $this->db->from('master.tindakan_ruangan tr');
        $this->db->join('master.tindakan td', 'td.ID = tr.TINDAKAN');

        if (in_array($ruangan, ['105020201', '105020202', '105020204'])) {
            $this->db->join('master.tarif_tindakan tt', 'td.ID = tt.TINDAKAN AND tt.KELAS=56 AND tt.STATUS = 1');
        } else {
            $this->db->join('master.tarif_tindakan tt', 'td.ID = tt.TINDAKAN AND tt.STATUS = 1');
        }

        $this->db->where('tr.RUANGAN', $ruangan);
        // $this->db->where('td.STATUS', 1);
        $this->db->where('tr.STATUS', 1);
        $this->db->group_by('td.ID');

        // Menyortir berdasarkan tindakan yang berawalan 'Konsultasi' dan NAMA secara ASC
        $this->db->order_by('(CASE WHEN td.NAMA LIKE "Konsultasi%" THEN 0 ELSE 1 END)', 'ASC');
        $this->db->order_by('td.NAMA', 'ASC');

        $query = $this->db->get();
        return $query->result();
    }

    public function cekPengkajianAwalRanap($nopen)
	{
		$query = $this->db->query(
			"SELECT pp.NOMOR nopen, IF(SUM(IF(mm.jenis IN (5,6,7), 1, 0)) >= 1, 1, 0) pengkajianAwal, master.getNamaLengkapPegawai(ap.NIP) dokter, mm.created_at

            FROM pendaftaran.pendaftaran pp

            LEFT JOIN medis.tb_medis mm ON mm.nopen = pp.NOMOR AND mm.status != 0
            LEFT JOIN aplikasi.pengguna ap ON ap.ID = mm.created_by

            WHERE pp.STATUS = 1 AND pp.NOMOR = '$nopen'

            GROUP BY pp.NOMOR"
		);
		return $query->row_array();
	}

    public function listHistoryPergantianDPJP($nopen)
    {
        $query = $this->db->query("SELECT
                                        cds.*,
                                        master.getNamaLengkapPegawai(dok.NIP) DPJP_UTAMA,
                                        master.getNamaLengkapPegawai(dok2.NIP) DPJP_PENGGANTI,
                                        CASE
                                            WHEN cds.alasan = 1 THEN 'DPJP Bersama'
                                            WHEN cds.alasan = 2 THEN 'Pindah DPJP'
                                            WHEN cds.alasan = 3 THEN 'Cuti'
                                            ELSE
                                            '-'
                                        END ALASAN_GANTI,
                                        peng.NAMA OLEH_INPUT
                                    FROM
                                        keperawatan.tb_cppt_dpjp_sementara cds
                                        LEFT JOIN master.dokter dok ON dok.ID = cds.dpjp
                                        LEFT JOIN master.dokter dok2 ON dok2.ID = cds.dpjp_pengganti
                                        LEFT JOIN aplikasi.pengguna peng ON peng.ID = cds.oleh
                                    WHERE
                                        cds.nopen = '$nopen'
                                    ORDER BY
                                    cds.created_at DESC");
        return $query;
    }

    function listRanapTowerC()
    {
        $query = $this->db->query("SELECT * FROM `master`.`ruangan` WHERE `GEDUNG` = '1' AND `STATUS` = '1' AND `JENIS_KUNJUNGAN` = '3'");
        return $query;
    }

    function cekDPJPPenggantiCPPT($nopen,$iddokter)
    {
        $query = $this->db->query("SELECT
                                    * 
                                FROM
                                    keperawatan.tb_cppt_dpjp_sementara cds
                                WHERE cds.status = 1 AND cds.nopen = '$nopen' AND cds.dpjp_pengganti = $iddokter");
        return $query;
    }
}

// End of file CPPTModel.php
// Location: ./application/models/rekam_medis/rawat_inap/catatanTerintegrasi/CPPTModel.php