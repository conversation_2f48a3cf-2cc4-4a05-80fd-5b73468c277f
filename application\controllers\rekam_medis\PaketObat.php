<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PaketObat extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
        redirect('login');
    }

    $this->load->model(array('rekam_medis/PaketObatModel','rekam_medis/PaketObatDetilModel'));
  }

  public function datatables(){
    $result = $this->PaketObatModel->datatables();

    $data = array();
    $no=1;
    foreach ($result as $row){
      $sub_array = array();  
      $sub_array[] = $no;
      $sub_array[] = $row -> NAMA;
      $sub_array[] = "<button type='button' class='btn btn-primary btn-xs pilihPaketObat' data-id='".$row -> ID."'>Pilih</button>";

      $data[] = $sub_array;
      $no++;
    }

    $output = array(
      "draw"              => intval($_POST["draw"]),  
      "recordsTotal"      => $this->PaketObatModel->total_count(),
      "recordsFiltered"   => $this->PaketObatModel->filter_count(),
      "data"              => $data
    );
    echo json_encode($output);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'ambil') {
        $result = $this->PaketObatDetilModel->get_table(false);

        echo json_encode(
          array(
            'status' => 'success',
            'data'   => $result
          )
        );
      }
    }
  }

}
