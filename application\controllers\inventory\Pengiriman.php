<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>giriman extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(22, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('inventory/Model_barang', 'inventory/Model_kategori', 'inventory/Model_satuan', 'inventory/Model_penyedia', 'inventory/Model_permintaan', 'inventory/Model_pengiriman'));
    date_default_timezone_set("Asia/Bangkok");
  }

  function index()
  {
    $gudang     = $this->Model_permintaan->tampilkan_gudang()->result();
    $data       = array(
      'title'         => 'Halaman Input Pengiriman',
      'isi'           => 'inventory/pengiriman/index',
      'gudang'        => $gudang,
    );
    $this->load->view('layout/wrapper', $data);
  }

  public function view_data()
  {
    if (isset($_POST['cari'])) {
      $data['v_data']  = $this->Model_pengiriman->view_data($this->input->post('id_gudang'));
      $this->load->view('inventory/pengiriman/tabel_data', $data);
    } else {
      echo "Silahkan Cek koneksi internet Anda!";
    }
  }

  function lihatPermintaan()
  {
    $id =  $this->uri->segment(4);
    $record =  $this->Model_pengiriman->detail_kirim($id)->result_array();
    $pegawai = $this->Model_pengiriman->listPegawai()->result();
    $data       = array(
      'title'         => 'Halaman Input Pengiriman',
      'isi'           => 'inventory/pengiriman/form_kirim',
      'record'        => $record,
      'pegawai'       => $pegawai,
    );
    $this->load->view('layout/wrapper', $data);
  }

  function getnopengiriman($ruangan)
  {
    $tanggal = date("Y-m-d");
    $query = $this->db->query("SELECT invenumum.generateNoPengiriman('$ruangan','$tanggal') ID")->row();
    return $query->ID;
  }

  public function tambah()
  {
    $post = $this->input->post();
    //$this->db->trans_start();
    $oleh         = $this->session->userdata("id");
    $ruang_asal   = $this->input->post('TUJUAN');
    $gudang       = $this->input->post('ASAL');
    $nomor        = $this->input->post('NOMOR');
    $penerima     = $this->input->post('PENERIMA');
    $keterangan   = $this->input->post('KETERANGAN');
    $tanggalkirim = $this->input->post('TANGGAL_KIRIM');
    $harga        = $this->input->post('HARGA');
    //$barang     = $this->input->post('BARANG');
    $tanggal      = date('Y-m-d H:i:s');
    $idpengiriman = $this->Model_pengiriman->get_no_pengiriman();
    $post = $this->input->post();
    $data = array(
      'NOMOR'         => $idpengiriman,
      'ASAL'          => $ruang_asal,
      'TUJUAN'        => $gudang,
      'TANGGAL'       => $tanggal,
      'PERMINTAAN'    => $nomor,
      'OLEH'          => $oleh,
      'PENERIMA'      => $penerima,
      'TANGGAL_KIRIM' => $tanggalkirim
    );
    // echo "<pre>";
    // print_r($data);
    // exit();
    $this->Model_pengiriman->simpan_kirim($data);
    $this->db->query('update invenumum.permintaan SET STATUS=2 WHERE NOMOR=' . $nomor . '');

    foreach ($post['ID_DETAIL'] as $key => $value) {
      //$total = $post['STOK'] - $post['JUMLAH'];
      if ($post['ID_DETAIL'][$key] != '' || $post['JUMLAH'][$key] != '') {
        $simpan = array(
          'PENGIRIMAN'                => $idpengiriman,
          'PERMINTAAN_BARANG_DETIL'   => $post['ID_DETAIL'][$key],
          'STOK_GUDANG'               => $post['STOK'][$key],
          'JUMLAH'                    => $post['JUMLAH'][$key],
          'KETERANGAN'                => $post['KETERANGAN'][$key]
        );
        $this->db->insert('invenumum.pengiriman_detil', $simpan);
        $insert_id = $this->db->insert_id();
        $datatr = array(
          //'PENGIRIMAN'                => $idpengiriman,
          'BARANG_RUANGAN'        => $post['BARANG'][$key],
          'JUMLAH'                => $post['JUMLAH'][$key],
          'HARGA'                 => $post['HARGA'][$key],
          'TANGGAL_TRANSAKSI'     => $tanggalkirim,
          'STOK'                  => $post['STOK'][$key] - $post['JUMLAH'][$key],
          'JENIS'                 => 2,
          'REF'                   => $insert_id
        );
        // echo "<pre>";
        // print_r($datatr);
        // exit();

        $this->db->insert('invenumum.transaksi_stok_ruangan', $datatr);
        $insert_idtsr = $this->db->insert_id();
        $this->db->query('update invenumum.barang_gudang SET TRANSAKSI_STOK_RUANGAN=' . $insert_idtsr . ' WHERE ID_BARANG_GUDANG=' . $post['BARANG'][$key] . '');
      }
    }
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function datapengiriman()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listpengiriman = $this->Model_pengiriman->dataAll();

    //echo "<pre>";print_r($listPasien);exit();
    $data  = array();
    $no    = 1;
    foreach ($listpengiriman->result() as $lp) {

      if ($lp->STATUS == 1) {

        $ok = "<p class='text-success'>Prosess</p>";
      } else {

        $ok = "<p class='text-info'>Final</p>";
      }


      $data[] = array(
        $no,
        $lp->UNIT_MINTA,
        $lp->TUJUAN,
        date("d-m-Y", strtotime($lp->TANGGAL)),
        // $ok,
        '<a href="#detailpengiriman" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-id="' . $lp->NOMOR . '"><i class="fas fa-search"></i> Lihat</a>',

      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listpengiriman->num_rows(),
      "recordsFiltered" => $listpengiriman->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function datamodalkirim()
  {
    $id       = $this->input->post('id');
    $record =  $this->Model_pengiriman->detail_modal($id)->result_array();
    $data     = array(

      'record' => $record,
    );
    $this->load->view('inventory/pengiriman/ModalDetilKirim', $data);
  }

  public function getDataPegawai()
  {
    $peg = $this->input->get('peg');
    $query = $this->Model_pengiriman->getDataPegawai($peg, 'NAMA');
    echo json_encode($query);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'view') {
        $post = $this->input->post();
        $dataDetailOrderBarang = array();
        $index = 0;

        if (isset($post['BARANG'])) {
          foreach ($post['BARANG'] as $input) {
            if ($post['BARANG'][$index] != "") {
              $id = explode("|", $post['BARANG'][$index]);
              $minta  = $post['MINTA'][$index];
              $jumlah = $post['JUMLAH'][$index];
              if ($minta < $jumlah) {
                $nilai = 'Jumlah yang dikirim Berlebih';
              } else {
                $nilai = '';
              }
              array_push(
                $dataDetailOrderBarang,
                array(
                  'NAMA_BARANG'   => $post['NAMA_BARANG'][$index],
                  'STOK'          => $post['STOK'][$index],
                  'MINTA'         => $post['MINTA'][$index],
                  'JUMLAH'        => $post['JUMLAH'][$index],
                  'NILAI'         => $nilai,
                )
              );
            }
            $index++;
          }
        }

        $result = array(
          'dataDetailBarang' => $dataDetailOrderBarang,
        );

        echo json_encode($result);
      }
    }
  }
}
