<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Rira extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');

    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/rawat_inap/transferRuangan/RiraModel',
      )
    );
  }

  public function index()
  {
    $data = array(
      'nokun' => $this->uri->segment(6),
      'jumlah' => $this->RiraModel->history($this->uri->segment(6), 'jumlah'),
      'pilihan' => $this->masterModel->referensi(1495),
      'carsinomaTiroidIodium' => $this->masterModel->referensi(1496),
      'ecogIodium' => $this->masterModel->referensi(1497),
      'hbIodium' => $this->masterModel->referensi(1498),
      'leukositIodium' => $this->masterModel->referensi(1499),
      'trombositIodium' => $this->masterModel->referensi(1500),
      'hamilMenyusuiIodium' => $this->masterModel->referensi(1501),
      'thyraxIodium' => $this->masterModel->referensi(1502),
      'iodineIodium' => $this->masterModel->referensi(1503),
      'lprIodium' => $this->masterModel->referensi(1504),
      'efekSampingIodium' => $this->masterModel->referensi(1505),
      'meninggalIodium' => $this->masterModel->referensi(1506),
      'boneScanSamarium' => $this->masterModel->referensi(1507),
      'ecogSamarium' => $this->masterModel->referensi(1508),
      'hbSamarium' => $this->masterModel->referensi(1509),
      'leukositSamarium' => $this->masterModel->referensi(1510),
      'trombositSamarium' => $this->masterModel->referensi(1511),
      'kreatininSamarium' => $this->masterModel->referensi(1512),
      'hamilMenyusuiSamarium' => $this->masterModel->referensi(1513),
      'terapiSamarium' => $this->masterModel->referensi(1514),
      'efekSampingSamarium' => $this->masterModel->referensi(1515),
      'meninggalSamarium' => $this->masterModel->referensi(1516),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/transferRuangan/Rira/index', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->RiraModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();

          $data = array(
            'id' => isset($post['id']) ? $post['id'] : null,
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'pilihan' => isset($post['pilihan']) ? implode('-', $post['pilihan']) : null,

            // Masuk iodium
            'carsinoma_tiroid_iodium' => isset($post['carsinoma_tiroid_iodium']) ? $post['carsinoma_tiroid_iodium'] : null,
            'ecog_iodium' => isset($post['ecog_iodium']) ? $post['ecog_iodium'] : null,
            'hb_iodium' => isset($post['hb_iodium']) ? $post['hb_iodium'] : null,
            'leukosit_iodium' => isset($post['leukosit_iodium']) ? $post['leukosit_iodium'] : null,
            'trombosit_iodium' => isset($post['trombosit_iodium']) ? $post['trombosit_iodium'] : null,
            'hamil_menyusui_iodium' => isset($post['hamil_menyusui_iodium']) ? $post['hamil_menyusui_iodium'] : null,
            'thyrax_iodium' => isset($post['thyrax_iodium']) ? $post['thyrax_iodium'] : null,
            'iodine_iodium' => isset($post['iodine_iodium']) ? $post['iodine_iodium'] : null,

            // Keluar iodium
            'lpr_iodium' => isset($post['lpr_iodium']) ? $post['lpr_iodium'] : null,
            'efek_samping_iodium' => isset($post['efek_samping_iodium']) ? $post['efek_samping_iodium'] : null,
            'meninggal_iodium' => isset($post['meninggal_iodium']) ? $post['meninggal_iodium'] : null,

            // Masuk samarium
            'bone_scan_samarium' => isset($post['bone_scan_samarium']) ? $post['bone_scan_samarium'] : null,
            'ecog_samarium' => isset($post['ecog_samarium']) ? $post['ecog_samarium'] : null,
            'hb_samarium' => isset($post['hb_samarium']) ? $post['hb_samarium'] : null,
            'leukosit_samarium' => isset($post['leukosit_samarium']) ? $post['leukosit_samarium'] : null,
            'trombosit_samarium' => isset($post['trombosit_samarium']) ? $post['trombosit_samarium'] : null,
            'kreatinin_samarium' => isset($post['kreatinin_samarium']) ? $post['kreatinin_samarium'] : null,
            'hamil_menyusui_samarium' => isset($post['hamil_menyusui_samarium']) ? $post['hamil_menyusui_samarium'] : null,

            // Keluar samarium
            'terapi_samarium' => isset($post['terapi_samarium']) ? $post['terapi_samarium'] : null,
            'efek_samping_samarium' => isset($post['efek_samping_samarium']) ? $post['efek_samping_samarium'] : null,
            'meninggal_samarium' => isset($post['meninggal_samarium']) ? $post['meninggal_samarium'] : null,

            'oleh' => $this->session->userdata('id'),
            'status' => '1',
          );

          // echo '<pre>';print_r($data);exit();
          $this->RiraModel->replace($data);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->RiraModel->get($post['id'], true);
        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data,
          )
        );
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/transferRuangan/Rira/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->RiraModel->history($nokun, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $keterangan = null;
    $status = null;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      // Keterangan
      if ($h->pilihan == '4880') {
        $keterangan = 'Iodium-131';
      } elseif ($h->pilihan == '4881') {
        $keterangan = 'Samarium';
      } elseif ($h->pilihan == '4880-4881') {
        $keterangan = 'Iodium-131 dan Samarium';
      }

      // Status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->jam)),
        $keterangan,
        $status,
        $h->pengisi,
        date('d-m-Y, H.i.s', strtotime($h->updated_at)),
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-rira' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-rira' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-rira' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array('status' => 0,);
    $this->RiraModel->ubah($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}