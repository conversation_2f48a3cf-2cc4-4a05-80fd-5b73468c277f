<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianIgdRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel', 'rekam_medis/MedisModel', 'rekam_medis/rawat_inap/igd/PengkajianIgdRiModel'));
  }

  public function index($idLoadNorm, $idLoadNopen, $idLoadNokun, $idLoad)
  {
    $norm = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    $getNomr = $this->PengkajianIgdRiModel->getNomrIgd($nopen);
    $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];

    if($idLoad === "00000"){
    $getPengkajian = $this->PengkajianIgdRiModel->getPengkajian($getIdEmr);
    }else{
    $getPengkajian = $this->PengkajianIgdRiModel->getPengkajian($idLoad);
    }

    $getTandaVital = $this->PengkajianIgdRiModel->getTandaVital($nokun);
    $getTbBb = $this->PengkajianIgdRiModel->getTbBb($nokun);

    // START INDEKS BARTHEL
    $indeksBarthel = $this->PengkajianIgdRiModel->hasilIndeksBarthel($nokun);
    if (isset($indeksBarthel)) {
      $totalSkorIndeksBarthel = $indeksBarthel['rangsang_bab'] + $indeksBarthel['rangsang_berkemih'] + $indeksBarthel['bersihkan_diri'] + $indeksBarthel['penggunaan_kloset'] + $indeksBarthel['makan'] + $indeksBarthel['berubah_posisi'] + $indeksBarthel['berpindah'] + $indeksBarthel['memakai_baju'] + $indeksBarthel['naik_tangga'] + $indeksBarthel['mandi'];

      if ($totalSkorIndeksBarthel == 20) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 4) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 8) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 11) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 19) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
      }
    }

    $jumlahIndeksBarthel = $this->PengkajianIgdRiModel->get_count_indeksBarthel($nokun);
    // END INDEKS BARTHEL

    $data = array(
      'pasien' => $getNomr,
      'getTandaVital' => $getTandaVital,
      'getTbBb' => $getTbBb,
      'nokun' => $nokun,
      'norm' => $norm,
      'nopen' => $nopen,
      'idLoad' => $idLoad,
      'getPengkajian' => $getPengkajian,
      'hasilSkorIndeksBarthel' => isset($hasilSkorIndeksBarthel) ? $hasilSkorIndeksBarthel : null,
      'jumlahIndeksBarthel' => isset($jumlahIndeksBarthel) ? $jumlahIndeksBarthel : null,
      'anamnesis' => $this->masterModel->referensi(54),
      'jenisKunjungan' => $this->masterModel->referensi(149),
      'dibawaDengan' => $this->masterModel->referensi(1111),
      'operasiPali' => $this->masterModel->referensi(370),
      'radiasiPali' => $this->masterModel->referensi(372),
      'masalahKesehatan' => $this->masterModel->formMasalahKesehatan(),
      'terapiPali' => $this->masterModel->referensi(371),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'riwayatTransfusiDarah' => $this->masterModel->referensi(140),
      'riwayatTransfusiDarahDesk' => $this->masterModel->referensi(141),
      'kesadaran' => $this->masterModel->referensi(5),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'statusFungsional' => $this->masterModel->referensi(18),
      'skriningResikoJatuhPusing' => $this->masterModel->referensi(120),
      'skriningResikoJatuhBerdiri' => $this->masterModel->referensi(121),
      'skriningResikoJatuh6Bulan' => $this->masterModel->referensi(122),
      'psikologis' => $this->masterModel->referensi(13),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'sosialDanEkonomiPencariNafkah' => $this->masterModel->referensi(15),
      'sosialDanEkonomiTinggalSerumah' => $this->masterModel->referensi(16),
      'spiritualKultural' => $this->masterModel->referensi(17),
      'pengobatanAlternatif' => $this->masterModel->referensi(146),
      'pengobatanBertentangan' => $this->masterModel->referensi(147),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'apakahInginMasukEws' => $this->masterModel->referensi(1162),
      'PENGGUNAAN_O2' => $this->masterModel->referensi(129),
      'listRangsangbab' => $this->masterModel->referensi(834),
      'listRangsangberkemih' => $this->masterModel->referensi(835),
      'listMembersihkandiri' => $this->masterModel->referensi(836),
      'listPenggunaankloset' => $this->masterModel->referensi(837),
      'listMakan' => $this->masterModel->referensi(838),
      'listBerubahposisi' => $this->masterModel->referensi(839),
      'listBerpindah' => $this->masterModel->referensi(840),
      'listMemakaibaju' => $this->masterModel->referensi(841),
      'listNaiktangga' => $this->masterModel->referensi(842),
      'listMandi' => $this->masterModel->referensi(843),
    );

    $this->load->view('rekam_medis/rawat_inap/igd/pengkajianIgdRi', $data);
  }

  public function masalahKesehatan_edit()
    {
        $id = $this->input->post('id');
        $idemr = $this->input->post('idemr');

        $resultMasalahKesehatan = $this->masterModel->masalahKesehatan($id);
        $resultMasalahKesehatanDetil = $this->masterModel->masalahKesehatanDetil($resultMasalahKesehatan->ID);
        $getPengkajian = $this->PengkajianIgdRiModel->getPengkajian($idemr);

        $data = array(
            'titleMasalahKesehatan' => $resultMasalahKesehatan->KATEGORI,
            'DataMasalahKesehatan' => $resultMasalahKesehatanDetil,
            'getPengkajian' => $getPengkajian,
        );

        $this->load->view('Pengkajian/emr/masalahKesehatan/masalahKesehatan_edit', $data);
    }

  public function simpanIndeksBarthel()
  {
    $nokun = $this->input->post('nokun');
    $total_barthel_1 = $this->input->post('total_barthel_1');
    $total_barthel_2 = $this->input->post('total_barthel_2');
    $total_barthel_3 = $this->input->post('total_barthel_3');
    $total_barthel_4 = $this->input->post('total_barthel_4');
    $total_barthel_5 = $this->input->post('total_barthel_5');
    $total_barthel_6 = $this->input->post('total_barthel_6');
    $total_barthel_7 = $this->input->post('total_barthel_7');
    $total_barthel_8 = $this->input->post('total_barthel_8');
    $total_barthel_9 = $this->input->post('total_barthel_9');
    $total_barthel_10 = $this->input->post('total_barthel_10');
    $oleh = $this->session->userdata("id");

    $totalSkorIndeksBarthel = $total_barthel_1 + $total_barthel_2 + $total_barthel_3 + $total_barthel_4 + $total_barthel_5 + $total_barthel_6 + $total_barthel_7 + $total_barthel_8 + $total_barthel_9 + $total_barthel_10;

    if ($totalSkorIndeksBarthel == 20) {
      echo "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 4) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 8) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 11) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 19) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 9,
      'rangsang_bab' => $total_barthel_1,
      'rangsang_berkemih' => $total_barthel_2,
      'bersihkan_diri' => $total_barthel_3,
      'penggunaan_kloset' => $total_barthel_4,
      'makan' => $total_barthel_5,
      'berubah_posisi' => $total_barthel_6,
      'berpindah' => $total_barthel_7,
      'memakai_baju' => $total_barthel_8,
      'naik_tangga' => $total_barthel_9,
      'mandi' => $total_barthel_10,
      'oleh' => $oleh,
    );

    // echo "<pre>data Indeks Barthel ";print_r($data);echo "</pre>";
    // exit();

    $jumlah = $this->PengkajianIgdRiModel->get_count_indeksBarthel($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_barthel_indek', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->update('keperawatan.tb_barthel_indek', $data);
    }
  }

  public function asuhanKeperawatan_edit()
    {
        $id = $this->input->post('id');
        $idemr = $this->input->post('idemr');

        $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
        $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
        $getPengkajian = $this->PengkajianIgdRiModel->getPengkajian($idemr);

        $data = array(
            'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
            'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
            'getPengkajian' => $getPengkajian,
        );

        $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
    }

  public function simpanPengkajianIgdRi($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();
        // exit();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');
        $masukEwsAtauTidak = $this->input->post('masukKeEwsIgdRi');
        $id_ews = $this->input->post('id_ews');
        $id_tanda_vital = $this->input->post('id_tanda_vital');
        $id_kesadaran = $this->input->post('id_kesadaran');
        $id_o2 = $this->input->post('id_o2');

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 9,
          'rujukan' => $post['rujukanIgdRi'],
          // 'diagnosa_masuk' => $post['rujukanRiD'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataKunjunganigd = array(
          'id_emr' => $getIdEmr,
          'jenis_kunjungan' => isset($post['kunjungan']) ? $post['kunjungan'] : "",
          'rujukan' => isset($post['jenis_kunjungan_desk']) ? $post['jenis_kunjungan_desk'] : "",
          'nama_pengantar' => isset($post['nama_pengantar']) ? $post['nama_pengantar'] : "",
          'dibawa_dengan' => isset($post['dibawa_dengan']) ? $post['dibawa_dengan'] : "",
          'hubungan_pasien' => isset($post['hubungan_pasien']) ? $post['hubungan_pasien'] : "",
        );

        // echo "<pre>data kunjungan igd ";print_r($dataKunjunganigd);echo "</pre>";

        $dataRiwayatKesehatan = array(
          'id_emr' => $getIdEmr,
          'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
          'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
          'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : "",
          'riwayat_transfusi' => isset($post['riwayat_transfusi_darah']) ? $post['riwayat_transfusi_darah'] : "",
          'reaksi_transfusi' => isset($post['riwayat_transfusi_darah_desk']) ? $post['riwayat_transfusi_darah_desk'] : "",
          'isi_reaksi_transfusi' => isset($post['riwayat_td_reaksi_alergi']) ? json_encode($post['riwayat_td_reaksi_alergi']) : "",
          'riwpeng_operasi_hemo' => isset($post['riwPeng_operasi_hemo']) ? $post['riwPeng_operasi_hemo'] : "",
          'pengobatan_operasi_lainnya_hemo' => isset($post['pengobatan_operasi_Lainnya_hemo']) ? $post['pengobatan_operasi_Lainnya_hemo'] : "",
          'riwpeng_radiasi_hemo' => isset($post['riwPeng_radiasi_hemo']) ? $post['riwPeng_radiasi_hemo'] : "",
          'pengobatan_radiasi_lainnya_hemo' => isset($post['pengobatan_radiasi_Lainnya_hemo']) ? $post['pengobatan_radiasi_Lainnya_hemo'] : "",
          'riwpeng_terapi_hemo' => isset($post['riwPeng_terapi_hemo']) ? $post['riwPeng_terapi_hemo'] : "",
          'pengobatan_terapi_lainnya_hemo' => isset($post['pengobatan_terapi_Lainnya_hemo']) ? $post['pengobatan_terapi_Lainnya_hemo'] : "",
          'pengobatan_lainlain_lainnya_hemo' => isset($post['pengobatan_lainlain_Lainnya_hemo']) ? $post['pengobatan_lainlain_Lainnya_hemo'] : "",
        );

        // echo "<pre>data riwayat kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataKesadaran = array(
          'data_source' => 10,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($id_kesadaran)) {
          $this->db->where('tb_kesadaran.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_kesadaran', $dataKesadaran);
        }else{
          $getIdKesadaran = $this->PengkajianIgdRiModel->simpanKesadaran($dataKesadaran);
        }
        
        // echo "<pre>data kesadaran ";print_r($dataKesadaran);echo "</pre>";

        $dataPemeriksaanFisik = array(
          'id_emr' => $getIdEmr,
          'apakah_ingin_masuk_keews' => isset($post['masukKeEwsIgdRi']) ? $post['masukKeEwsIgdRi'] : "",
          'vertigo' => isset($post['skriningresikojatuhpusingIgdRi']) ? $post['skriningresikojatuhpusingIgdRi'] : "",
          'sulit_berdiri' => isset($post['skriningresikojatuhberdiriIgdRi']) ? $post['skriningresikojatuhberdiriIgdRi'] : "",
          'jatuh_dlm_6' => isset($post['skriningresikojatuh6bulanIgdRi']) ? $post['skriningresikojatuh6bulanIgdRi'] : "",
          'psikologis' => isset($post['psikologis']) ? $post['psikologis'] : "",
          'isi_psikologi' => isset($post['psikologis_lainnya']) ? $post['psikologis_lainnya'] : "",
          'hub_keluarga' => isset($post['sdeh']) ? $post['sdeh'] : "",
          'nafkah_utama' => isset($post['sdepn']) ? $post['sdepn'] : "",
          'tinggal' => isset($post['sdets']) ? $post['sdets'] : "",
          'keyakinan' => isset($post['spiritualkultural']) ? $post['spiritualkultural'] : "",
          'sebutkan_keyakinan' => isset($post['spiritualkultural_lainnya']) ? $post['spiritualkultural_lainnya'] : "",
          'pengobatan_alternatif' => isset($post['pengobatan_alternatif']) ? $post['pengobatan_alternatif'] : "",
          'sebutkan_pengobatan_alternatif' => isset($post['pengobatan_alternatif_desk']) ? json_encode($post['pengobatan_alternatif_desk']) : "",
          'pengobatan_bertentangan' => isset($post['pengobatan_bertentangan']) ? $post['pengobatan_bertentangan'] : "",
          'sebutkan_pengobatan_bertentangan' => isset($post['pengobatan_bertentangan_desk']) ? json_encode($post['pengobatan_bertentangan_desk']) : "",
          'masalah_kesehatan_keperawatan' => isset($post['masalahKesehatanKeperawatanDewasaIgdRi']) ? json_encode($post['masalahKesehatanKeperawatanDewasaIgdRi']) : "",
        );

        // echo "<pre>data pemeriksaan fisik ";print_r($dataPemeriksaanFisik);echo "</pre>";

        $dataO2 = array(
          'data_source' => 10,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'saturasi_o2' => isset($post['deskMasukEwsIgdRi']) ? $post['deskMasukEwsIgdRi'] : "",
          'penggunaan_o2' => isset($post['penggunaanO2IgdRi']) ? $post['penggunaanO2IgdRi'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($id_o2)) {
          $this->db->where('tb_o2.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_o2', $dataO2);
        }else{
          $getIdO2 = $this->PengkajianIgdRiModel->simpanO2($dataO2);
        }
        // echo "<pre>data o2 ";print_r($dataO2);echo "</pre>";

        $dataTandaVital = array(
          'data_source' => 10,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($id_tanda_vital)) {
          $this->db->where('tb_tanda_vital.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
        }else{
          $getIdTandaVital = $this->PengkajianIgdRiModel->simpanTandaVital($dataTandaVital);
        }

        // echo "<pre>data Tanda Vital ";print_r($dataTandaVital);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 10,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0 ,
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";

        $dataSkriningNyeri = array(
          'nokun' => $post['nokun'],
          'data_source' => 10,
          'ref' => $getIdEmr,
          'metode' => isset($post['skrining_nyeri']) ? $post['skrining_nyeri'] : "",
          'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'provokative' => isset($post['propocative']) ? $post['propocative'] : "",
          'quality' => isset($post['quality']) ? $post['quality'] : "",
          'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
          'regio' => isset($post['regio']) ? $post['regio'] : "",
          'severity' => isset($post['severity']) ? $post['severity'] : "",
          'time' => isset($post['time']) ? $post['time'] : "",
          'ket_time' => isset($post['durasi_nyeri']) ? $post['durasi_nyeri'] : "",
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining nyeri ";print_r($dataSkriningNyeri);echo "</pre>";

        $pernapasanEws = $this->input->post('pernapasan');
        $nadiEws = $this->input->post('nadi');
        $tekananDarahSistolikEws = $this->input->post('tekanan_darah_1');
        $suhuEws = $this->input->post('suhu');
        $saturasiO2Ews = $this->input->post('deskMasukEwsIgdRi');
        $penggunaanO2Ews = $this->input->post('penggunaanO2IgdRi');
        $kesadaranEws = $this->input->post('kesadaran');
        $masukKeEwsIgdRi = $this->input->post('masukKeEwsIgdRi');

        // KONDISI UNTUK PERNAPASAN EWS
     if ($pernapasanEws >= 25) {
      $hasilPernapasan = 3;
    } elseif ($pernapasanEws >= 21 && $pernapasanEws <= 24.99) {
      $hasilPernapasan = 2;
    } elseif ($pernapasanEws >= 12 && $pernapasanEws <= 20.99) {
      $hasilPernapasan = 0;
    } elseif ($pernapasanEws >= 9 && $pernapasanEws <= 11.99) {
      $hasilPernapasan = 1;
    } elseif ($pernapasanEws <= 8.99) {
      $hasilPernapasan = 3;
    }

    // KONDISI UNTUK DENYUT NADI EWS
    if ($nadiEws >= 130) {
      $nadiEws = 3;
    } elseif ($nadiEws >= 111 && $nadiEws <= 129.99) {
      $nadiEws = 2;
    } elseif ($nadiEws >= 101 && $nadiEws <= 110.99) {
      $nadiEws = 1;
    } elseif ($nadiEws >= 60 && $nadiEws <= 100.99) {
      $nadiEws = 0;
    } elseif ($nadiEws >= 51 && $nadiEws <= 59.99) {
      $nadiEws = 1;
    } elseif ($nadiEws >= 40 && $nadiEws <= 50.99) {
      $nadiEws = 2;
    } elseif ($nadiEws <= 39.99) {
      $nadiEws = 3;
    }

    // KONDISI UNTUK TEKANAN DARAH SISTOLIK
    if ($tekananDarahSistolikEws >= 180) {
      $tekananDarahSistolikEws = 3;
    } elseif ($tekananDarahSistolikEws >= 170 && $tekananDarahSistolikEws <= 179.99) {
      $tekananDarahSistolikEws = 2;
    } elseif ($tekananDarahSistolikEws >= 150 && $tekananDarahSistolikEws <= 169.99) {
      $tekananDarahSistolikEws = 1;
    } elseif ($tekananDarahSistolikEws >= 101 && $tekananDarahSistolikEws <= 149.99) {
      $tekananDarahSistolikEws = 0;
    } elseif ($tekananDarahSistolikEws >= 81 && $tekananDarahSistolikEws <= 100.99) {
      $tekananDarahSistolikEws = 1;
    } elseif ($tekananDarahSistolikEws >= 71 && $tekananDarahSistolikEws <= 80.99) {
      $tekananDarahSistolikEws = 2;
    } elseif ($tekananDarahSistolikEws <= 70.99) {
      $tekananDarahSistolikEws = 3;
    }

    // KONDISI UNTUK SUHU
    if ($suhuEws >= 39) {
      $suhuEws = 2;
    } elseif ($suhuEws >= 38 && $suhuEws <= 38.99) {
      $suhuEws = 1;
    } elseif ($suhuEws >= 36 && $suhuEws <= 37.99) {
      $suhuEws = 0;
    } elseif ($suhuEws <= 35.99) {
      $suhuEws = 3;
    }

    // KONDISI UNTUK SATURASI O2
    if ($saturasiO2Ews >= 96) {
      $saturasiO2Ews = 0;
    } elseif ($saturasiO2Ews >= 94 && $saturasiO2Ews <= 95.99) {
      $saturasiO2Ews = 1;
    } elseif ($saturasiO2Ews >= 92 && $saturasiO2Ews <= 93.99) {
      $saturasiO2Ews = 2;
    } elseif ($saturasiO2Ews <= 91.99) {
      $saturasiO2Ews = 3;
    }

    // KONDISI UNTUK PENGGUNAAN O2
    if ($penggunaanO2Ews == 388) {
      $penggunaanO2Ews = 2;
    } elseif ($penggunaanO2Ews == 389) {
      $penggunaanO2Ews = 0;
    }

    // KONDISI UNTUK TINGKAT KESADARAN
    if ($kesadaranEws == 105) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 12) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 11) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 10) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 9) {
      $kesadaranEws = 0;
    }

        $totalScoreEws = $hasilPernapasan + $nadiEws + $tekananDarahSistolikEws + $suhuEws + $saturasiO2Ews + $penggunaanO2Ews + $kesadaranEws;

        if (!empty($id_ews)) {
          $dataTotalScoreEws = array(
            'id_tanda_vital' => $id_tanda_vital,
            'id_kesadaran' => $id_kesadaran,
            'id_o2' => $id_o2,
            'tanggal' => date("Y-m-d"),
            'jam' => date("H:i:s"),
            'score_ews' => $totalScoreEws,
            'ref' => $getIdEmr,
            'nokun' => $post['nokun'],
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
          );
        }else if(empty($id_ews)){
          if (!empty($post['idemr'])){
            $dataTotalScoreEws = array(
              'id_tanda_vital' => $id_tanda_vital,
              'id_kesadaran' => $id_kesadaran,
              'id_o2' => $id_o2,
              'tanggal' => date("Y-m-d"),
              'jam' => date("H:i:s"),
              'score_ews' => $totalScoreEws,
              'ref' => $getIdEmr,
              'nokun' => $post['nokun'],
              'oleh' => $this->session->userdata('id'),
              'status' => 1,
            );
          }else if(empty($post['idemr'])){
            $dataTotalScoreEws = array(
              'id_tanda_vital' => $getIdTandaVital,
              'id_kesadaran' => $getIdKesadaran,
              'id_o2' => $getIdO2,
              'tanggal' => date("Y-m-d"),
              'jam' => date("H:i:s"),
              'score_ews' => $totalScoreEws,
              'ref' => $getIdEmr,
              'nokun' => $post['nokun'],
              'oleh' => $this->session->userdata('id'),
              'status' => 1,
            );
          }
        }

        // echo "<pre>data data total score ews ";print_r($dataTotalScoreEws);echo "</pre>";
        // exit();
        
        $this->db->trans_begin();
        if (!empty($id_ews)  && $masukEwsAtauTidak == 3863){
          $this->db->where('tb_ews.id', $id_ews);
          $this->db->update('keperawatan.tb_ews', $dataTotalScoreEws);
        }else if(empty($id_ews)  && $masukEwsAtauTidak == 3863){
          $this->db->insert('keperawatan.tb_ews', $dataTotalScoreEws);
        } 

        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->replace('keperawatan.tb_kunjungan_igd', $dataKunjunganigd);
          $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->where('tb_ews.id', $id_ews);
          $this->db->update('keperawatan.tb_ews', $dataTotalScoreEws);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->where('ref', $post['idemr']);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          // $this->db->replace('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->where('ref', $post['idemr']);
          $this->db->update('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          // $this->db->replace('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
          $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $idRefEmr));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan, array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $this->db->delete('keperawatan.tb_masalah_kesehatan', array('id_emr' => $idRefEmr));
          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan, array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->insert('keperawatan.tb_kunjungan_igd', $dataKunjunganigd);
          $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan, array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan, array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      }

      else if($param == 'count') {
        $result = $this->PengkajianIgdRiModel->get_count();
        echo json_encode($result);
      }

      else if($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataPengkajianIgdRi = $this->PengkajianIgdRi->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataPengkajianIgdRi
        ));
      }
    }
  }

  public function datatables(){
        $result = $this->MedisModel->historyPengkajian();

        $data = array();
        foreach ($result as $row){
          // $status_edit_perawat = $row -> STATUS_EDIT_PERAWAT;
          // $status_edit_medis = $row -> STATUS_EDIT_MEDIS;
          $action = "";
          $verif = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
          if($row -> ID_EMR_PERAWAT != null){
            $action .= '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>';
            if($this->session->userdata('status') == 2){
              $action .='<button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
              
              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
            }
          }

          if($row -> ID_EMR_MEDIS != null){
            $action .= '<a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>';
            if($this->session->userdata('status') == 1){
              $action .='<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'"><i class="fa fa-eye"></i> lihat</button>';
              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
            }
          }

          $sub_array = array();
          $sub_array[] = $row -> INFO;
          $sub_array[] = $verif;
          $sub_array[] = $row -> RUANGAN;
          $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
          $sub_array[] = $action;
          $sub_array[] = $row -> DPJP;
          $sub_array[] = $namaVerif;
          $sub_array[] = $tombolCetak;
          $sub_array[] = $row -> USER_MEDIS;
          $sub_array[] = $row -> USER_PERAWAT;   
          $data[] = $sub_array;

            // if($STATUS_EDIT == 0){
            // $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
            // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
            // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> lihat</button>';
            // }else{
            //   $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
            // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
            // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
            // }
        }

        $output = array(
            "draw" => intval($this->input->post("draw")),
            "data"              => $data
        );
        echo json_encode($output);
    }

}