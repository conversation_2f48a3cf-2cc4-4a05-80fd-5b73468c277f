<div class="row">
  <div class="col-sm-12">
    <h4 class="page-title">Form Pengiriman</h4>
  </div>
</div>
<!-- end page title -->
<div class="row">
  <div class="col-12">
    <div class="card-box">

      <form id="orderBarang">
        <div class="table-responsive">
          <table class="table table-bordered" cellspacing="0" width="100%" id="">
            <thead>
              <tr>
                <th width="5%">No</th>
                <th width="40%">Nama Barang</th>
                <th width="10%">Harga</th>
                <th width="">Satuan</th>
                <th width="">Stok Tersedia</th>
                <th width="">Jumlah permintaan</th>
                <th width="">Realisasi</th>
                <th width="15%">Keterangan</th>
              </tr>
            </thead>
            <tbody>
              <?php
              $no = 1; //untuk menampilkan no
              foreach ($record as $row) {
              ?>
                <tr>
                  <td><?= $no ?></td>
                  <input class='form-control' type='hidden' value="<?php echo $row['NOMOR'] ?>" name='NOMOR'>
                  <input class='form-control' type='hidden' value="<?php echo $row['ID_DETAIL'] ?>" name='ID_DETAIL[]'>
                  <input class='form-control' type='hidden' value="<?php echo $row['TUJUAN'] ?>" name='ASAL'>
                  <input class='form-control' type='hidden' value="<?php echo $row['ASAL'] ?>" name='TUJUAN'>
                  <input class='form-control' type='hidden' value="<?php echo $row['ID_BARANG_GUDANG'] ?>" name='BARANG[]'>
                  <td><input class='form-control' type='text' value="<?php echo $row['NAMA'] ?>" name='NAMA_BARANG[]' / readonly></td>
                  <td><input class='form-control' type='text' value="<?php echo $row['HARGA'] ?>" name='HARGA[]' readonly></td>
                  <td><input class='form-control' type='text' value="<?= $row['SATUAN'] ?>" name='' / disabled></td>
                  <td><input class='form-control' type='text' value="<?php echo $row['STOK'] ?>" name='STOK[]' id="stok" / readonly></td>
                  <td><input class='form-control' type='text' value="<?= $row['JUMLAH'] ?>" name='MINTA[]' / readonly></td>
                  <td><input class='form-control' type='number' name='JUMLAH[]' id="jumlah" / required></td>
                  <td><input class='form-control' type='text' name='KETERANGAN[]' autocomplete="off" id="total"></td>
                </tr>
              <?php
                $no++;
              }
              ?>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="2">
                  <select class="itemname form-control" id="itemname" name="PENERIMA" require>
                  </select>
                </td>
                <td>
                  <label>Tanggal Order</label>
                </td>
                <td colspan="4">
                  <div class="input-group">
                    <input type="text" class="form-control" name="TANGGAL_KIRIM" id="tanggal_kirim" placeholder="Tanggal order" id="tanggal" autocomplete="off" required>
                    <div class="input-group-append">
                      <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                    </div>
                  </div><!-- input-group -->
                </td>
                <!-- <td colspan="2"><input class='form-control' type='text' placeholder="KETERANGAN" name='KETERANGAN'></td> -->
                <td colspan="5"><button type="submit" name="submit" value="submit" class="btn btn-primary"><i class="fas fa-save"></i> Simpan</button></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </form>
    </div><!-- end col -->
  </div>
</div>

<div class="modal fade" id="konfirmasibarangkirim" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title mt-0">Detil barang akan dikirim</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <div class="alert alert-warning">
            <strong>Perhatian..! </strong> Pastikan jumlah yang dikirim sudah benar, tekan ya untuk mengirim barang.
          </div>
        </div>
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Nama Barang</th>
              <th>Stok tersedia</th>
              <th>Permintaan</th>
              <th>Jumlah Kirim</th>
              <th>Keterangan</th>
            </tr>
          </thead>
          <tbody id="listBarangKirim">
          </tbody>
        </table>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-danger" data-dismiss="modal" aria-hidden="true">Tidak</button>
        <button type="button" class="btn btn-sm btn-success" id="kirimOrder">Ya</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  $('.itemname').select2({
    placeholder: "[ Masukkan nama pegawai yang menerima ]",
    ajax: {
      url: '<?php echo base_url() ?>inventory/Pengiriman/getDataPegawai',
      dataType: "json",
      delay: 250,
      data: function(params) {
        return {
          peg: params.term
        };
      },
      processResults: function(data) {
        var results = [];
        $.each(data, function(index, item) {
          results.push({
            id: item.NIP,
            text: item.NAMA
          });
        });
        return {
          results: results
        };
      }
    }
  });

  $(document).ready(function() {
    jQuery('#tanggal_kirim').datepicker({
      autoclose: true,
      todayHighlight: true,
      dateFormat: 'yy-mm-dd',
    });
  });
</script>

<script type="text/javascript">
  $(document).ready(function() {
    $("#orderBarang").submit(function(event) {
      event.preventDefault();
      var data = $("#orderBarang").serializeArray();
      $.ajax({
        dataType: 'json',
        url: "<?php echo base_url('inventory/pengiriman/action/view') ?>",
        method: "POST",
        data: data,
        success: function(data) {
          $('#konfirmasibarangkirim').modal('show');
          var html = "";
          $.each(data.dataDetailBarang, function(index, element) {
            html += '<tr>' +
              '<td>' + element.NAMA_BARANG + '</td>' +
              '<td>' + element.STOK + '</td>' +
              '<td>' + element.MINTA + '</td>' +
              '<td>' + element.JUMLAH + '</td>' +
              '<td>' + element.NILAI + '</td>' +
            '</tr>';
          });

          $("#listBarangKirim").html(html);
        }
      });
    });

    $("#kirimOrder").on('click', function(event) {
      event.preventDefault();
      var data = $("#orderBarang").serializeArray();
      alertify.confirm('Konfirmasi', 'Pilih Ok, jika setuju disimpan',
        function() {
          $.ajax({
            dataType: 'json',
            url: "<?php echo base_url('inventory/pengiriman/tambah') ?>",
            method: "POST",
            data: data,
            success: function(data) {
              if (data.status == 'success') {
                alertify.success('Barang Berhasil Dikirim');
                window.location.href = "<?php echo base_url('inventory/pengiriman') ?>";
              } else {
                alertify.warning('Internal Server Error');
              }
            }
          });
        },
        function() {
          alertify.error('Barang Batal Dikirim')
        });
    });

  });
</script>