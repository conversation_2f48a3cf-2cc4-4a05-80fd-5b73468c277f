<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Flosheet extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/flosheet/FlosheetModel', 'rekam_medis/KesadaranModel'));
    }

    public function index() {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
        $nokun = $this->uri->segment(2);
        $data = array(
            'pasien'            => $pasien,
            'riwayatAlergi'     => $this->masterModel->referensi(774),
            'listDrPelaksana' => $this->masterModel->listDrPelaksana(),
            'dataFlosheet' => $this->FlosheetModel->dataAkhirFlosheet($nokun),
            'pilihFlosheet' => $this->FlosheetModel->pilihFlosheet($pasien['NORM']),

        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/index',$data);
    }

    public function pilihFlosheet()
    {
        $idflosheet = $this->input->post('idflosheet');
        $getFlosheet = $this->FlosheetModel->getFlosheet($idflosheet);
        $pasien = $this->pengkajianAwalModel->getNomr($getFlosheet['NOKUN']);
        $data = array(
            'getFlosheet'            => $getFlosheet,
            'pasien'            => $pasien,
            'riwayatAlergi'     => $this->masterModel->referensi(774),
            'listDrPelaksana' => $this->masterModel->listDrPelaksana(),
        );

        $this->load->view('rekam_medis/rawat_inap/flosheet/flosheet',$data);
    }

    public function simpanFlosheet()
    {
        $nomr = $this->input->post('nomr');
        $nokun = $this->input->post('nokun');
        $oleh = $this->input->post('oleh');
        $idFlosheet = $this->input->post('idFlosheet');
        $date = $this->input->post('tglDnJamKeFlosheetRi');
        $hariPostOpKeFlosheetRi = $this->input->post('hariPostOpKeFlosheetRi');
        $hariPerawatanIceKeFlosheetRi = $this->input->post('hariPerawatanIceKeFlosheetRi');
        $riwayatAlergiFlosheetRi = $this->input->post('riwayatAlergiFlosheetRi');
        $deskRiwayatAlergiFlosheetRi = $this->input->post('deskRiwayatAlergiFlosheetRi');
        // $lingkatPerutFlosheetRi = $this->input->post('lingkatPerutFlosheetRi');
        $diagnosaFlosheetRi = $this->input->post('diagnosaFlosheetRi');
        $tindakanOperasiFlosheetRi = $this->input->post('tindakanOperasiFlosheetRi');
        $golDarahFlosheetRi = $this->input->post('golDarahFlosheetRi');
        // $noTempatTidurFlosheetRi = $this->input->post('noTempatTidurFlosheetRi');
        $drPrimerFlosheetRi = $this->input->post('drPrimerFlosheetRi');
        $drKonsultanFlosheetRi = $this->input->post('drKonsultanFlosheetRi');
        // $drAnestesiFlosheetRi = $this->input->post('drAnestesiFlosheetRi');
        $tinggi_badan = $this->input->post('tinggi_badan');
        $berat_badan = $this->input->post('berat_badan');

        $tglDnJamKeFlosheetRi = date('Y-m-d H:i:s', strtotime($date));

        $data = array(
            'nomr'            => $nomr,
            'nokun'            => $nokun,
            'tgl_jam'            => $tglDnJamKeFlosheetRi,
            'hari_post'            => $hariPostOpKeFlosheetRi,
            'hari_perawat'            => $hariPerawatanIceKeFlosheetRi,
            'alergi'            => $riwayatAlergiFlosheetRi,
            'desk_alergi'            => $deskRiwayatAlergiFlosheetRi,
            // 'lingkar_perut'            => $lingkatPerutFlosheetRi,
            'diagnosa'            => $diagnosaFlosheetRi,
            'tindakan_operasi'            => $tindakanOperasiFlosheetRi,
            'golongan_darah'            => $golDarahFlosheetRi,
            // 'no_tempat_tidur'            => $noTempatTidurFlosheetRi,
            'dr_primer'            => isset($drPrimerFlosheetRi) ? $drPrimerFlosheetRi : "",
            'dr_konsultan'            => isset($drKonsultanFlosheetRi) ? $drKonsultanFlosheetRi : "",
            // 'dr_anestesi'            => $drAnestesiFlosheetRi,
            'oleh'            => $oleh,
            'status'            => 1,
        );
        // echo "<pre>";print_r($data);echo "</pre>";exit();
        if(!empty($idFlosheet))
        {
            $this->db->where('tb_flosheet.id', $idFlosheet);
            $this->db->update('keperawatan.tb_flosheet', $data);

            $dataTbBb = array(
              'data_source' => 28,
              'ref' => $idFlosheet,
              'nomr' => $nomr,
              'nokun' => $nokun,
              'jenis' => "0",
              'tb' => $tinggi_badan,
              'bb' => $berat_badan,
              'oleh' => $oleh,
              'status' => 1,
          );
            $this->db->where('tb_tb_bb.ref', $idFlosheet);
            $this->db->where('tb_tb_bb.data_source', 28);
            $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
        }else{
            $getIdFlosheet = $this->FlosheetModel->simpanFlosheet($data);
            $dataTbBb = array(
              'data_source' => 28,
              'ref' => $getIdFlosheet,
              'nomr' => $nomr,
              'nokun' => $nokun,
              'jenis' => "0",
              'tb' => $tinggi_badan,
              'bb' => $berat_badan,
              'oleh' => $oleh,
              'status' => 1,
          );
            $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
        }
        
    }

    public function ssp()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $tableSSP = $this->FlosheetModel->tableSSP($this->input->post('idflosheet'));
        $data = array(
            'pasien'            => $pasien,
            'idFlosheet'        => $this->input->post('idflosheet'),
            'kesadaran'         => $this->masterModel->referensi(5),
            'bukaMata'          => $this->masterModel->referensi(1565),
            'responsMotorik'    => $this->masterModel->referensi(1566),
            'responsVerbal'     => $this->masterModel->referensi(1567),
            'reaksiPupil'       => $this->masterModel->referensi(1568),
            'besarPupil'        => $this->masterModel->referensi(1569),
            'extremitas'        => $this->masterModel->referensi(1584),
            'responsMata'       => $this->masterModel->referensi(1645),
            'responsMotorikFS'  => $this->masterModel->referensi(1646),
            'reflekBatangOtak'  => $this->masterModel->referensi(1647),
            'respirasi'         => $this->masterModel->referensi(1648),

            'tableSSP'          => $tableSSP,
        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/ssp',$data);
    }

    public function ventilasi()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $tableVentilasi = $this->FlosheetModel->tableVentilasi($idFlosheet);
        $data = array(
            'pasien'            => $pasien,
            'idFlosheet'        => $idFlosheet,
            'tableVentilasi'        => $tableVentilasi,
            'tipeVentilasi'        => $this->masterModel->referensi(1640),
            'tipeVentilasiSpontan'        => $this->masterModel->referensi(1641),
            'tipeVentilasiMekanik'        => $this->masterModel->referensi(1642),
            'tekananP'        => $this->masterModel->referensi(1643),
        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/ventilasi', $data);
    }

    public function tandaVital()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $HistoryTandaVital = $this->FlosheetModel->HistoryTandaVital($idFlosheet);
        $HistoryTandaVitalSuhu = $this->FlosheetModel->HistoryTandaVitalSuhu($idFlosheet);
        $hasilInputTandaVital = $this->FlosheetModel->hasilInputTandaVital($idFlosheet);

        $waktu = array();
        $waktuSuhu = array();
        $napas = array();
        $nadi = array();
        $sistolik = array();
        $diastolik = array();
        $map = array();
        $suhu = array();
        foreach ($HistoryTandaVital as $HistoryTandaVital){
            array_push($waktu,$HistoryTandaVital['TANGGAL_DAN_JAM_NEW']);
            array_push($napas,$HistoryTandaVital['pernapasan']);
            array_push($nadi,$HistoryTandaVital['nadi']);
            array_push($sistolik,$HistoryTandaVital['td_sistolik']);
            array_push($diastolik,$HistoryTandaVital['td_diastolik']);
            array_push($map,$HistoryTandaVital['map']);
        }

        foreach ($HistoryTandaVitalSuhu as $HistoryTandaVitalSuhu){
            array_push($waktuSuhu,$HistoryTandaVitalSuhu['TANGGAL_DAN_JAM_NEW']);
            array_push($suhu,$HistoryTandaVitalSuhu['suhu']);
        }

        $data = array(
            'pasien'                            => $pasien,
            'idFlosheet'                        => $idFlosheet,
            'waktu'                             => $waktu,
            'waktuSuhu'                         => $waktuSuhu,
            'napas'                             => $napas,
            'nadi'                              => $nadi,
            'sistolik'                          => $sistolik,
            'diastolik'                         => $diastolik,
            'map'                               => $map,
            'suhu'                              => $suhu,
            'hasilInputTandaVital'              => $hasilInputTandaVital,
        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/tandaVital', $data);
    }

    public function ekgCvpSato2()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $tableEkgCvpSato2 = $this->FlosheetModel->tableEkgCvpSato2($idFlosheet);
        $data = array(
            'pasien'                            => $pasien,
            'idFlosheet'                        => $idFlosheet,
            'tableEkgCvpSato2'                  => $tableEkgCvpSato2,
        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/ekgCvpSato2', $data);
    }

    public function skriningNyeri()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $HistorySkriningNyeri = $this->FlosheetModel->HistorySkriningNyeri($idFlosheet);
        $HistoryInputSkriningNyeri = $this->FlosheetModel->HistoryInputSkriningNyeri($idFlosheet);

        $waktu = array();
        $score = array();
        foreach ($HistorySkriningNyeri as $HistorySkriningNyeri){
            array_push($waktu,$HistorySkriningNyeri['TANGGAL_DAN_JAM_NEW']);
            array_push($score,$HistorySkriningNyeri['VAR_SKOR']);
        }

        $data = array(
            'pasien'                            => $pasien,
            'idFlosheet'                        => $idFlosheet,
            'skriningNyeri'                     => $this->masterModel->referensi(7),
            'skalaNyeriNRS'                     => $this->masterModel->referensi(114),
            'skalaNyeriWBR'                     => $this->masterModel->referensi(115),
            'skalaNyeriFLACC'                   => $this->masterModel->referensi(123),
            'skalaNyeriBPS'                     => $this->masterModel->referensi(133),
            'special1BPS'                       => $this->masterModel->referensi(1760),
            'special2BPS'                       => $this->masterModel->referensi(1761),
            'special3BPS'                       => $this->masterModel->referensi(1762),
            'special1FLACC'                     => $this->masterModel->referensi(1763),
            'special2FLACC'                     => $this->masterModel->referensi(1764),
            'special3FLACC'                     => $this->masterModel->referensi(1765),
            'special4FLACC'                     => $this->masterModel->referensi(1766),
            'efeksampingNRS'                    => $this->masterModel->referensi(118),
            'pengkajianNyeriProvocative'        => $this->masterModel->referensi(8),
            'pengkajianNyeriQuality'            => $this->masterModel->referensi(9),
            'pengkajianNyeriTime'               => $this->masterModel->referensi(12),
            'waktu'                             => $waktu,
            'score'                             => $score,
            'HistoryInputSkriningNyeri'         => $HistoryInputSkriningNyeri,
        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/skriningNyeri', $data);
    }

    public function pemasukan()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $tablePemasukan = $this->FlosheetModel->tablePemasukan($this->input->post('idflosheet'));
        $infus = $this->FlosheetModel->getIsValueInfus($this->input->post('nokun'));
        $darah = $this->FlosheetModel->getDarah($this->input->post('nokun'));
        $pemasukanInfus = $this->FlosheetModel->getPemasukanInfus($this->input->post('idflosheet'));
        $pemasukanDarah = $this->FlosheetModel->getPemasukanDarah($this->input->post('idflosheet'));
        $kumulatifInfus = $this->FlosheetModel->getKumulatifInfus($this->input->post('idflosheet'));
        $kumulatifMasuk = $this->FlosheetModel->getKumulatifMasuk($this->input->post('idflosheet'));
        $kumulatifOral = $this->FlosheetModel->getKumulatifOral($this->input->post('idflosheet'));
        $kumulatif = $this->FlosheetModel->getKumulatif($this->input->post('idflosheet'));
        $kumulatif3Jam = $this->FlosheetModel->getKumulatif3Jam($this->input->post('idflosheet'));

        $data = array(
            'pasien'            => $pasien,
            'idFlosheet'        => $this->input->post('idflosheet'),
            'masuk'             => $this->masterModel->referensi(1609),
            'tablePemasukan'    => $tablePemasukan,
            'getInfus'          => $infus,
            'getDarah'          => $darah,
            'getPemasukanInfus' => $pemasukanInfus,
            'getPemasukanDarah' => $pemasukanDarah,
            'getKumulatifInfus' => $kumulatifInfus,
            'getKumulatifMasuk' => $kumulatifMasuk,
            'getKumulatifOral'  => $kumulatifOral,
            'getKumulatif'      => $kumulatif,
            'getKumulatif3Jam'  => $kumulatif3Jam,
        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/pemasukan',$data);
    }

    public function masalah()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $tableMasalah = $this->FlosheetModel->tableMasalah($idFlosheet);

        $data = array(
            'pasien' => $pasien,
            'idFlosheet' => $idFlosheet,
            'tableMasalah' => $tableMasalah,
            'listDrUmum' => $this->masterModel->listDrUmum()
        );

        $this->load->view('rekam_medis/rawat_inap/flosheet/masalah', $data);
    }

    public function masalahDetail()
    {
        $id_masalah = $this->input->post('id');
        $idFlosheet = $this->input->post('idflosheet');
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $getDetailMasalah = $this->FlosheetModel->getDetailMasalah($id_masalah);

        $data = array(
            'id_masalah' => $id_masalah,
            'getDetail' => $getDetailMasalah,
            'pasien' => $pasien,
            'idFlosheet' => $idFlosheet,
            'listDrUmum' => $this->masterModel->listDrUmum()
        );

        $this->load->view('rekam_medis/rawat_inap/flosheet/masalahDetail', $data);
    }

    public function ITK()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $tableITK = $this->FlosheetModel->tableITK($idFlosheet);

        $data = array(
            'pasien' => $pasien,
            'idFlosheet' => $idFlosheet,
            'tableITK' => $tableITK,
            'listPerawat' => $this->masterModel->listPerawat(),
            'listShift' => $this->masterModel->referensi(1611),
            'listCatatanPerawatan' => $this->masterModel->listTindakanKeperawatan()
        );

        $this->load->view('rekam_medis/rawat_inap/flosheet/itk', $data);
    }

    public function ITKDetail()
    {
        $id_itk = $this->input->post('id');
        $idFlosheet = $this->input->post('idflosheet');
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $getDetailITK = $this->FlosheetModel->getDetailITK($id_itk);

        $data = array(
            'id_itk' => $id_itk,
            'getDetail' => $getDetailITK,
            'pasien' => $pasien,
            'idFlosheet' => $idFlosheet,
            'listPerawat' => $this->masterModel->listPerawat(),
            'listShift' => $this->masterModel->referensi(1611),
            'listCatatanPerawatan' => $this->masterModel->listTindakanKeperawatan()
        );

        $this->load->view('rekam_medis/rawat_inap/flosheet/ITKDetail',$data);
    }

    public function pengeluaran()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $tablePengeluaran = $this->FlosheetModel->tablePengeluaran($idFlosheet);
        $tablePengeluaranDrain = $this->FlosheetModel->tablePengeluaranDrain($idFlosheet);
        $getLastPengeluaran = $this->FlosheetModel->getLastPengeluaran($idFlosheet);
        $totalPengeluaran = $this->FlosheetModel->totalPengeluaran($idFlosheet);
        $totalUrine = $this->FlosheetModel->totalUrine($idFlosheet);
        $getDrain = $this->FlosheetModel->getDrain($idFlosheet);
        $getTotalDrain = $this->FlosheetModel->getTotalDrain($idFlosheet);

        // echo "<pre>".print_r($pasien)."</pre>";
        $data = array(
            'pasien' => $pasien,
            'idFlosheet' => $idFlosheet,
            'getLastPengeluaran' => $getLastPengeluaran,
            'tablePengeluaran' => $tablePengeluaran,
            'tablePengeluaranDrain' => $tablePengeluaranDrain,
            'totalPengeluaran' => $totalPengeluaran,
            'listPerawat' => $this->masterModel->listPerawat(),
            'listShift' => $this->masterModel->referensi(1611),
            'listBAB' => $this->masterModel->referensi(1639),
            'listCatatanPerawatan' => $this->masterModel->listTindakanKeperawatan(),
            'getDrain' => $getDrain,
            'getTotalDrain' => $getTotalDrain,
            'totalUrine' => $totalUrine
        );

        $this->load->view('rekam_medis/rawat_inap/flosheet/pengeluaran', $data);

    }

    public function laboratorium()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $tableLaboratorium = $this->FlosheetModel->tableLaboratorium($this->input->post('nokun'));
        $pemeriksaanLab = $this->FlosheetModel->getPemeriksaanLab($this->input->post('nokun'));
        $hasilLab = $this->FlosheetModel->getHasilLab($this->input->post('nokun'));

        $data = array(
            'pasien'            => $pasien,
            'idFlosheet'        => $this->input->post('idflosheet'),
            'tableLaboratorium' => $tableLaboratorium,
            'getPemeriksaanLab'    => $pemeriksaanLab,
            'getHasilLab'          => $hasilLab,
        );
        $this->load->view('rekam_medis/rawat_inap/flosheet/laboratorium',$data);
    }

//START PENGKAJIAN FISIK
    public function pengkajianFisik()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->input->post('nokun'));
        $idFlosheet = $this->input->post('idflosheet');
        $tablePF = $this->FlosheetModel->tablepengkajianFisik($idFlosheet);
        $HM = $this->FlosheetModel->gambarFisik();
        $history = $this->FlosheetModel->historyPengkajianFisik($idFlosheet);

        $data = array(
            'pasien' => $pasien,
            'idFlosheet' => $idFlosheet,
            'tablepengkajianFisik' => $tablePF,
            'HM' => $HM
        );

        $this->load->view('rekam_medis/rawat_inap/flosheet/pengkajianFisik', $data);
    }

    public function tampilGambar()
    {
        $idGambar = $this->input->post('idGambar');

        // echo "<pre>";print_r($nokun);exit();
        $fisik = $this->FlosheetModel->getGambar($idGambar);

        $tes = base_url("assets/admin/assets/images/soap/" . $fisik['file']);

        echo "<div class='col-lg-12 col-md-12'><div class='form-group'>
    <canvas id='my_canvasFisik' width='434' height='434' style='background:url($tes) no-repeat; margin-top:30px;'></canvas>
    <input type='hidden' name='img_val' id='img_val' value='' />
    </div>";
        // echo"<pre>";print_r($fisik);exit();
    }

     public function historyPengkajianFisik()
    {
        $idFlosheet = $this->uri->segment(5);
        // $idFlosheet = $this->input->post('idflosheet');
        $data = $this->FlosheetModel->historyPengkajianFisik($idFlosheet);
        echo json_encode($data);
        // echo "<pre>";print_r($data);
    }

    public function keteranganPengkajianFisik()
    {
        $id = $this->input->post('id');
        $hasilFotoPengkajianFisik = $this->FlosheetModel->hasilFotoPengkajianFisik($id);
        echo '
        <div class="row">
        <div class="col-lg-12">
        <div class="form-group">
        <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . date("d-m-Y H:i:s", strtotime($hasilFotoPengkajianFisik['created_at'])) . '</span> ]</h4>
        </div>
        </div>
        </div>
        <div class="row">
        <div class="col-lg-12">
        <div class="form-group">
        <label for="judulLokalis">Judul</label>
        <input type="text" name="judulFisik" class="form-control" placeholder="[ Judul SOAP ]" value="' . $hasilFotoPengkajianFisik['judul'] . '" readonly>
        </div>
        </div>
        </div>
        <div class="row">
        <div class="col-lg-6 col-lg-6">
        <div class="form-group">
        <label>Hasil Foto</label><br>
        <img src="data:image;base64,' . base64_encode($hasilFotoPengkajianFisik['gambar']) . '" >
        </div>
        </div>
        <div class="col-lg-6">
        <div class="form-group">
        <label for="catatan">Catatan</label>
        <textarea class="form-control" cols="15" rows="10" placeholder="[Catatan ]" name="catatanFisik" readonly>' . $hasilFotoPengkajianFisik['catatan'] . '</textarea>
        </div>
        </div>
        </div>';
    }

//END PENGKAJIAN FISIK

    public function action_ekgCvpSato2($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah' || $param == 'ubah'){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataEkgCvpSato2 = array(
                        'id_flosheet'               => $this->input->post('id_flosheet'),
                        'nomr'                      => $this->input->post('nomr'),
                        'nokun'                     => $this->input->post('nokun'),
                        'tanggal_dan_jam'           => date('Y-m-d H:i:s', strtotime($post['pukulEkgCvpSato2FlosheetRi'])),
                        'pukul'                     => date('H:i:s', strtotime($post['pukulEkgCvpSato2FlosheetRi'])),
                        'ekg_nadi'                  => $this->input->post('ekgNadiFlosheetRi'),
                        'cvp'                       => $this->input->post('cvpFlosheetRi'),
                        'oleh'                      => $this->input->post('oleh'),
                        'status'                    => 1,
                    );

                    $getEkgCvpSato2 = $this->FlosheetModel->simpanEkgCvpSato2($dataEkgCvpSato2);

                    $dataO2 = array(
                      'data_source' => 28,
                      'ref' => $getEkgCvpSato2,
                      'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
                      'nokun' => $post['nokun'],
                      'saturasi_o2' => isset($post['saturasiFlosheetRi']) ? $post['saturasiFlosheetRi'] : "",
                      'oleh' => $this->session->userdata('id'),
                      'status' => 1,
                  );

                    $getdataO2 = $this->FlosheetModel->simpanO2($dataO2);

                    $this->db->where('tb_flosheet_ekgcvpsato2.id', $getEkgCvpSato2);
                    $this->db->update('keperawatan.tb_flosheet_ekgcvpsato2', array('id_sato2' => $getdataO2));
                 
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();
                $post = $this->input->post();

                $this->db->where('tb_flosheet_ekgcvpsato2.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_ekgcvpsato2', array('status' => 0));

                $this->db->where('tb_o2.data_source', 28);
                $this->db->where('tb_o2.ref', $post['id']);
                $this->db->update('db_pasien.tb_o2', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
        }
    }

    public function action_tandaVital($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah' || $param == 'ubah'){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataTandaVitalFlosheet = array(
                      'id_flosheet' => isset($post['id_flosheet']) ? $post['id_flosheet'] : "",
                      // 'id_tanda_vital' => $getIdTandaVital,
                      'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
                      'nokun' => $post['nokun'],
                      'tanggal_dan_jam' => date('Y-m-d H:i:s', strtotime($post['pukulTandaVitalFlosheetRi'])),
                      'pukul' => date('H:i:s', strtotime($post['pukulTandaVitalFlosheetRi'])),
                      'td_sistolik' => isset($post['sistolikFlosheetRi']) ? $post['sistolikFlosheetRi'] : "",
                      'td_diastolik' => isset($post['diastolikFlosheetRi']) ? $post['diastolikFlosheetRi'] : "",
                      'map' => isset($post['mapFlosheetRi']) ? $post['mapFlosheetRi'] : "",
                      'nadi' => isset($post['nadiFlosheetRi']) ? $post['nadiFlosheetRi'] : "",
                      'suhu' => isset($post['suhuFlosheetRi']) ? $post['suhuFlosheetRi'] : "",
                      'pernapasan' => isset($post['pernapasanFlosheetRi']) ? $post['pernapasanFlosheetRi'] : "",
                      'oleh' => $this->session->userdata('id'),
                      'status' => 1,
                  );

                    $getTandaVitalFlosheet = $this->FlosheetModel->simpanTandaVitalFlosheet($dataTandaVitalFlosheet);

                    $dataTandaVital = array(
                      'data_source' => 28,
                      'ref' => $getTandaVitalFlosheet,
                      'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
                      'nokun' => $post['nokun'],
                      'td_sistolik' => isset($post['sistolikFlosheetRi']) ? $post['sistolikFlosheetRi'] : "",
                      'td_diastolik' => isset($post['diastolikFlosheetRi']) ? $post['diastolikFlosheetRi'] : "",
                      'nadi' => isset($post['nadiFlosheetRi']) ? $post['nadiFlosheetRi'] : "",
                      'suhu' => isset($post['suhuFlosheetRi']) ? $post['suhuFlosheetRi'] : "",
                      'pernapasan' => isset($post['pernapasanFlosheetRi']) ? $post['pernapasanFlosheetRi'] : "",
                      'oleh' => $this->session->userdata('id'),
                      'status' => 1,
                  );

                    $getIdTandaVital = $this->FlosheetModel->simpanTandaVital($dataTandaVital);

                    $this->db->where('tb_flosheet_tandavital.id', $getTandaVitalFlosheet);
                    $this->db->update('keperawatan.tb_flosheet_tandavital', array('id_tanda_vital' => $getIdTandaVital));
                 
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();
                $post = $this->input->post();

                $this->db->where('tb_flosheet_tandavital.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_tandavital', array('status' => 0));

                $this->db->where('tb_tanda_vital.data_source', 28);
                $this->db->where('tb_tanda_vital.ref', $post['id']);
                $this->db->update('db_pasien.tb_tanda_vital', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
        }
    }

    public function action_skriningNyeri($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah' || $param == 'ubah'){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataSkriningNyeriFlosheet = array(
                      'id_flosheet' => isset($post['id_flosheet']) ? $post['id_flosheet'] : "",
                      'nokun' => $post['nokun'],
                      'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
                      'tanggal_dan_jam' => date('Y-m-d H:i:s', strtotime($post['pukulSkriningNyeriFlosheetRi'])),
                      'pukul' => date('H:i:s', strtotime($post['pukulSkriningNyeriFlosheetRi'])),
                      'metode' => isset($post['skrining_nyeri_Flosheet']) ? $post['skrining_nyeri_Flosheet'] : "",
                      'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
                      'provokative' => isset($post['propocative_Flosheet']) ? $post['propocative_Flosheet'] : "",
                      'quality' => isset($post['quality_Flosheet']) ? $post['quality_Flosheet'] : "",
                      'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
                      'regio' => isset($post['regio_Flosheet']) ? $post['regio_Flosheet'] : "",
                      'severity' => isset($post['severity_Flosheet']) ? $post['severity_Flosheet'] : "",
                      'time' => isset($post['time_Flosheet']) ? $post['time_Flosheet'] : "",
                      'ket_time' => isset($post['durasi_nyeri_Flosheet']) ? $post['durasi_nyeri_Flosheet'] : "",
                      'status' => 1,
                      'oleh' => $this->session->userdata('id'),
                  );

                    $getSkriningNyeriFlosheet = $this->FlosheetModel->simpanSkriningNyeriFlosheet($dataSkriningNyeriFlosheet);

                    $dataSkriningNyeri = array(
                      'nokun' => $post['nokun'],
                      'data_source' => 28,
                      'ref' => $getSkriningNyeriFlosheet,
                      'metode' => isset($post['skrining_nyeri_Flosheet']) ? $post['skrining_nyeri_Flosheet'] : "",
                      'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
                      'provokative' => isset($post['propocative_Flosheet']) ? $post['propocative_Flosheet'] : "",
                      'quality' => isset($post['quality_Flosheet']) ? $post['quality_Flosheet'] : "",
                      'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
                      'regio' => isset($post['regio_Flosheet']) ? $post['regio_Flosheet'] : "",
                      'severity' => isset($post['severity_Flosheet']) ? $post['severity_Flosheet'] : "",
                      'time' => isset($post['time_Flosheet']) ? $post['time_Flosheet'] : "",
                      'ket_time' => isset($post['durasi_nyeri_Flosheet']) ? $post['durasi_nyeri_Flosheet'] : "",
                      'status' => 1,
                      'created_by' => $this->session->userdata('id'),
                  );

                    $getIdSkriningNyeri = $this->FlosheetModel->simpanSkriningNyeri($dataSkriningNyeri);

                    $this->db->where('tb_flosheet_skriningnyeri.id', $getSkriningNyeriFlosheet);
                    $this->db->update('keperawatan.tb_flosheet_skriningnyeri', array('id_skrining_nyeri' => $getIdSkriningNyeri));

                    $dataSkriningNyeriSpecial = array(
                      'id_flosheet' => isset($post['id_flosheet']) ? $post['id_flosheet'] : "",
                      'id_skrining_nyeri' => $getIdSkriningNyeri,
                      'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
                      'nokun' => $post['nokun'],
                      'face_wajah' => isset($post['special1FLACC']) ? $post['special1FLACC'] : "",
                      'leg_kaki' => isset($post['special2FLACC']) ? $post['special2FLACC'] : "",
                      'activity_aktivitas' => isset($post['special3FLACC']) ? $post['special3FLACC'] : "",
                      'cry_menangis' => isset($post['special4FLACC']) ? $post['special4FLACC'] : "",
                      'ekspresi_wajah' => isset($post['special1BPS']) ? $post['special1BPS'] : "",
                      'pergerakan' => isset($post['special2BPS']) ? $post['special2BPS'] : "",
                      'toleransi_terhadap_ventilator_mekanik' => isset($post['special3BPS']) ? $post['special3BPS'] : "",
                      'oleh' => $this->session->userdata('id'),
                      'status' => 1,
                  );

                    $this->db->insert('keperawatan.tb_flosheet_skriningnyeri_special', $dataSkriningNyeriSpecial);
                 
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();
                $post = $this->input->post();

                $this->db->where('tb_flosheet_skriningnyeri.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_skriningnyeri', array('status' => 0));

                $this->db->where('tb_skrining_nyeri.data_source', 28);
                $this->db->where('tb_skrining_nyeri.ref', $post['id']);
                $this->db->update('keperawatan.tb_skrining_nyeri', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
        }
    }

    public function action_ventilasi($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah' || $param == 'ubah'){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $data = array(
                        'id_flosheet'            => $this->input->post('id_flosheet'),
                        'nomr'            => $this->input->post('nomr'),
                        'nokun'            => $this->input->post('nokun'),
                        'tanggal_dan_jam'            => date('Y-m-d H:i:s', strtotime($post['pukulVentilasiRi'])),
                        'pukul'            => date('H:i:s', strtotime($post['pukulVentilasiRi'])),
                        'tipe_ventilasi'            => $this->input->post('tipeVentilasiRi'),
                        'tipe_ventilasi_spontan'            => $this->input->post('tipeVentilasiRiSpontan'),
                        'tipe_ventilasi_mekanik'            => $this->input->post('tipeVentilasiRiMekanik'),
                        'pressure_control'            => $this->input->post('pressureControlMekanik'),
                        'pressure_support'            => $this->input->post('pressureSupportMekanik'),
                        'volume_control'            => $this->input->post('volumeControlMekanik'),
                        'intermiten'            => $this->input->post('intermitenMekanik'),
                        'lainnya'            => $this->input->post('lainnyaMekanik'),
                        'tekanan'            => $this->input->post('tekananVentilasiRi'),
                        'desk_tekanan'            => $this->input->post('tekananDeskVentilasiRi'),
                        'resp_rate'            => $this->input->post('respRateVentilasiRi'),
                        'tidal_volume'            => $this->input->post('tidalVolumeVentilasiRi'),
                        'fi'            => $this->input->post('fiVentilasiRi'),
                        'flow'            => $this->input->post('flowVentilasiRi'),
                        'flow_e'            => $this->input->post('flowEVentilasiRi'),
                        'ukuran'            => $this->input->post('ukuranEttVentilasiRi'),
                        'tekanan_balon'            => $this->input->post('tekananBalonVentilasiRi'),
                        'end_tidal'            => $this->input->post('endTidalVentilasiRi'),
                        'oleh'            => $this->session->userdata("id"),
                        'status'            => 1,
                    );
                    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
                    $this->db->insert('keperawatan.tb_flosheet_ventilasi', $data);
                 
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();
                $post = $this->input->post();

                $this->db->where('tb_flosheet_ventilasi.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_ventilasi', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
        }
    }

    public function action_ssp($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			// $rules = $this->validasiMalnutrisiDewasaModel->rules;
                // $this->form_validation->set_rules($rules);

    			// if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataKesadaran = array(
                        'data_source' => 28,
                        'nomr' => $this->input->post('nomr'),
                        'nokun' => $this->input->post('nokun'),
                        'kesadaran' => $this->input->post('kesadaran'),
                        'oleh' =>  $this->session->userdata("id"),
                    );

                    $id_kesadaran = $this->KesadaranModel->insert($dataKesadaran);

                    $data = array(
                        'nokun' => $post['nokun'],
                        'id_flosheet' => $post['id_flosheet'],
                        'pukul' => $post['pukul'],
                        'tanggal' => $post['tanggal'],
                        'id_kesadaran' => $id_kesadaran,
                        'jenis' => $post['jenis'],
                        'buka_mata' => $post['buka_mata'],
                        'respons_motorik' => $post['respons_motorik'],
                        'respons_verbal' => $post['respons_verbal'],
                        'respons_mata' => $post['respons_mata'],
                        'respons_motorik_fs' => $post['respons_motorik_fs'],
                        'reflek_batang_otak' => $post['reflek_batang_otak'],
                        'respirasi' => $post['respirasi'],

                        'reaksi_pupil' => $post['reaksi_pupil'],
                        'reaksi_pupil_kiri' => $post['reaksi_pupil_kiri'],
                        'besar_pupil' => $post['besar_pupil'],
                        'besar_pupil_kiri' => $post['besar_pupil_kiri'],
                        'tki' => $post['tki'],
                        'tka' => $post['tka'],
                        'kki' => $post['kki'],
                        'kka' => $post['kka'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    $this->db->insert('keperawatan.tb_flosheet_ssp', $data);
                    $id_ssp = $this->db->insert_id();
                    $this->KesadaranModel->update(array('ref' => $id_ssp), array('id' => $id_kesadaran));

                    
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			// }else{
    			// 	$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			// }
    			echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();

                $post = $this->input->post();

                $this->db->where('tb_flosheet_ssp.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_ssp', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
    			echo json_encode($result);
            }
    	}
    }

    public function action_infus($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			// $rules = $this->validasiMalnutrisiDewasaModel->rules;
                // $this->form_validation->set_rules($rules);

    			// if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataInfus = array();
                    $index = 0;
                    if (isset($post['infus'])) {
                        foreach ($post['infus'] as $input) {
                            if ($post['infus'][$index] != "") {
                                array_push(
                                    $dataInfus,
                                    array(
                                        'nokun' => $post['nokun'],
                                        'id_flosheet' => $post['id_flosheet'],
                                        'id_cairan' => $post['infus'][$index],
                                        'dosis' => $post['dosis'][$index],
                                        'keterangan_dosis' => $post['ket_dosis'][$index],
                                        'oleh' => $this->session->userdata("id"),
                                    )
                                );
                            }
                            $index++;
                        }
                        $this->db->insert_batch('keperawatan.tb_infus', $dataInfus);
                    }
                    
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			// }else{
    			// 	$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			// }
    			echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();

                $post = $this->input->post();

                $this->db->where('tb_infus.id', $post['id']);
                $this->db->update('keperawatan.tb_infus', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
    			echo json_encode($result);
            }else if ($param == 'getInfus') {
                $dataInfus = $this->FlosheetModel->get_infus();

                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $dataInfus
                    )
                );
            }else if ($param == 'getCairan') {
                $data = $this->FlosheetModel->get_cairan();
                $json = array();
                foreach ($data as $row) {
                    $json[] = array('id' => $row -> ID, 'text' => $row -> NAMA );
                }

                echo json_encode($json);
            }else if ($param == 'selectInfus') {
                $dataInfus = $this->FlosheetModel->get_infus();

                $json = array();
                foreach ($dataInfus as $row) {
                    $json[] = array('id' => $row -> id, 'text' => $row -> cairan. '('. $row -> dosis_ca_terakhir . '/' . $row -> dosis_awal . ')');
                }

                echo json_encode($json);
            }
    	}
    }

    public function action_darah($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			// $rules = $this->validasiMalnutrisiDewasaModel->rules;
                // $this->form_validation->set_rules($rules);

    			// if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataDarah = array();
                    $index = 0;
                    if (isset($post['darah'])) {
                        foreach ($post['darah'] as $input) {
                            if ($post['darah'][$index] != "") {
                                array_push(
                                    $dataDarah,
                                    array(
                                        'nokun' => $post['nokun'],
                                        'id_flosheet' => $post['id_flosheet'],
                                        'id_darah' => $post['darah'][$index],
                                        'dosis' => $post['dosis'][$index],
                                        'oleh' => $this->session->userdata("id"),
                                    )
                                );
                            }
                            $index++;
                        }
                        $this->db->insert_batch('keperawatan.tb_flosheet_darah', $dataDarah);
                    }
                    
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			// }else{
    			// 	$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			// }
    			echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();

                $post = $this->input->post();

                $this->db->where('tb_flosheet_darah.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_darah', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
    			echo json_encode($result);
            }else if ($param == 'getDarah') {
                $data = $this->FlosheetModel->get_darah();

                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $data
                    )
                );
            }else if ($param == 'getPemasukanMasuk') {
                $data = $this->FlosheetModel->get_pemasukan_masuk();

                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $data
                    )
                );
            }
    	}
    }

    public function action_pemasukan($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			// $rules = $this->validasiMalnutrisiDewasaModel->rules;
                // $this->form_validation->set_rules($rules);

    			// if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $data = array(
                        'nokun' => $post['nokun'],
                        'id_flosheet' => $post['id_flosheet'],
                        'pukul' => $post['pukul'],
                        'tanggal' => $post['tanggal'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    $this->db->insert('keperawatan.tb_flosheet_pemasukan', $data);
                    $id_pemasukan = $this->db->insert_id();
                    
                    $dataInfus = array();
                    $index = 0;
                    if (isset($post['infus'])) {
                        foreach ($post['infus'] as $input) {
                            if ($post['infus'][$index] != "") {
                                array_push(
                                    $dataInfus,
                                    array(
                                        'id_pemasukan' => $id_pemasukan,
                                        'id_infus' => $post['infus'][$index],
                                        'cm' => $post['cm_infus'][$index],
                                        'ca' => $post['ca_infus'][$index]
                                    )
                                );
                            }
                            $index++;
                        }
                        $this->db->insert_batch('keperawatan.tb_flosheet_pemasukan_infus', $dataInfus);
                    }

                    $dataMasuk = array();
                    $index = 0;
                    if (isset($post['darah'])) {
                        foreach ($post['darah'] as $input) {
                            if ($post['darah'][$index] != "") {
                                array_push(
                                    $dataMasuk,
                                    array(
                                        'id_pemasukan' => $id_pemasukan,
                                        'id_masuk' => $post['darah'][$index],
                                        'cm' => $post['cm_darah'][$index],
                                        // 'ca' => $post['ca_darah'][$index]
                                    )
                                );
                            }
                            $index++;
                        }
                        $this->db->insert_batch('keperawatan.tb_flosheet_pemasukan_masuk', $dataMasuk);
                    }

                    if($post['cm_oral'] != ''){
                        $data = array(
                            'id_pemasukan' => $id_pemasukan,
                            'cm' => $post['cm_oral'],
                            // 'ca' => $post['ca_oral']
                        );

                        $this->db->insert('keperawatan.tb_flosheet_pemasukan_oral', $data);
                    }

                    if($post['cm_ngt'] != ''){
                        $data = array(
                            'id_pemasukan' => $id_pemasukan,
                            'cm' => $post['cm_ngt'],
                            // 'ca' => $post['ca_ngt']
                        );

                        $this->db->insert('keperawatan.tb_flosheet_pemasukan_ngt', $data);
                    }

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			// }else{
    			// 	$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			// }
    			echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();

                $post = $this->input->post();

                $this->db->where('tb_flosheet_pemasukan.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_pemasukan', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
    			echo json_encode($result);
            }
    	}
    }

    public function action_masalah($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah'){
                    $post = $this->input->post();

                    $data = array(
                        'nokun' => $post['nokun'],
                        'id_flosheet' => $post['id_flosheet'],
                        'jam' => $post['jam'],
                        'masalah' => $post['masalah'],
                        'dokter' => $post['dokter'],
                        'instruksi_cito' => $post['instruksi_cito'],
                        'therafi_cito' => $post['therafi_cito'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    $this->db->trans_begin();

                    $this->db->insert('keperawatan.tb_flosheet_masalah', $data);

                    
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			echo json_encode($result);
            }else if($param == 'ubah'){
                $post = $this->input->post();
                
                $dataUbah = array(
                    'jam' => $post['jam'],
                    'masalah' => $post['masalah'],
                    'dokter' => $post['dokter'],
                    'instruksi_cito' => $post['instruksi_cito'],
                    'therafi_cito' => $post['therafi_cito']
                );

                $this->db->trans_begin();
                $this->db->where('keperawatan.tb_flosheet_masalah.id', $post['id_masalah']);
                $this->db->update('keperawatan.tb_flosheet_masalah', $dataUbah);
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
    			echo json_encode($result);
            }
    	}
    }

    public function action_drain($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataDrain = array();
                    $index = 0;
                    if (isset($post['drain'])) {
                        foreach ($post['drain'] as $input) {
                            if ($post['drain'][$index] != "") {
                                array_push(
                                    $dataDrain,
                                    array(
                                        'nokun' => $post['nokun'],
                                        'id_flosheet' => $post['id_flosheet'],
                                        'drain' => $post['drain'][$index],
                                        'oleh' => $this->session->userdata("id")
                                    )
                                );
                            }
                            $index++;
                        }
                        $this->db->insert_batch('keperawatan.tb_flosheet_drain', $dataDrain);
                    }
                    
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();

                $post = $this->input->post();

                $this->db->where('tb_flosheet_drain.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_drain', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
    			echo json_encode($result);
            }else if ($param == 'getDrain') {
                $post = $this->input->post();
                $dataDrain = $this->FlosheetModel->get_drain($post['id_flosheet']);
                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $dataDrain
                    )
                );
            }else if ($param == 'getLastDrain') {
                $post = $this->input->post();
                $dataLastDrain = $this->FlosheetModel->get_last_drain($post['id_flosheet']);
                // echo "<pre>".print_r($dataLastDrain)."</pre>";
                echo json_encode(
                    array(
                        'status' => 'success',
                        'data' => $dataLastDrain
                    )
                );
            }
    	}
    }

    public function action_pengeluaran($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah'){
                    $post = $this->input->post();

                    $data = array(
                        'nokun' => $post['nokun'],
                        'id_flosheet' => $post['id_flosheet'],
                        'jam' => $post['jam'],
                        'urine' => $post['urine'],
                        'urine_per' => $post['urine_per'],
                        'ngt' => $post['ngt'],
                        'ngt_per' => $post['ngt_per'],
                        'bab' => $post['bab'],
                        'bab_volume' => $post['bab_volume'],
                        'bab_per' => $post['bab_per'],
                        'oleh' => $this->session->userdata("id")
                    );

                    $this->db->trans_begin();

                    $this->db->insert('keperawatan.tb_flosheet_pengeluaran', $data);

                    $id_pengeluaran = $this->db->insert_id();
                    
                    $dataDrain = array();
                    $index = 0;
                    if (isset($post['drain'])) {
                        foreach ($post['drain'] as $input) {
                            if ($post['drain'][$index] != "") {
                                array_push(
                                    $dataDrain,
                                    array(
                                        'id_pengeluaran' => $id_pengeluaran,
                                        'id_drain' => $post['drain'][$index],
                                        'volume' => $post['volume'][$index],
                                        'volume_per' => $post['volume'][$index]+$post['volume_per'][$index]
                                    )
                                );
                            }
                            $index++;
                        }

                        $this->db->insert_batch('keperawatan.tb_flosheet_pengeluaran_drain', $dataDrain);
                    }

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			echo json_encode($result);
            }else if($param == 'nonaktif'){
                $this->db->trans_begin();
                $post = $this->input->post();

                $this->db->where('keperawatan.tb_flosheet_pengeluaran.id', $post['id']);
                $this->db->update('keperawatan.tb_flosheet_pengeluaran', array('status' => 0));
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
    	}
    }

    public function action_itk($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah'){
                    $post = $this->input->post();

                    $data = array(
                        'nokun' => $post['nokun'],
                        'id_flosheet' => $post['id_flosheet'],
                        'perawat' => $post['perawat'],
                        'shift' => $post['shift'],
                        'jam' => $post['jam'],
                        'catatan_perawatan' => isset($post['catatan_perawatan']) ? json_encode($post['catatan_perawatan']) : "",
                        'catatan_perawatan_lain' => $post['catatan_perawatan_lain'],
                        'oleh' => $this->session->userdata("id")
                    );

                    $this->db->trans_begin();

                    $this->db->insert('keperawatan.tb_flosheet_itk', $data);

                    
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			echo json_encode($result);
            }else if($param == 'ubah'){
                $post = $this->input->post();
                
                $dataUbah = array(
                    'perawat' => $post['perawat'],
                    'shift' => $post['shift'],
                    'jam' => $post['jam'],
                    'catatan_perawatan' => isset($post['catatan_perawatan']) ? json_encode($post['catatan_perawatan']) : "",
                    'catatan_perawatan_lain' => $post['catatan_perawatan_lain']
                );

                $this->db->trans_begin();
                $this->db->where('keperawatan.tb_flosheet_itk.id', $post['id_itk']);
                $this->db->update('keperawatan.tb_flosheet_itk', $dataUbah);
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
    			echo json_encode($result);
            }
    	}
    }

    public function action_pengkajianfisik($param)
    {
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah'){
                    $post = $this->input->post();

                    $data = array(
                        'nokun' => $post['nokun'],
                        'id_flosheet' => $post['idflosheet'],
                        'judul' => $post['judulFisik'],
                        'gambar' => file_get_contents($this->input->post('img_val')),
                        'catatan' => $post['catatanFisik'],
                        'oleh' => $this->session->userdata("id"),
                        'status_pengkajian_fisik' => 1
                    );

                    $this->db->trans_begin();
                    // echo "<pre>";print_r($data);echo "</pre>";

                    $this->db->insert('keperawatan.tb_flosheet_pengkajian_fisik', $data);

                    
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                echo json_encode($result);
            }else if($param == 'ubah'){
                $post = $this->input->post();
                $idfpf = $this->uri->segment(6);
                $dataUbah = array(
                    'status_pengkajian_fisik' => 0
                );

                $this->db->trans_begin();
                // echo "<pre>";print_r($dataUbah);echo "</pre>";
                $this->db->where('keperawatan.tb_flosheet_pengkajian_fisik.id', $idfpf);
                $this->db->update('keperawatan.tb_flosheet_pengkajian_fisik', $dataUbah);
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
        }
    }

}
