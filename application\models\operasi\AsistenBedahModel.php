<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AsistenBedahModel extends MY_Model
{
    protected $_table = 'medis.tb_asisten_pendaftaran_operasi';

    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'dokter_bedah[]',
                'label' => 'Dokter bedah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'rencana_tindakan_operasi[]',
                'label' => 'Rencana tindakan operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function simpan($data)
    {
        $this->db->insert_batch($this->_table, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_asisten_pendaftaran_operasi.id_pendaftaran', $id);
        $this->db->update($this->_table, $data);
    }

    public function ambilTindakan($id)
    {
        $this->db->select(
            'tapo.asisten_bedah dokter_lain, master.getNamaLengkapPegawai(d.NIP) nama_dokter_lain,
            tapo.rencana_tindakan'
        );
        $this->db->from('medis.tb_asisten_pendaftaran_operasi tapo');
        $this->db->join('master.dokter d', 'd.ID = tapo.asisten_bedah', 'left');
        $this->db->where('tapo.status', 1);
        $this->db->where('tapo.id_pendaftaran', $id);
        $this->db->order_by('tapo.id', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }
}

/* End of file AsistenBedahModel.php */
/* Location: ./application/models/operasi/AsistenBedahModel.php */