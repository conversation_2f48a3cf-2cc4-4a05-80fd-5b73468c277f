<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>maja extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/RemajaModel'));
  }

  public function index()
  {
    $norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->RemajaModel->getNomrRawatInap($nopen);
    $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
    $getPengkajian = $this->RemajaModel->getPengkajian($getIdEmr);
    $data = array(
      'nopen' => $nopen,
      'nokun' => $nokun,
      'norm' => $norm,
      'getPengkajian' => $getPengkajian,
      'pasien' => $getNomr,
      'listRambut' => $this->masterModel->referensi(1167),
      'listJerawat' => $this->masterModel->referensi(1168),
      'listPerubahanSuara' => $this->masterModel->referensi(1169),
      'listPertumbuhanPayudara' => $this->masterModel->referensi(1170),
      'listRiwayatHaid' => $this->masterModel->referensi(1171),
      'listRiwayatSirkumsisi' => $this->masterModel->referensi(1172),
      'listSiklus' => $this->masterModel->referensi(1173),
      'listKeluhan' => $this->masterModel->referensi(1174),
      'listPendidikan' => $this->masterModel->referensi(24),
      'listPendidikanDilanjutkan' => $this->masterModel->referensi(1175),
      'listSahabat' => $this->masterModel->referensi(1176),
      'listKlpremaja' => $this->masterModel->referensi(1177),
      'listPanutan' => $this->masterModel->referensi(1178),
      'listMemberidukungan' => $this->masterModel->referensi(1179),
      'listMelakukanibadah' => $this->masterModel->referensi(1180),
      'listBercerita' => $this->masterModel->referensi(1181),
      'listStress' => $this->masterModel->referensi(1182),
      'listDepresi' => $this->masterModel->referensi(1183),
      'listBunuhdiri' => $this->masterModel->referensi(1184),
      'listNarkotika' => $this->masterModel->referensi(1185),
      'anamnesis' => $this->masterModel->referensi(54)
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/pengkajianRiRemaja', $data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 7,
          'diagnosa_masuk' => $post['rujukanRiD'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataFisik = array(
          'id_emr' => $getIdEmr,
          'riwayat_rambut' => isset($post['riwayat_rambut']) ? $post['riwayat_rambut'] : "",
          'sebutkan_rambut' => isset($post['sebutkan_rambut']) ? $post['sebutkan_rambut'] : "",
          'jerawat' => isset($post['jerawat']) ? $post['jerawat'] : "",
          'perubahansuara' => isset($post['perubahansuara']) ? $post['perubahansuara'] : "",
          'pertumbuhanpayudara' => isset($post['pertumbuhanpayudara']) ? $post['pertumbuhanpayudara'] : "",
          'riwayat_haid' => isset($post['riwayat_haid']) ? $post['riwayat_haid'] : "",
          'sebutkan_haid' => isset($post['sebutkan_haid']) ? $post['sebutkan_haid'] : "",
          'siklus' => isset($post['siklus']) ? $post['siklus'] : "",
          'keluhan' => isset($post['keluhan']) ? $post['keluhan'] : "",
          'sebutkan_keluhan' => isset($post['sebutkan_keluhan']) ? $post['sebutkan_keluhan'] : "",
          'riwayat_sirkumsisi' => isset($post['riwayat_sirkumsisi']) ? $post['riwayat_sirkumsisi'] : "",
          'sebutkan_sirkumsisi' => isset($post['sebutkan_sirkumsisi']) ? $post['sebutkan_sirkumsisi'] : "",
          'siklus' => isset($post['siklus']) ? $post['siklus'] : "",
          'keluhan' => isset($post['keluhan']) ? $post['keluhan'] : "",
          'sebutkan_keluhan' => isset($post['sebutkan_keluhan']) ? $post['sebutkan_keluhan'] : ""
        );

        // echo "<pre>data riwayat kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataPsikososial = array(
          'id_emr' => $getIdEmr,
          'riwayat_pendidikan' => isset($post['riwayat_pendidikan']) ? $post['riwayat_pendidikan'] : "",
          'riwayat_pendidikan_dilanjutkan' => isset($post['riwayat_pendidikan_dilanjutkan']) ? $post['riwayat_pendidikan_dilanjutkan'] : "",
          'hobi' => isset($post['hobi']) ? $post['hobi'] : "",
          'citacita' => isset($post['citacita']) ? $post['citacita'] : "",
          'riwayat_sahabat' => isset($post['riwayat_sahabat']) ? $post['riwayat_sahabat'] : "",
          'sebutkan_sahabat' => isset($post['sebutkan_sahabat']) ? $post['sebutkan_sahabat'] : "",
          'riwayat_klpremaja' => isset($post['riwayat_klpremaja']) ? $post['riwayat_klpremaja'] : "",
          'sebutkan_klpremaja' => isset($post['sebutkan_klpremaja']) ? $post['sebutkan_klpremaja'] : "",
          'riwayat_panutan' => isset($post['riwayat_panutan']) ? $post['riwayat_panutan'] : "",
          'sebutkan_panutan' => isset($post['sebutkan_panutan']) ? $post['sebutkan_panutan'] : "",
          'riwayat_memberidukungan' => isset($post['riwayat_memberidukungan']) ? $post['riwayat_memberidukungan'] : "",
          'sebutkan_memberidukungan' => isset($post['sebutkan_memberidukungan']) ? $post['sebutkan_memberidukungan'] : "",
          'riwayat_melakukanibadah' => isset($post['riwayat_melakukanibadah']) ? $post['riwayat_melakukanibadah'] : "",
          'sebutkan_melakukanibadah' => isset($post['sebutkan_melakukanibadah']) ? $post['sebutkan_melakukanibadah'] : "",
          'riwayat_bercerita' => isset($post['riwayat_bercerita']) ? $post['riwayat_bercerita'] : "",
          'sebutkan_bercerita' => isset($post['sebutkan_bercerita']) ? $post['sebutkan_bercerita'] : "",
          'riwayat_stress' => isset($post['riwayat_stress']) ? $post['riwayat_stress'] : "",
          'sebutkan_stress' => isset($post['sebutkan_stress']) ? $post['sebutkan_stress'] : "",
          'riwayat_depresi' => isset($post['riwayat_depresi']) ? $post['riwayat_depresi'] : "",
          'sebutkan_depresi' => isset($post['sebutkan_depresi']) ? $post['sebutkan_depresi'] : "",
          'riwayat_bunuhdiri' => isset($post['riwayat_bunuhdiri']) ? $post['riwayat_bunuhdiri'] : "",
          'sebutkan_bunuhdiri' => isset($post['sebutkan_bunuhdiri']) ? $post['sebutkan_bunuhdiri'] : "",
          'riwayat_narkotika' => isset($post['riwayat_narkotika']) ? $post['riwayat_narkotika'] : "",
          'sebutkan_narkotika' => isset($post['sebutkan_narkotika']) ? $post['sebutkan_narkotika'] : "",
          'oleh' => $this->session->userdata('id')
        );

        $this->db->trans_begin();
        
        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_pengkajianri_remaja', $dataPsikososial);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataFisik);
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success_ubah');
          }
  
          echo json_encode($result);
        }else{
            $this->db->insert('keperawatan.tb_pengkajianri_remaja', $dataPsikososial);
            $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataFisik);
            $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
            $this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
        }
        // echo "<pre>data kesadaran ";print_r($dataKesedaran);echo "</pre>";
      }
      else if($param == 'count'){
        $result = $this->RemajaModel->get_count();
        echo json_encode($result);
      }
    }
  }

  public function yakinVerifPengkajian()
  {
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif' => 1,
      'verif_oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $this->db->where('id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

    public function datatables(){
      
      $result = $this->RemajaModel->historyPengkajian();

      $data = array();
      foreach ($result as $row){
        $tombolCetak = '<a class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
        $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
        $action = "";
        $verif = "";
        $userLogin = $this->session->userdata('status');

        // KONDISI UNTUK ADA PENGKAJIAN PERAWAT
        if($row -> ID_EMR_PERAWAT != null){
          if($userLogin == 2){
            $jenisPengkajian = $row -> JENIS_PENGKAJIAN_KEPERAWATAN;
            
            if($row -> STATUS_VERIFIKASI == 0){
              $namaVerif = '<h4>-</h4>';
              $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
            }elseif($row -> STATUS_VERIFIKASI == 1){
              $namaVerif = $row -> INFO_VERIFIKASI;
              $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
            }

            if($jenisPengkajian == 5){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
            }elseif($jenisPengkajian == 6){
              $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
            }elseif($jenisPengkajian == 7){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
            }elseif($jenisPengkajian == 9){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatIGD" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian IGD</button>';
            }elseif($jenisPengkajian == 11){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
            }elseif($jenisPengkajian == 14){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Pengkajian Akhir Kehidupan</button>';
            }else{
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
            }
          }elseif($userLogin == 1){
            $jenisPengkajian = $row -> JENIS_PENGKAJIAN_MEDIS;

            if($row -> STATUS_VERIFIKASI == 0){
              $namaVerif = '<h4>-</h4>';
              $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
            }elseif($row -> STATUS_VERIFIKASI == 1){
              $namaVerif = $row -> INFO_VERIFIKASI;
              $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
            }

            if($jenisPengkajian == 5){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
            }elseif($jenisPengkajian == 6){
              $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
            }elseif($jenisPengkajian == 7){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
            }elseif($jenisPengkajian == 9){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatIGD" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Pengkajian IGD</button>';
            }elseif($jenisPengkajian == 11){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
            }elseif($jenisPengkajian == 14){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKMedis" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Medis Pengkajian Akhir Kehidupan</button>';
            }else{
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
            }
          }

        }

        // KONDISI UNTUK ADA PENGKAJIAN MEDIS
        if($row -> ID_EMR_MEDIS != null){
          if($userLogin == 1){
            if($row -> STATUS_VERIFIKASI == 0){
              if($row -> ID_EMR_PERAWAT != null){
                $verif = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'">Verif</button>';
              }else{
                $verif = $row -> INFO_VERIFIKASI;
              }
            }elseif($row -> STATUS_VERIFIKASI == 1){
              $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
            }

            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
          }elseif($userLogin == 2){
            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="2"><i class="fa fa-eye"></i>  View Medis</button>';
          }
        }

        $sub_array = array();
        $sub_array[] = $row -> INFO;
        $sub_array[] = $verif;
        $sub_array[] = $row -> RUANGAN;
        $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
        $sub_array[] = $action;
        $sub_array[] = $row -> DPJP;
        $sub_array[] = $namaVerif;
        $sub_array[] = $tombolCetak;
        $sub_array[] = $row -> USER_MEDIS;
        $sub_array[] = $row -> USER_PERAWAT;   
        $data[] = $sub_array;
      }

      $output = array(
          "draw" => intval($this->input->post("draw")),
          "data"              => $data
      );
      echo json_encode($output);
    }


}