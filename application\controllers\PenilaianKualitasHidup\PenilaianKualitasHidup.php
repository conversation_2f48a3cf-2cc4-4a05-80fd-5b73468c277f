<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PenilaianKualitasHidup extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'EresepModel'));
  }

  public function formpenilaian()
  {
    $nomr = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    $idemr = $this->uri->segment(7);
    $detilkualitas = $this->pengkajianAwalModel->detilKualitasHidup($nokun);
    $datakuis = $this->masterModel->referensi(179);


    $data = array(
      'title' 		=> 'Form penilaian',
      'isi' 			=> 'Pengkajian/emr/penilaiankualitashidup/formpenilaian',
      'nomr' 			=> $nomr,
      'nopen' 		=> $nopen,
      'nokun' 		=> $nokun,
      'idemr'     => $idemr,
      'datakuis' 		=> $datakuis,
      'detilkualitas' => $detilkualitas,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function simpankualitas($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post(NULL,TRUE);
//$imr = $post['idemr'];
//echo "<pre>"; print_r($post); exit;
        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $nokun = $post['nokun'];
        $detilkualitas = $this->pengkajianAwalModel->getNokunKualitasHidup($nokun);
        $oleh = $this->session->userdata("id");	
//$datanya = $detilkualitas['nokun'];

        $data = array(

          'nokun' 				=> isset($post['nokun']) ? $post['nokun'] : "",
          'bagian_a' 				=> isset($post['bagian_a']) ? $post['bagian_a'] : "",
          'bagian1' 				=> isset($post['bagian1']) ? $post['bagian1'] : "",
          'gejala_penyakit' 		=> isset($post['gejala_penyakit']) ? $post['gejala_penyakit'] : "",
          'bagian2' 				=> isset($post['bagian2']) ? $post['bagian2'] : "",
          'bagian3' 				=> isset($post['bagian3']) ? $post['bagian3'] : "",
          'bagian4' 				=> isset($post['bagian4']) ? $post['bagian4'] : "",
          'bagian5' 				=> isset($post['bagian5']) ? $post['bagian5'] : "",
          'bagian6' 				=> isset($post['bagian6']) ? $post['bagian6'] : "",
          'bagian7' 				=> isset($post['bagian7']) ? $post['bagian7'] : "",
          'bagian8' 				=> isset($post['bagian8']) ? $post['bagian8'] : "",
          'bagian9' 				=> isset($post['bagian9']) ? $post['bagian9'] : "",
          'bagian10' 				=> isset($post['bagian10']) ? $post['bagian10'] : "",
          'bagian11' 				=> isset($post['bagian11']) ? $post['bagian11'] : "",
          'bagian12' 				=> isset($post['bagian12']) ? $post['bagian12'] : "",
          'bagian13' 				=> isset($post['bagian13']) ? $post['bagian13'] : "",
          'bagian14' 				=> isset($post['bagian14']) ? $post['bagian14'] : "",
          'nilai_bagian_a' 		=> isset($post['nilai_bagian_a']) ? $post['nilai_bagian_a'] : "",
          'nilai_bagian1' 		=> isset($post['nilai_bagian1']) ? $post['nilai_bagian1'] : "",
          'nilai_bagian2' 		=> isset($post['nilai_bagian2']) ? $post['nilai_bagian2'] : "",
          'nilai_bagian3' 		=> isset($post['nilai_bagian3']) ? $post['nilai_bagian3'] : "",
          'nilai_bagian4' 		=> isset($post['nilai_bagian4']) ? $post['nilai_bagian4'] : "",
          'nilai_bagian5' 		=> isset($post['nilai_bagian5']) ? $post['nilai_bagian5'] : "",
          'nilai_bagian6' 		=> isset($post['nilai_bagian6']) ? $post['nilai_bagian6'] : "",
          'nilai_bagian7' 		=> isset($post['nilai_bagian7']) ? $post['nilai_bagian7'] : "",
          'nilai_bagian8' 		=> isset($post['nilai_bagian8']) ? $post['nilai_bagian8'] : "",
          'nilai_bagian9' 		=> isset($post['nilai_bagian9']) ? $post['nilai_bagian9'] : "",
          'nilai_bagian10' 		=> isset($post['nilai_bagian10']) ? $post['nilai_bagian10'] : "",
          'nilai_bagian11' 		=> isset($post['nilai_bagian11']) ? $post['nilai_bagian11'] : "",
          'nilai_bagian12' 		=> isset($post['nilai_bagian12']) ? $post['nilai_bagian12'] : "",
          'nilai_bagian13' 		=> isset($post['nilai_bagian13']) ? $post['nilai_bagian13'] : "",
          'nilai_bagian14' 		=> isset($post['nilai_bagian14']) ? $post['nilai_bagian14'] : "",
          'nilai_fisik' 		  => isset($post['nilai_fisik']) ? $post['nilai_fisik'] : "",
          'nilai_psikologis' 	=> isset($post['nilai_psikologis']) ? $post['nilai_psikologis'] : "",
          'nilai_spiritual' 	=> isset($post['nilai_spiritual']) ? $post['nilai_spiritual'] : "",
          'nilai_sosial' 		  => isset($post['nilai_sosial']) ? $post['nilai_sosial'] : "",
          'nilai_total' 		  => isset($post['nilai_total']) ? $post['nilai_total'] : "",
//'oleh' 				=> $oleh,
        );
//echo "<pre>";print_r($data);exit();
        if (!empty($detilkualitas)) {
          if($this->db->replace('medis.tb_kualitas_hidup',$data)){
            $this->db->where(array('nokun' => $post['nokun']));             
          } }else { 			
            $this->db->insert('medis.tb_kualitas_hidup', $data);
          }
          redirect(base_url("PenilaianKualitasHidup/PenilaianKualitasHidup/formpenilaian/" . $nomr . "/" . $nopen . "/" . $nokun));

        }
      }
    }

  }
