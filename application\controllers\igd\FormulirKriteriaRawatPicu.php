<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormulirKriteriaRawatPicu extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'igd/FKRPModel'));
  }

  public function indexFKRPRI() {
    $id = $this->input->post('id');
    $nopen = $this->input->post('nopen');
    $history_fkrpri = $this->FKRPModel->historyFKRPRI($id);
    $getTekananDarah = $this->FKRPModel->getTekananDarah($nopen);

    $data = array(
      'id_fkrp' => $id,
      'nopen' => $nopen,
      'listAlatBantuNapas' => $this->masterModel->referensi(1457),
      'listKondisiStabil' => $this->masterModel->referensi(1458),
      'listTerapiGagal' => $this->masterModel->referensi(1459),
      'listEndOfLife' => $this->masterModel->referensi(1460),
      'listMenolakDirawat' => $this->masterModel->referensi(1461),
      'ruanganIntensif' => $this->masterModel->ruanganIntensif(),
      'history_fkrpri' => $history_fkrpri,
      'getTekananDarah' => $getTekananDarah
    );
    $this->load->view('Pengkajian/igd/formulirKriteriaRawatPicu/modalFKRPRI', $data);
  }

  public function indexRawatInap()
  {
    $id_pengguna = $this->session->userdata('id');
    $nokun = $this->uri->segment(4);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $nopen = $getNomr['NOPEN'];
    $getIdEmrFkrp = $this->pengkajianAwalModel->getIdEmrFkrp($nokun);
    $id_emr = $this->uri->segment(5);

    $fkrp_kriteria_eksklusi = $this->masterModel->referensi(818);
    $fkrp_gangguan_kardiovaskular = $this->masterModel->referensi(820);
    $fkrp_usia = $this->masterModel->referensi(830);
    $fkrp_frekuensi_nadi = $this->masterModel->referensi(831);
    $fkrp_tekanan_darah_sistolik = $this->masterModel->referensi(832);
    $fkrp_gangguan_pernapasan = $this->masterModel->referensi(821);
    $fkrp_gangguan_neurologi = $this->masterModel->referensi(822);
    $fkrp_gangguan_gastrointestinal = $this->masterModel->referensi(823);
    $fkrp_gangguan_endokrin_metabolik = $this->masterModel->referensi(824);
    $fkrp_kegawatdaruratan_onkologi = $this->masterModel->referensi(826);
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
    $history_fkrp = $this->FKRPModel->history_fkrp($nomr);

    if ($id_emr != "") {
        $id_fkrp = $id_emr;
        $get_fkrp = $this->FKRPModel->get_fkrp($id_fkrp);
    }
    
    $data = array(
      'id_emr' => isset($id_emr) ? $id_emr : "",
      'id_pengguna' => $id_pengguna,
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'nomr' => $nomr,
      'nopen' => $nopen,
      'history_fkrp' => $history_fkrp,
      'get_fkrp' => isset($get_fkrp) ? $get_fkrp : "",
      'fkrp_kriteria_eksklusi' => $fkrp_kriteria_eksklusi,
      'fkrp_gangguan_kardiovaskular' => $fkrp_gangguan_kardiovaskular,
      'fkrp_usia' => $fkrp_usia,
      'fkrp_frekuensi_nadi' => $fkrp_frekuensi_nadi,
      'fkrp_tekanan_darah_sistolik' => $fkrp_tekanan_darah_sistolik,
      'fkrp_gangguan_pernapasan' => $fkrp_gangguan_pernapasan,
      'fkrp_gangguan_neurologi' => $fkrp_gangguan_neurologi,
      'fkrp_gangguan_gastrointestinal' => $fkrp_gangguan_gastrointestinal,
      'fkrp_gangguan_endokrin_metabolik' => $fkrp_gangguan_endokrin_metabolik,
      'fkrp_kegawatdaruratan_onkologi' => $fkrp_kegawatdaruratan_onkologi,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
    );

    // echo "<pre>".print_r($data)."</pre>";
    // exit();

    $this->load->view('Pengkajian/igd/formulirKriteriaRawatPicu/index', $data);
  }

  public function index()
  {
    $id_pengguna = $this->session->userdata('id');
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $nopen = $getNomr['NOPEN'];
    $getIdEmrFkrp = $this->pengkajianAwalModel->getIdEmrFkrp($nokun);
    $id_emr = $getIdEmrFkrp['id_fkrp'];

    $fkrp_kriteria_eksklusi = $this->masterModel->referensi(818);
    $fkrp_gangguan_kardiovaskular = $this->masterModel->referensi(820);
    $fkrp_usia = $this->masterModel->referensi(830);
    $fkrp_frekuensi_nadi = $this->masterModel->referensi(831);
    $fkrp_tekanan_darah_sistolik = $this->masterModel->referensi(832);
    $fkrp_gangguan_pernapasan = $this->masterModel->referensi(821);
    $fkrp_gangguan_neurologi = $this->masterModel->referensi(822);
    $fkrp_gangguan_gastrointestinal = $this->masterModel->referensi(823);
    $fkrp_gangguan_endokrin_metabolik = $this->masterModel->referensi(824);
    $fkrp_kegawatdaruratan_onkologi = $this->masterModel->referensi(826);
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
    $history_fkrp = $this->FKRPModel->history_fkrp($nomr);
    
    
    if ($id_emr != "") {
        $id_fkrp = $id_emr;
        $get_fkrp = $this->FKRPModel->get_fkrp($id_fkrp);
    }
    
    $data = array(
      'id_emr' => isset($id_emr) ? $id_emr : "",
      'id_pengguna' => $id_pengguna,
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'nomr' => $nomr,
      'nopen' => $nopen,
      'history_fkrp' => $history_fkrp,
      'get_fkrp' => isset($get_fkrp) ? $get_fkrp : "",
      'fkrp_kriteria_eksklusi' => $fkrp_kriteria_eksklusi,
      'fkrp_gangguan_kardiovaskular' => $fkrp_gangguan_kardiovaskular,
      'fkrp_usia' => $fkrp_usia,
      'fkrp_frekuensi_nadi' => $fkrp_frekuensi_nadi,
      'fkrp_tekanan_darah_sistolik' => $fkrp_tekanan_darah_sistolik,
      'fkrp_gangguan_pernapasan' => $fkrp_gangguan_pernapasan,
      'fkrp_gangguan_neurologi' => $fkrp_gangguan_neurologi,
      'fkrp_gangguan_gastrointestinal' => $fkrp_gangguan_gastrointestinal,
      'fkrp_gangguan_endokrin_metabolik' => $fkrp_gangguan_endokrin_metabolik,
      'fkrp_kegawatdaruratan_onkologi' => $fkrp_kegawatdaruratan_onkologi,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
    );

    //  echo "<pre>";print_r($data);exit();

    $this->load->view('Pengkajian/igd/formulirKriteriaRawatPicu/index', $data);
  }

  public function action_fkrp($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $get_id_fkrp = !empty($post['id_fkrp']) ? $post['id_fkrp'] : $this->pengkajianAwalModel->getIdEmr();

        $data_fkrp = array(
          'id_fkrp'  => $get_id_fkrp,
          'nokun'           => isset($post['nokun']) ? $post['nokun'] : null,
          'fkrp_diagnosis'   => isset($post['fkrp_diagnosis']) ? $post['fkrp_diagnosis'] : null,
          'fkrp_jenis_ruangan'   => isset($post['fkrp_jenis_ruangan']) ? $post['fkrp_jenis_ruangan'] : null,
          'fkrp_ruangan' => isset($post['fkrp_ruangan']) ? $post['fkrp_ruangan'] : null,

          'fkrp_kriteria_eksklusi' => isset($post['fkrp_kriteria_eksklusi']) ? json_encode($post['fkrp_kriteria_eksklusi']) : null,

          'fkrp_gangguan_kardiovaskular' => isset($post['fkrp_gangguan_kardiovaskular']) ? json_encode($post['fkrp_gangguan_kardiovaskular']) : null,
          'fkrp_usia' => isset($post['fkrp_usia']) ? $post['fkrp_usia'] : null,
          'fkrp_frekuensi_nadi' => isset($post['fkrp_frekuensi_nadi']) ? $post['fkrp_frekuensi_nadi'] : null,
          'fkrp_tekanan_darah_sistolik' => isset($post['fkrp_tekanan_darah_sistolik']) ? $post['fkrp_tekanan_darah_sistolik'] : null,
          'fkrp_gangguan_kardiovaskular_lainnya' => isset($post['fkrp_gangguan_kardiovaskular_lainnya']) ? $post['fkrp_gangguan_kardiovaskular_lainnya'] : null,

          'fkrp_gangguan_pernapasan' => isset($post['fkrp_gangguan_pernapasan']) ? json_encode($post['fkrp_gangguan_pernapasan']) : null,
          'fkrp_gangguan_pernapasan_lainnya' => isset($post['fkrp_gangguan_pernapasan_lainnya']) ? $post['fkrp_gangguan_pernapasan_lainnya'] : null,

          'fkrp_gangguan_neurologi' => isset($post['fkrp_gangguan_neurologi']) ? json_encode($post['fkrp_gangguan_neurologi']) : null,
          'fkrp_gangguan_neurologi_lainnya' => isset($post['fkrp_gangguan_neurologi_lainnya']) ? $post['fkrp_gangguan_neurologi_lainnya'] : null,

          'fkrp_gangguan_gastrointestinal' => isset($post['fkrp_gangguan_gastrointestinal']) ? json_encode($post['fkrp_gangguan_gastrointestinal']) : null,
          'fkrp_gangguan_gastrointestinal_lainnya' => isset($post['fkrp_gangguan_gastrointestinal_lainnya']) ? $post['fkrp_gangguan_gastrointestinal_lainnya'] : null,

          'fkrp_gangguan_endokrin_metabolik' => isset($post['fkrp_gangguan_endokrin_metabolik']) ? json_encode($post['fkrp_gangguan_endokrin_metabolik']) : null,
          'fkrp_gangguan_endokrin_metabolik_lainnya' => isset($post['fkrp_gangguan_endokrin_metabolik_lainnya']) ? $post['fkrp_gangguan_endokrin_metabolik_lainnya'] : null,

          'fkrp_perawatan_perioperatif' => isset($post['fkrp_perawatan_perioperatif']) ? $post['fkrp_perawatan_perioperatif'] : null,

          'fkrp_kegawatdaruratan_onkologi' => isset($post['fkrp_kegawatdaruratan_onkologi']) ? json_encode($post['fkrp_kegawatdaruratan_onkologi']) : null,

          'created_at' => date('Y-m-d H:i:s'),
          'fkrp_tanggal' => isset($post['tanggal']) ? date('Y-m-d', strtotime($post['tanggal'])) : "",
          'fkrp_waktu' => isset($post['jam']) ? date('H:i:s', strtotime($post['jam'])) : "",
          'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

        // print_r($data_fkrp);exit();

        if (!empty($post['id_fkrp'])) {
          $this->db->replace('medis.tb_igd_fkrp', $data_fkrp);
          $result = array('status' => 'success', 'pesan' => 'ubah');
        }else {
          $this->db->insert('medis.tb_igd_fkrp', $data_fkrp);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }

  public function action_fkrpri($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();

        $data = array(
          'id' => isset($post['id_fkrpri']) ? $post['id_fkrpri'] : "",
          'id_fkrp'  => isset($post['id_fkrp']) ? $post['id_fkrp'] : "",
          'tanggal'  => isset($post['tanggal']) ? date('Y-m-d', strtotime($post['tanggal'])) : "",
          'jam'  => isset($post['jam']) ? date('H:i', strtotime($post['jam'])) : "",
          'ruangan' => isset($post['ruangan']) ? $post['ruangan'] : "",
          'alat_bantu_napas' => isset($post['alat_bantu_napas']) ? $post['alat_bantu_napas'] : "",
          'kondisi_stabil' => isset($post['kondisi_stabil']) ? $post['kondisi_stabil'] : "",
          'sistolik' => isset($post['sistolik']) ? $post['sistolik'] : "",
          'diastolik' => isset($post['diastolik']) ? $post['diastolik'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'rr' => isset($post['rr']) ? $post['rr'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'gcs' => isset($post['gcs']) ? $post['gcs'] : "",
          'terapi_gagal' => isset($post['terapi_gagal']) ? $post['terapi_gagal'] : "",
          'end_of_life' => isset($post['end_of_life']) ? $post['end_of_life'] : "",
          'menolak_dirawat' => isset($post['menolak_dirawat']) ? $post['menolak_dirawat'] : ""
        );

        if (!empty($post['id_fkrpri'])) {
          $this->db->where('medis.tb_igd_fkrpri.id', $post['id_fkrpri']);
        $this->db->update('medis.tb_igd_fkrpri', $data);
          $result = array('status' => 'success');
        }else {
          $this->db->insert('medis.tb_igd_fkrpri', $data);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }

  public function lihatHistoryFKRPRI() {

    $data = array(
      'id_fkrpri' => $this->input->post('id'),
      'listAlatBantuNapas' => $this->masterModel->referensi(1457),
      'listKondisiStabil' => $this->masterModel->referensi(1458),
      'listTerapiGagal' => $this->masterModel->referensi(1459),
      'listEndOfLife' => $this->masterModel->referensi(1460),
      'listMenolakDirawat' => $this->masterModel->referensi(1461),
      'ruanganIntensif' => $this->masterModel->ruanganIntensif(),
      'detailFKRPRI' => $this->FKRPModel->detailFKRPRI($this->input->post('id'))
      // 'detailFKPKRI' => $this->FKRIModel->detailFKPKRI($this->input->post('id'))
    );

    $this->load->view('Pengkajian/igd/formulirKriteriaRawatPicu/modalViewEditFKRPRI', $data);
  }
}


/* End of file FormulirKriteriaRawatPicu.php */
/* Location: ./application/controllers/igd/FormulirKriteriaRawatPicu.php */
