<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Regkan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(1,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('pengkajianAwalModel','masterModel','RegkanModel'));
  }

  public function index()
  {

    $perluasanTumor = $this->pengkajianAwalModel->masterVariableREGKAN(4);
    $LateralitasTumor = $this->pengkajianAwalModel->masterVariableREGKAN(5);
    $metastasisJauh = $this->pengkajianAwalModel->masterVariableREGKAN(6);

    $data = array(
      'title'             => 'Halaman Pelayanan Kanker',
      'isi'               => 'Regkan/index',
      'perluasanTumor'    => $perluasanTumor,
      'LateralitasTumor'  => $LateralitasTumor,
      'metastasisJauh'    => $metastasisJauh,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function listPasien()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $listPasien = $this->RegkanModel->dataTableListPasien();

    $data = array();
    $no = $_POST['start'];
    foreach ($listPasien as $lp) {
      $no++;

      $tombol = "<div class='text-center'><button type='button' class='badge badge-warning tombolPilihLayananKanker' data-norm='" . $lp->NORM . "' data-nmpasien='" . $lp->NAMA_PASIEN . "' data-encver='" . $lp->ID_ENCOUNTER_VER . "' data-perluasan='" . $lp->KODE_PERLUASAN_TUMOR . "' data-lateralitas='" . $lp->KODE_LATERALITAS_TUMOR . "' data-metas1='" . $lp->KODE_METAS_1 . "' data-metas2='" . $lp->KODE_METAS_2 . "' data-metas3='" . $lp->KODE_METAS_3 . "' data-metas4='" . $lp->KODE_METAS_4 . "' data-enc='" . $lp->ID_ENCOUNTER . "' data-idihspasien='" . $lp->ID_IHS_PASIEN . "' data-tgldaftar='" . $lp->TANGGAL_DAFTAR . "'><span style='color: black;'>Pilih</span></button></div>";

      $data[] = array(
        $no,
        $lp->NORM,
        $lp->NAMA_PASIEN,
        $lp->TANGGAL_DAFTAR,
        $tombol,
      );
    }
    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $this->RegkanModel->total_countListPasien(),
      "recordsFiltered" => $this->RegkanModel->filter_countListPasien(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function simpanPerLatMet()
  {
    $post = $this->input->post();
    $data = array(
      'id_encounter'           => isset($post['encLayananKanker']) ? $post['encLayananKanker'] : NULL,
      'jenis_condition'        => 6,
      'id_ihs_pasien'          => isset($post['idihspasienLayananKanker']) ? $post['idihspasienLayananKanker'] : NULL,
      'nm_pasien'              => isset($post['nmpasienLayananKanker']) ? $post['nmpasienLayananKanker'] : NULL,
      'kode_perluasan_tumor'   => isset($_POST['pilihperluasanTumor']) && $_POST['pilihperluasanTumor'] !== '' ? $_POST['pilihperluasanTumor'] : NULL,
      'kode_lateralitas_tumor' => isset($_POST['pilihLateralitasTumor']) && $_POST['pilihLateralitasTumor'] !== '' ? $_POST['pilihLateralitasTumor'] : NULL,
      'kode_metastasis_jauh_1' => isset($_POST['pilihMetastasisJauh1']) && $_POST['pilihMetastasisJauh1'] !== '' ? $_POST['pilihMetastasisJauh1'] : NULL,
      'kode_metastasis_jauh_2' => isset($_POST['pilihMetastasisJauh2']) && $_POST['pilihMetastasisJauh2'] !== '' ? $_POST['pilihMetastasisJauh2'] : NULL,
      'kode_metastasis_jauh_3' => isset($_POST['pilihMetastasisJauh3']) && $_POST['pilihMetastasisJauh3'] !== '' ? $_POST['pilihMetastasisJauh3'] : NULL,
      'kode_metastasis_jauh_4' => isset($_POST['pilihMetastasisJauh4']) && $_POST['pilihMetastasisJauh4'] !== '' ? $_POST['pilihMetastasisJauh4'] : NULL,
      'tgl_jam'                => isset($post['tgldaftarLayananKanker']) ? $post['tgldaftarLayananKanker'] : NULL,
      'jenis_pengiriman'       => 1,
      );

    if($post['encverLayananKanker'] == ""){
      echo "tidak ada";
      $this->db->insert('db_regkan.tb_condition_v2', $data);
    }else{
      echo "ada";
      $this->db->where('tb_condition_v2.id_encounter', $post['encLayananKanker']);
      $this->db->where('tb_condition_v2.jenis_condition', 6);
      $this->db->update('db_regkan.tb_condition_v2', $data);
    }
    echo '<pre>'; print_r($data); echo '</pre>'; exit();
  }

}
