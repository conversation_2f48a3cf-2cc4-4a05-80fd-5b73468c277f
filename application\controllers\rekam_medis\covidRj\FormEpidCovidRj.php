<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormEpidCovidRj extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/covid19/EpidCovidModel'));
    }

    public function index()
    {
        $norm = $this->uri->segment(5);
        $nopen = $this->uri->segment(6);
        $nokun = $this->uri->segment(7);
        $data = array(
            'norm' => $norm,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'yatidak' => $this->masterModel->referensi(1576),
            'pertanyaan' => $this->masterModel->referensi(1580),
            'kriteriakasus' => $this->masterModel->referensi(1583),
            'gejala' => $this->masterModel->referensi(1636),
            'apd' => $this->masterModel->referensi(1603),
            'statusakhir' => $this->masterModel->referensi(1582),
            'jenis_kelamin' => $this->masterModel->referensi(969),
            'tujuanPengobatan' => $this->masterModel->tujuanPengobatan(),
            'historyEpidCovid' => $this->EpidCovidModel->historyEpidCovid($norm),
        );
        $this->load->view('rekam_medis/covidRj/FormEpidCovidRj', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();

                $dataEpidCovid = array(
                    // 'id' => $post['id'],
                    'norm' => $post['norm'],
                    'nokun' => $post['nokun'],
                    'kriteria_kasus' => $post['kriteria_kasus'],
                    'pewawancara' => $post['pewawancara'],
                    'tempat_tugas' => $post['tempat_tugas'],
                    'hp_pewawancara' => $post['hp_pewawancara'],
                    'tgl_wawancara' => $post['tgl_wawancara'],
                    'tanggal' => $post['tanggal'],
                    'gejala' => $post['gejala'],
                    'demam' => $post['demam'],
                    'riwayat_demam' => $post['riwayat_demam'],
                    'batuk' => $post['batuk'],
                    'pilek' => $post['pilek'],
                    'sakit_tenggorokan' => $post['sakit_tenggorokan'],
                    'sesak_nafas' => $post['sesak_nafas'],
                    'sakit_kepala' => $post['sakit_kepala'],
                    'lemah' => $post['lemah'],
                    'nyeri_otot' => $post['nyeri_otot'],
                    'mual_muntah' => $post['mual_muntah'],
                    'nyeri_abdomen' => $post['nyeri_abdomen'],
                    'diare' => $post['diare'],
                    'info_klinis_lain' => $post['info_klinis_lain'],
                    'hamil' => $post['hamil'],
                    'diabetes' => $post['diabetes'],
                    'jantung' => $post['jantung'],
                    'hipertensi' => $post['hipertensi'],
                    'keganasan' => $post['keganasan'],
                    'imunologi' => $post['imunologi'],
                    'gagal_ginjal' => $post['gagal_ginjal'],
                    'gagal_hati' => $post['gagal_hati'],
                    'ppok' => $post['ppok'],
                    'kondisi_penyerta' => $post['kondisi_penyerta'],
                    'dirawat_dirs' => $post['dirawat_dirs'],
                    'nama_rsterakhir' => $post['nama_rsterakhir'],
                    'tanggal_masukrs' => $post['tanggal_masukrs'],
                    'ruang_rawat' => $post['ruang_rawat'],
                    'rawat_icu' => $post['rawat_icu'],
                    'intubasi' => $post['intubasi'],
                    'emco' => $post['emco'],
                    'namars_sebelumnya' => $post['namars_sebelumnya'],
                    'status_pasien' => $post['status_pasien'],
                    'tgl_meninggal' => $post['tgl_meninggal'],
                    'pneumonia' => $post['pneumonia'],
                    'ards' => $post['ards'],
                    'diagnosa_lainnya' => $post['diagnosa_lainnya'],
                    'diagnosis_pernafasan' => $post['diagnosis_pernafasan'],
                    'diagnosis_nafaslain' => $post['diagnosis_nafaslain'],
                    'luar_negeri' => $post['luar_negeri'],
                    'negara_ln' => $post['negara_ln'],
                    'negara_ln_nd' => $post['negara_ln_nd'],
                    'kota_ln' => $post['kota_ln'],
                    'kota_ln_nd' => $post['kota_ln_nd'],
                    'tgl_perjalanan' => $post['tgl_perjalanan'],
                    'tgl_perjalanan_nd' => $post['tgl_perjalanan_nd'],
                    'tgl_tiba' => $post['tgl_tiba'],
                    'tgl_tiba_nd' => $post['tgl_tiba_nd'],
                    'perjalanan' => $post['perjalanan'],
                    'provinsi' => $post['provinsi'],
                    'provinsi_nd' => $post['provinsi_nd'],
                    'kota_area' => $post['kota_area'],
                    'kota_area_nd' => $post['kota_area_nd'],
                    'tgl_perjalanan_area' => $post['tgl_perjalanan_area'],
                    'tgl_perjalanan_area_nd' => $post['tgl_perjalanan_area_nd'],
                    'tgl_tiba_area' => $post['tgl_tiba_area'],
                    'tgl_tiba_area_nd' => $post['tgl_tiba_area_nd'],
                    'kunjungan_faskes' => $post['kunjungan_faskes'],
                    'nama_rs' => $post['nama_rs'],
                    'nama_rs_nd' => $post['nama_rs_nd'],
                    'kota_faskes' => $post['kota_faskes'],
                    'kota_faskes_nd' => $post['kota_faskes_nd'],
                    'provinsi_faskes' => $post['provinsi_faskes'],
                    'provinsi_faskes_nd' => $post['provinsi_faskes_nd'],
                    'tgl_kunjungan_faskes' => $post['tgl_kunjungan_faskes'],
                    'tgl_kunjungan_faskes_nd' => $post['tgl_kunjungan_faskes_nd'],
                    'kunjung_psr_hewan' => $post['kunjung_psr_hewan'],
                    'lokasi_psr_hewan' => $post['lokasi_psr_hewan'],
                    'lokasi_psr_hewan_nd' => $post['lokasi_psr_hewan_nd'],
                    'kota_psr_hean' => $post['kota_psr_hean'],
                    'kota_psr_hean_nd' => $post['kota_psr_hean_nd'],
                    'prov_psr_hewan' => $post['prov_psr_hewan'],
                    'prov_psr_hewan_nd' => $post['prov_psr_hewan_nd'],
                    'tgl_kunj_psr_hewan' => $post['tgl_kunj_psr_hewan'],
                    'tgl_kunj_psr_hewan_nd' => $post['tgl_kunj_psr_hewan_nd'],
                    'kontak_pengawasan' => $post['kontak_pengawasan'],
                    'nama_kontak_pengawasan' => $post['nama_kontak_pengawasan'],
                    'nama_kontak_pengawasan_nd' => $post['nama_kontak_pengawasan_nd'],
                    'alamat_kontak_pengawasan' => $post['alamat_kontak_pengawasan'],
                    'alamat_kontak_pengawasan_nd' => $post['alamat_kontak_pengawasan_nd'],
                    'hubungan_kontak_pengawasan' => $post['hubungan_kontak_pengawasan'],
                    'hubungan_kontak_pengawasan_nd' => $post['hubungan_kontak_pengawasan_nd'],
                    'tgl_kontak_pertama_pengawasan' => $post['tgl_kontak_pertama_pengawasan'],
                    'tgl_kontak_pertama_pengawasan_nd' => $post['tgl_kontak_pertama_pengawasan_nd'],
                    'tgl_kontak_terakhir_pengawasan' => $post['tgl_kontak_terakhir_pengawasan'],
                    'tgl_kontak_terakhir_pengawasan_nd' => $post['tgl_kontak_terakhir_pengawasan_nd'],
                    'kontak_konfirmasi' => $post['kontak_konfirmasi'],
                    'nama_kontak_konfirmasi' => $post['nama_kontak_konfirmasi'],
                    'nama_kontak_konfirmasi_nd' => $post['nama_kontak_konfirmasi_nd'],
                    'alamat_kontak_konfirmasi' => $post['alamat_kontak_konfirmasi'],
                    'alamat_kontak_konfirmasi_nd' => $post['alamat_kontak_konfirmasi_nd'],
                    'hubungan_kontak_konfirmasi' => $post['hubungan_kontak_konfirmasi'],
                    'hubungan_kontak_konfirmasi_nd' => $post['hubungan_kontak_konfirmasi_nd'],
                    'tgl_kontak_pertama_konfirmasi' => $post['tgl_kontak_pertama_konfirmasi'],
                    'tgl_kontak_pertama_konfirmasi_nd' => $post['tgl_kontak_pertama_konfirmasi_nd'],
                    'tgl_kontak_terakhir_konfirmasi' => $post['tgl_kontak_terakhir_konfirmasi'],
                    'tgl_kontak_terakhir_konfirmasi_nd' => $post['tgl_kontak_terakhir_konfirmasi_nd'],
                    'cluster_ispa' => $post['cluster_ispa'],
                    'petugas_kesehatan' => $post['petugas_kesehatan'],
                    'apd' => $post['apd'],
                    'aerosol' => $post['aerosol'],
                    'aerosol_sebutkan' => $post['aerosol_sebutkan'],
                    'aerosol_lain' => $post['aerosol_lain'],
                    'tgl_spesimen' => $post['tgl_spesimen'],
                    'tempat_periksa_spesi' => $post['tempat_periksa_spesi'],
                    'hasil_spesi' => $post['hasil_spesi'],

                    'tgl_naso' => $post['tgl_naso'],
                    'tempat_periksa_naso' => $post['tempat_periksa_naso'],
                    'hasil_naso' => $post['hasil_naso'],
                    'tgl_oro' => $post['tgl_oro'],
                    'tempat_periksa_oro' => $post['tempat_periksa_oro'],
                    'hasil_oro' => $post['hasil_oro'],

                    'tgl_sputum' => $post['tgl_sputum'],
                    'tempat_periksa_sputum' => $post['tempat_periksa_sputum'],
                    'hasil_sputum' => $post['hasil_sputum'],
                    'tgl_serum' => $post['tgl_serum'],
                    'tempat_periksa_serum' => $post['tempat_periksa_serum'],
                    'hasil_serum' => $post['hasil_serum'],
                    'tgl_darah' => $post['tgl_darah'],
                    'tempat_periksa_darah' => $post['tempat_periksa_darah'],
                    'hasil_darah' => $post['hasil_darah'],
                    'tgl_serum_lain' => $post['tgl_serum_lain'],
                    'tempat_serum_lain' => $post['tempat_serum_lain'],
                    'hasil_serum_lain' => $post['hasil_serum_lain'],
                    'tgl_periksa_lain' => $post['tgl_periksa_lain'],
                    'tempat_periksa_lain' => $post['tempat_periksa_lain'],
                    'hasil_lain' => $post['hasil_lain'],
                    'oleh' => $this->session->userdata("id")
                );
                // echo '<pre>';
                // print_r($dataEpidCovid);
                // exit();

                if (!empty($post['id'])) {
                    $this->db->where('keperawatan.tb_epidemologi_covid.id', $post['id']);
                    $this->db->update('keperawatan.tb_epidemologi_covid', $dataEpidCovid);

                    $dataCovRj = array();
                    $indexCovRj = 0;
                    if (isset($post['nama_kontak_erat'])) {
                        foreach ($post['nama_kontak_erat'] as $input) {
                            if ($post['nama_kontak_erat'][$indexCovRj] != "") {
                                array_push(
                                    $dataCovRj,
                                    array(
                                        'nokun'                 => $post['nokun'],
                                        'id_epid_covid'         => $post['id'],
                                        'nama_kontak_erat'      => $post['nama_kontak_erat'][$indexCovRj],
                                        'umur_kontak_erat'      => $post['umur_kontak_erat'][$indexCovRj],
                                        'jenkel'                => $post['jenkel'][$indexCovRj],
                                        'hub_kontak_erat'       => $post['hub_kontak_erat'][$indexCovRj],
                                        'alamat_kontak_erat'    => $post['alamat_kontak_erat'][$indexCovRj],
                                        'telp_kontak_erat'      => $post['telp_kontak_erat'][$indexCovRj],
                                        'aktifitas_kontak'      => $post['aktifitas_kontak'][$indexCovRj],
                                        'oleh'                  => $this->session->userdata("id")
                                    )
                                );
                            }
                            $indexCovRj++;
                        }
                    }
                } else {
                    $getIdCov = $this->EpidCovidModel->insertEpidCovid($dataEpidCovid);

                    $dataCovRj = array();
                    $indexCovRj = 0;
                    if (isset($post['nama_kontak_erat'])) {
                        foreach ($post['nama_kontak_erat'] as $input) {
                            if ($post['nama_kontak_erat'][$indexCovRj] != "") {
                                array_push(
                                    $dataCovRj,
                                    array(
                                        'nokun'                     => $post['nokun'],
                                        'id_epid_covid'             => $getIdCov,
                                        'nama_kontak_erat'          => $post['nama_kontak_erat'][$indexCovRj],
                                        'umur_kontak_erat'          => $post['umur_kontak_erat'][$indexCovRj],
                                        'jenkel'                    => $post['jenkel'][$indexCovRj],
                                        'hub_kontak_erat'           => $post['hub_kontak_erat'][$indexCovRj],
                                        'alamat_kontak_erat'        => $post['alamat_kontak_erat'][$indexCovRj],
                                        'telp_kontak_erat'          => $post['telp_kontak_erat'][$indexCovRj],
                                        'aktifitas_kontak'          => $post['aktifitas_kontak'][$indexCovRj],
                                        'oleh'                      => $this->session->userdata("id")
                                    )
                                );
                            }
                            $indexCovRj++;
                        }
                    }
                }

                $this->db->trans_begin();
                if (!empty($post['id'])) {
                    $this->db->delete('keperawatan.tb_kontak_epid_covid', array('id_epid_covid' => $post['id']));
                    foreach ($dataCovRj as $key => $value) {
                        $this->db->replace('keperawatan.tb_kontak_epid_covid', $value, 'nokun');
                    }
                } else {
                    $result = array('status' => 'failed');
                    if (isset($post['nama_kontak_erat'])) {
                        $this->db->insert_batch('keperawatan.tb_kontak_epid_covid', $dataCovRj);
                    }
                }

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            }
        }
    }


    public function viewEditEpidCovidRj($norm, $nopen, $nokun, $id)
    {
        $id = $id;
        $norm = $norm;
        $nopen = $nopen;
        $nokun = $nokun;
        $getHistoryEpidCovid = $this->EpidCovidModel->historyEpidCovid($norm);
        $getDataEpidCovid = $this->EpidCovidModel->getDataEpidCovid($id);
        $HistoryEpidCovidArr = $this->EpidCovidModel->historyDetailKontakEratArr($getDataEpidCovid['ID']);

        $data = array(
            'norm' => $norm,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'yatidak' => $this->masterModel->referensi(1576),
            'pertanyaan' => $this->masterModel->referensi(1580),
            'kriteriakasus' => $this->masterModel->referensi(1583),
            'apd' => $this->masterModel->referensi(1603),
            'statusakhir' => $this->masterModel->referensi(1582),
            'jenis_kelamin' => $this->masterModel->referensi(969),
            'gejala' => $this->masterModel->referensi(1636),
            'getDataEpidCovid' => $getDataEpidCovid,
            'HistoryEpidCovid' => $getHistoryEpidCovid,
            'EpidCovid_arr' => $HistoryEpidCovidArr,
            'id' => $id
        );
        $this->load->view('rekam_medis/covidRj/FormEpidCovidRj', $data);
    }
}
