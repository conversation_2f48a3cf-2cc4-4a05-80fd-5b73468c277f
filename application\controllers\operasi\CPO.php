<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CPO extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array(
            'masterModel',
            'pengkajianAwalModel',
            'rekam_medis/rawat_inap/operasi/CPOModel',
        ));
    }

    public function CPO()
    {
        $norm = $this->uri->segment(3);
        $nopen = $this->uri->segment(7);
        $nokun = $this->uri->segment(5);

        $data = array(
            'pasien'                => $pasien,
            'nopen'                 => $nopen,
            'norm'                  => $norm,
            'nokun'                 => $nokun,
            'getNomr'               => $this->pengkajianAwalModel->getNomr($nokun),
            'dokumen'               => $this->masterModel->referensi(1830),
            'pilihCPO'              => $this->CPOModel->pilihCPO($norm),
            // 'konsultasi_lainnya'    => $this->masterModel->referensi(308),
            // 'jadwal_dari_ok'       => $this->masterModel->referensi(308),
        );
        $this->load->view('Pengkajian/operasi/CPO', $data);
    }

    public function pilihCPO()
    {
        $idCPO = $this->input->post('idCPO');
        $getCPO = $this->CPOModel->getCPO($idCPO);
        if (!$getCPO) {
            echo json_encode(['status' => 'error', 'message' => 'CPO not found']);
            return;
        }
        $pasien = $this->pengkajianAwalModel->getNomr($getCPO['NOKUN']);
        $dokumenSemua = $this->masterModel->referensi(1830);

        $dokumenDimiliki = $this->CPOModel->getDokumenByCPO($idCPO);
        $dokumenDimilikiIds = array_column($dokumenDimiliki, 'ID_DOKUMEN');

        $dokumenTersedia = array_filter($dokumenSemua, function ($dokumen) use ($dokumenDimilikiIds) {
            return !in_array($dokumen['id_variabel'], $dokumenDimilikiIds);
        });

        $data = array(
            'getCPO' => $getCPO,
            'pasien' => $pasien,
            'dokumen' => $dokumenTersedia,
        );

        $this->load->view('rekam_medis/rawat_inap/operasi/CPO/pilihCPO', $data);
    }


    public function tableDokumen()
    {
        $idCPO = $this->input->post('idCPO');
        $getCPO = $this->CPOModel->getCPO($idCPO);
        if (!$getCPO) {
            echo json_encode(['status' => 'error', 'message' => 'CPO not found']);
            return;
        }
        $pasien = $this->pengkajianAwalModel->getNomr($getCPO['NOKUN']);
        $data = array(
            'getCPO'            => $getCPO,
            'pasien'            => $pasien,
            'tableDokumen'      => $this->CPOModel->tableDokumen($idCPO),
        );

        $this->load->view('rekam_medis/rawat_inap/operasi/CPO/tableDokumen', $data);
    }
    public function tableDokumenVerif()
    {
        $idCPO = $this->input->post('idCPO');
        $getCPO = $this->CPOModel->getCPO($idCPO);
        if (!$getCPO) {
            echo json_encode(['status' => 'error', 'message' => 'CPO not found']);
            return;
        }
        $pasien = $this->pengkajianAwalModel->getNomr($getCPO['NOKUN']);
        $data = array(
            'getCPO'            => $getCPO,
            'pasien'            => $pasien,
            'tableDokumen'      => $this->CPOModel->tableDokumen($idCPO),
        );

        $this->load->view('rekam_medis/rawat_inap/operasi/CPO/tableDokumenVerif', $data);
    }

    public function simpanCPO()
    {
        $nomr = $this->input->post('nomr');
        $nokun = $this->input->post('nokun');
        $oleh = $this->input->post('oleh');
        $idCPO = $this->input->post('idCPO');
        $dokumen = $this->input->post('dokumen');
        $keterangan = $this->input->post('keterangan');
        // $tanggal = $this->input->post('tanggal');

        $dataOperasi = [
            'nokun' => $nokun,
            'nomr' => $nomr,
            'oleh' => $oleh
        ];

        if (!empty($idCPO)) {
            $dataDetail = [];
            for ($i = 0; $i < count($dokumen); $i++) {
                $dataDetail[] = [
                    'id_cpo' => $idCPO,
                    'nama_dokumen' => $dokumen[$i],
                    'keterangan' => $keterangan[$i],
                    'oleh' => $oleh
                ];
            }
            $this->CPOModel->insert_detail($dataDetail);
            $response = ['status' => 'success', 'message' => 'Data berhasil disimpan (Tambah)'];
        } else {
            $insertCPO = $this->CPOModel->insert_cpo($dataOperasi);

            if ($insertCPO) {
                $dataDetail = [];
                for ($i = 0; $i < count($dokumen); $i++) {
                    $dataDetail[] = [
                        'id_cpo' => $insertCPO,
                        'nama_dokumen' => $dokumen[$i],
                        'keterangan' => $keterangan[$i],
                        'oleh' => $oleh,
                    ];
                }
                $this->CPOModel->insert_detail($dataDetail);
                $response = ['status' => 'success', 'message' => 'Data berhasil disimpan (Simpan Baru)'];
            } else {
                $response = ['status' => 'error', 'message' => 'Data gagal disimpan'];
            }
        }

        echo json_encode($response);
    }

    public function verifDokumen()
    {
        $id = $this->input->post('id');
        $verified_by = $this->input->post('verifiedby');
        if (isset($id)) {
            $this->CPOModel->verifDokumen($id, $verified_by);
            $response = ['status' => 'success', 'message' => 'Verifikasi Dokumen Berhasil!'];
        } else {
            $response = ['status' => 'error', 'message' => 'Verifikasi Dokumen Gagal!'];
        }
        echo json_encode($response);
    }
    public function hapusDokumen()
    {
        $id = $this->input->post('iddoc');
        $by = $this->input->post('by');
        if (isset($id)) {
            $this->CPOModel->hapusDokumen($id, $by);
            $response = ['status' => 'success', 'message' => 'Hapus Dokumen Berhasil!'];
        } else {
            $response = ['status' => 'error', 'message' => 'Hapus Dokumen Gagal!'];
        }
        echo json_encode($response);
    }
}
