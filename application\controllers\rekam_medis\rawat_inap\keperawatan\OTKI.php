<?php
defined('BASEPATH') or exit('No direct script access allowed');

class OTKI extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'FormulirTriaseModel',
        'rekam_medis/rawat_inap/keperawatan/OTKIModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
      )
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $nokun = $this->uri->segment(2);

    $data = array(
      'vitalTriaseTerbaru' => $this->FormulirTriaseModel->vitalTriaseTerbaru($nokun),
      'tindakanKeperawatan' => $this->masterModel->referensi(1649),
      'pasienDiantar' => $this->masterModel->referensi(1650),
      'dengan' => $this->masterModel->referensi(1651),
      'cekPersiapan' => $this->masterModel->referensi(1652),
      'demo' => $this->masterModel->referensi(1653),
      'observasi' => $this->masterModel->referensi(1654),
      'mengambilSampel' => $this->masterModel->referensi(1658),
      'formPerjanjian' => $this->masterModel->referensi(1660),
    );

    if (isset($id)) {
      // Form detail
      $data['id'] = $id;
      $data['detail'] = $this->OTKIModel->history(null, null, $id);
      $data['isiTindakanKeperawatan'] = isset($data['detail'][0]['tindakan_keperawatan']) ? explode('-', $data['detail'][0]['tindakan_keperawatan']) : null;
      $data['isiPasienDiantar'] = isset($data['detail'][0]['pasien_diantar']) ? explode('-', $data['detail'][0]['pasien_diantar']) : null;
      $data['isiDengan'] = isset($data['detail'][0]['dengan']) ? explode('-', $data['detail'][0]['dengan']) : null;
      $data['isiCekPersiapan'] = isset($data['detail'][0]['cek_persiapan']) ? explode('-', $data['detail'][0]['cek_persiapan']) : null;
      $data['isiDemo'] = isset($data['detail'][0]['demo']) ? explode('-', $data['detail'][0]['demo']) : null;
      $data['isiObservasi'] = isset($data['detail'][0]['observasi']) ? explode('-', $data['detail'][0]['observasi']) : null;
      $data['isiMengambilSampel'] = isset($data['detail'][0]['mengambil_sampel']) ? explode('-', $data['detail'][0]['mengambil_sampel']) : null;
      $data['isiFormPerjanjian'] = isset($data['detail'][0]['form_perjanjian']) ? explode('-', $data['detail'][0]['form_perjanjian']) : null;
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKI/detail', $data);
    } else {
      // Form tambah
      $data['nokun'] = $nokun;
      $data['jumlah'] = $this->OTKIModel->history($nokun, 'jumlah', null);
      $data['pasien'] = $this->pengkajianAwalModel->getNomr($nokun);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKI/index', $data);
    }
  }

  public function simpan($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah') {
        $rules = $this->OTKeperawatanModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $nokun = isset($post['nokun']) ? $post['nokun'] : null;
          $oleh = $this->session->userdata['id'];
          $status = 1;
          // echo '<pre>';print_r($post);exit();

          // Awal simpan ke observasi dan tindakan keperawatan
          $dataObservasi = array(
            'nokun' => $nokun,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'perifer' => isset($post['perifer']) ? $post['perifer'] : null,
            'tindakan_keperawatan' => isset($post['tindakan_keperawatan']) ? implode('-', $post['tindakan_keperawatan']) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataObservasi);exit();
          $idObservasi = $this->OTKeperawatanModel->simpanObservasi($dataObservasi);
          // Akhir simpan ke observasi dan tindakan keperawatan

          // Awal simpan ke tanda vital
          $dataTandaVital = array(
            'data_source' => 32,
            'ref' => $idObservasi,
            'nomr' => isset($post['nomr']) ? $post['nomr'] : null,
            'nokun' => $nokun,
            'td_sistolik' => isset($post['td_sistolik']) ? round($post['td_sistolik']) : null,
            'td_diastolik' => isset($post['td_diastolik']) ? round($post['td_diastolik'], 2) : null,
            'nadi' => isset($post['nadi']) ? round($post['nadi']) : null,
            'pernapasan' => isset($post['pernapasan']) ? round($post['pernapasan']) : null,
            'suhu' => isset($post['suhu']) ? round($post['suhu']) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataTandaVital);exit();
          $this->OTKeperawatanModel->simpanTandaVital($dataTandaVital);
          // Akhir simpan ke tanda vital

          // Awal simpan ke observasi dan tindakan keperawatan iodium
          $data = array(
            'id_observasi_tindakan' => $idObservasi,
            'protese' => isset($post['protese']) ? $post['protese'] : null,
            'dosis_pemberian_1' => isset($post['dosis_pemberian_1']) ? round($post['dosis_pemberian_1'], 2) : null,
            'tgl_pemberian_1' => isset($post['tgl_pemberian_1']) ? $post['tgl_pemberian_1'] : null,
            'dosis_pemberian_2' => isset($post['dosis_pemberian_2']) ? round($post['dosis_pemberian_2'], 2) : null,
            'tgl_pemberian_2' => isset($post['tgl_pemberian_2']) ? $post['tgl_pemberian_2'] : null,
            'dosis_pemberian_3' => isset($post['dosis_pemberian_3']) ? round($post['dosis_pemberian_3'], 2) : null,
            'tgl_pemberian_3' => isset($post['tgl_pemberian_3']) ? $post['tgl_pemberian_3'] : null,
            'dosis_pemberian_4' => isset($post['dosis_pemberian_4']) ? round($post['dosis_pemberian_4'], 2) : null,
            'tgl_pemberian_4' => isset($post['tgl_pemberian_4']) ? $post['tgl_pemberian_4'] : null,
            'dosis_pemberian_5' => isset($post['dosis_pemberian_5']) ? round($post['dosis_pemberian_5'], 2) : null,
            'tgl_pemberian_5' => isset($post['tgl_pemberian_5']) ? $post['tgl_pemberian_5'] : null,
            'dosis_iodium_1' => isset($post['dosis_iodium_1']) ? round($post['dosis_iodium_1'], 2) : null,
            'tgl_iodium_1' => isset($post['tgl_iodium_1']) ? $post['tgl_iodium_1'] : null,
            'dosis_iodium_2' => isset($post['dosis_iodium_2']) ? round($post['dosis_iodium_2'], 2) : null,
            'tgl_iodium_2' => isset($post['tgl_iodium_2']) ? $post['tgl_iodium_2'] : null,
            'dosis_iodium_3' => isset($post['dosis_iodium_3']) ? round($post['dosis_iodium_3'], 2) : null,
            'tgl_iodium_3' => isset($post['tgl_iodium_3']) ? $post['tgl_iodium_3'] : null,
            'dosis_iodium_4' => isset($post['dosis_iodium_4']) ? round($post['dosis_iodium_4'], 2) : null,
            'tgl_iodium_4' => isset($post['tgl_iodium_4']) ? $post['tgl_iodium_4'] : null,
            'dosis_iodium_5' => isset($post['dosis_iodium_5']) ? round($post['dosis_iodium_5'], 2) : null,
            'tgl_iodium_5' => isset($post['tgl_iodium_5']) ? $post['tgl_iodium_5'] : null,
            'cairan_masuk' => isset($post['cairan_masuk']) ? round($post['cairan_masuk'], 2) : null,
            'cairan_keluar' => isset($post['cairan_keluar']) ? round($post['cairan_keluar'], 2) : null,
            'pasien_diantar' => isset($post['pasien_diantar']) ? implode('-', $post['pasien_diantar']) : null,
            'dengan' => isset($post['dengan']) ? implode('-', $post['dengan']) : null,
            'cek_persiapan' => isset($post['cek_persiapan']) ? implode('-', $post['cek_persiapan']) : null,
            'demo' => isset($post['demo']) ? implode('-', $post['demo']) : null,
            'observasi' => isset($post['observasi']) ? implode('-', $post['observasi']) : null,
            'banyaknya_obat' => isset($post['banyaknya_obat']) ? round($post['banyaknya_obat'], 2) : null,
            'memberi_obat' => isset($post['memberi_obat']) ? $post['memberi_obat'] : null,
            'mencatat_urin' => isset($post['mencatat_urin']) ? $post['mencatat_urin'] : null,
            'mengambil_sampel' => isset($post['mengambil_sampel']) ? implode('-', $post['mengambil_sampel']) : null,
            'pengukuran_radiasi' => isset($post['pengukuran_radiasi']) ? round($post['pengukuran_radiasi'], 2) : null,
            'form_perjanjian' => isset($post['form_perjanjian']) ? implode('-', $post['form_perjanjian']) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($data);exit();
          $this->OTKIModel->simpan($data);
          // Akhir simpan ke observasi dan tindakan keperawatan iodium

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
          echo json_encode($result);
        }
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKI/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->OTKIModel->history($nokun, 'tabel', null);
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H:i', strtotime($h->jam)),
        $h->pengisi,
        date('d-m-Y, H:i:s', strtotime($h->updated_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-otki' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-otki' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-otki' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-otki' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $idObservasi = $post['id'];

    $dataObservasi = array(
      'updated_at' => date('Y-m-d H:i:s'),
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahObservasi($idObservasi, $dataObservasi);

    $dataTandaVital = array(
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahTandaVital($idObservasi, '32', $dataTandaVital);

    $dataOTKB = array(
      'status' => 0,
    );
    $this->OTKIModel->ubah($idObservasi, $dataOTKB);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}

/* End of file OTKI.php */
/* Location: ./application/controllers/rawat_inap/keperawatan/OTKI.php */