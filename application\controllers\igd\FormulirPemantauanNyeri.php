<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormulirPemantauanNyeri extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'formulirPemantauanNyeriModel'));
  }

  public function tblPemantauanNyeriIgd()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listPemantauanNyeri = $this->formulirPemantauanNyeriModel->listHistoryPemantauanNyeri($nomr);

    $data = array();
    $no = 1;
    foreach ($listPemantauanNyeri->result() as $lpny) {
      $data[] = array(
        $no,
        $lpny->NOKUN,
        $lpny->NAMA_USER,
        $lpny->SUMBER,
        $lpny->METODE,
        $lpny->SKOR != '' ? $lpny->SKOR : '0' ,
        date("d-m-Y H:i:s",strtotime($lpny->TANGGAL)),
        '<a href="#viewPemantauanNyeriIgd" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$lpny->ID.'" data-jenis="'.$lpny->JENIS.'"><i class="fa fa-eye"></i> View</a>'
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listPemantauanNyeri->num_rows(),
      "recordsFiltered" => $listPemantauanNyeri->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function simpanPemantauanNyeri()
  {
    $data = array(
      'nokun'                   => $this->input->post('nokunIgd'),
      'metode'                  => $this->input->post('skrining_nyeriIgd'),
      'skor'                    => $this->input->post('skala_nyeri_Igd'),
      'farmakologi'             => $this->input->post('farmakologi_Igd'),
      'non_farmakologi'         => $this->input->post('non_farmakologi_Igd'),
      'efek_samping'            => $this->input->post('efek_samping_Igd'),
      'keterangan_efek_samping' => $this->input->post('efek_samping_lain_Igd'),
      'tanggal'                 => $this->input->post('tanggal'),
      'oleh'                    => $this->session->userdata('id'),
    );

    $this->formulirPemantauanNyeriModel->simpanPNyeri($data);
  }

  public function detailHistoryPemantauanNyeri()
  {
    $id = $this->input->post('id');
    $jenis = $this->input->post('jenis');


    if($jenis == 1){
      $dHPN = $this->formulirPemantauanNyeriModel->dHistoryPemantauanNyeriPengkajian($id);
    }elseif($jenis == 2){
      $dHPN = $this->formulirPemantauanNyeriModel->dHistoryPemantauanNyeriCPPT($id);
    }else{
      $dHPN = $this->formulirPemantauanNyeriModel->dHistoryPemantauanNyeriIGD($id);
    }


    $skriningNyeri              = $this->masterModel->referensi(7);
    $skalaNyeriNRS              = $this->masterModel->referensi(114);
    $skalaNyeriWBR              = $this->masterModel->referensi(115);
    $skalaNyeriFLACC            = $this->masterModel->referensi(123);
    $skalaNyeriBPS              = $this->masterModel->referensi(133);
    $efeksampingNRS             = $this->masterModel->referensi(118);
    $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
    $pengkajianNyeriQuality     = $this->masterModel->referensi(9);
    $pengkajianNyeriTime        = $this->masterModel->referensi(12);

    $data = array(
      'jenis'                      => $jenis,
      'dHPN'                       => $dHPN,
      'skriningNyeri'              => $skriningNyeri,
      'skalaNyeriNRS'              => $skalaNyeriNRS,
      'skalaNyeriWBR'              => $skalaNyeriWBR,
      'skalaNyeriFLACC'            => $skalaNyeriFLACC,
      'skalaNyeriBPS'              => $skalaNyeriBPS,
      'efeksampingNRS'             => $efeksampingNRS,
      'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
      'pengkajianNyeriQuality'     => $pengkajianNyeriQuality,
      'pengkajianNyeriTime'        => $pengkajianNyeriTime,
    );

    $this->load->view('Pengkajian/igd/formulirPemantauanNyeri/edit', $data);
  }

  public function updatePemantauanNyeri()
  {
    $id = $this->input->post('id_pemantauan_nyeri');

    $data = array(
      'metode'                  => $this->input->post('skrining_nyeriIgdEdit'),
      'skor'                    => $this->input->post('skala_nyeri_IgdEdit'),
      'farmakologi'             => $this->input->post('farmakologi_IgdEdit'),
      'non_farmakologi'         => $this->input->post('non_farmakologi_IgdEdit'),
      'efek_samping'            => $this->input->post('efek_samping_IgdEdit'),
      'keterangan_efek_samping' => $this->input->post('efek_samping_lain_IgdEdit'),
      'tanggal'                 => $this->input->post('tanggal_edit'),
      'oleh'                    => $this->session->userdata('id'),
    );

    $this->formulirPemantauanNyeriModel->updatePNyeri($data,$id);
  }

}

/* End of file FormulirPemantauanNyeri.php */
/* Location: ./application/controllers/igd/FormulirPemantauanNyeri.php */
