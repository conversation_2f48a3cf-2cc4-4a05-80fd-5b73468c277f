<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class LTBGModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('ltbg.`*`,ru.DESKRIPSI ruangan, peng.NAMA user');
        $this->db->from('keperawatan.tb_ltbg ltbg');
        $this->db->join('pendaftaran.kunjungan kun','kun.NOMOR = ltbg.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','pen.NOMOR = kun.NOPEN','LEFT');
        $this->db->join('master.ruangan ru','kun.RUANGAN = ru.ID','LEFT');
        $this->db->join('aplikasi.pengguna peng','ltbg.oleh = peng.ID','LEFT');
        $this->db->where('ltbg.STATUS !=','0');
        $this->db->where('pen.NORM',$this->input->post('nomr'));
        $this->db->order_by('ltbg.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getLTBG($id_ltbg)
    {
      $query = $this->db->query(
        'SELECT ltbg.`*` FROM keperawatan.tb_ltbg ltbg
        WHERE ltbg.id = "'.$id_ltbg.'"'
      );
      return $query->row_array();
    }

}
