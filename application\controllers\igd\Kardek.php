<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>rdek extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    // if (!in_array(8, $this->session->userdata('akses'))) {
    //   redirect('login');
    // }
    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  ///////////// Modal view kardek obat detail ////////////////
  public function viewKardekObatDetail()
  {
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $detKarObat = $this->pengkajianAwalModel->detailViewKardekObat($nopen);
    $listDpjp = $this->masterModel->listDrUmum();
    $jalurPem = $this->masterModel->referensi(901);
    $getDataPasien = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'getKardekObat' => $detKarObat,
      'getPasien' => $getDataPasien,
      'nokun' => $nokun,
      'listDpjp' => $listDpjp,
      'jalurPemberian' => $jalurPem
    );

    $this->load->view('Pengkajian/igd/kardek/viewKardek', $data);
  }

   ///////////// Modal view hasil kardek ////////////////
  public function viewHasilIsiKardek()
  {
    $id = $this->input->post('idkardek');
    $nokun = $this->input->post('nokun');
    $keterangan = $this->input->post('keterangan');
    $dpjp = $this->input->post('dpjp');
    $sttskardek = $this->input->post('sttskardek');
    $tglpembuatan = $this->input->post('tglpembuatan');
    $idkardek = $this->pengkajianAwalModel->viewHasilIsiKardek($id);
    $idkardekHistory = $this->pengkajianAwalModel->historyPemberianKardek($id);
    $getDataPasien = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'getIsiKardek' => $idkardek,
      'hKardekPemberian' => $idkardekHistory,
      'dpjp' => $dpjp,
      'sttskardek' => $sttskardek,
      'keterangan' => $keterangan,
      'idkardek' => $id,
      'tglpembuatan' => $tglpembuatan,
      'getPasien' => $getDataPasien
    );

    $this->load->view('Pengkajian/igd/kardek/isiPerawatKardek', $data);
  }

  ///////////// Modal view input pemberian obat ////////////////
  public function tambahPemberian()
  {
    $idfarmasi = $this->input->post('idfarmasi');
    $nopen = $this->input->post('nopen');
    $sisaobat = $this->input->post('sisaobat');
    $nmobat = $this->input->post('nmobat');
    $idkardek = $this->input->post('idkardek');
    $pilihPerawat = $this->pengkajianAwalModel->pilihPerawatKardek($nopen);
    $jalurPem = $this->masterModel->referensi(901);
    $data = array(
      'pilihPerawat' => $pilihPerawat,
      'sisaobat' => $sisaobat,
      'nmobat' => $nmobat,
      'idkardek' => $idkardek,
      'jalurPemberian' => $jalurPem,
      'idfarmasi' => $idfarmasi
    );

    $this->load->view('Pengkajian/igd/kardek/inputPemberian', $data);
  }

  ///////////// Modal view stop pemberian ////////////////
  public function stopPemberian()
  {
    $idfarmasi = $this->input->post('idfarmasi');
    $idkardek = $this->input->post('idkardek');
    $sisaobat = $this->input->post('sisaobat');
    $nmobat = $this->input->post('nmobat');
    $nokun = $this->input->post('nokun');
    $getDataPasien = $this->pengkajianAwalModel->getNomr($nokun);
    $listDpjp = $this->masterModel->listDrUmum();
    $data = array(
      'idfarmasi' => $idfarmasi,
      'idkardek' => $idkardek,
      'sisaobat' => $sisaobat,
      'getPasien' => $getDataPasien,
      'listDpjp' => $listDpjp,
      'nmobat' => $nmobat
    );

    $this->load->view('Pengkajian/igd/kardek/stopPemberian', $data);
  }

   ///////////// Modal simpan stop pemberian ////////////////
  public function stopSimpanPemberian()
  {
    $post = $this->input->post();
    $idFarmasiKardek = $this->input->post('idFarmasiStop');
    $idKardekStop = $this->input->post('idKardekStop');
    $date = $this->input->post('tanggalStopPemberian');
    $jamPemberian = $this->input->post('jamStopPemberian');
    $stopPilihDokterPemberian = $this->input->post('stopPilihDokterPemberian');
    $oleh = $this->session->userdata("id");

    $tglPemberian = date('Y-m-d', strtotime($date));
    echo $tglPemberian;

    $data = array(
      'id_farmasi' => $idFarmasiKardek,
      'dokter' => $stopPilihDokterPemberian,
      'tanggal' => $tglPemberian,
      'jam' => $jamPemberian,
      'oleh' => $oleh,
      'status' => 1
    );

    $this->db->insert('db_layanan.tb_kardek_stop', $data);

    $sttsObatKardek = array(
      'status' => 2
    );
    $this->db->where('id_kardek', $idKardekStop);
    $this->db->where('id_farmasi', $idFarmasiKardek);
    $this->db->update('db_layanan.tb_kardek_detail', $sttsObatKardek);
  }

  ///////////// Modal view ubah pemberian ////////////////
  public function ubahViewPemberian()
  {
    $idpemberian = $this->input->post('idpemberian');
    $jalurPem = $this->masterModel->referensi(901);
    $idkardek = $this->input->post('idkardek');
    $idfarmasi = $this->input->post('idfarmasi');
    $tglpemberian = $this->input->post('tglpemberian');
    $jampemberian = $this->input->post('jampemberian');
    $nmobat = $this->input->post('nmobat');
    $ketpasien = $this->input->post('ketpasien');
    $jmlhobatawal = $this->input->post('jmlhobatawal');
    $prwtprtm = $this->input->post('prwtprtm');
    $jmlberi = $this->input->post('jmlberi');
    $dosisberi = $this->input->post('dosisberi');
    $jlrpember = $this->input->post('jlrpember');
    $pilihPerawat = $this->pengkajianAwalModel->pilihPerawatKardek();
    $data = array(
      'idpemberian' => $idpemberian,
      'jalurPemberian' => $jalurPem,
      'idkardek' => $idkardek,
      'idfarmasi' => $idfarmasi,
      'tglpemberian' => $tglpemberian,
      'jampemberian' => $jampemberian,
      'ketpasien' => $ketpasien,
      'jmlhobatawal' => $jmlhobatawal,
      'prwtprtm' => $prwtprtm,
      'pilihPerawat' => $pilihPerawat,
      'jmlberi' => $jmlberi,
      'dosisberi' => $dosisberi,
      'jlrpember' => $jlrpember,
      'nmobat' => $nmobat
    );

    $this->load->view('Pengkajian/igd/kardek/ubahPemberian', $data);
  }

  ///////////// Modal ubah pemberian ////////////////
  public function ubahPemberian()
  {

    $post = $this->input->post();
    $id = $this->input->post('idPemberianUbah');
    $ubahKetPasienKeluarga = $this->input->post('ubahKetPasienKeluarga');
    $date = $this->input->post('ubahTanggalPemberianKardek');
    $ubahJamPemberianKardek = $this->input->post('ubahJamPemberianKardek');
    $ubahJumlahBerikanObat = $this->input->post('ubahJumlahBerikanObat');
    $ubahDosisBerikanKardek = $this->input->post('ubahDosisBerikanKardek');
    $ubahPilihJalurPemberianKardek = $this->input->post('ubahPilihJalurPemberianKardek');
    $ubahPilihprwt = $this->input->post('ubahPilihprwt');
    $idFarmasiKardekUbah = $this->input->post('idFarmasiKardekUbah');
    $idKardekUbah = $this->input->post('idKardekUbah');
    $sisaUbah = $this->input->post('ubahSIsaBerikan');

     $ubahTanggalPemberianKardek = date('Y-m-d', strtotime($date));
     echo $ubahTanggalPemberianKardek;

    $data = array(
      'tanggal' => $ubahTanggalPemberianKardek,
      'pasien_keluarga' => $ubahKetPasienKeluarga,
      'jam' => $ubahJamPemberianKardek,
      'jumlah_pemberian' => $ubahJumlahBerikanObat,
      'dosis' => $ubahDosisBerikanKardek,
      'jalur_pemberian_input' => $ubahPilihJalurPemberianKardek,
      'perawat_2' => $ubahPilihprwt
    );
    $this->db->where('id', $id);
    $this->db->update('db_layanan.tb_kardek_pemberian', $data);

    $jumlahObat = array(
      'jumlah_obat' => $sisaUbah
    );
    $this->db->where('id_kardek', $idKardekUbah);
    $this->db->where('id_farmasi', $idFarmasiKardekUbah);
    $this->db->update('db_layanan.tb_kardek_detail', $jumlahObat);
  }

   ///////////// Modal Simpan beri ////////////////
  public function simpanBeriKardek()
  {
    $post = $this->input->post();
    $idFarmasiKardek = $this->input->post('idFarmasiKardek');
    $idKardek = $this->input->post('idKardek');
    $date = $this->input->post('tanggalPemberianKardek');
    $jamPemberian = $this->input->post('jamPemberianKardek');
    $pilihPerawat = $this->input->post('pilihPerawat');
    $jumlahPemb = $this->input->post('jumlahBerikanKardek');
    $dosisBerikanKardek = $this->input->post('dosisBerikanKardek');
    $pilihJalurPemberianKardek = $this->input->post('pilihJalurPemberianKardek');
    $sisa = $this->input->post('sisaBerikan');
    $pasienKeluargaKardek = $this->input->post('pasienKeluargaKardek');
    $oleh = $this->session->userdata("id");

    $tglPemberian = date('Y-m-d', strtotime($date));
    echo $tglPemberian;

    $data = array(
      'id_farmasi' => $idFarmasiKardek,
      'tanggal' => $tglPemberian,
      'jam' => $jamPemberian,
      'jumlah_pemberian' => $jumlahPemb,
      'dosis' => $dosisBerikanKardek,
      'jalur_pemberian_input' => $pilihJalurPemberianKardek,
      'perawat_2' => $pilihPerawat,
      'pasien_keluarga' => $pasienKeluargaKardek,
      'oleh' => $oleh,
      'status' => 1
    );

    $this->db->insert('db_layanan.tb_kardek_pemberian', $data);

    $jumlahObat = array(
      'jumlah_obat' => $sisa
    );
    $this->db->where('id_kardek', $idKardek);
    $this->db->where('id_farmasi', $idFarmasiKardek);
    $this->db->update('db_layanan.tb_kardek_detail', $jumlahObat);

  }

    ///////////// Modal Simpan kardek obat ////////////////
  public function simpanKardekObat()
  {
    $post = $this->input->post();
    $nokun = $this->input->post('nokun');
    $pilihDpjpKardek = $this->input->post('pilihDpjpKardekObat');
    $keteranganKardek = $this->input->post('keteranganKardekObat');
    $oleh = $this->session->userdata("id");

    $data = array(
      'nokun' => $nokun,
      'keterangan' => $keteranganKardek,
      'oleh' => $oleh,
      'dpjp' => $pilihDpjpKardek,
      'status' => 1
    );

    // $this->db->insert('db_layanan.tb_kardek', $data);

    $id_kardek = $this->pengkajianAwalModel->insertDetailKardek($data);

    $dataKardekDetail = array();
    $indexKardekDetail = 0;
    if (isset($post['cekPilihKardekObat'])) {
      foreach ($post['cekPilihKardekObat'] as $input) {
        if ($post['cekPilihKardekObat'][$indexKardekDetail] != "") {
          array_push(
            $dataKardekDetail, array(
              'id_kardek' => $id_kardek,
              'id_farmasi' => $post['cekPilihKardekObat'][$indexKardekDetail],
              'jumlah_obat' => $post['jumlahObatKardek'][$indexKardekDetail],
              'jalur_pemberian' => $post['pilihJalurPemberianKardek'][$indexKardekDetail],
              'status' => 1,
            )
          );
        }
        $indexKardekDetail++;
      }
      $this->db->insert_batch('db_layanan.tb_kardek_detail', $dataKardekDetail);
    }

  }

  ///////////// Modal Final kardek obat ////////////////
  public function finalKardek()
  {
    $post = $this->input->post();
    $id = $this->input->post('idKardek');

    $finalObat = array(
      'status' => 2
    );
    $this->db->where('id', $id);
    $this->db->update('db_layanan.tb_kardek', $finalObat);
  }

}


/* End of file PendaftaranOperasi.php */
/* Location: ./application/controllers/operasi/PendaftaranOperasi.php */
