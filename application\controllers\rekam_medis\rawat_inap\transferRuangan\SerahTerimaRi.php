<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SerahTerimaRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel'
      ]
    );
  }

  public function index()
  {
    $nomr = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $id_srht = $this->uri->segment(7);

    if ($id_srht == '') {
      $jenisSerahShowHide = 1;
    } else {
      $jenisSerahShowHide = 2;
    }

    $data = [
      'ruangan' => 2,
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'jenisSerahShowHide' => $jenisSerahShowHide,
      'cekNopenSerahTerima' => $this->pengkajianAwalModel->cekNopenSerahTerima($nopen),
      'kondisisaatiniprosedurpratindakan' => $this->masterModel->referensi(418),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'historySerahTerima' => $this->pengkajianAwalModel->historySerahTerima($nomr),
      'kesadaran' => $this->masterModel->referensi(5),
      'risikojatuhprosedur_pasca' => $this->masterModel->referensi(447),
      'kesadaran_pasca' => $this->masterModel->referensi(446),
      'kondisisaatiniprosedurpascatindakan' => $this->masterModel->referensi(437),
      'penyakitmenular' => $this->masterModel->referensi(400),
      'suratizintindakan' => $this->masterModel->referensi(401),
      'penandaansisioperasi' => $this->masterModel->referensi(402),
      'hasilpemeriksaanpratindakan' => $this->masterModel->referensi(403),
      'hasilpemeriksaanpascatindakan' => $this->masterModel->referensi(414),
      'fotopratindakan' => $this->masterModel->referensi(404),
      'getSerahTerima' => $this->pengkajianAwalModel->getSerahTerima($id_srht),
      'ctscanpratindakan' => $this->masterModel->referensi(405),
      'usgpascatindakan' => $this->masterModel->referensi(411),
      'usgpratindakan' => $this->masterModel->referensi(406),
      'ctscanpascatindakan' => $this->masterModel->referensi(410),
      'mripratindakan' => $this->masterModel->referensi(407),
      'mripascatindakan' => $this->masterModel->referensi(412),
      'echocardiografipascatindakan' => $this->masterModel->referensi(413),
      'risikojatuhprosedur' => $this->masterModel->referensi(438),
      'implant_pra' => $this->masterModel->referensi(439),
      'implant_pasca' => $this->masterModel->referensi(448),
      'pemakaian_alat_bantu_pra' => $this->masterModel->referensi(440),
      'pemakaian_alat_bantu_pasca' => $this->masterModel->referensi(449),
      'gigi_palsu_pra' => $this->masterModel->referensi(441),
      'echocardiografipratindakan' => $this->masterModel->referensi(408),
      'gigi_palsu_pasca' => $this->masterModel->referensi(450),
      'ketersediaandarah' => $this->masterModel->referensi(442),
      'ketersediaanalatobat' => $this->masterModel->referensi(443),
      'instruksipratindakan' => $this->masterModel->referensi(444),
      'instruksipascatindakan' => $this->masterModel->referensi(445),
      'program_terapi_pra' => $this->masterModel->referensi(451),
      'program_terapi_pasca' => $this->masterModel->referensi(452),
      'fotopascatindakan' => $this->masterModel->referensi(409),
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'rencanaserahterima' => $this->masterModel->referensi(478),
      'perawat' => $this->masterModel->listPetugasSerahTerima(105090101, null),
      'pengisi' => $this->session->userdata('id')
    ];
    // echo '<pre>';print_r($data);exit();

    $this->load->view('Pengkajian/serahterima/index', $data);
  }

  public function viewIndexRi($idNorm, $idNopen, $idNokun, $idLoad)
  {
    if ($idLoad == '') {
      $jenisSerahShowHide = 1;
    } else {
      $jenisSerahShowHide = 2;
    }

    $data = [
      'idNorm' => $idNorm,
      'ruangan' => 2,
      'cekNopenSerahTerima' => $this->pengkajianAwalModel->cekNopenSerahTerima($idNopen),
      'getNomr' => $this->pengkajianAwalModel->getNomr($idNokun),
      'jenisSerahShowHide' => $jenisSerahShowHide,
      'historySerahTerima' => $this->pengkajianAwalModel->historySerahTerima($idNorm),
      'pengisi' => $this->session->userdata('id'),
      'kondisisaatiniprosedurpratindakan' => $this->masterModel->referensi(418),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'kesadaran' => $this->masterModel->referensi(5),
      'risikojatuhprosedur_pasca' => $this->masterModel->referensi(447),
      'kesadaran_pasca' => $this->masterModel->referensi(446),
      'kondisisaatiniprosedurpascatindakan' => $this->masterModel->referensi(437),
      'penyakitmenular' => $this->masterModel->referensi(400),
      'suratizintindakan' => $this->masterModel->referensi(401),
      'penandaansisioperasi' => $this->masterModel->referensi(402),
      'hasilpemeriksaanpratindakan' => $this->masterModel->referensi(403),
      'hasilpemeriksaanpascatindakan' => $this->masterModel->referensi(414),
      'fotopratindakan' => $this->masterModel->referensi(404),
      'ctscanpratindakan' => $this->masterModel->referensi(405),
      'usgpascatindakan' => $this->masterModel->referensi(411),
      'usgpratindakan' => $this->masterModel->referensi(406),
      'ctscanpascatindakan' => $this->masterModel->referensi(410),
      'mripratindakan' => $this->masterModel->referensi(407),
      'mripascatindakan' => $this->masterModel->referensi(412),
      'echocardiografipascatindakan' => $this->masterModel->referensi(413),
      'risikojatuhprosedur' => $this->masterModel->referensi(438),
      'implant_pra' => $this->masterModel->referensi(439),
      'implant_pasca' => $this->masterModel->referensi(448),
      'pemakaian_alat_bantu_pra' => $this->masterModel->referensi(440),
      'pemakaian_alat_bantu_pasca' => $this->masterModel->referensi(449),
      'gigi_palsu_pra' => $this->masterModel->referensi(441),
      'echocardiografipratindakan' => $this->masterModel->referensi(408),
      'gigi_palsu_pasca' => $this->masterModel->referensi(450),
      'ketersediaandarah' => $this->masterModel->referensi(442),
      'ketersediaanalatobat' => $this->masterModel->referensi(443),
      'instruksipratindakan' => $this->masterModel->referensi(444),
      'instruksipascatindakan' => $this->masterModel->referensi(445),
      'program_terapi_pra' => $this->masterModel->referensi(451),
      'program_terapi_pasca' => $this->masterModel->referensi(452),
      'fotopascatindakan' => $this->masterModel->referensi(409),
      'getSerahTerima' => $this->pengkajianAwalModel->getSerahTerima($idLoad),
      'rencanaserahterima' => $this->masterModel->referensi(478),
      'perawat' => $this->masterModel->listPetugasSerahTerima(105090101, null)
    ];
    // echo '<pre>';print_r($data);exit();

    $this->load->view('Pengkajian/serahterima/index', $data);
  }
}

// End of file SerahTerimaRi.php
// Location: ./application/controllers/rekam_medis/rawat_inap/transferRuangan/SerahTerimaRi.php