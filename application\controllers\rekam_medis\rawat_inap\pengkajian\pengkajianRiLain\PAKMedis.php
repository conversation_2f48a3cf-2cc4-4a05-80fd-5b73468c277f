<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PAKMedis extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PAKMedisModel'));
  }

  public function index()
  {
    $pasien = $this->PAKMedisModel->getNomrRawatInap($this->uri->segment(2));
    $getPengkajian = $this->PAKMedisModel->getPengkajian($pasien['ID_EMR_PAK_MEDIS']);
    $get_pmfak = $this->PAKMedisModel->get_pmfak($pasien['NOPEN']);

    $data = array(
      'nopen' => $this->uri->segment(2),
      'pasien' => $pasien,
      'getPengkajian' => $getPengkajian,
      'listIntervensi' => $this->masterModel->referensi(506),
      'listPerencanaanAsuhan' => $this->masterModel->referensi(512),
      'listKausatif' => $this->masterModel->referensi(508),
      'listPenghentianIntervensi' => $this->masterModel->referensi(509),
      'get_pmfak' => $get_pmfak
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/pengkajianPAKMedis', $data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $this->db->trans_begin();
          $post = $this->input->post();

          $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
          $idRefEmr = $this->input->post('idemr');

          $dataMedis = array(
            'id_emr' => $getIdEmr,
            'nopen' => $post['nopen'],
            'nokun' => $post['nokun'],
            'jenis' => 14,
            'created_by' => $this->session->userdata('id'),
            'flag' => '1',
          );

          $dataKondisi_kebutuhan_pak = array(
            'id_emr' => $getIdEmr,
            'diagnosis1' => isset($post['diagnosis1']) ? $post['diagnosis1'] : "",
            'diagnosis2' => isset($post['diagnosis2']) ? $post['diagnosis2'] : "",
            'intervensi' => isset($post['intervensi']) ? $post['intervensi'] : "",
            'kausatif' => isset($post['kausatif']) ? json_encode($post['kausatif']) : "",
            'kausatiflain' => isset($post['kausatiflain']) ? $post['kausatiflain'] : "",
            'penghentianintervensi' => isset($post['penghentianintervensi']) ? json_encode($post['penghentianintervensi']) : "",
            'tatalaksana' => isset($post['tatalaksana']) ? $post['tatalaksana'] : "",
            'edukasi' => isset($post['edukasi']) ? $post['edukasi'] : ""
          );

          if (!empty($post['idemr'])) {
            $this->db->replace('medis.tb_medis', $dataMedis);
            $this->db->replace('medis.tb_kondisi_kebutuhan_pak', $dataKondisi_kebutuhan_pak);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          } else {
            $this->db->insert('medis.tb_medis', $dataMedis);
            $this->db->insert('medis.tb_kondisi_kebutuhan_pak', $dataKondisi_kebutuhan_pak);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }
      }

      else if($param == 'count'){
        $result = $this->PAKMedisModel->get_count();;
        echo json_encode($result);
      }

    }
  }

  public function datatables(){
    $result = $this->PAKMedisModel->historyPengkajian();

    $data = array();
        foreach ($result as $row){
          $tombolCetak = '<a class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
          $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
          $action = "";
          $verif = "";
          $userLogin = $this->session->userdata('status');
  
          // KONDISI UNTUK ADA PENGKAJIAN PERAWAT
          if($row -> ID_EMR_PERAWAT != null){
            if($userLogin == 2){
              $jenisPengkajian = $row -> JENIS_PENGKAJIAN_KEPERAWATAN;

              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
  
              if($jenisPengkajian == 5){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
              }elseif($jenisPengkajian == 6){
                $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
              }elseif($jenisPengkajian == 7){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
              }elseif($jenisPengkajian == 11){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
                $action .= '<a href="#lihatPemantauanAsesmenRestrain" class="btn btn-primary btn-block btn-sm showPemantauanAsesmenRestrain" data-toggle="modal" data-idpemasres="'.$row -> ID_EMR_PERAWAT.'" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Pemantauan Restrain</a>';
              }elseif($jenisPengkajian == 14){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Pasien Akhir Kehidupan</button>';
              }else{
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
              }
            }elseif($userLogin == 1){
              $jenisPengkajian = $row -> JENIS_PENGKAJIAN_MEDIS;

              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
  
              if($jenisPengkajian == 5){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
              }elseif($jenisPengkajian == 6){
                $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
              }elseif($jenisPengkajian == 7){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
              }elseif($jenisPengkajian == 11){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
              }elseif($jenisPengkajian == 14){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKMedis" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Medis Pasien Akhir Kehidupan</button><button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Pasien Akhir Kehidupan</button>';
              }else{
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
              }
            }
  
          }
  
          // KONDISI UNTUK ADA PENGKAJIAN MEDIS
          if($row -> ID_EMR_MEDIS != null){
            if($userLogin == 1){
              if($row -> STATUS_VERIFIKASI == 0){
                if($row -> ID_EMR_PERAWAT != null){
                  $verif = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'">Verif</button>';
                }else{
                  $verif = $row -> INFO_VERIFIKASI;
                }
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
  
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
            }elseif($userLogin == 2){
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="2"><i class="fa fa-eye"></i>  View Medis</button>';
            }
          }
  
          $sub_array = array();
          $sub_array[] = $row -> INFO;
          $sub_array[] = $verif;
          $sub_array[] = $row -> RUANGAN;
          $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
          $sub_array[] = $action;
          $sub_array[] = $row -> DPJP;
          $sub_array[] = $namaVerif;
          $sub_array[] = $tombolCetak;
          $sub_array[] = $row -> USER_MEDIS;
          $sub_array[] = $row -> USER_PERAWAT;   
          $data[] = $sub_array;
        }
  
        $output = array(
            "draw" => intval($this->input->post("draw")),
            "data"              => $data
        );
        echo json_encode($output);
  }

}

/* End of file MedisDewasa.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasa.php */
