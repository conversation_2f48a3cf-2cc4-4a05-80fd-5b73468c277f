<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON><PERSON> extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'MPP/SkriningModel',
        'MPP/AsesmenModel'
      )
    );
  }

  public function index($id = null)
  {
    $nokun = $this->uri->segment(3);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $pasien['NORM'];
    $data = array(
      'id' => $id,
      'nokun' => $nokun,
      'pasien' => $pasien,
      'periksa' => $this->SkriningModel->periksa($nomr),
      'jumlah' => $this->AsesmenModel->hitungSemua($nomr),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/MPP/Asesmen/index', $data);
  }

  public function simpan()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->AsesmenModel->rules;
      $this->form_validation->set_rules($rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = $post['id'] ?? null;
        $tanggal = $post['tanggal'] ?? null;
        $waktu = $post['waktu'] ?? null;
        $tanggalWaktu = date('Y-m-d H:i:s', strtotime("$tanggal $waktu"));
        // echo '<pre>';print_r($tanggalWaktu);exit();

        // Mulai data
        $data = array(
          'nokun' => $post['nokun'] ?? null,
          'tanggal' => $tanggal,
          'waktu' => $waktu,
          'catatan' => $post['catatan'] ?? null,
          'oleh' => $this->session->userdata['id'],
          'created_at' => $tanggalWaktu,
          'status' => 1,
        );
        // Akhir data

        // Mulai simpan
        // echo '<pre>';print_r($data);exit();
        if (!empty($id)) {
          $this->AsesmenModel->ubah($data, $id);
        } else {
          $this->AsesmenModel->simpan($data);
        }
        // Akhir simpan

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array(
      'nomr' => $post['nomr'],
      'nokun' => $post['nokun'],
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/MPP/Asesmen/history', $data);
  }

  public function tabel()
  {
    $nomr = $this->input->post('nomr');
    $history = $this->AsesmenModel->ambilTabel($nomr);
    $data = array();
    $no = $_POST['start'];
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history as $h) {
      // Mulai periksa status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }
      // Akhir periksa status

      // Mulai data
      $row = array();
      $row[] = ++$no . '.';
      $row[] = date('d/m/Y', strtotime($h->tanggal));
      $row[] = date('H.i', strtotime($h->waktu));
      $row[] = $h->catatan;
      $row[] = $h->pengisi;
      $row[] = date('d/m/Y, H:i', strtotime("$h->tanggal $h->waktu"));
      $row[] = $status;
      $row[] = "<div class='btn-group' role='group'>
                  <button type='button' href='#modal-batal-ampp' class='btn btn-sm btn-danger waves-effect tbl-batal-ampp' data-toggle='modal' data-id='" . $h->id . "' $disabled>
                    <i class='fa fa-window-close'></i> Batal
                  </button>
                  <button type='button' class='btn btn-sm btn-primary waves-effect tbl-detail-ampp' data-id='" . $h->id . "' $disabled>
                    <i class='fa fa-eye'></i> Lihat
                  </button>
                </div>";
      $data[] = $row;
      // Akhir data
    }

    $output = array(
      'draw' => $_POST['draw'],
      'recordsTotal' => $this->AsesmenModel->hitungSemua($nomr),
      'recordsFiltered' => $this->AsesmenModel->hitungTersaring($nomr),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = $post['id'] ?? null;
    $data = array('status' => 0);
    $this->AsesmenModel->ubah($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detail()
  {
    $post = $this->input->post(null, true);
    $detail = $this->AsesmenModel->detail($post['id']);
    // echo '<pre>';print_r($detail);exit();
    echo json_encode(
      array(
        'status' => 'succes',
        'data' => $detail,
      )
    );
  }
}

/* End of file Asesmen.php */
/* Location: ./application/controllers/MPP/Asesmen.php */