<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SkalaBraden extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/keperawatan/SkalaBradenModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $id = $this->input->post('id');
    $data = array(
      'id' => $id,
      'pasien' => $pasien,
      'getPengkajian' => $this->pengkajianAwalModel->getPengkajian($pasien['NORM']),
      'persepsiSensori' => $this->masterModel->referensi(1154),
      'kelembaban' => $this->masterModel->referensi(1155),
      'aktivitas' => $this->masterModel->referensi(1156),
      'mobilitas' => $this->masterModel->referensi(1157),
      'nutrisi' => $this->masterModel->referensi(1158),
      'gesekan' => $this->masterModel->referensi(1159),
      'history' => $this->SkalaBradenModel->history($nokun),
      'jumlah' => $this->SkalaBradenModel->jumlah($nokun),
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('rekam_medis/rawat_inap/keperawatan/skalaBraden/index', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan' || $param == 'ubah') {
        $rules = $this->SkalaBradenModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();

          if ($post['skor_braden_rendah'] != '' && $post['skor_braden_sedang'] == '' && $post['skor_braden_tinggi'] == '') {
            $skor = $post['skor_braden_rendah'];
            $tingkat = 0;
          } elseif ($post['skor_braden_rendah'] == '' && $post['skor_braden_sedang'] != '' && $post['skor_braden_tinggi'] == '') {
            $skor = $post['skor_braden_sedang'];
            $tingkat = 1;
          } elseif ($post['skor_braden_rendah'] == '' && $post['skor_braden_sedang'] == '' && $post['skor_braden_tinggi'] != '') {
            $skor = $post['skor_braden_tinggi'];
            $tingkat = 2;
          }

          $data = array(
            'id' => $post['id'],
            'nokun' => $post['nokun'],
            'tanggal' => $post['tanggal'],
            'persepsi_sensori' => $post['persepsi_sensori'],
            'kelembaban' => $post['kelembaban'],
            'aktivitas' => $post['aktivitas'],
            'mobilitas' => $post['mobilitas'],
            'nutrisi' => $post['nutrisi'],
            'gesekan' => $post['gesekan'],
            'naikkan_risiko' => $post['naikkan_risiko'],
            'skor' => $skor,
            'tingkat' => $tingkat,
            'oleh' => $this->session->userdata('id'),
            'status' => '1',
          );

          /*echo "<pre>";print_r($data);exit();*/
          $this->SkalaBradenModel->replace($data);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->SkalaBradenModel->get($post['id'], true);
        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data,
          )
        );
      }
    }
  }

  public function history()
  {
    $nokun = $this->input->post('nokun');
    $data = array(
      'history' => $this->SkalaBradenModel->history($nokun),
    );
    $this->load->view('rekam_medis/rawat_inap/keperawatan/skalaBraden/history', $data);
  }
}