<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PAKPerawatModel extends MY_Model {
  protected $_table_name = 'keperawatan.tb_keperawatan';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>.'
      ),
    ),
  );

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select("pk.NOMOR NOKUN
                      , rk.ID ID_RUANGAN
                      , rk.DESKRIPSI RUANGAN
                      , pk.MASUK TANGGAL_KUNJUNGAN
                      , kp.id_emr ID_EMR_PERAWAT
                      , kp.created_at TANGGAL_PENGKAJIAN_PERAWAT
                      , master.getNamaLengkapPegawai(peng.NIP) USER_PERAWAT
                      , md.id_emr ID_EMR_MEDIS
                      , md.created_at TANGGAL_PENGKAJIAN_MEDIS
                      , master.getNamaLengkapPegawai(pemed.NIP) USER_MEDIS
                      , p.NOMOR NOPEN
                      , p.NORM
                      , master.getNamaLengkap(p.NORM) NAMA_PASIEN
                      , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
                      , kp.created_by ID_USER_PERAWAT
                      , md.created_by ID_USER_MEDIS
                      , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
                      , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
                      , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18, 2, 1) USIA
                      , kp.jenis JENIS_PENGKAJIAN_PERAWAT
                      , 'Pengkajian Keperawatan Remaja' KET_PENGKAJIAN_PERAWAT
                      , md.jenis JENIS_PENGKAJIAN_MEDIS
                      , 'Pengkajian Medis Remaja' KET_PENGKAJIAN_MEDIS
                      ,HOUR(TIMEDIFF(NOW(),md.created_at)) DURASI_MEDIS,IF(HOUR(TIMEDIFF(NOW(),md.created_at))<=24,1,0) STATUS_EDIT_MEDIS
                      ,HOUR(TIMEDIFF(NOW(),kp.created_at)) DURASI_PERAWAT,IF(HOUR(TIMEDIFF(NOW(),kp.created_at))<=24,1,0) STATUS_EDIT_PERAWAT
                      , 'Pengkajian Remaja' INFO
                      , kp.status_verif STATUS_VERIFIKASI
                      , IF(kp.status_verif=0,'Belum Verifikasi',IF(kp.status_verif=1,CONCAT('Sudah Verifikasi', ' (',master.getNamaLengkapPegawai(pever.NIP),')'),'Tidak Perlu Verifikasi')) INFO_VERIFIKASI
                      , kp.verif_oleh VERIF_OLEH, master.getNamaLengkapPegawai(pever.NIP) USER_VERIF");
                  $this->db->from('pendaftaran.kunjungan pk');
                  $this->db->join('keperawatan.tb_keperawatan kp','pk.NOMOR = kp.nokun AND kp.flag=1 AND kp.`status`=1','LEFT');
                  $this->db->join('medis.tb_medis md','pk.NOMOR = md.nokun AND md.flag=1 AND md.`status`=1','LEFT');
                  $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
                  $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
                  $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
                  $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
                  $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
                  $this->db->join('master.pasien pas','pas.NORM = p.NORM','LEFT');
                  $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
                  $this->db->join('master.ruangan rp','rp.ID = tp.RUANGAN','LEFT');
                  $this->db->join('master.referensi refpj','refpj.ID = pj.JENIS AND refpj.JENIS=10','LEFT');
                  $this->db->join('aplikasi.pengguna peng','peng.ID = kp.created_by','LEFT');
                  $this->db->join('aplikasi.pengguna pemed','pemed.ID = md.created_by','LEFT');
                  $this->db->join('aplikasi.pengguna pever','pever.ID = kp.verif_oleh','LEFT');

                  $this->db->where("(kp.id_emr IS NOT NULL OR md.id_emr IS NOT NULL)");
                  $this->db->where('p.NORM',$this->input->post('nomr'));
                  $this->db->where('kp.jenis=14 OR md.jenis=14');
                  $this->db->group_by('pk.NOMOR');
                  $this->db->order_by('pk.MASUK ', 'DESC');
  }

  public function historyPengkajian(){
    $norm = $this->input->post("nomr");
    $query = $this->db->query("CALL keperawatan.HistoryRawatInap($norm)");
    return $query->result();
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }


  //  Hitung jumlah skala remaja
  function get_count_pengkajian_ri_remaja($idemr){
    $query = $this->db->query(
      "SELECT COUNT(remaja.id_emr) JUMLAH
      FROM keperawatan.tb_pengkajianri_remaja remaja
      WHERE remaja.id_emr = '$idemr'"
    );
    if ($query->num_rows() > 0) {
      return $query->row()->JUMLAH;
    }
    return false;
  }



  public function getNomrRawatInap($nopen)
  {
      $query = $this->db->query(
          "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
          , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
          , pas.JENIS_KELAMIN ID_JK
          , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
          , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
          , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
          , p.TANGGAL TANGGAL_DAFTAR
          , r.JENIS_KUNJUNGAN
          , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
          , pk.MASUK TANGGAL_KUNJUNGAN
          , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
          , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
          ) DESKRIPSI_DIAGNOSA_MASUK
          , ref.ID IDPENJAMIN
          , ref.DESKRIPSI PENJAMIN
          , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
          dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
          )))) STATUS_KUNJUNGAN
          , penggu.ID ID_USER
          , dok.ID ID_DOKTER
          , pas.TANGGAL_LAHIR
          , dtt.NAME_PIC
          , pk.REF
          , mkp.NOMOR NOTLPN
          ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
          FROM pendaftaran.kunjungan tpas
          LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
          WHERE tpas.NOMOR = pk.NOMOR
          ) ID_TUJUAN_FARMASI
    
          , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
          FROM pendaftaran.kunjungan tpas
          LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
          WHERE tpas.NOMOR = pk.NOMOR
          ) TUJUAN_FARMASI
          , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
          , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
          , ppk.NAMA RUJUKAN_DARI
          , refdar.DESKRIPSI GOL_DARAH
          , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                  WHERE kepe.nopen=p.NOMOR
                      AND kepe.`status`=1
                      AND kepe.jenis=14
                      AND kepe.flag=1
                  ORDER BY kepe.created_at DESC 
                  LIMIT 1) ID_EMR_PAK_PERAWAT
          , (SELECT id_emr FROM medis.tb_medis kepe
                  WHERE kepe.nopen=p.NOMOR
                      AND kepe.`status`=1
                      AND kepe.jenis=5
                      AND kepe.flag=1
                  ORDER BY kepe.created_at DESC 
                  LIMIT 1) ID_EMR_MEDIS_DEWASA_RI
    
          FROM pendaftaran.pendaftaran p
          LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
          LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
          LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
          LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
          LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR
          LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
          LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
          LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND refdar.JENIS=6
          LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
          LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
          LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
          LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
          LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
          LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
          LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
          LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
          LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM
    
          WHERE tp.`STATUS` not in (0,1)
          AND p.`STATUS`!= 0
          AND pk.`STATUS` != 0 AND p.NOMOR = '$nopen'
          GROUP BY dtt.ID #DESC"
      );
      return $query->row_array();
  }

  // Get Pengkajian Rawat Inap Remaja
  public function getPengkajian($idemr)
  {
    $query = $this->db->query(
      'SELECT pak.id_emr ID_EMR, pak.created_at, pak.kondisipasien, pak.kondisikeluarga
        , pak.berdukapasien, pak.berdukakeluarga, pak.potensireaksi, pak.pasienpsikolog
        , pak.keluargapsikolog, pak.spiritualpasien, pak.spiritualkeluarga, pak.terapikomplementer
        , pak.membutuhkancaregiver, pak.perawatandirumah, pak.alasan_perawatan_dirumah
        , kep.nopen, kep.nokun, kep.jenis, kep.rujukan, kep.diagnosa_masuk
        , kep.created_at tanggal_pembuatan_emr, kes.kesehatan_kondisi_pak riwayatkesehatan
        , kes.kesehatan_kondisi_pak_lain
        /*START DIAGNOSIS KEPERAWATAN*/
        , db_master.getIDAsuhanKeperawatan(pak.id_emr) ID_ASUHAN_KEPERAWATAN
        , db_master.getAsuhanKeperawatan(pak.id_emr) ASUHAN_KEPERAWATAN
        , db_master.getIDAsuhanKeperawatanDiagnosa(pak.id_emr) ID_DIAGNOSA_KEP
        , db_master.getAsuhanKeperawatanDiagnosa(pak.id_emr) DIAGNOSA_KEP
        , db_master.getIDAsuhanKeperawatanNOC(pak.id_emr) ID_NOC
        , db_master.getAsuhanKeperawatanNOC(pak.id_emr) NOC
        , db_master.getIDAsuhanKeperawatanNIC(pak.id_emr) ID_NIC
        , db_master.getAsuhanKeperawatanNIC(pak.id_emr) NIC
        , pak.`status` STATUS_EMR, db_master.getLainLainIDDiagnosa(pak.id_emr) ID_DIAGNOSA_LAIN_LAIN
        , db_master.getLainLainDiagnosa(pak.id_emr) DIAGNOSA_LAIN_LAIN
        , db_master.getLainLainIDNOC(pak.id_emr) ID_NOC_LAIN_LAIN, db_master.getLainLainNOC(pak.id_emr) NOC_LAIN_LAIN
        , db_master.getLainLainIDNIC(pak.id_emr) ID_NIC_LAIN_LAIN, db_master.getLainLainNIC(pak.id_emr) NIC_LAIN_LAIN
        /*END DIAGNOSIS KEPERAWATAN*/
      FROM keperawatan.tb_kondisi_kebutuhan_pak pak
      LEFT JOIN keperawatan.tb_keperawatan kep ON pak.id_emr = kep.id_emr
      LEFT JOIN keperawatan.tb_riwayat_kesehatan kes ON pak.id_emr = kes.id_emr
      WHERE pak.`status` = 1 AND kep.jenis=14 AND pak.id_emr = "'.$idemr.'" '
    );
    return $query->row_array();
  }



}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
