<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Flap extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(
            array(
                'masterModel',
                'pengkajianAwalModel',
                'rekam_medis/rawat_inap/ruanganIntensif/FlapModel'
            )
        );
        date_default_timezone_set('Asia/Jakarta');
    }

    public function index()
    {
        $nokun = $this->uri->segment(2);
        $id = $this->uri->segment(3);
        $isi = $this->FlapModel->isi($id);

        $data = array(
            'nokun' => $nokun,
            'id' => $id,
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'warna' => $this->masterModel->referensi(1402),
            'suhu' => $this->masterModel->referensi(1403),
            'crt' => $this->masterModel->referensi(1404),
            'turgor' => $this->masterModel->referensi(1405),
            'kesimpulan' => $this->masterModel->referensi(1406),
            'ruangRSKD' => $this->masterModel->ruanganRskd(),
            'isi' => $isi,
        );
        // echo '<pre>';print_r($data);exit();
        $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/flap.php', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();
                $dataFlap = array(
                    'id'            => isset($post['id_flap']) ? $post['id_flap'] : null,
                    'nopen'         => $post['nopen'],
                    'nokun'         => $post['nokun'],
                    'warna'         => isset($post['warna']) ? $post['warna'] : null,
                    'suhu'          => isset($post['suhu']) ? $post['suhu'] : null,
                    'crt'           => isset($post['crt']) ? $post['crt'] : null,
                    'turgor'        => isset($post['turgor']) ? $post['turgor'] : null,
                    'kesimpulan'    => isset($post['kesimpulan']) ? $post['kesimpulan'] : null,
                    'total'         => isset($post['total']) ? $post['total'] : null,
                    'ruang'         => isset($post['ruang']) ? $post['ruang'] : null,
                    'keterangan'    => isset($post['keterangan']) ? $post['keterangan'] : null,
                    'oleh'          => $this->session->userdata("id"),
                );
                // echo '<pre>';print_r($dataFlap);echo '</pre>';

                $this->db->trans_begin();

                if (!empty($post['id_flap'])) {
                    $this->db->replace('keperawatan.tb_flap', $dataFlap);
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success_ubah');
                    }

                    echo json_encode($result);
                } else {
                    $this->db->insert('keperawatan.tb_flap', $dataFlap);
                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success_simpan');
                    }

                    echo json_encode($result);
                }
            } else if ($param == 'count') {
                $result = $this->FlapModel->jumlah();;
                echo json_encode($result);
            }
        }
    }

    public function datatables()
    {
        $result = $this->FlapModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = "<a class='btn btn-primary btn-block btn-sm editFlap' data-toggle='modal' data-id='" . $row->ID . "'><i class='fa fa-eye'></i> Lihat</a>";
            $sub_array[] = $row->TANGGAL;
            $sub_array[] = $row->SKOR;
            $sub_array[] = $row->RUANG;
            $sub_array[] = $row->DPJP;
            $sub_array[] = $row->USER;
            $data[] = $sub_array;
        }

        $output = array(
            'draw'              => intval($_POST['draw']),
            'recordsTotal'      => $this->FlapModel->total_count(),
            'recordsFiltered'   => $this->FlapModel->filter_count(),
            'data'              => $data
        );
        echo json_encode($output);
    }
}
