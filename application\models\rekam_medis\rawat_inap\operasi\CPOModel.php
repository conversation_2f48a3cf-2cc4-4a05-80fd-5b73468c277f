<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CPOModel extends MY_Model
{
  public function insert_cpo($dataOperasi)
  {
    $this->db->insert('medis.tb_persiapan_operasi', $dataOperasi);
    return $this->db->insert_id();
  }

  public function insert_detail($dataDetail)
  {
    $this->db->insert_batch('medis.tb_persiapan_operasi_detail', $dataDetail);
  }

  public function pilihCPO($norm)
  {
    $query = $this->db->query("
  		SELECT cpo.id ID_CPO, cpo.nokun NOKUN, DATE_FORMAT(cpo.created_at,'%d/%m/%Y %H:%i:%s') TANGGAL
  		FROM medis.tb_persiapan_operasi cpo
  		WHERE cpo.nomr='$norm' AND cpo.`status`='1'
  		");
    return $query->result_array();
  }
  public function getCPO($idCPO)
  {
    $query = $this->db->query("
        SELECT cpo.id ID_CPO, cpo.nokun NOKUN, DATE_FORMAT(cpo.created_at,'%d-%m-%Y %H:%i:%s') TANGGAL,
        cpo.nomr NOMR, cpo.oleh PERAWAT, cpod.nama_dokumen DOKUMEN, cpod.keterangan KETERANGAN, cpod.verified, cpod.verified_by
        FROM medis.tb_persiapan_operasi cpo
        LEFT JOIN medis.tb_persiapan_operasi_detail cpod ON cpod.id_CPO = cpo.id
        WHERE cpo.id = '$idCPO' AND cpo.status = '1'
    ");
    return $query->row_array();
  }

  public function tableDokumen($idCPO)
  {
    $query = $this->db->query("
    SELECT cpo.id ID_CPO, cpod.id ID_DOKUMEN, cpo.nokun NOKUN, DATE_FORMAT(cpod.tanggal,'%d-%m-%Y %H:%i:%s') TANGGAL,
        cpo.nomr NOMR, cpo.oleh PERAWAT, cpod.nama_dokumen DOKUMEN, cpod.keterangan KETERANGAN,
        cpod.verified VERIFIED, cpod.verified_by VERIFIED_BY, cpod.verified_date VERIFIED_DATE,
        var.variabel NAMA_DOKUMEN, CONCAT(peg.GELAR_DEPAN,' ',peg.NAMA,' ',peg.GELAR_BELAKANG) AS NAMA_PERAWAT,
        CONCAT(peg2.GELAR_DEPAN,' ',peg2.NAMA,' ',peg2.GELAR_BELAKANG) AS NAMA_VERIFIKATOR, cpod.status STATUS
        FROM medis.tb_persiapan_operasi cpo
        LEFT JOIN medis.tb_persiapan_operasi_detail cpod ON cpod.id_CPO = cpo.id
        LEFT JOIN db_master.variabel var ON var.id_variabel=cpod.nama_dokumen AND var.id_referensi=1830
        LEFT JOIN aplikasi.pengguna per ON per.ID=cpod.oleh
        LEFT JOIN master.pegawai peg ON peg.NIP=per.NIP
        LEFT JOIN aplikasi.pengguna per2 ON per2.ID=cpod.verified_by
        LEFT JOIN master.pegawai peg2 ON peg2.NIP=per2.NIP
        WHERE cpo.id = $idCPO AND cpo.status = 1 AND cpod.status = 1
    ");
    return $query->result_array();
  }
  public function getDokumenByCPO($idCPO)
  {
    $query = $this->db->query("
      SELECT cpod.nama_dokumen AS ID_DOKUMEN, cpod.status
      FROM medis.tb_persiapan_operasi_detail cpod
      WHERE cpod.id_CPO = '$idCPO' AND cpod.status=1");
    return $query->result_array();
  }

  public function verifDokumen($id, $verified_by)
  {
    $this->db->set('verified', 2);
    $this->db->set('verified_by', $verified_by);
    $this->db->set('verified_date', date('Y-m-d H:i:s'));
    $this->db->where('id', $id);
    $this->db->update('medis.tb_persiapan_operasi_detail');
  }
  public function hapusDokumen($id, $by)
  {
    $this->db->set('dihapus_oleh', $by);
    $this->db->set('status', 0);
    $this->db->where('id', $id);
    $this->db->update('medis.tb_persiapan_operasi_detail');
  }
}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
