<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengBrak extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PengBrakModel'
    ));
  }

  public function index($idLoadNorm, $idLoadNopen, $idLoadNokun, $idLoad){
  	$norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->PengBrakModel->getNomr($nopen);
    $getTbBb = $this->PengBrakModel->getTbBb($nokun);
    $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
    if($idLoad === "00000"){
    $getPengkajian = $this->PengBrakModel->getPengkajian($getIdEmr);
    }else{
    $getPengkajian = $this->PengBrakModel->getPengkajian($idLoad);
    }
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'idLoad' => $idLoad,
      'pasien' => $getNomr,
      'getPengkajian' => $getPengkajian,
      'getTbBb' => $getTbBb,
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'kesadaran' => $this->masterModel->referensi(5),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'efeksampingNRS' => $this->masterModel->referensi(118),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'skriningResikoJatuhPusing' => $this->masterModel->referensi(120),
      'skriningResikoJatuhBerdiri' => $this->masterModel->referensi(121),
      'skriningResikoJatuh6Bulan' => $this->masterModel->referensi(122),
      'antikoagulan' => $this->masterModel->referensi(1301),
      'alatBantu' => $this->masterModel->referensi(19),
      'gigiPalsu' => $this->masterModel->referensi(752),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
    );
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/pengkajianRiBrak', $data);
  }

  public function yakinVerifPengkajian()
  {
    $post = $this->input->post();
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif' => 1,
      'verif_oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $this->db->where('id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

  public function asuhanKeperawatan_edit()
    {
        $id = $this->input->post('id');
        $idemr = $this->input->post('idemr');

        $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
        $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
        $getPengkajian = $this->PengBrakModel->getPengkajian($idemr);

        $data = array(
            'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
            'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
            'getPengkajian' => $getPengkajian,
        );

        $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
    }

  public function simpanPengkajianRiBrak($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $dataMinumTrakhir = $this->input->post('terakhirMinumTanggalRiBrak');
        $terakhirMinumTanggalRiBrak = date('Y-m-d H:i:s', strtotime($dataMinumTrakhir));

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 15,
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 15,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0 ,
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";

        $dataRiwayatKesehatan = array(
          'id_emr' => $getIdEmr,
          'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
          'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
          'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : "",
          'riwayat_cabut_gigi' => isset($post['riwayatCabutGigiRiBrak']) ? json_encode($post['riwayatCabutGigiRiBrak']) : "",
          'riwayat_pengobatan' => isset($post['riwayatPengobatanRiBrak']) ? json_encode($post['riwayatPengobatanRiBrak']) : "",
        );

        // echo "<pre>data riwayat kesehatan sebelumnya ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataKesadaran = array(
          'data_source' => 15,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data kesadaran ";print_r($dataKesadaran);echo "</pre>";

        $dataTandaVital = array(
          'data_source' => 15,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data tanda vital ";print_r($dataTandaVital);echo "</pre>";

        $dataSkriningNyeri = array(
          'nokun' => $post['nokun'],
          'data_source' => 15,
          'ref' => $getIdEmr,
          'metode' => isset($post['skrining_nyeri_RiBrak']) ? $post['skrining_nyeri_RiBrak'] : "",
          'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'provokative' => isset($post['propocative_RiBrak']) ? $post['propocative_RiBrak'] : "",
          'quality' => isset($post['quality_RiBrak']) ? $post['quality_RiBrak'] : "",
          'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
          'regio' => isset($post['regio_RiBrak']) ? $post['regio_RiBrak'] : "",
          'severity' => isset($post['severity_RiBrak']) ? $post['severity_RiBrak'] : "",
          'time' => isset($post['time_RiBrak']) ? $post['time_RiBrak'] : "",
          'ket_time' => isset($post['durasi_nyeri_RiBrak']) ? $post['durasi_nyeri_RiBrak'] : "",
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining nyeri ";print_r($dataSkriningNyeri);echo "</pre>";

        $dataPemeriksaanFisik = array(
          'id_emr' => $getIdEmr,
          'vertigo' => isset($post['skriningresikojatuhpusingRiBrak']) ? $post['skriningresikojatuhpusingRiBrak'] : "",
          'sulit_berdiri' => isset($post['skriningresikojatuhberdiriRiBrak']) ? $post['skriningresikojatuhberdiriRiBrak'] : "",
          'jatuh_dlm_6' => isset($post['skriningresikojatuh6bulanRiBrak']) ? $post['skriningresikojatuh6bulanRiBrak'] : "",
          'penggunaan_antikoagulan' => isset($post['antikoagulanRiBrak']) ? $post['antikoagulanRiBrak'] : "",
          'penggunaan_antikoagulan_ada' => isset($post['antikoagulanAda']) ? $post['antikoagulanAda'] : "",
          'terakhir_minum_tanggal' => $terakhirMinumTanggalRiBrak,
          'alat_bantu_napas_json' => isset($post['alatBantuRiBrak']) ? json_encode($post['alatBantuRiBrak']) : "",
          'bantu_napas_lain' => isset($post['deskAlatBantuRiBrak']) ? $post['deskAlatBantuRiBrak'] : "",
          'gigi_palsu_brak_array' => isset($post['gigiPalsuRiBrak']) ? json_encode($post['gigiPalsuRiBrak']) : "",
          'masalah_kesehatan_keperawatan' => isset($post['masalahKesehatanKeperawatanDewasaRiBrak']) ? json_encode($post['masalahKesehatanKeperawatanDewasaRiBrak']) : "",
        );

        // echo "<pre>data pemeriksaan fisik ";print_r($dataPemeriksaanFisik);echo "</pre>";
        // exit();
        
        $this->db->trans_begin(); 
        if (!empty($post['idemr'])) {
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 15);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          // $this->db->replace('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 15);
          $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
          // $this->db->replace('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 15);
          $this->db->update('db_pasien.tb_kesadaran', $dataKesadaran);
          // $this->db->replace('db_pasien.tb_kesadaran', $dataKesadaran);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 15);
          $this->db->update('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          // $this->db->replace('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
           $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $post['idemr']));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan, array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->insert('db_pasien.tb_kesadaran', $dataKesadaran);
          $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan, array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      }

      else if($param == 'count') {
        $result = $this->PengBrakModel->get_count();
        echo json_encode($result);
      }

      else if($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataPengBrakModel = $this->PengBrakModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataPengBrakModel
        ));
      }
    }
  }
  
  public function datatables(){
        $result = $this->MedisModel->historyPengkajian();

        $data = array();
        foreach ($result as $row){
          // $status_edit_perawat = $row -> STATUS_EDIT_PERAWAT;
          // $status_edit_medis = $row -> STATUS_EDIT_MEDIS;
          $action = "";
          $verif = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
          if($row -> ID_EMR_PERAWAT != null){
            $action .= '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>';
            if($this->session->userdata('status') == 2){
              $action .='<button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
              
              if($row -> STATUS_VERIFIKASI == 0){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-clock" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
            }
          }

          if($row -> ID_EMR_MEDIS != null){
            $action .= '<a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>';
            if($this->session->userdata('status') == 1){
              $action .='<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'"><i class="fa fa-eye"></i> lihat</button>';
              if($row -> STATUS_VERIFIKASI == 0){
                $verif = '<a class="btn btn-custom btn-block btn-sm verif" data-nokun="'.$row -> NOKUN.'" data-nopen="'.$row -> NOPEN.'" data-norm="'.$row -> NORM.'">Verif</i></a>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
            }
          }

            $sub_array = array();
            $sub_array[] = $row -> INFO;
            $sub_array[] = $verif;
            $sub_array[] = $row -> RUANGAN;
            $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $action;
            // if($STATUS_EDIT == 0){
            // $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
            // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
            // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> lihat</button>';
            // }else{
            //   $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
            // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
            // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
            // }
            $sub_array[] = $row -> USER_PERAWAT;
            $sub_array[] = $row -> USER_MEDIS;      

            $data[] = $sub_array;
        }

        $output = array(
            "draw" => intval($this->input->post("draw")),
            "data"              => $data
        );
        echo json_encode($output);
    }

}