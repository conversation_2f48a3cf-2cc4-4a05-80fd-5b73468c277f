<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Menu extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'konsultasi/KonsultasiModel',
        'rekam_medis/rawat_inap/catatanTerintegrasi/PPRAModel',
        'rekam_medis/rawat_inap/catatanTerintegrasi/NotifCpptModel',
      )
    );
  }

  public function index()
  {
    $role_menu = $this->masterModel->menu();
    $data = array();
    foreach ($role_menu->result() as $main) {
      $sub_menu = $this->masterModel->menu($main->ID);
      $menu_array = array();
      if ($sub_menu->num_rows() > 0) {
        $menu_array['label'] = $main->LABEL;
        $menu_array['link'] = $main->LINK;
        $menu_array['icon'] = $main->ICON;
        $subb = array();
        foreach ($sub_menu->result() as $sub) {
          $sub_array = array();
          $sub_menu = $this->masterModel->menu($sub->ID);

          $sub_array['label'] = $sub->LABEL;
          $sub_array['link'] = $sub->LINK;
          $subb[] = $sub_array;
        }
        $menu_array['sub'] = $subb;
      } else {
        $menu_array['label'] = $main->LABEL;
        $menu_array['link'] = $main->LINK;
        $menu_array['icon'] = $main->ICON;
      }
      $data[] = $menu_array;
    }
    echo json_encode($data);
  }

  public function jumlahNotifikasiKonsul()
  {
    $id_pengguna = $this->session->userdata('id');
    $jumlahNotif = $this->KonsultasiModel->jumlahNotifikasiKonsul($id_pengguna);
    echo json_encode($jumlahNotif);
  }

  public function notifikasiKonsul()
  {
    $id_pengguna = $this->session->userdata('id');
    $konsul = $this->pengkajianAwalModel->list_konsul($id_pengguna);
    $data = array(
      'dat' => $konsul,
      'id_pengguna' => $id_pengguna,
      'tampilNotifikasiKonsul' => $this->KonsultasiModel->notifikasiKonsul($id_pengguna),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/notifikasiKonsul/index', $data);
  }

  public function jumlahVerifikasiKonsul()
  {
    $id_pengguna = $this->session->userdata('id');
    $jumlahNotif = $this->KonsultasiModel->tabelVerifikasi($id_pengguna, 'jumlah');
    echo json_encode($jumlahNotif);
  }

  public function verifikasiKonsul()
  {
    $id_pengguna = $this->session->userdata('id');
    $tabel = $this->KonsultasiModel->tabelVerifikasi($id_pengguna, 'tabel');
    $data = array(
      'tabel' => $tabel,
      'id_pengguna' => $id_pengguna,
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/konsultasi/verifikasi/index', $data);
  }

  public function jumlahVerifikasiCPPT()
  {
    $id_pengguna = $this->session->userdata('id');
    $jumlahNotifVerifCPPT = $this->NotifCpptModel->dataNotifCppt($id_pengguna, 'jumlah')->num_rows();
    echo json_encode($jumlahNotifVerifCPPT);
  }

  public function verifikasiCPPT()
  {
    $id_pengguna = $this->session->userdata('id');
    $tabel = $this->NotifCpptModel->dataNotifCppt($id_pengguna)->result();
    $data = array(
      'tabel' => $tabel,
      'id_pengguna' => $id_pengguna,
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/notifikasiCPPT/index', $data);
  }

  public function jumlahNotifikasiKonsulPPRA()
  {
    $id_pengguna = $this->session->userdata('id');
    $jumlahNotif = $this->PPRAModel->jumlahNotifikasi($id_pengguna);
    echo json_encode($jumlahNotif);
  }

  public function notifikasiKonsulPPRA()
  {
    $id_pengguna = $this->session->userdata('id');
    $konsul = $this->pengkajianAwalModel->list_konsul($id_pengguna);
    $data = array(
      'dat' => $konsul,
      'id_pengguna' => $id_pengguna,
      'tampilNotifikasiKonsul' => $this->PPRAModel->notifikasiPPRA($id_pengguna),
    );
    $this->load->view('Pengkajian/notifikasiPPRA/index', $data);
  }

  public function radiofarmaka(){
    redirect('https://inventory.radnusa.com/');
}
    public function get_data_verif_notif_cppt(){
      $draw   = intval($this->input->POST("draw"));
      $start  = intval($this->input->POST("start"));
      $length = intval($this->input->POST("length"));
    
      $id_pengguna = $this->session->userdata('id');
      $tabel = $this->NotifCpptModel->dataNotifCppt($id_pengguna);
    
      $data = array();
      $no = 1;
      foreach ($tabel->result() as $LP) {
        
        $button = '<a id="view-cppt" data-idcppt="'.$LP->id_cppt.'" data-url="/reports/simrskd/cppt/cpptnew.php?format=pdf&id=' . $LP->id_cppt . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Verif</a>';

        $data[] = array(
            $no,
            $LP->nama_pasien,
            $LP->NORM,
            $LP->nama_ruang,
            $LP->tgl_daftar,
            $LP->tgl_lahir_umur,
            $LP->rencana_desk,
            $LP->c_tgl_input,
            $LP->nama_user,
            $button
        );
        $no++;
      }
    
      $output = array(
              "draw"            => $draw,
              "recordsTotal"    => $tabel->num_rows(),
              "recordsFiltered" => $tabel->num_rows(),
              "data"            => $data
      );
      echo json_encode($output);
    }
}

/* End of file Menu.php */
/* Location: ./application/controllers/Menu.php */