
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RujukanEksternal extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $id_pengguna = $this->session->userdata('id');
    $nomr  = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'id_pengguna' => $id_pengguna,
      'nomr'        => $nomr,
      'nopen'       => $nopen,
      'nokun'       => $nokun,
      'getNomr'     => $getNomr,
      'alasanRujukan'             => $this->masterModel->referensi(59),
      'tindakLanjutTerapirujulan' => $this->masterModel->referensi(60),
      'statusFungsionalEksternal' => $this->masterModel->referensi(61),
      'transportasiPasien'        => $this->masterModel->referensi(62),
    );

    $this->load->view('Pengkajian/emr/konsultasiEksternal/index', $data);
  }

}

/* End of file RujukanEksternal.php */
/* Location: ./application/controllers/transferRuangan/RujukanEksternal.php */
