<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TBAK extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/MedisModel',
        'rekam_medis/rawat_inap/catatanTerintegrasi/TbakDetailModel',
      )
    );
  }

  public function index()
  {
    $data = array(
      'nomr' => $this->uri->segment(2),
      'nokun' => $this->uri->segment(4),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/TBAK/index', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nomr = $this->input->post('nomr');
    $tbak = $this->TbakDetailModel->tabel($nomr);
    $data = array();
    $no = 1;
    $disabled = null;
    $statusVerif = null;
    // echo '<pre>';print_r($tbak->result());exit();

    foreach ($tbak->result() as $t) {
      if ($t->status_verif == 0) {
        if ($_SESSION['status'] == 1) {
          $disabled = null;
        } else {
          $disabled = 'disabled';
        }
        $statusVerif = '<p class="text-warning">Belum</p>';
      } elseif ($t->status_verif == 1) {
        $disabled = 'disabled';
        $statusVerif = '<p class="text-success">Sudah</p>';
      }

      $data[] = array(
        $no++ . '.',
        date('d-m-Y', strtotime($t->created_at)) . ', pukul ' . date('H.i', strtotime($t->created_at)),
        $t->ruang,
        $t->instruksi_tbak,
        $t->dokter_tbak,
        $t->oleh,
        $t->hasil_kritis == 1 ? "<i class='fa-solid fa-check'></i>" : '-',
        isset($t->waktu_lapor) ? date('d-m-Y', strtotime($t->waktu_lapor)) . ', pukul ' . date('H.i', strtotime($t->waktu_lapor)) : '-',
        $t->catatan_verif ?? '-',
        $statusVerif,
        "<button type='button' href='#modal-verifikasi-tbak' class='btn btn-sm btn-primary waves-effect tbl-verifikasi-tbak' data-toggle='modal' data-id='" . $t->id . "' " . $disabled . ">
          <i class='fa fa-check'></i> Verifikasi
        </button>",
      );
    }

    $output = array(
      'draw' => $draw,
      'data' => $data
    );
    echo json_encode($output);
  }

  public function tambah()
  {
    $post = $this->input->post();
    $data = array(
      'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/TBAK/tambah', $data);
  }

  public function tr()
  {
    $data = array(
      'listDr' => $this->masterModel->listDrUmum(),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/TBAK/tr', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {

        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $data = array();
        $i = 0;
        if (isset($post['dokter_tbak'])) {
          foreach ($post['dokter_tbak'] as $input) {
            if ($post['dokter_tbak'][$i] != '') {
              array_push(
                $data,
                array(
                  'id_cppt' => null,
                  'nokun' => $post['nokun'],
                  'dokter_tbak' => $post['dokter_tbak'][$i],
                  'instruksi_tbak' => $post['instruksi_tbak'][$i],
                  'hasil_kritis' => $post['hasil_kritis'][$i] ?? null,
                  'waktu_lapor' => $post['waktu_lapor'][$i] ?? null,
                  'created_at' => date('Y-m-d H:i:s'),
                  'status' => '1',
                  'oleh' => $this->session->userdata['id'],
                )
              );
            }
            $i++;
          }
          // echo '<pre>';print_r($data);exit();
          $this->TbakDetailModel->simpan($data);
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }
}

/* End of file TBAK.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/TBAK.php */