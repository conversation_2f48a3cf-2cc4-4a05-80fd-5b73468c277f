<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Tpn extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('masterModel');
  }

  public function index()
  {
    $dataTpnIng = $this->masterModel->masterTpnIng();
    $data = array(
      'title'         => 'Halaman Master',
      'isi'           => 'Master/tpn/index',
      'masterTpnIng'  => $dataTpnIng,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function simpanIngredients()
  {
    $post = $this->input->post();
    $data = array(
      'ingredients'     => isset($post['masterTpnIng']) ? $post['masterTpnIng'] : NULL,
      'solution_bags'   => isset($post['masterTpnSolBag']) ? $post['masterTpnSolBag'] : NULL,
      'created_by'      => $this->session->userdata('id'),
    );
    $this->db->insert('medis.tb_ingredients', $data);
  }

  public function simpanIngredientsChild()
  {
    $post = $this->input->post();
    $data = array(
      'id_ingredients'     => isset($post['idIngredients']) ? $post['idIngredients'] : NULL,
      'preparation_used'   => isset($post['inputChildIngPreUsed']) ? $post['inputChildIngPreUsed'] : NULL,
      'present_solution'   => isset($post['inputChildIngPreInSol']) ? $post['inputChildIngPreInSol'] : NULL,
      'konsentrasi'   => isset($post['inputChildIngKonsen']) ? $post['inputChildIngKonsen'] : NULL,
      'dalam'   => isset($post['inputChildIngDlm']) ? $post['inputChildIngDlm'] : NULL,
      'osmolarity'   => isset($post['inputChildIngOsmola']) ? $post['inputChildIngOsmola'] : NULL,
      'kalori'   => isset($post['inputChildIngKal']) ? $post['inputChildIngKal'] : NULL,
      'created_by'      => $this->session->userdata('id'),
    );
    $this->db->insert('medis.tb_ingredients_child', $data);
  }

  public function ubahSttsIngredients()
  {
    $post = $this->input->post();
    $data = array(
      'status'      => $post['cek'],
    );
    $this->db->where('tb_ingredients.id', $post['id']);
    $this->db->update('medis.tb_ingredients', $data);
  }

  public function ubahSttsIngredientsChild()
  {
    $post = $this->input->post();
    $data = array(
      'status'      => $post['cek'],
    );
    $this->db->where('tb_ingredients_child.id', $post['id']);
    $this->db->update('medis.tb_ingredients_child', $data);
  }

  public function viewInputTpnChild()
  {
    $post = $this->input->post();
    $data = array(
      'id'    => $post['id'],
      'nama'  => $post['nama'],
      );
    $this->load->view('Master/tpn/viewInputTpnChild', $data);
  }

  function getChildIngredients(){
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));
    $id = $this->input->post('id');

    $listChild = $this->masterModel->getChildIngredients($id);

    $data = array();
    $no = $_POST['start'];
    foreach ($listChild as $LI) {
      $no++;

      $button = '<td>
      <div class="checkbox checkbox-primary">
      <input type="checkbox" name="cekStatusTpnIngChild" id="cekStatusTpnIngChild_'.$LI->id.'" '.$LI->cek_status.' data-id="'.$LI->id.'" class="cekStatusTpnIngChild">
      <label for="cekStatusTpnIngChild_'.$LI->id.'"></label>
      </div>
      </td>';

      $data[] = array(
        $LI->preparation_used,
        $LI->present_solution,
        $LI->konsentrasi,
        $LI->dalam,
        $LI->osmolarity,
        $LI->kalori,
        $button

      );
      
    }

    $output = array(
      "draw"            => $draw,
      "data"            => $data
    );
    echo json_encode($output);
  }

}
