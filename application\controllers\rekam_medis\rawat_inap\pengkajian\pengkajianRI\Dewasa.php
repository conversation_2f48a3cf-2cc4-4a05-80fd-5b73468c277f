<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>wasa extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel', 'rekam_medis/MedisModel'));
  }

  public function index($idLoadNorm, $idLoadNopen, $idLoadNokun, $idLoad)
  {
    $norm = $this->uri->segment(7);
    
    if ($idLoad === "00000") {
      $nopen = $this->uri->segment(8);
      $nokun = $this->uri->segment(9);
      $getNomr = $this->DewasaModel->getNomrRawatInap($nopen);
      $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
      $getPengkajian = $this->DewasaModel->getPengkajian($getIdEmr);
    } else {
      $getPengkajian = $this->DewasaModel->getPengkajian($idLoad);
      $nopen = $getPengkajian['NOPEN'];
      $nokun = $getPengkajian['KUNJUNGAN'];
      $getNomr = $this->DewasaModel->getNomrRawatInap($nopen);
      $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
    }

    // START INDEKS BARTHEL
    $indeksBarthel = $this->DewasaModel->hasilIndeksBarthel($nokun);
    if (isset($indeksBarthel)) {
      $totalSkorIndeksBarthel = $indeksBarthel['rangsang_bab'] + $indeksBarthel['rangsang_berkemih'] + $indeksBarthel['bersihkan_diri'] + $indeksBarthel['penggunaan_kloset'] + $indeksBarthel['makan'] + $indeksBarthel['berubah_posisi'] + $indeksBarthel['berpindah'] + $indeksBarthel['memakai_baju'] + $indeksBarthel['naik_tangga'] + $indeksBarthel['mandi'];

      if ($totalSkorIndeksBarthel == 20) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 4) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 8) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 11) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 19) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
      }
    }

    $jumlahIndeksBarthel = $this->DewasaModel->get_count_indeksBarthel($nokun);
    // END INDEKS BARTHEL

    // START SKALA ONTARIO
    $skalaOntario = $this->DewasaModel->hasilSkalaOntario($nokun);
    if (isset($skalaOntario)) {
      $totalSkalaOntario = $skalaOntario['total'];

      if ($totalSkalaOntario >= 0 && $totalSkalaOntario <= 5) {
        $hasilSkalaOntario = "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Rendah</b> ( Skala Ontario ) </div>";
      } elseif ($totalSkalaOntario >= 6 && $totalSkalaOntario <= 16) {
        $hasilSkalaOntario = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Sedang</b> ( Skala Ontario ) </div>";
      } elseif ($totalSkalaOntario >= 17) {
        $hasilSkalaOntario = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Tinggi</b> ( Skala Ontario ) </div>";
      }
    }

    $jumlahSkalaOntario = $this->DewasaModel->get_count_skalaOntario($nokun);
    // END SKALA ONTARIO

    // START SKALA MORSE
    $skalaMorse = $this->DewasaModel->hasilSkalaMorse($nokun);
    if (isset($skalaMorse)) {
      $totalSkalaMorse = $skalaMorse['NILAI_1'] + $skalaMorse['NILAI_2'] + $skalaMorse['NILAI_3'] + $skalaMorse['NILAI_4'] + $skalaMorse['NILAI_5'] + $skalaMorse['NILAI_6'];

      if ($totalSkalaMorse < 25) {
        $hasilSkalaMorse = "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaMorse . "</b> = <b>Tidak risiko</b> ( Skala Morse )</div>";
      } elseif ($totalSkalaMorse < 45) {
        $hasilSkalaMorse = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko Rendah</b> ( Skala Morse )</div>";
      } elseif ($totalSkalaMorse >= 45) {
        $hasilSkalaMorse = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko tinggi</b> ( Skala Morse )</div>";
      }
    }

    $jumlahSkalaMorse = $this->DewasaModel->get_count_skalaMorse($nokun);
    // END SKALA MORSE

    // START SKRINING PERASAAN TERTEKAN
    $skriningPerasaanTerkekan = $this->DewasaModel->hasilSkriningPerasaanTertekan($nokun);
    if (isset($skriningPerasaanTerkekan)) {
      $scorePerasaan = $skriningPerasaanTerkekan['thermometer'];

      if ($scorePerasaan == 527) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>0</b></div>";
      } else if ($scorePerasaan == 528) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>1</b></div>";
      } else if ($scorePerasaan == 529) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>2</b></div>";
      } else if ($scorePerasaan == 530) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>3</b></div>";
      } else if ($scorePerasaan == 531) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>4</b></div>";
      } else if ($scorePerasaan == 532) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>5</b></div>";
      } else if ($scorePerasaan == 533) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>6</b></div>";
      } else if ($scorePerasaan == 534) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>7</b></div>";
      } else if ($scorePerasaan == 535) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>8</b></div>";
      } else if ($scorePerasaan == 536) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>9</b></div>";
      } else if ($scorePerasaan == 537) {
        $hasilScorePerasaan = "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>10</b></div>";
      }
    }

    $jumlahPerasaanTertekan = $this->DewasaModel->get_count_skriningPerasaanTertekan($nokun);
    // END SKRINING PERASAAN TERTEKAN

    // START SKALA BRADEN
    $skalaBraden = $this->DewasaModel->hasilSkalaBraden($nokun);
    if (isset($skalaBraden)) {
      $skorSkalaBraden = $skalaBraden['skor'];
      $tingkatSkalaBraden = $skalaBraden['tingkat'];

      if ($tingkatSkalaBraden == 0) {
        if ($skorSkalaBraden == 23 || $skorSkalaBraden == 22 || $skorSkalaBraden == 21 || $skorSkalaBraden == 20 || $skorSkalaBraden == 19 || $skorSkalaBraden == 18 || $skorSkalaBraden == 17 || $skorSkalaBraden == 16 || $skorSkalaBraden == 15) {
          $hasilSkalaBraden = "<div class='alert alert-success' role='alert'>Score <b>" . $skorSkalaBraden . "</b> . Berisiko, penkes dan berikan leaflet Pencegahan Luka Tekan.</div>";
        }
      } elseif ($tingkatSkalaBraden == 1) {
        if ($skorSkalaBraden == 23 || $skorSkalaBraden == 22 || $skorSkalaBraden == 21 || $skorSkalaBraden == 20 || $skorSkalaBraden == 19 || $skorSkalaBraden == 18 || $skorSkalaBraden == 17 || $skorSkalaBraden == 16 || $skorSkalaBraden == 15) {
          $hasilSkalaBraden = "<div class='alert alert-warning' role='alert'>Score <b>" . $skorSkalaBraden . "</b> . Risiko sedang, ubah posisi setiap 4-6 jam, lindungi tulang yang menonjol, jaga kelembaban kulit, jaga intake nutrisi atau hidrasi, hindari gerakan atau lecet.</div>";
        } elseif ($skorSkalaBraden == 14 || $skorSkalaBraden == 13 || $skorSkalaBraden == 12 || $skorSkalaBraden == 11 || $skorSkalaBraden == 10 || $skorSkalaBraden == 9 || $skorSkalaBraden == 8 || $skorSkalaBraden == 7 || $skorSkalaBraden == 6 || $skorSkalaBraden == 5 || $skorSkalaBraden == 4 || $skorSkalaBraden == 3 || $skorSkalaBraden == 2 || $skorSkalaBraden == 1) {
          $hasilSkalaBraden = "<div class='alert alert-warning' role='alert'>Score <b>" . $skorSkalaBraden . "</b> . Risiko sedang, ubah posisi setiap 4-6 jam, lindungi tulang yang menonjol, jaga kelembaban kulit, jaga intake nutrisi atau hidrasi, hindari gerakan atau lecet.</div>";
        }
      } elseif ($tingkatSkalaBraden == 2) {
        if ($skorSkalaBraden == 14 || $skorSkalaBraden == 13 || $skorSkalaBraden == 12 || $skorSkalaBraden == 11 || $skorSkalaBraden == 10 || $skorSkalaBraden == 9 || $skorSkalaBraden == 8 || $skorSkalaBraden == 7 || $skorSkalaBraden == 6 || $skorSkalaBraden == 5 || $skorSkalaBraden == 4 || $skorSkalaBraden == 3 || $skorSkalaBraden == 2 || $skorSkalaBraden == 1) {
          $hasilSkalaBraden = "<div class='alert alert-danger' role='alert'>Score <b>" . $skorSkalaBraden . "</b> . Risiko tinggi, ubah posisi 2-4 jam, k/p gunakan kasur dekubitus, gunakan bantal untuk memberi kemiringan 30°, lindungi tulang yang menonjol, jaga kelembaban kulit, jaga intake nutrisi atau hidrasi, hindari gerakan atau lecet.</div>";
        }
      }
    }

    $jumlahSkalaBraden = $this->DewasaModel->get_count_skalaBraden($nokun);
    // END SKALA BRADEN

    $data = array(
      'hasilSkorIndeksBarthel' => isset($hasilSkorIndeksBarthel) ? $hasilSkorIndeksBarthel : null,
      'hasilSkalaMorse' => isset($hasilSkalaMorse) ? $hasilSkalaMorse : null,
      'hasilScorePerasaan' => isset($hasilScorePerasaan) ? $hasilScorePerasaan : null,
      'hasilSkalaBraden' => isset($hasilSkalaBraden) ? $hasilSkalaBraden : null,
      'hasilSkalaOntario' => isset($hasilSkalaOntario) ? $hasilSkalaOntario : null,

      'jumlahIndeksBarthel' => isset($jumlahIndeksBarthel) ? $jumlahIndeksBarthel : null,
      'jumlahSkalaMorse' => isset($jumlahSkalaMorse) ? $jumlahSkalaMorse : null,
      'jumlahPerasaanTertekan' => isset($jumlahPerasaanTertekan) ? $jumlahPerasaanTertekan : null,
      'jumlahSkalaBraden' => isset($jumlahSkalaBraden) ? $jumlahSkalaBraden : null,
      'jumlahSkalaOntario' => isset($jumlahSkalaOntario) ? $jumlahSkalaOntario : null,

      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'idLoad' => $idLoad,
      'user_perawat' => $this->session->userdata("id"),
      'getPengkajian' => $getPengkajian,
      'pasien' => $getNomr,
      'masalahKesehatan' => $this->masterModel->formMasalahKesehatan(),
      'asupanEnergi' => $this->masterModel->referensi(1202),
      'penurunanLemak' => $this->masterModel->referensi(1204),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'penurunanOtot' => $this->masterModel->referensi(1205),
      'akumulasiCairan' => $this->masterModel->referensi(1206),
      'diagnosis' => $this->masterModel->referensi(1207),
      'jalur' => $this->masterModel->referensi(1208),
      'enteral' => $this->masterModel->referensi(1209),
      'anamnesis' => $this->masterModel->referensi(54),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'riwayatTransfusiDarah' => $this->masterModel->referensi(140),
      'kebiasaanmerokok' => $this->masterModel->referensi(142),
      'riwayatKanker' => $this->masterModel->referensi(1),
      'riwayatMetabolik' => $this->masterModel->referensi(143),
      'riwayatDDK' => $this->masterModel->referensi(145),
      'riwayatTransfusiDarahDesk' => $this->masterModel->referensi(141),
      'kesadaran' => $this->masterModel->referensi(5),
      'komponenPenilaian' => $this->masterModel->referensi(22),
      'komponenPenilaianAsupan' => $this->masterModel->referensi(23),
      'pendidikan' => $this->masterModel->referensi(24),
      'bahasaSehari' => $this->masterModel->referensi(25),
      'perluPenerjemah' => $this->masterModel->referensi(26),
      'kesediaanInformasi' => $this->masterModel->referensi(27),
      'hambatan' => $this->masterModel->referensi(28),
      'kebutuhanPembelajaran' => $this->masterModel->referensi(29),
      'gastroMulut' => $this->masterModel->referensi(206),
      'gastroEsophagus' => $this->masterModel->referensi(1122),
      'gastroAbdomen' => $this->masterModel->referensi(207),
      'thermometer' => $this->masterModel->referensi(170),
      'sirkulasiHidung' => $this->masterModel->referensi(1112),
      'sirkulasiDada' => $this->masterModel->referensi(1113),
      'sirkulasiJantung' => $this->masterModel->referensi(1114),
      'sirkulasiPacuJantung' => $this->masterModel->referensi(1115),
      'sirkulasiPadaParu' => $this->masterModel->referensi(1116),
      'sirkulasiPerdarahan' => $this->masterModel->referensi(1117),
      'sirkulasiTurgorKulit' => $this->masterModel->referensi(1118),
      'sirkulasiCrt' => $this->masterModel->referensi(1120),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'efeksampingNRS' => $this->masterModel->referensi(118),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'aktivitasIstirahat' => $this->masterModel->referensi(1123),
      'aktivitasKemampuan' => $this->masterModel->referensi(1124),
      'integritasKondisiKulit' => $this->masterModel->referensi(1121),
      'integritasLuka' => $this->masterModel->referensi(1125),
      'seksualGenitalia' => $this->masterModel->referensi(1126),
      'seksualKontrasepsi' => $this->masterModel->referensi(1127),
      'seksualSirkumsisi' => $this->masterModel->referensi(1128),
      'eliminasiDefekasi' => $this->masterModel->referensi(1129),
      'eliminasiMiksi' => $this->masterModel->referensi(1130),
      'keselamatanStatusMental' => $this->masterModel->referensi(1131),
      'keselamatanGangguan' => $this->masterModel->referensi(1132),
      'keselamatanPotensi' => $this->masterModel->referensi(1133),
      'programPengobatanDenganKeyakinan' => $this->masterModel->referensi(17),
      'pengobatanAlternatif' => $this->masterModel->referensi(146),
      'pengobatanBudaya' => $this->masterModel->referensi(147),
      'pasienPerluP3' => $this->masterModel->referensi(1134),
      'P31' => $this->masterModel->referensi(1135),
      'P32' => $this->masterModel->referensi(1136),
      'P33' => $this->masterModel->referensi(1137),
      'P34' => $this->masterModel->referensi(1138),
      'P35' => $this->masterModel->referensi(1139),
      'P36' => $this->masterModel->referensi(1140),
      'P37' => $this->masterModel->referensi(1141),
      'P38' => $this->masterModel->referensi(1142),
      'P39' => $this->masterModel->referensi(1143),
      'P310' => $this->masterModel->referensi(1144),
      'listRangsangbab' => $this->masterModel->referensi(834),
      'listRangsangberkemih' => $this->masterModel->referensi(835),
      'listMembersihkandiri' => $this->masterModel->referensi(836),
      'listPenggunaankloset' => $this->masterModel->referensi(837),
      'listMakan' => $this->masterModel->referensi(838),
      'listBerubahposisi' => $this->masterModel->referensi(839),
      'listBerpindah' => $this->masterModel->referensi(840),
      'listMemakaibaju' => $this->masterModel->referensi(841),
      'listNaiktangga' => $this->masterModel->referensi(842),
      'listMandi' => $this->masterModel->referensi(843),
      'persepsiSensori' => $this->masterModel->referensi(1154),
      'kelembaban' => $this->masterModel->referensi(1155),
      'aktivitas' => $this->masterModel->referensi(1156),
      'mobilitas' => $this->masterModel->referensi(1157),
      'nutrisi' => $this->masterModel->referensi(1158),
      'gesekan' => $this->masterModel->referensi(1159),
      'apakahInginMasukEws' => $this->masterModel->referensi(1162),
      'cekEdema' => $this->masterModel->referensi(569),
      'jenisNutrisi' => $this->masterModel->referensi(1164),
      'PENGGUNAAN_O2' => $this->masterModel->referensi(129),
      'listRiwayatJatuh' => $this->masterModel->referensi(1006),
      'listDiagnosisSekunder' => $this->masterModel->referensi(1007),
      'listAlatBantu' => $this->masterModel->referensi(1008),
      'listMenggunakanInfus' => $this->masterModel->referensi(1009),
      'listCaraBerjalan' => $this->masterModel->referensi(1010),
      'listStatusMental' => $this->masterModel->referensi(1011),
      'listKarenaJatuh' => $this->masterModel->referensi(1674),
      'listJatuh2Bln' => $this->masterModel->referensi(1675),
      'listPasienDilirium' => $this->masterModel->referensi(1676),
      'listPasienDisorientasi' => $this->masterModel->referensi(1677),
      'listPasienAgitasi' => $this->masterModel->referensi(1678),
      'listPakaiKacamata' => $this->masterModel->referensi(1679),
      'listPenglihatanBuram' => $this->masterModel->referensi(1680),
      'listPasienGlaukoma' => $this->masterModel->referensi(1681),
      'listPerilakuBerkemih' => $this->masterModel->referensi(1682),
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/pengkajianRiDewasa', $data);
  }

  public function asuhanKeperawatan_edit()
  {
    $id = $this->input->post('id');
    $idemr = $this->input->post('idemr');

    $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
    $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
    $getPengkajian = $this->DewasaModel->getPengkajian($idemr);

    $data = array(
      'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
      'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
      'getPengkajian' => $getPengkajian,
    );

    $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
  }

  public function masalahKesehatan_edit()
  {
    $id = $this->input->post('id');
    $idemr = $this->input->post('idemr');

    $resultMasalahKesehatan = $this->masterModel->masalahKesehatan($id);
    $resultMasalahKesehatanDetil = $this->masterModel->masalahKesehatanDetil($resultMasalahKesehatan->ID);
    $getPengkajian = $this->DewasaModel->getPengkajian($idemr);

    $data = array(
      'titleMasalahKesehatan' => $resultMasalahKesehatan->KATEGORI,
      'DataMasalahKesehatan' => $resultMasalahKesehatanDetil,
      'getPengkajian' => $getPengkajian,
    );

    $this->load->view('Pengkajian/emr/masalahKesehatan/masalahKesehatan_edit', $data);
  }

  public function simpanIndeksBarthel()
  {
    $nokun = $this->input->post('nokun');
    $total_barthel_1 = $this->input->post('total_barthel_1');
    $total_barthel_2 = $this->input->post('total_barthel_2');
    $total_barthel_3 = $this->input->post('total_barthel_3');
    $total_barthel_4 = $this->input->post('total_barthel_4');
    $total_barthel_5 = $this->input->post('total_barthel_5');
    $total_barthel_6 = $this->input->post('total_barthel_6');
    $total_barthel_7 = $this->input->post('total_barthel_7');
    $total_barthel_8 = $this->input->post('total_barthel_8');
    $total_barthel_9 = $this->input->post('total_barthel_9');
    $total_barthel_10 = $this->input->post('total_barthel_10');
    $oleh = $this->session->userdata("id");

    $totalSkorIndeksBarthel = $total_barthel_1 + $total_barthel_2 + $total_barthel_3 + $total_barthel_4 + $total_barthel_5 + $total_barthel_6 + $total_barthel_7 + $total_barthel_8 + $total_barthel_9 + $total_barthel_10;

    if ($totalSkorIndeksBarthel == 20) {
      echo "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 4) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 8) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 11) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 19) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 5,
      'rangsang_bab' => $total_barthel_1,
      'rangsang_berkemih' => $total_barthel_2,
      'bersihkan_diri' => $total_barthel_3,
      'penggunaan_kloset' => $total_barthel_4,
      'makan' => $total_barthel_5,
      'berubah_posisi' => $total_barthel_6,
      'berpindah' => $total_barthel_7,
      'memakai_baju' => $total_barthel_8,
      'naik_tangga' => $total_barthel_9,
      'mandi' => $total_barthel_10,
      'oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->DewasaModel->get_count_indeksBarthel($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_barthel_indek', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 5);
      $this->db->update('keperawatan.tb_barthel_indek', $data);
    }
  }

  public function simpanSkriningPerasaanTertekan()
  {
    $post = $this->input->post();
    $scorePerasaan = $this->input->post('thermometerSkriningPerTerRiD');
    if ($scorePerasaan == 527) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>0</b></div>";
    } else if ($scorePerasaan == 528) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>1</b></div>";
    } else if ($scorePerasaan == 529) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>2</b></div>";
    } else if ($scorePerasaan == 530) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>3</b></div>";
    } else if ($scorePerasaan == 531) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>4</b></div>";
    } else if ($scorePerasaan == 532) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>5</b></div>";
    } else if ($scorePerasaan == 533) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>6</b></div>";
    } else if ($scorePerasaan == 534) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>7</b></div>";
    } else if ($scorePerasaan == 535) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>8</b></div>";
    } else if ($scorePerasaan == 536) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>9</b></div>";
    } else if ($scorePerasaan == 537) {
      echo "<div class='alert alert-info' role='alert'> Score perasaan tertekan yang dialami oleh pasien = <b>10</b></div>";
    }
    $dataSkrining = array(
      'nokun' => $post['nokun'],
      'ref' => 5,
      'thermometer' => $post['thermometerSkriningPerTerRiD'],
      'mengasuh_anak' => $post['mengasuh_anakSkriningPerTerRiD'],
      'penampilan' => $post['penampilanSkriningPerTerRiD'],
      'pekerjaan_rumah' => $post['pekerjaan_rumahSkriningPerTerRiD'],
      'mandi_pakaian' => $post['mandi_pakaianSkriningPerTerRiD'],
      'asuransi_keuangan' => $post['asuransi_keuanganSkriningPerTerRiD'],
      'bernapas' => $post['bernapasSkriningPerTerRiD'],
      'transportasi' => $post['transportasiSkriningPerTerRiD'],
      'perubahan_berkemih' => $post['perubahan_berkemihSkriningPerTerRiD'],
      'pekerjaan_sekolah' => $post['pekerjaan_sekolahSkriningPerTerRiD'],
      'sembelit' => $post['sembelitSkriningPerTerRiD'],
      'diare' => $post['diareSkriningPerTerRiD'],
      'makan' => $post['makanSkriningPerTerRiD'],
      'hubungan_anak' => $post['hubungan_anakSkriningPerTerRiD'],
      'kelelahan' => $post['kelelahanSkriningPerTerRiD'],
      'hubungan_pasangan' => $post['hubungan_pasanganSkriningPerTerRiD'],
      'merasa_kembung' => $post['merasa_kembungSkriningPerTerRiD'],
      'memiliki_anak' => $post['memiliki_anakSkriningPerTerRiD'],
      'demam' => $post['demamSkriningPerTerRiD'],
      'pusing' => $post['pusingSkriningPerTerRiD'],
      'gangguan_cerna' => $post['gangguan_cernaSkriningPerTerRiD'],
      'depresi' => $post['depresiSkriningPerTerRiD'],
      'ingatan_konsen' => $post['ingatan_konsenSkriningPerTerRiD'],
      'ketakutan' => $post['ketakutanSkriningPerTerRiD'],
      'sariawan' => $post['sariawanSkriningPerTerRiD'],
      'gugup' => $post['gugupSkriningPerTerRiD'],
      'mual' => $post['mualSkriningPerTerRiD'],
      'kesedihan' => $post['kesedihanSkriningPerTerRiD'],
      'hidung_mampet' => $post['hidung_mampetSkriningPerTerRiD'],
      'khawatir' => $post['khawatirSkriningPerTerRiD'],
      'nyeri' => $post['nyeriSkriningPerTerRiD'],
      'hilang_minat' => $post['hilang_minatSkriningPerTerRiD'],
      'seksual' => $post['seksualSkriningPerTerRiD'],
      'kulit_kering' => $post['kulit_keringSkriningPerTerRiD'],
      'spiritual' => $post['spiritualSkriningPerTerRiD'],
      'tidur' => $post['tidurSkriningPerTerRiD'],
      'kesemutan' => $post['kesemutanSkriningPerTerRiD'],
      'masalah_lainnya' => $post['masalah_lainnyaSkriningPerTerRiD'],
      'oleh' => $this->session->userdata("id")
    );
    // echo "<pre>";print_r($dataSkrining);echo "</pre>";exit();

    $jumlah = $this->DewasaModel->get_count_skriningPerasaanTertekan($post['nokun']);
    if ($jumlah == 0) {
      $this->db->insert('db_pasien.tb_skrining_perasaan_tertekan', $dataSkrining);
    } else {
      $this->db->where('nokun', $post['nokun']);
      $this->db->where('ref', 5);
      $this->db->update('db_pasien.tb_skrining_perasaan_tertekan', $dataSkrining);
    }
  }

  public function simpanSkalaBraden()
  {
    $post = $this->input->post();
    $nokun = $this->input->post('nokun');
    $oleh = $this->session->userdata("id");

    if ($post['skor_braden_rendah'] != '' && $post['skor_braden_sedang'] == '' && $post['skor_braden_tinggi'] == '') {
      $skor = $post['skor_braden_rendah'];
      $tingkat = 0;
    } elseif ($post['skor_braden_rendah'] == '' && $post['skor_braden_sedang'] != '' && $post['skor_braden_tinggi'] == '') {
      $skor = $post['skor_braden_sedang'];
      $tingkat = 1;
    } elseif ($post['skor_braden_rendah'] == '' && $post['skor_braden_sedang'] == '' && $post['skor_braden_tinggi'] != '') {
      $skor = $post['skor_braden_tinggi'];
      $tingkat = 2;
    }

    if ($tingkat == 0) {
      if ($skor == 23 || $skor == 22 || $skor == 21 || $skor == 20 || $skor == 19 || $skor == 18 || $skor == 17 || $skor == 16 || $skor == 15) {
        echo "<div class='alert alert-success' role='alert'>Score <b>" . $skor . "</b> . Berisiko, penkes dan berikan leaflet Pencegahan Luka Tekan.</div>";
      }
    } elseif ($tingkat == 1) {
      if ($skor == 23 || $skor == 22 || $skor == 21 || $skor == 20 || $skor == 19 || $skor == 18 || $skor == 17 || $skor == 16 || $skor == 15) {
        echo "<div class='alert alert-warning' role='alert'>Score <b>" . $skor . "</b> . Risiko sedang, ubah posisi setiap 4-6 jam, lindungi tulang yang menonjol, jaga kelembaban kulit, jaga intake nutrisi atau hidrasi, hindari gerakan atau lecet.</div>";
      } elseif ($skor == 14 || $skor == 13 || $skor == 12 || $skor == 11 || $skor == 10 || $skor == 9 || $skor == 8 || $skor == 7 || $skor == 6 || $skor == 5 || $skor == 4 || $skor == 3 || $skor == 2 || $skor == 1) {
        echo "<div class='alert alert-warning' role='alert'>Score <b>" . $skor . "</b> . Risiko sedang, ubah posisi setiap 4-6 jam, lindungi tulang yang menonjol, jaga kelembaban kulit, jaga intake nutrisi atau hidrasi, hindari gerakan atau lecet.</div>";
      }
    } elseif ($tingkat == 2) {
      if ($skor == 14 || $skor == 13 || $skor == 12 || $skor == 11 || $skor == 10 || $skor == 9 || $skor == 8 || $skor == 7 || $skor == 6 || $skor == 5 || $skor == 4 || $skor == 3 || $skor == 2 || $skor == 1) {
        echo "<div class='alert alert-danger' role='alert'>Score <b>" . $skor . "</b> . Risiko tinggi, ubah posisi 2-4 jam, k/p gunakan kasur dekubitus, gunakan bantal untuk memberi kemiringan 30°, lindungi tulang yang menonjol, jaga kelembaban kulit, jaga intake nutrisi atau hidrasi, hindari gerakan atau lecet.</div>";
      }
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 5,
      'tanggal' => $post['tanggalRiD'],
      'persepsi_sensori' => $post['persepsi_sensori'],
      'kelembaban' => $post['kelembaban'],
      'aktivitas' => $post['aktivitas'],
      'mobilitas' => $post['mobilitas'],
      'nutrisi' => $post['nutrisi'],
      'gesekan' => $post['gesekan'],
      'skor' => $skor,
      'naikkan_risiko' => $post['naikkan_risiko'],
      'tingkat' => $tingkat,
      'oleh' => $oleh,
      'status' => '1',
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->DewasaModel->get_count_skalaBraden($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_skala_braden', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 5);
      $this->db->update('keperawatan.tb_skala_braden', $data);
    }
  }

  public function yakinVerifPengkajian()
  {
    $post = $this->input->post();
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif' => 1,
      'verif_oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $this->db->where('id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

  public function yakinVerifPengkajianOlehPerawat()
  {
    $post = $this->input->post();
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif_perawat' => 1,
      'verif_oleh_perawat' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $this->db->where('tb_keperawatan.id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

  public function simpanSkalaOntario()
  {
    $nokun = $this->input->post('nokun');
    $post = $this->input->post();
    $totalSkalaOntario = $this->input->post('totalSkalaOntarioMASS');

    $oleh = $this->session->userdata("id");

    if ($totalSkalaOntario >= 0 && $totalSkalaOntario <= 5) {
      echo "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Rendah</b> ( Skala Ontario ) </div>";
    } elseif ($totalSkalaOntario >= 6 && $totalSkalaOntario <= 16) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Sedang</b> ( Skala Ontario ) </div>";
    } elseif ($totalSkalaOntario >= 17) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Tinggi</b> ( Skala Ontario ) </div>";
    }

    $data = array(
      'nokun' => $post['nokun'],
      'ref' => 5,
      // 'diagnosa_masuk' => isset($post['diagnosa_masuk']) ? $post['diagnosa_masuk']: "",
      'tanggal' => isset($post['tanggalRiD']) ? date('Y-m-d', strtotime($post['tanggalRiD'])) : "",
      'karena_jatuh' => isset($post['karena_jatuh']) ? $post['karena_jatuh'] : "",
      'jatuh_2_bln' => isset($post['jatuh_2_bln']) ? $post['jatuh_2_bln'] : "",
      'pasien_dirilium' => isset($post['pasien_dirilium']) ? $post['pasien_dirilium'] : "",
      'pasien_disorientasi' => isset($post['pasien_disorientasi']) ? $post['pasien_disorientasi'] : "",
      'pasien_agitasi' => isset($post['pasien_agitasi']) ? $post['pasien_agitasi'] : "",
      'pakai_kacamata' => isset($post['pakai_kacamata']) ? $post['pakai_kacamata'] : "",
      'penglihatan_buram' => isset($post['penglihatan_buram']) ? $post['penglihatan_buram'] : "",
      'pasien_glaukoma' => isset($post['pasien_glaukoma']) ? $post['pasien_glaukoma'] : "",
      'perilaku_berkemih' => isset($post['perilaku_berkemih']) ? $post['perilaku_berkemih'] : "",
      'transfer' => isset($post['transfer']) ? $post['transfer'] : "",
      'mobilitas' => isset($post['mobilitas']) ? $post['mobilitas'] : "",
      'total' => isset($post['totalSkalaOntarioMASS']) ? $post['totalSkalaOntarioMASS'] : "",
      'tingkat_risiko' => isset($post['tingkat_risiko']) ? $post['tingkat_risiko'] : "",
      'oleh' => $this->session->userdata('id')
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->DewasaModel->get_count_skalaOntario($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_skala_ontario_mass', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 5);
      $this->db->update('keperawatan.tb_skala_ontario_mass', $data);
    }
  }

  public function simpanSkalaMorse()
  {
    $nokun = $this->input->post('nokun');
    $riwayatJatuhBaruIniSkalaMorse = $this->input->post('riwayatJatuhBaruIniSkalaMorse');
    $diagnosisSekunderSkalaMorse = $this->input->post('diagnosisSekunderSkalaMorse');
    $alatBantuJalanSkalaMorse = $this->input->post('alatBantuJalanSkalaMorse');
    $menggunakanInfusSkalaMorse = $this->input->post('menggunakanInfusSkalaMorse');
    $caraBerjalanSkalaMorse = $this->input->post('caraBerjalanSkalaMorse');
    $statusMentalSkalaMorse = $this->input->post('statusMentalSkalaMorse');
    $totalSkalaMorse = $this->input->post('totalSkalaMorse');
    $oleh = $this->session->userdata("id");

    if ($totalSkalaMorse < 25) {
      echo "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaMorse . "</b> = <b>Tidak risiko</b> ( Skala Morse )</div>";
    } elseif ($totalSkalaMorse < 45) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko Rendah</b> ( Skala Morse )</div>";
    } elseif ($totalSkalaMorse >= 45) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko tinggi</b> ( Skala Morse )</div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 5,
      'riwayat_jatuh' => $riwayatJatuhBaruIniSkalaMorse,
      'diagnosis_sekunder' => $diagnosisSekunderSkalaMorse,
      'alat_bantu' => $alatBantuJalanSkalaMorse,
      'infus' => $menggunakanInfusSkalaMorse,
      'cara_berjalan' => $caraBerjalanSkalaMorse,
      'status_mental' => $statusMentalSkalaMorse,
      'oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->DewasaModel->get_count_skalaMorse($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 5);
      $this->db->update('keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa', $data);
    }
  }

  public function simpanPengkajianRiD($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');
        $masukEwsAtauTidak = $this->input->post('masukKeEwsRiD');
        $id_ews = $this->input->post('id_ews');
        $id_tanda_vital = $this->input->post('id_tanda_vital');
        $id_kesadaran = $this->input->post('id_kesadaran');
        $id_o2 = $this->input->post('id_o2');

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 5,
          // 'rujukan' => $post['rujukanRiD'],
          'diagnosa_masuk' => $post['rujukanRiD'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataRiwayatKesehatan = array(
          'id_emr' => $getIdEmr,
          'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
          'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
          'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : "",
          'riwayat_transfusi' => isset($post['riwayat_transfusi_darah']) ? $post['riwayat_transfusi_darah'] : "",
          'reaksi_transfusi' => isset($post['riwayat_transfusi_darah_desk']) ? $post['riwayat_transfusi_darah_desk'] : "",
          'isi_reaksi_transfusi' => isset($post['riwayat_td_reaksi_alergi']) ? json_encode($post['riwayat_td_reaksi_alergi']) : "",
          'kebiasaan' => isset($post['kebiasaan_merokok']) ? $post['kebiasaan_merokok'] : "",
          'isi_kebiasaan' => isset($post['kebiasaan_merokok_desk']) ? json_encode($post['kebiasaan_merokok_desk']) : "",
          'riwayat_kanker' => isset($post['riwayat_kanker_keluarga']) ? $post['riwayat_kanker_keluarga'] : "",
          'isi_kanker' => isset($post['riwayat_kanker_keluarga_desk']) ? json_encode($post['riwayat_kanker_keluarga_desk']) : "",
          'riwayat_metabolik' => isset($post['riwayat_metabolik_keluarga']) ? $post['riwayat_metabolik_keluarga'] : "",
          'isi_metabolik' => isset($post['riwayat_metabolik_keluarga_desk']) ? json_encode($post['riwayat_metabolik_keluarga_desk']) : "",
          'deteksidini' => isset($post['riwayat_deteksi_dini_kanker']) ? $post['riwayat_deteksi_dini_kanker'] : "",
          'isi_deteksidini' => isset($post['riwayat_deteksi_dini_kanker_desk']) ? json_encode($post['riwayat_deteksi_dini_kanker_desk']) : "",
          'produksi_pemeriksaan_genitalia' => isset($post['seksual_genitalia']) ? $post['seksual_genitalia'] : "",
          'ket_produksi_pemeriksaan_genitalia' => isset($post['desk_seksual_genitalia']) ? $post['desk_seksual_genitalia'] : "",
          'produksi_pemakaian_kontrasepsi' => isset($post['seksual_kontrasepsi']) ? $post['seksual_kontrasepsi'] : "",
          'ket_produksi_pemakaian_kontrasepsi' => isset($post['desk_seksual_kontrasepsi']) ? $post['desk_seksual_kontrasepsi'] : "",
          'produksi_sirkumsisi' => isset($post['seksual_sirkumsisi']) ? $post['seksual_sirkumsisi'] : "",
          'ket_produksi_sirkumsisi' => isset($post['desk_seksual_sirkumsisi']) ? $post['desk_seksual_sirkumsisi'] : "",
          'eliminasi_defekasi' => isset($post['eliminasi_defekasi']) ? $post['eliminasi_defekasi'] : "",
          'ket_eliminasi_defekasi' => isset($post['desk_eliminasi_defekasi']) ? $post['desk_eliminasi_defekasi'] : "",
          'eliminasi_miksi' => isset($post['eliminasi_miksi']) ? $post['eliminasi_miksi'] : "",
          'ket_eliminasi_miksi' => isset($post['desk_eliminasi_miksi']) ? $post['desk_eliminasi_miksi'] : "",
          'keselamatan_status_mental' => isset($post['keselamatan_status_mental']) ? $post['keselamatan_status_mental'] : "",
          'ket_keselamatan_status_mental' => isset($post['desk_keselamatan_status_mental']) ? $post['desk_keselamatan_status_mental'] : "",
          'keselamatan_gangguan_panca_indra' => isset($post['keselamatan_gangguan']) ? $post['keselamatan_gangguan'] : "",
          'ket_keselamatan_gangguan_panca_indra' => isset($post['desk_keselamatan_gangguan']) ? $post['desk_keselamatan_gangguan'] : "",
          'keselamatan_membahayakan_dirinya_sendiri' => isset($post['keselamatan_potensi']) ? $post['keselamatan_potensi'] : "",
        );

        // echo "<pre>data riwayat kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataKesedaran = array(
          'data_source' => 1,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($post['idemr'])) {
          $this->db->where('tb_kesadaran.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_kesadaran', $dataKesedaran);
        } else {
          $getIdKesadaran = $this->DewasaModel->simpanKesadaran($dataKesedaran);
        }
        // echo "<pre>data kesadaran ";print_r($dataKesedaran);echo "</pre>";

        $dataTandaVital = array(
          'data_source' => 1,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($post['idemr'])) {
          $this->db->where('tb_tanda_vital.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
        } else {
          $getIdTandaVital = $this->DewasaModel->simpanTandaVital($dataTandaVital);
        }
        // echo "<pre>data tanda vital ";print_r($dataTandaVital);echo "</pre>";



        $dataSkriningGizi = array(
          'id_emr' => $getIdEmr,
          'penurunan_bb' => isset($post['komponenpenilaian']) ? $post['komponenpenilaian'] : "",
          'asupan_bb' => isset($post['komponenpenilaianasupan']) ? $post['komponenpenilaianasupan'] : "",
          'jalur_nutrisi' => isset($post['jenisNutrisi']) ? json_encode($post['jenisNutrisi']) : "",
          'jenis' => 1,
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining gizi ";print_r($dataSkriningGizi);echo "</pre>";

        $dataP3 = array(
          'id_emr' => $getIdEmr,
          'memerlukan_p3' => isset($post['pasien_perlu_p3']) ? $post['pasien_perlu_p3'] : "",
          'umur_65' => isset($post['pasien_perlu_p31']) ? $post['pasien_perlu_p31'] : 3788,
          'ket_umur_65' => isset($post['ketP31']) ? $post['ketP31'] : "",
          'keterbatasan_mobilitas' => isset($post['pasien_perlu_p32']) ? $post['pasien_perlu_p32'] : 3790,
          'ket_keterbatasan_mobilitas' => isset($post['ketP32']) ? $post['ketP32'] : "",
          'perawatan' => isset($post['pasien_perlu_p33']) ? $post['pasien_perlu_p33'] : 3792,
          'ket_perawatan' => isset($post['ketP33']) ? $post['ketP33'] : "",
          'pasien_tinggal_sendiri' => isset($post['pasien_perlu_p34']) ? $post['pasien_perlu_p34'] : 3794,
          'ket_pasien_tinggal_sendiri' => isset($post['ketP34']) ? $post['ketP34'] : "",
          'adakah_keluarga' => isset($post['pasien_perlu_p35']) ? $post['pasien_perlu_p35'] : 3796,
          'ket_adakah_keluarga' => isset($post['ketP35']) ? $post['ketP35'] : "",
          'pasien_tanggung_jawab_anak' => isset($post['pasien_perlu_p36']) ? $post['pasien_perlu_p36'] : 3798,
          'ket_pasien_tanggung_jawab_anak' => isset($post['ketP36']) ? $post['ketP36'] : "",
          'pasien_pulang_bawa_obat' => isset($post['pasien_perlu_p37']) ? $post['pasien_perlu_p37'] : 3800,
          'ket_pasien_pulang_bawa_obat' => isset($post['ketP37']) ? $post['ketP37'] : "",
          'risiko_infeksi' => isset($post['pasien_perlu_p38']) ? $post['pasien_perlu_p38'] : 3802,
          'ket_risiko_infeksi' => isset($post['ketP38']) ? $post['ketP38'] : "",
          'efek_samping' => isset($post['pasien_perlu_p39']) ? $post['pasien_perlu_p39'] : 3804,
          'ket_efek_samping' => isset($post['ketP39']) ? $post['ketP39'] : "",
          'masalah_untuk_transportasi' => isset($post['pasien_perlu_p310']) ? $post['pasien_perlu_p310'] : 3806,
          'ket_masalah_untuk_transportasi' => isset($post['ketP310']) ? $post['ketP310'] : "",
          'jenis' => 1,
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data perencanaan pemulangan pasien ";print_r($dataP3);echo "</pre>";

        $dataPemeriksaanFisik = array(
          'id_emr' => $getIdEmr,
          'keluhan_pasien' => isset($post['keluhan_pasien']) ? $post['keluhan_pasien'] : "",
          'mulut' => isset($post['gastro_mulut']) ? $post['gastro_mulut'] : "",
          'isi_mulut' => isset($post['gastro_mulut_desk']) ? $post['gastro_mulut_desk'] : "",
          'abdomen' => isset($post['gastro_Abdomen']) ? $post['gastro_Abdomen'] : "",
          'isi_abdomen' => isset($post['gastro_Abdomen_desk']) ? $post['gastro_Abdomen_desk'] : "",
          'masalah_kesehatan_keperawatan' => isset($post['masalahKesehatanKeperawatanDewasaRiD']) ? json_encode($post['masalahKesehatanKeperawatanDewasaRiD']) : "",
          'esophagus' => isset($post['gastro_Esophagus']) ? $post['gastro_Esophagus'] : "",
          'isi_esophagus' => isset($post['gastro_Esophagus_desk']) ? $post['gastro_Esophagus_desk'] : "",
          'keluhan_pada_hidung' => isset($post['sirkulasi_hidung']) ? $post['sirkulasi_hidung'] : "",
          'ket_keluhan_pada_hidung' => isset($post['sirkulasi_hidung_desk']) ? $post['sirkulasi_hidung_desk'] : "",
          'keluhan_pada_dada' => isset($post['sirkulasi_Dada']) ? $post['sirkulasi_Dada'] : "",
          'ket_keluhan_pada_dada' => isset($post['sirkulasi_Dada_desk']) ? $post['sirkulasi_Dada_desk'] : "",
          'keluhan_pada_jantung' => isset($post['sirkulasi_Jantung']) ? $post['sirkulasi_Jantung'] : "",
          'ket_keluhan_pada_jantung' => isset($post['sirkulasi_Jantung_desk']) ? $post['sirkulasi_Jantung_desk'] : "",
          'alat_pacu_jantung' => isset($post['sirkulasi_PacuJantung']) ? $post['sirkulasi_PacuJantung'] : "",
          'keluhan_pada_paru' => isset($post['sirkulasi_PadaParu']) ? $post['sirkulasi_PadaParu'] : "",
          'ket_keluhan_pada_paru' => isset($post['sirkulasi_PadaParu_desk']) ? $post['sirkulasi_PadaParu_desk'] : "",
          'perdarahan' => isset($post['sirkulasi_Perdarahan']) ? $post['sirkulasi_Perdarahan'] : "",
          'lokasi_perdarahan' => isset($post['sirkulasi_PerdarahanLokasi_desk']) ? $post['sirkulasi_PerdarahanLokasi_desk'] : "",
          'jumlah_perdarahan' => isset($post['sirkulasi_PerdarahanJumlah_desk']) ? $post['sirkulasi_PerdarahanJumlah_desk'] : "",
          'turgor_kulit' => isset($post['sirkulasi_TurgorKulit']) ? $post['sirkulasi_TurgorKulit'] : "",
          'edema' => isset($post['cekEdema']) ? $post['cekEdema'] : 0,
          'ket_edema' => isset($post['sirkulasi_cekEdema_desk']) ? $post['sirkulasi_cekEdema_desk'] : "",
          'crt' => isset($post['sirkulasi_Crt']) ? $post['sirkulasi_Crt'] : "",
          'keluhan_istirahat' => isset($post['aktivitas_istirahat']) ? $post['aktivitas_istirahat'] : "",
          'ket_keluhan_istirahat' => isset($post['desk_aktivitas_istirahat']) ? $post['desk_aktivitas_istirahat'] : "",
          'kemampuan_mobilisasi' => isset($post['aktivitas_kemampuan']) ? $post['aktivitas_kemampuan'] : "",
          'ket_kemampuan_mobilisasi' => isset($post['desk_aktivitas_kemampuan']) ? $post['desk_aktivitas_kemampuan'] : "",
          'integritas_kondisi_kulit' => isset($post['integritas_kulit']) ? $post['integritas_kulit'] : "",
          'ket_integritas_kondisi_kulit' => isset($post['desk_integritas_kulit']) ? $post['desk_integritas_kulit'] : "",
          'integritas_luka' => isset($post['integritas_Luka']) ? $post['integritas_Luka'] : "",
          'keyakinan' => isset($post['program_pengobatan_keyakinan']) ? $post['program_pengobatan_keyakinan'] : "",
          'sebutkan_keyakinan' => isset($post['desk_program_pengobatan_keyakinan']) ? $post['desk_program_pengobatan_keyakinan'] : "",
          'pengobatan_alternatif' => isset($post['pengobatan_alternatif']) ? $post['pengobatan_alternatif'] : "",
          'sebutkan_pengobatan_alternatif' => isset($post['desk_pengobatan_alternatif']) ? json_encode($post['desk_pengobatan_alternatif']) : "",
          'pengobatan_bertentangan' => isset($post['pengobatan_budaya']) ? $post['pengobatan_budaya'] : "",
          'sebutkan_pengobatan_bertentangan' => isset($post['desk_pengobatan_budayaRiD']) ? json_encode($post['desk_pengobatan_budayaRiD']) : "",
          'apakah_ingin_masuk_keews' => isset($post['masukKeEwsRiD']) ? $post['masukKeEwsRiD'] : "",
        );

        // echo "<pre>data pemeriksaan fisik ";print_r($dataPemeriksaanFisik);echo "</pre>";exit();

        $dataSkriningNyeri = array(
          'nokun' => $post['nokun'],
          'data_source' => 1,
          'ref' => $getIdEmr,
          'metode' => isset($post['skrining_nyeri_RiD']) ? $post['skrining_nyeri_RiD'] : "",
          'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'provokative' => isset($post['propocative_RiD']) ? $post['propocative_RiD'] : "",
          'quality' => isset($post['quality_RiD']) ? $post['quality_RiD'] : "",
          'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
          'regio' => isset($post['regio_RiD']) ? $post['regio_RiD'] : "",
          'severity' => isset($post['severity_RiD']) ? $post['severity_RiD'] : "",
          'time' => isset($post['time_RiD']) ? $post['time_RiD'] : "",
          'ket_time' => isset($post['durasi_nyeri_RiD']) ? $post['durasi_nyeri_RiD'] : "",
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining nyeri ";print_r($dataSkriningNyeri);echo "</pre>";

        $dataEdukasiKeperawatan = array(
          'id_emr' => $getIdEmr,
          'tingkat_pendidikan' => isset($post['tingkat_pendidikan']) ? $post['tingkat_pendidikan'] : "",
          'bahasa' => isset($post['bahasa_sehari_hari']) ? $post['bahasa_sehari_hari'] : "",
          'bahasa_daerah' => isset($post['bahasa_daerah']) ? $post['bahasa_daerah'] : "",
          'bahasa_lain' => isset($post['bahasa_lainnya']) ? $post['bahasa_lainnya'] : "",
          'penerjemah' => isset($post['perlu_penerjemah']) ? $post['perlu_penerjemah'] : "",
          'penerjemah_lain' => isset($post['penerjemah_lainnya']) ? $post['penerjemah_lainnya'] : "",
          'informasi' => isset($post['kesedian_informasi']) ? $post['kesedian_informasi'] : "",
        );

        // echo "<pre>data kebutuhan edukasi ";print_r($dataEdukasiKeperawatan);echo "</pre>";

        $dataHambatan = array();
        $indexHambatan = 0;
        if (isset($post['hambatan'])) {
          foreach ($post['hambatan'] as $input) {
            if ($post['hambatan'][$indexHambatan] != "") {
              array_push(
                $dataHambatan,
                array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['hambatan'][$indexHambatan],
                  'keterangan' => isset($post['hambatan_lainnya']) ? ($post['hambatan'][$indexHambatan] == 106 ? $post['hambatan_lainnya'] : "") : "",
                )
              );
            }
            $indexHambatan++;
          }
        }

        // echo "<pre>data hambatan ";print_r($dataHambatan);echo "</pre>";

        $dataKebutuhanPembelajaran = array();
        $indexKebutuhanPembelajaran = 0;
        if (isset($post['kebutuhan_pembelajaran'])) {
          foreach ($post['kebutuhan_pembelajaran'] as $input) {
            if ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] != "") {
              array_push(
                $dataKebutuhanPembelajaran,
                array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran],
                  'keterangan' => isset($post['kebutuhan_pembelajaran_lainnya']) ? ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] == 104 ? $post['kebutuhan_pembelajaran_lainnya'] : "") : "",
                )
              );
            }
            $indexKebutuhanPembelajaran++;
          }
        }

        // echo "<pre>data kebutuhan pembelajaran pasien ";print_r($dataKebutuhanPembelajaran);echo "</pre>";

        $dataO2 = array(
          'data_source' => 1,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'saturasi_o2' => isset($post['deskMasukEws']) ? $post['deskMasukEws'] : "",
          'penggunaan_o2' => isset($post['penggunaanO2RiD']) ? $post['penggunaanO2RiD'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($post['idemr'])) {
          $this->db->where('tb_o2.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_o2', $dataO2);
        } else {
          $getIdO2 = $this->DewasaModel->simpanO2($dataO2);
        }
        // echo "<pre>data data o2 ";print_r($dataO2);echo "</pre>";

        $pernapasanEws = $this->input->post('pernapasan');
        $nadiEws = $this->input->post('nadi');
        $tekananDarahSistolikEws = $this->input->post('tekanan_darah_1');
        $suhuEws = $this->input->post('suhu');
        $saturasiO2Ews = $this->input->post('deskMasukEws');
        $penggunaanO2Ews = $this->input->post('penggunaanO2RiA');
        $kesadaranEws = $this->input->post('kesadaran');

        // KONDISI UNTUK PERNAPASAN EWS
        if ($pernapasanEws >= 25) {
          $hasilPernapasan = 3;
        } elseif ($pernapasanEws >= 21 && $pernapasanEws <= 24.99) {
          $hasilPernapasan = 2;
        } elseif ($pernapasanEws >= 12 && $pernapasanEws <= 20.99) {
          $hasilPernapasan = 0;
        } elseif ($pernapasanEws >= 9 && $pernapasanEws <= 11.99) {
          $hasilPernapasan = 1;
        } elseif ($pernapasanEws <= 8.99) {
          $hasilPernapasan = 3;
        }

        // KONDISI UNTUK DENYUT NADI EWS
        if ($nadiEws >= 130) {
          $nadiEws = 3;
        } elseif ($nadiEws >= 111 && $nadiEws <= 129.99) {
          $nadiEws = 2;
        } elseif ($nadiEws >= 101 && $nadiEws <= 110.99) {
          $nadiEws = 1;
        } elseif ($nadiEws >= 60 && $nadiEws <= 100.99) {
          $nadiEws = 0;
        } elseif ($nadiEws >= 51 && $nadiEws <= 59.99) {
          $nadiEws = 1;
        } elseif ($nadiEws >= 40 && $nadiEws <= 50.99) {
          $nadiEws = 2;
        } elseif ($nadiEws <= 39.99) {
          $nadiEws = 3;
        }

        // KONDISI UNTUK TEKANAN DARAH SISTOLIK
        if ($tekananDarahSistolikEws >= 180) {
          $tekananDarahSistolikEws = 3;
        } elseif ($tekananDarahSistolikEws >= 170 && $tekananDarahSistolikEws <= 179.99) {
          $tekananDarahSistolikEws = 2;
        } elseif ($tekananDarahSistolikEws >= 150 && $tekananDarahSistolikEws <= 169.99) {
          $tekananDarahSistolikEws = 1;
        } elseif ($tekananDarahSistolikEws >= 101 && $tekananDarahSistolikEws <= 149.99) {
          $tekananDarahSistolikEws = 0;
        } elseif ($tekananDarahSistolikEws >= 81 && $tekananDarahSistolikEws <= 100.99) {
          $tekananDarahSistolikEws = 1;
        } elseif ($tekananDarahSistolikEws >= 71 && $tekananDarahSistolikEws <= 80.99) {
          $tekananDarahSistolikEws = 2;
        } elseif ($tekananDarahSistolikEws <= 70.99) {
          $tekananDarahSistolikEws = 3;
        }

        // KONDISI UNTUK SUHU
        if ($suhuEws >= 39) {
          $suhuEws = 2;
        } elseif ($suhuEws >= 38 && $suhuEws <= 38.99) {
          $suhuEws = 1;
        } elseif ($suhuEws >= 36 && $suhuEws <= 37.99) {
          $suhuEws = 0;
        } elseif ($suhuEws <= 35.99) {
          $suhuEws = 3;
        }

        // KONDISI UNTUK SATURASI O2
        if ($saturasiO2Ews >= 96) {
          $saturasiO2Ews = 0;
        } elseif ($saturasiO2Ews >= 94 && $saturasiO2Ews <= 95.99) {
          $saturasiO2Ews = 1;
        } elseif ($saturasiO2Ews >= 92 && $saturasiO2Ews <= 93.99) {
          $saturasiO2Ews = 2;
        } elseif ($saturasiO2Ews <= 91.99) {
          $saturasiO2Ews = 3;
        }

        // KONDISI UNTUK PENGGUNAAN O2
        if ($penggunaanO2Ews == 388) {
          $penggunaanO2Ews = 2;
        } elseif ($penggunaanO2Ews == 389) {
          $penggunaanO2Ews = 0;
        }

        // KONDISI UNTUK TINGKAT KESADARAN
        if ($kesadaranEws == 105) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 12) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 11) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 10) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 9) {
          $kesadaranEws = 0;
        }

        $totalScoreEws = $hasilPernapasan + $nadiEws + $tekananDarahSistolikEws + $suhuEws + $saturasiO2Ews + $penggunaanO2Ews + $kesadaranEws;

        // echo "<pre>data data o2 ";print_r($dataTotalScoreEws);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 1,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0,
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";
        // exit();

        if (!empty($id_ews)) {
          $dataTotalScoreEws = array(
            'id_tanda_vital' => $id_tanda_vital,
            'id_kesadaran' => $id_kesadaran,
            'id_o2' => $id_o2,
            'tanggal' => date("Y-m-d"),
            'jam' => date("H:i:s"),
            'score_ews' => $totalScoreEws,
            'ref' => $getIdEmr,
            'nokun' => $post['nokun'],
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
          );
        } else if (empty($id_ews)) {
          if (!empty($post['idemr'])) {
            $dataTotalScoreEws = array(
              'id_tanda_vital' => $id_tanda_vital,
              'id_kesadaran' => $id_kesadaran,
              'id_o2' => $id_o2,
              'tanggal' => date("Y-m-d"),
              'jam' => date("H:i:s"),
              'score_ews' => $totalScoreEws,
              'ref' => $getIdEmr,
              'nokun' => $post['nokun'],
              'oleh' => $this->session->userdata('id'),
              'status' => 1,
            );
          } else if (empty($post['idemr'])) {
            $dataTotalScoreEws = array(
              'id_tanda_vital' => $getIdTandaVital,
              'id_kesadaran' => $getIdKesadaran,
              'id_o2' => $getIdO2,
              'tanggal' => date("Y-m-d"),
              'jam' => date("H:i:s"),
              'score_ews' => $totalScoreEws,
              'ref' => $getIdEmr,
              'nokun' => $post['nokun'],
              'oleh' => $this->session->userdata('id'),
              'status' => 1,
            );
          }
        }

        $this->db->trans_begin();
        if (!empty($id_ews)  && $masukEwsAtauTidak == 3863) {
          $this->db->where('tb_ews.id', $id_ews);
          $this->db->update('keperawatan.tb_ews', $dataTotalScoreEws);
        } else if (empty($id_ews)  && $masukEwsAtauTidak == 3863) {
          $this->db->insert('keperawatan.tb_ews', $dataTotalScoreEws);
        }

        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->where('tb_tb_bb.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->replace('keperawatan.tb_skrining_gizi', $dataSkriningGizi);
          $this->db->replace('keperawatan.tb_perencanaan_pemulangan_pasien', $dataP3);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->where('tb_skrining_nyeri.ref', $idRefEmr);
          $this->db->update('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->replace('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
          $this->db->delete('keperawatan.tb_hambatan', array('id_emr' => $idRefEmr));
          foreach ($dataHambatan as $key => $value) {
            $this->db->replace('keperawatan.tb_hambatan', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_kebutuhan_pembelajaran', array('id_emr' => $idRefEmr));
          foreach ($dataKebutuhanPembelajaran as $key => $value) {
            $this->db->replace('keperawatan.tb_kebutuhan_pembelajaran', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $idRefEmr));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $this->db->delete('keperawatan.tb_masalah_kesehatan', array('id_emr' => $idRefEmr));
          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          // $this->db->insert('db_pasien.tb_kesadaran', $dataKesedaran);
          // $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->insert('keperawatan.tb_skrining_gizi', $dataSkriningGizi);
          $this->db->insert('keperawatan.tb_perencanaan_pemulangan_pasien', $dataP3);
          $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->insert('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          // $this->db->insert('db_pasien.tb_o2', $dataO2);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
          if (isset($post['hambatan'])) {
            $this->db->insert_batch('keperawatan.tb_hambatan', $dataHambatan);
          }
          if (isset($post['kebutuhan_pembelajaran'])) {
            $this->db->insert_batch('keperawatan.tb_kebutuhan_pembelajaran', $dataKebutuhanPembelajaran);
          }
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      } else if ($param == 'count') {
        $result = $this->DewasaModel->get_count();
        echo json_encode($result);
      } else if ($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataDewasaModel = $this->DewasaModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataDewasaModel
        ));
      }
    }
  }

  public function datatables()
  {
    $result = $this->MedisModel->historyPengkajian();

    $data = array();
    foreach ($result as $row) {
      $tombolCetak = "";
      $action = "";
      $verif = "";
      $namaVerif = "";
      $userLogin = $this->session->userdata('status');
      $userLoginFarmasi = $this->session->userdata('profesi');

      // KONDISI UNTUK ADA PENGKAJIAN PERAWAT
      if ($row->ID_EMR_PERAWAT != null) {
        if ($userLogin == 2 || $userLoginFarmasi == 5) {
          $jenisPengkajianPerawat = $row->JENIS_PENGKAJIAN_KEPERAWATAN;

          if ($row->STATUS_VERIFIKASI == 0) {
            $namaVerif = '<h4>-</h4>';
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $namaVerif = $row->INFO_VERIFIKASI;
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }

          if ($row->JENIS_PENGKAJIAN_MEDIS == 1) {
            $tombolCetak .= '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';

            if ($row->USIA == 1) {
              $tombolCetak .= '<a href="/reports/simrskd/keperawatananak/pengkajianRJKeperawatanAnak.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
            }

            if ($row->ID_RUANGAN == '105120101') {
              $tombolCetak .= '<a href="/reports/simrskd/radioterapi/pengkajian_keperawatan_radio_terapi.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
            }
          }

          if ($jenisPengkajianPerawat == 5) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan Dewasa</button>';
            $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/dewasa/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr=' . $row->ID_EMR_PERAWAT . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
          } elseif ($jenisPengkajianPerawat == 6) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatAnak" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan Anak</button>';
            $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/anak/PengkajianRIKeperawatanAnak.php?format=pdf&idEmr=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat Anak </span></a>';
          } elseif ($jenisPengkajianPerawat == 7) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View PengkajianRemaja</button>';
          } elseif ($jenisPengkajianPerawat == 8) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemik" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Terapi</button>';
            $tombolCetak .= '<a href="/reports/simrskd/sistemik/PengkajianSistemikRi.php?format=pdf&idEmr=' . $row->ID_EMR_PERAWAT . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Terapi Perawat</span></a>';
          } elseif ($jenisPengkajianPerawat == 9) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatIGD" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian IGD</button>';
            $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_AwalIgd.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
          } elseif ($jenisPengkajianPerawat == 10) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiKritis" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Kritis</button>';
            $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/pengkajianKritisPerawat.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
          } elseif ($jenisPengkajianPerawat == 11) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
            $action .= '<a href="#lihatPemantauanAsesmenRestrain" class="btn btn-primary btn-block btn-sm showPemantauanAsesmenRestrain" data-toggle="modal" data-idpemasres="' . $row->ID_EMR_PERAWAT . '" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Pemantauan Restrain</a>';
            $tombolCetak .= '<a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/restrain/PengkajianRestrain.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
          } elseif ($jenisPengkajianPerawat == 12) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiim" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian RIIM</button>';
          } elseif ($jenisPengkajianPerawat == 13) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRira" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian RIRA</button>';
          } elseif ($jenisPengkajianPerawat == 14) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan Pengkajian Akhir Kehidupan</button>';
          } elseif ($jenisPengkajianPerawat == 15) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiBrak" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Ri Brakhiterapi</button>';
            $tombolCetak .= '<a href="/reports/simrskd/Brakhiterapi/PengkajianRIKeperawatanBrakhiterapi.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
          } elseif ($jenisPengkajianPerawat == 16) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRadioterapiRi" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Ri Radioterapi</button>';
          } elseif ($jenisPengkajianPerawat == 17) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemikRJ" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Perawat Terapi Sistemik RJ</button>';
            $tombolCetak .= '<a href="/reports/simrskd/sistemik/PengkajianSistemikRj.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
          } elseif ($jenisPengkajianPerawat == 2) {
            $tombolCetak .= '<a href="/reports/simrskd/invasif/invasifpengkajian.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan Invasif</span></a>';
          } else {
            $tombolCetak .= '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
          }
        } elseif ($userLogin == 1) {
          $jenisPengkajianMedis = $row->JENIS_PENGKAJIAN_MEDIS;
          $jenisPengkajianPerawat = $row->JENIS_PENGKAJIAN_KEPERAWATAN;
          if ($row->STATUS_VERIFIKASI == 0) {
            if ($row->JENIS_PENGKAJIAN_KEPERAWATAN == 9) {
              $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_AwalIgd.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
            } elseif ($row->JENIS_PENGKAJIAN_KEPERAWATAN == 10) {
              $verif = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat-pasien-kritis" data-id="' . $row->ID_EMR_PERAWAT . '">Verif</button>';
            } elseif ($row->JENIS_PENGKAJIAN_KEPERAWATAN == 15) {
              $verif = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat-pasien-brakhiterapi" data-id="' . $row->ID_EMR_PERAWAT . '">Verif</button>';
            } else {
              $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
            }
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }

          if ($jenisPengkajianPerawat == 5) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan Dewasaa</button>';
            $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/dewasa/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
          } elseif ($jenisPengkajianPerawat == 6) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatAnak" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan Anak</button>';
            $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/anak/PengkajianRIKeperawatanAnak.php?format=pdf&idEmr=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat Anak </span></a>';
          } elseif ($jenisPengkajianPerawat == 7) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Remaja</button>';
          } elseif ($jenisPengkajianPerawat == 8) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemik" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Terapi</button>';
            $tombolCetak .= '<a href="/reports/simrskd/sistemik/PengkajianSistemikRi.php?format=pdf&idEmr=' . $row->ID_EMR_PERAWAT . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Terapi Perawat</span></a>';
          } elseif ($jenisPengkajianPerawat == 9) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatIGD" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian IGD</button>';
          } elseif ($jenisPengkajianPerawat == 10) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat-pasien-kritis" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan Kritis</button>';
            $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/PengkajianKritisPerawat.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
          } elseif ($jenisPengkajianPerawat == 11) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
            $action .= '<a href="#lihatPemantauanAsesmenRestrain" class="btn btn-primary btn-block btn-sm showPemantauanAsesmenRestrain" data-toggle="modal" data-idpemasres="' . $row->ID_EMR_PERAWAT . '" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Pemantauan Restrain</a>';
            $tombolCetak .= '<a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/restrain/PengkajianRestrain.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
          } elseif ($jenisPengkajianPerawat == 12) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRiim" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian RIIM</button>';
          } elseif ($jenisPengkajianPerawat == 13) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatRira" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian RIRA</button>';
          } elseif ($jenisPengkajianPerawat == 14) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan Pengkajian Akhir Kehidupan</button>';
          } elseif ($jenisPengkajianPerawat == 15) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat-pasien-brakhiterapi" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Brakhiterapi</button>';
          } elseif ($jenisPengkajianPerawat == 17) {
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemikRJ" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Pengkajian Perawat Terapi Sistemik RJ</button>';
            $tombolCetak .= '<a href="/reports/simrskd/sistemik/PengkajianSistemikRj.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
          } elseif ($jenisPengkajianPerawat == 2) {
            $tombolCetak .= '<a href="/reports/simrskd/invasif/invasifpengkajian.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan Invasif</span></a>';
          } else {
            $tombolCetak .= '<a href="/reports/simrskd/pengkajian/pengkajianRJKeperawatanDewasa.php?format=pdf&id=' . $row->ID_EMR_PERAWAT . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Keperawatan</span></a>';
          }
        }
      }

      // KONDISI UNTUK ADA PENGKAJIAN MEDIS
      if ($row->ID_EMR_MEDIS != null) {

        // CETAK RAWAT JALAN
        if ($row->JENIS_PENGKAJIAN_MEDIS == 1) {

          if ($row->USIA == 1) {
            $tombolCetak .= '<a href="/reports/simrskd/keperawatananak/pengkajianRJMedisAnak.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
          } else {
            $tombolCetak .= '<button type="button" data-name="emr.pengkajian.pengkajian_medis" data-parameter=\'{"ID_EMR":"' . $row->ID_EMR_MEDIS . '"}\' class="btn btn-warning btn-block btn-sm tombolCetakan" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></button>';
          }

          if ($row->ID_RUANGAN == '105120101') {
            $tombolCetak .= '<a href="/reports/simrskd/radioterapi/pengkajian_medis_radio_terapi.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
          }
        }

        if ($userLogin == 1) {
          if ($row->STATUS_VERIFIKASI == 0) {
            $namaVerif = '<h4>-</h4>';
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $namaVerif = $row->INFO_VERIFIKASI;
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }

          if ($row->JENIS_PENGKAJIAN_MEDIS != 1) {
            if ($row->JENIS_PENGKAJIAN_MEDIS == 9) {
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis IGD</button>';
              $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_medis_igd.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
            } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 10) {
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisKritis" data-id="' . $row->NOKUN . '" data-idemr="' . $row->ID_EMR_MEDIS . '" data-status="1"><i class="fa fa-eye"></i>  View Medis Kritis</button>';
              $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/PengkajianKritisMedis.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
            } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 15) {
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisBrakhiterapi" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
              $tombolCetak .= '<a href="/reports/simrskd/Brakhiterapi/MedisRIKeperawatanBrakhiterapi.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
            } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 14) {
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPAKMedis" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-eye"></i> View Medis Pengkajian Akhir Kehidupan</button>';
            } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 6) {
              // $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatAnak" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Keperawatan Anak</button>';
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i> View Medis Anak</button>';
              $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/medis/PengkajianRIMedisAnak.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis Anak </span></a>';
            } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 5) {
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis Dewasa</button>';
              $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/medis/PengkajianRIMedisDewasa.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis Dewasa</span></a>';
            } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 8) {
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianTerapiSistemik" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-eye"></i> View Pengkajian Terapi</button>';
              $tombolCetak .= '<a href="/reports/simrskd/sistemik/PengkajianSistemikRiMedis.php?format=pdf&idEmr=' . $row->ID_EMR_MEDIS . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Terapi Medis</span></a>';
            } else {
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
              $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/medis/PengkajianRIMedisDewasa.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
            }
          }
        } elseif ($userLogin == 2 || $userLoginFarmasi == 5) {
          if ($row->STATUS_VERIFIKASI == 0) {
            if ($row->ID_EMR_PERAWAT != null) {
              $verif = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="' . $row->ID_EMR_PERAWAT . '">Verif</button>';
            } else {
              $verif = $row->INFO_VERIFIKASI;
            }
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }

          if ($row->JENIS_PENGKAJIAN_MEDIS == 5) {
            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis Dewasa</button>';
            $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/medis/PengkajianRIMedisDewasa.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis Dewasa</span></a>';
          } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 9) {
            $tombolCetak .= '<a href="/reports/simrskd/igd/pengkajian_medis_igd.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
          } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 10) {
            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisKritis" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
            $tombolCetak .= '<a href="/reports/simrskd/pengkajiankritis/PengkajianKritisMedis.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
          } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 15) {
            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisBrakhiterapi" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
          } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 14) {
            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPAKMedis" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-eye"></i> View Medis Pengkajian Akhir Kehidupan</button>';
          } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 6) {
            // $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawatAnak" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Keperawatan Anak</button>';
            $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i> View Medis Anak</button>';
            $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/medis/PengkajianRIMedisAnak.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis Anak</span></a>';
          } elseif ($row->JENIS_PENGKAJIAN_MEDIS == 2) {
            $tombolCetak .= '<a href="/reports/simrskd/invasif/invasifmedis.php?format=pdf&id=' . $row->ID_EMR_MEDIS . '" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis Invasif</span></a>';
          } else {
            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOKUN . '" data-status="1"><i class="fa fa-eye"></i>  View Medis' . $row->JENIS_PENGKAJIAN_MEDIS . '</button>';
          }
        }
      }

      if ($row->TANGGAL_PENGKAJIAN_MEDIS != null) {
        $userMedisTgl = $row->USER_MEDIS . " (" . date('d M Y H:i:s', strtotime($row->TANGGAL_PENGKAJIAN_MEDIS)) . ")";
      } else {
        $userMedisTgl = "";
      }

      if ($row->TANGGAL_PENGKAJIAN_PERAWAT != null) {
        $userPerawatTgl = $row->USER_PERAWAT . " (" . date('d M Y H:i:s', strtotime($row->TANGGAL_PENGKAJIAN_PERAWAT)) . ")";
      } else {
        $userPerawatTgl = "";
      }


      $sub_array = array();
      $sub_array[] = $row->INFO;
      $sub_array[] = $verif;
      $sub_array[] = $row->RUANGAN;
      $sub_array[] = $row->TANGGAL_KUNJUNGAN;
      $sub_array[] = $row->DPJP;
      $sub_array[] = $namaVerif;
      $sub_array[] = $action;
      $sub_array[] = $tombolCetak;
      $sub_array[] = $userMedisTgl;
      $sub_array[] = $userPerawatTgl;
      $data[] = $sub_array;
    }

    $output = array(
      "draw" => intval($this->input->post("draw")),
      "data"              => $data
    );
    echo json_encode($output);
  }
}

/* End of file Dewasa.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkajianRI/Dewasa.php */