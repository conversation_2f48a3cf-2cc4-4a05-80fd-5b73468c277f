<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RINAD extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');

    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/rawat_inap/transferRuangan/RINADModel',
      )
    );
  }

  public function index()
  {
    $data = array(
      'nokun' => $this->uri->segment(6),
      'jumlah' => $this->RINADModel->history($this->uri->segment(6), 'jumlah'),
      'kontakLangsung' => $this->masterModel->referensi(1474),
      'droplet' => $this->masterModel->referensi(1475),
      'sida' => $this->masterModel->referensi(1476),
      'tidakMenular' => $this->masterModel->referensi(1477),
      'negatifMRSA' => $this->masterModel->referensi(1478),
      'bukanIsolasi' => $this->masterModel->referensi(1479),
      'perawatanIntensif' => $this->masterModel->referensi(1480),
      'meninggal' => $this->masterModel->referensi(1481),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/transferRuangan/RINAD/index', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->RINADModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $diizinkan = null;

          $data = array(
            'id' => isset($post['id']) ? $post['id'] : null,
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'kontak_langsung' => isset($post['kontak_langsung']) ? $post['kontak_langsung'] : null,
            'droplet' => isset($post['droplet']) ? $post['droplet'] : null,
            'sida' => isset($post['sida']) ? $post['sida'] : null,
            'diizinkan_masuk' => isset($post['diizinkan_masuk']) ? $post['diizinkan_masuk'] : null,
            'tidak_menular' => isset($post['tidak_menular']) ? $post['tidak_menular'] : null,
            'negatif_mrsa' => isset($post['negatif_mrsa']) ? $post['negatif_mrsa'] : null,
            'bukan_isolasi' => isset($post['bukan_isolasi']) ? $post['bukan_isolasi'] : null,
            'perawatan_intensif' => isset($post['perawatan_intensif']) ? $post['perawatan_intensif'] : null,
            'meninggal' => isset($post['meninggal']) ? $post['meninggal'] : null,
            'diizinkan_keluar' => isset($post['diizinkan_keluar']) ? $post['diizinkan_keluar'] : null,
            'oleh' => $this->session->userdata('id'),
            'status' => '1',
          );

          // echo '<pre>';print_r($data);exit();
          $this->RINADModel->replace($data);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->RINADModel->get($post['id'], true);
        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data,
          )
        );
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/transferRuangan/RINAD/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->RINADModel->history($nokun, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        if ($h->diizinkan_masuk == 0) {
          $status = '<p class="text-warning">Ditolak masuk</p>';
        } elseif ($h->diizinkan_masuk == 1) {
          if ($h->diizinkan_keluar == null) {
            $status = '<p class="text-success">Diizinkan masuk</p>';
          } elseif ($h->diizinkan_keluar == 0) {
            $status = '<p class="text-warning">Ditolak keluar</p>';
          } elseif ($h->diizinkan_keluar == 1) {
            $status = '<p class="text-primary">Diizinkan keluar</p>';
          }
        }
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->jam)),
        $status,
        $h->pengisi,
        date('d-m-Y, H.i.s', strtotime($h->updated_at)),
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-rinad' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-rinad' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-rinad' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array('status' => 0,);
    $this->RINADModel->ubah($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}