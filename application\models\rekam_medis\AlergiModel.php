<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AlergiModel extends MY_Model{
    protected $_table_name = 'db_pasien.tb_riwayat_kesehatan';
    protected $_primary_key = 'id_emr';
    protected $_order_by = 'id_emr';
    protected $_order_by_type = 'DESC';

    function __construct(){
        parent::__construct();
    }

    function table_query(){
        $this->db->select(' rk.isi_alergi, rk.reaksi_alergi, k.created_at, `master`.getNamaLengkapPegawai(ap.NIP) oleh');
        $this->db->from('keperawatan.tb_keperawatan k');
        $this->db->join('keperawatan.tb_riwayat_kesehatan rk','k.id_emr = rk.id_emr','left');
        $this->db->join('pendaftaran.pendaftaran p', 'k.nopen = p.NOMOR','left');
        $this->db->join('aplikasi.pengguna ap', 'k.created_by = ap.ID','left');
        $this->db->where('rk.isi_alergi != ""');
        $this->db->group_by('k.id_emr');
        $this->db->order_by('k.created_at','DESC');
        if($this->input->post('norm')){
            $this->db->where('p.NORM', $this->input->post('norm'));
        }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }
}
