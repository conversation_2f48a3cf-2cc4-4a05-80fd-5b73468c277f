<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SkalaOntarioMASSModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('mass.id, masru.DESKRIPSI RUANGAN, mass.tanggal TANGGAL, mass.nokun, mass.total, mass.tingkat_risiko
        , master.getNamaLengkapPegawai(peng.NIP) USER');
        $this->db->from('keperawatan.tb_skala_ontario_mass mass');
        $this->db->join('aplikasi.pengguna peng','peng.ID = mass.oleh','LEFT');
        $this->db->join('pendaftaran.kunjungan penkun','mass.nokun = penkun.NOMOR','LEFT');
        $this->db->join('pendaftaran.pendaftaran penpen','penkun.NOPEN = penpen.NOMOR','LEFT');
        $this->db->join('master.ruangan masru','penkun.RUANGAN = masru.ID','LEFT');
        $this->db->where('mass.STATUS !=','0');
        $this->db->where('penpen.NORM',$this->input->post('nomr'));
        $this->db->order_by('mass.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id_mass)
    {
      $query = $this->db->query(
        "SELECT * FROM keperawatan.tb_skala_ontario_mass m
        WHERE m.id = '$id_mass' AND m.`status` = 1"
      );
      return $query->row_array();
    }

    public function getTBBB($nokun)
    {
        $query = $this->db->query(
            'SELECT * FROM db_pasien.tb_tb_bb tbbb
            WHERE tbbb.nokun = "'.$nokun.'"
            ORDER BY tbbb.created_at DESC limit 1'
        );
        return $query->row_array();
    }

    public function getTotalSkorGizi($nokun)
    {
        $query = $this->db->query(
            'SELECT (penurbb.nilai+asumak.nilai) TOTAL_SKOR_GIZI FROM keperawatan.tb_keperawatan k 
            LEFT JOIN keperawatan.tb_skrining_gizi sg ON sg.id_emr = k.id_emr
            LEFT JOIN db_master.variabel penurbb ON penurbb.id_variabel = sg.penurunan_bb
            LEFT JOIN db_master.variabel asumak ON asumak.id_variabel = sg.asupan_bb
            WHERE k.nokun = "'.$nokun.'" AND k.`status` = 1
            ORDER	BY k.created_at DESC LIMIT 1'
        );

        return $query->row_array();
    }

    public function getDiagnosaMasuk($nopen)
    {
        $query = $this->db->query(
            "SELECT a.diagnosa FROM
            ((SELECT kp.diagnosa_masuk diagnosa, kp.created_at tgl
            FROM keperawatan.tb_keperawatan kp
            
            WHERE kp.`status`=1 AND kp.flag=1 AND kp.diagnosa_masuk IS NOT NULL 
                AND kp.nopen='$nopen'
            ORDER BY kp.created_at DESC)
            UNION ALL
            (SELECT mk.desk_diagnosa_medis diagnosa, md.created_at tgl FROM medis.tb_medis md
            
                LEFT JOIN medis.tb_masalah_medis_kep mk ON mk.id_emr = md.id_emr
            
            WHERE md.`status`=1 AND md.flag=1 AND mk.desk_diagnosa_medis IS NOT NULL
                AND md.nopen='$nopen'
            ORDER BY md.created_at DESC)) a
            
            ORDER BY a.tgl DESC
            
            LIMIT 1"
        );

        return $query->row_array();
    }

    public function table_intervensiRRRS($id_mass)
    {
        $query = $this->db->query(
            "SELECT rrrs.`*`, master.getNamaLengkapPegawai(p.NIP) PENGGUNA FROM keperawatan.tb_skala_ontario_mass_intervensi_rrrs rrrs
            LEFT JOIN aplikasi.pengguna p ON rrrs.oleh = p.ID
            WHERE rrrs.`status` = 1 AND rrrs.id_mass = '$id_mass'"
        );

        return $query->result_array();
    }

    public function getHistoryIntervensiRRRS($id_intervensi_rrrs)
    {
      $query = $this->db->query(
        "SELECT rrrs.`*` FROM keperawatan.tb_skala_ontario_mass_intervensi_rrrs rrrs
        WHERE rrrs.`status` = 1 AND rrrs.id = '$id_intervensi_rrrs'"
      );

      return $query->row_array();
    }

    public function table_intervensiRT($id_mass)
    {
        $query = $this->db->query(
            "SELECT rt.`*`, master.getNamaLengkapPegawai(p.NIP) PENGGUNA FROM keperawatan.tb_skala_ontario_mass_intervensi_rt rt
            LEFT JOIN aplikasi.pengguna p ON rt.oleh = p.ID
            WHERE rt.`status` = 1 AND rt.id_mass = '$id_mass'"
        );

        return $query->result_array();
    }

    public function getHistoryIntervensiRT($id_intervensi_rt)
    {
      $query = $this->db->query(
        "SELECT rt.`*` FROM keperawatan.tb_skala_ontario_mass_intervensi_rt rt
        WHERE rt.`status` = 1 AND rt.id = '$id_intervensi_rt'"
      );

      return $query->row_array();
    }

}
