<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Penyedia extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(26, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('inventory/Model_penyedia'));
  }

  function index(){    
   $penyedia = $this->Model_penyedia->tampil_data();
   $data       = array(
    'title'         => 'Halaman Input Penyedia',
    'isi'           => 'inventory/penyedia/index',   
    'penyedia'      => $penyedia        
  );
   $this->load->view('layout/wrapper',$data);    
 }

 function tambah(){
  $data = array(
    'NAMA'    => $this->input->post('NAMA'),
    'ALAMAT'  => $this->input->post('ALAMAT'),
    'TELEPON' => $this->input->post('TELEPON'),
    'FAX' => $this->input->post('FAX')
  );
  $this->Model_penyedia->tambah($data);
  $this->session->set_flashdata('notif','<div class="alert alert-success" role="alert"> Data Berhasil ditambahkan <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button></div>');
  redirect('inventory/Penyedia');
}

function ubah(){
  $id = $this->input->post('ID');
  $data = array(
    'NAMA'    => $this->input->post('NAMA'),
    'ALAMAT'  => $this->input->post('ALAMAT'),
    'TELEPON' => $this->input->post('TELEPON'),
    'FAX' => $this->input->post('FAX')
  );
  $this->Model_penyedia->ubah($data,$id);
  $this->session->set_flashdata('notif','<div class="alert alert-success" role="alert"> Data Berhasil diubah <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button></div>');
  redirect('inventory/Penyedia');
}

}
