<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PrmjRiModel extends CI_Model
{
    private $db4;
    private $db5;

    public function __construct()
    {
        parent::__construct();
        $this->db4 = $this->load->database('196', true);
        $this->db5 = $this->load->database('237', true);
    }

    public function simpanPrmjRi($data)
    {
        $this->db->insert('medis.tb_summary_list', $data);
    }

    public function tblPrmjRi($nomr)
    {
        $query = $this->db->query("SELECT a.*

        FROM 
        ((SELECT mts.id, mts.nomr, mts.nokun, mts.diagnosis#, mts.diagnosis_kanker
                    , mts.operasi
                    , mts.kemoterapi, mts.radiasi, mts.pengobatan, mts.tata_laksana
                    , mts.catatan_penting, mts.jenis
                    , mts.oleh,DATE_FORMAT(mts.tanggal,'%d-%m-%Y') tanggal
                    , mts.`status`, master.getNamaLengkap(mts.nomr)NAMAPASIEN,
                 master.getNamaLengkapPegawai(ap.NIP)NAMADOKTER, mr.DESKRIPSI RUANGASAL
                    , mts.jenis jenis_summary_list
                    , 'PRMRJ' deskripsi_jenis, DATE(mts.tanggal) tgl_input
        FROM medis.tb_summary_list mts
                  LEFT JOIN aplikasi.pengguna ap ON  ap.ID = mts.oleh
                 LEFT JOIN master.pasien mp ON mp.NORM = mts.nomr
                  LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = mts.nokun
                  LEFT JOIN master.ruangan mr ON mr.ID = pk.RUANGAN
        WHERE mts.nomr = '$nomr' AND mts.status = 1 AND mts.jenis IN (1,2) 
                 ORDER BY mts.tanggal DESC)
        UNION ALL
        (SELECT mts.id, mts.nomr, mts.nokun
                    , CONCAT('Diagnosis Utama: ', IF(mts.diagnosis IS NULL,' - ',mts.diagnosis),'\r'
                        , 'Diagnosis Kanker: ',IF(mts.diagnosis_kanker IS NULL,' - ',mts.diagnosis_kanker)) diagnosis		
                    , mts.operasi
                    , mts.kemoterapi, mts.radiasi, mts.pengobatan, mts.tata_laksana
                    , mts.catatan_penting, mts.jenis
                    , mts.oleh,DATE_FORMAT(mts.tanggal,'%d-%m-%Y') tanggal
                    , mts.`status`, master.getNamaLengkap(mts.nomr)NAMAPASIEN,
                 master.getNamaLengkapPegawai(dok.NIP)NAMADOKTER, mr.DESKRIPSI RUANGASAL
                    , mts.jenis jenis_summary_list
                    , 'Ringkasan Medis Pasien Rawat Inap' deskripsi_jenis, DATE(mts.tanggal) tgl_input
        FROM medis.tb_summary_list mts
                  LEFT JOIN aplikasi.pengguna ap ON  ap.ID = mts.oleh
                 LEFT JOIN master.pasien mp ON mp.NORM = mts.nomr
                  LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = mts.nokun
                  LEFT JOIN master.ruangan mr ON mr.ID = mts.ruangan
                  LEFT JOIN master.dokter dok ON dok.ID = mts.dpjp
        WHERE mts.nomr = '$nomr' AND mts.status = 1 AND mts.jenis IN (3) 
                 ORDER BY mts.tanggal DESC)
        UNION ALL        
        (SELECT rm.id
            , p.NORM nomr
            , (SELECT pk.NOMOR FROM pendaftaran.kunjungan pk
                    LEFT JOIN master.ruangan ruan ON ruan.ID = pk.RUANGAN AND ruan.JENIS=5
                WHERE pk.NOPEN=rm.nopen AND ruan.DESKRIPSI=rm.ruang_rawat_terakhir
                ORDER BY pk.KELUAR DESC
                LIMIT 1) nokun
            , rm.diagnosis_utama_des diagnosis 
            #, NULL diagnosis_kanker
            , NULL operasi
            , NULL kemoterapi
            , NULL radiasi
            , NULL pengobatan
            , SUBSTR(CONCAT('• ' ,REPLACE(rm.tindakan_procedure_des,'\n','<br/>• ')), 1, LENGTH(CONCAT('• ' ,REPLACE(rm.tindakan_procedure_des,'\n','<br/>• '))) -2) tata_laksana
            , rm.catatan_utama catatan_penting
            , 4 jenis
            , dok.ID oleh
            , CONCAT('Tgl Masuk: ', DATE_FORMAT(rm.tgl_masuk,'%d-%m-%Y')
                , ' | ',' Tgl Keluar: ' ,DATE_FORMAT(rm.tgl_keluar,'%d-%m-%Y')) tanggal
            , 1 status
            , master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , master.getNamaLengkapPegawai(dok.NIP) NAMADOKTER
            , rm.ruang_rawat_terakhir
            , 4 jenis_summary_list
            , 'Resume Medis Rawat Inap/IGD' jenis_deskripsi
            , rm.tgl_keluar 
        
        FROM resume_medis.resume_medis rm
            
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = rm.nopen
            LEFT JOIN master.dokter dok ON dok.ID = rm.id_dpjp
            
        WHERE p.NORM='$nomr' AND rm.`status`=2
        ORDER BY rm.tgl_keluar DESC)
        
        UNION ALL
        
        (SELECT rm.ID_ERESUME_IGD
            , p.NORM nomr
            , (SELECT pk.NOMOR FROM pendaftaran.kunjungan pk
                    LEFT JOIN master.ruangan ruan ON ruan.ID = pk.RUANGAN AND ruan.JENIS=5
                WHERE pk.NOPEN=rm.NOPEN AND ruan.DESKRIPSI=rm.ruang_rawat_terakhir
                ORDER BY pk.KELUAR DESC
                LIMIT 1) nokun
            , rm.diagnosis_utama_des diagnosis 
            #, NULL diagnosis_kanker
            , NULL operasi
            , NULL kemoterapi
            , NULL radiasi
            , NULL pengobatan
            , SUBSTR(CONCAT('• ' ,REPLACE(rm.tindakan_procedure_des,'\n','<br/>• ')), 1, LENGTH(CONCAT('• ' ,REPLACE(rm.tindakan_procedure_des,'\n','<br/>• '))) -2) tata_laksana
            , rm.catatan_utama catatan_penting
            , 4 jenis
            , dok.ID oleh
            , CONCAT('Tgl Masuk: ', DATE_FORMAT(rm.tgl_masuk,'%d-%m-%Y')
                , ' | ',' Tgl Keluar: ' ,DATE_FORMAT(IF(rm.tgl_keluar IS NULL, rm.TGL_INPUT, rm.TGL_KELUAR),'%d-%m-%Y')) tanggal
            , 1 status
            , master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , master.getNamaLengkapPegawai(dok.NIP) NAMADOKTER
            , rm.ruang_rawat_terakhir
            , 4 jenis
            , 'Resume Medis Rawat Inap/IGD' jenis_deskripsi
            , DATE(IF(rm.tgl_keluar IS NULL, rm.TGL_INPUT, rm.TGL_KELUAR))tgl_inputx
            
        
        FROM eresume_igd.eresume_igd rm
            
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = rm.NOPEN
            LEFT JOIN master.dokter dok ON dok.ID = rm.ID_DPJP
            
        WHERE p.NORM='$nomr' AND rm.STATUS=2
        ORDER BY tgl_inputx DESC)
        
        ) a
        
        ORDER BY a.tgl_input DESC ");
        return $query;
    }

    public function detailSum($id)
    {
        $query = $this->db->query("SELECT mts.`*`, master.getNamaLengkap(mts.nomr)NAMAPASIEN,
                                master.getNamaLengkapPegawai(ap.NIP)NAMADOKTER, mr.DESKRIPSI RUANGASAL
                                ,HOUR(TIMEDIFF(NOW(),mts.tanggal)) DURASI,IF(HOUR(TIMEDIFF(NOW(),mts.tanggal))<=24,1,0) STATUSBUTTON
                                FROM medis.tb_summary_list mts
                                LEFT JOIN aplikasi.pengguna ap ON  ap.ID = mts.oleh
                                LEFT JOIN master.pasien mp ON mp.NORM = mts.nomr
                                LEFT JOIN master.ruangan mr ON mr.ID = mts.ruangan
                                WHERE mts.id = '$id'");
        return $query->row_array();
    }

    public function updatePrmjRi($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update('medis.tb_summary_list', $data);
    }
}

/* End of file PrmjRiModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/catatanTerintegrasi/PrmjRiModel.php */
