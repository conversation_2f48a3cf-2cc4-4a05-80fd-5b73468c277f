<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class InstrumenMNA extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','geriatri/InstrumenMNA_Model'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'getNomr' => $getNomr,
      'cekSriningMna' => $this->InstrumenMNA_Model->cekSriningMna($nokun),
      'listPenurunanAsupan' => $this->masterModel->referensi(918),
      'listKehilanganBB' => $this->masterModel->referensi(919),
      'listKemampuanMobilitas' => $this->masterModel->referensi(920),
      'listMenderitaStress' => $this->masterModel->referensi(921),
      'listNeuropsikologis' => $this->masterModel->referensi(922),
      'listNilaiIMT' => $this->masterModel->referensi(923)
    );
    $this->load->view('Pengkajian/geriatri/instrumenMiniNutrionalAssesment/index', $data);
  }

  public function simpanFSkriningMna()
  {
    $data= array(
      'nokun'      => $this->input->post('nokun'),
      'skrining_a' => $this->input->post('listPenurunanAsupan'),
      'skrining_b' => $this->input->post('listKehilanganBB'),
      'skrining_c' => $this->input->post('listKemampuanMobilitas'),
      'skrining_d' => $this->input->post('listMenderitaStress'),
      'skrining_e' => $this->input->post('listNeuropsikologis'),
      'skrining_f' => $this->input->post('listNilaiIMT'),
      'bb'         => $this->input->post('bbMna'),
      'tb'         => $this->input->post('tbMna'),
      'oleh'       => $this->session->userdata('id'),
    );

    $this->InstrumenMNA_Model->simpanFSkriningMna($data);
    $cekSriningMna = $this->InstrumenMNA_Model->cekSriningMna($this->input->post('nokun'));
    if($cekSriningMna['TOTAL_TGMS'] <= 11){
      $this->simpanFPenilaianMna($this->input->post('nokun'));
    }
  }

  public function simpanFPenilaianMna($nokun)
  {
    $data = array(
      'nokun'        => $nokun,
      'penilaian_g'  => '0',
      'penilaian_h'  => '0',
      'penilaian_i'  => '0',
      'penilaian_j'  => '0',
      'penilaian_k1' => '0',
      'penilaian_k2' => '0',
      'penilaian_k3' => '0',
      'penilaian_l'  => '0',
      'penilaian_m'  => '0',
      'penilaian_n'  => '0',
      'penilaian_o'  => '0',
      'penilaian_p'  => '0',
      'penilaian_q'  => '0',
      'penilaian_r'  => '0',
      'penilaian_s'  => '0',
      'oleh'         => '0',
    );

    $this->InstrumenMNA_Model->simpanFPenilaianMna($data);
  }

  public function historyInstrumenMna()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listInstrumenMna = $this->InstrumenMNA_Model->listInstrumenMna($nomr);

    $data = array();
    $no = 1;
    foreach ($listInstrumenMna->result() as $lIm) {
      $totalTgmp = $lIm->TOTAL_TGMP == "" ? "0" : $lIm->TOTAL_TGMP ;
      $total     = $lIm->TOTAL_TGMS+$lIm->TOTAL_TGMP;
      if($total <= 17){
        $ketTotal = "Malnutrisi";
      }else if($total > 17 && $total <= 23.5){
        $ketTotal = "Risiko Malnutrisi";
      }else if($total > 23.5){
        $ketTotal = "Tidak Malnutrisi";
      }

      if(!empty($lIm->IDPENILAIAN || $total <= 11)){
        $btnEditPenilaian = '<a href="#editModalPenilaian" class="btn btn-custom btn-block" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$lIm->IDPENILAIAN.'"><i class="fas fa-edit"></i></a>';
      }else{
        $btnEditPenilaian = '<a href="#" class="btn btn-custom btn-block disabled" ><i class="fas fa-edit"></i></a>';
      }

      $data[] = array(
        $no,
        $lIm->nokun,
        date("d-m-Y H:i:s",strtotime($lIm->tanggal)),
        '<b> Total Skrining '.$lIm->TOTAL_TGMS.'</b> <br> <b> Total Penilaian '.$totalTgmp.'</b> <br> <span style="color:#e96048;">Total Skor ( '.$total.' )</span>',
        $ketTotal,
        '<a href="#editModalSkrining" class="btn btn-purple btn-block" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$lIm->IDSKRINING.'"><i class="fas fa-edit"></i></a>',
        $btnEditPenilaian,
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listInstrumenMna->num_rows(),
      "recordsFiltered" => $listInstrumenMna->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalSkrining()
  {
    $id = $this->input->post('id');
    $datSkrining = $this->InstrumenMNA_Model->getSkriningMna($id);

    $data = array(
      'id'                     => $id,
      'datSkrining'            => $datSkrining,
      'listPenurunanAsupan'    => $this->masterModel->referensi(918),
      'listKehilanganBB'       => $this->masterModel->referensi(919),
      'listKemampuanMobilitas' => $this->masterModel->referensi(920),
      'listMenderitaStress'    => $this->masterModel->referensi(921),
      'listNeuropsikologis'    => $this->masterModel->referensi(922),
      'listNilaiIMT'           => $this->masterModel->referensi(923),
    );

    $this->load->view('Pengkajian/geriatri/instrumenMiniNutrionalAssesment/editSkrining', $data);
  }

  public function modalPenilaian()
  {
    $id = $this->input->post('id');
    $datPenilaian = $this->InstrumenMNA_Model->getPenilaianMna($id);

    $data = array(
      'id'                   => $id,
      'datPenilaian'         => $datPenilaian,
      'listTinggal'          => $this->masterModel->referensi(924),
      'listPenggunaan3Obat'  => $this->masterModel->referensi(925),
      'listLuka'             => $this->masterModel->referensi(926),
      'listMakanLengkap'     => $this->masterModel->referensi(927),
      'listKonsumsiMakanan1' => $this->masterModel->referensi(928),
      'listKonsumsiMakanan2' => $this->masterModel->referensi(929),
      'listKonsumsiMakanan3' => $this->masterModel->referensi(930),
      'listKonsumsiBuah'     => $this->masterModel->referensi(931),
      'listBanyakCairan'     => $this->masterModel->referensi(932),
      'listKonsumsiBuah2'    => $this->masterModel->referensi(933),
      'listCaraMakan'        => $this->masterModel->referensi(934),
      'listPandanganSendiri' => $this->masterModel->referensi(935),
      'listKesehatanLain'    => $this->masterModel->referensi(936),
      'listLingkarLengan'    => $this->masterModel->referensi(937),
      'listLingkarBetis'     => $this->masterModel->referensi(938),
    );

    $this->load->view('Pengkajian/geriatri/instrumenMiniNutrionalAssesment/editPenilaian', $data);
  }

   public function updateFPenilaianMna()
  {
    $id = $this->input->post('id');

    $data = array(
      'penilaian_g'  => $this->input->post('listTinggalEdit'),
      'penilaian_h'  => $this->input->post('listPenggunaan3ObatEdit'),
      'penilaian_i'  => $this->input->post('listLukaEdit'),
      'penilaian_j'  => $this->input->post('listMakanLengkapEdit'),
      'penilaian_k1' => $this->input->post('listKonsumsiMakanan1Edit'),
      'penilaian_k2' => $this->input->post('listKonsumsiMakanan2Edit'),
      'penilaian_k3' => $this->input->post('listKonsumsiMakanan3Edit'),
      'penilaian_l'  => $this->input->post('listKonsumsiBuahEdit'),
      'penilaian_m'  => $this->input->post('listBanyakCairanEdit'),
      'penilaian_n'  => $this->input->post('listKonsumsiBuah2Edit'),
      'penilaian_o'  => $this->input->post('listCaraMakanEdit'),
      'penilaian_p'  => $this->input->post('listPandanganSendiriEdit'),
      'penilaian_q'  => $this->input->post('listKesehatanLainEdit'),
      'penilaian_r'  => $this->input->post('listLingkarLenganEdit'),
      'penilaian_s'  => $this->input->post('listLingkarBetisEdit'),
      'oleh'         => $this->session->userdata('id'),
    );

    $this->InstrumenMNA_Model->updateFPenilaianMna($data,$id);
  }

  public function updateFSkriningMna()
  {
    $id = $this->input->post('id');

    $data= array(
      'skrining_a' => $this->input->post('listPenurunanAsupanEdit'),
      'skrining_b' => $this->input->post('listKehilanganBBEdit'),
      'skrining_c' => $this->input->post('listKemampuanMobilitasEdit'),
      'skrining_d' => $this->input->post('listMenderitaStressEdit'),
      'skrining_e' => $this->input->post('listNeuropsikologisEdit'),
      'skrining_f' => $this->input->post('listNilaiIMTEdit'),
      'bb'         => $this->input->post('bbMnaEdit'),
      'tb'         => $this->input->post('tbMnaEdit'),
    );

    $this->InstrumenMNA_Model->updateFSkriningMna($data,$id);
  }
}

/* End of file InstrumenMNA.php */
/* Location: ./application/controllers/geriatri/InstrumenMNA.php */
