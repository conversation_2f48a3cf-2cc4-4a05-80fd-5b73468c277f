<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SkalaOntarioMASS extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','geriatri/SkalaOntarioMASSModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_mass = $this->uri->segment(3);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $getDiagnosaMasuk = $this->SkalaOntarioMASSModel->getDiagnosaMasuk($getNomr['NOPEN']);
      $getPengkajian = $this->SkalaOntarioMASSModel->getPengkajian($id_mass);

      $data = array(
        'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
        'getNomr' => $getNomr,
        'getDiagnosaMasuk' => $getDiagnosaMasuk,
        'listKarenaJatuh' => $this->masterModel->referensi(1674),
        'listJatuh2Bln' => $this->masterModel->referensi(1675),
        'listPasienDilirium' => $this->masterModel->referensi(1676),
        'listPasienDisorientasi' => $this->masterModel->referensi(1677),
        'listPasienAgitasi' => $this->masterModel->referensi(1678),
        'listPakaiKacamata' => $this->masterModel->referensi(1679),
        'listPenglihatanBuram' => $this->masterModel->referensi(1680),
        'listPasienGlaukoma' => $this->masterModel->referensi(1681),
        'listPerilakuBerkemih' => $this->masterModel->referensi(1682),
        'listTransferMandiri' => $this->masterModel->referensi(1683),
        'getPengkajian' => $getPengkajian
      );
      $this->load->view('Pengkajian/geriatri/SkalaOntarioMASS/skalaOntarioMASS', $data);
    }

    public function indexRJ() {
      $nokun = $this->uri->segment(4);
      $id_mass = $this->uri->segment(3);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $getDiagnosaMasuk = $this->SkalaOntarioMASSModel->getDiagnosaMasuk($getNomr['NOPEN']);
      $getPengkajian = $this->SkalaOntarioMASSModel->getPengkajian($id_mass);

      $data = array(
        'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
        'nokun' => $nokun,
        'getNomr' => $getNomr,
        'getDiagnosaMasuk' => $getDiagnosaMasuk,
        'listKarenaJatuh' => $this->masterModel->referensi(1674),
        'listJatuh2Bln' => $this->masterModel->referensi(1675),
        'listPasienDilirium' => $this->masterModel->referensi(1676),
        'listPasienDisorientasi' => $this->masterModel->referensi(1677),
        'listPasienAgitasi' => $this->masterModel->referensi(1678),
        'listPakaiKacamata' => $this->masterModel->referensi(1679),
        'listPenglihatanBuram' => $this->masterModel->referensi(1680),
        'listPasienGlaukoma' => $this->masterModel->referensi(1681),
        'listPerilakuBerkemih' => $this->masterModel->referensi(1682),
        'listTransferMandiri' => $this->masterModel->referensi(1683),
        'getPengkajian' => $getPengkajian
      );
      $this->load->view('Pengkajian/geriatri/SkalaOntarioMASS/skalaOntarioMASS', $data);
    }

    public function indexIntervensiRRRS() {
      $id_mass = $this->input->post('id');
      $historyIntervensiRRRS = $this->SkalaOntarioMASSModel->table_intervensiRRRS($id_mass);

      $data = array(
        'id_mass' => $id_mass,
        'listIntervensi' => $this->masterModel->referensi(1692),
        'historyIntervensiRRRS' => $historyIntervensiRRRS
      );

      $this->load->view('Pengkajian/geriatri/SkalaOntarioMASS/intervensiRRRS', $data);
    }

    public function historyIntervensiRRRS() {
      $id_intervensi_rrrs = $this->input->post('id');
      $getHistoryIntervensiRRRS = $this->SkalaOntarioMASSModel->getHistoryIntervensiRRRS($id_intervensi_rrrs);

      $data = array(
        'id_intervensi_rrrs' => $id_intervensi_rrrs,
        'listIntervensi' => $this->masterModel->referensi(1692),
        'getHistoryIntervensiRRRS' => $getHistoryIntervensiRRRS
      );

      $this->load->view('Pengkajian/geriatri/SkalaOntarioMASS/viewEditIntervensiRRRS', $data);
    }

    public function indexIntervensiRT() {
      $id_mass = $this->input->post('id');
      $historyIntervensiRT = $this->SkalaOntarioMASSModel->table_intervensiRT($id_mass);

      $data = array(
        'id_mass' => $id_mass,
        'listShift' => $this->masterModel->referensi(654),
        'listIntervensi' => $this->masterModel->referensi(1693),
        'historyIntervensiRT' => $historyIntervensiRT
      );

      $this->load->view('Pengkajian/geriatri/SkalaOntarioMASS/intervensiRT', $data);
    }

    public function historyIntervensiRT() {
      $id_intervensi_rt = $this->input->post('id');
      $getHistoryIntervensiRT = $this->SkalaOntarioMASSModel->getHistoryIntervensiRT($id_intervensi_rt);

      $data = array(
        'id_intervensi_rt' => $id_intervensi_rt,
        'listShift' => $this->masterModel->referensi(654),
        'listIntervensi' => $this->masterModel->referensi(1693),
        'getHistoryIntervensiRT' => $getHistoryIntervensiRT
      );

      $this->load->view('Pengkajian/geriatri/SkalaOntarioMASS/viewEditIntervensiRT', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $data = array(
            'nokun' => $post['nokun'],
            'diagnosa_masuk' => isset($post['diagnosa_masuk']) ? $post['diagnosa_masuk']: "",
            'tanggal' => isset($post['tanggal']) ? date('Y-m-d',strtotime($post['tanggal'])): "",
            'karena_jatuh' => isset($post['karena_jatuh']) ? $post['karena_jatuh']: "",
            'jatuh_2_bln' => isset($post['jatuh_2_bln']) ? $post['jatuh_2_bln']: "",
            'pasien_dirilium' => isset($post['pasien_dirilium']) ? $post['pasien_dirilium']: "",
            'pasien_disorientasi' => isset($post['pasien_disorientasi']) ? $post['pasien_disorientasi']: "",
            'pasien_agitasi' => isset($post['pasien_agitasi']) ? $post['pasien_agitasi']: "",
            'pakai_kacamata' => isset($post['pakai_kacamata']) ? $post['pakai_kacamata']: "",
            'penglihatan_buram' => isset($post['penglihatan_buram']) ? $post['penglihatan_buram']: "",
            'pasien_glaukoma' => isset($post['pasien_glaukoma']) ? $post['pasien_glaukoma']: "",
            'perilaku_berkemih' => isset($post['perilaku_berkemih']) ? $post['perilaku_berkemih']: "",
            'transfer' => isset($post['transfer']) ? $post['transfer']: "",
            'mobilitas' => isset($post['mobilitas']) ? $post['mobilitas']: "",
            'total' => isset($post['totalSkalaOntarioMASS']) ? $post['totalSkalaOntarioMASS']: "",
            'tingkat_risiko' => isset($post['tingkat_risiko']) ? $post['tingkat_risiko']: "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_mass'])) {
            $this->db->where('keperawatan.tb_skala_ontario_mass.id', $post['id_mass']);
            $this->db->update('keperawatan.tb_skala_ontario_mass', $data);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_skala_ontario_mass', $data);

              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->SkalaOntarioMASSModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function action_intervensiRRRS($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $data = array(
            'id_mass' => $post['id_mass'],
            'tanggal' => isset($post['tanggal']) ? date('Y-m-d',strtotime($post['tanggal'])): "",
            'intervensi' => isset($post['intervensi']) ? json_encode($post['intervensi']): "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_intervensi_rrrs'])) {
            $this->db->where('keperawatan.tb_skala_ontario_mass_intervensi_rrrs.id', $post['id_intervensi_rrrs']);
            $this->db->update('keperawatan.tb_skala_ontario_mass_intervensi_rrrs', $data);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_skala_ontario_mass_intervensi_rrrs', $data);

              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }
      }
    }

    public function action_intervensiRT($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $data = array(
            'id_mass' => $post['id_mass'],
            'tanggal' => isset($post['tanggal']) ? date('Y-m-d',strtotime($post['tanggal'])): "",
            'shift' => isset($post['shift']) ? $post['shift']: "",
            'intervensi' => isset($post['intervensi']) ? json_encode($post['intervensi']): "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_intervensi_rt'])) {
            $this->db->where('keperawatan.tb_skala_ontario_mass_intervensi_rt.id', $post['id_intervensi_rt']);
            $this->db->update('keperawatan.tb_skala_ontario_mass_intervensi_rt', $data);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_skala_ontario_mass_intervensi_rt', $data);

              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }
      }
    }

    public function datatables(){
        $result = $this->SkalaOntarioMASSModel->datatables();

        $data = array();
        foreach ($result as $row){
          $action = "";

          if($row -> tingkat_risiko == 'RR' || $row -> tingkat_risiko == 'RS') {
            $action = '<button class="btn btn-primary btn-block btn-sm intervensiRRRS" data-target="#modalIntervensiRRRS" data-toggle="modal" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> RR/RS</button>';
          } else if($row -> tingkat_risiko == 'RT') {
            $action = '<button class="btn btn-danger btn-block btn-sm intervensiRT" data-target="#modalIntervensiRT" data-toggle="modal" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> RT</button>';
          }
          $sub_array = array();
          $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editSkalaOntarioMASS" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
          $sub_array[] = $action;
          $sub_array[] = date('d M Y', strtotime($row -> TANGGAL));
          $sub_array[] = $row -> RUANGAN;
          $sub_array[] = $row -> total;
          $sub_array[] = $row -> tingkat_risiko;
          $sub_array[] = $row -> USER;

          $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->SkalaOntarioMASSModel->total_count(),
            "recordsFiltered"   => $this->SkalaOntarioMASSModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}