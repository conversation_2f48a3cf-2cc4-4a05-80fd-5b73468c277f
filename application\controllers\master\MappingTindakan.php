<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MappingTindakan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) OR ! in_array(6,$this->session->userdata('akses'))){
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel'));
    }

    public function index()
    {
        $referensiPK        = $this->masterModel->referensiTindakanSimpelPK();
        $referensiRadiologi = $this->masterModel->referensiTindakanSimpelRadiologi();
		$referensiProsedur  = $this->masterModel->referensiTindakanSimpelProsedur();

        $data = array(
            'title'              => 'Halaman Master',
            'isi'                => 'Master/tindakan/index',
            'referensiPK'        => $referensiPK,
            'referensiRadiologi' => $referensiRadiologi,
			'referensiProsedur'  => $referensiProsedur,
        );

        $this->load->view('layout/wrapper', $data);
    }

    public function listReferensiPK()
    {
        $draw   = intval($this->input->get("draw"));
        $start  = intval($this->input->get("start"));
        $length = intval($this->input->get("length"));

        $listReferensiPK = $this->masterModel->referensiTindakanSimpelPK();

        // echo "<pre>";print_r($listReferensiPK);exit();
        $data = array();
        $no   = 1;
        foreach ($listReferensiPK->result() as $lr) {

            $data[] = array(
                '<a href="#pilihTindakanPK" class="btn btn-sm btn-block btn-custom pilihTindakanPK" data-toggle="modal" data-id="' . $lr->ID_REFERENSI . '" pilihTindakanPK="' . $lr->ID_REFERENSI . '" id_tindakan_simpel="' . $lr->TINDAKAN . '"><i class="fa fa-check"></i></a>',
                $no,
                $lr->DESKRIPSI,
                $lr->NAMA,
                $lr->RUANGAN,
            );
            $no++;
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listReferensiPK->num_rows(),
            "recordsFiltered" => $listReferensiPK->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }

    public function listReferensiRadiologi()
    {
        $draw   = intval($this->input->get("draw"));
        $start  = intval($this->input->get("start"));
        $length = intval($this->input->get("length"));

        $listReferensiRadiologi = $this->masterModel->referensiTindakanSimpelRadiologi();

        // echo "<pre>";print_r($listReferensiRadiologi);exit();
        $data = array();
        $no   = 1;
        foreach ($listReferensiRadiologi->result() as $lr) {

            $data[] = array(
                '<a href="#pilihTindakanRadiologi" class="btn btn-sm btn-block btn-custom pilihTindakanRadiologi" data-toggle="modal" data-id="' . $lr->ID_REFERENSI . '" pilihTindakanRadiologi="' . $lr->ID_REFERENSI . '" id_tindakan_simpel="' . $lr->TINDAKAN . '"><i class="fa fa-check"></i></a>',
                $no,
                $lr->DESKRIPSI,
                $lr->NAMA,
                $lr->RUANGAN,
            );
            $no++;
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listReferensiRadiologi->num_rows(),
            "recordsFiltered" => $listReferensiRadiologi->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }

	public function listReferensiProsedur()
	{
		 $draw   = intval($this->input->get("draw"));
		 $start  = intval($this->input->get("start"));
		 $length = intval($this->input->get("length"));
		 
		 $listReferensiProsedur = $this->masterModel->referensiTindakanSimpelProsedur();
		  //echo "<pre>";print_r($listReferensiProsedur);exit();
		 $data = array();
		 $no   = 1;
		 foreach ($listReferensiProsedur->result() as $lr) {

			$data[] = array(
				'<a href="#pilihTindakanProsdur" class="btn btn-sm btn-block btn-custom pilihTindakanProsdur" data-toggle="modal" data-id="' . $lr->ID_REFERENSI . '" pilihTindakanProsdur="' . $lr->ID_REFERENSI . '" id_tindakan_simpel="' . $lr->TINDAKAN . '"><i class="fa fa-check"></i></a>',
				$no,
				$lr->DESKRIPSI,
				$lr->NAMA,
				$lr->RUANGAN,
			);
			$no++;
		}

		$output = array(
			"draw"            => $draw,
			"recordsTotal"    => $listReferensiProsedur->num_rows(),
			"recordsFiltered" => $listReferensiProsedur->num_rows(),
			"data"            => $data
		);
		echo json_encode($output);
	}
	
    public function SReferensiAktif()
    {
        $id_referensi = $this->input->post('id_referensi');
        $tindakan     = $this->input->post('tindakan');

        $referensi = array();
        $referensi['tindakan'] = $id_referensi;

        // print_r($referensi);exit();

        $update = $this->masterModel->SReferensiAktif($referensi, $tindakan);
      }

      public function SReferensiNonAktif()
      {
          $kosong   = $this->input->post('kosong');
          $tindakan = $this->input->post('tindakan');

          $referensi = array();
          $referensi['tindakan'] = $kosong;

          $update = $this->masterModel->SReferensiNonAktif($referensi, $tindakan);
        }

    public function pilihanDataPK()
    {
        $id             = $this->input->post('id');
        $tindakanSimpel = $this->tindakanPK($id);
        $data = array(
            'tindakanSimpel' => $tindakanSimpel,
            'idTindakan'     => $id,
        );
        $this->load->view('Master/tindakan/modalMappingTindakan', $data);
    }

    public function pilihanDataRadiologi()
    {
        $id             = $this->input->post('id');
        $tindakanSimpel = $this->tindakanRadiologi($id);
        $data = array(
            'tindakanSimpel' => $tindakanSimpel,
            'idTindakan'     => $id,
        );
        $this->load->view('Master/tindakan/modalMappingTindakan', $data);
    }

	public function pilihanDataProsedur()
	{
		$id				= $this->input->post('id');
		$tindakanSimpel = $this->tindakanProsedur($id);
		$data = array(
			'tindakanSimpel' => $tindakanSimpel,
            'idTindakan'     => $id,
		);
		$this->load->view('Master/tindakan/modalMappingTindakan', $data);
	}
	
    public function tindakanAktif()
    {
        $result = $this->masterModel->tindakanAktif();
        echo json_encode($result);
    }

    public function tindakanNonAktif()
    {
        $result = $this->masterModel->tindakanNonAktif();
        echo json_encode($result);
    }

    public function tindakanPK($id)
    {
        $role_menu = $this->masterModel->tindakanSimpelPK($id);
        $data = array();
        foreach ($role_menu as $main) {
            $tindakan_array = array();
            $tindakan_array['id']     = $main['ID_TINDAKAN_SIMPEL'];
            $tindakan_array['nama']   = $main['TINDAKAN_SIMPEL'];
            $tindakan_array['status'] = $main['STATUS_TINDAKAN_SIMPEL'];
            $tindakan_array['tarif']  = $main['TARIF'];
            $data[] = $tindakan_array;
        }
        return $data;
    }

    public function tindakanRadiologi($id)
    {
        $role_menu = $this->masterModel->tindakanSimpelRadiologi($id);
        $data = array();
        foreach ($role_menu as $main) {
            $tindakan_array = array();
            $tindakan_array['id']     = $main['ID_TINDAKAN_SIMPEL'];
            $tindakan_array['nama']   = $main['TINDAKAN_SIMPEL'];
            $tindakan_array['status'] = $main['STATUS_TINDAKAN_SIMPEL'];
            $tindakan_array['tarif']  = $main['TARIF'];
            $data[] = $tindakan_array;
        }
        return $data;
    }
	
	public function tindakanProsedur($id)
	{
		$role_menu = $this->masterModel->tindakanSimpelProsedur($id);
        $data = array();
        foreach ($role_menu as $main) {
            $tindakan_array = array();
            $tindakan_array['id']     = $main['ID_TINDAKAN_SIMPEL'];
            $tindakan_array['nama']   = $main['TINDAKAN_SIMPEL'];
            $tindakan_array['status'] = $main['STATUS_TINDAKAN_SIMPEL'];
            $tindakan_array['tarif']  = $main['TARIF'];
            $data[] = $tindakan_array;
        }
        return $data;
	}
    ///////////////////////////////////////////////////////////// END TINDAKAN /////////////////////////////////////////////////////////////////////////
}

/* End of file MappingTindakan.php */
/* Location: ./application/controllers/master/MappingTindakan.php */