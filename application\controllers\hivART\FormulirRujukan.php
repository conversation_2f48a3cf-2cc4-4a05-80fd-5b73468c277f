<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormulirRujukan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    // if (!in_array(8, $this->session->userdata('akses'))) {
    //   redirect('login');
    // }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'hivART/FormulirRujukan_Model'));
  }


  public function index()
  {
    $nomr  = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'nomr'  => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
    //   'getNomr' => $getNomr,
    //   'listDrUmum' => $this->masterModel->listDrUmum(),
    //   'resep' => $this->masterModel->referensi(1742),
    //   'radiologi' => $this->masterModel->referensi(1743),
    //   'laboratorium' => $this->masterModel->referensi(1744),
    //   'rehab' => $this->masterModel->referensi(1745),
    );

    $this->load->view('Pengkajian/hivART/formulirRujukan/index', $data);
  }

}

/* End of file BuktiPelayananRJ.php */
/* Location: ./application/controllers/emr/BuktiPelayananRJ.php */
