<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AnamnesaModel extends MY_Model {
  protected $_table_name = 'medis.tb_anamnesa';
  protected $_primary_key = 'id_emr';
  protected $_order_by = 'id_emr';
  protected $_order_by_type = 'DESC';

  function __construct(){
    parent::__construct();
  }

  public function getPerawat($id){
  	$query = $this->db->query(
        "SELECT * FROM keperawatan.tb_anamnesa_perawat WHERE id_emr='$id'"
    );
    return $query->row_array();
  }

}
