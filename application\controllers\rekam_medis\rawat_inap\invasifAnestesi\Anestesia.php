<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Anestesia extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
        $nomr = $this->uri->segment(3);
        $nopen = $this->uri->segment(4);
        $nokun = $this->uri->segment(5);
        $history_sa = $this->pengkajianAwalModel->history_sa($nomr);
        $sa_riwayatpengobatan = $this->masterModel->referensi(529);
        $sa_alergi = $this->masterModel->referensi(530);
        $sa_riwayat_apk = $this->masterModel->referensi(531);
        $sa_riwayat_merokok_alkohol = $this->masterModel->referensi(532);
        $sa_wajah = $this->masterModel->referensi(585);
        $sa_thorax = $this->masterModel->referensi(586);
        $sa_kemampuan = $this->masterModel->referensi(587);
        $sa_jarak = $this->masterModel->referensi(588);
        $sa_mallampati = $this->masterModel->referensi(589);
        $sa_obstruksi = $this->masterModel->referensi(590);
        $sa_mobilitas_menggerakan = $this->masterModel->referensi(592);
        $sa_mobilitas_melakukan = $this->masterModel->referensi(593);
        $sa_mobilitas_pasien = $this->masterModel->referensi(594);
        $sa_sistem_respirasi = $this->masterModel->referensi(595);
        $sa_sistem_kardiovaskular = $this->masterModel->referensi(596);
        $sa_sistem_renal_endokrin = $this->masterModel->referensi(597);
        $sa_hepato = $this->masterModel->referensi(598);
        $sa_neuro = $this->masterModel->referensi(599);
        $sa_lain_lain = $this->masterModel->referensi(600);
        $sa_teknik_anestesi = $this->masterModel->referensi(745);
        $sa_monitoring = $this->masterModel->referensi(746);
        $sa_kontrol_nyeri = $this->masterModel->referensi(747);
        $sa_pemasangan_iv_line = $this->masterModel->referensi(748);
        $sa_kesiapan_mesin = $this->masterModel->referensi(749);
        $sa_sumber_gas = $this->masterModel->referensi(750);
        $sa_penyakit_yang_diderita = $this->masterModel->referensi(751);
        $sa_gigi_palsu = $this->masterModel->referensi(752);
        $sa_kontak_lens = $this->masterModel->referensi(753);
        $sa_penggunaan_obat = $this->masterModel->referensi(288);
        $sa_kesadaran = $this->masterModel->referensi(727);
        $sa_pernapasan = $this->masterModel->referensi(728);
        $sa_nyeri = $this->masterModel->referensi(729);
        $sa_risiko_jatuh = $this->masterModel->referensi(739);
        $sa_ruangan = $this->masterModel->referensi(736);
        $sa_nyeri_keluar = $this->masterModel->referensi(738);
        $sa_risiko_jatuh_keluar = $this->masterModel->referensi(739);
        $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
        $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
        $listDrAnestesi = $this->masterModel->listDrAnestesi();
        $listDr = $this->masterModel->listDrUmum();
        $skalaNyeriNRS = $this->masterModel->referensi(114);
        $skalaNyeriWBR = $this->masterModel->referensi(115);
        $skalaNyeriFLACC = $this->masterModel->referensi(123);
        $skalaNyeriBPS = $this->masterModel->referensi(133);
        $efeksampingNRS = $this->masterModel->referensi(118);
        $efeksampingWBR = $this->masterModel->referensi(119);
        $efeksampingFLACC = $this->masterModel->referensi(131);
        $efeksampingBPS = $this->masterModel->referensi(134);
        $statusnyeriNRS = $this->masterModel->referensi(136);
        $statusnyeriWBR = $this->masterModel->referensi(136);
        $statusnyeriFLACC = $this->masterModel->referensi(136);
        $statusnyeriBPS = $this->masterModel->referensi(136);
        $riwayatAlergi = $this->masterModel->referensi(2);
        $golonganDarah = $this->masterModel->referensi(756);
        $rhesus = $this->masterModel->referensi(882);
        $ruangan = 2;

    $data = array(
            'nokun' => $nokun,
            'ruangan' => $ruangan,
            'sa_riwayatpengobatan' => $sa_riwayatpengobatan,
            // 'listPerawat' => $this->masterModel->listPegawai(),
            'listPerawat' => $this->masterModel->listPerawatPenataAnestesi(),
            'sa_alergi' => $sa_alergi,
            'sa_riwayat_apk' => $sa_riwayat_apk,
            'sa_riwayat_merokok_alkohol' => $sa_riwayat_merokok_alkohol,
            'sa_wajah' => $sa_wajah,
            'golonganDarah' => $golonganDarah,
            'rhesus' => $rhesus,
            'sa_thorax' => $sa_thorax,
            'sa_kemampuan' => $sa_kemampuan,
            'sa_jarak' => $sa_jarak,
            'sa_mallampati' => $sa_mallampati,
            'sa_obstruksi' => $sa_obstruksi,
            'sa_mobilitas_menggerakan' => $sa_mobilitas_menggerakan,
            'sa_mobilitas_melakukan' => $sa_mobilitas_melakukan,
            'sa_mobilitas_pasien' => $sa_mobilitas_pasien,
            'sa_sistem_respirasi' => $sa_sistem_respirasi,
            'sa_sistem_kardiovaskular' => $sa_sistem_kardiovaskular,
            'sa_sistem_renal_endokrin' => $sa_sistem_renal_endokrin,
            'sa_hepato' => $sa_hepato,
            'sa_neuro' => $sa_neuro,
            'sa_lain_lain' => $sa_lain_lain,
            'sa_teknik_anestesi' => $sa_teknik_anestesi,
            'sa_monitoring' => $sa_monitoring,
            'sa_kontrol_nyeri' => $sa_kontrol_nyeri,
            'sa_pemasangan_iv_line' => $sa_pemasangan_iv_line,
            'sa_kesiapan_mesin' => $sa_kesiapan_mesin,
            'sa_sumber_gas' => $sa_sumber_gas,
            'sa_penyakit_yang_diderita' => $sa_penyakit_yang_diderita,
            'sa_gigi_palsu' => $sa_gigi_palsu,
            'sa_kontak_lens' => $sa_kontak_lens,
            'sa_penggunaan_obat' => $sa_penggunaan_obat,
            'sa_kesadaran' => $sa_kesadaran,
            'sa_pernapasan' => $sa_pernapasan,
            'sa_nyeri' => $sa_nyeri,
            'sa_risiko_jatuh' => $sa_risiko_jatuh,
            'sa_ruangan' => $sa_ruangan,
            'sa_nyeri_keluar' => $sa_nyeri_keluar,
            'sa_risiko_jatuh_keluar' => $sa_risiko_jatuh_keluar,
            'ruanganRawatJalan' => $ruanganRawatJalan,
            'ruanganRawatInap' => $ruanganRawatInap,
            'history_sa' => $history_sa,
            'listDrAnestesi' => $listDrAnestesi,
            'listDr' => $listDr,
            'riwayatAlergi' => $riwayatAlergi,
            'skalaNyeriNRS' => $skalaNyeriNRS,
            'skalaNyeriWBR' => $skalaNyeriWBR,
            'skalaNyeriFLACC' => $skalaNyeriFLACC,
            'skalaNyeriBPS' => $skalaNyeriBPS,
            'efeksampingNRS' => $efeksampingNRS,
            'efeksampingWBR' => $efeksampingWBR,
            'efeksampingFLACC' => $efeksampingFLACC,
            'efeksampingBPS' => $efeksampingBPS,
            'statusnyeriNRS' => $statusnyeriNRS,
            'statusnyeriWBR' => $statusnyeriWBR,
            'statusnyeriFLACC' => $statusnyeriFLACC,
            'statusnyeriBPS' => $statusnyeriBPS,
          );
        $this->load->view('Pengkajian/anestesia/status_anestesia/index', $data);
  }

  public function viewIndexRi($idNorm, $idNopen, $idNokun, $idLoad)
  {
        $nomr = $idNorm;
        $nopen = $idNopen;
        $nokun = $idNokun;
        $history_sa = $this->pengkajianAwalModel->history_sa($nomr);
        $sa_riwayatpengobatan = $this->masterModel->referensi(529);
        $sa_alergi = $this->masterModel->referensi(530);
        $sa_riwayat_apk = $this->masterModel->referensi(531);
        $sa_riwayat_merokok_alkohol = $this->masterModel->referensi(532);
        $sa_wajah = $this->masterModel->referensi(585);
        $sa_thorax = $this->masterModel->referensi(586);
        $sa_kemampuan = $this->masterModel->referensi(587);
        $sa_jarak = $this->masterModel->referensi(588);
        $sa_mallampati = $this->masterModel->referensi(589);
        $sa_obstruksi = $this->masterModel->referensi(590);
        $sa_mobilitas_menggerakan = $this->masterModel->referensi(592);
        $sa_mobilitas_melakukan = $this->masterModel->referensi(593);
        $sa_mobilitas_pasien = $this->masterModel->referensi(594);
        $sa_sistem_respirasi = $this->masterModel->referensi(595);
        $sa_sistem_kardiovaskular = $this->masterModel->referensi(596);
        $sa_sistem_renal_endokrin = $this->masterModel->referensi(597);
        $sa_hepato = $this->masterModel->referensi(598);
        $sa_neuro = $this->masterModel->referensi(599);
        $sa_lain_lain = $this->masterModel->referensi(600);
        $sa_teknik_anestesi = $this->masterModel->referensi(745);
        $sa_monitoring = $this->masterModel->referensi(746);
        $sa_kontrol_nyeri = $this->masterModel->referensi(747);
        $sa_pemasangan_iv_line = $this->masterModel->referensi(748);
        $sa_kesiapan_mesin = $this->masterModel->referensi(749);
        $sa_sumber_gas = $this->masterModel->referensi(750);
        $sa_penyakit_yang_diderita = $this->masterModel->referensi(751);
        $sa_gigi_palsu = $this->masterModel->referensi(752);
        $sa_kontak_lens = $this->masterModel->referensi(753);
        $sa_penggunaan_obat = $this->masterModel->referensi(288);
        $sa_kesadaran = $this->masterModel->referensi(727);
        $sa_pernapasan = $this->masterModel->referensi(728);
        $sa_nyeri = $this->masterModel->referensi(729);
        $sa_risiko_jatuh = $this->masterModel->referensi(739);
        $sa_ruangan = $this->masterModel->referensi(736);
        $sa_nyeri_keluar = $this->masterModel->referensi(738);
        $sa_risiko_jatuh_keluar = $this->masterModel->referensi(739);
        $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
        $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
        $listDrAnestesi = $this->masterModel->listDrAnestesi();
        $listDr = $this->masterModel->listDrUmum();
        $skalaNyeriNRS = $this->masterModel->referensi(114);
        $skalaNyeriWBR = $this->masterModel->referensi(115);
        $skalaNyeriFLACC = $this->masterModel->referensi(123);
        $skalaNyeriBPS = $this->masterModel->referensi(133);
        $efeksampingNRS = $this->masterModel->referensi(118);
        $efeksampingWBR = $this->masterModel->referensi(119);
        $efeksampingFLACC = $this->masterModel->referensi(131);
        $efeksampingBPS = $this->masterModel->referensi(134);
        $statusnyeriNRS = $this->masterModel->referensi(136);
        $statusnyeriWBR = $this->masterModel->referensi(136);
        $statusnyeriFLACC = $this->masterModel->referensi(136);
        $statusnyeriBPS = $this->masterModel->referensi(136);
        $riwayatAlergi = $this->masterModel->referensi(2);
        $golonganDarah = $this->masterModel->referensi(756);
        $rhesus = $this->masterModel->referensi(882);
        $ruangan = 2;
        $get_sa = $this->pengkajianAwalModel->get_sa($idLoad);

    $data = array(
            'get_sa' => $get_sa,
            'nokun' => $nokun,
            'ruangan' => $ruangan,
            'sa_riwayatpengobatan' => $sa_riwayatpengobatan,
            'golonganDarah' => $golonganDarah,
            'rhesus' => $rhesus,
            'listPerawat' => $this->masterModel->listPerawatPenataAnestesi(),
            'sa_alergi' => $sa_alergi,
            'sa_riwayat_apk' => $sa_riwayat_apk,
            'sa_riwayat_merokok_alkohol' => $sa_riwayat_merokok_alkohol,
            'sa_wajah' => $sa_wajah,
            'sa_thorax' => $sa_thorax,
            'sa_kemampuan' => $sa_kemampuan,
            'sa_jarak' => $sa_jarak,
            'sa_mallampati' => $sa_mallampati,
            'sa_obstruksi' => $sa_obstruksi,
            'sa_mobilitas_menggerakan' => $sa_mobilitas_menggerakan,
            'sa_mobilitas_melakukan' => $sa_mobilitas_melakukan,
            'sa_mobilitas_pasien' => $sa_mobilitas_pasien,
            'sa_sistem_respirasi' => $sa_sistem_respirasi,
            'sa_sistem_kardiovaskular' => $sa_sistem_kardiovaskular,
            'sa_sistem_renal_endokrin' => $sa_sistem_renal_endokrin,
            'sa_hepato' => $sa_hepato,
            'sa_neuro' => $sa_neuro,
            'sa_lain_lain' => $sa_lain_lain,
            'sa_teknik_anestesi' => $sa_teknik_anestesi,
            'sa_monitoring' => $sa_monitoring,
            'sa_kontrol_nyeri' => $sa_kontrol_nyeri,
            'sa_pemasangan_iv_line' => $sa_pemasangan_iv_line,
            'sa_kesiapan_mesin' => $sa_kesiapan_mesin,
            'sa_sumber_gas' => $sa_sumber_gas,
            'sa_penyakit_yang_diderita' => $sa_penyakit_yang_diderita,
            'sa_gigi_palsu' => $sa_gigi_palsu,
            'sa_kontak_lens' => $sa_kontak_lens,
            'sa_penggunaan_obat' => $sa_penggunaan_obat,
            'sa_kesadaran' => $sa_kesadaran,
            'sa_pernapasan' => $sa_pernapasan,
            'sa_nyeri' => $sa_nyeri,
            'sa_risiko_jatuh' => $sa_risiko_jatuh,
            'sa_ruangan' => $sa_ruangan,
            'sa_nyeri_keluar' => $sa_nyeri_keluar,
            'sa_risiko_jatuh_keluar' => $sa_risiko_jatuh_keluar,
            'ruanganRawatJalan' => $ruanganRawatJalan,
            'ruanganRawatInap' => $ruanganRawatInap,
            'history_sa' => $history_sa,
            'listDrAnestesi' => $listDrAnestesi,
            'listDr' => $listDr,
            'riwayatAlergi' => $riwayatAlergi,
            'skalaNyeriNRS' => $skalaNyeriNRS,
            'skalaNyeriWBR' => $skalaNyeriWBR,
            'skalaNyeriFLACC' => $skalaNyeriFLACC,
            'skalaNyeriBPS' => $skalaNyeriBPS,
            'efeksampingNRS' => $efeksampingNRS,
            'efeksampingWBR' => $efeksampingWBR,
            'efeksampingFLACC' => $efeksampingFLACC,
            'efeksampingBPS' => $efeksampingBPS,
            'statusnyeriNRS' => $statusnyeriNRS,
            'statusnyeriWBR' => $statusnyeriWBR,
            'statusnyeriFLACC' => $statusnyeriFLACC,
            'statusnyeriBPS' => $statusnyeriBPS,
          );

    $this->load->view('Pengkajian/anestesia/status_anestesia/index', $data);
  }

}