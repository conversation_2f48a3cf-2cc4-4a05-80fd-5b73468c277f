<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TandaVitalAnes extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $post = $this->input->post();
    $nomr = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $historyTandaVital_SA = $this->pengkajianAwalModel->historyTandaVital_SA($nomr);

    $data = array(
      'historyTandaVital_SA' => $historyTandaVital_SA,
      'getNomr' => $getNomr,
    );

    $this->load->view('Pengkajian/anestesia/status_anestesia/tandavital_sa', $data);
  }

}