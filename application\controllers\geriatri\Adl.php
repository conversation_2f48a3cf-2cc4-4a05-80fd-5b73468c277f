<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Adl extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }
    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $idAdl = $this->uri->segment(8);
    $getAdl = $this->pengkajianAwalModel->get_adl($idAdl);

    $history_adl = $this->pengkajianAwalModel->history_adl($getNomr['NORM']);

    $data = array(
      'getNomr' => $getNomr,
      'nomr' => $getNomr['NORM'],
      'history_adl' => $history_adl,
      'id_adl' => $idAdl,
      'get_adl' => $getAdl,
    );
    
    $this->load->view('Pengkajian/geriatri/adl/index',$data);
  }

  public function action_adl($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $get_id_adl = !empty($post['id_adl']) ? $post['id_adl'] : $this->pengkajianAwalModel->getIdEmr();
        $data_adl = array(
          'id_adl'  => $get_id_adl,
          'nokun'    => isset($post['nokun']) ? $post['nokun'] : null,
          'no_1' => isset($post['no_1']) ? $post['no_1'] : null,
          'hasil_1' => isset($post['hasil_1']) ? $post['hasil_1'] : null,
          'no_2' => isset($post['no_2']) ? $post['no_2'] : null,
          'hasil_2' => isset($post['hasil_2']) ? $post['hasil_2'] : null,
          'no_3' => isset($post['no_3']) ? $post['no_3'] : null,
          'hasil_3' => isset($post['hasil_3']) ? $post['hasil_3'] : null,
          'no_4' => isset($post['no_4']) ? $post['no_4'] : null,
          'hasil_4' => isset($post['hasil_4']) ? $post['hasil_4'] : null,
          'no_5' => isset($post['no_5']) ? $post['no_5'] : null,
          'hasil_5' => isset($post['hasil_5']) ? $post['hasil_5'] : null,
          'no_6' => isset($post['no_6']) ? $post['no_6'] : null,
          'hasil_6' => isset($post['hasil_6']) ? $post['hasil_6'] : null,
          'no_7' => isset($post['no_7']) ? $post['no_7'] : null,
          'hasil_7' => isset($post['hasil_7']) ? $post['hasil_7'] : null,
          'no_8' => isset($post['no_8']) ? $post['no_8'] : null,
          'hasil_8' => isset($post['hasil_8']) ? $post['hasil_8'] : null,
          'no_9' => isset($post['no_9']) ? $post['no_9'] : null,
          'hasil_9' => isset($post['hasil_9']) ? $post['hasil_9'] : null,
          'no_10' => isset($post['no_10']) ? $post['no_10'] : null,
          'hasil_10' => isset($post['hasil_10']) ? $post['hasil_10'] : null,
          'total_adl' => isset($post['total_adl']) ? $post['total_adl'] : null,
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

        // print_r($data_adl);exit();

        if (!empty($post['id_adl'])) {
          $this->db->replace('db_layanan.tb_geriatri_adl', $data_adl);
          $result = array('status' => 'success', 'pesan' => 'ubah');
        }else {
          $this->db->insert('db_layanan.tb_geriatri_adl', $data_adl);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }

    }
  }

  public function view_adl()
  {
    $id         = $this->input->post('id');
    $get_adl = $this->pengkajianAwalModel->get_adl($id);

    $data = array(
      'get_adl' => $get_adl,
    );
    $this->load->view('Pengkajian/geriatri/adl/view',$data);
  }
}


/* End of file Adl.php */
/* Location: ./application/controllers/geriatri/adl/Adl.php */
