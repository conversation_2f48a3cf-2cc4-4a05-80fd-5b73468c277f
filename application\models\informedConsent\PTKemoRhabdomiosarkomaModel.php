<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PTKemoRhabdomiosarkomaModel extends CI_Model {

  public function simpanInformedConcent($data)
  {
    $this->db->insert('db_informed_consent.tb_informed_consent', $data);
    return $this->db->insert_id();
  }

  public function simpanPTKemoRhabdomiosarkoma($data)
  {
    $this->db->insert('db_informed_consent.tb_pt_kemo_rhabdomiosarkoma', $data);
  }

  public function listHistoryInformedConsentPTKemoRhabdomiosarkoma($nomr)
  {
    $query = $this->db->query("SELECT pp.NORM, tic.id, tic.nokun,
                              master.getNamaLengkapPegawai(ap.NIP) OLEH,
                              master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA,
                              tic.created_at tanggal
                              FROM db_informed_consent.tb_informed_consent tic
                              LEFT JOIN db_informed_consent.tb_pt_kemo_rhabdomiosarkoma tkr ON tkr.id_informed_consent = tic.id
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE pp.NORM = '$nomr' AND tic.`status` = 1
                              ");
    return $query;
  }
   public function getPTKemoRhabdomiosarkoma($id)
  {
    $query = $this->db->query("SELECT tic.*, tkr.*, tic.id idtic, tkr.id idtkr
                              , master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA, tic.created_at tanggal
                              FROM db_informed_consent.tb_informed_consent tic
                              LEFT JOIN db_informed_consent.tb_pt_kemo_rhabdomiosarkoma tkr ON tkr.id_informed_consent = tic.id
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE tic.id = $id AND tic.`status` = 1
                              ");
    return $query->row_array();
  }

  public function updateInformedConcent($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('db_informed_consent.tb_informed_consent', $data);
  }

  public function updatePTKemoRhabdomiosarkoma($data,$idtkr)
  {
    $this->db->where('id', $idtkr);
    $this->db->update('db_informed_consent.tb_pt_kemo_rhabdomiosarkoma', $data);
  }
}

/* End of file PTKemoRhabdomiosarkomaModel.php */
/* Location: ./application/models/informedConsent/PTKemoRhabdomiosarkomaModel.php */
