<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormTriaseModel extends CI_Model
{
  public function simpanTriase($dataTriase)
  {
    $this->db->insert('keperawatan.tb_triase_nonpasien', $dataTriase);
    return $this->db->insert_id();
  }
//   public function simpano2($dataO2)
//   {
//     $this->db->insert('db_pasien.tb_o2', $dataO2);
//     return $this->db->insert_id();
//   }
//   public function simpanTandaVital($dataTandaVital)
//   {
//     $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
//     return $this->db->insert_id();
//   }
//   public function simpanKesadaran($dataKesadaran)
//   {
//     $this->db->insert('db_pasien.tb_kesadaran', $dataKesadaran);
//     return $this->db->insert_id();
//   }

  function ceknorm($norm){
      $hasil = "SELECT * FROM master.pasien pas
      WHERE pas.NORM = ? AND pas.STATUS = ?";
      $bind = $this->db->query($hasil, array($norm, 1));
      return $bind;
  }

    function cekktp($ktp){
      $hasil = "SELECT * FROM master.kartu_identitas_pasien kip
      LEFT JOIN master.pasien pas ON pas.NORM = kip.NORM
      WHERE kip.NOMOR = ? AND kip.JENIS = ? AND pas.STATUS = ?";
      $bind = $this->db->query($hasil, array($ktp, 1, 1));
      return $bind;
  }

  public function referensi_nyeri($id)
  {
      $query = $this->db->query("SELECT *
                              FROM db_master.variabel dv
                              WHERE dv.id_referensi = '$id' AND dv.status='1' AND dv.id_variabel IN ('17','18') ORDER BY dv.seq,dv.id_variabel");

      return $query->result_array();
  }

  public function listTriase()
  {
    $query = $this->db->query("SELECT sv.id
            , sv.jenis_identitas
            , sv.no_identitas
            , sv.nama_lengkap
            , sv.oleh
            , master.getNamaLengkapPegawai(peng.NIP) DOKTER
            , sv.tanggal_masuk
            , sv.diterima_igd
            , sv.ket_diterima_igd
            , sv.mapping_nomr

      FROM keperawatan.tb_triase_nonpasien sv
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = sv.oleh
      LEFT JOIN master.dokter dpjp ON dpjp.NIP = peng.NIP


      WHERE sv.`status`=1

      ORDER BY sv.tanggal_masuk DESC, sv.jam desc");

    return $query;
  }

  public function detailHistoryTs($id)
  {
    $query = $this->db->query("SELECT sv.id, sv.nokun NOKUN, sv.tanggal_masuk TANGGAL, sv.jam JAM
      , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
      , rk.DESKRIPSI RUANGAN_KUNJUNGAN
      , sv.saturasiTriase
      , sv.penggunaanO2
      , (SELECT ref.id_referensi
      FROM keperawatan.tb_triase_ats_nonpasien pin
      LEFT JOIN db_master.variabel var ON var.id_variabel = pin.ats_detail
      LEFT JOIN db_master.referensi ref ON ref.id_referensi = var.id_referensi
      WHERE pin.id_triase=sv.id
      LIMIT 1) ATS_DEKSRIPSI
      , (SELECT ref.referensi
      FROM keperawatan.tb_triase_ats_nonpasien pin
      LEFT JOIN db_master.variabel var ON var.id_variabel = pin.ats_detail
      LEFT JOIN db_master.referensi ref ON ref.id_referensi = var.id_referensi
      WHERE pin.id_triase=sv.id
      LIMIT 1) ATS
      , (SELECT CONCAT('[', GROUP_CONCAT('''',pin.ats_detail,'''' SEPARATOR ','),']')
      FROM keperawatan.tb_triase_ats_nonpasien pin
      WHERE pin.id_triase=sv.id) CHECKBOX_ATS
      , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
      , sv.jenis_kunjungan JENIS_KUNJUNGAN, sv.rujukan_dari RUJUKAN_DARI
      , sv.tekanan_darah TEKANAN_DARAH, sv.per_tekanan_darah PER_TEKANAN_DARAH
      , sv.pernapasan PERNAPASAN, sv.nadi NADI, sv.suhu SUHU, sv.oleh OLEH
      , master.getNamaLengkapPegawai(peng.NIP) USER
      , sv.id_tanda_vital, sv.id_o2
      , tv.td_sistolik, tv.td_diastolik
      , tv.nadi, tv.pernapasan, tv.suhu
      , oo.penggunaan_o2, oo.saturasi_o2
      , sv.apakah_masuk_ews
      , tk.id ID_KESADARAN
      , tk.kesadaran


      FROM keperawatan.tb_triase_nonpasien sv
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = sv.nokun
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
      LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
      LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
      LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = sv.oleh
      LEFT JOIN db_pasien.tb_tanda_vital tv ON tv.id = sv.id_tanda_vital AND tv.nokun = sv.nokun
      LEFT JOIN db_pasien.tb_o2 oo ON oo.id = sv.id_o2 AND oo.nokun = sv.nokun
      LEFT JOIN keperawatan.tb_ews te ON sv.id = te.ref AND te.data_source=9
      LEFT JOIN db_pasien.tb_kesadaran tk ON sv.id = tk.ref AND tk.data_source=9
      WHERE sv.id='$id'");

    return $query->row_array();
  }

  public function vitalTriaseTerbaru($nokun)
  {
    $this->db->select('sv.tekanan_darah sistolik, sv.per_tekanan_darah diastolik, sv.pernapasan, sv.nadi, sv.suhu');
    $this->db->from('keperawatan.tb_triase_nonpasien sv');
    $this->db->where('sv.nokun', $nokun);
    $this->db->order_by('sv.id', 'DESC');
    $this->db->limit('1');
    $query = $this->db->get();
    return $query->row_array();
  }

  public function updateTriase($dataTriase, $idTriase)
  {
    $this->db->where('id', $idTriase);
    $this->db->update('keperawatan.tb_triase_nonpasien', $dataTriase);
  }

    public function cariMappingMR($mr)
  {
    $this->db->select('ps.NORM
                  , CONCAT(ps.NORM, " | ", ps.NAMA, " | ", DATE_FORMAT(ps.TANGGAL_LAHIR, "%d-%m-%Y")) ISI_SELECT');
    $this->db->from('master.pasien ps
');
    $this->db->where('ps.STATUS', 1);
    $this->db->where('ps.NORM', $mr);
    $this->db->limit('1');
    $query = $this->db->get();
    return $query->row_array();
  }

  function detailTriasebaru($id){
     $query = $this->db->query("SELECT sv.*
          , master.getNamaLengkapPegawai(peng.NIP) DOKTER

          FROM keperawatan.tb_triase_nonpasien sv

          LEFT JOIN aplikasi.pengguna peng ON peng.ID = sv.oleh
          LEFT JOIN master.dokter dpjp ON dpjp.NIP = peng.NIP

          WHERE sv.status=1
          AND sv.id = $id

          ORDER BY sv.tanggal_masuk DESC, sv.jam desc"
      );
     return $query;
  }
}

/* End of file FormulirTriaseModel.php */
/* Location: ./application/models/FormulirTriaseModel.php */