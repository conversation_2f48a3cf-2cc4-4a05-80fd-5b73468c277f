<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PrmjRi extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'EresepModel', 'hemodialisaModel', 'geriatri/InstrumenMNA_Model', 'laporanModel', 'rekam_medis/rawat_inap/catatanTerintegrasi/PrmjRiModel'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);

    $data = array(
      'nomr'    => $nomr,
      'nokun'   => $nokun,
      'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
      'listDr' => $this->masterModel->listDr(),
      'ruanganRskd' => $this->masterModel->ruanganRskd(),
    );

    $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/PrmjRi', $data);
  }

  public function pilihRangeSummary()
  {
    $post = $this->input->post('tgl');
    $nomr = $this->input->post('nomr');
    $pisah  = explode("-", $post);
    $tgl1 = date("Y-m-d", strtotime($pisah[0]));
    $tgl2 = date("Y-m-d", strtotime($pisah[1]));

    $dataSummaryList = $this->pengkajianAwalModel->dSummaryList($tgl1, $tgl2, $nomr);

    // echo"<pre>";print_r($dataSummaryList);exit();

    $data = array(
      'tgl1'  => $tgl1,
      'tgl2'  => $tgl2,
      'nomr'  => $nomr,
      'dataSummaryList'  => $dataSummaryList,
    );
    $this->load->view('Pengkajian/summaryList/dataPilihSummary', $data);
  }

  public function simpanPrmjRi()
  {
    $data = array(
      'nomr'              => $this->input->post('nomr'),
      'nokun'             => $this->input->post('nokun'),
      'tanggal_perawatan' => $this->input->post('tanggal'),
      'ruangan'           => $this->input->post('ruangan'),
      'diagnosis'         => $this->input->post('diagnosis'),
      'diagnosis_kanker'  => $this->input->post('diagnosis_kanker'),
      'tata_laksana'      => $this->input->post('tatalaksana'),
      'catatan_penting'   => $this->input->post('catatanPenting'),
      'dpjp'              => $this->input->post('dpjp'),
      'jenis'             => 3,
      'oleh'              => $this->session->userdata('id'),
    );
    // echo "<pre>";
    // print_r($data);
    // exit();
    $this->PrmjRiModel->simpanPrmjRi($data);
  }

  public function tblPrmjRi()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblPrmjRi = $this->PrmjRiModel->tblPrmjRi($nomr);
    $data = array();
    $no = 1;
    foreach ($tblPrmjRi->result() as $ts) {
      $button = $ts->jenis == 2 ? '<a href="#detailPrmjRi" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $ts->id . '"><i class="fas fa-edit"></i> Edit</a>' : '<a href="#detailPrmjRiLama" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $ts->id . '"><i class="fas fa-edit"></i> Edit</a>';

      $button = '<a href="#detailPrmjRiLama" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $ts->id . '"><i class="fas fa-edit"></i> Edit</a>';
      if($ts->jenis == 3){
          $button =  '<a href="#detailPrmjRi" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $ts->id . '"><i class="fas fa-edit"></i> Edit</a>';
      }elseif($ts->jenis == 2 || $ts->jenis == 4){
          $button = '-';
      }
      $data[] = array(
        $no,
        $ts->RUANGASAL,
        $ts->tanggal,
        $ts->diagnosis,
        $ts->tata_laksana,
        $ts->catatan_penting,
        $ts->NAMADOKTER,
        $ts->deskripsi_jenis,
        $button
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblPrmjRi->num_rows(),
      "recordsFiltered" => $tblPrmjRi->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function detailPrmjRi()
  {
    $id = $this->input->post("id");
    $datSLP = $this->PrmjRiModel->detailSum($id);
    $listDr = $this->masterModel->listDr();
    $ruanganRskd = $this->masterModel->ruanganRskd();

    echo '<div class="modal-header">';
    echo '<h4 class="modal-title" id="myModalLabel">Summary List ' . date("d-m-Y", strtotime($datSLP["tanggal"])) . '</h4>';
    echo '<button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>';
    echo '</div>';
    echo '<form id="formEditPrmjRi">';
    echo '<input type="hidden" value="' . $datSLP['id'] . '" name="idSum">';
    echo '<div class="modal-body">';
    echo '<div class="row">
            <div class="col-lg-4">
              <div class="form-group">
                <label>Tanggal</label>
                <input name="editTanggal" type="date" class="form-control" placeholder="[ Tanggal Masuk ]" value="'.date("Y-m-d",strtotime($datSLP['tanggal_perawatan'])).'">
              </div>
            </div>';
    echo    '<div class="col-lg-4">
              <div class="form-group">
                <label>DPJP</label>
                <select name="editDpjp" id="dpjp" class="form-control se">
                  <option disabled selected>[ Pilih Dokter DPJP ]</option>';
                  foreach ($listDr as $dpjpSmf) :
                    $se = '';
                    if($dpjpSmf['ID_DOKTER'] == $datSLP['dpjp']){
                      $se = 'selected';
                    }
    echo            '<option
                      value="'.$dpjpSmf['ID_DOKTER'].'"'.$se.'
                    >'. $dpjpSmf['DOKTER'] .
                    '</option>';
                  endforeach;
    echo        '</select>
              </div>
            </div>';
    echo    '<div class="col-lg-4">
              <div class="form-group">
                <label>Ruangan</label>
                <select name="editRuangan" id="ruangan" class="form-control se">
                  <option disabled selected>[ Pilih Ruang ]</option>';
                  foreach ($ruanganRskd as $rr) :
                    $se = '';
                    if($rr['ID_RUANGAN'] == $datSLP['ruangan']){
                      $se = 'selected';
                    }
    echo            '<option
                      value="'.$rr['ID_RUANGAN'].'"'.$se.'
                    >'. $rr['DESKRIPSI'] .
                    '</option>';
                  endforeach;
    echo        '</select>
              </div>
            </div>
          </div>';
    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Diagnosa Utama</label>';
    echo '<textarea class="form-control" name="editDiagnosis" rows="5" placeholder="Jelaskan">' . $datSLP['diagnosis'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Diagnosa Kanker</label>';
    echo '<textarea class="form-control" name="editDiagnosisKanker" rows="5" placeholder="Jelaskan">' . $datSLP['diagnosis_kanker'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Tatalaksana (Operasi, Terapi Sistemik, Radiasi)</label>';
    echo '<textarea class="form-control" name="edittatalaksana" rows="10" placeholder="Jelaskan">' . $datSLP['tata_laksana'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Catatan Penting Lainnya</label>';
    echo '<textarea class="form-control" name="editcatatanPenting" rows="10" placeholder="Jelaskan">' . $datSLP['catatan_penting'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Oleh</label>';
    echo '<input type="text" class="form-control" value="' . $datSLP['NAMADOKTER'] . '" readonly>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<div class="modal-footer">';
    echo '<button type="button" class="btn btn-warning" data-dismiss="modal"><i class="fa fa-refresh"></i> Close</button>';
    if ($this->session->userdata('id') == $datSLP['oleh'] && $datSLP['STATUSBUTTON'] == 1) {
      echo '<button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> Simpan</button>';
    }
    echo '</div>';
    echo '</div>';
    echo '</form>';
  }

  public function simpanEditPrmjRi()
  {
    $id = $this->input->post('idSum');

    $data = array(
      'tanggal_perawatan' => $this->input->post('editTanggal'),
      'dpjp' => $this->input->post('editDpjp'),
      'ruangan' => $this->input->post('editRuangan'),
      'diagnosis' => $this->input->post('editDiagnosis'),
      'diagnosis_kanker' => $this->input->post('editDiagnosisKanker'),
      'tata_laksana' => $this->input->post('edittatalaksana'),
      'catatan_penting' => $this->input->post('editcatatanPenting'),
    );

    // echo "<pre>";
    // print_r($data);
    // exit();
    $this->PrmjRiModel->updatePrmjRi($id, $data);
  }
}

/* End of file SummaryList.php */
/* Location: ./application/controllers/emr/SummaryList.php */
