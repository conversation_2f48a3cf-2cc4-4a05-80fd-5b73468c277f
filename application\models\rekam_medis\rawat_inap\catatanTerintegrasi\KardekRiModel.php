<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KardekRiModel extends CI_Model
{

	// Simpan list obat kardek luar
	public function simpanKardekLuar($data)
	{
		$this->db->insert('db_layanan.tb_kardek_obat_luar', $data);
		return $this->db->insert_id();
	}

	public function detailKardek($nopen)
	{
		$query = $this->db->query("CALL db_layanan.KardekList($nopen)");
		$this->db->reconnect();
		return $query->result_array();
	}

	public function historyAturanPakai($nopen, $idfarmasi, $paramgenerik, $katgenerik)
	{
		$query = $this->db->query("CALL db_layanan.HistoryPerubahanAturanPakai($nopen, $idfarmasi, $paramgenerik, $katgenerik)");
		$this->db->reconnect();
		return $query->result_array();
	}

	public function getAll($nomr)
	{
		$query = $this->db->query(
			"SELECT * FROM pendaftaran.pendaftaran WHERE NORM='$nomr' ORDER BY TANGGAL DESC"
		);
		return $query->result_array();
	}

	public function historyKeterangan($idfarmasi, $jenisobat)
	{
		$query = $this->db->query(
			"SELECT ket.id ID_KETERANGAN, ket.id_farmasi ID_FARMASI
			, ket.keterangan KETERANGAN, ket.jenis JENIS
			, ket.tanggal TANGGAL, master.getNamaLengkapPegawai(peng.NIP) OLEH
			FROM db_layanan.tb_kardek_keterangan ket
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = ket.oleh
			WHERE ket.id_farmasi='$idfarmasi' AND ket.jenis='$jenisobat' AND ket.status!=0"
		);
		return $query->result_array();
	}

	public function detailIsiNamaObatRacikan($nokun, $grupracikan)
	{
		$query = $this->db->query("SELECT CONCAT('RACIKAN ', far.GROUP_RACIKAN) GROUP_RACIKAN
, pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP
, kd.id_farmasi ID_FARMASI
, ib.NAMA NAMA_OBAT
, kat.NAMA KATEGORI
, apak.DESKRIPSI ATURAN_PAKAI
, far.JUMLAH JUMLAH_OBAT
, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
, var.variabel JALUR_PEMBERIAN_VAR
FROM db_layanan.tb_kardek_rawatinap kd
LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
LEFT JOIN aplikasi.pengguna peng ON peng.ID = ore.OLEH
LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
LEFT JOIN db_master.variabel var ON var.id_variabel = kd.jalur_pemberian
WHERE far.KUNJUNGAN='$nokun' AND far.GROUP_RACIKAN='$grupracikan' AND orde.KARDEX=1 AND far.RACIKAN!=0
ORDER BY far.JUMLAH DESC ");
		return $query->result_array();
	}

	public function isiNamaObatRacikan($nokun, $grupracikan)
	{
		$query = $this->db->query("SELECT CONCAT('RACIKAN ', far.GROUP_RACIKAN) GROUP_RACIKAN
, pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP
, kd.id_farmasi ID_FARMASI
, ib.NAMA NAMA_OBAT
, kat.NAMA KATEGORI
, apak.DESKRIPSI ATURAN_PAKAI
, far.JUMLAH JUMLAH_OBAT
, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
, var.variabel JALUR_PEMBERIAN_VAR
FROM db_layanan.tb_kardek_rawatinap kd
LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
LEFT JOIN aplikasi.pengguna peng ON peng.ID = ore.OLEH
LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
LEFT JOIN db_master.variabel var ON var.id_variabel = kd.jalur_pemberian
WHERE far.KUNJUNGAN='$nokun' AND far.GROUP_RACIKAN='$grupracikan' AND orde.KARDEX=1 AND far.RACIKAN!=0
ORDER BY far.JUMLAH DESC ");
		return $query->row_array();
	}

	public function detailObatGenNonGen($nopen, $idgenerik)
	{
		$query = $this->db->query("CALL db_layanan.DetailKardekNonRacik($nopen, $idgenerik, '')");
		$this->db->reconnect();
		return $query->result_array();
	}

	public function detailKardekView($nopen, $idgenerik, $katgenerik)
	{
		$query = $this->db->query("CALL db_layanan.KardekStop($nopen, $idgenerik, $katgenerik)");
		$this->db->reconnect();
		return $query->row_array();
	}

	public function detailKardekViewRacikan($nopen, $groupracikan)
	{
		$query = $this->db->query("CALL db_layanan.KardekStopRacikan($nopen, $groupracikan)");
		$this->db->reconnect();
		return $query->row_array();
	}

	public function detailKardekViewOld($idfarmasi)
	{
		$query = $this->db->query("SELECT pk.NOMOR NOKUN, p.NOMOR, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP, ruan.DESKRIPSI RUANGAN
			, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
			, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
			, orde.KETERANGAN KETERANGAN, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP

			, far.ID ID_FARMASI
			, ib.NAMA NAMA_OBAT, kat.NAMA KATEGORI, apak.DESKRIPSI ATURAN_PAKAI, far.JUMLAH JUMLAH_OBAT_PERTAMA, orde.DOSIS DOSIS_PEMBERIAN
			, SUM(kd.jumlah_obat) SISA_OBAT, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
			, IF(kd.jumlah_obat<=0,3,IF(kd.`status`=1,1,IF(kd.`status`=2,2,0))) STATUS_OBT
			, IF(kd.jumlah_obat<=0,'Obat Habis',IF(kd.`status`=1,'Aktif',IF(kd.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
			, var.variabel JALUR_PEMBERIAN_VAR
			, orde.KETERANGAN KETERANGAN

			FROM db_layanan.tb_kardek_rawatinap kd

			LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
			LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
			LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
			LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
			LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
			LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = ore.OLEH
			LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
			LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
			LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
			LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
			LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
			LEFT JOIN db_master.variabel var ON var.id_variabel = kd.jalur_pemberian

			WHERE far.ID='$idfarmasi' AND orde.KARDEX=1
			GROUP BY ib.NAMA_DASAR");
		return $query->row_array();
	}

	public function detailKardekViewLuar($idfarmasi)
	{
		$query = $this->db->query("SELECT 2 JENISH, 'INI OBAT LUAR' JENISH_DESKRIPSI,pk.NOMOR NOKUN, p.NORM, p.NOMOR NOPEN, kl.tanggal TANGGAL_PEMBUATAN_RESEP
			, r.DESKRIPSI RUANGAN
			, kd.tanggal_jam TANGGAL_PEMBERIAN
			, kd.dosis
			, kd.pasien_keluarga PASIEN_KELUARGA
			, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
			, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
			, kl.keterangan KETERANGAN
			, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
			, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
			, master.getNamaLengkapPegawai(pere.NIP) PERAWAT_2

			, kl.id ID_FARMASI
			, kl.nama_obat NAMA_OBAT, NULL KATEGORI
			, CONCAT(IF(kl.dosis IS NULL, '', CONCAT(kl.dosis,' / '))
			, IF(kl.jam IS NULL, '', CONCAT(kl.jam, ' Jam'))
			, IF(kl.jalur_pemberian IS NULL, '', CONCAT(' / ',var.variabel))) ATURAN_PAKAI
			, kl.jumlah JUMLAH_OBAT_PERTAMA
			, kr.jumlah_obat SISA_OBAT, kd.jalur_pemberian_input JALUR_PEMBERIAN_ID
			, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
			, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
			, var.variabel JALUR_PEMBERIAN_VAR
			, kl.keterangan KETERANGANS
			, null WAKTU_PEMBERIAN_INPUT
			, kd.id ID_PEMBERIAN
			, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
			, kl.rencana_jam RENCANA_JAM
			FROM db_layanan.tb_kardek_obat_luar kl

#db_layanan.tb_kardek_pemberian_luar kd

			LEFT JOIN db_layanan.tb_kardek_rawatinap_luar kr ON kr.id_kardek_obat_luar = kl.id
			LEFT JOIN db_layanan.tb_kardek_pemberian_luar kd ON kd.id = kd.id_kardek_obat_luar
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kl.nokun
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
			LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
			LEFT JOIN master.ruangan r ON r.ID = kl.ruangan
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = kl.oleh
			LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
			LEFT JOIN master.perawat pere ON pere.ID = kd.perawat_2

			WHERE kl.id='$idfarmasi'

			ORDER BY kd.tanggal_jam ASC");
		return $query->row_array();
	}

	public function detailHisKardekPemberianNew($nopen, $idgenerik, $katgenerik)
	{
		$query = $this->db->query("SELECT 2 JENISH, 'INI OBAT LUAR' JENISH_DESKRIPSI,pk.NOMOR NOKUN, p.NORM, p.NOMOR NOPEN, kl.tanggal TANGGAL_PEMBUATAN_RESEP
			, r.DESKRIPSI RUANGAN
			, kd.tanggal_jam TANGGAL_PEMBERIAN
			, kd.dosis
			, kd.pasien_keluarga PASIEN_KELUARGA
			, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
			, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
			, kl.keterangan KETERANGAN
			, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
			, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
			, master.getNamaLengkapPegawai(pere.NIP) PERAWAT_2

			, kl.id ID_FARMASI
			, kl.nama_obat NAMA_OBAT, NULL KATEGORI
			, CONCAT(IF(kl.dosis IS NULL, '', CONCAT(kl.dosis,' / '))
			, IF(kl.jam IS NULL, '', CONCAT(kl.jam, ' Jam'))
			, IF(kl.jalur_pemberian IS NULL, '', CONCAT(' / ',var.variabel))) ATURAN_PAKAI
			, kl.jumlah JUMLAH_OBAT_PERTAMA
			, kr.jumlah_obat SISA_OBAT, kd.jalur_pemberian_input JALUR_PEMBERIAN_ID
			, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
			, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
			, var.variabel JALUR_PEMBERIAN_VAR
			, kl.keterangan KETERANGANS
			, null WAKTU_PEMBERIAN_INPUT
			, kd.id ID_PEMBERIAN
			, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
			FROM db_layanan.tb_kardek_pemberian_luar kd

			LEFT JOIN db_layanan.tb_kardek_rawatinap_luar kr ON kr.id_kardek_obat_luar = kd.id_kardek_obat_luar
			LEFT JOIN db_layanan.tb_kardek_obat_luar kl ON kl.id = kr.id_kardek_obat_luar
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kl.nokun
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
			LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
			LEFT JOIN master.ruangan r ON r.ID = kl.ruangan
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = kl.oleh
			LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
			LEFT JOIN master.perawat pere ON pere.ID = kd.perawat_2

			WHERE kl.id='$idfarmasi'

			ORDER BY kd.tanggal_jam ASC");
		return $query->result_array();
	}

	public function detailKardekViewLuarResult($idfarmasi)
	{
		$query = $this->db->query("SELECT 2 JENISH, 'INI OBAT LUAR' JENISH_DESKRIPSI,pk.NOMOR NOKUN, p.NORM, p.NOMOR NOPEN, kl.tanggal TANGGAL_PEMBUATAN_RESEP
			, r.DESKRIPSI RUANGAN
			, kd.tanggal_jam TANGGAL_PEMBERIAN
			, kd.dosis
			, kd.pasien_keluarga PASIEN_KELUARGA
			, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
			, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
			, kl.keterangan KETERANGAN
			, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
			, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
			, master.getNamaLengkapPegawai(pere.NIP) PERAWAT_2

			, kl.id ID_FARMASI
			, kl.nama_obat NAMA_OBAT, NULL KATEGORI
			, CONCAT(IF(kl.dosis IS NULL, '', CONCAT(kl.dosis,' / '))
			, IF(kl.jam IS NULL, '', CONCAT(kl.jam, ' Jam'))
			, IF(kl.jalur_pemberian IS NULL, '', CONCAT(' / ',var.variabel))) ATURAN_PAKAI
			, kl.jumlah JUMLAH_OBAT_PERTAMA
			, kr.jumlah_obat SISA_OBAT, kd.jalur_pemberian_input JALUR_PEMBERIAN_ID
			, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
			, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
			, var.variabel JALUR_PEMBERIAN_VAR
			, kl.keterangan KETERANGANS
			, null WAKTU_PEMBERIAN_INPUT
			, kd.id ID_PEMBERIAN
			, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
			FROM db_layanan.tb_kardek_pemberian_luar kd

			LEFT JOIN db_layanan.tb_kardek_rawatinap_luar kr ON kr.id_kardek_obat_luar = kd.id_kardek_obat_luar
			LEFT JOIN db_layanan.tb_kardek_obat_luar kl ON kl.id = kr.id_kardek_obat_luar
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kl.nokun
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
			LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
			LEFT JOIN master.ruangan r ON r.ID = kl.ruangan
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = kl.oleh
			LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
			LEFT JOIN master.perawat pere ON pere.ID = kd.perawat_2

			WHERE kl.id='$idfarmasi'

			ORDER BY kd.tanggal_jam ASC");
		return $query->result_array();
	}

	public function detailKardekLuarView($id_kardek)
	{
		$query = $this->db->query("SELECT ko.id ID_FARMASI, ko.nokun NOKUN, ko.nama_obat NAMA_OBAT, ko.dosis DOSIS_PEMBERIAN, ko.jam JAM
			, ko.jalur_pemberian JALUR_PEMBERIAN, ko.jumlah JUMLAH, ko.keterangan KETERANGAN
			, rl.jumlah_obat SISA_OBAT, var.variabel JALUR_PEMBERIAN_VAR
			FROM db_layanan.tb_kardek_obat_luar ko
			LEFT JOIN db_layanan.tb_kardek_rawatinap_luar rl ON rl.id_kardek_obat_luar = ko.id
			LEFT JOIN db_master.variabel var ON var.id_variabel = ko.jalur_pemberian
			WHERE ko.id='$id_kardek'");
		return $query->row_array();
	}

	public function listObatPilih($nopen)
	{
		$query = $this->db->query("SELECT pk.NOMOR NOKUN, p.NOMOR, p.NORM
#, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP
, (SELECT orei.TANGGAL
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) TANGGAL_PEMBUATAN_RESEP

, (SELECT ruani.DESKRIPSI RUANGAN
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) RUANGAN

, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR

, (SELECT ordei.KETERANGAN KETERANGAN
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) KETERANGAN

, (SELECT master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) USER_RESEP

, (SELECT far.ID ID_FARMASI
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) ID_FARMASI

, (SELECT inventory.getNamaBarangGenerik(ibi.ID) NAMA
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) NAMA_OBAT

#, IF(kgen.DESKRIPSI IS NULL, jgen.DESKRIPSI, CONCAT(jgen.DESKRIPSI, ' - ',kgen.DESKRIPSI)) JENIS_GENERIK
, (SELECT kati.NAMA KATEGORI
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) KATEGORI

, (SELECT CONCAT(IF(ordei.DOSIS IS NULL,'-',ordei.DOSIS), '/', IF(ordei.JAM IS NULL,'-', CONCAT(ordei.JAM, ' Jam'))
,'/', IF(varii.variabel IS NULL,'-',varii.variabel)) ATURAN_PAKAI
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) ATURAN_PAKAI

, far.JUMLAH JUMLAH_OBAT_PERTAMA
, SUM(kd.jumlah_obat) SISA_OBAT
, (SELECT ordei.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) JALUR_PEMBERIAN_ID

, IF(SUM(kd.jumlah_obat)<=0,3,IF(kd.status=1,1,IF(kd.status=2,2,0))) STATUS_OBT
, IF(SUM(kd.jumlah_obat)<=0,'Obat Habis',IF(kd.status=1,'Aktif',IF(kd.status=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
, (SELECT varii.variabel
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
#AND ibi.GENERIK=ib.GENERIK AND ibi.KATEGORI_GENERIK=ib.KATEGORI_GENERIK
ORDER BY orei.TANGGAL DESC LIMIT 1) JALUR_PEMBERIAN_VAR
, var.variabel JALUR_PEMBERIAN_VAR
, orde.KETERANGAN KETERANGAN
, br.BARANG BARANG_BR
, br.ID ID_BR

, (SELECT ordei.DOSIS
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) DOSIS_TERAKHIR

, (SELECT ordei.JAM
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) JAM_TERAKHIR

, (SELECT ordei.JALUR_PEMBERIAN
FROM db_layanan.tb_kardek_rawatinap kdi
LEFT JOIN layanan.farmasi fari ON fari.ID = kdi.id_farmasi
LEFT JOIN pendaftaran.kunjungan pki ON pki.NOMOR = fari.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pi ON pi.NOMOR = pki.NOPEN
LEFT JOIN master.pasien pasi ON pasi.NORM = pi.NORM
LEFT JOIN master.ruangan ri ON ri.ID = pki.RUANGAN
LEFT JOIN layanan.order_detil_resep ordei ON ordei.REF = fari.ID
LEFT JOIN layanan.order_resep orei ON orei.NOMOR = ordei.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pkui ON pkui.NOMOR = orei.KUNJUNGAN
LEFT JOIN master.ruangan ruani ON ruani.ID = pkui.RUANGAN
LEFT JOIN aplikasi.pengguna pengi ON pengi.ID = orei.OLEH
LEFT JOIN master.dokter dokii ON dokii.ID = orei.OLEH
LEFT JOIN inventory.barang ibi ON ibi.ID = fari.FARMASI
LEFT JOIN inventory.barang_ruangan brii ON brii.ID = fari.STOK
LEFT JOIN master.referensi apaki ON apaki.ID = fari.ATURAN_PAKAI AND apaki.JENIS=41
LEFT JOIN inventory.kategori kati ON kati.ID = ibi.KATEGORI
LEFT JOIN db_master.variabel varii ON varii.id_variabel = kdi.jalur_pemberian
LEFT JOIN master.referensi jgeni ON jgeni.ID = ibi.GENERIK AND jgeni.JENIS=42
LEFT JOIN master.referensi kgeni ON kgeni.ID = ibi.KATEGORI_GENERIK AND kgeni.JENIS=105
WHERE pi.NOMOR=p.NOMOR AND
ordei.KARDEX=1 AND fari.RACIKAN=0
AND kdi.id_farmasi=kd.id_farmasi
ORDER BY orei.TANGGAL DESC LIMIT 1) JALUR_PEMBERIAN_TERAKHIR

FROM db_layanan.tb_kardek_rawatinap kd

LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
LEFT JOIN aplikasi.pengguna peng ON peng.ID = ore.OLEH
LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
LEFT JOIN db_master.variabel var ON var.id_variabel = kd.jalur_pemberian

WHERE p.NOMOR='$nopen' AND orde.KARDEX=1

GROUP BY far.FARMASI

ORDER BY NAMA_OBAT ASC
");
		return $query->result_array();
	}

	// public function detailKardekViewPemberian($nopen, $idfarmasi)
	// {
	// 	$query = $this->db->query("SELECT pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP, ruan.DESKRIPSI RUANGAN
	// 		, kd.tanggal_jam TANGGAL_PEMBERIAN
	// 		, kd.dosis DOSIS_PEMBERIAN
	// 		, kd.pasien_keluarga PASIEN_KELUARGA
	// 		, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
	// 		, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
	// 		, orde.KETERANGAN KETERANGAN, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
	// 		, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
	// 		, master.getNamaLengkapPegawai(per.NIP) PERAWAT_2

	// 		, far.ID ID_FARMASI
	// 		, ib.NAMA NAMA_OBAT, kat.NAMA KATEGORI, apak.DESKRIPSI ATURAN_PAKAI, far.JUMLAH JUMLAH_OBAT_PERTAMA
	// 		, kr.jumlah_obat SISA_OBAT, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
	// 		, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
	// 		, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
	// 		, var.variabel JALUR_PEMBERIAN_VAR
	// 		, orde.KETERANGAN KETERANGAN
	// 		, IF(kd.waktu_pemberian_input=1,'Pagi',IF(kd.waktu_pemberian_input=2,'Siang',IF(kd.waktu_pemberian_input=3,'Sore',IF(kd.waktu_pemberian_input=4,'Malam','OK')))) WAKTU_PEMBERIAN_INPUT
	// 		, kd.id ID_PEMBERIAN
	// 		, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN

	// 		FROM db_layanan.tb_kardek_pemberian_rawatinap kd

	// 		LEFT JOIN db_layanan.tb_kardek_rawatinap kr ON kr.id_farmasi = kd.id_farmasi
	// 		LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
	// 		LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
	// 		LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
	// 		LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
	// 		LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
	// 		LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
	// 		LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
	// 		LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
	// 		LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
	// 		LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
	// 		LEFT JOIN aplikasi.pengguna peng ON peng.ID = kd.oleh
	// 		LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
	// 		LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
	// 		LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
	// 		LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
	// 		LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
	// 		LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian

	// 		WHERE p.NOMOR='$nopen' AND orde.KARDEX=1 AND far.ID='$idfarmasi'
	// 		ORDER BY kd.tanggal_jam ASC");
	// 	return $query->result_array();
	// }

	public function detailKardekViewPemberian($nopen, $idgenerik, $katgenerik)
	{
		$query = $this->db->query("SELECT pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP, ruan.DESKRIPSI RUANGAN
			, kd.tanggal_jam TANGGAL_PEMBERIAN
			, kd.dosis DOSIS_PEMBERIAN
			, kd.pasien_keluarga PASIEN_KELUARGA
			, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
			, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
			, orde.KETERANGAN KETERANGAN, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
			, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
			, master.getNamaLengkapPegawai(per.NIP) PERAWAT_2

			, far.ID ID_FARMASI
			, ib.NAMA NAMA_OBAT, kat.NAMA KATEGORI, apak.DESKRIPSI ATURAN_PAKAI, far.JUMLAH JUMLAH_OBAT_PERTAMA
			, kr.jumlah_obat SISA_OBAT, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
			, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
			, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
			, var.variabel JALUR_PEMBERIAN_VAR
			, orde.KETERANGAN KETERANGAN
			, IF(kd.waktu_pemberian_input=1,'Pagi',IF(kd.waktu_pemberian_input=2,'Siang',IF(kd.waktu_pemberian_input=3,'Sore',IF(kd.waktu_pemberian_input=4,'Malam','OK')))) WAKTU_PEMBERIAN_INPUT
			, kd.id ID_PEMBERIAN
			, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN

			FROM db_layanan.tb_kardek_pemberian_rawatinap kd
			
			LEFT JOIN db_layanan.tb_kardek_rawatinap kr ON kr.id_farmasi = kd.id_farmasi
			LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
			LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
			LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
			LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
			LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
			LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
			LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = kd.oleh
			LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
			LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
			LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
			LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
			LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
			LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
			LEFT JOIN master.referensi jgen ON jgen.ID = ib.GENERIK AND jgen.JENIS=42
			LEFT JOIN master.referensi kgen ON kgen.ID = ib.KATEGORI_GENERIK AND kgen.JENIS=105

			WHERE p.NOMOR='$nopen' AND orde.KARDEX=1 AND far.RACIKAN=0
			AND jgen.ID='$idgenerik' AND kgen.ID='$katgenerik'
			ORDER BY kd.tanggal_jam ASC");
		return $query->result_array();
	}

	// 	public function detailHistoryKardekViewPemberian($nopen)
	// 	{
	// 		$query = $this->db->query("SELECT * FROM(
	// (SELECT 1 JENISH, 'INI RACIKAN DAN NON RACIK' JENISH_DESKRIPSI, pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP, ruan.DESKRIPSI RUANGAN
	// , kd.tanggal_jam TANGGAL_PEMBERIAN
	// , kd.dosis DOSIS_PEMBERIAN
	// , kd.pasien_keluarga PASIEN_KELUARGA
	// , CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
	// , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
	// , orde.KETERANGAN KETERANGAN, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
	// , master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
	// , master.getNamaLengkapPegawai(per.NIP) PERAWAT_2

	// , far.ID ID_FARMASI
	// , ib.NAMA NAMA_OBAT, kat.NAMA KATEGORI, apak.DESKRIPSI ATURAN_PAKAI, far.JUMLAH JUMLAH_OBAT_PERTAMA
	// , kr.jumlah_obat SISA_OBAT, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
	// , IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
	// , IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
	// , var.variabel JALUR_PEMBERIAN_VAR
	// , orde.KETERANGAN KETERANGANS
	// , IF(kd.waktu_pemberian_input=1,'Pagi',IF(kd.waktu_pemberian_input=2,'Siang',IF(kd.waktu_pemberian_input=3,'Sore',IF(kd.waktu_pemberian_input=4,'Malam','OK')))) WAKTU_PEMBERIAN_INPUT
	// , kd.id ID_PEMBERIAN
	// , kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN

	// FROM db_layanan.tb_kardek_pemberian_rawatinap kd

	// LEFT JOIN db_layanan.tb_kardek_rawatinap kr ON kr.id_farmasi = kd.id_farmasi
	// LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
	// LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
	// LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
	// LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
	// LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
	// LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
	// LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
	// LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
	// LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
	// LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
	// LEFT JOIN aplikasi.pengguna peng ON peng.ID = kd.oleh
	// LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
	// LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
	// LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
	// LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
	// LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
	// LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian

	// WHERE p.NOMOR='$nopen' AND orde.KARDEX=1) #AND far.ID='')

	// UNION ALL

	// (SELECT 2 JENISH, 'INI OBAT LUAR' JENISH_DESKRIPSI,pk.NOMOR NOKUN, p.NORM, kl.tanggal TANGGAL_PEMBUATAN_RESEP
	// , r.DESKRIPSI RUANGAN
	// , kd.tanggal_jam TANGGAL_PEMBERIAN
	// , kd.dosis
	// , kd.pasien_keluarga PASIEN_KELUARGA
	// , CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
	// , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
	// , kl.keterangan KETERANGAN
	// , master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
	// , master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
	// , master.getNamaLengkapPegawai(pere.NIP) PERAWAT_2

	// , kl.id ID_FARMASI
	// , kl.nama_obat NAMA_OBAT, NULL KATEGORI
	// , CONCAT(IF(kl.dosis IS NULL, '', CONCAT(kl.dosis,' / '))
	// , IF(kl.jam IS NULL, '', CONCAT(kl.jam, ' Jam'))
	// , IF(kl.jalur_pemberian IS NULL, '', CONCAT(' / ',var.variabel))) ATURAN_PAKAI
	// , kl.jumlah JUMLAH_OBAT_PERTAMA
	// , kr.jumlah_obat SISA_OBAT, kd.jalur_pemberian_input JALUR_PEMBERIAN_ID
	// , IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
	// , IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
	// , var.variabel JALUR_PEMBERIAN_VAR
	// , kl.keterangan KETERANGANS
	// , null WAKTU_PEMBERIAN_INPUT
	// , kd.id ID_PEMBERIAN
	// , kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
	// FROM db_layanan.tb_kardek_pemberian_luar kd

	// LEFT JOIN db_layanan.tb_kardek_rawatinap_luar kr ON kr.id_kardek_obat_luar = kd.id_kardek_obat_luar
	// LEFT JOIN db_layanan.tb_kardek_obat_luar kl ON kl.id = kr.id_kardek_obat_luar
	// LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kl.nokun
	// LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
	// LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
	// LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
	// LEFT JOIN master.ruangan r ON r.ID = kl.ruangan
	// LEFT JOIN aplikasi.pengguna peng ON peng.ID = kd.oleh
	// LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
	// LEFT JOIN master.perawat pere ON pere.ID = kd.perawat_2

	// WHERE p.NOMOR='$nopen')) a #AND kd.id_kardek_obat_luar=7

	// ORDER BY a.TANGGAL_PEMBERIAN ASC");
	// 		return $query->result_array();
	// 	}

	public function detailHistoryKardekViewPemberian($nopen)
	{
		$query = $this->db->query("SELECT * FROM(
(SELECT 1 JENISH, 'INI NON RACIKAN' JENISH_DESKRIPSI, pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP, ruan.DESKRIPSI RUANGAN
, kd.tanggal_jam TANGGAL_PEMBERIAN
, kd.dosis DOSIS_PEMBERIAN
, kd.pasien_keluarga PASIEN_KELUARGA
, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
, orde.KETERANGAN KETERANGAN, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
, master.getNamaLengkapPegawai(per.NIP) PERAWAT_2

, far.ID ID_FARMASI
, ib.NAMA NAMA_OBAT
#, IF(ib.NAMA_DASAR IS NULL,far.ID,ib.NAMA_DASAR) NAMA_DASASR

, IF((jgen.ID IS NULL OR jgen.ID=0 OR kgen.ID IS NULL), far.ID, CONCAT(jgen.DESKRIPSI, ' - ',kgen.DESKRIPSI)) NAMA_DASAR

, kat.NAMA KATEGORI, apak.DESKRIPSI ATURAN_PAKAI, far.JUMLAH JUMLAH_OBAT_PERTAMA
, kr.jumlah_obat SISA_OBAT, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
, var.variabel JALUR_PEMBERIAN_VAR
, orde.KETERANGAN KETERANGANS
, IF(kd.waktu_pemberian_input=1,'Pagi',IF(kd.waktu_pemberian_input=2,'Siang',IF(kd.waktu_pemberian_input=3,'Sore',IF(kd.waktu_pemberian_input=4,'Malam','OK')))) WAKTU_PEMBERIAN_INPUT
, kd.id ID_PEMBERIAN
, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
, jgen.ID ID_GENERIK, jgen.DESKRIPSI NAMA_GENERIK
, kgen.ID ID_KATEGORI_GENERIK, kgen.DESKRIPSI KATEGORI_GENERIK
, p.NOMOR NOPEN
, IF((jgen.ID IS NULL OR jgen.ID=0), far.ID, jgen.ID) PARAM_GENERIK
, IF(kgen.ID IS NOT NULL, kgen.ID, 0) PARAM_KAT_GENERIK

FROM db_layanan.tb_kardek_pemberian_rawatinap kd

LEFT JOIN db_layanan.tb_kardek_rawatinap kr ON kr.id_farmasi = kd.id_farmasi
LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
LEFT JOIN aplikasi.pengguna peng ON peng.ID = kd.oleh
LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
LEFT JOIN master.referensi jgen ON jgen.ID = ib.GENERIK AND jgen.JENIS=42
LEFT JOIN master.referensi kgen ON kgen.ID = ib.KATEGORI_GENERIK AND kgen.JENIS=105

WHERE p.NOMOR='$nopen' AND orde.KARDEX=1 AND far.RACIKAN=0
#jgen.ID=16 AND kgen.ID=1
) #AND far.ID='')

UNION ALL

(SELECT 2 JENISH, 'INI RACIKAN' JENISH_DESKRIPSI, pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP, ruan.DESKRIPSI RUANGAN
, kd.tanggal_jam TANGGAL_PEMBERIAN
, kd.dosis DOSIS_PEMBERIAN
, kd.pasien_keluarga PASIEN_KELUARGA
, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
, orde.KETERANGAN KETERANGAN, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
, master.getNamaLengkapPegawai(per.NIP) PERAWAT_2

, far.ID ID_FARMASI
, ib.NAMA NAMA_OBAT, ib.NAMA_DASAR
, kat.NAMA KATEGORI, apak.DESKRIPSI ATURAN_PAKAI, far.JUMLAH JUMLAH_OBAT_PERTAMA
, kr.jumlah_obat SISA_OBAT, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
, var.variabel JALUR_PEMBERIAN_VAR
, orde.KETERANGAN KETERANGANS
, IF(kd.waktu_pemberian_input=1,'Pagi',IF(kd.waktu_pemberian_input=2,'Siang',IF(kd.waktu_pemberian_input=3,'Sore',IF(kd.waktu_pemberian_input=4,'Malam','OK')))) WAKTU_PEMBERIAN_INPUT
, kd.id ID_PEMBERIAN
, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
, jgen.ID ID_GENERIK, jgen.DESKRIPSI NAMA_GENERIK
, kgen.ID ID_KATEGORI_GENERIK, kgen.DESKRIPSI KATEGORI_GENERIK
, p.NOMOR NOPEN
, IF((jgen.ID IS NULL OR jgen.ID=0), far.ID, jgen.ID) PARAM_GENERIK
, IF(kgen.ID IS NOT NULL, kgen.ID, 0) PARAM_KAT_GENERIK

FROM db_layanan.tb_kardek_pemberian_rawatinap kd

LEFT JOIN db_layanan.tb_kardek_rawatinap kr ON kr.id_farmasi = kd.id_farmasi
LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
LEFT JOIN aplikasi.pengguna peng ON peng.ID = kd.oleh
LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
LEFT JOIN master.referensi jgen ON jgen.ID = ib.GENERIK AND jgen.JENIS=42
LEFT JOIN master.referensi kgen ON kgen.ID = ib.KATEGORI_GENERIK AND kgen.JENIS=105

WHERE p.NOMOR='$nopen' AND orde.KARDEX=1 AND far.RACIKAN!=0) #AND far.ID='')

UNION ALL

(SELECT 3 JENISH, 'INI OBAT LUAR' JENISH_DESKRIPSI,pk.NOMOR NOKUN, p.NORM, kl.tanggal TANGGAL_PEMBUATAN_RESEP
, r.DESKRIPSI RUANGAN
, kd.tanggal_jam TANGGAL_PEMBERIAN
, kd.dosis
, kd.pasien_keluarga PASIEN_KELUARGA
, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
, kl.keterangan KETERANGAN
, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
, master.getNamaLengkapPegawai(pere.NIP) PERAWAT_2

, kl.id ID_FARMASI
, kl.nama_obat NAMA_OBAT, NULL NAMA_DASAR
, NULL KATEGORI
, CONCAT(IF(kl.dosis IS NULL, '', CONCAT(kl.dosis,' / '))
, IF(kl.jam IS NULL, '', CONCAT(kl.jam, ' Jam'))
, IF(kl.jalur_pemberian IS NULL, '', CONCAT(' / ',var.variabel))) ATURAN_PAKAI
, kl.jumlah JUMLAH_OBAT_PERTAMA
, kr.jumlah_obat SISA_OBAT, kd.jalur_pemberian_input JALUR_PEMBERIAN_ID
, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
, var.variabel JALUR_PEMBERIAN_VAR
, kl.keterangan KETERANGANS
, null WAKTU_PEMBERIAN_INPUT
, kd.id ID_PEMBERIAN
, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
, NULL ID_GENERIK
, NULL NAMA_GENERIK
, NULL ID_KATEGORI_GENERIK
, NULL KATEGORI_GENERIK
, p.NOMOR NOPEN
, NULL PARAM_GENERIK
, NULL PARAM_KAT_GENERIK
FROM db_layanan.tb_kardek_pemberian_luar kd

LEFT JOIN db_layanan.tb_kardek_rawatinap_luar kr ON kr.id_kardek_obat_luar = kd.id_kardek_obat_luar
LEFT JOIN db_layanan.tb_kardek_obat_luar kl ON kl.id = kr.id_kardek_obat_luar
LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kl.nokun
LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
LEFT JOIN master.ruangan r ON r.ID = kl.ruangan
LEFT JOIN aplikasi.pengguna peng ON peng.ID = kd.oleh
LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian
LEFT JOIN master.perawat pere ON pere.ID = kd.perawat_2

WHERE p.NOMOR='$nopen')) a #AND kd.id_kardek_obat_luar=7

ORDER BY a.TANGGAL_PEMBERIAN ASC");
		return $query->result_array();
	}

	public function detailIsiKardekViewPemberian($idpemberian)
	{
		$query = $this->db->query("SELECT pk.NOMOR NOKUN, p.NORM, ore.TANGGAL TANGGAL_PEMBUATAN_RESEP, ruan.DESKRIPSI RUANGAN
			, CONCAT(DATE_FORMAT(DATE(pas.TANGGAL_LAHIR),'%d-%m-%Y'), ' / '
			, master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR),' Tahun') TANGGAL_LAHIR
			, orde.KETERANGAN KETERANGAN, master.getNamaLengkapPegawai(peng.NIP) USER_RESEP
			, master.getNamaLengkapPegawai(peng.NIP) PERAWAT_1
			, master.getNamaLengkapPegawai(per.NIP) PERAWAT_2

			, far.ID ID_FARMASI
			, kd.id ID_PEMBERIAN
			, ib.NAMA NAMA_OBAT, kat.NAMA KATEGORI, apak.DESKRIPSI ATURAN_PAKAI, far.JUMLAH JUMLAH_OBAT_PERTAMA
			, kr.jumlah_obat SISA_OBAT, orde.JALUR_PEMBERIAN JALUR_PEMBERIAN_ID
			, IF(kr.jumlah_obat<=0,3,IF(kr.`status`=1,1,IF(kr.`status`=2,2,0))) STATUS_OBT
			, IF(kr.jumlah_obat<=0,'Obat Habis',IF(kr.`status`=1,'Aktif',IF(kr.`status`=2,'Obat Distop','Dibatalkan'))) STATUS_OBAT
			, var.variabel JALUR_PEMBERIAN_VAR
			, orde.KETERANGAN KETERANGAN
			, kd.jumlah_pemberian JUMLAH_OBAT_BERIKAN
			, kd.dosis DOSIS
			, kd.pasien_keluarga PASIEN_KELUARGA
			, IF(kd.waktu_pemberian_input=1,'Pagi',IF(kd.waktu_pemberian_input=2,'Siang',IF(kd.waktu_pemberian_input=3,'Sore',IF(kd.waktu_pemberian_input=4,'Malam','OK')))) WAKTU_PEMBERIAN_INPUT
			

			FROM db_layanan.tb_kardek_pemberian_rawatinap kd
			
			LEFT JOIN db_layanan.tb_kardek_rawatinap kr ON kr.id_farmasi = kd.id_farmasi
			LEFT JOIN layanan.farmasi far ON far.ID = kd.id_farmasi
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = far.KUNJUNGAN
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
			LEFT JOIN master.perawat per ON per.ID = kd.perawat_2
			LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
			LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
			LEFT JOIN layanan.order_resep ore ON ore.NOMOR = orde.ORDER_ID
			LEFT JOIN pendaftaran.kunjungan pku ON pku.NOMOR = ore.KUNJUNGAN
			LEFT JOIN master.ruangan ruan ON ruan.ID = pku.RUANGAN
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = ore.OLEH
			LEFT JOIN master.dokter doki ON doki.ID = ore.OLEH
			LEFT JOIN inventory.barang ib ON ib.ID = far.FARMASI
			LEFT JOIN inventory.barang_ruangan br ON br.ID = far.STOK
			LEFT JOIN master.referensi apak ON apak.ID = far.ATURAN_PAKAI AND apak.JENIS=41
			LEFT JOIN inventory.kategori kat ON kat.ID = ib.KATEGORI
			LEFT JOIN db_master.variabel var ON var.id_variabel = kr.jalur_pemberian

			WHERE kd.id='$idpemberian' AND orde.KARDEX=1");
		return $query->row_array();
	}
}

/* End of file ProfileModel.php */
/* Location: ./application/models/ProfileModel.php */
