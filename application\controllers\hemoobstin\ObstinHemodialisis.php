<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ObstinHemodialisis extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }
  public function simpanObstinKeperawatanHemodialisis()
  {
    $kunjungan = $this->input->post("nokun");
    $pengguna = $this->input->post("pengguna");
    $diagmed = $this->input->post("diagmed");
    $dokter_pengirim = $this->input->post("dokter_pengirim");
    $status_rawat = $this->input->post("status_rawat");
    $jenis_rawat = $this->input->post("jenis_rawat");
    if ($jenis_rawat == '1') {
      $ruang_rawat = $this->input->post("ruang_rawat1");
    } elseif ($jenis_rawat == '2') {
      $ruang_rawat = $this->input->post("ruang_rawat2");
    }
    $hd_ke = $this->input->post("hd_ke");
    $mulai_hd = $this->input->post("mulai_hd");
    $selesai_hd = $this->input->post("selesai_hd");
    $dialiser = $this->input->post("dialiser");
    $priming = $this->input->post("priming");
    $dialisat = $this->input->post("dialisat");
    $vaskular = $this->input->post("vaskular");
    $heparinawal = $this->input->post("heparinawal");
    $heparinlanjutan = $this->input->post("heparinlanjutan");
    $kesadaran_pre_hd = $this->input->post("kesadaran_pre_hd");
    $keluhan_pre_hd = $this->input->post("keluhan_pre_hd");
    $td_pre_hd_sistolik = $this->input->post("td_pre_hd_sistolik");
    $td_pre_hd_diastolik = $this->input->post("td_pre_hd_diastolik");
    $n_pre_hd = $this->input->post("n_pre_hd");
    $p_pre_hd = $this->input->post("p_pre_hd");
    $s_pre_hd = $this->input->post("s_pre_hd");
    $bb_pre_hd_1 = $this->input->post("bb_pre_hd_1");
    $tb_pre_hd_2 = $this->input->post("tb_pre_hd_2");
    $bbkering_pre_hd = $this->input->post("bbkering_pre_hd");
    $bbhd_pre_hd = $this->input->post("bbhd_pre_hd");
    $kenaikan_pre_hd = $this->input->post("kenaikan_pre_hd");
    $kesadaran_post_hd = $this->input->post("kesadaran_post_hd");
    $keluhan_post_hd = $this->input->post("keluhan_post_hd");
    $td_post_hd_sistolik = $this->input->post("td_post_hd_sistolik");
    $td_post_hd_diastolik = $this->input->post("td_post_hd_diastolik");
    $n_post_hd = $this->input->post("n_post_hd");
    $p_post_hd = $this->input->post("p_post_hd");
    $s_post_hd = $this->input->post("s_post_hd");
    $bb_post_hd_1 = $this->input->post("bb_post_hd_1");
    $tb_post_hd_2 = $this->input->post("tb_post_hd_2");
    $bbkering_post_hd = $this->input->post("bbkering_post_hd");
    $bbhd_post_hd = $this->input->post("bbhd_post_hd");
    $penurunan_post_hd = $this->input->post("penurunan_post_hd");
    $oral = $this->input->post("oral");
    $iv = $this->input->post("iv");
    $intake_priming = $this->input->post("intake_priming");
    $washout = $this->input->post("washout");
    $transfusi = $this->input->post("transfusi");
    $dll = $this->input->post("dll");
    $intake_total = $this->input->post("intake_total");
    $uf = $this->input->post("uf");
    $urine = $this->input->post("urine");
    $muntah = $this->input->post("muntah");
    $perdarahan = $this->input->post("perdarahan");
    $total_output = $this->input->post("total_output");

    $data = array(
      'nokun' => $kunjungan,
      'diagmed' => $diagmed,
      'dokter_pengirim' => $dokter_pengirim,
      'status_rawat' => $status_rawat,
      'jenis_rawat' => $jenis_rawat,
      'ruang_rawat' => $ruang_rawat,
      'hd_ke' => $hd_ke,
      'mulai_hd' => $mulai_hd,
      'selesai_hd' => $selesai_hd,
      'dialiser' => $dialiser,
      'priming' => $priming,
      'dialisat' => $dialisat,
      'vaskular' => $vaskular,
      'heparinawal' => $heparinawal,
      'heparinlanjutan' => $heparinlanjutan,
      'kesadaran_pre_hd' => $kesadaran_pre_hd,
      'keluhan_pre_hd' => $keluhan_pre_hd,
      'td_pre_sis' => $td_pre_hd_sistolik,
      'td_pre_dis' => $td_pre_hd_diastolik,
      'n_pre_hd' => $n_pre_hd,
      'p_pre_hd' => $p_pre_hd,
      's_pre_hd' => $s_pre_hd,
      'bb_pre_hd_1' => $bb_pre_hd_1,
      'tb_pre_hd_2' => $tb_pre_hd_2,
      'bbkering_pre_hd' => $bbkering_pre_hd,
      'bbhd_pre_hd' => $bbhd_pre_hd,
      'kenaikan_pre_hd' => $kenaikan_pre_hd,
      'kesadaran_post_hd' => $kesadaran_post_hd,
      'keluhan_post_hd' => $keluhan_post_hd,
      'td_post_sis' => $td_post_hd_sistolik,
      'td_post_dis' => $td_post_hd_diastolik,
      'n_post_hd' => $n_post_hd,
      'p_post_hd' => $p_post_hd,
      's_post_hd' => $s_post_hd,
      'bb_post_hd_1' => $bb_post_hd_1,
      'tb_post_hd_2' => $tb_post_hd_2,
      'bbkering_post_hd' => $bbkering_post_hd,
      'bbhd_post_hd' => $bbhd_post_hd,
      'penurunan_post_hd' => $penurunan_post_hd,
      'oral' => $oral,
      'iv' => $iv,
      'intake_priming' => $intake_priming,
      'washout' => $washout,
      'transfusi' => $transfusi,
      'dll' => $dll,
      'intake_total' => $intake_total,
      'uf' => $uf,
      'urine' => $urine,
      'muntah' => $muntah,
      'perdarahan' => $perdarahan,
      'total_output' => $total_output,
      'oleh' => $pengguna,
    );
    
    // echo "<pre>";print_r($data);exit();
    $this->db->trans_begin();
    $this->db->insert('keperawatan.tb_observasi_hemodialisis', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function lihatHistoryObstinKeperawatanHemodialisis()
  {
    $id = $this->input->post('id');
    $nokun = $this->input->post('nokun');
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $listDr = $this->masterModel->listDr();
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
    $hemodialisa = $this->pengkajianAwalModel->historyDetailObstinKeperawatanHemodialisa($id);

    $dataEdit = array(
      'getNomr' => $getNomr,
      'listDr' => $listDr,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
      'hemodialisa' => $hemodialisa,
    );

    $this->load->view('Pengkajian/hemodialisa/obstinKeperawatanHemodialisis/modalViewEditObstinKeperawatanHemodialisis', $dataEdit);
  }

  public function ubahObstinKeperawatanHemodialisis()
  {
    $id_hemodialisa = $this->input->post("id_hemodialisa");
    $diagmed = $this->input->post("diagmed_edit");
    $dokter_pengirim = $this->input->post("dokter_pengirim_edit");
    $status_rawat = $this->input->post("status_rawat_edit");
    $jenis_rawat = $this->input->post("jenis_rawat_edit");
    if ($jenis_rawat == '1') {
      $ruang_rawat = $this->input->post("ruang_rawat1_edit");
    } elseif ($jenis_rawat == '2') {
      $ruang_rawat = $this->input->post("ruang_rawat2_edit");
    }
    $hd_ke = $this->input->post("hd_ke_edit");
    $mulai_hd = $this->input->post("mulai_hd_edit");
    $selesai_hd = $this->input->post("selesai_hd_edit");
    $dialiser = $this->input->post("dialiser_edit");
    $priming = $this->input->post("priming_edit");
    $dialisat = $this->input->post("dialisat_edit");
    $vaskular = $this->input->post("vaskular_edit");
    $heparinawal = $this->input->post("heparinawal_edit");
    $heparinlanjutan = $this->input->post("heparinlanjutan_edit");
    $kesadaran_pre_hd = $this->input->post("kesadaran_pre_hd_edit");
    $keluhan_pre_hd = $this->input->post("keluhan_pre_hd_edit");
    $td_pre_hd_sistolik = $this->input->post("td_pre_hd_sistolik_edit");
    $td_pre_hd_diastolik = $this->input->post("td_pre_hd_diastolik_edit");
    $n_pre_hd = $this->input->post("n_pre_hd_edit");
    $p_pre_hd = $this->input->post("p_pre_hd_edit");
    $s_pre_hd = $this->input->post("s_pre_hd_edit");
    $bb_pre_hd_1 = $this->input->post("bb_pre_hd_1_edit");
    $tb_pre_hd_2 = $this->input->post("tb_pre_hd_2_edit");
    $bbkering_pre_hd = $this->input->post("bbkering_pre_hd_edit");
    $bbhd_pre_hd = $this->input->post("bbhd_pre_hd_edit");
    $kenaikan_pre_hd = $this->input->post("kenaikan_pre_hd_edit");
    $kesadaran_post_hd = $this->input->post("kesadaran_post_hd_edit");
    $keluhan_post_hd = $this->input->post("keluhan_post_hd_edit");
    $td_post_hd_sistolik = $this->input->post("td_post_hd_sistolik_edit");
    $td_post_hd_diastolik = $this->input->post("td_post_hd_diastolik_edit");
    $n_post_hd = $this->input->post("n_post_hd_edit");
    $p_post_hd = $this->input->post("p_post_hd_edit");
    $s_post_hd = $this->input->post("s_post_hd_edit");
    $bb_post_hd_1 = $this->input->post("bb_post_hd_1_edit");
    $tb_post_hd_2 = $this->input->post("tb_post_hd_2_edit");
    $bbkering_post_hd = $this->input->post("bbkering_post_hd_edit");
    $bbhd_post_hd = $this->input->post("bbhd_post_hd_edit");
    $penurunan_post_hd = $this->input->post("penurunan_post_hd_edit");
    $oral = $this->input->post("oral_edit");
    $iv = $this->input->post("iv_edit");
    $intake_priming = $this->input->post("intake_priming_edit");
    $washout = $this->input->post("washout_edit");
    $transfusi = $this->input->post("transfusi_edit");
    $dll = $this->input->post("dll_edit");
    $intake_total = $this->input->post("intake_total_edit");
    $uf = $this->input->post("uf_edit");
    $urine = $this->input->post("urine_edit");
    $muntah = $this->input->post("muntah_edit");
    $perdarahan = $this->input->post("perdarahan_edit");
    $total_output = $this->input->post("total_output_edit");

    $dataUbah = array(
      'diagmed' => $diagmed,
      'dokter_pengirim' => $dokter_pengirim,
      'status_rawat' => $status_rawat,
      'jenis_rawat' => $jenis_rawat,
      'ruang_rawat' => $ruang_rawat,
      'hd_ke' => $hd_ke,
      'mulai_hd' => $mulai_hd,
      'selesai_hd' => $selesai_hd,
      'dialiser' => $dialiser,
      'priming' => $priming,
      'dialisat' => $dialisat,
      'vaskular' => $vaskular,
      'heparinawal' => $heparinawal,
      'heparinlanjutan' => $heparinlanjutan,
      'kesadaran_pre_hd' => $kesadaran_pre_hd,
      'keluhan_pre_hd' => $keluhan_pre_hd,
      'td_pre_sis' => $td_pre_hd_sistolik,
      'td_pre_dis' => $td_pre_hd_diastolik,
      'n_pre_hd' => $n_pre_hd,
      'p_pre_hd' => $p_pre_hd,
      's_pre_hd' => $s_pre_hd,
      'bb_pre_hd_1' => $bb_pre_hd_1,
      'tb_pre_hd_2' => $tb_pre_hd_2,
      'bbkering_pre_hd' => $bbkering_pre_hd,
      'bbhd_pre_hd' => $bbhd_pre_hd,
      'kenaikan_pre_hd' => $kenaikan_pre_hd,
      'kesadaran_post_hd' => $kesadaran_post_hd,
      'keluhan_post_hd' => $keluhan_post_hd,
      'td_post_sis' => $td_post_hd_sistolik,
      'td_post_dis' => $td_post_hd_diastolik,
      'n_post_hd' => $n_post_hd,
      'p_post_hd' => $p_post_hd,
      's_post_hd' => $s_post_hd,
      'bb_post_hd_1' => $bb_post_hd_1,
      'tb_post_hd_2' => $tb_post_hd_2,
      'bbkering_post_hd' => $bbkering_post_hd,
      'bbhd_post_hd' => $bbhd_post_hd,
      'penurunan_post_hd' => $penurunan_post_hd,
      'oral' => $oral,
      'iv' => $iv,
      'intake_priming' => $intake_priming,
      'washout' => $washout,
      'transfusi' => $transfusi,
      'dll' => $dll,
      'intake_total' => $intake_total,
      'uf' => $uf,
      'urine' => $urine,
      'muntah' => $muntah,
      'perdarahan' => $perdarahan,
      'total_output' => $total_output,
    );
    $this->db->trans_begin();

    $this->db->where('keperawatan.tb_observasi_hemodialisis.id', $id_hemodialisa);
    $this->db->update('keperawatan.tb_observasi_hemodialisis', $dataUbah);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    
  }

  public function viewObservasiHemoObstin()
  {
    $nokun = $this->input->post('nokun');
    $nomr = $this->input->post('nomr');
    $idhemo = $this->input->post('idhemo');
    $historyObservasiObstinHemo = $this->pengkajianAwalModel->historyObservasiObstinHemo($idhemo);
    $hisTvCObservasiObstinHemo = $this->pengkajianAwalModel->hisTvCObservasiObstinHemo($idhemo);

    $waktu = array();
    $napas = array();
    $nadi = array();
    $sistolik = array();
    $diastolik = array();
    $suhu = array();

    $ufg = array();
    $ufr = array();
    $uf = array();
    $qb = array();
    $qd = array();
    $vp = array();
    $tmp = array();
    foreach ($hisTvCObservasiObstinHemo as $hisTvCObservasiObstinHemo) {
      array_push($waktu, $hisTvCObservasiObstinHemo['JAM']);
      array_push($napas, $hisTvCObservasiObstinHemo['pernapasan']);
      array_push($nadi, $hisTvCObservasiObstinHemo['nadi']);
      array_push($sistolik, $hisTvCObservasiObstinHemo['td_sistolik']);
      array_push($diastolik, $hisTvCObservasiObstinHemo['td_diastolik']);
      array_push($suhu, $hisTvCObservasiObstinHemo['suhu']);

      array_push($ufg, $hisTvCObservasiObstinHemo['ufg']);
      array_push($ufr, $hisTvCObservasiObstinHemo['ufr']);
      array_push($uf, $hisTvCObservasiObstinHemo['uf']);
      array_push($qb, $hisTvCObservasiObstinHemo['qb']);
      array_push($qd, $hisTvCObservasiObstinHemo['qd']);
      array_push($vp, $hisTvCObservasiObstinHemo['vp']);
      array_push($tmp, $hisTvCObservasiObstinHemo['tmp']);
    }
    $data = array(
      'nokun' => $nokun,
      'nomr' => $nomr,
      'historyObservasiObstinHemo' => $historyObservasiObstinHemo,
      'idhemo' => $idhemo,

      'waktu' => $waktu,
      'napas' => $napas,
      'nadi' => $nadi,
      'sistolik' => $sistolik,
      'diastolik' => $diastolik,
      'suhu' => $suhu,

      'ufg' => $ufg,
      'ufr' => $ufr,
      'uf' => $uf,
      'qb' => $qb,
      'qd' => $qd,
      'vp' => $vp,
      'tmp' => $tmp,
    );

    $this->load->view('Pengkajian/hemodialisa/obstinKeperawatanHemodialisis/viewObservasiObstin', $data);

  }

  public function viewTindakanKeperawatanHemoObstin()
  {
    $nokun = $this->input->post('nokun');
    $nomr = $this->input->post('nomr');
    $idhemo = $this->input->post('idhemo');
    $tindakanKeperawatan = $this->masterModel->referensi(1083);
    $mengaturPosisiNyaman = $this->masterModel->referensi(1084);
    $mengkajiKemampuanPasien = $this->masterModel->referensi(1085);
    $melakukanInsersi = $this->masterModel->referensi(1086);
    $ManagemenKomplikasiHd = $this->masterModel->referensi(1087);
    $tranfusi = $this->masterModel->referensi(1088);
    $historyTindakanKeperawatanHemo = $this->pengkajianAwalModel->historyTindakanKeperawatanHemo($idhemo);
    $data = array(
      'nokun' => $nokun,
      'nomr' => $nomr,
      'listPerawat' => $this->masterModel->listPerawat(),
      'tindakanKeperawatan' => $tindakanKeperawatan,
      'mengaturPosisiNyaman' => $mengaturPosisiNyaman,
      'mengkajiKemampuanPasien' => $mengkajiKemampuanPasien,
      'melakukanInsersi' => $melakukanInsersi,
      'ManagemenKomplikasiHd' => $ManagemenKomplikasiHd,
      'tranfusi' => $tranfusi,
      'historyTindakanKeperawatanHemo' => $historyTindakanKeperawatanHemo,
      'idhemo' => $idhemo
    );

    $this->load->view('Pengkajian/hemodialisa/obstinKeperawatanHemodialisis/viewTindakanKeperawatanHemoObstin', $data);

  }

  public function viewTindakanKeperawatanHemoObstinDetail()
  {
    $idtind = $this->input->post('idtind');
    $tindakanKeperawatan = $this->masterModel->referensi(1083);
    $mengaturPosisiNyaman = $this->masterModel->referensi(1084);
    $mengkajiKemampuanPasien = $this->masterModel->referensi(1085);
    $melakukanInsersi = $this->masterModel->referensi(1086);
    $ManagemenKomplikasiHd = $this->masterModel->referensi(1087);
    $tranfusi = $this->masterModel->referensi(1088);
    $getTindakanKeperawatanHemo = $this->pengkajianAwalModel->getTindakanKeperawatanHemo($idtind);
    $arrayTindakanKep = explode(',' , $getTindakanKeperawatanHemo['tindakan_keperawatan_hemo']);
    $arrayMengaturPosisiNyaman = explode(',' , $getTindakanKeperawatanHemo['mengatur_posisi_nyaman']);
    $arrayMengkajiKemampuanPasien = explode(',' , $getTindakanKeperawatanHemo['mengkaji_kemampuan_pasien']);
    $arrayMelakukanInsersi = explode(',' , $getTindakanKeperawatanHemo['melakukan_insersi']);
    $arrayManagemenKomHd = explode(',' , $getTindakanKeperawatanHemo['managemen_komplikasi_hd']);
    $arrayTranfusi = explode(',' , $getTindakanKeperawatanHemo['tranfusi_obstin']);
    $data = array(
      'tindakanKeperawatan' => $tindakanKeperawatan,
      'mengaturPosisiNyaman' => $mengaturPosisiNyaman,
      'mengkajiKemampuanPasien' => $mengkajiKemampuanPasien,
      'melakukanInsersi' => $melakukanInsersi,
      'ManagemenKomplikasiHd' => $ManagemenKomplikasiHd,
      'tranfusi' => $tranfusi,
      'getTindakanKeperawatanHemo' => $getTindakanKeperawatanHemo,
      'arrayTindakanKep' => $arrayTindakanKep,
      'arrayMengaturPosisiNyaman' => $arrayMengaturPosisiNyaman,
      'arrayMengkajiKemampuanPasien' => $arrayMengkajiKemampuanPasien,
      'arrayMelakukanInsersi' => $arrayMelakukanInsersi,
      'arrayManagemenKomHd' => $arrayManagemenKomHd,
      'arrayTranfusi' => $arrayTranfusi,
      'idtind' => $idtind
    );

    $this->load->view('Pengkajian/hemodialisa/obstinKeperawatanHemodialisis/viewDetailTindKepHemo', $data);

  }

  public function simpanOberservasiObstinHemo()
  {
    $idhemo = $this->input->post("idHemoObservasiObstinHemo");
    $date = $this->input->post('tanggalObstinHemo');
    $tglObservasi = date('Y-m-d', strtotime($date));
    $jamObstinHemo = $this->input->post('jamObstinHemo');
    $sistolikObstinHemo = $this->input->post('sistolikObstinHemo');
    $diastolikObstinHemo = $this->input->post('diastolikObstinHemo');
    $nadiObstinHemo = $this->input->post('nadiObstinHemo');
    $pernapasanObstinHemo = $this->input->post('pernapasanObstinHemo');
    $suhuObstinHemo = $this->input->post('suhuObstinHemo');
    $ufgObstinHemo = $this->input->post('ufgObstinHemo');
    $ufrObstinHemo = $this->input->post('ufrObstinHemo');
    $ufObstinHemo = $this->input->post('ufObstinHemo');
    $qbObstinHemo = $this->input->post('qbObstinHemo');
    $qdObstinHemo = $this->input->post('qdObstinHemo');
    $vpObstinHemo = $this->input->post('vpObstinHemo');
    $apObstinHemo = $this->input->post('apObstinHemo');
    $tmpObstinHemo = $this->input->post('tmpObstinHemo');
    $hepObstinHemo = $this->input->post('hepObstinHemo');
    $oleh = $this->session->userdata('id');


    $data = array(
      'id_hemo' => $idhemo,
      'tanggal' => $tglObservasi,
      'jam' => $jamObstinHemo,
      'td_sistolik' => $sistolikObstinHemo,
      'td_diastolik' => $diastolikObstinHemo,
      'nadi' => $nadiObstinHemo,
      'pernapasan' => $pernapasanObstinHemo,
      'suhu' => $suhuObstinHemo,
      'ufg' => $ufgObstinHemo,
      'ufr' => $ufrObstinHemo,
      'uf' => $ufObstinHemo,
      'qb' => $qbObstinHemo,
      'qd' => $qdObstinHemo,
      'vp' => $vpObstinHemo,
      'ap' => $apObstinHemo,
      'tmp' => $tmpObstinHemo,
      'hep' => $hepObstinHemo,
      'oleh' => $oleh,
      'status' => 1,
    );
    // echo "<pre>";print_r($data);exit();
    $this->db->insert('keperawatan.tb_observasi_obstin_hemo', $data);
  }

  public function simpanTindakanKeperawatanObstinHemo()
  {
    $post = $this->input->post();
    $idhemo = $this->input->post("idHemoTindakanKeperawatanObstinHemo");
    $date = $this->input->post('tanggalObstinTindakanKeperawatanHemo');
    $tglTindKep = date('Y-m-d', strtotime($date));
    $jamObstinTindakanKeperawatanHemo = $this->input->post('jamObstinTindakanKeperawatanHemo');
    $tindakanKeperawatanHemo = implode(',',$post["tindakanKeperawatanHemo"]);
    $mengaturPosisiNyaman = implode(',',$post["mengaturPosisiNyaman"]);
    $mengkajiKemampuanPasien = implode(',',$post["mengkajiKemampuanPasien"]);
    $melakukanInsersi = implode(',',$post["melakukanInsersi"]);
    $ManagemenKomplikasiHd = implode(',',$post["ManagemenKomplikasiHd"]);
    $therapiIVTindakanKeperawatan = $this->input->post('therapiIVTindakanKeperawatan');
    $therapiSCTindakanKeperawatan = $this->input->post('therapiSCTindakanKeperawatan');
    $therapiOralTindakanKeperawatan = $this->input->post('therapiOralTindakanKeperawatan');
    $tranfusiObstinHemo = implode(',',$post["tranfusiObstinHemo"]);
    $deskGolonganDarahHemo = $this->input->post('deskGolonganDarahHemo');
    $deskNoStockHemo = $this->input->post('deskNoStockHemo');
    $deskJumlahHemo = $this->input->post('deskJumlahHemo');
    $deskJenisHemo = $this->input->post('deskJenisHemo');
    $namaPerawatPertama = $this->input->post('namaPerawatPertama');
    $namaPerawatKedua = $this->input->post('namaPerawatKedua');
    $oleh = $this->session->userdata('id');

    $data = array(
      'id_hemo' => $idhemo,
      'tanggal' => $tglTindKep,
      'jam' => $jamObstinTindakanKeperawatanHemo,
      'tindakan_keperawatan_hemo' => $tindakanKeperawatanHemo,
      'mengatur_posisi_nyaman' => $mengaturPosisiNyaman,
      'mengkaji_kemampuan_pasien' => $mengkajiKemampuanPasien,
      'melakukan_insersi' => $melakukanInsersi,
      'managemen_komplikasi_hd' => $ManagemenKomplikasiHd,
      'therapi_iv' => $therapiIVTindakanKeperawatan,
      'therapi_sc' => $therapiSCTindakanKeperawatan,
      'therapi_oral' => $therapiOralTindakanKeperawatan,
      'tranfusi_obstin' => $tranfusiObstinHemo,
      'desk_gol_darah' => $deskGolonganDarahHemo,
      'desk_no_stock' => $deskNoStockHemo,
      'desk_jumlah' => $deskJumlahHemo,
      'desk_jenis' => $deskJenisHemo,
      'nama_perawat_1' => $namaPerawatPertama,
      'nama_perawat_2' => $namaPerawatKedua,
      'perawat_1' => file_get_contents($this->input->post('perawat_1')),
      'perawat_2' => file_get_contents($this->input->post('perawat_2')),
      'oleh' => $oleh,
      'status' => 1,
    );
    // echo "<pre>";print_r($data);exit();
    $this->db->insert('keperawatan.tb_observasi_dan_tindkep_hemo', $data);
  }

}


/* End of file Pra_anestesi.php */
/* Location: ./application/controllers/anestesi/evaluasikemampuanfungsionalmobilisasi/FormEKFM.php */
