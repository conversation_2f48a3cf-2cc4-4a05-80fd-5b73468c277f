<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Eresep extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','EresepModel','antrian/antrianModel'));
    }

    public function index() {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
        $data = array(
            'pasien' => $pasien,
            'farmasi_eresep' => $this->masterModel->farmasi(),
            'obat' => $this->EresepModel->getobat($pasien['ID_TUJUAN_FARMASI'], $pasien['IDPENJAMIN']),
            'getTbBbAlergi' => $this->pengkajianAwalModel->getTbBbAlergi($pasien['NORM']),
            'jalur_pemberiaan' => $this->masterModel->referensi(901),
            'adapenunjang' => $this->EresepModel->checkpenunjang($pasien['NOKUN'])
        );

        // echo json_encode($data);

        $this->load->view('rekam_medis/eresep/index',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
                $post = $this->input->post();
                $kode = $this->pengkajianAwalModel->generateNoOrderResep();
                $dataOrderResep = array(
                    'NOMOR' => $kode,
                    'KUNJUNGAN' => $post["kunjungan"],
                    'TANGGAL' => date("Y-m-d H:i:s"),
                    'DOKTER_DPJP' => $post["dokter"],
                    'PEMBERI_RESEP' => $post["pemberi"],
                    'BERAT_BADAN' => $post["bb"],
                    'TINGGI_BADAN' => $post["tb"],
                    'DIAGNOSA' => $post["diagnosa"],
                    'ALERGI_OBAT' => $post["alergi"],
                    'GANGGUAN_FUNGSI_GINJAL' => $post["gangguan_fungsi_ginjal"],
                    'MENYUSUI' => $post["menyusui"],
                    'HAMIL' => $post["hamil"],
                    'RESEP_PASIEN_PULANG' => isset($post["resep_pp"]) ? $post["resep_pp"] : 0,
                    'TUJUAN' => $post["farmasi_tujuan"],
                    'RESEP_CITO' => isset($post['cito']) ? $post['cito'] : "0",
                    'OLEH' => $this->session->userdata('id'),
                );

                $dataDetailOrderResep = array();
                $index = 0;

                $this->db->trans_begin();
                if (isset($post['obat'])) {
                    $aturanPakai = "";
                    $id_obat = array();
                    foreach ($post['obat'] as $input) {
                        if ($post['obat'][$index] != "") {
                            if (empty($post['aturan_pakai_id'][$index])) {
                                if (empty($post['aturan_pakai_text'][$index]) && empty($post['jdosis'][$index]) && empty($post['kardek'][$index])) {
                                    $aturanPakai = "";
                                } else {
                                    if (!empty($post['aturan_pakai_text'][$index])) {
                                        $dataAturanPakai = array(
                                            'JENIS' => 41,
                                            'DESKRIPSI' => $post['aturan_pakai_text'][$index]
                                        );
                                    } 

                                    if(!empty($post['kardek'][$index])){
                                        $dataAturanPakai = array(
                                            'JENIS' => 41,
                                            'DESKRIPSI' =>  $post['dosis'][$index] ."/". $post['jam'][$index] ." Jam/". $this->EresepModel->getJalurPemberian($post['jalur_pemberian'][$index])
                                        );
                                    }
                                    
                                    if(!empty($post['jdosis'][$index])){
                                        $dataAturanPakai = array(
                                            'JENIS' => 41,
                                            'DESKRIPSI' => $this->masterModel->referensiSimpel(107,$post['jdosis'][$index])['DESKRIPSI'] .' '.$this->masterModel->referensiSimpel(108,$post['sdosis'][$index])['DESKRIPSI'].' '.$this->masterModel->referensiSimpel(109,$post['frekuensi'][$index])['DESKRIPSI'].' '.$this->masterModel->referensiSimpel(110,$post['keteranganAp'][$index])['DESKRIPSI']
                                        );
                                    }
                                    $this->db->insert('master.referensi', $dataAturanPakai);
                                    $aturanPakai = $this->db->insert_id();
                                }
                            } else {
                                $aturanPakai = isset($post['aturan_pakai_id'][$index]) ? $post['aturan_pakai_id'][$index] : "";
                            }
                            $id = explode("|", $post['obat'][$index]);
                            array_push(
                                $dataDetailOrderResep, array(
                                    'ORDER_ID' => $kode,
                                    'FARMASI' => $id[0],
                                    'STOK' => $id[1],
                                    'DOSIS' => $post['dosis'][$index],
                                    'JUMLAH' => $post['jumlah'][$index],
                                    'JUMLAH_SIGNA' => $post['jdosis'][$index],
                                    'SATUAN_SIGNA' => $post['sdosis'][$index],
                                    'FREKUENSI_SIGNA' => $post['frekuensi'][$index],
                                    'KETERANGAN_SIGNA' => $post['keteranganAp'][$index],
                                    'ATURAN_PAKAI' => $aturanPakai,
                                    'KETERANGAN' => $post['keterangan'][$index],
                                    // 'RACIKAN'       => isset($post['racikan'][$index]) ? $post['racikan'][$index] : "0",
                                    'RACIKAN' => $post['group_racikan'][$index] > 0 ? 1 : 0,
                                    'GROUP_RACIKAN' => $post['group_racikan'][$index],
                                    'KARDEX' => isset($post['kardek']) ? $post['kardek'][$index] : 0,
                                    'JAM' => isset($post['jam']) ? $post['jam'][$index] : null,
                                    'JALUR_PEMBERIAN' => isset($post['jalur_pemberian']) ? $post['jalur_pemberian'][$index] : null,
                                    // 'PETUNJUK_RACIKAN' => $post['tindakanRadiologi'][$index],
                                    'PAKET'            => isset($post['paket']) ? $post['paket'][$index] : 0,
                                    // 'JENIS_RESEP'      => $post['tindakanRadiologi'][$index],
                                    // 'KARDEX'           => $post['tindakanRadiologi'][$index],
                                    // 'REF'              => $post['tindakanRadiologi'][$index],
                                )
                            );
                            $id_obat[] = $id[0];
                        }
                        $index++;
                    }
                }

                $this->db->insert('layanan.order_resep', $dataOrderResep);
                $this->db->insert_batch('layanan.order_detil_resep', $dataDetailOrderResep);
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();

                    // $id_obat = implode(',', $id_obat);
                    // $createdby = $this->session->userdata('id');
                    // $this->antrianModel->getNoAntrianFarmasi($kode, $id_obat, $createdby, $post["cekpenunjang"]);
                    $result = array('status' => 'success', 'data' => array('norm' => $post['norm'],'farmasi_tujuan' => $post['farmasi_tujuan'], 'iddokter' => $post["dokter"], 'noorder' => $kode));
                }
    		
    			echo json_encode($result);
            }else if($param == 'batal'){
    			$data = $this->EresepModel->cekStatusResep();
                if ($data['STATUS'] == 1) {
                    $this->db->set(array('STATUS' => 0 ,'OLEH' => $this->session->userdata('id')));
                    $this->db->where('NOMOR', $this->input->post('id'));
                    $this->db->update('layanan.order_resep');
                    $result = array('status' => 'success', 'pesan' => 'Resep Berhasil Di Batalkan');
                } elseif ($data['STATUS'] == 2) {
                    $result = array('status' => 'failed', 'pesan' => 'Tidak bisa di batalkan, resep telah diterima');
                } elseif ($data['STATUS'] == 0) {
                    $result = array('status' => 'failed', 'pesan' => 'Status Resep Di Batalkan');
                }

                echo json_encode($result);
            }else if($param == 'view'){
    			$post = $this->input->post();
                $dataOrderResep = array(
                    'KUNJUNGAN' => $post["kunjungan"],
                    'TANGGAL' => date("Y-m-d H:i:s"),
                    'DOKTER_DPJP' => $post["dokter"],
                    'PEMBERI_RESEP' => $post["pemberi"],
                    'BERAT_BADAN' => $post["bb"],
                    'TINGGI_BADAN' => $post["tb"],
                    'DIAGNOSA' => $post["diagnosa"],
                    'ALERGI_OBAT' => $post["alergi"],
                    'GANGGUAN_FUNGSI_GINJAL' => $post["gangguan_fungsi_ginjal"],
                    'MENYUSUI' => $post["menyusui"],
                    'HAMIL' => $post["hamil"],
                    'RESEP_PASIEN_PULANG' => isset($post["resep_pp"]) ? $post["resep_pp"] : 0,
                    'TUJUAN' => $post["farmasi_tujuan"],
                    'RESEP_CITO' => isset($post['cito']) ? $post['cito'] : "0",
                    'OLEH' => $this->session->userdata('id'),
                );

                $dataDetailOrderResep = array();
                $index = 0;
                $notif="";
                if (isset($post['obat'])) {
                    foreach ($post['obat'] as $input) {
                        if ($post['obat'][$index] != "") {
                            $id = explode("|", $post['obat'][$index]);
                            $dataNotif = "";
                            $notif = $this->EresepModel->notifResep($post["norm"],$id[0]);
                            foreach ($notif as $value) {
                                $dataNotif .= '- '.$value['INFO'].'<br/>';
                            }
                            array_push(
                                $dataDetailOrderResep, array(
                                    'FARMASI' => $this->EresepModel->getNameObat($id[0]),
                                    'STOK' => $id[1],
                                    'DOSIS' => $post['dosis'][$index],
                                    'JUMLAH' => $post['jumlah'][$index],
                                    'JUMLAH_SIGNA' => $this->masterModel->referensiSimpel(107,$post['jdosis'][$index])['DESKRIPSI'],
                                    'SATUAN_SIGNA' => $this->masterModel->referensiSimpel(108,$post['sdosis'][$index])['DESKRIPSI'],
                                    'FREKUENSI_SIGNA' => $this->masterModel->referensiSimpel(109,$post['frekuensi'][$index])['DESKRIPSI'],
                                    'KETERANGAN_SIGNA' => $this->masterModel->referensiSimpel(110,$post['keteranganAp'][$index])['DESKRIPSI'],
                                    'ATURAN_PAKAI' => (!empty($post['aturan_pakai_text'][$index]) ? $post['aturan_pakai_text'][$index] : (!empty($post['kardek'][$index]) ? $post['dosis'][$index] ."/". $post['jam'][$index] ." Jam/". $this->EresepModel->getJalurPemberian($post['jalur_pemberian'][$index]) : "")),
                                    'KETERANGAN' => $post['keterangan'][$index],
                                    'RACIKAN' => $post['group_racikan'][$index] > 0 ? "Racikan[" . $post['group_racikan'][$index] . "]" : "Non-racik",
                                    'GROUP_RACIKAN' => $post['group_racikan'][$index],
                                    'KARDEX' => isset($post['kardek']) ? $post['kardek'][$index] == 1 ? "Ya": "Tidak" : "Tidak",
                                    'JALUR_PEMBERIAN' => isset($post['jalur_pemberian']) && !empty($post['jalur_pemberian'][$index]) ? $this->EresepModel->getJalurPemberian($post['jalur_pemberian'][$index]) : "",
                                    'INFO' => $dataNotif
                                )
                            );
                        }
                        $index++;
                    }
                }

                $result = array(
                    'dataResep' => $dataOrderResep,
                    'dataDetailResep' => $dataDetailOrderResep
                );

                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->validasiMalnutrisiDewasaModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_validasi_malnutrisi" data-id="'.$row -> NOPEN.'"><i class="fa fa-eye"></i> Lihat</a><a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/validasimalnutrisi/validasimalnutrisi.php?format=pdf&nopen='.$row -> NOPEN.'" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->validasiMalnutrisiDewasaModel->total_count(),
            "recordsFiltered"   => $this->validasiMalnutrisiDewasaModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function jalur_pemberian()
    {
        $result = $this->masterModel->jalurPemberian();

        $json = array();
        foreach ($result as $row) {

            $json[] = array('id' => $row['id_variabel'] , 'text' => $row['variabel']);
        }
        echo json_encode($json);
    }

    public function detilResep()
    {
        $id = $this->input->post('id');
        $dResep = $this->EresepModel->detilResep($id);
        $dOResep = $this->EresepModel->detailOrderResep($id);

        echo '<div class="row">' .
            '<div class="col-lg-6">' .
            '<div class="form-group">' .
            '<table class="table" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' .
            '<tr>' .
            '<td>Tanggal Resep</td>' .
            '<td>' . date("d-m-Y H:i:s", strtotime($dOResep->TANGGAL_RESEP)) . '</td>' .
            '</tr>' .
            '<tr>' .
            '<td>Pengirim</td>' .
            '<td>' . $dOResep->OLEH . '</td>' .
            '</tr>' .
            '<tr>' .
            '<td>DPJP</td>' .
            '<td>' . $dOResep->DPJP, '</td>' .
            '</tr>' .
            '<tr>' .
            '<td>Ruang Asal Order</td>' .
            '<td>' . $dOResep->ASAL_UNIT . '</td>' .
            '</tr>' .
            '</table>' .
            '</div>' .
            '</div>' .
            '<div class="col-lg-6">' .
            '<div class="form-group">' .
            '<table class="table" style="background: #f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' .
            '<tr>' .
            '<td>Status Resep</td>' .
            '<td>' . $dOResep->STATUS_RESEP . '</td>' .
            '</tr>' .
            '</table>' .
            '</div>' .
            '</div>' .
            '</div>';
        echo "<table class='table table-bordered table-bordered dt-responsive' id='tblDetilResep'  cellspacing='0' width='100%''>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>No</th>";
        echo "<th>Jenis</th>";
        echo "<th>Nama Obat</th>";
        echo "<th>Jumlah</th>";
        echo "<th>Aturan Pakai</th>";
        echo "<th>Keterangan</th>";
        echo "<th>Kardek</th>";
        echo "<th>Jalur Pemberian</th>";
        echo "</tr>";
        echo "</thead>";

        echo "<tbody>";
        $no = 1;
        foreach ($dResep as $dResep):
            $kardek = $dResep['KARDEX'] == 1 ? "Ya" : "Tidak";
            $jalur = $dResep['JALUR_PEMBERIAN'] !== null ? $this->EresepModel->getJalurPemberian($dResep['JALUR_PEMBERIAN']) : "-";
            $generik = $dResep['GENERIK'] == "" ? "" : " (".$dResep['GENERIK'].")";
            echo "<tr>";
            echo "<td>" . $no . "</td>";
            echo "<td>" . $dResep['KLP_RACIKAN'] . "</td>";
            echo "<td>" . $dResep['NAMA_OBAT'] . $generik . "</td>";
            echo "<td>" . $dResep['JUMLAH'] . "</td>";
            echo "<td>" . $dResep['ATURAN_PAKAI'] . "</td>";
            echo "<td>" . $dResep['KETERANGAN'] . "</td>";
            echo "<td>" . $kardek . "</td>";
            echo "<td>" . $jalur . "</td>";
            echo "</tr>";

            $no++;
        endforeach;
        echo "</tbody>";
        if ($dOResep->STATUS == 1 && $this->session->userdata('status') == 1) {
            echo "<tfooter>
        <tr>
        <td colspan='6'>
            <div class='row'>
                <div class='offset-11'>
                    <a href='#' id='batalResep' class='btn btn-sm btn-danger' data-toggle='modal' data-id='$id' data-backdrop='static' data-keyboard='false'><i class='fa fa-times'></i> Batalkan</a>
                </div>
            </div>
        </td>
        </tr>
        </tfooter>";
            echo "</table>";
        }
    }
    
    public function daftarobat(){
        
        $this->load->view('rekam_medis/eresep/daftarobat',$data);
    }

    public function datadaftarobat(){
        $draw = intval($this->input->post("draw"));
        $start = intval($this->input->post("start"));
        $length = intval($this->input->post("length"));

        $jenisp = $this->input->POST('jenis');

        $dObat = $this->EresepModel->listdataobat();

        // var_dump($dObat);exit;
        $data = array();
        $no = 1;
        foreach ($dObat as $rr) {
            
                $data[] = array(
                    $no,
                    $rr['nama_obat'],
                    '<span class="'.(($rr['tipe']=='obat' && $rr['FORMULARIUM']=="Non-fornas")?"text-danger":"").'">'.$rr['FORMULARIUM'].'</span>',
                    '<span class="'.(($rr['tipe']=='alkes' && $rr['EKATALOG']=="Non-ekatalog")?"text-danger":(($rr['tipe']=='obat' && $rr['EKATALOG']=="Non-ekatalog")?"text-primary":"")).'">'.$rr['EKATALOG'].'</span>',
                    $rr['generik'],
                    $rr['nama_ruangan'],
                    (int)$rr['STOK'],
                    $rr['restriksi']
                );
                
            $no++;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => count($dObat),
            "recordsFiltered" => count($dObat),
            "data" => $data
        );
        echo json_encode($output);
    }
}