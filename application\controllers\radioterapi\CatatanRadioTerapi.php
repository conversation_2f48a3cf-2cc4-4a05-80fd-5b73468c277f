<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CatatanRadioTerapi extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model([
            'masterModel',
            'pengkajianAwalModel',
            'rekam_medis/rawat_inap/keperawatan/Idomodel',
            'radioterapi/CatatanModel'
        ]);
    }

    public function index()
    {
        $nomr = $this->uri->segment(4);
        $nopen = $this->uri->segment(5);
        $nokun = $this->uri->segment(6);

        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listDoctors = $this->CatatanModel->getListDoctor();
        $listPerawat = $this->CatatanModel->getListPerawat();
        $listRadiografer = $this->CatatanModel->getListRadiografer();
        $listFisikawan = $this->CatatanModel->getListFisikawan();
        $data = [
            'nopen'           => $nopen,
            'nomr'            => $nomr,
            'nokun'           => $nokun,
            'getNomr'         => $getNomr,
            'listDoctors'     => $listDoctors,
            'listPerawat'     => $listPerawat,
            'listFisikawan'   => $listFisikawan,
            'listRadiografer' => $listRadiografer
        ];

        $this->load->view('Pengkajian/radioTerapi/catatan', $data);
    }

    public function saveNote()
    {
        // Start a transaction
        $this->db->trans_begin();

        // Get form data
        $profesi_id = $this->input->post('tujuan');
        $pegawai_id = null; // Default null
        $catatan = $this->input->post('catatan');
        $nokun = $this->input->post('nokun');
        $nopen = $this->input->post('nopen');
        $user_id = $this->session->userdata('id');

        if (!$profesi_id) {
            echo json_encode(['status' => 'error', 'message' => 'Tidak ada profesi ID!']);
            return;
        }


        // Set id_pegawai based on which radio button (profesi) is selected
        if ($profesi_id == 11) { // Dokter
            $pegawai_id = $this->input->post('dokterselect');
        } elseif ($profesi_id == 6) { // Perawat
            $pegawai_id = $this->input->post('perawatselect');
        } elseif ($profesi_id == 8) { // Radiografer (RTT)
            $pegawai_id = $this->input->post('radiograferselect');
        } elseif ($profesi_id == 17) { // Radiografer (RTT)
            $pegawai_id = $this->input->post('fisikawanselect');
        }

        // Ensure that if the selected pegawai is empty, set it to null
        if (empty($pegawai_id)) {
            $pegawai_id = null;
        }

        $filePath = null;
        $fileName = null;

        if (!empty($_FILES['fileUpload']['name'])) {
            // Define upload path
            $uploadPath = '../upload_emr/radioterapi/catatan/';

            // Check if the directory exists, if not, create it
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true); // Create the directory with permissions
            }

            $config['upload_path'] = $uploadPath;
            // Configure upload
            $config['allowed_types'] = 'jpg|jpeg|png|pdf|doc|docx'; // Set allowed file types
            $config['max_size'] = 2048; // Set max file size in KB
            // Generate a random string and combine it with the timestamp for the file name
            $randomString = bin2hex(random_bytes(5)); // Generates a random 10-character hexadecimal string
            $config['file_name'] = time() . '_' . $randomString; // Rename file with timestamp and random string

            $this->load->library('upload');
            $this->upload->initialize($config);

            // Attempt to upload the file
            if ($this->upload->do_upload('fileUpload')) {
                $uploadData = $this->upload->data();
                $filePath = '../upload_emr/radioterapi/catatan/'; // Set the file path
                $fileName = $uploadData['file_name']; // Get the filename
            } else {
                // Handle upload errors
                echo json_encode(['status' => 'error', 'message' => $this->upload->display_errors()]);
                return;
            }
        }



        // Prepare data for insertion
        $data = [
            'nomr'         => $this->pengkajianAwalModel->getNomr($nokun)['NORM'], // Assuming 'nomr' comes from this model function
            'nokun'        => $nokun,
            'id_profesi'   => $profesi_id,
            'id_pegawai'   => $pegawai_id, // This will be null if empty
            'catatan'      => $catatan,
            'file_path'    => $filePath, // Save file path
            'file_name'    => $fileName,
            'status'       => 1, // Assuming default status is 1 (active)
            'created_at'   => date('Y-m-d H:i:s'),
            'created_by'   => $user_id,
            'updated_at'   => null, // Null initially, updated when edited
            'updated_by'   => null // Null initially, updated when edited
        ];

        // Insert data into database
        $this->CatatanModel->saveCatatan($data);

        // Check transaction status
        if ($this->db->trans_status() === FALSE) {
            // Rollback transaction if any error occurs
            $this->db->trans_rollback();
            echo json_encode(['status' => 'error', 'message' => 'Failed to save data']);
        } else {
            // Commit the transaction if successful
            $this->db->trans_commit();
            echo json_encode(['status' => 'success', 'message' => 'Data saved successfully']);
        }
    }


    public function getCatatanRadioterapi()
    {
        $start = $this->input->get('start');
        $length = $this->input->get('length');
        $search = $this->input->get('search')['value']; // Search value
        $order_column = $this->input->get('order')[0]['column']; // Column to order
        $order_dir = $this->input->get('order')[0]['dir']; // Order direction
        $nomr = $this->input->get('nomr') ?? null;

        $columns = ['mcr.id', 'mcr.created_at', 'apc.NAMA', 'mp.NAMA', 'mcr.catatan', 'jumlah_jawaban'];

        // Fetch filtered data, total count, and filtered count
        $result = $this->CatatanModel->getCatatanData($start, $length, $search, $columns[$order_column], $order_dir, $nomr);

        echo json_encode([
            'draw' => intval($this->input->get('draw')),
            'recordsTotal' => $result['total_count'],
            'recordsFiltered' => $result['filtered_count'],
            'data' => $result['data']
        ]);
    }

    public function getCatatanModal()
    {
        $start = $this->input->get('start');
        $length = $this->input->get('length');
        $search = $this->input->get('search')['value'];
        $order_column = $this->input->get('order')[0]['column'];
        $order_dir = $this->input->get('order')[0]['dir'];
        $user_id = $this->session->userdata('id');
        $profesi_id = $this->session->userdata('profesi');

        // Set default status to 1 if no status is provided
        $status = $this->input->get('status') ?? 1;

        $columns = ['mcr.id', 'mcr.created_at', 'apc.NAMA', 'mp.NAMA', 'mcr.catatan', 'jumlah_jawaban'];

        // Fetch data with the status parameter
        $result = $this->CatatanModel->getCatatanDataModal($start, $length, $search, $columns[$order_column], $order_dir, $user_id, $profesi_id, $status);
        echo json_encode([
            'draw' => intval($this->input->get('draw')),
            'recordsTotal' => $result['total_count'],
            'recordsFiltered' => $result['filtered_count'],
            'data' => $result['data'],
            'status'    => $status,
            'prfesi' => $profesi_id
        ]);
    }


    public function countNotif()
    {
        $user_id = $this->session->userdata('id');
        $profesi_id = $this->session->userdata('profesi');
        $status = 1;

        // Call the getCatatanDataModal function from the model and pass necessary parameters
        $result = $this->CatatanModel->getCatatanDataModal(0, 0, '', 'id', 'ASC', $user_id, $profesi_id, $status);

        // Return only the total_count as JSON
        echo json_encode(['total_count' => $result['total_count']]);
    }

    public function modalCatatan()
    {
        // print_r($this->session->userdata());
        $this->load->view('Pengkajian/radioTerapi/modal_catatan');
    }

    public function saveJawaban()
    {
        // Start a transaction
        $this->db->trans_begin();

        // Get form data from the modal
        $id_catatan = $this->input->post('id_catatan');
        $jawaban = $this->input->post('jawaban');
        $user_id = $this->session->userdata('id');
        $filePath = null;
        $fileName = null;

        if (!empty($_FILES['fileupjawaban']['name'])) {
            // Define upload path
            $uploadPath = '../upload_emr/radioterapi/jawaban/';

            // Check if the directory exists, if not, create it
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true); // Create the directory with permissions
            }

            $config['upload_path'] = $uploadPath;
            // Configure upload
            $config['allowed_types'] = 'jpg|jpeg|png|pdf|doc|docx'; // Set allowed file types
            $config['max_size'] = 2048; // Set max file size in KB
            // Generate a random string and combine it with the timestamp for the file name
            $randomString = bin2hex(random_bytes(5)); // Generates a random 10-character hexadecimal string
            $config['file_name'] = time() . '_' . $randomString; // Rename file with timestamp and random string

            $this->load->library('upload');
            $this->upload->initialize($config);

            // Attempt to upload the file
            if ($this->upload->do_upload('fileupjawaban')) {
                $uploadData = $this->upload->data();
                $filePath = '../upload_emr/radioterapi/jawaban/'; // Set the file path
                $fileName = $uploadData['file_name']; // Get the filename
            } else {
                // Handle upload errors
                echo json_encode(['status' => 'error', 'message' => $this->upload->display_errors()]);
                return;
            }
        }
        // Prepare data for insertion into the medis.jawaban_radioterapi table
        $data = [
            'id_catatan'   => $id_catatan,
            'jawaban'      => $jawaban,
            'status'       => 1, // Assuming 1 is for 'active' status
            'created_at'   => date('Y-m-d H:i:s'),
            'file_path'     => $filePath,
            'file_name'     => $fileName,
            'created_by'   => $user_id,
            'updated_at'   => null,
            'updated_by'   => null
        ];
        // Cek jika mcr.created_by = $user_id
        $catatan = $this->CatatanModel->getCatatanById($id_catatan); // Get catatan details based on id_catatan

        if ($catatan) {
            if ($catatan['created_by'] == $user_id) {
                // Jika created_by sama dengan user_id, ubah status menjadi 1
                $data_update = [
                    'status'     => 1, // Update status menjadi 1 jika created_by = user_id
                    'updated_at' => date('Y-m-d H:i:s'),
                    'updated_by' => $user_id
                ];
            } else {
                // Jika created_by bukan user_id, ubah status menjadi 2
                $data_update = [
                    'status'     => 2, // Assuming 2 indicates 'completed' status
                    'updated_at' => date('Y-m-d H:i:s'),
                    'updated_by' => $user_id
                ];
            }

            // Update the status of the catatan in the medis.tb_catatanradioterapi table
            $this->CatatanModel->updateCatatan($id_catatan, $data_update);
        }
        // Insert data into database
        $this->CatatanModel->saveJawaban($data);

        // Check transaction status
        if ($this->db->trans_status() === FALSE) {
            // Rollback transaction if any error occurs
            $this->db->trans_rollback();
            echo json_encode(['status' => 'error', 'message' => 'Failed to save jawaban']);
        } else {
            // Commit the transaction if successful
            $this->db->trans_commit();
            echo json_encode(['status' => 'success', 'message' => 'Jawaban saved successfully']);
        }
    }

    public function lihatCatatan()
    {
        $user_id = $this->session->userdata('id');
        $profesi_id = $this->session->userdata('profesi');
        $id_catatan = $this->input->post('id_catatan');

        // Mulai transaksi
        $this->db->trans_begin();

        // Dapatkan data catatan berdasarkan id_catatan
        $catatan = $this->CatatanModel->getCatatanById($id_catatan);

        if ($catatan) {
            // 1. Update ke tb_jawabanradioterapi jika $user_id = mcr.created_by
            if ($user_id == $catatan['created_by']) {
                // Update status di tb_jawabanradioterapi yang created_by-nya bukan $user_id
                $data_jawaban = ['status' => 2]; // Contoh: status = 2 untuk updated
                $this->db->where('id_catatan', $id_catatan);
                $this->db->where('created_by !=', $user_id);
                $this->db->update('medis.tb_jawabanradioterapi', $data_jawaban);
            }

            // 2. Update ke tb_catatanradioterapi jika mcr.id_pegawai ada isinya dan $user_id = apc.ID
            if (!empty($catatan['id_pegawai']) && $user_id == $catatan['pengguna_id']) {
                // Update status tb_catatanradioterapi
                $data_catatan = ['status' => 2];
                $this->CatatanModel->updateCatatan($id_catatan, $data_catatan);

                // Update status tb_jawabanradioterapi yang created_by-nya bukan $user_id
                $data_jawaban = ['status' => 2];
                $this->db->where('id_catatan', $id_catatan);
                $this->db->where('created_by !=', $user_id);
                $this->db->update('medis.tb_jawabanradioterapi', $data_jawaban);
            }

            // 3. Update ke tb_catatanradioterapi jika mcr.id_pegawai kosong dan $profesi_id = mcr.id_profesi
            if (empty($catatan['id_pegawai']) && $profesi_id == $catatan['id_profesi']) {
                // Update status tb_catatanradioterapi
                $data_catatan = ['status' => 2];
                $this->CatatanModel->updateCatatan($id_catatan, $data_catatan);

                // Update status tb_jawabanradioterapi yang created_by-nya bukan $user_id
                $data_jawaban = ['status' => 2];
                $this->db->where('id_catatan', $id_catatan);
                $this->db->where('created_by !=', $user_id);
                $this->db->update('medis.tb_jawabanradioterapi', $data_jawaban);
            }

            // Selesaikan transaksi
            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                return ['status' => false, 'message' => 'Gagal memperbarui data.'];
            } else {
                $this->db->trans_commit();
                return ['status' => true, 'message' => 'Data berhasil diperbarui.'];
            }
        } else {
            $this->db->trans_rollback();
            return ['status' => false, 'message' => 'Catatan tidak ditemukan.'];
        }
    }


    public function getJawabanByCatatan()
    {
        $id_catatan = $this->input->get('id_catatan');  // Get the id_catatan from the GET request
        if ($id_catatan) {
            $jawaban = $this->CatatanModel->getJawabanByCatatan($id_catatan);  // Pass the id_catatan to the model
            echo json_encode(['jawaban' => $jawaban]);
        } else {
            echo json_encode(['error' => 'No ID provided']);
        }
    }

    public function batalkanCatatan()
    {
        // Start a transaction
        $this->db->trans_begin();

        // Get the ID of the note to cancel
        $id_catatan = $this->input->post('id_catatan');

        // Prepare the data for cancellation
        $data = [
            'status' => 0, // Assuming 0 indicates cancelled status
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->userdata('id')
        ];

        // Update the record in the database
        $this->CatatanModel->updateCatatan($id_catatan, $data); // You may need to implement this method in your model

        // Check transaction status
        if ($this->db->trans_status() === FALSE) {
            // Rollback transaction if any error occurs
            $this->db->trans_rollback();
            echo json_encode(['status' => 'error', 'message' => 'Failed to cancel the note']);
        } else {
            // Commit the transaction if successful
            $this->db->trans_commit();
            echo json_encode(['status' => 'success', 'message' => 'Note cancelled successfully']);
        }
    }

    public function getCatatanById()
    {
        // Get the ID from the GET request
        $id_catatan = $this->input->get('id_catatan');

        // Load the model if not already loaded

        // Get the catatan by ID using the model method
        $catatan = $this->CatatanModel->getCatatanById($id_catatan);

        // Check if data is found
        if ($catatan) {
            // Return the result as JSON
            echo json_encode($catatan);
        } else {
            // If no data found, return an empty JSON object or an error message
            echo json_encode([]);
        }
    }

    public function checkUser() {
        $profesi_id = $this->session->userdata('profesi');
        $user_id = $this->session->userdata('id');
        
        // Get the result from the model
        $can_show_icon = $this->CatatanModel->checkUser($user_id, $profesi_id);
        
        // Return JSON response
        echo json_encode(['can_show_icon' => $can_show_icon]);
    }
    
}
