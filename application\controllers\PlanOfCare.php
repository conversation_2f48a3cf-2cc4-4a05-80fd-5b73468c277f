<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PlanOfCare extends CI_Controller
{

	public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $data = array(
            'title' => 'Formulir Plan Of Care',
            'isi'   => 'Pengkajian/planofcare/caripasien',
      );
        $this->load->view('layout/wrapper',$data);
    }

    public function dataPengkajian()
    {
        $nomr       = $this->input->post('nomr');
        $dataPasien = $this->masterModel->dataDiriPasien($nomr);
        $dataKunjungan = $this->pengkajianAwalModel->getNomrByMr($nomr);
        $listDiskusiKasus = $this->masterModel->referensi(889);
        $listStatusRawat = $this->masterModel->referensi(890);
        $listSMF = $this->masterModel->listSMF();
        $listDr = $this->masterModel->listDr();
        $historyPlanOfCare =  $this->pengkajianAwalModel->historyPlanOfCare($nomr);

        $data = array(
        'title'      => 'Halaman View All Pengkajian',
        'nomr'       => $nomr,
        'dataPasien' => $dataPasien,
        'getNomr' => $dataKunjungan,
        'listDiskusiKasus' => $listDiskusiKasus,
        'listStatusRawat' => $listStatusRawat,
        'listSMF' => $listSMF,
        'listDr' => $listDr,
        'historyPlanOfCare' => $historyPlanOfCare,
        'isi'   => 'Pengkajian/planofcare/formulirPlanOfCare',
        );

        $this->load->view('layout/wrapper',$data);

    }

    public function simpanFormPlanOfCare()
    {
        $idemr = $this->pengkajianAwalModel->getIdEmr();
        $kunjungan = $this->input->post("nokun");
        $nomr = $this->input->post("nomr");
        $id_pengguna = $this->session->userdata('id');
        $diskusikasus = $this->input->post("diskusikasus");
        $diskusikasuslain = $this->input->post("diskusikasuslain");
        $statusrawat = $this->input->post("statusrawat");
        $dpjp = $this->input->post("dpjp");
        $tanggal = $this->input->post("tanggal");
        $ke = $this->input->post("ke");
        $datahasil = $this->input->post("datahasil");
        $daftarmasalah = $this->input->post("daftarmasalah");
        $sasaranterukur = $this->input->post("sasaranterukur");
        $lamarawat = $this->input->post("lamarawat");
        $rencanaselanjutnya = $this->input->post("rencanaselanjutnya");
        $dpjputama = $this->input->post("dpjputama");
        $spesialis = $this->input->post("spesialis");

        $data = array(
            'id_emr' => $idemr,
            'nokun' => $kunjungan,
            'nomr' => $nomr,
            'jenis_diskusi' => $diskusikasus,
            'jenis_diskusi_lain' => $diskusikasuslain,
            'status_rawat' => $statusrawat,
            'dpjp' => $dpjp,
            'tanggal' => date("Y-m-d", strtotime($tanggal)),
            'ke' => $ke,
            'data_hasil' => $datahasil,
            'daftar_masalah' => $daftarmasalah,
            'rumusan_sasaran' => $sasaranterukur,
            'lama_rawat' => $lamarawat,
            'rencana_selanjutnya' => $rencanaselanjutnya,
            'oleh' => $id_pengguna,
        );

        $this->db->trans_begin();

        $dataarray = array();
        $indexArray = 0;
        if ($dpjputama != NULL) {
            foreach ($dpjputama as $inputdpjputama) {
                if ($dpjputama[$indexArray] != "") {
                    array_push(
                        $dataarray, array(
                        'id_emr' => $idemr,
                        'spesialis' => $spesialis[$indexArray],
                        'dokter' => $dpjputama[$indexArray],
                        )
                    );
                }
                $indexArray++;
            }
        }

        $this->db->insert('medis.tb_plan_of_care', $data);
        $this->db->insert_batch('medis.tb_plan_of_care_array', $dataarray);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
        //$pengkajianprasedasi = $this->pengkajianAwalModel->insertFormPengkajianPraSedasi($data);
    }

    public function lihatHistoryPlanOfCare()
    {   
        $id = $this->input->post('id');
        $planofcare = $this->pengkajianAwalModel->HistoryDetailPlanOfCare($id);
        $planofcarearray = $this->pengkajianAwalModel->historyDetailPlanOfCareArray($id);
        $listDiskusiKasus = $this->masterModel->referensi(889);
        $listStatusRawat = $this->masterModel->referensi(890);
        $listSMF = $this->masterModel->listSMF();
        $listDr = $this->masterModel->listDr();

        foreach($planofcare as $poc):
        echo '  <ul class="nav nav-tabs">
                    <li class="nav-item col-sm-12 col-md-12">
                    <div class="row form-group">	
                        <label for="diskusikasus" class="col-md-3 col-form-label">
                        Jenis diskusi kasus
                        </label>
                        </br>
                        <div class="row">';
                            foreach ($listDiskusiKasus as $list):
        echo '              <div class="radio radio-primary jarak2 col-sm-12 col-md-4" style="margin-left:59px;">
                                <input type="radio" name="diskusikasus" value="'.$list['id_variabel'].'" class="diskusikasus" id="diskusikasus'.$list['id_variabel'].'" ';
                                if($poc['jenis_diskusi'] == $list['id_variabel']){
                                    echo "checked";
                                }else{
                                    echo "";
                                }
        echo '                  >
                                <label for="diskusikasus'.$list['id_variabel'].'">
                                '.$list['variabel'].'                             
                                </label>
                            </div>';
                            endforeach;
        echo '              <div class="col-sm-12 col-md-8" id="iddiskusikasuslain">
                                <div class="col-sm-12 col-md-6" style="margin-left:360px">
                                    <input type="text" class="form-control diskusikasuslain" id="diskusikasuslain" name="diskusikasuslain"
                                    placeholder="[ Tuliskan Jenis Diskusi Kasus lain ]" value="'.$poc['jenis_diskusi_lain'].'" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-group">	
                        <label for="statusrawat" class="col-md-3 col-form-label">
                        Status Dirawat
                        </label>';
                            foreach ($listStatusRawat as $list):
        echo '              <div class="col-md-2 form-check">
                                <div class="radio radio-primary form-check-input jarak2">
                                    <input type="radio" name="statusrawat" value="'.$list['id_variabel'].'" class="statusrawat" id="statusrawat'.$list['id_variabel'].'" ';
                                    if($poc['status_rawat'] == $list['id_variabel']){
                                        echo "checked";
                                    }else{
                                        echo "";
                                    }
        echo '                      >
                                    <label for="statusrawat'.$list['id_variabel'].'" class="form-check-label">'.$list['variabel'].'</label>
                                </div>
                            </div> ';
                            endforeach;
        echo '      </div>
                    <div class="row form-group">	
                        <label for="dpjp" class="col-md-3 col-form-label">
                        DPJP
                        </label>
                        <div class="col-md-6">
                        <select id="dpjp" name="dpjp" class="form-control select2">
                            <option disabled selected>[ Pilih ]</option>';
                            foreach ($listSMF as $list): 
        echo '              <option value="'.$list['ID_SMF'].'" ';
                                if($poc['dpjp'] == $list['ID_SMF']){
                                    echo "selected";
                                }else{
                                    echo "";
                                }
        echo '              >'.$list['SMF'].'</option>';                           
                            endforeach;
        echo '          </select>
                        </div>
                    </div>
                    <div class="row form-group">	
                        <label class="col-md-12 col-form-label">
                        Pembahasan :
                        </label>
                        <label for="pembahasan" class="col-md-3 col-form-label">
                        Hari dan Tanggal
                        </label>	
                        <div class="col-md-3">
                        <input type="text" class="form-control" placeholder="[ Tanggal ]" id="datepicker-tglpoc" name="tanggal" value="'.$poc['tanggal'].'" autocomplete=off>
                        </div>
                        <label for="pembahasan" class="col-md-1 col-form-label">
                        Ke
                        </label>	
                        <div class="col-md-2">
                        <input type="text" class="form-control" placeholder="[ Ke ]" name="ke" value="'.$poc['ke'].'" autocomplete=off>
                        </div>
                    </div>
                    <div class="row form-group">	
                        <label class="col-md-12 col-form-label">
                        Data hasil pemeriksaan pasien (anamnesa, pemeriksaan fisik. pemeriksaan penunjang, dsb)
                        </label>
                        <div class="col-md-9">
                        <textarea name="datahasil" class="form-control" placeholder="[ Tuliskan Data Hasil Pemeriksaan ]">'.$poc['data_hasil'].'</textarea>
                        </div>
                    </div>
                    <div class="row form-group">	
                        <label class="col-md-12 col-form-label">
                        Daftar Masalah
                        </label>
                        <div class="col-md-9">
                        <textarea name="daftarmasalah" class="form-control" placeholder="[ Tuliskan Daftar Masalah ]">'.$poc['daftar_masalah'].'</textarea>
                        </div>
                    </div>
                    <div class="row form-group">	
                        <label class="col-md-12 col-form-label">
                        Plan of Care Merumuskan sasaran terukur pelayanan pasien
                        </label>
                        <div class="col-md-9">
                        <textarea name="sasaranterukur" class="form-control" placeholder="[ Tuliskan Rumusan Sasaran Terukur ]">'.$poc['rumusan_sasaran'].'</textarea>
                        </div>
                    </div>
                    <div class="row form-group">	
                        <label class="col-md-12 col-form-label">
                        Perkiraan lama rawat
                        </label>
                        <div class="col-md-9">
                        <input type="text" name="lamarawat" class="form-control" placeholder="[ Tuliskan Lama Rawat ]" value="'.$poc['lama_rawat'].'">
                        </div>
                    </div>
                    <div class="row form-group">	
                        <label class="col-md-12 col-form-label">
                        Rencana pertemuan selanjutnya
                        </label>
                        <div class="col-md-9">
                        <input type="text" name="rencanaselanjutnya" class="form-control" placeholder="[ Rencana Selanjutnya ]" value="'.$poc['rencana_selanjutnya'].'">
                        </div>
                    </div>
                    <div class="row form-group">
                        <label class="col-md-12 col-form-label">
                        PPA yang hadir
                        </label>	
                        <label class="col-md-12 col-form-label">
                        Spesialis
                        </label>
                        <div class="col-md-6">
                        <select id="spesialis" class="form-control select2 spesialis">
                            <option disabled selected>[ Pilih ]</option>';
                            foreach ($listSMF as $list):
        echo '              <option value="'.$list['SMF'].'" data-smf="'.$list['ID_SMF'].'">'.$list['SMF'].'</option>';                           
                            endforeach; 
        echo '          </select>
                        </div>

                        <label class="col-md-12 col-form-label">
                        DPJP Utama
                        </label>
                        <div class="col-md-6">
                        <select id="dpjputama" class="form-control select2 dpjputama">
                            <option disabled selected>[ Pilih ]</option>';
                            foreach ($listDr as $list):
        echo '              <option value="'.$list['DOKTER'].'" data-dr="'.$list['ID_DOKTER'].'">'.$list['DOKTER'].'</option>  ';                           
                            endforeach;
        echo '          </select>
                        </div>
                        
                    <div class="row jarak">
                        <div class="offset-md-1 col-md-5">
                        <button type="button" class="btn btn-danger hapusstatusdpjp">Hapus</button>
                        </div>
                        <div class="col-md-5">
                        <button type="button" class="btn btn-success tambahstatusdpjp">Tambah</button>
                        </div>
                    </div>
                    </div>
                    <table class="table table-bordered dt-responsive nowrap" cellspacing="0" width="100%">
                        <thead class="thead-light">
                            <th>#</th>
                            <th>Spesialis</th>
                            <th>Nama Dokter</th>
                        </th>
                        <tbody id="hasilTableStatusDPJP">';
                        foreach ($planofcarearray as $pocarray):
        echo '              <tr>    
                                <td><input type="checkbox" name="pilihStatusPlanOfCare"></td>
                                <td>'.$pocarray['SPESIALIS'].'</td>
                                <td>'.$pocarray['DOKTER'].'</td>
                            </tr>';
                        endforeach;
        echo '          </tbody>
                    </table>
                    </li>
                </ul>';
        endforeach;
    }
}