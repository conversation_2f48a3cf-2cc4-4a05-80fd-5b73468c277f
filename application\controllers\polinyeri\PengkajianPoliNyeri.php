<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPoliNyeri extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'EresepModel'));
    }

public function pengkajian_nyeri($param)
{
	if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
     if ($param == 'tambah' || $param == 'ubah') {
      $post = $this->input->post(NULL,TRUE);
      $imr = $post['idemr'];
      $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
      $oleh = $this->session->userdata("id");			
      $dataperawatan = array(
          'id_emr'    => $getIdEmr,
          'nopen'     => $post['nopen'],
          'nokun'     => $post['nokun'],
          'flag'      => '1',
      );
      
      $data = array(
          'id_emr' 						=> $getIdEmr,
          'nokun' 						=> isset($post['nokun']) ? $post['nokun'] : "",
		// 'tanggal' 						=> isset($post['tanggal']) ? $post['tanggal'] : "",
		// 'waktu' 							=> isset($post['waktu']) ? $post['waktu'] : "",
          'tb' 							=> isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' 							=> isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'tekanan_darah' 				=> isset($post['tekanan_darah']) ? $post['tekanan_darah'] : "",
          'nafas' 						=> isset($post['frekuensi_napas']) ? $post['frekuensi_napas'] : "",
          'nadi' 						=> isset($post['frekuensi_nadi']) ? $post['frekuensi_nadi'] : "",
          'suhu' 						=> isset($post['suhu']) ? $post['suhu'] : "",
          'nyeri' 						=> isset($post['skrining_nyeri']) ? $post['skrining_nyeri'] : "",
          'skor' 						=> isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'penjalaran_nyeri' 			=> isset($post['penjalaran']) ? $post['penjalaran'] : "",
          'ada_penjalaran_nyeri' 		=> isset($post['ada_penjalaran_nyeri']) ? $post['ada_penjalaran_nyeri'] : "",
          'sejak_kapan_nyeri' 			=> isset($post['sejak_kapan']) ? $post['sejak_kapan'] : "",
          'memperberat_nyeri' 			=> isset($post['pemberat_nyeri']) ? $post['pemberat_nyeri'] : "",
		  'ada_pemberat_nyeri' 			=> isset($post['ada_pemberat_nyeri']) ? $post['ada_pemberat_nyeri'] : "",
          'meringankan_nyeri' 			=> isset($post['peringan_nyeri']) ? $post['peringan_nyeri'] : "",
          'ada_meringankan_nyeri' 		=> isset($post['ket_peringan_nyeri']) ? $post['ket_peringan_nyeri'] : "",
          'intensitas_nyeri' 			=> isset($post['intensitas_nyeri']) ? $post['intensitas_nyeri'] : "",
          'deskripsi_rasa_nyeri' 		=> isset($post['rasa_nyeri']) ? $post['rasa_nyeri'] : "",
          'deskripsi_lain' 				=> isset($post['ket_rasa_nyeri']) ? $post['ket_rasa_nyeri'] : "",
          'progresivitas_nyeri' 		=> isset($post['progresifitas_nyeri']) ? $post['progresifitas_nyeri'] : "",
          'gejala_menyertai' 			=> isset($post['gejala_nyeri']) ? $post['gejala_nyeri'] : "",
          'mempengaruhi_akt' 			=> isset($post['pengaruh_nyeri']) ? $post['pengaruh_nyeri'] : "",
          'ya_mempengaruhi_akt' 		=> isset($post['pengaruh_aktifitas_nyeri']) ? $post['pengaruh_aktifitas_nyeri'] : "",
          'riwayat_jatuh' 				=> isset($post['riwayat_jatuh']) ? $post['riwayat_jatuh'] : "",
          'ya_riwayat_jatuh' 			=> isset($post['ya_riwayat_jatuh']) ? $post['ya_riwayat_jatuh'] : "",
          'riwayat_alergi' 				=> isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
          'sebutkan_riwayat_alergi' 	=> isset($post['ket_riwayat_alergi']) ? $post['ket_riwayat_alergi'] : "",
          'status_obat_antinyeri' 		=> isset($post['status_obat_antinyeri']) ? $post['status_obat_antinyeri'] : "",
          'obat_antinyeri' 				=> isset($post['daftar_obat_dikonsumsi']) ? $post['daftar_obat_dikonsumsi'] : "",
          'status_renjatan_nyeri' 		=> isset($post['status_renjatan_nyeri']) ? $post['status_renjatan_nyeri'] : "",
          'jml_renjatan_nyeri' 			=> isset($post['jml_renjatan_nyeri']) ? $post['jml_renjatan_nyeri'] : "",
          'sedang_hamil' 				=> isset($post['sedang_hamil']) ? $post['sedang_hamil'] : "",
          'status_daftar_obat_lain' 	=> isset($post['status_daftar_obat_lain']) ? $post['status_daftar_obat_lain'] : "",
          'daftar_obat_lain' 			=> isset($post['daftar_obat_lain']) ? $post['daftar_obat_lain'] : "",
          'status_riwayat_pengobatan' 	=> isset($post['status_riwayat_pengobatan']) ? $post['status_riwayat_pengobatan'] : "",
          'riwayat_operasi' 			=> isset($post['riwayat_oprasi']) ? $post['riwayat_oprasi'] : "",
          'tanggal_operasi' 			=> isset($post['tanggal_operasi']) ? $post['tanggal_operasi'] : "",
          'radiasi' 					=> isset($post['riwayat_radiasi']) ? $post['riwayat_radiasi'] : "",
          'tanggal_radiasi' 			=> isset($post['tanggal_radiasi']) ? $post['tanggal_radiasi'] : "",
          'riwayat_kemoterapi' 			=> isset($post['kemoterapi']) ? $post['kemoterapi'] : "",
          'tanggal_kemoterapi' 			=> isset($post['tanggal_kemoterapi']) ? $post['tanggal_kemoterapi'] : "",
          'hasil_penunjang' 			=> isset($post['hasil_penunjang']) ? $post['hasil_penunjang'] : "",
          'diagnosis_primer' 			=> isset($post['diagnosa_primer']) ? $post['diagnosa_primer'] : "",
          'jenis_diagnosis_sekunder' 	=> isset($post['jenis_diagnosis_sekunder']) ? $post['jenis_diagnosis_sekunder'] : "",
          'diagnosis_sekunder' 			=> isset($post['diagnosis_sekunder']) ? $post['diagnosis_sekunder'] : "",
          'status_farmakologis' 		=> isset($post['status_farmakologis']) ? $post['status_farmakologis'] : "",
          'farmakologis' 				=> isset($post['farmakologis']) ? $post['farmakologis'] : "",
          'status_non_farmakologis' 	=> isset($post['status_non_farmakologis']) ? $post['status_non_farmakologis'] : "",
          'non_farmakologis' 			=> isset($post['non_farmakologis']) ? $post['non_farmakologis'] : "",
          'oleh' 						=> $oleh,
      );
				//echo "<pre>";print_r($data);exit();
if (!empty($post['idemr'])) {
    $this->db->replace('keperawatan.tb_keperawatan', $dataperawatan);
    $this->db->replace('keperawatan.tb_pengkajian_poli_nyeri', $data);               
} else { 
    $this->db->insert('keperawatan.tb_keperawatan', $dataperawatan);				
    $this->db->insert('keperawatan.tb_pengkajian_poli_nyeri', $data);
}
}
}
}

public function lokasinyeri()
{
    $nomr = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);

    $SoapCppt = $this->masterModel->soap();

    $data = array(
        'title' => 'Lokasi nyeri',
        'isi' => 'Pengkajian/pengkajiannyeri/lokasiNyeri',
        'nomr' => $nomr,
        'nopen' => $nopen,
        'nokun' => $nokun,
        'SoapCppt' => $SoapCppt,
    );

    $this->load->view('layout/wrapper', $data);
}


public function simpanlokasinyeri()
{
    $nomr = $this->input->post('nomr');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $iddokter = $this->input->post('iddokter');

    $data = array(
        'NOMR' => $nomr,
        'NOPEN' => $nopen,
        'NOKUN' => $nokun,
        //'iddokter' => $iddokter,
        'DATA' => file_get_contents($this->input->post('img_geno')),
        'CATATAN' => $this->input->post('catatan'),
    );
    
	//echo "<pre>";print_r($data);exit();
	$this->db->insert('medis.tb_lokasi_nyeri', $data);		
    redirect(base_url("polinyeri/PengkajianPoliNyeri/lokasinyeri/" . $nomr . "/" . $nopen . "/" . $nokun));
}

public function historyGambarNyeri()
{
    $nomr = $this->uri->segment(4);
    $data = $this->pengkajianAwalModel->historyGambarNyeri($nomr);
    echo json_encode($data);
}

public function keteranganLokasiNyeri()
{
    $id = $this->input->post('id');
    $hasilCaptureLokasiNyeri = $this->pengkajianAwalModel->hasilCaptureLokasiNyeri($id);
    echo '
    <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . $hasilCaptureLokasiNyeri['TGL_INPUT'] . '</span> ]</h4>
    <div class="row form-group">
    <div class="col-lg-6">
    <label class="col-form-label">Hasil Foto</label><br>
    <img src="data:image;base64,' . base64_encode($hasilCaptureLokasiNyeri['DATA']) . '" >
    </div>
    <div class="col-lg-6">
    <label for="catatan" class="col-form-label">Catatan</label>
    <textarea class="form-control" rows="8" placeholder="[Catatan ]" name="catatan" readonly>' . $hasilCaptureLokasiNyeri['CATATAN'] . '</textarea>
    </div>
    </div>';
}

public function NonaktifStatusLokasiNyeri()
{
    $id = $this->input->post('id');
    $data = array(
        'STATUS' => 0,
    );
	$this->db->where('ID', $id);
	$this->db->update('medis.tb_lokasi_nyeri', $data);
}

}
