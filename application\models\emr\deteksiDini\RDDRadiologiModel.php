<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RDDRadiologiModel extends MY_Model
{
    protected $_table_name = 'medis.tb_rdd_radiologi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }

    public function simpan($data)
    {
        $this->db->insert_batch($this->_table_name, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_rdd_radiologi.id_rdd', $id);
        $this->db->update($this->_table_name, $data);
    }

    public function penunjang($id)
    {
        $this->db->select(
            "rr.id_tindakan_medis, DATE_FORMAT(tm.TANGGAL,'%d-%m-%Y, %H.%i.%s') tanggal, t.NAMA nama_tindakan,
            tm.TINDAKAN tindakan"
        );
        $this->db->from('medis.tb_rdd_radiologi rr');
        $this->db->join('layanan.tindakan_medis tm', 'tm.ID = rr.id_tindakan_medis', 'left');
        $this->db->join('master.tindakan t', 't.ID = tm.TINDAKAN', 'left');
        $this->db->where('rr.id_rdd', $id);
        $this->db->where('rr.status', 1);
        $this->db->order_by('tanggal');
        return $this->db->get();
    }
}

// End of file RDDRadiologiModel.php
// Location: ./application/models/emr/deteksiDini/RDDRadiologiModel.php