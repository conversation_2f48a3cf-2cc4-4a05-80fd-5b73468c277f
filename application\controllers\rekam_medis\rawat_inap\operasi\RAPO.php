<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RAPO extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'operasi/LaporanOperasiModel',
        'rekam_medis/rawat_inap/operasi/RAPOModel',
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    // echo '<pre>';print_r($nokun);exit();
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $pasien['NORM'];
    $lapOperasi = $this->LaporanOperasiModel->history(null, $nokun, 'rapo', null);
    // echo '<pre>';print_r($lapOperasi);exit();

    // Tindakan Operasi
    $tindakan = [];
    if (!empty($lapOperasi['tindakan_operasi_lainnya'])) {
      $tindakan[0] = $lapOperasi['tindakan_operasi_lainnya'];
    }

    if (!empty($lapOperasi['tindakan'])) {
      $i = 1;
      foreach (explode(',', str_replace(', ', '^', $lapOperasi['tindakan'])) as $isiTindakan) {
        $tindakan[$i] = str_replace('^', ', ', $isiTindakan);
        $i++;
      };
    }

    // Diagnosis Pasca Operasi
    $diagnosis = null;
    if (!empty($lapOperasi['diagnosis_pasca_bedah'])) {
      $diagnosis = $lapOperasi['diagnosis_pasca_bedah'];
    } else {
      if (!empty($lapOperasi['diagnosis_pasca_bedah_lainnya'])) {
        $diagnosis = $lapOperasi['diagnosis_pasca_bedah_lainnya'];
      }
    }

    $data = [
      'pasien' => $pasien,
      'idEmr' => isset($this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr) ? $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr : null,
      'ruangRawat' => $this->masterModel->referensi(1613),
      'namaRuang' => $this->masterModel->ruanganRskd(),
      'posisiBadan' => $this->masterModel->referensi(1256),
      'posisiEkstrimitas' => $this->masterModel->referensi(1267),
      'diet' => $this->masterModel->referensi(1268),
      'infus' => $this->masterModel->referensi(1269),
      'pemberianTransfusi' => $this->masterModel->referensi(1270),
      'terpasangDrain' => $this->masterModel->referensi(1271),
      'jenisDrain' => $this->masterModel->referensi(1272),
      'terpasangTampon' => $this->masterModel->referensi(1276),
      'perawatanLuka' => $this->masterModel->referensi(1277),
      'dauerCatheter' => $this->masterModel->referensi(1278),
      'nasogastricTube' => $this->masterModel->referensi(1279),
      'periksaLaboratorium' => $this->masterModel->referensi(1280),
      'periksaRontgen' => $this->masterModel->referensi(1281),
      'observasiTerhadap' => $this->masterModel->referensi(1282),
      'pendampingPasien' => $this->masterModel->referensi(1283),
      'kontakDarurat' => $this->masterModel->referensi(1284),
      'anjuran' => $this->masterModel->referensi(1285),
      'cekNopen' => $this->RAPOModel->cekNopen($nomr),
      'jumlah' => $this->RAPOModel->jumlah($nomr),
      'lapOperasi' => $lapOperasi,
      'tindakan' => $tindakan,
      'diagnosis' => $diagnosis,
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/operasi/RAPO/index', $data);
  }

  public function barisObat()
  {
    $this->load->view('rekam_medis/rawat_inap/operasi/RAPO/obat');
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->RAPOModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();
          $oleh = $this->session->userdata['id'];
          $status = 1;
          $diet = $post['diet'] ?? null;
          $ketDiet1 = null;
          $tanggal = date('Y-m-d H:i:s');

          // Keterangan diet
          if ($diet == '4260') {
            $ketDiet1 = null;
          } elseif ($diet == '4261') {
            $ketDiet1 = isset($post['ket_puasa_total']) ? $post['ket_puasa_total'] : null;
          } elseif ($diet == '4262') {
            $ketDiet1 = isset($post['ket_diet_1']) ? $post['ket_diet_1'] : null;
          }

          // Simpan Rencana Asuhan Pasca Operasi
          $data = [
            'nokun' => $post['nokun'] ?? null,
            'tanggal' => $post['tanggal'] ?? null,
            'tindakan_operasi' => $post['tindakan_operasi'] ?? null,
            'diagnosis_pasca_operasi' => $post['diagnosis_pasca_operasi'] ?? null,
            'ruang_rawat' => $post['ruang_rawat'] ?? null,
            'nama_ruang_rawat' => $post['nama_ruang_rawat'] ?? null,
            'posisi_badan' => $post['posisi_badan'] ?? null,
            'ket_posisi_badan' => $post['ket_posisi_badan'] ?? null,
            'posisi_ekstrimitas' => $post['posisi_ekstrimitas'] ?? null,
            'ket_posisi_ekstrimitas' => $post['ket_posisi_ekstrimitas'] ?? null,
            'diet' => $diet,
            'ket_diet_1' => $ketDiet1,
            'ket_diet_2' => $post['ket_diet_2'] ?? null,
            'infus' => $post['infus'] ?? null,
            'ket_infus' => $post['ket_infus'] ?? null,
            'transfusi' => $post['transfusi'] ?? null,
            'ket_transfusi' => $post['ket_transfusi'] ?? null,
            'drain' => $post['drain'] ?? null,
            'lokasi_drain' => $post['lokasi_drain'] ?? null,
            'jenis_drain' => $post['jenis_drain'] ?? 0,
            'observasi_drain' => $post['observasi_drain'] ?? null,
            'tampon' => $post['tampon'] ?? null,
            'durasi_tampon' =>  $post['durasi_tampon'] ?? null,
            'pelepasan_tampon' =>  $post['pelepasan_tampon'] ?? null,
            'perawatan_luka' =>  $post['perawatan_luka'] ?? null,
            'dauer_catheter' =>  $post['dauer_catheter'] ?? null,
            'ukur_urin' =>  $post['ukur_urin'] ?? null,
            'ngt' =>  $post['ngt'] ?? null,
            'ukur_produksi' =>  $post['ukur_produksi'] ?? null,
            'periksa_lab' =>  $post['periksa_lab'] ?? null,
            'ket_periksa_lab' =>  $post['ket_periksa_lab'] ?? null,
            'periksa_rontgen' =>  $post['periksa_rontgen'] ?? null,
            'ket_periksa_rontgen' => $post['ket_periksa_rontgen'] ?? null,
            'observasi' => $post['observasi'] ?? null,
            'ket_observasi' => $post['ket_observasi'] ?? null,
            'instruksi_lain' => $post['instruksi_lain'] ?? null,
            'pendamping' => $post['pendamping'] ?? null,
            'ket_pendamping' => $post['ket_pendamping'] ?? null,
            'kontak_darurat' => $post['kontak_darurat'] ?? null,
            'ket_kontak_darurat' => $post['ket_kontak_darurat'] ?? null,
            'anjuran' => isset($post['anjuran']) ? implode('-', $post['anjuran']) : null,
            'ket_anjuran' => $post['ket_anjuran'] ?? null,
            'oleh' => $oleh,
            'status' => $status,
            'tgl_update' => $tanggal,
          ];
          // echo '<pre>';print_r($data);exit();
          $idRAPO = $this->RAPOModel->simpanRAPO($data);

          // Simpan Resep Obat
          $data = [];
          $i = 0;
          if (isset($post['nama_obat'])) {
            foreach ($post['nama_obat'] as $dataObat) {
              if ($post['nama_obat'][$i] != null) {
                array_push(
                  $data,
                  [
                    'id_rapo' => $idRAPO,
                    'nama_obat' => $post['nama_obat'][$i] ?? null,
                    'dosis_obat' => $post['dosis_obat'][$i] ?? null,
                    'pemberian_obat' => $post['pemberian_obat'][$i] ?? null,
                    'ket_obat' => $post['ket_obat'][$i] ?? null,
                    'oleh' => $oleh,
                    'status' => $status,
                    'tgl_update' => $tanggal,
                  ]
                );
              }
              $i++;
            }
          }
          // echo '<pre>';print_r($data);exit();

          $this->RAPOModel->simpanObatRAPO($data);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
          } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
          }
        } else {
          $result = ['status' => 'failed', 'errors' => $this->form_validation->error_array()];
        }
        echo json_encode($result);
      }
    }
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nomr = $this->input->post('nomr');
    $history = $this->RAPOModel->history($nomr);
    $data = [];
    $no = 1;
    // echo '<pre>';print_r($nomr);exit();

    foreach ($history->result() as $h) {
      $data[] = [
        $no++,
        date('d/m/Y', strtotime($h->tanggal)),
        $h->pengisi,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-detail-rapo' class='btn btn-sm btn-warning waves-effect' id='ubah-history-rapo' data-toggle='modal' data-id='" . $h->id . "-" . $h->nokun . "'>
            <i class='fas fa-edit'></i> Lihat Rencana dan Ubah
          </button>
          <button type='button' href='#modal-obat-rapo' class='btn btn-sm btn-custom waves-effect' id='tbl-obat-rapo' data-toggle='modal' data-id='" . $h->id . "'>
            <i class='fa fa-flask'></i> Lihat Obat
          </button>
        </div>",
      ];
    }

    $output = [
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    ];
    echo json_encode($output);
  }

  public function history()
  {
    $data = ['nomr' => $this->input->post('nomr')];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/operasi/RAPO/history', $data);
  }

  public function detail()
  {
    $id = $this->input->post('id');
    $nokun = $this->input->post('nokun');
    $detail = $this->RAPOModel->detail($id);
    $data = [
      'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
      'idEmr' => isset($this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr) ? $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr : null,
      'ruangRawat' => $this->masterModel->referensi(1613),
      'namaRuang' => $this->masterModel->ruanganRskd(),
      'posisiBadan' => $this->masterModel->referensi(1256),
      'posisiEkstrimitas' => $this->masterModel->referensi(1267),
      'diet' => $this->masterModel->referensi(1268),
      'infus' => $this->masterModel->referensi(1269),
      'pemberianTransfusi' => $this->masterModel->referensi(1270),
      'terpasangDrain' => $this->masterModel->referensi(1271),
      'jenisDrain' => $this->masterModel->referensi(1272),
      'terpasangTampon' => $this->masterModel->referensi(1276),
      'perawatanLuka' => $this->masterModel->referensi(1277),
      'dauerCatheter' => $this->masterModel->referensi(1278),
      'nasogastricTube' => $this->masterModel->referensi(1279),
      'periksaLaboratorium' => $this->masterModel->referensi(1280),
      'periksaRontgen' => $this->masterModel->referensi(1281),
      'observasiTerhadap' => $this->masterModel->referensi(1282),
      'pendampingPasien' => $this->masterModel->referensi(1283),
      'kontakDarurat' => $this->masterModel->referensi(1284),
      'anjuran' => $this->masterModel->referensi(1285),
      'detail' => $detail,

      // Isi checkbox
      'isiAnjuran' => isset($detail['anjuran']) ? explode('-', $detail['anjuran']) : null,
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/operasi/RAPO/detail', $data);
  }

  public function ubahRAPO()
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->RAPOModel->rules;
      $this->form_validation->set_rules($rules);
      $this->db->trans_begin();
      $post = $this->input->post();
      $oleh = $this->session->userdata['id'];
      $status = 1;
      $ruangRawat = $post['ruang_rawat'] ?? null;
      $diet = $post['diet'] ?? null;
      $ketDiet1 = null;
      $id = $post['id'] ?? null;

      // Keterangan diet
      if ($diet == '4260') {
        $ketDiet1 = null;
      } elseif ($diet == '4261') {
        $ketDiet1 = $post['ket_puasa_total'] ?? null;
      } elseif ($diet == '4262') {
        $ketDiet1 = $post['ket_diet_1'] ?? null;
      }

      // Simpan Rencana Asuhan Pasca Operasi
      $data = [
        'tanggal' => $post['tanggal'] ?? null,
        'tindakan_operasi' => $post['tindakan_operasi'] ?? null,
        'diagnosis_pasca_operasi' => $post['diagnosis_pasca_operasi'] ?? null,
        'ruang_rawat' => $ruangRawat,
        'nama_ruang_rawat' => $post['nama_ruang_rawat'] ?? null,
        'posisi_badan' => $post['posisi_badan'] ?? null,
        'ket_posisi_badan' => $post['ket_posisi_badan'] ?? null,
        'posisi_ekstrimitas' => $post['posisi_ekstrimitas'] ?? null,
        'ket_posisi_ekstrimitas' => $post['ket_posisi_ekstrimitas'] ?? null,
        'diet' => $diet,
        'ket_diet_1' => $ketDiet1,
        'ket_diet_2' => $post['ket_diet_2'] ?? null,
        'infus' => $post['infus'] ?? null,
        'ket_infus' => $post['ket_infus'] ?? null,
        'transfusi' => $post['transfusi'] ?? null,
        'ket_transfusi' => $post['ket_transfusi'] ?? null,
        'drain' => $post['drain'] ?? null,
        'lokasi_drain' => $post['lokasi_drain'] ?? null,
        'jenis_drain' => $post['jenis_drain'] ?? 0,
        'observasi_drain' => $post['observasi_drain'] ?? null,
        'tampon' => $post['tampon'] ?? null,
        'durasi_tampon' => $post['durasi_tampon'] ?? null,
        'pelepasan_tampon' => $post['pelepasan_tampon'] ?? null,
        'perawatan_luka' => $post['perawatan_luka'] ?? null,
        'dauer_catheter' => $post['dauer_catheter'] ?? null,
        'ukur_urin' => $post['ukur_urin'] ?? null,
        'ngt' => $post['ngt'] ?? null,
        'ukur_produksi' => $post['ukur_produksi'] ?? null,
        'periksa_lab' => $post['periksa_lab'] ?? null,
        'ket_periksa_lab' => $post['ket_periksa_lab'] ?? null,
        'periksa_rontgen' => $post['periksa_rontgen'] ?? null,
        'ket_periksa_rontgen' => $post['ket_periksa_rontgen'] ?? null,
        'observasi' => $post['observasi'] ?? null,
        'ket_observasi' => $post['ket_observasi'] ?? null,
        'instruksi_lain' => $post['instruksi_lain'] ?? null,
        'pendamping' => $post['pendamping'] ?? null,
        'ket_pendamping' => $post['ket_pendamping'] ?? null,
        'kontak_darurat' => $post['kontak_darurat'] ?? null,
        'ket_kontak_darurat' => $post['ket_kontak_darurat'] ?? null,
        'anjuran' => isset($post['anjuran']) ? implode('-', $post['anjuran']) : null,
        'ket_anjuran' => $post['ket_anjuran'] ?? null,
        'oleh' => $oleh,
        'status' => $status,
        'tgl_update' => date('Y-m-d H:i:s'),
      ];
      // echo '<pre>';print_r($data);exit();
      $this->RAPOModel->ubahRAPO($id, $data);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = ['status' => 'failed'];
      } else {
        $this->db->trans_commit();
        $result = ['status' => 'success'];
      }
    } else {
      $result = ['status' => 'failed', 'errors' => $this->form_validation->error_array()];
    }
    echo json_encode($result);
  }

  public function tabelObat()
  {
    $id = $this->input->post('id');
    $data = ['tabel' => $this->RAPOModel->tabelObat($id)];
    $this->load->view('rekam_medis/rawat_inap/operasi/RAPO/obat/tabel', $data);
  }

  public function tambahObat()
  {
    $this->load->view('rekam_medis/rawat_inap/operasi/RAPO/obat/tambah');
  }

  public function simpanObat()
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $this->db->trans_begin();
      $post = $this->input->post();
      $data = [
        'id_rapo' => $post['id_rapo'] ?? null,
        'nama_obat' => $post['nama_obat'] ?? null,
        'dosis_obat' => $post['dosis_obat'] ?? null,
        'pemberian_obat' => $post['pemberian_obat'] ?? null,
        'ket_obat' => $post['ket_obat'] ?? null,
        'oleh' =>  $this->session->userdata['id'],
        'status' => 1,
        'tgl_update' => date('Y-m-d H:i:s'),
      ];

      // echo '<pre>';print_r($data);exit();
      $this->RAPOModel->simpanObat($data);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = ['status' => 'failed'];
      } else {
        $this->db->trans_commit();
        $result = ['status' => 'success'];
      }
      echo json_encode($result);
    }
  }

  public function detailObat()
  {
    $id = $this->input->post('id');
    $data = [$this->RAPOModel->detailObat($id)];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/operasi/RAPO/obat/detail', $data);
  }

  public function ubahObat()
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $this->db->trans_begin();
      $post = $this->input->post();
      $id = $post['id'] ?? null;
      $data = [
        'nama_obat' => $post['nama_obat'] ?? null,
        'dosis_obat' => $post['dosis_obat'] ?? null,
        'pemberian_obat' => $post['pemberian_obat'] ?? null,
        'ket_obat' => $post['ket_obat'] ?? null,
        'oleh' => $this->session->userdata['id'],
        'status' => 1,
        'tgl_update' => date('Y-m-d H:i:s'),
      ];

      // echo '<pre>';print_r($data);exit();
      $this->RAPOModel->ubahObat($id, $data);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = ['status' => 'failed'];
      } else {
        $this->db->trans_commit();
        $result = ['status' => 'success'];
      }
      echo json_encode($result);
    }
  }

  public function hapusObat()
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $this->db->trans_begin();
      $id = $this->input->post('id') ?? null;
      $data = ['status' => 0];

      // echo '<pre>';print_r($data);exit();
      $this->RAPOModel->ubahObat($id, $data);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = ['status' => 'failed'];
      } else {
        $this->db->trans_commit();
        $result = ['status' => 'success'];
      }
      echo json_encode($result);
    }
  }
}

/* End of file RAPO.php */
/* Location: ./application/controllers/rekam_medis/rawat_inapp/operasi/RAPO.php */