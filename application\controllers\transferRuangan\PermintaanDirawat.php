<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PermintaanDirawat extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        // if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
        //     redirect('login');
        // }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            ['masterModel', 'pengkajianAwalModel']
        );

        $this->load->library('whatsapp');
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = [
            'getNomr' => $getNomr,
            'listKasus' => $this->masterModel->listKasus(),
            'listRencanaPerawatan' => $this->masterModel->referensi(258),
            'listRencana' => $this->masterModel->listRencana(),
            'listKebutuhanPelayanan' => $this->masterModel->referensi(266),
            'listPerbaikanKeadaanUmum' => $this->masterModel->listPerbaikanKeadaanUmum(),
            'listDiet' => $this->masterModel->listDiet(),
            'listJenisDiet' => $this->masterModel->listJenisDiet(),
            'listHistoryDirawat' => $this->pengkajianAwalModel->listHistoryDirawat($getNomr['NORM']),
            'listDrUmum' => $this->masterModel->listDrUmum(),
        ];

        $this->load->view('Pengkajian/permintaanDirawat/index', $data);
    }

    ///////////////////////////////////////////// Permintaan Dirawat ///////////////////////////////////////////
    public function simpanFormPermintaanDirawat()
    {
        $post = $this->input->post();
        $kunjungan = $post['nokun'];
        $getNomr = $this->pengkajianAwalModel->getNomr($kunjungan);
        $kasus = $post['kasus'];
        $tanggal_rencana = $post['tanggalrencana'] ?? null;
        $diet = $post['diet'] ?? null;
        $dokter = $post['dokter'] ?? null;
        $user = $this->session->userdata('id');

        $data = [
            'kunjungan' => $kunjungan,
            'kasus' => $post['kasus'] ?? null,
            'rencana_perawatan' => $post['rencanaperawatan'] ?? null,
            'ruangan' => $post['ruangan'] ?? null,
            'diagnosis_masuk' => $post['diagnosis'] ?? null,
            'rencana' => $post['pilih_rencana'] ?? null,
            'hasil_yang_diharapkan' => $post['hasilyangdiharapkan'] ?? null,
            'perbaikan_keadaan_umum' => isset($post['keadaanumum']) ? json_encode($post['keadaanumum']) : null,
            'perbaikan_lain_lain' => $post['keumlainlain'] ?? null,
            'prosedur_medis_lain' => $post['prosedurmedislain'] ?? null,
            'jenis' => $post['pilih_cito'] ?? null,
            'persiapan' => $post['persiapan'] ?? null,
            'tanggal' => $tanggal_rencana,
            'terapi_tindakan' => $post['tindakan'] ?? null,
            'diet' => $diet,
            'kebutuhan_pelayanan' => isset($post['kebpel']) ? json_encode($post['kebpel']) : null,
            'dokter' => $dokter,
            'oleh' => $user,
        ];

        if ($diet == '172') {
            $data['jenis_diet'] = $post['jenisdiet'] ?? null;
            $data['diet_lainnya'] = $post['dietlainnya'] ?? null;
            $data['bentuk'] = $post['bentuk'] ?? null;
        }

        // echo '<pre>';print_r($data);exit();
        $this->db->trans_begin();

        $this->db->insert('medis.tb_permintaan_rawat', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
        } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];

            //  Send Whatsapp
            if ($kasus == 911) {
                if (!empty($dokter)) {
                    $jam = date('H');
                    $selamat = null;

                    // Mulai menentukan waktu
                    if ($jam >= '05' && $jam < '10') {
                        $selamat = 'pagi';
                    } elseif ($jam >= '10' && $jam < '15') {
                        $selamat = 'siang';
                    } elseif ($jam >= '15' && $jam < '19') {
                        $selamat = 'sore';
                    } else {
                        $selamat = 'malam';
                    }
                    // Akhir menentukan waktu

                    $dataNomor = $this->masterModel->nomorDokter($dokter);
                    $norm = $getNomr['NORM'];
                    $namaPasien = $this->masterModel->getPasien($norm);
                    $namaDokter = $this->masterModel->getDokter($dokter);

                    if (!empty($dataNomor)) {
                        $nomor = '+62' . substr(trim($dataNomor->NOMOR), 1);
                        try {
                            $this->whatsapp->send(
                                $nomor,
                                [
                                    $selamat . ' ' . $namaDokter,
                                    'kami sampaikan',
                                    'Anda mendapatkan pasien sedang dirawat di IGD, MR ' . $norm . ' atas nama ' . $namaPasien . ' masuk IGD pada tanggal ' . date('d/m/Y', strtotime($tanggal_rencana))
                                ]
                            );
                        } catch (Exception $e) {
                            //throw $th;
                        }
                    }
                }
            }
        }

        echo json_encode($result);
    }

    public function lihatHistoryPermintaanDirawat()
    {
        $id = $this->input->post('id');

        $data = [
            'id' => $id,
            'permintaanDirawat' => $this->pengkajianAwalModel->historyDetailPermintaanDirawat($id),
            'listKebutuhanPelayanan' => $this->masterModel->referensi(266),
            'listKasus' => $this->masterModel->listKasus(),
            'listRencanaPerawatan' => $this->masterModel->referensi(258),
            'listRencana' => $this->masterModel->listRencana(),
            'listDiet' => $this->masterModel->listDiet(),
            'listPerbaikanKeadaanUmum' => $this->masterModel->listPerbaikanKeadaanUmum(),
            'listJenisDiet' => $this->masterModel->listJenisDiet(),
            'listDrUmum' => $this->masterModel->listDrUmum(),
        ];

        $this->load->view('Pengkajian/permintaanDirawat/modalViewEditPermintaanDirawat', $data);
    }

    public function ubahFormPermintaanDirawat()
    {
        $post = $this->input->post();
        $diet = $post['diet_edit'] ?? null;

        $data = [
            'kasus' => $post['kasus_edit'] ?? null,
            'rencana_perawatan' => $post['rencanaperawatan_edit'] ?? null,
            'ruangan' => $post['ruangan_edit'] ?? null,
            'diagnosis_masuk' => $post['diagnosis_edit'] ?? null,
            'rencana' => $post['pilih_rencana_edit'] ?? null,
            'hasil_yang_diharapkan' => $post['hasilyangdiharapkan_edit'] ?? null,
            'perbaikan_keadaan_umum' => isset($post['keadaanumum_edit']) ? json_encode($post['keadaanumum_edit']) : null,
            'perbaikan_lain_lain' => $post['keumlainlain_edit'] ?? null,
            'prosedur_medis_lain' => $post['prosedurmedislain_edit'] ?? null,
            'jenis' => $post['pilih_cito_edit'] ?? null,
            'persiapan' => $post['persiapan_edit'] ?? null,
            'tanggal' => $post['tanggalrencana_edit'] ?? null,
            'terapi_tindakan' => $post['tindakan_edit'] ?? null,
            'diet' => $diet,
            'kebutuhan_pelayanan' => isset($post['kebpel_edit']) ? json_encode($post['kebpel_edit']) : null,
            'bentuk' => $post['bentuk_edit'] ?? null,
            'dokter' => $post['dokter_edit'] ?? null,
            'user_edit' => $this->session->userdata('id'),
            'catatan' => $post['catatan_edit'] ?? null
        ];

        if ($diet == '172') {
            $data['jenis_diet'] = $post['jenisdiet_edit'] ?? null;
            $data['diet_lainnya'] = $post['dietlainnya_edit'] ?? null;
            $data['bentuk'] = $post['bentuk_edit'] ?? null;
        }

        // echo '<pre>';print_r($data);exit();
        $this->db->trans_begin();

        $this->db->where('medis.tb_permintaan_rawat.id', $post['id_permintaandirawat']);
        $this->db->update('medis.tb_permintaan_rawat', $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
        } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
        }

        echo json_encode($result);
    }
}