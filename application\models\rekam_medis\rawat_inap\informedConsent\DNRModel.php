<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DNRModel extends MY_Model {

  public function simpanInformedConcent($data)
  {
    $this->db->insert('db_informed_consent.tb_informed_consent', $data);
    return $this->db->insert_id();
  }

  public function simpanDNR($data)
  {
    $this->db->insert('db_informed_consent.tb_persetujuan_dnr', $data);
  }

  public function listHistoryInformedConsentDNR($nomr)
  {
    $query = $this->db->query("SELECT pp.NORM, tic.id, tic.nokun,
                              master.getNamaLengkapPegawai(ap.NIP) OLEH,
                              master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA,
                              tic.created_at tanggal, tpd.status_persetujuan
                              FROM db_informed_consent.tb_persetujuan_dnr tpd
                              LEFT JOIN db_informed_consent.tb_informed_consent tic ON tic.id = tpd.id_informed_consent
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE pp.NORM = '$nomr' AND tic.`status` = 1
                              ");
    return $query;
  }

   public function getDNR($id)
  {
    $query = $this->db->query("SELECT tic.*, tpd.*, tic.id idtic, tpd.id idDNR
                              , master.getNamaLengkapPegawai(md.NIP) DOKTERPELAKSANA, tic.created_at tanggal
                              FROM db_informed_consent.tb_persetujuan_dnr tpd
                              LEFT JOIN db_informed_consent.tb_informed_consent tic ON tic.id = tpd.id_informed_consent
                              LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tic.nokun
                              LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                              LEFT JOIN aplikasi.pengguna ap ON ap.ID = tic.oleh
                              LEFT JOIN master.dokter md ON md.ID = tic.dokter_pelaksana
                              WHERE tic.id = $id AND tic.`status` = 1
                              ");
    return $query->row_array();
  }

  public function updateInformedConcent($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('db_informed_consent.tb_informed_consent', $data);
  }

  public function updateDNR($data,$idDNR)
  {
    $this->db->where('id', $idDNR);
    $this->db->update('db_informed_consent.tb_persetujuan_dnr', $data);
  }

  public function listPegawai($saksi)
    {
        if ($this->input->get('q')) {
            $this->db->like('master.getNamaLengkapPegawai(peg.NIP)', $this->input->get('q'));
        }
        $this->db->select('pengguna.ID, peg.NIP, master.getNamaLengkapPegawai(peg.NIP) NAMA_LENGKAP');
        $this->db->from('aplikasi.pengguna pengguna');
        $this->db->join('master.pegawai peg', 'pengguna.NIP = peg.NIP', 'left');
         $this->db->where('pengguna.ID', $saksi);
        $this->db->where('peg.NIP is NOT NULL', null, false);

        $query = $this->db->get();
        return $query->row_array();
    }
}
?>