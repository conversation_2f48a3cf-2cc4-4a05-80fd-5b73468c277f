<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('rekam_medis/TbBbModel','pengkajianAwalModel','MalnutrisiModel','DashboardEMR'));
    }

    public function index() {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
        $data = array(
            'pasien' => $pasien
        );
        $this->load->view('rekam_medis/dashboard',$data);
    }

    public function resep(){
        redirect('/dashboard_resep');
    }

    public function eemer()
    {
      $data = array(
        'title' => 'Dashboard Penggunaan EMR',
        'isi' => 'DashRemun/dashboard',
        'statusPengguna' => $_SESSION['status'],
        'idPengguna' => $this->session->userdata('id'),
      );
      // echo '<pre>';print_r($data);exit();
  
      $this->load->view('layout/wrapper', $data);
    }

    public function get_data_listemr()
	{
	$draw   = intval($this->input->POST("draw"));
	$start  = intval($this->input->POST("start"));
	$length = intval($this->input->POST("length"));

    $DOKTER = $this->input->post('DOKTER');
    $TGLAWAL = $this->input->post('TGLAWAL');
    $TGLAKHIR = $this->input->post('TGLAKHIR');

    $listEMR = $this->DashboardEMR->listEMR($TGLAWAL, $TGLAKHIR, $DOKTER);
    

	$data = array();
	$no = 1;
	foreach ($listEMR->result() as $LEM) {

        $mydate = strtotime($LEM->DATE);
        $newformat = date('d-m-Y',$mydate);

		$data[] = array(
		// $no,
            $newformat,
            $LEM->NORM,
            $LEM->PASIEN,
            $LEM->RUANGAN,
            $LEM->FORMULIR,
            $LEM->PENJAMIN,
            $LEM->BILLING,
		);
		// $no++;
	}

	$output = array(
		"draw"            => $draw,
		"recordsTotal"    => $listEMR->num_rows(),
		"recordsFiltered" => $listEMR->num_rows(),
		"data"            => $data
	);
	echo json_encode($output);
	}

    public function get_data_listtanpaemr()
	{
	$draw   = intval($this->input->POST("draw"));
	$start  = intval($this->input->POST("start"));
	$length = intval($this->input->POST("length"));

    $DOKTER1 = $this->input->post('DOKTER1');
    $TGLAWAL1 = $this->input->post('TGLAWAL1');
    $TGLAKHIR1 = $this->input->post('TGLAKHIR1');

    $listEMR = $this->DashboardEMR->listtanpaEMR($TGLAWAL1, $TGLAKHIR1, $DOKTER1);
    

	$data = array();
	$no = 1;
	foreach ($listEMR->result() as $LEM) {

        $mydate = strtotime($LEM->TGL);
        $newformat = date('d-m-Y',$mydate);

		$data[] = array(
		// $no,
            $newformat,
            $LEM->NORM,
            $LEM->PASIEN,
            $LEM->RUANGAN,
            $LEM->TINDAKAN,
            $LEM->TUJUAN,
		);
		// $no++;
	}

	$output = array(
		"draw"            => $draw,
		"recordsTotal"    => $listEMR->num_rows(),
		"recordsFiltered" => $listEMR->num_rows(),
		"data"            => $data
	);
	echo json_encode($output);
	}


}
