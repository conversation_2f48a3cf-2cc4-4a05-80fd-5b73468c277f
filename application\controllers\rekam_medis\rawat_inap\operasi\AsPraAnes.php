<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AsPraAnes extends CI_Controller 
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/operasi/AsPraAnesModel',
    ));
  }

  public function index(){
  	$norm = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);

    $data = array(
      'nopen'                 => $nopen,
      'norm'                  => $norm,
      'nokun'                 => $nokun,
      'getNomr'               => $this->pengkajianAwalModel->getNomr($nokun),
      'menikah'               => $this->masterModel->referensi(308),
      'merokok'               => $this->masterModel->referensi(293),
      'alkohol'               => $this->masterModel->referensi(295),
      'kopi'                  => $this->masterModel->referensi(1026),
      'olahraga'              => $this->masterModel->referensi(1027),
      'aspirin'               => $this->masterModel->referensi(1028),
      'obtAntSkt'             => $this->masterModel->referensi(1029),
      'alergiObt'             => $this->masterModel->referensi(1661),
      'alergiMkn'             => $this->masterModel->referensi(1662),
      'prdhnTNK'              => $this->masterModel->referensi(1030),
      'srngnJantung'          => $this->masterModel->referensi(1031),
      'pembekuanDarah'        => $this->masterModel->referensi(1032),
      'gangguanIrJan'         => $this->masterModel->referensi(1033),
      'permslhnDlmBs'         => $this->masterModel->referensi(1034),
      'hipertensi'            => $this->masterModel->referensi(1035),
      'demamTinggi'           => $this->masterModel->referensi(1036),
      'tuberkulosis'          => $this->masterModel->referensi(1037),
      'diabetes'              => $this->masterModel->referensi(1038),
      'pnykitBerat'           => $this->masterModel->referensi(1039),
      'prdhnTNP'              => $this->masterModel->referensi(1030),
      'mengorok'              => $this->masterModel->referensi(1048),
      'pembekuanDarahP'       => $this->masterModel->referensi(1032),
      'hepatitis'             => $this->masterModel->referensi(1051),
      'sakitMaag'             => $this->masterModel->referensi(1044),
      'hipertensiP'           => $this->masterModel->referensi(1050),
      'anemia'                => $this->masterModel->referensi(1045),
      'pnyktBertAnk'          => $this->masterModel->referensi(1056),
      'srnganJantungP'        => $this->masterModel->referensi(1031),
      'kejang'                => $this->masterModel->referensi(1052),
      'asma'                  => $this->masterModel->referensi(1046),
      'pnyktBwnLahir'         => $this->masterModel->referensi(1054),
      'diabetesP'             => $this->masterModel->referensi(1038),
      'pingsan'               => $this->masterModel->referensi(1047),
      'trnfsDarah'            => $this->masterModel->referensi(1057),
      'pernhHiv'              => $this->masterModel->referensi(1058),
      'hasilHIV'              => $this->masterModel->referensi(1663),
      'apkhPMek'              => $this->masterModel->referensi(1059),
      'jnsAnesP'              => $this->masterModel->referensi(1664),
      'mnsyi'                 => $this->masterModel->referensi(1060),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/asPraAnes/index', $data);
  }

  public function viewEditRi($norm, $nopen, $nokun, $id){
    $getAsPraAnes = $this->AsPraAnesModel->getAsPraAnes($id);
    $data = array(
      'nopen'                 => $nopen,
      'norm'                  => $norm,
      'nokun'                 => $nokun,
      'id'                    => $id,
      'getAsPraAnes'          => $getAsPraAnes,
      'getNomr'               => $this->pengkajianAwalModel->getNomr($nokun),
      'menikah'               => $this->masterModel->referensi(308),
      'merokok'               => $this->masterModel->referensi(293),
      'alkohol'               => $this->masterModel->referensi(295),
      'kopi'                  => $this->masterModel->referensi(1026),
      'olahraga'              => $this->masterModel->referensi(1027),
      'aspirin'               => $this->masterModel->referensi(1028),
      'obtAntSkt'             => $this->masterModel->referensi(1029),
      'alergiObt'             => $this->masterModel->referensi(1661),
      'alergiMkn'             => $this->masterModel->referensi(1662),
      'prdhnTNK'              => $this->masterModel->referensi(1030),
      'srngnJantung'          => $this->masterModel->referensi(1031),
      'pembekuanDarah'        => $this->masterModel->referensi(1032),
      'gangguanIrJan'         => $this->masterModel->referensi(1033),
      'permslhnDlmBs'         => $this->masterModel->referensi(1034),
      'hipertensi'            => $this->masterModel->referensi(1035),
      'demamTinggi'           => $this->masterModel->referensi(1036),
      'tuberkulosis'          => $this->masterModel->referensi(1037),
      'diabetes'              => $this->masterModel->referensi(1038),
      'pnykitBerat'           => $this->masterModel->referensi(1039),
      'prdhnTNP'              => $this->masterModel->referensi(1030),
      'mengorok'              => $this->masterModel->referensi(1048),
      'pembekuanDarahP'       => $this->masterModel->referensi(1032),
      'hepatitis'             => $this->masterModel->referensi(1051),
      'sakitMaag'             => $this->masterModel->referensi(1044),
      'hipertensiP'           => $this->masterModel->referensi(1050),
      'anemia'                => $this->masterModel->referensi(1045),
      'pnyktBertAnk'          => $this->masterModel->referensi(1056),
      'srnganJantungP'        => $this->masterModel->referensi(1031),
      'kejang'                => $this->masterModel->referensi(1052),
      'asma'                  => $this->masterModel->referensi(1046),
      'pnyktBwnLahir'         => $this->masterModel->referensi(1054),
      'diabetesP'             => $this->masterModel->referensi(1038),
      'pingsan'               => $this->masterModel->referensi(1047),
      'trnfsDarah'            => $this->masterModel->referensi(1057),
      'pernhHiv'              => $this->masterModel->referensi(1058),
      'hasilHIV'              => $this->masterModel->referensi(1663),
      'apkhPMek'              => $this->masterModel->referensi(1059),
      'jnsAnesP'              => $this->masterModel->referensi(1664),
      'mnsyi'                 => $this->masterModel->referensi(1060),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/asPraAnes/index', $data);
  }

  public function historyAsPraAnes()
  {
    $nokun = $this->input->post('nokun');
    $data = array(
      'nokun'            => $nokun,
      'historyAsPraAnes'               => $this->AsPraAnesModel->historyAsPraAnes($nokun),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/asPraAnes/historyAsPraAnes', $data);
  }

  public function simpanAsPraAnes()
  {
    $post = $this->input->post();
    $date = $this->input->post('tglPrksPAsPraAnes');
    $tglPeriksaDokter = date('Y-m-d', strtotime($date));
    $oleh = $this->session->userdata("id");
    $data = array(
      'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
      'menikah' => isset($post['menikahAsPraAnes']) ? $post['menikahAsPraAnes'] : "",
      'pekerjaan' => isset($post['pekerjaanAsPraAnes']) ? $post['pekerjaanAsPraAnes'] : "",
      'merokok' => isset($post['merokokAsPraAnes']) ? $post['merokokAsPraAnes'] : "",
      'jmlh_merokok' => isset($post['deskMerokokAsPraAnes']) ? $post['deskMerokokAsPraAnes'] : "",
      'alkohol' => isset($post['alkoholAsPraAnes']) ? $post['alkoholAsPraAnes'] : "",
      'jmlh_alkohol' => isset($post['deskAlkoholAsPraAnes']) ? $post['deskAlkoholAsPraAnes'] : "",
      'kopiteh' => isset($post['kopiAsPraAnes']) ? $post['kopiAsPraAnes'] : "",
      'jmlh_kopiteh' => isset($post['deskKopiAsPraAnes']) ? $post['deskKopiAsPraAnes'] : "",
      'olahraga' => isset($post['olahragaAsPraAnes']) ? $post['olahragaAsPraAnes'] : "",
      'jmlh_olahraga' => isset($post['deskOlahragaAsPraAnes']) ? $post['deskOlahragaAsPraAnes'] : "",
      'obat_resep' => isset($post['obatResepAsPraAnes']) ? $post['obatResepAsPraAnes'] : "",
      'obat_bebas' => isset($post['obatBebasAsPraAnes']) ? $post['obatBebasAsPraAnes'] : "",
      'aspirin' => isset($post['aspirinAsPraAnes']) ? $post['aspirinAsPraAnes'] : "",
      'desk_aspirin' => isset($post['deskAspirinAsPraAnes']) ? $post['deskAspirinAsPraAnes'] : "",
      'obat_anti_sakit' => isset($post['obtAntSktAsPraAnes']) ? $post['obtAntSktAsPraAnes'] : "",
      'desk_obat_anti_sakit' => isset($post['deskObtAntSktAsPraAnes']) ? $post['deskObtAntSktAsPraAnes'] : "",
      'alergi_obat' => isset($post['alergiObtAsPraAnes']) ? $post['alergiObtAsPraAnes'] : "",
      'desk_alergi_obat' => isset($post['deskAlergiObtAsPraAnes']) ? $post['deskAlergiObtAsPraAnes'] : "",
      'alergi_makanan' => isset($post['alergiMknAsPraAnes']) ? $post['alergiMknAsPraAnes'] : "",
      'perdarahan_tidak_normal' => isset($post['perdarahanTidakNormalKAsPraAnes']) ? $post['perdarahanTidakNormalKAsPraAnes'] : "",
      'serangan_jantung' => isset($post['srngnJantungAsPraAnes']) ? $post['srngnJantungAsPraAnes'] : "",
      'pembekuan_tidak_normal' => isset($post['pembekuanDarahKAsPraAnes']) ? $post['pembekuanDarahKAsPraAnes'] : "",
      'gangguan_irama_jantung' => isset($post['gangguanIrJanAsPraAnes']) ? $post['gangguanIrJanAsPraAnes'] : "",
      'masalah_dalam_bius' => isset($post['permslhnDlmBsAsPraAnes']) ? $post['permslhnDlmBsAsPraAnes'] : "",
      'hipertensi' => isset($post['hipertensiAsPraAnes']) ? $post['hipertensiAsPraAnes'] : "",
      'demam_pasca_operasi' => isset($post['demamTinggiAsPraAnes']) ? $post['demamTinggiAsPraAnes'] : "",
      'tuberkulosis' => isset($post['tuberkulosisAsPraAnes']) ? $post['tuberkulosisAsPraAnes'] : "",
      'diabetes' => isset($post['diabetesAsPraAnes']) ? $post['diabetesAsPraAnes'] : "",
      'penyakit_berat_lainnya' => isset($post['pnykitBeratAsPraAnes']) ? $post['pnykitBeratAsPraAnes'] : "",
      'desk_penyakit_keluarga' => isset($post['jelaskanPenyakitKeluargaAsPraAnes']) ? $post['jelaskanPenyakitKeluargaAsPraAnes'] : "",
      'perdarahan_tidak_normal_pasien' => isset($post['prdhnTNPAsPraAnes']) ? $post['prdhnTNPAsPraAnes'] : "",
      'mengorok_pasien' => isset($post['mengorokAsPraAnes']) ? $post['mengorokAsPraAnes'] : "",
      'pembekuan_darah_pasien' => isset($post['pembekuanDarahPAsPraAnes']) ? $post['pembekuanDarahPAsPraAnes'] : "",
      'hepatitis_pasien' => isset($post['hepatitisAsPraAnes']) ? $post['hepatitisAsPraAnes'] : "",
      'sakit_maag_pasien' => isset($post['sakitMaagAsPraAnes']) ? $post['sakitMaagAsPraAnes'] : "",
      'hipertensi_pasien' => isset($post['hipertensiPAsPraAnes']) ? $post['hipertensiPAsPraAnes'] : "",
      'anemia_pasien' => isset($post['anemiaAsPraAnes']) ? $post['anemiaAsPraAnes'] : "",
      'penyakit_berat_pasien' => isset($post['pnyktBertAnkAsPraAnes']) ? $post['pnyktBertAnkAsPraAnes'] : "",
      'serangan_jantung_pasien' => isset($post['srnganJantungPAsPraAnes']) ? $post['srnganJantungPAsPraAnes'] : "",
      'kejang_pasien' => isset($post['kejangAsPraAnes']) ? $post['kejangAsPraAnes'] : "",
      'asma_pasien' => isset($post['asmaAsPraAnes']) ? $post['asmaAsPraAnes'] : "",
      'penyakit_bawaan_pasien' => isset($post['pnyktBwnLahirAsPraAnes']) ? $post['pnyktBwnLahirAsPraAnes'] : "",
      'diabetes_pasien' => isset($post['diabetesPAsPraAnes']) ? $post['diabetesPAsPraAnes'] : "",
      'pingsan_pasien' => isset($post['pingsanAsPraAnes']) ? $post['pingsanAsPraAnes'] : "",
      'desk_penyakit_pasien' => isset($post['jelaskanPenyakitPasienAsPraAnes']) ? $post['jelaskanPenyakitPasienAsPraAnes'] : "",
      'pasien_transfusi_darah' => isset($post['trnfsDarahAsPraAnes']) ? $post['trnfsDarahAsPraAnes'] : "",
      'thun_pasien_transfusi_darah' => isset($post['thnBrpAsPraAnes']) ? $post['thnBrpAsPraAnes'] : "",
      'pasien_hiv' => isset($post['pernhHivAsPraAnes']) ? $post['pernhHivAsPraAnes'] : "",
      'thun_pasien_hiv' => isset($post['thnBrpHIVAsPraAnes']) ? $post['thnBrpHIVAsPraAnes'] : "",
      'hasil_pasien_hiv' => isset($post['hasilHIVAsPraAnes']) ? $post['hasilHIVAsPraAnes'] : "",
      'pasien_memakai' => isset($post['apkhPMekAsPraAnes']) ? json_encode($post['apkhPMekAsPraAnes']) : "",
      'desk_pasien_memakai' => isset($post['deskApkhPMekAsPraAnes']) ? $post['deskApkhPMekAsPraAnes'] : "",
      'desk_riwayat_operasi' => isset($post['rwytOpAsPraAnes']) ? $post['rwytOpAsPraAnes'] : "",
      'jenis_anestesia' => isset($post['jnsAnesPAsPraAnes']) ? json_encode($post['jnsAnesPAsPraAnes']) : "",
      'desk_anestesia_lokal' => isset($post['jnsAnesPLokalAsPraAnes']) ? $post['jnsAnesPLokalAsPraAnes'] : "",
      'desk_anestesia_regional' => isset($post['jnsAnesPRegionalAsPraAnes']) ? $post['jnsAnesPRegionalAsPraAnes'] : "",
      'desk_anestesia_umum' => isset($post['jnsAnesPUmumAsPraAnes']) ? $post['jnsAnesPUmumAsPraAnes'] : "",
      'tgl_periksa_kedokter' => $tglPeriksaDokter,
      'dimana' => isset($post['dmnAsPraAnes']) ? $post['dmnAsPraAnes'] : "",
      'untuk_penyakit' => isset($post['untukPnyktGApAsPraAnes']) ? $post['untukPnyktGApAsPraAnes'] : "",
      'jumlah_kehamilan' => isset($post['jmlhHamilAsPraAnes']) ? $post['jmlhHamilAsPraAnes'] : "",
      'jumlah_anak' => isset($post['jmlhAnakAsPraAnes']) ? $post['jmlhAnakAsPraAnes'] : "",
      'menstruasi_anak' => isset($post['menstruasiTrkhrAsPraAnes']) ? $post['menstruasiTrkhrAsPraAnes'] : "",
      'menyusui' => isset($post['mnsyiAsPraAnes']) ? $post['mnsyiAsPraAnes'] : "",
      'oleh' => $oleh,
    );

      if(!empty($post['idAsPraAnes']))
      {
        $this->db->where('id', $post['idAsPraAnes']);
        $this->db->update('medis.tb_assesmen_pra_anestesi_pasien', $data);
      }else{
        $this->db->insert('medis.tb_assesmen_pra_anestesi_pasien', $data);
      }
  }

  public function viewInputDokterAsPraAnes()
  {
    $idaspra = $this->input->post('idaspra');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');

    $data = array(
      'idaspra'               => $idaspra,
      'nokun'                 => $nokun,
      'norm'                  => $norm,
      'getIdAsPraAnesDokter'  => $this->AsPraAnesModel->getAsPraAnesDokter($idaspra),
      'hlngGigi'              => $this->masterModel->referensi(633),
      'muntah'                => $this->masterModel->referensi(641),
      'mslhLeher'             => $this->masterModel->referensi(664),
      'pingsan'               => $this->masterModel->referensi(672),
      'jtgTdkNrml'            => $this->masterModel->referensi(665),
      'stroke'                => $this->masterModel->referensi(673),
      'batuk'                 => $this->masterModel->referensi(666),
      'kejang'                => $this->masterModel->referensi(674),
      'sskNps'                => $this->masterModel->referensi(667),
      'sdgHml'                => $this->masterModel->referensi(675),
      'baruSj'                => $this->masterModel->referensi(668),
      'lainTlg'               => $this->masterModel->referensi(676),
      'slrnNps'               => $this->masterModel->referensi(669),
      'obesitas'              => $this->masterModel->referensi(677),
      'sktDada'               => $this->masterModel->referensi(670),
      'asaClass'              => $this->masterModel->referensi(1666),
      'reional'               => $this->masterModel->referensi(1668),
      'tknkKhu'               => $this->masterModel->referensi(1669),
      'monitoring'            => $this->masterModel->referensi(1670),
      'altKhsus'              => $this->masterModel->referensi(1671),
      'prwtnPsc'              => $this->masterModel->referensi(1672),
    );
    $this->load->view('rekam_medis/rawat_inap/operasi/asPraAnes/viewInputDokterAsPraAnes', $data);
  }

  public function simpanAsPraAnesDokter()
  {
    $post = $this->input->post();
    $datepuasaMliAsPraAnesDokter = $this->input->post('puasaMliAsPraAnesDokter');
    $datePreMedAsPraAnesDokter = $this->input->post('preMedikasiAsPraAnesDokter');
    $dateTransAsPraAnesDokter = $this->input->post('transportasiAsPraAnesDokter');
    $dateRencanaAsPraAnesDokter = $this->input->post('rencanaOperasiAsPraAnesDokter');

    $puasaMliAsPraAnesDokter = date('Y-m-d H:i:s', strtotime($datepuasaMliAsPraAnesDokter));
    $preMedikasiAsPraAnesDokter = date('Y-m-d H:i:s', strtotime($datePreMedAsPraAnesDokter));
    $transportasiAsPraAnesDokter = date('Y-m-d H:i:s', strtotime($dateTransAsPraAnesDokter));
    $rencanaOperasiAsPraAnesDokter = date('Y-m-d H:i:s', strtotime($dateRencanaAsPraAnesDokter));

    $oleh = $this->session->userdata("id");
    $data = array(
      'id_praanes_pasien' => isset($post['idaspra']) ? $post['idaspra'] : "",
      'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
      'hilangnya_gigi' => isset($post['hlngGigiAsPraAnesDokter']) ? $post['hlngGigiAsPraAnesDokter'] : "",
      'muntah' => isset($post['muntahAsPraAnesDokter']) ? $post['muntahAsPraAnesDokter'] : "",
      'masalah_leher' => isset($post['mslhLeherAsPraAnesDokter']) ? $post['mslhLeherAsPraAnesDokter'] : "",
      'pingsan' => isset($post['pingsanAsPraAnesDokter']) ? $post['pingsanAsPraAnesDokter'] : "",
      'denyut_jantung' => isset($post['jtgTdkNrmlAsPraAnesDokter']) ? $post['jtgTdkNrmlAsPraAnesDokter'] : "",
      'stroke' => isset($post['strokeAsPraAnesDokter']) ? $post['strokeAsPraAnesDokter'] : "",
      'batuk' => isset($post['batukAsPraAnesDokter']) ? $post['batukAsPraAnesDokter'] : "",
      'kejang' => isset($post['kejangAsPraAnesDokter']) ? $post['kejangAsPraAnesDokter'] : "",
      'sesak_napas' => isset($post['sskNpsAsPraAnesDokter']) ? $post['sskNpsAsPraAnesDokter'] : "",
      'sedang_hamil' => isset($post['sdgHmlAsPraAnesDokter']) ? $post['sdgHmlAsPraAnesDokter'] : "",
      'menderita_infeksi' => isset($post['baruSjAsPraAnesDokter']) ? $post['baruSjAsPraAnesDokter'] : "",
      'tulang_belakang' => isset($post['lainTlgAsPraAnesDokter']) ? $post['lainTlgAsPraAnesDokter'] : "",
      'saluran_napas' => isset($post['slrnNpsAsPraAnesDokter']) ? $post['slrnNpsAsPraAnesDokter'] : "",
      'obesitas' => isset($post['obesitasAsPraAnesDokter']) ? $post['obesitasAsPraAnesDokter'] : "",
      'sakit_dada' => isset($post['sktDadaAsPraAnesDokter']) ? $post['sktDadaAsPraAnesDokter'] : "",
      'ket_kajian_sistem' => isset($post['jelaskanPenyakitKeluargaAsPraAnesDokter']) ? $post['jelaskanPenyakitKeluargaAsPraAnesDokter'] : "",
      'skor_mallampati' => isset($post['skor_mallampati']) ? $post['skor_mallampati'] : "",
      'gigi_palsu' => isset($post['gigi_palsu']) ? $post['gigi_palsu'] : "",
      'jantung' => isset($post['jantung']) ? $post['jantung'] : "",
      'paru_paru' => isset($post['paru_paru']) ? $post['paru_paru'] : "",
      'abdomen' => isset($post['abdomen']) ? $post['abdomen'] : "",
      'tulang_belakang_umum' => isset($post['tulang_belakang']) ? $post['tulang_belakang'] : "",
      'ekstremitas' => isset($post['ekstremitas']) ? $post['ekstremitas'] : "",
      'neurologi' => isset($post['neurologi']) ? $post['neurologi'] : "",
      'ket_keadaan_umum' => isset($post['keteranganKeadaanUmum']) ? $post['keteranganKeadaanUmum'] : "",
      'hb_ht' => isset($post['hbHt']) ? $post['hbHt'] : "",
      'leukosit' => isset($post['leukosit']) ? $post['leukosit'] : "",
      'pt' => isset($post['pt']) ? $post['pt'] : "",
      'trombosit' => isset($post['trombosit']) ? $post['trombosit'] : "",
      'glukosa_darah' => isset($post['glukosaDarah']) ? $post['glukosaDarah'] : "",
      'rontgen_dada' => isset($post['rontgenDada']) ? $post['rontgenDada'] : "",
      'tes_kehamilan' => isset($post['tesKehamilan']) ? $post['tesKehamilan'] : "",
      'ekg' => isset($post['ekg']) ? $post['ekg'] : "",
      'kalium' => isset($post['kalium']) ? $post['kalium'] : "",
      'naci' => isset($post['naCi']) ? $post['naCi'] : "",
      'ureum' => isset($post['ureum']) ? $post['ureum'] : "",
      'kreatinin' => isset($post['kreatinin']) ? $post['kreatinin'] : "",
      'ket_lab' => isset($post['keteranganLaboratorium']) ? $post['keteranganLaboratorium'] : "",
      'diagnosis_1' => isset($post['diagnosis1']) ? $post['diagnosis1'] : "",
      'diagnosis_2' => isset($post['diagnosis2']) ? $post['diagnosis2'] : "",
      'asa_class' => isset($post['asaClassAsPraAnesDokter']) ? json_encode($post['asaClassAsPraAnesDokter']) : "",
      'penyulit_anestesia_1' => isset($post['penyulit1']) ? $post['penyulit1'] : "",
      'penyulit_anestesia_2' => isset($post['penyulit2']) ? $post['penyulit2'] : "",
      'cat_tindak_lanjut' => isset($post['catatanTindakLanjut']) ? $post['catatanTindakLanjut'] : "",
      'teknik_sedasi' => isset($post['sedasiAsPraAnesDokter']) ? $post['sedasiAsPraAnesDokter'] : "",
      'teknik_ga' => isset($post['gaAsPraAnesDokter']) ? $post['gaAsPraAnesDokter'] : "",
      'teknik_regional' => isset($post['reionalAsPraAnesDokter']) ? $post['reionalAsPraAnesDokter'] : "",
      'teknik_lainlain' => isset($post['lainAsPraAnesDokter']) ? $post['lainAsPraAnesDokter'] : "",
      'tekkhusus_pilih' => isset($post['tknkKhuAsPraAnesDokter']) ? $post['tknkKhuAsPraAnesDokter'] : "",
      'tekkhusus_lain' => isset($post['lainTknkKhuAsPraAnesDokter']) ? $post['lainTknkKhuAsPraAnesDokter'] : "",
      'monitoring_pilih' => isset($post['monitoringAsPraAnesDokter']) ? json_encode($post['monitoringAsPraAnesDokter']) : "",
      'monitoring_penjelasan' => isset($post['deskMonitoringAsPraAnesDokter']) ? $post['deskMonitoringAsPraAnesDokter'] : "",
      'alat_khu' => isset($post['altKhsusAsPraAnesDokter']) ? $post['altKhsusAsPraAnesDokter'] : "",
      'alat_khu_lain' => isset($post['lainAltKhususAsPraAnesDokter']) ? $post['lainAltKhususAsPraAnesDokter'] : "",
      'perawatan_pilih' => isset($post['prwtnPscAsPraAnesDokter']) ? $post['prwtnPscAsPraAnesDokter'] : "",
      'perawatan_khusus' => isset($post['lainrwtKhususAsPraAnesDokter']) ? $post['lainrwtKhususAsPraAnesDokter'] : "",
      'perawatan_aps' => isset($post['lainAPSAsPraAnesDokter']) ? $post['lainAPSAsPraAnesDokter'] : "",
      'perawatan_lain' => isset($post['lainlainAsPraAnesDokter']) ? $post['lainlainAsPraAnesDokter'] : "",
      'puasa_mulai' => $puasaMliAsPraAnesDokter,
      'pre_medikasi' => $preMedikasiAsPraAnesDokter,
      'transportasi_kamar' => $transportasiAsPraAnesDokter,
      'rencana_operasi' => $rencanaOperasiAsPraAnesDokter,
      'cat_persiapan_pra' => isset($post['cttnPersiapanAsPraAnesDokter']) ? $post['cttnPersiapanAsPraAnesDokter'] : "",
      'oleh' => $oleh,
    );

    if(!empty($post['idaspraanesdokter']))
    {
      $this->db->where('id', $post['idaspraanesdokter']);
      $this->db->update('medis.tb_assesmen_pra_anestesi_dokter', $data);

      $dataTbBb = array(
      'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0 ,
      'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
      'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
      'oleh' => $this->session->userdata('id'),
    );

    $dataTandaVital = array(
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
        );

    $this->db->where('ref', $post['idaspraanesdokter']);
    $this->db->where('data_source', 35);
      $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);

      $this->db->where('ref', $post['idaspraanesdokter']);
      $this->db->where('data_source', 35);
      $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);

    }else{
      $getIdAsPraAnes = $this->AsPraAnesModel->simpanAsPraAnesDokter($data);

      $dataTbBb = array(
      'data_source' => 35,
      'ref' => $getIdAsPraAnes,
      'nomr' => isset($post['norm']) ? $post['norm'] : "",
      'nokun' => $post['nokun'],
      'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0 ,
      'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
      'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
    );

    $dataTandaVital = array(
          'data_source' => 35,
          'ref' => $getIdAsPraAnes,
          'nomr' => isset($post['norm']) ? $post['norm'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

    $getIdTbBb = $this->AsPraAnesModel->simpanTbBb($dataTbBb);
    $getIdTandaVital = $this->AsPraAnesModel->simpanTandaVital($dataTandaVital);
    $data = array(
      'id_tb_bb'    => $getIdTbBb,
      'id_tanda_vital'    => $getIdTandaVital
    );

      $this->db->where('id', $getIdAsPraAnes);
      $this->db->update('medis.tb_assesmen_pra_anestesi_dokter', $data);
    }

  }

}