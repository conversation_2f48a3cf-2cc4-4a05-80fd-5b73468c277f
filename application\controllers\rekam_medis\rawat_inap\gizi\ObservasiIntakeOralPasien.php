<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ObservasiIntakeOralPasien extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/gizi/ObservasiIntakeOralPasienModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_intake = $this->uri->segment(3);
      
      $data = array(
        'nokun' => $nokun,
        'id_intake' => $id_intake,
        'getPengkajian' => $this->ObservasiIntakeOralPasienModel->getPengkajian($id_intake),
        'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
        'listPegawai' => $this->masterModel->listAllPegawai(),
        'listDiet' => $this->masterModel->referensi(56),
        'listJenisDiet' => $this->masterModel->referensi(57),
        'listPorsi' => $this->masterModel->referensi(1638),
      );
      $this->load->view('rekam_medis/rawat_inap/gizi/observasiIntakeOralPasien',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah' || $param == 'ubah'){
                    $post = $this->input->post();

                    $data = array(
                        'nokun' => $post['nokun'],

                        //DIET
                        'diet' => $post['dietIntake'],
                        'jenis_diet' => $post['jenisDietIntake'],
                        'diet_lainnya' => $post['dietLainnyaIntake'],

                        //MAKAN PAGI
                        'makan_pagi' => $post['makan_pagi'],
                        'porsi_pagi' => $post['porsi_pagi'],
                        'kal_pagi' => $post['kal_pagi'],
                        'snack_pagi' => $post['snack_pagi'],
                        'porsi_snack_pagi' => $post['porsi_snack_pagi'],
                        'kal_snack_pagi' => $post['kal_snack_pagi'],

                        //MAKAN SIANG
                        'makan_siang' => $post['makan_siang'],
                        'porsi_siang' => $post['porsi_siang'],
                        'kal_siang' => $post['kal_siang'],
                        'snack_siang' => $post['snack_siang'],
                        'porsi_snack_siang' => $post['porsi_snack_siang'],
                        'kal_snack_siang' => $post['kal_snack_siang'],

                        //MAKAN MALAM
                        'makan_malam' => $post['makan_malam'],
                        'porsi_malam' => $post['porsi_malam'],
                        'kal_malam' => $post['kal_malam'],
                        'snack_malam' => $post['snack_malam'],
                        'porsi_snack_malam' => $post['porsi_snack_malam'],
                        'kal_snack_malam' => $post['kal_snack_malam'],

                        'oleh' => $this->session->userdata("id"),
                        'status_observasi' => 1
                    );

                    $dataUbah = array(
                        'nokun' => $post['nokun'],

                        //DIET
                        'diet' => isset($post['dietIntake']) ? $post['dietIntake']: "",
                        'jenis_diet' => isset($post['jenisDietIntake']) ? $post['jenisDietIntake']: "",
                        'diet_lainnya' => isset($post['dietLainnyaIntake']) ? $post['dietLainnyaIntake']: "",

                        //MAKAN PAGI
                        'makan_pagi' => isset($post['makan_pagi']) ? $post['makan_pagi']: "",
                        'porsi_pagi' => isset($post['porsi_pagi']) ? $post['porsi_pagi']: "",
                        'kal_pagi' => isset($post['kal_pagi']) ? $post['kal_pagi']: "",
                        'snack_pagi' => isset($post['snack_pagi']) ? $post['snack_pagi']: "",
                        'porsi_snack_pagi' => isset($post['porsi_snack_pagi']) ? $post['porsi_snack_pagi']: "",
                        'kal_snack_pagi' => isset($post['kal_snack_pagi']) ? $post['kal_snack_pagi']: "",

                        //MAKAN SIANG
                        'makan_siang' => isset($post['makan_siang']) ? $post['makan_siang']: "",
                        'porsi_siang' => isset($post['porsi_siang']) ? $post['porsi_siang']: "",
                        'kal_siang' => isset($post['kal_siang']) ? $post['kal_siang']: "",
                        'snack_siang' => isset($post['snack_siang']) ? $post['snack_siang']: "",
                        'porsi_snack_siang' => isset($post['porsi_snack_siang']) ? $post['porsi_snack_siang']: "",
                        'kal_snack_siang' => isset($post['kal_snack_siang']) ? $post['kal_snack_siang']: "",

                        //MAKAN MALAM
                        'makan_malam' => isset($post['makan_malam']) ? $post['makan_malam']: "",
                        'porsi_malam' => isset($post['porsi_malam']) ? $post['porsi_malam']: "",
                        'kal_malam' => isset($post['kal_malam']) ? $post['kal_malam']: "",
                        'snack_malam' => isset($post['snack_malam']) ? $post['snack_malam']: "",
                        'porsi_snack_malam' => isset($post['porsi_snack_malam']) ? $post['porsi_snack_malam']: "",
                        'kal_snack_malam' => isset($post['kal_snack_malam']) ? $post['kal_snack_malam']: "",

                        'oleh' => $this->session->userdata("id")
                    );

                    $this->db->trans_begin();
                   
                    if (!empty($post['id_intake'])) {
                        // echo "<pre>";print_r($dataUbah);echo "</pre>";exit();
                        $this->db->where('keperawatan.tb_observasi_intake_oral_pasien.id', $post['id_intake']);
                        $this->db->update('keperawatan.tb_observasi_intake_oral_pasien', $dataUbah);
                        if ($this->db->trans_status() === false) {
                          $this->db->trans_rollback();
                          $result = array('status' => 'failed');
                      } else {
                          $this->db->trans_commit();
                          $result = array('status' => 'success_simpan');
                      }

                      echo json_encode($result);
                  }else{
                      // echo "<pre>";print_r($data);echo "</pre>";exit();
                      $this->db->insert('keperawatan.tb_observasi_intake_oral_pasien', $data);
                      if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success_simpan');
                    }

                    echo json_encode($result);
                }
            }
        }
    }

    public function datatables(){
        $nomr = $this->input->post('nomr');
        $result = $this->ObservasiIntakeOralPasienModel->listHistoryOIOP($nomr);

        $data = array();
        foreach ($result->result() as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editObservasiIntakeOralPasien" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
            $sub_array[] = $row -> diet;
            $sub_array[] = $row -> NAMA;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $result->num_rows(),
            "recordsFiltered"   => $result->num_rows(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}