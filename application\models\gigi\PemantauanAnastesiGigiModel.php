<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PemantauanAnastesiGigiModel extends CI_Model{

    function listHistory($norm)
    {
        $this->db->select('pag.*
        , master.getNama<PERSON><PERSON>kap<PERSON>i(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP');
        $this->db->from('keperawatan.tb_pemantauan_anastesi_gigi pag');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = pag.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = pag.oleh','LEFT');
        $this->db->where('pag.status !=','0');
        $this->db->where('p.NORM', $norm);
        $this->db->order_by('pag.created_at', 'DESC');

        // $this->db->group_start();
        // $this->db->like('mb.KODE_SIMAK', $_POST['search']['value']);
        // $this->db->or_like("mb.NAMA_BARANG", $_POST['search']['value']);
        // $this->db->group_end();
    }

    function datatablesHistory($norm){
        $this->listHistory($norm);
        if($_POST["length"] != -1){
          $this->db->limit($_POST["length"], $_POST["start"]);
        }
        $query = $this->db->get();
        return $query->result();
    }

    function filter_count($norm){
        $this->listHistory($norm);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function total_count($norm){
        $this->listHistory($norm);
        return $this->db->count_all_results();
    }

    function getData($id){
        $hasil = "SELECT * FROM keperawatan.tb_pemantauan_anastesi_gigi pag
        WHERE pag.id = ?";
        $bind = $this->db->query($hasil, array($id));
        return $bind->row_array();
    }

    function getDataFsikologi($id){
        $hasil = "SELECT * FROM keperawatan.tb_pemantauan_anastesi_gigi_fsikologi pagf
        WHERE pagf.id_pag = ?";
        $bind = $this->db->query($hasil, array($id));
        return $bind->result_array();
    }

}
