<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BuktiPelayananRJModel extends CI_Model {

    public function simpanBuktiPelayananRJ($data)
  {
    $this->db->insert('medis.tb_bukti_pelayanan_rj', $data);
  }

  	function batch_icd10($data){
		return $this->db->insert_batch('medis.tb_bukti_pelayanan_rj_icd10', $data);
	}

	function batch_icd9($data){
		return $this->db->insert_batch('medis.tb_bukti_pelayanan_rj_icd9', $data);
	}

	public function batch_delete_icd10($id){
		$this->db->where_in('id_detail_icd10', $id);
		$this->db->delete('medis.tb_bukti_pelayanan_rj_icd10');
  	}

	public function batch_delete_icd9($id){
		$this->db->where_in('id_detail_icd9', $id);
		$this->db->delete('medis.tb_bukti_pelayanan_rj_icd9');
  	}

  public function updateBuktiPelayananRJ($data,$id)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_bukti_pelayanan_rj', $data);
  }

    public function icd10()
	{
		$q = $this->input->get('q');
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD10_1998' AND (CODE LIKE '%". $q ."%' OR STR LIKE '%". $q ."%')
			GROUP BY CODE LIMIT 10");

		return $query->result();
	}

	public function icd10_new()
	{
		$q = $this->input->get('q');
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD10_1998' AND (CODE LIKE '%". $q ."%' OR STR LIKE '%". $q ."%')
			GROUP BY CODE LIMIT 10");

		return $query->result_array();
	}

    public function icd9()
	{
		$q = $this->input->get('q');
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD9CM_2005' AND (CODE LIKE '%". $q ."%' OR STR LIKE '%". $q ."%')
			GROUP BY CODE LIMIT 10");

		return $query->result();
	}

	public function icd9_new()
	{
		$q = $this->input->get('q');
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD9CM_2005' AND (CODE LIKE '%". $q ."%' OR STR LIKE '%". $q ."%')
			GROUP BY CODE LIMIT 10");

		return $query->result_array();
	}

	public function listHistoryBuktiPelayananRJ($nomr)
  {
    $query = $this->db->query("SELECT
								pp.NORM,
								bprj.id,
								bprj.nokun,
								master.getNamaLengkapPegawai ( ap.NIP ) OLEH,
								master.getNamaLengkapPegawai ( md.NIP ) DOKTERPELAKSANA,
								bprj.created_at tanggal,
								bprj.status
							FROM
								medis.tb_bukti_pelayanan_rj bprj
								LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = bprj.nokun
								LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
								LEFT JOIN aplikasi.pengguna ap ON ap.ID = bprj.oleh
								LEFT JOIN master.dokter md ON md.ID = bprj.dokter 
							WHERE
								pp.NORM = '$nomr'
								AND bprj.`status` = 1");
    return $query;
  }

  public function getBuktiPelayananRJ($id)
  {
    $query = $this->db->query("SELECT
								bprj.*,
								master.getNamaLengkapPegawai ( md.NIP ) DOKTERPELAKSANA,
								bprj.created_at tanggal 
							FROM
								medis.tb_bukti_pelayanan_rj bprj
								LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = bprj.nokun
								LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
								LEFT JOIN aplikasi.pengguna ap ON ap.ID = bprj.oleh
								LEFT JOIN master.dokter md ON md.ID = bprj.dokter 
							WHERE
								bprj.id = $id
								AND bprj.`status` = 1");
    return $query->row_array();
  }

  public function geticd10($id)
  {
    $query = $this->db->query("SELECT
				bprjicd10.id_detail_icd10,
				bprjicd10.bukti_pelayanan_rj_fk buktiicd10,
				bprjicd10.tindakan_icd10,
				mrc.CODE,
				mrc.STR
			FROM
				medis.tb_bukti_pelayanan_rj bprj
			LEFT JOIN medis.tb_bukti_pelayanan_rj_icd10 bprjicd10 ON bprjicd10.bukti_pelayanan_rj_fk = bprj.id	
			LEFT JOIN master.mrconso mrc ON mrc.CODE = bprjicd10.tindakan_icd10 AND mrc.SAB = 'ICD10_1998'
			WHERE
				bprj.id = $id
			AND bprj.`status` = 1
			GROUP BY mrc.CODE");
    return $query->result_array();
  }

  public function geticd9($id)
  {
    $query = $this->db->query("SELECT
						bprjicd9.id_detail_icd9,
						bprjicd9.bukti_pelayanan_rj_fk bukticd9,
						bprjicd9.tindakan_icd9,
						mrc.CODE,
						mrc.STR
					FROM
						medis.tb_bukti_pelayanan_rj bprj
					LEFT JOIN medis.tb_bukti_pelayanan_rj_icd9 bprjicd9 ON bprjicd9.bukti_pelayanan_rj_fk = bprj.id
					LEFT JOIN master.mrconso mrc ON mrc.CODE = bprjicd9.tindakan_icd9 AND mrc.SAB = 'ICD9CM_2005'
					WHERE
						bprj.id = $id
						AND bprj.`status` = 1
					GROUP BY mrc.CODE");
    return $query->result_array();
  }
}

/* End of file PT_PengobatanKemoterapiModel.php */
/* Location: ./application/models/informedConsent/PT_PengobatanKemoterapiModel.php */
