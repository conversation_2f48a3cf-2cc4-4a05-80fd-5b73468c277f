<div class="row">
	<div class="col-sm-12">
		<h4 class="page-title">Data Stock Opname</h4>
	</div>
</div>
<div class="row">
	<div class="col-12">
		<div class="card-box">
			<div class="well">
				<button type="button" id="btn-tambah" data-toggle="modal" data-target="#form-modal" class="btn btn-sm btn-info pull-right">
					<span class="far fa-credit-card"></span> Buat SO
				</button>
			</div>
			<table class="table table-striped table-bordered table-hover" id="example">
				<thead>
					<tr>
						<th>No.</th>
						<th>Periode SO</th>
						<th>Tanggal Dibuat</th>
						<th>Gudang</th>
						<th>Aksi</th>
					</tr>
				</thead>
				<tbody>
					<?php
					$no = 1;
					foreach ($dataso as $data) {
						$dataid = $data->ID;
					?>
						<tr>
							<th><?php echo $no++; ?></th>
							<td><?php echo $data->TANGGAL; ?></td>
							<td><?php echo $data->TGL_BUAT; ?></td>
							<td><?php echo $data->GUDANG; ?></td>
							<td width="310px" style="text-align: center; vertical-align: middle;">
								<?php if ($data->STATUS == 'Proses') { ?>
									<a href="<?php echo site_url('inventory/StokOpname/barang_so/' . $data->ID) ?>" class="btn btn-icon waves-effect waves-light btn-info btn-sm"><i class="fas fa-expand"></i> Proses</a>
									<a href="<?php echo site_url('inventory/StokOpname/hapus/' . $data->ID) ?>" class="btn btn-icon waves-effect waves-light btn-danger btn-sm"><i class="fas fa-trash"></i> Hapus</a>
								<?php } else { ?>
									<button type="button" class="btn btn-warning btn-sm waves-effect width-md waves-light"><i class="far fa-check-square"></i> Final</button>
									<button type="button" class="btn btn-success btn-sm waves-effect width-md waves-light" data-toggle="modal" data-target="#viewso" data-id=<?php echo $data->ID ?>><i class="fas fa-eye"></i> Lihat</button>
									<!--<a href="<?php echo site_url('inventory/StokOpname/batal/' . $data->ID) ?>" class="btn btn-icon waves-effect waves-light btn-danger btn-sm"><i class="fas fa-window-close"></i> Batal Final</a>-->
									<a href="<?php echo site_url('inventory/StokOpname/hapus/' . $data->ID) ?>" class="btn btn-icon waves-effect waves-light btn-danger btn-sm"><i class="fas fa-trash"></i> Hapus</a>
								<?php } ?>
							</td>
						</tr>
					<?php
					}
					?>
				</tbody>
			</table>

			<!-- Star Modal Creat SO -->
			<div id="form-modal" class="modal fade">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<span>Form buat SO</span>
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
							<h4 class="modal-title">
								<!-- Beri id "modal-title" untuk tag span pada judul modal -->
								<span id="modal-title"></span>
							</h4>
						</div>
						<div class="modal-body">
							<form action="<?php echo site_url('inventory/StokOpname/buat_so'); ?>" method="post">
								<div class="form-group row">
									<div class="col-6">
										<label class="col-sm-6  col-form-label" for="example-placeholder">Periode SO</label>
										<div class="col-sm-12">
											<div class="input-group">
												<input type="text" data-date-format='yyyy-mm-dd' id='tanggal' name="TANGGAL" class="form-control" autocomplete="off" required>
												<div class="input-group-append">
													<span class="input-group-text"><i class="fa fa-calendar"></i></span>
												</div>
											</div><!-- input-group -->
										</div>
									</div>
									<div class="col-6">
										<label class="col-sm-6  col-form-label">Gudang</label>
										<div class="col-sm-12">
											<select class="form-control select2" id="gudang" name="RUANGAN" required>
												<option value="">Pilih Gudang</option>
												<?php foreach ($gudang as $k) {
													echo "<option value='$k->ID'>$k->DESKRIPSI</option>";
												} ?>
											</select>
										</div>
									</div>
								</div>
								<div class="modal-footer">
									<button type="submit" class="btn btn-sm btn-primary"><i class="fas fa-save"></i> Create So</button>
									<button type="button" class="btn btn-sm btn-success" data-dismiss="modal">Tutup</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
			<!-- End Modal Creat SO -->
			<!-- Modal detil SO -->
			<div class="modal fade" id="viewso" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title mt-0" id="mySmallModalLabel">Detail SO</h4>
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
						</div>
						<div class="modal-body">
							<div id="viewdetailso"></div>
						</div>
					</div>
				</div>
			</div>
			<!-- End Modal detil SO -->
		</div>
	</div>
</div><!-- div row-->
<style>
	#tanggal {
		z-index: 1151;
	}
</style>
<script>
	$(document).ready(function() {
		jQuery('#tanggal').datepicker({
			autoclose: true,
			todayHighlight: true,
			dateFormat: 'yy-mm-dd',
		});
	});
</script>
<script>
	$(document).ready(function() {
		$('#example').DataTable({
			responsive: true
		});
	});
</script>
<script type="text/javascript">
	$('#viewso').on('show.bs.modal', function(e) {
		var id = $(e.relatedTarget).data('id');
		$.ajax({
			type: 'POST',
			url: '<?php echo base_url() ?>inventory/StokOpname/datamodal',
			data: {
				id: id
			},
			success: function(data) {
				$('#viewdetailso').html(data);
			}
		});
	});
</script>