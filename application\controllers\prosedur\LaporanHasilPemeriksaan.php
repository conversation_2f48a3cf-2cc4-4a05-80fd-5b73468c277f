<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanHasilPemeriksaan extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $nomr = $getNomr['NORM'];
        $statusPengguna = $_SESSION['status'];
        $id_pengguna = $this->session->userdata('id');
        $olehDok = $this->session->userdata('iddokter');
        $historyLaporanHasilPemeriksaan = $this->pengkajianAwalModel->historyLaporanHasilPemeriksaan($nomr);
        $dataTindakanProsedur = array();

        $listDrUmum = $this->db->select(
            'dok.ID AS ID_DOKTER, 
             pen.ID AS ID_PENGGUNA, 
             master.getNamaLengkapPegawai(dok.NIP) AS DOKTER, 
             p.SMF AS ID_SMF, 
             smf.DESKRIPSI AS SMF'
        )
        ->from('master.dokter dok')
        ->join('master.pegawai p', 'p.NIP = dok.NIP', 'left')
        ->join('aplikasi.pengguna pen', 'pen.NIP = dok.NIP', 'left')
        ->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left')
        ->where('dok.STATUS', 1)
        ->get()
        ->result_array();   

        // Query untuk mengambil data tindakan penunjang (judul/optgroup)
        $this->db->select('*');
        $this->db->from('master.referensi_tindakan_penunjang mf');
        $this->db->where('mf.jenis_penunjang', '105060101'); // Jenis penunjang
        $this->db->where('mf.parent', 0); // Parent = 0 (kategori utama)
        $this->db->where('mf.status !=', 0); // Status tidak sama dengan 0
        $this->db->order_by('mf.urutan', 'ASC');
        $resultTindakanProsedur = $this->db->get();

        // Loop melalui hasil query
        foreach ($resultTindakanProsedur->result() as $tindakanProsedur) {
            $jenisTindakan = array();
            $jenisTindakan['id'] = $tindakanProsedur->id;
            $jenisTindakan['jenis'] = $tindakanProsedur->deskripsi;

            // Query untuk mengambil sub-tindakan
            $this->db->select('*');
            $this->db->from('master.referensi_tindakan_penunjang mf');
            $this->db->where('mf.jenis_penunjang', '105060101'); // Jenis penunjang
            $this->db->where('mf.parent', $tindakanProsedur->id); // Parent = id kategori utama
            $this->db->where('mf.status !=', 0);
            $this->db->where('mf.tindakan !=', 0);
            $this->db->order_by('mf.urutan', 'ASC');
            $resultTindakanProsedurSub = $this->db->get();

            $subJenisTindakan = array();

            // Loop melalui hasil query sub-tindakan
            foreach ($resultTindakanProsedurSub->result() as $subTindakanProsedur) {
                $subsubJenisTindakan = array();
                $subsubJenisTindakan['id'] = $subTindakanProsedur->id;
                $subsubJenisTindakan['tindakan'] = $subTindakanProsedur->tindakan;
                $subsubJenisTindakan['jenis'] = $subTindakanProsedur->deskripsi;
                $subJenisTindakan[] = $subsubJenisTindakan;
            }

            $jenisTindakan['subJenis'] = $subJenisTindakan;
            $dataTindakanProsedur[] = $jenisTindakan;
        }

        // Data untuk dikirim ke view
        $data = [
            'nomr' => $nomr,
            'nokun' => $nokun,
            'getNomr' => $getNomr,
            'id_pengguna' => $id_pengguna,
            'statusPengguna' => $statusPengguna,
            'olehDok' => $olehDok,
            'dataTindakanProsedur' => $dataTindakanProsedur,
            'historyLaporanHasilPemeriksaan' => $historyLaporanHasilPemeriksaan,
            'listDrUmum'  => $listDrUmum
        ];

        // Load view
        $this->load->view('Pengkajian/laporanHasilPemeriksaan/index', $data);
    }

    public function simpanFormLaporanHasilPemeriksaan()
    {
        // Ambil data dari POST
        $kunjungan = $this->input->post("nokun");
        $tanggaltindakan = $this->input->post("tanggaltindakan");
        $jenispemeriksaan = $this->input->post("jenispemeriksaan"); // Array ID tindakan
        $deskripsiTindakan = $this->input->post("deskripsiTindakan"); // JSON string
        $ketklinis = $this->input->post("ketklinis");
        $indikasitindakan = $this->input->post("indikasitindakan");
        $hasilpemeriksaan = $this->input->post("hasilpemeriksaan");
        $hasilkesimpulan = $this->input->post("hasilkesimpulan");
        $anjuran = $this->input->post("anjuran");
        $id_ruangan = $this->input->post("id_ruangan");
        $statusPengguna = $_SESSION['status'];
        $id_pengguna = $this->input->post('id_pengguna');
        $backdate = $this->input->post("backdate") ?? NULL;
        $oleh = $_SESSION['id'];

        // Validasi data
        if (empty($kunjungan) || empty($tanggaltindakan) || empty($jenispemeriksaan) || empty($deskripsiTindakan)) {
            echo json_encode(['status' => 'failed', 'message' => 'Data tidak lengkap']);
            return;
        }

        // Decode JSON deskripsiTindakan
        $deskripsiTindakan = json_decode($deskripsiTindakan, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo json_encode(['status' => 'failed', 'message' => 'Data deskripsi tindakan tidak valid']);
            return;
        }

        // Konversi tanggal ke format database
        $tanggaltindakan = DateTime::createFromFormat('m/d/Y', $tanggaltindakan);
        if (!$tanggaltindakan) {
            echo json_encode(['status' => 'failed', 'message' => 'Format tanggal tidak valid']);
            return;
        }
        $tanggaltindakan = $tanggaltindakan->format('Y-m-d');

        // Gabungkan deskripsi tindakan
        $namaTindakanArray = [];
        foreach ($deskripsiTindakan as $tindakan) {
            $namaTindakanArray[] = $tindakan['deskripsi'];
        }
        $namaTindakanString = implode(', ', $namaTindakanArray);

        // Gabungkan ID tindakan
        $jenispemeriksaanString = implode(',', $jenispemeriksaan); // Hasil: "451,2120"

        // Data untuk disimpan ke tb_cppt
        $dataCppt = array(
            'nokun' => $kunjungan,
            'pemberi_cppt' => $statusPengguna,
            'subyektif' => $ketklinis,
            'obyektif' => $ketklinis,
            'analisis' => $indikasitindakan,
            'perencanaan' => $hasilpemeriksaan . ' ' . $hasilkesimpulan,
            'instruksi' => $anjuran,
            'jenis' => 1,
            'oleh' => $id_pengguna,
        );

        // Mulai transaksi database
        $this->db->trans_begin();

        if ($id_ruangan == 105060101 && intval($backdate) != 1) {
            // Insert ke tabel tb_cppt
            $this->db->insert('keperawatan.tb_cppt', $dataCppt);
            if ($this->db->affected_rows() == 0) {
                $this->db->trans_rollback();
                echo json_encode(['status' => 'failed', 'message' => 'Gagal menyimpan data CPPT']);
                return;
            }
        
            // Ambil ID dari data yang baru saja diinsert ke tb_cppt
            $id_cppt = $this->db->insert_id();
        } else {
            $id_cppt = null;
        }        

        // Data untuk disimpan ke tb_laporan_hasil_pemeriksaan
        $dataLaporan = array(
            'nokun' => $kunjungan,
            'dokter_pemeriksa' => $this->input->post("dokter_pemeriksa"),
            'tanggal_tindakan' => $tanggaltindakan,
            'jenis_pemeriksaan_id' => $jenispemeriksaanString,
            'jenis_pemeriksaan' => $namaTindakanString,
            'ket_klinis' => $ketklinis,
            'indikasi_tindakan' => $indikasitindakan,
            'hasilpemeriksaan' => $hasilpemeriksaan,
            'kesimpulan' => $hasilkesimpulan,
            'anjuran' => $anjuran,
            'id_cppt' => $id_cppt,
            'oleh' => $this->session->userdata('id')
        );

        // Insert ke tabel tb_laporan_hasil_pemeriksaan
        $this->db->insert('keperawatan.tb_laporan_hasil_pemeriksaan', $dataLaporan);
        if ($this->db->affected_rows() == 0) {
            $this->db->trans_rollback();
            echo json_encode(['status' => 'failed', 'message' => 'Gagal menyimpan laporan hasil pemeriksaan']);
            return;
        }

        // Commit transaksi
        $this->db->trans_commit();
        echo json_encode(['status' => 'success']);
    }

    public function ubahFormLaporanHasilPemeriksaan()
    {
        // Ambil data dari POST
        $id = $this->input->post("id_laporanhasilpemeriksaan");
        $id_cppt = $this->input->post("id_cppt");
        $kunjungan = $this->input->post("nokun");
        $tanggaltindakan = $this->input->post("tanggaltindakan");
        if (DateTime::createFromFormat('Y-m-d H:i:s', $tanggaltindakan) !== false || 
            DateTime::createFromFormat('Y-m-d', $tanggaltindakan) !== false) {
            $tanggaltindakan = date('Y-m-d H:i:s', strtotime($tanggaltindakan));
        } else {
            $tanggaltindakan = DateTime::createFromFormat('m/d/Y', $tanggaltindakan)->format('Y-m-d H:i:s');
        }
        $jenispemeriksaan = $this->input->post("jenispemeriksaan"); // Array jika dari select2
        $ketklinis = $this->input->post("ketklinis");
        $indikasitindakan = $this->input->post("indikasitindakan");
        $hasilpemeriksaan = $this->input->post("hasilpemeriksaan");
        $hasilkesimpulan = $this->input->post("hasilkesimpulan");
        $anjuran = $this->input->post("anjuran");
        $deskripsiTindakan = json_decode($this->input->post("deskripsiTindakan"), true); // Decode JSON dari select2
        $jenispemeriksaanTextarea = $this->input->post("jenispemeriksaan_textarea"); // Data dari textarea
        $id_ruangan = $this->input->post("id_ruangan");
        $id_pengguna = $this->input->post('id_pengguna');

        // Cek apakah deskripsiTindakan kosong (jika select2 tidak digunakan)
        if (empty($deskripsiTindakan)) {
            // Gunakan nilai dari textarea sebagai fallback
            if (empty($jenispemeriksaanTextarea)) {
                echo json_encode(['status' => 'failed', 'message' => 'Deskripsi tindakan tidak valid']);
                return;
            }

            // Pecah data textarea menjadi array (jika ada separator seperti koma)
            $namaTindakanArray = array_map('trim', explode(',', $jenispemeriksaanTextarea));
        } else {
            // Gunakan deskripsiTindakan dari JSON (select2)
            $namaTindakanArray = [];
            foreach ($deskripsiTindakan as $tindakan) {
                $namaTindakanArray[] = $tindakan['deskripsi'];
            }
        }

        // Gabungkan nama tindakan menjadi string
        $namaTindakanString = implode(', ', $namaTindakanArray);

        // Jika $jenispemeriksaan kosong, gunakan string kosong sebagai fallback
        $jenispemeriksaanString = is_array($jenispemeriksaan) ? implode(',', $jenispemeriksaan) : '';

        // Data untuk update tb_laporan_hasil_pemeriksaan
        $dataLaporan = array(
            'dokter_pemeriksa' => $this->input->post("dokter_pemeriksa_edit"),
            'nokun' => $kunjungan,
            'tanggal_tindakan' => $tanggaltindakan, // Format sudah sesuai
            'jenis_pemeriksaan' => $namaTindakanString,
            'jenis_pemeriksaan_id' => $jenispemeriksaanString,
            'ket_klinis' => $ketklinis,
            'indikasi_tindakan' => $indikasitindakan,
            'hasilpemeriksaan' => $hasilpemeriksaan,
            'kesimpulan' => $hasilkesimpulan,
            'anjuran' => $anjuran,
        );

        // Data untuk update tb_cppt
        $dataCppt = array(
            'nokun' => $kunjungan,
            'subyektif' => $ketklinis,
            'obyektif' => $ketklinis,
            'analisis' => $indikasitindakan,
            'perencanaan' => $hasilpemeriksaan . ' ' . $hasilkesimpulan,
            'instruksi' => $anjuran,
            'oleh' => $id_pengguna,
        );

        // Mulai transaksi database
        $this->db->trans_begin();

        // Update tb_laporan_hasil_pemeriksaan
        $this->db->where('id', $id);
        $this->db->update('keperawatan.tb_laporan_hasil_pemeriksaan', $dataLaporan);
        if ($this->db->affected_rows() == 0) {
            log_message('error', 'Gagal update tb_laporan_hasil_pemeriksaan: ' . $this->db->last_query());
        }

        if ($id_ruangan == 105060101) {
            // Update tb_cppt
            $this->db->where('id', $id_cppt);
            $this->db->update('keperawatan.tb_cppt', $dataCppt);
            if ($this->db->affected_rows() == 0) {
                log_message('error', 'Gagal update tb_cppt: ' . $this->db->last_query());
            }
        }

        // Commit atau rollback transaksi
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed', 'message' => 'Gagal menyimpan data');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihathistoryLaporanHasilPemeriksaan()
    {
        $id = $this->input->post('id');
        $HistoryLaporanHasilPemeriksaan = $this->pengkajianAwalModel->historyDetailLaporanHasilPemeriksaan($id);

        $nokun = $HistoryLaporanHasilPemeriksaan['nokun'];
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $jenisPemeriksaanId = $HistoryLaporanHasilPemeriksaan['jenis_pemeriksaan_id'];
        $jenisPemeriksaanIdArray = explode(',', $jenisPemeriksaanId);

        $listDrUmum = $this->db->select(
            'dok.ID AS ID_DOKTER, 
             pen.ID AS ID_PENGGUNA, 
             master.getNamaLengkapPegawai(dok.NIP) AS DOKTER, 
             p.SMF AS ID_SMF, 
             smf.DESKRIPSI AS SMF'
        )
        ->from('master.dokter dok')
        ->join('master.pegawai p', 'p.NIP = dok.NIP', 'left')
        ->join('aplikasi.pengguna pen', 'pen.NIP = dok.NIP', 'left')
        ->join('master.referensi smf', 'smf.ID = p.SMF AND smf.JENIS = 26', 'left')
        ->where('dok.STATUS', 1)
        ->get()
        ->result_array();

        $this->db->select('*');
        $this->db->from('master.referensi_tindakan_penunjang mf');
        $this->db->where_in('mf.id', $jenisPemeriksaanIdArray); // Ambil data berdasarkan array ID
        $this->db->where('mf.status !=', 0);
        $this->db->where('mf.tindakan !=', 0);
        $this->db->order_by('mf.urutan', 'ASC');
        $queryTindakanDipilih = $this->db->get();
        $tindakanDipilih = $queryTindakanDipilih->result_array();

        // Ambil semua data tindakan prosedur
        $dataTindakanProsedur = array();

        // Query untuk mengambil data tindakan penunjang (judul/optgroup)
        $this->db->select('*');
        $this->db->from('master.referensi_tindakan_penunjang mf');
        $this->db->where('mf.jenis_penunjang', '105060101'); // Jenis penunjang
        $this->db->where('mf.parent', 0); // Parent = 0 (kategori utama)
        $this->db->where('mf.status !=', 0); // Status tidak sama dengan 0
        $this->db->order_by('mf.urutan', 'ASC');
        $resultTindakanProsedur = $this->db->get();

        // Loop melalui hasil query
        foreach ($resultTindakanProsedur->result() as $tindakanProsedur) {
            $jenisTindakan = array();
            $jenisTindakan['id'] = $tindakanProsedur->id;
            $jenisTindakan['jenis'] = $tindakanProsedur->deskripsi;

            // Query untuk mengambil sub-tindakan
            $this->db->select('*');
            $this->db->from('master.referensi_tindakan_penunjang mf');
            $this->db->where('mf.jenis_penunjang', '105060101'); // Jenis penunjang
            $this->db->where('mf.parent', $tindakanProsedur->id); // Parent = id kategori utama
            $this->db->where('mf.status !=', 0);
            $this->db->where('mf.tindakan !=', 0);
            $this->db->order_by('mf.urutan', 'ASC');
            $resultTindakanProsedurSub = $this->db->get();

            $subJenisTindakan = array();

            // Loop melalui hasil query sub-tindakan
            foreach ($resultTindakanProsedurSub->result() as $subTindakanProsedur) {
                $subsubJenisTindakan = array();
                $subsubJenisTindakan['id'] = $subTindakanProsedur->id;
                $subsubJenisTindakan['tindakan'] = $subTindakanProsedur->tindakan;
                $subsubJenisTindakan['jenis'] = $subTindakanProsedur->deskripsi;
                $subJenisTindakan[] = $subsubJenisTindakan;
            }

            $jenisTindakan['subJenis'] = $subJenisTindakan;
            $dataTindakanProsedur[] = $jenisTindakan;
        }

        // Data untuk dikirim ke view
        $data = array(
            'hasilpemeriksaan' => $HistoryLaporanHasilPemeriksaan,
            'getNomr' => $getNomr,
            'tindakanDipilih' => $tindakanDipilih, // Data tindakan yang dipilih (history)
            'dataTindakanProsedur' => $dataTindakanProsedur, // Semua data tindakan prosedur
            'listDrUmum'  => $listDrUmum
        );

        $this->load->view('Pengkajian/laporanHasilPemeriksaan/viewEditLaporanHasilPemeriksaan', $data);
    }

    public function scan()
    {
        $nokun = $this->uri->segment(6);
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);
        $nomr = $pasien['NORM'];
        $statusPengguna = $_SESSION['status'];
        $id_pengguna = $this->session->userdata('id');
        $olehDok = $this->session->userdata('iddokter');

        // Query untuk mengambil tanggal pemeriksaan
        $this->db->select('lhp.tanggal, lhp.nokun NOKUN, lhp.id, lhp.jenis_pemeriksaan'); // Ganti dengan nama kolom yang benar
        $this->db->from('pendaftaran.pendaftaran pen');
        $this->db->join('pendaftaran.kunjungan kun', 'pen.NOMOR = kun.NOPEN', 'inner');
        $this->db->join('keperawatan.tb_laporan_hasil_pemeriksaan lhp', 'lhp.nokun = kun.NOMOR', 'inner');
        $this->db->where('pen.NORM', $nomr);
        $query = $this->db->get();
        $tanggalPemeriksaan = $query->result_array();

        // Data untuk dikirim ke view
        $data = [
            'nomr' => $nomr,
            'nokun' => $nokun,
            'pasien' => $pasien,
            'id_pengguna' => $id_pengguna,
            'statusPengguna' => $statusPengguna,
            'olehDok' => $olehDok,
            'tanggalPemeriksaan' => $tanggalPemeriksaan,
        ];

        // Load view
        $this->load->view('Pengkajian/laporanHasilPemeriksaan/scan', $data);
    }

    public function getDataHistory()
    {
        $nomr = $this->input->post('nomr');

        $this->db->select('shp.id, shp.id_laporan, shp.nokun, IF(shp.status=1,"Aktif","Batal") status, shp.tanggal, shp.tanggal_upload, master.getNamaLengkapPegawai(peng.NIP) oleh');
        $this->db->from('pendaftaran.pendaftaran pen');
        $this->db->join('pendaftaran.kunjungan kun', 'pen.NOMOR = kun.NOPEN', 'inner');
        $this->db->join('keperawatan.tb_scan_hasil_pemeriksaan shp', 'shp.nokun = kun.NOMOR', 'inner');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = shp.oleh', 'left');
        $this->db->where('pen.NORM', $nomr);
        $this->db->order_by('shp.tanggal_upload', 'DESC'); // Urutkan berdasarkan tanggal upload terbaru
        $this->db->order_by('shp.id', 'DESC'); // Jika tanggal sama, urutkan berdasarkan ID
        $query = $this->db->get();
        $data = $query->result_array();

        // Tambahkan nomor urut (no++) di server-side
        $no = 1;
        $responseData = array_map(function ($row) use (&$no) {
            return array(
                $no++,  // Nomor urut di server-side
                $row['id_laporan'],
                date('d-m-Y H:i:s', strtotime($row['tanggal'])),
                date('d-m-Y H:i:s', strtotime($row['tanggal_upload'])),
                $row['oleh'],
                $row['status'],
                '<a href="#lihathistoryScanHasilPemeriksaan" class="btn btn-sm btn-primary btn-block LoadhistoryScanHasilPemeriksaan" data-toggle="modal" data-idshp="' . $row['id'] . '" data-backdrop="static" data-keyboard="false">
                <i class="fa fa-eye"></i> View
            </a>
            <button class="btn btn-sm btn-danger btn-block btn-batal" data-idshp="' . $row['id'] . '">
                <i class="fa fa-trash"></i> Batal
            </button>'
            );
        }, $data);

        $response = array(
            "draw" => $this->input->post('draw'),
            "recordsTotal" => count($data),
            "recordsFiltered" => count($data),
            "data" => $responseData
        );

        echo json_encode($response);
    }

    public function simpanFormScanHasilPemeriksaan()
    {
        // Ambil data dari request
        $dataInput = $this->input->post();
        $nokun = $dataInput['nokun'];
        $id_laporan = $dataInput['id_laporan'];
        $tanggal = $dataInput['tanggal']; // Ambil nilai tanggal dari input hidden
        $setupNotes = $dataInput['setupNote'] ?? [];
        $id_pengguna = $this->session->userdata('id');

        // Validasi data
        if (empty($nokun) || empty($tanggal)) {
            echo json_encode(['status' => false, 'message' => 'Nomor kunjungan atau tanggal pemeriksaan tidak boleh kosong.']);
            return;
        }

        // Proses simpan setupNote dan file upload
        $notesData = [];
        $files = $_FILES['setupNoteimage'] ?? [];

        if (!empty($setupNotes)) {
            // Set upload directory
            $upload_dir = '../upload_emr/scan_hasil_pemeriksaan/';

            // Buat direktori jika belum ada
            if (!is_dir($upload_dir)) {
                if (!mkdir($upload_dir, 0755, true)) {
                    echo json_encode(['status' => false, 'message' => 'Gagal membuat direktori upload']);
                    return;
                }
            }

            // Load library upload
            $this->load->library('upload');

            foreach ($setupNotes as $index => $note) {
                $imagePath = null;

                // Jika ada file, upload dan simpan path-nya
                if (!empty($files['name'][$index])) {
                    $fileNameParts = pathinfo($files['name'][$index]);
                    $fileExtension = strtolower($fileNameParts['extension']);
                    $namagambar = 'image_' . date("YmdHis") . '_' . $index . '.' . $fileExtension;

                    $_FILES['file']['name'] = $namagambar;
                    $_FILES['file']['type'] = $files['type'][$index];
                    $_FILES['file']['tmp_name'] = $files['tmp_name'][$index];
                    $_FILES['file']['error'] = $files['error'][$index];
                    $_FILES['file']['size'] = $files['size'][$index];

                    // Konfigurasi upload
                    $config = array(
                        'upload_path' => $upload_dir,
                        'allowed_types' => 'jpg|jpeg|png',
                        'max_size' => 2048, // 2MB
                        'file_name' => $namagambar,
                        'overwrite' => true
                    );

                    $this->upload->initialize($config);

                    if ($this->upload->do_upload('file')) {
                        $uploadData = $this->upload->data();
                        $imagePath = '../upload_emr/scan_hasil_pemeriksaan/' . $uploadData['file_name'];
                    } else {
                        $error = $this->upload->display_errors('', ''); // Hapus tag <p>
                        echo json_encode(['status' => false, 'message' => $error]);
                        return;
                    }
                }

                // Tambahkan data ke array untuk batch insert
                $notesData[] = [
                    'nokun' => $nokun,
                    'tanggal' => $tanggal, // Gunakan nilai tanggal dari input hidden
                    'id_laporan' => $id_laporan,
                    'note' => $note,
                    'image' => $imagePath,
                    'oleh' => $id_pengguna,
                ];
            }

            // Simpan data ke database
            if (!empty($notesData)) {
                $inserted = $this->db->insert_batch('keperawatan.tb_scan_hasil_pemeriksaan', $notesData);

                if ($inserted) {
                    echo json_encode([
                        'status' => true,
                        'message' => 'Data berhasil disimpan.'
                    ]);
                } else {
                    echo json_encode([
                        'status' => false,
                        'message' => 'Terjadi kesalahan saat menyimpan data.'
                    ]);
                }
            } else {
                echo json_encode([
                    'status' => false,
                    'message' => 'Tidak ada data yang akan disimpan.'
                ]);
            }
        } else {
            echo json_encode([
                'status' => false,
                'message' => 'Tidak ada data setup note yang dikirim.'
            ]);
        }
    }

    public function lihathistoryScanHasilPemeriksaan()
    {
        $id = $this->input->post('id');

        // Query untuk mengambil data berdasarkan id
        $this->db->select('shp.id, shp.id_laporan, shp.nokun, shp.tanggal, shp.note, shp.image');
        $this->db->from('keperawatan.tb_scan_hasil_pemeriksaan shp');
        $this->db->where('shp.id', $id);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $historyScanHasilPemeriksaan = $query->row_array();
        } else {
            $historyScanHasilPemeriksaan = null;
        }

        // Kirim data ke view
        $data['historyScanHasilPemeriksaan'] = $historyScanHasilPemeriksaan;
        $this->load->view('Pengkajian/laporanHasilPemeriksaan/viewScanHasilPemeriksaan', $data);
    }

    public function batalkanScanHasilPemeriksaan()
    {
        $id = $this->input->post('id');

        if (empty($id)) {
            echo json_encode(['status' => false, 'message' => 'ID tidak valid.']);
            return;
        }

        $this->db->where('id', $id);
        $updated = $this->db->update('keperawatan.tb_scan_hasil_pemeriksaan', ['status' => 0]);

        if ($updated) {
            echo json_encode([
                'status' => true,
                'message' => 'Data berhasil dibatalkan.'
            ]);
        } else {
            $error = $this->db->error(); // Ambil error dari database
            echo json_encode([
                'status' => false,
                'message' => 'Terjadi kesalahan saat membatalkan data.',
                'error_detail' => $error // Opsional: Tampilkan detail error untuk debugging
            ]);
        }
    }
}