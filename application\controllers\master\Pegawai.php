<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pegawai extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(5,$this->session->userdata('akses')) OR !in_array(6,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('masterModel');
  }

  ///////////////////////////////////////////////////////////// START PEGAWAI ////////////////////////////////////////////////////////////////////////
  public function index()
  {
    $pegawai = $this->masterModel->pegawai();

    $data = array(
      'title'   => 'Halaman Master',
      'isi'     => 'Master/pegawai/index',
      'pegawai' => $pegawai,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function listPegawai()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listPegawai = $this->masterModel->pegawai();

    // echo "<pre>";print_r($listPegawai);exit();
    $data  = array();
    $no    =1;
    foreach($listPegawai->result() as $lp) {

      if($lp->STATUS == 1){
        $check = "checked" ;
      }else{
        $check = "" ;
      }


      $data[] = array(
        $no,
        $lp->NIP,
        $lp->NAMA,
        $lp->TTL,
        $lp->JENIS_KELAMIN_PEGAWAI,
        $lp->ALAMAT,
        $lp->SMF_PEGAWAI,
        '<div class="checkbox checkbox-primary">
        <input type="checkbox" id="statusPegawai'.$lp->NIP.'" value="'.$lp->NIP.'"  class="SPegawai" '.$check.'>
        <label for="statusPegawai'.$lp->NIP.'"></label>
        </div>',
        // '<input type="checkbox">',
        '<a href="#" class="btn btn-sm btn-block btn-primary"><i class="fas fa-edit"></i> Edit</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listPegawai->num_rows(),
      "recordsFiltered" => $listPegawai->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }


  public function SPegawaiAktiv()
  {
    $nip = $this->input->post('nip');

    $data = array(
      'STATUS' => 1,
    );

    $this->masterModel->SPegawaiAktiv($nip,$data);
  }

  public function SPegawaiNonAktiv()
  {
    $nip = $this->input->post('nip');

    $data = array(
      'STATUS' => 0,
    );

    $this->masterModel->SPegawaiNonAktiv($nip,$data);
  }
  ///////////////////////////////////////////////////////////// END PEGAWAI /////////////////////////////////////////////////////////////////////////

}

/* End of file Pegawai.php */
/* Location: ./application/controllers/master/Pegawai.php */
