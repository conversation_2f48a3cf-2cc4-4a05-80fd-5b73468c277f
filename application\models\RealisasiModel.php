<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RealisasiModel extends CI_Model {

  public function dataRealisasi()
  {
    $query  = $this->db->query("SELECT * FROM keuangan.rkakl rkakl");

    return $query->result_array();
  }
  
  public function ruanganRealisasi()
  {
    $query  = $this->db->query("SELECT * FROM master.ruangan mr WHERE mr.JENIS IN(3,4)");

    return $query->result_array();
  }
  
  public function insertRealisasi($data)
  {
    $this->db->insert('keuangan.realisasi_rkakl', $data);
  }

  public function getIdMak($id_mak)
  {
    $query  = $this->db->query("SELECT * FROM keuangan.rkakl rkakl WHERE rkakl.MAK='$id_mak'");
    return $query->row_array();
  }

  public function updateRkaklJmlVlm($id_mak, $data2)
  {
    $this->db->where('MAK', $id_mak);
    $this->db->update('keuangan.rkakl', $data2);
  }

}

/* End of file RealisasiModel.php */
/* Location: ./application/models/RealisasiModel.php */