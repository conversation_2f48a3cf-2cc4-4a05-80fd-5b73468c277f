<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for %s/BankAccount.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="css/bootstrap.min.css" rel="stylesheet">
  <link href="css/style.css" rel="stylesheet">
  <!--[if lt IE 9]>
  <script src="js/html5shiv.min.js"></script>
  <script src="js/respond.min.js"></script>
  <![endif]-->
 </head>
 <body>
  <header>
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <ol class="breadcrumb">
        <li><a href="index.html">%s</a></li>
        <li class="active">BankAccount.php</li>

      </ol>
     </div>
    </div>
   </div>
  </header>
  <div class="container">
   <table class="table table-bordered">
    <thead>
     <tr>
      <td>&nbsp;</td>
      <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
     </tr>
     <tr>
      <td>&nbsp;</td>
      <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
      <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td class="danger">Total</td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">0.00%</div></td>
      <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      <td class="warning big">       <div class="progress">
         <div class="progress-bar progress-bar-warning" role="progressbar" aria-valuenow="75.00" aria-valuemin="0" aria-valuemax="100" style="width: 75.00%">
           <span class="sr-only">75.00% covered (warning)</span>
         </div>
       </div>
</td>
      <td class="warning small"><div align="right">75.00%</div></td>
      <td class="warning small"><div align="right">3&nbsp;/&nbsp;4</div></td>
      <td class="warning small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">50.00%</div></td>
      <td class="danger small"><div align="right">5&nbsp;/&nbsp;10</div></td>
     </tr>

     <tr>
      <td class="danger">BankAccount</td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">0.00%</div></td>
      <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      <td class="warning big">       <div class="progress">
         <div class="progress-bar progress-bar-warning" role="progressbar" aria-valuenow="75.00" aria-valuemin="0" aria-valuemax="100" style="width: 75.00%">
           <span class="sr-only">75.00% covered (warning)</span>
         </div>
       </div>
</td>
      <td class="warning small"><div align="right">75.00%</div></td>
      <td class="warning small"><div align="right">3&nbsp;/&nbsp;4</div></td>
      <td class="warning small">8.12</td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">50.00%</div></td>
      <td class="danger small"><div align="right">5&nbsp;/&nbsp;10</div></td>
     </tr>

     <tr>
      <td class="success" colspan="4">&nbsp;<a href="#6"><abbr title="getBalance()">getBalance</abbr></a></td>
      <td class="success big">       <div class="progress">
         <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
      <td class="success small"><div align="right">100.00%</div></td>
      <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      <td class="success small">1</td>
      <td class="success big">       <div class="progress">
         <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
      <td class="success small"><div align="right">100.00%</div></td>
      <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
     </tr>

     <tr>
      <td class="danger" colspan="4">&nbsp;<a href="#11"><abbr title="setBalance($balance)">setBalance</abbr></a></td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">0.00%</div></td>
      <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      <td class="danger small">6</td>
      <td class="danger big">       <div class="progress">
         <div class="progress-bar progress-bar-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
      <td class="danger small"><div align="right">0.00%</div></td>
      <td class="danger small"><div align="right">0&nbsp;/&nbsp;5</div></td>
     </tr>

     <tr>
      <td class="success" colspan="4">&nbsp;<a href="#20"><abbr title="depositMoney($balance)">depositMoney</abbr></a></td>
      <td class="success big">       <div class="progress">
         <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
      <td class="success small"><div align="right">100.00%</div></td>
      <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      <td class="success small">1</td>
      <td class="success big">       <div class="progress">
         <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
      <td class="success small"><div align="right">100.00%</div></td>
      <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
     </tr>

     <tr>
      <td class="success" colspan="4">&nbsp;<a href="#27"><abbr title="withdrawMoney($balance)">withdrawMoney</abbr></a></td>
      <td class="success big">       <div class="progress">
         <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
      <td class="success small"><div align="right">100.00%</div></td>
      <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      <td class="success small">1</td>
      <td class="success big">       <div class="progress">
         <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
      <td class="success small"><div align="right">100.00%</div></td>
      <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
     </tr>


    </tbody>
   </table>
   <table id="code" class="table table-borderless table-condensed">
    <tbody>
     <tr><td><div align="right"><a name="1"></a><a href="#1">1</a></div></td><td class="codeLine"><span class="default">&lt;?php</span></td></tr>
     <tr><td><div align="right"><a name="2"></a><a href="#2">2</a></div></td><td class="codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">BankAccount</span></td></tr>
     <tr><td><div align="right"><a name="3"></a><a href="#3">3</a></div></td><td class="codeLine"><span class="keyword">{</span></td></tr>
     <tr><td><div align="right"><a name="4"></a><a href="#4">4</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$balance</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">0</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="5"></a><a href="#5">5</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="6"></a><a href="#6">6</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getBalance</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
     <tr><td><div align="right"><a name="7"></a><a href="#7">7</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="covered-by-large-tests popin" data-title="2 tests cover line 8" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testBalanceIsInitiallyZero&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testDepositWithdrawMoney&lt;/li&gt;&lt;/ul&gt;" data-placement="bottom" data-html="true"><td><div align="right"><a name="8"></a><a href="#8">8</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">balance</span><span class="keyword">;</span></td></tr>
     <tr class="warning"><td><div align="right"><a name="9"></a><a href="#9">9</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr><td><div align="right"><a name="10"></a><a href="#10">10</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="11"></a><a href="#11">11</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setBalance</span><span class="keyword">(</span><span class="default">$balance</span><span class="keyword">)</span></td></tr>
     <tr><td><div align="right"><a name="12"></a><a href="#12">12</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="13"></a><a href="#13">13</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$balance</span><span class="default">&nbsp;</span><span class="default">&gt;=</span><span class="default">&nbsp;</span><span class="default">0</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="14"></a><a href="#14">14</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">balance</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$balance</span><span class="keyword">;</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="15"></a><a href="#15">15</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="16"></a><a href="#16">16</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">RuntimeException</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="17"></a><a href="#17">17</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr class="danger"><td><div align="right"><a name="18"></a><a href="#18">18</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr><td><div align="right"><a name="19"></a><a href="#19">19</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="20"></a><a href="#20">20</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">depositMoney</span><span class="keyword">(</span><span class="default">$balance</span><span class="keyword">)</span></td></tr>
     <tr><td><div align="right"><a name="21"></a><a href="#21">21</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="covered-by-large-tests popin" data-title="2 tests cover line 22" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testBalanceCannotBecomeNegative2&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testDepositWithdrawMoney&lt;/li&gt;&lt;/ul&gt;" data-placement="bottom" data-html="true"><td><div align="right"><a name="22"></a><a href="#22">22</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setBalance</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getBalance</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">+</span><span class="default">&nbsp;</span><span class="default">$balance</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="23"></a><a href="#23">23</a></div></td><td class="codeLine"></td></tr>
     <tr class="covered-by-large-tests popin" data-title="1 test covers line 24" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testDepositWithdrawMoney&lt;/li&gt;&lt;/ul&gt;" data-placement="bottom" data-html="true"><td><div align="right"><a name="24"></a><a href="#24">24</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getBalance</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr class="warning"><td><div align="right"><a name="25"></a><a href="#25">25</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr><td><div align="right"><a name="26"></a><a href="#26">26</a></div></td><td class="codeLine"></td></tr>
     <tr><td><div align="right"><a name="27"></a><a href="#27">27</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">withdrawMoney</span><span class="keyword">(</span><span class="default">$balance</span><span class="keyword">)</span></td></tr>
     <tr><td><div align="right"><a name="28"></a><a href="#28">28</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
     <tr class="covered-by-large-tests popin" data-title="2 tests cover line 29" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testBalanceCannotBecomeNegative&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testDepositWithdrawMoney&lt;/li&gt;&lt;/ul&gt;" data-placement="bottom" data-html="true"><td><div align="right"><a name="29"></a><a href="#29">29</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setBalance</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getBalance</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">-</span><span class="default">&nbsp;</span><span class="default">$balance</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr><td><div align="right"><a name="30"></a><a href="#30">30</a></div></td><td class="codeLine"></td></tr>
     <tr class="covered-by-large-tests popin" data-title="1 test covers line 31" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;BankAccountTest::testDepositWithdrawMoney&lt;/li&gt;&lt;/ul&gt;" data-placement="bottom" data-html="true"><td><div align="right"><a name="31"></a><a href="#31">31</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getBalance</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
     <tr class="warning"><td><div align="right"><a name="32"></a><a href="#32">32</a></div></td><td class="codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
     <tr><td><div align="right"><a name="33"></a><a href="#33">33</a></div></td><td class="codeLine"><span class="keyword">}</span></td></tr>

    </tbody>
   </table>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="success"><strong>Executed</strong></span>
     <span class="danger"><strong>Not Executed</strong></span>
     <span class="warning"><strong>Dead Code</strong></span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage %s</a> using <a href="%s" target="_top">%s</a> at %s.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#"><span class="glyphicon glyphicon-arrow-up"></span></a>
   </footer>
  </div>
  <script src="js/jquery.min.js" type="text/javascript"></script>
  <script src="js/bootstrap.min.js" type="text/javascript"></script>
  <script src="js/holder.min.js" type="text/javascript"></script>
  <script type="text/javascript">
  $(function() {
   var $window   = $(window)
     , $top_link = $('#toplink')
     , $body     = $('body, html')
     , offset    = $('#code').offset().top;

   $top_link.hide().click(function(event) {
    event.preventDefault();
    $body.animate({scrollTop:0}, 800);
   });

   $window.scroll(function() {
    if($window.scrollTop() > offset) {
     $top_link.fadeIn();
    } else {
     $top_link.fadeOut();
    }
   }).scroll();

   $('.popin').popover({trigger: 'hover'});
  });
  </script>
 </body>
</html>
