<!-- end page title end breadcrumb -->
<div class="row">
    <div class="col-12">
        <div class="card-box table-responsive">
            <ul class="nav nav-tabs nav-justified" style="color:white;">
                <li class="nav-item">
                    <a href="#formpermintaan" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        Form permintaan barang
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#historypermintaan" data-toggle="tab" aria-expanded="true" class="nav-link">
                        History permintaan barang
                    </a>
                </li>
            </ul>
            <div class="tab-content" style="border-color:#40739e">
                <div role="tabpanel" class="tab-pane fade show active" id="formpermintaan">
                    <div class="row">
                        <div class="col-12">
                            <form id="form_barang">
                                <div class="form-group row">
                                    <div class="col-3">
                                        <label class="col-sm-6  col-form-label" for="example-placeholder">Tanggal Order</label>
                                        <div class="col-sm-12">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="TANGGAL_PERMINTAAN" id="tanggal_minta" value="<?= date('Y-m-d') ?>" placeholder="Tanggal order" id="tanggal" autocomplete="off" disabled>
                                                <div class="input-group-append">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                            </div><!-- input-group -->
                                        </div>
                                    </div>
                                    <div class="col-5">
                                        <label class="col-sm-4  col-form-label">Ruang Asal</label>
                                        <div class="col-sm-12">
                                            <select class="form-control select2" id="ruang_asal" name="RUANG" required>
                                                <option value=""></option>
                                                <?php foreach ($ruanganuser as $k) {
                                                    echo "<option value='$k->ID'>$k->DESKRIPSI</option>";
                                                } ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <label class="col-sm-6  col-form-label">Gudang Tujuan</label>
                                        <div class="col-sm-12">
                                            <select class="form-control select2 gudang" id="gudang" name="" required>
                                                <option value="0">Pilih Gudang</option>
                                                <?php foreach ($gudang as $k) {
                                                    echo "<option value='$k->ID'>$k->DESKRIPSI</option>";
                                                } ?>
                                            </select>
                                        </div>
                                        <select name="GUDANG" id="" class="id_gudang form-control" style="visibility:hidden;"></select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-5">
                                        <label class="col-sm-4  col-form-label" for="example-placeholder">Nama Barang</label>
                                        <div class="col-sm-12">
                                            <select name="ID_BARANG" id="nama_barang" placeholder="Nama Barang" class="barang form-control"></select>
                                            <select name="NAMA_BARANG" id="nama_barang" class="namabarang form-control" style="visibility:hidden;"></select>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <label class="col-sm-4  col-form-label" for="example-placeholder">Satuan</label>
                                        <div class="col-sm-12">
                                            <select name="SATUAN" id="" placeholder="Satuan" class="satuan form-control"></select>
                                            <select name="ID_SATUAN" id="" class="form-control" style="visibility:hidden;"></select>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <label class="col-sm-6  col-form-label" for="example-placeholder">Jumlah</label>
                                        <div class="col-sm-12">
                                            <input class="form-control" type="text" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" id="jumlah" placeholder="Jml" name="JUMLAH" / required>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-group row">
                                            <div class="col-sm-6">
                                                <label class="col-sm-4  col-form-label" for="example-placeholder">&nbsp;</label>
                                                <div class="col-sm-12">
                                                    <button class="btn btn-icon waves-effect waves-light btn-info" id="submit" name="submit"> <i class="fas fa-save"> </i> Tambah</button>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <label class="col-sm-4  col-form-label" for="example-placeholder">&nbsp;</label>
                                                <div class="col-sm-12" align="right">
                                                    <a href="<?php echo site_url('inventory/permintaan/simpan_order'); ?>" class="btn btn-icon waves-effect waves-light btn-success"><i class="fas fa-save"> </i> Selesai</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <table id="" class="table table-bordered dt-responsive nowrap">
                                <thead>
                                    <tr>
                                        <th width="70%" align="center">NAMA BARANG</th>
                                        <th>SATUAN</th>
                                        <th>JUMLAH</th>
                                        <th>AKSI</th>
                                    </tr>
                                </thead>
                                <tbody id="list_barang">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="historypermintaan">
                    <div class="table-responsive">
                        <table id="tabeldatapermintaan" class="table table-bordered table-bordered dt-responsive " cellspacing="0" width="100%">
                            <thead style="color:#fff;">
                                <tr>
                                    <th>#</th>
                                    <th>Unit peminta</th>
                                    <th>Gudang tujuan</th>
                                    <th>Tanggal permintaan</th>
                                    <th>Status</th>
                                    <th>Cetak</th>
                                </tr>
                            </thead>
                            <tbody style="color:#fff;"></tbody>
                        </table>
                    </div>
                </div>
                </form>
            </div>
        </div>
    </div> <!-- end row -->
</div>
<!-- Modal Start -->
<div class="modal fade" id="detailpermintaan" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0" id="mySmallModalLabel">Detail permintaan</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div id="viewdetailpermintaan"></div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="<?= base_url() ?>assets/admin/assets/css/jquery-ui.css" />
<script src="<?= base_url() ?>assets/admin/assets/js/jquery-ui.js"></script>

<script>
    $(document).ready(function() {
        jQuery('#tanggal_minta').datepicker({
            autoclose: true,
            todayHighlight: true,
            dateFormat: 'yy-mm-dd',
        });
    });
    $(document).ready(function() {
        $("#satuan").select2({
            placeholder: "[ Pilih Satuan ]",
            allowClear: true
        });
    });

    $("#ruang_asal").select2({
        placeholder: "Ruang asal"
    });

    $(document).ready(function() {
        $('#gudang').change(function() {
            $(this).prop('disabled', true);
        });
    });

    //untuk simpan barang
    $("#submit").click(function() {
        $.ajax({
            url: "<?php echo base_url(); ?>inventory/Permintaan/pilih_barang",
            type: "POST",
            data: $("#form_barang").serialize(),
            success: function(data) {

                $('#list_barang').html(data);
            }
        });
        return false;
    });
    //untuk load list barang di awal
    $('#list_barang').load("<?php echo base_url(); ?>inventory/Permintaan/list_barang");
    //**end**
</script>

<script type="text/javascript">
    $(document).ready(function() {
        $('#gudang').change(function() {
            var gudang = $("#gudang").val();
            // alert(gudang);
            $('#nama_barang').select2({
                placeholder: "[ Pilih Barang ]",
                ajax: {
                    url: "<?php echo base_url('inventory/Permintaan/pilihbarang/') ?>" + gudang,
                    dataType: 'json',
                    delay: 250,
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                escapeMarkup: function(markup) {
                    return markup;
                },
                templateResult: function(data) {
                    return data.html;
                },
                templateSelection: function(data) {
                    return data.text;
                }
            });
        });

        $('#tabeldatapermintaan').DataTable({
            "ajax": {
                url: '<?php echo base_url() ?>inventory/Permintaan/datapermintaan',
                type: 'GET'
            }
        });
    });

    $(document).ready(function() {
        $('#nama_barang').change(function() {
            var id = $(this).val();
            $.ajax({
                url: "<?php echo base_url(); ?>inventory/Permintaan/tampil_nama_barang",
                method: "POST",
                data: {
                    id: id
                },
                async: false,
                dataType: 'json',
                success: function(data) {
                    var html = '';
                    var i;
                    for (i = 0; i < data.length; i++) {
                        html += '<option>' + data[i].BARANG + '</option>';
                    }
                    $('.namabarang').html(html);
                }
            });
        });
    });

    $(document).ready(function() {
        $('.barang').change(function() {
            var id = $(this).val();
            $.ajax({
                url: "<?php echo base_url(); ?>inventory/Permintaan/tampil_satuan_barang",
                method: "POST",
                data: {
                    id: id
                },
                async: false,
                dataType: 'json',
                success: function(data) {
                    var html = '';
                    var i;
                    for (i = 0; i < data.length; i++) {
                        html += '<option>' + data[i].SATUAN + '</option>';
                    }
                    $('.satuan').html(html);
                }
            });
        });
    });

    $(document).ready(function() {
        $('.gudang').change(function() {
            var id = $(this).val();
            $.ajax({
                url: "<?php echo base_url(); ?>inventory/Permintaan/tampil_id_gudang",
                method: "POST",
                data: {
                    id: id
                },
                async: false,
                dataType: 'json',
                success: function(data) {
                    var html = '';
                    var i;
                    for (i = 0; i < data.length; i++) {
                        html += '<option>' + data[i].ID + '</option>';
                    }
                    $('.id_gudang').html(html);
                }
            });
        });
    });

    //Hapus Item Cart
    $(document).on('click', '.hapus_cart', function() {
        var row_id = $(this).attr("id"); //mengambil row_id dari artibut id
        $.ajax({
            url: "<?php echo base_url(); ?>inventory/Permintaan/hapus_barang",
            method: "POST",
            data: {
                row_id: row_id
            },
            success: function(data) {
                $('#list_barang').html(data);
            }
        });
    });

    $('#detailpermintaan').on('show.bs.modal', function(e) {
        var id = $(e.relatedTarget).data('id');
        $.ajax({
            type: 'POST',
            url: '<?php echo base_url() ?>inventory/Permintaan/datamodal',
            data: {
                id: id
            },
            success: function(data) {
                $('#viewdetailpermintaan').html(data);
            }
        });
    });

    ///SELECT BARANG YG AWAL///
    // $('#').change(function() {
    //     var id = $(this).val();
    //     $.ajax({
    //         url: "<?php echo base_url(); ?>inventory/Permintaan/tampil_id_barang",
    //         method: "POST",
    //         data: {
    //             id: id
    //         },
    //         async: false,
    //         dataType: 'json',
    //         success: function(data) {
    //             var html = '<option value="0">Pilih Barang</option>';
    //             var i;
    //             var style = "";
    //             for (i = 0; i < data.length; i++) {
    //                 style = data[i].STOK == 0 ? "" : 'style="color:#F08080"';
    //                 stok = data[i].STOK == 0 ? "" : ' - Stok saat ini';
    //                 html += '<option ' + style + ' value=' + data[i].ID_BARANG_GUDANG + '>' + data[i].BARANG + '' + stok + '' + data[i].STOK+ '</option>';
    //             }
    //             $('.barang').html(html);
    //         }
    //     });
    // });
</script>