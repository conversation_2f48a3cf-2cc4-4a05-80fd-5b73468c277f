<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class WhatsappDokterModel extends MY_Model{
	protected $_table_name = 'log.log_whatsapp';
	protected $_primary_key = 'ID';
	protected $_order_by = 'WAKTU_KIRIM';
	protected $_order_by_type = 'DESC';

	public $rules = array(
        'dokter' => array(
            'field' => 'dokter',
            'label' => 'Dokter',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
		'nomor' => array(
            'field' => 'nomor',
            'label' => 'Nomor',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'pasien' => array(
            'field' => 'pasien',
            'label' => 'Pasien',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.'
                ),
		),

		'pesan' => array(
            'field' => 'pesan',
            'label' => 'Pesan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
    );

	function __construct(){
		parent::__construct();
    }
    
    function table_query()
    {
        $this->db->select('`master`.getNamaLengkapPegawai(md.NIP) DOKTER
        , `master`.getNamaLengkap(lw.NORM) PASIEN
        , lw.PESAN
        , `master`.getNamaLengkapPegawai(ap.NIP) OLEH
        , lw.TANGGAL');
        $this->db->from('layanan.wa_dokter lw');
        $this->db->join('`master`.dokter md','lw.ID_DOKTER = md.ID','LEFT');
        $this->db->join('aplikasi.pengguna ap','lw.OLEH = ap.ID','LEFT');

        $this->db->order_by('lw.TANGGAL', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
