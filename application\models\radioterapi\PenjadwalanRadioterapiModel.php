<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PenjadwalanRadioterapiModel extends CI_Model {


  public function getDokter()
  {
    $str="SELECT md.ID, `master`.getNamaLengkapPegawai(md.NIP) DOKTER, mp.NAMA,mp.SMF, mr.<PERSON>SK<PERSON>PSI, mru.DESKRIPSI
        FROM master.dokter md 
        LEFT JOIN `master`.pegawai mp ON md.NIP = mp.NIP 
        LEFT JOIN `master`.referensi mr ON mp.SMF = mr.ID AND mr.JENIS = 26 AND mr.STATUS = 1
        LEFT JOIN `master`.dokter_ruangan mdr ON md.ID = mdr.DOKTER AND mdr.`STATUS` = 1 #AND mdr.RUANGAN IN(105020201,105020704,105020705,105150101)
        LEFT JOIN `master`.ruangan mru ON mdr.RUANGAN = mru.ID
        WHERE 
        md.`STATUS`='1'
        AND mp.NAMA!=''
        GROUP BY md.ID
      ORDER BY mp.NAMA";
    $query  = $this->db->query($str);
    // echo $str;exit;
   return $query->result_array();
  }

  public function getAlat($jenis='1')
  {
    $str="SELECT alat.ID, alat.NAMA_ALAT
          FROM medis.radioterapi_alat alat
          WHERE alat.JENIS='$jenis' 
          ORDER BY alat.NAMA_ALAT";
    $query  = $this->db->query($str);
    // echo $str;exit;
   return $query->result_array();
  }

  public function getEvent($param=null)
  {
   
    // $date=date_create($param['start']);
    // $tanggal=date_format($date,"Ym");
    $time = strtotime($param['start']);
    $tanggal = date("Ym", strtotime("+1 month", $time));
    // $tanggal='202405';
    $str="SELECT kuo.ID,kuo.ID_DOKTER,kuo.ID_ALAT,kuo.TANGGAL,kuo.KUOTA_REG,kuo.KUOTA_CITO,kuo.TERPAKAI_REG,kuo.TERPAKAI_CITO       
    FROM medis.radioterapi_kuota kuo 
    #LEFT JOIN radioterapi_jadwal jad ON kuo.ID_DOKTER=jad.ID_DOKTER
    
    WHERE kuo.ID_DOKTER='".$param['iddokter']."'
    AND kuo.ID_ALAT='".$param['idalat']."'
    AND kuo.`STATUS`=1
    AND DATE_FORMAT(kuo.TANGGAL, '%Y%m') = '$tanggal'";
    $query  = $this->db->query($str);
    // echo '<pre>'.$str;exit;
   return $query->result_array();
  }

  public function getTglCTPlan($param=null)
  {
    $time = strtotime($param['start']);
    $tanggal = date("Ym", strtotime("+1 month", $time));
    // $date=date_create($param['tanggal']);
    // $tanggal=date_format($date,"Ym");
    $idalat=$param['idalat'];
    // $tanggal='202405';
    $str="SELECT count(jad.ID) jml,jad.TGL_CT_PLAN    
    FROM medis.radioterapi_jadwal jad 
    WHERE 
    jad.`STATUS`=1
    AND jad.ALAT_CT_PLAN='$idalat'
    AND DATE_FORMAT(jad.TGL_CT_PLAN, '%Y%m') = '$tanggal'
    GROUP BY jad.TGL_CT_PLAN";
    $query  = $this->db->query($str);
    // echo '<pre>'.$str;exit;
   return $query->result_array();
  }


  public function simpanRencanaRadioterapi($data)
  {
    
    // var_dump($data);exit;
    $str1="SELECT count(kuo.ID) jml
          FROM medis.radioterapi_kuota kuo
          WHERE 
          kuo.ID_DOKTER='".$data['iddokter']."'
          AND kuo.ID_ALAT='".$data['idalat']."'
          AND IFNULL(kuo.TERPAKAI_REG,0) < IFNULL(kuo.KUOTA_REG,0)
          AND kuo.TANGGAL >=CURDATE()
          AND kuo.TANGGAL >='".$data['tgl_mulai']."'
          ";
    // echo '<pre>'.$str1;exit;
    $query1  = $this->db->query($str1);
   $datacek =$query1->row_array();
   if($data['jumlah_jadwal'] > $datacek['jml']){
    $result = array('status' => 'failed','message'=>'Kuota tidak mencukupi, sisa kuota '.((int)$datacek['jml']));
    echo json_encode($result);
    exit;
   }
  //  var_dump($datacek);exit;

    $str="INSERT INTO medis.radioterapi_jadwal (NOMR,ID_DOKTER,ID_ALAT,ICD,DIAGNOSIS,SEVERITY, TEKNIK,RADIOEKSTERNA, SIMULATOR, DIAG_CT_PLAN, TGL_CT_PLAN, ALAT_CT_PLAN, TPS,BOLUS,MASKER,
    INDIVIDUAL_BLOK,KONSULTASI_DOKTER,BRAKHITERAPI,JUMLAH_JADWAL,OLEH) VALUES 
    ('".$data['nomr']."','".$data['iddokter']."','".$data['idalat']."','".$data['icd']."','".$data['diagnosis']."','".$data['severity']."','".$data['teknik']."','".$data['radioekstema']."','".$data['simulator']."','".$data['diag_ctplan']."','".$data['tgl_ctplan']."','".$data['alat_ctplan']."','".$data['tps']."','".$data['bolus']."','".$data['masker']."'
    ,'".$data['individual_blok']."','".$data['konsultasi_dokter']."','".$data['brakhiterapi']."','".$data['jumlah_jadwal']."','".$data['oleh']."')";
    $query  = $this->db->query($str);
    $insertid= $this->db->insert_id();
    // $query=false;
    if($query){
      
      $dataset=array();
      // var_dump($data['jumlah_jadwal']);
      $jumlah_jadwal=$data['jumlah_jadwal'];
      $str2="SELECT TANGGAL
      FROM medis.radioterapi_kuota kuo
      WHERE 
      kuo.ID_DOKTER='".$data['iddokter']."'
      AND kuo.ID_ALAT='".$data['idalat']."'
      AND IFNULL(kuo.TERPAKAI_REG,0) < IFNULL(kuo.KUOTA_REG,0)
      AND kuo.TANGGAL >=CURDATE()
      AND kuo.TANGGAL >='".$data['tgl_mulai']."'
      ORDER BY kuo.TANGGAL ASC
      LIMIT $jumlah_jadwal
      ";
// echo '<pre>'.$str1;exit;
      $query2  = $this->db->query($str2);
      $datakuo =$query2->result_array();
      foreach ($datakuo as $key => $value) {
        # code...
      // }

      // for ($i=0; $i < $jumlah_jadwal; $i++) {
        $item=array();
        // $nextweek=date('Y-m-d', strtotime('+'.$i.' day', strtotime($data['tgl_mulai'])));
        $nextweek=$value['TANGGAL'];
      // var_dump($nextweek);exit;
        $item['ID_JADWAL']=$insertid;
        $item['ID_DOKTER']=$data['iddokter'];
        $item['ID_ALAT']=$data['idalat'];
        $item['TANGGAL']=$nextweek;
        $item['OLEH']=$data['oleh'];
         $stri="INSERT INTO medis.radioterapi_tanggal_tindakan (ID_JADWAL,ID_DOKTER,ID_ALAT,TANGGAL,OLEH)
         SELECT $insertid idjadwal, ".$data['iddokter']." iddokter, ".$data['idalat']." idalat, kuo.TANGGAL tanggal, ".$data['oleh']." oleh  FROM medis.radioterapi_kuota kuo
         WHERE kuo.TANGGAL='$nextweek'
         AND IFNULL(kuo.KUOTA_REG,0) > IFNULL(kuo.TERPAKAI_REG,0) 
         LIMIT 1";
        //  echo '<pre>'.$stri.'<br>';;
        $queryi  = $this->db->query($stri);
        $afftectedRows = $this->db->affected_rows();
        if($afftectedRows>0){
          $strup="UPDATE medis.radioterapi_kuota SET TERPAKAI_REG=(IFNULL(TERPAKAI_REG,0)+1)
          WHERE 
          ID_DOKTER='".$data['iddokter']."' AND ID_ALAT='".$data['idalat']."' AND TANGGAL='$nextweek'
          ";
          $this->db->query($strup);

        }else{
          //  $jumlah_jadwal++;
        }

        // array_push($dataset,$item);
      }
// var_dump($dataset);exit;
      // $this->db->trans_begin();
      // $this->db->insert_batch('medis.radioterapi_tanggal_tindakan', $dataset);
      // if ($this->db->trans_status() === false) {
      //   $this->db->trans_rollback();
      //   $result = array('status' => 'failed','message' => 'failed');
      // } else {
      //   $this->db->trans_commit();
      //   $result = array('status' => 'success','message' => 'success');
      // }
      


      $result = array('status' => 'success','message' => 'success');
    }else{
      $result = array('status' => 'failed','message' => 'failed');
    }
    // $this->db->trans_begin();
    // $this->db->insert('medis.tb_treatmentDoseDr', $data);
    // if ($this->db->trans_status() === false) {
    //   $this->db->trans_rollback();
    //   $result = array('status' => 'failed');
    // } else {
    //   $this->db->trans_commit();
    //   $result = array('status' => 'success');
    // }

    echo json_encode($result);
  }



  public function monitoringkedatangan($param='')
  {
    $str="SELECT mrj.*, master.getNamaLengkap(mrj.NOMR) NAMA_PASIEN,mrt.ID idtt,mrt.TANGGAL, mra.NAMA_ALAT,mrt.STATUS statustt,
    (SELECT master.getNamaLengkapPegawai(md.NIP) DPJP 
 			FROM pendaftaran.pendaftaran pp 
            LEFT JOIN pendaftaran.tujuan_pasien ptp ON pp.NOMOR=ptp.NOPEN
            LEFT JOIN master.dokter md ON md.ID = ptp.DOKTER
            WHERE pp.NORM=mrj.NOMR AND date(pp.TANGGAL)=mrt.TANGGAL
            LIMIT 1
            ) DPJP
            FROM medis.radioterapi_jadwal mrj
            LEFT JOIN medis.radioterapi_tanggal_tindakan mrt ON mrj.ID=mrt.ID_JADWAL
            LEFT JOIN medis.radioterapi_alat mra ON mrt.ID_ALAT=mra.ID
            
            WHERE mrt.TANGGAL BETWEEN '".$param['tgl_mulai']."' AND  '".$param['tgl_akhir']."' 
            AND mrj.ID_DOKTER='".$param['iddokter']."'
            AND mrt.ID_ALAT='".$param['idalat']."'
            AND mrt.STATUS='".$param['idstatus']."'

            ORDER BY mrt.TANGGAL ASC";
            // echo '<pre>'.$str.'<br>';;
    $query  = $this->db->query($str);

    return $query;

  }


  public function pasienradiasieksterna($param='')
  {
    $str="SELECT mrj.*, master.getNamaLengkap(mrj.NOMR) NAMA_PASIEN,mrt.ID idtt, min(mrt.TANGGAL) tgl_mulai,max(mrt.TANGGAL) tgl_akhir, mra.NAMA_ALAT,mrt.STATUS statustt,
    (SELECT master.getNamaLengkapPegawai(md.NIP) DPJP 
 			FROM pendaftaran.pendaftaran pp 
            LEFT JOIN pendaftaran.tujuan_pasien ptp ON pp.NOMOR=ptp.NOPEN
            LEFT JOIN master.dokter md ON md.ID = ptp.DOKTER
            WHERE pp.NORM=mrj.NOMR AND date(pp.TANGGAL)=mrt.TANGGAL
            LIMIT 1
            ) DPJP
            FROM medis.radioterapi_jadwal mrj
            LEFT JOIN medis.radioterapi_tanggal_tindakan mrt ON mrj.ID=mrt.ID_JADWAL AND mrt.`STATUS` > 0
            LEFT JOIN medis.radioterapi_alat mra ON mrt.ID_ALAT=mra.ID
            
            WHERE mrt.TANGGAL BETWEEN '".$param['tgl_mulai']."' AND  '".$param['tgl_akhir']."' 
            AND mrt.STATUS='".$param['idstatus']."'
				GROUP BY mrj.ID
            ORDER BY mrt.TANGGAL ASC";
            // echo '<pre>'.$str.'<br>';;
    $query  = $this->db->query($str);

    return $query;

  }


  public function simpancheckin($param='')
  {
    $str="UPDATE medis.radioterapi_tanggal_tindakan SET STATUS='".$param['status']."'
            
            WHERE ID='".$param['id']."'
            ";
            // echo '<pre>'.$str.'<br>';;
    $query  = $this->db->query($str);
    if($query){
      $result = array('status' => 'success','message' => 'success','flag' => 'Checkin');
    }else{
      $result = array('status' => 'failed','message' => 'failed','flag' => '');
    }

    echo json_encode($result);

  }

  public function simpanstop($param='')
  {
    $str="UPDATE medis.radioterapi_jadwal SET STATUS='".$param['status']."', TANGGAL_STOP=now()
          WHERE ID='".$param['id']."'
            ";
            // echo '<pre>'.$str.'<br>';;
    $query  = $this->db->query($str);
    if($query){
      $result = array('status' => 'success','message' => 'success','flag' => 'Stop');
    }else{
      $result = array('status' => 'failed','message' => 'failed','flag' => '');
    }

    echo json_encode($result);

  }

  public function simpanreschedule($param='')
  {
    $str2="SELECT mrtt.ID ,mrtt.ID_JADWAL,  (mrtt2.TANGGAL) tanggalmax,
    kuo.ID id_kuota, (kuo.TANGGAL) tanggalnext
    
    FROM medis.radioterapi_tanggal_tindakan mrtt
    LEFT JOIN medis.radioterapi_tanggal_tindakan mrtt2 ON mrtt.ID_JADWAL=mrtt.ID_JADWAL AND mrtt2.TANGGAL > mrtt.TANGGAL
    LEFT JOIN medis.radioterapi_kuota kuo ON kuo.ID_DOKTER=mrtt2.ID_DOKTER AND kuo.ID_ALAT=mrtt2.ID_ALAT AND kuo.TANGGAL > mrtt2.TANGGAL
    AND IFNULL(kuo.KUOTA_REG,0) > IFNULL(kuo.TERPAKAI_REG,0)
    WHERE 
    mrtt.ID='".$param['id']."'
    AND mrtt.STATUS=1
    AND mrtt2.STATUS=1
    ORDER BY tanggalmax DESC, tanggalnext ASC 
    LIMIT 1";
// echo '<pre>'.$str2;exit;
      $query2  = $this->db->query($str2);
      $datakuo =$query2->row_array();
      $result =array();
      // var_dump($datakuo);exit;
    if($datakuo['tanggalnext']!=''){
      
      $str="UPDATE medis.radioterapi_tanggal_tindakan SET STATUS='".$param['status']."', KETERANGAN='".$param['keterangan']."'
            WHERE ID='".$param['id']."'";
              // echo '<pre>'.$str.'<br>';;
      $query  = $this->db->query($str);

        $stri="INSERT INTO medis.radioterapi_tanggal_tindakan (ID_JADWAL,ID_DOKTER,ID_ALAT,TANGGAL,OLEH)
        SELECT ".$datakuo['ID_JADWAL']." idjadwal, kuo.ID_DOKTER iddokter, kuo.ID_ALAT idalat, kuo.TANGGAL tanggal, ".$param['oleh']." oleh  FROM medis.radioterapi_kuota kuo
        WHERE kuo.ID='".$datakuo['id_kuota']."'
        AND IFNULL(kuo.KUOTA_REG,0) > IFNULL(kuo.TERPAKAI_REG,0) 
        LIMIT 1";
      //  echo '<pre>'.$stri.'<br>';;
        $queryi  = $this->db->query($stri);

      if($query){
        $result = array('status' => 'success','message' => 'success','flag' => 'Stop');
      }else{
        $result = array('status' => 'failed','message' => 'failed','flag' => '');
      }
    }else{
      $result = array('status' => 'failed','message' => 'Kuota pengganti tidak tersedia','flag' => '');
    }

    echo json_encode($result);

  }





}
