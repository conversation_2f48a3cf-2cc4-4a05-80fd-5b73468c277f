<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pendapatan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('keuanganModel','masterModel'));
  }

  public function index()
  {
    $dataPendapatan          = $this->keuanganModel->dataPendapatan();
    $dataRealisasiPendapatan = $this->keuanganModel->listTableRpendapatan();
    $data = array(
      'title' => 'Halaman Pendapatan Sistem Informasi Manajemen Anggaran',
      'isi'   => 'Keuangan/pendapatan/index',
      'dataPendapatan' => $dataPendapatan,
      'dataRealisasiPendapatan'  => $dataRealisasiPendapatan,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function simpanRealisasiPendapatan()
  {

    $mak = $this->input->post('mak');
    
    // //Mengambil Deskripsi Mak
    $datatblPendapatan = $this->keuanganModel->getDeskripsiPendapatan($mak);
    // $deskripsi = $datatblPendapatan['URAIAN'];

    // //Update Pagu Transaksi
    $paguTransaksi = $datatblPendapatan['PAGU_TRANSAKSI'];

    //Simpan Realisasi Pendapatan
    $data = array(
      'JUMLAH'              => $this->input->post("jumlah"),
      'DESKRIPSI'           => $this->input->post("deskripsi"),
      'ID_MAK'              => $mak,
      'TANGGAL'             => $this->input->post("tanggal"),
      'OLEH'                => $this->session->userdata('id'),
    );
    $simpanRealisasiPendapatan = $this->keuanganModel->insertRealisasiPendapatan($data);

    $jmlpendapatan = $this->input->post("jumlah");
    $hasiljumlah = $jmlpendapatan + $paguTransaksi;

    $data2 = array(
      'PAGU_TRANSAKSI' => $hasiljumlah,
    );

    $this->keuanganModel->updatePaguTransaksi($mak, $data2);


  }

}

/* End of file Pendapatan.php */
/* Location: ./application/controllers/keuangan/Pendapatan.php */
