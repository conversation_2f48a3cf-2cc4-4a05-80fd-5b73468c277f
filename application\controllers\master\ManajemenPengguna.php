<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ManajemenPengguna extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(5,$this->session->userdata('akses')) OR !in_array(7,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('masterModel');
  }

  ///////////////////////////////////////////////////////////// START PENGGUNA //////////////////////////////////////////////////////////////////////

  public function index()
  {
    $pegawai = $this->masterModel->pegawai();

    $data = array(
      'title'   => 'Halaman Master',
      'isi'     => 'Master/pengguna/index',
      'pegawai' => $pegawai,
    );

    $this->load->view('layout/wrapper',$data);
  }


  public function listPengguna()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listPengguna = $this->masterModel->pengguna();

    $data  = array();
    $no    =1;
    foreach($listPengguna->result() as $lp) {

      if($lp->STATUS == 1){
        $check = "checked" ;
      }else{
        $check = "" ;
      }


      $data[] = array(
        $no,
        $lp->NIP,
        $lp->LOGIN,
        $lp->NAMA,
        '<div class="checkbox checkbox-primary">
        <input type="checkbox" id="statusPengguna'.$lp->ID.'" value="'.$lp->ID.'" class="SPengguna" '.$check.'>
        <label for="statusPengguna'.$lp->ID.'"></label>
        </div>',
        // '<input type="checkbox">',
        '<a href="#pilihPengguna" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-id="'.$lp->ID.'"><i class="fa fa-check"></i> Pilih</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listPengguna->num_rows(),
      "recordsFiltered" => $listPengguna->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function SPenggunaAktiv()
  {
    $id = $this->input->post('id_pengguna');
    $data = array(
      'STATUS' => 1,
    );

    // $this->masterModel->SPenggunaAktiv($id,$data);
  }

  public function SPenggunaNonAktiv()
  {
    $id = $this->input->post('id_pengguna');
    $data = array(
      'STATUS' => 0,
    );

    // $this->masterModel->SPenggunaNonAktiv($id,$data);
  }

  public function pilihanData()
  {
    $id = $this->input->post('id');

    // echo "<pre>";print_r($id);exit();

    $menuSimrsKd = $this->menu($id);
    $ruanganSimrsKd = $this->ruangan($id);
    // print_r($ruanganSimrsKd);exit();
    $data = array(
      'menuSimrsKd'    => $menuSimrsKd,
      'idPengguna'     => $id,
      'ruanganSimrsKd' => $ruanganSimrsKd,
    );

    $this->load->view('Master/pengguna/modalManajemenPengguna',$data);

  }

  public function ruanganAktiv()
  {
    $result = $this->masterModel->ruanganAktiv();

    echo json_encode($result);
  }

  public function ruanganNonAktiv()
  {
    $result = $this->masterModel->ruanganNonAkativ();

    echo json_encode($result);
  }

  public function menuAktiv()
  {
    $result = $this->masterModel->menuAktiv();

    echo json_encode($result);
  }

  public function menuNonAktiv()
  {
    $result = $this->masterModel->menuNonAkativ();

    echo json_encode($result);
  }

  public function menu($id){
    $role_menu = $this->masterModel->menuSimrsKd($id);
    $data = array();
    foreach ($role_menu->result() as $main) {
      $sub_menu = $this->masterModel->menuSimrsKd($id,$main -> ID);
      $menu_array = array();
      if($sub_menu->num_rows() > 0){
        $menu_array['id'] = $main -> ID;
        $menu_array['label'] = $main -> LABEL;
        $menu_array['link'] = $main -> LINK;
        $menu_array['status'] = $main -> STATUS;
        $subb = array();
        foreach ($sub_menu->result() as $sub) {
          $sub_array = array();
          $sub_menu = $this->masterModel->menuSimrsKd($id,$sub -> ID);
          $sub_array['id'] = $sub -> ID;
          $sub_array['label'] = $sub -> LABEL;
          $sub_array['link'] = $sub -> LINK;
          $sub_array['status'] = $sub -> STATUS;
          $subb[] = $sub_array;
        }
        $menu_array['sub'] = $subb;
      }else{
        $menu_array['id'] = $main -> ID;
        $menu_array['label'] = $main -> LABEL;
        $menu_array['link'] = $main -> LINK;
        $menu_array['status'] = $main -> STATUS;
      }
      $data[] = $menu_array;
    }
        // echo json_encode($data);
    return $data;
  }


  public function ruangan($id){
    $role_ruangan = $this->masterModel->RuanganSimrsKd($id);
    $data = array();
    foreach ($role_ruangan->result() as $main) {
      $sub_ruangan = $this->masterModel->RuanganSimrsKd($id,$main -> ID);
      $ruangan_array = array();
      if($sub_ruangan->num_rows() > 0){
        $ruangan_array['ID'] = $main -> ID;
        $ruangan_array['DESKRIPSI'] = $main -> DESKRIPSI;
        $ruangan_array['STATUS'] = $main -> STATUS;
        $subb = array();
        // foreach ($sub_ruangan->result() as $sub) {
        //   $sub_array = array();
        //   $sub_ruangan = $this->masterModel->RuanganSimrsKd($id,$sub -> ID);
        //   $ruangan_array['ID'] = $main -> ID;
        //   $ruangan_array['DESKRIPSI'] = $main -> DESKRIPSI;
        //   $subb[] = $sub_array;
        // }
        // $ruangan_array['sub'] = $subb;
      }else{
        $ruangan_array['ID'] = $main -> ID;
        $ruangan_array['DESKRIPSI'] = $main -> DESKRIPSI;
        $ruangan_array['STATUS'] = $main -> STATUS;
      }
      $data[] = $ruangan_array;
    }
        // echo json_encode($data);
    return $data;
  }

  ///////////////////////////////////////////////////////////// END PENGGUNA /////////////////////////////////////////////////////////////////////////

}

/* End of file ManajemenPengguna.php */
/* Location: ./application/controllers/master/ManajemenPengguna.php */
