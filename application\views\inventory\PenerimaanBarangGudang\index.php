<!-- end page title end breadcrumb -->
<div class="row">
    <div class="col-12">
        <div class="card-box table-responsive">
            <ul class="nav nav-tabs nav-justified" style="color:white;">
                <li class="nav-item">
                    <a href="#formpermintaan" data-toggle="tab" aria-expanded="false" class="nav-link active">
                        Form penerimaan barang
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#historypermintaan" data-toggle="tab" aria-expanded="true" class="nav-link">
                        History penerimaan barang
                    </a>
                </li>
            </ul>
            <div class="tab-content" style="border-color:#40739e">
                <div role="tabpanel" class="tab-pane fade show active" id="formpermintaan">
                    <div class="row">
                        <div class="col-12">
                            <form id="form_barang_po">
                                <div class="form-group row">
                                    <div class="col-2">
                                        <label class="col-sm-12  col-form-label" for="example-placeholder"><PERSON>gal Masuk</label>
                                        <div class="col-sm-12">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id='tgl_terima' name="TGL_MASUK" placeholder="Tanggal Masuk" autocomplete="off" require>
                                                <div class="input-group-append">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                            </div><!-- input-group -->
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <label class="col-sm-12  col-form-label" for="example-placeholder">Nomor Surat Jalan</label>
                                        <div class="col-sm-12">
                                            <input type="text" name="NOSURAT_JALAN" class="form-control" placeholder="Nomor Surat Jalan" required>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <label class="col-sm-6  col-form-label">Gudang</label>
                                        <div class="col-sm-12">
                                            <select class="form-control select2" id="gudang" name="GUDANG">
                                                <option value="0">Pilih Gudang</option>
                                                <?php foreach ($gudang as $k) {
                                                    echo "<option value='$k->ID'>$k->DESKRIPSI</option>";
                                                } ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <label class="col-sm-12  col-form-label" for="example-placeholder">Nama Barang</label>
                                        <div class="col-sm-12">
                                            <input type="text" name="NAMA" class="form-control" id="terima" placeholder="Nama barang" autocomplete="off" required>
                                            <input class="form-control" type="hidden" placeholder="ID_BARANG" name="ID_BARANG">
                                            <input class="form-control" type="hidden" placeholder="" name="STOK" required>
                                            <input class="form-control" type="hidden" placeholder="ID_BARANG_GUDANG" name="ID_BARANG_GUDANG" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-2">
                                        <label class="col-sm-4  col-form-label" for="example-placeholder">Satuan</label>
                                        <div class="col-sm-12">
                                            <input class="form-control" type="text" placeholder="Satuan" name="SATUAN" readonly>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <label class="col-sm-6  col-form-label">Perusahaan</label>
                                        <div class="col-sm-12">
                                            <select class="form-control select2" id="penyedia" name="PENYEDIA">
                                                <option value="0">Pilih Penyedia</option>
                                                <?php foreach ($penyedia as $k) {
                                                    echo "<option value='$k->ID'>$k->NAMA</option>";
                                                } ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <label class="col-sm-6  col-form-label" for="example-placeholder">Harga</label>
                                        <div class="col-sm-12">
                                            <input class="form-control" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" type="text" id="harga" placeholder="Harga" name="HARGA" / required>
                                        </div>
                                    </div>
                                    <!--  <div class="col-2">
                                        <label class="col-sm-12  col-form-label" for="example-placeholder">Terima UPB</label>
                                        <div class="col-sm-12">
                                            <input class="form-control" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" type="text" id="jumlah" placeholder="Jumlah" name="JUMLAH_MASUK"/ required >
                                        </div>
                                    </div> -->
                                    <!-- <div class="col-2">
                                        <label class="col-sm-12  col-form-label" for="example-placeholder">Konversi</label>
                                        <div class="col-sm-12">
                                            <input class="form-control" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" type="text" id="jumlah" placeholder="Jumlah" name="JUMLAH_MASUK"/ required >
                                        </div>
                                    </div> -->
                                    <div class="col-2">
                                        <label class="col-sm-12  col-form-label" for="example-placeholder">Jumlah Masuk</label>
                                        <div class="col-sm-12">
                                            <input class="form-control" onkeypress="if ( isNaN(this.value + String.fromCharCode(event.keyCode) )) return false;" type="text" id="jumlah" placeholder="Jumlah" name="JUMLAH_MASUK" / required>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group row">
                                            <div class="col-sm-6">
                                                <label class="col-sm-12  col-form-label" for="example-placeholder">&nbsp;</label>
                                                <div class="col-sm-12">
                                                    <button class="btn btn-icon waves-effect waves-light btn-info pull-right" id="tambah_terima" name="submit"> <i class="fas fa-save"> </i> Tambah</button>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">

                                                <label class="col-sm-4  col-form-label" for="example-placeholder">&nbsp;</label>
                                                <div class="col-sm-12">
                                                    <a href="<?php echo site_url('inventory/PenerimaanBarangGudang/simpan_penerimaan'); ?>" class="btn btn-icon waves-effect waves-light btn-success pull-right"><i class="fas fa-save"> </i> Selesai</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <table id="" class="table table-bordered dt-responsive nowrap">
                                <thead>
                                    <tr>
                                        <th width="5%" align="center">NO</th>
                                        <th width="30%">BARANG</th>
                                        <th width="15%">HARGA BARANG</th>
                                        <th width="10%">STOK AWAL</th>
                                        <th width="15%">JUMLAH MASUK</th>
                                        <th width="10%">SATUAN</th>
                                        <th width="5%">AKSI</th>
                                    </tr>
                                </thead>
                                <tbody id="list_penerimaan">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane fade" id="historypermintaan">
                    <div class="table-responsive">
                        <div class="table-responsive">
                            <table id="tabeldatapenerimaan" class="table table-bordered table-bordered dt-responsive " cellspacing="0" width="100%">
                                <thead style="color:#fff;">
                                    <tr>
                                        <th>No</th>
                                        <th>Nomor Transaksi</th>
                                        <th>Tanggal Masuk</th>
                                        <th>No Surat Jalan</th>
                                        <th>Gudang</th>
                                        <th>#</th>
                                    </tr>
                                </thead>
                                <tbody style="color:#fff;"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> <!-- end row -->
</div>
<!-- Modal Start -->
<div class="modal fade" id="detailpenerimaan" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title mt-0" id="mySmallModalLabel">Detail penerimaan</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div id="viewdetailpenerimaan"></div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="<?= base_url() ?>assets/admin/assets/css/jquery-ui.css" />
<script src="<?= base_url() ?>assets/admin/assets/js/jquery-ui.js"></script>
<script src="<?= base_url() ?>assets/admin/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
<script type="text/javascript">
    $(document).ready(function() {

        $("#penyedia").select2({
            placeholder: "Penyedia"
        });

        $("#satuan").select2({
            placeholder: "Satuan"
        });

        $('#terima').autocomplete({
            source: "<?php echo site_url('inventory/PenerimaanBarangGudang/get_autocomplete'); ?>",
            select: function(event, ui) {
                $('[name="BARANG"]').val(ui.item.label);
                $('[name="ID_BARANG"]').val(ui.item.idbarang);
                $('[name="SATUAN"]').val(ui.item.idsatuan);
                $('[name="STOK"]').val(ui.item.idstok);
                $('[name="ID_BARANG_GUDANG"]').val(ui.item.idbaranggudang);
            }
        });

        $("#tambah_terima").click(function() {
            $.ajax({
                url: "<?php echo base_url(); ?>inventory/PenerimaanBarangGudang/input_barang",
                type: "POST",
                data: $("#form_barang_po").serialize(),
                success: function(data) {
                    $('#list_penerimaan').html(data);
                }
            });
            return false;
        });

        //Hapus Item Cart
        $(document).on('click', '.hapus_terima', function() {
            var row_id = $(this).attr("id"); //mengambil row_id dari artibut id
            $.ajax({
                url: "<?php echo base_url(); ?>inventory/PenerimaanBarangGudang/hapus_barang",
                method: "POST",
                data: {
                    row_id: row_id
                },
                success: function(data) {
                    $('#list_penerimaan').html(data);
                }
            });
        });

        $("#ruang_asal").select2({
            placeholder: "Ruang asal"
        });

        jQuery('#tgl_terima').datepicker({

            autoclose: true,
            dateFormat: 'yy-mm-dd',
            todayHighlight: true
        });

        $('#tabeldatapenerimaan').DataTable({
            "ajax": {
                url: '<?php echo base_url() ?>inventory/PenerimaanBarangGudang/datapenerimaan',
                type: 'GET'
            }
        });

    });
</script>
<script type="text/javascript">
    $('#detailpenerimaan').on('show.bs.modal', function(e) {
        var id = $(e.relatedTarget).data('id');
        $.ajax({
            type: 'POST',
            url: '<?php echo base_url() ?>inventory/PenerimaanBarangGudang/datamodal',
            data: {
                id: id
            },
            success: function(data) {
                $('#viewdetailpenerimaan').html(data);
            }
        });
    });
</script>