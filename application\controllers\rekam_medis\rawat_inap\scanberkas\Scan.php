<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Scan extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/scanberkas/ScanModel'
    ));
  }

  public function index(){
  	// $norm = $this->uri->segment(6);
    // $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $listScan = $this->ScanModel->getScan($nomr)->result();
    $data = array(
    //   'nopen' => $nopen,
    //   'norm' => $norm,
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'listScan' => $listScan
    //   'Berkas' => $this->ScanModel->getScan(),
    );
     // print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/scanberkas/index', $data);
  }

  public function scanBerkas()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    $nomr = $this->input->get('nomr');
    $listScan = $this->ScanModel->getScan($nomr)->result();

    $data = array(
        'listScan' => $listScan
    );

    $this->load->view('rekam_medis/rawat_inap/scanberkas/index', $data);

    // $data = array();
    // $no = 1;
    // foreach ($listScan->result() as $Scan) {
    // $button = base_url($Scan->LOKASI . $Scan->FOLDER . "/" . $Scan->SUBFOLDER . "/" . $Scan->FILE);

    //   $data[] = array(
    //     $no,
    //     $Scan->NOMR,
    //     $Scan->FILE,
    //     '<a href="'.$button.'" class="btn btn-primary btn-block loadiframe" data-id="'.$Scan->ID_SCAN.'" data-keyboard="false"><i class="fa fa-eye"></i> Lihat</a>',
    //   );
    //   $no++;
    // }

    // $output = array(
    //   "draw"            => $draw,
    //   "recordsTotal"    => $listScan->num_rows(),
    //   "recordsFiltered" => $listScan->num_rows(),
    //   "data"            => $data
    // );
    // echo json_encode($output);
  }
}
?>