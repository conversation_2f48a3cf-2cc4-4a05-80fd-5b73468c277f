<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON><PERSON><PERSON><PERSON> extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','gigi/PengkajianGigiModel','gigi/DiagnosaKesehatanModel'));
    }

    public function index() {
        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(5)),
            'perawatan_gigi' => $this->masterModel->referensi(515),
            'berapa_menyikat' => $this->masterModel->referensi(516),
            'kapan_menyikat' => $this->masterModel->referensi(517),
            'gerakan_menyikat' => $this->masterModel->referensi(518),
            'minum_teh_kopi' => $this->masterModel->referensi(519),
            'merokok' => $this->masterModel->referensi(520),
            'minuman_beralkohol' => $this->masterModel->referensi(521),
            'menggigit_pensil' => $this->masterModel->referensi(522),
            'mengunyah_rahang' => $this->masterModel->referensi(523),
            'bruxism' => $this->masterModel->referensi(524),
            'diagnosa_kesehatan' => $this->masterModel->referensi(525),
            'nyeri_gigi' => $this->masterModel->referensi(526),
            'skala_gigi' => $this->masterModel->referensi(527),
            'anamnesis_gigi' => $this->masterModel->referensi(528),
        );

        $this->load->view('Pengkajian/gigi/pengkajian',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->PengkajianGigiModel->rules;
    			$this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();

                    $data = array(
                        'kunjungan' => $post['nokun'],
                        'auto_allo' => $post['auto_allo'],
                        'allo_nama' => isset($post['nama']) ? $post['nama'] : null,
                        'hubungan_dengan_pasien' => isset($post['hubungan_pasien']) ? $post['hubungan_pasien'] : null,
                        'keluhan_pasien' => $post['keluhan_pasien'],
                        'skrining_nyeri_gigi' => $post['nyeri_gigi'],
                        'perawatan_gigi' => $post['perawatan_gigi'],
                        'berapa_menyikat_gigi' => $post['berapa_kali'],
                        'kapan_menyikat_gigi' => $post['kapan_menyikat'],
                        'gerakan_menyikat_gigi' => $post['gerakan_menyikat'],
                        'minum_teh_kopi' => $post['minum_teh_kopi'],
                        'minum_teh_kopi_jelaskan' =>  isset($post['desk_minum_teh_kopi']) ? $post['desk_minum_teh_kopi'] : null,
                        'minum_alkohol' => $post['minuman_beralkohol'],
                        'minum_alkohol_jelaskan' => isset($post['desk_minuman_beralkohol']) ? $post['desk_minuman_beralkohol'] : null,
                        'mengunyah_1_rahang' => isset($post['mengunyah_rahang']) ? $post['mengunyah_rahang'] : null,
                        'kebiasaan_merokok' => $post['merokok'],
                        'kebiasaan_merokok_jelaskan' => isset($post['desk_merokok']) ? $post['desk_merokok'] : null,
                        'kebiasaan_gigit_pensil' => $post['menggigit_pensil'],
                        'bruxism' => $post['bruxism'],
                        'instruksi_medis' => $post['intruksi_medis'],
                        'oleh' => $this->session->userdata("id"),
                    );

    				if($this->db->replace('keperawatan.tb_pengkajian_awal_gigi',$data)){
                        $this->db->where(array('kunjungan' => $post['nokun'], 'jenis' => 1));
                        $this->db->delete('keperawatan.tb_diagnosis_kesehatan_gigi');
                        if(isset($post['diagnosa_kesehatan'])){
                            $data =array();
                            $index = 0;
                            foreach ($post['diagnosa_kesehatan'] as $input) {
                                if($post['diagnosa_kesehatan'][$index] != ""){
                                    array_push($data, array(
                                        'kunjungan' => $post['nokun'],
                                        'diagnosis' => $post['diagnosa_kesehatan'][$index],
                                    ));
                                }
                            $index++;
                            }
                            $this->DiagnosaKesehatanModel->insert($data, TRUE);
                        }

                        $this->db->where('kunjungan',$post['nokun']);
                        $this->db->delete('keperawatan.tb_skrining_nyeri_gigi');
                        if($post['nyeri_gigi'] == '1805'){
                            $data =array();
                            for($i=1; $i<=$post['nomor']; $i++){
                                if(isset($post["elemen_gigi_$i"]) && isset($post["skala_gigi_$i"])){
                                    if($post["elemen_gigi_$i"] != "" && $post["skala_gigi_$i"]){
                                        array_push($data, array(
                                            'kunjungan' => $post["nokun"],
                                            'elemen_gigi' => $post["elemen_gigi_$i"],
                                            'skala_nyeri' => $post["skala_gigi_$i"],
                                        ));
                                    }
                                }
                            }
                            $this->db->insert_batch('keperawatan.tb_skrining_nyeri_gigi',$data);
                        }
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataPengkajianGigi = $this->PengkajianGigiModel->get($post['nokun'], true);
                $data = array();
    			if(!empty($dataPengkajianGigi)){
    				echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataPengkajianGigi
                    ));
    			}else{
                    echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataPengkajianGigi
                    ));
                }
            }else if($param == 'ambilDiagnosaKesehatan'){
                $post = $this->input->post(NULL,TRUE);
                $this->db->where(array('kunjungan' => $post['nokun'], 'jenis' => 1));
                // $query = $this->db->get('keperawatan.tb_diagnosis_kesehatan_gigi')->result();
                $dataDiagnosaKesehatan = $this->db->get('keperawatan.tb_diagnosis_kesehatan_gigi')->result();
    			if(!empty($dataDiagnosaKesehatan)){
    				echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataDiagnosaKesehatan
                    ));
    			}else{
                    echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataDiagnosaKesehatan
                    ));
                }
            }else if($param == 'ambilSkriningNyeri'){
                $post = $this->input->post(NULL,TRUE);
                $this->db->where('kunjungan',$post['nokun']);
                $dataSkriningNyeri = $this->db->get('keperawatan.tb_skrining_nyeri_gigi')->result();
    			if(!empty($dataSkriningNyeri)){
    				echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataSkriningNyeri
                    ));
    			}else{
                    echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataSkriningNyeri
                    ));
                }
            }else if($param == 'count'){
                $result = $this->PengkajianGigiModel->get_count();;
                echo json_encode($result);
            }
            //else if($param == 'tolak'){
    		// 	$post = $this->input->post(NULL,TRUE);
    		// 	if(!empty($post['kunjungan'])){
            //         $data = array(
            //             'KUNJUNGAN' => $post['kunjungan'],
            //             'NOMOR_LAB' => $post['nolab'],
            //             'CATATAN' => $post['catatan'],
            //             'OLEH' => $this->session->userdata('id'),
            //         );
    		// 		$this->Batal_histologi_model->insert($data);
            //         $result = array('status' => 'success');
    		// 	}

    		// 	echo json_encode($result);
    		// }else if($param == 'aktif'){
    		// 	$post = $this->input->post(NULL,TRUE);
    		// 	if(!empty($post['id'])){
            //         if($post['status'] == '1'){
        	// 			$data = array(
        	// 				'STATUS' => '2'
        	// 			);
            //         }else{
            //             $data = array(
            //                 'STATUS' => '1'
            //             );
            //         }
    		// 		$this->Histologi_model->update($data, array('ID' => $post['id']));
            //         $result = array('status' => 'success');
    		// 	}

    		// 	echo json_encode($result);
    		// }
    	}
    }

    public function datatables(){
        $result = $this->PengkajianGigiModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_pengkajian_gigi" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> TANGGAL_ODONTO;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->PengkajianGigiModel->total_count(),
            "recordsFiltered"   => $this->PengkajianGigiModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
