<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Master extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('keuanganModel');
  }

  public function masterRkakl()
  {
    $masterRkakl = $this->keuanganModel->masterRkakl();

    $data = array(
      'title'       => 'Halaman Master RKAKL',
      'isi'         => 'Keuangan/master/rkakl',
      'masterRkakl' => $masterRkakl,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function masterProgram()
  {
    $masterProgram = $this->keuanganModel->masterProgram();

    $data = array(
      'title'       => 'Halaman Master Program',
      'isi'         => 'Keuangan/master/program',
      'masterProgram' => $masterProgram,
    );

    $this->load->view('layout/wrapper',$data);
  }

}

/* End of file Master.php */
/* Location: ./application/controllers/keuangan/Master.php */