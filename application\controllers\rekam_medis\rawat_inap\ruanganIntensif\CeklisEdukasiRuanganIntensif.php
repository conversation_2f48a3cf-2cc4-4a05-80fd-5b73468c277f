<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CeklisEdukasiRuanganIntensif extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/ruanganIntensif/CERIModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_ceri = $this->uri->segment(3);
      
      $data = array(
        'nokun' => $nokun,
        'id_ceri' => isset($id_ceri) ? $id_ceri : "",
        'getPengkajian' => $this->CERIModel->getPengkajian($id_ceri),
        'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
        'listKepada' => $this->masterModel->referensi(1548),
        'listFasilitasRuangan' => $this->masterModel->referensi(1549),
        'listKebijakan' => $this->masterModel->referensi(1550),
        'listRuanganICU' => $this->masterModel->referensi(1551),
        'listRuanganHCU' => $this->masterModel->referensi(1552),
        'listManKes' => $this->masterModel->referensi(1553),
        'listPegawai' => $this->masterModel->listAllPegawai()
      );
      $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/ceklisEdukasiRuanganIntensif',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
          $ttd_menjelaskan = $this->input->post('ttd_menjelaskan');
          $ttd_menerima = $this->input->post('ttd_menerima');
          $data = array(
            'nokun' => $post['nokun'],
            'kepada' => isset($post['kepada']) ? $post['kepada']: "",
            'kepada_lain' => isset($post['kepada_lain']) ? $post['kepada_lain']: "",
            'fasilitas_ruangan' => isset($post['fasilitas_ruangan']) ? json_encode($post['fasilitas_ruangan']): "",
            'kebijakan_tatatertib' => isset($post['kebijakan_tatatertib']) ? json_encode($post['kebijakan_tatatertib']): "",
            'ruangan_icu' => isset($post['ruangan_icu']) ? json_encode($post['ruangan_icu']): "",
            'ruangan_hcu' => isset($post['ruangan_hcu']) ? json_encode($post['ruangan_hcu']): "",
            'ruangan_picu' => isset($post['ruangan_picu']) ? json_encode($post['ruangan_picu']): "",
            'manajemen_keselamatan' => isset($post['manajemen_keselamatan']) ? json_encode($post['manajemen_keselamatan']): "",
            'ttd_menjelaskan' => isset($ttd_menjelaskan) ? file_get_contents($ttd_menjelaskan) : "",
            'ttd_menerima' => isset($ttd_menerima) ? file_get_contents($ttd_menerima) : "",
            'nama_menjelaskan' => isset($post['nama_menjelaskan']) ? $post['nama_menjelaskan']: "",
            'nama_penerima' => isset($post['nama_penerima']) ? $post['nama_penerima']: "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_ceri'])) {
            $this->db->where('keperawatan.tb_ceri.id', $post['id_ceri']);
            $this->db->update('keperawatan.tb_ceri', $data);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_ceri', $data);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->CERIModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->CERIModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editCeklisEdukasiRuanganIntensif" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->CERIModel->total_count(),
            "recordsFiltered"   => $this->CERIModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}