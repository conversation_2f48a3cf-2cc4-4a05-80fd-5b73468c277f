<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengkajianRisikoJatuhPasienDewasa extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listRiwayatJatuh = $this->masterModel->referensi(1006);
        $listDiagnosisSekunder = $this->masterModel->referensi(1007);
        $listAlatBantu = $this->masterModel->referensi(1008);
        $listMenggunakanInfus = $this->masterModel->referensi(1009);
        $listCaraBerjalan = $this->masterModel->referensi(1010);
        $listStatusMental = $this->masterModel->referensi(1011);
        $historyPengkajianRisikoJatuhPasienDewasa = $this->pengkajianAwalModel->historyPengkajianRisikoJatuhPasienDewasa($getNomr['NORM']);

        $data = array(
            'getNomr' => $getNomr,
            'listRiwayatJatuh' => $listRiwayatJatuh,
            'listDiagnosisSekunder' => $listDiagnosisSekunder,
            'listAlatBantu' => $listAlatBantu,
            'listMenggunakanInfus' => $listMenggunakanInfus,
            'listCaraBerjalan' => $listCaraBerjalan,
            'listStatusMental' => $listStatusMental,
            'historyPengkajianRisikoJatuhPasienDewasa' => $historyPengkajianRisikoJatuhPasienDewasa,
        );

        $this->load->view('Pengkajian/igd/pengkajianRisikoJatuhPasienDewasa/index', $data);
    }

    public function simpanPengkajianRisikoJatuhPasienDewasa()
    {
        $kunjungan = $this->input->post("nokun");
        $pengguna = $this->input->post("pengguna");
        $tanggal = $this->input->post("tanggal");
        $jam = $this->input->post("jam");
        $riwayatjatuh = $this->input->post("riwayatjatuh");
        $diagnosissekunder = $this->input->post("diagnosissekunder");
        $alatbantu = $this->input->post("alatbantu");
        $infus = $this->input->post("infus");
        $caraberjalan = $this->input->post("caraberjalan");
        $statusmental = $this->input->post("statusmental");

        $data = array(
            'nokun' => $kunjungan,
            'tanggal' => $tanggal,
            'jam' => $jam,
            'riwayat_jatuh' => $riwayatjatuh,
            'diagnosis_sekunder' => $diagnosissekunder,
            'alat_bantu' => $alatbantu,
            'infus' => $infus,
            'cara_berjalan' => $caraberjalan,
            'status_mental' => $statusmental,
            'oleh' => $pengguna,
        );

        $this->db->trans_begin();
        $this->db->insert('keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function ubahPengkajianRisikoJatuhPasienDewasa()
    {
        $id_risikojatuh = $this->input->post("id_risikojatuh");
        $kunjungan = $this->input->post("nokun_edit");
        $pengguna = $this->input->post("pengguna_edit");
        $tanggal = $this->input->post("tanggal_edit");
        $jam = $this->input->post("jam_edit");
        $riwayatjatuh = $this->input->post("riwayatjatuh_edit");
        $diagnosissekunder = $this->input->post("diagnosissekunder_edit");
        $alatbantu = $this->input->post("alatbantu_edit");
        $infus = $this->input->post("infus_edit");
        $caraberjalan = $this->input->post("caraberjalan_edit");
        $statusmental = $this->input->post("statusmental_edit");

        $dataUbah = array(
            'nokun' => $kunjungan,
            'tanggal' => $tanggal,
            'jam' => $jam,
            'riwayat_jatuh' => $riwayatjatuh,
            'diagnosis_sekunder' => $diagnosissekunder,
            'alat_bantu' => $alatbantu,
            'infus' => $infus,
            'cara_berjalan' => $caraberjalan,
            'status_mental' => $statusmental,
            'oleh' => $pengguna,
        );

        $this->db->trans_begin();

        $this->db->where('keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa.id', $id_risikojatuh);
        $this->db->update('keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa', $dataUbah);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryPengkajianRisikoJatuhPasienDewasa()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $historyDetailPengkajianRisikoJatuhPasienDewasa = $this->pengkajianAwalModel->historyDetailPengkajianRisikoJatuhPasienDewasa($id);
        $listRiwayatJatuh = $this->masterModel->referensi(1006);
        $listDiagnosisSekunder = $this->masterModel->referensi(1007);
        $listAlatBantu = $this->masterModel->referensi(1008);
        $listMenggunakanInfus = $this->masterModel->referensi(1009);
        $listCaraBerjalan = $this->masterModel->referensi(1010);
        $listStatusMental = $this->masterModel->referensi(1011);

        $dataEdit = array(
            'risikojatuh' => $historyDetailPengkajianRisikoJatuhPasienDewasa,
            'getNomr' => $getNomr,
            'listRiwayatJatuh' => $listRiwayatJatuh,
            'listDiagnosisSekunder' => $listDiagnosisSekunder,
            'listAlatBantu' => $listAlatBantu,
            'listMenggunakanInfus' => $listMenggunakanInfus,
            'listCaraBerjalan' => $listCaraBerjalan,
            'listStatusMental' => $listStatusMental,
        );

        $this->load->view('Pengkajian/igd/pengkajianRisikoJatuhPasienDewasa/modalViewEditPengkajianRisikoJatuhPasienDewasa', $dataEdit);
    }

    // Pelaksanaa nPencegahan Pasien Jatuh Dewasa

    public function simpanPelaksanaanPencegahanPasienJatuhDewasa()
    {
        $pengguna = $this->input->post("pengguna");
        $jenis_pengkajian = $this->input->post("jenis_pengkajian");
        $id_pengkajian = $this->input->post("id_pengkajian");
        $tanggal = $this->input->post("tanggal");
        $jam = $this->input->post("jam");
        $pencegahanrisikojatuhrendah = $this->input->post("pencegahanrisikojatuhrendah");
        $pencegahanrisikojatuhtinggi = $this->input->post("pencegahanrisikojatuhtinggi");

        $data = array(
            'id_pengkajian' => $id_pengkajian,
            'tanggal' => $tanggal,
            'jam' => $jam,
            'jenis_pengkajian' => $jenis_pengkajian,
            'resiko_rendah' => isset($pencegahanrisikojatuhrendah) ? implode($pencegahanrisikojatuhrendah,',') : "",
            'resiko_tinggi' => isset($pencegahanrisikojatuhtinggi) ? implode($pencegahanrisikojatuhtinggi,',') : "",
            'oleh' => $pengguna,
        );

        $this->db->trans_begin();
        $this->db->insert('keperawatan.tb_pencegahan_pasien_jatuh', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);

    }

    public function modalPasienDewasaRisikoRendahTinggi()
    {
        $nokun = $this->input->post('nokun');
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $id = $this->input->post('id');
        $total = $this->input->post('total');
        $listIntervensiPencegahanJatuhRisikoRendah = $this->masterModel->referensi(1012);
        $listIntervensiPencegahanJatuhRisikoTinggi = $this->masterModel->referensi(1013);
        $historyPelaksanaanPencegahanPasienJatuhDewasa = $this->pengkajianAwalModel->historyPelaksanaanPencegahanPasienJatuhDewasa($getNomr['NORM']);

        $data = array(
            'getNomr' => $getNomr,
            'id_pengkajian' => $id,
            'total_nilai' => $total,
            'listIntervensiPencegahanJatuhRisikoRendah' => $listIntervensiPencegahanJatuhRisikoRendah,
            'listIntervensiPencegahanJatuhRisikoTinggi' => $listIntervensiPencegahanJatuhRisikoTinggi,
            'historyPelaksanaanPencegahanPasienJatuhDewasa' => $historyPelaksanaanPencegahanPasienJatuhDewasa,

        );
        $this->load->view('Pengkajian/igd/pengkajianRisikoJatuhPasienDewasa/modalPasienDewasaRisikoRendahTinggi', $data);
    }

    public function lihatHistoryPelaksanaanPencegahanPasienJatuhDewasa()
    {
        $id = $this->input->post('id');
        $pelaksanaan = $this->pengkajianAwalModel->historyDetailPelaksanaanPencegahanPasienJatuhDewasa($id);
        $listIntervensiPencegahanJatuhRisikoRendah = $this->masterModel->referensi(1012);
        $listIntervensiPencegahanJatuhRisikoTinggi = $this->masterModel->referensi(1013);

        $data = array(
            'pelaksanaan' => $pelaksanaan,
            'listIntervensiPencegahanJatuhRisikoRendah' => $listIntervensiPencegahanJatuhRisikoRendah,
            'listIntervensiPencegahanJatuhRisikoTinggi' => $listIntervensiPencegahanJatuhRisikoTinggi,
        );


        $this->load->view('Pengkajian/igd/pengkajianRisikoJatuhPasienDewasa/modalViewEditPelaksanaanPencegahanPasienJatuhDewasa',$data);
    }

    public function ubahPelaksanaanPencegahanPasienJatuhDewasa()
    {
        $pengguna = $this->input->post("pengguna_edit");
        $id_pelaksanaan = $this->input->post("id_pelaksanaan_edit");
        $jenis_pengkajian = $this->input->post("jenis_pengkajian_edit");
        $id_pengkajian = $this->input->post("id_pengkajian_edit");
        $tanggal = $this->input->post("tanggal_edit");
        $jam = $this->input->post("jam_edit");
        $pencegahanrisikojatuhrendah = $this->input->post("pencegahanrisikojatuhrendah_edit");
        $pencegahanrisikojatuhtinggi = $this->input->post("pencegahanrisikojatuhtinggi_edit");

        $dataUbah = array(
            'id_pengkajian' => $id_pengkajian,
            'tanggal' => $tanggal,
            'jam' => $jam,
            'jenis_pengkajian' => $jenis_pengkajian,
            'resiko_rendah' => isset($pencegahanrisikojatuhrendah) ? implode($pencegahanrisikojatuhrendah,',') : "",
            'resiko_tinggi' => isset($pencegahanrisikojatuhtinggi) ? implode($pencegahanrisikojatuhtinggi,',') : "",
            'oleh' => $pengguna,
        );

        $this->db->trans_begin();

        $this->db->where('keperawatan.tb_pencegahan_pasien_jatuh.id', $id_pelaksanaan);
        $this->db->update('keperawatan.tb_pencegahan_pasien_jatuh', $dataUbah);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }
}