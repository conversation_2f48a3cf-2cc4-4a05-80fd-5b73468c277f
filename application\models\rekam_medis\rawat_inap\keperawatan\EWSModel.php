<?php
defined('BASEPATH') or exit('No direct script access allowed');

class EWSModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_ews';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'ref' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpanEWS($dataEWS)
  {
    $this->db->insert('keperawatan.tb_ews', $dataEWS);
    return $this->db->insert_id();
  }

  public function ubahEWS($idObservasi, $dataEWS)
  {
    $this->db->where('keperawatan.tb_ews.ref', $idObservasi)->or_where('keperawatan.tb_ews.id_otk', $idObservasi);
    $this->db->update('keperawatan.tb_ews', $dataEWS);
  }

  public function history($nomr)
  {
    $this->db->select(
      'e.nokun,
      (
        SELECT ews.id
        FROM keperawatan.tb_ews ews
        WHERE ews.status = 1 AND ews.nokun = e.nokun
        ORDER BY ews.date_created DESC
        LIMIT 1
      ) id,
      (
        SELECT ews.tanggal
        FROM keperawatan.tb_ews ews
        WHERE ews.status = 1 AND ews.nokun = e.nokun
        ORDER BY ews.date_created DESC
        LIMIT 1
      ) tanggal,
      (
        SELECT ews.jam
        FROM keperawatan.tb_ews ews
        WHERE ews.status = 1 AND ews.nokun = e.nokun
        ORDER BY ews.date_created DESC
        LIMIT 1
      ) jam,
      (
        SELECT ews.score_ews
        FROM keperawatan.tb_ews ews
        WHERE ews.status = 1 AND ews.nokun = e.nokun
        ORDER BY ews.date_created DESC
        LIMIT 1
      ) score_ews,
      (
        SELECT pegz.NAMA
        FROM keperawatan.tb_ews ews
        LEFT JOIN master.pegawai pegz ON pegz.NIP = ews.perawat2
        WHERE ews.status = 1 AND ews.nokun = e.nokun
        ORDER BY ews.date_created DESC
        LIMIT 1
      ) perawat2,
      (
        SELECT master.getNamaLengkapPegawai(penggu.NIP)
        FROM keperawatan.tb_ews ews
        LEFT JOIN aplikasi.pengguna penggu ON penggu.ID = ews.oleh
        WHERE ews.status = 1 AND ews.nokun = e.nokun
        ORDER BY ews.date_created DESC
        LIMIT 1
      ) pengisi,
      (
        SELECT ews.status
        FROM keperawatan.tb_ews ews
        WHERE ews.status = 1 AND ews.nokun = e.nokun
        ORDER BY ews.date_created DESC
        LIMIT 1
      ) status,
      IF(k.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) ruang'
    );
    $this->db->from('keperawatan.tb_ews e');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = e.oleh', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = e.perawat2', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = e.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'left');
    $this->db->join('master.ruangan r', 'r.ID = tp.RUANGAN', 'left');
    $this->db->join('master.ruangan rk', 'rk.ID = k.RUANGAN', 'left');
    $this->db->where('p.NORM', $nomr);
    $this->db->where('e.status', 1);
    $this->db->group_by('e.nokun');
    $this->db->order_by('e.tanggal', 'desc');
    $this->db->order_by('e.jam', 'desc');
    $query = $this->db->get();
    return $query->result_array();
  }

  public function keterangan($id, $nokun)
  {
    $this->db->select(
      'e.id, e.nokun, e.tanggal, e.jam, e.score_ews, peg.NAMA perawat2, master.getNamaLengkapPegawai(peng.NIP) pengisi,
      e.status'
    );
    $this->db->from('keperawatan.tb_ews e');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = e.oleh', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = e.perawat2', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = e.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
    if (isset($id)) {
      $this->db->where('e.id', $id);
    } elseif (isset($nokun)) {
      $this->db->where('e.nokun', $nokun);
      $this->db->where('e.status !=', 0);
      $this->db->order_by('e.id', 'desc');
    }
    $query = $this->db->get();
    return $query->row_array();
  }

  public function detailHistory($nokun)
  {
    $this->db->select(
      'e.id, e.tanggal, e.jam, tv.pernapasan, tv.nadi, tv.td_sistolik, tv.td_diastolik, tv.suhu, o2.saturasi_o2,
      vo2.variabel penggunaan_o2, vk.variabel kesadaran, e.score_ews, e.nokun, e.perawat2,
      master.getNamaLengkapPegawai(peng.NIP) pengisi'
    );
    $this->db->from('keperawatan.tb_ews e');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'e.id_tanda_vital = tv.id', 'left');
    $this->db->join('db_pasien.tb_o2 o2', 'e.id_o2 = o2.id', 'left');
    $this->db->join('db_pasien.tb_kesadaran k', 'e.id_kesadaran = k.id', 'left');
    $this->db->join('db_master.variabel vo2', 'vo2.id_variabel = o2.penggunaan_o2', 'left');
    $this->db->join('db_master.variabel vk', 'vk.id_variabel = k.kesadaran', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = e.oleh', 'left');
    $this->db->where('e.status !=', 0);
    $this->db->where('e.nokun', $nokun);
    $this->db->order_by('e.tanggal', 'desc');
    $this->db->order_by('e.jam', 'desc');
    return $this->db->get();
  }
}

/* End of file EWSModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/keperawatan/EWSModel.php */