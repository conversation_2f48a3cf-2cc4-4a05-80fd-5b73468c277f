<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RadiologiHistory extends CI_Controller
{
    public function __construct()
    {
      parent::__construct();
      if ($this->session->userdata('logged_in') == false) {
        redirect('login');
      }

      date_default_timezone_set('Asia/Jakarta');
      $this->load->model(
        array(
          'masterModel',
          'pengkajianAwalModel',
          'rekam_medis/penunjang/radiologi/RadiologiHistoryModel'
        )
      );
    }

    public function datatables(){
      $result = $this->RadiologiHistoryModel->datatables();
      $no = 1;
      $data = array();
      foreach ($result as $row){
        if (isset($row->DOKTER_ASAL)) {
          $dokterPerujuk = $row -> DOKTER_ASAL;
        } else {
          $dokterPerujuk = '-';
        }

        $sub_array = array();
        $sub_array[] = $no;
        $sub_array[] = $row -> NOMOR_LAB;
        $sub_array[] = date('d-m-Y H:i:s', strtotime($row->TANGGAL_ORDER));
        $sub_array[] = $row -> RUANG_ASAL;
        // $sub_array[] = $row -> RUANG_TUJUAN;
        $sub_array[] = $row -> STATUS_ORDER;
        $sub_array[] = date('d-m-Y', strtotime($row->TANGGAL_RENCANA));
        $sub_array[] = $dokterPerujuk;
        $sub_array[] = '<a href="#viewDetilORderRadiologi" class="btn btn-sm btn-primary btn-block" data-toggle="modal" data-id="'.$row -> NOMOR_LAB.'" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> View</a>
        <a href="/reports/simrskd/penunjang/penunjangRadiologi.php?format=pdf&id='.$row -> NOMOR_LAB.'" class="btn btn-success btn-block btn-sm" target="_blank"><i class="fa fa-print"></i> Cetak</a>';

        $no++;
        $data[] = $sub_array;
      }

      $output = array(
        "draw"              => intval($_POST["draw"]),
        "recordsTotal"      => $this->RadiologiHistoryModel->total_count(),
        "recordsFiltered"   => $this->RadiologiHistoryModel->filter_count(),
        "data"              => $data
      );
      echo json_encode($output);
    }

    public function detilOrderRadiologi()
    {
      $id = $this->input->post('id');
      $OrderRad = $this->pengkajianAwalModel->historyDetilOrderRadiologi($id);
      $hOrderRadiologi = $this->pengkajianAwalModel->detailOrderRadiologi($id);
      echo '<div class="row">' .
          '<div class="col-lg-6">' .
          '<div class="form-group">' .
          '<table class="table" style="background-color:#9ccbe2; color: #242a30; font-size: 13px; line-height:15px;">' .
          '<tr>' .
          '<td>Tanggal Order</td>' .
          '<td>' . date("d-m-Y H:i:s", strtotime($hOrderRadiologi['TANGGAL_ORDER'])) . '</td>' .
          '</tr>' .
          '<tr>' .
          '<td>Pengirim</td>' .
          '<td>' . $hOrderRadiologi['OLEH'] . '</td>' .
          '</tr>' .
          '<tr>' .
          '<td>DPJP</td>' .
          '<td>' . $hOrderRadiologi['DPJP'] . '</td>' .
          '</tr>' .
          '<tr>' .
          '<td>Ruang Asal Order</td>' .
          '<td>' . $hOrderRadiologi['RUANG_ASAL'] . '</td>' .
          '</tr>' .
          '<tr>' .
          '<td>Dokter Perujuk</td>' .
          '<td>' . $hOrderRadiologi['DOKTER_ASAL'] . '</td>' .
          '</tr>' .
          '</table>' .
          '</div>' .
          '</div>' .
          '<div class="col-lg-6">' .
          '<div class="form-group">' .
          '<table class="table" style="background: #f7ca71; color: #242a30; font-size: 13px; line-height:15px;">' .
          '<tr>' .
          '<td>Status Order</td>' .
          '<td>' . $hOrderRadiologi['STATUS_ORDER'] . '</td>' .
          '</tr>' .
          '</table>' .
          '</div>' .
          '</div>' .
          '</div>';
      echo "<table class='table table-bordered table-bordered dt-responsive' id='tblDetilOrderRadiologi'  cellspacing='0' width='100%''>";
      echo "<thead>";
      echo "<tr>";
      echo "<th width='5%'>No</th>";
      echo "<th>Nama Tindakan</th>";
      echo "<th>Tindakan SIMPEL</th>";
      echo "</tr>";
      echo "</thead>";

      echo "<tbody>";
      $no = 1;
      foreach ($OrderRad as $OrderRad) :
          echo "<tr>";
          echo "<td>" . $no . "</td>";
          echo "<td>" . $OrderRad['TINDAKAN_SIMRSKD'] . "</td>";
          echo "<td>" . $OrderRad['TINDAKAN_SIMPEL'] . "</td>";
          echo "</tr>";

          $no++;
      endforeach;
      echo "</tbody>";
      if ($hOrderRadiologi['STATUS'] == 1) {
          echo "<tfooter>
          <tr>
              <td colspan='6'>
                  <div class='row'>
                      <div class='offset-11'>
                          <a href='#' id='batalRad' class='btn btn-sm btn-danger' data-toggle='modal' data-id='$id' data-backdrop='static' data-keyboard='false'><i class='fa fa-times'></i> Batalkan</a>
                      </div>
                  </div>
              </td>
          </tr>
          </tfooter>";
      }
      echo "</table>";
    }
}
