<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PAKPerawat extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PAKPerawatModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/PAKMedisModel'));
  }

  public function index()
  {
    $pasien = $this->PAKPerawatModel->getNomrRawatInap($this->uri->segment(2));
    $getPengkajian = $this->PAKPerawatModel->getPengkajian($pasien['ID_EMR_PAK_PERAWAT']);
    $get_pmfak = $this->PAKMedisModel->get_pmfak($pasien['NOPEN']);

    $data = array(
      'nopen' => $this->uri->segment(2),
      'pasien' => $pasien,
      'getPengkajian' => $getPengkajian,
      'listRiwayatKesehatan' => $this->masterModel->referensi(487),
      'listKondisiPsikologis' => $this->masterModel->referensi(488),
      'listKondisi' => $this->masterModel->referensi(489),
      'listKondisiPasien' => $this->masterModel->referensi(490),
      'listKondisiKeluarga' => $this->masterModel->referensi(491),
      'listBerduka' => $this->masterModel->referensi(492),
      'listBerdukaKeluarga' => $this->masterModel->referensi(494),
      'listBerdukaPasien' => $this->masterModel->referensi(493),
      'listPotensiReaksi' => $this->masterModel->referensi(496),
      'listPasienPsikolog' => $this->masterModel->referensi(497),
      'listKeluargaPsikolog' => $this->masterModel->referensi(498),
      'listKebutuhanSpiritual' => $this->masterModel->referensi(499),
      'listKebutuhanSpiritualPasien' => $this->masterModel->referensi(500),
      'listKebutuhanSpiritualKeluarga' => $this->masterModel->referensi(501),
      'listKebutuhanPendukung' => $this->masterModel->referensi(502),
      'listTerapiKomplementer' => $this->masterModel->referensi(503),
      'listMembutuhkanCaregiver' => $this->masterModel->referensi(504),
      'listPerawatanDirumah' => $this->masterModel->referensi(505),
      'listMasalahKeperawatan' => $this->masterModel->referensi(507),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'get_pmfak' => $get_pmfak
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/pengkajianPAKPerawat', $data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $this->db->trans_begin();
          $post = $this->input->post();

          $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
          $idRefEmr = $this->input->post('idemr');

          $dataKeperawatan = array(
            'id_emr' => $getIdEmr,
            'nopen' => $post['nopen'],
            'nokun' => $post['nokun'],
            'jenis' => 14,
            'diagnosa_masuk' => $post['rujukanPAKP'],
            'created_by' => $this->session->userdata('id'),
            'flag' => '1',
          );

          $dataRiwayatKesehatan = array(
            'id_emr' => $getIdEmr,
            'kesehatan_kondisi_pak' => isset($post['riwayatkesehatan']) ? json_encode($post['riwayatkesehatan']) : "",
            'kesehatan_kondisi_pak_lain' => isset($post['kesehatan_kondisi_pak_lain']) ? $post['kesehatan_kondisi_pak_lain'] : "",
          );

          $dataKondisi_kebutuhan_pak = array(
            'id_emr' => $getIdEmr,
            'kondisipasien' => isset($post['kondisipasien']) ? $post['kondisipasien'] : "",
            'kondisikeluarga' => isset($post['kondisikeluarga']) ? $post['kondisikeluarga'] : "",
            'berdukapasien' => isset($post['berdukapasien']) ? json_encode($post['berdukapasien']) : "",
            'berdukakeluarga' => isset($post['berdukakeluarga']) ? json_encode($post['berdukakeluarga']) : "",
            'potensireaksi' => isset($post['potensireaksi']) ? $post['potensireaksi'] : "",
            'pasienpsikolog' => isset($post['pasienpsikolog']) ? $post['pasienpsikolog'] : "",
            'keluargapsikolog' => isset($post['keluargapsikolog']) ? $post['keluargapsikolog'] : "",
            'spiritualpasien' => isset($post['spiritualpasien']) ? $post['spiritualpasien'] : "",
            'spiritualkeluarga' => isset($post['spiritualkeluarga']) ? $post['spiritualkeluarga'] : "",
            'terapikomplementer' => isset($post['terapikomplementer']) ? $post['terapikomplementer'] : "",
            'membutuhkancaregiver' => isset($post['membutuhkancaregiver']) ? $post['membutuhkancaregiver'] : "",
            'perawatandirumah' => isset($post['perawatandirumah']) ? $post['perawatandirumah'] : "",
            'alasan_perawatan_dirumah' => isset($post['alasan_perawatan_dirumah']) ? $post['alasan_perawatan_dirumah'] : ""

          );

          if (!empty($post['idemr'])) {
            $this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan);
            $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
            $this->db->replace('keperawatan.tb_kondisi_kebutuhan_pak', $dataKondisi_kebutuhan_pak);

            $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $idRefEmr));
            $dataAsuhanKeperawatan = array();
            $index = 0;
            $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
            if (isset($post['asuhanKeperawatan'])) {
              foreach ($post['asuhanKeperawatan'] as $input) {
                if ($post['asuhanKeperawatan'][$index] != "") {
                  $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                  array_push(
                    $dataAsuhanKeperawatan, array(
                      'id_emr' => $getIdEmr,
                      'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                      'lain_lain' => isset($post[$id]) ? $post[$id] : null
                    )
                  );
                }
                $index++;
              }
              $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
            }

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          } else {
            $this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan);
            $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
            $this->db->insert('keperawatan.tb_kondisi_kebutuhan_pak', $dataKondisi_kebutuhan_pak);
            
            $dataAsuhanKeperawatan = array();
            $index = 0;
            $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
            if (isset($post['asuhanKeperawatan'])) {
              foreach ($post['asuhanKeperawatan'] as $input) {
                if ($post['asuhanKeperawatan'][$index] != "") {
                  $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                  array_push(
                    $dataAsuhanKeperawatan, array(
                      'id_emr' => $getIdEmr,
                      'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                      'lain_lain' => isset($post[$id]) ? $post[$id] : null
                    )
                  );
                }
                $index++;
              }
              $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
            }

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }
      }

      else if($param == 'count'){
        $result = $this->PAKPerawatModel->get_count();;
        echo json_encode($result);
      }

    }
  }

  public function asuhanKeperawatan_edit()
  {
      $id = $this->input->post('id');
      $idemr = $this->input->post('idemr');

      $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
      $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
      $getPengkajian = $this->PAKPerawatModel->getPengkajian($idemr);

      $data = array(
          'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
          'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
          'getPengkajian' => $getPengkajian,
      );

      $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
  }

  public function datatables(){
    $result = $this->PAKPerawatModel->historyPengkajian();

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $data = array();
        foreach ($result as $row){
          $tombolCetak = '<a class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
          $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
          $action = "";
          $verif = "";
          $userLogin = $this->session->userdata('status');
  
          // KONDISI UNTUK ADA PENGKAJIAN PERAWAT
          if($row -> ID_EMR_PERAWAT != null){
            if($userLogin == 2){
              $jenisPengkajian = $row -> JENIS_PENGKAJIAN_KEPERAWATAN;

              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
  
              if($jenisPengkajian == 5){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
              }elseif($jenisPengkajian == 6){
                $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
              }elseif($jenisPengkajian == 7){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
              }elseif($jenisPengkajian == 11){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
                $action .= '<a href="#lihatPemantauanAsesmenRestrain" class="btn btn-primary btn-block btn-sm showPemantauanAsesmenRestrain" data-toggle="modal" data-idpemasres="'.$row -> ID_EMR_PERAWAT.'" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Pemantauan Restrain</a>';
              }elseif($jenisPengkajian == 14){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Perawat Pengkajian Pasien Akhir Kehidupan</button>';
              }else{
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
              }
            }elseif($userLogin == 1){
              $jenisPengkajian = $row -> JENIS_PENGKAJIAN_MEDIS;

              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
  
              if($jenisPengkajian == 5){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
              }elseif($jenisPengkajian == 6){
                $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
              }elseif($jenisPengkajian == 7){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
              }elseif($jenisPengkajian == 11){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
              }elseif($jenisPengkajian == 14){
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPAKPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Perawat Pengkajian Pasien Akhir Kehidupan</button>';
              }else{
                $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
              }
            }
  
          }
  
          // KONDISI UNTUK ADA PENGKAJIAN MEDIS
          if($row -> ID_EMR_MEDIS != null){
            if($userLogin == 1){
              if($row -> STATUS_VERIFIKASI == 0){
                if($row -> ID_EMR_PERAWAT != null){
                  $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
                }else{
                  $verif = $row -> INFO_VERIFIKASI;
                }
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
  
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
            }elseif($userLogin == 2){
              $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="2"><i class="fa fa-eye"></i>  View Medis</button>';
            }
          }
  
          $sub_array = array();
          $sub_array[] = $row -> INFO;
          $sub_array[] = $verif;
          $sub_array[] = $row -> RUANGAN;
          $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
          $sub_array[] = $action;
          $sub_array[] = $row -> DPJP;
          $sub_array[] = $namaVerif;
          $sub_array[] = $tombolCetak;
          $sub_array[] = $row -> USER_MEDIS;
          $sub_array[] = $row -> USER_PERAWAT;   
          $data[] = $sub_array;
        }
  
        $output = array(
            "draw" => intval($this->input->post("draw")),
            "data"              => $data
        );
        echo json_encode($output);
  }

}

/* End of file MedisDewasa.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasa.php */
