<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PatologiKlinikModel extends MY_Model
{
    protected $_table_name = 'medis.tb_validasi_malnutrisi';
    protected $_primary_key = 'nokun';
    protected $_order_by = 'nokun';
    protected $_order_by_type = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $nokun = $this->input->post('nokun');
        $kritis = !empty($this->input->post('kritis')) ? $this->input->post('kritis') : 0;
        $lis_kode = array('00000019','00000034','00000013','00000019','00000034','00000096','00000097','00000104','00000105','00000251','00000013','00000105','00000719','00000013','00000019','00000034','00000252','00000505','00000681','00000762','00000099');
        $param_tindakan = array(1430004,1430005,1435006,1435007,1435015,1530001,1534001,1540001,1526001,1820001,1430003,1526001,1576009,1439006,1439008,1439017,1494001,1593008,1593010,1593014,1683001);
        $this->db->select('*');
        $this->db->from("(SELECT 1, hlo.ID, pendK.NOMOR NOKUN, pendK.MASUK, mt.NAMA, hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_HASIL HASIL, hlo.LIS_NILAI_NORMAL NILAI, hlo.LIS_SATUAN SATUAN, hlo.LIS_CATATAN KETERANGAN, hlo.LIS_FLAG , hlo.LIS_KODE_TEST, '' AS PARAMETER_TINDAKAN
        FROM lis.hasil_log hlo
        LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
        LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
        WHERE hlo.HIS_NO_LAB='$nokun'
        UNION ALL
        SELECT 3, lhl.ID, pendK.NOMOR NOKUN, pendK.MASUK, mt.NAMA, mptl.PARAMETER, lhl.HASIL, lhl.NILAI, lhl.SATUAN, lhl.KETERANGAN, lhl.LIS_FLAG , '' AS LIS_KODE_TEST, lhl.PARAMETER_TINDAKAN
        FROM pendaftaran.kunjungan pendK
        LEFT JOIN layanan.tindakan_medis ltm ON ltm.KUNJUNGAN = pendK.NOMOR
        LEFT JOIN master.tindakan mt ON mt.ID = ltm.TINDAKAN
        LEFT JOIN master.parameter_tindakan_lab mptl ON mptl.TINDAKAN = mt.ID AND mptl.STATUS!=0
        LEFT JOIN layanan.hasil_lab lhl ON lhl.TINDAKAN_MEDIS = ltm.ID AND lhl.PARAMETER_TINDAKAN = mptl.ID
        WHERE pendK.NOMOR ='$nokun' AND ltm.STATUS=1
        UNION ALL
        SELECT 2, lhl.ID, pendK.NOMOR NOKUN, pendK.MASUK, mt.NAMA, mptl.PARAMETER, lhl.HASIL, lhl.NILAI, lhl.SATUAN, lhl.KETERANGAN, lhl.LIS_FLAG, '' AS LIS_KODE_TEST, lhl.PARAMETER_TINDAKAN
        FROM pendaftaran.kunjungan pendK
        LEFT JOIN layanan.tindakan_medis ltm ON ltm.KUNJUNGAN = pendK.NOMOR
        LEFT JOIN master.tindakan mt ON mt.ID = ltm.TINDAKAN
        LEFT JOIN master.parameter_tindakan_lab mptl ON mptl.TINDAKAN = mt.ID AND mptl.STATUS!=0
        LEFT JOIN layanan.hasil_lab lhl ON lhl.TINDAKAN_MEDIS = ltm.ID AND lhl.PARAMETER_TINDAKAN = mptl.ID
        WHERE pendK.NOMOR ='$nokun' AND ltm.STATUS=1 AND lhl.HASIL IS NULL) a");
        if($kritis == 1){
            // $this->db->where_in('a.LIS_KODE_TEST', $lis_kode);
            $this->db->where_in('a.PARAMETER_TINDAKAN', $param_tindakan);
        }
        $this->db->group_by(array('a.NAMA', 'a.PARAMETER'));
        $this->db->order_by('a.NAMA asc');
        
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

    function getRuanganPK($id,$penjamin){
        $hasil = "SELECT distinct r.ID, r.DESKRIPSI 
                FROM remun_medis.jadwal rmj
                LEFT JOIN master.ruangan r ON r.ID = rmj.RUANGAN
                WHERE rmj.DOKTER = ?
                AND rmj.TANGGAL >= CURDATE()
                AND if($penjamin=1, rmj.RUANGAN IN ('105020704','105020705'), rmj.RUANGAN='105021201')
                LIMIT 1";
        $bind = $this->db->query($hasil, array($id));
        return $bind;
    }

    function cekNokunLogKritis($nokun){
        $hasil = "SELECT * FROM log.log_hasil_lab_kritis hlk WHERE hlk.nokun = ? AND hlk.status = ?";
        $bind = $this->db->query($hasil, array($nokun,1));
        return $bind;
    }

    function dataTindakanPenangananIGD($nokun){
        $hasil= "CALL layanan.getPenangananIGD(?)";
        $bind = $this->db->query($hasil, array($nokun));
        mysqli_next_result($this->db->conn_id);
        return $bind;
    }
}

/* End of file PatologiKlinikModel.php */
/* Location: ./application/models/rekam_medis/penunjang/patologi_klinik/PatologiKlinikModel.php */