<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FollowUpModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_follow_up_art';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_follow_up_art', $data);
    return $this->db->insert_id();
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_follow_up_art.id', $id);
    $this->db->update('keperawatan.tb_follow_up_art', $data);
  }

  public function history($nokun, $param)
  {
    if (isset($param)) {
      if ($param == 'jumlah') {
        $this->db->select('f.id');
      } elseif ($param == 'tabel') {
        $this->db->select(
          'f.id, f.tanggal, f.waktu, f.tanggal_kunjungan, f.tanggal_rencana,
          master.getNamaLengkapPegawai(p.NIP) pengisi, f.created_at, f.status'
        );
      }
    }
    $this->db->from('keperawatan.tb_follow_up_art f');
    $this->db->join('aplikasi.pengguna p', 'p.ID = f.oleh', 'left');
    $this->db->where('f.nokun', $nokun);
    if (isset($param)) {
      if ($param == 'jumlah') {
        $this->db->where('f.status', 1);
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        $this->db->order_by('f.tanggal', 'desc');
        $this->db->order_by('f.waktu', 'desc');
        return $this->db->get();
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  public function detail($id)
  {
    $this->db->select(
      'f.id, f.nokun, f.tanggal, f.waktu, f.tanggal_kunjungan, f.tanggal_rencana, f.rujuk_masuk, f.dengan_art,
      f.klinik_sebelumnya, tbb.nomr, tbb.tb, tbb.bb, f.status_fungsional, f.stad_klinis, f.status_kehamilan,
      f.metode_kb, f.infeksi_oportunistis, f.ket_infeksi_oportunistis, f.obat_io, f.status_tb, f.ppk, f.ppi,
      f.hasil_akhir, f.obat_arv, f. sisa_obat_arv, f.adherence_art, f.efek_samping, f.ket_efek_samping, f.jumlah_cd4,
      f.hasil_lab, f.diberikan_kondom, f.jumlah_kondom, f.rujuk, f.akhir_follow_up'
    );
    $this->db->from('keperawatan.tb_follow_up_art f');
    $this->db->join('db_pasien.tb_tb_bb tbb', 'tbb.ref = f.id', 'left');
    $this->db->where('tbb.data_source', 47);
    $this->db->where('f.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file FollowUpModel.php */
/* Location: ./application/models/HIV/FollowUpModel.php */