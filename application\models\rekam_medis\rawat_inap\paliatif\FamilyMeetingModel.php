<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FamilyMeetingModel extends MY_Model {

  public function listHistoryFM($nomr)
  {
    $query = $this->db->query("SELECT
                                  fm.*,
                                  master.getNamaLengkapPegawai (ap.NIP) OLEH                                  
                                FROM
                                  medis.tb_family_meeting fm
                                  LEFT JOIN aplikasi.pengguna ap ON ap.ID = fm.created_by 
                                WHERE
                                  fm.STATUS = 1 AND fm.norm = $nomr
                              ");
    return $query;
  }

  public function getFammeet($id)
  {
    $query = $this->db->query("SELECT
                                  fm.*,
                                  fmam.id id_aspek
                                FROM
                                  medis.tb_family_meeting fm
                                  LEFT JOIN medis.tb_family_meeting_aspek_medis fmam ON fmam.id_fammeet = fm.id AND fmam.status = 1
                                WHERE fm.id = $id AND fm.status = 1
                              ");
    return $query->row_array();
  }

  public function getFammeetFamily($id)
  {
    $query = $this->db->query("SELECT
                                  * 
                                FROM
                                  medis.tb_family_meeting_keluarga fmk
                                WHERE fmk.id_fammeet = $id AND fmk.status = 1
                              ");
    return $query->result_array();
  }

  public function getFammeetAspek($id)
  {
    $query = $this->db->query("SELECT
                                  * 
                                FROM
                                  medis.tb_family_meeting_aspek_medis fmk
                                WHERE fmk.id_fammeet = $id AND fmk.status = 1
                              ");
    return $query->row_array();
  }

  public function cekAspek($id)
  {
    $query = $this->db->query("SELECT
    (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 1 AND fmam.status = 1) ASPEK_MEDIS,
    (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 2 AND fmam.status = 1) ASPEK_KEPERAWATAN,
    (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 3 AND fmam.status = 1) ASPEK_PSIKOLOGI,
    (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 4 AND fmam.status = 1) ASPEK_SOSIAL,
    (SELECT COUNT(*) FROM medis.tb_family_meeting_aspek_medis fmam WHERE fmam.id_fammeet = $id AND fmam.jenis_aspek = 5 AND fmam.status = 1) ASPEK_SPIRITUAL
                          ");
    return $query->row_array();
  }

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
?>
