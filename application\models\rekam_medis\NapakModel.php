<?php
defined('BASEPATH') or exit('No direct script access allowed');

class NapakModel extends CI_Model
{
  // public $rules_rencana_tindak_lanjut = array(
  //   'reancanaPemeriksaan' => array(
  //     'field' => 'identifikasiPotensi',
  //     'label' => 'Identifikasi Potensi',
  //     'rules' => 'trim|required',
  //     'errors' => array(
  //       'required' => '%s Wajib <PERSON>.',
  //     ),
  //   ),
  // );

  public function namaPegawai($id)
  {
    $query = $this->db->query("SELECT master.getNamaLengkapPegawai(mp.NIP) NAMAPEGAWAI
     FROM aplikasi.pengguna ap
     LEFT JOIN master.pegawai mp ON ap.NIP = mp.NIP
     WHERE ap.ID = '$id'");

    return $query->row_array();
  }

  function dbFollowUpHistory($id)
  {
    $this->followUpHistory($id);
    if ($_POST["length"] != -1) {
      $this->db->limit($_POST["length"], $_POST["start"]);
    }
    $query = $this->db->get();
    return $query->result();
  }

  public function followUpHistory($id)
  {
    $this->db->select(
      "nfu.id ID_FU
      , nfu.id_pengkajian_napak ID_PENGKAJIAN
      , nfu.jenis JENIS_ROW
      , jv.variabel JENIS
      , jv2.variabel PREOP
      , r.DESKRIPSI SPESIALIS
      , nfu.pemeriksaan PEMERIKSAAN
      , nfu.siklus SIKLUS
      , nfu.total_siklus TOTAL_SIKLUS
      , nfu.nama_obat NAMA_OBAT
      , DATE_FORMAT(nfu.tanggal,'%d-%m-%Y') TANGGAL
      , DATE_FORMAT(nfu.jadwal_janji,'%d-%m-%Y') TANGGAL_TEMU
      , master.getNamaLengkapPegawai(ap.NIP) NAMAPEGAWAI
      , master.getNamaLengkapPegawai(ap2.NIP) DPJP
      "
    );
    $this->db->from('db_layanan.tb_napak_fu nfu');
    $this->db->join('db_master.variabel jv', 'jv.id_variabel = nfu.jenis', 'LEFT');
    $this->db->join('db_master.variabel jv2', 'jv2.id_variabel = nfu.preop', 'LEFT');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = nfu.oleh', 'LEFT');
    $this->db->join('master.dokter ap2', 'ap2.ID = nfu.dpjp', 'LEFT');
    $this->db->join('master.referensi r', 'nfu.spesialis = r.ID AND r.JENIS=26 AND r.STATUS=1', 'LEFT');
    $this->db->where('nfu.id_pengkajian_napak=', $id);
    $this->db->where('nfu.status=', 1);

    $this->db->group_start();
    $this->db->like('jv.variabel', $_POST['search']['value']);
    $this->db->group_end();
  }

  function filter_count_followUpHistory($id)
  {
    $this->followUpHistory($id);
    $query = $this->db->get();
    return $query->num_rows();
  }

  function total_count_followUpHistory($id)
  {
    $this->followUpHistory($id);
    return $this->db->count_all_results();
  }

  public function spesialisDokter()
  {
    $query = $this->db->query("SELECT *
      FROM master.referensi r
      WHERE r.JENIS='26' AND r.STATUS='1'");

    return $query->result_array();
  }

  public function pengkajianNapak($nomr)
  {
    $query = "SELECT np.id ID_NAPAK, np.nopen NOPEN
      , DATE_FORMAT(np.created_at,'%d-%m-%Y') TANGGAL
      FROM db_layanan.tb_napak_pengkajian np
      WHERE np.nomr=? AND np.status=?";

    $bind = $this->db->query($query, array($nomr, 1));
    return $bind->result_array();
  }

  public function dataDiriPasien($nomr)
  {
    $query = $this->db->query(
      "SELECT
        mp.`*`, master.getNamaLengkap(mp.NORM)NAMAPASIEN,
        CONCAT(master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR), ' Tahun') UMUR, mk.NOMOR tlpn,
        mp.ALAMAT domisili, kip.NOMOR nik, pp.NOMOR NOPEN, r.GEDUNG
      FROM master.pasien mp
      LEFT JOIN pendaftaran.pendaftaran pp ON pp.NORM = mp.NORM
      LEFT JOIN master.kontak_pasien mk ON mk.NORM = mp.NORM
      LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = mp.NORM
			LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pp.NOMOR
			LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
      WHERE mp.NORM = '$nomr'
        AND kip.JENIS = 1
      ORDER BY pp.NOMOR DESC
      LIMIT 1"
    );
    if ($query->num_rows() > 0) {
      return $query->row_array();
    } else {
      return $query->num_rows();
    }
  }

  // public function listPasienNapak()
  // {
  //   $query = $this->db->query("SELECT n.id, n.nomr, `master`.getNamaLengkap(n.nomr) nama ,tanggal FROM pendaftaran.napak n WHERE n.status = 1");
  //   return $query->result_array();
  // }

  // public function listPasienNapak()
  // {
  //   $query = $this->db->query("SELECT * FROM (
  //         SELECT md.KODE KODEICD10,
  //             (SELECT ms.STR FROM master.mrconso ms WHERE ms.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND ms.CODE=md.KODE LIMIT 1) diagnosa
  //         , pp.NORM norm,master.getNamaLengkap(pp.NORM) nama
  //         , IF(ps.JENIS_KELAMIN=1,'Laki-laki','Perempuan') JENISKELAMIN
  //         , CONCAT(DATE_FORMAT(ps.TANGGAL_LAHIR,'%d-%m-%Y')) tanggal
  //         , np.id
 
  //         FROM pendaftaran.kunjungan pk
  //             LEFT JOIN master.ruangan r ON pk.RUANGAN=r.ID AND r.JENIS=5
  //             ,pendaftaran.pendaftaran pp
  //             LEFT JOIN master.pasien ps ON pp.NORM=ps.NORM
  //             LEFT JOIN db_layanan.tb_napak_pengkajian np ON np.nomr=ps.NORM
  //             LEFT JOIN pendaftaran.penjamin pj ON pp.NOMOR=pj.NOPEN
  //             LEFT JOIN master.referensi crbyr ON pj.JENIS=crbyr.ID AND crbyr.JENIS=10,
  //             medicalrecord.diagnosa md,
  //             pendaftaran.tujuan_pasien tp
              
  //         WHERE  pk.NOPEN=md.NOPEN AND md.STATUS=1 AND pp.NOMOR=tp.NOPEN AND pk.RUANGAN=tp.RUANGAN
  //         AND np.id IS NULL
  //         AND (md.KODE LIKE 'C50%' OR md.KODE LIKE 'C53%' OR md.KODE LIKE 'C25%' OR md.KODE LIKE 'C61%' OR md.KODE LIKE 'C34%' OR md.KODE LIKE 'C91%' AND TIMESTAMPDIFF(YEAR, ps.TANGGAL_LAHIR , CURDATE()) < 17 )
  //       AND pk.NOPEN=pp.NOMOR AND pp.TANGGAL BETWEEN '2024-02-01 00:00:00' AND '2024-02-29 23:59:59'
  //       ) c
  //       GROUP BY c.norm
  //       ORDER BY c.diagnosa");
  //   return $query->result_array();
  // }

    public function get_data_pasien_staged() {
        
        // ====================================================================
        // LANGKAH A: Dapatkan daftar NORM pasien yang memenuhi kriteria awal
        // Query ini sengaja dibuat seringan mungkin.
        // ====================================================================
        $this->db->select('mp.NORM');
        $this->db->from('master.pasien mp');
        $this->db->join('db_layanan.tb_napak_pengkajian np', 'np.nomr = mp.norm', 'left');
        
        $this->db->where('mp.TANGGAL >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)', NULL, FALSE);
        $this->db->where('mp.NORM <', 1000000);
        $this->db->where('np.created_at IS NULL');
        
        $query_norm = $this->db->get();
        
        if ($query_norm->num_rows() === 0) {
            return []; // Tidak ada pasien yang cocok, hentikan proses.
        }

        // Ambil semua NORM dan simpan dalam array
        $list_norm = array_column($query_norm->result_array(), 'NORM');

        // ====================================================================
        // LANGKAH B: Ambil data lengkap menggunakan daftar NORM yang sudah difilter
        // Query ini sekarang hanya akan bekerja pada subset pasien yang relevan.
        // ====================================================================
        $this->db->select("
            mp.tanggal_lahir AS tgl_lahir,
            mp.NORM AS nomr,
            pp.DIAGNOSA_MASUK AS diagnosa_masuk,
            CASE 
                WHEN mr.STR IS NULL OR mr.STR = '.' THEN '' 
                ELSE mr.STR 
            END AS diagnosa_awal,
            master.getNamaLengkap(mp.NORM) AS nama,
            MAX(pk.MASUK) AS tgl_kunjungan_pertama
        ", FALSE);

        $this->db->from('master.pasien mp');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NORM = mp.NORM', 'left');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOPEN = pp.NOMOR', 'left');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = pk.NOPEN', 'left');
        $this->db->join('master.diagnosa_masuk mdm', 'mdm.ID = pp.DIAGNOSA_MASUK', 'left');
        $this->db->join('master.mrconso mr', 'mr.CODE = mdm.ICD', 'left');
        
        // KUNCI OPTIMISASI: Gunakan WHERE IN dengan daftar NORM dari Langkah A
        $this->db->where_in('mp.NORM', $list_norm);
        
        // Filter tambahan yang bergantung pada join
        $this->db->where('pk.MASUK IS NOT NULL');
        $this->db->where('tp.RESERVASI IS NULL');
        $this->db->where_in('pp.status', [1, 2]);

        $this->db->group_by('mp.NORM');
        $this->db->order_by('tgl_kunjungan_pertama', 'DESC');
        $this->db->order_by('diagnosa_awal', 'DESC');

        $final_query = $this->db->get();
        return $final_query->result_array();
    }

    public function listHistoryPasienNapak()
  {
    $query = $this->db->query("SELECT master.getNamaLengkap(tnp.nomr) nama
                                  , tnp.id
                                  , tnp.nomr
                                  , tnp.gedung
                                  , COUNT(tnp.id) cek
                                  , (SELECT case WHEN tnp1.gedung=1 THEN 'Gedung Existing'
                                              WHEN tnp1.gedung=2 THEN 'Gedung Cendana'
                                              ELSE '-' END 
                                    FROM db_layanan.tb_napak_pengkajian tnp1
                                    WHERE tnp1.nomr = tnp.nomr and tnp1.status=1 AND tnp1.gedung IS NOT null
                                    ORDER BY tnp1.created_at DESC LIMIT 1 ) nama_gedung
                              FROM db_layanan.tb_napak_pengkajian tnp
                              WHERE tnp.status = 1 
                              GROUP BY tnp.nomr");
    return $query->result_array($query);
  }

  // public function listHistoryPasienNapak()
  // {
  //   $query = $this->db->query("SELECT * FROM (
  //         SELECT md.KODE KODEICD10,
  //             (SELECT ms.STR FROM master.mrconso ms WHERE ms.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND ms.CODE=md.KODE LIMIT 1) diagnosa
  //         , pp.NORM norm,master.getNamaLengkap(pp.NORM) nama
  //         , IF(ps.JENIS_KELAMIN=1,'Laki-laki','Perempuan') JENISKELAMIN
  //         , CONCAT(DATE_FORMAT(ps.TANGGAL_LAHIR,'%d-%m-%Y')) tanggal
  //         , np.id
 
  //         FROM pendaftaran.kunjungan pk
  //             LEFT JOIN master.ruangan r ON pk.RUANGAN=r.ID AND r.JENIS=5
  //             ,pendaftaran.pendaftaran pp
  //             LEFT JOIN master.pasien ps ON pp.NORM=ps.NORM
  //             LEFT JOIN db_layanan.tb_napak_pengkajian np ON np.nomr=ps.NORM
  //             LEFT JOIN pendaftaran.penjamin pj ON pp.NOMOR=pj.NOPEN
  //             LEFT JOIN master.referensi crbyr ON pj.JENIS=crbyr.ID AND crbyr.JENIS=10,
  //             medicalrecord.diagnosa md,
  //             pendaftaran.tujuan_pasien tp
              
  //         WHERE  pk.NOPEN=md.NOPEN AND md.STATUS=1 AND pp.NOMOR=tp.NOPEN AND pk.RUANGAN=tp.RUANGAN
  //         AND np.id IS NOT NULL
  //         AND (md.KODE LIKE 'C50%' OR md.KODE LIKE 'C53%' OR md.KODE LIKE 'C25%' OR md.KODE LIKE 'C61%' OR md.KODE LIKE 'C34%' OR md.KODE LIKE 'C91%')
  //       AND pk.NOPEN=pp.NOMOR AND pp.TANGGAL BETWEEN '2024-02-01 00:00:00' AND '2024-02-29 23:59:59'
  //       ) c
  //       GROUP BY c.norm
  //       ORDER BY c.diagnosa");
  //   return $query->result_array();
  // }

public function historyRencanaTindakLanjut($single = TRUE)
{
    $this->db->select('tnr.*, master.getNamaLengkapPegawai(p.NIP) nama, GROUP_CONCAT(nrh.jenis) hambatan'); // Include checkbox values
    $this->db->from('db_layanan.tb_napak_rencana tnr');
    $this->db->join('aplikasi.pengguna p', 'tnr.oleh = p.ID', 'left');
    $this->db->join('db_layanan.tb_napak_rencana_hambatan nrh', 'tnr.id = nrh.id_napak_rencana AND nrh.status = 1', 'left');
    $this->db->where('tnr.status !=', 0);

    $this->db->group_by('tnr.id'); // Group by primary key to get aggregated checkbox values
    $this->db->order_by('tnr.created_at', 'DESC');

    if ($this->input->post('id_pengkajian_napak')) {
        $this->db->where('tnr.id_pengkajian_napak', $this->input->post('id_pengkajian_napak'));
    }

    if ($this->input->post('id')) {
        $this->db->where('tnr.id', $this->input->post('id'));
    }

    $query = $this->db->get();
    if ($single == TRUE) {
        $method = 'row';
    } else {
        $method = 'result';
    }
    return $query->$method();
}

    public function dataPengkajian($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM
                  , DATE_FORMAT(ps.TANGGAL_LAHIR, '%d-%m-%Y') tgl_lahir
                  , ps.ALAMAT alamat
                  , kp.NOMOR no_telp
                  , v1.variabel terima_wa
                  , npk.keluarga_pendamping
                  , npk.kontak_pendamping
                  , ref1.DESKRIPSI hubungan_pendamping
                  , CASE
                      WHEN v2.id_variabel = 5939 THEN CONCAT(v2.variabel, ' : ', npk.bagaimana_dirujuk_deskripsi)
                      WHEN v2.id_variabel = 5937 THEN CONCAT(v2.variabel, ' : ', npk.bagaimana_dirujuk_deskripsi)
                      ELSE v2.variabel
                    END bagaimana_dirujuk
                  , dg.diagnosa diagnosa_
                  , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dg.diagnosa AND mr.SAB = 'ICD10_1998' GROUP BY mr.CODE) diagnosa
                  , CONCAT (dg.diagnosa, ' | ', (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dg.diagnosa AND mr.SAB = 'ICD10_1998' GROUP BY mr.CODE)) diagnosa2
                  , st.DESKRIPSI stadium
                  , DATE_FORMAT(npk.tgl_biopsi, '%d-%m-%Y') tglbiopsi
                  , npk.hasil_biopsi
                  , DATE_FORMAT(npk.tgl_hasil_pemeriksaan, '%d-%m-%Y') tgl_hasilpemeriksaan
                  , npk.hasil_pemeriksaan
                  , npk.rencana_pengobatan
                  , npk.rencana_pemeriksaan_lanjutan
                  , npk.prognosis
                  , npk.penjelasan_lainnya
                  , CASE
                      WHEN v3.id_variabel = 5935 THEN CONCAT(v3.variabel, ' : ', npk.asuransi_deskripsi)
                      ELSE v3.variabel
                    END asuransi
                  , CASE
                      WHEN v4.id_variabel = 5921 THEN CONCAT(v4.variabel, ' : ', npk.penyakit_penyerta_deskripsi)
                      ELSE v4.variabel
                    END penyakit_penyerta
                    
                  , v5.id_variabel riwayatkanker
                  , v5.variabel riwayatkanker2
                  , npk.riwayat_kanker_diagnosa
                  , npk.riwayat_kanker_tahun
                  , npk.riwayat_kanker_pengobatan
                  , npk.riwayat_kanker_tempat_pengobatan

                  , v6.id_variabel riwayatkanker_keluarga
                  , v6.variabel riwayatkanker_keluarga2
                  , ref2.DESKRIPSI riwayathubungan_keluarga -- belum di betulkan --
                  , npk.riwayat_diagnosa_anggota

                  , CASE
                      WHEN v7.id_variabel = 5950 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5956 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5964 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5976 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5982 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5988 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      ELSE v7.variabel
                    END masalah
                  
                  ,npk.tindak_lanjut_navigator
                  ,npk.target_hasil
                  ,npk.rencana_follow_up
                  ,npk.gedung

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_master.variabel v1 ON v1.id_variabel = npk.terima_wa AND v1.id_referensi = 1783
                  LEFT JOIN master.referensi ref1 ON ref1.ID = npk.hubungan_pendamping AND ref1.JENIS = 7
                  LEFT JOIN db_master.variabel v2 ON v2.id_variabel = npk.bagaimana_dirujuk AND v2.id_referensi = 1775
                  LEFT JOIN master.stadium st ON st.ID = npk.stadium
                  LEFT JOIN db_layanan.tb_napak_diagnosa_penyakit dg ON dg.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v3 ON v3.id_variabel = npk.asuransi AND v3.id_referensi = 1776
                  LEFT JOIN db_master.variabel v4 ON v4.id_variabel = npk.penyakit_penyerta AND v4.id_referensi = 1772
                  LEFT JOIN db_master.variabel v5 ON v5.id_variabel = npk.riwayat_kanker AND v5.id_referensi = 1773
                  LEFT JOIN db_master.variabel v6 ON v6.id_variabel = npk.riwayat_anggota_keluarga AND v6.id_referensi = 1774
                  LEFT JOIN master.referensi ref2 ON ref2.ID = npk.riwayat_hubungan_anggota AND ref2.JENIS = 7
                  LEFT JOIN db_layanan.tb_napak_masalah mas ON mas.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v7 ON v7.id_variabel = mas.masalah AND v7.id_referensi IN (1777, 1778,1779,1780,1781,1782)

                  WHERE npk.id = ?
                  AND npk.status = ?";
        $bind = $this->db->query($query, array($id,1)); 
        return $bind;
    }

    public function diagnosa($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM

                  , dg.diagnosa diagnosa_
                  , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dg.diagnosa AND mr.SAB = 'ICD10_1998' GROUP BY mr.CODE) diagnosa
                  , CONCAT (dg.diagnosa, ' | ', (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dg.diagnosa AND mr.SAB = 'ICD10_1998' GROUP BY mr.CODE)) diagnosa2

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_master.variabel v1 ON v1.id_variabel = npk.terima_wa AND v1.id_referensi = 1783
                  LEFT JOIN master.referensi ref1 ON ref1.ID = npk.hubungan_pendamping AND ref1.JENIS = 7
                  LEFT JOIN db_master.variabel v2 ON v2.id_variabel = npk.bagaimana_dirujuk AND v2.id_referensi = 1775
                  LEFT JOIN master.stadium st ON st.ID = npk.stadium
                  LEFT JOIN db_layanan.tb_napak_diagnosa_penyakit dg ON dg.id_pengkajian_napak = npk.id


                  WHERE npk.id = ?
                  AND npk.status = ?";
        $bind = $this->db->query($query, array($id,1)); 
        return $bind;
    }

    public function masalah1($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM
                  , CASE
                      WHEN v7.id_variabel = 5950 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5956 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5964 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5976 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5982 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5988 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      ELSE v7.variabel
                    END masalah

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_layanan.tb_napak_masalah mas ON mas.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v7 ON v7.id_variabel = mas.masalah AND v7.id_referensi IN (1777, 1778,1779,1780,1781,1782)

                  WHERE npk.id = ?
                  AND npk.status = ?
                  AND mas.referensi = ?";
        $bind = $this->db->query($query, array($id,1,1777)); 
        return $bind;
    }

    public function masalah2($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM
                  , CASE
                      WHEN v7.id_variabel = 5950 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5956 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5964 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5976 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5982 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5988 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      ELSE v7.variabel
                    END masalah

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_layanan.tb_napak_masalah mas ON mas.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v7 ON v7.id_variabel = mas.masalah AND v7.id_referensi IN (1777, 1778,1779,1780,1781,1782)

                  WHERE npk.id = ?
                  AND npk.status = ?
                  AND mas.referensi = ?";
        $bind = $this->db->query($query, array($id,1,1778)); 
        return $bind;
    }

    public function masalah3($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM
                  , CASE
                      WHEN v7.id_variabel = 5950 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5956 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5964 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5976 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5982 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5988 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      ELSE v7.variabel
                    END masalah

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_layanan.tb_napak_masalah mas ON mas.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v7 ON v7.id_variabel = mas.masalah AND v7.id_referensi IN (1777, 1778,1779,1780,1781,1782)

                  WHERE npk.id = ?
                  AND npk.status = ?
                  AND mas.referensi = ?";
        $bind = $this->db->query($query, array($id,1,1779)); 
        return $bind;
    }

    public function masalah4($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM
                  , CASE
                      WHEN v7.id_variabel = 5950 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5956 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5964 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5976 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5982 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5988 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      ELSE v7.variabel
                    END masalah

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_layanan.tb_napak_masalah mas ON mas.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v7 ON v7.id_variabel = mas.masalah AND v7.id_referensi IN (1777, 1778,1779,1780,1781,1782)

                  WHERE npk.id = ?
                  AND npk.status = ?
                  AND mas.referensi = ?";
        $bind = $this->db->query($query, array($id,1,1780)); 
        return $bind;
    }

    public function masalah5($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM
                  , CASE
                      WHEN v7.id_variabel = 5950 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5956 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5964 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5976 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5982 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5988 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      ELSE v7.variabel
                    END masalah

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_layanan.tb_napak_masalah mas ON mas.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v7 ON v7.id_variabel = mas.masalah AND v7.id_referensi IN (1777, 1778,1779,1780,1781,1782)

                  WHERE npk.id = ?
                  AND npk.status = ?
                  AND mas.referensi = ?";
        $bind = $this->db->query($query, array($id,1,1781)); 
        return $bind;
    }

    public function masalah6($id){
        $query = "SELECT npk.id
                  , npk.nopen
                  , master.getNamaLengkap(ps.NORM) nama_pasien
                  , kip.NOMOR NIK
                  , ps.NORM
                  , CASE
                      WHEN v7.id_variabel = 5950 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5956 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5964 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5976 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5982 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      WHEN v7.id_variabel = 5988 THEN CONCAT(v7.variabel, ' : ', mas.deskripsi)
                      ELSE v7.variabel
                    END masalah

                  FROM db_layanan.tb_napak_pengkajian npk

                  LEFT JOIN master.pasien ps ON ps.NORM = npk.nomr
                  LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM = ps.NORM
                  LEFT JOIN master.kontak_pasien kp ON kp.NORM = ps.NORM
                  LEFT JOIN db_layanan.tb_napak_masalah mas ON mas.id_pengkajian_napak = npk.id
                  LEFT JOIN db_master.variabel v7 ON v7.id_variabel = mas.masalah AND v7.id_referensi IN (1777, 1778,1779,1780,1781,1782)

                  WHERE npk.id = ?
                  AND npk.status = ?
                  AND mas.referensi = ?";
        $bind = $this->db->query($query, array($id,1,1782)); 
        return $bind;
    }

        // List Dokter
    public function listHubungan()
    {
        if ($this->input->get('q')) {
            $this->db->like('p.NAMA', $this->input->get('q'));
        }

        $this->db->select(
            '*'
        );
        $this->db->from('master.referensi mr');
        $this->db->where('mr.jenis', 7);

        $query = $this->db->get();
        return $query->result_array();
    }

    function listAllPasien($nomr){
        if ($this->input->get('q')) {
            $this->db->like('p.NORM ', $this->input->get('q'));
        }
        $this->db->select("mp.NORM, master.getNamaLengkap(mp.NORM) NAMAPASIEN, mp.TANGGAL_LAHIR, pk.NOMOR NOKUN, np.status NAPAK");
        $this->db->from('master.pasien mp');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NORM = mp.NORM', 'LEFT');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOPEN = pp.NOMOR', 'LEFT');
        $this->db->join('master.kontak_pasien mk', 'mk.NORM = mp.NORM', 'LEFT');
        $this->db->join('pendaftaran.napak np', 'mp.NORM = np.nomr', 'LEFT');
        $this->db->where("mp.NORM", $nomr);
        $this->db->order_by('pp.NOMOR', 'DESC');
        $this->db->limit(1);
        
        $query = $this->db->get();
        return $query;
    }

    //     public function dataDiriPasien($nomr)
    // {
    //     $query = $this->db->query(
    //         "SELECT mp.*, master.getNamaLengkap(mp.NORM) NAMAPASIEN, pp.NOMOR NOPEN, pk.NOMOR NOKUN,
    //         CONCAT(master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR), ' Tahun') UMUR, mk.NOMOR tlpn
    //         FROM master.pasien mp
    //         LEFT JOIN pendaftaran.pendaftaran pp ON pp.NORM = mp.NORM
    //         LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = pp.NOMOR
    //         LEFT JOIN master.kontak_pasien mk ON mk.NORM = mp.NORM
    //         WHERE mp.NORM = '$nomr'
    //         ORDER BY pp.NOMOR DESC
    //         LIMIT 1"
    //     );
    //     return $query->row_array();
    // }
}

/* End of file NapakModel.php */
/* Location: ./application/models/rekam_medis/NapakModel.php */