<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('keuanganModel','masterModel','keuangan/dashboardModel'));
  }

  public function index()
  {
    $data = array(
      'title'            => 'Halaman Dashboard',
      'isi'              => 'Keuangan/dashboard/dashboard',
    );
    // echo "<pre>";print_r($data);exit();
    $this->load->view('layout/wrapper',$data);
  }

  public function penPerInst()
  {
    $instalasi = $this->keuanganModel->instalasi();

    $data = array(
      'instalasi' => $instalasi,
    );

    $this->load->view('Keuangan/dashboard/dPendPerInst',$data);
  }

  public function dataPendapatanPerInst(){
    $pisah     = explode("-", $this->input->post('pilihTanggal'));  
    $tgl1      = date("Y-m-d",strtotime($pisah[0]));
    $tgl2      = date("Y-m-d",strtotime($pisah[1]));
    
    $instalasi = $this->input->post('instalasi');

    $dataPendapatan = $this->keuanganModel->dataPendapatanPerInst($tgl1, $tgl2, $instalasi);

    $data = array(
      'tgl1'           => $tgl1,
      'tgl2'           => $tgl2,
      'instalasi'      => $instalasi,
      'dataPendapatan' => $dataPendapatan,
    );

    $this->load->view('Keuangan/dashboard/dataRealisasiPerInst',$data);
  }

  public function belPerInst()
  {
    $instalasi = $this->keuanganModel->instalasi();

    $data = array(
      'instalasi' => $instalasi,
    );

    $this->load->view('Keuangan_new/dashboard/dBelanjaPerInst',$data);
  }

  public function dataBelanjaPerInst(){
    $pisah     = explode("-", $this->input->post('pilihTanggal'));  
    $tgl1      = date("Y-m-d",strtotime($pisah[0]));
    $tgl2      = date("Y-m-d",strtotime($pisah[1]));
    
    $instalasi = $this->input->post('instalasi');

    $dataBelanja = $this->dashboardModel->dataBelanjaPerInst($tgl1, $tgl2, $instalasi);
    $data = array(
      'tgl1'           => $tgl1,
      'tgl2'           => $tgl2,
      'instalasi'      => $instalasi,
      'dataBelanja' => $dataBelanja,
    );

    $this->load->view('Keuangan_new/dashboard/dataBelanjaPerInst',$data);
  }

  public function efisiensi()
  {
    $instalasi = $this->keuanganModel->instalasi();

    $data = array(
      'instalasi' => $instalasi,
    );

    $this->load->view('Keuangan_new/dashboard/dEfisiensi',$data);
  }

  public function dataEfisiensi(){
    $pisah = explode("-", $this->input->post('pilihTanggal'));  
    $tgl1  = date("Y-m-d",strtotime($pisah[0]));
    $tgl2  = date("Y-m-d",strtotime($pisah[1]));
    
    $instalasi = $this->input->post('instalasi');

    $datEfisiensi = $this->keuanganModel->dataPendapatanPerInst($tgl1, $tgl2, $instalasi);

    // echo "<pre>";print_r($datEfisiensi);
    $data = array(
      'tgl1'        => $tgl1,
      'tgl2'        => $tgl2,
      'instalasi'   => $instalasi,
      'datEfisiensi' => $datEfisiensi,
    );

    $this->load->view('Keuangan_new/dashboard/dataEfisiensi',$data);
  }

}

/* End of file Dashboard.php */
/* Location: ./application/controllers/keuangan/Dashboard.php */