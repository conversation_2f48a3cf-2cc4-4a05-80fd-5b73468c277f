<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TindakanDonorAferesis extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function simpanFormTindakanDonorAferesis()
    {
        $kunjungan = $this->input->post("nokun");
        $pengguna = $this->input->post("pengguna");
        $jenis_informed_consent = "3029"; 
        $dokterpelaksana = $this->input->post("dokterpelaksana");
        $penerimainformasi = $this->input->post("penerimainformasi");

        $pemberiinformasi = $this->input->post("pemberiinformasi");
        $diagnosisbanding = $this->input->post("diagnosisbanding");
        $dasardiagnosis = $this->input->post("dasardiagnosis");
        $tindakankedokteran = $this->input->post("tindakankedokteran");
        $indikasitindakan = $this->input->post("indikasitindakan");
        $tatacara = $this->input->post("tatacara");
        $tujuantindakan = $this->input->post("tujuantindakan");
        $tujuanpengobatan = $this->input->post("tujuanpengobatan");
        $risiko = $this->input->post("risiko");
        $komplikasi = $this->input->post("komplikasi");
        $prognosis = $this->input->post("prognosis");
        $alternatif = $this->input->post("alternatif");
        $risikolain = $this->input->post("risikolain");
        $lainlain = $this->input->post("lainlain");
        
        $nama = $this->input->post("nama");
        $umur = $this->input->post("umur");
        $jenis_kelamin = $this->input->post("jenis_kelamin");
        $alamat = $this->input->post("alamat");
        $tindakan = $this->input->post("tindakan");
        $hubungan = $this->input->post("hubungan");
        $nama_keluarga = $this->input->post("nama_keluarga");
        $nama_saksi_rs = $this->input->post("nama_saksi_rs");
        $signMenyatakan = $this->input->post('signMenyatakan');
        $signKeluarga = $this->input->post('signKeluarga');
        $signRumahSakit = $this->input->post('signRumahSakit');

        $dataInformedConsent = array(
            'nokun' => $kunjungan,
            'jenis_informed_consent' => $jenis_informed_consent,
            'dokter_pelaksana' => $dokterpelaksana,
            'penerima_informasi' => $penerimainformasi,
            'oleh' => $pengguna,
        );

        $this->db->trans_begin();
        $this->db->insert('db_informed_consent.tb_informed_consent', $dataInformedConsent);
        
        $idInformedConsent = $this->db->insert_id();
        
        $dataTindakanDonorAferesis = array(
            'id_informed_consent' => $idInformedConsent,
            'pemberi_informasi' => $pemberiinformasi,
            'diagnosis_banding' => $diagnosisbanding,
            'dasar_diagnosis' => isset($dasardiagnosis) ? json_encode($dasardiagnosis) : "",
            'tindakan_kedokteran' => $tindakankedokteran,
            'indikasi_tindakan' => $indikasitindakan,
            'tatacara' => $tatacara,
            'tujuan_tindakan' => $tujuantindakan,
            'tujuan_pengobatan' => $tujuanpengobatan,
            'risiko' => isset($risiko) ? json_encode($risiko) : "",
            'komplikasi' => $komplikasi,
            'prognosis' => $prognosis,
            'alternatif' => isset($alternatif) ? json_encode($alternatif) : "",
            'risiko_lain' => $risikolain,
            'lain_lain' => $lainlain,
        );

        $dataPersetujuanTidakanKedokteran = array(
            'id_informed_consent' => $idInformedConsent,
            'nama_keluarga' => $nama,
            'umur_keluarga' => $umur,
            'jk_keluarga' => $jenis_kelamin,
            'alamat_keluarga' => $alamat,
            'tindakan' => $tindakan,
            'hub_keluarga_dgn_pasien' => $hubungan,
            'ttd_menyatakan' => file_get_contents($signMenyatakan),
            'ttd_saksi_keluarga' => file_get_contents($signKeluarga),
            'ttd_saksi_rumah_sakit' => file_get_contents($signRumahSakit),
            'saksi_keluarga' => $nama_keluarga,
            'saksi_rumah_sakit' => $nama_saksi_rs,
        );

        $this->db->insert('db_informed_consent.tb_tindakan_donor_aferesis', $dataTindakanDonorAferesis);
        $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran',$dataPersetujuanTidakanKedokteran);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);

    }

    public function ubahFormTindakanDonorAferesis()
    {
        $id_ic = $this->input->post("id_ic");
        $id_aferesis = $this->input->post("id_aferesis");

        $kunjungan = $this->input->post("nokun_edit");
        $pengguna = $this->input->post("pengguna_edit");
        $jenis_informed_consent = "3029"; 
        $dokterpelaksana = $this->input->post("dokterpelaksana_edit");
        $penerimainformasi = $this->input->post("penerimainformasi_edit");

        $pemberiinformasi = $this->input->post("pemberiinformasi_edit");
        $diagnosisbanding = $this->input->post("diagnosisbanding_edit");
        $dasardiagnosis = $this->input->post("dasardiagnosis_edit");
        $tindakankedokteran = $this->input->post("tindakankedokteran_edit");
        $indikasitindakan = $this->input->post("indikasitindakan_edit");
        $tatacara = $this->input->post("tatacara_edit");
        $tujuantindakan = $this->input->post("tujuantindakan_edit");
        $tujuanpengobatan = $this->input->post("tujuanpengobatan_edit");
        $risiko = $this->input->post("risiko_edit");
        $komplikasi = $this->input->post("komplikasi_edit");
        $prognosis = $this->input->post("prognosis_edit");
        $alternatif = $this->input->post("alternatif_edit");
        $risikolain = $this->input->post("risikolain_edit");
        $lainlain = $this->input->post("lainlain_edit");

        $dataUbahInformedConsent = array(
            'nokun' => $kunjungan,
            'jenis_informed_consent' => $jenis_informed_consent,
            'dokter_pelaksana' => $dokterpelaksana,
            'penerima_informasi' => $penerimainformasi,
            'oleh' => $pengguna,
        );

        $dataUbahTindakanDonorAferesis = array(
            'id_informed_consent' => $id_ic,
            'pemberi_informasi' => $pemberiinformasi,
            'diagnosis_banding' => $diagnosisbanding,
            'dasar_diagnosis' => isset($dasardiagnosis) ? json_encode($dasardiagnosis) : "",
            'tindakan_kedokteran' => $tindakankedokteran,
            'indikasi_tindakan' => $indikasitindakan,
            'tatacara' => $tatacara,
            'tujuan_tindakan' => $tujuantindakan,
            'tujuan_pengobatan' => $tujuanpengobatan,
            'risiko' => isset($risiko) ? json_encode($risiko) : "",
            'komplikasi' => $komplikasi,
            'prognosis' => $prognosis,
            'alternatif' => isset($alternatif) ? json_encode($alternatif) : "",
            'risiko_lain' => $risikolain,
            'lain_lain' => $lainlain,
        );

        $this->db->trans_begin();

        $this->db->where('db_informed_consent.tb_informed_consent.id', $id_ic);
        $this->db->update('db_informed_consent.tb_informed_consent', $dataUbahInformedConsent);

        $this->db->where('db_informed_consent.tb_tindakan_donor_aferesis.id', $id_aferesis);
        $this->db->update('db_informed_consent.tb_tindakan_donor_aferesis', $dataUbahTindakanDonorAferesis);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryTindakanDonorAferesis()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $historyDetailTindakanDonorAferesis = $this->pengkajianAwalModel->historyDetailTindakanDonorAferesis($id);
        
        $listDiagnosisBanding = $this->masterModel->referensi(953);
        $listDasarDiagnosis = $this->masterModel->referensi(954);
        $listTindakanKedokteran = $this->masterModel->referensi(955);
        $listIndikasiTindakan = $this->masterModel->referensi(956);
        $listTataCara = $this->masterModel->referensi(957);
        $listTujuanTindakan = $this->masterModel->referensi(958);
        $listTujuanPengobatan = $this->masterModel->referensi(959);
        $listRisiko = $this->masterModel->referensi(960);
        $listKomplikasi = $this->masterModel->referensi(961);
        $listPrognosis = $this->masterModel->referensi(962);
        $listAlternatif = $this->masterModel->referensi(963);
        $listDr = $this->masterModel->listDr();

        $dataEdit = array(
            'aferesis' => $historyDetailTindakanDonorAferesis,
            'getNomr' => $getNomr,
            'listDiagnosisBanding' => $listDiagnosisBanding,
            'listDasarDiagnosis' => $listDasarDiagnosis,
            'listTindakanKedokteran' => $listTindakanKedokteran,
            'listIndikasiTindakan' => $listIndikasiTindakan,
            'listTataCara' => $listTataCara,
            'listTujuanTindakan' => $listTujuanTindakan,
            'listTujuanPengobatan' => $listTujuanPengobatan,
            'listRisiko' => $listRisiko,
            'listKomplikasi' => $listKomplikasi,
            'listPrognosis' => $listPrognosis,
            'listAlternatif' => $listAlternatif,
            'listDr' => $listDr,

        );

        $this->load->view('Pengkajian/bankdarah/tindakanDonorAferesis/modalViewEditTindakanDonorAferesis', $dataEdit);
    }
}
?>