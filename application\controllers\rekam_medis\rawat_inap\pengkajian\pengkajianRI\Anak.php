<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Anak extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/AnakModel', 'rekam_medis/MedisModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel'));
  }

  public function index($idLoadNorm, $idLoadNopen, $idLoadNokun, $idLoad)
  {
    $norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->AnakModel->getNomrRawatInapAnak($nopen);
    // $getNomr = $this->DewasaModel->getNomrRawatInap($nopen);
    $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
    if ($idLoad === "00000") {
      $getPengkajian = $this->AnakModel->getPengkajian($getIdEmr);
    } else {
      $getPengkajian = $this->AnakModel->getPengkajian($idLoad);
    }

    // START INDEKS BARTHEL
    $indeksBarthel = $this->AnakModel->hasilIndeksBarthel($nokun);
    if (isset($indeksBarthel)) {
      $totalSkorIndeksBarthel = $indeksBarthel['rangsang_bab'] + $indeksBarthel['rangsang_berkemih'] + $indeksBarthel['bersihkan_diri'] + $indeksBarthel['penggunaan_kloset'] + $indeksBarthel['makan'] + $indeksBarthel['berubah_posisi'] + $indeksBarthel['berpindah'] + $indeksBarthel['memakai_baju'] + $indeksBarthel['naik_tangga'] + $indeksBarthel['mandi'];

      if ($totalSkorIndeksBarthel == 20) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 4) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 8) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 11) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
      } elseif ($totalSkorIndeksBarthel <= 19) {
        $hasilSkorIndeksBarthel = "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
      }
    }

    $jumlahIndeksBarthel = $this->AnakModel->get_count_indeksBarthel($nokun);
    // END INDEKS BARTHEL

    // START HUMPTY DUMPTY
    $humptyDumpty = $this->AnakModel->hasilHumptyDumpty($nokun);
    if (isset($humptyDumpty)) {
      $totalHumptyDumpty = $humptyDumpty['HASIL'];

      if ($totalHumptyDumpty <= 7) {
        $hasilHumptyDumpty = "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b> ( Humpty Dumpty )</div>";
      } elseif ($totalHumptyDumpty <= 11) {
        $hasilHumptyDumpty = "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b> ( Humpty Dumpty )</div>";
      } elseif ($totalHumptyDumpty >= 12) {
        $hasilHumptyDumpty = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko tinggi</b> ( Humpty Dumpty )</div>";
      }
    }

    $jumlahHumptyDumpty = $this->AnakModel->get_count_humptyDumpty($nokun);
    // END HUMPTY DUMPTY

    $data = array(
      'hasilSkorIndeksBarthel' => isset($hasilSkorIndeksBarthel) ? $hasilSkorIndeksBarthel : null,
      'hasilHumptyDumpty' => isset($hasilHumptyDumpty) ? $hasilHumptyDumpty : null,

      'jumlahIndeksBarthel' => $jumlahIndeksBarthel,
      'jumlahHumptyDumpty' => $jumlahHumptyDumpty,

      'norm' => $norm,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'idLoad' => $idLoad,
      'getPengkajian' => $getPengkajian,
      'pasien' => $getNomr,
      'user_perawat' => $this->session->userdata("id"),
      'asupanEnergi' => $this->masterModel->referensi(1202),
      'penurunanLemak' => $this->masterModel->referensi(1204),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'penurunanOtot' => $this->masterModel->referensi(1205),
      'akumulasiCairan' => $this->masterModel->referensi(1206),
      'diagnosis' => $this->masterModel->referensi(1207),
      'jalur' => $this->masterModel->referensi(1208),
      'enteral' => $this->masterModel->referensi(1209),
      'masalahKesehatan' => $this->masterModel->formMasalahKesehatan(),
      'anamnesis' => $this->masterModel->referensi(54),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'riwayatTransfusiDarah' => $this->masterModel->referensi(140),
      'kebiasaanmerokok' => $this->masterModel->referensi(142),
      'riwayatKanker' => $this->masterModel->referensi(1),
      'riwayatMetabolik' => $this->masterModel->referensi(143),
      'riwayatDDK' => $this->masterModel->referensi(145),
      'riwayatTransfusiDarahDesk' => $this->masterModel->referensi(141),
      'kesadaran' => $this->masterModel->referensi(5),
      'komponenPenilaian' => $this->masterModel->referensi(22),
      'komponenPenilaianAsupan' => $this->masterModel->referensi(23),
      'pendidikan' => $this->masterModel->referensi(24),
      'bahasaSehari' => $this->masterModel->referensi(25),
      'perluPenerjemah' => $this->masterModel->referensi(26),
      'kesediaanInformasi' => $this->masterModel->referensi(27),
      'hambatan' => $this->masterModel->referensi(28),
      'kebutuhanPembelajaran' => $this->masterModel->referensi(29),
      'gastroMulut' => $this->masterModel->referensi(206),
      'gastroEsophagus' => $this->masterModel->referensi(1122),
      'gastroAbdomen' => $this->masterModel->referensi(207),
      'sirkulasiHidung' => $this->masterModel->referensi(1112),
      'sirkulasiDada' => $this->masterModel->referensi(1113),
      'sirkulasiJantung' => $this->masterModel->referensi(1114),
      'sirkulasiPacuJantung' => $this->masterModel->referensi(1115),
      'sirkulasiPadaParu' => $this->masterModel->referensi(1116),
      'sirkulasiPerdarahan' => $this->masterModel->referensi(1117),
      'sirkulasiTurgorKulit' => $this->masterModel->referensi(1118),
      'sirkulasiCrt' => $this->masterModel->referensi(1120),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'efeksampingNRS' => $this->masterModel->referensi(118),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'aktivitasIstirahat' => $this->masterModel->referensi(1123),
      'aktivitasKemampuan' => $this->masterModel->referensi(1124),
      'integritasKondisiKulit' => $this->masterModel->referensi(1121),
      'integritasLuka' => $this->masterModel->referensi(1125),
      'seksualGenitalia' => $this->masterModel->referensi(1126),
      'seksualKontrasepsi' => $this->masterModel->referensi(1127),
      'seksualSirkumsisi' => $this->masterModel->referensi(1128),
      'eliminasiDefekasi' => $this->masterModel->referensi(1129),
      'eliminasiMiksi' => $this->masterModel->referensi(1130),
      'keselamatanStatusMental' => $this->masterModel->referensi(1131),
      'keselamatanGangguan' => $this->masterModel->referensi(1132),
      'keselamatanPotensi' => $this->masterModel->referensi(1133),
      'programPengobatanDenganKeyakinan' => $this->masterModel->referensi(17),
      'pengobatanAlternatif' => $this->masterModel->referensi(146),
      'pengobatanBudaya' => $this->masterModel->referensi(147),
      'pasienPerluP3' => $this->masterModel->referensi(1134),
      'P31' => $this->masterModel->referensi(1135),
      'P32' => $this->masterModel->referensi(1136),
      'P33' => $this->masterModel->referensi(1137),
      'P34' => $this->masterModel->referensi(1138),
      'P35' => $this->masterModel->referensi(1139),
      'P36' => $this->masterModel->referensi(1140),
      'P37' => $this->masterModel->referensi(1141),
      'P38' => $this->masterModel->referensi(1142),
      'P39' => $this->masterModel->referensi(1143),
      'P310' => $this->masterModel->referensi(1144),
      'listRangsangbab' => $this->masterModel->referensi(834),
      'listRangsangberkemih' => $this->masterModel->referensi(835),
      'listMembersihkandiri' => $this->masterModel->referensi(836),
      'listPenggunaankloset' => $this->masterModel->referensi(837),
      'listMakan' => $this->masterModel->referensi(838),
      'listBerubahposisi' => $this->masterModel->referensi(839),
      'listBerpindah' => $this->masterModel->referensi(840),
      'listMemakaibaju' => $this->masterModel->referensi(841),
      'listNaiktangga' => $this->masterModel->referensi(842),
      'listMandi' => $this->masterModel->referensi(843),
      'persepsiSensori' => $this->masterModel->referensi(1154),
      'kelembaban' => $this->masterModel->referensi(1155),
      'aktivitas' => $this->masterModel->referensi(1156),
      'mobilitas' => $this->masterModel->referensi(1157),
      'nutrisi' => $this->masterModel->referensi(1158),
      'gesekan' => $this->masterModel->referensi(1159),
      'pernahDiRawat' => $this->masterModel->referensi(1160),
      'perawatanPertamaDiRsKd' => $this->masterModel->referensi(1161),
      'caraKelahiran' => $this->masterModel->referensi(189),
      'kondisiLahir' => $this->masterModel->referensi(233),
      'kelainanBawaan' => $this->masterModel->referensi(192),
      'riwayatImunisasiDasar' => $this->masterModel->referensi(193),
      'riwayatTumbuhKembang' => $this->masterModel->referensi(234),
      'nafsuMakan' => $this->masterModel->referensi(1145),
      'polaMakan' => $this->masterModel->referensi(1146),
      'makananSaatIni' => $this->masterModel->referensi(1147),
      'polaTidur' => $this->masterModel->referensi(1148),
      'kebiasaanSebelumTidur' => $this->masterModel->referensi(1149),
      'mandi' => $this->masterModel->referensi(1150),
      'kebersihanKuku' => $this->masterModel->referensi(1151),
      'kebersihanAnal' => $this->masterModel->referensi(1152),
      'aktivitasBermain' => $this->masterModel->referensi(1153),
      'bentukKepala' => $this->masterModel->referensi(195),
      'iramaPernapasan' => $this->masterModel->referensi(196),
      'retraksiDada' => $this->masterModel->referensi(197),
      'alatBantuNapasRiA' => $this->masterModel->referensi(1165),
      'sianosis' => $this->masterModel->referensi(199),
      'pucat' => $this->masterModel->referensi(200),
      'capillaryRefillTest' => $this->masterModel->referensi(201),
      'akral' => $this->masterModel->referensi(202),
      'pembesaranKelenjarGetahBening' => $this->masterModel->referensi(203),
      'gangguanNeurologi' => $this->masterModel->referensi(204),
      'neurologiMata' => $this->masterModel->referensi(205),
      'mulut' => $this->masterModel->referensi(206),
      'abdomen' => $this->masterModel->referensi(207),
      'asites' => $this->masterModel->referensi(208),
      'defekasi' => $this->masterModel->referensi(209),
      'karakteristikFeses' => $this->masterModel->referensi(210),
      'urin' => $this->masterModel->referensi(211),
      'rectal' => $this->masterModel->referensi(212),
      'genetalia' => $this->masterModel->referensi(213),
      'kulit' => $this->masterModel->referensi(214),
      'warnaKulit' => $this->masterModel->referensi(215),
      'luka' => $this->masterModel->referensi(216),
      'lokasiLuka' => $this->masterModel->referensi(217),
      'kelainanTulang' => $this->masterModel->referensi(218),
      'gerakanAnak' => $this->masterModel->referensi(219),
      'statusMentalDanTingkahLaku' => $this->masterModel->referensi(220),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'tempatTinggal' => $this->masterModel->referensi(226),
      'pengasuh' => $this->masterModel->referensi(227),
      'jenisSekolah' => $this->masterModel->referensi(228),
      'privasiKhusus' => $this->masterModel->referensi(1110),
      'kepercayaanAtauBudaya' => $this->masterModel->referensi(1166),
      'strongKidsKurus' => $this->masterModel->referensi(221),
      'strongKidsTurunBerat' => $this->masterModel->referensi(222),
      'strongKidsKondisi' => $this->masterModel->referensi(223),
      'strongKidsMalnutrisi' => $this->masterModel->referensi(224),
      'hdsUmur' => $this->masterModel->referensi(1014),
      'hdsJk'   => $this->masterModel->referensi(1015),
      'hdsDa'   => $this->masterModel->referensi(1016),
      'hdsGk'   => $this->masterModel->referensi(1017),
      'hdsFl'   => $this->masterModel->referensi(1018),
      'hdsOb'   => $this->masterModel->referensi(1019),
      'hdsPo'   => $this->masterModel->referensi(1020),
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/pengkajianRiAnak', $data);
  }

  public function masalahKesehatan_edit()
  {
    $id = $this->input->post('id');
    $idemr = $this->input->post('idemr');

    $resultMasalahKesehatan = $this->masterModel->masalahKesehatan($id);
    $resultMasalahKesehatanDetil = $this->masterModel->masalahKesehatanDetil($resultMasalahKesehatan->ID);
    $getPengkajian = $this->AnakModel->getPengkajian($idemr);

    $data = array(
      'titleMasalahKesehatan' => $resultMasalahKesehatan->KATEGORI,
      'DataMasalahKesehatan' => $resultMasalahKesehatanDetil,
      'getPengkajian' => $getPengkajian,
    );

    $this->load->view('Pengkajian/emr/masalahKesehatan/masalahKesehatan_edit', $data);
  }

  public function asuhanKeperawatan_edit()
  {
    $id = $this->input->post('id');
    $idemr = $this->input->post('idemr');

    $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
    $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
    $getPengkajian = $this->AnakModel->getPengkajian($idemr);

    $data = array(
      'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
      'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
      'getPengkajian' => $getPengkajian,
    );

    $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
  }

  public function simpanIndeksBarthelRiA()
  {
    $nokun = $this->input->post('nokun');
    $total_barthel_1 = $this->input->post('total_barthel_1');
    $total_barthel_2 = $this->input->post('total_barthel_2');
    $total_barthel_3 = $this->input->post('total_barthel_3');
    $total_barthel_4 = $this->input->post('total_barthel_4');
    $total_barthel_5 = $this->input->post('total_barthel_5');
    $total_barthel_6 = $this->input->post('total_barthel_6');
    $total_barthel_7 = $this->input->post('total_barthel_7');
    $total_barthel_8 = $this->input->post('total_barthel_8');
    $total_barthel_9 = $this->input->post('total_barthel_9');
    $total_barthel_10 = $this->input->post('total_barthel_10');
    $oleh = $this->session->userdata("id");

    $totalSkorIndeksBarthel = $total_barthel_1 + $total_barthel_2 + $total_barthel_3 + $total_barthel_4 + $total_barthel_5 + $total_barthel_6 + $total_barthel_7 + $total_barthel_8 + $total_barthel_9 + $total_barthel_10;

    if ($totalSkorIndeksBarthel == 20) {
      echo "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 4) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 8) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 11) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 19) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 6,
      'rangsang_bab' => $total_barthel_1,
      'rangsang_berkemih' => $total_barthel_2,
      'bersihkan_diri' => $total_barthel_3,
      'penggunaan_kloset' => $total_barthel_4,
      'makan' => $total_barthel_5,
      'berubah_posisi' => $total_barthel_6,
      'berpindah' => $total_barthel_7,
      'memakai_baju' => $total_barthel_8,
      'naik_tangga' => $total_barthel_9,
      'mandi' => $total_barthel_10,
      'oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->AnakModel->get_count_indeksBarthel($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_barthel_indek', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 6);
      $this->db->update('keperawatan.tb_barthel_indek', $data);
    }
  }

  public function simpanHumptyDumptyRiA()
  {
    $nokun = $this->input->post('nokun');
    $hdsUmur = $this->input->post('hdsUmur');
    $hdsJk = $this->input->post('hdsJk');
    $hdsDa = $this->input->post('hdsDa');
    $hdsGk = $this->input->post('hdsGk');
    $hdsFl = $this->input->post('hdsFl');
    $hdsOb = $this->input->post('hdsOb');
    $hdsPo = $this->input->post('hdsPo');
    $oleh = $this->session->userdata("id");

    $hdsUmurNilai = $this->AnakModel->getNilaiHumptyDumpty($hdsUmur);
    $hdsJkNilai = $this->AnakModel->getNilaiHumptyDumpty($hdsJk);
    $hdsDaNilai = $this->AnakModel->getNilaiHumptyDumpty($hdsDa);
    $hdsGkNilai = $this->AnakModel->getNilaiHumptyDumpty($hdsGk);
    $hdsFlNilai = $this->AnakModel->getNilaiHumptyDumpty($hdsFl);
    $hdsObNilai = $this->AnakModel->getNilaiHumptyDumpty($hdsOb);
    $hdsPoNilai = $this->AnakModel->getNilaiHumptyDumpty($hdsPo);

    $totalHumptyDumpty = $hdsUmurNilai + $hdsJkNilai + $hdsDaNilai + $hdsGkNilai + $hdsFlNilai + $hdsObNilai + $hdsPoNilai;

    if ($totalHumptyDumpty <= 7) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b></div>";
    } elseif ($totalHumptyDumpty <= 11) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b></div>";
    } elseif ($totalHumptyDumpty >= 12) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko tinggi</b></div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 6,
      'umur' => $hdsUmur,
      'jenis_kelamin' => $hdsJk,
      'diagnosa' => $hdsDa,
      'gangguan_kognitif' => $hdsGk,
      'faktor_lingkungan' => $hdsFl,
      'respon_terhadap_operasi' => $hdsOb,
      'penggunaan_obat' => $hdsPo,
      'oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $jumlah = $this->AnakModel->get_count_humptyDumpty($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_penilaian_humptydumpty_igd', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->where('ref', 6);
      $this->db->update('keperawatan.tb_penilaian_humptydumpty_igd', $data);
    }
  }

  public function yakinVerifPengkajian()
  {
    $post = $this->input->post();
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif' => 1,
      'verif_oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $this->db->where('id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

  public function yakinVerifPengkajianOlehPerawat()
  {
    $post = $this->input->post();
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif_perawat' => 1,
      'verif_oleh_perawat' => $oleh,
    );

    $this->db->where('tb_keperawatan.id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }


  public function simpanPengkajianRiA($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');

        $dataKeperawatan = array(
          'id_emr'         => $getIdEmr,
          'nopen'          => $post['nopen'],
          'nokun'          => $post['nokun'],
          'jenis'          => 6,
          'diagnosa_masuk' => $post['rujukanRiA'],
          'created_by'     => $this->session->userdata('id'),
          'flag'           => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataRiwayatKesehatan = array(
          'id_emr' => $getIdEmr,
          'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
          'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
          'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : "",
          'riwayat_transfusi' => isset($post['riwayat_transfusi_darah']) ? $post['riwayat_transfusi_darah'] : "",
          'reaksi_transfusi' => isset($post['riwayat_transfusi_darah_desk']) ? $post['riwayat_transfusi_darah_desk'] : "",
          'isi_reaksi_transfusi' => isset($post['riwayat_td_reaksi_alergi']) ? json_encode($post['riwayat_td_reaksi_alergi']) : "",
          'kebiasaan' => isset($post['kebiasaan_merokok']) ? $post['kebiasaan_merokok'] : "",
          'isi_kebiasaan' => isset($post['kebiasaan_merokok_desk']) ? json_encode($post['kebiasaan_merokok_desk']) : "",
          'riwayat_kanker' => isset($post['riwayat_kanker_keluarga']) ? $post['riwayat_kanker_keluarga'] : "",
          'isi_kanker' => isset($post['riwayat_kanker_keluarga_desk']) ? json_encode($post['riwayat_kanker_keluarga_desk']) : "",
          'riwayat_metabolik' => isset($post['riwayat_metabolik_keluarga']) ? $post['riwayat_metabolik_keluarga'] : "",
          'isi_metabolik' => isset($post['riwayat_metabolik_keluarga_desk']) ? json_encode($post['riwayat_metabolik_keluarga_desk']) : "",
          'deteksidini' => isset($post['riwayat_deteksi_dini_kanker']) ? $post['riwayat_deteksi_dini_kanker'] : "",
          'isi_deteksidini' => isset($post['riwayat_deteksi_dini_kanker_desk']) ? json_encode($post['riwayat_deteksi_dini_kanker_desk']) : "",
          'apakah_pernah_dirawat' => isset($post['pernah_di_rawat']) ? $post['pernah_di_rawat'] : "",
          'apakah_pernah_dirawat_penyakit_lain' => isset($post['deskPernahDiRawatRiAPenyakitLain']) ? $post['deskPernahDiRawatRiAPenyakitLain'] : "",
          'apakah_pernah_dirawat_tahun' => isset($post['deskPernahDiRawatRiATahun']) ? $post['deskPernahDiRawatRiATahun'] : "",
          'apakah_ini_pertama_di_rskd' => isset($post['perawatan_pertama_dirskd']) ? $post['perawatan_pertama_dirskd'] : "",
          'ket_apakah_ini_pertama_di_rskd' => isset($post['deskPerawatanPertamaDiRskdRiA']) ? $post['deskPerawatanPertamaDiRskdRiA'] : "",
        );

        // echo "<pre>data riwayat kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataRiwayatKelahiran = array(
          'id_emr' => $getIdEmr,
          'anak_ke' => isset($post['anak_ke']) ? $post['anak_ke'] : "",
          'saudara' => isset($post['jml_saudara']) ? $post['jml_saudara'] : "",
          'cara_lahir' => isset($post['cara_kelahiran']) ? $post['cara_kelahiran'] : "",
          'kondisi_lahir' => isset($post['kondisi_lahir']) ? $post['kondisi_lahir'] : "",
          'berat_badan' => isset($post['berat_badan_lahir']) ? $post['berat_badan_lahir'] : "",
          'panjang_badan' => isset($post['panjang_badan']) ? $post['panjang_badan'] : "",
          'lingkar_kepala' => isset($post['lingkar_kepala_lahir']) ? $post['lingkar_kepala_lahir'] : "",
          'kelainan_bawaan' => isset($post['kelainan_bawaan_anak']) ? $post['kelainan_bawaan_anak'] : "",
          'isi_kelainan' => isset($post['kelainan_bawaan_anak_desk']) ? json_encode($post['kelainan_bawaan_anak_desk']) : "",
          'imunisasi' => isset($post['riwayat_imunisasi_dasar_anak']) ? $post['riwayat_imunisasi_dasar_anak'] : "",
          'isi_imunisasi' => isset($post['riwayat_imunisasi_dasar_anak_desk']) ? json_encode($post['riwayat_imunisasi_dasar_anak_desk']) : "",
          'riwayat_tumbuh_kembang' => isset($post['riwayat_tumbuh_kembang']) ? $post['riwayat_tumbuh_kembang'] : "",
          'tengkurap' => isset($post['usia_tengkurap']) ? $post['usia_tengkurap'] : "",
          'berjalan' => isset($post['usia_berjalan']) ? $post['usia_berjalan'] : "",
          'duduk' => isset($post['usia_duduk']) ? $post['usia_duduk'] : "",
          'bicara' => isset($post['usia_bicara']) ? $post['usia_bicara'] : "",
          'berdiri' => isset($post['usia_berdiri']) ? $post['usia_berdiri'] : "",
          'tumbuh_gigi' => isset($post['usia_tumbuh_gigi']) ? $post['usia_tumbuh_gigi'] : "",
          'cek_riwayat_kelahiran' => isset($post['cekNotApplicableRiwayatKelahiran1']) ? $post['cekNotApplicableRiwayatKelahiran1'] : 0,
          'cek_riwayat_tumbuh_kembang' => isset($post['cekNotApplicableRiwayatTumbuhKembang']) ? $post['cekNotApplicableRiwayatTumbuhKembang'] : 0,
        );

        // echo "<pre>data riwayat kelahiran ";print_r($dataRiwayatKelahiran);echo "</pre>";

        $dataPemeriksaanKesehatan = array(
          'id_emr' => $getIdEmr,
          'keluhan_pasien' => isset($post['keluhan_pasien']) ? $post['keluhan_pasien'] : "",
          'makanan_yang_disukai' => isset($post['makanan_yang_disukaiRiA']) ? $post['makanan_yang_disukaiRiA'] : "",
          'nafsu_makan' => isset($post['nafsu_makan']) ? $post['nafsu_makan'] : "",
          'pola_makan' => isset($post['pola_makan']) ? $post['pola_makan'] : "",
          'makanan_saat_ini' => isset($post['makanan_saat_ini']) ? $post['makanan_saat_ini'] : "",
          'keterangan_makanan_saat_ini' => isset($post['keterangan_makanan_saat_ini']) ? $post['keterangan_makanan_saat_ini'] : "",
          'pola_tidur_lebih_satu' => isset($post['pola_tidur']) ? json_encode($post['pola_tidur']) : "",
          'durasi_tidur' => isset($post['durasi_tidur']) ? $post['durasi_tidur'] : "",
          'kebiasaan_sebelum_tidur' => isset($post['kebiasaan_sebelum_tidur']) ? $post['kebiasaan_sebelum_tidur'] : "",
          'mandi' => isset($post['mandi']) ? $post['mandi'] : "",
          'masalah_kesehatan_keperawatan' => isset($post['masalahKesehatanKeperawatanDewasaRiA']) ? json_encode($post['masalahKesehatanKeperawatanDewasaRiA']) : "",
          'jumlah_mandi' => isset($post['jumlah_mandi']) ? $post['jumlah_mandi'] : "",
          'gosok_gigi' => isset($post['gosok_gigi']) ? $post['gosok_gigi'] : "",
          'kebersihan_kuku' => isset($post['kebersihan_kuku']) ? $post['kebersihan_kuku'] : "",
          'kebersihan_anal' => isset($post['kebersihan_anal']) ? $post['kebersihan_anal'] : "",
          'aktivitas_bermain' => isset($post['aktivitas_bermain']) ? $post['aktivitas_bermain'] : "",
          'lingkar_kepala' => isset($post['lingkarKepala']) ? $post['lingkarKepala'] : "",
          'bentuk_kepala' => isset($post['bentuk_kepala_anak']) ? $post['bentuk_kepala_anak'] : "",
          'isi_kelainan_kepala' => isset($post['bentuk_kepala_anak_desk']) ? json_encode($post['bentuk_kepala_anak_desk']) : "",
          'irama' => isset($post['irama_pernapasan']) ? $post['irama_pernapasan'] : "",
          'reaksi_dada' => isset($post['retraksi_dada']) ? $post['retraksi_dada'] : "",
          'alat_bantu_napas' => isset($post['alat_bantu_napas_anak']) ? $post['alat_bantu_napas_anak'] : "",
          'alat_bantu_napas_o2' => isset($post['alatBantuNapasO2RiA']) ? $post['alatBantuNapasO2RiA'] : "",
          'alat_bantu_napas_ventilator' => isset($post['alatBantuNapasVentilatorRiA']) ? $post['alatBantuNapasVentilatorRiA'] : "",
          'sianosis' => isset($post['sianosis']) ? $post['sianosis'] : "",
          'pucat' => isset($post['pucat']) ? $post['pucat'] : "",
          'capillary_refill_test' => isset($post['capillary_refill_test']) ? $post['capillary_refill_test'] : "",
          'akral' => isset($post['akral']) ? $post['akral'] : "",
          'pembesaran_kelenjar' => isset($post['pembesaran_kelenjar_getah_bening_anak']) ? $post['pembesaran_kelenjar_getah_bening_anak'] : "",
          'gangguan_neurologi' => isset($post['gangguan_neurologi']) ? $post['gangguan_neurologi'] : "",
          'isi_gangguan_neurologi' => isset($post['neurologi_gangguan_anak_deskRiA']) ? $post['neurologi_gangguan_anak_deskRiA'] : "",
          'mata' => isset($post['neurologi_mata_anak']) ? $post['neurologi_mata_anak'] : "",
          'isi_abnormal' => isset($post['neurologi_mata_anak_deskRiA']) ? $post['neurologi_mata_anak_deskRiA'] : "",
          'mulut' => isset($post['mulut_anak']) ? $post['mulut_anak'] : "",
          'isi_mulut' => isset($post['mulut_anak_desk']) ? json_encode($post['mulut_anak_desk']) : "",
          'abdomen' => isset($post['abdomen_anak']) ? $post['abdomen_anak'] : "",
          'isi_abdomen' => isset($post['abdomen_anak_desk']) ? json_encode($post['abdomen_anak_desk']) : "",
          'asites' => isset($post['asites_anak']) ? $post['asites_anak'] : "",
          'isi_asites' => isset($post['asites_anak_desk']) ? json_encode($post['asites_anak_desk']) : "",
          'defekasi' => isset($post['defekasi_anak']) ? $post['defekasi_anak'] : "",
          'defekasi_stoma' => isset($post['defekasi_anak_desk_stoma']) ? json_encode($post['defekasi_anak_desk_stoma']) : "",
          'defekasi_frekuensi' => isset($post['defekasi_anak_desk_spontan_frekuensi']) ? json_encode($post['defekasi_anak_desk_spontan_frekuensi']) : "",
          'defekasi_konsistensi' => isset($post['defekasi_anak_desk_spontan_konsistensi']) ? json_encode($post['defekasi_anak_desk_spontan_konsistensi']) : "",
          'fases' => isset($post['karakteristik_feses_anak']) ? $post['karakteristik_feses_anak'] : "",
          'fases_lain' => isset($post['karakteristik_feses_anak_desk']) ? json_encode($post['karakteristik_feses_anak_desk']) : "",
          'urin' => isset($post['urin']) ? $post['urin'] : "",
          'rectal' => isset($post['rectal_anak']) ? $post['rectal_anak'] : "",
          'isi_rectal' => isset($post['rectal_anak_desk']) ? json_encode($post['rectal_anak_desk']) : "",
          'genetalia' => isset($post['genetalia_anak']) ? $post['genetalia_anak'] : "",
          'isi_genetalia' => isset($post['genetalia_anak_desk']) ? json_encode($post['genetalia_anak_desk']) : "",
          'kulit' => isset($post['kulit']) ? $post['kulit'] : "",
          'warna_kulit' => isset($post['warna_kulit']) ? $post['warna_kulit'] : "",
          'luka' => isset($post['luka']) ? $post['luka'] : "",
          'lokasi_luka' => isset($post['lokasi_luka']) ? $post['lokasi_luka'] : "",
          'kelainan_tulang' => isset($post['kelainan_tulang_anak']) ? $post['kelainan_tulang_anak'] : "",
          'isi_kelainan_tulang' => isset($post['kelainan_tulang_anak_desk']) ? json_encode($post['kelainan_tulang_anak_desk']) : "",
          'gerakan_anak' => isset($post['gerakan_anak']) ? $post['gerakan_anak'] : "",
          'isi_gerakan_anak' => isset($post['gerakan_anak_desk']) ? json_encode($post['gerakan_anak_desk']) : "",
          'status_mental' => isset($post['status_mental']) ? json_encode($post['status_mental']) : "",
          'hub_keluarga' => isset($post['sdeh']) ? $post['sdeh'] : "",
          'tempat_tinggal' => isset($post['tempat_tinggal']) ? $post['tempat_tinggal'] : "",
          'isi_tempat_tinggal' => isset($post['tempat_tinggal_desk']) ? json_encode($post['tempat_tinggal_desk']) : "",
          'pengasuh' => isset($post['pengasuh']) ? $post['pengasuh'] : "",
          'jenis_sekolah' => isset($post['jenis_sekolah']) ? $post['jenis_sekolah'] : "",
          'privasi_khusus' => isset($post['privasi_khusus']) ? $post['privasi_khusus'] : "",
          'privasi_khusus_lainnya' => isset($post['privasi_khusus_desk']) ? $post['privasi_khusus_desk'] : "",
          'kebutuhan_atau_budaya' => isset($post['kebutuhanAtauBudayaRiA']) ? $post['kebutuhanAtauBudayaRiA'] : "",
          'ket_kebutuhan_atau_budaya' => isset($post['deskKebutuhanAtauBudayaRiA']) ? json_encode($post['deskKebutuhanAtauBudayaRiA']) : "",
          'tampak_kurus' => isset($post['strongKidsKurusRiA']) ? $post['strongKidsKurusRiA'] : "",
          'kondisi' => isset($post['strongKidsKondisiRiA']) ? $post['strongKidsKondisiRiA'] : "",
          'resiko_malnutrisi' => isset($post['strongKidsMalnutrisiRiA']) ? $post['strongKidsMalnutrisiRiA'] : "",
        );

        // echo "<pre>data pemeriksaan fisik ";print_r($dataPemeriksaanKesehatan);echo "</pre>";exit();

        $dataSkriningNyeri = array(
          'nokun' => $post['nokun'],
          'data_source' => 2,
          'ref' => $getIdEmr,
          'metode' => isset($post['skrining_nyeri_RiA']) ? $post['skrining_nyeri_RiA'] : "",
          'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'provokative' => isset($post['propocative_RiA']) ? $post['propocative_RiA'] : "",
          'quality' => isset($post['quality_RiA']) ? $post['quality_RiA'] : "",
          'regio' => isset($post['regio_RiA']) ? $post['regio_RiA'] : "",
          'severity' => isset($post['severity_RiA']) ? $post['severity_RiA'] : "",
          'time' => isset($post['time_RiA']) ? $post['time_RiA'] : "",
          'ket_time' => isset($post['durasi_nyeri_RiA']) ? $post['durasi_nyeri_RiA'] : "",
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining nyeri ";print_r($dataSkriningNyeri);echo "</pre>";

        $dataEdukasiKeperawatan = array(
          'id_emr' => $getIdEmr,
          'tingkat_pendidikan' => isset($post['tingkat_pendidikan']) ? $post['tingkat_pendidikan'] : "",
          'bahasa' => isset($post['bahasa_sehari_hari']) ? $post['bahasa_sehari_hari'] : "",
          'bahasa_daerah' => isset($post['bahasa_daerah']) ? $post['bahasa_daerah'] : "",
          'bahasa_lain' => isset($post['bahasa_lainnya']) ? $post['bahasa_lainnya'] : "",
          'penerjemah' => isset($post['perlu_penerjemah']) ? $post['perlu_penerjemah'] : "",
          'penerjemah_lain' => isset($post['penerjemah_lainnya']) ? $post['penerjemah_lainnya'] : "",
          'informasi' => isset($post['kesedian_informasi']) ? $post['kesedian_informasi'] : "",
        );

        // echo "<pre>data kebutuhan edukasi ";print_r($dataEdukasiKeperawatan);echo "</pre>";

        $dataHambatan = array();
        $indexHambatan = 0;
        if (isset($post['hambatan'])) {
          foreach ($post['hambatan'] as $input) {
            if ($post['hambatan'][$indexHambatan] != "") {
              array_push(
                $dataHambatan,
                array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['hambatan'][$indexHambatan],
                  'keterangan' => isset($post['hambatan_lainnya']) ? ($post['hambatan'][$indexHambatan] == 106 ? $post['hambatan_lainnya'] : "") : "",
                )
              );
            }
            $indexHambatan++;
          }
        }

        // echo "<pre>data hambatan ";print_r($dataHambatan);echo "</pre>";

        $dataKebutuhanPembelajaran = array();
        $indexKebutuhanPembelajaran = 0;
        if (isset($post['kebutuhan_pembelajaran'])) {
          foreach ($post['kebutuhan_pembelajaran'] as $input) {
            if ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] != "") {
              array_push(
                $dataKebutuhanPembelajaran,
                array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran],
                  'keterangan' => isset($post['kebutuhan_pembelajaran_lainnya']) ? ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] == 104 ? $post['kebutuhan_pembelajaran_lainnya'] : "") : "",
                )
              );
            }
            $indexKebutuhanPembelajaran++;
          }
        }

        // echo "<pre>data kebutuhan pembelajaran pasien ";print_r($dataKebutuhanPembelajaran);echo "</pre>";

        $dataP3 = array(
          'id_emr' => $getIdEmr,
          'memerlukan_p3' => isset($post['pasien_perlu_p3']) ? $post['pasien_perlu_p3'] : "",
          'keterbatasan_mobilitas' => isset($post['pasien_perlu_p32']) ? $post['pasien_perlu_p32'] : 3790,
          'ket_keterbatasan_mobilitas' => isset($post['ketP32']) ? $post['ketP32'] : "",
          'perawatan' => isset($post['pasien_perlu_p33']) ? $post['pasien_perlu_p33'] : 3792,
          'ket_perawatan' => isset($post['ketP33']) ? $post['ketP33'] : "",
          'adakah_keluarga' => isset($post['pasien_perlu_p35']) ? $post['pasien_perlu_p35'] : 3796,
          'ket_adakah_keluarga' => isset($post['ketP35']) ? $post['ketP35'] : "",
          'pasien_pulang_bawa_obat' => isset($post['pasien_perlu_p37']) ? $post['pasien_perlu_p37'] : 3800,
          'ket_pasien_pulang_bawa_obat' => isset($post['ketP37']) ? $post['ketP37'] : "",
          'risiko_infeksi' => isset($post['pasien_perlu_p38']) ? $post['pasien_perlu_p38'] : 3802,
          'ket_risiko_infeksi' => isset($post['ketP38']) ? $post['ketP38'] : "",
          'efek_samping' => isset($post['pasien_perlu_p39']) ? $post['pasien_perlu_p39'] : 3804,
          'ket_efek_samping' => isset($post['ketP39']) ? $post['ketP39'] : "",
          'masalah_untuk_transportasi' => isset($post['pasien_perlu_p310']) ? $post['pasien_perlu_p310'] : 3806,
          'ket_masalah_untuk_transportasi' => isset($post['ketP310']) ? $post['ketP310'] : "",
          'jenis' => 2,
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data perencanaan pemulangan pasien ";print_r($dataP3);echo "</pre>";

        $dataSkriningGizi = array(
          'id_emr' => $getIdEmr,
          'kurus_anak' => isset($post['strongKidsKurusRiA']) ? $post['strongKidsKurusRiA'] : "",
          'penurunan_bb_anak' => isset($post['strongKidsTurunBeratRiA']) ? $post['strongKidsTurunBeratRiA'] : "",
          'kondisi_anak' => isset($post['strongKidsKondisiRiA']) ? $post['strongKidsKondisiRiA'] : "",
          'malnutrisi_anak' => isset($post['strongKidsMalnutrisiRiA']) ? $post['strongKidsMalnutrisiRiA'] : "",
          'jenis' => 2,
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data Skrining Gizi ";print_r($dataSkriningGizi);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 2,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0,
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";

        $dataTandaVital = array(
          'data_source' => 2,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data Tanda vital ";print_r($dataTandaVital);echo "</pre>";

        $dataKesedaran = array(
          'data_source' => 2,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data kesadaran ";print_r($dataKesedaran);echo "</pre>";

        // exit();

        $this->db->trans_begin();

        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanKesehatan);
          $this->db->where('nokun', $post['nokun']);
          $this->db->where('data_source', 2);
          $this->db->update('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          // $this->db->replace('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->replace('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          $this->db->replace('keperawatan.tb_perencanaan_pemulangan_pasien', $dataP3);
          $this->db->replace('keperawatan.tb_skrining_gizi', $dataSkriningGizi);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 2);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          // $this->db->replace('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 2);
          $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
          // $this->db->replace('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->where('ref', $post['idemr']);
          $this->db->where('data_source', 2);
          $this->db->update('db_pasien.tb_kesadaran', $dataKesedaran);
          // $this->db->replace('db_pasien.tb_kesadaran', $dataKesedaran);
          $this->db->replace('keperawatan.tb_riwayat_kelahiran', $dataRiwayatKelahiran);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
          $this->db->delete('keperawatan.tb_hambatan', array('id_emr' => $idRefEmr));
          foreach ($dataHambatan as $key => $value) {
            $this->db->replace('keperawatan.tb_hambatan', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_kebutuhan_pembelajaran', array('id_emr' => $idRefEmr));
          foreach ($dataKebutuhanPembelajaran as $key => $value) {
            $this->db->replace('keperawatan.tb_kebutuhan_pembelajaran', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $idRefEmr));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $this->db->delete('keperawatan.tb_masalah_kesehatan', array('id_emr' => $idRefEmr));
          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanKesehatan);
          $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->insert('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          $this->db->insert('keperawatan.tb_perencanaan_pemulangan_pasien', $dataP3);
          $this->db->insert('keperawatan.tb_skrining_gizi', $dataSkriningGizi);
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->insert('db_pasien.tb_kesadaran', $dataKesedaran);
          $this->db->insert('keperawatan.tb_riwayat_kelahiran', $dataRiwayatKelahiran);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
          if (isset($post['hambatan'])) {
            $this->db->insert_batch('keperawatan.tb_hambatan', $dataHambatan);
          }
          if (isset($post['kebutuhan_pembelajaran'])) {
            $this->db->insert_batch('keperawatan.tb_kebutuhan_pembelajaran', $dataKebutuhanPembelajaran);
          }
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      } else if ($param == 'count') {
        $result = $this->AnakModel->get_count();
        echo json_encode($result);
      } else if ($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataAnakModel = $this->AnakModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataAnakModel
        ));
      }
    }
  }

  public function datatables()
  {
    $result = $this->MedisModel->historyPengkajian();
    $namaVerif = null;
    $tombolCetak = null;

    $data = array();
    foreach ($result as $row) {
      // $status_edit_perawat = $row -> STATUS_EDIT_PERAWAT;
      // $status_edit_medis = $row -> STATUS_EDIT_MEDIS;
      $action = "";
      $verif = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
      if ($row->ID_EMR_PERAWAT != null) {
        $action .= '<a class="btn btn-success btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan</a>';
        if ($this->session->userdata('status') == 2) {
          $action .= '<button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> lihat</button>';

          if ($row->STATUS_VERIFIKASI == 0) {
            $namaVerif = '<h4>-</h4>';
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $namaVerif = $row->INFO_VERIFIKASI;
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }
        }
      }

      if ($row->ID_EMR_MEDIS != null) {
        $action .= '<a class="btn btn-purple btn-block btn-sm" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-eye"></i> View Medis</a>';
        if ($this->session->userdata('status') == 1) {
          $action .= '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOPEN . '"><i class="fa fa-eye"></i> lihat</button>';
          if ($row->STATUS_VERIFIKASI == 0) {
            $verif = '<a class="btn btn-custom btn-block btn-sm verif" data-nokun="' . $row->NOKUN . '" data-nopen="' . $row->NOPEN . '" data-norm="' . $row->NORM . '">Verif</i></a>';
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }
        }
      }

      $sub_array = array();
      $sub_array[] = $row->INFO;
      $sub_array[] = $verif;
      $sub_array[] = $row->RUANGAN;
      $sub_array[] = $row->TANGGAL_KUNJUNGAN;
      $sub_array[] = $action;
      $sub_array[] = $row->DPJP;
      $sub_array[] = $namaVerif;
      $sub_array[] = $tombolCetak;
      $sub_array[] = $row->USER_MEDIS;
      $sub_array[] = $row->USER_PERAWAT;
      $data[] = $sub_array;

      // if($STATUS_EDIT == 0){
      // $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
      // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
      // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> lihat</button>';
      // }else{
      //   $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
      // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
      // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
      // }
    }

    $output = array(
      "draw" => intval($this->input->post("draw")),
      "data" => $data
    );
    echo json_encode($output);
  }
}

/* End of file Anak.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkajianRI/Anak.php */