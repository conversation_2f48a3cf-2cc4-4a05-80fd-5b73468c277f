<?php
defined('BASEPATH') or exit('No direct script access allowed');

class StatusSedasi extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $id_status_sedasi = $this->uri->segment(8);
        $getPengkajian = $this->pengkajianAwalModel->historyDetailSedasi($id_status_sedasi);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $historySedasi = $this->pengkajianAwalModel->historySedasi($getNomr['NORM']);
        $listRiwayatPengobatan = $this->masterModel->referensi(529);
        $listAlergi = $this->masterModel->referensi(530);
        $listKomplikasiAnestesi = $this->masterModel->referensi(579);
        $listDrAnastesi = $this->masterModel->listDrAnastesi();
        $listPerawat = $this->masterModel->listAllPegawai();
        $listGolonganDarah = $this->masterModel->listGolonganDarah();
        $listDr = $this->masterModel->listDr();
        $listMerokokAlkohol = $this->masterModel->referensi(583);
        $listWajah = $this->masterModel->referensi(585);
        $listThoraxAbdomen = $this->masterModel->referensi(586);
        $listMembukaMulut = $this->masterModel->referensi(587);
        $listJarakThyro = $this->masterModel->referensi(588);
        $listMallampati = $this->masterModel->referensi(589);
        $listObstruksiNapas = $this->masterModel->referensi(590);
        $listRahangKedepan = $this->masterModel->referensi(592);
        $listEkstensiLeher = $this->masterModel->referensi(593);
        $listCollar = $this->masterModel->referensi(594);
        $listRespirasi = $this->masterModel->referensi(595);
        $listKardiovaskular = $this->masterModel->referensi(596);
        $listRenal = $this->masterModel->referensi(597);
        $listHepato = $this->masterModel->referensi(598);
        $listNeuro = $this->masterModel->referensi(599);
        $listLainlain = $this->masterModel->referensi(600);
        $listKonversiTindakan = $this->masterModel->referensi(601);
        $listPerawat2 = $this->masterModel->listPerawat();
        $listPerawat3 = $this->masterModel->listPerawat();
        $listPemberianObat = $this->masterModel->referensi(717);
        $listNapas = $this->masterModel->referensi(723);
        $listNadi = $this->masterModel->referensi(724);
        $listTekananDarah = $this->masterModel->referensi(725);

        $data = array(
            'id_status_sedasi' => $id_status_sedasi,
            'getPengkajian' => isset($getPengkajian) ? $getPengkajian: "",
            'getNomr' => $getNomr,
            'historySedasi' => $historySedasi,
            'listRiwayatPengobatan' => $listRiwayatPengobatan,
            'listAlergi' => $listAlergi,
            'listKomplikasiAnestesi' => $listKomplikasiAnestesi,
            'listDrAnastesi' => $listDrAnastesi,
            'listDr' => $listDr,
            'listPerawat' => $listPerawat,
            'listPerawat2' => $listPerawat2,
            'listPerawat3' => $listPerawat3,
            'listGolonganDarah' => $listGolonganDarah,
            'listMerokokAlkohol' => $listMerokokAlkohol,
            'listWajah' => $listWajah,
            'listThoraxAbdomen' => $listThoraxAbdomen,
            'listMembukaMulut' => $listMembukaMulut,
            'listJarakThyro' => $listJarakThyro,
            'listMallampati' => $listMallampati,
            'listObstruksiNapas' => $listObstruksiNapas,
            'listRahangKedepan' => $listRahangKedepan,
            'listEkstensiLeher' => $listEkstensiLeher,
            'listCollar' => $listCollar,
            'listRespirasi' => $listRespirasi,
            'listKardiovaskular' => $listKardiovaskular,
            'listRenal' => $listRenal,
            'listHepato' => $listHepato,
            'listNeuro' => $listNeuro,
            'listLainlain' => $listLainlain,
            'listKonversiTindakan' => $listKonversiTindakan,
            'listPemberianObat' => $listPemberianObat,
            'listNapas' => $listNapas,
            'listNadi' => $listNadi,
            'listTekananDarah' => $listTekananDarah,
            'category' => 1
        );

        $this->load->view('Pengkajian/sedasi/sedasi/index',$data);
    }

    public function indexRawatInap()
    {
      $nokun = $this->uri->segment(2);
      $id_status_sedasi = $this->uri->segment(3);
      $getPengkajian = $this->pengkajianAwalModel->historyDetailSedasi($id_status_sedasi);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $historySedasi = $this->pengkajianAwalModel->historySedasi($getNomr['NORM']);
      $listRiwayatPengobatan = $this->masterModel->referensi(529);
      $listAlergi = $this->masterModel->referensi(530);
      $listKomplikasiAnestesi = $this->masterModel->referensi(579);
      $listDrAnastesi = $this->masterModel->listDrAnastesi();
      $listPerawat = $this->masterModel->listAllPegawai();
      $listGolonganDarah = $this->masterModel->listGolonganDarah();
      $listDr = $this->masterModel->listDr();
      $listMerokokAlkohol = $this->masterModel->referensi(583);
      $listWajah = $this->masterModel->referensi(585);
      $listThoraxAbdomen = $this->masterModel->referensi(586);
      $listMembukaMulut = $this->masterModel->referensi(587);
      $listJarakThyro = $this->masterModel->referensi(588);
      $listMallampati = $this->masterModel->referensi(589);
      $listObstruksiNapas = $this->masterModel->referensi(590);
      $listRahangKedepan = $this->masterModel->referensi(592);
      $listEkstensiLeher = $this->masterModel->referensi(593);
      $listCollar = $this->masterModel->referensi(594);
      $listRespirasi = $this->masterModel->referensi(595);
      $listKardiovaskular = $this->masterModel->referensi(596);
      $listRenal = $this->masterModel->referensi(597);
      $listHepato = $this->masterModel->referensi(598);
      $listNeuro = $this->masterModel->referensi(599);
      $listLainlain = $this->masterModel->referensi(600);
      $listKonversiTindakan = $this->masterModel->referensi(601);
      $listPerawat2 = $this->masterModel->listPerawat();
      $listPerawat3 = $this->masterModel->listPerawat();
      $listPemberianObat = $this->masterModel->referensi(717);
      $listNapas = $this->masterModel->referensi(723);
      $listNadi = $this->masterModel->referensi(724);
      $listTekananDarah = $this->masterModel->referensi(725);

      $data = array(
          'id_status_sedasi' => $id_status_sedasi,
          'getPengkajian' => isset($getPengkajian) ? $getPengkajian: "",
          'getNomr' => $getNomr,
          'historySedasi' => $historySedasi,
          'listRiwayatPengobatan' => $listRiwayatPengobatan,
          'listAlergi' => $listAlergi,
          'listKomplikasiAnestesi' => $listKomplikasiAnestesi,
          'listDrAnastesi' => $listDrAnastesi,
          'listDr' => $listDr,
          'listPerawat' => $listPerawat,
          'listPerawat2' => $listPerawat2,
          'listPerawat3' => $listPerawat3,
          'listGolonganDarah' => $listGolonganDarah,
          'listMerokokAlkohol' => $listMerokokAlkohol,
          'listWajah' => $listWajah,
          'listThoraxAbdomen' => $listThoraxAbdomen,
          'listMembukaMulut' => $listMembukaMulut,
          'listJarakThyro' => $listJarakThyro,
          'listMallampati' => $listMallampati,
          'listObstruksiNapas' => $listObstruksiNapas,
          'listRahangKedepan' => $listRahangKedepan,
          'listEkstensiLeher' => $listEkstensiLeher,
          'listCollar' => $listCollar,
          'listRespirasi' => $listRespirasi,
          'listKardiovaskular' => $listKardiovaskular,
          'listRenal' => $listRenal,
          'listHepato' => $listHepato,
          'listNeuro' => $listNeuro,
          'listLainlain' => $listLainlain,
          'listKonversiTindakan' => $listKonversiTindakan,
          'listPemberianObat' => $listPemberianObat,
          'listNapas' => $listNapas,
          'listNadi' => $listNadi,
          'listTekananDarah' => $listTekananDarah,
          'category' => 2
      );

      $this->load->view('Pengkajian/sedasi/sedasi/index',$data);
    
    }

    public function viewInputIntraSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

      $data = array(
          'idsed' => $idsed,
          'getNomr' => $getNomr,
          'nokun' => $nokun
      );

      $this->load->view('Pengkajian/sedasi/sedasi/intraSedasi/intraSedasi',$data);
    }

    public function viewPascaSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $getPascaSedasi = $this->pengkajianAwalModel->getPascaSedasi($idsed);
      $listDrAnastesi = $this->masterModel->listDrAnastesi();

      $sa_ruangan = $this->masterModel->referensi(736);
      $nyeri = $this->masterModel->referensi(734);
      $risikoJatuh = $this->masterModel->referensi(730);
      $kesadaran = $this->masterModel->referensi(727);
      $pernapasan = $this->masterModel->referensi(728);

      $data = array(
          'idsed' => $idsed,
          'getNomr' => $getNomr,
          'nokun' => $nokun,
          'sa_ruangan' => $sa_ruangan,
          'nyeri' => $nyeri,
          'risikoJatuh' => $risikoJatuh,
          'kesadaran' => $kesadaran,
          'pernapasan' => $pernapasan,
          'getPascaSedasi' => $getPascaSedasi,
          'listDrAnastesi' => $listDrAnastesi,
          'listPegawai' => $this->masterModel->listAllPegawai(),
      );

      $this->load->view('Pengkajian/sedasi/sedasi/pascaSedasi/index',$data);
    }

    public function viewPascaInputanSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $getPascaSedasi = $this->pengkajianAwalModel->getPascaSedasi($idsed);
      $listDrAnastesi = $this->masterModel->listDrAnastesi();

      $sa_ruangan = $this->masterModel->referensi(736);
      $nyeri = $this->masterModel->referensi(734);
      $risikoJatuh = $this->masterModel->referensi(730);
      $kesadaran = $this->masterModel->referensi(727);
      $pernapasan = $this->masterModel->referensi(728);

      $data = array(
          'idsed' => $idsed,
          'getNomr' => $getNomr,
          'nokun' => $nokun,
          'sa_ruangan' => $sa_ruangan,
          'nyeri' => $nyeri,
          'risikoJatuh' => $risikoJatuh,
          'kesadaran' => $kesadaran,
          'pernapasan' => $pernapasan,
          'getPascaSedasi' => $getPascaSedasi,
          'listDrAnastesi' => $listDrAnastesi,
          'listPegawai' => $this->masterModel->listAllPegawai(),
      );

      $this->load->view('Pengkajian/sedasi/sedasi/pascaSedasi/pascaSedasi',$data);
    }

    public function viewTandaVitalPascaSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $norm = $this->input->post("norm");

      $HistoryTandaVitalPasca = $this->pengkajianAwalModel->HistoryTandaVitalPascaSedasi($idsed);
      $HistoryInputTandaVitalPascaSedasi = $this->pengkajianAwalModel->HistoryInputTandaVitalPascaSedasi($idsed);

      $waktu = array();
      $napas = array();
      $nadi = array();
      $sistolik = array();
      $diastolik = array();
      $map = array();
      foreach ($HistoryTandaVitalPasca as $HistoryTandaVitalPasca){
        array_push($waktu,$HistoryTandaVitalPasca['pukul']);
        array_push($napas,$HistoryTandaVitalPasca['pernapasan']);
        array_push($nadi,$HistoryTandaVitalPasca['nadi']);
        array_push($sistolik,$HistoryTandaVitalPasca['td_sistolik']);
        array_push($diastolik,$HistoryTandaVitalPasca['td_diastolik']);
        array_push($map,$HistoryTandaVitalPasca['map']);
      }

      $data = array(
        'idsed' => $idsed,
        'norm' => $norm,
        'nokun' => $nokun,
        'waktu'                             => $waktu,
        'napas'                             => $napas,
        'nadi'                              => $nadi,
        'sistolik'                          => $sistolik,
        'diastolik'                         => $diastolik,
        'map'                               => $map,
        'HistoryInputTandaVitalPascaSedasi' => $HistoryInputTandaVitalPascaSedasi,
      );

      $this->load->view('Pengkajian/sedasi/sedasi/pascaSedasi/tandaVital',$data);
    }

    public function viewPremedikasiIntraSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $norm = $this->input->post("norm");
      $nmObat = $this->pengkajianAwalModel->namaObatInfusAnestesia();
      $premedikasiSedasiObat = $this->pengkajianAwalModel->premedikasiSedasiObat($idsed);
      $jalurPemberian = $this->masterModel->referensi(901);
      $jenisPemberian = $this->masterModel->referensi(1722);

      $data = array(
          'idsed' => $idsed,
          'norm' => $norm,
          'nokun' => $nokun,
          'nmObat' => $nmObat,
          'jalurPemberian' => $jalurPemberian,
          'premedikasiSedasiObat' => $premedikasiSedasiObat,
          'jenisPemberian' => $jenisPemberian,
      );

      $this->load->view('Pengkajian/sedasi/sedasi/intraSedasi/premedikasiIntraSedasi',$data);
    }

    public function viewInfusIntraSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $norm = $this->input->post("norm");
      $infusSedasiObat = $this->pengkajianAwalModel->infusSedasiObat($idsed);

      $data = array(
          'idsed' => $idsed,
          'norm' => $norm,
          'nokun' => $nokun,
          'infusSedasiObat' => $infusSedasiObat,
      );

      $this->load->view('Pengkajian/sedasi/sedasi/intraSedasi/infusIntraSedasi',$data);
    }

    public function viewTandaVitalIntraSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $norm = $this->input->post("norm");

      $HistoryTandaVitalIntra = $this->pengkajianAwalModel->HistoryTandaVitalIntraSedasi($idsed);
      $HistoryInputTandaVitalIntraSedasi = $this->pengkajianAwalModel->HistoryInputTandaVitalIntraSedasi($idsed);

      $waktu = array();
      $napas = array();
      $nadi = array();
      $sistolik = array();
      $diastolik = array();
      $map = array();
      foreach ($HistoryTandaVitalIntra as $HistoryTandaVitalIntra){
        array_push($waktu,$HistoryTandaVitalIntra['pukul']);
        array_push($napas,$HistoryTandaVitalIntra['pernapasan']);
        array_push($nadi,$HistoryTandaVitalIntra['nadi']);
        array_push($sistolik,$HistoryTandaVitalIntra['td_sistolik']);
        array_push($diastolik,$HistoryTandaVitalIntra['td_diastolik']);
        array_push($map,$HistoryTandaVitalIntra['map']);
      }

      $data = array(
        'idsed' => $idsed,
        'norm' => $norm,
        'nokun' => $nokun,
        'waktu'                             => $waktu,
        'napas'                             => $napas,
        'nadi'                              => $nadi,
        'sistolik'                          => $sistolik,
        'diastolik'                         => $diastolik,
        'map'                               => $map,
        'HistoryInputTandaVitalIntraSedasi' => $HistoryInputTandaVitalIntraSedasi,
      );

      $this->load->view('Pengkajian/sedasi/sedasi/intraSedasi/tandaVitalIntraSedasi',$data);
    }

    public function viewJumlahCatatanIntraSedasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $norm = $this->input->post("norm");

      $catatanIntraSedasi = $this->pengkajianAwalModel->catatanIntraSedasi($idsed);

      $data = array(
          'idsed' => $idsed,
          'norm' => $norm,
          'nokun' => $nokun,
          'getIdCatatan' => $catatanIntraSedasi,
      );

      $this->load->view('Pengkajian/sedasi/sedasi/intraSedasi/jumlahCatatanIntraSedasi',$data);
    }

    public function simpanPremedikasi()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $oleh = $this->input->post("pengisi");
      $pilihNamaObatPremedikasiSedasiRi = $this->input->post("pilihNamaObatPremedikasiSedasiRi");
      $dosisPremedikasiSedasiRi = $this->input->post("dosisPremedikasiSedasiRi");
      $waktuPremedikasiSedasiRi = $this->input->post("waktuPremedikasiSedasiRi");
      $jalurPemObatInfusAnestesiaRi = $this->input->post("jalurPemObatInfusAnestesiaRi");
      $efekPremedikasiSedasiRi = $this->input->post("efekPremedikasiSedasiRi");
      $keteranganPremedikasiSedasiRi = $this->input->post("keteranganPremedikasiSedasiRi");
      $jenisPemberian = $this->input->post("jenisPemberian");

      $data = array(
          'id_sedasi' => $idsed,
          'nokun' => $nokun,
          'jenis' => $jenisPemberian,
          'nmobat' => $pilihNamaObatPremedikasiSedasiRi,
          'dosis' => $dosisPremedikasiSedasiRi,
          'waktu' => $waktuPremedikasiSedasiRi,
          'jalur_pemberian' => $jalurPemObatInfusAnestesiaRi,
          'efek' => $efekPremedikasiSedasiRi,
          'keterangan' => $keteranganPremedikasiSedasiRi,
          'oleh' => $oleh,
          'status' => 1,
      );
      // echo'<pre>';print_r($data);echo'</pre>';exit();
      $this->db->insert('medis.tb_sedasi_premedikasi', $data);
    }

    public function simpanInfus()
    {
      $idsed = $this->input->post("idsed");
      $nokun = $this->input->post("nokun");
      $oleh = $this->input->post("pengisi");
      $pukulInfusSedasiRi = $this->input->post("pukulInfusSedasiRi");
      $infusSedasiRi = $this->input->post("infusSedasiRi");
      $barbituratInfusSedasiRi = $this->input->post("barbituratInfusSedasiRi");
      $analGetikaInfusSedasiRi = $this->input->post("analGetikaInfusSedasiRi");
      $lainInfusSedasiRi = $this->input->post("lainInfusSedasiRi");

      $data = array(
          'id_sedasi' => $idsed,
          'nokun' => $nokun,
          'waktu' => $pukulInfusSedasiRi,
          'infus' => $infusSedasiRi,
          'barbiturat' => $barbituratInfusSedasiRi,
          'analgetika' => $analGetikaInfusSedasiRi,
          'lain_lain' => $lainInfusSedasiRi,
          'oleh' => $oleh,
          'status' => 1,
      );
      // echo'<pre>';print_r($data);echo'</pre>';exit();
      $this->db->insert('medis.tb_sedasi_infus', $data);

    }

    public function hapusInfus()
    {
      $idsedasi = $this->input->post("id");
      $data = array(
          'status' => 0,
      );
      $this->db->where('id', $idsedasi);
      $this->db->update('medis.tb_sedasi_infus', $data);
    }

    public function simpanTandaVital()
  {
    $idsed = $this->input->post('idsed');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');
    $pukulTandaVitalIntraSedasiRi = $this->input->post('pukulTandaVitalIntraSedasiRi');
    $sistolikIntraSedasiRi = $this->input->post('sistolikIntraSedasiRi');
    $diastolikIntraSedasiRi = $this->input->post('diastolikIntraSedasiRi');
    $mapIntraSedasiRi = $this->input->post('mapIntraSedasiRi');
    $nadiIntraSedasiRi = $this->input->post('nadiIntraSedasiRi');
    $pernapasanIntraSedasiRi = $this->input->post('pernapasanIntraSedasiRi');
    $oleh = $this->input->post('pengisi');

    $data = array(
      'data_source' => 39, 
      'ref' => $idsed, 
      'nomr' => $norm, 
      'nokun' => $nokun, 
      'pukul' => $pukulTandaVitalIntraSedasiRi, 
      'td_sistolik' => $sistolikIntraSedasiRi, 
      'td_diastolik' => $diastolikIntraSedasiRi, 
      'nadi' => $nadiIntraSedasiRi, 
      'pernapasan' => $pernapasanIntraSedasiRi, 
      'map' => $mapIntraSedasiRi, 
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->insert('db_pasien.tb_tanda_vital', $data);
  }

  public function simpanTandaVitalPasca()
  {
    $idsed = $this->input->post('idsed');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');
    $pukulTandaVitalPascaSedasiRi = $this->input->post('pukulTandaVitalPascaSedasiRi');
    $sistolikPascaSedasiRi = $this->input->post('sistolikPascaSedasiRi');
    $diastolikPascaSedasiRi = $this->input->post('diastolikPascaSedasiRi');
    $mapPascaSedasiRi = $this->input->post('mapPascaSedasiRi');
    $nadiPascaSedasiRi = $this->input->post('nadiPascaSedasiRi');
    $pernapasanPascaSedasiRi = $this->input->post('pernapasanPascaSedasiRi');
    $oleh = $this->input->post('pengisi');

    $data = array(
      'data_source' => 40, 
      'ref' => $idsed, 
      'nomr' => $norm, 
      'nokun' => $nokun, 
      'pukul' => $pukulTandaVitalPascaSedasiRi, 
      'td_sistolik' => $sistolikPascaSedasiRi, 
      'td_diastolik' => $diastolikPascaSedasiRi, 
      'nadi' => $nadiPascaSedasiRi, 
      'pernapasan' => $pernapasanPascaSedasiRi, 
      'map' => $mapPascaSedasiRi, 
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    $this->db->insert('db_pasien.tb_tanda_vital', $data);
  }

  public function simpanCatatanIntra()
  {
    $idsed = $this->input->post('idsed');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');
    $jumlahMedikasiIntraSedasi = $this->input->post('jumlahMedikasiIntraSedasi');
    $jumlahCairanIntraSedasi = $this->input->post('jumlahCairanIntraSedasi');
    $catatanIntraSedasi = $this->input->post('catatanIntraSedasi');
    $perdarahanCatatanIntraSedasi = $this->input->post('perdarahanCatatanIntraSedasi');
    $getIdSedasi = $this->input->post('getIdSedasi');
    $oleh = $this->input->post('pengisi');

    $data = array(
      'id_sedasi' => $idsed,  
      'nokun' => $nokun, 
      'jumlah_medikasi' => $jumlahMedikasiIntraSedasi,  
      'jumlah_cairan' => $jumlahCairanIntraSedasi,  
      'catatan' => $catatanIntraSedasi,  
      'perdarahan' => $perdarahanCatatanIntraSedasi,  
      'oleh' => $oleh, 
      'status' => 1, 
    );
    // echo "<pre>";print_r($data);echo "</pre>";exit();
    if($getIdSedasi == "")
    {
      $this->db->insert('medis.tb_sedasi_catatan', $data);
    }elseif($getIdSedasi != "")
    {
      $this->db->where('tb_sedasi_catatan.id_sedasi', $getIdSedasi);
      $this->db->update('medis.tb_sedasi_catatan', $data);
    }
  }

  public function simpanPemulihan()
  {
    $idsed = $this->input->post('idsed');
    $nokun = $this->input->post('nokun');
    $norm = $this->input->post('norm');
    $oleh = $this->input->post('pengisi');
    $getIdPemulihan = $this->input->post('getIdPemulihan');
    $post = $this->input->post();

    $pukulCatatanPascaSedasi = $this->input->post('pukulCatatanPascaSedasi');
    $penataAnestesiPengirimCatatanPascaSedasiRi = $this->input->post('penataAnestesiPengirimCatatanPascaSedasiRi');
    $penataAnestesiPenerimaCatatanPascaSedasiRi = $this->input->post('penataAnestesiPenerimaCatatanPascaSedasiRi');
    $kesadaranCatatanPascaSedasi = $this->input->post('kesadaranCatatanPascaSedasi');
    $pernapasanCatatanPascaSedasi = $this->input->post('pernapasanCatatanPascaSedasi');
    $deskPernapasanCatatanPascaSedasi = $this->input->post('deskPernapasanCatatanPascaSedasi');
    $nyeriCatatanPascaSedasi = $this->input->post('nyeriCatatanPascaSedasi');
    $risikoJatuhCatatanPascaSedasi = $this->input->post('risikoJatuhCatatanPascaSedasi');
    $penyulitIntraCatatanPascaSedasi = $this->input->post('penyulitIntraCatatanPascaSedasi');
    $instruksiKhususCatatanPascaSedasi = $this->input->post('instruksiKhususCatatanPascaSedasi');
    $informasiJamJemputPasienPasca1 = $this->input->post('informasiJamJemputPasienPasca1');
    $informasiPenerimaJemputPasienPasca1 = $this->input->post('informasiPenerimaJemputPasienPasca1');
    $informasiJamJemputPasienPasca2 = $this->input->post('informasiJamJemputPasienPasca2');
    $informasiPenerimaJemputPasienPasca2 = $this->input->post('informasiPenerimaJemputPasienPasca2');
    $informasiJamJemputPasienPasca3 = $this->input->post('informasiJamJemputPasienPasca3');
    $informasiPenerimaJemputPasienPasca3 = $this->input->post('informasiPenerimaJemputPasienPasca3');
    $pukulKeluarPascaSedasi = $this->input->post('pukulKeluarPascaSedasi');
    $ruangPemulihanPascaSedasi = $this->input->post('ruangPemulihanPascaSedasi');
    $deskRuangPemulihanPascaSedasi = $this->input->post('deskRuangPemulihanPascaSedasi');
    $saturasiAldPascaSedasi = $this->input->post('saturasiAldPascaSedasi');
    $pernapasanAldPascaSedasi = $this->input->post('pernapasanAldPascaSedasi');
    $sirkulasiAldPascaSedasi = $this->input->post('sirkulasiAldPascaSedasi');
    $motorikAldPascaSedasi = $this->input->post('motorikAldPascaSedasi');
    $kesadaranAldPascaSedasi = $this->input->post('kesadaranAldPascaSedasi');
    $hasilAldPascaSedasi = $this->input->post('hasilAldPascaSedasi');
    $pergerakanStePascaSedasi = $this->input->post('pergerakanStePascaSedasi');
    $pernapasanStePascaSedasi = $this->input->post('pernapasanStePascaSedasi');
    $kesadaranStePascaSedasi = $this->input->post('kesadaranStePascaSedasi');
    $hasilStePascaSedasi = $this->input->post('hasilStePascaSedasi');
    $gerakanBroPascaSedasi = $this->input->post('gerakanBroPascaSedasi');
    $ekstensiBroPascaSedasi = $this->input->post('ekstensiBroPascaSedasi');
    $lututBroPascaSedasi = $this->input->post('lututBroPascaSedasi');
    $pergelanganBroPascaSedasi = $this->input->post('pergelanganBroPascaSedasi');
    $hasilBroPascaSedasi = $this->input->post('hasilBroPascaSedasi');
    $scorePadssPascaSedasi = $this->input->post('scorePadssPascaSedasi');
    $cekScorePadssPascaSedasi = $this->input->post('cekScorePadssPascaSedasi');
    $nyeriPemulihanPascaSedasi = $this->input->post('nyeriPemulihanPascaSedasi');
    $risikoJatuhPemulihanPascaSedasi = $this->input->post('risikoJatuhPemulihanPascaSedasi');
    $catatanPemulihanPascaSedasi = $this->input->post('catatanPemulihanPascaSedasi');
    $pengelolaanPemulihanPascaSedasi = $this->input->post('pengelolaanPemulihanPascaSedasi');
    $penangananPemulihanPascaSedasi = $this->input->post('penangananPemulihanPascaSedasi');
    $antibiotikaPemulihanPascaSedasi = $this->input->post('antibiotikaPemulihanPascaSedasi');
    $obatObatanPemulihanPascaSedasi = $this->input->post('obatObatanPemulihanPascaSedasi');
    $infusPemulihanPascaSedasi = $this->input->post('infusPemulihanPascaSedasi');
    $dietDanNutrisiPemulihanPascaSedasi = $this->input->post('dietDanNutrisiPemulihanPascaSedasi');
    $setiapPemantauanPascaSedasi = $this->input->post('setiapPemantauanPascaSedasi');
    $selamaPemantauanPascaSedasi = $this->input->post('selamaPemantauanPascaSedasi');
    $lainLainPemantauanPascaSedasi = $this->input->post('lainLainPemantauanPascaSedasi');
    $catatanKhususPemulihanPascaSedasi = $this->input->post('catatanKhususPemulihanPascaSedasi');
    $penataAnestesiPemulihanPascaSedasiRi = $this->input->post('penataAnestesiPemulihanPascaSedasiRi');
    $dokterAnestesiPemulihanPascaSedasiRi = $this->input->post('dokterAnestesiPemulihanPascaSedasiRi');

    $data = array(
      'id_sedasi' => $idsed,  
      'nokun' => $nokun, 
      'pukul_catatan' => $pukulCatatanPascaSedasi,    
      'penata_pengirim' => $penataAnestesiPengirimCatatanPascaSedasiRi,    
      'penata_penerima' => $penataAnestesiPenerimaCatatanPascaSedasiRi,  
      'kesadaran_catatan' => $kesadaranCatatanPascaSedasi,  
      'pernapasan_catatan' => $pernapasanCatatanPascaSedasi,  
      'desk_pernapasan_catatan' => $deskPernapasanCatatanPascaSedasi,  
      'nyeri_catatan' => $nyeriCatatanPascaSedasi,  
      'risiko_jatuh_catatan' => $risikoJatuhCatatanPascaSedasi,  
      'penyulit_intra' => $penyulitIntraCatatanPascaSedasi,  
      'instruksi_khusus' => $instruksiKhususCatatanPascaSedasi,  
      'informasi_jam_1' => $informasiJamJemputPasienPasca1,    
      'penerima_jam_1' => $informasiPenerimaJemputPasienPasca1,    
      'informasi_jam_2' => $informasiJamJemputPasienPasca2,    
      'penerima_jam_2' => $informasiPenerimaJemputPasienPasca2,    
      'informasi_jam_3' => $informasiJamJemputPasienPasca3,    
      'penerima_jam_3' => $informasiPenerimaJemputPasienPasca3,    
      'pukul_keluar' => $pukulKeluarPascaSedasi,    
      'ruang_pemulihan' => $ruangPemulihanPascaSedasi,    
      'desk_ruang_pemulihan' => $deskRuangPemulihanPascaSedasi,    
      'saturasi_ald' => $saturasiAldPascaSedasi,    
      'pernapasan_ald' => $pernapasanAldPascaSedasi,    
      'sirkulasi_ald' => $sirkulasiAldPascaSedasi,    
      'motorik_ald' => $motorikAldPascaSedasi,    
      'kesadaran_ald' => $kesadaranAldPascaSedasi,    
      'hasil_ald' => $hasilAldPascaSedasi,    
      'pergerakan_ste' => $pergerakanStePascaSedasi,    
      'pernapasan_ste' => $pernapasanStePascaSedasi,    
      'kesadaran_ste' => $kesadaranStePascaSedasi,    
      'hasil_ste' => $hasilStePascaSedasi,    
      'gerakan_bro' => $gerakanBroPascaSedasi,    
      'ekstensi_bro' => $ekstensiBroPascaSedasi,    
      'lutut_bro' => $lututBroPascaSedasi,    
      'pergelangan_bro' => $pergelanganBroPascaSedasi,    
      'hasil_bro' => $hasilBroPascaSedasi,    
      'score_padss' => $scorePadssPascaSedasi,    
      'cek_score_padss' => isset($cekScorePadssPascaSedasi) ? 1 : 0 ,    
      'nyeri_pemulihan' => $nyeriPemulihanPascaSedasi,    
      'risiko_jatuh' => $risikoJatuhPemulihanPascaSedasi,    
      'catatan' => $catatanKhususPemulihanPascaSedasi,    
      'pengelolaan_pemulihan' => $pengelolaanPemulihanPascaSedasi,    
      'penanganan_pemulihan' => $penangananPemulihanPascaSedasi,    
      'antibiotika' => $antibiotikaPemulihanPascaSedasi,    
      'obat_pemulihan' => $obatObatanPemulihanPascaSedasi,    
      'infus_pemulihan' => $infusPemulihanPascaSedasi,    
      'diet_nutrisi_pemulihan' => $dietDanNutrisiPemulihanPascaSedasi,    
      'setiap_pemulihan' => $setiapPemantauanPascaSedasi,    
      'selama_pemulihan' => $selamaPemantauanPascaSedasi,    
      'lain_lain_pemulihan' => $lainLainPemantauanPascaSedasi,    
      'catatan_khusus_pemulihan' => $catatanPemulihanPascaSedasi,      
      'penata_anestesi' => $penataAnestesiPemulihanPascaSedasiRi,      
      'dokter_anestesi' => $dokterAnestesiPemulihanPascaSedasiRi,      
      'oleh' => $oleh, 
      'status' => 1, 
    );

    $dataTandaVital = array(
          'data_source' => 41,
          'ref' => $idsed,
          'nomr' => isset($post['norm']) ? $post['norm'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

    // echo "<pre>";print_r($data);echo "</pre>";exit();
    if($getIdPemulihan == "")
    {
      $this->db->insert('medis.tb_sedasi_pasca', $data);
      $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
    }elseif($getIdPemulihan != "")
    {
      $this->db->where('tb_sedasi_pasca.id', $getIdPemulihan);
      $this->db->update('medis.tb_sedasi_pasca', $data);

      $this->db->where('tb_tanda_vital.data_source', 41);
      $this->db->where('tb_tanda_vital.ref', $idsed);
      $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
    }
  }

  public function stopTandaVitalIntra()
  {
    $idsedasi = $this->input->post('id');
    $this->db->where('tb_tanda_vital.id', $idsedasi);
    $this->db->update('db_pasien.tb_tanda_vital', array('status' => 0));
  }

  public function stopTandaVitalPasca()
  {
    $idsedasi = $this->input->post('id');
    $this->db->where('tb_tanda_vital.id', $idsedasi);
    $this->db->update('db_pasien.tb_tanda_vital', array('status' => 0));
  }

    // simpan form sedasi
    public function action($param){
      if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
        if($param == 'tambah' || $param == 'ubah'){
          
          $id_status_sedasi = $this->input->post("id_status_sedasi");
          $kunjungan = $this->input->post("nokun");
          $pengguna = $this->input->post("pengguna");
          $dokteranestesi = $this->input->post("dokteranestesi");
          $penataanastesi = $this->input->post("penataanastesi");
          $dokteroperator = $this->input->post("dokteroperator");
          $diagprabedah = $this->input->post("diagprabedah");
          $jenispembedahan = $this->input->post("jenispembedahan");
          $diagpascabedah = $this->input->post("diagpascabedah");
          $goldar = $this->input->post("goldar");
          $rhesus = $this->input->post("rhesus");
          $hemoglobin = $this->input->post("hemoglobin");
          $hematokrit = $this->input->post("hematokrit");
          $riwayatpengobatan = $this->input->post("riwayatpengobatan");
          $alergi = $this->input->post("alergi");
          $komplikasianestesi = $this->input->post("komplikasianestesi");
          $merokokalkohol = $this->input->post("merokokalkohol");
          $wajah = $this->input->post("wajah"); //array
          $thoraxabdomen = $this->input->post("thoraxabdomen"); //array
          $membukamulut = $this->input->post("membukamulut");
          $jarakthyro = $this->input->post("jarakthyro");
          $mallampati = $this->input->post("mallampati");
          $obstruksinapas = $this->input->post("obstruksinapas"); //array
          $rahangkedepan = $this->input->post("rahangkedepan");
          $ekstensileher = $this->input->post("ekstensileher");
          $collar = $this->input->post("collar");
          $sistemrespirasi = $this->input->post("sistemrespirasi"); //array
          $kardiovaskular = $this->input->post("kardiovaskular"); //array
          $renal = $this->input->post("renal"); //array
          $hepato = $this->input->post("hepato"); //array
          $neuro = $this->input->post("neuro"); //array
          $lainlain = $this->input->post("lainlain"); //array
          $ekg = $this->input->post("ekg"); 
          $pulmonarystudies = $this->input->post("pulmonarystudies"); 
          $radiologi = $this->input->post("radiologi"); 
          $diagnostiklainlain = $this->input->post("diagnostiklainlain"); 
          $hbht = $this->input->post("hbht"); 
          $urinalisis = $this->input->post("urinalisis"); 
          $elektrolit = $this->input->post("elektrolit"); 
          $lablainlain = $this->input->post("lablainlain"); 
          $konversitindakan = $this->input->post("konversitindakan"); 
          $sebutkan = $this->input->post("sebutkan"); 
          $obat = $this->input->post("obat"); 
          $pemberian = $this->input->post("pemberian"); 
          $waktu = $this->input->post("waktu"); 
          $efek = $this->input->post("efek"); 
          $infus = $this->input->post("infus"); 
          $jaminfus = $this->input->post("jaminfus"); 
          $napas = $this->input->post("napas"); 
          $infusnadi = $this->input->post("infusnadi"); 
          $infustekanandarah = $this->input->post("infustekanandarah");
          $waktunapas = $this->input->post("waktunapas");
          // $jumlahnamaobat = $this->input->post("jumlahnamaobat"); 
          // $jumlahdosis = $this->input->post("jumlahdosis"); 
          // $namacairan = $this->input->post("namacairan"); 
          // $jumlahcairan = $this->input->post("jumlahcairan"); 
          // $catatan = $this->input->post("catatan"); 
          // $perdarahan = $this->input->post("perdarahan"); 


          $data = array(
            'id' => isset($id_status_sedasi) ? $id_status_sedasi : "",
            'nokun' => $this->input->post("nokun"),
            'dokter_anestesi' => $dokteranestesi,
            'penata_anestesi' => $penataanastesi,
            'dokter_operator' => $dokteroperator,
            'diag_pra_bedah' => $diagprabedah,
            'jenis_pembedahan' => $jenispembedahan,
            'diag_pasca_bedah' => $diagpascabedah,
            'golongan_darah' => $goldar,
            'rhesus' => $rhesus,
            'hemoglobin' => $hemoglobin,
            'hematokrit' => $hematokrit,
            'riwayat_pengobatan' => $riwayatpengobatan,
            'alergi' => $alergi,
            'riwayat_komplikasi' => $komplikasianestesi,
            'riwayat_merokok' => $merokokalkohol,
            'wajah' => isset($wajah) ? json_encode($wajah) : "",
            'thorax_abdomen' => isset($thoraxabdomen) ? json_encode($thoraxabdomen) : "",
            'membuka_mulut' => $membukamulut,
            'jarak_thyro' => $jarakthyro,
            'mallampati' => $mallampati,
            'obstruksi' => isset($obstruksinapas) ? json_encode($obstruksinapas) : "",
            'rahang_kedepan' => $rahangkedepan,
            'ekstensi_leher' => $ekstensileher,
            'colar' => $collar,
            'sistem_respirasi' => isset($sistemrespirasi) ? json_encode($sistemrespirasi) : "",
            'sistem_kardiovaskular' => isset($kardiovaskular) ? json_encode($kardiovaskular) : "",
            'sistem_renal' => isset($renal) ? json_encode($renal) : "",
            'hepato' => isset($hepato) ? json_encode($hepato) : "",
            'neuro' => isset($neuro) ? json_encode($neuro) : "",
            'jalan_napas_lain_lain' => isset($lainlain) ? json_encode($lainlain) : "",
            'ekg' => $ekg,
            'pulmonary' => $pulmonarystudies,
            'radiologi' => $radiologi,
            'diagnostik_lain_lain' => $diagnostiklainlain,
            'hbht' => $hbht,
            'urinalisasi' => $urinalisis,
            'elektrolit' => $elektrolit,
            'lab_lain_lain' => $lablainlain,
            'konversi_tindakan' => $konversitindakan,
            'konversi_tindakan_sebutkan' => $sebutkan,
            'infus' => $infus,
            'jam_infus' => $jaminfus,
            // 'jumlah_nama_obat' => $jumlahnamaobat,
            // 'jumlah_dosis' => $jumlahdosis,
            // 'jumlah_nama_cairan' => $namacairan,
            // 'jumlah_cairan' => $jumlahcairan,
            // 'catatan' => $catatan,
            // 'perdarahan' => $perdarahan,
            'oleh' => $pengguna,
          );

          $this->db->trans_begin();
        
          if (!empty($this->input->post("id_status_sedasi"))) {
            $this->db->where('medis.tb_sedasi.id', $this->input->post("id_status_sedasi"));
            $this->db->update('medis.tb_sedasi', $data);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          }else{
            $this->db->insert('medis.tb_sedasi', $data);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }
        }
      }
    }

}
?>