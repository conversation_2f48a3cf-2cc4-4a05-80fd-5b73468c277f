<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');

        $this->load->model(
            array(
                'rekam_medis/rawat_inap/igd/DashboardModel',
                'rekam_medis/TbBbModel',
                'pengkajianAwalModel',
                'MalnutrisiModel',
                'DashboardEMR',
            )
        );
    }

    public function index()
    {
        $nokun = $this->uri->segment(2);
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);
        $nopen = $pasien['NOPEN'];
        $data = array(
            'nokun' => $nokun,
            'nopen' => $nopen,
            'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
            'tanggal' => $this->DashboardModel->tglHasilLab($nopen),
        );
        // echo '<pre>';print_r($data);exit();
        $this->load->view('rekam_medis/rawat_inap/igd/dashboard/index', $data);
    }

    public function lab()
    {
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $post = $this->input->post();
            $nopen = $post['nopen'] ?? null;
            $no_order = $post['no_order'] ?? null;
            $tabel = null;
            $ambil_pemeriksaan = null;
            $ambil_hasil = null;

            // Mulai tabel lab
            if ($no_order != null) {
                $tabel = $this->DashboardModel->tabelLab($nopen, $no_order);
                $ambil_pemeriksaan = $this->DashboardModel->ambilPemeriksaanLab($nopen, $no_order);
                $ambil_hasil = $this->DashboardModel->ambilHasilLab($nopen, $no_order);
            } else {
                $tabel = $this->DashboardModel->tabelLab($nopen, null);
                $ambil_pemeriksaan = $this->DashboardModel->ambilPemeriksaanLab($nopen, null);
                $ambil_hasil = $this->DashboardModel->ambilHasilLab($nopen, null);
            }
            // Akhir tabel lab

            $data = array(
                'tabelLab' => $tabel,
                'ambilPemeriksaanLab' => $ambil_pemeriksaan,
                'ambilHasilLab' => $ambil_hasil,
            );
        }

        // echo '<pre>';print_r($data);exit();
        $this->load->view('rekam_medis/rawat_inap/igd/dashboard/lab', $data);
    }
}

/* End of file Dashboard.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/Dashboard.php */