<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CpptTemplate extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set('Asia/Jakarta');

    $this->load->model(
      array(
        'rekam_medis/rawat_inap/catatanTerintegrasi/CpptTemplateModel',
      )
    );
    $this->load->library('whatsapp');
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $rules = $this->CpptTemplateModel->rules;
        $this->form_validation->set_rules($rules);

        if ($this->form_validation->run() == TRUE) {
          $post = $this->input->post();
          $this->db->trans_begin();

          $dataCppt = array(
            'deskripsi' => $this->input->post('deskripsi'),
            'subyektif' => $this->input->post('subyektif'),
            'obyektif' => $this->input->post('obyektif'),
            'analisis' => $this->input->post('analisis'),
            'perencanaan' => $this->input->post('perencanaan'),
            'instruksi' => $this->input->post('instruksi'),
            'oleh' => $this->session->userdata("id")
          );
          if(!empty($post['id'])){
            $this->CpptTemplateModel->update($dataCppt, array('id' => $post['id']));
          }else{
            $this->CpptTemplateModel->insert($dataCppt);
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }  else if ($param == 'getTemplate') {
        $data = $this->CpptTemplateModel->get_table(FALSE);

        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data
          )
        );
      } else if ($param == 'ambil') {
        $data = $this->CpptTemplateModel->get_table();

        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data
          )
        );
      }
    }
  }
}
