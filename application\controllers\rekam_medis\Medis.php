<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Medis extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    // if (!in_array(44, $this->session->userdata('akses'))) {
    //   redirect('login');
    // }
    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/MedisModel',
        'konsultasi/KonsultasiModel',
        'BgsiModel',
      )
    );
    $this->load->model('Farmasi/StockartModel', 'stockartmodel');
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $getKonsul = isset($getNomr['NOPEN']) ? $this->pengkajianAwalModel->getDataUdahBlmKonsul($getNomr['NOPEN']) : null;
    $pilihTandaVitalMon = isset($getNomr['NORM']) ? $this->MedisModel->pilihTandaVitalMon($getNomr['NORM']) : null;

    $historyTandaVitalMon = $this->pengkajianAwalModel->historyTandaVitalMon($nokun);
    $historyDetailTandaVitalMon = $this->pengkajianAwalModel->historyDetailTandaVitalMon($nokun);

    $waktu = [];
    $napas = [];
    $nadi = [];
    $sistolik = [];
    $diastolik = [];
    $map = [];
    $suhu = [];
    $id_pengguna = $this->session->userdata('id');
    $infoNyeri = $this->pengkajianAwalModel->infoNyeri($nokun);
    $pemantauanNyeri = null;
    $getNoBPJS = isset($getNomr['NORM']) ? $this->pengkajianAwalModel->getNoBPJS($getNomr['NORM']) : null;
    $getKodeDokter = $this->pengkajianAwalModel->getKodeDokter($id_pengguna);
    $cekNokunHasilLabPk = $this->MedisModel->cekNokunHasilLabPk($getNomr['NORM'])->row_array();

    foreach ($historyTandaVitalMon as $htvm) {
      array_push($waktu, $htvm['JAM']);
      array_push($napas, $htvm['pernapasan']);
      array_push($nadi, $htvm['nadi']);
      array_push($sistolik, $htvm['td_sistolik']);
      array_push($diastolik, $htvm['td_diastolik']);
      array_push($map, $htvm['map']);
      array_push($suhu, $htvm['suhu']);
    }

    $data = [
      'title' => 'Rekam Medik Pasien',
      'isi' => 'rekam_medis/index',
      'getNomr' => $getNomr,
      'pasien' => $getNomr,
      // 'infoCovid' => $this->pengkajianAwalModel->infoCovid($getNomr['NORM'], date('Y-m-d H:i:s')),
      'getNoBPJS' => $getNoBPJS,
      'getKodeDokter' => $getKodeDokter,
      'pasienKonsul' => $getKonsul,
      'pilihTandaVitalMon' => $pilihTandaVitalMon,
      'nokun' => $nokun,
      'waktu' => $waktu,
      'napas' => $napas,
      'nadi' => $nadi,
      'sistolik' => $sistolik,
      'diastolik' => $diastolik,
      'map' => $map,
      'suhu' => $suhu,
      'historyDetailTandaVitalMon' => $historyDetailTandaVitalMon,
      'jumlahNotif' => isset($getNomr['NORM']) ? $this->KonsultasiModel->jumlahNotifPerPasien($id_pengguna, $getNomr['NORM']) : null,
      'infoPermintaanDarah' => isset($getNomr['NORM']) ? $this->pengkajianAwalModel->infoPermintaanDarah($getNomr['NORM']) : null,
      'infoResikoJatuh' => $this->pengkajianAwalModel->infoResikoJatuh((isset($getNomr['NOPEN']) ? $getNomr['NOPEN'] : 0)),
      'pemantauanNyeri' => $pemantauanNyeri,
      'infoEWS' => $this->pengkajianAwalModel->infoEWS($nokun),

      // Penelitian BGSi
      'statusBgsi' => isset($getNomr['NORM']) ? $this->BgsiModel->statusBgsi($getNomr['NORM']) : null,
      'infoconsenBgsi' => isset($getNomr['NORM']) ? $this->BgsiModel->infoconsenBgsi($getNomr['NORM']) : null,

      // Info Paliatif
      'infoPaliatif' => isset($getNomr['NORM']) ? $this->pengkajianAwalModel->infoPaliatif($getNomr['NORM']) : null,
      // Info HIV
      'infoHIV' => isset($getNomr['NORM']) ? $this->pengkajianAwalModel->infoHIV($getNomr['NORM'])->row_array() : null,
      'dataHIV' => isset($getNomr['NORM']) ? $this->pengkajianAwalModel->infoHIV($getNomr['NORM'])->row_array() : null,
      // Info DNR
      'dataDNR' => isset($getNomr['NORM']) ? $this->pengkajianAwalModel->infoDNR($getNomr['NORM'])->row_array() : null,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'kontrolKembali' => $this->masterModel->referensi(1850),
      'cekNokunHasilLabPk' => $cekNokunHasilLabPk,
      'cekHasilLabPk' => $this->MedisModel->cekHasilLabPk($cekNokunHasilLabPk['NOKUN'])->row_array(),
      'jmlHasiLabKritis' => $this->MedisModel->cekHasilLabPk($cekNokunHasilLabPk['NOKUN'])->num_rows(),
      'detailKritis' => $this->MedisModel->detail_kunjungan_pk_kritis($cekNokunHasilLabPk['NOKUN']),
      'tanggalKritis' => $this->MedisModel->tanggal_sampling_kritis($cekNokunHasilLabPk['NOKUN']),
      'ceklogKritis' => $this->MedisModel->log_kritis($cekNokunHasilLabPk['NOKUN'])->num_rows(),
      'logKritis' => $this->MedisModel->log_kritis($cekNokunHasilLabPk['NOKUN'])->row_array(),
      
      // Stockart room check
      'isStockartRoom' => isset($getNomr['ID_RUANGAN']) ? $this->stockartmodel->isRoomRegistered($getNomr['ID_RUANGAN']) : false,
    ];

    // Mulai pemantauan nyeri
    if ($infoNyeri) {
      $nyeriAkhir = json_decode($infoNyeri['NYERI_AKHIR'], true);
      $waktuNyeri = date('d/m/Y, H.i.s', strtotime($nyeriAkhir['waktu']));
      // echo '<pre>';print_r($waktuPantauNyeriLagi);exit();

      if ($nyeriAkhir['metode'] == 'WongBaker') { // Nyeri anak
        if ($nyeriAkhir['skor'] == 0) {
          $pemantauanNyeri = ' Tidak nyeri.';
        } elseif ($nyeriAkhir['skor'] >= 1 && $nyeriAkhir['skor'] <= 3) {
          $pemantauanNyeri = ' Nyeri ringan.';
        } elseif ($nyeriAkhir['skor'] >= 4 && $nyeriAkhir['skor'] <= 6) {
          $pemantauanNyeri = ' Nyeri sedang.';
        } elseif ($nyeriAkhir['skor'] >= 7) {
          $pemantauanNyeri = ' Nyeri berat.';
        }
      } else { // Nyeri dewasa
        if ($nyeriAkhir['skor'] == 0) {
          $pemantauanNyeri = ' Tidak nyeri.';
          $waktuPantauNyeriLagi = null;
        } elseif ($nyeriAkhir['skor'] >= 1 && $nyeriAkhir['skor'] <= 3) {
          $pemantauanNyeri = ' Nyeri ringan. Dipantau setiap <strong>shift atau 8 jam</strong> dari pemantauan sebelumnya';
          $waktuPantauNyeriLagi = ' pada <strong>' . date('d/m/Y, H.i.s', strtotime($nyeriAkhir['waktu']) + 8 * 3600) . '</strong>';
        } elseif ($nyeriAkhir['skor'] >= 4 && $nyeriAkhir['skor'] <= 6) {
          $pemantauanNyeri = ' Nyeri sedang. Dipantau setiap <strong>2-3 jam</strong> dari pemantauan sebelumnya';
          $waktuPantauNyeriLagi = ' pada <strong>' . date('d/m/Y, H.i.s', strtotime($nyeriAkhir['waktu']) + 2 * 3600) . '</strong>';
        } elseif ($nyeriAkhir['skor'] >= 7) {
          $pemantauanNyeri = ' Nyeri berat. Dipantau setiap <strong>1 jam</strong> dari pemantauan sebelumnya';
          $waktuPantauNyeriLagi = ' pada <strong>' . date('d/m/Y, H.i.s', strtotime($nyeriAkhir['waktu']) + 3600) . '</strong>';
        }
      }

      $data['nyeriAkhir'] = $nyeriAkhir;
      $data['pemantauanNyeri'] = $pemantauanNyeri;
      $data['waktuNyeri'] = $waktuNyeri;
      $data['waktuPantauNyeriLagi'] = $waktuPantauNyeriLagi ?? null;
    }
    // Akhir pemantuan nyeri

    if (isset($getNomr['NOPEN'])) {
      $data['infoPemberianAntiBiotik'] = $this->pengkajianAwalModel->infoPemberianAntiBiotik($getNomr['NOPEN']);
      $data['tarifPasien'] = $this->pengkajianAwalModel->tarifPasien($getNomr['NOPEN']);
      // $data['infoResikoJatuh'] = $this->pengkajianAwalModel->infoResikoJatuh($getNomr['NOPEN']);
    }

    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
  }

  public function labPkSideBarRi()
  {
    $nokun = $this->uri->segment(4);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'getNomr' => $getNomr
    );
    $this->load->view('Pengkajian/hasilLabPK', $data);
  }

  public function labPaSideBarRi()
  {
    $nokun = $this->uri->segment(4);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'getNomr' => $getNomr
    );
    $this->load->view('Pengkajian/hasilLabPa', $data);
  }

  public function labPtSideBarRi()
  {
    $nokun = $this->uri->segment(4);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'getNomr' => $getNomr
    );
    $this->load->view('Pengkajian/hasilLabPt', $data);
  }

  public function radiologiSideBarRi()
  {
    $nokun = $this->uri->segment(4);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'getNomr' => $getNomr
    );
    $this->load->view('Pengkajian/hasilRadiologi', $data);
  }

  public function dashboard()
  {
    $statusPengguna = $_SESSION['status'];
    $id_pengguna = $this->session->userdata('id');
    $user = $statusPengguna == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    $totalPasienRi = $this->MedisModel->total_pasienRi($user);
    // echo "<pre>";print_r($statusPengguna);exit();
    $data = array(
      'title' => 'Halaman Dashboard Rawat Inap',
      'isi' => 'PengkajianRi/dashboard',
      'statusPengguna' => $statusPengguna,
      'id_pengguna' => $id_pengguna,
      'totalPasienRi' => $totalPasienRi,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function listPasienRi()
  {
    $id_ruangan = $this->uri->segment(3);

    $data = array(
      'title' => 'Halaman List Pasien Rawat Inap',
      'isi' => 'PengkajianRi/listPasienRi',
      'id_ruangan' => $id_ruangan,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function tandaVitalMon()
  {
    $nokun = $this->input->post('id');
    // $tanggal = $this->input->post('tanggal');
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);

    $historyTandaVitalMon = $this->pengkajianAwalModel->historyTandaVitalMon($nokun);
    $historyDetailTandaVitalMon = $this->pengkajianAwalModel->historyDetailTandaVitalMon($nokun);

    $waktu = array();
    $napas = array();
    $nadi = array();
    $sistolik = array();
    $diastolik = array();
    $map = array();
    $suhu = array();
    $saturasi = array();
    foreach ($historyTandaVitalMon as $historyTandaVitalMon) {
      array_push($waktu, $historyTandaVitalMon['JAM']);
      array_push($napas, $historyTandaVitalMon['pernapasan']);
      array_push($nadi, $historyTandaVitalMon['nadi']);
      array_push($sistolik, $historyTandaVitalMon['td_sistolik']);
      array_push($diastolik, $historyTandaVitalMon['td_diastolik']);
      array_push($map, $historyTandaVitalMon['map']);
      array_push($suhu, $historyTandaVitalMon['suhu']);
      array_push($saturasi, $historyTandaVitalMon['SATURASI']);
    }

    $data = array(
      'waktu' => $waktu,
      'napas' => $napas,
      'nadi' => $nadi,
      'sistolik' => $sistolik,
      'diastolik' => $diastolik,
      'map' => $map,
      'suhu' => $suhu,
      'saturasi' => $saturasi,
      'historyDetailTandaVitalMon' => $historyDetailTandaVitalMon,
    );

    $this->load->view('rekam_medis/tandaVital', $data);
  }

  public function ListPasienRawatInap()
  {
    $id_ruangan = $this->uri->segment(3);
    $status = $this->input->post('status');
    $user = $this->session->userdata('status') == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    $list_pasienRi = $this->MedisModel->datatables_lp_ruangan($id_ruangan, $status, $user);
    $data = array();
    $no = 1;

    if ($status == 1) {
      foreach ($list_pasienRi as $lpr) {
        $data[] = array(
          '<a href="' . base_url('PengkajianRawatInap/') . $lpr->NOKUN . '" class="btn btn-info btn-sm btn-block"><i class="fa fa-check"></i> Pilih</a>',
          $lpr->NORM,
          $lpr->NAMA_PASIEN,
          $lpr->JK,
          $lpr->TUJUAN_RAWAT,
          $lpr->RUANGAN_TUJUAN,
          $lpr->KAMAR,
          $lpr->DOKTER_TUJUAN,
          $lpr->JENIS_KUNJUNGAN,
          $lpr->TGLMASUK,
          $lpr->DIAGNOSA_MASUK,
          $lpr->LAMADIRAWAT,
        );
        $no++;
      }
    } else {
      foreach ($list_pasienRi as $lpr) {
        $data[] = array(
          '<a href="#" class="btn btn-info btn-sm btn-block"><i class="fa fa-check"></i> Pilih</a>',
          $lpr->NORM,
          $lpr->NAMA_PASIEN,
          $lpr->JK,
          $lpr->AGAMA,
          $lpr->RUANGAN_TUJUAN,
          $lpr->KAMAR,
          $lpr->DOKTER_TUJUAN,
          $lpr->LAMADIRAWAT,
          $lpr->TANGGAL_KELUAR,
          $lpr->CARA_KELUAR,
          $lpr->KEADAAN_KELUAR,
          $lpr->DIAGNOSA_AKHIR,
        );
        $no++;
      }
    }

    $output = array(
      "draw" => intval($this->input->post("draw")),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function listPasien()
  {
    $id_ruangan = $this->input->post('ruangan');
    $status = $this->input->post('status');
    $user = $this->session->userdata('status') == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    // $limit = 5;
    // $offset = ceil(1 * $limit);
    $norm = $this->input->post('norm');
    $dataPasien = $this->MedisModel->datatables_lp_ruangan($id_ruangan, $status, $user, $norm);

    echo json_encode(
      array(
        'status' => 'success',
        'data' => $dataPasien,
        'total' => count($dataPasien)
      )
    );
  }

  public function ruangan()
  {
    $statusPengguna = $_SESSION['status'];
    $user = $statusPengguna == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    $listPasien = $this->MedisModel->total_pasienRi($user);

    $data = array();

    foreach ($listPasien as $list) {
      $sub_array = array();
      $sub_array['ID'] = $list['ID'];
      $sub_array['DESKRIPSI'] = $list['DESKRIPSI'];
      $sub_array['JUMLAH_PASIEN'] = $list['JUMLAH_PASIEN'];

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function kunjungan_pk()
  {
    $kunjungan = $this->MedisModel->kunjungan_pk();

    $data = array();

    foreach ($kunjungan as $dataKunjungan) {
      $sub_array = array();
      $sub_array['ID'] = $dataKunjungan->NOKUN;
      $sub_array['DESKRIPSI'] = $dataKunjungan->TGLMASUK;
      // $sub_array['JUMLAH_PASIEN'] = $dataKunjungan['JUMLAH_PASIEN'];

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }
  public function kunjungan_pk_baru()
  {
    $kunjungan = $this->MedisModel->kunjungan_pk_baru();

    $data = array();

    foreach ($kunjungan as $dataKunjungan) {
      $sub_array = array();
      $sub_array['ID'] = $dataKunjungan->NOKUN;
      $sub_array['DESKRIPSI'] = $dataKunjungan->TGLMASUK;
      $sub_array['TINDAKAN'] = $dataKunjungan->TINDAKAN;
      // $sub_array['JUMLAH_PASIEN'] = $dataKunjungan['JUMLAH_PASIEN'];

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function detail_kunjungan_pk()
  {
    $data = array(
      'detail' => $this->MedisModel->detail_kunjungan_pk(),
      'tanggal' => $this->MedisModel->tanggal_sampling(),
    );

    echo json_encode($data);
  }

  public function pemeriksaan_radiologi()
  {
    $kunjungan = $this->MedisModel->pemeriksaan_radiologi();

    $data = array();

    foreach ($kunjungan as $dataKunjungan) {
      $sub_array = array();
      $sub_array['ID'] = $dataKunjungan->KUNJUNGAN;
      $sub_array['DESKRIPSI'] = $dataKunjungan->TANGGAL_KUNJUNGAN;
      $sub_array['TINDAKAN'] = $dataKunjungan->TINDAKAN;

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function pemeriksaan_pa_gabung()
  {
    $kunjungan = $this->MedisModel->pemeriksaan_pa_gabung();

    $data = array();

    foreach ($kunjungan as $dataKunjungan) {
      $sub_array = array();
      $sub_array['ID'] = $dataKunjungan->NO_LAB;
      $sub_array['DESKRIPSI'] = $dataKunjungan->TGL_TERIMA;
      $sub_array['TINDAKAN'] = $dataKunjungan->JENIS_PEMERIKSAAN;
      $sub_array['STATUS_PA'] = $dataKunjungan->STATUS_PA;

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function pemeriksaan_pt_gabung()
  {
    $patmol = $this->MedisModel->pemeriksaan_pt_gabung();

    $data = array();

    foreach ($patmol as $dataPatmol) {
      $sub_array = array();
      $sub_array['ID'] = $dataPatmol->NOMOR_PATMOL;
      $sub_array['TANGGAL'] = $dataPatmol->TANGGAL_PATMOL_FORMAT;
      $sub_array['TINDAKAN'] = $dataPatmol->DESK_JENIS_PEMERIKSAAN;
      $sub_array['ID_TINDAKAN'] = $dataPatmol->JENIS_PEMERIKSAAN;
      $sub_array['STATUS_PT'] = $dataPatmol->STATUS;

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function pemeriksaan_radiologi_gabung()
  {
    $norm = $this->input->post('norm');
    $kunjungan = $this->MedisModel->pemeriksaan_radiologi_gabung($norm);

    $data = array();

    foreach ($kunjungan as $dataKunjungan) {
      $sub_array = array();
      $sub_array['ID'] = $dataKunjungan->ID;
      $sub_array['DESKRIPSI'] = $dataKunjungan->TGL_PEMERIKSAAN;
      $sub_array['TINDAKAN'] = $dataKunjungan->TINDAKAN;
      $sub_array['TINDAKAN_MEDIS'] = $dataKunjungan->TINDAKAN_MEDIS;
      $sub_array['HASIL'] = $dataKunjungan->HASIL;

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function tindakan_radiologi()
  {
    $tindakan = $this->MedisModel->tindakan_radiologi();

    $data = array();

    foreach ($tindakan as $dataTindakan) {
      $sub_array = array();
      $sub_array['ID'] = $dataTindakan->IDTM;
      $sub_array['DESKRIPSI'] = $dataTindakan->NMTINDAKAN;

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function expertise()
  {
    $data = $this->MedisModel->expertise();
    $id = $this->input->post('id');
    $norm = $this->input->post('norm');
    $Dtindakan = $this->pengkajianAwalModel->Dtindakan($norm, $id);
    if ($Dtindakan != null) {
      $data->STUDY_ID_LINK = $Dtindakan['study_id_link'];
    }


    echo json_encode($data);
  }

  public function pemeriksaan_patologi_anatomi()
  {
    $data = null;
    if ($this->input->post('jenis') == 1) {
      $data = $this->MedisModel->sitologi();
    } else if ($this->input->post('jenis') == 2) {
      $data = $this->MedisModel->histologi();
    } else if ($this->input->post('jenis') == 3) {
      $data = $this->MedisModel->imuno();
    }
    echo json_encode($data);
  }

  public function hasil_pemeriksaan_patologi_anatomi()
  {
    $data = null;
    if ($this->input->post('jenis') == 1) {
      $data = $this->MedisModel->hasil_sitologi();
    } else if ($this->input->post('jenis') == 2) {
      $data = $this->MedisModel->hasil_histo();
    } else if ($this->input->post('jenis') == 3) {
      $data = $this->MedisModel->hasil_imunohistokimia();
    }
    echo json_encode($data);
  }

  public function hasil_pemeriksaan_patmol()
  {
    $data = null;
    $data = $this->MedisModel->hasil_patmol();
    echo json_encode($data);
  }

  public function getMedis()
  {
    $medis = $this->MedisModel->medis();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $medis
      )
    );
  }

  public function getAsuhanKeperawatanCppt()
  {
    $result = $this->MedisModel->asuhanKeperawatanCpptRI();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function getAsuhanKeperawatanPengkajian()
  {
    $result = $this->MedisModel->asuhanKeperawatanPengkajianRI();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function historyObservasiKeperawatan()
  {
    $result = $this->MedisModel->obeservasi_keperawatan();

    $data = array();
    foreach ($result as $row) {
      $sub_array = array();
      $sub_array[] = '<input type="checkbox" name="ckasuhan" class="ckasuhan" value="' . $row->id_perencanaan_asuhan_keperawatan . '" data-parent=' . $row->id_parent . '>';
      $sub_array[] = $row->RUANGAN;
      $sub_array[] = $row->tanggal;
      $sub_array[] = $row->jam;
      $sub_array[] = $row->perawat;
      $sub_array[] = $row->pak;

      $data[] = $sub_array;
    }

    $output = array(
      "data" => $data
    );
    echo json_encode($output);
  }

  public function pengkajianDashboard()
  {
    $result = $this->MedisModel->pengkajianDashboard();

    $data = array();
    foreach ($result as $row) {
      $sub_array = array();
      // $sub_array[] = '<a class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasaMedis" data-id="'.$row -> ID_EMR.'"><i class="fa fa-eye"></i> Lihat</a>';
      $sub_array[] = $row->DESK_JENIS_PENGKAJIAN;
      $sub_array[] = $row->RUANGAN;
      $sub_array[] = $row->TANGGAL;
      $sub_array[] = $row->PROFESI;
      $sub_array[] = $row->OLEH;

      $data[] = $sub_array;
    }

    $output = array(
      // "draw"              => intval($_POST["draw"]),
      // "recordsTotal"      => $this->MedisDewasaModel->total_count(),
      // "recordsFiltered"   => $this->MedisDewasaModel->filter_count(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function ewsDashboard()
  {
    $result = $this->MedisModel->ewsDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function pewsDashboard()
  {
    $result = $this->MedisModel->pewsDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function observasiKeperawatanDashboard()
  {
    $result = $this->MedisModel->observasiKeperawatanDashboard();
    $data = array();
    foreach ($result as $row) {
      $sub_array = array();
      // $sub_array[] = '<a class="btn btn-primary btn-block btn-sm descPemberianCairan" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Keterangan</a>';
      $sub_array[] = $row->DINAS;
      $sub_array[] = $row->OLEH;
      $sub_array[] = $row->TANGGAL;
      $sub_array[] = $row->balance;
      $sub_array[] = $row->iwl;
      $sub_array[] = $row->pak;
      $sub_array[] = $row->TINDAKAN_LAINNYA;

      $data[] = $sub_array;
    }

    $output = array(
      // "draw"              => intval($_POST["draw"]),
      // "recordsTotal"      => $this->MedisDewasaModel->total_count(),
      // "recordsFiltered"   => $this->MedisDewasaModel->filter_count(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function pemberianCairanIntravenaDashboard()
  {
    $result = $this->MedisModel->pemberianCairanIntravenaDashboard();
    $data = array();
    foreach ($result as $row) {
      $sub_array = array();
      $sub_array[] = '<a class="btn btn-primary btn-block btn-sm descPemberianCairan" data-id="' . $row->id . '"><i class="fa fa-eye"></i> Keterangan</a>';
      $sub_array[] = $row->TANGGAL;
      $sub_array[] = $row->dimulai;
      $sub_array[] = $row->nama_perawat1;
      $sub_array[] = $row->nama_perawat2;
      $sub_array[] = $row->isicairan;
      $sub_array[] = $row->keterangan;

      $data[] = $sub_array;
    }

    $output = array(
      // "draw"              => intval($_POST["draw"]),
      // "recordsTotal"      => $this->MedisDewasaModel->total_count(),
      // "recordsFiltered"   => $this->MedisDewasaModel->filter_count(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function resikoJatuhDashboard()
  {
    $result = $this->MedisModel->resikoJatuhDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function keluhanUtamaDashboard()
  {
    $result = $this->MedisModel->keluhanUtamaDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function instruksiDokterDashboard()
  {
    $result = $this->MedisModel->instruksiDokterDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function terapiPasienDashboard()
  {
    $result = $this->MedisModel->terapiPasienDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function saturasiOksigenDashboard()
  {
    $result = $this->MedisModel->saturasiOksigenDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function tbakDashboard()
  {
    $result = $this->MedisModel->tbakDashboard();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function ambilRiwayatPenyakitSekarang()
  {
    $post = $this->input->post();
    $nokun = !empty($post['nokun']) ? $post['nokun'] : null;
    $id = !empty($post['id']) ? $post['id'] : null;
    $result = $this->pengkajianAwalModel->ambilRiwayatPenyakitSekarang($nokun, $id);
    // echo '<pre>';print_r($data);exit();
    echo json_encode(
      array(
        'status' => 'success',
        'data' => $result
      )
    );
  }

  public function listPasienRJV2()
  {
    $id = $this->session->userdata('id');

    $query = $this->db->query("CALL keperawatan.ListPasienVer2_RJ_2($id)");

    $data = array();
    foreach ($query->result() as $lpri) {

      if ($lpri->STS_PANGGIL == 1) {
        $statusWarnaPanggil = 'text-danger';
      } elseif ($lpri->STS_PANGGIL == 2) {
        $statusWarnaPanggil = 'text-success';
      } elseif ($lpri->STS_PANGGIL == 3) {
        $statusWarnaPanggil = 'text-warning';
      } else {
        $statusWarnaPanggil = '';
      }
      $statusPanggil = "<span class='" . $statusWarnaPanggil . "'>" . $lpri->STS_PANGGIL_DESK . "</span>";

      if ($lpri->STS_PANGGIL == 2 || $lpri->STS_PANGGIL_DESK == '-') {
        $disabledTombolPanggil = 'disabled';
        $disabledWarnaPanggil = 'badge badge-secondary';
      } else {
        $disabledTombolPanggil = '';
        $disabledWarnaPanggil = 'badge badge-warning';
      }

      $tombolPanggil = "<div class='text-center'><button type='button' class='" . $disabledWarnaPanggil . " tombolPanggilListPasien' data-nmpasien='" . $lpri->NAMA_PASIEN . "' data-idjenis='" . $lpri->id_jenis_antrian . "' data-nokun='" . $lpri->NOKUN . "' data-token='" . $lpri->TOKEN . "' data-nomor='" . $lpri->nomor . "' data-iddokter='" . $lpri->ID_DOKTER . "' data-id='" . $lpri->id_antrian . "' data-norm='" . $lpri->NORM . "' data-tglpgl='" . $lpri->dipanggil_at . "' " . $disabledTombolPanggil . " data-idruangan='" . $lpri->ID_RUANGAN . "'><span style='color: black;'>Panggil</span></button></div>";

      $data[] = array(
        $lpri->NOKONTROLDOKTER,
        $lpri->TOKEN,
        $lpri->RUANGAN_TUJUAN,
        $lpri->NORM,
        $lpri->NAMA_PASIEN,
        // $lpri->JK,
        $lpri->RENCANA,
        $statusPanggil,
        $tombolPanggil,
        $lpri->STATUS_FORM_KONSUL,
        $lpri->NOPEN,
        $lpri->NOKUN,
      );
    }

    $output = array(
      "draw" => intval($this->input->post("draw")),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function listPasienRIV2()
  {
    $id = $this->session->userdata('id');

    $query = $this->db->query("CALL keperawatan.ListPasienVer2_RI($id)");

    $data = array();
    foreach ($query->result() as $lpri) {
      $data[] = array(
        $lpri->KAMAR,
        $lpri->NAMA_PASIEN,
        $lpri->NORM,
        $lpri->JENIS_KUNJUNGAN,
        $lpri->TUJUAN_RAWAT,
        $lpri->NOKUN,
      );
    }

    $output = array(
      "draw" => intval($this->input->post("draw")),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function notifGizi()
  {
    $status = $this->input->post('status');
    $nomr = $this->input->post('nomr');
    $nokun = $this->input->post('nokun');
    $data = array(
      'nokun' => $nokun,
      'nomr' => $nomr,
      'status_alasan' => $status,
      'oleh' => $this->session->userdata("id"),
      'status' => 1,
    );
    $this->db->insert('medis.tb_notif_gizi', $data);
  }

  public function simpanPenandaHIV()
  {
    $this->db->trans_begin();

    $userHIV = $this->session->userdata('id');
    $nomrHIV = $this->input->post('normHIV');
    $keteranganHIV = $this->input->post('keteranganTandaHIV');
    $dataHIV = array(
      'DOKTER' => $userHIV,
      'NOMR' => $nomrHIV,
      'KETERANGAN' => $keteranganHIV,
      'STATUS' => 1,
    );
    // echo "<pre>";print_r($dataHIV);echo "</pre>";exit();
    $this->db->insert('master.penanda_hiv', $dataHIV);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }

    echo json_encode($result);
  }
}

/* End of file Medis.php */
/* Location: ./application/controllers/rekam_medis/Medis.php */