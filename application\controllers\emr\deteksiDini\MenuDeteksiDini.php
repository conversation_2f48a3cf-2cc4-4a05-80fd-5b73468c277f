<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MenuDeteksiDini extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/TbBbModel',
        'emr/deteksiDini/UKDDModel',
      ]
    );
  }

  public function index()
  {
    $data = [
      'title' => 'Tabel Pasien Deteksi Dini',
      'isi' => 'Pengkajian/emr/deteksiDini/MenuDeteksiDini/index',
    ];
    $this->load->view('layout/wrapper', $data);
  }

  public function tabel()
  {
    $post = $this->input->post();
    $tanggalMulai = isset($post['tanggal_mulai']) ? $post['tanggal_mulai'] : null;
    $tanggalSelesai = isset($post['tanggal_selesai']) ? $post['tanggal_selesai'] : null;
    $mulai = strtotime($tanggalMulai);
    $selesai = strtotime($tanggalSelesai);
    $data = [
      'tanggalMulai' => date('d-m-Y', strtotime($tanggalMulai)),
      'tanggalSelesai' => date('d-m-Y', strtotime($tanggalSelesai)),
      'mulai' => date('Y-m-d H:i:s', $mulai),
      'selesai' => date('Y-m-d H:i:s', $selesai),
    ];
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/emr/deteksiDini/MenuDeteksiDini/tabel', $data);
  }

  public function isiTabel()
  {
    $draw = intval($this->input->POST('draw'));
    $post = $this->input->post();
    $idRuang = '105130101';
    $mulai = isset($post['mulai']) ? $post['mulai'] : null;
    $selesai = isset($post['selesai']) ? $post['selesai'] : null;
    $tabel = $this->UKDDModel->tabelMenuDeteksiDini($mulai, $selesai);
    // echo '<pre>';print_r($tabel->result());exit();

    if ($tabel->result() != null) {
      // Mulai jika data tersedia
      foreach ($tabel->result() as $t) {
        $pengkajian = ($t->USIA == 2 or ($t->USIA == 1 && in_array($idRuang, ['105140101', '105120101', '105020101', '105020901', '105100101']))) && $_SESSION['status'] == 2 && !in_array($idRuang, ['105130101', '105020401', '105130103']) ? 'pengkajianKeperawatanDewasa' : ($t->USIA == 1 && $_SESSION['status'] == 2 && !in_array($idRuang, ['105020401']) ? 'pengkajianKeperawatanAnak' : (($t->USIA == 2 or ($t->USIA == 1 && in_array($idRuang, ['105140101', '105120101', '105020101', '105020901', '105100101']))) && $_SESSION['status'] == 1 && !in_array($idRuang, ['105130101', '105020401', '105130103']) ? 'pengkajianMedisDewasa' : ($t->USIA == 1 && $_SESSION['status'] == 1 && !in_array($idRuang, ['105020401']) ? 'pengkajianMedisAnak' : ($idRuang == 105020401 && $_SESSION['status'] == 2 ? 'paliatifKeperawatan' : ($idRuang == 105020401 && $_SESSION['status'] == 1 ? 'paliatifMedis' : '')))));
        $idDokter = $t->ID_EMR_MEDIS;
        $idPerawat = $t->ID_EMR;

        $action = null;
        if ($_SESSION['status'] == 1) {
          if ($t->STATUS_ISI_EMR_MEDIS == 1 /*&& $t->STATUS_ISI_EMR == 1*/) {
            $action = "<a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/ews' class='btn btn-custom btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='Pengkajian'><i class='fa fa-check'></i></a>";
          } else {
            $action = "<a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/" . $pengkajian . "/" . $idDokter . "' class='btn btn-custom btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='Edit Pengkajian'><i class='fas fa-pencil-alt'></i></a> <a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/cppt/" . $idDokter . "' class='btn btn-warning btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='CPPT'><i class='fa fa-book'></i></a> <a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/ews/" . $idDokter . "' class='btn btn-primary btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='EWS'><i class='fa fa-file'></i></a>";
          }
        } elseif ($_SESSION['status'] == 2) {
          if ($t->STATUS_ISI_EMR == 1 /*&& $t->STATUS_ISI_EMR_MEDIS == 1*/) {
            $action = "<a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/ews' class='btn btn-custom btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='Pengkajian'><i class='fa fa-check'></i></a>";
          } else {
            $action = "<a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/" . $pengkajian . "/" . $idPerawat . "' class='btn btn-custom btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='Edit Pengkajian'><i class='fas fa-pencil-alt'></i></a> <a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/cppt/" . $idPerawat . "' class='btn btn-warning btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='CPPT'><i class='fa fa-book'></i></a> <a href='" . base_url("pengkajianAwal/index/") . $t->NORM . "/" . $t->NOPEN . "/" . $t->NOKUN . "/ews/" . $idPerawat . "' class='btn btn-primary btn-block btn-sm' data-toggle='tooltip' data-placement='right' title='EWS'><i class='fa fa-file'></i></a>";
          }
        }

        if (!empty($t->UMUR_PENGKAJIAN_KEPERAWATAN)) {
          $upk = $t->UMUR_PENGKAJIAN_KEPERAWATAN;
        } else {
          $upk = "<h6 class='text-center align-middle'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
        }

        if (!empty($t->UMUR_PENGKAJIAN_MEDIS)) {
          $upm = $t->UMUR_PENGKAJIAN_MEDIS;
        } else {
          $upm = "<h6 class='text-center align-middle'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
        }

        if (($t->STATUS_ISI_EMR == 2 && $this->session->userdata('status') == 2) || ($t->STATUS_ISI_EMR_MEDIS == 2 && $this->session->userdata('status') == 1)) {
          $kajiUlang = "<a href='#' class='btn btn-sm btn-custom btn-block kajiUlang' data-toggle='tooltip' data-placement='right' title='Kaji Ulang' norm='" . $t->NORM . "' nopen='" . $t->NOPEN . "' nokun='" . $t->NOKUN . "'><i class='fa fa-plus'></i></a>";
        } else {
          $kajiUlang = "<h6 class='text-center align-middle'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
        }

        $statusCPPTPerawat = "<h6 class='text-center align-middle'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
        if ($t->CPPT_PERAWAT != null) {
          $statusCPPTPerawat = "<h4 class='text-center align-middle'><i class='fa fa-check' aria-hidden='true'></i></h4>";
        }

        $statusCPPTDokter = "<h6 class='text-center align-middle'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
        if ($t->CPPT_DOKTER != null) {
          $statusCPPTDokter = "<h4 class='text-center align-middle'><i class='fa fa-check' aria-hidden='true'></i></h4>";
        }

        $statusSummaryList = "<h6 class='text-center align-middle'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
        if ($t->SUMMARY_LIST != null) {
          $statusSummaryList = "<h4 class='text-center align-middle'><i class='fa fa-check' aria-hidden='true'></i></h4>";
        }

        $warnaLabel = $t->LABEL;
        if ($warnaLabel == 'Hijau') {
          $hasilPemeriksaanCovid = "<span class='text-success'>" . $t->HASIL_PEMERIKSAAN . "</span>";
        } elseif ($warnaLabel == 'Merah') {
          $hasilPemeriksaanCovid = "<span class='text-danger'>" . $t->HASIL_PEMERIKSAAN . "</span>";
        } else {
          $hasilPemeriksaanCovid = $t->HASIL_PEMERIKSAAN;
        }

        $skorDDPayudara = null;
        if (isset($t->SKOR_DD_PAYUDARA)) {
          $skorDDPayudara = $t->SKOR_DD_PAYUDARA;
        } else {
          $skorDDPayudara = '-';
        }

        $data[] = [
          $action,
          $t->NORM,
          "<a href='#data-pasien-dd' data-toggle='modal' data-id='" . $t->NOKUN . "' data-backdrop='static' data-keyboard='false'>" . $t->NAMA_PASIEN . "</a>",
          $t->JK,
          $t->RUANGAN_TUJUAN,
          $t->DOKTER_TUJUAN,
          date('d-m-Y H:i:s', strtotime($t->TANGGAL_DAFTAR)),
          $upk,
          $upm,
          $statusCPPTPerawat,
          $statusCPPTDokter,
          $hasilPemeriksaanCovid,
          $skorDDPayudara,
          $kajiUlang,
          $t->JENIS_KUNJUNGAN,
          $statusSummaryList,
        ];
      }

      $output = [
        'draw' => $draw,
        'recordsTotal' => $tabel->num_rows(),
        'recordsFiltered' => $tabel->num_rows(),
        'data' => $data
      ];
      // echo '<pre>';print_r($data);exit();
      // Akhir jika data tersedia
    } else {
      // Mulai jika data tidak tersedia
      $output = [
        'draw' => $draw,
        'data' => ''
      ];
      // Akhir jika data tidak tersedia
    }

    echo json_encode($output);
  }
}
