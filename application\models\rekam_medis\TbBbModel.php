<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TbBbModel extends MY_Model
{
    protected $_table_name = 'db_pasien.tb_tb_bb';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'nokun',
                'label' => 'Nomor kunjungan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s Wajib Diisi',
                    'numeric' => '%s Wajib Angka',
                ]
            ],
            [
                'field' => 'nomr',
                'label' => 'Nomor rekam medis',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s Wajib Diisi',
                    'numeric' => '%s Wajib Angka',
                ]
            ],
            [
                'field' => 'tb',
                'label' => 'Tinggi badan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s Wajib <PERSON>',
                    'numeric' => '%s Wajib <PERSON>',
                ]
            ],
            [
                'field' => 'bb',
                'label' => 'Berat badan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s Wajib Diisi',
                    'numeric' => '%s Wajib Angka',
                ]
            ]
        ];
    }

    public function ubah($id, $data)
    {
        $this->db->where('db_pasien.tb_tb_bb.id', $id);
        $this->db->update('db_pasien.tb_tb_bb', $data);
    }

    public function ubahRef($data, $id)
    {
        $this->db->where('db_pasien.tb_tb_bb.ref', $id);
        $this->db->update('db_pasien.tb_tb_bb', $data);
    }

    function table_query()
    {
        $this->db->select(
            't.id, t.data_source, t.ref, t.id_otk, t.nokun, t.nomr, t.jenis, t.tb, t.bb, t.lpb, t.oleh, t.status, t.created_at, ds.deskripsi, master.getNamaLengkapPegawai(ap.NIP) oleh_desc'
        );
        $this->db->from('db_pasien.tb_tb_bb t');
        $this->db->join('db_master.tb_data_source ds', 't.data_source = ds.id', 'left');
        $this->db->join('aplikasi.pengguna ap', 't.oleh = ap.ID', 'left');
        $this->db->order_by('t.created_at', 'DESC');
        if ($this->input->post('nomr')) {
            $this->db->where('t.nomr', $this->input->post('nomr'));
            $this->db->limit('1');
        }

        if ($this->input->post('nokun')) {
            $this->db->where('t.nokun', $this->input->post('nokun'));
            $this->db->limit('1');
        }

        if ($this->input->post('id')) {
            $this->db->where('t.id', $this->input->post('id'));
        }
    }

    function get_table($single = true)
    {
        $this->db->where('t.status', 1);
        $this->table_query();
        $query = $this->db->get();
        if ($single == true) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}

// End of file TbBbModel.php
// Location: ./application/models/rekam_medis/TbBbModel.php