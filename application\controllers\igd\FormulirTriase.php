<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormulirTriase extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'formulirTriaseModel', 'EresepModel'));
  }

  public function atsTriase()
  {
    $id = $this->input->post("id");

    $atsTriase = $this->masterModel->referensi($id);

    foreach ($atsTriase as $atsTriase) {
      echo '<div class="checkbox checkbox-primary">';
      echo '<input type="checkbox" name="atsTriase[]" id="atsTriase'.$atsTriase['id_variabel'].'" value="'.$atsTriase['id_variabel'].'">';
      echo '<label for="atsTriase'.$atsTriase['id_variabel'].'">'.$atsTriase['variabel'].'</label>';
      echo '</div>';
    }
  }

  public function simpanTriase()
  {
     $post = $this->input->post();
     $nomr = $this->input->post('nomr');

     $pernapasanEws = $this->input->post('pernapasanTriase');
     $nadiEws = $this->input->post('nadiTriase');
     $tekananDarahSistolikEws = $this->input->post('tekanan_darah_1Triase');
     $suhuEws = $this->input->post('suhuTriase');
     $saturasiO2Ews = $this->input->post('saturasiTriase');
     $penggunaanO2Ews = $this->input->post('penggunaanO2');
     $kesadaranEws = $this->input->post('kesadaran');
     $masukEws = $this->input->post('masukKeEwsTriaseRi');

     // KONDISI UNTUK PERNAPASAN EWS
     if ($pernapasanEws >= 25) {
      $hasilPernapasan = 3;
    } elseif ($pernapasanEws >= 21 && $pernapasanEws <= 24.99) {
      $hasilPernapasan = 2;
    } elseif ($pernapasanEws >= 12 && $pernapasanEws <= 20.99) {
      $hasilPernapasan = 0;
    } elseif ($pernapasanEws >= 9 && $pernapasanEws <= 11.99) {
      $hasilPernapasan = 1;
    } elseif ($pernapasanEws <= 8.99) {
      $hasilPernapasan = 3;
    }

    // KONDISI UNTUK DENYUT NADI EWS
    if ($nadiEws >= 130) {
      $nadiEws = 3;
    } elseif ($nadiEws >= 111 && $nadiEws <= 129.99) {
      $nadiEws = 2;
    } elseif ($nadiEws >= 101 && $nadiEws <= 110.99) {
      $nadiEws = 1;
    } elseif ($nadiEws >= 60 && $nadiEws <= 100.99) {
      $nadiEws = 0;
    } elseif ($nadiEws >= 51 && $nadiEws <= 59.99) {
      $nadiEws = 1;
    } elseif ($nadiEws >= 40 && $nadiEws <= 50.99) {
      $nadiEws = 2;
    } elseif ($nadiEws <= 39.99) {
      $nadiEws = 3;
    }

    // KONDISI UNTUK TEKANAN DARAH SISTOLIK
    if ($tekananDarahSistolikEws >= 180) {
      $tekananDarahSistolikEws = 3;
    } elseif ($tekananDarahSistolikEws >= 170 && $tekananDarahSistolikEws <= 179.99) {
      $tekananDarahSistolikEws = 2;
    } elseif ($tekananDarahSistolikEws >= 150 && $tekananDarahSistolikEws <= 169.99) {
      $tekananDarahSistolikEws = 1;
    } elseif ($tekananDarahSistolikEws >= 101 && $tekananDarahSistolikEws <= 149.99) {
      $tekananDarahSistolikEws = 0;
    } elseif ($tekananDarahSistolikEws >= 81 && $tekananDarahSistolikEws <= 100.99) {
      $tekananDarahSistolikEws = 1;
    } elseif ($tekananDarahSistolikEws >= 71 && $tekananDarahSistolikEws <= 80.99) {
      $tekananDarahSistolikEws = 2;
    } elseif ($tekananDarahSistolikEws <= 70.99) {
      $tekananDarahSistolikEws = 3;
    }

    // KONDISI UNTUK SUHU
    if ($suhuEws >= 39) {
      $suhuEws = 2;
    } elseif ($suhuEws >= 38 && $suhuEws <= 38.99) {
      $suhuEws = 1;
    } elseif ($suhuEws >= 36 && $suhuEws <= 37.99) {
      $suhuEws = 0;
    } elseif ($suhuEws <= 35.99) {
      $suhuEws = 3;
    }

    // KONDISI UNTUK SATURASI O2
    if ($saturasiO2Ews >= 96) {
      $saturasiO2Ews = 0;
    } elseif ($saturasiO2Ews >= 94 && $saturasiO2Ews <= 95.99) {
      $saturasiO2Ews = 1;
    } elseif ($saturasiO2Ews >= 92 && $saturasiO2Ews <= 93.99) {
      $saturasiO2Ews = 2;
    } elseif ($saturasiO2Ews <= 91.99) {
      $saturasiO2Ews = 3;
    }

    // KONDISI UNTUK PENGGUNAAN O2
    if ($penggunaanO2Ews == 388) {
      $penggunaanO2Ews = 2;
    } elseif ($penggunaanO2Ews == 389) {
      $penggunaanO2Ews = 0;
    }

    // KONDISI UNTUK TINGKAT KESADARAN
    if ($kesadaranEws == 105) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 12) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 11) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 10) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 9) {
      $kesadaranEws = 0;
    }

        $totalScoreEws = $hasilPernapasan + $nadiEws + $tekananDarahSistolikEws + $suhuEws + $saturasiO2Ews + $penggunaanO2Ews + $kesadaranEws;

     $dataTriase = array(
      'nokun'             => $post['nokun'],
      'tanggal_masuk'     => $post['tanggalMasuk'],
      'jam'               => $post['jamMasuk'],
      'jenis_kunjungan'   => $post['jenisKunjungan'],
      'rujukan_dari'      => isset($post['deskRujukanDariTriase']) ? $post['deskRujukanDariTriase'] : "",
      'apakah_masuk_ews'      => isset($post['masukKeEwsTriaseRi']) ? $post['masukKeEwsTriaseRi'] : "",
      'oleh'              => $this->session->userdata('id'),
    );
     // echo "<pre>";print_r($dataTriase);echo "</pre>";
    $getIdTriase = $this->formulirTriaseModel->simpanTriase($dataTriase);

    $dataO2 = array(
      'data_source'       => 9,
      'ref'               => $getIdTriase,
      'nomr'              => $post['nomr'],
      'nokun'             => $post['nokun'],
      'saturasi_o2'       => $post['saturasiTriase'],
      'penggunaan_o2'     => $post['penggunaanO2'],
      'oleh'              => $this->session->userdata('id'),
    );

     // echo "<pre>";print_r($dataO2);echo "</pre>";
    $getIdO2 = $this->formulirTriaseModel->simpano2($dataO2);

     $dataTandaVital = array(
      'data_source'       => 9,
      'ref'               => $getIdTriase,
      'nomr'              => $post['nomr'],
      'nokun'             => $post['nokun'],
      'td_sistolik'       => $post['tekanan_darah_1Triase'],
      'td_diastolik'      => $post['tekanan_darah_2Triase'],
      'nadi'              => $post['nadiTriase'],
      'pernapasan'        => $post['pernapasanTriase'],
      'suhu'              => $post['suhuTriase'],
      'oleh'              => $this->session->userdata('id'),
    );

     // echo "<pre>";print_r($dataTandaVital);exit();
     $getIdTandaVital = $this->formulirTriaseModel->simpanTandaVital($dataTandaVital);

     $dataKesadaran = array(
      'data_source' => 9,
      'ref' => $getIdTriase,
      'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
      'nokun' => $post['nokun'],
      'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
    );

     $getIdKesadaran = $this->formulirTriaseModel->simpanKesadaran($dataKesadaran);

     $dataTotalScoreEws = array(
      'id_tanda_vital' => $getIdTandaVital,
      'id_kesadaran' => $getIdKesadaran,
      'id_o2' => $getIdO2,
      'data_source' => 9,
      'tanggal' => date("Y-m-d"),
      'jam' => date("H:i:s"),
      'score_ews' => $totalScoreEws,
      'ref' => $getIdTriase,
      'nokun' => $post['nokun'],
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
    );

     if ($masukEws == 3863){
      $this->db->insert('keperawatan.tb_ews', $dataTotalScoreEws);
    }

    $atsTriase = array();
    $indexAtsTriase = 0;
    if (isset($post['atsTriase'])) {
      foreach ($post['atsTriase'] as $input) {
        if ($post['atsTriase'][$indexAtsTriase] != "") {
          array_push(
            $atsTriase, array(
              'id_triase'   => $getIdTriase,
              'ats_detail' => $post['atsTriase'][$indexAtsTriase],
            )
          );
        }
        $indexAtsTriase++;
      }
      $this->db->insert_batch('keperawatan.tb_triase_ats', $atsTriase);
    }

    $dataIdTandaVitalO2 = array(
      'id_tanda_vital' => $getIdTandaVital,
      'id_o2' => $getIdO2
    );
    $this->db->where('id', $getIdTriase);
    $this->db->update('keperawatan.tb_triase', $dataIdTandaVitalO2);
  }

  public function tblHistoryTriase()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listTriase = $this->formulirTriaseModel->listTriase($nomr);

    $data = array();
    $no = 1;
    foreach ($listTriase->result() as $historyTs) {
      $data[] = array(
        $no,
        $historyTs->NOKUN,
        $historyTs->NORM,
        $historyTs->NAMA_PASIEN,
        $historyTs->USER,
        date("d-m-Y",strtotime($historyTs->TANGGAL)),
        '<a href="#modalHistoryTs" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$historyTs->id.'"><i class="fas fa-edit"></i> Edit</a>'
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listTriase->num_rows(),
      "recordsFiltered" => $listTriase->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function detailHistoryTs()
  {
    $id = $this->input->post('id');
    $dHistoryTs = $this->formulirTriaseModel->detailHistoryTs($id);
    //IGD Triase
    $PENGGUNAAN_O2 = $this->masterModel->referensi(129);
    $atsTriase1 = $this->masterModel->referensi(479);
    $atsTriase2 = $this->masterModel->referensi(480);
    $atsTriase3 = $this->masterModel->referensi(481);
    $atsTriase4 = $this->masterModel->referensi(482);
    $atsTriase5 = $this->masterModel->referensi(483);
    $jenisKunjunganTriase = $this->masterModel->referensi(485);
    $triaseIgd = $this->masterModel->referensi(486);

    // echo "<pre>";print_r($dHistoryTs);exit();
    $data = array(
      'id'                   => $id,
      'dHistoryTs'           => $dHistoryTs,
      'PENGGUNAAN_O2'        => $PENGGUNAAN_O2,
      'atsTriase1'           => $atsTriase1,
      'atsTriase2'           => $atsTriase2,
      'atsTriase3'           => $atsTriase3,
      'atsTriase4'           => $atsTriase4,
      'atsTriase5'           => $atsTriase5,
      'jenisKunjunganTriase' => $jenisKunjunganTriase,
      'triaseIgd'            => $triaseIgd,
      'apakahInginMasukEws' => $this->masterModel->referensi(1162),
      'kesadaran' => $this->masterModel->referensi(5),
    );

    $this->load->view('Pengkajian/igd/formulirTriase/edit', $data);
  }

  public function atsTriaseEdit()
  {
    $id = $this->input->post("atsE");
    $ceklistAts = $this->input->post("ceklistAts");
    $atsTriase = $this->masterModel->referensi($id);
    // echo $ceklistAts;

    foreach ($atsTriase as $atst) {
      // $cek = in_array($atst["id_variabel"], ['1632','1633','1640','1644','1645']) ? "checked": "";
      $cek = in_array($atst["id_variabel"], $ceklistAts) ? "checked": "";

      echo '<div class="checkbox checkbox-primary">';
      echo '<input type="checkbox" name="atsTriase[]" id="atsTriaseEdit'.$atst['id_variabel'].'" value="'.$atst['id_variabel'].'" '.$cek.' >';
      echo '<label for="atsTriaseEdit'.$atst['id_variabel'].'">'.$atst['variabel'].'</label>';
      echo '</div>';
    }
  }

  public function updateTriase()
  {
    $post     = $this->input->post();
    $idTriase = $post['idTriase'];

    $dataO2 = array(
      'data_source'       => 9,
      'ref'               => $idTriase,
      // 'nomr'              => $post['nomr'],
      // 'nokun'             => $post['nokun'],
      'saturasi_o2'       => $post['saturasiTriaseEdit'],
      'penggunaan_o2'     => $post['penggunaanO2Edit'],
      'oleh'              => $this->session->userdata('id'),
    );

    $this->db->where('tb_o2.data_source', 9);
    $this->db->where('tb_o2.ref', $idTriase);
    $this->db->update('db_pasien.tb_o2', $dataO2);

    $dataTandaVital = array(
      'data_source'       => 9,
      'ref'               => $idTriase,
      // 'nomr'              => $post['nomr'],
      // 'nokun'             => $post['nokun'],
      'td_sistolik'       => $post['tekanan_darah_1TriaseEdit'],
      'td_diastolik'      => $post['tekanan_darah_2TriaseEdit'],
      'nadi'              => $post['nadiTriaseEdit'],
      'pernapasan'        => $post['pernapasanTriaseEdit'],
      'suhu'              => $post['suhuTriaseEdit'],
      'oleh'              => $this->session->userdata('id'),
    );

    $this->db->where('tb_tanda_vital.data_source', 9);
    $this->db->where('tb_tanda_vital.ref', $idTriase);
    $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);

    $dataKesadaran = array(
      'data_source' => 9,
      'ref' => $idTriase,
      // 'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
      // 'nokun' => $post['nokun'],
      'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
    );

      $this->db->where('tb_kesadaran.data_source', 9);
      $this->db->where('tb_kesadaran.ref', $idTriase);
      $this->db->update('db_pasien.tb_kesadaran', $dataKesadaran);



    $pernapasanEws = $this->input->post('pernapasanTriaseEdit');
     $nadiEws = $this->input->post('nadiTriaseEdit');
     $tekananDarahSistolikEws = $this->input->post('tekanan_darah_1TriaseEdit');
     $suhuEws = $this->input->post('suhuTriaseEdit');
     $saturasiO2Ews = $this->input->post('saturasiTriaseEdit');
     $penggunaanO2Ews = $this->input->post('penggunaanO2Edit');
     $kesadaranEws = $this->input->post('kesadaran');
     $masukEws = $this->input->post('masukKeEwsTriaseRiEdit');

     // KONDISI UNTUK PERNAPASAN EWS
     if ($pernapasanEws >= 25) {
      $hasilPernapasan = 3;
    } elseif ($pernapasanEws >= 21 && $pernapasanEws <= 24.99) {
      $hasilPernapasan = 2;
    } elseif ($pernapasanEws >= 12 && $pernapasanEws <= 20.99) {
      $hasilPernapasan = 0;
    } elseif ($pernapasanEws >= 9 && $pernapasanEws <= 11.99) {
      $hasilPernapasan = 1;
    } elseif ($pernapasanEws <= 8.99) {
      $hasilPernapasan = 3;
    }

    // KONDISI UNTUK DENYUT NADI EWS
    if ($nadiEws >= 130) {
      $nadiEws = 3;
    } elseif ($nadiEws >= 111 && $nadiEws <= 129.99) {
      $nadiEws = 2;
    } elseif ($nadiEws >= 101 && $nadiEws <= 110.99) {
      $nadiEws = 1;
    } elseif ($nadiEws >= 60 && $nadiEws <= 100.99) {
      $nadiEws = 0;
    } elseif ($nadiEws >= 51 && $nadiEws <= 59.99) {
      $nadiEws = 1;
    } elseif ($nadiEws >= 40 && $nadiEws <= 50.99) {
      $nadiEws = 2;
    } elseif ($nadiEws <= 39.99) {
      $nadiEws = 3;
    }

    // KONDISI UNTUK TEKANAN DARAH SISTOLIK
    if ($tekananDarahSistolikEws >= 180) {
      $tekananDarahSistolikEws = 3;
    } elseif ($tekananDarahSistolikEws >= 170 && $tekananDarahSistolikEws <= 179.99) {
      $tekananDarahSistolikEws = 2;
    } elseif ($tekananDarahSistolikEws >= 150 && $tekananDarahSistolikEws <= 169.99) {
      $tekananDarahSistolikEws = 1;
    } elseif ($tekananDarahSistolikEws >= 101 && $tekananDarahSistolikEws <= 149.99) {
      $tekananDarahSistolikEws = 0;
    } elseif ($tekananDarahSistolikEws >= 81 && $tekananDarahSistolikEws <= 100.99) {
      $tekananDarahSistolikEws = 1;
    } elseif ($tekananDarahSistolikEws >= 71 && $tekananDarahSistolikEws <= 80.99) {
      $tekananDarahSistolikEws = 2;
    } elseif ($tekananDarahSistolikEws <= 70.99) {
      $tekananDarahSistolikEws = 3;
    }

    // KONDISI UNTUK SUHU
    if ($suhuEws >= 39) {
      $suhuEws = 2;
    } elseif ($suhuEws >= 38 && $suhuEws <= 38.99) {
      $suhuEws = 1;
    } elseif ($suhuEws >= 36 && $suhuEws <= 37.99) {
      $suhuEws = 0;
    } elseif ($suhuEws <= 35.99) {
      $suhuEws = 3;
    }

    // KONDISI UNTUK SATURASI O2
    if ($saturasiO2Ews >= 96) {
      $saturasiO2Ews = 0;
    } elseif ($saturasiO2Ews >= 94 && $saturasiO2Ews <= 95.99) {
      $saturasiO2Ews = 1;
    } elseif ($saturasiO2Ews >= 92 && $saturasiO2Ews <= 93.99) {
      $saturasiO2Ews = 2;
    } elseif ($saturasiO2Ews <= 91.99) {
      $saturasiO2Ews = 3;
    }

    // KONDISI UNTUK PENGGUNAAN O2
    if ($penggunaanO2Ews == 388) {
      $penggunaanO2Ews = 2;
    } elseif ($penggunaanO2Ews == 389) {
      $penggunaanO2Ews = 0;
    }

    // KONDISI UNTUK TINGKAT KESADARAN
    if ($kesadaranEws == 105) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 12) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 11) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 10) {
      $kesadaranEws = 3;
    } elseif ($kesadaranEws == 9) {
      $kesadaranEws = 0;
    }

        $totalScoreEws = $hasilPernapasan + $nadiEws + $tekananDarahSistolikEws + $suhuEws + $saturasiO2Ews + $penggunaanO2Ews + $kesadaranEws;

    $dataTotalScoreEws = array(
      // 'id_tanda_vital' => $getIdTandaVital,
      // 'id_kesadaran' => $getIdKesadaran,
      // 'id_o2' => $getIdO2,
      'data_source' => 9,
      'tanggal' => date("Y-m-d"),
      'jam' => date("H:i:s"),
      'score_ews' => $totalScoreEws,
      'ref' => $idTriase,
      // 'nokun' => $post['nokun'],
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
    );

    $this->db->where('tb_ews.data_source', 9);
    $this->db->where('tb_ews.ref', $idTriase);
    $this->db->update('db_pasien.tb_ews', $dataTotalScoreEws);

    $dataTriase = array(
      'tanggal_masuk'     => $post['tanggalMasukEdit'],
      'jam'               => $post['jamMasukEdit'],
      'jenis_kunjungan'   => $post['jenisKunjunganTriaseEdit'],
      'rujukan_dari'      => isset($post['deskRujukanDariTriaseEdit']) ? $post['deskRujukanDariTriaseEdit'] : "",
      'tekanan_darah'     => $post['tekanan_darah_1TriaseEdit'],
      'per_tekanan_darah' => $post['tekanan_darah_2TriaseEdit'],
      'pernapasan'        => $post['pernapasanTriaseEdit'],
      'nadi'              => $post['nadiTriaseEdit'],
      'suhu'              => $post['suhuTriaseEdit'],
      'saturasiTriase'    => $post['saturasiTriaseEdit'],
      'penggunaanO2'      => $post['penggunaanO2Edit'],
      'apakah_masuk_ews'      => isset($post['masukKeEwsTriaseRiEdit']) ? $post['masukKeEwsTriaseRiEdit'] : "",
      'oleh'              => $this->session->userdata('id'),
    );

    $this->formulirTriaseModel->updateTriase($dataTriase,$idTriase);

    $this->db->delete('keperawatan.tb_triase_ats', array('id_triase' => $idTriase));

    $atsTriase = array();
    $indexAtsTriase = 0;
    if (isset($post['atsTriase'])) {
      foreach ($post['atsTriase'] as $input) {
        if ($post['atsTriase'][$indexAtsTriase] != "") {
          array_push(
            $atsTriase, array(
              'id_triase'   => $idTriase,
              'ats_detail' => $post['atsTriase'][$indexAtsTriase],
            )
          );
        }
        $indexAtsTriase++;
      }
      $this->db->insert_batch('keperawatan.tb_triase_ats', $atsTriase);
    }
    // echo "<pre>data ";print_r($atsTriase);echo "</pre>";
  }


}

/* End of file FormulirTriase.php */
/* Location: ./application/controllers/igd/FormulirTriase.php */
