<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengkajianAferesisModel extends MY_Model{
	protected $_table_name = 'keperawatan.tb_pengkajian_aferesis';
	protected $_primary_key = 'kunjungan';
	protected $_order_by = 'kunjungan';
	protected $_order_by_type = 'DESC';

	public $rules = array(
		'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
		),

		'jenis_pasien_aferesis' => array(
            'field' => 'jenis_pasien_aferesis',
            'label' => 'Jenis Pasien',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),
        
        'e_gcs' => array(
            'field' => 'e_gcs',
            'label' => 'GCS E',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'm_gcs' => array(
            'field' => 'm_gcs',
            'label' => 'GCS M',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'v_gcs' => array(
            'field' => 'v_gcs',
            'label' => 'GCS V',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'kesadaran_aferesis' => array(
            'field' => 'kesadaran_aferesis',
            'label' => 'Kesadaran',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),
		
		'sistolik' => array(
            'field' => 'sistolik',
            'label' => 'Sistolik',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'distolik' => array(
            'field' => 'distolik',
            'label' => 'Distolik',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'pernapasan' => array(
            'field' => 'pernapasan',
            'label' => 'Pernapasan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'nadi' => array(
            'field' => 'nadi',
            'label' => 'Nadi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'suhu' => array(
            'field' => 'suhu',
            'label' => 'Suhu',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'tb' => array(
            'field' => 'tb',
            'label' => 'Tinggi Badan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'bb' => array(
            'field' => 'bb',
            'label' => 'Berat Badan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'akses_veskuler' => array(
            'field' => 'akses_veskuler',
            'label' => 'Akses Veskuler',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'jenis_akses[]' => array(
            'field' => 'jenis_akses[]',
            'label' => 'Jenis Akses',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'pola_napas' => array(
            'field' => 'pola_napas',
            'label' => 'Pola Napas',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'suara_napas' => array(
            'field' => 'suara_napas',
            'label' => 'Suara Napas',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'nadi_aferesis' => array(
            'field' => 'nadi_aferesis',
            'label' => 'Sirkulasi Nadi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'ctr' => array(
            'field' => 'ctr',
            'label' => 'CTR',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'akral' => array(
            'field' => 'akral',
            'label' => 'Akral',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'skrining_nyeri' => array(
            'field' => 'skrining_nyeri',
            'label' => 'Skrining Nyeri',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'skrining_resiko_jatuh_pusing_aferesis' => array(
            'field' => 'skrining_resiko_jatuh_pusing_aferesis',
            'label' => 'keluhan pusing/vertigo',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'skrining_resiko_jatuh_berdiri_aferesis' => array(
            'field' => 'skrining_resiko_jatuh_berdiri_aferesis',
            'label' => 'Kesulitan saat berdiri/berjalan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'skrining_resiko_jatuh_6bulan_aferesis' => array(
            'field' => 'skrining_resiko_jatuh_6bulan_aferesis',
            'label' => 'Mengalami jatuh dalam 6 bulan terakhir',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'psikososial[]' => array(
            'field' => 'psikososial[]',
            'label' => 'Jenis Luka',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'edukasi[]' => array(
            'field' => 'edukasi[]',
            'label' => 'Edukasi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'masalah_keperawatan[]' => array(
            'field' => 'masalah_keperawatan[]',
            'label' => 'Masalah Keperawatan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'hb' => array(
            'field' => 'hb',
            'label' => 'Hb',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'leko' => array(
            'field' => 'leko',
            'label' => 'Leko',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'tr' => array(
            'field' => 'tr',
            'label' => 'Tr',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'eri' => array(
            'field' => 'eri',
            'label' => 'Eri',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'ht' => array(
            'field' => 'ht',
            'label' => 'Ht',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $pendonor = array(
        'golongan_darah' => array(
            'field' => 'golongan_darah',
            'label' => 'Golongan Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'rh_darah' => array(
            'field' => 'rh_darah',
            'label' => 'RH Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'riwayat_alergi_aferesis' => array(
            'field' => 'riwayat_alergi_aferesis',
            'label' => 'Riwayat Alergi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'riwayat_penyakit_aferesis' => array(
            'field' => 'riwayat_penyakit_aferesis',
            'label' => 'Riwayat Penyakit',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'riwayat_donor' => array(
            'field' => 'riwayat_donor',
            'label' => 'Riwayat Donor Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'riwayat_aferesis' => array(
            'field' => 'riwayat_aferesis',
            'label' => 'Riwayat Aferesis',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),        
    );
    
    public $pasien = array(
		'keluhan_utama' => array(
            'field' => 'keluhan_utama',
            'label' => 'Keluhan Utama',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'riwayat_penyakit_dahulu' => array(
            'field' => 'riwayat_penyakit_dahulu',
            'label' => 'Riwayat Penyakit Dahulu',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'riwayat_penyakit_saat_ini' => array(
            'field' => 'riwayat_penyakit_saat_ini',
            'label' => 'Riwayat Penyakit Saat Ini',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'golongan_darah_pasien' => array(
            'field' => 'golongan_darah_pasien',
            'label' => 'Golongan Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'rh_darah_pasien' => array(
            'field' => 'rh_darah_pasien',
            'label' => 'RH Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'riwayat_alergi_aferesis_pasien' => array(
            'field' => 'riwayat_alergi_aferesis_pasien',
            'label' => 'Riwayat Alergi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'obat_kemoterapi' => array(
            'field' => 'obat_kemoterapi',
            'label' => 'Obat Kemoterapi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        'hbsag' => array(
            'field' => 'hbsag',
            'label' => 'HbsAg',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'antihcv' => array(
            'field' => 'antihcv',
            'label' => 'Anti-HCV',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'antihiv' => array(
            'field' => 'antihiv',
            'label' => 'Anti-HIV',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'vdrl' => array(
            'field' => 'vdrl',
            'label' => 'VDRL',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'cd34' => array(
            'field' => 'cd34',
            'label' => 'CD 34+',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'ureum' => array(
            'field' => 'ureum',
            'label' => 'Ureum',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'creatinin' => array(
            'field' => 'creatinin',
            'label' => 'Creatinin',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'prototal' => array(
            'field' => 'prototal',
            'label' => 'Pro.total',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'albumin' => array(
            'field' => 'albumin',
            'label' => 'Albumin',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'globulin' => array(
            'field' => 'globulin',
            'label' => 'Globulin',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'sgot' => array(
            'field' => 'sgot',
            'label' => 'SGOT',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'sgpt' => array(
            'field' => 'sgpt',
            'label' => 'SGPT',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'na' => array(
            'field' => 'na',
            'label' => 'Na',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'k' => array(
            'field' => 'k',
            'label' => 'K',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'ch' => array(
            'field' => 'ch',
            'label' => 'Ch',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'mg' => array(
            'field' => 'mg',
            'label' => 'Mg',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'ca' => array(
            'field' => 'ca',
            'label' => 'Ca',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $riwayat_alergi_pendonor = array(
        'alergi_aferesis_desc' => array(
            'field' => 'alergi_aferesis_desc',
            'label' => 'Jelaskan Riwayat Alergi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
    );

    public $riwayat_alergi_pasien = array(
        'alergi_aferesis_pasien_desc' => array(
            'field' => 'alergi_aferesis_pasien_desc',
            'label' => 'Jelaskan Riwayat Alergi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
    );

    public $riwayat_penyakit_pendonor = array(
        'alergi_penyakit_desc' => array(
            'field' => 'alergi_penyakit_desc',
            'label' => 'Jelaskan Riwayat Penyakait',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
    );

    public $jenis_donor = array(
        'jenis_donor' => array(
            'field' => 'jenis_donor',
            'label' => 'Jenis Donor',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'kapan_donor' => array(
            'field' => 'kapan_donor',
            'label' => 'Kapan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
    );

    public $kesadaran = array(
        'kesadaran_aferesis_desc' => array(
            'field' => 'kesadaran_aferesis_desc',
            'label' => 'Kesadaran lain',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
    );

    public $perifer = array(
        'lokasi_akses' => array(
            'field' => 'lokasi_akses',
            'label' => 'Lokasi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'no_jarum' => array(
            'field' => 'no_jarum',
            'label' => 'No Jarum',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
    );

    public $frekuensi = array(
        'frekuensi_aferesis' => array(
            'field' => 'frekuensi_aferesis',
            'label' => 'Frekuensi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
    );

    public $nyeri = array(
		'skor_nyeri' => array(
            'field' => 'skor_nyeri',
            'label' => 'Skala Nyeri',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),
    );
    
    public $edukasi = array(
		'edukasi_aferesis_desc' => array(
            'field' => 'edukasi_aferesis_desc',
            'label' => 'Edukasi lain',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),
    );
    
    public $masalah_keperawatan = array(
		'masalasah_keperawatan_aferesis_desc' => array(
            'field' => 'masalasah_keperawatan_aferesis_desc',
            'label' => 'Masalah keperawatan lain',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),
	);

	function __construct(){
		parent::__construct();
    }
    
    function table_query()
    {
        $this->db->select('kp.kunjungan NOKUN, kp.tanggal TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('keperawatan.tb_pengkajian_aferesis kp');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = kp.kunjungan','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = kp.oleh','LEFT');

        $this->db->where('kp.STATUS !=','0');
        $this->db->where('p.NORM',$this->input->post('nomr'));
        $this->db->order_by('kp.TANGGAL', 'DESC');

        // if($this->input->post('id')){
        // 	$this->db->where('hph.ID', $this->input->post('id'));
        // }

        // if($this->input->post('status')){
        //     $this->db->where_in('hph.STATUS_LIS',$this->input->post('status'),FALSE);
        // }
        // else{
        //     $this->db->where('his.STATUS IS NULL');
        // }

        // if($this->input->post('search[value]')){
        //     $this->db->group_start();
        //     $this->db->like('hph.NORM', $this->input->post('search[value]'));
        //     $this->db->or_like('hph.NOMOR_LAB', $this->input->post('search[value]'));
        //     $this->db->group_end();
        //     // $this->db->where_in('his.STATUS',$this->input->post('status'));            
        // }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
