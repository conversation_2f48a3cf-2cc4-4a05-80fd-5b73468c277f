<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cppt extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses')) && !in_array(31, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');

        $this->load->model(
            [
                'masterModel',
                'pengkajianAwalModel',
                'rekam_medis/rawat_inap/catatanTerintegrasi/CpptModel',
                'rekam_medis/KesadaranModel',
                'rekam_medis/TbBbModel',
                'rekam_medis/TandaVitalModel',
                'rekam_medis/rawat_inap/keperawatan/pemantauanNyeriModel',
                'rekam_medis/rawat_inap/catatanTerintegrasi/TbakDetailModel',
                'pendaftaranModel',
                'TindakanBillingModel'
            ]
        );
        $this->load->library('whatsapp');
    }

    public function index()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
        $ruanganRanapTowerC = $this->CpptModel->listRanapTowerC()->result_array();
        $idruanganRanapTowerC = array_column($ruanganRanapTowerC, 'ID');
        $data = [
            'pasien' => $pasien,
            'kesadaran' => $this->masterModel->referensi(5),
            'skriningNyeri' => $this->masterModel->referensi(7),
            'skalaNyeriNRS' => $this->masterModel->referensi(114),
            'skalaNyeriWBR' => $this->masterModel->referensi(115),
            'skalaNyeriFLACC' => $this->masterModel->referensi(123),
            'skalaNyeriBPS' => $this->masterModel->referensi(133),
            'efeksampingNRS' => $this->masterModel->referensi(118),
            'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
            'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
            'pengkajianNyeriTime' => $this->masterModel->referensi(12),
            'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
            'listDr' => $this->masterModel->listDr(),
            'statusGizi' => $this->masterModel->referensi(1231),
            'kontrolKembali' => $this->masterModel->referensi(1850),
            'cekPengkajianAwalRanap' => $this->CpptModel->cekPengkajianAwalRanap($pasien['NOPEN']),
            'idruanganRanapTowerC' => $idruanganRanapTowerC,
        ];

        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/cppt', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();
                // echo '<pre>';print_r($post);exit();
                $nokun = $post['nokun'];
                $oleh = $this->session->userdata('id');

                if ($post['jenis'] == 1) {
                    $rules = $this->CpptModel->rules;
                    $this->form_validation->set_rules($rules);
                    if ($this->session->userdata('config')['cppt'] == 2) {
                        $this->form_validation->set_rules($this->CpptModel->rules_skrining_nyeri);
                        $this->form_validation->set_rules($this->CpptModel->kesadaran);
                    }
                    if ($this->session->userdata('config')['cppt'] == 1) {
                        $this->form_validation->set_rules($this->CpptModel->rules_ap);
                    }
                    if ($this->session->userdata('config')['cppt'] == 2) {
                        if ($post['skrining_nyeri'] != 17) {
                            $this->form_validation->set_rules($this->CpptModel->rules_nyeri);
                        }
                    }
                } else {
                    $rules = $this->CpptModel->rules_adime;
                    $this->form_validation->set_rules($rules);
                }

                if ($this->form_validation->run() == true) {
                    $this->db->trans_begin();
                    // mulai CPPT
                    $dataCppt = [
                        'nokun' => $nokun,
                        'oleh' => $oleh,
                        'jenis' => $post['jenis_ruangan'],
                        'jenis_cppt' => 1,
                        'tanggal' => date('Y-m-d H:i:s'),
                    ];
                    // akhir CPPT

                    if ($post['jenis'] == 1) {
                        $dataTbBb = [
                            'data_source' => 6,
                            'nomr' => $post['nomr'],
                            'nokun' => $nokun,
                            'jenis' => 2,
                            'tb' => $post['tb'],
                            'bb' => $post['bb'],
                            'oleh' => $oleh,
                        ];

                        $id_tb_bb = $post['id_tb_bb'];
                        if (empty($id_tb_bb)) {
                            $id_tb_bb = $this->TbBbModel->insert($dataTbBb);
                        }

                        $dataTandaVital = [
                            'data_source' => 6,
                            'nomr' => $post['nomr'],
                            'nokun' => $nokun,
                            'td_sistolik' => $post['sistolik'],
                            'td_diastolik' => $post['diastolik'],
                            'pernapasan' => $post['pernapasan'],
                            'nadi' => $post['nadi'],
                            'suhu' => $post['suhu'],
                            'oleh' => $oleh,
                        ];

                        $id_tanda_vital = $post['id_tanda_vital'];
                        if (empty($id_tanda_vital)) {
                            $id_tanda_vital = $this->TandaVitalModel->insert($dataTandaVital);
                        }

                        // mulai CPPT
                        $dataCppt['subyektif'] = $post['subyektif'];
                        $dataCppt['obyektif'] = $post['obyektif'];
                        $dataCppt['perencanaan'] = $post['perencanaan'];
                        $dataCppt['instruksi'] = $post['instruksi'];
                        $dataCppt['tb_bb'] = $id_tb_bb;
                        $dataCppt['tanda_vital'] = $id_tanda_vital;
                        // akhir CPPT

                        // Insert Oleh Perawat
                        if ($this->session->userdata('config')['cppt'] == 2) {
                            $dataKesadaran = [
                                'data_source' => 6,
                                'nomr' => $post['nomr'],
                                'nokun' => $nokun,
                                'kesadaran' => $post['kesadaran'],
                                'oleh' => $oleh,
                            ];

                            $id_kesadaran = $post['id_kesadaran'];
                            if (empty($id_kesadaran)) {
                                $id_kesadaran = $this->KesadaranModel->insert($dataKesadaran);
                            }

                            $dataPemantauanNyeri = [
                                'nokun' => $nokun,
                                'data_source' => 6,
                                'metode' => $post['skrining_nyeri'],
                                'skor' => $post['skrining_nyeri'] != 17 ? $post['skor_nyeri'] : "",
                                'created_by' => $oleh,
                            ];

                            $id_nyeri = $post['id_nyeri'];
                            if (empty($id_nyeri)) {
                                $id_nyeri = $this->pemantauanNyeriModel->insert($dataPemantauanNyeri);
                            }

                            // mulai CPPT
                            $dataCppt['pemberi_cppt'] = 2;
                            $dataCppt['kesadaran'] = $id_kesadaran;
                            $dataCppt['skrining_nyeri'] = $id_nyeri;
                            // akhir CPPT
                        } else {
                            // mulai CPPT
                            $dataCppt['pemberi_cppt'] = 1;
                            $dataCppt['analisis'] = $post['analisis'];
                            // akhir CPPT
                        }
                    } else {
                        $dataCppt['pemberi_cppt'] = 3;
                        $dataCppt['asesmen_gizi'] = $post['asesmen'];
                        $dataCppt['diagnosa_gizi'] = $post['diagnosa'];
                        $dataCppt['status_gizi'] = $post['status_gizi'];
                        $dataCppt['intervensi'] = $post['intervensi'];
                        $dataCppt['diet'] = $post['diet'];
                        $dataCppt['bentuk'] = $post['bentuk'];
                        $dataCppt['route'] = $post['route'];
                        $dataCppt['energi'] = $post['energi'];
                        $dataCppt['protein'] = $post['protein'];
                        $dataCppt['lemak'] = $post['lemak'];
                        $dataCppt['kh'] = $post['kh'];
                        $dataCppt['energi_desc'] = $post['energi_desc'];
                        $dataCppt['protein_desc'] = $post['protein_desc'];
                        $dataCppt['lemak_desc'] = $post['lemak_desc'];
                        $dataCppt['kh_desc'] = $post['kh_desc'];
                        $dataCppt['monitoring'] = $post['monitoring'];
                        $dataCppt['evaluasi'] = $post['evaluasi'];
                    }

                    if (!empty($post['id'])) {
                        $id_cppt = $post['id'];

                        // mulai kontrol CPPT
                        $kontrolCPPT = [
                            'ID_CPPT' => $id_cppt,
                            'NOKUN' => $nokun,
                            'KONTROL' => $post['kontrol'] ?? null,
                            'TANGGAL_KONTROL' => $post['tanggal_kontrol'] ?? null,
                        ];
                        // echo '<pre>';print_r($kontrolCPPT);exit();
                        $this->pengkajianAwalModel->insertCpptDetail($kontrolCPPT);
                        // akhir kontrol CPPT

                        if ($post['jenis'] == 1) {
                            $this->CpptModel->update($dataCppt, ['id' => $id_cppt]);

                            if (empty($post['id_tb_bb'])) {
                                $this->TbBbModel->update(['ref' => $id_cppt], ['id' => $id_tb_bb]);
                            }
                            if (empty($post['id_tanda_vital'])) {
                                $this->TandaVitalModel->update(['ref' => $id_cppt], ['id' => $id_tanda_vital]);
                            }

                            if ($this->session->userdata('config')['cppt'] == 2) {
                                if (empty($post['id_kesadaran'])) {
                                    $this->KesadaranModel->update(['ref' => $id_cppt], ['id' => $id_kesadaran]);
                                }
                                if (empty($post['id_nyeri'])) {
                                    $this->pemantauanNyeriModel->update(['ref' => $id_cppt], ['id' => $id_nyeri]);
                                }

                                $this->db->delete('keperawatan.tb_cppt_perencanaan_asuhan_keperawatan', ['id_cppt' => $id_cppt]);
                                $dataPerencanaanAsuhan = [];
                                $index = 0;
                                if (isset($post['asuhankeperawatancppt'])) {
                                    foreach ($post['asuhankeperawatancppt'] as $input) {
                                        if ($post['asuhankeperawatancppt'][$index] != '') {
                                            array_push(
                                                $dataPerencanaanAsuhan,
                                                [
                                                    'id_cppt' => $id_cppt,
                                                    'id_asuhan_keperawatan_detil' => $post['asuhankeperawatancppt'][$index],
                                                ]
                                            );
                                        }
                                        $index++;
                                    }
                                    $this->db->insert_batch('keperawatan.tb_cppt_perencanaan_asuhan_keperawatan', $dataPerencanaanAsuhan);
                                }

                                $dataTBAK = [];
                                $index = 0;
                                if (isset($post['tbakDpjpp'])) {
                                    foreach ($post['tbakDpjpp'] as $input) {
                                        if ($post['tbakDpjpp'][$index] != '') {
                                            array_push(
                                                $dataTBAK,
                                                [
                                                    'id_cppt' => $id_cppt,
                                                    'dokter_tbak' => $post['tbakDpjpp'][$index],
                                                    'instruksi_tbak' => $post['instruksi_tbak'][$index],
                                                    'hasil_kritis' => $post['hasil_kritis'][$index],
                                                    'waktu_lapor' => $post['hasil_kritis'][$index] ? $post['waktu_lapor'][$index] : null,
                                                    'created_at' => date('Y-m-d H:i:s'),
                                                    'status' => 1,
                                                    'oleh' => $this->session->userdata['id'],
                                                ]
                                            );
                                        }
                                        $index++;
                                    }
                                    $this->db->insert_batch('keperawatan.tb_tbak_detail', $dataTBAK);
                                }
                            } elseif ($this->session->userdata('config')['cppt'] == 1) {
                                $dataTBAK = [];
                                $index = 0;
                                if (isset($post['tbakDpjpp'])) {
                                    foreach ($post['tbakDpjpp'] as $input) {
                                        if ($post['tbakDpjpp'][$index] != "") {
                                            array_push(
                                                $dataTBAK,
                                                [
                                                    'id_cppt' => $id_cppt,
                                                    'dokter_tbak' => $post['tbakDpjpp'][$index],
                                                    'instruksi_tbak' => $post['instruksi_tbak'][$index],
                                                    'hasil_kritis' => $post['hasil_kritis'][$index],
                                                    'waktu_lapor' => $post['hasil_kritis'][$index] ? $post['waktu_lapor'][$index] : null,
                                                    'created_at' => date('Y-m-d H:i:s'),
                                                    'status' => 1,
                                                    'oleh' => $this->session->userdata['id'],
                                                ]
                                            );
                                        }
                                        $index++;
                                    }
                                    $this->db->insert_batch('keperawatan.tb_tbak_detail', $dataTBAK);
                                }

                                if ($post['ruangan'] == 105120101) {
                                    $datatoksitasradiasi = [
                                        'id_cppt' => $id_cppt,
                                        'rtog' => $post['rtog'],
                                        'jenis_tokisisitas' => $post['jenisToksisitas'],
                                        'kemoradiasi' => $post['kemoradiasi'],
                                        'kemoradiasi_lainnya' => $post['deskKemoradiasi'],
                                    ];
                                    $this->db->where('id', $post['id_tokisistas'])->update('medis.tb_toksisitas_radiasi_regio', $datatoksitasradiasi);

                                    $this->db->delete('medis.tb_toksisitas_jaringan', ['id_toksisitas_radiasi_regio' => $post['id_tokisistas']]);

                                    $datagradeToksitas = [];
                                    $indexT = 0;

                                    if (isset($post['gradeToksitas'])) {
                                        foreach ($post['gradeToksitas'] as $inputToksitas) {
                                            if ($post['gradeToksitas'][$indexT] != '') {
                                                array_push(
                                                    $datagradeToksitas,
                                                    [
                                                        'id_toksisitas_radiasi_regio' => $post['id_tokisistas'],
                                                        'id_toksisitas_jaringan' => $post['idgradeToksitas'][$indexT],
                                                        'grade' => $post['gradeToksitas'][$indexT],
                                                    ]
                                                );
                                            }
                                            $indexT++;
                                        }
                                        $this->db->insert_batch('medis.tb_toksisitas_jaringan', $datagradeToksitas);
                                    }
                                }
                            }
                        } else {
                            $this->CpptModel->update($dataCppt, ['id' => $id_cppt]);
                        }
                    } else {
                        if ($post['jenis'] == 1) {
                            $dataCppt['tanggal'] = isset($post['backdate']) ? $post['tanggal'] : date('Y-m-d H:i:s');
                            $dataCppt['status_backdate'] = isset($post['backdate']) ? $post['backdate'] : 0;
                            $id_cppt = $this->CpptModel->insert($dataCppt);

                            // mulai kontrol CPPT
                            $kontrolCPPT = [
                                'ID_CPPT' => $id_cppt,
                                'NOKUN' => $nokun,
                                'KONTROL' => $post['kontrol'] ?? null,
                                'TANGGAL_KONTROL' => $post['tanggal_kontrol'] ?? null,
                            ];
                            // echo '<pre>';print_r($kontrolCPPT);exit();
                            $this->pengkajianAwalModel->insertCpptDetail($kontrolCPPT);
                            // akhir kontrol CPPT

                            if (empty($post['id_tb_bb'])) {
                                $this->TbBbModel->update(['ref' => $id_cppt], ['id' => $id_tb_bb]);
                            }
                            if (empty($post['id_tanda_vital'])) {
                                $this->TandaVitalModel->update(['ref' => $id_cppt], ['id' => $id_tanda_vital]);
                            }

                            if ($this->session->userdata('config')['cppt'] == 2) {
                                if (empty($post['id_kesadaran'])) {
                                    $this->KesadaranModel->update(['ref' => $id_cppt], ['id' => $id_kesadaran]);
                                }
                                if (empty($post['id_nyeri'])) {
                                    $this->pemantauanNyeriModel->update(['ref' => $id_cppt], ['id' => $id_nyeri]);
                                }

                                $this->db->delete('keperawatan.tb_cppt_perencanaan_asuhan_keperawatan', ['id_cppt' => $id_cppt]);
                                $dataPerencanaanAsuhan = [];
                                $index = 0;
                                if (isset($post['asuhankeperawatancppt'])) {
                                    foreach ($post['asuhankeperawatancppt'] as $input) {
                                        if ($post['asuhankeperawatancppt'][$index] != '') {
                                            array_push(
                                                $dataPerencanaanAsuhan,
                                                [
                                                    'id_cppt' => $id_cppt,
                                                    'id_asuhan_keperawatan_detil' => $post['asuhankeperawatancppt'][$index],
                                                ]
                                            );
                                        }
                                        $index++;
                                    }
                                    $this->db->insert_batch('keperawatan.tb_cppt_perencanaan_asuhan_keperawatan', $dataPerencanaanAsuhan);
                                }

                                $dataTBAK = [];

                                $index = 0;
                                if (isset($post['tbakDpjpp'])) {
                                    foreach ($post['tbakDpjpp'] as $input) {
                                        if ($post['tbakDpjpp'][$index] != '') {
                                            array_push(
                                                $dataTBAK,
                                                [
                                                    'id_cppt' => $id_cppt,
                                                    'dokter_tbak' => $post['tbakDpjpp'][$index],
                                                    'instruksi_tbak' => $post['instruksi_tbak'][$index],
                                                    'hasil_kritis' => $post['hasil_kritis'][$index],
                                                    'waktu_lapor' => $post['hasil_kritis'][$index] ? $post['waktu_lapor'][$index] : null,
                                                    'created_at' => date('Y-m-d H:i:s'),
                                                    'status' => 1,
                                                    'oleh' => $this->session->userdata['id'],
                                                ]
                                            );
                                        }
                                        $index++;
                                    }
                                    $this->db->insert_batch('keperawatan.tb_tbak_detail', $dataTBAK);
                                }
                            } elseif ($this->session->userdata('config')['cppt'] == 1) {
                                $dataTBAK = [];
                                $index = 0;
                                if (isset($post['tbakDpjpp'])) {
                                    foreach ($post['tbakDpjpp'] as $input) {
                                        if ($post['tbakDpjpp'][$index] != '') {
                                            array_push(
                                                $dataTBAK,
                                                [
                                                    'id_cppt' => $id_cppt,
                                                    'dokter_tbak' => $post['tbakDpjpp'][$index],
                                                    'instruksi_tbak' => $post['instruksi_tbak'][$index],
                                                    'hasil_kritis' => $post['hasil_kritis'][$index],
                                                    'waktu_lapor' => $post['hasil_kritis'][$index] ? $post['waktu_lapor'][$index] : null,
                                                    'created_at' => date('Y-m-d H:i:s'),
                                                    'status' => 1,
                                                    'oleh' => $this->session->userdata['id'],
                                                ]
                                            );
                                        }
                                        $index++;
                                    }
                                    $this->db->insert_batch('keperawatan.tb_tbak_detail', $dataTBAK);
                                }

                                if ($post['ruangan'] == 105120101) {
                                    $datatoksitasradiasi = [
                                        'id_cppt' => $id_cppt,
                                        'rtog' => $post['rtog'],
                                        'jenis_tokisisitas' => $post['jenisToksisitas'],
                                        'kemoradiasi' => $post['kemoradiasi'],
                                        'kemoradiasi_lainnya' => $post['deskKemoradiasi'],
                                    ];

                                    $id_toksisitas = $this->pengkajianAwalModel->insertToksitasRadiasi($datatoksitasradiasi);

                                    $datagradeToksitas = [];
                                    $indexT = 0;

                                    if (isset($post['gradeToksitas'])) {
                                        foreach ($post['gradeToksitas'] as $inputToksitas) {
                                            if ($post['gradeToksitas'][$indexT] != '') {
                                                array_push(
                                                    $datagradeToksitas,
                                                    [
                                                        'id_toksisitas_radiasi_regio' => $id_toksisitas,
                                                        'id_toksisitas_jaringan' => $post['idgradeToksitas'][$indexT],
                                                        'grade' => $post['gradeToksitas'][$indexT],
                                                    ]
                                                );
                                            }
                                            $indexT++;
                                        }
                                        $this->db->insert_batch('medis.tb_toksisitas_jaringan', $datagradeToksitas);
                                    }
                                }
                            }
                        } else {
                            $id_cppt = $this->CpptModel->insert($dataCppt);
                        }
                    }

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = ['status' => 'failed'];
                    } else {
                        $this->db->trans_commit();
                        if($param == 'tambah'){
                            $ruanganRanapTowerC = $this->CpptModel->listRanapTowerC()->result_array();
                            $idruanganRanapTowerC = array_column($ruanganRanapTowerC, 'ID');
                            // $ruanganRanapTowerC = array('105011701', '105011801','105011901','105030108', '105011501');
                            $cek = $this->TindakanBillingModel->cekBilling($post['nokun'], $this->session->userdata('iddokter'));
                            $sudahAdaHariIni = false;
                            if ($cek) {
                                foreach ($cek as $row) {
                                    if (($row->TINDAKAN == 4610 && date('Y-m-d', strtotime($row->TANGGAL)) == date('Y-m-d')) || 
                                        ($row->TINDAKAN == 4773 && date('Y-m-d', strtotime($row->TANGGAL)) == date('Y-m-d'))) 
                                    {
                                    $sudahAdaHariIni = true;
                                    break;
                                    }
                                }
                            }
                            if (!$sudahAdaHariIni) {
                                // if(in_array($post['ruangan'], $idruanganRanapTowerC)){
                                    if($this->session->userdata('profesi') == 11){
                                        if($this->session->userdata('smf') != 31 || $this->session->userdata('iddokter') == 10){
                                            $cekDPJPPengganti = $this->CpptModel->cekDPJPPenggantiCPPT($post['nopen'], $this->session->userdata('iddokter'))->num_rows();
                                            if($this->session->userdata('iddokter') == $post['dpjp'] || $cekDPJPPengganti > 0){
                                                $this->TindakanBillingModel->simpanOrder($nokun, 4610, $this->session->userdata('id'), $this->session->userdata('iddokter'));
                                            }else{
                                                $this->TindakanBillingModel->simpanOrder($nokun, 4773, $this->session->userdata('id'), $this->session->userdata('iddokter'));
                                            }
                                        }else{
    
                                        }
                                    }
                                // }else{
                                    
                                //     if($this->session->userdata('profesi') == 11){
                                //         if($this->session->userdata('smf') != 31 || $this->session->userdata('iddokter') == 10){
                                //             $cekDPJPPengganti = $this->CpptModel->cekDPJPPenggantiCPPT($post['nopen'], $this->session->userdata('iddokter'))->num_rows();
                                //             if($this->session->userdata('iddokter') == $post['dpjp'] || $cekDPJPPengganti > 0){
                                //                 $this->TindakanBillingModel->simpanOrder($nokun, 4610, $this->session->userdata('id'), $this->session->userdata('iddokter'));
                                //             }else{
                                //                 $this->TindakanBillingModel->simpanOrder($nokun, 4773, $this->session->userdata('id'), $this->session->userdata('iddokter'));
                                //             }
                                //         }else{
    
                                //         }
                                //     }
                                // }
                            }
                        }
                        $result = ['status' => 'success'];
                    }
                } else {
                    $result = [
                        'status' => 'failed',
                        'errors' => $this->form_validation->error_array()
                    ];
                }
                echo json_encode($result);
            } elseif ($param == 'ambil') {
                echo json_encode(
                    [
                        'status' => 'success',
                        'data' => $this->CpptModel->get_table(),
                        'kontrol' => $this->pengkajianAwalModel->ambilDetailCppt($this->input->post('id')),
                    ]
                );
            } elseif ($param == 'count') {
                $result = $this->CpptModel->get_count();
                echo json_encode($result);
            } elseif ($param == 'simpan_catatan') {
                $post = $this->input->post();
                // echo '<pre>';print_r($post);exit();

                $this->db->trans_begin();
                if (isset($post['id_cppt'])) {
                    $data = [
                        'id_cppt' => $post['id_cppt'],
                        'catatan' => $post['catatan'],
                        'oleh' => $this->session->userdata('id'),
                    ];
                    // echo '<pre>';print_r($data);exit();

                    if (empty($post['id_catatan'])) { // Jika id_catatan kosong, simpan catatan
                        $this->CpptModel->simpanCatatan($data);
                    } else { // Jika id_catatan tidak kosong, ubah catatan
                        $this->CpptModel->ubahCatatan($post['id_catatan'], $data);
                    }

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = ['status' => 'failed'];
                    } else {
                        $this->db->trans_commit();
                        $result = ['status' => 'success'];
                    }
                    echo json_encode($result);
                }
            } elseif ($param == 'verif') {
                $post = $this->input->post();
                $this->db->trans_begin();
                $this->CpptModel->update(
                    [
                        'status_verif' => 1,
                        'verif_oleh' => $this->session->userdata('id'),
                        'tanggal_verif' => date('Y-m-d H:i:s')
                    ],
                    ['id' => $post['id']]
                );

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
                echo json_encode($result);
            } elseif ($param == 'verif_banyak') {
                $post = $this->input->post();
                $waktu = date('Y-m-d H:i:s');
                $oleh = $this->session->userdata('id');
                // echo '<pre>';print_r($oleh);exit();

                $this->db->trans_begin();
                $data = [];
                if (isset($post['id_cppt'])) {
                    foreach ($post['id_cppt'] as $id) {
                        array_push(
                            $data,
                            [
                                'id' => $id,
                                'status_verif' => 1,
                                'verif_oleh' => $oleh,
                                'tanggal_verif' => $waktu,
                            ]
                        );
                    }
                }
                // echo '<pre>';print_r($data);exit();
                $this->CpptModel->ubahBanyak($data, 'id');

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
                echo json_encode($result);
            } elseif ($param == 'getTbak') {
                $post = $this->input->post(null, true);
                $dataTBAK = $this->TbakDetailModel->get_table();

                echo json_encode(
                    [
                        'status' => 'success',
                        'data' => $dataTBAK
                    ]
                );
            } elseif ($param == 'deleteTbak') {
                $post = $this->input->post(null, true);
                if (!empty($post['id'])) {
                    $data = [
                        'status' => '0',
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'deleted_by' => $this->session->userdata('id'),
                    ];
                    $this->TbakDetailModel->update($data, ['id' => $post['id']]);
                    $result = ['status' => 'success'];
                }

                echo json_encode($result);
            } elseif ($param == 'getRTOG') {
                $post = $this->input->post(null, true);
                $dataToksitas = $this->db->get_where('medis.tb_toksisitas_radiasi_regio', ['id_cppt' => $post['id']])->row();
                $dataToksitasJaringan = $this->db->get_where('medis.tb_toksisitas_jaringan', ['id_toksisitas_radiasi_regio' => $dataToksitas->id])->result();

                echo json_encode(
                    [
                        'status' => 'success',
                        'dataToksitas' => $dataToksitas,
                        'dataToksitasJaringan' => $dataToksitasJaringan
                    ]
                );
            }
        }
    }

    public function datatables()
    {
        $result = $this->CpptModel->datatables();
        $data = [];

        foreach ($result as $row) {
            $verif = "<h6 style='text-align: center; vertical-align: middle;'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
            $cetak = "<h6 style='text-align: center; vertical-align: middle;'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
            $lihat = "<h6 style='text-align: center; vertical-align: middle;'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
            $tbak = "<h6 style='text-align: center; vertical-align: middle;'><i class='fa fa-minus' aria-hidden='true'></i></h6>";

            if ($row->jenis == 2 && $row->pemberi_cppt == 2) {
                $verif = "<h4 style='text-align: center; vertical-align: middle;'><i class='fa fa-check' aria-hidden='true'></i></h4>";
                if ($row->status_verif == 0) {
                    $verif = "<h4 style='text-align: center; vertical-align: middle;'><i class='fa fa-times' aria-hidden='true'></i></h4>";
                }
            }

            if ($row->TBAK == 1) {
                $tbak = "<h4 style='text-align: center; vertical-align: middle;'><i class='fa fa-check' aria-hidden='true'></i></h4>";
            }

            if ($row->pemberi_cppt == 3) {
                $cetak = "<button type='button' id='view-cppt' data-iframe='true' data-name='emr.cppt.cpptgizi' data-parameter='{\"ID\":\"".$row->IDCPPT."\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'><i class='fa fa-print'></i> Cetak</button>";
            } else {
                if ($row->JENIS_KUNJUNGAN == 1 || $row->JENIS_KUNJUNGAN == 13 || $row->JENIS_KUNJUNGAN == 14 || $row->JENIS_KUNJUNGAN == 15) {
                    $cetak = "<button type='button' id='view-cppt' data-iframe='true' data-name='emr.cppt.cpptnew' data-parameter='{\"ID\":\"".$row->IDCPPT."\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'><i class='fa fa-print'></i> Cetak</button>";
                    if ($row->IDRUANGAN == 105120101) {
                        $cetak = "<button type='button' id='view-cppt' data-iframe='true' data-name='emr.cppt.cpptradioterapinew' data-parameter='{\"ID\":\"".$row->IDCPPT."\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'><i class='fa fa-print'></i> Cetak</button>";
                    } elseif ($row->IDRUANGAN == 105110101) {
                        $cetak = "<button type='button' id='view-cppt' data-iframe='true' data-name='emr.cppt.cpptrehab' data-parameter='{\"NOKUN\":\"".$row->nokun."\",\"JENISCPPT\":".$row->JENIS_CPPT."}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'><i class='fa fa-print'></i> Cetak</button>";
                    }
                } elseif ($row->JENIS_KUNJUNGAN == 2 || $row->JENIS_KUNJUNGAN == 3) {
                    $cetak = "<button type='button' id='view-cppt' data-iframe='true' data-name='emr.cppt.cpptrawatinap' data-parameter='{\"ID\":\"".$row->IDCPPT."\"}' class='btn btn-warning btn-block btn-sm tombolCetakan' target='_blank'><i class='fa fa-print'></i> Cetak</button>";
                }
            }


            if ($row->JENIS_KUNJUNGAN != 1 && $row->JENIS_KUNJUNGAN != 14) {
                $lihat = "<a class='btn btn-primary btn-block btn-sm history_cppt' data-id='" . $row->IDCPPT . "'><i class='fa fa-eye'></i> Lihat</a>";
            }

            if ($row->IDRUANGAN == 105020101 || $row->IDRUANGAN == 105020102) {
                $lihat = "<a class='btn btn-primary btn-block btn-sm history_cppt' data-id='" . $row->IDCPPT . "'><i class='fa fa-eye'></i> Lihat</a>";
            }

            $sub_array = [];
            $sub_array[] = $lihat;
            $sub_array[] = $cetak;
            $sub_array[] = $verif;
            $sub_array[] = $tbak;
            $sub_array[] = $row->tanggal;
            $sub_array[] = $row->RUANGAN;
            $sub_array[] = $row->PROFESI;
            $sub_array[] = $row->NAMAPEGAWAI;
            $sub_array[] = $row->DOKTERDPJP;

            $data[] = $sub_array;
        }

        $output = [
            'draw' => intval($_POST['draw']),
            'recordsTotal' => $this->CpptModel->total_count(),
            'recordsFiltered' => $this->CpptModel->filter_count(),
            'data' => $data
        ];
        echo json_encode($output);
    }

    public function jumlahNotifikasiTBAK()
    {
        $idPengguna = $this->session->userdata('id');
        $jumlahTBAK = $this->TbakDetailModel->notifTBAK($idPengguna, 'jumlah');
        echo json_encode($jumlahTBAK);
    }

    public function modalNotifikasiTBAK()
    {
        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/notifikasiTBAK/index');
    }

    public function notifikasiTBAK()
    {
        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/notifikasiTBAK/notifikasi');
    }

    public function historyTBAK()
    {
        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/notifikasiTBAK/history');
    }

    public function tabelNotifikasiTBAK($param)
    {
        $draw = intval($this->input->POST('draw'));
        $idPengguna = $this->input->POST('id_pengguna');
        $tabel = $this->TbakDetailModel->notifTBAK($idPengguna, $param);
        $data = [];
        $no = 1;
        // echo '<pre>';print_r($tabel);exit();

        if ($param == 'notifikasi') {
            foreach ($tabel->result() as $t) {
                $data[] = [
                    $no,
                    $t->NORM,
                    $t->pasien,
                    $t->instruksi,
                    $t->pengirim,
                    $t->profesi,
                    $t->ruang,
                    date('d-m-Y', strtotime($t->tanggal)) . ', pukul ' . date('H.i', strtotime($t->tanggal)),
                    "<button type='button' href='#modal-verifikasi-notifikasi-tbak' class='btn btn-sm btn-primary waves-effect' id='tbl-verifikasi-notifikasi-tbak' data-toggle='modal' data-id='" . $t->id . '-' . $t->tipe . "'>
                        <i class='fa fa-check'></i> Verifikasi
                    </button>",
                ];
                $no++;
            }
        } elseif ($param == 'history') {
            foreach ($tabel->result() as $t) {
                $data[] = [
                    $no,
                    $t->NORM,
                    $t->pasien,
                    $t->instruksi,
                    $t->pengirim,
                    $t->profesi,
                    $t->ruang,
                    date('d-m-Y', strtotime($t->tanggal)) . ', pukul ' . date('H.i', strtotime($t->tanggal)),
                    $t->pemverifikasi,
                    date('d-m-Y', strtotime($t->tanggal_verif)) . ', pukul ' . date('H.i', strtotime($t->tanggal_verif)),
                    isset($t->catatan_verif) ? $t->catatan_verif : '-',
                ];
                $no++;
            }
        }

        $output = [
            'draw' => $draw,
            'recordsTotal' => $tabel->num_rows(),
            'recordsFiltered' => $tabel->num_rows(),
            'data' => $data
        ];
        echo json_encode($output);
    }

    public function verifikasiTBAK()
    {
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $post = $this->input->post();
            // echo '<pre>';print_r($post);exit();
            $id = isset($post['id']) ? $post['id'] : null;
            $tipe = isset($post['tipe']) ? $post['tipe'] : null;

            $data = array(
                'catatan_verif' => isset($post['catatan']) ? $post['catatan'] : null,
                'status_verif' => 1,
                'tanggal_verif' => date('Y-m-d H:i:s'),
                'verif_oleh' => $this->session->userdata('id'),
            );
            // echo '<pre>';print_r($data);exit();
            if ($tipe == 1 || $tipe == 2) {
                $this->TbakDetailModel->ubah($data, $id);
            } elseif ($tipe == 3) {
                $this->CpptModel->update($data, array('id' => $id));
            }

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = ['status' => 'failed'];
            } else {
                $this->db->trans_commit();
                $result = ['status' => 'success'];
            }
            echo json_encode($result);
        }
    }

    public function list()
    {
        $nomr = $this->uri->segment(2);
        $nopen = null;
        $tanggal = null;
        $bagian = 0;

        // Mulai mengecek parameter
        $param = explode('_', $this->uri->segment(3));
        // echo '<pre>';print_r($param);exit();
        if ($param[0] == 'id') { // Jika parameter diawali dengan id
            $nopen = $param[1];
        } elseif ($param[0] == 'tanggal') { // Jika parameter diawali dengan tanggal
            $tanggal = $param[1];
        } elseif ($param[0] == 'bagian') {
            $bagian = $this->uri->segment(4);
            // echo '<pre>';print_r($bagian);exit();
        } else {
            $bagian = 0;
        }
        // Akhir mengecek parameter
        // echo '<pre>';print_r($nopen);exit();

        $data = [
            'pendaftaran' => $this->pendaftaranModel->getAll($nomr),
            'nopen' => $nopen,
            'nomr' => $nomr,
            'tanggal' => $tanggal,
            'tanggalCPPT' => $this->CpptModel->ambilTanggal($nomr),
            'verifikasi' => null,
            'disabled' => null,
            'izin_pengguna' => $this->session->userdata('config')['cppt']
        ];
        // echo '<pre>';print_r($data);exit();

        $_POST = [];
        if (isset($tanggal) || $tanggal != null) { // Cari CPPT berdasarkan tanggal
            $_POST['nomr'] = $nomr;
            $_POST['tanggal'] = $tanggal;
            $data['tanggal'] = $tanggal;
            $data['cppt'] = $this->CpptModel->get_history_tanggal(false);
        } else {
            if (isset($nopen) || $nopen != null) { // Cari CPPT berdasarkan nopen
                $_POST['nopen'] = $nopen;
                // $data['nopen'] = $nopen;

                $data['cppt'] = $this->CpptModel->get_history(false);
                $data['pendaftaran'] = $this->pendaftaranModel->getAll($nomr);
            } else { // Semua data CPPT
                $get = $this->pagination($nomr, $bagian);
                $data['cppt'] = $get['cppt'];
                $data['pagination'] = $get['pagination'];
            }
        }

        // echo '<pre>';print_r($data);exit();
        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/cpptList', $data);
    }

    public function pagination($nomr, $bagian)
    {
        // echo '<pre>';print_r($bagian);exit();
        $_POST['nomr'] = $nomr;

        $this->load->library('pagination');
        $config = [
            'base_url' => base_url('cpptList/' . $nomr . '/bagian'), // Link
            'total_rows' => $this->CpptModel->count_history(false)['jumlah'], // Total data
            'per_page' => 5, // Data per halaman
            'num_links' => 4, // Jumlah nomor halaman di sisi nomor halaman sekarang
            'first_link' => "<i class='fa-solid fa-angles-left'></i>",
            'last_link' => "<i class='fa-solid fa-angles-right'></i>",
            'next_link' => "<i class='fa-solid fa-angle-right'></i>",
            'prev_link' => "<i class='fa-solid fa-angle-left'></i>",
            'full_tag_open' => "<div class='btn-group' role='group'>",
            'full_tag_close' => "</div>",
            'cur_tag_open' => "<a class='btn btn-primary waves-effect'>",
            'cur_tag_close' => "</a>",
            'attributes' => [
                'class' => 'btn btn-outline-primary waves-effect halaman-cppt-list'
            ]
        ];
        $this->pagination->initialize($config);

        $_POST['limit'] = $config['per_page'];
        $_POST['offset'] = $bagian;

        $data['cppt'] = $this->CpptModel->get_history(false);
        $data['pagination'] = $this->pagination->create_links();
        // echo '<pre>';print_r($data);exit();
        return $data;
    }

    public function history()
    {
        $_POST['nomr'] = $this->uri->segment(2);
        $history = $this->CpptModel->get_table(false);
        $id = $this->uri->segment(3);
        if (isset($id) && $id != 0) {
            $_POST['id'] = $this->uri->segment(3);
        } elseif ($id == 'me') {
            $_POST['oleh'] = $this->session->userdata('id');
        } else {
            $_POST['nomr'] = $this->uri->segment(2);
        }
        $cppt = $this->CpptModel->get_history(false);

        $data = [
            'cppt' => $cppt,
            'history_cppt' => $history,
            'id' => $id,
        ];

        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/cpptView', $data);
    }

    public function getTagihan()
    {
        $nopen = $this->input->GET('nopen');

        if (empty($nopen)) {
            $response = [
                'status' => false,
                'message' => 'Parameter NOPEN tidak boleh kosong'
            ];
            echo json_encode($response);
            return;
        }

        $data = $this->CpptModel->ambilIDTagihan($nopen);

        if ($data) {
            $response = [
                'status' => true,
                'data' => $data
            ];
        } else {
            $response = [
                'status' => false,
                'message' => 'Data tidak ditemukan'
            ];
        }

        // Tampilkan hasil sebagai JSON
        echo json_encode($response);
    }

    public function getTindakanList()
    {
        $ruangan = $this->input->post('ruangan');
        $tindakanList = $this->CpptModel->listTindakan($ruangan);
        echo json_encode($tindakanList);
    }

    public function simpanTindakan()
    {
        $post = $this->input->post();
        $nokun = $post['kunjungan'];
        $oleh = $this->session->userdata('id');
        $olehDok = $this->session->userdata('iddokter');

        $saved_ids = [];

        foreach ($post['id_tindakan'] as $index => $tindakan) {
            $result = $this->TindakanBillingModel->simpanOrder($nokun, $tindakan, $oleh, $olehDok);

            if (!$result['success']) {
                $response = [
                    'success' => false,
                    'message' => 'Gagal menyimpan tindakan: ' . $tindakan
                ];
                echo json_encode($response);
                return;
            }

            $saved_ids[] = $result['ID'];
        }

        $response = [
            'success' => true,
            'message' => 'Order berhasil disimpan.',
            'saved_ids' => $saved_ids
        ];
        echo json_encode($response);
    }

    public function simpanDiskonDokter()
    {
        $post = $this->input->post();

        try {
            if (empty($post['saved_ids']) || empty($post['tarifDiskon'])) {
                throw new Exception('Data diskon tidak lengkap');
            }

            // Loop untuk setiap saved_ids dan tarifDiskon
            foreach ($post['saved_ids'] as $index => $saved_id) {
                $data = array(
                    'TAGIHAN' => $post['id_tagihan'],
                    'DOKTER' => $this->session->userdata('iddokter'),
                    'TOTAL' => $post['tarifDiskon'][$index], // Gunakan diskon per tindakan
                    'TINDAKAN' => $saved_id, // Simpan ID tindakan tunggal
                    'TANGGAL' => date("Y-m-d H:i:s"),
                    'OLEH' => $this->session->userdata('id'),
                    'STATUS' => 1,
                );

                $result = $this->db->insert('pembayaran.diskon_dokter', $data);

                if (!$result) {
                    throw new Exception('Gagal menyimpan diskon: ' . $this->db->error()['message']);
                }
            }

            $response = [
                'success' => true,
                'message' => 'Diskon berhasil disimpan'
            ];
        } catch (Exception $e) {
            log_message('error', 'Error di simpanDiskonDokter: ' . $e->getMessage());
            $response = [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }

        echo json_encode($response);
    }

    public function modalGdpjp()
    {
        $nokun = $this->input->post('nokun');
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array(
            'nokun' => $nokun,
            'pasien' => $pasien,
            'dokter' => $this->masterModel->listDr(),
        );

        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/modal_GDpjp', $data);
    }

    public function simpanPergantianDPJP()
    {
        $this->db->trans_begin();
        $post = $this->input->post();

        $dataDPJPSementara = array (
            'nopen'             => $post['nopen_dpjp'],
            'nokun'             => $post['nokun_dpjp'],
            'ruangan'           => $post['ruangan_dpjp'],
            'dpjp'              => $post['iddpjpSekarang'],
            'dpjp_pengganti'    => $post['dpjpPengganti'],
            'alasan'            => $post['alasanGanti'],
            'oleh'              => $this->session->userdata('id'),
            'created_at'        => date('Y-m-d H:i:s'),
        );
        // echo "<pre>";print_r($dataDPJPSementara);echo "</pre>";

        $this->db->insert('keperawatan.tb_cppt_dpjp_sementara', $dataDPJPSementara);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function updatePergantianDPJP()
    {
        $this->db->trans_begin();
        $post = $this->input->post();

        $id    = $post['ID'];

        if($post['PAR'] == 'selesai'){
            $dataDPJPSementaraUbah = array (
                'oleh_update'           => $this->session->userdata('id'),
                'updated_at'            => date('Y-m-d H:i:s'),
                'status'                => 2
            );
        }elseif($post['PAR'] == 'batal'){
            $dataDPJPSementaraUbah = array (
                'oleh_update'           => $this->session->userdata('id'),
                'updated_at'            => date('Y-m-d H:i:s'),
                'status'                => 0
            );
        }

        // echo "<pre>";print_r($dataEdukasiTAS_edit);echo "</pre>";exit();

        $this->db->where('id', $id);
        $this->db->update('keperawatan.tb_cppt_dpjp_sementara', $dataDPJPSementaraUbah);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function historyPergantianDPJP()
    {
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));

        $nopen = $this->input->post('nopen');
        $listHistory = $this->CpptModel->listHistoryPergantianDPJP($nopen);

        $data = array();
        $no = 1;
        foreach ($listHistory->result() as $LH) {
            if($LH->status == 1){
                $button = '<a class="btn btn-block selesaiDPJP" style="background-color: #3EC1D3; color: #fff;" data="'.$LH->id.'"><i class="fas fa-check"></i> Selesai</a><a class="btn btn-block batalDPJP" style="background-color: #FF165D; color: #fff;" data="'.$LH->id.'"><i class="fas fa-xmark"></i> Batal</a>';
                $tgl = date_indo(date('Y-m-d', strtotime($LH->created_at)));
            }else{
                if($LH->status == 0){
                    $button = '<span class="badge badge-danger">Batal</span>';
                    $tgl = date_indo(date('Y-m-d', strtotime($LH->created_at)));
                }elseif($LH->status == 2){
                    $button = '<span class="badge badge-success">Selesai</span>';
                    $tgl = date_indo(date('Y-m-d', strtotime($LH->created_at))).' <b>s/d</b> '.date_indo(date('Y-m-d', strtotime($LH->updated_at)));
                }
            }
             
            $data[] = array(
                $no,
                $LH->DPJP_UTAMA,
                $LH->DPJP_PENGGANTI,
                $LH->ALASAN_GANTI,
                $LH->OLEH_INPUT,
                $tgl,
                $button,

            );
            $no++;
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listHistory->num_rows(),
            "recordsFiltered" => $listHistory->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }
}

// End of file Cppt.php
// Location: ./application/controllers/rekam_medis/rawat_inap/catatanTerintegrasi/Cppt.php