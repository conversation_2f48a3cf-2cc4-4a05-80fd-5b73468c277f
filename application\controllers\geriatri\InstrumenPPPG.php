<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class InstrumenPPPG extends CI_Controller {

  public function __construct()
  {
      parent::__construct();
      if ($this->session->userdata('logged_in') == false) {
          redirect('login');
      }
  
      if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
          redirect('login');
      }
  
      date_default_timezone_set("Asia/Bangkok");
      $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $hgeriatripppg = $this->pengkajianAwalModel->historyGeriatriPppg($getNomr['NORM']);
    $listGeriatriPppg = $this->pengkajianAwalModel->listGeriatriPppg($nokun);

    $data = array(
      'getNomr' => $getNomr,
      'hgeriatripppg' => $hgeriatripppg,
      'rekKatPppg' => $this->masterModel->referensi(1732),
      'listGeriatriPppg' => $listGeriatriPppg
    );

    $this->load->view('Pengkajian/geriatri/instrumenPPPG/index', $data);
  }
}

/* End of file InstrumenMNA.php */
/* Location: ./application/controllers/geriatri/InstrumenMNA.php */

?>
