<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AdmPermintaanDirawat extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(1,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('PengkajianAwalModel','masterModel', 'permintaanDirawat/AdmModel'));
  }

  public function index()
  {
    $id_pengguna = $this->session->userdata('id');
    $listPermintaanRawatNoFinal = $this->AdmModel->listPermintaanRawatNoFinal();
    $listPermintaanRawatFinal = $this->AdmModel->listPermintaanRawatFinal();
    $getCount = $this->AdmModel->get_count();

    $data = array(
      'title'       => 'Permintaan Dirawat',
      'isi'         => 'Pengkajian/permintaanDirawat/listPasienRawat',
      'listPermintaanRawatNoFinal' => $listPermintaanRawatNoFinal,
      'listPermintaanRawatFinal' => $listPermintaanRawatFinal,
      'getCount' => $getCount
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function loadAdmPermintaanPasienFinal()
  {
    $id_pengguna = $this->session->userdata('id');
    $id = $this->input->post('id');
    $nmpasien = $this->input->post('nmpasien');

    $data = array(
      'id' => $id,
      'nmpasien' => $nmpasien,
    );

    $this->load->view('Pengkajian/permintaanDirawat/yakinFinalPasien', $data);
  }

  public function inputFinalAdmPermintaanPasienNoFinal()
  {
    $post = $this->input->post();
    $id = $this->input->post('idnmpasien');
    $oleh = $this->input->post('oleh');
    $catatan = $this->input->post('catatan');
    $tgl_final = $this->input->post('tgl_final');

    $data = array(
      'status' => 2,
      'oleh' => $oleh,
      'catatan' => $catatan,
      'tgl_final' => $tgl_final
    );


    // echo'<pre>';print_r($data);exit();

    $this->db->where('id', $id);
    $this->db->update('medis.tb_permintaan_rawat', $data);
  }

}