<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemakaianO2 extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pemakaianO2/PemakaianO2Model'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $historyPemakaianO2 = $this->PemakaianO2Model->historyPemakaianO2($nomr);
    // $nopen = $this->input->post('nopen');

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
    );

    $this->load->view('rekam_medis/rawat_inap/pemakaianO2/index', $data);
  }

  public function hasilInputPemakaianO2()
  {
    $nomr = $this->input->post('nomr');
    $nopen = $this->input->post('nopen');
    $nokun = $this->input->post('nokun');
    $historyPemakaianO2 = $this->PemakaianO2Model->historyPemakaianO2($nomr);

    $data = array(
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'historyPemakaianO2' => $historyPemakaianO2,
    );

    $this->load->view('rekam_medis/rawat_inap/pemakaianO2/hasilInputPemakaianO2', $data);
  }

  public function simpanPemakaian()
  {
    $post = $this->input->post();

    $data = array(
      'nomr' => isset($post["nomr"]) ? $post["nomr"] : "",
      'nokun' => isset($post["nokun"]) ? $post["nokun"] : "",
      'nama_oksigen' => isset($post["nmOksigenPemakaianO2"]) ? $post["nmOksigenPemakaianO2"] : "",
      'tgl_jam_pasang' => date('Y-m-d H:i:s', strtotime($post["tglJamPasangPemakaianO2"])),
      'flow' => isset($post["flowPemakaianO2"]) ? $post["flowPemakaianO2"] : 0,
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
    );

    $this->db->insert('keperawatan.tb_pemakaian_o2', $data);

  }

  public function updatePemakaian()
  {
    $post = $this->input->post();

    $data = array(
      'tgl_jam_lepas' => date('Y-m-d H:i:s', strtotime($post["tglJamLepasUpdatePemakaianO2"])),
      'jumlah_pemakaian' => isset($post["jumlahPemakaianO2"]) ? $post["jumlahPemakaianO2"] : 0,
      'oleh' => $this->session->userdata('id'),
      'status' => 2,
    );

    $this->db->where('tb_pemakaian_o2.id', $post["idPemakaianO2"]);
    $this->db->update('keperawatan.tb_pemakaian_o2', $data);
  }

  public function hapusPemakaian()
  {
    $post = $this->input->post();

    $data = array(
      'oleh' => $this->session->userdata('id'),
      'status' => 0,
    );

    $this->db->where('tb_pemakaian_o2.id', $post["id"]);
    $this->db->update('keperawatan.tb_pemakaian_o2', $data);
  }

}