<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BarangGudang extends CI_Controller {
	public function __construct()
	{
		parent::__construct();
		if ($this->session->userdata('logged_in') == false) {
			redirect('login');
		}

		if (!in_array(24, $this->session->userdata('akses'))) {
			redirect('login');
		}

		date_default_timezone_set("Asia/Bangkok");
		$this->load->model(array('inventory/Model_barang_gudang','inventory/Model_satuan','inventory/Model_permintaan','inventory/Model_barang'));
	}

	public function index()
	{
		$gudang     	= $this->Model_barang_gudang->tampilkan_gudang()->result();
		$barang     	= $this->Model_barang_gudang->tampl_barang()->result();
		$baranggudang    = $this->Model_barang_gudang->tampil_barang_gudang()->result();
		$data = array(
			'title'       	=> 'Halaman Input Barang',
			'isi'         	=> 'inventory/barang_gudang/barang_gudang',
			'gudang'		=>  $gudang,
			'barang'		=> 	$barang,
			'baranggudang'	=> 	$baranggudang,
		);
		$this->load->view('layout/wrapper',$data);
	}

	function insert()
	{
		if(isset($_POST['submit'])){
			$BARANG       =   $this->input->post('BARANG');
			$GUDANG   =   $this->input->post('GUDANG');
//echo "<pre>";print_r($_POST);exit();
			$sql = $this->db->query("SELECT BARANG FROM invenumum.barang_gudang where BARANG='$BARANG'");
			$cek_nama = $sql->num_rows();
			if ($cek_nama > 0) {
				$this->session->set_flashdata('warning', 'Nama barang sudah ada...!');
				redirect('inventory/BarangGudang');
			}else{
				$data           = 	array
				(   'GUDANG'  =>$GUDANG,
					'BARANG'  =>$BARANG,
				);
				$this->Model_barang_gudang->post($data);
				redirect('inventory/BarangGudang');
			}
		}
	}

	public function listBarangGudang()
	{
		$draw   = intval($this->input->get("draw"));
		$start  = intval($this->input->get("start"));
		$length = intval($this->input->get("length"));
		$listBarangGudang = $this->Model_barang_gudang->barangGudang();
    // echo "<pre>";print_r($listPegawai);exit();
		$data  = array();
		$no    =1;
		foreach($listBarangGudang->result() as $lp) {
			if($lp->STATUS == 1){
				$check = "checked" ;
			}else{
				$check = "" ;
			}
			$data[] = array(
				$no,
				$lp->NAMA,
				$lp->SATUAN,
				$lp->STOK,
				$lp->GUDANG,
				'<div class="checkbox checkbox-primary">
				<input type="checkbox" id="statusBarang'.$lp->ID.'" value="'.$lp->ID.'"  class="Dbarang" '.$check.'>
				<label for="statusBarang'.$lp->ID.'"></label>
				</div>',

				'<a href="#formeditbaranggudang" class="btn btn-md btn-block btn-info" data-toggle="modal" data-id="' . $lp->ID . '"><i class="fas fa-edit"></i> Ubah</a>',

			);
			$no++;
		}

		$output = array(
			"draw"            => $draw,
			"recordsTotal"    => $listBarangGudang->num_rows(),
			"recordsFiltered" => $listBarangGudang->num_rows(),
			"data"            => $data
		);
		echo json_encode($output);
	}


	public function ambildatabarang($id)
	{
		$databarang = $this->Model_barang_gudang->modaldatabaranggudang($id);
		$data = array();
		foreach ($databarang as $main) {
			$isi_array = array();
			$isi_array['ID_BARANG_GUDANG'] = $main['ID_BARANG_GUDANG'];
			$isi_array['ID_BARANG'] = $main['ID_BARANG'];
			$isi_array['BARANG'] 	= $main['BARANG'];
			$isi_array['ID_GUDANG'] = $main['ID_GUDANG'];
			$isi_array['GUDANG'] 	= $main['GUDANG'];
			$data[] = $isi_array;
		}
		return $data;
	}

	public function update()
	{
		$id = $this->input->post('ID');
		$data = array(
			'GUDANG'    => $this->input->post('GUDANG'),
		);
    //  echo "<pre>";print_r($data);exit();
		$this->Model_barang_gudang->update($id, $data);
		redirect('inventory/BarangGudang');
	}

	public function form_edit()
	{
		$id       		= $this->input->post('id');
		$isiModal 		= $this->ambildatabarang($id);
		$gudang     	= $this->Model_barang_gudang->tampilkan_gudang()->result();
		$barang			= $this->Model_barang_gudang->tampl_barang()->result();
		$baranggudang   = $this->Model_barang_gudang->tampil_barang_gudang()->result();
		$data     = array(
			'isiModal' 		=> $isiModal,
			'gudang'		=>  $gudang,
			'barang'		=> 	$barang,
			'baranggudang'	=> $baranggudang
		);
		$this->load->view('inventory/barang_gudang/modalEditBarangGudang', $data);
	}

	public function sBarangAktif()
	{
		$id = $this->input->post('id');

		$data = array(
			'STATUS' => 1,
		);

		$this->Model_barang_gudang->sBarangAktif($id,$data);
	}

	public function sBarangNonAktif()
	{
		$id = $this->input->post('id');

		$data = array(
			'STATUS' => 0,
		);

		$this->Model_barang_gudang->sBarangNonAktif($id,$data);
	}


}
