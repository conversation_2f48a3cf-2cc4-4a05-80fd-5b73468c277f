<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Model_so extends CI_Model
{
    protected $_table_name = 'invenumum.stok_opname_detil';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nopen' => array(
            'field' => 'ID_SO',
            'label' => 'Nomor SO',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>isi.',
                'numeric' => '%s Wajib <PERSON>.'
            ),
        ),
    );

    public function __construct()
    {
        parent::__construct();
    }

    function tampil_data()
    {
        $query = "SELECT * FROM invenumum.stok_opname";
        return $this->db->query($query);
    }

    // function tampil_data_client()
    // {
    //     $query = "SELECT sod.ID, br.NAMA,sa.DESKRIPSI SATUAN, bru.STOK, sod.MANUAL, sod.SISTEM
    //     from invenumum.stok_opname_detil sod 
    //     left join invenumum.barang_ruangan bru ON bru.ID=sod.BARANG_RUANGAN
    //     left join invenumum.barang br ON br.ID=bru.BARANG
    //     left join invenumum.satuan sa ON sa.ID=br.SATUAN
    //     where sod.STOK_OPNAME=212";
    //     return $this->db->query($query);
    // }

    function tampilkan_gudang()
    {
        $query  = "SELECT * from master.ruangan where ID like '%1060602%'";
        return $this->db->query($query);
    }

    function list_so()
    {
        $query  = $this->db->query("SELECT so.ID, so.TANGGAL, ru.DESKRIPSI GUDANG, so.`STATUS`
            from invenumum.stok_opname so
            left join master.ruangan ru ON so.GUDANG=ru.ID
            where so.`STATUS`!=0
            order by so.ID desc");
        return $query->result_array();
    }

    public function view()
    {
        return $this->db->get('invenumum.view_so')->result();
    }

    function data($id)
    {
        $query = "SELECT sd.ID, bm.BARANG NAMA, sd.AWAL, sa.SATUAN, sd.MANUAL, sd.STOK_OPNAME, bm.HARGA, bg.ID_BARANG_GUDANG
        from  invenumum.stok_opname_detil sd 
        left join invenumum.barang_gudang bg ON bg.ID_BARANG_GUDANG=sd.BARANG_RUANGAN  
        left join invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
        left join invenumum.satuan_ sa ON sa.ID_SATUAN=bm.SATUAN
        where sd.STOK_OPNAME=$id AND sd.`STATUS`=1 AND bm.`STATUS`=1
        ORDER BY bm.BARANG ASC";
        return $this->db->query($query);
    }

    function barang_gudang($id)
    {
        $query  = "SELECT bg.* FROM invenumum.barang_gudang bg 
        LEFT JOIN invenumum.barang_master bm ON bm.ID_BARANG=bg.BARANG
        WHERE bg.GUDANG='$id' AND bg.`STATUS`=1 AND bm.`STATUS`=1";
        return $this->db->query($query);
    }

    public function create_so($data, $data2)
    {
        $this->db->trans_start();
        $this->tambah_so_detil($data2);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function transaksi_stok($data)
    {
        $this->db->trans_start();
        $this->insert_transaksi($data);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    public function insert_transaksi($data)
    {
        $res = $this->db->insert_batch('invenumum.transaksi_stok_ruangan', $data);
        return $res;
    }

    public function tambah_so($data)
    {
        $this->db->insert('invenumum.stok_opname', $data);
        $id = $this->db->insert_id();
        return (isset($id)) ? $id : FALSE;
    }

    public function tambah_so_detil($data)
    {
        $res = $this->db->insert_batch('invenumum.stok_opname_detil', $data);
        return $res;
    }

    public function update_status($id_so)
    {
        $hsl = $this->db->query("UPDATE invenumum.stok_opname SET STATUS=2 WHERE ID='$id_so'");
        return $hsl;
    }

    public function batal_status($id)
    {
        $hsl = $this->db->query("UPDATE invenumum.stok_opname SET STATUS=1 WHERE ID='$id'");
        return $hsl;
    }

    public function hapus_status($id)
    {
        $hsl = $this->db->query("UPDATE invenumum.stok_opname SET STATUS=0 WHERE ID='$id'");
        return $hsl;
    }

    function updatejumlah($id, $field, $value)
    {
        // Update
        $data = array($field => $value);
        $this->db->where('ID', $id);
        $this->db->update('invenumum.stok_opname_detil', $data);
    }
}
