<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KesInvRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $post = $this->input->post();
    $nomr = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $penandaanSisiLokasi = $this->masterModel->referensi(415);
    $inforConTin = $this->masterModel->referensi(416);
    $inforConTinAnesSed = $this->masterModel->referensi(417);
    $pemPenunjangFoto = $this->masterModel->referensi(419);
    $pemPenunjangCtScan = $this->masterModel->referensi(420);
    $pemPenunjangUSG = $this->masterModel->referensi(421);
    $pemPenunjangMRI = $this->masterModel->referensi(422);
    $pemPenunjangLab = $this->masterModel->referensi(423);
    $persediaanDarah = $this->masterModel->referensi(425);
    $ketersediaanImplant = $this->masterModel->referensi(426);
    $benarSisiLokasiTin = $this->masterModel->referensi(427);
    $khususPerhatikan = $this->masterModel->referensi(428);
    $instrumenSudahBenar = $this->masterModel->referensi(429);
    $fotoRadiologiSesuai = $this->masterModel->referensi(430);
    $kelengkapanKasaJarum = $this->masterModel->referensi(431);
    $spesimenBeriLabel = $this->masterModel->referensi(432);
    $namaImplanDanLokasi = $this->masterModel->referensi(433);
    $peralatanYangPerlu = $this->masterModel->referensi(434);
    $masalahHarusPerhatikan = $this->masterModel->referensi(435);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $ruangan = 2;
    $historyKTI = $this->pengkajianAwalModel->historyKTI($nomr);

    $data = array(
      'ruangan'    => $ruangan,
      'getNomr' => $getNomr,
      'penandaanSisiLokasi' => $penandaanSisiLokasi,
      'inforConTin' => $inforConTin,
      'inforConTinAnesSed' => $inforConTinAnesSed,
      'pemPenunjangFoto' => $pemPenunjangFoto,
      'pemPenunjangCtScan' => $pemPenunjangCtScan,
      'pemPenunjangUSG' => $pemPenunjangUSG,
      'pemPenunjangMRI' => $pemPenunjangMRI,
      'pemPenunjangLab' => $pemPenunjangLab,
      'persediaanDarah' => $persediaanDarah,
      'ketersediaanImplant' => $ketersediaanImplant,
      'historyKTI' => $historyKTI,
      'benarSisiLokasiTin' => $benarSisiLokasiTin,
      'khususPerhatikan' => $khususPerhatikan,
      'instrumenSudahBenar' => $instrumenSudahBenar,
      'fotoRadiologiSesuai' => $fotoRadiologiSesuai,
      'kelengkapanKasaJarum' => $kelengkapanKasaJarum,
      'spesimenBeriLabel' => $spesimenBeriLabel,
      'namaImplanDanLokasi' => $namaImplanDanLokasi,
      'peralatanYangPerlu' => $peralatanYangPerlu,
      'masalahHarusPerhatikan' => $masalahHarusPerhatikan,
    );

    $this->load->view('Pengkajian/keselamatanTindakanInvasif/index', $data);
  }

  public function viewIndexRi($idNorm, $idNopen, $idNokun, $idLoad)
  {
    $post = $this->input->post();
    $penandaanSisiLokasi = $this->masterModel->referensi(415);
    $inforConTin = $this->masterModel->referensi(416);
    $inforConTinAnesSed = $this->masterModel->referensi(417);
    $pemPenunjangFoto = $this->masterModel->referensi(419);
    $pemPenunjangCtScan = $this->masterModel->referensi(420);
    $pemPenunjangUSG = $this->masterModel->referensi(421);
    $pemPenunjangMRI = $this->masterModel->referensi(422);
    $pemPenunjangLab = $this->masterModel->referensi(423);
    $persediaanDarah = $this->masterModel->referensi(425);
    $ketersediaanImplant = $this->masterModel->referensi(426);
    $benarSisiLokasiTin = $this->masterModel->referensi(427);
    $khususPerhatikan = $this->masterModel->referensi(428);
    $instrumenSudahBenar = $this->masterModel->referensi(429);
    $fotoRadiologiSesuai = $this->masterModel->referensi(430);
    $kelengkapanKasaJarum = $this->masterModel->referensi(431);
    $spesimenBeriLabel = $this->masterModel->referensi(432);
    $namaImplanDanLokasi = $this->masterModel->referensi(433);
    $peralatanYangPerlu = $this->masterModel->referensi(434);
    $masalahHarusPerhatikan = $this->masterModel->referensi(435);
    $getNomr = $this->pengkajianAwalModel->getNomr($idNokun);
    $ruangan = 2;
    $historyKTI = $this->pengkajianAwalModel->historyKTI($idNorm);
    $getKTI = $this->pengkajianAwalModel->getKTI($idLoad);

    $data = array(
      'ruangan'    => $ruangan,
      'getNomr' => $getNomr,
      'penandaanSisiLokasi' => $penandaanSisiLokasi,
      'inforConTin' => $inforConTin,
      'inforConTinAnesSed' => $inforConTinAnesSed,
      'pemPenunjangFoto' => $pemPenunjangFoto,
      'pemPenunjangCtScan' => $pemPenunjangCtScan,
      'pemPenunjangUSG' => $pemPenunjangUSG,
      'pemPenunjangMRI' => $pemPenunjangMRI,
      'pemPenunjangLab' => $pemPenunjangLab,
      'persediaanDarah' => $persediaanDarah,
      'getKTI' => $getKTI,
      'ketersediaanImplant' => $ketersediaanImplant,
      'historyKTI' => $historyKTI,
      'benarSisiLokasiTin' => $benarSisiLokasiTin,
      'khususPerhatikan' => $khususPerhatikan,
      'instrumenSudahBenar' => $instrumenSudahBenar,
      'fotoRadiologiSesuai' => $fotoRadiologiSesuai,
      'kelengkapanKasaJarum' => $kelengkapanKasaJarum,
      'spesimenBeriLabel' => $spesimenBeriLabel,
      'namaImplanDanLokasi' => $namaImplanDanLokasi,
      'peralatanYangPerlu' => $peralatanYangPerlu,
      'masalahHarusPerhatikan' => $masalahHarusPerhatikan,
    );

    $this->load->view('Pengkajian/keselamatanTindakanInvasif/index', $data);
  }

}