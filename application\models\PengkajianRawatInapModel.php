<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianRawatInapModel extends CI_Model
{

  public function total_pasienRi($user)
  {
    $query = $this->db->query("CALL keperawatan.DashboardRI($user)");
    return $query->result_array();
  }

  public function datatables_lp_ruangan($id = 0, $status = 0, $user = 0, $norm = 0, $limit = 0, $offset = 0)
  {
    $query = $this->db->query("CALL keperawatan.listPasienRI($user, $id, $status, $norm, $limit, $offset)");
    return $query->result();
  }

  public function listPasienDashboard($id = 0, $status = 0, $user = 0, $norm = 0, $limit = 0, $offset = 0)
  {
    $query = $this->db->query("CALL keperawatan.listPasienRI2($user, $id, $status, $norm, $limit, $offset)");
    return $query->result();
  }

  public function konsultasiBelumDijawab($norm)
  {
    $this->db->select('kon.tujuan, master.getNamaLengkapPegawai(dok.NIP) dokter, smf.DESKRIPSI smf');
    $this->db->from('medis.tb_konsul kon');
    $this->db->join('pendaftaran.kunjungan kun', 'kun.NOMOR = kon.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran pen', 'pen.NOMOR = kun.NOPEN', 'left');
    $this->db->join('master.dokter dok', 'dok.ID = kon.dokter_tujuan', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = dok.NIP', 'left');
    $this->db->join('master.referensi smf', 'smf.ID = kon.smf AND smf.JENIS = 26', 'left');
    $this->db->where('kon.status', 1);
    $this->db->where('pen.NORM', $norm);
    $query = $this->db->get();
    return $query->result();
  }
}

/* End of file PengkajianRawatInapModel.php */
/* Location: ./application/models/PengkajianRawatInapModel.php */