<?php
defined('BASEPATH') or exit('No direct script access allowed');

class HADSModel extends MY_Model
{
    public function getHADS($id)
    {
        $query = $this->db->query("
            SELECT * FROM db_master.referensi 
            WHERE id_referensi = '$id' AND status = 1
        ");
        return $query->row_array();
    }
    public function referensi($id)
    {
        $query = $this->db->query("
            SELECT * FROM db_master.variabel 
            WHERE id_referensi = '$id' AND status = 1
        ");
        return $query->result_array();
    }

    public function listHistoryHADS($nomr)
    {
        $query = $this->db->query("
        SELECT
        hads.*,
        master.getNamaLengkapPegawai (ap.NIP) OLEH,
        master.getNamaLengkapPegawai (ap1.NIP) OLEH_UPDATE                                 
        FROM
        medis.tb_hospital_anxiety_and_depression_scale hads
        LEFT JOIN aplikasi.pengguna ap ON ap.ID = hads.created_by 
        LEFT JOIN aplikasi.pengguna ap1 ON ap1.ID = hads.updated_by 
        WHERE
        hads.STATUS = 1 AND hads.norm = $nomr
        ");
        return $query;
    }
    public function getDataHADS($id)
    {
        $query = $this->db->query("SELECT
                                    hads.*
                                FROM
                                    medis.tb_hospital_anxiety_and_depression_scale hads
                                WHERE hads.id = $id AND hads.status = 1
                                ");
        return $query->row_array();
    }
}
/* End of file HADSModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/paliatif/HADSModel.php */
