<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RiraModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_rira';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  public function __construct()
  {
    parent::__construct();
  }

  public function replace($data)
  {
    $this->db->replace('keperawatan.tb_rira', $data);
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_rira.id', $id);
    $this->db->update('keperawatan.tb_rira', $data);
  }

  public function history($nokun, $param)
  {
    if ($param == 'jumlah') {
      $this->db->select('r.id');
    } elseif ($param == 'tabel') {
      $this->db->select(
        'r.id, r.tanggal, r.jam, r.pilihan, master.getNamaLengkapPegawai(peng.NIP) pengisi, r.updated_at, r.status'
      );
    }
    $this->db->from('keperawatan.tb_rira r');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = r.oleh', 'left');
    $this->db->where('r.nokun', $nokun);
    if ($param == 'jumlah') {
      $this->db->where('r.status', 1);
      $query = $this->db->get();
      return $query->num_rows();
    } elseif ($param == 'tabel') {
      $this->db->order_by('r.updated_at', 'DESC');
      $query = $this->db->get();
      return $query;
    } else {
      return null;
    }
  }

  public function detail($id)
  {
    $this->db->select(
      'r.id, r.tanggal, r.jam, r.pilihan, r.carsinoma_tiroid_iodium, r.ecog_iodium, r.hb_iodium, r.leukosit_iodium,
      r.trombosit_iodium, r.hamil_menyusui_iodium, r.thyrax_iodium, r.iodine_iodium, r.lpr_iodium, 
      r.efek_samping_iodium, r.meninggal_iodium, r.bone_scan_samarium, r.ecog_samarium, r.hb_samarium,
      r.leukosit_samarium, r.trombosit_samarium, r.kreatinin_samarium, r.hamil_menyusui_samarium, r.terapi_samarium,
      r.efek_samping_samarium, r.meninggal_samarium'
    );
    $this->db->from('keperawatan.tb_rira r');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = r.oleh', 'left');
    $this->db->where('r.id', $id);
    $query = $this->db->get();
    return $query->result_array();
  }
}