<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SurveilansModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s <PERSON>ajib <PERSON>.',
                        'numeric' => '%s <PERSON>ajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('survei.id, survei.nokun, survei.created_at tanggal, ru.DESKRIPSI ruangan, peng.NAMA user');
        $this->db->from('keperawatan.tb_surveilans survei');
        $this->db->join('pendaftaran.kunjungan kun','kun.NOMOR = survei.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','pen.NOMOR = kun.NOPEN','LEFT');
        $this->db->join('master.ruangan ru','kun.RUANGAN = ru.ID','LEFT');
        $this->db->join('aplikasi.pengguna peng','survei.oleh = peng.ID','LEFT');
        $this->db->where('survei.STATUS !=','0');
        $this->db->where('pen.NORM',$this->input->post('nomr'));
        $this->db->order_by('survei.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id_surveilans)
    {
      $query = $this->db->query(
        "SELECT * FROM keperawatan.tb_surveilans survei WHERE survei.`status` = 1 AND survei.id = '$id_surveilans'"
      );
      return $query->row_array();
    }

    public function getPengkajianOperasi($id_surveilans)
    {
      $query = $this->db->query(
        "SELECT survei.*, drain.variabel drain_label, asa_score.variabel asa_score_label
        , jo.variabel jenis_operasi_label, k.variabel kategori_operasi_label
        FROM keperawatan.tb_surveilans_operasi survei 
        LEFT JOIN db_master.variabel drain ON survei.drain = drain.id_variabel
        LEFT JOIN db_master.variabel asa_score ON survei.asa_score = asa_score.id_variabel
        LEFT JOIN db_master.variabel jo ON survei.jenis_operasi = jo.id_variabel
        LEFT JOIN db_master.variabel k ON survei.kategori_operasi = k.id_variabel
        WHERE survei.`status` = 1 AND survei.id_surveilans = '$id_surveilans'"
      );
      return $query->result_array();
    }

    public function getPengkajianAntibiotika($id_surveilans)
    {
      $query = $this->db->query(
        "SELECT survei.`*`, pemberian.variabel pemberian_antibiotika_label
        FROM keperawatan.tb_surveilans_antibiotika survei
        LEFT JOIN db_master.variabel pemberian ON survei.pemberian_antibiotika = pemberian.id_variabel 
        WHERE survei.`status` = 1 AND survei.id_surveilans = '$id_surveilans'"
      );
      return $query->result_array();
    }

}
