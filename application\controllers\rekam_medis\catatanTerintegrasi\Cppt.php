<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cppt extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(
            [
                'masterModel',
                'pengkajianAwalModel',
                'rekam_medis/rawat_inap/catatanTerintegrasi/CpptModel',
                'rekam_medis/KesadaranModel',
                'rekam_medis/TbBbModel',
                'rekam_medis/TandaVitalModel',
                'rekam_medis/rawat_inap/keperawatan/pemantauanNyeriModel'
            ]
        );
    }

    public function index()
    {

        $data = [
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'kesadaran' => $this->masterModel->referensi(5),
            'skriningNyeri' => $this->masterModel->referensi(7),
            'skalaNyeriNRS' => $this->masterModel->referensi(114),
            'skalaNyeriWBR' => $this->masterModel->referensi(115),
            'skalaNyeriFLACC' => $this->masterModel->referensi(123),
            'skalaNyeriBPS' => $this->masterModel->referensi(133),
            'efeksampingNRS' => $this->masterModel->referensi(118),
            'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
            'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
            'pengkajianNyeriTime' => $this->masterModel->referensi(12),
            'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
            'listDr' => $this->masterModel->listDr()
        ];

        $this->load->view('rekam_medis/rawat_inap/catatanTerintegrasi/cppt', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();
                // echo '<pre>';print_r($post);exit();
                $nomr = $post['nomr'];
                $nokun = $post['nokun'];
                $oleh = $this->session->userdata('id');
                $rules = $this->CpptModel->rules;
                $this->form_validation->set_rules($rules);

                if ($post['skrining_nyeri'] != 17 && $this->session->userdata('status') == 2) {
                    $this->form_validation->set_rules($this->CpptModel->rules_nyeri);
                }

                if ($this->session->userdata('status') == 2) {
                    $this->form_validation->set_rules($this->CpptModel->rules_skrining_nyeri);
                    $this->form_validation->set_rules($this->CpptModel->kesadaran);
                }

                // if($post['form_asuhan_keperawatan'] != 1471){
                //     $this->form_validation->set_rules($this->CpptModel->rules_asuhan_keperawatan);
                // }

                if ($this->form_validation->run() == true) {
                    $this->db->trans_begin();

                    // mulai data CPPT
                    $dataCppt = [
                        'nokun' => $nokun,
                        'subyektif' => $post['subyektif'],
                        'obyektif' => $post['obyektif'],
                        'instruksi' => $post['instruksi'],
                        'oleh' => $oleh,
                        'jenis' => 2,
                        'jenis_cppt' => 1,
                        'tanggal' => date('Y-m-d H:i:s')
                    ];
                    // akhir data CPPT

                    if ($this->session->userdata('status') == 2) { // Perawat
                        $dataKesadaran = [
                            'data_source' => 6,
                            'nomr' => $nomr,
                            'nokun' => $nokun,
                            'kesadaran' => $post['kesadaran'],
                            'oleh' => $oleh,
                        ];

                        $id_kesadaran = $post['id_kesadaran'];
                        if (empty($id_kesadaran)) {
                            $id_kesadaran = $this->KesadaranModel->insert($dataKesadaran);
                        }

                        $dataTbBb = [
                            'data_source' => 6,
                            'nomr' => $nomr,
                            'nokun' => $nokun,
                            'jenis' => 2,
                            'tb' => $post['tb'],
                            'bb' => $post['bb'],
                            'oleh' => $oleh,
                        ];

                        $id_tb_bb = $post['id_tb_bb'];
                        if (empty($id_tb_bb)) {
                            $id_tb_bb = $this->TbBbModel->insert($dataTbBb);
                        }

                        $dataTandaVital = [
                            'data_source' => 6,
                            'nomr' => $nomr,
                            'nokun' => $nokun,
                            'td_sistolik' => $post['sistolik'],
                            'td_diastolik' => $post['diastolik'],
                            'pernapasan' => $post['pernapasan'],
                            'nadi' => $post['nadi'],
                            'suhu' => $post['suhu'],
                            'oleh' => $oleh,
                        ];

                        $id_tanda_vital = $post['id_tanda_vital'];
                        if (empty($id_tanda_vital)) {
                            $id_tanda_vital = $this->TandaVitalModel->insert($dataTandaVital);
                        }

                        $dataPemantauanNyeri = [
                            'nokun' => $post['nokun'],
                            'data_source' => 6,
                            'metode' => $post['skrining_nyeri'],
                            'skor' => $post['skrining_nyeri'] != 17 ? $post['skor_nyeri'] : null,
                            'created_by' => $oleh,
                        ];

                        $id_nyeri = $post['id_nyeri'];
                        if (empty($id_nyeri)) {
                            $id_nyeri = $this->pemantauanNyeriModel->insert($dataPemantauanNyeri);
                        }

                        // mulai tambah data CPPT
                        $dataCppt['pemberi_cppt'] = 2;
                        $dataCppt['kesadaran'] = $id_kesadaran;
                        $dataCppt['tb_bb'] = $id_tb_bb;
                        $dataCppt['tanda_vital'] = $id_tanda_vital;
                        $dataCppt['skrining_nyeri'] = $id_nyeri;
                        $dataCppt['dokter_tbak'] = $post['tbakDpjpp'];
                        // akhir tambah data CPPT
                    } else {
                        // mulai tambah data CPPT
                        $dataCppt['pemberi_cppt'] = 1;
                        $dataCppt['analisis'] = $post['analisis'];
                        $dataCppt['perencanaan'] = $post['perencanaan'];
                        // akhir tambah data CPPT
                    }

                    if (!empty($post['id'])) {
                        $id_cppt = $post['id'];
                        $this->CpptModel->update($dataCppt, ['id' => $id_cppt]);
                    } else {
                        $id_cppt = $this->CpptModel->insert($dataCppt);
                    }

                    // mulai ubah data pasien
                    if ($this->session->userdata('status') == 2) {
                        if (empty($post['id_kesadaran'])) {
                            $this->KesadaranModel->update(['ref' => $id_cppt], ['id' => $id_kesadaran]);
                        }
                        if (empty($post['id_tb_bb'])) {
                            $this->TbBbModel->update(['ref' => $id_cppt], ['id' => $id_tb_bb]);
                        }
                        if (empty($post['id_tb_bb'])) {
                            $this->TandaVitalModel->update(['ref' => $id_cppt], ['id' => $id_tanda_vital]);
                        }
                        if (empty($post['id_nyeri'])) {
                            $this->pemantauanNyeriModel->update(['ref' => $id_cppt], ['id' => $id_nyeri]);
                        }

                        $this->db->delete('keperawatan.tb_cppt_perencanaan_asuhan_keperawatan', ['id_cppt' => $id_cppt]);
                        $dataPerencanaanAsuhan = [];
                        $index = 0;
                        if (isset($post['asuhankeperawatancppt'])) {
                            foreach ($post['asuhankeperawatancppt'] as $input) {
                                if ($post['asuhankeperawatancppt'][$index] != "") {
                                    array_push(
                                        $dataPerencanaanAsuhan,
                                        [
                                            'id_cppt' => $id_cppt,
                                            'id_asuhan_keperawatan_detil' => $post['asuhankeperawatancppt'][$index],
                                        ]
                                    );
                                }
                                $index++;
                            }
                            $this->db->insert_batch('keperawatan.tb_cppt_perencanaan_asuhan_keperawatan', $dataPerencanaanAsuhan);
                        }
                    }
                    // akhir ubah data pasien

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = ['status' => 'failed'];
                    } else {
                        $this->db->trans_commit();
                        $result = ['status' => 'success'];
                    }
                } else {
                    $result = [
                        'status' => 'failed',
                        'errors' => $this->form_validation->error_array()
                    ];
                }
                echo json_encode($result);
            } else if ($param == 'ambil') {
                $post = $this->input->post();
                $dataPemantauanNyeri = $this->CpptModel->get_table();

                echo json_encode(
                    [
                        'status' => 'success',
                        'data' => $dataPemantauanNyeri
                    ]
                );
            } else if ($param == 'count') {
                $result = $this->CpptModel->get_count();
                echo json_encode($result);
            } else if ($param == 'verif') {
                $this->db->trans_begin();
                $data = [
                    'status_verif' => 1,
                    'verif_oleh' => $this->session->userdata('id'),
                    'tanggal_verif' => date('Y-m-d H:i:s')
                ];

                $this->CpptModel->update(
                    $data,
                    ['id' => $this->input->post('id')]
                );

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
                echo json_encode($result);
            }
        }
    }

    public function datatables()
    {
        $result = $this->CpptModel->datatables();

        $data = [];
        foreach ($result as $row) {

            $verif = "<h6 style='text-align: center; vertical-align: middle;'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
            $cetak = "<h6 style='text-align: center; vertical-align: middle;'><i class='fa fa-minus' aria-hidden='true'></i></h6>";
            $lihat = "<h6 style='text-align: center; vertical-align: middle;'><i class='fa fa-minus' aria-hidden='true'></i></h6>";

            if ($row->jenis == 2 && $row->pemberi_cppt == 2) {
                $verif = "<h4 style='text-align: center; vertical-align: middle;'><i class='fa fa-check' aria-hidden='true'></i></h4>";
                if ($row->status_verif == 0) {
                    $verif = "<h4 style='text-align: center; vertical-align: middle;'><i class='fa fa-times' aria-hidden='true'></i></h4>";
                }
            }

            if ($row->JENIS_KUNJUNGAN == 1) {
                $cetak = "<a href='/reports/simrskd/cppt/cpptnew.php?format=pdf&id=" . $row->IDCPPT . "' class='btn btn-warning btn-block btn-sm' target='_blank'><i class='fa fa-print'></i> Cetak</a>";
                if ($row->IDRUANGAN == 105120101) {
                    $cetak = "<a href='/reports/simrskd/cppt/cpptRadioterapinew.php?format=pdf&id=" . $row->IDCPPT . "' class='btn btn-warning btn-block btn-sm' target='_blank'><i class='fa fa-print'></i> Cetak</a>";
                } elseif ($row->IDRUANGAN == 105110101) {
                    $cetak = "<a href='/reports/simrskd/cppt/cpptrehab.php?format=pdf&nokun=" . $row->nokun . "&jenis=" . $row->JENIS_CPPT . "' class='btn btn-warning btn-block btn-sm' target='_blank'><i class='fa fa-print'></i> Cetak</a>";
                }
            }

            if ($row->JENIS_KUNJUNGAN != 1) {
                $lihat = "<a class='btn btn-primary btn-block btn-sm history_cppt' data-id='" . $row->IDCPPT . "'><i class='fa fa-eye'></i> Lihat</a>";
            }

            $sub_array = [];
            $sub_array[] = $lihat;
            $sub_array[] = $cetak;
            $sub_array[] = $verif;
            $sub_array[] = $row->tanggal;
            $sub_array[] = $row->RUANGAN;
            $sub_array[] = $row->PROFESI;
            $sub_array[] = $row->NAMAPEGAWAI;
            $sub_array[] = $row->DOKTERDPJP;

            $data[] = $sub_array;
        }

        $output = [
            'draw' => intval($_POST['draw']),
            'recordsTotal' => $this->CpptModel->total_count(),
            'recordsFiltered' => $this->CpptModel->filter_count(),
            'data' => $data
        ];
        echo json_encode($output);
    }
}

// End of file CPPT.php
// Location: ./application/models/rekam_medis/rawat_inap/catatanTerintegrasi/CPPT.php