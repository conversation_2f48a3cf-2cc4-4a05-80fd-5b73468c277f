<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SerahTerimaModel extends MY_Model{
	protected $_table_name = 'keperawatan.tb_serah_terima_shift_igd';
	protected $_primary_key = 'ID';
	protected $_order_by = 'tanggal';
	protected $_order_by_type = 'DESC';

	public $rules = array(
		'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
		)	
    );

	function __construct(){
		parent::__construct();
    }
    
    function table_query()
    {
        $this->db->select('kp.id ID,kp.nokun NOKUN, kp.tanggal TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , `master`.getNamaLengkapPegawai(kp.yangMenerima) MENERIMA');
        $this->db->from('keperawatan.tb_serah_terima_shift_igd kp');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = kp.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = kp.oleh','LEFT');

        $this->db->where('kp.STATUS !=','0');
        $this->db->order_by('kp.tanggal', 'DESC');

        if($this->input->post('nomr')){
            $this->db->where('p.NORM',$this->input->post('nomr'));
        }

        if($this->input->post('id')){
            $this->db->where('kp.id',$this->input->post('id'));
        }

        if($this->input->post('nokun')){
            $this->db->where('kp.nokun',$this->input->post('nokun'));
        }

        if($this->input->post('nopen')){
            $this->db->where('pk.nopen',$this->input->post('nopen'));
        }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
