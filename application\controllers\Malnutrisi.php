<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Malnutrisi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'MalnutrisiModel',
        'PengkajianRawatInapModel',
      )
    );
  }

  public function dashboard()
  {
    $data = array(
      'title' => 'Dashboard Malnutrisi',
      'isi' => 'Malnutrisi/dashboard',
      'statusPengguna' => $_SESSION['status'],
      'idPengguna' => $this->session->userdata('id'),
      'data' => $this->MalnutrisiModel->dashboard(),
    );
    // echo '<pre>';print_r($data);exit();

    $this->load->view('layout/wrapper', $data);
  }

  public function pasien()
  {
    $idRuang = $this->uri->segment(3);
    $data = array(
      'titile' => 'Pasien Malnutrisi',
      'isi' => 'Malnutrisi/pasien',
      'idRuang' => $idRuang,
      'listPasien' => $this->MalnutrisiModel->pasien($idRuang),
    );
    // echo '<pre>';print_r($data);exit();

    $this->load->view('layout/wrapper', $data);
  }
}

/* End of file Malnutrisi.php */
/* Location: ./application/controllers/Malnutrisi.php */