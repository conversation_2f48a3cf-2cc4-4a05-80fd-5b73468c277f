<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RkaklRuanganModel extends MY_Model{
	protected $_table_name = 'db_keuangan.rkakl_instalasi';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
    protected $_order_by_type = 'DESC';


	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('r.ID,mi.DESKRIPSI, r.VOLUME, r.PAGU');
        $this->db->from('db_keuangan.rkakl_instalasi r');
        $this->db->join('db_keuangan.master_instalasi mi', 'mi.ID=r.ID_INSTALASI', 'left');
        if($this->input->get('id')){
          $this->db->where('r.ID_RKAKL', $this->input->get('id'));
        }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
