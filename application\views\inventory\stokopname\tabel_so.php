<div class="row">
	<div class="col-sm-12">
		<h4 class="page-title">Form Stock Opname</h4>
	</div>
</div>

<body>
	<!-- end page title -->
	<div class="row">
		<div class="col-12">
			<div class="card-box">
				<form method="POST" id="form_so">
					<table class="table table-striped table-bordered" id="">
						<thead>
							<tr>
								<th>No.</th>
								<th>Nama Barang</th>
								<th>Satuan</th>
								<th>Harga</th>
								<th>Stok Awal</th>
								<th>Stok opname</th>
							</tr>
						</thead>
						<tbody>
							<?php
							$no = 1;
							foreach ($barang->result() as $baris) :
							?>
								<tr>
									<th><?php echo $no++; ?></th>
									<td><?php echo $baris->NAMA; ?></td>
									<td><?php echo $baris->SATUAN; ?></td>
									<td><?php echo $baris->HARGA; ?></td>
									<td width="100px"><?php echo $baris->AWAL; ?></td>
									<td width="120px">
										<input type="hidden" value="<?php echo $baris->ID; ?>" name="ID[]" class="form-control" placeholder="STOK">
										<input type="hidden" name="ID_SO" value="<?php echo $baris->STOK_OPNAME; ?>">
										<input type="hidden" name="STOK[]" value="<?php echo $baris->AWAL; ?>">
										<input type="hidden" name="HARGA[]" value="<?php echo $baris->HARGA; ?>">
										<input type="text" class="form-control" data-id='<?php echo $baris->ID; ?>' data-field='MANUAL' name="MANUAL[]" value='<?php echo $baris->MANUAL; ?>'>
										<input type="hidden" name="BARANG[]" value="<?php echo $baris->ID_BARANG_GUDANG; ?>">
									</td>
								</tr>
							<?php
							endforeach;
							?>
						</tbody>
					</table>
					<br>
					<div class="row">
						<div class="col-12">
							<button type="submit" class="btn btn-sm btn-info pull-right simpan_so">
								<span class="fas fa-save"></span> Simpan SO
							</button>
						</div>
					</div>
				</form>
			</div><!-- end col -->
		</div>
	</div>

	<script>
		$(document).ready(function() {
			$('#barang_gudang').DataTable({
				responsive: true
			});
		});
	</script>

	<!-- Script -->
	<script type="text/javascript">
		$(document).ready(function() {

			// On text click
			// $('.edit').click(function() {
			// 	// Hide input element
			// 	$('.txtedit').hide();

			// 	// Show next input element
			// 	$(this).next('.txtedit').show().focus();

			// 	// Hide clicked element
			// 	$(this).hide();
			// });

			// Focus out from a textbox
			$('input').focusout(function() {
				// Get edit id, field name and value
				var edit_id = $(this).data('id');
				var fieldname = $(this).data('field');
				var value = $(this).val();

				// assign instance to element variable
				var element = this;

				// Send AJAX request
				$.ajax({
					url: '<?= base_url() ?>inventory/StokOpname/updatejumlah',
					type: 'post',
					data: {
						field: fieldname,
						value: value,
						id: edit_id
					},
					success: function(response) {

					// 	// Hide Input element
					// 	$(element).hide();

					// 	// Update viewing value and display it
					// 	$(element).prev('.edit').show();
					// 	$(element).prev('.edit').text(value);
					}
				});
			});
		});
	</script>

	<script>
		$('.simpan_so').on('click', function(e) {
			e.preventDefault();

			alertify.confirm('Data disimpan', 'Pilih Ok, jika setuju disimpan',
				function() {
					//var action = $('#form_so').attr('action');
					var form = $('#form_so').serialize();

					$.ajax('<?= base_url() ?>inventory/StokOpname/simpan_so/', {

						dataType: 'json',
						type: 'POST',
						data: form,

						success: function(data) {
							if (data.status == 'success') {
								toastr.success('Berhasil Disimpan');
								location.href = "../../StokOpname";
							} else {
								$.each(data.errors, function(index, element) {
									toastr.warning(element);
								});
							}
						},
						error: function(jqXHR, textStatus, errorThrown) {
							toastr.error('Internal Server Error!');
						}
					});
				},
				function() {
					alertify.error('Cancel')
				});
		});
	</script>