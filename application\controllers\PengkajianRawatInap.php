<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianRawatInap extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'EresepModel',
        'hemodialisaModel',
        'geriatri/InstrumenMNA_Model',
        'laporanModel',
        'PengkajianRawatInapModel',
        'konsultasi/KonsultasiModel',
      )
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $data = array(
      'title' => 'Rekam Medik Pasien',
      'isi' => 'PengkajianRi/index',
      'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function dashboard()
  {
    $statusPengguna = $_SESSION['status'];
    $id_pengguna = $this->session->userdata('id');
    $user = $statusPengguna == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    $totalPasienRi = $this->PengkajianRawatInapModel->total_pasienRi($user);
    // echo "<pre>";print_r($statusPengguna);exit();
    $data = array(
      'title'          => 'Halaman Dashboard Rawat Inap',
      'isi'            => 'PengkajianRi/dashboard',
      'statusPengguna' => $statusPengguna,
      'id_pengguna'    => $id_pengguna,
      'totalPasienRi'  => $totalPasienRi,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function listPasienRi()
  {
    $id_ruangan = $this->uri->segment(3);

    $data = array(
      'title'      => 'Halaman List Pasien Rawat Inap',
      'isi'        => 'PengkajianRi/listPasienRi',
      'id_ruangan' => $id_ruangan,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function ListPasienRawatInap()
  {
    $draw = intval($this->input->post('draw'));
    $idRuangan = $this->uri->segment(3);
    $status = $this->input->post('status');
    $user = $this->session->userdata('status') == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    $listPasien = $this->PengkajianRawatInapModel->listPasienDashboard($idRuangan, $status, $user);
    $konsultasi = null;
    $data = array();

    foreach ($listPasien as $lp) {
      // Mulai cek konsultasi
      if (isset($lp->KONSUL_BLM_JAWAB)) {
        $konsultasi = "<button type='button' href='#modal-konsultasi-pasien-ri' class='btn btn-sm btn-block btn-info waves-effect tbl-konsultasi-pasien-ri' data-toggle='modal' data-id='" . $lp->NORM . "'><i class='fa fa-eye'></i> Lihat</button>";
      } else {
        $konsultasi = '-';
      }
      // Akhir cek konsultasi

      $data[] = array(
        "<a href='" . base_url("medis/") . $lp->NOKUN . "' class='btn btn-custom btn-sm btn-block waves-effect'><i class='fa fa-check'></i></a>",
        $lp->NORM,
        $lp->NAMA_PASIEN,
        $lp->KAMAR,
        $lp->DOKTER_TUJUAN,
        date('d-m-Y, H:i:s', strtotime($lp->TGLMASUK)),
        isset($lp->ID_EMR_MEDIS) ? "<i class='fa fa-check'></i>" : '-',
        isset($lp->ID_EMR_PERAWAT) ? "<i class='fa fa-check'></i>" : '-',
        $lp->STATUS_VERIF_PENGKAJIAN_PERAWAT_OLEH_DOKTER == 1 ? "<i class='fa fa-check'></i>" : '-',
        isset($lp->CPPT_DOKTER) ? "<i class='fa fa-check'></i>" : '-',
        isset($lp->CPPT_PERAWAT) ? "<i class='fa fa-check'></i>" : '-',
        $lp->STATUS_VERIF != 0 ? "<i class='fa fa-check'></i>" : '-',
        isset($lp->PLAN_OF_CARE) ? $lp->PLAN_OF_CARE : '-',
        $konsultasi,
      );
    }

    $output = array(
      'draw' => $draw,
      'data' => $data
    );
    echo json_encode($output);
  }

  public function KonsultasiBelumDijawab()
  {
    $post = $this->input->post();
    $data = array('norm' => $post['norm']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('PengkajianRi/konsultasiBelumDijawab', $data);
  }

  public function TabelKonsultasiBelumDijawab()
  {
    $draw = intval($this->input->post('draw'));
    $norm = $this->input->post('norm');
    $tabel = $this->PengkajianRawatInapModel->konsultasiBelumDijawab($norm);
    $data = array();
    $no = 1;
    $jenisTujuan = null;
    $tujuan = null;
    // echo '<pre>';print_r($tabel);exit();

    foreach ($tabel as $t) {
      // Mulai tujuan
      if ($t->tujuan == 1) {
        $jenisTujuan = 'Dokter';
        $tujuan = $t->dokter;
      } elseif ($t->tujuan == 2) {
        $jenisTujuan = 'SMF';
        $tujuan = $t->smf;
      }
      // Akhir tujuan

      $data[] = array(
        $no++,
        $jenisTujuan,
        $tujuan,
      );
    }

    $output = array(
      'draw' => $draw,
      'data' => $data
    );
    echo json_encode($output);
  }

  public function listPasien()
  {
    $id_ruangan = $this->input->post('ruangan');
    $status = $this->input->post('status');
    $user = $this->session->userdata('status') == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    // $limit = 5;
    // $offset = ceil(1 * $limit);
    $norm = $this->input->post('norm');
    $dataPasien = $this->PengkajianRawatInapModel->datatables_lp_ruangan($id_ruangan, $status, $user, $norm);

    echo json_encode(array(
      'status' => 'success',
      'data' => $dataPasien,
      'total' => count($dataPasien)
    ));
  }

  public function ruangan()
  {
    $statusPengguna = $_SESSION['status'];
    $user = $statusPengguna == 1 && $this->session->userdata('smf') != 31 && $this->session->userdata('smf') != 26 ? $this->session->userdata('id') : 0;
    $listPasien = $this->PengkajianRawatInapModel->total_pasienRi($user);

    $data = array();

    foreach ($listPasien as $list) {
      $sub_array = array();
      $sub_array['ID'] = $list['ID'];
      $sub_array['DESKRIPSI'] = $list['DESKRIPSI'];
      $sub_array['JUMLAH_PASIEN'] = $list['JUMLAH_PASIEN'];

      $data[] = $sub_array;
    }

    echo json_encode($data);
  }
}

/* End of file PengkajianRawatInap.php */
/* Location: ./application/controllers/PengkajianRawatInap.php */