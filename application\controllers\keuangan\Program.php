<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Program extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('keuangan/ProgramModel'));
    }

    public function index() {

        $data = array(
            'title'       => 'Halaman Master Program',
            'isi'         => 'Keuangan_new/master/program'
        );
      
        $this->load->view('layout/wrapper',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'get'){
                $program = $this->ProgramModel->get_table(FALSE);

                $data = array();
                foreach($program as $dataProgram){
                    $sub_array = array();
                    $sub_array['id'] = $dataProgram -> MAK;
                    $sub_array['text'] = $dataProgram -> URAIAN . ' [ ' . $dataProgram -> MAK . ' ]';

                    $data[] = $sub_array;
                }

                echo json_encode($data);
            }
    	}
    }

    public function datatables(){
        $result = $this->ProgramModel->datatables();
        $no=1;
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $row -> MAK;
            $sub_array[] = $row -> URAIAN;      
            $sub_array[] = $row -> LEVEL;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->ProgramModel->total_count(),
            "recordsFiltered"   => $this->ProgramModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}