<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengkajianRiraModel extends MY_Model {
  protected $_table_name = 'keperawatan.tb_keperawatan';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor <PERSON>',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>ka.'
      ),
    ),
  );

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('pk.NOMOR NOKUN
        , rk.ID ID_RUANGAN
        , rk.DESKRIPSI RUANGAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , kp.id_emr ID_EMR_PERAWAT
        , kp.created_at TANGGAL_PENGKAJIAN_PERAWAT
        , master.getNamaLengkapPegawai(peng.NIP) USER_PERAWAT
        , md.id_emr ID_EMR_MEDIS
        , md.created_at TANGGAL_PENGKAJIAN_MEDIS
        , master.getNamaLengkapPegawai(pemed.NIP) USER_MEDIS
        , p.NOMOR NOPEN
        , p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , kp.created_by ID_USER_PERAWAT
        , md.created_by ID_USER_MEDIS
        , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
        , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18, 2, 1) USIA
        , kp.jenis JENIS_PENGKAJIAN_PERAWAT
        , md.jenis JENIS_PENGKAJIAN_MEDIS
        ,HOUR(TIMEDIFF(NOW(),md.created_at)) DURASI_MEDIS,IF(HOUR(TIMEDIFF(NOW(),md.created_at))<=24,1,0) STATUS_EDIT_MEDIS
        ,HOUR(TIMEDIFF(NOW(),kp.created_at)) DURASI_PERAWAT,IF(HOUR(TIMEDIFF(NOW(),kp.created_at))<=24,1,0) STATUS_EDIT_PERAWAT');
    $this->db->from('pendaftaran.kunjungan pk');
    $this->db->join('keperawatan.tb_keperawatan kp','pk.NOMOR = kp.nokun AND kp.flag=1 AND kp.`status`=1','LEFT');
    $this->db->join('medis.tb_medis md','pk.NOMOR = md.nokun AND md.flag=1 AND md.`status`=1','LEFT');
    $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
    $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
    $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
    $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
    $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
    $this->db->join('master.pasien pas','pas.NORM = p.NORM','LEFT');
    $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
    $this->db->join('master.ruangan rp','rp.ID = tp.RUANGAN','LEFT');
    $this->db->join('master.referensi refpj','refpj.ID = pj.JENIS AND refpj.JENIS=10','LEFT');
    $this->db->join('aplikasi.pengguna peng','peng.ID = kp.created_by','LEFT');
    $this->db->join('aplikasi.pengguna pemed','pemed.ID = md.created_by','LEFT');
    

    $this->db->where('p.NORM',$this->input->post('nomr'));
    $this->db->where("(kp.id_emr IS NOT NULL OR md.id_emr IS NOT NULL)");
    $this->db->where("(kp.jenis!=6 OR md.jenis!=6)");
    $this->db->group_by('pk.NOMOR');
    $this->db->order_by('pk.MASUK ', 'DESC');
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }

  public function getNomrRiim($nopen)
    {
        $query = $this->db->query(
            "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
        , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , pas.JENIS_KELAMIN ID_JK
        , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
        , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
        , p.TANGGAL TANGGAL_DAFTAR
        , r.JENIS_KUNJUNGAN
        , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
        , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
        ) DESKRIPSI_DIAGNOSA_MASUK
        , ref.ID IDPENJAMIN
        , ref.DESKRIPSI PENJAMIN
        , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
        dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
        )))) STATUS_KUNJUNGAN
        , penggu.ID ID_USER
        , dok.ID ID_DOKTER
        , pas.TANGGAL_LAHIR
        , dtt.NAME_PIC
        , pk.REF
        , mkp.NOMOR NOTLPN
        ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) ID_TUJUAN_FARMASI

        , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) TUJUAN_FARMASI
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
        , ppk.NAMA RUJUKAN_DARI
        , refdar.DESKRIPSI GOL_DARAH
        , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=13
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_KEPERAWATAN_DEWASA_RI
        , (SELECT id_emr FROM medis.tb_medis kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=13
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_MEDIS_DEWASA_RI

        FROM pendaftaran.pendaftaran p
        LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
        LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
        LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND refdar.JENIS=6
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
        LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
        LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM

        WHERE tp.`STATUS` not in (0,1)
        AND p.`STATUS`!= 0
        AND pk.`STATUS` != 0 AND p.NOMOR = '$nopen'
        GROUP BY dtt.ID #DESC"
        );
        return $query->row_array();
    }

    // Get Pengkajian Rawat Inap IGD
public function getPengkajian($idemr)
{
  $query = $this->db->query(
    'SELECT kp.id_emr ID_EMR
    , master.getNamaLengkapPegawai(peng.NIP) USER
    , p.NOMOR NOPEN
    , p.TANGGAL TANGGAL_DAFTAR
    , rp.DESKRIPSI RUANGAN_PENDAFTARAN, refpj.DESKRIPSI PENJAMIN
    , dm.ICD DIAGNOSA_MASUK
    , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
    , pk.NOMOR KUNJUNGAN, pk.MASUK TANGGAL_MASUK_RUANGAN
    , pk.KELUAR TANGGAL_KELUAR_RUANGAN
    , rk.DESKRIPSI RUANGAN_KUNJUNGAN, p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
    , IF(pas.JENIS_KELAMIN = 1,
    "Laki-Laki",
    "Perempuan") JK
    , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
    , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
    , TIMEDIFF(NOW()
    , (SELECT kep.created_at
    FROM keperawatan.tb_keperawatan kep
    LEFT JOIN pendaftaran.pendaftaran pen ON pen.NOMOR = kep.nopen
    WHERE kep.status = 1 AND pen.NORM = p.NORM
    ORDER BY kep.created_at DESC
    LIMIT 1)) DURATION_KEPERAWATAN
    , IF(HOUR(TIMEDIFF(NOW(),MAX(kp.created_at)))<=24=1,1,IF(kp.flag=1,0,1)) STATUS_EDIT
    , kp.created_at TANGGAL_PEMBUATAN_EMR
    , kp.rujukan, kp.diagnosa_masuk DIAGNOSA_MASUK_PERMINTAAN_DIRAWAT
    , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dm.ICD LIMIT 1) DESKRIPSI_DIAGNOSA_MASUK
    , (SELECT anam.id_auto_allo FROM keperawatan.tb_anamnesa_perawat anam
    WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
    LIMIT 1) ID_AUTO_ALLO
    , (SELECT var.variabel FROM keperawatan.tb_anamnesa_perawat anam
    LEFT JOIN db_master.variabel var ON var.id_variabel = anam.id_auto_allo
    WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
    LIMIT 1) AUTO_ALLO
    , (SELECT anam.hubungan_dengan_pasien FROM keperawatan.tb_anamnesa_perawat anam
    WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
    LIMIT 1) HUBUNGAN_DENGAN_PASIEN
    , (SELECT anam.allo_nama FROM keperawatan.tb_anamnesa_perawat anam
    WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
    LIMIT 1) ALLO_NAMA
    , (SELECT anam.info_dari_keluarga_pasien FROM keperawatan.tb_anamnesa_perawat anam
    WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
    LIMIT 1) INFO_DARI_KELUARGA_PASIEN

    , pas.GOLONGAN_DARAH
    , refgol.DESKRIPSI
    , pf.rhesus RHESUS

    , (SELECT tb.id FROM db_pasien.tb_tb_bb tb
    WHERE tb.data_source=13 AND tb.`status`=1
    AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
    ORDER BY tb.created_at DESC
    LIMIT 1) ID_TB_BB
    , (SELECT tb.tb FROM db_pasien.tb_tb_bb tb
    WHERE tb.data_source=13 AND tb.`status`=1
    AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
    ORDER BY tb.created_at DESC
    LIMIT 1) TB
    , (SELECT tb.bb FROM db_pasien.tb_tb_bb tb
    WHERE tb.data_source=13 AND tb.`status`=1
    AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
    ORDER BY tb.created_at DESC
    LIMIT 1) BB
    , (SELECT tb.jenis FROM db_pasien.tb_tb_bb tb
    WHERE tb.data_source=13 AND tb.`status`=1
    AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
    ORDER BY tb.created_at DESC
    LIMIT 1) JENIS_TB_BB
    , rke.riwayat_ekstravasasi RIWAYAT_EKSTRAVASASI
    , rke.isi_ekstravasasi ISI_EKSTRAVASASI
    , rke.pilih_ekstravasasi_foto PILIH_EKSTRAVASASI_FOTO
    , rke.ekstravasasi_depan EKSTRAVASASI_DEPAN
    , rke.ekstravasasi_belakang EKSTRAVASASI_BELAKANG
    , rke.hasil_laboratorium HASIL_LABORATORIUM
    , rke.isi_laboratorium ISI_LABORATORIUM
    , rke.hasil_BMP HASIL_BMP
    , rke.isi_BMP ISI_BMP
    , rke.kemoterapi KEMOTERAPI
    , rke.isi_kemoterapi ISI_KEMOTERAPI
    , rke.tindakan_perawatan TINDAKAN_PERAWATAN
    , rke.perawatan_lain PERAWATAN_LAIN
    , rke.riwayat_graft RIWAYAT_GRAFT

    , rke.rwyt_pngbtn_sblmnya_rira RWYT_PNGBTN_SBLMNYA_RIRA
    , rke.rwyt_pngbtn_sblmnya_nal_kapan_rira RWYT_PNGBTN_SBLMNYA_NAL_KAPAN_RIRA
    , rke.rwyt_pngbtn_sblmnya_nal_dosis_rira RWYT_PNGBTN_SBLMNYA_NAL_DOSIS_RIRA
    , rke.rwyt_pngbtn_sblmnya_samarium_kapan_rira RWYT_PNGBTN_SBLMNYA_SAMARIUM_KAPAN_RIRA
    , rke.rwyt_pngbtn_sblmnya_samarium_dosis_rira RWYT_PNGBTN_SBLMNYA_SAMARIUM_DOSIS_RIRA
    , rke.rwyt_pngbtn_sblmnya_lainnya_rira RWYT_PNGBTN_SBLMNYA_LAINNYA_RIRA

    , rke.klhn_yg_dialami_slm_ablasi_rira KLHN_YG_DIALAMI_SLM_ABLASI_RIRA
    , rke.klhn_yg_dialami_slm_ablasi_lainnya_rira KLHN_YG_DIALAMI_SLM_ABLASI_LAINNYA_RIRA

    , pf.rwyt_pngbtn_radio_rira RWYT_PNGBTN_RADIO_RIRA
    , pf.rwyt_pngbtn_radio_nal_siklus_rira RWYT_PNGBTN_RADIO_NAL_SIKLUS_RIRA
    , pf.rwyt_pngbtn_radio_nal_dosis_rira RWYT_PNGBTN_RADIO_NAL_DOSIS_RIRA
    , pf.rwyt_pngbtn_radio_samarium_siklus_rira RWYT_PNGBTN_RADIO_SAMARIUM_SIKLUS_RIRA
    , pf.rwyt_pngbtn_radio_samarium_dosis_rira RWYT_PNGBTN_RADIO_SAMARIUM_DOSIS_RIRA
    , pf.rwyt_pngbtn_radio_lainnya_rira RWYT_PNGBTN_RADIO_LAINNYA_RIRA

    , pf.keluhan_pasien KELUHAN_PASIEN
    , pf.skala_nyeri SKALA_NYERI
    , pf.skala_lelah SKALA_LELAH
    , pf.skala_mual SKALA_MUAL
    , pf.skala_depresi SKALA_DEPRESI
    , pf.skala_cemas SKALA_CEMAS
    , pf.skala_mengantuk SKALA_MENGANTUK
    , pf.skala_nafsu_makan SKALA_NAFSU_MAKAN
    , pf.skala_sehat SKALA_SEHAT
    , pf.skala_sesak_napas SKALA_SESAK_NAPAS
    , pf.skala_masalah SKALA_MASALAH
    , kp.status_verif STATUS_VERIF
    , kp.verif_oleh VERIF_OLEH
    , master.getNamaLengkapPegawai(uver.NIP) USER_YG_VERIF

    FROM keperawatan.tb_keperawatan kp

    LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
    LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
    LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
    LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
    LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
    LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
    LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
    LEFT JOIN master.ruangan rp ON rp.ID = tp.RUANGAN
    LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS = 10
    LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by
    LEFT JOIN keperawatan.tb_barthel_indek bii ON bii.nokun = kp.nokun AND bii.`status`=1 AND bii.ref=9
    LEFT JOIN keperawatan.tb_skrining_nyeri sny ON sny.ref = kp.id_emr AND sny.data_source=10 AND sny.`status`=1
    LEFT JOIN keperawatan.tb_skala_braden brad ON brad.nokun = kp.nokun AND brad.`status`=1
    LEFT JOIN keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa pde ON pde.nokun = kp.nokun AND pde.`status`=1
    LEFT JOIN keperawatan.tb_penilaian_risiko_skala_morse peris ON peris.nokun = kp.nokun AND peris.`status`=1
    LEFT JOIN keperawatan.tb_penilaian_humptydumpty_igd hump ON hump.nokun = kp.nokun AND hump.`status`=1
    LEFT JOIN keperawatan.tb_perencanaan_pemulangan_pasien ppp ON ppp.id_emr = kp.id_emr AND ppp.`status`=1
    LEFT JOIN db_pasien.tb_o2 odua ON odua.ref = kp.id_emr AND odua.data_source=1 AND odua.`status`=1
    LEFT JOIN master.referensi refgol ON refgol.ID = pas.GOLONGAN_DARAH AND refgol.JENIS=6

    LEFT JOIN keperawatan.tb_riwayat_kesehatan rke ON rke.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel valerg ON valerg.id_variabel = rke.alergi
    LEFT JOIN db_master.variabel vtran ON vtran.id_variabel = rke.riwayat_transfusi
    LEFT JOIN db_master.variabel vralg ON vralg.id_variabel = rke.reaksi_transfusi
    LEFT JOIN db_master.variabel vkebi ON vkebi.id_variabel = rke.kebiasaan
    LEFT JOIN db_master.variabel vrkan ON vrkan.id_variabel = rke.riwayat_kanker
    LEFT JOIN db_master.variabel vmet ON vmet.id_variabel = rke.riwayat_metabolik
    LEFT JOIN db_master.variabel vdd ON vdd.id_variabel = rke.deteksidini

    LEFT JOIN keperawatan.tb_kunjungan_igd igz ON igz.id_emr = kp.id_emr

    LEFT JOIN keperawatan.tb_riwayat_kelahiran rkel ON rkel.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_skrining_gizi sgiz ON sgiz.id_emr = kp.id_emr

    LEFT JOIN keperawatan.tb_pemeriksaan_fisik pf ON pf.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_skrining_gizi sg ON sg.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_ews ewz ON ewz.ref = kp.id_emr AND ewz.`status`=1
    LEFT JOIN db_master.variabel penurbb ON penurbb.id_variabel = sg.penurunan_bb
    LEFT JOIN db_master.variabel asumak ON asumak.id_variabel = sg.asupan_bb
    LEFT JOIN db_master.variabel mulu ON mulu.id_variabel = pf.mulut
    LEFT JOIN db_master.variabel eso ON eso.id_variabel = pf.esophagus
    LEFT JOIN db_master.variabel abdo ON abdo.id_variabel = pf.abdomen

    LEFT JOIN db_master.variabel idung ON idung.id_variabel = pf.keluhan_pada_hidung
    LEFT JOIN db_master.variabel dada ON dada.id_variabel = pf.keluhan_pada_dada
    LEFT JOIN db_master.variabel jantung ON jantung.id_variabel = pf.keluhan_pada_jantung
    LEFT JOIN db_master.variabel pcjan ON pcjan.id_variabel = pf.alat_pacu_jantung
    LEFT JOIN db_master.variabel kelpar ON kelpar.id_variabel = pf.keluhan_pada_paru
    LEFT JOIN db_master.variabel pendar ON pendar.id_variabel = pf.perdarahan
    LEFT JOIN db_master.variabel keltid ON keltid.id_variabel = pf.keluhan_istirahat
    LEFT JOIN db_master.variabel mob ON mob.id_variabel = pf.kemampuan_mobilisasi
    LEFT JOIN db_master.variabel spiri ON spiri.id_variabel = pf.keyakinan

    LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan pak ON pak.id_emr = kp.id_emr
    LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON akd.ID = pak.id_asuhan_keperawatan_detil

    LEFT JOIN aplikasi.pengguna uver ON uver.ID = kp.verif_oleh

    WHERE kp.`status`=1 AND kp.jenis=13 AND kp.id_emr="' . $idemr . '"

    GROUP BY kp.id_emr'
);
return $query->row_array();
}

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
