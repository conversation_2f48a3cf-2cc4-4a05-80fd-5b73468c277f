<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PersetujuanTindakanKemoterapiLlaAnak extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel'
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $nomr = $this->uri->segment(4);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $historyPtKemoLlaAnak = $this->pengkajianAwalModel->historyPtKemoLlaAnak($nomr);

    $data = [
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'historyPtKemoLlaAnak' => $historyPtKemoLlaAnak,
      'diagnosisWdDdLlaAnak' => $this->masterModel->referensi(997),
      'dasardiag_aml' => $this->masterModel->referensi(985),
      'tindakandokter_aml' => $this->masterModel->referensi(986),
      'indikasi_aml' => $this->masterModel->referensi(987),
      'tatacara_aml' => $this->masterModel->referensi(967),
      'tujuan_pengobatan_aml' => $this->masterModel->referensi(972),
      'risikoLlaAnak' => $this->masterModel->referensi(998),
      'kompilasi_aml' => $this->masterModel->referensi(981),
      'prognosis_aml' => $this->masterModel->referensi(982),
      'alternatif_aml' => $this->masterModel->referensi(983),
      'lainlain_aml' => $this->masterModel->referensi(984),
      'jenis_kelamin' => $this->masterModel->referensi(965),
    ];

    $this->load->view('Pengkajian/informedConsent/persetujuanTindakanKemoterapiLLAAnak/index', $data);
  }

  public function simpanPTLlaAnak()
  {
    $post = $this->input->post();

    // tanggal Persetujuan
    $date = $post['datePickerLlaAnak'];
    $tglPersetujuanLlaAnk = date('Y-m-d H:i', strtotime($date));
    // echo $tglPersetujuanLlaAnk;

    $dataInformedConcent = [
      'nokun'                  => $post['nokun'],
      'jenis_informed_consent' => 3033,
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanLlaAnak'],
      'penerima_informasi'     => $post['penerimaInformasiLlaAnak'],
      'oleh'                   => $this->session->userdata('id'),
    ];

    $this->db->trans_begin();
    $this->db->insert('db_informed_consent.tb_informed_consent', $dataInformedConcent);
    $idInformedConsent = $this->db->insert_id();

    $dataKemo = [
      'id_informed_consent'              => $idInformedConsent,
      'diagnosis_wd_dd'                  => implode(',', $post["diagnosisWdDdLlaAnak"]),
      'dasar_diagnosis'                  => implode(',', $post["dasarDiagnosisPKemLlaAnak"]),
      'tindakan_kedokteran'              => implode(',', $post["tindakanKedokteranLlaAnak"]),
      'indikasi_tindakan'                => implode(',', $post["indikasiTindakanLlaAnak"]),
      'tata_cara'                        => implode(',', $post["tataCaraLlaAnak"]),
      'tujuan_tindakan'                  => $post["tujuanTindakanLlaAnak"],
      'tujuan_pengobatan'                => implode(',', $post["tujuanPengobatanLlaAnak"]),
      'risiko'                           => implode(',', $post["risikoLlaAnak"]),
      'komplikasi'                       => implode(',', $post["komplikasiLlaAnak"]),
      'prognosis'                        => implode(',', $post["prognosisLlaAnak"]),
      'alternatif'                       => implode(',', $post["alternatifLlaAnak"]),
      'lain_lain'                        => implode(',', $post["lainLainLlaAnak"]),
    ];

    $dataPersetujuanLlaAnak = [
      'id_informed_consent'        => $idInformedConsent,
      'nama_keluarga'              => $post['namaLlaAnak'],
      'umur_keluarga'              => $post['umurLlaAnak'],
      'jk_keluarga'                => $post['jenis_kelaminLlaAnak'],
      'alamat_keluarga'            => $post['alamatLlaAnak'],
      'tindakan'                   => $post['tindakanLlaAnak'],
      'hub_keluarga_dgn_pasien'    => $post['hubunganLlaAnak'],
      'tanggal_persetujuan'        => $tglPersetujuanLlaAnk,
      'ttd_menyatakan'             => file_get_contents($post['signMenyatakanLlaAnak']),
      'ttd_saksi_keluarga'         => file_get_contents($post['signKeluargaLlaAnak']),
      'ttd_saksi_rumah_sakit'      => file_get_contents($post['signRumahSakitLlaAnak']),
      'saksi_keluarga'             => $post['nama_keluarga'],
      'saksi_rumah_sakit'          => $post['nama_saksi_rs']
    ];

    $this->db->insert('db_informed_consent.tb_pt_tindkemo_llaanak', $dataKemo);
    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran', $dataPersetujuanLlaAnak);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }

    echo json_encode($result);
  }
}

/* End of file PersetujuanTindakanKemoterapiLlaAnak.php */
/* Location: ./application/controllers/informedConsent/PersetujuanTindakanKemoterapiLlaAnak.php */