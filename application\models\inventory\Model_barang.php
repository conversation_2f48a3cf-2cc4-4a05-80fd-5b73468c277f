<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Model_barang extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    public function barang()
    {
        $query  = $this->db->query("SELECT br.ID_BARANG as ID, br.BARANG as NAMA, sa.SATUAN, br.STATUS, br.HARGA, br.KODE_BMN
            FROM invenumum.barang_master as br          
            LEFT JOIN invenumum.satuan_ as sa ON sa.ID_SATUAN = br.SATUAN");
        return $query;
    }

    function tampilkan_data()
    {

        return $this->db->get('invenumum.barang_master');
    }

    function post($data)
    {
        $this->db->insert('invenumum.barang_master', $data);
    }

    function get_one($id)
    {
        $param  =   array('ID' => $id);
        return $this->db->get_where('inventory.barang', $param);
    }

    function edit($data, $id)
    {
        $ID         =   $this->input->post('ID');
        $NAMA       =   $this->input->post('NAMA');
        $KATEGORI   =   $this->input->post('KATEGORI');
        $KODE_BMN   =   $this->input->post('KODE_BMN');
        $SATUAN        =   $this->input->post('SATUAN');
        $PENYEDIA      =   $this->input->post('PENYEDIA');
        //echo "<pre>";print_r($_POST);exit();
        $data       =     array(
            'NAMA' => $NAMA,
            'KATEGORI' => $KATEGORI,
            'KODE_BMN' => $KODE_BMN,
            'SATUAN' => $SATUAN,
            'PENYEDIA' => $PENYEDIA
        );

        $this->db->where('ID', $this->input->post('ID'));
        $this->db->update('inventory.barang', $data);
    }

    function cari_barang($title)
    {
        $this->db->like('BARANG', $title, 'both');
        $this->db->order_by('BARANG', 'ASC');
        $this->db->limit(10);
        return $this->db->get('invenumum.barang_master')->result();
    }

    function delete($id)
    {
        $this->db->where('barang_id', $id);
        $this->db->delete('invenumum.barang');
    }

    public function sBarangAktif($id, $data)
    {
        $this->db->where('ID_BARANG', $id);
        $this->db->update('invenumum.barang_master', $data);
    }

    public function sBarangNonAktif($id, $data)
    {
        $this->db->where('ID_BARANG', $id);
        $this->db->update('invenumum.barang_master', $data);
    }

    public function modaldatabarang($id)
    {
        $query = $this->db->query(
            "SELECT * FROM invenumum.barang_master WHERE ID_BARANG='$id'"
        );
        return $query->result_array();
    }

    public function update($id, $data)
    {
        $this->db->where('ID_BARANG', $id);
        $this->db->update('invenumum.barang_master', $data);
    }
}
