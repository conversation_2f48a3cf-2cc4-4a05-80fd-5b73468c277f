<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianAwalKeperawatan extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'EresepModel',
        'hemodialisaModel',
        'konsultasi/KonsultasiModel',
        'geriatri/InstrumenGDSModel',
        'rehabilitasiMedik/KonsulRehabMedikModel',
        'rehabilitasiMedik/PengukuranLimfedemaModel',
        'igd/SerahTerimaShiftModel',
        'operasi/PengkajianDafOpeModel'
      )
    );
  }

  public function index()
  {
    $nomr = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $id_ruangan = substr($nokun, 0, 9);
    $rawat = 1;

    // START SKALA ONTARIO
    $skalaOntario = $this->pengkajianAwalModel->hasilSkalaOntario($nokun);
    $totalSkalaOntario = isset($skalaOntario['total']) ? $skalaOntario['total'] : "";

    if ($totalSkalaOntario >= 0 && $totalSkalaOntario <= 5) {
      $hasilSkalaOntario = "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Rendah</b> ( Skala Ontario ) </div>";
    } elseif ($totalSkalaOntario >= 6 && $totalSkalaOntario <= 16) {
      $hasilSkalaOntario = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Sedang</b> ( Skala Ontario ) </div>";
    } elseif ($totalSkalaOntario >= 17) {
      $hasilSkalaOntario = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaOntario . "</b> = <b>Risiko Tinggi</b> ( Skala Ontario ) </div>";
    }

    $jumlahSkalaOntario = $this->pengkajianAwalModel->get_count_skalaOntario($nokun);
    // END SKALA ONTARIO

    // START SKALA MORSE
    $skalaMorse = $this->pengkajianAwalModel->hasilSkalaMorse($nokun);
    if (isset($skalaMorse)) {
      $totalSkalaMorse = $skalaMorse['NILAI_1'] + $skalaMorse['NILAI_2'] + $skalaMorse['NILAI_3'] + $skalaMorse['NILAI_4'] + $skalaMorse['NILAI_5'] + $skalaMorse['NILAI_6'];
    } else {
      $totalSkalaMorse = null;
    }

    if ($totalSkalaMorse < 25) {
      $hasilSkalaMorse = "<div class='alert alert-success' role='alert'> Score <b>" . $totalSkalaMorse . "</b> = <b>Tidak risiko</b> ( Skala Morse )</div>";
    } elseif ($totalSkalaMorse < 45) {
      $hasilSkalaMorse = "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko Rendah</b> ( Skala Morse )</div>";
    } elseif ($totalSkalaMorse >= 45) {
      $hasilSkalaMorse = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkalaMorse . "</b> = <b>Risiko tinggi</b> ( Skala Morse )</div>";
    }

    $jumlahSkalaMorse = $this->pengkajianAwalModel->get_count_skalaMorse($nokun);
    // END SKALA MORSE

    // START HUMPTY DUMPTY
    $humptyDumpty = $this->pengkajianAwalModel->hasilHumptyDumpty($nokun);
    $totalHumptyDumpty = isset($humptyDumpty['HASIL']) ? $humptyDumpty['HASIL'] : "";

    if ($totalHumptyDumpty <= 7) {
      $hasilHumptyDumpty = "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b> ( Humpty Dumpty )</div>";
    } elseif ($totalHumptyDumpty <= 11) {
      $hasilHumptyDumpty = "<div class='alert alert-success' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko rendah</b> ( Humpty Dumpty )</div>";
    } elseif ($totalHumptyDumpty >= 12) {
      $hasilHumptyDumpty = "<div class='alert alert-danger' role='alert'>Score <b>" . $totalHumptyDumpty . "</b> = <b>Risiko tinggi</b> ( Humpty Dumpty )</div>";
    }

    $jumlahHumptyDumpty = $this->pengkajianAwalModel->get_count_humptyDumpty($nokun);
    // END HUMPTY DUMPTY

    $id_pengguna = $this->session->userdata('id');
    $statusPengguna = $_SESSION['status'];
    $getPengkajianMedis = "";
    $idemr = "";
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $getIDEMR_AwalKeperawatan = "";
    if ($this->uri->segment(7) != "" && $this->session->userdata('status') == 2) {
      $idemr = $this->uri->segment(7);
      $getPengkajian = $this->pengkajianAwalModel->getPengkajian($idemr);
      $getIDEMR_AwalKeperawatan = $this->pengkajianAwalModel->getIDEMR_AwalKeperawatan($idemr);
      if ($getNomr['ID_RUANGAN'] == 105130101 || $getNomr['ID_RUANGAN'] == 105130103) {
        $getPengkajianDeteksiDini = $this->pengkajianAwalModel->getPengkajianDeteksiDini($idemr);
      }
    }

    $getUreum = $this->pengkajianAwalModel->getUreum($nomr);

    $getIDEMR_AwalMedis = "";
    if ($this->uri->segment(7) != "" && $this->session->userdata('status') == 1) {
      $idemr = $this->uri->segment(7);

      ///////////////////////////////////////// HISTORY Medis //////////////////////////////
      $getPengkajianMedis = $this->pengkajianAwalModel->getPengkajianMedis($idemr);
      $hReaksiAlergiMedis = $this->pengkajianAwalModel->hReaksiAlergiMedis($idemr);
      $getPengkajianAsuhanKeperawatan = $this->pengkajianAwalModel->getPengkajianAsuhanKeperawatan($idemr);
      $getIDEMR_AwalMedis = $this->pengkajianAwalModel->getIDEMR_AwalMedis($idemr);
    }

    // echo "<pre>";print_r($getPengkajianMedis);exit();

    $dataKolaborasiIGD = array();
    $indexKolaborasiIGD = 0;
    $getIdEmr = null;
    if (isset($post['kolaborasi'])) {
      foreach ($post['kolaborasi'] as $input) {
        if ($post['kolaborasi'][$indexKolaborasiIGD] != "") {
          array_push(
            $dataKolaborasiIGD,
            array(
              'id_emr' => $getIdEmr,
              'id_variabel' => $post['kolaborasi'][$indexKolaborasiIGD],
              'kolaborasi_lainnya' => isset($post['kolaborasi_lainnya']) ? ($post['kolaborasi'][$indexKolaborasiIGD] == 1662 ? $post['kolaborasi_lainnya'] : "") : "",
            )
          );
        }
        $indexKolaborasiIGD++;
      }
    }

    //MASTER KEPERAWATAN
    $riwayatKanker = $this->masterModel->referensi(1);
    $riwayatAlergi = $this->masterModel->referensi(2);
    $kesadaran = $this->masterModel->referensi(5);
    $skriningNyeri = $this->masterModel->referensi(7);
    $psikologis = $this->masterModel->referensi(13);
    $sosialDanEkonomiHubungan = $this->masterModel->referensi(14);
    $sosialDanEkonomiPencariNafkah = $this->masterModel->referensi(15);
    $sosialDanEkonomiTinggalSerumah = $this->masterModel->referensi(16);
    $spiritualKultural = $this->masterModel->referensi(17);
    $alatBantu = $this->masterModel->referensi(19);
    $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
    $pengkajianNyeriQuality = $this->masterModel->referensi(9);
    $pengkajianNyeriTime = $this->masterModel->referensi(12);
    $statusFungsional = $this->masterModel->referensi(18);
    $skriningResikoJatuhPusing = $this->masterModel->referensi(120);
    $skriningResikoJatuhBerdiri = $this->masterModel->referensi(121);
    $skriningResikoJatuh6Bulan = $this->masterModel->referensi(122);
    $komponenPenilaian = $this->masterModel->referensi(22);
    $komponenPenilaianAsupan = $this->masterModel->referensi(23);
    $pendidikan = $this->masterModel->referensi(24);
    $bahasaSehari = $this->masterModel->referensi(25);
    $perluPenerjemah = $this->masterModel->referensi(26);
    $kesediaanInformasi = $this->masterModel->referensi(27);
    $hambatan = $this->masterModel->referensi(28);
    $kebutuhanPembelajaran = $this->masterModel->referensi(29);
    $skalaNyeriNRS = $this->masterModel->referensi(114);
    $skalaNyeriWBR = $this->masterModel->referensi(115);
    $skalaNyeriFLACC = $this->masterModel->referensi(123);
    $skalaNyeriBPS = $this->masterModel->referensi(133);
    $efeksampingNRS = $this->masterModel->referensi(118);
    $efeksampingWBR = $this->masterModel->referensi(119);
    $efeksampingFLACC = $this->masterModel->referensi(131);
    $efeksampingBPS = $this->masterModel->referensi(134);
    $statusnyeriNRS = $this->masterModel->referensi(136);
    $statusnyeriWBR = $this->masterModel->referensi(136);
    $statusnyeriFLACC = $this->masterModel->referensi(136);
    $statusnyeriBPS = $this->masterModel->referensi(136);
    $riwayatTransfusiDarah = $this->masterModel->referensi(140);
    $riwayatTransfusiDarahDesk = $this->masterModel->referensi(141);
    $kebiasaanmerokok = $this->masterModel->referensi(142);
    $riwayatMetabolik = $this->masterModel->referensi(143);
    $riwayatDDK = $this->masterModel->referensi(145);
    $pengobatanAlternatif = $this->masterModel->referensi(146);
    $pengobatanBertentangan = $this->masterModel->referensi(147);
    $formAsuhanKeperawatan = $this->masterModel->referensi(148);
    $jenisKunjungan = $this->masterModel->referensi(149);
    $riwayatPenyakitKeluarga = $this->masterModel->referensi(151);
    $riwayatEkstravasasi = $this->masterModel->referensi(164);
    $hasilLaboratorium = $this->masterModel->referensi(165);
    $hasilBmpTerakhir = $this->masterModel->referensi(166);
    $kemoterapiTerdahulu = $this->masterModel->referensi(167);
    $tindakanPerawatanTerakhir = $this->masterModel->referensi(168);
    $riwayatGVHD = $this->masterModel->referensi(169);
    $ESASnyeri = $this->masterModel->referensi(170);
    $ESASlelah = $this->masterModel->referensi(171);
    $ESASmual = $this->masterModel->referensi(172);
    $ESASdepresi = $this->masterModel->referensi(173);
    $ESAScemas = $this->masterModel->referensi(174);
    $ESASmengantuk = $this->masterModel->referensi(175);
    $ESASnafsuMakan = $this->masterModel->referensi(176);
    $ESASsehat = $this->masterModel->referensi(177);
    $ESASsesakNapas = $this->masterModel->referensi(178);
    $ESASmasalah = $this->masterModel->referensi(179);
    $fotodepanbelakang = $this->masterModel->referensi(253);
    $fotodepan = $this->masterModel->referensi(254);
    $fotobelakang = $this->masterModel->referensi(255);

    //Eresep
    $farmasi_eresep = $this->masterModel->farmasi();
    $obat = $this->EresepModel->getobat($getNomr['ID_TUJUAN_FARMASI'], $getNomr['IDPENJAMIN']);
    // $menyusui               = $this->masterModel->referensi(229);
    // $resep_pp               = $this->masterModel->referensi(231);
    // $gangguan_fungsi_ginjal = $this->masterModel->referensi(232);

    // Radiologi Diagnostik / Nuklir
    $pasienHamil = $this->masterModel->referensi(230);
    $pemberiObatKontras = $this->masterModel->referensi(352);
    $dosisYangDiberikan = $this->masterModel->referensi(356);
    $caraPemberianRad = $this->masterModel->referensi(357);
    $pemeriksaanFungsiGinjal = $this->masterModel->referensi(358);
    $pemeriksaanGulaDarah = $this->masterModel->referensi(359);
    $pemasanganMonitorSaturasi = $this->masterModel->referensi(360);
    $pemberianOksigenasi = $this->masterModel->referensi(361);
    $tindakanAnestesiSedasi = $this->masterModel->referensi(362);
    $dosisYangDiberikanNuklir = $this->masterModel->referensi(392);
    $perluBowelPreparation = $this->masterModel->referensi(383);
    $perluObatDiuretik = $this->masterModel->referensi(384);
    $injeksiRadiofarmakaDi = $this->masterModel->referensi(385);
    $wholeBodyScan = $this->masterModel->referensi(386);
    $letakTangan = $this->masterModel->referensi(387);
    $dimulaiDari = $this->masterModel->referensi(388);
    $diabetesMellitus = $this->masterModel->referensi(290);
    $hipertensi = $this->masterModel->referensi(291);
    $penyakitGinjal = $this->masterModel->referensi(232);
    $penyakitJantung = $this->masterModel->referensi(366);
    $penyakitAsma = $this->masterModel->referensi(367);
    $traumaFraktur = $this->masterModel->referensi(368);
    $kejangRun = $this->masterModel->referensi(369);
    $operasiBiopsi = $this->masterModel->referensi(370);
    $sistemikKanker = $this->masterModel->referensi(371);
    $terapiRadiasiSebelumnya = $this->masterModel->referensi(372);
    $zatKontras = $this->masterModel->referensi(373);
    $riwayatClaustrophobia = $this->masterModel->referensi(374);
    $obatRutinDikonsumsi = $this->masterModel->referensi(375);
    $posisiBerbaringRun = $this->masterModel->referensi(376);
    $gangguanBuangAirKecil = $this->masterModel->referensi(318);
    $colostomyBag = $this->masterModel->referensi(377);
    $hamilAtauMenyusuiAnak = $this->masterModel->referensi(378);
    $menstruasiRun = $this->masterModel->referensi(379);
    $sedangHamilRun = $this->masterModel->referensi(380);
    $asalRuanganRunRad = $this->masterModel->referensi(365);
    $penandaanSisiLokasi = $this->masterModel->referensi(415);
    $inforConTin = $this->masterModel->referensi(416);
    $inforConTinAnesSed = $this->masterModel->referensi(417);
    $pemPenunjangFoto = $this->masterModel->referensi(419);
    $pemPenunjangCtScan = $this->masterModel->referensi(420);
    $pemPenunjangUSG = $this->masterModel->referensi(421);
    $pemPenunjangMRI = $this->masterModel->referensi(422);
    $pemPenunjangLab = $this->masterModel->referensi(423);
    $persediaanDarah = $this->masterModel->referensi(425);
    $ketersediaanImplant = $this->masterModel->referensi(426);
    $benarSisiLokasiTin = $this->masterModel->referensi(427);
    $khususPerhatikan = $this->masterModel->referensi(428);
    $instrumenSudahBenar = $this->masterModel->referensi(429);
    $fotoRadiologiSesuai = $this->masterModel->referensi(430);
    $kelengkapanKasaJarum = $this->masterModel->referensi(431);
    $spesimenBeriLabel = $this->masterModel->referensi(432);
    $namaImplanDanLokasi = $this->masterModel->referensi(433);
    $peralatanYangPerlu = $this->masterModel->referensi(434);
    $masalahHarusPerhatikan = $this->masterModel->referensi(435);
    $riwayatTerapiTACETACI = $this->masterModel->referensi(513);
    $bilaAdaTACETACI = $this->masterModel->referensi(514);

    // PENGKAJIAN OPERASI
    $sifatOperasi = $this->masterModel->referensi(621);
    $rencanaJenisPembiusan = $this->masterModel->referensi(622);

    // echo "<pre>";print_r($obat);exit();

    // MASTER KEPERAWATAN ANAK
    $caraKelahiran = $this->masterModel->referensi(189);
    $kondisiLahir = $this->masterModel->referensi(233);
    $kelainanBawaan = $this->masterModel->referensi(192);
    $riwayatTumbuhKembang = $this->masterModel->referensi(234);
    $riwayatImunisasiDasar = $this->masterModel->referensi(193);
    $bentukKepala = $this->masterModel->referensi(195);
    $iramaPernapasan = $this->masterModel->referensi(196);
    $retraksiDada = $this->masterModel->referensi(197);
    $alatBantuNapas = $this->masterModel->referensi(198);
    $sianosis = $this->masterModel->referensi(199);
    $pucat = $this->masterModel->referensi(200);
    $capillaryRefillTest = $this->masterModel->referensi(201);
    $akral = $this->masterModel->referensi(202);
    $pembesaranKelenjarGetahBening = $this->masterModel->referensi(203);
    $gangguanNeurologi = $this->masterModel->referensi(204);
    $neurologiMata = $this->masterModel->referensi(205);
    $mulut = $this->masterModel->referensi(206);
    $abdomen = $this->masterModel->referensi(207);
    $asites = $this->masterModel->referensi(208);
    $defekasi = $this->masterModel->referensi(209);
    $karakteristikFeses = $this->masterModel->referensi(210);
    $urin = $this->masterModel->referensi(211);
    $rectal = $this->masterModel->referensi(212);
    $genetalia = $this->masterModel->referensi(213);
    $kulit = $this->masterModel->referensi(214);
    $warnaKulit = $this->masterModel->referensi(215);
    $luka = $this->masterModel->referensi(216);
    $lokasiLuka = $this->masterModel->referensi(217);
    $kelainanTulang = $this->masterModel->referensi(218);
    $gerakanAnak = $this->masterModel->referensi(219);
    $statusMentalDanTingkahLaku = $this->masterModel->referensi(220);
    $tempatTinggal = $this->masterModel->referensi(226);
    $pengasuh = $this->masterModel->referensi(227);
    $jenisSekolah = $this->masterModel->referensi(228);
    $strongKidsKurus = $this->masterModel->referensi(221);
    $strongKidsTurunBerat = $this->masterModel->referensi(222);
    $strongKidsKondisi = $this->masterModel->referensi(223);
    $strongKidsMalnutrisi = $this->masterModel->referensi(224);

    //MASTER MEDIS
    $anamnesis = $this->masterModel->referensi(54);
    $riwayatPenyakitDahulu = $this->masterModel->referensi(55);
    // $riwayatAlergi          = $this->masterModel->riwayatAlergi();
    $performanceStatus = $this->masterModel->referensi(30);
    $mata = $this->masterModel->referensi(31);
    $leher = $this->masterModel->referensi(32);
    $dadaIramaJantung = $this->masterModel->referensi(33);
    $dadaSuaraNafas = $this->masterModel->referensi(34);
    $perutHati = $this->masterModel->referensi(35);
    $perutLimpa = $this->masterModel->referensi(36);
    $ekstremitasAtasKanan = $this->masterModel->referensi(37);
    $ekstremitasAtasKiri = $this->masterModel->referensi(38);
    $ekstremitasBawahKanan = $this->masterModel->referensi(39);
    $ekstremitasBawahKiri = $this->masterModel->referensi(40);
    $kulitTurgor = $this->masterModel->referensi(41);
    $kulitSianosis = $this->masterModel->referensi(42);
    $refleks = $this->masterModel->referensi(43);
    $kelenjarGetahBening = $this->masterModel->referensi(44);
    $aksesVaskuler = $this->masterModel->referensi(802);
    $aksesVaskulerLokasi = $this->masterModel->referensi(803);
    $aksesVaskulerCDL = $this->masterModel->referensi(804);
    $tumor = $this->masterModel->referensi(45);
    $laboratorium = $this->masterModel->referensi(46);
    $radiologi = $this->masterModel->referensi(47);
    $histopatologi = $this->masterModel->referensi(48);
    $sisiTubuh = $this->masterModel->referensi(49);
    $tujuanPengobatan = $this->masterModel->referensi(50);
    $masalahKeperawatan = $this->masterModel->referensi(51);
    $tindakanKonsul = $this->masterModel->referensi(52);
    $konsulYgDiminta = $this->masterModel->referensi(53);
    $diet = $this->masterModel->referensi(56);
    $jenisDiet = $this->masterModel->referensi(57);
    $rujukanKe = $this->masterModel->referensi(58);
    $alasanRujukan = $this->masterModel->referensi(59);
    $tindakLanjutTerapirujulan = $this->masterModel->referensi(60);
    $statusFungsionalEksternal = $this->masterModel->referensi(61);
    $transportasiPasien = $this->masterModel->referensi(62);
    $diagnosisMedis = $this->masterModel->referensi(63);
    $klinis = $this->masterModel->referensi(256);
    $performanceStatus_2 = $this->masterModel->referensi(262);
    $patologiAnatomi = $this->masterModel->referensi(263);
    $batasSayatan = $this->masterModel->referensi(264);
    $invasiLimfovaskular = $this->masterModel->referensi(265);
    $simulator = $this->masterModel->referensi(243);
    $ctsimulator = $this->masterModel->referensi(244);
    $radiasiteknik = $this->masterModel->referensi(246);
    $radiasijenis = $this->masterModel->referensi(247);
    $radiasilokal = $this->masterModel->referensi(248);
    $radiasijenisfraksinasi = $this->masterModel->referensi(250);
    $penempatanSumber = $this->masterModel->referensi(161);
    $brakhiterapiTeknik = $this->masterModel->referensi(162);
    $tindakLanjutLain = $this->masterModel->referensi(163);
    $Ecog = $this->masterModel->referensi(239);
    $Karnofsky = $this->masterModel->referensi(240);
    $Lansky = $this->masterModel->referensi(267);
    $kolaborasi = $this->masterModel->referensi(251);
    $masalahKesehatan = $this->masterModel->referensi(181);
    $penunjangLaboratorium = $this->masterModel->referensi(180);
    $lokalLokoregional = $this->masterModel->referensi(249);
    $ctsimulatordengankontras = $this->masterModel->referensi(245);
    $RisikoJatuhTR = $this->masterModel->referensi(259);
    $ps2 = $this->masterModel->referensi(262);
    $karnofsky = $this->masterModel->referensi(240);
    $lansky = $this->masterModel->referensi(267);
    $terapiRater = $this->masterModel->referensi(242);
    $dataECOG = $this->masterModel->referensi(239);
    $riwayatpenggunaanobat = $this->masterModel->referensi(288);
    $persiapandarah = $this->masterModel->referensi(324);
    $persiapanalatkhusus = $this->masterModel->referensi(325);
    $stadium = $this->masterModel->stadium();
    $kesadaran = $this->masterModel->referensi(5);
    $dibawaDengan = $this->masterModel->referensi(1111);

    // Form Konsultasi
    $listDr = $this->masterModel->listDr();
    $listDrUmum = $this->masterModel->listDrUmum();
    $pilihProtokolKemo = $this->masterModel->pilihProtokolKemo();
    $listSMF = $this->masterModel->listSMF();
    $jenisKonsultasi = $this->masterModel->referensi(235);
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
    $penilaianKasusSaatIni = $this->masterModel->referensi(236);
    $setujuUntuk = $this->masterModel->referensi(237);
    $historyKonsul = $this->KonsultasiModel->tampilHistoryKonsul($nomr);
    $jawabKonsul = $this->KonsultasiModel->tampilJawabKonsul();

    // Form Permintaan Dirawat
    $listKasus = $this->masterModel->listKasus();
    // $listRencanaPerawatan = $this->masterModel->listRencanaPerawatan();
    $listRencana = $this->masterModel->listRencana();
    // $listKebutuhanPelayanan = $this->masterModel->listKebutuhanPelayanan();
    $listPerbaikanKeadaanUmum = $this->masterModel->listPerbaikanKeadaanUmum();
    $listTindakan = $this->masterModel->listTindakan();
    $listDiet = $this->masterModel->listDiet();
    $listJenisDiet = $this->masterModel->listJenisDiet();
    $listOdc = $this->masterModel->listOdc();
    $listRawatInap = $this->masterModel->listRawatInap();
    $listRawatKhusus = $this->masterModel->listRawatKhusus();
    $listIntensiveCare = $this->masterModel->listIntensiveCare();

    // Form CPO
    $listObat = $this->masterModel->listObat();
    $listJenisObat = $this->masterModel->listJenisObat();

    //Form IGD
    $listPalliative = $this->masterModel->listPalliative();
    $listRiwayatKesehatan = $this->masterModel->listRiwayatKesehatan();
    $listKondisiPsikologis = $this->masterModel->listKondisiPsikologis();
    $listKondisi = $this->masterModel->listKondisi();
    $listKondisiPasien = $this->masterModel->listKondisiPasien();
    $listKondisiKeluarga = $this->masterModel->listKondisiKeluarga();
    $listBerduka = $this->masterModel->listBerduka();
    $listBerdukaPasien = $this->masterModel->listBerdukaPasien();
    $listBerdukaKeluarga = $this->masterModel->listBerdukaKeluarga();
    $listPotensiReaksi = $this->masterModel->listPotensiReaksi();
    $listPasienPsikolog = $this->masterModel->listPasienPsikolog();
    $listKeluargaPsikolog = $this->masterModel->listKeluargaPsikolog();
    $listKebutuhanSpiritual = $this->masterModel->listKebutuhanSpiritual();
    $listKebutuhanSpiritualPasien = $this->masterModel->listKebutuhanSpiritualPasien();
    $listKebutuhanSpiritualKeluarga = $this->masterModel->listKebutuhanSpiritualKeluarga();
    $listKebutuhanPendukung = $this->masterModel->listKebutuhanPendukung();
    $listTerapiKomplementer = $this->masterModel->listTerapiKomplementer();
    $listMembutuhkanCaregiver = $this->masterModel->listMembutuhkanCaregiver();
    $listPerawatanDirumah = $this->masterModel->listPerawatanDirumah();
    $listIntervensi = $this->masterModel->listIntervensi();
    $listMasalahKeperawatan = $this->masterModel->listMasalahKeperawatan();
    $listPerencanaanAsuhan = $this->masterModel->listPerencanaanAsuhan();
    $listKausatif = $this->masterModel->listKausatif();
    $listPenghentianIntervensi = $this->masterModel->listPenghentianIntervensi();
    $listserahTerimaDataPasienIGD = $this->masterModel->listserahTerimaDataPasienIGD();
    $listAlatTerpasangIGD = $this->masterModel->listAlatTerpasangIGD();
    $listMasalahAsuhanIGD = $this->masterModel->listMasalahAsuhanIGD();
    $listShiftIGD = $this->masterModel->listShiftIGD();
    $listKesadaranIGD = $this->masterModel->listKesadaranIGD();
    $listResikoJatuhIGD = $this->masterModel->listResikoJatuhIGD();
    $listOksigenIGD = $this->masterModel->listOksigenIGD();
    $listStatusPasienRekonsiliasi = $this->masterModel->listStatusPasienRekonsiliasi();
    $listRiwayatAlergiObat = $this->masterModel->listRiwayatAlergiObat();

    //form persetujuan tindakn transfusi darah
    $dasarDiagnosis = $this->masterModel->dasarDiagnosis();
    $transfusiDarahTTD = $this->masterModel->transfusiDarahTTD();
    $indikasiTindakan = $this->masterModel->indikasiTindakan();
    $tujuanTindakan = $this->masterModel->tujuanTindakan();
    $tujuanPengobatan = $this->masterModel->tujuanPengobatan();
    $risikoTTD = $this->masterModel->risikoTTD();
    $komplikasiTTD = $this->masterModel->komplikasiTTD();
    $prognosisTTD = $this->masterModel->prognosisTTD();
    $alternatifRisikoTTD = $this->masterModel->alternatifRisikoTTD();

    //Form Pra Sedasi
    $listKajianSistem = $this->masterModel->listKajianSistem();
    $listKajianSistem1 = $this->masterModel->listKajianSistem1();
    $listKajianSistem2 = $this->masterModel->listKajianSistem2();
    $listKajianSistem3 = $this->masterModel->listKajianSistem3();
    $listKajianSistem4 = $this->masterModel->listKajianSistem4();
    $listKajianSistem5 = $this->masterModel->listKajianSistem5();
    $listKajianSistem6 = $this->masterModel->listKajianSistem6();
    $listKajianSistem7 = $this->masterModel->listKajianSistem7();
    $listKajianSistem8 = $this->masterModel->listKajianSistem8();
    $listKajianSistem9 = $this->masterModel->listKajianSistem9();
    $listKajianSistem10 = $this->masterModel->listKajianSistem10();
    $listKajianSistem11 = $this->masterModel->listKajianSistem11();
    $listKajianSistem12 = $this->masterModel->listKajianSistem12();
    $listKajianSistem13 = $this->masterModel->listKajianSistem13();
    $listKajianSistem14 = $this->masterModel->listKajianSistem14();
    $listKajianSistem15 = $this->masterModel->listKajianSistem15();
    $listPemriksaanFisik = $this->masterModel->listPemriksaanFisik();
    $listPemriksaanFisik1 = $this->masterModel->listPemriksaanFisik1();
    $listPemriksaanFisik2 = $this->masterModel->listPemriksaanFisik2();
    $listPemriksaanFisik3 = $this->masterModel->listPemriksaanFisik3();
    $listPemriksaanFisik4 = $this->masterModel->listPemriksaanFisik4();
    $listPemriksaanFisik5 = $this->masterModel->listPemriksaanFisik5();
    $listPemriksaanFisik6 = $this->masterModel->listPemriksaanFisik6();
    $listPemriksaanPenunjang = $this->masterModel->listPemriksaanPenunjang();
    $listKlasifikasiAsa = $this->masterModel->listKlasifikasiAsa();
    $listMonitoring = $this->masterModel->listMonitoring();
    $listPerawatanPascaSedasi = $this->masterModel->listPerawatanPascaSedasi();

    // Form Sedasi Kamar Pemulihan
    $listKesadaranSedasi = $this->masterModel->listKesadaranSedasi();
    $listPernapasanSedasi = $this->masterModel->listPernapasanSedasi();
    $listNyeriSedasi = $this->masterModel->listNyeriSedasi();
    $listRisikoJatuhSedasi = $this->masterModel->listRisikoJatuhSedasi();
    $listFrekuensiNapasSedasi = $this->masterModel->listFrekuensiNapasSedasi();
    $listFrekuensiNadiSedasi = $this->masterModel->listFrekuensiNadiSedasi();
    $listTekananDarahSedasi = $this->masterModel->listTekananDarahSedasi();
    $listSkalaNyeriSedasi = $this->masterModel->listSkalaNyeriSedasi();
    $listPerawat4 = $this->masterModel->listPerawat();
    $listPerawat5 = $this->masterModel->listPerawat();
    $listRuangPemulihan = $this->masterModel->listRuangPemulihan();
    $listScorePadss = $this->masterModel->listScorePadss();
    $listKeluarNyeri = $this->masterModel->listKeluarNyeri();
    $listPemulihanRisiko = $this->masterModel->listPemulihanRisiko();

    // Formulir Pasien Preaferesis Terapeutik (Stem Cells)
    $jenisDataPreaferesis = $this->masterModel->referensi(968);
    $jenisKelamin = $this->masterModel->referensi(969);
    $tidurPreaferesis = $this->masterModel->referensi(970);
    $statusKesadaran = $this->masterModel->referensi(971);

    // Form Bank Darah Ceklis Tindakan Aferesis
    $listFormCalonDonor = $this->masterModel->referensi(776);
    $listIdentifikasiDonor = $this->masterModel->referensi(778);
    $listRiwayatPenyakitDonor = $this->masterModel->referensi(779);
    $listKeadaanUmumDonor = $this->masterModel->referensi(780);
    $listPeriksaGolonganDarah = $this->masterModel->referensi(781);
    $listPeriksaLabPK = $this->masterModel->referensi(782);
    $listHasilGolonganDarah = $this->masterModel->referensi(783);
    $listHasilLaboratorium = $this->masterModel->referensi(784);
    $listDonorDatang = $this->masterModel->referensi(785);
    $listSuratPersetujuan = $this->masterModel->referensi(786);
    $listMesinEferesis = $this->masterModel->referensi(787);
    $listProtocolCard = $this->masterModel->referensi(788);
    $listPaketKitEferesis = $this->masterModel->referensi(789);
    $listLarutanACD = $this->masterModel->referensi(790);
    $listPeralatanAntiseptik = $this->masterModel->referensi(791);
    $listBahanAntiseptik = $this->masterModel->referensi(792);
    $listCalciumGluconas = $this->masterModel->referensi(793);
    $listAdrenalin = $this->masterModel->referensi(794);
    $listAntiHistamin = $this->masterModel->referensi(795);
    $listCorticoSteroid = $this->masterModel->referensi(796);
    $listDisposibleSyringe = $this->masterModel->referensi(797);
    $listLKBIBD = $this->masterModel->referensi(798);
    $listProsedurDimulai = $this->masterModel->referensi(799);
    $listProsedurBerakhir = $this->masterModel->referensi(800);
    $listDonorMeninggalkanRuang = $this->masterModel->referensi(801);

    // Barthel Indek
    $listRangsangbab = $this->masterModel->referensi(834);
    $listRangsangberkemih = $this->masterModel->referensi(835);
    $listMembersihkandiri = $this->masterModel->referensi(836);
    $listPenggunaankloset = $this->masterModel->referensi(837);
    $listMakan = $this->masterModel->referensi(838);
    $listBerubahposisi = $this->masterModel->referensi(839);
    $listBerpindah = $this->masterModel->referensi(840);
    $listMemakaibaju = $this->masterModel->referensi(841);
    $listNaiktangga = $this->masterModel->referensi(842);
    $listMandi = $this->masterModel->referensi(843);

    // Abbreviated Mental Test
    $listUmur = $this->masterModel->referensi(891);
    $listJamBerapa = $this->masterModel->referensi(892);
    $listDimanaAlamat = $this->masterModel->referensi(893);
    $listTahunBerapa = $this->masterModel->referensi(894);
    $listKitaDimana = $this->masterModel->referensi(895);
    $listMengenaliDokter = $this->masterModel->referensi(896);
    $listIndonesiaMerdeka = $this->masterModel->referensi(897);
    $listPresidenRI = $this->masterModel->referensi(898);
    $listAndaLahir = $this->masterModel->referensi(899);
    $listMenghitungMundur = $this->masterModel->referensi(900);
    $listPerasaanHati = $this->masterModel->referensi(902);

    // Mini Nutrional Assessment
    $listPenurunanAsupan = $this->masterModel->referensi(918);
    $listKehilanganBB = $this->masterModel->referensi(919);
    $listKemampuanMobilitas = $this->masterModel->referensi(920);
    $listMenderitaStress = $this->masterModel->referensi(921);
    $listNeuropsikologis = $this->masterModel->referensi(922);
    $listNilaiIMT = $this->masterModel->referensi(923);

    // Penilian risiko jatuh pasien geriatri
    $riwayatjatuh1 = $this->masterModel->referensi(861);
    $riwayatjatuh2 = $this->masterModel->referensi(862);

    // Instrumen Geriatric Depression Scale
    $igds1 = $this->masterModel->referensi(903);
    $igds2 = $this->masterModel->referensi(904);
    $igds3 = $this->masterModel->referensi(905);
    $igds4 = $this->masterModel->referensi(906);
    $igds5 = $this->masterModel->referensi(907);
    $igds6 = $this->masterModel->referensi(908);
    $igds7 = $this->masterModel->referensi(909);
    $igds8 = $this->masterModel->referensi(910);
    $igds9 = $this->masterModel->referensi(911);
    $igds10 = $this->masterModel->referensi(912);
    $igds11 = $this->masterModel->referensi(913);
    $igds12 = $this->masterModel->referensi(914);
    $igds13 = $this->masterModel->referensi(915);
    $igds14 = $this->masterModel->referensi(916);
    $igds15 = $this->masterModel->referensi(917);
    $historyIGDS = $this->InstrumenGDSModel->history($nokun, null, 'tabel');

    // Form Observasi Anyelir
    $listJam = $this->masterModel->listJam();
    $tindakanKeperawatanAnyelir = $this->masterModel->referensi(631);

    //History Observasi Anyelir
    $listHistoryObservasiAnyelir = $this->pengkajianAwalModel->listHistoryObservasiAnyelir();

    //History Keperawatan Anyelir
    $historyKeperawatanAnyelir = $this->pengkajianAwalModel->historyKeperawatanAnyelir();

    // Form Spirometri
    $listKesan = $this->masterModel->listKesan();
    $listRestriksi = $this->masterModel->listRestriksi();
    $listObstruksi = $this->masterModel->listObstruksi();
    //print_r($listRestriksi);exit;

    // Form Persiapan Bronkoskopi
    //$listPersiapanBronkoskopi1 = $this->masterModel->listPersiapanBronkoskopi1();
    //$listPersiapanBronkoskopi2 = $this->masterModel->listPersiapanBronkoskopi2();
    //$listPersiapanBronkoskopi3 = $this->masterModel->listPersiapanBronkoskopi3();
    //$listPersiapanBronkoskopi4 = $this->masterModel->listPersiapanBronkoskopi4();
    //$listPersiapanBronkoskopi5 = $this->masterModel->listPersiapanBronkoskopi5();
    //$listPersiapanBronkoskopi6 = $this->masterModel->listPersiapanBronkoskopi6();
    // $listPersiapanBronkoskopi7 = $this->masterModel->listPersiapanBronkoskopi7();

    // Form Persiapan Gastroskopi
    $listPersiapanGastroskopi1 = $this->masterModel->listPersiapanGastroskopi1();
    $listPersiapanGastroskopi2 = $this->masterModel->listPersiapanGastroskopi2();
    $listPersiapanGastroskopi3 = $this->masterModel->listPersiapanGastroskopi3();

    // Pemantauan Anestesi Lokal
    $kesadaranAnestesiLokal = $this->masterModel->referensi(394);
    $riwayatAlergiAnestesiLokal = $this->masterModel->referensi(395);
    $obatAnestesiLokal = $this->masterModel->referensi(396);
    $diencerkan = $this->masterModel->referensi(397);
    $penggunaanAdrenalin = $this->masterModel->referensi(398);
    $konversiAnestesi = $this->masterModel->referensi(399);
    $kesadaranUmumPascaPasien = $this->masterModel->referensi(467);
    $tandaToksisitas = $this->masterModel->referensi(462);
    $mualMuntah = $this->masterModel->referensi(463);
    $keluhanNyeri = $this->masterModel->referensi(464);
    $perdarahanPascaPasien = $this->masterModel->referensi(465);
    $pasienPindah = $this->masterModel->referensi(466);

    // Pemeriksaan Sitologi
    $caraPengambilanSito = $this->masterModel->referensi(271);
    $cairanFiksasiSito = $this->masterModel->referensi(272);
    $statusMenstruasi = $this->masterModel->referensi(273);
    $siklusHaid = $this->masterModel->referensi(274);
    $kontrasepsi = $this->masterModel->referensi(275);
    $statusGinekologi = $this->masterModel->referensi(276);
    $sitologiNonGinekolog = $this->masterModel->referensi(278);

    //CPPT
    $alertKeperawatan = $this->pengkajianAwalModel->alertCppt($nokun, 2, $id_pengguna);
    $alertMedis = $this->pengkajianAwalModel->alertCppt($nokun, 1, $id_pengguna);
    $VkeperwatanCppt = $this->pengkajianAwalModel->VkeperwatanCppt($nokun);
    $tandaVitalCppt = $this->pengkajianAwalModel->tandaVitalCppt($nokun);
    $alertCpptKonsul = $this->pengkajianAwalModel->alertCpptKonsul($nokun);
    // echo "<pre>";print_r($tandaVitalCppt);exit();

    //Pengkajian Medis
    $kunjungan_pk = $this->pengkajianAwalModel->kunjungan_pk($nomr);
    $sitologi = $this->pengkajianAwalModel->sitologi($nomr);
    $histologi = $this->pengkajianAwalModel->histologi($nomr);
    $tindakan_rad = $this->pengkajianAwalModel->tindakan_rad($nomr);

    // Histopatologi
    $historyLabPA = $this->pengkajianAwalModel->tampilHistoryLabPA();
    $hPengkajianRater = $this->pengkajianAwalModel->historyPengkajianRater();

    //Deteksi Dini
    $paketPemeriksaan = $this->masterModel->referensi(289);
    $diabetesMellitus = $this->masterModel->referensi(285);
    $hipertensi = $this->masterModel->referensi(286);
    $jantung = $this->masterModel->referensi(287);
    $merokok = $this->masterModel->referensi(293);
    $perokokPasif = $this->masterModel->referensi(294);
    $minumAlkohol = $this->masterModel->referensi(295);
    $riwayatHepatitis = $this->masterModel->referensi(296);
    $riwayatHepatitisKeluarga = $this->masterModel->referensi(297);
    $pernahOperasi = $this->masterModel->referensi(298);
    $pernahTranfusiDarah = $this->masterModel->referensi(299);
    $pernahSuntik = $this->masterModel->referensi(300);
    $makanDaging = $this->masterModel->referensi(301);
    $makanDiasap = $this->masterModel->referensi(302);
    $polipUsus = $this->masterModel->referensi(303);
    $infeksiUsus = $this->masterModel->referensi(304);
    $polaBAB = $this->masterModel->referensi(305);
    $babTercapurDarah = $this->masterModel->referensi(306);
    $riwayatKankerKeluarga = $this->masterModel->referensi(307);

    //Khusus Wanita
    $kawinMenikah = $this->masterModel->referensi(308);
    $melahirkanAnak = $this->masterModel->referensi(309);
    $pernahMenyusui = $this->masterModel->referensi(310);
    $kontrasepsiHormonal = $this->masterModel->referensi(311);
    $obatHormonal = $this->masterModel->referensi(312);
    $haidPertama = $this->masterModel->referensi(313);
    $monoPause = $this->masterModel->referensi(314);
    $keluhan = $this->masterModel->referensi(315);
    $keputihan = $this->masterModel->referensi(363);
    $pendarahanSpontan = $this->masterModel->referensi(364);
    $pendarahan = $this->masterModel->referensi(316);
    $pernahPapsSmear = $this->masterModel->referensi(317);

    // Khusus Pria
    $gangguanBuangAirKecil = $this->masterModel->referensi(318);
    $pernahProstat = $this->masterModel->referensi(319);

    // PERMERIKSAAN FISIK UMUM
    $keadaanUmum = $this->masterModel->referensi(320);
    $keadaanGizi = $this->masterModel->referensi(321);
    $habitus = $this->masterModel->referensi(322);

    // Rehabilitasi Medik
    $transportasi = $this->masterModel->referensi(323);
    $pelayananRehabMedik = $this->masterModel->referensi(326);
    $historyRehabMedik = $this->KonsulRehabMedikModel->tampilHistoryRehabMedik();
    $karnofskyIndex = $this->PengukuranLimfedemaModel->karnofskyKategori();
    // $historyPengukuranLimfedema = $this->PengukuranLimfedemaModel->tampilHistoryPengukuranLimfedema();

    // Observasi IGD
    $observasiIGD = $this->load->model(array('igd/ObservasiIGDModel'));

    ////////////////////////////////////////////// EWS ////////////////////////////////////////////////////
    $tahun = date('Y');
    $bulan = date('m');
    $tanggal_head = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);

    $parameterEws = $this->masterModel->parameterEws();
    $scoreEws = array();
    foreach ($parameterEws as $parEws) {
      $dataScoreEws = array();
      $dataScoreEws['title'] = $parEws['referensi'];
      $dataScoreEws['id_referensi'] = $parEws['id_referensi'];
      $dataScoreEws['sub'] = $this->masterModel->referensi($parEws['id_referensi']);

      $scoreEws[] = $dataScoreEws;
    }

    ////////////////////////////////////////////// PEWS ////////////////////////////////////////////////////
    $parameterPews = $this->masterModel->parameterPews();
    $scorePews = array();
    foreach ($parameterPews as $parPews) {
      $dataScorePews = array();
      $dataScorePews['title'] = $parPews['referensi'];
      $dataScorePews['id_referensi'] = $parPews['id_referensi'];
      $dataScorePews['sub'] = $this->masterModel->referensi($parPews['id_referensi']);

      $scorePews[] = $dataScorePews;
    }

    //PALIATIF
    $tujuanpalitaif = $this->masterModel->referensi(346);
    $riwayatsakitpaliatif = $this->masterModel->referensi(347);
    $riwayatpengobatanpaliatif = $this->masterModel->referensi(348);
    $riwayatalergipaliatif = $this->masterModel->referensi(71);
    $topikbelajarpaliatif = $this->masterModel->referensi(349);
    $mediabelajarpaliatif = $this->masterModel->referensi(350);
    $kolaborasipaliatif = $this->masterModel->referensi(393);
    $klasifikasi = $this->masterModel->referensi(152);
    $stadiumPaliataif = $this->masterModel->referensi(153);
    $operasiPali = $this->masterModel->referensi(370);
    $radiasiPali = $this->masterModel->referensi(372);
    $terapiPali = $this->masterModel->referensi(371);
    $hPaliatif = $this->pengkajianAwalModel->historyPaliatif();

    if ($this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105020401) {
      $idemr = $this->uri->segment(7);
      $getPengkajianPaliatif = $this->pengkajianAwalModel->getPengkajianPaliatif($idemr);
    }
    if ($this->uri->segment(7) != "" && $this->session->userdata('status') == 1 && $getNomr['ID_RUANGAN'] == 105020401) {
      $idemr = $this->uri->segment(7);
      $getPaliMedis = $this->pengkajianAwalModel->getPengkajianPaliatifMedis($idemr);
    }
    $datakuis = $this->masterModel->referensi(179);
    $idemr = $this->uri->segment(7);
    $getPolinyeri = $this->pengkajianAwalModel->getdatapolinyeri($nomr);
    $detilnyeri = $this->pengkajianAwalModel->detailpengkajianpolinyeri($idemr);

    ////////////////////////////////////////////////////////////////// LAB PK /////////////////////////////////////////////////////////////////
    $historypk = $this->pengkajianAwalModel->history_pasien_pk($nomr);
    $hasil_lab = $this->pengkajianAwalModel->hasil_lab_pk($nokun);

    //Tindakan Patologi Klinik
    $resultTindakanPatologiKlinik = $this->masterModel->tindakanPenunjang('105070101');
    $dataTindakanPatologiKlinik = array();
    foreach ($resultTindakanPatologiKlinik->result() as $tindakanPatologiKlinik) {
      $resultTindakanPatologiKlinikSub = $this->masterModel->tindakanPenunjang('105070101', $tindakanPatologiKlinik->id);
      $jenisTindakan = array();
      if ($resultTindakanPatologiKlinikSub->num_rows() > 0) {
        $jenisTindakan['id'] = $tindakanPatologiKlinik->id;
        $jenisTindakan['tindakan'] = $tindakanPatologiKlinik->tindakan;
        $jenisTindakan['jenis'] = $tindakanPatologiKlinik->deskripsi;
        $jenisTindakan['flag'] = $tindakanPatologiKlinik->flag;
        $subJenisTindakan = array();
        foreach ($resultTindakanPatologiKlinikSub->result() as $subTindakanPatologiKlinik) {
          $subSubJenisTindakan = array();
          $resultTindakanPatologiKlinikSubSub = $this->masterModel->tindakanPenunjang('105070101', $subTindakanPatologiKlinik->id);
          if ($resultTindakanPatologiKlinikSubSub->num_rows() > 0) {
            $subSubSubJenisTindakan = array();
            $subSubJenisTindakan['id'] = $subTindakanPatologiKlinik->id;
            $subSubJenisTindakan['tindakan'] = $subTindakanPatologiKlinik->tindakan;
            $subSubJenisTindakan['jenis'] = $subTindakanPatologiKlinik->deskripsi;
            $subSubJenisTindakan['flag'] = $subTindakanPatologiKlinik->flag;
            foreach ($resultTindakanPatologiKlinikSubSub->result() as $subSubTindakanPatologiKlinik) {
              $subSubSubSubJenisTindakan = array();
              $resultTindakanPatologiKlinikSubSubSub = $this->masterModel->tindakanPenunjang('105070101', $subSubTindakanPatologiKlinik->id);
              if ($resultTindakanPatologiKlinikSubSubSub->num_rows() > 0) {
                $subSubSubSubSubJenisTindakan = array();
                $subSubSubSubJenisTindakan['id'] = $subSubTindakanPatologiKlinik->id;
                $subSubSubSubJenisTindakan['tindakan'] = $subSubTindakanPatologiKlinik->tindakan;
                $subSubSubSubJenisTindakan['jenis'] = $subSubTindakanPatologiKlinik->deskripsi;
                $subSubSubSubJenisTindakan['flag'] = $subSubTindakanPatologiKlinik->flag;
                foreach ($resultTindakanPatologiKlinikSubSubSub->result() as $subSubSubTindakanPatologiKlinik) {
                  $subSubSubSubSubSubJenisTindakan = array();
                  $subSubSubSubSubSubJenisTindakan['id'] = $subSubSubTindakanPatologiKlinik->id;
                  $subSubSubSubSubSubJenisTindakan['tindakan'] = $subSubSubTindakanPatologiKlinik->tindakan;
                  $subSubSubSubSubSubJenisTindakan['jenis'] = $subSubSubTindakanPatologiKlinik->deskripsi;
                  $subSubSubSubSubSubJenisTindakan['flag'] = $subSubSubTindakanPatologiKlinik->flag;
                  $subSubSubSubSubJenisTindakan[] = $subSubSubSubSubSubJenisTindakan;
                }
                $subSubSubSubJenisTindakan['subJenis'] = $subSubSubSubSubJenisTindakan;
              } else {
                $subSubSubSubJenisTindakan['id'] = $subSubTindakanPatologiKlinik->id;
                $subSubSubSubJenisTindakan['tindakan'] = $subSubTindakanPatologiKlinik->tindakan;
                $subSubSubSubJenisTindakan['jenis'] = $subSubTindakanPatologiKlinik->deskripsi;
                $subSubSubSubJenisTindakan['flag'] = $subSubTindakanPatologiKlinik->flag;
              }
              $subSubSubJenisTindakan[] = $subSubSubSubJenisTindakan;
            }
            $subSubJenisTindakan['subJenis'] = $subSubSubJenisTindakan;
          } else {
            $subSubJenisTindakan['id'] = $subTindakanPatologiKlinik->id;
            $subSubJenisTindakan['tindakan'] = $subTindakanPatologiKlinik->tindakan;
            $subSubJenisTindakan['jenis'] = $subTindakanPatologiKlinik->deskripsi;
            $subSubJenisTindakan['flag'] = $subTindakanPatologiKlinik->flag;
          }
          $subJenisTindakan[] = $subSubJenisTindakan;
        }
        $jenisTindakan['subJenis'] = $subJenisTindakan;
      } else {
        $jenisTindakan['id'] = $tindakanPatologiKlinik->id;
        $jenisTindakan['tindakan'] = $tindakanPatologiKlinik->tindakan;
        $jenisTindakan['jenis'] = $tindakanPatologiKlinik->deskripsi;
        $jenisTindakan['flag'] = $tindakanPatologiKlinik->flag;
      }
      $dataTindakanPatologiKlinik[] = $jenisTindakan;
    }

    ////////////////////////////////////////////////////////////////// LAB PA ////////////////////////////////////////////////////////////
    $sito = $this->pengkajianAwalModel->sitologi($nomr);
    $histo = $this->pengkajianAwalModel->histologi($nomr);
    $imuno = $this->pengkajianAwalModel->imuno($nomr);

    // Pemeriksaan Histopatologi
    $caraDapatJaringan = $this->masterModel->referensi(268);
    $cairanFiksasi = $this->masterModel->referensi(269);

    ////////////////////////////////////////////////////////////////// RADIOLOGI ///////////////////////////////////////////////////////////////
    $PasRad = $this->pengkajianAwalModel->LkunRad($nomr);

    //Tindakan Radiologi
    $resultTindakanRadiologi = $this->masterModel->tindakanPenunjang('105100101');
    $dataTindakanRadiologi = array();
    foreach ($resultTindakanRadiologi->result() as $tindakanRadiologi) {
      $resultTindakanRadiologiSub = $this->masterModel->tindakanPenunjang('105100101', $tindakanRadiologi->id);
      $jenisTindakan = array();
      if ($resultTindakanRadiologiSub->num_rows() > 0) {
        $jenisTindakan['id'] = $tindakanRadiologi->id;
        $jenisTindakan['tindakan'] = $tindakanRadiologi->tindakan;
        $jenisTindakan['jenis'] = $tindakanRadiologi->deskripsi;
        $subJenisTindakan = array();
        foreach ($resultTindakanRadiologiSub->result() as $subTindakanRadiologi) {
          $subSubJenisTindakan = array();
          $resultTindakanRadiologiSubSub = $this->masterModel->tindakanPenunjang('105100101', $subTindakanRadiologi->id);
          if ($resultTindakanRadiologiSubSub->num_rows() > 0) {
            $subSubSubJenisTindakan = array();
            $subSubJenisTindakan['id'] = $subTindakanRadiologi->id;
            $subSubJenisTindakan['tindakan'] = $subTindakanRadiologi->tindakan;
            $subSubJenisTindakan['jenis'] = $subTindakanRadiologi->deskripsi;
            foreach ($resultTindakanRadiologiSubSub->result() as $subSubTindakanRadiologi) {
              $subSubSubSubJenisTindakan = array();
              $subSubSubSubJenisTindakan['id'] = $subSubTindakanRadiologi->id;
              $subSubSubSubJenisTindakan['tindakan'] = $subSubTindakanRadiologi->tindakan;
              $subSubSubSubJenisTindakan['jenis'] = $subSubTindakanRadiologi->deskripsi;
              $subSubSubSubJenisTindakan['subJenis'] = $subSubTindakanRadiologi->deskripsi;
              $subSubSubJenisTindakan[] = $subSubSubSubJenisTindakan;
            }
            $subSubJenisTindakan['subJenis'] = $subSubSubJenisTindakan;
          } else {
            $subSubJenisTindakan['id'] = $subTindakanRadiologi->id;
            $subSubJenisTindakan['tindakan'] = $subTindakanRadiologi->tindakan;
            $subSubJenisTindakan['jenis'] = $subTindakanRadiologi->deskripsi;
          }
          $subJenisTindakan[] = $subSubJenisTindakan;
        }
        $jenisTindakan['subJenis'] = $subJenisTindakan;
      } else {
        $jenisTindakan['id'] = $tindakanRadiologi->id;
        $jenisTindakan['tindakan'] = $tindakanRadiologi->tindakan;
        $jenisTindakan['jenis'] = $tindakanRadiologi->deskripsi;
      }
      $dataTindakanRadiologi[] = $jenisTindakan;
    }

    //Tindakan Prosedur
    $resultTindakanProsedur = $this->masterModel->tindakanPenunjang('105060101');
    $dataTindakanProsedur = array();
    foreach ($resultTindakanProsedur->result() as $tindakanProsedur) {
      $resultTindakanProsedurSub = $this->masterModel->tindakanPenunjang('105060101', $tindakanProsedur->id);
      $jenisTindakan = array();
      if ($resultTindakanProsedurSub->num_rows() > 0) {
        $jenisTindakan['id'] = $tindakanProsedur->id;
        $jenisTindakan['jenis'] = $tindakanProsedur->deskripsi;
        $subJenisTindakan = array();
        foreach ($resultTindakanProsedurSub->result() as $subTindakanProsedur) {
          $subsubJenisTindakan = array();
          $subsubJenisTindakan['id'] = $subTindakanProsedur->id;
          $subsubJenisTindakan['tindakan'] = $subTindakanProsedur->tindakan;
          $subsubJenisTindakan['jenis'] = $subTindakanProsedur->deskripsi;
          $subJenisTindakan[] = $subsubJenisTindakan;
        }
        $jenisTindakan['subJenis'] = $subJenisTindakan;
      }
      $dataTindakanProsedur[] = $jenisTindakan;
    }

    $historySpectrOptia = $this->pengkajianAwalModel->historySpectrOptia();

    ///////////////////////////////////////// HISTORY PENGKAJIAN //////////////////////////////
    $hPengkajian = $this->pengkajianAwalModel->listHistoryPengkajianMedis($nomr);

    ///////////////////////////////////// HISTORY GERIATRI PPPG ///////////////////////////
    $hgeriatripppg = $this->pengkajianAwalModel->historyGeriatriPppg($nomr);

    ///////////////////////////////////////// HISTORY Protokol Kemoterapi //////////////////////////////
    $hProtokolKemo = $this->pengkajianAwalModel->historyProtokolKemo($nomr);

    ///////////////////////////////////////// HISTORY List Kardek //////////////////////////////
    $hKardekObat = $this->pengkajianAwalModel->historyKardekObat($nomr);

    ///////////////////////////////////////// HISTORY Order //////////////////////////////
    $hOrderRadiologi = $this->pengkajianAwalModel->historyOrderRadiologi($nomr);

    /////////////////////////////////////////////////////////////////////////////////////

    $hOrderProsedur = $this->pengkajianAwalModel->historyOrderProsedurDiagnostik($nokun);

    $getTandaVitalTriase = $this->pengkajianAwalModel->getTandaVitalTriase($nokun);

    /////////////////////////////////////////  History Permintaan Dirawat //////////////////////////////
    $listHistoryDirawat = $this->pengkajianAwalModel->listHistoryDirawat($nomr);

    /////////////////////////////////////////  History CPO //////////////////////////////
    $historyCPO = $this->pengkajianAwalModel->historyCPO($nomr);

    /////////////////////////////////////////  History Echo EKG //////////////////////////////
    $historyEchoEKG = $this->pengkajianAwalModel->historyEchoEKG($nomr);

    ///////////////////////////////////////// HISTORY Persiapan Bronkoskopi //////////////////////////////
    //$historyPersiapanBronkoskopi = $this->pengkajianAwalModel->historyPersiapanBronkoskopi();
    // print_r($historyPersiapanBronkoskopi);exit;

    ///////////////////////////////////////// HISTORY Persiapan Gastroskopi //////////////////////////////
    $historyPersiapanGastroskopi = $this->pengkajianAwalModel->historyPersiapanGastroskopi();

    ///////////////////////////////////////// HISTORY Persiapan Spirometri //////////////////////////////
    $historyPersiapanSpirometri = $this->pengkajianAwalModel->historyPersiapanSpirometri();

    ///////////////////////////////////////// HISTORY Persiapan Laporan Hasil Pemeriksaan //////////////////////////////
    $historyLaporanHasilPemeriksaan = $this->pengkajianAwalModel->historyLaporanHasilPemeriksaan($nomr);

    /////////////////////////////////////////  History PMFAK //////////////////////////////
    $historyPMFAK = $this->pengkajianAwalModel->historyPMFAK($nomr);

    /////////////////////////////////////////  History Pengkajian PAK //////////////////////////////
    $historyPengkajianPAK = $this->pengkajianAwalModel->historyPengkajianPAK($nomr);

    /////////////////////////////////////////  History Serah Terima Shift IGD //////////////////////////////
    $historySerahTerimaIGD = $this->SerahTerimaShiftModel->history($nomr);

    /////////////////////////////////////////  History MIni Cog //////////////////////////////
    $historyMiniCog = $this->pengkajianAwalModel->historyMiniCog($nomr);
    $historyRekonsiliasiObatIGD = $this->pengkajianAwalModel->historyRekonsiliasiObatIGD($nomr);
    $historyTrombosisFeresis = $this->pengkajianAwalModel->historyTrombosisFeresis($nomr);
    $historyPersetujuanTransfusiD = $this->pengkajianAwalModel->historyPersetujuanTransfusiD($nomr);

    /////////////////////////////////////////  History Pengkajian PAK //////////////////////////////
    $listPengkajianPAK = $this->pengkajianAwalModel->listPengkajianPAK($nomr);
    $listPengkajianPAKMedis = $this->pengkajianAwalModel->listPengkajianPAKMedis($nomr);

    $historyHemodialisa = $this->hemodialisaModel->historyHemodialisa($nomr);
    $historyPrHemodialisa = $this->hemodialisaModel->historyPrHemodialisa($nomr);

    //////// History Pra Sedasi ////////
    $historyPengkajianPraSedasi = $this->pengkajianAwalModel->historyPengkajianPraSedasi($nomr);
    $listPengkajianPraSedasi = $this->pengkajianAwalModel->listPengkajianPraSedasi($nomr);

    /////// Geriatri List ///////
    $listGeriatriPppg = $this->pengkajianAwalModel->listGeriatriPppg($nokun);

    // Tanda Vital Status Anestesia
    $historyTandaVital_SA = $this->pengkajianAwalModel->historyTandaVital_SA($nomr);

    ////// History Pemulihan Sedasi /////////////
    $historyPemulihanSedasi = $this->pengkajianAwalModel->historyPemulihanSedasi($nomr);
    $historyPemulihanNapas = $this->pengkajianAwalModel->historyPemulihanNapas($nomr);

    /////// History Pemberian Intravena ////////////////
    $historyPemberianIntravena = $this->pengkajianAwalModel->historyPemberianIntravena($nomr);

    ////// History Ceklis Tindakan Aferesis Donor /////////////
    $historyCeklisTindakanAferesis = $this->pengkajianAwalModel->historyCeklisTindakanAferesis($nomr);

    ////// History Preaferesis /////////////
    $historyPreaferesis = $this->pengkajianAwalModel->historyPreaferesis($nomr);

    ////// History Barthel Indek /////////////
    $historyBarthelIndek = $this->pengkajianAwalModel->historyBarthelIndek($nomr);

    ////// History Abbreviated Mental Test /////////////
    $historyAbbreviatedMentalTest = $this->pengkajianAwalModel->historyAbbreviatedMentalTest($nomr);

    ////// History Abbreviated Mental Test /////////////
    $historyTindakanDonorAferesis = $this->pengkajianAwalModel->historyTindakanDonorAferesis($nomr);

    ////// History Persetujuan Tindakan Kemoterapi LLA Anak /////////////
    $historyPtKemoLlaAnak = $this->pengkajianAwalModel->historyPtKemoLlaAnak($nomr);

    ///// History Pengkajian Risiko Jatuh Pasien Dewasa /////////////
    $historyPengkajianRisikoJatuhPasienDewasa = $this->pengkajianAwalModel->historyPengkajianRisikoJatuhPasienDewasa($nomr);

    // Formulir Pemindahan Pasien
    $riwayatPengobatan = $this->masterModel->referensi(329);
    $kondisiSaatIni = $this->masterModel->referensi(330);
    $score = $this->masterModel->referensi(331);
    $resikoJatuh = $this->masterModel->referensi(333);
    $pemeriksaanPenunjang = $this->masterModel->referensi(351);
    $kondisiKulit = $this->masterModel->referensi(844);
    $mobilisasiMerujukPasien = $this->masterModel->referensi(845);
    $terapi = $this->masterModel->referensi(334);
    $nutrisi = $this->masterModel->referensi(335);
    $perawatan = $this->masterModel->referensi(336);
    $lainPr = $this->masterModel->referensi(337);
    $riwayatPenyakitMenular = $this->masterModel->referensi(338);
    $ruanganRskd = $this->masterModel->ruanganRskd();

    //Serah Terima Prosedur
    $kondisisaatiniprosedurpratindakan = $this->masterModel->referensi(418);
    $kondisisaatiniprosedurpascatindakan = $this->masterModel->referensi(437);
    $penyakitmenular = $this->masterModel->referensi(400);
    $suratizintindakan = $this->masterModel->referensi(401);
    $penandaansisioperasi = $this->masterModel->referensi(402);
    $hasilpemeriksaanpratindakan = $this->masterModel->referensi(403);
    $hasilpemeriksaanpascatindakan = $this->masterModel->referensi(414);
    $fotopratindakan = $this->masterModel->referensi(404);
    $ctscanpratindakan = $this->masterModel->referensi(405);
    $usgpratindakan = $this->masterModel->referensi(406);
    $mripratindakan = $this->masterModel->referensi(407);
    $echocardiografipratindakan = $this->masterModel->referensi(408);
    $fotopascatindakan = $this->masterModel->referensi(409);
    $ctscanpascatindakan = $this->masterModel->referensi(410);
    $usgpascatindakan = $this->masterModel->referensi(411);
    $mripascatindakan = $this->masterModel->referensi(412);
    $echocardiografipascatindakan = $this->masterModel->referensi(413);
    $risikojatuhprosedur = $this->masterModel->referensi(438);
    $kesadaran_pasca = $this->masterModel->referensi(446);
    $risikojatuhprosedur_pasca = $this->masterModel->referensi(447);
    $implant_pra = $this->masterModel->referensi(439);
    $implant_pasca = $this->masterModel->referensi(448);
    $pemakaian_alat_bantu_pra = $this->masterModel->referensi(440);
    $pemakaian_alat_bantu_pasca = $this->masterModel->referensi(449);
    $gigi_palsu_pra = $this->masterModel->referensi(441);
    $gigi_palsu_pasca = $this->masterModel->referensi(450);
    $ketersediaandarah = $this->masterModel->referensi(442);
    $ketersediaanalatobat = $this->masterModel->referensi(443);
    $instruksipratindakan = $this->masterModel->referensi(444);
    $instruksipascatindakan = $this->masterModel->referensi(445);
    $program_terapi_pra = $this->masterModel->referensi(451);
    $program_terapi_pasca = $this->masterModel->referensi(452);
    $rencanaserahterima = $this->masterModel->referensi(478);

    if ($this->uri->segment(7) != "" && $this->session->userdata('status') == 2) {
      $id_spectra_optia = $this->uri->segment(7);
      $getSpectraOptia = $this->pengkajianAwalModel->getSpectraOptia($id_spectra_optia);
    }
    if ($this->uri->segment(5) != "" && $this->session->userdata('status') == 1) {
      $nokun_pra_operasi = $this->uri->segment(5);
      $getpraoperasi = $this->pengkajianAwalModel->getpraoperasi($nokun_pra_operasi);
    }
    if ($this->uri->segment(5) != "" && $this->session->userdata('status') == 1) {
      $nokun_daftar_operasi = $this->uri->segment(5);
      $getdaftaroperasi = $this->pengkajianAwalModel->detail($nokun_daftar_operasi);
    }
    if ($this->uri->segment(5) != "") {
      $nokun_merujuk_pasien = $this->uri->segment(5);
      $getmerujukpasien = $this->pengkajianAwalModel->getmerujukpasien($nokun_merujuk_pasien);
    }

    $historypraoperasi = $this->pengkajianAwalModel->historypraoperasi();
    $historydaftaroperasi = $this->pengkajianAwalModel->historydaftaroperasi();
    $historyMerujukPasien = $this->pengkajianAwalModel->historyMerujukPasien();
    // print_r($getLaporanOperasi);exit;

    // Serah Terima
    // $historySerahTerima = $this->pengkajianAwalModel->historySerahTerima();
    // if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105060101) {
    //   $id_serahterima = $this->uri->segment(7);
    //   $getSerahTerima = $this->pengkajianAwalModel->getSerahTerima($id_serahterima);
    // }
    // print_r($getSerahTerima);exit();

    // Evaluasi Kemampuan Fungsional Mobilisasi
    $historyekfm = $this->pengkajianAwalModel->historyekfm();
    if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105110101) {
      $id_ekfm = $this->uri->segment(7);
      $getekfm = $this->pengkajianAwalModel->getekfm($id_ekfm);
    }

    //print_r($getekfm);exit();

    //Tindakan Invasif Keperawatan
    //$getIDEMR_InvasifKP = "";
    //$hPengkajianTindakanInvasif = $this->pengkajianAwalModel->historyPengkajianTindakanInvasif();
    //if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105060101 || $this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105100101) {
    //$id_tindakaninvasif = $this->uri->segment(7);
    //$getPengkajianTindakanInvasif = $this->pengkajianAwalModel->getPengkajianTindakanInvasif($id_tindakaninvasif);
    //$getIDEMR_InvasifKP = $this->pengkajianAwalModel->getIDEMR_InvasifKP($id_tindakaninvasif);
    // }

    //Tindakan Intervensi Keperawatan
    $getIDEMR_IntervensiKP = "";
    $hPengkajianTindakanIntervensi = $this->pengkajianAwalModel->historyPengkajianTindakanIntervensi();
    if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105100101) {
      $id_tindakanIntervensi = $this->uri->segment(7);
      $getPengkajianTindakanIntervensi = $this->pengkajianAwalModel->getPengkajianTindakanIntervensi($id_tindakanIntervensi);
      $getIDEMR_IntervensiKP = $this->pengkajianAwalModel->getIDEMR_IntervensiKP($id_tindakanIntervensi);
    }
    //print_r($getIDEMR_IntervensiKP);exit();

    //Tindakan Invasif Medis
    $getIDEMR_Invasif = "";
    $hPengkajianTindakanInvasifMedis = $this->pengkajianAwalModel->historyPengkajianTindakanInvasifMedis();
    if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105060101 || $this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105100101) {
      $id_tindakaninvasifmedis = $this->uri->segment(7);
      $getPengkajianTindakanInvasifMedis = $this->pengkajianAwalModel->getPengkajianTindakanInvasifMedis($id_tindakaninvasifmedis);
      $getIDEMR_Invasif = $this->pengkajianAwalModel->getIDEMR_Invasif($id_tindakaninvasifmedis);
    }

    //Tindakan Intervensi Medis
    $getIDEMR_Intervensi = "";
    $hPengkajianTindakanIntervensiMedis = $this->pengkajianAwalModel->historyPengkajianTindakanIntervensiMedis();
    if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105100101) {
      $id_tindakanIntervensimedis = $this->uri->segment(7);
      $getPengkajianTindakanIntervensiMedis = $this->pengkajianAwalModel->getPengkajianTindakanIntervensiMedis($id_tindakanIntervensimedis);
      $getIDEMR_Intervensi = $this->pengkajianAwalModel->getIDEMR_Intervensi($id_tindakanIntervensimedis);
    }
    // print_r($getIDEMR_Intervensi);exit();

    // print_r($getPengkajianTindakanInvasifMedis);exit();

    // Keselamatan Tindakan Invasif
    // $getKTI = "";
    // $historyKTI = $this->pengkajianAwalModel->historyKTI();
    // if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105060101) {
    //     $id_kti = $this->uri->segment(7);
    //     $getKTI = $this->pengkajianAwalModel->getKTI($id_kti);
    // }
    //History Pengkajian Radiologi Nuklir
    $hPengkajianRadiologiNuklir = $this->pengkajianAwalModel->historyPengkajianRadiologiNuklir();
    if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105100101) {
      $id_radiologiNuklir = $this->uri->segment(7);
      $getPengkajianRadiologiNuklir = $this->pengkajianAwalModel->getPengkajianRadiologiNuklir($id_radiologiNuklir);
    }

    // Get Tanda Vital IGD dari Triase
    if ($this->uri->segment(5) != "" && $getNomr['ID_RUANGAN'] == 105140101) {
      $nokun_igd = $this->uri->segment(5);
      $get_tv = $this->pengkajianAwalModel->get_tv($nokun_igd);
    }

    // print_r($get_tv);exit();

    // Pemantauan Anestesi Lokal
    // $getPALPemantauan = "";
    // $historyPAL = $this->pengkajianAwalModel->historyPAL();
    // if ($this->uri->segment(7) != "") {
    //     $id_pal = $this->uri->segment(7);
    //     $getPAL = $this->pengkajianAwalModel->getPAL($id_pal);
    //     $getPALPemantauan = $this->pengkajianAwalModel->getPALPemantauan($id_pal);
    // }
    //print_r($getPALPemantauan);exit();

    //IGD Skrining Visual
    $pinereIgd = $this->masterModel->referensi(468);
    $tuberkolosisIgd = $this->masterModel->referensi(469);
    $mobilisasiIgd = $this->masterModel->referensi(470);
    $resikoJatuhIgd = $this->masterModel->referensi(471);
    $labIgd = $this->masterModel->referensi(472);
    $radIgd = $this->masterModel->referensi(473);
    $paIgd = $this->masterModel->referensi(474);
    $ditujukanIgd = $this->masterModel->referensi(475);
    $kebutuhanPelayananIgd = $this->masterModel->referensi(476);
    $dirujukIgd = $this->masterModel->referensi(477);

    //IGD Triase
    $atsTriase1 = $this->masterModel->referensi(479);
    $atsTriase2 = $this->masterModel->referensi(480);
    $atsTriase3 = $this->masterModel->referensi(481);
    $atsTriase4 = $this->masterModel->referensi(482);
    $atsTriase5 = $this->masterModel->referensi(483);
    $jenisKunjunganTriase = $this->masterModel->referensi(485);
    $triaseIgd = $this->masterModel->referensi(486);

    //Pra Anestesi
    $hilangnya_gigi = $this->masterModel->referensi(633);
    $masalah_leher = $this->masterModel->referensi(664);
    $denyut_jantung = $this->masterModel->referensi(665);
    $batuk = $this->masterModel->referensi(666);
    $sesak_napas = $this->masterModel->referensi(667);
    $baru_saja_menderita_infeksi = $this->masterModel->referensi(668);
    $saluran_napas_atas = $this->masterModel->referensi(669);
    $sakit_dada = $this->masterModel->referensi(670);
    $muntah = $this->masterModel->referensi(671);
    $pingsan = $this->masterModel->referensi(672);
    $stroke = $this->masterModel->referensi(673);
    $kejang = $this->masterModel->referensi(674);
    $sedang_hamil = $this->masterModel->referensi(675);
    $kelainan_tulang_belakang = $this->masterModel->referensi(676);
    $obesitas = $this->masterModel->referensi(677);
    $klasifikasi_asa = $this->masterModel->referensi(655);
    $teknik_anestesia = $this->masterModel->referensi(740);
    $teknik_khusus = $this->masterModel->referensi(741);
    $monitoring = $this->masterModel->referensi(742);
    $alat_khusus = $this->masterModel->referensi(743);
    $perawatan_pasca_anestesi = $this->masterModel->referensi(744);

    // IGD Kriteria Pasien Masuk Rawat Intensif
    $fkri_gangguan_kardiovaskular = $this->masterModel->referensi(811);
    $fkri_gangguan_pernapasan = $this->masterModel->referensi(812);
    $fkri_gangguan_neurologi = $this->masterModel->referensi(813);
    $fkri_gangguan_gastrointestinal = $this->masterModel->referensi(814);
    $fkri_gangguan_metabolik = $this->masterModel->referensi(815);
    $fkri_kegawatdaruratan_onkologi = $this->masterModel->referensi(817);
    $fkri_kriteria_eksklusi = $this->masterModel->referensi(818);
    $fkri_ruangan_masuk = $this->masterModel->referensi(829);

    $history_fkri = $this->pengkajianAwalModel->history_fkri($nomr);
    if ($this->uri->segment(7) != "") {
      $id_fkri = $this->uri->segment(7);
      $get_fkri = $this->pengkajianAwalModel->get_fkri($id_fkri);
    }

    // IGD Kriteria Pasien Masuk Rawat PICU
    $fkrp_kriteria_eksklusi = $this->masterModel->referensi(818);
    $fkrp_gangguan_kardiovaskular = $this->masterModel->referensi(820);
    $fkrp_usia = $this->masterModel->referensi(830);
    $fkrp_frekuensi_nadi = $this->masterModel->referensi(831);
    $fkrp_tekanan_darah_sistolik = $this->masterModel->referensi(832);
    $fkrp_gangguan_pernapasan = $this->masterModel->referensi(821);
    $fkrp_gangguan_neurologi = $this->masterModel->referensi(822);
    $fkrp_gangguan_gastrointestinal = $this->masterModel->referensi(823);
    $fkrp_gangguan_endokrin_metabolik = $this->masterModel->referensi(824);
    $fkrp_kegawatdaruratan_onkologi = $this->masterModel->referensi(826);

    // Bank Darah Persetujuan Tindakan Donor Aferesis
    $listDiagnosisBanding = $this->masterModel->referensi(953);
    $listDasarDiagnosis = $this->masterModel->referensi(954);
    $listTindakanKedokteran = $this->masterModel->referensi(955);
    $listIndikasiTindakan = $this->masterModel->referensi(956);
    $listTataCara = $this->masterModel->referensi(957);
    $listTujuanTindakan = $this->masterModel->referensi(958);
    $listTujuanPengobatan = $this->masterModel->referensi(959);
    $listRisiko = $this->masterModel->referensi(960);
    $listKomplikasi = $this->masterModel->referensi(961);
    $listPrognosis = $this->masterModel->referensi(962);
    $listAlternatif = $this->masterModel->referensi(963);
    $listAllPegawai = $this->masterModel->listAllPegawai();

    // Pengkajian Risiko Jatuh Pasien Dewasa
    $listRiwayatJatuh = $this->masterModel->referensi(1006);
    $listDiagnosisSekunder = $this->masterModel->referensi(1007);
    $listAlatBantu = $this->masterModel->referensi(1008);
    $listMenggunakanInfus = $this->masterModel->referensi(1009);
    $listCaraBerjalan = $this->masterModel->referensi(1010);
    $listStatusMental = $this->masterModel->referensi(1011);

    $history_fkrp = $this->pengkajianAwalModel->history_fkrp($nomr);
    if ($this->uri->segment(7) != "") {
      $id_fkrp = $this->uri->segment(7);
      $get_fkrp = $this->pengkajianAwalModel->get_fkrp($id_fkrp);
    }

    // $historypraanestesi = $this->pengkajianAwalModel->historypraanestesi();
    // if ($this->uri->segment(7) != "") {
    //     $idpra_anestesi = $this->uri->segment(7);
    //     $getpra_anestesi = $this->pengkajianAwalModel->getpra_anestesi($idpra_anestesi);
    // }

    $history_iadl = $this->pengkajianAwalModel->history_iadl($nomr);
    if ($this->uri->segment(7) != "") {
      $id_iadl = $this->uri->segment(7);
      $get_iadl = $this->pengkajianAwalModel->get_iadl($id_iadl);
    }

    $history_adl = $this->pengkajianAwalModel->history_adl($nomr);
    if ($this->uri->segment(7) != "") {
      $id_adl = $this->uri->segment(7);
      $get_adl = $this->pengkajianAwalModel->get_adl($id_adl);
    }

    // print_r($get_adl);exit();

    //Operasi
    $siteMarking = $this->masterModel->siteMarking();

    //History Perencanaan Asuhan Keperawatan
    $tblhistoryPAK = $this->pengkajianAwalModel->historyPAK($nomr);

    //Geriatri

    $data = array(
      'title' => 'Form Pengkajian Awal Rawat Jalan',
      'isi' => 'Pengkajian/index',
      'nomr' => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'id_pengguna' => $id_pengguna,
      'getNomr' => $getNomr,
      'statusPengguna' => $statusPengguna,
      'id_ruangan' => $id_ruangan,
      'rawat' => $rawat,
      'idemr' => $this->uri->segment(7) != "" ? $idemr : "",

      //IGD Formulir Kriteria Pasien Masuk Rawat Intensif
      'fkri_gangguan_kardiovaskular' => $fkri_gangguan_kardiovaskular,
      'fkri_gangguan_pernapasan' => $fkri_gangguan_pernapasan,
      'fkri_gangguan_neurologi' => $fkri_gangguan_neurologi,
      'fkri_gangguan_gastrointestinal' => $fkri_gangguan_gastrointestinal,
      'fkri_gangguan_metabolik' => $fkri_gangguan_metabolik,
      'fkri_kegawatdaruratan_onkologi' => $fkri_kegawatdaruratan_onkologi,
      'fkri_kriteria_eksklusi' => $fkri_kriteria_eksklusi,
      'fkri_ruangan_masuk' => $fkri_ruangan_masuk,

      //IGD Formulir Kriteria Pasien Masuk Rawat PICU
      'fkrp_kriteria_eksklusi' => $fkrp_kriteria_eksklusi,
      'fkrp_gangguan_kardiovaskular' => $fkrp_gangguan_kardiovaskular,
      'fkrp_usia' => $fkrp_usia,
      'fkrp_frekuensi_nadi' => $fkrp_frekuensi_nadi,
      'fkrp_tekanan_darah_sistolik' => $fkrp_tekanan_darah_sistolik,
      'fkrp_gangguan_pernapasan' => $fkrp_gangguan_pernapasan,
      'fkrp_gangguan_neurologi' => $fkrp_gangguan_neurologi,
      'fkrp_gangguan_gastrointestinal' => $fkrp_gangguan_gastrointestinal,
      'fkrp_gangguan_endokrin_metabolik' => $fkrp_gangguan_endokrin_metabolik,
      'fkrp_kegawatdaruratan_onkologi' => $fkrp_kegawatdaruratan_onkologi,

      // Bank Darah Tindakan Donor Aferesis
      'listDiagnosisBanding' => $listDiagnosisBanding,
      'listDasarDiagnosis' => $listDasarDiagnosis,
      'listTindakanKedokteran' => $listTindakanKedokteran,
      'listIndikasiTindakan' => $listIndikasiTindakan,
      'listTataCara' => $listTataCara,
      'listTujuanTindakan' => $listTujuanTindakan,
      'listTujuanPengobatan' => $listTujuanPengobatan,
      'listRisiko' => $listRisiko,
      'listKomplikasi' => $listKomplikasi,
      'listPrognosis' => $listPrognosis,
      'listAlternatif' => $listAlternatif,
      'listAllPegawai' => $listAllPegawai,

      'masalahKesehatanRj' => $this->masterModel->formMasalahKesehatan(),

      // Pengkajian Risiko Jatuh Pasien Dewasa
      'listRiwayatJatuh' => $listRiwayatJatuh,
      'listDiagnosisSekunder' => $listDiagnosisSekunder,
      'listAlatBantu' => $listAlatBantu,
      'listMenggunakanInfus' => $listMenggunakanInfus,
      'listCaraBerjalan' => $listCaraBerjalan,
      'listStatusMental' => $listStatusMental,

      //MASTER KEPERAWATAN
      'riwayatKanker' => $riwayatKanker,
      'riwayatAlergi' => $riwayatAlergi,
      'kesadaran' => $kesadaran,
      'skriningNyeri' => $skriningNyeri,
      'psikologis' => $psikologis,
      'sosialDanEkonomiHubungan' => $sosialDanEkonomiHubungan,
      'sosialDanEkonomiPencariNafkah' => $sosialDanEkonomiPencariNafkah,
      'sosialDanEkonomiTinggalSerumah' => $sosialDanEkonomiTinggalSerumah,
      'spiritualKultural' => $spiritualKultural,
      'alatBantu' => $alatBantu,
      'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
      'pengkajianNyeriQuality' => $pengkajianNyeriQuality,
      'pengkajianNyeriTime' => $pengkajianNyeriTime,
      'statusFungsional' => $statusFungsional,
      'skriningResikoJatuhPusing' => $skriningResikoJatuhPusing,
      'skriningResikoJatuhBerdiri' => $skriningResikoJatuhBerdiri,
      'skriningResikoJatuh6Bulan' => $skriningResikoJatuh6Bulan,
      'komponenPenilaian' => $komponenPenilaian,
      'komponenPenilaianAsupan' => $komponenPenilaianAsupan,
      'pendidikan' => $pendidikan,
      'bahasaSehari' => $bahasaSehari,
      'perluPenerjemah' => $perluPenerjemah,
      'kesediaanInformasi' => $kesediaanInformasi,
      'hambatan' => $hambatan,
      'kebutuhanPembelajaran' => $kebutuhanPembelajaran,
      'skalaNyeriNRS' => $skalaNyeriNRS,
      'skalaNyeriWBR' => $skalaNyeriWBR,
      'skalaNyeriFLACC' => $skalaNyeriFLACC,
      'skalaNyeriBPS' => $skalaNyeriBPS,
      'efeksampingNRS' => $efeksampingNRS,
      'efeksampingWBR' => $efeksampingWBR,
      'efeksampingFLACC' => $efeksampingFLACC,
      'efeksampingBPS' => $efeksampingBPS,
      'statusnyeriNRS' => $statusnyeriNRS,
      'statusnyeriWBR' => $statusnyeriWBR,
      'statusnyeriFLACC' => $statusnyeriFLACC,
      'statusnyeriBPS' => $statusnyeriBPS,
      'riwayatTransfusiDarah' => $riwayatTransfusiDarah,
      'riwayatTransfusiDarahDesk' => $riwayatTransfusiDarahDesk,
      'kebiasaanmerokok' => $kebiasaanmerokok,
      'riwayatMetabolik' => $riwayatMetabolik,
      'riwayatDDK' => $riwayatDDK,
      'pengobatanAlternatif' => $pengobatanAlternatif,
      'pengobatanBertentangan' => $pengobatanBertentangan,
      'formAsuhanKeperawatan' => $formAsuhanKeperawatan,
      'fotodepanbelakang' => $fotodepanbelakang,
      'fotodepan' => $fotodepan,
      'fotobelakang' => $fotobelakang,

      // MASTER KEPERAWATAN ANAK
      'caraKelahiran' => $caraKelahiran,
      'kondisiLahir' => $kondisiLahir,
      'kelainanBawaan' => $kelainanBawaan,
      'riwayatTumbuhKembang' => $riwayatTumbuhKembang,
      'riwayatImunisasiDasar' => $riwayatImunisasiDasar,
      'bentukKepala' => $bentukKepala,
      'iramaPernapasan' => $iramaPernapasan,
      'retraksiDada' => $retraksiDada,
      'alatBantuNapas' => $alatBantuNapas,
      'sianosis' => $sianosis,
      'pucat' => $pucat,
      'capillaryRefillTest' => $capillaryRefillTest,
      'akral' => $akral,
      'pembesaranKelenjarGetahBening' => $pembesaranKelenjarGetahBening,
      'gangguanNeurologi' => $gangguanNeurologi,
      'neurologiMata' => $neurologiMata,
      'mulut' => $mulut,
      'abdomen' => $abdomen,
      'asites' => $asites,
      'defekasi' => $defekasi,
      'karakteristikFeses' => $karakteristikFeses,
      'urin' => $urin,
      'rectal' => $rectal,
      'genetalia' => $genetalia,
      'kulit' => $kulit,
      'warnaKulit' => $warnaKulit,
      'luka' => $luka,
      'lokasiLuka' => $lokasiLuka,
      'kelainanTulang' => $kelainanTulang,
      'gerakanAnak' => $gerakanAnak,
      'statusMentalDanTingkahLaku' => $statusMentalDanTingkahLaku,
      'tempatTinggal' => $tempatTinggal,
      'pengasuh' => $pengasuh,
      'jenisSekolah' => $jenisSekolah,
      'strongKidsKurus' => $strongKidsKurus,
      'strongKidsTurunBerat' => $strongKidsTurunBerat,
      'strongKidsKondisi' => $strongKidsKondisi,
      'strongKidsMalnutrisi' => $strongKidsMalnutrisi,

      //MASTER MEDIS
      'jenisKunjungan' => $jenisKunjungan,
      'riwayatPenyakitKeluarga' => $riwayatPenyakitKeluarga,
      'riwayatEkstravasasi' => $riwayatEkstravasasi,
      'hasilLaboratorium' => $hasilLaboratorium,
      'hasilBmpTerakhir' => $hasilBmpTerakhir,
      'kemoterapiTerdahulu' => $kemoterapiTerdahulu,
      'tindakanPerawatanTerakhir' => $tindakanPerawatanTerakhir,
      'riwayatGVHD' => $riwayatGVHD,
      'ESASnyeri' => $ESASnyeri,
      'ESASlelah' => $ESASlelah,
      'ESASmual' => $ESASmual,
      'ESASdepresi' => $ESASdepresi,
      'ESAScemas' => $ESAScemas,
      'ESASmengantuk' => $ESASmengantuk,
      'ESASnafsuMakan' => $ESASnafsuMakan,
      'ESASsehat' => $ESASsehat,
      'ESASsesakNapas' => $ESASsesakNapas,
      'ESASmasalah' => $ESASmasalah,
      'klinis' => $klinis,
      'radiasilokal' => $radiasilokal,
      'radiasijenisfraksinasi' => $radiasijenisfraksinasi,
      'penempatanSumber' => $penempatanSumber,
      'brakhiterapiTeknik' => $brakhiterapiTeknik,
      'tindakLanjutLain' => $tindakLanjutLain,
      'lokalLokoregional' => $lokalLokoregional,
      'ctsimulatordengankontras' => $ctsimulatordengankontras,
      'getTandaVitalTriase' => $getTandaVitalTriase,

      //MASTER MEDIS
      'anamnesis' => $anamnesis,
      'riwayatPenyakitDahulu' => $riwayatPenyakitDahulu,
      // 'riwayatAlergi'             => $riwayatAlergi,
      'performanceStatus' => $performanceStatus,
      'ps2' => $ps2,
      'karnofsky' => $karnofsky,
      'lansky' => $lansky,
      'mata' => $mata,
      'leher' => $leher,
      'dadaIramaJantung' => $dadaIramaJantung,
      'dadaSuaraNafas' => $dadaSuaraNafas,
      'perutHati' => $perutHati,
      'perutLimpa' => $perutLimpa,
      'ekstremitasAtasKanan' => $ekstremitasAtasKanan,
      'ekstremitasAtasKiri' => $ekstremitasAtasKiri,
      'ekstremitasBawahKanan' => $ekstremitasBawahKanan,
      'ekstremitasBawahKiri' => $ekstremitasBawahKiri,
      'kulitTurgor' => $kulitTurgor,
      'kulitSianosis' => $kulitSianosis,
      'refleks' => $refleks,
      'kelenjarGetahBening' => $kelenjarGetahBening,
      'aksesVaskuler' => $aksesVaskuler,
      'aksesVaskulerLokasi' => $aksesVaskulerLokasi,
      'aksesVaskulerCDL' => $aksesVaskulerCDL,
      'tumor' => $tumor,
      'laboratorium' => $laboratorium,
      'radiologi' => $radiologi,
      'histopatologi' => $histopatologi,
      'sisiTubuh' => $sisiTubuh,
      'tujuanPengobatan' => $tujuanPengobatan,
      'masalahKeperawatan' => $masalahKeperawatan,
      'diet' => $diet,
      'jenisDiet' => $jenisDiet,
      'rujukanKe' => $rujukanKe,
      'alasanRujukan' => $alasanRujukan,
      'tindakLanjutTerapirujulan' => $tindakLanjutTerapirujulan,
      'statusFungsionalEksternal' => $statusFungsionalEksternal,
      'transportasiPasien' => $transportasiPasien,
      'diagnosisMedis' => $diagnosisMedis,
      'performanceStatus_2' => $performanceStatus_2,
      'patologiAnatomi' => $patologiAnatomi,
      'batasSayatan' => $batasSayatan,
      'invasiLimfovaskular' => $invasiLimfovaskular,
      'simulator' => $simulator,
      'ctsimulator' => $ctsimulator,
      'radiasiteknik' => $radiasiteknik,
      'radiasijenis' => $radiasijenis,
      'Ecog' => $Ecog,
      'Karnofsky' => $Karnofsky,
      'Lansky' => $Lansky,
      'kolaborasi' => $kolaborasi,
      'stadium' => $stadium,
      'masalahKesehatan' => $masalahKesehatan,
      'penunjangLaboratorium' => $penunjangLaboratorium,
      'riwayatpenggunaanobat' => $riwayatpenggunaanobat,
      'persiapandarah' => $persiapandarah,
      'persiapanalatkhusus' => $persiapanalatkhusus,

      'RisikoJatuhTR' => $RisikoJatuhTR,
      'terapiRater' => $terapiRater,
      'dataECOG' => $dataECOG,

      'kunjungan_pk' => $kunjungan_pk,
      'sitologi' => $sitologi,
      'histologi' => $histologi,
      'tindakan_rad' => $tindakan_rad,

      //Konsul
      'listDr' => $listDr,
      'listDrUmum' => $listDrUmum,
      'pilihProtokolKemo' => $pilihProtokolKemo,
      'listSMF' => $listSMF,
      'tindakanKonsul' => $tindakanKonsul,
      'konsulYgDiminta' => $konsulYgDiminta,

      // Radiologi Diagnostik / Nuklir
      'pasienHamil' => $pasienHamil,
      'pemberiObatKontras' => $pemberiObatKontras,
      'dosisYangDiberikan' => $dosisYangDiberikan,
      'caraPemberianRad' => $caraPemberianRad,
      'pemeriksaanFungsiGinjal' => $pemeriksaanFungsiGinjal,
      'pemeriksaanGulaDarah' => $pemeriksaanGulaDarah,
      'pemasanganMonitorSaturasi' => $pemasanganMonitorSaturasi,
      'pemberianOksigenasi' => $pemberianOksigenasi,
      'tindakanAnestesiSedasi' => $tindakanAnestesiSedasi,
      'dosisYangDiberikanNuklir' => $dosisYangDiberikanNuklir,
      'perluBowelPreparation' => $perluBowelPreparation,
      'perluObatDiuretik' => $perluObatDiuretik,
      'injeksiRadiofarmakaDi' => $injeksiRadiofarmakaDi,
      'wholeBodyScan' => $wholeBodyScan,
      'letakTangan' => $letakTangan,
      'dimulaiDari' => $dimulaiDari,
      'diabetesMellitus' => $diabetesMellitus,
      'hipertensi' => $hipertensi,
      'penyakitGinjal' => $penyakitGinjal,
      'penyakitJantung' => $penyakitJantung,
      'penyakitAsma' => $penyakitAsma,
      'traumaFraktur' => $traumaFraktur,
      'kejangRun' => $kejangRun,
      'operasiBiopsi' => $operasiBiopsi,
      'sistemikKanker' => $sistemikKanker,
      'terapiRadiasiSebelumnya' => $terapiRadiasiSebelumnya,
      'zatKontras' => $zatKontras,
      'riwayatClaustrophobia' => $riwayatClaustrophobia,
      'obatRutinDikonsumsi' => $obatRutinDikonsumsi,
      'posisiBerbaringRun' => $posisiBerbaringRun,
      'gangguanBuangAirKecil' => $gangguanBuangAirKecil,
      'colostomyBag' => $colostomyBag,
      'hamilAtauMenyusuiAnak' => $hamilAtauMenyusuiAnak,
      'menstruasiRun' => $menstruasiRun,
      'sedangHamilRun' => $sedangHamilRun,
      'asalRuanganRunRad' => $asalRuanganRunRad,
      'penandaanSisiLokasi' => $penandaanSisiLokasi,
      'inforConTin' => $inforConTin,
      'inforConTinAnesSed' => $inforConTinAnesSed,
      'pemPenunjangFoto' => $pemPenunjangFoto,
      'pemPenunjangCtScan' => $pemPenunjangCtScan,
      'pemPenunjangUSG' => $pemPenunjangUSG,
      'pemPenunjangMRI' => $pemPenunjangMRI,
      'pemPenunjangLab' => $pemPenunjangLab,
      'persediaanDarah' => $persediaanDarah,
      'ketersediaanImplant' => $ketersediaanImplant,
      'benarSisiLokasiTin' => $benarSisiLokasiTin,
      'khususPerhatikan' => $khususPerhatikan,
      'instrumenSudahBenar' => $instrumenSudahBenar,
      'fotoRadiologiSesuai' => $fotoRadiologiSesuai,
      'kelengkapanKasaJarum' => $kelengkapanKasaJarum,
      'spesimenBeriLabel' => $spesimenBeriLabel,
      'namaImplanDanLokasi' => $namaImplanDanLokasi,
      'peralatanYangPerlu' => $peralatanYangPerlu,
      'masalahHarusPerhatikan' => $masalahHarusPerhatikan,
      'riwayatTerapiTACETACI' => $riwayatTerapiTACETACI,
      'bilaAdaTACETACI' => $bilaAdaTACETACI,
      'dibawaDengan' => $dibawaDengan,

      // Pengkajian Operasi
      'sifatOperasi' => $sifatOperasi,
      'rencanaJenisPembiusan' => $rencanaJenisPembiusan,

      // Permintaan Dirawat
      'listKasus' => $listKasus,
      // 'listRencanaPerawatan' => $listRencanaPerawatan,
      'listRencana' => $listRencana,
      // 'listKebutuhanPelayanan' => $listKebutuhanPelayanan,
      'listPerbaikanKeadaanUmum' => $listPerbaikanKeadaanUmum,
      'listTindakan' => $listTindakan,
      'listDiet' => $listDiet,
      'listJenisDiet' => $listJenisDiet,
      'listOdc' => $listOdc,
      'listRawatInap' => $listRawatInap,
      'listRawatKhusus' => $listRawatKhusus,
      'listIntensiveCare' => $listIntensiveCare,

      // Form CPO
      'listObat' => $listObat,
      'listJenisObat' => $listJenisObat,

      // Form IGD
      'listPalliative' => $listPalliative,
      'listRiwayatKesehatan' => $listRiwayatKesehatan,
      'listKondisiPsikologis' => $listKondisiPsikologis,
      'listKondisi' => $listKondisi,
      'listKondisiPasien' => $listKondisiPasien,
      'listKondisiKeluarga' => $listKondisiKeluarga,
      'listBerduka' => $listBerduka,
      'listBerdukaPasien' => $listBerdukaPasien,
      'listBerdukaKeluarga' => $listBerdukaKeluarga,
      'listPotensiReaksi' => $listPotensiReaksi,
      'listPasienPsikolog' => $listPasienPsikolog,
      'listKeluargaPsikolog' => $listKeluargaPsikolog,
      'listKebutuhanSpiritual' => $listKebutuhanSpiritual,
      'listKebutuhanSpiritualPasien' => $listKebutuhanSpiritualPasien,
      'listKebutuhanSpiritualKeluarga' => $listKebutuhanSpiritualKeluarga,
      'listKebutuhanPendukung' => $listKebutuhanPendukung,
      'listTerapiKomplementer' => $listTerapiKomplementer,
      'listMembutuhkanCaregiver' => $listMembutuhkanCaregiver,
      'listPerawatanDirumah' => $listPerawatanDirumah,
      'listIntervensi' => $listIntervensi,
      'listMasalahKeperawatan' => $listMasalahKeperawatan,
      'listPerencanaanAsuhan' => $listPerencanaanAsuhan,
      'listKausatif' => $listKausatif,
      'listPenghentianIntervensi' => $listPenghentianIntervensi,
      'listserahTerimaDataPasienIGD' => $listserahTerimaDataPasienIGD,
      'listAlatTerpasangIGD' => $listAlatTerpasangIGD,
      'listMasalahAsuhanIGD' => $listMasalahAsuhanIGD,
      'listShiftIGD' => $listShiftIGD,
      'listKesadaranIGD' => $listKesadaranIGD,
      'listResikoJatuhIGD' => $listResikoJatuhIGD,
      'listOksigenIGD' => $listOksigenIGD,
      'listStatusPasienRekonsiliasi' => $listStatusPasienRekonsiliasi,
      'listRiwayatAlergiObat' => $listRiwayatAlergiObat,

      // Informed Consent
      'dasarDiagnosis' => $dasarDiagnosis,
      'indikasiTindakan' => $indikasiTindakan,
      'tujuanTindakan' => $tujuanTindakan,
      'tujuanPengobatan' => $tujuanPengobatan,
      'risikoTTD' => $risikoTTD,
      'komplikasiTTD' => $komplikasiTTD,
      'prognosisTTD' => $prognosisTTD,
      'alternatifRisikoTTD' => $alternatifRisikoTTD,
      'transfusiDarahTTD' => $transfusiDarahTTD,

      // Form Pra Sedasi
      'listKajianSistem' => $listKajianSistem,
      'listKajianSistem1' => $listKajianSistem1,
      'listKajianSistem2' => $listKajianSistem2,
      'listKajianSistem3' => $listKajianSistem3,
      'listKajianSistem4' => $listKajianSistem4,
      'listKajianSistem5' => $listKajianSistem5,
      'listKajianSistem6' => $listKajianSistem6,
      'listKajianSistem7' => $listKajianSistem7,
      'listKajianSistem8' => $listKajianSistem8,
      'listKajianSistem9' => $listKajianSistem9,
      'listKajianSistem10' => $listKajianSistem10,
      'listKajianSistem11' => $listKajianSistem11,
      'listKajianSistem12' => $listKajianSistem12,
      'listKajianSistem13' => $listKajianSistem13,
      'listKajianSistem14' => $listKajianSistem14,
      'listKajianSistem15' => $listKajianSistem15,
      'listPemriksaanFisik' => $listPemriksaanFisik,
      'listPemriksaanFisik1' => $listPemriksaanFisik1,
      'listPemriksaanFisik2' => $listPemriksaanFisik2,
      'listPemriksaanFisik3' => $listPemriksaanFisik3,
      'listPemriksaanFisik4' => $listPemriksaanFisik4,
      'listPemriksaanFisik5' => $listPemriksaanFisik5,
      'listPemriksaanFisik6' => $listPemriksaanFisik6,
      'listPemriksaanPenunjang' => $listPemriksaanPenunjang,
      'listKlasifikasiAsa' => $listKlasifikasiAsa,
      'listMonitoring' => $listMonitoring,
      'listPerawatanPascaSedasi' => $listPerawatanPascaSedasi,

      // Form Sedasi Kamar Pemulihan
      'listKesadaranSedasi' => $listKesadaranSedasi,
      'listPernapasanSedasi' => $listPernapasanSedasi,
      'listNyeriSedasi' => $listNyeriSedasi,
      'listRisikoJatuhSedasi' => $listRisikoJatuhSedasi,
      'listFrekuensiNapasSedasi' => $listFrekuensiNapasSedasi,
      'listFrekuensiNadiSedasi' => $listFrekuensiNadiSedasi,
      'listTekananDarahSedasi' => $listTekananDarahSedasi,
      'listSkalaNyeriSedasi' => $listSkalaNyeriSedasi,
      'listPerawat4' => $listPerawat4,
      'listPerawat5' => $listPerawat5,
      'listRuangPemulihan' => $listRuangPemulihan,
      'listScorePadss' => $listScorePadss,
      'listKeluarNyeri' => $listKeluarNyeri,
      'listPemulihanRisiko' => $listPemulihanRisiko,

      // Formulir Pasien Preaferesis Terapeutik (Stem Cells)
      'jenisDataPreaferesis' => $jenisDataPreaferesis,
      'jenisKelamin' => $jenisKelamin,
      'tidurPreaferesis' => $tidurPreaferesis,
      'statusKesadaran' => $statusKesadaran,

      // Form Bank Darah Ceklis Tindakan Aferesis
      'listFormCalonDonor' => $listFormCalonDonor,
      'listIdentifikasiDonor' => $listIdentifikasiDonor,
      'listRiwayatPenyakitDonor' => $listRiwayatPenyakitDonor,
      'listKeadaanUmumDonor' => $listKeadaanUmumDonor,
      'listPeriksaGolonganDarah' => $listPeriksaGolonganDarah,
      'listPeriksaLabPK' => $listPeriksaLabPK,
      'listHasilGolonganDarah' => $listHasilGolonganDarah,
      'listHasilLaboratorium' => $listHasilLaboratorium,
      'listDonorDatang' => $listDonorDatang,
      'listSuratPersetujuan' => $listSuratPersetujuan,
      'listMesinEferesis' => $listMesinEferesis,
      'listProtocolCard' => $listProtocolCard,
      'listPaketKitEferesis' => $listPaketKitEferesis,
      'listLarutanACD' => $listLarutanACD,
      'listPeralatanAntiseptik' => $listPeralatanAntiseptik,
      'listBahanAntiseptik' => $listBahanAntiseptik,
      'listCalciumGluconas' => $listCalciumGluconas,
      'listAdrenalin' => $listAdrenalin,
      'listAntiHistamin' => $listAntiHistamin,
      'listCorticoSteroid' => $listCorticoSteroid,
      'listDisposibleSyringe' => $listDisposibleSyringe,
      'listLKBIBD' => $listLKBIBD,
      'listProsedurDimulai' => $listProsedurDimulai,
      'listProsedurBerakhir' => $listProsedurBerakhir,
      'listDonorMeninggalkanRuang' => $listDonorMeninggalkanRuang,

      //Form Barthel Indek

      'listRangsangbab' => $listRangsangbab,
      'listRangsangberkemih' => $listRangsangberkemih,
      'listMembersihkandiri' => $listMembersihkandiri,
      'listPenggunaankloset' => $listPenggunaankloset,
      'listMakan' => $listMakan,
      'listBerubahposisi' => $listBerubahposisi,
      'listBerpindah' => $listBerpindah,
      'listMemakaibaju' => $listMemakaibaju,
      'listNaiktangga' => $listNaiktangga,
      'listMandi' => $listMandi,

      // Form Abbreviated Mental Test
      'listUmur' => $listUmur,
      'listJamBerapa' => $listJamBerapa,
      'listDimanaAlamat' => $listDimanaAlamat,
      'listTahunBerapa' => $listTahunBerapa,
      'listKitaDimana' => $listKitaDimana,
      'listMengenaliDokter' => $listMengenaliDokter,
      'listIndonesiaMerdeka' => $listIndonesiaMerdeka,
      'listPresidenRI' => $listPresidenRI,
      'listAndaLahir' => $listAndaLahir,
      'listMenghitungMundur' => $listMenghitungMundur,
      'listPerasaanHati' => $listPerasaanHati,

      //Form Mini Nutrional Assessment Geriatri
      'listPenurunanAsupan' => $listPenurunanAsupan,
      'listKehilanganBB' => $listKehilanganBB,
      'listKemampuanMobilitas' => $listKemampuanMobilitas,
      'listMenderitaStress' => $listMenderitaStress,
      'listNeuropsikologis' => $listNeuropsikologis,
      'listNilaiIMT' => $listNilaiIMT,

      'listTinggal' => $this->masterModel->referensi(924),
      'listPenggunaan3Obat' => $this->masterModel->referensi(925),
      'listLuka' => $this->masterModel->referensi(926),
      'listMakanLengkap' => $this->masterModel->referensi(927),
      'listKonsumsiMakanan1' => $this->masterModel->referensi(928),
      'listKonsumsiMakanan2' => $this->masterModel->referensi(929),
      'listKonsumsiMakanan3' => $this->masterModel->referensi(930),
      'listKonsumsiBuah' => $this->masterModel->referensi(931),
      'listBanyakCairan' => $this->masterModel->referensi(932),
      'listKonsumsiBuah2' => $this->masterModel->referensi(933),
      'listCaraMakan' => $this->masterModel->referensi(934),
      'listPandanganSendiri' => $this->masterModel->referensi(935),
      'listKesehatanLain' => $this->masterModel->referensi(936),
      'listLingkarLengan' => $this->masterModel->referensi(937),
      'listLingkarBetis' => $this->masterModel->referensi(938),

      // Penilaian risiko jatuh pasien geriatri
      'riwayatjatuh1' => $riwayatjatuh1,
      'riwayatjatuh2' => $riwayatjatuh2,

      // Instrumen Geriatric Depression Scale
      'igds1' => $igds1,
      'igds2' => $igds2,
      'igds3' => $igds3,
      'igds4' => $igds4,
      'igds5' => $igds5,
      'igds6' => $igds6,
      'igds7' => $igds7,
      'igds8' => $igds8,
      'igds9' => $igds9,
      'igds10' => $igds10,
      'igds11' => $igds11,
      'igds12' => $igds12,
      'igds13' => $igds13,
      'igds14' => $igds14,
      'igds15' => $igds15,
      'historyIGDS' => $historyIGDS,

      'dominisasiHemisfer' => $this->masterModel->referensi(940),
      'kesadaranResponden' => $this->masterModel->referensi(941),

      // Form Observasi Anyelir
      'listJam' => $listJam,
      'tindakanKeperawatanAnyelir' => $tindakanKeperawatanAnyelir,

      // History Observasi Anyelir
      'listHistoryObservasiAnyelir' => $listHistoryObservasiAnyelir,

      // History Keperawatan Anyelir
      'historyKeperawatanAnyelir' => $historyKeperawatanAnyelir,

      // Form Persiapan Bronkoskopi
      // 'listPersiapanBronkoskopi1' => $listPersiapanBronkoskopi1,
      // 'listPersiapanBronkoskopi2' => $listPersiapanBronkoskopi2,
      // 'listPersiapanBronkoskopi3' => $listPersiapanBronkoskopi3,
      // 'listPersiapanBronkoskopi4' => $listPersiapanBronkoskopi4,
      // 'listPersiapanBronkoskopi5' => $listPersiapanBronkoskopi5,
      // 'listPersiapanBronkoskopi6' => $listPersiapanBronkoskopi6,
      // 'listPersiapanBronkoskopi7' => $listPersiapanBronkoskopi7,

      //Form Persiapan Gastroskopi
      'listPersiapanGastroskopi1' => $listPersiapanGastroskopi1,
      'listPersiapanGastroskopi2' => $listPersiapanGastroskopi2,
      'listPersiapanGastroskopi3' => $listPersiapanGastroskopi3,

      //Form Spirometri
      'listKesan' => $listKesan,
      'listRestriksi' => $listRestriksi,
      'listObstruksi' => $listObstruksi,

      // Pemantauan Anestesi Lokal
      'kesadaranAnestesiLokal' => $kesadaranAnestesiLokal,
      'riwayatAlergiAnestesiLokal' => $riwayatAlergiAnestesiLokal,
      'obatAnestesiLokal' => $obatAnestesiLokal,
      'diencerkan' => $diencerkan,
      'penggunaanAdrenalin' => $penggunaanAdrenalin,
      'konversiAnestesi' => $konversiAnestesi,
      'kesadaranUmumPascaPasien' => $kesadaranUmumPascaPasien,
      'tandaToksisitas' => $tandaToksisitas,
      'mualMuntah' => $mualMuntah,
      'keluhanNyeri' => $keluhanNyeri,
      'perdarahanPascaPasien' => $perdarahanPascaPasien,
      'pasienPindah' => $pasienPindah,

      // Form Konsultasi
      'jenisKonsultasi' => $jenisKonsultasi,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
      'penilaianKasusSaatIni' => $penilaianKasusSaatIni,
      'setujuUntuk' => $setujuUntuk,
      'historyKonsul' => $historyKonsul,
      'jawabKonsul' => $jawabKonsul,

      //EWS
      'tanggal_head' => $tanggal_head,
      'parameterEws' => $parameterEws,
      'scoreEws' => $scoreEws,
      'parameterPews' => $parameterPews,
      'scorePews' => $scorePews,

      //EResep
      'farmasi_eresep' => $farmasi_eresep,
      'obat' => $obat,
      // 'menyusui'               => $menyusui,
      // 'hamil'                  => $hamil,
      // 'resep_pp'               => $resep_pp,
      // 'gangguan_fungsi_ginjal' => $gangguan_fungsi_ginjal,

      //Tindakan
      'dataTindakanRadiologi' => $dataTindakanRadiologi,
      'dataTindakanPatologiKlinik' => $dataTindakanPatologiKlinik,
      'dataTindakanProsedur' => $dataTindakanProsedur,

      //PK
      'historypk' => $historypk,
      'hasil_lab' => $hasil_lab,

      //PA
      'caraDapatJaringan' => $caraDapatJaringan,
      'cairanFiksasi' => $cairanFiksasi,
      'sito' => $sito,
      'histo' => $histo,
      'imuno' => $imuno,
      'historyLabPA' => $historyLabPA,
      'caraPengambilanSito' => $caraPengambilanSito,
      'cairanFiksasiSito' => $cairanFiksasiSito,
      'statusMenstruasi' => $statusMenstruasi,
      'siklusHaid' => $siklusHaid,
      'kontrasepsi' => $kontrasepsi,
      'statusGinekologi' => $statusGinekologi,
      'sitologiNonGinekolog' => $sitologiNonGinekolog,

      //Radiologi
      'PasRad' => $PasRad,

      // History Permintaan Dirawat
      'listHistoryDirawat' => $listHistoryDirawat,

      // History CPO
      'historyCPO' => $historyCPO,

      // History Echo EKG
      'historyEchoEKG' => $historyEchoEKG,

      //History Persiapan Bronkoskopi
      // 'historyPersiapanBronkoskopi' => $historyPersiapanBronkoskopi,

      //History Persiapan Gastroskopi
      'historyPersiapanGastroskopi' => $historyPersiapanGastroskopi,

      //History Persiapan Spirometri
      'historyPersiapanSpirometri' => $historyPersiapanSpirometri,

      //History Laporan Hasil Pemeriksaan
      'historyLaporanHasilPemeriksaan' => $historyLaporanHasilPemeriksaan,

      //History PMFAK
      'historyPMFAK' => $historyPMFAK,

      //history Pengkajian PAK
      'historyPengkajianPAK' => $historyPengkajianPAK,

      'listPengkajianPAK' => $listPengkajianPAK,
      'listPengkajianPAKMedis' => $listPengkajianPAKMedis,

      //history serah terima IGD
      'historySerahTerimaIGD' => $historySerahTerimaIGD,
      //history Mini Cog
      'historyMiniCog' => $historyMiniCog,
      //history rekonsiliasi obat
      'historyRekonsiliasiObatIGD' => $historyRekonsiliasiObatIGD,
      //history tromboferesis
      'historyTrombosisFeresis' => $historyTrombosisFeresis,
      //history Persetujuan Transfusi Darah
      'historyPersetujuanTransfusiD' => $historyPersetujuanTransfusiD,
      // History pengkajian pra sedasi
      'historyPengkajianPraSedasi' => $historyPengkajianPraSedasi,
      'listPengkajianPraSedasi' => $listPengkajianPraSedasi,

      // list geriatri
      'listGeriatriPppg' => $listGeriatriPppg,

      // Tanda Vital Status Anestesia
      'historyTandaVital_SA' => $historyTandaVital_SA,

      // History Pemulihan Sedasi
      'historyPemulihanSedasi' => $historyPemulihanSedasi,
      'historyPemulihanNapas' => $historyPemulihanNapas,

      // History Pemberian Intravena
      'historyPemberianIntravena' => $historyPemberianIntravena,

      // History Tindakan Aferesis
      'historyCeklisTindakanAferesis' => $historyCeklisTindakanAferesis,

      // History Preaferesis
      'historyPreaferesis' => $historyPreaferesis,

      // History Barthel Indek
      'historyBarthelIndek' => $historyBarthelIndek,

      // History Abbreviated Mental Test
      'historyAbbreviatedMentalTest' => $historyAbbreviatedMentalTest,

      // History Tindakan Donor Aferesis
      'historyTindakanDonorAferesis' => $historyTindakanDonorAferesis,

      // History Persetujuan Tindakan Kemoterapi LLA Anak
      'historyPtKemoLlaAnak' => $historyPtKemoLlaAnak,

      // History Pengkajian Risiko Jatuh Pasien Dewasa
      'historyPengkajianRisikoJatuhPasienDewasa' => $historyPengkajianRisikoJatuhPasienDewasa,

      // Get Pengkajian
      'getPengkajian' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 ? $getNomr['ID_RUANGAN'] == 105130101 || $getNomr['ID_RUANGAN'] == 105130103 ? $getPengkajianDeteksiDini : $getPengkajian : "",
      'getUreum' => $getUreum,

      'hPengkajian' => $hPengkajian,
      'getIDEMR_AwalKeperawatan' => $getIDEMR_AwalKeperawatan,

      // CPPT
      'alertKeperawatan' => $alertKeperawatan,
      'alertMedis' => $alertMedis,
      'VkeperwatanCppt' => $VkeperwatanCppt,
      'tandaVitalCppt' => $tandaVitalCppt,
      'alertCpptKonsul' => $alertCpptKonsul,

      //history Hemodialisa
      'historyHemodialisa' => $historyHemodialisa,
      'historyPrHemodialisa' => $historyPrHemodialisa,

      // 'SoapCppt' => $SoapCppt,

      //History Medis
      'getPengkajianMedis' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $getPengkajianMedis : "",
      'getIDEMR_AwalMedis' => $getIDEMR_AwalMedis,

      'getPengkajianAsuhanKeperawatan' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $getPengkajianAsuhanKeperawatan : "",
      'hReaksiAlergiMedis' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $hReaksiAlergiMedis : "",

      'hOrderRadiologi' => $hOrderRadiologi,
      'hOrderProsedur' => $hOrderProsedur,
      'hProtokolKemo' => $hProtokolKemo,
      'hgeriatripppg' => $hgeriatripppg,
      'hKardekObat' => $hKardekObat,
      'hPengkajianRater' => $hPengkajianRater,

      //Deteksi Dini
      'paketPemeriksaan' => $paketPemeriksaan,
      'diabetesMellitus' => $diabetesMellitus,
      'hipertensi' => $hipertensi,
      'jantung' => $jantung,
      'merokok' => $merokok,
      'perokokPasif' => $perokokPasif,
      'minumAlkohol' => $minumAlkohol,
      'riwayatHepatitis' => $riwayatHepatitis,
      'riwayatHepatitisKeluarga' => $riwayatHepatitisKeluarga,
      'pernahOperasi' => $pernahOperasi,
      'pernahTranfusiDarah' => $pernahTranfusiDarah,
      'pernahSuntik' => $pernahSuntik,
      'makanDaging' => $makanDaging,
      'makanDiasap' => $makanDiasap,
      'polipUsus' => $polipUsus,
      'infeksiUsus' => $infeksiUsus,
      'polaBAB' => $polaBAB,
      'babTercapurDarah' => $babTercapurDarah,
      'riwayatKankerKeluarga' => $riwayatKankerKeluarga,

      // Khusus Wanita
      'kawinMenikah' => $kawinMenikah,
      'melahirkanAnak' => $melahirkanAnak,
      'pernahMenyusui' => $pernahMenyusui,
      'kontrasepsiHormonal' => $kontrasepsiHormonal,
      'obatHormonal' => $obatHormonal,
      'haidPertama' => $haidPertama,
      'monoPause' => $monoPause,
      'keluhan' => $keluhan,
      'keputihan' => $keputihan,
      'pendarahanSpontan' => $pendarahanSpontan,
      'pendarahan' => $pendarahan,
      'pernahPapsSmear' => $pernahPapsSmear,

      // Khusus Pria
      'gangguanBuangAirKecil' => $gangguanBuangAirKecil,
      'pernahProstat' => $pernahProstat,

      // Pemeriksaan fisik umum
      'keadaanUmum' => $keadaanUmum,
      'keadaanGizi' => $keadaanGizi,
      'habitus' => $habitus,

      // Formulir Pemindahan Pasien
      'score' => $score,
      'riwayatPengobatan' => $riwayatPengobatan,
      'pemeriksaanPenunjang' => $pemeriksaanPenunjang,
      'terapi' => $terapi,
      'nutrisi' => $nutrisi,
      'perawatan' => $perawatan,
      'lainPr' => $lainPr,
      'riwayatPenyakitMenular' => $riwayatPenyakitMenular,
      'kondisiSaatIni' => $kondisiSaatIni,
      'mobilisasiMerujukPasien' => $mobilisasiMerujukPasien,
      'kondisiKulit' => $kondisiKulit,
      'ruanganRskd' => $ruanganRskd,
      'resikoJatuh' => $resikoJatuh,

      // Rehabilitasi Medik
      'transportasi' => $transportasi,
      'pelayananRehabMedik' => $pelayananRehabMedik,
      'historyRehabMedik' => $historyRehabMedik,
      'karnofskyIndex' => $karnofskyIndex,
      // 'historyPengukuranLimfedema' => $historyPengukuranLimfedema,

      // Observasi IGD
      'observasiIGD' => $observasiIGD,

      //Serah Terima
      'penyakitmenular' => $penyakitmenular,
      'suratizintindakan' => $suratizintindakan,
      'penandaansisioperasi' => $penandaansisioperasi,
      'hasilpemeriksaanpratindakan' => $hasilpemeriksaanpratindakan,
      'hasilpemeriksaanpascatindakan' => $hasilpemeriksaanpascatindakan,
      'fotopratindakan' => $fotopratindakan,
      'ctscanpratindakan' => $ctscanpratindakan,
      'usgpratindakan' => $usgpratindakan,
      'mripratindakan' => $mripratindakan,
      'echocardiografipratindakan' => $echocardiografipratindakan,
      'fotopascatindakan' => $fotopascatindakan,
      'ctscanpascatindakan' => $ctscanpascatindakan,
      'usgpascatindakan' => $usgpascatindakan,
      'mripascatindakan' => $mripascatindakan,
      'echocardiografipascatindakan' => $echocardiografipascatindakan,
      'kondisisaatiniprosedurpratindakan' => $kondisisaatiniprosedurpratindakan,
      'kondisisaatiniprosedurpascatindakan' => $kondisisaatiniprosedurpascatindakan,
      'risikojatuhprosedur' => $risikojatuhprosedur,
      'kesadaran_pasca' => $kesadaran_pasca,
      'risikojatuhprosedur_pasca' => $risikojatuhprosedur_pasca,
      'implant_pra' => $implant_pra,
      'implant_pasca' => $implant_pasca,
      'pemakaian_alat_bantu_pra' => $pemakaian_alat_bantu_pra,
      'pemakaian_alat_bantu_pasca' => $pemakaian_alat_bantu_pasca,
      'gigi_palsu_pra' => $gigi_palsu_pra,
      'gigi_palsu_pasca' => $gigi_palsu_pasca,
      'ketersediaandarah' => $ketersediaandarah,
      'ketersediaanalatobat' => $ketersediaanalatobat,
      'instruksipratindakan' => $instruksipratindakan,
      'program_terapi_pra' => $program_terapi_pra,
      'program_terapi_pasca' => $program_terapi_pasca,
      'instruksipascatindakan' => $instruksipascatindakan,
      'rencanaserahterima' => $rencanaserahterima,
      // 'historySerahTerima' => $historySerahTerima,
      // 'getSerahTerima' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105060101 ? $getSerahTerima : "",

      //Pengkajian Pra Anestesi
      'hilangnya_gigi' => $hilangnya_gigi,
      'masalah_leher' => $masalah_leher,
      'denyut_jantung' => $denyut_jantung,
      'batuk' => $batuk,
      'sesak_napas' => $sesak_napas,
      'baru_saja_menderita_infeksi' => $baru_saja_menderita_infeksi,
      'saluran_napas_atas' => $saluran_napas_atas,
      'sakit_dada' => $sakit_dada,
      'muntah' => $muntah,
      'pingsan' => $pingsan,
      'stroke' => $stroke,
      'kejang' => $kejang,
      'sedang_hamil' => $sedang_hamil,
      'kelainan_tulang_belakang' => $kelainan_tulang_belakang,
      'obesitas' => $obesitas,
      'klasifikasi_asa' => $klasifikasi_asa,
      'teknik_anestesia' => $teknik_anestesia,
      'teknik_khusus' => $teknik_khusus,
      'monitoring' => $monitoring,
      'alat_khusus' => $alat_khusus,
      'perawatan_pasca_anestesi' => $perawatan_pasca_anestesi,
      // 'historypraanestesi' => $historypraanestesi,
      // 'getpra_anestesi' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $getpra_anestesi : "",
      'history_iadl' => $history_iadl,
      'get_iadl' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $get_iadl : "",
      'history_adl' => $history_adl,
      'get_adl' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $get_adl : "",
      'history_fkri' => $history_fkri,
      'get_fkri' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $get_fkri : "",
      'history_fkrp' => $history_fkrp,
      'get_fkrp' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 ? $get_fkrp : "",

      //EKFM
      'historyekfm' => $historyekfm,
      'getekfm' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105110101 ? $getekfm : "",

      'historySpectrOptia' => $historySpectrOptia,
      'getSpectraOptia' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 ? $getSpectraOptia : "",
      'getpraoperasi' => $this->uri->segment(5) != "" && $this->session->userdata('status') == 1 ? $getpraoperasi : "",
      'getdaftaroperasi' => $this->uri->segment(5) != "" && $this->session->userdata('status') == 1 ? $getdaftaroperasi : "",
      'getmerujukpasien' => $this->uri->segment(5) != "" ? $getmerujukpasien : "",
      'historypraoperasi' => $historypraoperasi,
      'historydaftaroperasi' => $historydaftaroperasi,
      'historyMerujukPasien' => $historyMerujukPasien,

      // Tindakan Invasif
      // 'hPengkajianTindakanInvasif' => $hPengkajianTindakanInvasif,
      // 'getPengkajianTindakanInvasif' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105060101 ? $getPengkajianTindakanInvasif : "",
      'hPengkajianTindakanInvasifMedis' => $hPengkajianTindakanInvasifMedis,
      'getPengkajianTindakanInvasifMedis' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 && $getNomr['ID_RUANGAN'] == 105060101 ? $getPengkajianTindakanInvasifMedis : "",
      'getIDEMR_Invasif' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 && $getNomr['ID_RUANGAN'] == 105060101 ? $getIDEMR_Invasif : "",
      // 'getIDEMR_InvasifKP' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105060101 ? $getIDEMR_InvasifKP : "",

      // Tindakan Intervensi
      'hPengkajianTindakanIntervensiMedis' => $hPengkajianTindakanIntervensiMedis,
      'getPengkajianTindakanIntervensiMedis' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 && $getNomr['ID_RUANGAN'] == 105100101 ? $getPengkajianTindakanIntervensiMedis : "",
      'getIDEMR_Intervensi' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 && $getNomr['ID_RUANGAN'] == 105100101 ? $getIDEMR_Intervensi : "",

      'getIDEMR_IntervensiKP' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105100101 ? $getIDEMR_IntervensiKP : "",

      'hPengkajianTindakanIntervensi' => $hPengkajianTindakanIntervensi,
      'getPengkajianTindakanIntervensi' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105100101 ? $getPengkajianTindakanIntervensi : "",

      // 'historyKTI' => $historyKTI,
      // 'getKTI' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105060101 ? $getKTI : "",

      // 'historyPAL' => $historyPAL,
      // 'getPAL' => $this->uri->segment(7) != "" ? $getPAL : "",
      // 'getPALPemantauan' => $this->uri->segment(7) != "" ? $getPALPemantauan : "",

      'hPengkajianRadiologiNuklir' => $hPengkajianRadiologiNuklir,
      'getPengkajianRadiologiNuklir' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105100101 ? $getPengkajianRadiologiNuklir : "",

      'getTbBbAlergi' => $this->pengkajianAwalModel->getTbBbAlergi($nomr),

      'get_tv' => $this->uri->segment(5) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105140101 ? $get_tv : "",

      //Odontogram
      'occlusi' => $this->masterModel->referensi(453),
      'torus_palatinus' => $this->masterModel->referensi(454),
      'torus_mandibularis' => $this->masterModel->referensi(455),
      'palatum' => $this->masterModel->referensi(456),
      'diastema' => $this->masterModel->referensi(457),
      'gigi_anomali' => $this->masterModel->referensi(458),
      'foto' => $this->masterModel->referensi(459),
      'foto_rontgen' => $this->masterModel->referensi(460),

      // Pengkajian awal PALIATIF
      'tujuanpalitaif' => $tujuanpalitaif,
      'riwayatsakitpaliatif' => $riwayatsakitpaliatif,
      'riwayatpengobatanpaliatif' => $riwayatpengobatanpaliatif,
      'riwayatalergipaliatif' => $riwayatalergipaliatif,
      'topikbelajarpaliatif' => $topikbelajarpaliatif,
      'mediabelajarpaliatif' => $mediabelajarpaliatif,
      'kolaborasipaliatif' => $kolaborasipaliatif,
      'klasifikasi' => $klasifikasi,
      'stadiumPaliataif' => $stadiumPaliataif,
      'operasiPali' => $operasiPali,
      'radiasiPali' => $radiasiPali,
      'terapiPali' => $terapiPali,
      'getPengkajianPaliatif' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 2 && $getNomr['ID_RUANGAN'] == 105020401 ? $getPengkajianPaliatif : "",
      'getPaliMedis' => $this->uri->segment(7) != "" && $this->session->userdata('status') == 1 && $getNomr['ID_RUANGAN'] == 105020401 ? $getPaliMedis : "",
      'hPaliatif' => $hPaliatif,
      'datakuis' => $datakuis,

      //IGD Skrining Visual
      'pinereIgd' => $pinereIgd,
      'tuberkolosisIgd' => $tuberkolosisIgd,
      'mobilisasiIgd' => $mobilisasiIgd,
      'resikoJatuhIgd' => $resikoJatuhIgd,
      'labIgd' => $labIgd,
      'radIgd' => $radIgd,
      'paIgd' => $paIgd,
      'ditujukanIgd' => $ditujukanIgd,
      'kebutuhanPelayananIgd' => $kebutuhanPelayananIgd,
      'dirujukIgd' => $dirujukIgd,

      //IGD Triase
      'atsTriase1' => $atsTriase1,
      'atsTriase2' => $atsTriase2,
      'atsTriase3' => $atsTriase3,
      'atsTriase4' => $atsTriase4,
      'atsTriase5' => $atsTriase5,
      'jenisKunjunganTriase' => $jenisKunjunganTriase,
      'triaseIgd' => $triaseIgd,

      //Tindakan Prosedur SIMPEL
      'tindakanProsedurSimpel' => $this->masterModel->tindakan('105060101'),

      //View Berkas VBPJS
      'listVBPJS' => $this->pengkajianAwalModel->listVBPJS($nomr),

      //Pengkajian Gigi
      'perawatan_gigi' => $this->masterModel->referensi(515),
      'berapa_menyikat' => $this->masterModel->referensi(516),
      'kapan_menyikat' => $this->masterModel->referensi(517),
      'gerakan_menyikat' => $this->masterModel->referensi(518),
      'minum_teh_kopi' => $this->masterModel->referensi(519),
      'merokok' => $this->masterModel->referensi(520),
      'minuman_beralkohol' => $this->masterModel->referensi(521),
      'menggigit_pensil' => $this->masterModel->referensi(522),
      'mengunyah_rahang' => $this->masterModel->referensi(523),
      'bruxism' => $this->masterModel->referensi(524),
      'diagnosa_kesehatan' => $this->masterModel->referensi(525),
      'nyeri_gigi' => $this->masterModel->referensi(526),
      'skala_gigi' => $this->masterModel->referensi(527),
      'anamnesis_gigi' => $this->masterModel->referensi(528),

      // Pengkajian Luka
      'riwayat_alergi_luka' => $this->masterModel->referensi(531),
      'jenis_luka' => $this->masterModel->referensi(533),
      'jenis_luka_kanker' => $this->masterModel->referensi(534),
      'warna_dasar_luka' => $this->masterModel->referensi(535),
      'perdarahan' => $this->masterModel->referensi(536),
      'bau' => $this->masterModel->referensi(537),
      'gatal_pruritus' => $this->masterModel->referensi(538),
      'eksudat' => $this->masterModel->referensi(539),
      'grade' => $this->masterModel->referensi(540),
      'drain' => $this->masterModel->referensi(541),
      'jahitan' => $this->masterModel->referensi(542),
      'tanda_infeksi' => $this->masterModel->referensi(543),
      'ftsg' => $this->masterModel->referensi(544),
      'stsg' => $this->masterModel->referensi(545),
      'tubes_cateter' => $this->masterModel->referensi(546),
      'infeksi' => $this->masterModel->referensi(547),
      'rembes' => $this->masterModel->referensi(548),
      'dehiscence' => $this->masterModel->referensi(549),
      'stadium_luka_tekan' => $this->masterModel->referensi(550),
      'produksi' => $this->masterModel->referensi(551),
      'manajemen' => $this->masterModel->referensi(552),
      'keluaran' => $this->masterModel->referensi(553),
      'nefrostomy' => $this->masterModel->referensi(577),
      'psikososial' => $this->masterModel->referensi(602),
      'masalah_keperawatan' => $this->masterModel->referensi(610),
      'pencucian_luka' => $this->masterModel->referensi(554),
      'merah' => $this->masterModel->referensi(555),
      'kuning' => $this->masterModel->referensi(556),
      'hitam' => $this->masterModel->referensi(557),
      'pink' => $this->masterModel->referensi(558),
      'kebutuhan_pembelajaran' => $this->masterModel->referensi(559),
      'kontrol_luka' => $this->masterModel->referensi(560),

      // Operasi
      'siteMarking' => $siteMarking,

      //Pengkajian Nyeri
      'penjalaran_nyeri' => $this->masterModel->referensi(603),
      'pemberat_nyeri' => $this->masterModel->referensi(604),
      'peringan_nyeri' => $this->masterModel->referensi(605),
      'intensitas_nyeri' => $this->masterModel->referensi(606),
      'rasa_nyeri' => $this->masterModel->referensi(607),
      'progresiv_nyeri' => $this->masterModel->referensi(608),
      'gejala_nyeri' => $this->masterModel->referensi(609),
      'pengaruh_nyeri' => $this->masterModel->referensi(611),
      'sebut_pengaruh_nyeri' => $this->masterModel->referensi(612),
      'riwayat_jatuh_nyeri' => $this->masterModel->referensi(613),
      'riwayat_alergi_nyeri' => $this->masterModel->referensi(614),
      'sedang_hamil_nyeri' => $this->masterModel->referensi(615),
      'riwayat_pengobatan_nyeri' => $this->masterModel->referensi(616),
      'riwayat_oprasi_nyeri' => $this->masterModel->referensi(617),
      'riwayat_radiasi_nyeri' => $this->masterModel->referensi(618),
      'riwayat_terapi_nyeri' => $this->masterModel->referensi(619),
      'diagnosis_sekunder' => $this->masterModel->referensi(620),
      'getPolinyeri' => $getPolinyeri,
      'detilnyeri' => $detilnyeri,

      //Radioterapi CT-Simulator
      'Position_of_Patient' => $this->masterModel->referensi(678),
      'Mask' => $this->masterModel->referensi(679),
      'Contrast' => $this->masterModel->referensi(680),
      'Slicethickness' => $this->masterModel->referensi(681),
      'Base_Plate' => $this->masterModel->referensi(682),
      'Fixation' => $this->masterModel->referensi(683),
      'Neck_Extention' => $this->masterModel->referensi(684),
      'Mouth_Bite' => $this->masterModel->referensi(685),
      'Scar_Marking' => $this->masterModel->referensi(686),
      'Arm_Pos' => $this->masterModel->referensi(687),
      'Breastboard' => $this->masterModel->referensi(688),
      'Knee_Rest' => $this->masterModel->referensi(689),
      'Bolus' => $this->masterModel->referensi(690),
      'Vac_Lock' => $this->masterModel->referensi(691),
      'Matras' => $this->masterModel->referensi(692),
      'In_Case_Of_Pelvic_Treatment_bladeer' => $this->masterModel->referensi(693),
      'referenceOfLaser' => $this->masterModel->referensi(700),
      'linacMachine' => $this->masterModel->referensi(696),
      'initialLat' => $this->masterModel->referensi(701),
      'initialVrt' => $this->masterModel->referensi(702),
      'initialLong' => $this->masterModel->referensi(703),
      'coordinatesX' => $this->masterModel->referensi(704),
      'coordinatesY' => $this->masterModel->referensi(705),
      'coordinatesZ' => $this->masterModel->referensi(706),
      'SignX' => $this->masterModel->referensi(707),
      'SignY' => $this->masterModel->referensi(708),
      'SignZ' => $this->masterModel->referensi(709),
      'CouchMovementX' => $this->masterModel->referensi(710),
      'CouchMovementY' => $this->masterModel->referensi(711),
      'CouchMovementZ' => $this->masterModel->referensi(712),
      'Treatment_Field' => $this->masterModel->referensi(713),
      'Direction_Block' => $this->masterModel->referensi(714),
      'Set_Up' => $this->masterModel->referensi(716),
      'FootRate' => $this->masterModel->referensi(999),
      'approTerapi' => $this->pengkajianAwalModel->approTerapi($nokun),

      // Re Evaluasi
      'ketersediaan_jaringan' => $this->masterModel->referensi(718),
      'lokasi_jaringan' => $this->masterModel->referensi(719),
      'jenis_pemeriksaan' => $this->masterModel->referensi(720),
      'asal_spesimen' => $this->masterModel->referensi(721),
      'jenis_spesimen' => $this->masterModel->referensi(722),
      'status_spesimen' => $this->masterModel->referensi(726),
      'ketersediaan_foto' => $this->masterModel->referensi(735),

      //pindah ruangan
      'skNyeriPr' => $this->pengkajianAwalModel->HskNyeriPr($nokun),
      'simulationTm' => $this->masterModel->referensi(805),
      'TPSTm' => $this->masterModel->referensi(806),
      'CtSimulatorTm' => $this->masterModel->referensi(807),

      // Pengkajian Aferesis
      'jenis_pasien_aferesis' => $this->masterModel->referensi(775),
      'riwayat_alergi_aferesis' => $this->masterModel->referensi(774),
      'riwayat_penyakit_aferesis' => $this->masterModel->referensi(777),
      'golongan_darah' => $this->masterModel->referensi(756),
      'rh_darah' => $this->masterModel->referensi(757),
      'riwayat_donor' => $this->masterModel->referensi(759),
      'jenis_donor' => $this->masterModel->referensi(760),
      'kesadaran_aferesis' => $this->masterModel->referensi(762),
      'akses_veskuler' => $this->masterModel->referensi(763),
      'jenis_akses' => $this->masterModel->referensi(764),
      'pola_napas' => $this->masterModel->referensi(765),
      'suara_napas' => $this->masterModel->referensi(766),
      'nadi' => $this->masterModel->referensi(767),
      'ctr' => $this->masterModel->referensi(768),
      'akral' => $this->masterModel->referensi(769),
      'psikososial_aferesis' => $this->masterModel->referensi(770),
      'edukasi' => $this->masterModel->referensi(771),
      'masalah_keperawatan_aferesis' => $this->masterModel->referensi(772),

      // Informed Consent
      'dasarDiagnosisPKem' => $this->masterModel->referensi(847),
      'tindakanKedokteranPKem' => $this->masterModel->referensi(848),
      'indikasiTindakanPKem' => $this->masterModel->referensi(850),
      'tataCaraPKem' => $this->masterModel->referensi(851),
      'tujuanTindakanPKem' => $this->masterModel->referensi(852),
      'tujuanPengobatanPKem' => $this->masterModel->referensi(853),
      'risikoPKem' => $this->masterModel->referensi(854),
      'komplikasiPKem' => $this->masterModel->referensi(855),
      'prognosisPKem' => $this->masterModel->referensi(856),
      'alternatifDanResikoPKem' => $this->masterModel->referensi(859),
      'lainLainPKem' => $this->masterModel->referensi(860),
      'diagnosisWdDdLlaAnak' => $this->masterModel->referensi(997),
      'risikoLlaAnak' => $this->masterModel->referensi(998),
      'diagnosis_aml' => $this->masterModel->referensi(966),
      'tatacara_aml' => $this->masterModel->referensi(967),
      'tujuan_pengobatan_aml' => $this->masterModel->referensi(972),
      'risiko_aml' => $this->masterModel->referensi(973),
      'kompilasi_aml' => $this->masterModel->referensi(981),
      'prognosis_aml' => $this->masterModel->referensi(982),
      'alternatif_aml' => $this->masterModel->referensi(983),
      'lainlain_aml' => $this->masterModel->referensi(984),
      'dasardiag_aml' => $this->masterModel->referensi(985),
      'tindakandokter_aml' => $this->masterModel->referensi(986),
      'indikasi_aml' => $this->masterModel->referensi(987),

      // Pemberian dan pemantauan
      'surat_izin' => $this->masterModel->referensi(879),
      'kesesuaian_instruksi' => $this->masterModel->referensi(880),
      'jenis_darah' => $this->masterModel->referensi(881),
      'jenis_darah_sesuai' => $this->masterModel->referensi(883),
      'rhesus' => $this->masterModel->referensi(882),
      'golongan_darah_sesuai' => $this->masterModel->referensi(884),
      'rhesus_sesuai' => $this->masterModel->referensi(885),
      'formulir_pmi' => $this->masterModel->referensi(888),
      'label_darah' => $this->masterModel->referensi(886),
      'identitas_pasien' => $this->masterModel->referensi(887),
      'listPegawai' => $this->masterModel->listPegawai(),

      // Persetujuan Tindakan Terapeutik Aferesis
      'dasar_diagnosis_terapeutik' => $this->masterModel->referensi(942),
      'tindakan_kedokteran_terapeutik' => $this->masterModel->referensi(943),
      'indikasi_tindakan_terapeutik' => $this->masterModel->referensi(944),
      'tata_cara_terapeutik' => $this->masterModel->referensi(945),
      'tujuan_tindakan_terapeutik' => $this->masterModel->referensi(946),
      'tujuan_pengobatan_terapeutik' => $this->masterModel->referensi(947),
      'resiko_terapeutik' => $this->masterModel->referensi(948),
      'komplikasi_terapeutik' => $this->masterModel->referensi(949),
      'prognosis_terapeutik' => $this->masterModel->referensi(950),
      'alternatif_terapeutik' => $this->masterModel->referensi(951),
      'resiko_2_terapeutik' => $this->masterModel->referensi(952),
      'jenis_kelamin' => $this->masterModel->referensi(965),

      // Persetujuan tindakan kemoterapi AML Anak
      'diagnosis_aml' => $this->masterModel->referensi(966),
      'tatacara_aml' => $this->masterModel->referensi(967),
      'tujuan_pengobatan_aml' => $this->masterModel->referensi(972),
      'risiko_aml' => $this->masterModel->referensi(973),
      'kompilasi_aml' => $this->masterModel->referensi(981),
      'prognosis_aml' => $this->masterModel->referensi(982),
      'alternatif_aml' => $this->masterModel->referensi(983),
      'lainlain_aml' => $this->masterModel->referensi(984),
      'dasardiag_aml' => $this->masterModel->referensi(985),
      'tindakandokter_aml' => $this->masterModel->referensi(986),
      'indikasi_aml' => $this->masterModel->referensi(987),
      'tblhistoryPAK' => $tblhistoryPAK,

      // START HUMPTY DUMPTY
      'hdsUmur' => $this->masterModel->referensi(1014),
      'hdsJk' => $this->masterModel->referensi(1015),
      'hdsDa' => $this->masterModel->referensi(1016),
      'hdsGk' => $this->masterModel->referensi(1017),
      'hdsFl' => $this->masterModel->referensi(1018),
      'hdsOb' => $this->masterModel->referensi(1019),
      'hdsPo' => $this->masterModel->referensi(1020),

      'hasilHumptyDumpty' => $hasilHumptyDumpty,
      'jumlahHumptyDumpty' => $jumlahHumptyDumpty,
      // END HUMPTY DUMPTY

      // START SKALA MORSE
      'listRiwayatJatuh' => $this->masterModel->referensi(1006),
      'listDiagnosisSekunder' => $this->masterModel->referensi(1007),
      'listAlatBantu' => $this->masterModel->referensi(1008),
      'listMenggunakanInfus' => $this->masterModel->referensi(1009),
      'listCaraBerjalan' => $this->masterModel->referensi(1010),
      'listStatusMental' => $this->masterModel->referensi(1011),

      'hasilSkalaMorse' => $hasilSkalaMorse,
      'jumlahSkalaMorse' => $jumlahSkalaMorse,
      // END SKALA MORSE

      // START SKALA ONTARIO
      'listKarenaJatuh' => $this->masterModel->referensi(1674),
      'listJatuh2Bln' => $this->masterModel->referensi(1675),
      'listPasienDilirium' => $this->masterModel->referensi(1676),
      'listPasienDisorientasi' => $this->masterModel->referensi(1677),
      'listPasienAgitasi' => $this->masterModel->referensi(1678),
      'listPakaiKacamata' => $this->masterModel->referensi(1679),
      'listPenglihatanBuram' => $this->masterModel->referensi(1680),
      'listPasienGlaukoma' => $this->masterModel->referensi(1681),
      'listPerilakuBerkemih' => $this->masterModel->referensi(1682),

      'hasilSkalaOntario' => $hasilSkalaOntario,
      'jumlahSkalaOntario' => $jumlahSkalaOntario,
      // END SKALA ONTARIO
    );
    $this->load->view('Pengkajian/emr/form-keperawatan-d-rj', $data);
  }
}
