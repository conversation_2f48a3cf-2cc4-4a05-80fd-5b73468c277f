<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Tindakan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses')) && !in_array(31, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            array(
                'masterModel',
                'tindakanModel'
            )
        );
    }

    public function tindakancppt()
    {
        $id_ruangan = $this->input->post('ID_RUANGAN');
        $data = $this->tindakanModel->tindakancppt($id_ruangan);
        echo json_encode($data);
    }

    public function simpantindakancppt()
    {
        $oleh = $this->session->userdata('id');
        $nokun = $this->input->post("nokun");
        $tindakan = $this->input->post("tindakan");
        $olehDok = $this->session->userdata('iddokter');

        $ids_tindakan = []; // Simpan ID tindakan yang berhasil dimasukkan

        if (!empty($tindakan)) {
            foreach ($tindakan as $value) {
                $data = $this->tindakanModel->simpantindakancppt($nokun, [$value], $oleh, $olehDok);
                if ($data['success']) {
                    $ids_tindakan[] = $data['ID']; // Simpan ID yang berhasil masuk
                }
            }
        }
        echo json_encode([
            'success' => !empty($ids_tindakan),
            'ids_tindakan' => $ids_tindakan
        ]);
    }
}
