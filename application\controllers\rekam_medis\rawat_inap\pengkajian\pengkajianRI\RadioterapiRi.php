<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RadioterapiRi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/RadioterapiRiModel'
    ));
  }

  public function index($idLoadNorm, $idLoadNopen, $idLoadNokun, $idLoad)
  {
    $norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->RadioterapiRiModel->getNomrRadioterapiRi($nopen);
    $getIdEmr = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
    if($idLoad === "00000"){
    $getPengkajian = $this->RadioterapiRiModel->getPengkajian($getIdEmr);
    }else{
    $getPengkajian = $this->RadioterapiRiModel->getPengkajian($idLoad);
    }
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      'idLoad' => $idLoad,
      'pasien' => $getNomr,
      'getPengkajian' => $getPengkajian,
      'anamnesis' => $this->masterModel->referensi(54),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'riwayatPenyakitKeluarga' => $this->masterModel->referensi(151),
      'kesadaran' => $this->masterModel->referensi(5),
      'masalahKesehatan' => $this->masterModel->formMasalahKesehatan(),
      'komponenPenilaian' => $this->masterModel->referensi(22),
      'komponenPenilaianAsupan' => $this->masterModel->referensi(23),
      'riwayatEkstravasasi' => $this->masterModel->referensi(164),
      'hasilLaboratorium' => $this->masterModel->referensi(165),
      'hasilBmpTerakhir' => $this->masterModel->referensi(166),
      'kemoterapiTerdahulu' => $this->masterModel->referensi(167),
      'tindakanPerawatanTerakhir' => $this->masterModel->referensi(168),
      'riwayatGVHD' => $this->masterModel->referensi(169),
      'fotodepanbelakang' => $this->masterModel->referensi(253),
      'fotodepan' => $this->masterModel->referensi(254),
      'fotobelakang' => $this->masterModel->referensi(255),
      'ESASnyeri' => $this->masterModel->referensi(170),
      'ESASlelah' => $this->masterModel->referensi(171),
      'ESASmual' => $this->masterModel->referensi(172),
      'ESASdepresi' => $this->masterModel->referensi(173),
      'ESAScemas' => $this->masterModel->referensi(174),
      'ESASmengantuk' => $this->masterModel->referensi(175),
      'ESASnafsuMakan' => $this->masterModel->referensi(176),
      'ESASsehat' => $this->masterModel->referensi(177),
      'ESASsesakNapas' => $this->masterModel->referensi(178),
      'ESASmasalah' => $this->masterModel->referensi(179),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'efeksampingNRS' => $this->masterModel->referensi(118),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'statusFungsional' => $this->masterModel->referensi(18),
      'skriningResikoJatuhPusing' => $this->masterModel->referensi(120),
      'skriningResikoJatuhBerdiri' => $this->masterModel->referensi(121),
      'skriningResikoJatuh6Bulan' => $this->masterModel->referensi(122),
      'psikologis' => $this->masterModel->referensi(13),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'sosialDanEkonomiPencariNafkah' => $this->masterModel->referensi(15),
      'sosialDanEkonomiTinggalSerumah' => $this->masterModel->referensi(16),
      'programPengobatanDenganKeyakinan' => $this->masterModel->referensi(17),
      'pengobatanAlternatif' => $this->masterModel->referensi(146),
      'pengobatanBudaya' => $this->masterModel->referensi(147),
      'alatBantu' => $this->masterModel->referensi(19),
      'pendidikan' => $this->masterModel->referensi(24),
      'bahasaSehari' => $this->masterModel->referensi(25),
      'perluPenerjemah' => $this->masterModel->referensi(26),
      'kesediaanInformasi' => $this->masterModel->referensi(27),
      'hambatan' => $this->masterModel->referensi(28),
      'kebutuhanPembelajaran' => $this->masterModel->referensi(29),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/radioterapiRi', $data);
  }

  public function masalahKesehatan_edit()
    {
        $id = $this->input->post('id');
        $idemr = $this->input->post('idemr');

        $resultMasalahKesehatan = $this->masterModel->masalahKesehatan($id);
        $resultMasalahKesehatanDetil = $this->masterModel->masalahKesehatanDetil($resultMasalahKesehatan->ID);
        $getPengkajian = $this->RadioterapiRiModel->getPengkajian($idemr);

        $data = array(
            'titleMasalahKesehatan' => $resultMasalahKesehatan->KATEGORI,
            'DataMasalahKesehatan' => $resultMasalahKesehatanDetil,
            'getPengkajian' => $getPengkajian,
        );

        $this->load->view('Pengkajian/emr/masalahKesehatan/masalahKesehatan_edit', $data);
    }

  public function asuhanKeperawatan_edit()
    {
        $id = $this->input->post('id');
        $idemr = $this->input->post('idemr');

        $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
        $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
        $getPengkajian = $this->RadioterapiRiModel->getPengkajian($idemr);

        $data = array(
            'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
            'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
            'getPengkajian' => $getPengkajian,
        );

        $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
    }

  public function simpanPengkajianRadioterapiRi($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');
        // $masukEwsAtauTidak = $this->input->post('masukKeEwsRiD');
        // $id_ews = $this->input->post('id_ews');
        // $id_tanda_vital = $this->input->post('id_tanda_vital');
        // $id_kesadaran = $this->input->post('id_kesadaran');
        // $id_o2 = $this->input->post('id_o2');

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 16,
          'rujukan' => $post['rujukanDariRadioterapiRi'],
          // 'diagnosa_masuk' => $post['rujukanDariRadioterapiRi'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataRiwayatKesehatan = array(
          'id_emr' => $getIdEmr,
          'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
          'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
          'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : "",
          'riwayat_penyakit_keluarga' => isset($post['riwayat_penyakit_dalam_keluarga']) ? $post['riwayat_penyakit_dalam_keluarga'] : "",
          'penyakit_keluarga_lain' => isset($post['riwayat_penyakit_dalam_keluarga_desk']) ? json_encode($post['riwayat_penyakit_dalam_keluarga_desk']) : "",
          'jenis_kanker_keluarga' => isset($post['riwayat_penyakit_kanker_dalam_keluarga_desk']) ? json_encode($post['riwayat_penyakit_kanker_dalam_keluarga_desk']) : "",
          'riwayat_cabut_gigi' => isset($post['riwayat_cabut_gigi']) ? json_encode($post['riwayat_cabut_gigi']) : "",
        );

        // echo "<pre>data riwayat kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataKesedaran = array(
          'data_source' => 26,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data kesadaran ";print_r($dataKesedaran);echo "</pre>";

        $dataTandaVital = array(
          'data_source' => 26,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data tanda vital ";print_r($dataTandaVital);echo "</pre>";

        $dataSkriningGizi = array(
          'id_emr' => $getIdEmr,
          'penurunan_bb' => isset($post['komponenpenilaian']) ? $post['komponenpenilaian'] : "",
          'asupan_bb' => isset($post['komponenpenilaianasupan']) ? $post['komponenpenilaianasupan'] : "",
          'jenis' => 3,
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining gizi ";print_r($dataSkriningGizi);echo "</pre>";

        $dataPemeriksaanFisik = array(
          'id_emr' => $getIdEmr,
          'keluhan_pasien' => isset($post['keluhan_pasien']) ? $post['keluhan_pasien'] : "",
          'masalah_kesehatan_keperawatan' => isset($post['masalahKesehatanKeperawatanDewasaRadioterapiRi']) ? json_encode($post['masalahKesehatanKeperawatanDewasaRadioterapiRi']) : "",
          'keyakinan' => isset($post['program_pengobatan_keyakinan']) ? $post['program_pengobatan_keyakinan'] : "",
          'sebutkan_keyakinan' => isset($post['desk_program_pengobatan_keyakinan']) ? $post['desk_program_pengobatan_keyakinan'] : "",
          'pengobatan_alternatif' => isset($post['pengobatan_alternatif']) ? $post['pengobatan_alternatif'] : "",
          'sebutkan_pengobatan_alternatif' => isset($post['desk_pengobatan_alternatif']) ? json_encode($post['desk_pengobatan_alternatif']) : "",
          'pengobatan_bertentangan' => isset($post['pengobatan_budaya']) ? $post['pengobatan_budaya'] : "",
          'sebutkan_pengobatan_bertentangan' => isset($post['desk_pengobatan_budayaRadioterapiRi']) ? json_encode($post['desk_pengobatan_budayaRadioterapiRi']) : "",
          'status_fungsionl' => isset($post['statusfungsional']) ? $post['statusfungsional'] : "",
          'vertigo' => isset($post['skriningresikojatuhpusingRadioterapiRi']) ? $post['skriningresikojatuhpusingRadioterapiRi'] : "",
          'sulit_berdiri' => isset($post['skriningresikojatuhberdiriRadioterapiRi']) ? $post['skriningresikojatuhberdiriRadioterapiRi'] : "",
          'jatuh_dlm_6' => isset($post['skriningresikojatuh6bulanRadioterapiRi']) ? $post['skriningresikojatuh6bulanRadioterapiRi'] : "",
          'psikologis' => isset($post['psikologis']) ? $post['psikologis'] : null,
          'isi_psikologi' => isset($post['psikologis_lainnyaRadioterapiRi']) ? $post['psikologis_lainnyaRadioterapiRi'] : null,
          'hub_keluarga' => isset($post['sdeh']) ? $post['sdeh'] : "",
          'nafkah_utama' => isset($post['sdepn']) ? $post['sdepn'] : "",
          'tinggal' => isset($post['sdets']) ? $post['sdets'] : "",
        );

        // echo "<pre>data pemeriksaan fisik ";print_r($dataPemeriksaanFisik);echo "</pre>";

        $dataSkriningNyeri = array(
          'nokun' => $post['nokun'],
          'data_source' => 26,
          'ref' => $getIdEmr,
          'metode' => isset($post['skrining_nyeri_RadioterapiRi']) ? $post['skrining_nyeri_RadioterapiRi'] : "",
          'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'provokative' => isset($post['propocative_RadioterapiRi']) ? $post['propocative_RadioterapiRi'] : "",
          'quality' => isset($post['quality_RadioterapiRi']) ? $post['quality_RadioterapiRi'] : "",
          'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
          'regio' => isset($post['regio_RadioterapiRi']) ? $post['regio_RadioterapiRi'] : "",
          'severity' => isset($post['severity_RadioterapiRi']) ? $post['severity_RadioterapiRi'] : "",
          'time' => isset($post['time_RadioterapiRi']) ? $post['time_RadioterapiRi'] : "",
          'ket_time' => isset($post['durasi_nyeri_RadioterapiRi']) ? $post['durasi_nyeri_RadioterapiRi'] : "",
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining nyeri ";print_r($dataSkriningNyeri);echo "</pre>";

        $dataEdukasiKeperawatan = array(
          'id_emr' => $getIdEmr,
          'tingkat_pendidikan' => isset($post['tingkat_pendidikan']) ? $post['tingkat_pendidikan'] : "",
          'bahasa' => isset($post['bahasa_sehari_hari']) ? $post['bahasa_sehari_hari'] : "",
          'bahasa_daerah' => isset($post['bahasa_daerah']) ? $post['bahasa_daerah'] : "",
          'bahasa_lain' => isset($post['bahasa_lainnya']) ? $post['bahasa_lainnya'] : "",
          'penerjemah' => isset($post['perlu_penerjemah']) ? $post['perlu_penerjemah'] : "",
          'penerjemah_lain' => isset($post['penerjemah_lainnya']) ? $post['penerjemah_lainnya'] : "",
          'informasi' => isset($post['kesedian_informasi']) ? $post['kesedian_informasi'] : "",
        );

        // echo "<pre>data kebutuhan edukasi ";print_r($dataEdukasiKeperawatan);echo "</pre>";

        $dataHambatan = array();
        $indexHambatan = 0;
        if (isset($post['hambatan'])) {
          foreach ($post['hambatan'] as $input) {
            if ($post['hambatan'][$indexHambatan] != "") {
              array_push(
                $dataHambatan, array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['hambatan'][$indexHambatan],
                  'keterangan' => isset($post['hambatan_lainnya']) ? ($post['hambatan'][$indexHambatan] == 106 ? $post['hambatan_lainnya'] : "") : "",
                )
              );
            }
            $indexHambatan++;
          }
        }

        // echo "<pre>data hambatan ";print_r($dataHambatan);echo "</pre>";

        $dataAlatBantu = array();
        $indexAlatBantu = 0;
        if (isset($post['alatbantu'])) {
          foreach ($post['alatbantu'] as $input) {
            if ($post['alatbantu'][$indexAlatBantu] != "") {
              array_push(
                $dataAlatBantu, array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['alatbantu'][$indexAlatBantu],
                  'keterangan' => isset($post['alatbantu_lainnya']) ? ($post['alatbantu'][$indexAlatBantu] == 58 ? $post['alatbantu_lainnya'] : "") : "",
                )
              );
            }
            $indexAlatBantu++;
          }
        }

        // echo "<pre>data alat bantu ";print_r($dataAlatBantu);echo "</pre>";

        $dataKebutuhanPembelajaran = array();
        $indexKebutuhanPembelajaran = 0;
        if (isset($post['kebutuhan_pembelajaran'])) {
          foreach ($post['kebutuhan_pembelajaran'] as $input) {
            if ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] != "") {
              array_push(
                $dataKebutuhanPembelajaran, array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran],
                  'keterangan' => isset($post['kebutuhan_pembelajaran_lainnya']) ? ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] == 104 ? $post['kebutuhan_pembelajaran_lainnya'] : "") : "",
                )
              );
            }
            $indexKebutuhanPembelajaran++;
          }
        }

        // echo "<pre>data kebutuhan pembelajaran pasien ";print_r($dataKebutuhanPembelajaran);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 26,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'jenis' => isset($post['skrining_gizi_bb_tb_not']) ? 1 : 0 ,
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";
        // exit();
        
        $this->db->trans_begin();
        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->where('tb_kesadaran.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_kesadaran', $dataKesedaran);
          $this->db->where('tb_tanda_vital.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->where('tb_tb_bb.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->replace('keperawatan.tb_skrining_gizi', $dataSkriningGizi);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->where('tb_skrining_nyeri.ref', $idRefEmr);
          $this->db->update('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->replace('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
          $this->db->delete('keperawatan.tb_hambatan', array('id_emr' => $idRefEmr));
          foreach ($dataHambatan as $key => $value) {
            $this->db->replace('keperawatan.tb_hambatan', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_alat_bantu', array('id_emr' => $idRefEmr));
          foreach ($dataAlatBantu as $key => $value) {
            $this->db->replace('keperawatan.tb_alat_bantu', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_kebutuhan_pembelajaran', array('id_emr' => $idRefEmr));
          foreach ($dataKebutuhanPembelajaran as $key => $value) {
            $this->db->replace('keperawatan.tb_kebutuhan_pembelajaran', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $idRefEmr));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan, array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $this->db->delete('keperawatan.tb_masalah_kesehatan', array('id_emr' => $idRefEmr));
          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan, array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->insert('db_pasien.tb_kesadaran', $dataKesedaran);
          $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->insert('keperawatan.tb_skrining_gizi', $dataSkriningGizi);
          $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->insert('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
          if (isset($post['hambatan'])) {
            $this->db->insert_batch('keperawatan.tb_hambatan', $dataHambatan);
          }
          if (isset($post['alatbantu'])) {
            $this->db->insert_batch('keperawatan.tb_alat_bantu', $dataAlatBantu);
          }
          if (isset($post['kebutuhan_pembelajaran'])) {
            $this->db->insert_batch('keperawatan.tb_kebutuhan_pembelajaran', $dataKebutuhanPembelajaran);
          }
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan, array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan, array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      }

      else if($param == 'count') {
        $result = $this->DewasaModel->get_count();
        echo json_encode($result);
      }

      else if($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataDewasaModel = $this->DewasaModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataDewasaModel
        ));
      }
    }
  }

  public function datatables(){
        $result = $this->MedisModel->historyPengkajian();

        $data = array();
        foreach ($result as $row){
          // $status_edit_perawat = $row -> STATUS_EDIT_PERAWAT;
          // $status_edit_medis = $row -> STATUS_EDIT_MEDIS;
          $action = "";
          $verif = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
          if($row -> ID_EMR_PERAWAT != null){
            $action .= '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>';
            if($this->session->userdata('status') == 2){
              $action .='<button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
              
              if($row -> STATUS_VERIFIKASI == 0){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-clock" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
            }
          }

          if($row -> ID_EMR_MEDIS != null){
            $action .= '<a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>';
            if($this->session->userdata('status') == 1){
              $action .='<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'"><i class="fa fa-eye"></i> lihat</button>';
              if($row -> STATUS_VERIFIKASI == 0){
                $namaVerif = '<h4>-</h4>';
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-close" aria-hidden="true"></i></h4>';
              }elseif($row -> STATUS_VERIFIKASI == 1){
                $namaVerif = $row -> INFO_VERIFIKASI;
                $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
              }
            }
          }

            $sub_array = array();
            $sub_array[] = $row -> INFO;
            $sub_array[] = $verif;
            $sub_array[] = $row -> RUANGAN;
            $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
            $sub_array[] = $action;
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $namaVerif;
            $sub_array[] = $tombolCetak;
            $sub_array[] = $row -> USER_MEDIS;
            $sub_array[] = $row -> USER_PERAWAT;   
            $data[] = $sub_array;
            // if($STATUS_EDIT == 0){
            // $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
            // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
            // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> lihat</button>';
            // }else{
            //   $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
            // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
            // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
            // }
            
        }

        $output = array(
            "draw" => intval($this->input->post("draw")),
            "data"              => $data
        );
        echo json_encode($output);
    }

}