<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProsedurDiagnostik extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();
                $nomor = $this->pengkajianAwalModel->getProsedur();
                $tanggal = date("Y-m-d H:i:s");
                $tujuan = '105060101';
                $cito = 0;
                $alasan = "";
                $oleh = $this->session->userdata("id");

                $data = array(
                    'NOMOR' => $nomor,
                    'KUNJUNGAN' => $this->input->post("nokun"),
                    'TANGGAL' => $tanggal,
                    'DOKTER_ASAL' => $this->input->post("dokter_pengirim"),
                    'TUJUAN' => $tujuan,
                    'CITO' => $cito,
                    'OLEH' => $oleh,
                    'ALASAN' => $alasan,
                    'KLINISI' => $this->input->post('klinisi'),
                    'DIAGNOSA' => $this->input->post("diagnosa"),
                    'PELAKSANA' => $this->input->post("dokter_pelaksana"),
                    'TANGGAL_PERMINTAAN' => $this->input->post("tanggal_permintaan"),
                    'TANGGAL_RENCANA_TINDAKAN' => $this->input->post("tanggal_rencana"),
                    'JAM_TINDAKAN' => $this->input->post("jam_tindakan"),
                    'ASAL_RUANGAN' => $this->input->post("asal_ruangan"),
                );

                $dataAlatBantu = array();
                $indexAlatBantu = 0;
                if (isset($post['tindakan_pd'])) {
                    foreach ($post['tindakan_pd'] as $input) {
                        if ($post['tindakan_pd'][$indexAlatBantu] != "") {
                            array_push(
                                $dataAlatBantu,
                                array(
                                    'ORDER_ID' => $nomor,
                                    'TINDAKAN' => $post['tindakan_pd'][$indexAlatBantu],
                                    'REF' => "",
                                )
                            );
                        }
                        $indexAlatBantu++;
                    }
                }

                $this->db->trans_begin();
                $this->db->insert('layanan.order_prosedur_diagnostik', $data);
                $this->db->insert_batch('layanan.order_detil_prosedur_diagnostik', $dataAlatBantu);
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            } elseif ($param == 'batal') {
                $data = $this->pengkajianAwalModel->cekStatusProsedur();
                if ($data['STATUS'] == 1) {
                    $this->db->set('STATUS', 0);
                    $this->db->where('NOMOR', $this->input->post('id'));
                    $this->db->update('layanan.order_prosedur_diagnostik');
                    $result = array('status' => 'success', 'pesan' => 'Berhasil Di Batalkan');
                } elseif ($data['STATUS'] == 2) {
                    $result = array('status' => 'failed', 'pesan' => 'Status Sudah Di Terima');
                } elseif ($data['STATUS'] == 0) {
                    $result = array('status' => 'failed', 'pesan' => 'Status Di Batalkan');
                }

                echo json_encode($result);
            }
        }
    }

    public function index()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
        $resultTindakanProsedur = $this->masterModel->tindakanPenunjang('105060101');
        $dataTindakanProsedur = array();
        foreach ($resultTindakanProsedur->result() as $tindakanProsedur) {
            $resultTindakanProsedurSub = $this->masterModel->tindakanPenunjang('105060101', $tindakanProsedur->id);
            $jenisTindakan = array();
            if ($resultTindakanProsedurSub->num_rows() > 0) {
                $jenisTindakan['id'] = $tindakanProsedur->id;
                $jenisTindakan['jenis'] = $tindakanProsedur->deskripsi;
                $subJenisTindakan = array();
                foreach ($resultTindakanProsedurSub->result() as $subTindakanProsedur) {
                    $subsubJenisTindakan = array();
                    $subsubJenisTindakan['id'] = $subTindakanProsedur->id;
                    $subsubJenisTindakan['tindakan'] = $subTindakanProsedur->tindakan;
                    $subsubJenisTindakan['jenis'] = $subTindakanProsedur->deskripsi;
                    $subJenisTindakan[] = $subsubJenisTindakan;
                }
                $jenisTindakan['subJenis'] = $subJenisTindakan;
            }
            $dataTindakanProsedur[] = $jenisTindakan;
        }

        $data = array(
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'hOrderProsedur' => $this->pengkajianAwalModel->historyOrderProsedurDiagnostik($pasien['NOKUN']),
            'dataTindakanProsedur' => $dataTindakanProsedur,
            'listDrUmum' => $this->masterModel->listDrUmum(),
            'ruanganRskd' => $this->masterModel->ruanganRskd(),
        );

        $this->load->view('rekam_medis/penunjang/prosedur_diagnostik/index', $data);
    }
}
