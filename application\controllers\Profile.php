<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Profile extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(1,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('profileModel','masterModel'));
  }

  public function index()
  {
    $id_pengguna = $this->session->userdata('id');
    $namaPegawai = $this->profileModel->namaPegawai($id_pengguna);

    $data = array(
      'title'       => 'Halaman Profile',
      'isi'         => 'Profile/index',
      'namaPegawai' => $namaPegawai,
    );

    $this->load->view('layout/wrapper',$data);
  }

  // public function hasilHistoryPasienCovid()
  // {
  //   $nomr = $this->input->post('nomr');
  //   $nama = $this->input->post('nama');
  //   $detailListPasien = $this->profileModel->detailListPasien($nomr);

  //   $data = array(
  //     'nomr' => $nomr,
  //     'nama' => $nama,
  //     'detailListPasien' => $detailListPasien,
  //   );

  //   $this->load->view('Profile/dataPasienCovid', $data);
  // }

}

/* End of file Profile.php */
/* Location: ./application/controllers/Profile.php */
