<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SkriningPerasaanTertekan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/SkriningPerasaanTertekanModel'));
    }

    public function index()
    {
        $data = array(
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'listDiskusiKasus' => $this->masterModel->referensi(889),
            'listStatusRawat' => $this->masterModel->referensi(890),
            'listSMF' => $this->masterModel->listSMF(),
            'listDr' => $this->masterModel->listDr(),
            'listPr' => $this->masterModel->listPerawat(),
            'thermometer' => $this->masterModel->referensi(170),
        );
        $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/skriningPerasaanTertekan', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $rules = $this->SkriningPerasaanTertekanModel->rules;
                $this->form_validation->set_rules($rules);

                if ($this->form_validation->run() == TRUE) {
                    $idsps = $this->input->post("id");
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataSkrining = array(
                        'id' => $post['id'],
                        'nokun' => $post['nokun'],
                        'thermometer' => $post['thermometer'],
                        'mengasuh_anak' => $post['mengasuh_anak'],
                        'penampilan' => $post['penampilan'],
                        'pekerjaan_rumah' => $post['pekerjaan_rumah'],
                        'mandi_pakaian' => $post['mandi_pakaian'],
                        'asuransi_keuangan' => $post['asuransi_keuangan'],
                        'bernapas' => $post['bernapas'],
                        'transportasi' => $post['transportasi'],
                        'perubahan_berkemih' => $post['perubahan_berkemih'],
                        'pekerjaan_sekolah' => $post['pekerjaan_sekolah'],
                        'sembelit' => $post['sembelit'],
                        'diare' => $post['diare'],
                        'makan' => $post['makan'],
                        'hubungan_anak' => $post['hubungan_anak'],
                        'kelelahan' => $post['kelelahan'],
                        'hubungan_pasangan' => $post['hubungan_pasangan'],
                        'merasa_kembung' => $post['merasa_kembung'],
                        'memiliki_anak' => $post['memiliki_anak'],
                        'demam' => $post['demam'],
                        'pusing' => $post['pusing'],
                        'gangguan_cerna' => $post['gangguan_cerna'],
                        'depresi' => $post['depresi'],
                        'ingatan_konsen' => $post['ingatan_konsen'],
                        'ketakutan' => $post['ketakutan'],
                        'sariawan' => $post['sariawan'],
                        'gugup' => $post['gugup'],
                        'mual' => $post['mual'],
                        'kesedihan' => $post['kesedihan'],
                        'hidung_mampet' => $post['hidung_mampet'],
                        'khawatir' => $post['khawatir'],
                        'nyeri' => $post['nyeri'],
                        'hilang_minat' => $post['hilang_minat'],
                        'seksual' => $post['seksual'],
                        'kulit_kering' => $post['kulit_kering'],
                        'spiritual' => $post['spiritual'],
                        'tidur' => $post['tidur'],
                        'kesemutan' => $post['kesemutan'],
                        'masalah_lainnya' => $post['masalah_lainnya'],
                        'oleh' => $this->session->userdata("id")
                        );
                    // echo '<pre>';
                    // print_r($dataSkrining);
                    // exit();

                    if (!empty($idsps)) {
                        $this->db->where('db_pasien.tb_skrining_perasaan_tertekan.id', $idsps);
                        $this->db->update('db_pasien.tb_skrining_perasaan_tertekan', $dataSkrining);
                    } else {
                        $this->db->insert('db_pasien.tb_skrining_perasaan_tertekan', $dataSkrining);
                        $idsps = $this->db->insert_id();
                    }
                    //$this->db->replace('db_layanan.tb_planofcare', $dataPlanOfcare);


                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                } else {
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            } else if ($param == 'ambil') {
                $post = $this->input->post(NULL, TRUE);
                $dataSkrining = $this->SkriningPerasaanTertekanModel->get($post['id'], true);

                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataSkrining
                ));
            } else if ($param == 'count') {
                $result = $this->SkriningPerasaanTertekanModel->get_count();;
                echo json_encode($result);
            }
        }
    }

    public function datatables()
    {
        $result = $this->SkriningPerasaanTertekanModel->datatables();

        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_skrinigPerasaan" data-id="' . $row->ID . '"><i class="fas fa-edit"></i> Edit</a><a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/tertekan/skriningperasaantertekan.php?format=pdf&id='. $row->ID .'" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row->TANGGAL;
            $sub_array[] = $row->RUANGAN_KUNJUNGAN;
            $sub_array[] = $row->DPJP;
            $sub_array[] = $row->USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->SkriningPerasaanTertekanModel->total_count(),
            "recordsFiltered"   => $this->SkriningPerasaanTertekanModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
