<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianTerapiSistemikRJ extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/PengkajianTSRJModel'
    ));
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $id_view = $this->uri->segment(3);
    $pasien = $this->MedisModel->getNomrRawatInap($this->uri->segment(2));

    $getPengkajian = $this->PengkajianTSRJModel->getPengkajian(isset($id_view) ? $id_view : null);
    $getBarthelIndek = $this->PengkajianTSRJModel->getBarthelIndek(isset($getPengkajian['nokun']) ? $getPengkajian['nokun'] : null);

    // echo "<pre>".print_r($getPengkajian)."</pre>";
    // echo "<script>alert('".$id_view."')</script>";
    // echo "<script>console.log('".$nokun."')</script>";

    $data = array(
      'nokun' => $nokun,
      'pasien' => $pasien,
      'id_view' => $id_view,
      'getPengkajian' => $getPengkajian,
      'getBarthelIndek' => $getBarthelIndek,
      'anamnesis' => $this->masterModel->referensi(54),
      'riwayatEkstravasasi' => $this->masterModel->referensi(164),
      'hasilLaboratorium' => $this->masterModel->referensi(165),
      'hasilBmpTerakhir' => $this->masterModel->referensi(166),
      'kemoterapiTerdahulu' => $this->masterModel->referensi(167),
      'tindakanPerawatanTerakhir' => $this->masterModel->referensi(168),
      'riwayatGVHD' => $this->masterModel->referensi(169),
      'fotodepanbelakang' => $this->masterModel->referensi(253),
      'fotodepan' => $this->masterModel->referensi(254),
      'fotobelakang' => $this->masterModel->referensi(255),
      'ESASnyeri' => $this->masterModel->referensi(170),
      'ESASlelah' => $this->masterModel->referensi(171),
      'ESASmual' => $this->masterModel->referensi(172),
      'ESASdepresi' => $this->masterModel->referensi(173),
      'ESAScemas' => $this->masterModel->referensi(174),
      'ESASmengantuk' => $this->masterModel->referensi(175),
      'ESASnafsuMakan' => $this->masterModel->referensi(176),
      'ESASsehat' => $this->masterModel->referensi(177),
      'ESASsesakNapas' => $this->masterModel->referensi(178),
      'ESASmasalah' => $this->masterModel->referensi(179),
      'kesadaran' => $this->masterModel->referensi(5),
      'listRangsangbab' => $this->masterModel->referensi(834),
      'listRangsangberkemih' => $this->masterModel->referensi(835),
      'listMembersihkandiri' => $this->masterModel->referensi(836),
      'listPenggunaankloset' => $this->masterModel->referensi(837),
      'listMakan' => $this->masterModel->referensi(838),
      'listBerubahposisi' => $this->masterModel->referensi(839),
      'listBerpindah' => $this->masterModel->referensi(840),
      'listMemakaibaju' => $this->masterModel->referensi(841),
      'listNaiktangga' => $this->masterModel->referensi(842),
      'listMandi' => $this->masterModel->referensi(843),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
      'pengkajianNyeriTime' => $this->masterModel->referensi(12),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'skriningResikoJatuhPusing' => $this->masterModel->referensi(120),
      'skriningResikoJatuhBerdiri' => $this->masterModel->referensi(121),
      'skriningResikoJatuh6Bulan' => $this->masterModel->referensi(122),
      'psikologis' => $this->masterModel->referensi(13),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'sosialDanEkonomiPencariNafkah' => $this->masterModel->referensi(15),
      'sosialDanEkonomiTinggalSerumah' => $this->masterModel->referensi(16),
      'programPengobatanDenganKeyakinan' => $this->masterModel->referensi(17),
      'pengobatanAlternatif' => $this->masterModel->referensi(146),
      'pengobatanBudaya' => $this->masterModel->referensi(147),
      'alatBantu' => $this->masterModel->referensi(19),
      'pendidikan' => $this->masterModel->referensi(24),
      'bahasaSehari' => $this->masterModel->referensi(25),
      'perluPenerjemah' => $this->masterModel->referensi(26),
      'kesediaanInformasi' => $this->masterModel->referensi(27),
      'hambatan' => $this->masterModel->referensi(28),
      'kebutuhanPembelajaran' => $this->masterModel->referensi(29),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'apakahInginMasukEws' => $this->masterModel->referensi(1162),
      'PENGGUNAAN_O2' => $this->masterModel->referensi(129),
      'masalahKesehatan' => $this->masterModel->formMasalahKesehatan()
    );
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/pengkajianTerapiSistemikRJ', $data);
  }

  public function asuhanKeperawatan_edit()
  {
    $id = $this->input->post('id');
    $nokun = $this->input->post('nokun');

    $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
    $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
    $getPengkajian = $this->PengkajianTSRJModel->getPengkajian($nokun);

    $data = array(
      'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
      'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
      'getPengkajian' => $getPengkajian,
    );

    $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
  }

  public function simpanPengkajianRiTSRJ($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');
        $masukEwsAtauTidak = $this->input->post('masukKeEwsTSRJ');
        $id_tanda_vital = $this->input->post('id_tanda_vital');
        $id_kesadaran = $this->input->post('id_kesadaran');
        $id_o2 = $this->input->post('id_o2');

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 17,
          'diagnosa_masuk' => $post['rujukanRiTSRJ'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAnamnesa = array(
          'id_emr' => $getIdEmr,
          'id_auto_allo' => $post['anamnesis'],
          'allo_nama' => isset($post['allo_nama']) ? $post['allo_nama'] : "",
          'hubungan_dengan_pasien' => isset($post['hubungan_dengan_pasien']) ? $post['hubungan_dengan_pasien'] : "",
          'info_dari_keluarga_pasien' => isset($post['informasiDariKeluargaPasien']) ? $post['informasiDariKeluargaPasien'] : "",
        );

        // echo "<pre>data anamnesa ";print_r($dataAnamnesa);echo "</pre>";

        $dataRiwayatKesehatan = array(
          'id_emr' => $getIdEmr,
          'riwayat_ekstravasasi' => isset($post['riwayat_ekstravasasi']) ? $post['riwayat_ekstravasasi'] : "",
          'isi_ekstravasasi' => isset($post['riwayat_ekstravasasi_desc']) ? $post['riwayat_ekstravasasi_desc'] : "",
          'pilih_ekstravasasi_foto' => isset($post['riwayat_ekstravasasi_foto']) ? $post['riwayat_ekstravasasi_foto'] : "",
          'ekstravasasi_depan' => isset($post['ekstravasasi_depan']) ? $post['ekstravasasi_depan'] : "",
          'ekstravasasi_belakang' => isset($post['ekstravasasi_belakang']) ? $post['ekstravasasi_belakang'] : "",
          'hasil_laboratorium' => isset($post['hasilLaboratoriumRiTSRJ']) ? $post['hasilLaboratoriumRiTSRJ'] : "",
          'isi_laboratorium' => isset($post['hasil_laboratorium_desk']) ? json_encode($post['hasil_laboratorium_desk']) : "",
          'hasil_BMP' => isset($post['hasilBmpTerakhirRiTS']) ? $post['hasilBmpTerakhirRiTS'] : "",
          'isi_BMP' => isset($post['deskHasilBmpTerakhirRiTSRJ']) ? json_encode($post['deskHasilBmpTerakhirRiTSRJ']) : "",
          'kemoterapi' => isset($post['hasilKemoterapiRiTSRJ']) ? $post['hasilKemoterapiRiTSRJ'] : "",
          'isi_kemoterapi' => isset($post['deskHasilKemoterapiRiTSRJ']) ? json_encode($post['deskHasilKemoterapiRiTSRJ']) : "",
          'tindakan_perawatan' => isset($post['tindakanPerawatanTerakhirRiTSRJ']) ? $post['tindakanPerawatanTerakhirRiTSRJ'] : "",
          'perawatan_lain' => isset($post['tindakanPerawatan_lain']) ? json_encode($post['tindakanPerawatan_lain']) : "",
          'riwayat_graft' => isset($post['riwayatGvhdRiTS']) ? $post['riwayatGvhdRiTS'] : "",
        );

        // echo "<pre>data Riwayat Kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $dataTbBb = array(
          'data_source' => 31,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
          'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        // echo "<pre>data data TB BB ";print_r($dataTbBb);echo "</pre>";

        $dataKesedaran = array(
          'data_source' => 31,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($post['idemr'])) {
          $this->db->where('tb_kesadaran.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_kesadaran', $dataKesedaran);
        } else {
          $getIdKesadaran = $this->PengkajianTSRJModel->simpanKesadaran($dataKesedaran);
        }

        // echo "<pre>data Kesadaran ";print_r($dataKesedaran);echo "</pre>";

        $dataTandaVital = array(
          'data_source' => 31,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
          'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
          'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
          'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($post['idemr'])) {
          $this->db->where('tb_tanda_vital.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
        } else {
          $getIdTandaVital = $this->PengkajianTSRJModel->simpanTandaVital($dataTandaVital);
        }

        // echo "<pre>data Tanda Vital ";print_r($dataTandaVital);echo "</pre>";

        $dataSkriningNyeri = array(
          'nokun' => $post['nokun'],
          'data_source' => 31,
          'ref' => $getIdEmr,
          'metode' => isset($post['skrining_nyeri_TSRJ']) ? $post['skrining_nyeri_TSRJ'] : "",
          'skor' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : "",
          'provokative' => isset($post['propocative_TSRJ']) ? $post['propocative_TSRJ'] : "",
          'quality' => isset($post['quality_TSRJ']) ? $post['quality_TSRJ'] : "",
          'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
          'regio' => isset($post['regio_TSRJ']) ? $post['regio_TSRJ'] : "",
          'severity' => isset($post['severity_TSRJ']) ? $post['severity_TSRJ'] : "",
          'time' => isset($post['time_TSRJ']) ? $post['time_TSRJ'] : "",
          'ket_time' => isset($post['durasi_nyeri_TSRJ']) ? $post['durasi_nyeri_TSRJ'] : "",
          'status' => 1,
          'created_by' => $this->session->userdata('id'),
        );

        // echo "<pre>data skrining nyeri ";print_r($dataSkriningNyeri);echo "</pre>";

        $dataPemeriksaanFisik = array(
          'id_emr' => $getIdEmr,
          'keluhan_pasien' => isset($post['keluhan_pasien']) ? $post['keluhan_pasien'] : "",
          'rhesus' => isset($post['rhesusTerapiSistemikRi']) ? $post['rhesusTerapiSistemikRi'] : "",
          'skala_nyeri' => isset($post['skala_nyeri']) ? $post['skala_nyeri'] : "",
          'skala_lelah' => isset($post['skala_lelah']) ? $post['skala_lelah'] : "",
          'skala_mual' => isset($post['skala_mual']) ? $post['skala_mual'] : "",
          'skala_depresi' => isset($post['skala_depresi']) ? $post['skala_depresi'] : "",
          'skala_cemas' => isset($post['skala_cemas']) ? $post['skala_cemas'] : "",
          'skala_mengantuk' => isset($post['skala_mengantuk']) ? $post['skala_mengantuk'] : "",
          'skala_nafsu_makan' => isset($post['skala_nafsuMakan']) ? $post['skala_nafsuMakan'] : "",
          'skala_sehat' => isset($post['skala_sehat']) ? $post['skala_sehat'] : "",
          'skala_sesak_napas' => isset($post['skala_sesakNapas']) ? $post['skala_sesakNapas'] : "",
          'skala_masalah' => isset($post['skala_masalah']) ? $post['skala_masalah'] : "",
          'keluhan_pasien' => isset($post['keluhan_pasien']) ? $post['keluhan_pasien'] : "",
          'masalah_kesehatan_keperawatan' => isset($post['masalahKesehatanKeperawatanTSRJ']) ? json_encode($post['masalahKesehatanKeperawatanTSRJ']) : "",
          'keyakinan' => isset($post['program_pengobatan_keyakinan']) ? $post['program_pengobatan_keyakinan'] : "",
          'sebutkan_keyakinan' => isset($post['desk_program_pengobatan_keyakinan']) ? $post['desk_program_pengobatan_keyakinan'] : "",
          'pengobatan_alternatif' => isset($post['pengobatan_alternatif']) ? $post['pengobatan_alternatif'] : "",
          'sebutkan_pengobatan_alternatif' => isset($post['desk_pengobatan_alternatif']) ? json_encode($post['desk_pengobatan_alternatif']) : "",
          'pengobatan_bertentangan' => isset($post['pengobatan_budaya']) ? $post['pengobatan_budaya'] : "",
          'sebutkan_pengobatan_bertentangan' => isset($post['pengobatan_budaya']) ? json_encode($post['pengobatan_budaya']) : "",
          'vertigo' => isset($post['skriningresikojatuhpusingRadioterapiRj']) ? $post['skriningresikojatuhpusingRadioterapiRj'] : "",
          'sulit_berdiri' => isset($post['skriningresikojatuhberdiriRadioterapiRj']) ? $post['skriningresikojatuhberdiriRadioterapiRj'] : "",
          'jatuh_dlm_6' => isset($post['skriningresikojatuh6bulanRadioterapiRj']) ? $post['skriningresikojatuh6bulanRadioterapiRj'] : "",
          'psikologis' => isset($post['psikologis']) ? $post['psikologis'] : null,
          'isi_psikologi' => isset($post['psikologis_lainnya']) ? $post['psikologis_lainnya'] : null,
          'hub_keluarga' => isset($post['sdeh']) ? $post['sdeh'] : "",
          'nafkah_utama' => isset($post['sdepn']) ? $post['sdepn'] : "",
          'tinggal' => isset($post['sdets']) ? $post['sdets'] : "",
          'apakah_ingin_masuk_keews' => isset($post['masukKeEwsTSRJ']) ? $post['masukKeEwsTSRJ'] : "",
        );

        // echo "<pre>data pemeriksaan fisik ";print_r($dataPemeriksaanFisik);echo "</pre>";

        $dataAlatBantu = array();
        $indexAlatBantu = 0;
        if (isset($post['alatbantu'])) {
          foreach ($post['alatbantu'] as $input) {
            if ($post['alatbantu'][$indexAlatBantu] != "") {
              array_push(
                $dataAlatBantu,
                array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['alatbantu'][$indexAlatBantu],
                  'keterangan' => isset($post['alatbantu_lainnya']) ? ($post['alatbantu'][$indexAlatBantu] == 58 ? $post['alatbantu_lainnya'] : "") : "",
                )
              );
            }
            $indexAlatBantu++;
          }
        }

        // echo "<pre>data alat bantu ";print_r($dataAlatBantu);echo "</pre>";

        $dataEdukasiKeperawatan = array(
          'id_emr' => $getIdEmr,
          'tingkat_pendidikan' => isset($post['tingkat_pendidikan']) ? $post['tingkat_pendidikan'] : "",
          'bahasa' => isset($post['bahasa_sehari_hari']) ? $post['bahasa_sehari_hari'] : "",
          'bahasa_daerah' => isset($post['bahasa_daerah']) ? $post['bahasa_daerah'] : "",
          'bahasa_lain' => isset($post['bahasa_lainnya']) ? $post['bahasa_lainnya'] : "",
          'penerjemah' => isset($post['perlu_penerjemah']) ? $post['perlu_penerjemah'] : "",
          'penerjemah_lain' => isset($post['penerjemah_lainnya']) ? $post['penerjemah_lainnya'] : "",
          'informasi' => isset($post['kesedian_informasi']) ? $post['kesedian_informasi'] : "",
        );

        // echo "<pre>data kebutuhan edukasi ";print_r($dataEdukasiKeperawatan);echo "</pre>";

        $dataHambatan = array();
        $indexHambatan = 0;
        if (isset($post['hambatan'])) {
          foreach ($post['hambatan'] as $input) {
            if ($post['hambatan'][$indexHambatan] != "") {
              array_push(
                $dataHambatan,
                array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['hambatan'][$indexHambatan],
                  'keterangan' => isset($post['hambatan_lainnya']) ? ($post['hambatan'][$indexHambatan] == 106 ? $post['hambatan_lainnya'] : "") : "",
                )
              );
            }
            $indexHambatan++;
          }
        }

        // echo "<pre>data hambatan ";print_r($dataHambatan);echo "</pre>";

        $dataKebutuhanPembelajaran = array();
        $indexKebutuhanPembelajaran = 0;
        if (isset($post['kebutuhan_pembelajaran'])) {
          foreach ($post['kebutuhan_pembelajaran'] as $input) {
            if ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] != "") {
              array_push(
                $dataKebutuhanPembelajaran,
                array(
                  'id_emr' => $getIdEmr,
                  'id_variabel' => $post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran],
                  'keterangan' => isset($post['kebutuhan_pembelajaran_lainnya']) ? ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] == 104 ? $post['kebutuhan_pembelajaran_lainnya'] : "") : "",
                )
              );
            }
            $indexKebutuhanPembelajaran++;
          }
        }

        // echo "<pre>data kebutuhan pembelajaran pasien ";print_r($dataKebutuhanPembelajaran);echo "</pre>";

        $dataO2 = array(
          'data_source' => 31,
          'ref' => $getIdEmr,
          'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
          'nokun' => $post['nokun'],
          'saturasi_o2' => isset($post['deskMasukEws']) ? $post['deskMasukEws'] : "",
          'penggunaan_o2' => isset($post['penggunaanO2TSRJ']) ? $post['penggunaanO2TSRJ'] : "",
          'oleh' => $this->session->userdata('id'),
          'status' => 1,
        );

        if (!empty($post['idemr'])) {
          $this->db->where('tb_o2.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_o2', $dataO2);
        } else {
          $getIdO2 = $this->PengkajianTSRJModel->simpanO2($dataO2);
        }
        // echo "<pre>data data o2 ";print_r($dataO2);echo "</pre>";

        $pernapasanEws = $this->input->post('pernapasan');
        $nadiEws = $this->input->post('nadi');
        $tekananDarahSistolikEws = $this->input->post('tekanan_darah_1');
        $suhuEws = $this->input->post('suhu');
        $saturasiO2Ews = $this->input->post('deskMasukEws');
        $penggunaanO2Ews = $this->input->post('penggunaanO2RiA');
        $kesadaranEws = $this->input->post('kesadaran');

        // KONDISI UNTUK PERNAPASAN EWS
        if ($pernapasanEws >= 25) {
          $hasilPernapasan = 3;
        } elseif ($pernapasanEws >= 21 && $pernapasanEws <= 24.99) {
          $hasilPernapasan = 2;
        } elseif ($pernapasanEws >= 12 && $pernapasanEws <= 20.99) {
          $hasilPernapasan = 0;
        } elseif ($pernapasanEws >= 9 && $pernapasanEws <= 11.99) {
          $hasilPernapasan = 1;
        } elseif ($pernapasanEws <= 8.99) {
          $hasilPernapasan = 3;
        }

        // KONDISI UNTUK DENYUT NADI EWS
        if ($nadiEws >= 130) {
          $nadiEws = 3;
        } elseif ($nadiEws >= 111 && $nadiEws <= 129.99) {
          $nadiEws = 2;
        } elseif ($nadiEws >= 101 && $nadiEws <= 110.99) {
          $nadiEws = 1;
        } elseif ($nadiEws >= 60 && $nadiEws <= 100.99) {
          $nadiEws = 0;
        } elseif ($nadiEws >= 51 && $nadiEws <= 59.99) {
          $nadiEws = 1;
        } elseif ($nadiEws >= 40 && $nadiEws <= 50.99) {
          $nadiEws = 2;
        } elseif ($nadiEws <= 39.99) {
          $nadiEws = 3;
        }

        // KONDISI UNTUK TEKANAN DARAH SISTOLIK
        if ($tekananDarahSistolikEws >= 180) {
          $tekananDarahSistolikEws = 3;
        } elseif ($tekananDarahSistolikEws >= 170 && $tekananDarahSistolikEws <= 179.99) {
          $tekananDarahSistolikEws = 2;
        } elseif ($tekananDarahSistolikEws >= 150 && $tekananDarahSistolikEws <= 169.99) {
          $tekananDarahSistolikEws = 1;
        } elseif ($tekananDarahSistolikEws >= 101 && $tekananDarahSistolikEws <= 149.99) {
          $tekananDarahSistolikEws = 0;
        } elseif ($tekananDarahSistolikEws >= 81 && $tekananDarahSistolikEws <= 100.99) {
          $tekananDarahSistolikEws = 1;
        } elseif ($tekananDarahSistolikEws >= 71 && $tekananDarahSistolikEws <= 80.99) {
          $tekananDarahSistolikEws = 2;
        } elseif ($tekananDarahSistolikEws <= 70.99) {
          $tekananDarahSistolikEws = 3;
        }

        // KONDISI UNTUK SUHU
        if ($suhuEws >= 39) {
          $suhuEws = 2;
        } elseif ($suhuEws >= 38 && $suhuEws <= 38.99) {
          $suhuEws = 1;
        } elseif ($suhuEws >= 36 && $suhuEws <= 37.99) {
          $suhuEws = 0;
        } elseif ($suhuEws <= 35.99) {
          $suhuEws = 3;
        }

        // KONDISI UNTUK SATURASI O2
        if ($saturasiO2Ews >= 96) {
          $saturasiO2Ews = 0;
        } elseif ($saturasiO2Ews >= 94 && $saturasiO2Ews <= 95.99) {
          $saturasiO2Ews = 1;
        } elseif ($saturasiO2Ews >= 92 && $saturasiO2Ews <= 93.99) {
          $saturasiO2Ews = 2;
        } elseif ($saturasiO2Ews <= 91.99) {
          $saturasiO2Ews = 3;
        }

        // KONDISI UNTUK PENGGUNAAN O2
        if ($penggunaanO2Ews == 388) {
          $penggunaanO2Ews = 2;
        } elseif ($penggunaanO2Ews == 389) {
          $penggunaanO2Ews = 0;
        }

        // KONDISI UNTUK TINGKAT KESADARAN
        if ($kesadaranEws == 105) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 12) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 11) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 10) {
          $kesadaranEws = 3;
        } elseif ($kesadaranEws == 9) {
          $kesadaranEws = 0;
        }

        $totalScoreEws = $hasilPernapasan + $nadiEws + $tekananDarahSistolikEws + $suhuEws + $saturasiO2Ews + $penggunaanO2Ews + $kesadaranEws;

        if (!empty($id_ews)) {
          $dataTotalScoreEws = array(
            'id_tanda_vital' => $id_tanda_vital,
            'id_kesadaran' => $id_kesadaran,
            'id_o2' => $id_o2,
            'tanggal' => date("Y-m-d"),
            'jam' => date("H:i:s"),
            'score_ews' => $totalScoreEws,
            'ref' => $getIdEmr,
            'nokun' => $post['nokun'],
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
          );
        } else if (empty($id_ews)) {
          if (!empty($post['idemr'])) {
            $dataTotalScoreEws = array(
              'id_tanda_vital' => $id_tanda_vital,
              'id_kesadaran' => $id_kesadaran,
              'id_o2' => $id_o2,
              'tanggal' => date("Y-m-d"),
              'jam' => date("H:i:s"),
              'score_ews' => $totalScoreEws,
              'ref' => $getIdEmr,
              'nokun' => $post['nokun'],
              'oleh' => $this->session->userdata('id'),
              'status' => 1,
            );
          } else if (empty($post['idemr'])) {
            $dataTotalScoreEws = array(
              'id_tanda_vital' => $getIdTandaVital,
              'id_kesadaran' => $getIdKesadaran,
              'id_o2' => $getIdO2,
              'tanggal' => date("Y-m-d"),
              'jam' => date("H:i:s"),
              'score_ews' => $totalScoreEws,
              'ref' => $getIdEmr,
              'nokun' => $post['nokun'],
              'oleh' => $this->session->userdata('id'),
              'status' => 1,
            );
          }
        }

        $this->db->trans_begin();
        if (!empty($id_ews)  && $masukEwsAtauTidak == 3863) {
          $this->db->where('tb_ews.id', $id_ews);
          $this->db->update('keperawatan.tb_ews', $dataTotalScoreEws);
        } else if (empty($id_ews)  && $masukEwsAtauTidak == 3863) {
          $this->db->insert('keperawatan.tb_ews', $dataTotalScoreEws);
        }

        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->where('tb_tb_bb.ref', $idRefEmr);
          $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->where('tb_skrining_nyeri.ref', $idRefEmr);
          $this->db->update('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->replace('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success', 'pesan' => 'ubah');
          }
          $this->db->delete('keperawatan.tb_alat_bantu', array('id_emr' => $idRefEmr));
          foreach ($dataAlatBantu as $key => $value) {
            $this->db->replace('keperawatan.tb_alat_bantu', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_hambatan', array('id_emr' => $idRefEmr));
          foreach ($dataHambatan as $key => $value) {
            $this->db->replace('keperawatan.tb_hambatan', $value, 'id_emr');
          }
          $this->db->delete('keperawatan.tb_kebutuhan_pembelajaran', array('id_emr' => $idRefEmr));
          foreach ($dataKebutuhanPembelajaran as $key => $value) {
            $this->db->replace('keperawatan.tb_kebutuhan_pembelajaran', $value, 'id_emr');
          }

          $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $idRefEmr));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $this->db->delete('keperawatan.tb_masalah_kesehatan', array('id_emr' => $idRefEmr));
          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesa);
          $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
          $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
          $this->db->insert('keperawatan.tb_skrining_nyeri', $dataSkriningNyeri);
          $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
          $this->db->insert('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
          if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
            $result = array('status' => 'success');
          }
          if (isset($post['alatbantu'])) {
            $this->db->insert_batch('keperawatan.tb_alat_bantu', $dataAlatBantu);
          }
          if (isset($post['hambatan'])) {
            $this->db->insert_batch('keperawatan.tb_hambatan', $dataHambatan);
          }
          if (isset($post['kebutuhan_pembelajaran'])) {
            $this->db->insert_batch('keperawatan.tb_kebutuhan_pembelajaran', $dataKebutuhanPembelajaran);
          }
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          $dataMasalahKesehatan = array();
          $index = 0;
          if (isset($post['mslhnKeshatann'])) {
            foreach ($post['mslhnKeshatann'] as $input) {
              if ($post['mslhnKeshatann'][$index] != "") {
                array_push(
                  $dataMasalahKesehatan,
                  array(
                    'id_emr' => $getIdEmr,
                    'id_masalah_kesehatan' => $post['mslhnKeshatann'][$index]
                    // 'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_masalah_kesehatan', $dataMasalahKesehatan);
          }
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      } else if ($param == 'count') {
        $result = $this->PengkajianTSRJModel->get_count();
        echo json_encode($result);
      } else if ($param == 'ambil') {
        $post = $this->input->post(NULL, TRUE);
        $dataPengkajianTSRJModel = $this->PengkajianTSRJModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data' => $dataPengkajianTSRJModel
        ));
      }
    }
  }

  public function masalahKesehatan_edit()
  {
    $id = $this->input->post('id');
    $nokun = $this->input->post('nokun');

    $resultMasalahKesehatan = $this->masterModel->masalahKesehatan($id);
    $resultMasalahKesehatanDetil = $this->masterModel->masalahKesehatanDetil($resultMasalahKesehatan->ID);
    $getPengkajian = $this->PengkajianTSRJModel->getPengkajian($nokun);

    $data = array(
      'titleMasalahKesehatan' => $resultMasalahKesehatan->KATEGORI,
      'DataMasalahKesehatan' => $resultMasalahKesehatanDetil,
      'getPengkajian' => $getPengkajian,
    );

    $this->load->view('Pengkajian/emr/masalahKesehatan/masalahKesehatan_edit', $data);
  }

  public function simpanIndeksBarthel()
  {
    $nokun = $this->input->post('nokun');
    $total_barthel_1 = $this->input->post('total_barthel_1');
    $total_barthel_2 = $this->input->post('total_barthel_2');
    $total_barthel_3 = $this->input->post('total_barthel_3');
    $total_barthel_4 = $this->input->post('total_barthel_4');
    $total_barthel_5 = $this->input->post('total_barthel_5');
    $total_barthel_6 = $this->input->post('total_barthel_6');
    $total_barthel_7 = $this->input->post('total_barthel_7');
    $total_barthel_8 = $this->input->post('total_barthel_8');
    $total_barthel_9 = $this->input->post('total_barthel_9');
    $total_barthel_10 = $this->input->post('total_barthel_10');
    $oleh = $this->session->userdata("id");

    $totalSkorIndeksBarthel = $total_barthel_1 + $total_barthel_2 + $total_barthel_3 + $total_barthel_4 + $total_barthel_5 + $total_barthel_6 + $total_barthel_7 + $total_barthel_8 + $total_barthel_9 + $total_barthel_10;

    if ($totalSkorIndeksBarthel == 20) {
      echo "<div class='alert alert-success' role='alert'> Score <b>20</b> = <b>Mandiri</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 4) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan total</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 8) {
      echo "<div class='alert alert-danger' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan berat</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 11) {
      echo "<div class='alert alert-warning' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan sedang</b></div>";
    } elseif ($totalSkorIndeksBarthel <= 19) {
      echo "<div class='alert alert-success' role='alert'>Score <b>" . $totalSkorIndeksBarthel . "</b> = <b>Ketergantungan ringan</b></div>";
    }

    $data = array(
      'nokun' => $nokun,
      'ref' => 17,
      'rangsang_bab' => $total_barthel_1,
      'rangsang_berkemih' => $total_barthel_2,
      'bersihkan_diri' => $total_barthel_3,
      'penggunaan_kloset' => $total_barthel_4,
      'makan' => $total_barthel_5,
      'berubah_posisi' => $total_barthel_6,
      'berpindah' => $total_barthel_7,
      'memakai_baju' => $total_barthel_8,
      'naik_tangga' => $total_barthel_9,
      'mandi' => $total_barthel_10,
      'oleh' => $oleh,
    );

    $jumlah = $this->PengkajianTSRJModel->get_count_indeksBarthel($nokun);
    if ($jumlah == 0) {
      $this->db->insert('keperawatan.tb_barthel_indek', $data);
    } else {
      $this->db->where('nokun', $nokun);
      $this->db->update('keperawatan.tb_barthel_indek', $data);
    }
  }

  public function yakinVerifPengkajian()
  {
    $idemr = $this->input->post('idemr_verif');
    $oleh = $this->session->userdata("id");

    $data = array(
      'status_verif' => 1,
      'verif_oleh' => $oleh,
    );

    // echo'<pre>';print_r($data);exit();

    $this->db->where('id_emr', $idemr);
    $this->db->update('keperawatan.tb_keperawatan', $data);
  }

  public function datatables()
  {
    $result = $this->MedisModel->historyPengkajian();

    $data = array();
    foreach ($result as $row) {
      // $status_edit_perawat = $row -> STATUS_EDIT_PERAWAT;
      // $status_edit_medis = $row -> STATUS_EDIT_MEDIS;
      $action = "";
      $verif = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
      if ($row->ID_EMR_PERAWAT != null) {
        $action .= '<a class="btn btn-success btn-block btn-sm" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> View Keperawatan</a>';
        if ($this->session->userdata('status') == 2) {
          $action .= '<button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="' . $row->ID_EMR_PERAWAT . '"><i class="fa fa-eye"></i> lihat</button>';

          if ($row->STATUS_VERIFIKASI == 0) {
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-clock" aria-hidden="true"></i></h4>';
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }
        }
      }

      if ($row->ID_EMR_MEDIS != null) {
        $action .= '<a class="btn btn-purple btn-block btn-sm" data-id="' . $row->ID_EMR_MEDIS . '"><i class="fa fa-eye"></i> View Medis</a>';
        if ($this->session->userdata('status') == 1) {
          $action .= '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRIMedisDewasa" data-id="' . $row->NOPEN . '"><i class="fa fa-eye"></i> lihat</button>';
          if ($row->STATUS_VERIFIKASI == 0) {
            $verif = '<a class="btn btn-custom btn-block btn-sm verif" data-nokun="' . $row->NOKUN . '" data-nopen="' . $row->NOPEN . '" data-norm="' . $row->NORM . '">Verif</i></a>';
          } elseif ($row->STATUS_VERIFIKASI == 1) {
            $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }
        }
      }

      $sub_array = array();
      $sub_array[] = $row->INFO;
      $sub_array[] = $verif;
      $sub_array[] = $row->RUANGAN;
      $sub_array[] = $row->TANGGAL_KUNJUNGAN;
      $sub_array[] = $row->DPJP;
      $sub_array[] = $action;
      // if($STATUS_EDIT == 0){
      // $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
      // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
      // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> lihat</button>';
      // }else{
      //   $sub_array[] = '<a class="btn btn-success btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</a>
      // <a class="btn btn-purple btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-eye"></i> View Medis</a>
      // <button type="button" class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasa" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> lihat</button>';
      // }
      $sub_array[] = $row->USER_PERAWAT;
      $sub_array[] = $row->USER_MEDIS;

      $data[] = $sub_array;
    }

    $output = array(
      "draw" => intval($this->input->post("draw")),
      "data"              => $data
    );
    echo json_encode($output);
  }
}

/* End of file PengkajianTerapiSistemikRJ.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkjianRI/PengkajianTerapiSistemikRJ.php */