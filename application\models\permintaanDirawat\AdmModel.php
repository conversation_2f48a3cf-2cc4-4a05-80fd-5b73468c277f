<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AdmModel extends CI_Model {

  public function listPermintaanRawatNoFinal()
  {
    $query = $this->db->query(
      "SELECT pr.id ID_PERMINTAAN, p.NORM, master.getNama<PERSON>engkap(p.NORM) PASIEN
      , r.DESKRIPSI RUANGAN_ASAL, var.variabel TUJUAN
      , rt.DESKRIPSI RUANGAN_TUJUAN, ren.variabel RENCANA
      , master.getNamaLengkapPegawai(peng.NIP) USER
      , pr.tanggal TANGGAL

      FROM medis.tb_permintaan_rawat pr

      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = pr.kunjungan
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
      LEFT JOIN master.ruangan rt ON rt.ID = pr.ruangan
      LEFT JOIN db_master.variabel var ON var.id_variabel = pr.rencana_perawatan
      LEFT JOIN db_master.variabel ren ON ren.id_variabel = pr.rencana
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = pr.oleh

      WHERE pr.`status`=1

      ORDER BY pr.created_at"
    );
    return $query->result_array();
  }

  public function countListPermintaanRawatNoFinal()
  {
    $query = $this->db->select("SELECT pr.id ID_PERMINTAAN, p.NORM, master.getNamaLengkap(p.NORM) PASIEN
    , r.DESKRIPSI RUANGAN_ASAL, var.variabel TUJUAN
    , rt.DESKRIPSI RUANGAN_TUJUAN, ren.variabel RENCANA
    , master.getNamaLengkapPegawai(peng.NIP) USER
    , pr.tanggal TANGGAL");
    $this->db->from('medis.tb_permintaan_rawat pr');
    $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = pr.kunjungan','LEFT');
    $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
    $this->db->join('master.ruangan r','r.ID = pk.RUANGAN','LEFT');
    $this->db->join('master.ruangan rt','rt.ID = pr.ruangan','LEFT');
    $this->db->join('db_master.variabel var','var.id_variabel = pr.rencana_perawatan','LEFT');
    $this->db->join('db_master.variabel ren','ren.id_variabel = pr.rencana','LEFT');
    $this->db->join('aplikasi.pengguna peng','peng.ID = pr.oleh','LEFT');
    $this->db->where('pr.`status`',1);
  }

  function get_count(){
    $this->countListPermintaanRawatNoFinal();
    return $this->db->count_all_results();
  }

  public function listPermintaanRawatFinal()
  {
    $query = $this->db->query(
      "SELECT pr.id ID_PERMINTAAN, p.NORM, master.getNamaLengkap(p.NORM) PASIEN
      , r.DESKRIPSI RUANGAN_ASAL, var.variabel TUJUAN
      , rt.DESKRIPSI RUANGAN_TUJUAN, ren.variabel RENCANA
      , master.getNamaLengkapPegawai(peng.NIP) USER
      , pr.tanggal TANGGAL, pr.tgl_final TANGGAL_FINAL

      FROM medis.tb_permintaan_rawat pr

      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = pr.kunjungan
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
      LEFT JOIN master.ruangan rt ON rt.ID = pr.ruangan
      LEFT JOIN db_master.variabel var ON var.id_variabel = pr.rencana_perawatan
      LEFT JOIN db_master.variabel ren ON ren.id_variabel = pr.rencana
      LEFT JOIN aplikasi.pengguna peng ON peng.ID = pr.oleh

      WHERE pr.`status`=2 AND pr.tanggal >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)

      ORDER BY pr.tgl_final DESC"
    );
    return $query->result_array();
  }


}

/* End of file ProfileModel.php */
/* Location: ./application/models/ProfileModel.php */
