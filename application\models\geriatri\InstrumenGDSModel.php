<?php
defined('BASEPATH') or exit('No direct script access allowed');

class InstrumenGDSModel extends MY_Model
{
  protected $_table_name = 'db_layanan.tb_geriatri_igds';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'kunjungan' => array(
      'field' => 'kunjungan',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s <PERSON>ajib <PERSON>.',
        'numeric' => '%s <PERSON>ajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  // Simpan Instrumen Geriatric Depression Scale
  public function simpan($data)
  {
    $this->db->insert('db_layanan.tb_geriatri_igds', $data);
  }

  // History Instrumen Geriatric Depression Scale
  public function history($nokun, $id, $param)
  {
    if (isset($id)) {
      // Ambil detail
      $this->db->select(
        'i.id, i.kunjungan, i.igds1, i.igds2, i.igds3, i.igds4, i.igds5, i.igds6, i.igds7, i.igds8, i.igds9, i.igds10,
        i.igds11, i.igds12, i.igds13, i.igds14, i.igds15, i.tanggal, i.score'
      );
    } elseif (isset($param)) {
      if ($param == 'jumlah') {
        // Ambil jumlah
        $this->db->select('i.id');
      } elseif ($param == 'tabel') {
        // Ambil tabel
        $this->db->select(
          'i.id, i.kunjungan, i.tanggal, i.score, i.status, i.tgl_update, master.getNamaLengkapPegawai(p.NIP) pengisi'
        );
      }
    }
    $this->db->from('db_layanan.tb_geriatri_igds i');
    if (isset($id)) {
      // Ambil detail
      $this->db->where('i.id', $id);
      $query = $this->db->get();
      return $query->row_array();
    } elseif (isset($param)) {
      $this->db->join('aplikasi.pengguna p', 'p.ID = i.oleh', 'left');
      $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = i.kunjungan', 'left');
      $this->db->where('i.kunjungan', $nokun);
      $this->db->order_by('i.tgl_update', 'DESC');
      $query = $this->db->get();
      if ($param == 'jumlah') {
        // Ambil jumlah
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        // Ambil tabel
        return $query;
      } else {
        return null;
      }
    }
  }

  // Ubah Instrumen Geriatric Depression Scale
  public function ubah($data, $id)
  {
    $this->db->where('i.id', $id);
    $this->db->update('db_layanan.tb_geriatri_igds i', $data);
  }
}

/* End of file InstrumenGDSModel.php */
/* Location: ./application/models/geriatri/InstrumenGDSModel.php */