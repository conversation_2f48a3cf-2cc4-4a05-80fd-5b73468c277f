<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PatologiAnatomi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses')) && !in_array(31, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'penunjang/PatologiAnatomiModel'
      )
    );
  }

  public function index()
  {
    $nomr = $this->uri->segment(2);
    $nokun = $this->uri->segment(4);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'nomr' => $nomr,
      'nokun' => $nokun,
      'getNomr' => $pasien,
      'listDr' => $this->masterModel->listDr(),
      'caraDapatJaringan' => $this->masterModel->referensi(268),
      'cairanFiksasi' => $this->masterModel->referensi(269),
      'sito' => $this->pengkajianAwalModel->sitologi($nomr),
      'histo' => $this->pengkajianAwalModel->histologi($nomr),
      'imuno' => $this->pengkajianAwalModel->imuno($nomr),
      'patmol' => $this->pengkajianAwalModel->pemeriksaan_pt_gabung($nomr),
      'historyLabPA' => $this->PatologiAnatomiModel->tampilHistoryLabPA($nomr),
      'hPengkajianRater' => $this->pengkajianAwalModel->historyPengkajianRater(),
      'caraPengambilanSito' => $this->masterModel->referensi(271),
      'cairanFiksasiSito' => $this->masterModel->referensi(272),
      'statusMenstruasi' => $this->masterModel->referensi(273),
      'siklusHaid' => $this->masterModel->referensi(274),
      'kontrasepsi' => $this->masterModel->referensi(275),
      'statusGinekologi' => $this->masterModel->referensi(276),
      'sitologiNonGinekolog' => $this->masterModel->referensi(278),
      'listDrTujuan' => $this->masterModel->listDrUmum(null, null, array(30)),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/patologiAnatomi/index', $data);
  }

  public function laporan()
  {
    $nomr = $this->uri->segment(3);
    $data = array(
      'nomr' => $nomr,
      'sito' => $this->pengkajianAwalModel->sitologi($nomr),
      'histo' => $this->pengkajianAwalModel->histologi($nomr),
      'imuno' => $this->pengkajianAwalModel->imuno($nomr),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/patologiAnatomi/index', $data);
  }

  public function order()
  {
    $data = array(
      'title' => 'History Order PA per Pasien',
      'isi' => 'Pengkajian/patologiAnatomi/order/index',
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function tabelOrder()
  {
    $nomr = $this->input->post('nomr');
    $status = $this->input->post('status') != '' ? $this->input->post('status') : null;
    $jenis = $this->input->post('jenis') != '' ? $this->input->post('jenis') : null;
    $history = $this->PatologiAnatomiModel->ambilTabel($nomr, $status, $jenis);
    $data = array();
    $no = $_POST['start'];
    $warnaTeks = null;
    $href = null;
    $class = null;
    $disabled = null;
    $linkCetak = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history as $h) {
      // Mulai periksa status
      if ($h->STATUS == 0) {
        $warnaTeks = 'text-danger';
      } elseif ($h->STATUS == 1) {
        $warnaTeks = 'text-warning';
      } elseif ($h->STATUS == 2) {
        $warnaTeks = 'text-success';
      }
      // Akhir periksa status

      // Mulai periksa class
      if ($jenis != null) {
        $href = '#modal-detail-history-order-pa'; // Menu order lab PA
        $class = 'tbl-detail-history-order-pa';
      } else {
        $href = '#modal-detail-history-lab-pa'; // Menu SIMRSKD
        $class = 'tbl-detail-history-lab-pa';
      }
      // Akhir periksa class

      // Mulai periksa jenis
      if ($h->JENIS == 1) {
        $disabled = null;
        $linkCetak = "<a href='/reports/simrskd/penunjang/histopatologi.php?format=pdf&ID_ORDER_SIMRSKD=" . $h->ID_ORDER_SIMRSKD . "' class='btn btn-warning btn-block btn-sm waves-effect' target='_blank'>
                        <i class='fa fa-print'></i> Cetak
                      </a>";
      } elseif ($h->JENIS == 2) {
        $disabled = null;
        $linkCetak = "<a href='/reports/simrskd/penunjang/sitologi.php?format=pdf&ID_ORDER_SIMRSKD=" . $h->ID_ORDER_SIMRSKD . "' class='btn btn-warning btn-block btn-sm waves-effect' target='_blank'>
                        <i class='fa fa-print'></i> Cetak
                      </a>";
      } elseif ($h->JENIS == 3) {
        $disabled = null;
        $linkCetak = "<a href='/reports/simrskd/penunjang/konsultasi.php?format=pdf&ID_ORDER_SIMRSKD=" . $h->ID_ORDER_SIMRSKD . "' class='btn btn-warning btn-block btn-sm waves-effect' target='_blank'>
                        <i class='fa fa-print'></i> Cetak
                      </a>";
      } else {
        $disabled = 'disabled';
        $linkCetak = null;
      }
      // Akhir periksa jenis

      // Mulai data
      $row = array();
      $row[] = ++$no . '.';
      $row[] = $h->NOMOR_ORDER;
      $row[] = date('d/m/Y', strtotime($h->TANGGAL_ORDER));
      $row[] = $h->RUANG_AWAL;
      $row[] = $h->RUANG_TUJUAN;
      $row[] = "<p class='" . $warnaTeks . "'>" . $h->STATUS_ORDER . "</p>";
      $row[] = $h->JENIS_HASIL;
      $row[] = "<button type='button' href='" . $href . "' class='btn btn-sm btn-block btn-custom waves-effect " . $class . "' data-toggle='modal' data-id='" . $h->ID_ORDER_SIMRSKD . "-" . $h->JENIS . "' " . $disabled . ">
                  <i class='fa fa-check'></i> Lihat
                </button>" . $linkCetak;
      $data[] = $row;
      // Akhir data
    }

    $output = array(
      'draw' => $_POST['draw'],
      'recordsTotal' => $this->PatologiAnatomiModel->hitungSemua($nomr, $status, $jenis),
      'recordsFiltered' => $this->PatologiAnatomiModel->hitungTersaring($nomr, $status, $jenis),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function semuaOrder()
  {
    $data = array(
      'title' => 'History Order PA dari Semua Pasien',
      'isi' => 'Pengkajian/patologiAnatomi/semuaOrder/index',
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function tabelSemuaOrder()
  {
    $status = $this->input->post('status') != '' ? $this->input->post('status') : null;
    $jenis = 'semua pasien';
    $history = $this->PatologiAnatomiModel->ambilTabel(null, $status, $jenis);
    $data = array();
    $no = $_POST['start'];
    $warnaTeks = null;
    $disabled = null;
    $linkCetak = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history as $h) {
      // Mulai periksa status
      if ($h->STATUS == 0) {
        $warnaTeks = 'text-danger';
      } elseif ($h->STATUS == 1) {
        $warnaTeks = 'text-warning';
      } elseif ($h->STATUS == 2) {
        $warnaTeks = 'text-success';
      }
      // Akhir periksa status

      // Mulai periksa jenis
      if ($h->JENIS == 1) {
        $disabled = null;
        $linkCetak = "<a href='/reports/simrskd/penunjang/histopatologi.php?format=pdf&ID_ORDER_SIMRSKD=" . $h->ID_ORDER_SIMRSKD . "' class='btn btn-warning btn-block btn-sm waves-effect' target='_blank'>
                        <i class='fa fa-print'></i> Cetak
                      </a>";
      } elseif ($h->JENIS == 2) {
        $disabled = null;
        $linkCetak = "<a href='/reports/simrskd/penunjang/sitologi.php?format=pdf&ID_ORDER_SIMRSKD=" . $h->ID_ORDER_SIMRSKD . "' class='btn btn-warning btn-block btn-sm waves-effect' target='_blank'>
                        <i class='fa fa-print'></i> Cetak
                      </a>";
      } elseif ($h->JENIS == 3) {
        $disabled = null;
        $linkCetak = "<a href='/reports/simrskd/penunjang/konsultasi.php?format=pdf&ID_ORDER_SIMRSKD=" . $h->ID_ORDER_SIMRSKD . "' class='btn btn-warning btn-block btn-sm waves-effect' target='_blank'>
                        <i class='fa fa-print'></i> Cetak
                      </a>";
      } else {
        $disabled = 'disabled';
        $linkCetak = null;
      }
      // Akhir periksa jenis

      // Mulai data
      $row = array();
      $row[] = ++$no . '.';
      $row[] = $h->NORM;
      $row[] = $h->NAMA_PASIEN;
      $row[] = $h->NOMOR_ORDER;
      $row[] = date('d/m/Y', strtotime($h->TANGGAL_ORDER));
      $row[] = $h->RUANG_AWAL;
      $row[] = "<p class='" . $warnaTeks . "'>" . $h->STATUS_ORDER . "</p>";
      $row[] = $h->JENIS_HASIL;
      $row[] = $h->SUMBER_SEDIAAN;
      $row[] = "<button type='button' href='#modal-detail-history-semua-order-pa' class='btn btn-sm btn-block btn-custom waves-effect tbl-detail-history-semua-order-pa' data-toggle='modal' data-id='" . $h->ID_ORDER_SIMRSKD . "-" . $h->JENIS . "' " . $disabled . ">
                  <i class='fa fa-check'></i> Lihat
                </button>" . $linkCetak;
      $data[] = $row;
      // Akhir data
    }

    $output = array(
      'draw' => $_POST['draw'],
      'recordsTotal' => $this->PatologiAnatomiModel->hitungSemua(null, $status, $jenis),
      'recordsFiltered' => $this->PatologiAnatomiModel->hitungTersaring(null, $status, $jenis),
      'data' => $data
    );
    echo json_encode($output);
  }
}

// End of file PatologiAnatomi.php
// Location: ./application/controllers/rekam_medis/penunjang/PatologiAnatomi.php