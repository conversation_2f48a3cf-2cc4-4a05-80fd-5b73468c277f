<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianLuka extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','luka/PengkajianLukaModel','gigi/DiagnosaKesehatanModel'));
    }

    public function index() {

        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(5)),
            'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
            'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
            'skriningResikoJatuhPusing' => $this->masterModel->referensi(120),
            'skriningResikoJatuhBerdiri' => $this->masterModel->referensi(121),
            'skriningResikoJatuh6Bulan' => $this->masterModel->referensi(122),
            'skriningNyeri' => $this->masterModel->referensi(7),
            'skalaNyeriNRS' => $this->masterModel->referensi(114),
            'skalaNyeriWBR' => $this->masterModel->referensi(115),
            'skalaNyeriFLACC' => $this->masterModel->referensi(123),
            'skalaNyeriBPS' => $this->masterModel->referensi(133),
            'efeksampingNRS' => $this->masterModel->referensi(118),
            'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
            'pengkajianNyeriQuality' => $this->masterModel->referensi(9),
            'pengkajianNyeriTime' => $this->masterModel->referensi(12),
            'riwayat_alergi_luka' => $this->masterModel->referensi(531),
            'jenis_luka' => $this->masterModel->referensi(533),
            'jenis_luka_kanker' => $this->masterModel->referensi(534),
            'warna_dasar_luka' => $this->masterModel->referensi(535),
            'perdarahan' => $this->masterModel->referensi(536),
            'bau' => $this->masterModel->referensi(537),
            'gatal_pruritus' => $this->masterModel->referensi(538),
            'eksudat' => $this->masterModel->referensi(539),
            'grade' => $this->masterModel->referensi(540),
            'drain' => $this->masterModel->referensi(541),
            'jahitan' => $this->masterModel->referensi(542),
            'tanda_infeksi' => $this->masterModel->referensi(543),
            'ftsg' => $this->masterModel->referensi(544),
            'stsg' => $this->masterModel->referensi(545),
            'tubes_cateter' => $this->masterModel->referensi(546),
            'infeksi' => $this->masterModel->referensi(547),
            'rembes' => $this->masterModel->referensi(548),
            'dehiscence' => $this->masterModel->referensi(549),
            'stadium_luka_tekan' => $this->masterModel->referensi(550),
            'produksi' => $this->masterModel->referensi(551),
            'manajemen' => $this->masterModel->referensi(552),
            'keluaran' => $this->masterModel->referensi(553),
            'nefrostomy' => $this->masterModel->referensi(577),
            'psikososial' => $this->masterModel->referensi(602),
            'masalah_keperawatan' => $this->masterModel->referensi(610),
            'pencucian_luka' => $this->masterModel->referensi(554),
            'merah' => $this->masterModel->referensi(555),
            'kuning' => $this->masterModel->referensi(556),
            'hitam' => $this->masterModel->referensi(557),
            'pink' => $this->masterModel->referensi(558),
            'kebutuhan_pembelajaran' => $this->masterModel->referensi(559),
            'kontrol_luka' => $this->masterModel->referensi(560),
            'status_pasien' => $this->masterModel->referensi(1726),
            'konsultasi_dpjp' => $this->masterModel->referensi(1727),
        );

        $this->load->view('Pengkajian/luka/pengkajianLuka',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->PengkajianLukaModel->rules;
                $this->form_validation->set_rules($rules);
                if($this->input->post('ruangan') == 1){
                    $this->form_validation->set_rules($this->PengkajianLukaModel->rules_rawat_jalan);
                }elseif($this->input->post('ruangan') == 2){
                    $this->form_validation->set_rules($this->PengkajianLukaModel->rules_rawat_inap);
                }

                if($this->input->post('riwayat_alergi_luka') == 1825){
                    $this->form_validation->set_rules($this->PengkajianLukaModel->rules_riwayat_alergi);
                }

                if($this->input->post('skrining_nyeri') != 17){
                    $this->form_validation->set_rules($this->PengkajianLukaModel->rules_nyeri);
                    if($this->input->post('efek_samping') != 337){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_efek_samping);
                    }
                    if($this->input->post('quality') == 26){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_quality);
                    }
                    if($this->input->post('time') == 31){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_time);
                    }
                }
                if(!empty($this->input->post('jenis_luka'))){
                    if(in_array('1830',$this->input->post('jenis_luka'))){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_luka_radiasi);                    
                    }

                    if(in_array('1831',$this->input->post('jenis_luka'))){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_luka_ekstravasasi);                    
                    }

                    if(in_array('1832',$this->input->post('jenis_luka'))){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_luka_operasi);
                        if($this->input->post('tubes_cateter') == 1868){
                            $this->form_validation->set_rules($this->PengkajianLukaModel->rules_nefrostomy);                    
                        }
                        if($this->input->post('dehiscence') == 1877){
                            $this->form_validation->set_rules($this->PengkajianLukaModel->rules_dehiscence);                    
                        }                    
                    }

                    if(in_array('1833',$this->input->post('jenis_luka'))){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_luka_tekan);                  
                    }
                    
                    if(in_array('1834',$this->input->post('jenis_luka'))){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_luka_fistula);
                        if($this->input->post('keluaran') == 1906){
                            $this->form_validation->set_rules($this->PengkajianLukaModel->rules_keluaran);                    
                        }                    
                    }

                    if(in_array('1835',$this->input->post('jenis_luka'))){
                        $this->form_validation->set_rules($this->PengkajianLukaModel->rules_luka_lain);                  
                    }
                }

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataPengkajianLuka = array(
                        'kunjungan' => $post['nokun'],
                        'rawat' => $post['ruangan'],
                        'ruangan' => $post['ruangan'] == 1 ? $post['luka_jalan_ruangan'] : $post['luka_inap_ruangan'],
                        'status_pasien' => $post['status_pasien'],
                        'konsultasi_dpjp' => $post['konsultasi_dpjp'],
                        'diagnosis' => $post['diagnosis'],
                        'riwayat_alergi' => $post['riwayat_alergi_luka'],
                        'sebutkan_riwayat_alergi' => $post['riwayat_alergi_luka'] == 1825 ? $post['alergi_luka_desc'] : null,
                        'keluhan_utama' => $post['keluhan_utama'],
                        'tekanan_darah' => $post['sistolik'],
                        'per_tekanan_darah' => $post['distolik'],
                        'pernapasan' => $post['pernapasan'],
                        'nadi' => $post['nadi'],
                        'suhu' => $post['suhu'],
                        'metode' => $post['skrining_nyeri'],
                        'skor' => $post['skrining_nyeri'] != 17 ? $post['skor_nyeri'] : "",
                        'farmakologi' => $post['skrining_nyeri'] != 17 ? $post['farmakologi'] : null,
                        'non_farmakologi' => $post['skrining_nyeri'] != 17 ? $post['non_farmakologi'] : null,
                        'efek_samping' => $post['skrining_nyeri'] != 17 ? $post['efek_samping'] : "",
                        'keterangan_efek_samping' => $post['skrining_nyeri'] != 17 && $post['efek_samping'] == 338 ? $post['efek_samping_lain'] : "",
                        'provocative' => $post['skrining_nyeri'] != 17 ? $post['provocative'] : "",
                        'quality' => $post['skrining_nyeri'] != 17 ? $post['quality'] : "",
                        'quality_lainnya' => $post['skrining_nyeri'] != 17 ? $post['quality_lainnya'] : null,
                        'regio' => $post['skrining_nyeri'] != 17 ? $post['regio'] : null,
                        'severity' => $post['skrining_nyeri'] != 17 ? $post['severity'] : null,
                        'time' => $post['skrining_nyeri'] != 17 ? $post['time'] : "",
                        'durasi_nyeri' => $post['skrining_nyeri'] != 17 ? $post['durasi_nyeri'] : null,
                        'pusing' => $post['skrining_resiko_jatuh_pusing_luka'],
                        'berdiri' => $post['skrining_resiko_jatuh_berdiri_luka'],
                        'jatuh' => $post['skrining_resiko_jatuh_6bulan_luka'],
                        'jenis_luka' => json_encode($post['jenis_luka']),
                        'status_psikososial' => $post['psikososial'],
                        'status_psikososial_lainnya' => isset($post['psikososial_lain']) ? $post['psikososial_lain'] : null,
                        'masalah_keperawatan' => json_encode($post['masalah_keperawatan_luka']),
                        'masalah_keperawatan_lainnya' => isset($post['masalah_keperawatan_lain']) ? $post['masalah_keperawatan_lain'] : null,
                        'pencucian_luka' => json_encode($post['pencucian_luka']),
                        'frekuensi' => $post['frekuensi'],
                        'kebutuhan_pembelajaran' => json_encode($post['kebutuhan_pembelajaran']),
                        'kebutuhan_pembelajaran_lainnya' => isset($post['kebutuhan_pembelajaran_lainnya']) ? $post['kebutuhan_pembelajaran_lainnya'] : null,
                        'kontrol_poli_luka' => $post['kontrol_luka'],
                        'tanggal_kontrol' => $post['tanggal_kontrol'],
                        'lokasi_luka' => file_get_contents($this->input->post('lukaVal')),
                        'oleh' => $this->session->userdata("id"),
                    );

                    $this->db->where(array('kunjungan' => $post['nokun']));
                    $this->db->delete('keperawatan.tb_jenis_luka');

                    // Luka Kanker
                    if(in_array('1829',$post['jenis_luka']) && $post['nomor'] != 0){
                        $dataLukaKanker =array();
                        for($i=1; $i<=$post['nomor']; $i++){
                            // if(isset($post["elemen_gigi_$i"]) && isset($post["skala_gigi_$i"])){
                                // if($post["elemen_gigi_$i"] != "" && $post["skala_gigi_$i"]){
                                    array_push($dataLukaKanker, array(
                                        'kunjungan' => $post["nokun"],
                                        'jenis_luka' => '1829',
                                        'jenis_luka_kanker' => isset($post["jenis_luka_kanker_$i"]) ? $post["jenis_luka_kanker_$i"] : null,
                                        'lokasi' => isset($post["lokasi_$i"]) ? $post["lokasi_$i"] : null,
                                        'panjang' => isset($post["panjang_$i"]) ? $post["panjang_$i"] : null,
                                        'lebar' => isset($post["lebar_$i"]) ? $post["lebar_$i"] : null,
                                        'tinggi' => isset($post["tinggi_$i"]) ? $post["tinggi_$i"] : null,
                                        'warna_dasar_luka' => isset($post["warna_dasar_luka_$i"]) ? json_encode($post["warna_dasar_luka_$i"]) : null,
                                        'jenis_balutan' => isset($post["balutan_kanker_$i"]) ? json_encode($post["balutan_kanker_$i"]) : null,
                                        'perdarahan' => isset($post["pendarahan_kanker_$i"]) ? $post["pendarahan_kanker_$i"] : null,
                                        'bau' => isset($post["bau_kanker_$i"]) ? $post["bau_kanker_$i"] : null,
                                        'gatal_pruritus' => isset($post["gatal_pruritus_kanker_$i"]) ? $post["gatal_pruritus_kanker_$i"] : null,
                                        'eksudat' => isset($post["eksudat_kanker_$i"]) ? $post["eksudat_kanker_$i"] : null,
                                    ));
                                // }
                            // }
                        }
                        $this->db->insert_batch('keperawatan.tb_jenis_luka', $dataLukaKanker);
                    }

                    // Luka Radiasi
                    if(in_array('1830',$post['jenis_luka'])){
                        $dataLukaRadiasi = array(
                            'kunjungan' => $post["nokun"],
                            'jenis_luka' => '1830',
                            'dosis_radiasi' => $post["dosis_radiasi"],
                            'radiasi_ke' => $post["radiasi_ke"],
                            'lokasi' => $post["lokasi_radiasi"],
                            'panjang' => $post["panjang_radiasi"],
                            'lebar' => $post["lebar_radiasi"],
                            'warna_dasar_luka' => json_encode($post["warna_dasar_luka_radiasi"]),
                            'jenis_balutan' => json_encode($post["balutan_radiasi"]),
                            'perdarahan' => $post["pendarahan_radiasi"],
                            'grade' => $post["grade_radiasi"],
                        );
                        $this->db->insert('keperawatan.tb_jenis_luka', $dataLukaRadiasi);
                    }

                    // Luka Ekstravasasi
                    if(in_array('1831',$post['jenis_luka'])){
                        $dataLukaEkstravasasi = array(
                            'kunjungan' => $post["nokun"],
                            'jenis_luka' => '1831',
                            'lokasi' => $post["lokasi_ekstravasasi"],
                            'tanggal_kemoterapi_terakhir' => $post["tanggal_kemoterapi"],
                            'nama_obat' => $post["obat_ekstravasasi"],
                            'panjang' => $post["panjang_ekstravasasi"],
                            'lebar' => $post["lebar_ekstravasasi"],
                            'warna_dasar_luka' => json_encode($post["warna_dasar_luka_ekstravasasi"]),
                            'jenis_balutan' => json_encode($post["balutan_ekstravasasi"]),
                        );
                        $this->db->insert('keperawatan.tb_jenis_luka', $dataLukaEkstravasasi);
                    }

                    // Luka Operasi
                    if(in_array('1832',$post['jenis_luka'])){
                        $dataLukaOperasi = array(
                            'kunjungan' => $post["nokun"],
                            'jenis_luka' => '1832',
                            'lokasi' => $post["lokasi_operasi"],
                            'tanggal_operasi' => $post["tanggal_operasi"],
                            'jenis_operasi' => $post["jenis_operasi"],
                            'drain' => $post["drain"],
                            'jahitan' => json_encode($post["jahitan"]),
                            'tanda_tanda_infeksi' => $post["tanda_infeksi"],
                            'ftsg' => $post["ftsg"],
                            'stsg' => $post["stsg"],
                            'tubes_cateter' => $post["tubes_cateter"],
                            'nefrostomy' => $post["tubes_cateter"] == 1868 ? json_encode($post['nefrostomy']) : null,
                            'infeksi' => $post["infeksi"],
                            'rembes' => $post["rembes"],
                            'dehiscence' => $post["dehiscence"],
                            'panjang' => $post["panjang_operasi"],
                            'lebar' => $post["lebar_operasi"],
                            'kedalaman' => $post["kedalaman_operasi"],
                            'warna_dasar_luka' => $post["dehiscence"] == 1877 ? json_encode($post["warna_dasar_luka_operasi"]): null,
                            'jenis_balutan' => $post["dehiscence"] == 1877 ? json_encode($post["balutan_operasi"]): null,
                        );
                        $this->db->insert('keperawatan.tb_jenis_luka', $dataLukaOperasi);
                    }

                    // Luka Takan
                    if(in_array('1833',$post['jenis_luka'])){
                        $dataLukaTekan = array(
                            'kunjungan' => $post["nokun"],
                            'jenis_luka' => '1833',
                            'lokasi' => $post["lokasi_tekan"],
                            'panjang' => $post["panjang_tekan"],
                            'lebar' => $post["lebar_tekan"],
                            'kedalaman' => $post["kedalaman_tekan"],
                            'warna_dasar_luka' => json_encode($post["warna_dasar_luka_tekan"]),
                            'jenis_balutan' => json_encode($post["balutan_tekan"]),
                            'stadium_luka_tekan' => json_encode($post['stadium_luka_tekan']),
                        );
                        $this->db->insert('keperawatan.tb_jenis_luka', $dataLukaTekan);
                    }

                    // Luka Fistula
                    if(in_array('1834',$post['jenis_luka'])){
                        $dataLukaFistula = array(
                            'kunjungan' => $post["nokun"],
                            'jenis_luka' => '1834',
                            'lokasi' => $post["lokasi_fistula"],
                            'produksi_24_jam' => $post["produksi"],
                            'keluaran' => $post["keluaran"],
                            'keluaran_lainnya' => isset($post['keluaran_lainnya']) ? $post['keluaran_lainnya'] : null,
                            'manajemen' => $post["manajemen"],
                        );
                        $this->db->insert('keperawatan.tb_jenis_luka', $dataLukaFistula);
                    }

                    // Luka Lain
                    if(in_array('1835',$post['jenis_luka'])){
                        $dataLukaLain = array(
                            'kunjungan' => $post["nokun"],
                            'jenis_luka' => '1835',
                            'jenis_luka_lain' => $post["jenis_luka_lain"],
                            'lokasi' => $post["lokasi_lain"],
                            'panjang' => $post["panjang_lain"],
                            'lebar' => $post["lebar_lain"],
                            'kedalaman' => $post["kedalaman_lain"],
                            'warna_dasar_luka' => json_encode($post["warna_dasar_luka_lain"]),
                            'jenis_balutan' => json_encode($post["balutan_lain"]),
                            'infeksi' => isset($post['infeksi_luka_lain']) ? $post['infeksi_luka_lain'] : null,
                        );
                        $this->db->insert('keperawatan.tb_jenis_luka', $dataLukaLain);
                    }

                    $this->db->replace('keperawatan.tb_pengkajian_luka',$dataPengkajianLuka);

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataPengkajianLuka = $this->PengkajianLukaModel->get($post['nokun'], true);
                $data = array();
    			if(!empty($dataPengkajianLuka)){
                    $data['kunjungan'] = $dataPengkajianLuka->kunjungan;
                    $data['rawat'] = $dataPengkajianLuka->rawat;
                    $data['ruangan'] = $dataPengkajianLuka->ruangan;
                    $data['tanggal'] = $dataPengkajianLuka->tanggal;
                    $data['status_pasien'] = $dataPengkajianLuka->status_pasien;
                    $data['konsultasi_dpjp'] = $dataPengkajianLuka->konsultasi_dpjp;
                    $data['diagnosis'] = $dataPengkajianLuka->diagnosis;
                    $data['riwayat_alergi'] = $dataPengkajianLuka->riwayat_alergi;
                    $data['sebutkan_riwayat_alergi'] = $dataPengkajianLuka->sebutkan_riwayat_alergi;
                    $data['keluhan_utama'] = $dataPengkajianLuka->keluhan_utama;
                    $data['tekanan_darah'] = $dataPengkajianLuka->tekanan_darah;
                    $data['per_tekanan_darah'] = $dataPengkajianLuka->per_tekanan_darah;
                    $data['nadi'] = $dataPengkajianLuka->nadi;
                    $data['pernapasan'] = $dataPengkajianLuka->pernapasan;
                    $data['suhu'] = $dataPengkajianLuka->suhu;
                    $data['metode'] = $dataPengkajianLuka->metode;
                    $data['skor'] = $dataPengkajianLuka->skor;
                    $data['farmakologi'] = $dataPengkajianLuka->farmakologi;
                    $data['non_farmakologi'] = $dataPengkajianLuka->non_farmakologi;
                    $data['efek_samping'] = $dataPengkajianLuka->efek_samping;
                    $data['keterangan_efek_samping'] = $dataPengkajianLuka->keterangan_efek_samping;
                    $data['provocative'] = $dataPengkajianLuka->provocative;
                    $data['quality'] = $dataPengkajianLuka->quality;
                    $data['quality_lainnya'] = $dataPengkajianLuka->quality_lainnya;
                    $data['regio'] = $dataPengkajianLuka->regio;
                    $data['severity'] = $dataPengkajianLuka->severity;
                    $data['time'] = $dataPengkajianLuka->time;
                    $data['durasi_nyeri'] = $dataPengkajianLuka->durasi_nyeri;
                    $data['pusing'] = $dataPengkajianLuka->pusing;
                    $data['berdiri'] = $dataPengkajianLuka->berdiri;
                    $data['jatuh'] = $dataPengkajianLuka->jatuh;
                    $data['lokasi_luka'] = base64_encode($dataPengkajianLuka->lokasi_luka);
                    $data['jenis_luka'] = $dataPengkajianLuka->jenis_luka;
                    $data['status_psikososial'] = $dataPengkajianLuka->status_psikososial;
                    $data['status_psikososial_lainnya'] = $dataPengkajianLuka->status_psikososial_lainnya;
                    $data['masalah_keperawatan'] = $dataPengkajianLuka->masalah_keperawatan;
                    $data['masalah_keperawatan_lainnya'] = $dataPengkajianLuka->masalah_keperawatan_lainnya;
                    $data['pencucian_luka'] = $dataPengkajianLuka->pencucian_luka;
                    $data['frekuensi'] = $dataPengkajianLuka->frekuensi;
                    $data['kebutuhan_pembelajaran'] = $dataPengkajianLuka->kebutuhan_pembelajaran;
                    $data['kebutuhan_pembelajaran_lainnya'] = $dataPengkajianLuka->kebutuhan_pembelajaran_lainnya;
                    $data['kontrol_poli_luka'] = $dataPengkajianLuka->kontrol_poli_luka;
                    $data['tanggal_kontrol'] = $dataPengkajianLuka->tanggal_kontrol;
                    $data['oleh'] = $dataPengkajianLuka->oleh;

    			}
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $data
                ));
            }else if($param == 'ambilJenisLuka'){
                $post = $this->input->post(NULL,TRUE);
    			$method = 'row';
                if(isset($post['method'])){
                    $method = 'result';
                }
                
                $this->db->where(array('kunjungan' => $post['nokun'], 'jenis_luka' => $post['jenis']));
                $dataJenisLuka = $this->db->get('keperawatan.tb_jenis_luka')->$method();
    			echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataJenisLuka
                ));
            }else if($param == 'count'){
                $result = $this->PengkajianLukaModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->PengkajianLukaModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_pengkajian_luka" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PengkajianLukaModel->total_count(),
            "recordsFiltered"   => $this->PengkajianLukaModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}