<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FKRPModel extends CI_Model {
    // IGD FKRP
    public function get_fkrp($id_fkrp)
    {
        $query = $this->db->query('SELECT sv.id_fkrp
            , sv.nokun NOKUN, sv.fkrp_tanggal TANGGAL
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , rk.DESKRIPSI RUANGAN_KUNJUNGAN
            , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , sv.`*`

            FROM medis.tb_igd_fkrp sv
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = sv.nokun
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
            LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
            LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = sv.oleh
            WHERE sv.id_fkrp="' . $id_fkrp . '"');
        return $query->row_array();
    }

    public function history_fkrp($nomr)
    {
        $query = $this->db->query(
            "SELECT kp.id_fkrp, kp.nokun NOKUN, pk.NOPEN NOPEN, kp.fkrp_tanggal TANGGAL_FKRP, kp.fkrp_waktu JAM_FKRP
            , master.getNamaLengkapPegawai(peng.NIP) USER
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , rk.DESKRIPSI RUANGAN_KUNJUNGAN
            , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN

            FROM medis.tb_igd_fkrp kp

            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
            LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
            LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
            LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.oleh

            WHERE kp.status=1 AND p.NORM=" . $nomr . "

            ORDER BY kp.created_at DESC"
        );
        return $query->result_array();
    }

    public function historyFKRPRI($id_fkrp) {
        $query = $this->db->query(
            "SELECT f.id, f.created_at, f.tanggal, f.jam, r.DESKRIPSI ruangan FROM medis.tb_igd_fkrpri f
            LEFT JOIN master.ruangan r ON f.ruangan = r.ID AND r.JENIS = 5
            WHERE f.id_fkrp = '$id_fkrp'"
          );
          return $query->result_array();
    }

    public function detailFKRPRI($id) {
        $query = $this->db->query(
            "SELECT r.DESKRIPSI nama_ruangan, f.`*` FROM medis.tb_igd_fkrpri f
            LEFT JOIN master.ruangan r ON f.ruangan = r.ID AND r.JENIS = 5
            WHERE f.id = '$id'"
        );

        return $query->row_array();
    }

    public function getTekananDarah($nopen) {
        $query = $this->db->query(
            "SELECT tv.nomr, tv.nokun, pk.NOPEN, tv.td_sistolik, tv.td_diastolik
            , tv.nadi, tv.pernapasan, tv.suhu, tv.created_at
            FROM db_pasien.tb_tanda_vital tv
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tv.nokun
            WHERE pk.NOPEN='$nopen' AND tv.status=1
            ORDER BY tv.created_at DESC
            LIMIT 1"
        );

        return $query->row_array();
    }
}