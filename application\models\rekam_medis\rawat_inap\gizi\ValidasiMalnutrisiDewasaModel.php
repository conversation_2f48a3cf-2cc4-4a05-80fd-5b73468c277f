<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ValidasiMalnutrisiDewasaModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';

    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('kp.nopen NOPEN, kp.tanggal TANGGAL, kp.jenis <PERSON>
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('medis.tb_validasi_malnutrisi kp');
        // $this->db->join('pendaftaran.kunjungan pk','pk.NOPEN = kp.nopen','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = kp.nopen','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('master.ruangan rk','rk.ID = tp.RUANGAN','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = kp.oleh','LEFT');

        $this->db->where('kp.STATUS !=','0');
        $this->db->where('p.NORM',$this->input->post('nomr'));
        $this->db->order_by('kp.tanggal', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    function cekPenilainStatusGizi(){
        $this->db->select('IF(pg.penilaian = 4080, 1,IF(pgl.id IS NOT NULL , 1, 0 )) STATUS');
        $this->db->from('medis.tb_penilaian_status_gizi pg');
        $this->db->join('pendaftaran.kunjungan pk','pg.nokun = pk.NOMOR','LEFT');
        $this->db->join('medis.tb_pengkajian_gizi_lanjutan pgl','pg.nokun = pgl.nokun','LEFT');
        $this->db->where('pk.NOPEN',$this->input->post('nopen'));
        // $this->db->order_by('kp.tanggal', 'DESC');
        return $this->db->get()->row();
    }

}
