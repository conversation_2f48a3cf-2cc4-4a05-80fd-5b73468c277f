<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengkajianGigiModel extends MY_Model{
	protected $_table_name = 'keperawatan.tb_pengkajian_awal_gigi';
	protected $_primary_key = 'kunjungan';
	protected $_order_by = 'kunjungan';
	protected $_order_by_type = 'DESC';

	public $rules = array(

        'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'auto_allo' => array(
            'field' => 'auto_allo',
            'label' => 'Auto/allo',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'keluhan_pasien' => array(
            'field' => 'keluhan_pasien',
            'label' => 'Ke<PERSON>han pasien',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        'nyeri_gigi' => array(
            'field' => 'nyeri_gigi',
            'label' => 'Skrining nyeri gigi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'perawatan_gigi' => array(
            'field' => 'perawatan_gigi',
            'label' => 'Perawatan gigi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'berapa_kali' => array(
            'field' => 'berapa_kali',
            'label' => 'Berapa kali menyikat gigi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'kapan_menyikat' => array(
            'field' => 'kapan_menyikat',
            'label' => 'Kapan waktu menyikat gigi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'gerakan_menyikat' => array(
            'field' => 'gerakan_menyikat',
            'label' => 'Gerikan menyikat gigi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'minum_teh_kopi' => array(
            'field' => 'minum_teh_kopi',
            'label' => 'Minum teh/kopi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'minuman_beralkohol' => array(
            'field' => 'minuman_beralkohol',
            'label' => 'Minum minuman beralkohol',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'mengunyah_rahang' => array(
            'field' => 'mengunyah_rahang',
            'label' => 'Mengunyah 1 sisi rahang',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'merokok' => array(
            'field' => 'merokok',
            'label' => 'Kebiasaan merokok',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'menggigit_pensil' => array(
            'field' => 'menggigit_pensil',
            'label' => 'Kebiasaan mengigit pensil',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'bruxism' => array(
            'field' => 'bruxism',
            'label' => 'Bruxism',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'diagnosa_kesehatan[]' => array(
            'field' => 'diagnosa_kesehatan[]',
            'label' => 'Diagnosa kesehatan gigi dan mulut',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'intruksi_medis' => array(
            'field' => 'intruksi_medis',
            'label' => 'Intruksi Medis',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                ),
        ),
	);

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('kp.kunjungan NOKUN, kp.tanggal TANGGAL_ODONTO
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('keperawatan.tb_pengkajian_awal_gigi kp');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = kp.kunjungan','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = kp.oleh','LEFT');

        $this->db->where('kp.STATUS !=','0');
        $this->db->where('p.NORM',$this->input->post('nomr'));
        $this->db->order_by('kp.TANGGAL', 'DESC');

        // if($this->input->post('id')){
        // 	$this->db->where('hph.ID', $this->input->post('id'));
        // }

        // if($this->input->post('status')){
        //     $this->db->where_in('hph.STATUS_LIS',$this->input->post('status'),FALSE);
        // }
        // else{
        //     $this->db->where('his.STATUS IS NULL');
        // }

        // if($this->input->post('search[value]')){
        //     $this->db->group_start();
        //     $this->db->like('hph.NORM', $this->input->post('search[value]'));
        //     $this->db->or_like('hph.NOMOR_LAB', $this->input->post('search[value]'));
        //     $this->db->group_end();
        //     // $this->db->where_in('his.STATUS',$this->input->post('status'));            
        // }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
