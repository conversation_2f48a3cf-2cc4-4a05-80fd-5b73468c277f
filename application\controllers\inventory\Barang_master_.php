<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Barang_master extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
  }

  if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
  }

  date_default_timezone_set("Asia/Bangkok");
  $this->load->model(array('inventory/Model_barang','inventory/Model_kategori','inventory/Model_satuan','inventory/Model_penyedia'));
}

public function index()
{
    $datakategori   =  $this->model_kategori->tampilkan_data()->result();           
    $datasatuan     =  $this->model_satuan->tampilkan_data()->result();           
    $datapenyedia   =  $this->model_penyedia->tampilkan_data()->result();

    $data = array(
        'title'       	=> 'Halaman Input Barang',
        'isi'         	=> 'inventory/barang/form_barang',
        'datakategori' 	=> $datakategori,
        'datasatuan' 	=> $datasatuan,
        'datapenyedia' 	=> $datapenyedia,
        'barang'        => $barang,
    );
    $this->load->view('layout/wrapper',$data);
}

function insert()
{
    if(isset($_POST['submit'])){
        $NAMA       =   $this->input->post('NAMA');
        $KATEGORI   =   $this->input->post('KATEGORI');
        $KODE_BMN   =   $this->input->post('KODE_BMN');
        $SATUAN    	=   $this->input->post('SATUAN');
        $PENYEDIA  	=   $this->input->post('PENYEDIA');
        $HARGA      =   $this->input->post('HARGA');
//echo "<pre>";print_r($_POST);exit();
        $sql = $this->db->query("SELECT NAMA FROM inventory.barang where NAMA='$NAMA'");
        $cek_nama = $sql->num_rows();
        if ($cek_nama > 0) {
            $this->session->set_flashdata('warning', 'Nama barang sudah ada...!');
            redirect('inventory/Barang');
        }else{
            $data           = 	array
            (   'BARANG'    =>$NAMA,
                'KODE_BMN'  =>$KODE_BMN,
                'SATUAN'    =>$SATUAN,
                'PENYEDIA'  =>$PENYEDIA,
                'HARGA'     =>$HARGA
            );
            $this->model_barang->post($data);
            redirect('inventory/Barang');
        }
    }
}

function get_autocomplete(){
    if (isset($_GET['term'])) {
        $result = $this->Model_barang->cari_barang($_GET['term']);
        if (count($result) > 0) {
            foreach ($result as $row)
                $arr_result[] = array(
                    'label'		 => $row->BARANG,
                    'idbarang'   => $row->ID_BARANG,
                );
            echo json_encode($arr_result);
        }
    }
}

public function update()
{
    $id = $this->input->post('ID');
    $data = array(
        'BARANG' => $this->input->post('NAMA'),
        'KODE_BMN' => $this->input->post('KODE_BMN'),
        'SATUAN' => $this->input->post('SATUAN'),
        'PENYEDIA' => $this->input->post('PENYEDIA'),
        'HARGA' => $this->input->post('HARGA'),
    );
    // echo "<pre>";print_r($data);exit();
    $this->Model_barang->update($id, $data);
    redirect('inventory/Barang');
}

}
