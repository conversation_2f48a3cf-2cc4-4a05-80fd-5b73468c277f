<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PersetujuanTindakanKedokteranModel extends CI_Model
{
  public function simpanInformedConsent($data)
  {
    $this->db->insert('db_informed_consent.tb_informed_consent', $data);
    return $this->db->insert_id();
  }

  public function simpanPTK($data)
  {
    $this->db->insert('db_informed_consent.tb_form_ptk', $data);
  }

  public function simpanPersetujuanTidakanKedokteran($data)
  {
    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran', $data);
  }

  public function historyInformedConsentPTK($nomr)
  {
    $this->db->select(
      'pp.NORM, tic.id, tic.nokun, master.getNamaLengkapPegawai(ap.NIP) oleh, ptk.status,
      master.getNamaLengkapPegawai(md.NIP) dokter_pela<PERSON><PERSON>, tic.created_at tanggal, tic.jenis_informed_consent'
    );
    $this->db->from('db_informed_consent.tb_informed_consent tic');
    $this->db->join('db_informed_consent.tb_form_ptk ptk', 'ptk.id_informed_consent = tic.id', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = tic.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = tic.oleh', 'left');
    $this->db->join('master.dokter md', 'md.ID = tic.dokter_pelaksana', 'left');
    $this->db->where('pp.NORM', $nomr);
    $this->db->where('tic.jenis_informed_consent', 3026);
    $this->db->order_by('tic.created_at', 'desc');

    $query = $this->db->get();
    return $query;
  }

  public function detailPTK($id)
  {
    $this->db->select(
      'tic.id id_tic, tic.nokun, tic.jenis_informed_consent, tic.penerima_informasi, tic.dokter_pelaksana, tic.status,
      ptk.id id_ptk, ptk.kunjungan, ptk.tanggal, ptk.diagnosis, ptk.dasar_diagnosis, ptk.tindakan_kedokteran,
      ptk.indikasi_tindakan, ptk.tata_cara, ptk.tujuan_tindakan, ptk.tujuan_pengobatan, ptk.risiko, ptk.komplikasi,
      ptk.prognosis, ptk.alternatif_risiko, ptk.lainnya, ptk.ttd_menerangkan, ptk.ttd_menerima, tptk.id id_tptk,
      tptk.nama_keluarga, tptk.umur_keluarga, tptk.jk_keluarga, tptk.alamat_keluarga, tptk.tindakan,
      tptk.hub_keluarga_dgn_pasien, tptk.tanggal_persetujuan, tptk.ttd_menyatakan, tptk.ttd_saksi_keluarga,
      tptk.ttd_saksi_rumah_sakit, tptk.saksi_keluarga, tptk.saksi_rumah_sakit, tptk.status_persetujuan,
      master.getNamaLengkap(pp.NORM) nama_pasien, master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR) umur,
      tic.created_at tanggal, mp.JENIS_KELAMIN jk, master.getNamaLengkapPegawai(ap2.NIP) saksi_rs'
    );
    $this->db->from('db_informed_consent.tb_informed_consent tic');
    $this->db->join('db_informed_consent.tb_form_ptk ptk', 'ptk.id_informed_consent = tic.id', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = tic.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = tic.oleh', 'left');
    $this->db->join('master.dokter md', 'md.ID = tic.dokter_pelaksana', 'left');
    $this->db->join('db_informed_consent.tb_persetujuan_tindakan_kedokteran tptk', 'tptk.id_informed_consent = tic.id', 'left');
    $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM', 'left');
    $this->db->join('aplikasi.pengguna ap2', 'ap2.ID = tptk.saksi_rumah_sakit', 'left');
    $this->db->where('tic.id', $id);

    $query = $this->db->get();
    return $query->row_array();
  }

  public function ubahInformedConcent($id, $data)
  {
    $this->db->where($id);
    $this->db->update('db_informed_consent.tb_informed_consent', $data);
  }

  public function ubahPTK($id, $data)
  {
    $this->db->where($id);
    $this->db->update('db_informed_consent.tb_form_ptk', $data);
  }

  public function ubahTPTK($id, $data)
  {
    $this->db->where($id);
    $this->db->update('db_informed_consent.tb_persetujuan_tindakan_kedokteran', $data);
  }
}