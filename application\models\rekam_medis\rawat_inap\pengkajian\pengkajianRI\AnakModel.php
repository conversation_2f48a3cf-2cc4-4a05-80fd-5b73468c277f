<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AnakModel extends MY_Model {
  protected $_table_name = 'keperawatan.tb_keperawatan';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor Ku<PERSON>',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib <PERSON>ka.'
      ),
    ),
  );

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('pk.NOMOR NOKUN
        , rk.ID ID_RUANGAN
        , rk.DESKRIPSI RUANGAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , kp.id_emr ID_EMR_PERAWAT
        , kp.created_at TANGGAL_PENGKAJIAN_PERAWAT
        , master.getNamaLengkapPegawai(peng.NIP) USER_PERAWAT
        , md.id_emr ID_EMR_MEDIS
        , md.created_at TANGGAL_PENGKAJIAN_MEDIS
        , master.getNamaLengkapPegawai(pemed.NIP) USER_MEDIS
        , p.NOMOR NOPEN
        , p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , kp.created_by ID_USER_PERAWAT
        , md.created_by ID_USER_MEDIS
        , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
        , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18, 2, 1) USIA
        , kp.jenis JENIS_PENGKAJIAN_PERAWAT
        , md.jenis JENIS_PENGKAJIAN_MEDIS
        ,HOUR(TIMEDIFF(NOW(),md.created_at)) DURASI_MEDIS,IF(HOUR(TIMEDIFF(NOW(),md.created_at))<=24,1,0) STATUS_EDIT_MEDIS
        ,HOUR(TIMEDIFF(NOW(),kp.created_at)) DURASI_PERAWAT,IF(HOUR(TIMEDIFF(NOW(),kp.created_at))<=24,1,0) STATUS_EDIT_PERAWAT');
    $this->db->from('pendaftaran.kunjungan pk');
    $this->db->join('keperawatan.tb_keperawatan kp','pk.NOMOR = kp.nokun AND kp.flag=1 AND kp.`status`=1','LEFT');
    $this->db->join('medis.tb_medis md','pk.NOMOR = md.nokun AND md.flag=1 AND md.`status`=1','LEFT');
    $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
    $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
    $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
    $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
    $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
    $this->db->join('master.pasien pas','pas.NORM = p.NORM','LEFT');
    $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
    $this->db->join('master.ruangan rp','rp.ID = tp.RUANGAN','LEFT');
    $this->db->join('master.referensi refpj','refpj.ID = pj.JENIS AND refpj.JENIS=10','LEFT');
    $this->db->join('aplikasi.pengguna peng','peng.ID = kp.created_by','LEFT');
    $this->db->join('aplikasi.pengguna pemed','pemed.ID = md.created_by','LEFT');
    

    $this->db->where('p.NORM',$this->input->post('nomr'));
    $this->db->where("(kp.id_emr IS NOT NULL OR md.id_emr IS NOT NULL)");
    $this->db->where("(kp.jenis=6 OR md.jenis=6)");
    $this->db->group_by('pk.NOMOR');
    $this->db->order_by('pk.MASUK ', 'DESC');
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }

  // Get Pengkajian Rawat Inap
public function getPengkajian($idemr)
{
  $query = $this->db->query(
    'SELECT kp.id_emr ID_EMR, kp.created_at TANGGAL_PEMBUATAN_EMR
    , kp.rujukan, kp.diagnosa_masuk DIAGNOSA_MASUK_PERMINTAAN_DIRAWAT
    , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dm.ICD LIMIT 1) DESKRIPSI_DIAGNOSA_MASUK
    , (SELECT anam.id_auto_allo FROM keperawatan.tb_anamnesa_perawat anam
        WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
      LIMIT 1) ID_AUTO_ALLO
    , (SELECT var.variabel FROM keperawatan.tb_anamnesa_perawat anam
        LEFT JOIN db_master.variabel var ON var.id_variabel = anam.id_auto_allo
        WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
      LIMIT 1) AUTO_ALLO
    , (SELECT anam.hubungan_dengan_pasien FROM keperawatan.tb_anamnesa_perawat anam
        WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
      LIMIT 1) HUBUNGAN_DENGAN_PASIEN
    , (SELECT anam.allo_nama FROM keperawatan.tb_anamnesa_perawat anam
        WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
      LIMIT 1) ALLO_NAMA
    , (SELECT anam.info_dari_keluarga_pasien FROM keperawatan.tb_anamnesa_perawat anam
        WHERE anam.id_emr=kp.id_emr AND anam.`status`=1
        LIMIT 1) INFO_DARI_KELUARGA_PASIEN
    , master.getNamaLengkapPegawai(peng.NIP) USER
    , p.NOMOR NOPEN
    , p.TANGGAL TANGGAL_DAFTAR
    , rp.DESKRIPSI RUANGAN_PENDAFTARAN, refpj.DESKRIPSI PENJAMIN
    , dm.ICD DIAGNOSA_MASUK
    , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
    , pk.NOMOR KUNJUNGAN, pk.MASUK TANGGAL_MASUK_RUANGAN
    , pk.KELUAR TANGGAL_KELUAR_RUANGAN
    , rk.DESKRIPSI RUANGAN_KUNJUNGAN, p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
    , IF(pas.JENIS_KELAMIN = 1,
    "Laki-Laki",
    "Perempuan") JK
    , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
    , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
    , TIMEDIFF(NOW()
    , (SELECT kep.created_at
        FROM keperawatan.tb_keperawatan kep
          LEFT JOIN pendaftaran.pendaftaran pen ON pen.NOMOR = kep.nopen
        WHERE kep.status = 1 AND pen.NORM = p.NORM
        ORDER BY kep.created_at DESC
        LIMIT 1)) DURATION_KEPERAWATAN
    , IF(HOUR(TIMEDIFF(NOW(),MAX(kp.created_at)))<=24=1,1,IF(kp.flag=1,0,1)) STATUS_EDIT

    /*START RIWAYAT KESEHATAN SEBELUMNYA*/
    , valerg.id_variabel ID_ALERGI, rke.isi_alergi ISI_ALERGI, rke.reaksi_alergi REAKSI_ALERGI
    , vtran.id_variabel ID_RIWAYAT_TRANSFUSI, vralg.id_variabel ID_REAKSI_TRANSFUSI
    , rke.isi_reaksi_transfusi ISI_REAKSI_TRANSFUSI
    , vkebi.id_variabel ID_KEBIASAAN, rke.isi_kebiasaan ISI_KEBIASAAN
    , vrkan.id_variabel ID_RIWAYAT_KANKER, rke.isi_kanker ISI_RIWAYAT_KANKER
    , vmet.id_variabel ID_RIWAYAT_METABOLIK
    , rke.isi_metabolik ISI_RIWAYAT_METABOLIK
    , vdd.id_variabel ID_RIWAYAT_DETEKSI_DINI
    , rke.isi_deteksidini ISI_DETEKSI
    , rke.apakah_pernah_dirawat APAKAH_PERNAH_DIRAWAT
    , rke.apakah_pernah_dirawat_penyakit_lain APAKAH_PERNAH_DIRAWAT_PENYAKIT_LAIN
    , rke.apakah_pernah_dirawat_tahun APAKAH_PERNAH_DIRAWAT_TAHUN
    , rke.apakah_ini_pertama_di_rskd PERAWATAN_PERTAMA_DIRSKD
    , rke.ket_apakah_ini_pertama_di_rskd KET_PERAWATAN_PERTAMA_DIRSKD
    /*END RIWAYAT KESEHATAN SEBELUMNYA*/
    
    /*START RIWAYAT KELAHIRAN*/
   , rkel.cek_riwayat_kelahiran CEK_RIWAYAT_KELAHIRAN
   , rkel.anak_ke ANAK_KE
   , rkel.saudara SAUDARA
   , rkel.cara_lahir CARA_LAHIR
   , rkel.kondisi_lahir KONDISI_LAHIR
   , rkel.berat_badan BERAT_BADAN
   , rkel.panjang_badan PANJANG_BADAN
   , rkel.lingkar_kepala LINGKAR_KEPALA
   , rkel.kelainan_bawaan KELAINAN_BAWAAN
   , rkel.isi_kelainan ISI_KELAINAN
   , rkel.imunisasi IMUNISASI
   , rkel.isi_imunisasi ISI_IMUNISASI
   , rkel.cek_riwayat_tumbuh_kembang CEK_RIWAYAT_TUMBUH_KEMBANG
   , rkel.riwayat_tumbuh_kembang RIWAYAT_TUMBUH_KEMBANG
   , rkel.tengkurap TENGKURAP
   , rkel.berjalan BERJALAN
   , rkel.duduk DUDUK
   , rkel.bicara BICARA
   , rkel.berdiri BERDIRI
   , rkel.tumbuh_gigi TUMBUH_GIGI
   /*END RIWAYAT KELAHIRAN*/
   
   /*START STATUS KESEHATAN SAAT INI*/
   , pf.keluhan_pasien KELUHAN_PASIEN
   , pf.makanan_yang_disukai MAKANAN_YANG_DISUKAI
   , pf.nafsu_makan NAFSU_MAKAN
   , pf.pola_makan POLA_MAKAN
   , pf.makanan_saat_ini MAKANAN_SAAT_INI
   , pf.keterangan_makanan_saat_ini KETERANGAN_MAKANAN_SAAT_INI
   , pf.pola_tidur POLA_TIDUR
   , pf.pola_tidur_lebih_satu POLA_TIDUR_LEBIH_SATU
   , pf.durasi_tidur DURASI_TIDUR
   , pf.kebiasaan_sebelum_tidur KEBIASAAN_SEBELUM_TIDUR
   , pf.mandi MANDI
   , pf.jumlah_mandi JUMLAH_MANDI
   , pf.gosok_gigi GOSOK_GIGI
   , pf.kebersihan_kuku KEBERSIHAN_KUKU
   , pf.kebersihan_anal KEBERSIHAN_ANAL
   , pf.aktivitas_bermain AKTIVITAS_BERMAIN
   /*END STATUS KESEHATAN SAAT INI*/


  /*START PEMERIKSAAN FISIK*/
    , (SELECT kes.id FROM db_pasien.tb_kesadaran kes
        LEFT JOIN db_master.variabel kesad ON kesad.id_variabel=kes.kesadaran
      WHERE kes.data_source=2 AND kes.`status`=1
      AND kes.nomr=p.NORM AND kes.ref=kp.id_emr
      ORDER BY kes.created_at DESC
      LIMIT 1) ID_TABEL_KESADARAN
    , (SELECT kes.kesadaran FROM db_pasien.tb_kesadaran kes
        LEFT JOIN db_master.variabel kesad ON kesad.id_variabel=kes.kesadaran
      WHERE kes.data_source=2 AND kes.`status`=1
      AND kes.nomr=p.NORM AND kes.ref=kp.id_emr
      ORDER BY kes.created_at DESC
      LIMIT 1) ID_VARIABEL_KESADARAN
    , (SELECT kesad.variabel FROM db_pasien.tb_kesadaran kes
        LEFT JOIN db_master.variabel kesad ON kesad.id_variabel=kes.kesadaran
      WHERE kes.data_source=2 AND kes.`status`=1
      AND kes.nomr=p.NORM AND kes.ref=kp.id_emr
      ORDER BY kes.created_at DESC
      LIMIT 1) KESADARAN
    
   , (SELECT vit.id FROM db_pasien.tb_tanda_vital vit
        WHERE vit.data_source=2 AND vit.`status`=1
        AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
        ORDER BY vit.created_at DESC
      LIMIT 1) ID_TABEL_TANDA_VITAL
    , (SELECT vit.td_sistolik FROM db_pasien.tb_tanda_vital vit
        WHERE vit.data_source=2 AND vit.`status`=1
        AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
      ORDER BY vit.created_at DESC
      LIMIT 1) TD_SISTOLIK
    , (SELECT vit.td_diastolik FROM db_pasien.tb_tanda_vital vit
        WHERE vit.data_source=2 AND vit.`status`=1
        AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
        ORDER BY vit.created_at DESC
      LIMIT 1) TD_DIASTOLIK
    , (SELECT vit.pernapasan FROM db_pasien.tb_tanda_vital vit
        WHERE vit.data_source=2 AND vit.`status`=1
        AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
        ORDER BY vit.created_at DESC
      LIMIT 1) PERNAPASAN
    , (SELECT vit.nadi FROM db_pasien.tb_tanda_vital vit
        WHERE vit.data_source=2 AND vit.`status`=1
        AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
        ORDER BY vit.created_at DESC
      LIMIT 1) NADI
    , (SELECT vit.suhu FROM db_pasien.tb_tanda_vital vit
        WHERE vit.data_source=2 AND vit.`status`=1
        AND vit.nomr=p.NORM AND vit.ref=kp.id_emr
        ORDER BY vit.created_at DESC
      LIMIT 1) SUHU

    , (SELECT tb.id FROM db_pasien.tb_tb_bb tb
        WHERE tb.data_source=2 AND tb.`status`=1
      AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
      ORDER BY tb.created_at DESC
      LIMIT 1) ID_TB_BB
    , (SELECT tb.tb FROM db_pasien.tb_tb_bb tb
        WHERE tb.data_source=2 AND tb.`status`=1
      AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
      ORDER BY tb.created_at DESC
      LIMIT 1) TB
    , (SELECT tb.bb FROM db_pasien.tb_tb_bb tb
        WHERE tb.data_source=2 AND tb.`status`=1
        AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
      ORDER BY tb.created_at DESC
      LIMIT 1) BB
    , (SELECT tb.jenis FROM db_pasien.tb_tb_bb tb
        WHERE tb.data_source=2 AND tb.`status`=1
      AND tb.nomr=p.NORM AND tb.ref=kp.id_emr
      ORDER BY tb.created_at DESC
      LIMIT 1) JENIS_TB_BB

   , pf.lingkar_kepala LINGKR_KPL
   , pf.bentuk_kepala BENTUK_KEPALA
   , pf.isi_kelainan_kepala ISI_KELAINAN_KEPALA
   , pf.irama IRAMA
   , pf.reaksi_dada REAKSI_DADA
   , pf.alat_bantu_napas ALAT_BANTU_NAPAS
   , pf.alat_bantu_napas_o2 ALAT_BANTU_NAPAS_O2
   , pf.alat_bantu_napas_ventilator ALAT_BANTU_NAPAS_VENTILATOR
   , pf.sianosis SIANOSIS
   , pf.pucat PUCAT
   , pf.capillary_refill_test CAPILLARY_REFILL_TEST
   , pf.akral AKRAL
   , pf.pembesaran_kelenjar PEMBESARAN_KELENJAR
   , pf.gangguan_neurologi GANGGUAN_NEUROLOGI
   , pf.isi_gangguan_neurologi ISI_GANGGUAN_NEUROLOGI
   , pf.mata MATA
   , pf.isi_abnormal ISI_ABNORMAL
   , pf.mulut MULUT
   , pf.isi_mulut ISI_MULUT
   , pf.abdomen ABDOMEN
   , pf.isi_abdomen ISI_ABDOMEN
   , pf.asites ASITES
   , pf.isi_asites ISI_ASITES
   , pf.defekasi DEFEKASI
   , pf.defekasi_stoma DEFEKASI_STOMA
   , pf.defekasi_frekuensi DEFEKASI_FREKUENSI
   , pf.defekasi_konsistensi DEFEKASI_KONSISTENSI
   , pf.fases FASES
   , pf.fases_lain FASES_LAIN
   , pf.urin URIN
   , pf.rectal RECTAL
   , pf.isi_rectal ISI_RECTAL
   , pf.genetalia GENETALIA
   , pf.isi_genetalia ISI_GENETALIA
   , pf.kulit KULIT
   , pf.warna_kulit WARNA_KULIT
   , pf.luka LUKA
   , pf.lokasi_luka LOKASI_LUKA
   , pf.kelainan_tulang KELAINAN_TULANG
   , pf.isi_kelainan_tulang ISI_KELAINAN_TULANG
   , pf.gerakan_anak GERAKAN_ANAK
   , pf.isi_gerakan_anak ISI_GERAKAN_ANAK
   /*END START PEMERIKSAAN FISIK*/
   
   /*START RIWAYAT PSIKOSOSIAL*/
    , pf.status_mental STATUS_MENTAL
    , pf.hub_keluarga HUB_KELUARGA
    , pf.tempat_tinggal TEMPAT_TINGGAL
    , pf.isi_tempat_tinggal ISI_TEMPAT_TINGGAL
    , pf.pengasuh PENGASUH
    , pf.jenis_sekolah JENIS_SEKOLAH
    , pf.privasi_khusus PRIVASI_KHUSUS
    , pf.privasi_khusus_lainnya PRIVASI_KHUSUS_LAINNYA
    , pf.kebutuhan_atau_budaya KEBUTUHAN_ATAU_BUDAYA
    , pf.ket_kebutuhan_atau_budaya KET_KEBUTUHAN_ATAU_BUDAYA
    /*END RIWAYAT PSIKOSOSIAL*/
   
   /*START STRONGKIDS*/
   , sgiz.kurus_anak KURUS_ANAK
   , sgiz.penurunan_bb_anak PENURUNAN_BB_ANAK
    , sgiz.kondisi_anak KONDISI_ANAK
    , sgiz.malnutrisi_anak MALNUTRISI_ANAK
    /*END STRONGKIDS*/
    
    /*START HUMPTY DUMPTY*/
    , hump.id ID_HUMPTY_DUMPTY
    /*END HUMPTY DUMPTY*/
    
    /*START STATUS FUNGSIONAL*/
    , bii.id ID_BERTHEL_INDEX
    /*END STATUS FUNGSIONAL*/

     /*START SKRINING NYERI*/
   , sny.id ID_SKRINING_NYERI
    , sny.metode METODE, sny.skor SKOR_NYERI, sny.farmakologi FARMAKOLOGI, sny.non_farmakologi NON_FARMAKOLOGI
    , sny.efek_samping ID_EFEK_SAMPING, sny.ket_efek_samping KET_EFEK_SAMPING, sny.provokative PROVOKATIVE
    , sny.quality QUALITY, sny.regio REGIO, sny.severity SEVERITY, sny.time ID_TIME, sny.ket_time KET_TIME
   /*START SKRINING GIZI*/
   
   /*START KEBUTUHAN EDUKASI*/
   , pendi.id_variabel PENDIDIKAN, bahas.id_variabel ID_BAHASA
    , ek.bahasa_daerah BAHASA_DAERAH, ek.bahasa_lain BAHASA_LAIN2
    , pener.id_variabel ID_PENERJEMAH
    , ek.penerjemah_lain PENERJEMAH_LAIN2
    , infok.id_variabel ID_INFORMASI, pak.lain_lain LAIN_LAIN_ASUHAN
    , (SELECT CONCAT(\'[\',GROUP_CONCAT(\'"\', ham.id_variabel, \'"\'SEPARATOR \',\'),\']\')
    FROM keperawatan.tb_hambatan hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE hb.id_emr = kp.id_emr AND hb.`status` = 1) ID_ARRAY_HAMBATAN
    , (SELECT hb.keterangan
    FROM keperawatan.tb_hambatan hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE kp.id_emr = hb.id_emr AND hb.keterangan != \'\'
    LIMIT 1) HAMBATAN_LAIN2
    , (SELECT CONCAT(\'[\',GROUP_CONCAT(\'"\', hb.id_variabel, \'"\'SEPARATOR \',\'),\']\')
    FROM keperawatan.tb_kebutuhan_pembelajaran hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE hb.id_emr = kp.id_emr AND hb.`status` = 1) ID_ARRAY_KEB_PEMBELAJARAN
    , (SELECT hb.keterangan
    FROM keperawatan.tb_kebutuhan_pembelajaran hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE kp.id_emr = hb.id_emr AND hb.keterangan != \'\'
    LIMIT 1) KEB_PEMBELAJARAN
   /*END KEBUTUHAN EDUKASI*/

   /*START PERENCANAAN PEMULANGAN PASIEN*/
    , ppp.memerlukan_p3 MEMERLUKAN_P3
    , ppp.keterbatasan_mobilitas KETERBATASAN_MOBILITAS, ppp.ket_keterbatasan_mobilitas KET_KETERBATASAN_MOBILITAS
    , ppp.perawatan PERAWATAN, ppp.ket_perawatan KET_PERAWATAN
    , ppp.pasien_tinggal_sendiri PASIEN_TINGGAL_SENDIRI, ppp.ket_pasien_tinggal_sendiri KET_PASIEN_TINGGAL_SENDIRI
    , ppp.adakah_keluarga ADAKAH_KELUARGA, ppp.ket_adakah_keluarga KET_ADAKAH_KELUARGA
    , ppp.pasien_tanggung_jawab_anak PASIEN_TANGGUNG_JAWAB_ANAK, ppp.ket_pasien_tanggung_jawab_anak KET_PASIEN_TANGGUNG_JAWAB_ANAK, ppp.pasien_pulang_bawa_obat PASIEN_PULANG_BAWA_OBAT, ppp.ket_pasien_pulang_bawa_obat KET_PASIEN_PULANG_BAWA_OBAT
    , ppp.risiko_infeksi RISIKO_INFEKSI, ppp.ket_risiko_infeksi KET_RISIKO_INFEKSI
    , ppp.efek_samping EFEK_SAMPING_P3, ppp.ket_efek_samping KET_EFEK_SAMPING_P3
    , ppp.masalah_untuk_transportasi MASALAH_UNTUK_TRANSPORTASI, ppp.ket_masalah_untuk_transportasi KET_MASALAH_UNTUK_TRANSPORTASI
    /*END PERENCANAAN PEMULANGAN PASIEN*/

    /*START KEPERAWATAN*/
    , kp.status_verif STATUS_VERIF
    , IF(HOUR(TIMEDIFF(NOW(),MAX(kp.created_at)))<=24=1,1,IF(kp.flag=1,0,1)) STATUS_EDIT
    , master.getNamaLengkapPegawai(uver.NIP) USER_YG_VERIF
    , kp.verif_oleh VERIF_OLEH
    /*END KEPERAWATAN*/
   
   /*START MASALAH KESEHATAN*/
   , pf.masalah_kesehatan_keperawatan MASALAH_KESEHATAN
   /*END MASALAH KESEHATAN*/
   
   /*START DIAGNOSIS KEPERAWATAN*/
    , db_master.getIDAsuhanKeperawatan(kp.id_emr) ID_ASUHAN_KEPERAWATAN
    , db_master.getAsuhanKeperawatan(kp.id_emr) ASUHAN_KEPERAWATAN
    , db_master.getIDAsuhanKeperawatanDiagnosa(kp.id_emr) ID_DIAGNOSA_KEP
    , db_master.getAsuhanKeperawatanDiagnosa(kp.id_emr) DIAGNOSA_KEP
    , db_master.getIDAsuhanKeperawatanNOC(kp.id_emr) ID_NOC
    , db_master.getAsuhanKeperawatanNOC(kp.id_emr) NOC
    , db_master.getIDAsuhanKeperawatanNIC(kp.id_emr) ID_NIC
    , db_master.getAsuhanKeperawatanNIC(kp.id_emr) NIC
    , kp.`status` STATUS_EMR, db_master.getLainLainIDDiagnosa(kp.id_emr) ID_DIAGNOSA_LAIN_LAIN
    , db_master.getLainLainDiagnosa(kp.id_emr) DIAGNOSA_LAIN_LAIN
    , db_master.getLainLainIDNOC(kp.id_emr) ID_NOC_LAIN_LAIN, db_master.getLainLainNOC(kp.id_emr) NOC_LAIN_LAIN
    , db_master.getLainLainIDNIC(kp.id_emr) ID_NIC_LAIN_LAIN, db_master.getLainLainNIC(kp.id_emr) NIC_LAIN_LAIN
   /*END DIAGNOSIS KEPERAWATAN*/
    
   /*
   
   , sg.jalur_nutrisi JALUR_NUTRISI
    , janut.variabel DESC_JALUR_NUTRISI
   , pf.apakah_ingin_masuk_keews APAKAH_INGIN_MASUK_KEEWS
    , ewz.id ID_EWS
    , odua.id ID_o2
    , odua.saturasi_o2 SATURASI_O2
    , odua.penggunaan_o2 PENGGUNAAN_O2

    , penurbb.id_variabel ID_PENURUNAN_BB
    , penurbb.variabel PENURUNAN_BB
    , penurbb.nilai SKOR_PENURUNAN_BB
    , asumak.id_variabel ID_ASUPAN_MAKANAN
    , asumak.variabel ASUPAN_MAKANAN
    , asumak.nilai SKOR_ASUPAN_MAKAN
    , (penurbb.nilai+asumak.nilai) TOTAL_SKOR_GIZI
    , mulu.id_variabel ID_MULUT
    , mulu.variabel MULUT
    , pf.isi_mulut KETERANGAN_MULUT
    , eso.id_variabel ID_ESO
    , eso.variabel ESO
    , pf.isi_esophagus KETERANGAN_ESO
    , abdo.id_variabel ID_ABD
    , abdo.variabel ABDOMEN
    , pf.isi_abdomen KETERANGAN_ABD

    , idung.id_variabel ID_IDUNG
    , idung.variabel IDUNG
    , pf.ket_keluhan_pada_hidung KETERANGAN_HIDUNG
    , dada.id_variabel ID_DADA
    , dada.variabel DADA
    , pf.ket_keluhan_pada_dada KETERANGAN_DADA
    , jantung.id_variabel ID_JANTUNG
    , jantung.variabel JANTUNG
    , pf.ket_keluhan_pada_jantung KETERANGAN_JANTUNG
    , pcjan.id_variabel ID_PACU_JANTUNG
    , pcjan.variabel PACUJANTUNG
    , kelpar.id_variabel ID_PARU
    , kelpar.variabel PARU
    , pf.ket_keluhan_pada_paru KETERANGAN_PARU
    , pendar.id_variabel ID_PENDARAHAN
    , pendar.variabel PENDARAHAN
    , pf.lokasi_perdarahan LOKASI_PENDARAHAN
    , pf.jumlah_perdarahan JUMLAH_PENDARAHAN
    , turgo.id_variabel ID_TURGOR_KULIT
    , turgo.variabel TURGOR_KULIT
    , edem.id_variabel ID_EDEMA
    , edem.variabel EDEMA
    , pf.ket_edema KETERANGAN_EDEMA
    , crot.id_variabel CRT
    , crot.variabel KETERANGAN_CRT
    , bii.id ID_BERTHEL_INDEX
    , keltid.id_variabel ID_KELUHAN_TIDUR
    , keltid.variabel KELUHAN_TIDUR
    , pf.ket_keluhan_istirahat KETERANGAN_KELUHAN_TIDUR
    , mob.id_variabel ID_MOBILISASI
    , mob.variabel MOBILISASI
    , pf.ket_kemampuan_mobilisasi KETERANGAN_MOBILISASI
  
    , pf.integritas_kondisi_kulit ID_INTEGRITAS_KULIT, pf.ket_integritas_kondisi_kulit KET_INTEGRITAS_KULIT
    , pf.integritas_luka INTEGRITAS_LUKA
    , brad.id ID_SKALA_BRADEN

    , rke.produksi_pemeriksaan_genitalia PRODUKSI_PEMERIKSAAN_GENITALIA
    , rke.ket_produksi_pemeriksaan_genitalia KET_PRODUKSI_PEM_GENITALIA
    , rke.produksi_pemakaian_kontrasepsi PRODUKSI_PEMAKAIAN_KONTRASEPSI
    , rke.ket_produksi_pemakaian_kontrasepsi KET_PRODUKSI_PEMAKAIAN_KONTRASEPSI
    , rke.produksi_sirkumsisi PRODUKSI_SIRKUMSISI
    , rke.ket_produksi_sirkumsisi KET_PRODUKSI_SIRKUMSISI
    , rke.eliminasi_defekasi ELIMINASI_DEFEKASI
    , rke.ket_eliminasi_defekasi KET_ELIMINASI_DEFEKASI
    , rke.eliminasi_miksi ELIMINASI_MIKSI
    , rke.ket_eliminasi_miksi KET_ELIMINASI_MIKSI
    , rke.keselamatan_status_mental KESELAMATAN_STATUS_MENTAL
    , rke.ket_keselamatan_status_mental KET_KESELAMATAN_STATUS_MENTAL
    , rke.keselamatan_gangguan_panca_indra KESELAMATAN_GANGGUAN_PANCA_INDRA
    , rke.ket_keselamatan_gangguan_panca_indra KET_KESELAMATAN_GANGGUAN_PANCA_INDRA
    , rke.keselamatan_membahayakan_dirinya_sendiri KESELAMATAN_MEMBAHAYAKAN_DIRINYA_SENDIRI
    , pde.id ID_DEWASA_SKALA_MORSE
    , peris.id ID_GERIATRI_SKALA_ONTARIO
    , spiri.id_variabel ID_SPIRITUAL
    , pf.sebutkan_keyakinan SPIRITUAL_LAINNYA
    , pf.pengobatan_alternatif ID_PENGOBATAN_ALTERNATIF
    , pf.sebutkan_pengobatan_alternatif PENGOBATAN_ALTERNATIF
    , pf.pengobatan_bertentangan ID_PENGOBATAN_BERTENTANGAN
    , pf.sebutkan_pengobatan_bertentangan PENGOBATAN_BERTENTANGAN
    , pendi.id_variabel PENDIDIKAN, bahas.id_variabel ID_BAHASA
    , ek.bahasa_daerah BAHASA_DAERAH, ek.bahasa_lain BAHASA_LAIN2
    , pener.id_variabel ID_PENERJEMAH
    , ek.penerjemah_lain PENERJEMAH_LAIN2
    , infok.id_variabel ID_INFORMASI, pak.lain_lain LAIN_LAIN_ASUHAN
    , (SELECT CONCAT(\'[\',GROUP_CONCAT(\'"\', ham.id_variabel, \'"\'SEPARATOR \',\'),\']\')
    FROM keperawatan.tb_hambatan hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE hb.id_emr = kp.id_emr AND hb.`status` = 1) ID_ARRAY_HAMBATAN
    , (SELECT hb.keterangan
    FROM keperawatan.tb_hambatan hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE kp.id_emr = hb.id_emr AND hb.keterangan != \'\'
    LIMIT 1) HAMBATAN_LAIN2
    , (SELECT CONCAT(\'[\',GROUP_CONCAT(\'"\', hb.id_variabel, \'"\'SEPARATOR \',\'),\']\')
    FROM keperawatan.tb_kebutuhan_pembelajaran hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE hb.id_emr = kp.id_emr AND hb.`status` = 1) ID_ARRAY_KEB_PEMBELAJARAN
    , (SELECT hb.keterangan
    FROM keperawatan.tb_kebutuhan_pembelajaran hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE kp.id_emr = hb.id_emr AND hb.keterangan != \'\'
    LIMIT 1) KEB_PEMBELAJARAN
    , ppp.memerlukan_p3 MEMERLUKAN_P3
    , ppp.umur_65 UMUR_65, ppp.ket_umur_65 KET_UMUR_65
    , ppp.keterbatasan_mobilitas KETERBATASAN_MOBILITAS, ppp.ket_keterbatasan_mobilitas KET_KETERBATASAN_MOBILITAS
    , ppp.perawatan PERAWATAN, ppp.ket_perawatan KET_PERAWATAN
    , ppp.pasien_tinggal_sendiri PASIEN_TINGGAL_SENDIRI, ppp.ket_pasien_tinggal_sendiri KET_PASIEN_TINGGAL_SENDIRI
    , ppp.adakah_keluarga ADAKAH_KELUARGA, ppp.ket_adakah_keluarga KET_ADAKAH_KELUARGA
    , ppp.pasien_tanggung_jawab_anak PASIEN_TANGGUNG_JAWAB_ANAK, ppp.ket_pasien_tanggung_jawab_anak KET_PASIEN_TANGGUNG_JAWAB_ANAK, ppp.pasien_pulang_bawa_obat PASIEN_PULANG_BAWA_OBAT, ppp.ket_pasien_pulang_bawa_obat KET_PASIEN_PULANG_BAWA_OBAT
    , ppp.risiko_infeksi RISIKO_INFEKSI, ppp.ket_risiko_infeksi KET_RISIKO_INFEKSI
    , ppp.efek_samping EFEK_SAMPING_P3, ppp.ket_efek_samping KET_EFEK_SAMPING_P3
    , ppp.masalah_untuk_transportasi MASALAH_UNTUK_TRANSPORTASI, ppp.ket_masalah_untuk_transportasi KET_MASALAH_UNTUK_TRANSPORTASI
    , pf.masalah_kesehatan_keperawatan MASALAH_KESEHATAN
    , sg.jalur_nutrisi JALUR_NUTRISI
    , janut.variabel DESC_JALUR_NUTRISI
  */

    , keperawatan.getMasalahKesehatanParentID(kp.id_emr) MASALAH_KESEHATAN_PARENT_ID
    , keperawatan.getMasalahKesehatanParentDESC(kp.id_emr) MASALAH_KESEHATAN_PARENT_DESC

    , keperawatan.getMasalahKesehatanChildID(kp.id_emr) MASALAH_KESEHATAN_CHILD_ID
    , keperawatan.getMasalahKesehatanChildDESC(kp.id_emr) MASALAH_KESEHATAN_CHILD_DESC

    , kp.status_verif_perawat STATUS_VERIF_PERAWAT
    , kp.verif_oleh_perawat VERIF_OLEH_PERAWAT
    , master.getNamaLengkapPegawai(usrprwt.NIP) USER_YG_VERIF_PERAWAT
    , (SELECT CONCAT(\'[\',GROUP_CONCAT(\'"\', vrp.id_user, \'"\'SEPARATOR \',\'),\']\')
    FROM db_master.tb_verif_pengkajian vrp
    WHERE vrp.id_jenis_pengkajian = kp.jenis AND vrp.status=1) ID_VERIF_PENGKAJIAN

    FROM keperawatan.tb_keperawatan kp

    LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
    LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
    LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
    LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
    LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
    LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
    LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
    LEFT JOIN master.ruangan rp ON rp.ID = tp.RUANGAN
    LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS = 10
    LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by
    LEFT JOIN keperawatan.tb_barthel_indek bii ON bii.nokun = kp.nokun AND bii.`status`=1
    LEFT JOIN keperawatan.tb_skrining_nyeri sny ON sny.ref = kp.id_emr AND sny.data_source=2 AND sny.`status`=1
    LEFT JOIN keperawatan.tb_skala_braden brad ON brad.nokun = kp.nokun AND brad.`status`=1
    #LEFT JOIN keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa pde ON pde.nokun = kp.nokun AND pde.`status`=1
    #LEFT JOIN keperawatan.tb_penilaian_risiko_skala_morse peris ON peris.nokun = kp.nokun AND peris.`status`=1
    LEFT JOIN keperawatan.tb_penilaian_humptydumpty_igd hump ON hump.nokun = kp.nokun AND hump.`status`=1
   LEFT JOIN keperawatan.tb_perencanaan_pemulangan_pasien ppp ON ppp.id_emr = kp.id_emr AND ppp.`status`=1
    LEFT JOIN db_pasien.tb_o2 odua ON odua.ref = kp.id_emr AND odua.data_source=1 AND odua.`status`=1

    LEFT JOIN keperawatan.tb_riwayat_kesehatan rke ON rke.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel valerg ON valerg.id_variabel = rke.alergi
    LEFT JOIN db_master.variabel vtran ON vtran.id_variabel = rke.riwayat_transfusi
    LEFT JOIN db_master.variabel vralg ON vralg.id_variabel = rke.reaksi_transfusi
    LEFT JOIN db_master.variabel vkebi ON vkebi.id_variabel = rke.kebiasaan
    LEFT JOIN db_master.variabel vrkan ON vrkan.id_variabel = rke.riwayat_kanker
    LEFT JOIN db_master.variabel vmet ON vmet.id_variabel = rke.riwayat_metabolik
    LEFT JOIN db_master.variabel vdd ON vdd.id_variabel = rke.deteksidini
    
    LEFT JOIN keperawatan.tb_riwayat_kelahiran rkel ON rkel.id_emr = kp.id_emr
   LEFT JOIN keperawatan.tb_skrining_gizi sgiz ON sgiz.id_emr = kp.id_emr
   
    LEFT JOIN keperawatan.tb_pemeriksaan_fisik pf ON pf.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_skrining_gizi sg ON sg.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_ews ewz ON ewz.ref = kp.id_emr AND ewz.`status`=1
    LEFT JOIN db_master.variabel penurbb ON penurbb.id_variabel = sg.penurunan_bb
    LEFT JOIN db_master.variabel asumak ON asumak.id_variabel = sg.asupan_bb
    LEFT JOIN db_master.variabel mulu ON mulu.id_variabel = pf.mulut
    LEFT JOIN db_master.variabel eso ON eso.id_variabel = pf.esophagus
    LEFT JOIN db_master.variabel abdo ON abdo.id_variabel = pf.abdomen
    LEFT JOIN db_master.variabel janut ON janut.id_variabel = sg.jalur_nutrisi

    LEFT JOIN db_master.variabel idung ON idung.id_variabel = pf.keluhan_pada_hidung
    LEFT JOIN db_master.variabel dada ON dada.id_variabel = pf.keluhan_pada_dada
    LEFT JOIN db_master.variabel jantung ON jantung.id_variabel = pf.keluhan_pada_jantung
    LEFT JOIN db_master.variabel pcjan ON pcjan.id_variabel = pf.alat_pacu_jantung
    LEFT JOIN db_master.variabel kelpar ON kelpar.id_variabel = pf.keluhan_pada_paru
    LEFT JOIN db_master.variabel pendar ON pendar.id_variabel = pf.perdarahan
    LEFT JOIN db_master.variabel turgo ON turgo.id_variabel = pf.turgor_kulit
    LEFT JOIN db_master.variabel edem ON edem.id_variabel = pf.edema
    LEFT JOIN db_master.variabel crot ON crot.id_variabel = pf.crt
    LEFT JOIN db_master.variabel keltid ON keltid.id_variabel = pf.keluhan_istirahat
    LEFT JOIN db_master.variabel mob ON mob.id_variabel = pf.kemampuan_mobilisasi
    LEFT JOIN db_master.variabel spiri ON spiri.id_variabel = pf.keyakinan

    LEFT JOIN keperawatan.tb_perencanaan_tindakan pertin ON pertin.id_emr = kp.id_emr
    LEFT JOIN keperawatan.tb_edukasi_keperawatan ek ON ek.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel pendi ON pendi.id_variabel = ek.tingkat_pendidikan
    LEFT JOIN db_master.variabel bahas ON bahas.id_variabel = ek.bahasa
    LEFT JOIN db_master.variabel pener ON pener.id_variabel = ek.penerjemah
    LEFT JOIN db_master.variabel infok ON infok.id_variabel = ek.informasi
    LEFT JOIN db_master.variabel hamba ON hamba.id_variabel = ek.hambatan
    LEFT JOIN db_master.variabel belaj ON belaj.id_variabel = ek.pembelajaran
    LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan pak ON pak.id_emr = kp.id_emr
    LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON akd.ID = pak.id_asuhan_keperawatan_detil
    LEFT JOIN aplikasi.pengguna uver ON uver.ID = kp.verif_oleh
    LEFT JOIN aplikasi.pengguna usrprwt ON usrprwt.ID = kp.verif_oleh_perawat

    WHERE kp.`status`=1 AND kp.jenis=6 AND kp.id_emr="' . $idemr . '"

    GROUP BY kp.id_emr'
  );
return $query->row_array();
}

  public function getNomrRawatInapAnak($nopen)
    {
        $query = $this->db->query(
            "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
        , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , pas.JENIS_KELAMIN ID_JK
        , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
        , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
        , p.TANGGAL TANGGAL_DAFTAR
        , r.JENIS_KUNJUNGAN
        , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
        , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
        ) DESKRIPSI_DIAGNOSA_MASUK
        , ref.ID IDPENJAMIN
        , ref.DESKRIPSI PENJAMIN
        , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
        dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
        )))) STATUS_KUNJUNGAN
        , penggu.ID ID_USER
        , dok.ID ID_DOKTER
        , pas.TANGGAL_LAHIR
        , dtt.NAME_PIC
        , pk.REF
        , mkp.NOMOR NOTLPN
        ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) ID_TUJUAN_FARMASI

        , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) TUJUAN_FARMASI
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
        , ppk.NAMA RUJUKAN_DARI
        , refdar.DESKRIPSI GOL_DARAH
        , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=6
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_KEPERAWATAN_DEWASA_RI
        , (SELECT id_emr FROM medis.tb_medis kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=6
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_MEDIS_DEWASA_RI

        FROM pendaftaran.pendaftaran p
        LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
        LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.`STATUS` != 0
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
        LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND refdar.JENIS=6
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
        LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
        LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM

        WHERE tp.`STATUS` not in (0,1)
        AND p.`STATUS`!= 0
        #AND pk.`STATUS` != 0 
      AND p.NOMOR = '$nopen'
        GROUP BY dtt.ID #DESC"
        );
        return $query->row_array();
    }

  //  Hitung jumlah indeks barthel
  function get_count_indeksBarthel($nokun){
    $query = $this->db->query(
      "SELECT COUNT(tbbi.nokun) JUMLAH
      FROM keperawatan.tb_barthel_indek tbbi
      WHERE tbbi.nokun = '$nokun' AND tbbi.ref='6' AND tbbi.`status`='1'"
    );
    if ($query->num_rows() > 0) {
     return $query->row()->JUMLAH;
   }
   return false;
 }

 public function hasilIndeksBarthel($nokun)
 {
    $query = $this->db->query(
        "SELECT *
        FROM keperawatan.tb_barthel_indek bi
        WHERE bi.nokun='$nokun' AND bi.ref='6' AND bi.`status`='1'"
    );
    return $query->row_array();
}

 //  Hitung jumlah humpty dumpty
  function get_count_humptyDumpty($nokun){
    $query = $this->db->query(
      "SELECT COUNT(tbbi.nokun) JUMLAH
      FROM keperawatan.tb_penilaian_humptydumpty_igd tbbi
      WHERE tbbi.nokun = '$nokun' AND tbbi.ref='6' AND tbbi.`status`='1'"
    );
    if ($query->num_rows() > 0) {
     return $query->row()->JUMLAH;
   }
   return false;
 }

 public function hasilHumptyDumpty($nokun)
{
    $query = $this->db->query(
        "SELECT bi.*, v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai + v7.nilai HASIL
        FROM keperawatan.tb_penilaian_humptydumpty_igd bi
        LEFT JOIN db_master.variabel v1 ON v1.id_variabel = bi.umur
        LEFT JOIN db_master.variabel v2 ON v2.id_variabel = bi.jenis_kelamin
        LEFT JOIN db_master.variabel v3 ON v3.id_variabel = bi.diagnosa
        LEFT JOIN db_master.variabel v4 ON v4.id_variabel = bi.gangguan_kognitif
        LEFT JOIN db_master.variabel v5 ON v5.id_variabel = bi.faktor_lingkungan
        LEFT JOIN db_master.variabel v6 ON v6.id_variabel = bi.respon_terhadap_operasi
        LEFT JOIN db_master.variabel v7 ON v7.id_variabel = bi.penggunaan_obat
        WHERE bi.nokun='$nokun' AND bi.ref='6' AND bi.`status`='1'"
    );
    return $query->row_array();
}

 public function getNilaiHumptyDumpty($idva)
 {
  $query = $this->db->query(
    "SELECT va.nilai NILAI FROM db_master.variabel va
    WHERE va.id_variabel='$idva'"
  );
  if ($query->num_rows() > 0) {
     return $query->row()->NILAI;
   }
   return false;
}


}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
