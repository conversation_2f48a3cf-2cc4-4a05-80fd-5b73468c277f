<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SkriningPasienBaruModel extends MY_Model {

    public function listPasienSkriningBaru()
    {
        if ($this->input->get('q')) {
            $this->db->like('pop.NAMA', $this->input->get('q'));
        }
        $this->db->select('adm.ID, pop.NAMA, adm.NOMR, pop.TGL_LAHIR, kp.TELP_SELULER, pop.TGL_INPUT TGL_DAFTAR, ref.DESKRIPSI JAMINAN, skb.STATUS');
        $this->db->from('pendaftaran_online.admision adm');
        $this->db->join('pendaftaran_online.pasien pop', 'adm.PENDAFTAR = pop.ID', 'left');
        $this->db->join('pendaftaran_online.kontak_pasien kp', 'adm.PENDAFTAR = kp.ID_PASIEN', 'left');
        $this->db->join('pendaftaran_online.referensi ref', 'pop.JAMINAN = ref.ID', 'left');
        $this->db->join('db_layanan.tb_skrining_pasienbaru skb', 'adm.ID = skb.ID', 'left');
        $this->db->where('adm.VERIF', 1);
        $this->db->where('adm.NOMR !=""');
        $this->db->where('skb.STATUS IS NULL');
        $this->db->where('adm.STATUS', 1);
        $this->db->order_by('pop.TGL_INPUT', 'DESC');

        $query = $this->db->get();
        return $query;
    }

    public function listDataPasienSkriningBaru($id)
    {
        if ($this->input->get('q')) {
            $this->db->like('pop.NAMA', $this->input->get('q'));
        }
        $this->db->select('adm.ID, pop.NAMA, adm.NOMR, pop.TGL_LAHIR, kp.TELP_SELULER, pop.TGL_INPUT TGL_DAFTAR, ref.DESKRIPSI JAMINAN, skb.STATUS');
        $this->db->from('pendaftaran_online.admision adm');
        $this->db->join('pendaftaran_online.pasien pop', 'adm.PENDAFTAR = pop.ID', 'left');
        $this->db->join('pendaftaran_online.kontak_pasien kp', 'adm.PENDAFTAR = kp.ID_PASIEN', 'left');
        $this->db->join('pendaftaran_online.referensi ref', 'pop.JAMINAN = ref.ID', 'left');
        $this->db->join('db_layanan.tb_skrining_pasienbaru skb', 'adm.ID = skb.ID', 'left');
        $this->db->where('adm.ID', $id);
        $this->db->where('adm.VERIF', 1);
        $this->db->where('adm.NOMR !=""');
        $this->db->where('skb.STATUS IS NULL');
        $this->db->where('adm.STATUS', 1);

        $query = $this->db->get();
        return $query;
    }

    public function historyPasienSkriningBaru()
    {
        if ($this->input->get('q')) {
            $this->db->like('pop.NAMA', $this->input->get('q'));
        }
        $this->db->select("skb.ID_SKRINING_EMR
                        , skb.ID
                        , skb.NAMA_PASIEN
                        , skb.NORM
                        , IF(skb.KESIMPULAN = 1, 'Diterima', IF(skb.KESIMPULAN = 2, 'Tidak diterima', 'Tidak Ada')) STATUS_PASIEN
                        , skb.CREATED_AT TGL_STATUS
                        , skb.STATUS");
        $this->db->from('db_layanan.tb_skrining_pasienbaru skb');
        $this->db->where('skb.STATUS', 1);

        $query = $this->db->get();
        return $query;
    }

    public function dataHistoryPasienSkriningBaru($id)
    {
        $this->db->select("skb.ID
                            , skb.NAMA_PASIEN
                            , skb.NORM
                            , skb.RS_RUJUKAN_ASAL
                            , smf.DESKRIPSI SMF
                            , skb.ALASAN_DIRUJUK
                            , skb.DOKTER_PERUJUK
                            , skb.RIWAYAT_TATA_LAKSANA
                            , IF(skb.LABORATORIUM = 27, 'Tidak Ada', IF(skb.LABORATORIUM = 28, CONCAT('Ada', ' : ', skb.LAB_DESKRIPSI), NULL)) LABORATORIUM
                            , IF(skb.PATOLOGI_ANATOMI = 27, 'Tidak Ada', IF(skb.PATOLOGI_ANATOMI = 28, CONCAT('Ada', ' : ', skb.PA_DESKRIPSI), NULL)) PATOLOGI_ANATOMI
                            , IF(skb.RADIODIAGNOSTIK = 27, 'Tidak Ada', IF(skb.RADIODIAGNOSTIK = 28, CONCAT('Ada', ' : ', skb.RADIODIAGNOSTIK_DESKRIPSI), NULL)) RADIODIAGNOSTIK
                            , std.DESKRIPSI STADIUM
                            , IF(skb.KESIMPULAN = 1, 'Diterima di RS Kanker Dharmais', IF(skb.KESIMPULAN = 2, 'Tidak diterima di RS Kanker Dharmais', NULL)) KESIMPULAN
                            , IF(skb.KESIMPULAN_DITERIMA = 1, 'Instalasi Gawat Darurat', IF(skb.KESIMPULAN_DITERIMA = 2, 'Layanan Rawat Jalan', NULL)) KESIMPULAN_DITERIMA
                            , timja.DESKRIPSI TIMJA
                            , master.getNamaLengkapPegawai(dok.NIP) DPJP
                            , r.DESKRIPSI POLI
                            , skb.CATATAN_KHUSUS
                            , IF(skb.KESIMPULAN_TIDAK_DITERIMA = 1, 'Tidak ada sarana/prasarana', IF(skb.KESIMPULAN = 2, 'Lainnya', NULL)) KESIMPULAN_TIDAK_DITERIMA
                            , skb.KESIMPULAN_LAINNYA");
        $this->db->from('db_layanan.tb_skrining_pasienbaru skb');
        $this->db->join('master.referensi smf', 'skb.SMF = smf.ID AND smf.JENIS =26', 'left');
        $this->db->join('master.stadium std', ' skb.STADIUM = std.ID', 'left');
        $this->db->join('master.referensi timja', ' skb.TIMJA = timja.ID AND timja.JENIS =102', 'left');
        $this->db->join('master.dokter dok', ' skb.DPJP = dok.ID', 'left');
        $this->db->join('master.ruangan r', ' skb.POLI = r.ID AND r.JENIS = 5 AND r.JENIS_KUNJUNGAN IN (1, 2, 4, 5, 14, 13)', 'left');
        $this->db->where('skb.ID_SKRINING_EMR', $id);
        $this->db->where('skb.STATUS', 1);
        $this->db->group_by('skb.ID');

        $query = $this->db->get();
        return $query;
    }

    
    public function diagnosa($id){
        $query = "SELECT skb.ID_SKRINING_EMR
                , dsp.DIAGNOSIS diagnosa_
                , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dsp.DIAGNOSIS AND mr.SAB = 'ICD10_1998' GROUP BY mr.CODE) diagnosa
                , CONCAT (dsp.DIAGNOSIS, ' | ', (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE = dsp.DIAGNOSIS AND mr.SAB = 'ICD10_1998' GROUP BY mr.CODE)) diagnosa2

                FROM db_layanan.tb_skrining_pasienbaru skb
                LEFT JOIN db_layanan.tb_diagnosa_skrining_pasienbaru dsp ON skb.ID_SKRINING_EMR = dsp.ID_SKRINING
                WHERE dsp.ID_SKRINING = ?
                AND dsp.STATUS = ?";
        $bind = $this->db->query($query, array($id,1)); 
        return $bind;
    }



}
?>