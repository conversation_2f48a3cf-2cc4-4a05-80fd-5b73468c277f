<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FlosheetModel extends MY_Model {

  public function simpanFlosheet($data)
  {
      $this->db->insert('keperawatan.tb_flosheet', $data);
      return $this->db->insert_id();
  }
  public function simpanO2($data)
  {
      $this->db->insert('db_pasien.tb_o2', $data);
      return $this->db->insert_id();
  }
  public function simpanTandaVital($data)
  {
      $this->db->insert('db_pasien.tb_tanda_vital', $data);
      return $this->db->insert_id();
  }
  public function simpanTandaVitalFlosheet($data)
  {
      $this->db->insert('keperawatan.tb_flosheet_tandavital', $data);
      return $this->db->insert_id();
  }
  public function simpanEkgCvpSato2($data)
  {
      $this->db->insert('keperawatan.tb_flosheet_ekgcvpsato2', $data);
      return $this->db->insert_id();
  }

  public function simpanSkriningNyeriFlosheet($data)
  {
      $this->db->insert('keperawatan.tb_flosheet_skriningnyeri', $data);
      return $this->db->insert_id();
  }

  public function simpanSkriningNyeri($data)
  {
      $this->db->insert('keperawatan.tb_skrining_nyeri', $data);
      return $this->db->insert_id();
  }

  public function HistoryTandaVital($id)
  {
    $query = $this->db->query(
      "SELECT *, IF(tft.tanggal_dan_jam = '0000-00-00 00:00:00', CONCAT(DATE_FORMAT(tft.created_at,'%Y-%m-%d'), ' ', DATE_FORMAT(tft.pukul,'%H:%i:%s')), tft.tanggal_dan_jam) TANGGAL_DAN_JAM_NEW
      FROM keperawatan.tb_flosheet_tandavital tft
      WHERE tft.id_flosheet = '$id' AND tft.status='1'
      ORDER BY TANGGAL_DAN_JAM_NEW ASC"
    );
    return $query->result_array();
  }

  public function HistoryTandaVitalSuhu($id)
  {
    $query = $this->db->query(
      "SELECT *, IF(tft.tanggal_dan_jam = '0000-00-00 00:00:00', CONCAT(DATE_FORMAT(tft.created_at,'%Y-%m-%d'), ' ', DATE_FORMAT(tft.pukul,'%H:%i:%s')), tft.tanggal_dan_jam) TANGGAL_DAN_JAM_NEW
      FROM keperawatan.tb_flosheet_tandavital tft
      WHERE tft.id_flosheet = '$id' AND tft.status='1' AND tft.suhu != '0'
      ORDER BY TANGGAL_DAN_JAM_NEW ASC"
    );
    return $query->result_array();
  }

  public function hasilInputTandaVital($id)
    {
        $query = $this->db->query(
            "SELECT * FROM keperawatan.tb_flosheet_tandavital tft
            WHERE tft.id_flosheet = '$id' AND tft.status='1'
            ORDER BY tft.tanggal_dan_jam DESC"
        );
        return $query->result_array();
    }

    public function HistorySkriningNyeri($id)
    {
      $query = $this->db->query(
        "SELECT tfs.id ID, tfs.id_flosheet ID_FLOSHEET, tfs.id_skrining_nyeri ID_SKRINING_NYERI, tfs.nomr NOMR, tfs.nokun NOKUN
        , tfs.pukul PUKUL, tfs.metode METODE, tfs.skor SKOR, tfs.provokative PROVOKATIVE, tfs.quality QUALITY
        , tfs.quality_lainnya DESK_QUALITY, tfs.regio REGIO, tfs.severity SEVERITY, tfs.time TIME, tfs.ket_time DESK_TIME
        , v.variabel VAR_SKOR
        , tfs.tanggal_dan_jam
        , IF(tfs.tanggal_dan_jam = '0000-00-00 00:00:00', CONCAT(DATE_FORMAT(tfs.created_at,'%Y-%m-%d'), ' ', DATE_FORMAT(tfs.pukul,'%H:%i:%s')), tfs.tanggal_dan_jam) TANGGAL_DAN_JAM_NEW
        FROM keperawatan.tb_flosheet_skriningnyeri tfs

        LEFT JOIN db_master.variabel v ON v.id_variabel = tfs.skor

        WHERE tfs.id_flosheet = '$id' AND tfs.status='1'
        ORDER BY tfs.tanggal_dan_jam ASC"
      );
      return $query->result_array();
    }
    

    public function HistoryInputSkriningNyeri($id)
    {
      $query = $this->db->query(
        "SELECT tfs.id ID, tfs.id_flosheet ID_FLOSHEET, tfs.id_skrining_nyeri ID_SKRINING_NYERI, tfs.nomr NOMR, tfs.nokun NOKUN
        , tfs.pukul PUKUL, tfs.metode METODE, tfs.skor SKOR, tfs.provokative PROVOKATIVE, tfs.quality QUALITY
        , tfs.quality_lainnya DESK_QUALITY, tfs.regio REGIO, tfs.severity SEVERITY, tfs.time TIME, tfs.ket_time DESK_TIME
        , v.variabel VAR_SKOR
        FROM keperawatan.tb_flosheet_skriningnyeri tfs

        LEFT JOIN db_master.variabel v ON v.id_variabel = tfs.skor

        WHERE tfs.id_flosheet = '$id' AND tfs.status='1'
        ORDER BY tfs.pukul ASC"
      );
      return $query->result_array();
    }

  public function dataAkhirFlosheet($nokun)
  {
  	$query = $this->db->query("
  		SELECT tbf.hari_post HARI_POST, tbf.hari_perawat HARI_PERAWAT, tbf.alergi ALERGI, tbf.desk_alergi DESK_ALERGI
  		, tb.tb TINGGI_BADAN, tb.bb BERAT_BADAN, tbf.diagnosa DIAGNOSA, tbf.tindakan_operasi TINDAKAN_OPERASI
  		, tbf.no_tempat_tidur NO_TEMPAT_TIDUR, tbf.dr_primer DR_PRIMER, tbf.dr_konsultan DR_KONSULTAN
  		, tbf.dr_anestesi DR_ANESTESI, tbf.id ID_FLOSHEET
  		FROM keperawatan.tb_flosheet tbf
  		LEFT JOIN db_pasien.tb_tb_bb tb ON tb.ref = tbf.id AND tb.data_source = 28
  		WHERE tbf.nokun='$nokun'
  		ORDER BY tbf.id DESC
  		LIMIT 1
  		");
  	return $query->row_array();
  }

  public function getFlosheet($id)
  {
  	$query = $this->db->query("
  		SELECT tbf.id ID_FLOSHEET, tbf.nokun NOKUN, DATE_FORMAT(tbf.tgl_jam,'%d-%m-%Y %H:%i:%s') TANGGAL, tbf.hari_post HARI_POST, tbf.hari_perawat HARI_PERAWAT
  		, tbf.alergi ALERGI, tbf.desk_alergi DESK_ALERGI, tbf.lingkar_perut LINGKAR_PERUT, tbf.diagnosa DIAGNOSA
  		, tbf.tindakan_operasi TINDAKAN_OPERASI, tbf.golongan_darah GOLONGAN_DARAH, tbf.no_tempat_tidur NO_TEMPAT_TIDUR
  		, tbf.dr_primer DR_PRIMER, tbf.dr_konsultan DR_KONSULTAN, tbf.dr_anestesi DR_ANESTESI, tb.tb TINGGI_BADAN
  		, tb.bb BERAT_BADAN
  		FROM keperawatan.tb_flosheet tbf
  		LEFT JOIN db_pasien.tb_tb_bb tb ON tb.ref = tbf.id AND tb.data_source = 28
  		WHERE tbf.id='$id' AND tbf.`status`='1'
  		");
  	return $query->row_array();
  }

  public function pilihFlosheet($norm)
  {
  	$query = $this->db->query("
  		SELECT tbf.id ID_FLOSHEET, tbf.nokun NOKUN, DATE_FORMAT(tbf.tgl_jam,'%d/%m/%Y %H:%i:%s') TANGGAL
  		FROM keperawatan.tb_flosheet tbf
  		WHERE tbf.nomr='$norm' AND tbf.`status`='1'
  		");
  	return $query->result_array();
  }

  public function tableVentilasi($idflosheet)
  {
  	$query = $this->db->query("SELECT tfv.*, vmk.variabel VAR_VEN_MEKANIK, vsp.variabel VAR_VEN_SPONTAN
      , vtp.variabel VAR_VEN_TIPE, vt.variabel VAR_VEN_TEKANAN
      , IF(tfv.tanggal_dan_jam = '0000-00-00 00:00:00', CONCAT(DATE_FORMAT(tfv.created_at,'%Y-%m-%d'), ' ', DATE_FORMAT(tfv.pukul,'%H:%i:%s')), tfv.tanggal_dan_jam) TANGGAL_DAN_JAM_NEW
      FROM keperawatan.tb_flosheet_ventilasi tfv
      LEFT JOIN db_master.variabel vtp ON vtp.id_variabel = tfv.tipe_ventilasi
      LEFT JOIN db_master.variabel vsp ON vsp.id_variabel = tfv.tipe_ventilasi_spontan
      LEFT JOIN db_master.variabel vmk ON vmk.id_variabel = tfv.tipe_ventilasi_mekanik
      LEFT JOIN db_master.variabel vt ON vt.id_variabel = tfv.tekanan
      WHERE tfv.id_flosheet='$idflosheet' AND tfv.`status`='1'
      ORDER BY TANGGAL_DAN_JAM_NEW ASC");
  	return $query->result_array();
  }

  public function tableEkgCvpSato2($idflosheet)
  {
    $query = $this->db->query("SELECT *, tfv.id ID_EKG, vb.variabel VAR_KATA
      , IF(tfv.tanggal_dan_jam = '0000-00-00 00:00:00', CONCAT(DATE_FORMAT(tfv.created_at,'%Y-%m-%d'), ' ', DATE_FORMAT(tfv.pukul,'%H:%i:%s')), tfv.tanggal_dan_jam) TANGGAL_DAN_JAM_NEW
      FROM keperawatan.tb_flosheet_ekgcvpsato2 tfv
      LEFT JOIN db_pasien.tb_o2 tbo ON tfv.id = tbo.ref AND tbo.data_source='28' AND tfv.nokun = tbo.nokun
      LEFT JOIN db_master.variabel vb ON vb.id_variabel = tbo.penggunaan_o2
      WHERE tfv.id_flosheet='$idflosheet' AND tfv.`status`='1'
      ORDER BY TANGGAL_DAN_JAM_NEW ASC");
    return $query->result_array();
  }
	
	public function tableSSP($idflosheet)
  {
  	$query = $this->db->query("SELECT ssp.id ID_SSP, ssp.nokun NOKUN, CONCAT(ssp.tanggal,' ',ssp.pukul) pukul
        , kesad.variabel KESADARAN
        , IF(ssp.jenis=1, 1,IF(ssp.jenis=2,2,1)) JENIZ
        , IF(ssp.jenis=1, 'Dewasa - GCS',IF(ssp.jenis=2,'Anak - Fourscore','Dewasa - GCS')) DESK_JENIZ
        , bumat.variabel BUKAMATA, bumat.nilai NILAI_BUKAMATA
        , rm.variabel RESPONMOTORIK, rm.nilai NILAI_RESPONMOTORIK
        , rv.variabel RESPONVERBAL, rv.nilai NILAI_RESPONVERBAL
        
        , resmat.variabel RESPONMATA
        , resmat.nilai NILAI_RESPONMATA
        , resmot.variabel RESPONMOTORIK
        , resmot.nilai NILAI_RESPONMOTORIKFS
        , batok.variabel BATANG_TOAK
        , batok.nilai NILAI_BATANG_TOAK
        , respi.variabel RESPIRASI
        , respi.nilai NILAI_RESPIRASI
        , rp.variabel REAKSIPUPIL
        , rpk.variabel REAKSIPUPILKIRI
        , bp.variabel BESARPUPIL
        , bpk.variabel BESARPUPILKIRI
      
        , tka.variabel TKA
        , tki.variabel TKI
        , kka.variabel KKA
        , kki.variabel KKI
      FROM keperawatan.tb_flosheet_ssp ssp
        LEFT JOIN keperawatan.tb_flosheet flo ON flo.id = ssp.id_flosheet
        LEFT JOIN db_pasien.tb_kesadaran kesa ON kesa.id = ssp.id_kesadaran
        LEFT JOIN db_master.variabel kesad ON kesad.id_variabel = kesa.kesadaran
        LEFT JOIN db_master.variabel bumat ON bumat.id_variabel = ssp.buka_mata
        LEFT JOIN db_master.variabel rm ON rm.id_variabel = ssp.respons_motorik
        LEFT JOIN db_master.variabel rv ON rv.id_variabel = ssp.respons_verbal
        LEFT JOIN db_master.variabel rp ON rp.id_variabel = ssp.reaksi_pupil
        LEFT JOIN db_master.variabel rpk ON rpk.id_variabel = ssp.reaksi_pupil_kiri
        LEFT JOIN db_master.variabel bp ON bp.id_variabel = ssp.besar_pupil
        LEFT JOIN db_master.variabel bpk ON bpk.id_variabel = ssp.besar_pupil_kiri
        LEFT JOIN db_master.variabel tki ON tki.id_variabel = ssp.tki
        LEFT JOIN db_master.variabel tka ON tka.id_variabel = ssp.tka
        LEFT JOIN db_master.variabel kki ON kki.id_variabel = ssp.kki
        LEFT JOIN db_master.variabel kka ON kka.id_variabel = ssp.kka
        
        LEFT JOIN db_master.variabel resmat ON resmat.id_variabel = ssp.respons_mata
        LEFT JOIN db_master.variabel resmot ON resmot.id_variabel = ssp.respons_motorik_fs
        LEFT JOIN db_master.variabel batok ON batok.id_variabel = ssp.reflek_batang_otak
        LEFT JOIN db_master.variabel respi ON respi.id_variabel = ssp.respirasi
        
      WHERE ssp.`status`=1 AND ssp.id_flosheet='$idflosheet'
      ORDER BY ssp.tanggal, ssp.pukul ASC");
  	return $query->result_array();
  }

  public function get_infus()
  {
    $nokun = $this->uri->segment(6);
    if($this->input->post('nokun')){
      $nokun = $this->input->post('nokun');
    }
    $query = $this->db->query("SELECT i.id id, i.nokun, i.id_flosheet, b.NAMA cairan, i.dosis dosis_awal, if(i.keterangan_dosis IS NULL ,'', i.keterangan_dosis) keterangan_dosis
        , if((SELECT fpi.ca 
            FROM keperawatan.tb_flosheet_pemasukan fp
              LEFT JOIN keperawatan.tb_flosheet_pemasukan_infus fpi ON fpi.id_pemasukan = fp.id
            WHERE fpi.id_infus=i.id AND fp.`status`=1 
            ORDER BY fpi.id DESC
            LIMIT 1) IS NULL , i.dosis , (SELECT fpi.ca 
            FROM keperawatan.tb_flosheet_pemasukan fp
              LEFT JOIN keperawatan.tb_flosheet_pemasukan_infus fpi ON fpi.id_pemasukan = fp.id
            WHERE fpi.id_infus=i.id AND fp.`status`=1 
            ORDER BY fpi.id DESC
            LIMIT 1)) dosis_ca_terakhir
        , i.oleh
        , i.`status`
        , i.created_at
      FROM `keperawatan`.`tb_infus` `i` 
      LEFT JOIN inventory.barang b ON i.id_cairan= b.ID
      WHERE `i`.`STATUS` != '0' AND `i`.`nokun` = '$nokun' 
      ORDER BY `i`.`created_at` DESC
    ");
    return $query->result();
  }

  public function get_cairan()
  {
      $this->db->select('*');
      $this->db->from('inventory.barang b');
      $this->db->where_in('b.KATEGORI',array('10103', '101002', '10102', '10115','10109','10113'));
      $this->db->where('b.`STATUS` = 1');
      // $this->db->limit(20);
      if($this->input->get('q')){
          $this->db->like(' b.NAMA', $this->input->get('q'));
      }
      
      $query  = $this->db->get();
      return $query->result();
  }

  public function get_darah()
  {
      $this->db->select('*');
      $this->db->from('db_master.tb_darah d');
      if($this->input->get('q')){
          $this->db->like(' d.nama', $this->input->get('q'));
      }
      
      $query  = $this->db->get();
      return $query->result();
  }

  public function get_pemasukan_masuk()
  {
    $nokun = $this->input->post('nokun');
    $query = $this->db->query("SELECT i.id id, i.nokun, i.id_flosheet, b.nama, i.dosis dosis_awal
        , if((SELECT fpi.ca 
            FROM keperawatan.tb_flosheet_pemasukan fp
              LEFT JOIN keperawatan.tb_flosheet_pemasukan_masuk fpi ON fpi.id_pemasukan = fp.id
            WHERE fpi.id_masuk=i.id AND fp.`status`=1 
            ORDER BY fpi.id DESC
            LIMIT 1) IS NULL , i.dosis , (SELECT fpi.ca 
            FROM keperawatan.tb_flosheet_pemasukan fp
              LEFT JOIN keperawatan.tb_flosheet_pemasukan_masuk fpi ON fpi.id_pemasukan = fp.id
            WHERE fpi.id_masuk=i.id AND fp.`status`=1 
            ORDER BY fpi.id DESC
            LIMIT 1)) dosis_ca_terakhir
        , i.oleh
        , i.`status`
        , i.created_at
      FROM `keperawatan`.tb_flosheet_darah i
      LEFT JOIN db_master.tb_darah b ON i.id_darah = b.id 
      WHERE `i`.`STATUS` != '0' AND `i`.`nokun` = '$nokun' 
      ORDER BY `i`.`created_at` DESC
    ");
    return $query->result();
  }

  public function tableMasalah($idFlosheet)
  {
    $query = $this->db->query("
      SELECT m.`*`, master.getNamaLengkapPegawai(d.NIP) nama_dokter 
      FROM keperawatan.tb_flosheet_masalah m
        LEFT JOIN master.dokter d ON m.dokter = d.ID
      WHERE m.id_flosheet = '$idFlosheet' ORDER BY m.created_at DESC
    ");
    return $query->result_array();
  }

  public function getDetailMasalah($id_masalah)
  {
    $query = $this->db->query("SELECT m.`*` FROM keperawatan.tb_flosheet_masalah m WHERE m.id = '$id_masalah'");
    return $query->row_array();
  }

  public function tableITK($idFlosheet)
  {
    $query = $this->db->query("SELECT itk.`*` FROM keperawatan.tb_flosheet_itk itk WHERE itk.id_flosheet = '$idFlosheet' ORDER BY itk.created_at DESC
    ");
    return $query->result_array();
  }

  public function getDetailITK($id_itk) 
  {
    $query = $this->db->query("SELECT itk.`*` FROM keperawatan.tb_flosheet_itk itk WHERE itk.id = '$id_itk'");
    return $query->row_array();
  }

  public function tablePengeluaran($idFlosheet)
  {
    $query = $this->db->query("SELECT p.id, p.jam, p.urine, p.urine_per, p.ngt, p.ngt_per, p.bab, p.bab_volume, p.bab_per
    , (p.urine+p.ngt+p.bab_volume) total, (p.urine_per+p.ngt_per+p.bab_per) total_per
        FROM keperawatan.tb_flosheet_pengeluaran p 
        WHERE p.id_flosheet = '$idFlosheet' AND p.status = 1 ORDER BY p.created_at ASC");
    return $query->result_array();
  }

  public function tablePengeluaranDrain($idFlosheet)
  {
    $query = $this->db->query("SELECT p.id, d.id id_drain, d.drain, pd.volume, pd.volume_per FROM keperawatan.tb_flosheet_pengeluaran_drain pd
    LEFT JOIN keperawatan.tb_flosheet_pengeluaran p ON pd.id_pengeluaran = p.id
    LEFT JOIN keperawatan.tb_flosheet_drain d ON pd.id_drain = d.id
    WHERE p.id_flosheet = '$idFlosheet' AND d.status=1 AND p.`status` = 1 AND pd.status = 1
    ORDER BY p.id ASC, pd.id ASC");
    return $query->result_array();
  }

  public function getDrain($idFlosheet)
  {
  	$query = $this->db->query("SELECT * FROM keperawatan.tb_flosheet_drain d
    WHERE d.id_flosheet = '$idFlosheet' AND d.status = 1");
  	return $query->result_array();
  }

  public function getTotalDrain($idFlosheet)
  {
    $query = $this->db->query("SELECT p.id, SUM(pd.volume) total_volume, SUM(pd.volume_per) total_volume_per FROM keperawatan.tb_flosheet_pengeluaran_drain pd
                                  LEFT JOIN keperawatan.tb_flosheet_pengeluaran p ON pd.id_pengeluaran = p.id
                                  LEFT JOIN keperawatan.tb_flosheet_drain d ON pd.id_drain = d.id
                                  WHERE p.id_flosheet = '$idFlosheet' AND p.`status` = 1 AND d.status = 1 AND pd.status = 1
                                  GROUP BY p.id order by p.id ASC");
  	return $query->result_array();
  }

  public function totalPengeluaran($idFlosheet)
  {
    $query = $this->db->query("SELECT SUM(pd.volume) total_drain, SUM(pd.volume_per) total_drain_per
    , (SELECT SUM(peng.urine+peng.ngt+peng.bab_volume) total
        FROM keperawatan.tb_flosheet_pengeluaran peng
        WHERE peng.id_flosheet = '$idFlosheet' AND peng.status = 1 GROUP BY peng.id_flosheet) total
    , (SELECT SUM(peng.urine_per+peng.ngt_per+peng.bab_per) total_per
        FROM keperawatan.tb_flosheet_pengeluaran peng
        WHERE peng.id_flosheet = '$idFlosheet' AND peng.status = 1 GROUP BY peng.id_flosheet) total_per 
       
    FROM keperawatan.tb_flosheet_pengeluaran_drain pd
        LEFT JOIN keperawatan.tb_flosheet_pengeluaran p ON pd.id_pengeluaran = p.id
        LEFT JOIN keperawatan.tb_flosheet_drain d ON pd.id_drain = d.id
        WHERE p.id_flosheet = '$idFlosheet' AND p.`status` = 1 AND pd.status = 1 AND d.status = 1");
    return $query->row_array();
  }

  // get total urine
  public function totalUrine($idFlosheet)
  {
    $query = $this->db->query("SELECT urine_per
    FROM keperawatan.tb_flosheet_pengeluaran p
    WHERE p.id_flosheet = '$idFlosheet' AND p.status = 1 order by p.id desc limit 1");
    return $query->row_array();
  }

  public function get_drain($idFlosheet)
  {
    $query = $this->db->query("SELECT * FROM keperawatan.tb_flosheet_drain d WHERE d.id_flosheet = '$idFlosheet' AND d.status = 1");
    return $query->result();
  }

  public function get_last_drain($idFlosheet)
  {
    // $query = $this->db->query("SELECT pd.id_drain, pd.volume, pd.volume_per, d.drain FROM keperawatan.tb_flosheet_pengeluaran_drain pd
    // LEFT JOIN keperawatan.tb_flosheet_drain d ON pd.id_drain = d.id
    // WHERE pd.id_pengeluaran = (
    //   SELECT p.id FROM keperawatan.tb_flosheet_pengeluaran p
    //   WHERE p.id_flosheet = '$idFlosheet' AND p.`status` = 1 AND pd.status = 1 AND d.status = 1
    //   ORDER BY p.id DESC LIMIT 1)");

    // $query = $this->db->query("SELECT drain.id id_drain, dra.volume, dra.volume_per, drain.drain 
    //   FROM keperawatan.tb_flosheet_pengeluaran_drain dra
    //     LEFT JOIN keperawatan.tb_flosheet_pengeluaran peng ON peng.id = dra.id_pengeluaran
    //     LEFT JOIN keperawatan.tb_flosheet_drain drain ON drain.id = dra.id_drain
    //   WHERE peng.id_flosheet='$idFlosheet' AND peng.`status`=1 AND dra.`status`=1 AND drain.`status`=1
    //   GROUP BY drain.id");

    $query = $this->db->query("SELECT drain.id id_drain, drain.drain drain
            , (SELECT dras.volume FROM keperawatan.tb_flosheet_pengeluaran_drain dras
              LEFT JOIN keperawatan.tb_flosheet_pengeluaran peng ON peng.id = dras.id_pengeluaran 
              WHERE dras.id_drain =  drain.id AND dras.`status`=1 AND peng.`status`=1
              ORDER BY dras.id DESC 
              LIMIT 1
              ) volume
            , (SELECT dras.volume_per FROM keperawatan.tb_flosheet_pengeluaran_drain dras
              LEFT JOIN keperawatan.tb_flosheet_pengeluaran peng ON peng.id = dras.id_pengeluaran 
              WHERE dras.id_drain =  drain.id AND dras.`status`=1 AND peng.`status`=1
              ORDER BY dras.id DESC 
              LIMIT 1
              ) volume_per
            , (SELECT dras.id FROM keperawatan.tb_flosheet_pengeluaran_drain dras
              LEFT JOIN keperawatan.tb_flosheet_pengeluaran peng ON peng.id = dras.id_pengeluaran 
              WHERE dras.id_drain =  drain.id AND dras.`status`=1 AND peng.`status`=1
              ORDER BY dras.id DESC 
              LIMIT 1
              ) id_pengeluaran_drain_terakhir
            , (SELECT peng.id FROM keperawatan.tb_flosheet_pengeluaran_drain dras
                LEFT JOIN keperawatan.tb_flosheet_pengeluaran peng ON peng.id = dras.id_pengeluaran 
              WHERE dras.id_drain =  drain.id AND dras.`status`=1 AND peng.`status`=1
              ORDER BY dras.id DESC 
              LIMIT 1
              ) id_pengeluaran_terakhir
          
          FROM keperawatan.tb_flosheet_drain drain
          WHERE drain.id_flosheet='$idFlosheet' AND drain.`status`=1
          
          GROUP BY drain.id");

    return $query->result();
  }

  public function getLastPengeluaran($idFlosheet)
  {
    $query = $this->db->query("SELECT p.*, SUM(p.ngt_per) ngt_total_per, SUM(p.ngt) ngt_total, SUM(p.urine) urine_total, SUM(p.urine_per) urine_total_per, SUM(p.bab_volume) bab_total FROM keperawatan.tb_flosheet_pengeluaran p
                              WHERE p.id_flosheet = '$idFlosheet' AND p.status = 1
                              ORDER BY p.jam DESC
                              LIMIT 1
    ");
    return $query->row_array();
    
  }

  public function tablePemasukan($idflosheet)
  {
  	$query = $this->db->query("SELECT p.NORM, master.getNamaLengkap(p.NORM) PASIEN
    , pm.`*`
    , CONCAT(pm.tanggal,' ',pm.pukul) TANGGAL
    , keperawatan.getDarahCM(pm.id) DARAH_CM
    , keperawatan.getDarahCA(pm.id) DARAH_CA
      
    , keperawatan.getPlasmaExpanderCM(pm.id) PLASMA_EXP_CM
    , keperawatan.getPlasmaExpanderCA(pm.id) PLASMA_EXP_CA
      
    , keperawatan.getAlbuminCM(pm.id) ALBUMIN_CM
    , keperawatan.getAlbuminCA(pm.id) ALBUMIN_CA
     
    , ora.cm ORAL_CM
    , ora.ca ORAL_CA
    
    , ngt.cm NGT_CM
    , ngt.ca NGT_CA

    FROM keperawatan.tb_flosheet_pemasukan pm
      
      LEFT JOIN keperawatan.tb_flosheet flo ON flo.id = pm.id_flosheet
      LEFT JOIN keperawatan.tb_flosheet_pemasukan_oral ora ON ora.id_pemasukan = pm.id
      LEFT JOIN keperawatan.tb_flosheet_pemasukan_ngt ngt ON ngt.id_pemasukan = pm.id
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = pm.nokun
      LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
      
    WHERE pm.id_flosheet=$idflosheet AND pm.`status`=1 ORDER BY tanggal, pukul ASC");
  	return $query->result_array();
  }

  public function tableLaboratorium($nokun)
  {
  	$query = $this->db->query("SELECT penda.NORM NO_MR
      , ruanzz.DESKRIPSI RUANGAN_ORDER
      , orlab.NOMOR NOMOR_ORDER
      , orlab.TANGGAL TANGGAL_ORDER 
      , kunju.NOMOR NOKUN_ORDER
      , kunjulab.MASUK MASUK_LAB 
      , kunjulab.NOMOR NOKUN_LAB
      
      FROM layanan.order_lab orlab
      LEFT JOIN pendaftaran.kunjungan kunju ON kunju.NOMOR = orlab.KUNJUNGAN
      LEFT JOIN pendaftaran.pendaftaran penda ON penda.NOMOR = kunju.NOPEN
      LEFT JOIN pendaftaran.kunjungan kunjulab ON kunjulab.REF = orlab.NOMOR AND penda.NOMOR = kunju.NOPEN
      LEFT JOIN master.ruangan ruanzz ON ruanzz.ID = kunju.RUANGAN
    WHERE orlab.`STATUS`= 2 AND kunju.NOMOR='$nokun'
    GROUP BY orlab.NOMOR");
  	return $query->result_array();
  }

  public function getPemeriksaanLab($nokun)
  {
  	$query = $this->db->query("SELECT orla.KUNJUNGAN NOKUN_ORDER, p.NORM,CONCAT(mt.NAMA,' - ',hlo.LIS_NAMA_TEST) PARAMETER, hlo.LIS_NAMA_TEST PARAMETER_DESC, p.NOMOR NOPEN
    , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        FROM lis.hasil_log hlo
          LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
          LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
          LEFT JOIN layanan.order_lab orla ON orla.NOMOR=pendK.REF
          LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pendK.NOPEN
        WHERE orla.KUNJUNGAN='$nokun'
        GROUP BY mt.ID, hlo.LIS_NAMA_TEST");
  	return $query->result_array();
  }

  public function getHasilLab($nokun)
  {
  	$query = $this->db->query("SELECT a.NORM, master.getNamaLengkap(a.NORM) NAMA
    , a.NOPEN, orlaba.NOKUN_ORDER, orlaba.RUANGAN_ORDER
    , orlaba.NOMOR_ORDER, orlaba.TANGGAL_ORDER
    , orlaba.NOKUN_LAB, orlaba.MASUK_LAB
    , a.TINDAKAN_SIMPEL
    , a.ID_TINDAKAN_SIMPEL
	 , CONCAT(a.TINDAKAN_SIMPEL, ' - ',a.PARAMETER) PARAMETER 
	 , (SELECT hlo.LIS_HASIL
      FROM lis.hasil_log hlo
        LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
        LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
        WHERE hlo.HIS_NO_LAB=orlaba.NOKUN_LAB AND hlo.LIS_NAMA_TEST=a.PARAMETER
		  	AND hlo.HIS_KODE_TEST=a.ID_TINDAKAN_SIMPEL
			  ) HASIL
    
    FROM 
    (SELECT orla.KUNJUNGAN NOKUN_ORDER, p.NORM, hlo.LIS_NAMA_TEST PARAMETER, p.NOMOR NOPEN
	 	, mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
      FROM lis.hasil_log hlo
        LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOMOR = hlo.HIS_NO_LAB
        LEFT JOIN master.tindakan mt ON mt.ID = hlo.HIS_KODE_TEST
        LEFT JOIN layanan.order_lab orla ON orla.NOMOR=pendK.REF
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pendK.NOPEN
      WHERE orla.KUNJUNGAN='$nokun'
      GROUP BY mt.ID, hlo.LIS_NAMA_TEST) a
  
      LEFT JOIN (SELECT penda.NORM NO_MR, orlab.NOMOR NOMOR_ORDER, kunju.NOMOR NOKUN_ORDER
              , kunjulab.NOMOR NOKUN_LAB, kunjulab.MASUK MASUK_LAB 
              , orlab.TANGGAL TANGGAL_ORDER 
              , ruanzz.DESKRIPSI RUANGAN_ORDER
              FROM layanan.order_lab orlab
              LEFT JOIN pendaftaran.kunjungan kunju ON kunju.NOMOR = orlab.KUNJUNGAN
              LEFT JOIN pendaftaran.pendaftaran penda ON penda.NOMOR = kunju.NOPEN
              LEFT JOIN pendaftaran.kunjungan kunjulab ON kunjulab.REF = orlab.NOMOR AND penda.NOMOR = kunju.NOPEN
              LEFT JOIN master.ruangan ruanzz ON ruanzz.ID = kunju.RUANGAN
            WHERE orlab.`STATUS`=2 AND kunju.NOMOR='$nokun'
            GROUP BY orlab.NOMOR) orlaba ON orlaba.NO_MR = a.NORM
            ORDER BY orlaba.TANGGAL_ORDER ASC, a.PARAMETER ASC");
  	return $query->result_array();
  }

  public function getInfus($idflosheet)
  {
  	$query = $this->db->query("SELECT i.id ID, CONCAT(b.NAMA,' (',i.dosis,')') INFUS
    FROM keperawatan.tb_infus i
    LEFT JOIN inventory.barang b ON i.id_cairan= b.ID
    WHERE i.id_flosheet = $idflosheet");
  	return $query->result_array();
  }

  public function getIsValueInfus($nokun)
  {
  	$query = $this->db->query("SELECT i.id ID, CONCAT(b.NAMA,' (',i.dosis,')') INFUS
    FROM keperawatan.tb_infus i
    LEFT JOIN inventory.barang b ON i.id_cairan= b.ID
    LEFT JOIN keperawatan.tb_flosheet_pemasukan_infus p ON i.id = p.id_infus
    LEFT JOIN keperawatan.tb_flosheet_pemasukan fp ON fp.id = p.id_pemasukan
    WHERE i.nokun = '$nokun' AND fp.`status` != 0 AND p.id IS NOT NULL GROUP BY i.id");
  	return $query->result_array();
  }


  public function getDarah($nokun)
  {
  	$query = $this->db->query("SELECT i.id ID, CONCAT(b.nama,' (',i.dosis,')') DARAH
    FROM keperawatan.tb_flosheet_darah i
    LEFT JOIN db_master.tb_darah b ON i.id_darah= b.ID
    WHERE i.nokun = $nokun");
  	return $query->result_array();
  }

  public function getPemasukanInfus($idflosheet)
  {
      $query = $this->db->query("SELECT flo.id ID_FLOSHEET , pem.id , pem.nokun, pem.pukul, pem.created_at
      ,inf.id id_infus, CONCAT(b.NAMA, ' (',inf.dosis,')') infus
        , (SELECT pfi.cm 
          FROM keperawatan.tb_flosheet_pemasukan_infus pfi 
          WHERE pfi.id_pemasukan=pem.id AND pfi.id_infus=inf.id LIMIT 1) cm
        , (SELECT pfi.ca
          FROM keperawatan.tb_flosheet_pemasukan_infus pfi 
          WHERE pfi.id_pemasukan=pem.id AND pfi.id_infus=inf.id LIMIT 1) ca
      
        FROM keperawatan.tb_flosheet flo
        
          LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
          LEFT JOIN keperawatan.tb_infus inf ON inf.nokun = pem.nokun
          LEFT JOIN inventory.barang b ON inf.id_cairan= b.ID
        WHERE flo.id=$idflosheet AND pem.`status`=1
        
        ORDER BY pem.tanggal, pem.pukul ASC
    ");
  	return $query->result_array();
  }

  public function getPemasukanDarah($idflosheet)
  {
      $query = $this->db->query("SELECT flo.id ID_FLOSHEET , pem.id , pem.nokun, pem.pukul, pem.created_at
      ,inf.id id_masuk, CONCAT(b.NAMA, ' (',inf.dosis,')') infus
        , (SELECT pfi.cm 
          FROM keperawatan.tb_flosheet_pemasukan_masuk pfi 
          WHERE pfi.id_pemasukan=pem.id AND pfi.id_masuk=inf.id LIMIT 1) cm
        , (SELECT pfi.ca
          FROM keperawatan.tb_flosheet_pemasukan_masuk pfi 
          WHERE pfi.id_pemasukan=pem.id AND pfi.id_masuk=inf.id LIMIT 1) ca
      
        FROM keperawatan.tb_flosheet flo
        
          LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
          LEFT JOIN keperawatan.tb_flosheet_darah inf ON inf.id_flosheet = pem.id_flosheet
          LEFT JOIN db_master.tb_darah b ON inf.id_darah= b.id
        WHERE flo.id=$idflosheet AND pem.`status`=1
        
        ORDER BY pem.tanggal, pem.pukul ASC
    ");
  	return $query->result_array();
  }

  public function getKumulatifInfus($idflosheet)
  {
      $query = $this->db->query("SELECT flo.id ID_FLOSHEET , pem.id ID_PEMASUKAN, pem.nokun, pem.pukul, pem.created_at 
      , (SELECT SUM(p.cm) cm 
          FROM keperawatan.tb_infus i
          JOIN keperawatan.tb_flosheet_pemasukan_infus p ON i.id = p.id_infus
          JOIN keperawatan.tb_flosheet_pemasukan pemz ON p.id_pemasukan = pemz.id
          WHERE i.nokun = flo.nokun AND pemz.id=pem.id AND pem.`status` != 0 ) cm
      , (SELECT SUM(p.ca) ca
          FROM keperawatan.tb_infus i
          JOIN keperawatan.tb_flosheet_pemasukan_infus p ON i.id = p.id_infus
          JOIN keperawatan.tb_flosheet_pemasukan pemz ON p.id_pemasukan = pemz.id
          WHERE i.nokun = flo.nokun AND pemz.id=pem.id AND pem.`status` != 0) ca
    FROM keperawatan.tb_flosheet flo
    
      LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
      
    WHERE flo.id=$idflosheet AND pem.`status`=1
    
    ORDER BY pem.tanggal, pem.pukul ASC
    ");
  	return $query->result_array();
  }

  public function getKumulatifMasuk($idflosheet)
  {
      $query = $this->db->query("SELECT flo.id ID_FLOSHEET , pem.id ID_PEMASUKAN, pem.nokun, pem.pukul, pem.created_at 
      , (SELECT SUM(pm.cm) cm
          FROM keperawatan.tb_flosheet_pemasukan_masuk pm
          JOIN keperawatan.tb_flosheet_pemasukan p ON pm.id_pemasukan = p.id
          WHERE p.id_flosheet=flo.id AND p.id=pem.id AND pem.`status` != 0) cm
      , (SELECT SUM(pm.ca) ca
          FROM keperawatan.tb_flosheet_pemasukan_masuk pm
          JOIN keperawatan.tb_flosheet_pemasukan p ON pm.id_pemasukan = p.id
          WHERE p.id_flosheet=flo.id AND p.id=pem.id AND pem.`status` != 0) ca
    FROM keperawatan.tb_flosheet flo
    
      LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
      
    WHERE flo.id=$idflosheet AND pem.`status`=1
    
    ORDER BY pem.tanggal, pem.pukul ASC
    ");
  	return $query->result_array();
  }

  public function getKumulatifOral($idflosheet)
  {
      $query = $this->db->query("SELECT flo.id ID_FLOSHEET , pem.id ID_PEMASUKAN, pem.nokun, pem.pukul, pem.created_at 
      , (SELECT IF(SUM(pm.cm) IS NULL, 0, SUM(pm.cm))+IF(SUM(ng.cm) IS NULL, 0, SUM(ng.cm))
        FROM keperawatan.tb_flosheet_pemasukan p
          LEFT JOIN keperawatan.tb_flosheet_pemasukan_oral pm ON pm.id_pemasukan = p.id
          LEFT JOIN keperawatan.tb_flosheet_pemasukan_ngt ng ON ng.id_pemasukan = p.id
        WHERE p.id_flosheet=flo.id AND p.id=pem.id AND p.`status`!=0) cm
      
    FROM keperawatan.tb_flosheet flo
    
      LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
      
    WHERE flo.id=$idflosheet AND pem.`status`=1
    
    ORDER BY pem.tanggal, pem.pukul ASC
    ");
  	return $query->result_array();
  }

  public function getKumulatif($idflosheet)
  {
      $query = $this->db->query("SELECT flo.id ID_FLOSHEET , pem.id ID_PEMASUKAN, pem.nokun, pem.pukul, pem.created_at 
      , (SELECT IF(SUM(p.cm) IS NULL, 0,SUM(p.cm)) cm# SUM(p.ca) ca
          FROM keperawatan.tb_infus i
          JOIN keperawatan.tb_flosheet_pemasukan_infus p ON i.id = p.id_infus
          JOIN keperawatan.tb_flosheet_pemasukan pemz ON p.id_pemasukan = pemz.id
          WHERE i.id_flosheet = flo.id AND pemz.id=pem.id AND pem.`status` != 0) 
        + (SELECT IF(SUM(pm.cm) IS NULL,0,SUM(pm.cm)) cm
          FROM keperawatan.tb_flosheet_pemasukan_masuk pm
          JOIN keperawatan.tb_flosheet_pemasukan p ON pm.id_pemasukan = p.id
          WHERE p.id_flosheet=flo.id AND p.id=pem.id AND pem.`status` != 0) 
        + (SELECT IF(SUM(pm.cm) IS NULL, 0, SUM(pm.cm))+IF(SUM(ng.cm) IS NULL, 0, SUM(ng.cm))
        FROM keperawatan.tb_flosheet_pemasukan p
          LEFT JOIN keperawatan.tb_flosheet_pemasukan_oral pm ON pm.id_pemasukan = p.id
          LEFT JOIN keperawatan.tb_flosheet_pemasukan_ngt ng ON ng.id_pemasukan = p.id
        WHERE p.id_flosheet=flo.id AND p.id=pem.id AND p.`status`!=0) cm
      
    FROM keperawatan.tb_flosheet flo
    
      LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
      
    WHERE flo.id=$idflosheet AND pem.`status`=1
    
    ORDER BY pem.tanggal, pem.pukul ASC
    ");
  	return $query->result_array();
  }

  public function getKumulatif3Jam($idflosheet)
  {
      $query = $this->db->query("SELECT a.ID_FLOSHEET#, a.ID_PEMASUKAN
      , a.NOKUN
      , SUM(IF(a.pukul BETWEEN '06:00:00' AND '09:00:00',a.cm,0)) jam6sampai9 
      , SUM(IF(a.pukul BETWEEN '09:00:01' AND '12:00:00',a.cm,0)) jam9sampai12 
      , SUM(IF(a.pukul BETWEEN '12:00:01' AND '15:00:00',a.cm,0)) jam12sampai15
      , SUM(IF(a.pukul BETWEEN '15:00:01' AND '18:00:00',a.cm,0)) jam15sampai18
      , SUM(IF(a.pukul BETWEEN '18:00:01' AND '21:00:00',a.cm,0)) jam18sampai21
      , SUM(IF(a.pukul BETWEEN '21:00:01' AND '23:59:59',a.cm,0)) jam21sampai00malam
      , SUM(IF(a.pukul BETWEEN '00:00:01' AND '03:00:00',a.cm,0)) jam00sampai3subuh
      , SUM(IF(a.pukul BETWEEN '00:03:01' AND '06:00:00',a.cm,0)) jam03sampa06subuh
      
    FROM 
    (SELECT flo.id ID_FLOSHEET , pem.id ID_PEMASUKAN, pem.nokun, pem.pukul, pem.created_at 
       , (SELECT SUM(p.cm) cm 
              FROM keperawatan.tb_infus i
              JOIN keperawatan.tb_flosheet_pemasukan_infus p ON i.id = p.id_infus
              JOIN keperawatan.tb_flosheet_pemasukan pemz ON p.id_pemasukan = pemz.id
              WHERE i.nokun = flo.nokun AND pemz.id=pem.id AND pem.`status` != 0 ) cm
        
    FROM keperawatan.tb_flosheet flo
        LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
    WHERE flo.id=$idflosheet AND pem.`status`=1
    
    UNION ALL
    
    SELECT flo.id ID_FLOSHEET , pem.id ID_PEMASUKAN, pem.nokun, pem.pukul, pem.created_at 
      , (SELECT SUM(pm.cm) cm
              FROM keperawatan.tb_flosheet_pemasukan_masuk pm
              JOIN keperawatan.tb_flosheet_pemasukan p ON pm.id_pemasukan = p.id
              WHERE p.nokun=flo.nokun AND p.id=pem.id AND pem.`status` != 0) cm
          
    FROM keperawatan.tb_flosheet flo
        LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
    WHERE flo.id=$idflosheet AND pem.`status`=1
    
    UNION ALL
    
    SELECT flo.id ID_FLOSHEET , pem.id ID_PEMASUKAN, pem.nokun, pem.pukul, pem.created_at 
       , (SELECT IF(SUM(pm.cm) IS NULL, 0, SUM(pm.cm))+IF(SUM(ng.cm) IS NULL, 0, SUM(ng.cm))
            FROM keperawatan.tb_flosheet_pemasukan p
              LEFT JOIN keperawatan.tb_flosheet_pemasukan_oral pm ON pm.id_pemasukan = p.id
              LEFT JOIN keperawatan.tb_flosheet_pemasukan_ngt ng ON ng.id_pemasukan = p.id
            WHERE p.nokun=flo.nokun AND p.id=pem.id AND p.`status`!=0) cm
          
    FROM keperawatan.tb_flosheet flo
       LEFT JOIN keperawatan.tb_flosheet_pemasukan pem ON pem.id_flosheet = flo.id
    WHERE flo.id=$idflosheet AND pem.`status`=1) a    
    ");
  	return $query->result_array();
  }


  public function tablepengkajianFisik($idFlosheet)
  {
    $query = $this->db->query("SELECT itk.`*` FROM keperawatan.tb_flosheet_itk itk WHERE itk.id_flosheet = '$idFlosheet' ORDER BY itk.created_at DESC
    ");
    return $query->result_array();
  }

  public function gambarFisik()
    {
        $query = $this->db->query("SELECT * FROM master.tb_gambar_soap");

        return $query->result_array();
    }

    public function getGambar($idGambar)
    {
        $query = $this->db->query("SELECT file FROM master.tb_gambar_soap WHERE id= '$idGambar'");
        return $query->row_array();
    }

    public function historyPengkajianFisik($idFlosheet)
    {
      $query = $this->db->query("SELECT fpf.id ID, fpf.nokun NOKUN, fpf.id_flosheet ID_FLOSHEET, fpf.judul JUDUL
                            , fpf.created_at TANGGAL
        FROM keperawatan.tb_flosheet_pengkajian_fisik fpf
        WHERE fpf.`status_pengkajian_fisik` = 1 AND fpf.id_flosheet = '$idFlosheet'
        ORDER BY fpf.created_at DESC");

      return $query->result_array();
    }

    public function hasilFotoPengkajianFisik($id)
    {
        $query = $this->db->query(
            "SELECT *
            FROM keperawatan.tb_flosheet_pengkajian_fisik fpf
            WHERE fpf.ID = '$id' AND fpf.`status_pengkajian_fisik` = 1"
        );
        return $query->row_array();
    }
}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
