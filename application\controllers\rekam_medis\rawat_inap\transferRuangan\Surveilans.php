<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Surveilans extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/MedisModel','rekam_medis/rawat_inap/transferRuangan/SurveilansModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_surveilans = $this->uri->segment(3);
      $pasien = $this->MedisModel->getNomrRawatInap($nokun);
      $getPengkajian = $this->SurveilansModel->getPengkajian($id_surveilans);
      $getPengkajianOperasi = $this->SurveilansModel->getPengkajianOperasi($id_surveilans);
      $getPengkajianAntibiotika = $this->SurveilansModel->getPengkajianAntibiotika($id_surveilans);
      
      $data = array(
        'nokun' => $nokun,
        'id_surveilans' => $id_surveilans,
        'listRuangan' => $this->masterModel->ruanganRawatInap(),
        'getPengkajian' => $getPengkajian,
        'getPengkajianOperasi' => $getPengkajianOperasi,
        'getPengkajianAntibiotika' => $getPengkajianAntibiotika,
        'pasien' => $pasien,
        'listTanggalPindah' => $this->masterModel->referensi(1696),
        'listPetandaInfeksi' => $this->masterModel->referensi(1697),
        'listKasusBedah' => $this->masterModel->referensi(1698),
        'listDrain' => $this->masterModel->referensi(1699),
        'listAsaScore' => $this->masterModel->referensi(1700),
        'listJenisOperasi' => $this->masterModel->referensi(1701),
        'listKategoriOperasi' => $this->masterModel->referensi(1702),
        'listAntibiotika' => $this->masterModel->referensi(1703),
        'listPemberianAntibiotika' => $this->masterModel->referensi(1704),
        'listTirah' => $this->masterModel->referensi(1705)
      );
      $this->load->view('rekam_medis/rawat_inap/transferRuangan/surveilans', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
          
          $data = array(
            'nokun' => $post['nokun'],
            'pindah_dari' => isset($post['pindah_dari']) ? $post['pindah_dari']: "",
            'status_tgl_pindah' => isset($post['status_tgl_pindah']) ? $post['status_tgl_pindah']: "",
            'tanggal_pindah' => !empty($post['tanggal_pindah']) ? $post['tanggal_pindah']: null,
            'diagnosa_masuk' => isset($post['diagnosa_masuk']) ? $post['diagnosa_masuk']: "",
            'ido_hari_ke' => isset($post['ido_hari_ke']) ? $post['ido_hari_ke']: "",
            'ido_tgl' => !empty($post['ido_tgl']) ? $post['ido_tgl']: null,
            'ido_hasil_kultur' => isset($post['ido_hasil_kultur']) ? $post['ido_hasil_kultur']: "",
            'iad_hari_ke' => isset($post['iad_hari_ke']) ? $post['iad_hari_ke']: "",
            'iad_tgl' => !empty($post['iad_tgl']) ? $post['iad_tgl']: null,
            'iad_hasil_kultur' => isset($post['iad_hasil_kultur']) ? $post['iad_hasil_kultur']: "",
            'isk_hari_ke' => isset($post['isk_hari_ke']) ? $post['isk_hari_ke']: "",
            'isk_tgl' => !empty($post['isk_tgl']) ? $post['isk_tgl']: null,
            'isk_hasil_kultur' => isset($post['isk_hasil_kultur']) ? $post['isk_hasil_kultur']: "",
            'hap_hari_ke' => isset($post['hap_hari_ke']) ? $post['hap_hari_ke']: "",
            'hap_tgl' => !empty($post['hap_tgl']) ? $post['hap_tgl']: null,
            'hap_hasil_kultur' => isset($post['hap_hasil_kultur']) ? $post['hap_hasil_kultur']: "",
            'vap_hari_ke' => isset($post['vap_hari_ke']) ? $post['vap_hari_ke']: "",
            'vap_tgl' => !empty($post['vap_tgl']) ? $post['vap_tgl']: null,
            'vap_hasil_kultur' => isset($post['vap_hasil_kultur']) ? $post['vap_hasil_kultur']: "",
            'dekubitus_hari_ke' => isset($post['dekubitus_hari_ke']) ? $post['dekubitus_hari_ke']: "",
            'dekubitus_tgl' => !empty($post['dekubitus_tgl']) ? $post['dekubitus_tgl']: null,
            'dekubitus_hasil_kultur' => isset($post['dekubitus_hasil_kultur']) ? $post['dekubitus_hasil_kultur']: "",
            'plebitis_hari_ke' => isset($post['plebitis_hari_ke']) ? $post['plebitis_hari_ke']: "",
            'plebitis_tgl' => !empty($post['plebitis_tgl']) ? $post['plebitis_tgl']:null,
            'plebitis_hasil_kultur' => isset($post['plebitis_hasil_kultur']) ? $post['plebitis_hasil_kultur']: "",
            'lain_hari_ke' => isset($post['lain_hari_ke']) ? $post['lain_hari_ke']: "",
            'lain_tgl' => !empty($post['lain_tgl']) ? $post['lain_tgl']: null,
            'lain_hasil_kultur' => isset($post['lain_hasil_kultur']) ? $post['lain_hasil_kultur']: "",
            'hbsag' => isset($post['hbsag']) ? $post['hbsag']: "",
            'anti_hcv' => isset($post['anti_hcv']) ? $post['anti_hcv']: "",
            'anti_hiv' => isset($post['anti_hiv']) ? $post['anti_hiv']: "",
            'kasus_bedah' => isset($post['kasus_bedah']) ? json_encode($post['kasus_bedah']): "",
            'diagnosis_operasi' => isset($post['diagnosis_operasi']) ? $post['diagnosis_operasi']: "",
            'lokasi_vena_sentral' => isset($post['lokasi_vena_sentral']) ? $post['lokasi_vena_sentral']: "",
            'mulai_vena_sentral' => !empty($post['mulai_vena_sentral']) ? $post['mulai_vena_sentral']: null,
            'sd_vena_sentral' => !empty($post['sd_vena_sentral']) ? $post['sd_vena_sentral']: null,
            'pemasangan_vena_sentral' => isset($post['pemasangan_vena_sentral']) ? $post['pemasangan_vena_sentral']: "",
            'infeksi_vena_sentral' => !empty($post['infeksi_vena_sentral']) ? $post['infeksi_vena_sentral']: null,
            'catatan_vena_sentral' => isset($post['catatan_vena_sentral']) ? $post['catatan_vena_sentral']: "",
            'lokasi_vena_perifer' => isset($post['lokasi_vena_perifer']) ? $post['lokasi_vena_perifer']: "",
            'mulai_vena_perifer' => !empty($post['mulai_vena_perifer']) ? $post['mulai_vena_perifer']: null,
            'sd_vena_perifer' => !empty($post['sd_vena_perifer']) ? $post['sd_vena_perifer']: null,
            'pemasangan_vena_perifer' => isset($post['pemasangan_vena_perifer']) ? $post['pemasangan_vena_perifer']: "",
            'infeksi_vena_perifer' => !empty($post['infeksi_vena_perifer']) ? $post['infeksi_vena_perifer']: null,
            'catatan_vena_perifer' => isset($post['catatan_vena_perifer']) ? $post['catatan_vena_perifer']: "",
            'lokasi_arteri' => isset($post['lokasi_arteri']) ? $post['lokasi_arteri']: "",
            'mulai_arteri' => !empty($post['mulai_arteri']) ? $post['mulai_arteri']: null,
            'sd_arteri' => !empty($post['sd_arteri']) ? $post['sd_arteri']: null,
            'pemasangan_arteri' => isset($post['pemasangan_arteri']) ? $post['pemasangan_arteri']: "",
            'infeksi_arteri' => !empty($post['infeksi_arteri']) ? $post['infeksi_arteri']: null,
            'catatan_arteri' => isset($post['catatan_arteri']) ? $post['catatan_arteri']: "",
            'lokasi_umbilical' => isset($post['lokasi_umbilical']) ? $post['lokasi_umbilical']: "",
            'mulai_umbilical' => !empty($post['mulai_umbilical']) ? $post['mulai_umbilical']: null,
            'sd_umbilical' => !empty($post['sd_umbilical']) ? $post['sd_umbilical']: null,
            'pemasangan_umbilical' => isset($post['pemasangan_umbilical']) ? $post['pemasangan_umbilical']: "",
            'infeksi_umbilical' => !empty($post['infeksi_umbilical']) ? $post['infeksi_umbilical']: null,
            'catatan_umbilical' => isset($post['catatan_umbilical']) ? $post['catatan_umbilical']: "",
            'lokasi_urin' => isset($post['lokasi_urin']) ? $post['lokasi_urin']: "",
            'mulai_urin' => !empty($post['mulai_urin']) ? $post['mulai_urin']: null,
            'sd_urin' => !empty($post['sd_urin']) ? $post['sd_urin']: null,
            'pemasangan_urin' => isset($post['pemasangan_urin']) ? $post['pemasangan_urin']: "",
            'infeksi_urin' => !empty($post['infeksi_urin']) ? $post['infeksi_urin']: null,
            'catatan_urin' => isset($post['catatan_urin']) ? $post['catatan_urin']: "",
            'lokasi_suprapublik' => isset($post['lokasi_suprapublik']) ? $post['lokasi_suprapublik']: "",
            'mulai_suprapublik' => !empty($post['mulai_suprapublik']) ? $post['mulai_suprapublik']: null,
            'sd_suprapublik' => !empty($post['sd_suprapublik']) ? $post['sd_suprapublik']: null,
            'pemasangan_suprapublik' => isset($post['pemasangan_suprapublik']) ? $post['pemasangan_suprapublik']: "",
            'infeksi_suprapublik' => !empty($post['infeksi_suprapublik']) ? $post['infeksi_suprapublik']: null,
            'catatan_suprapublik' => isset($post['catatan_suprapublik']) ? $post['catatan_suprapublik']: "",
            'lokasi_ventilasi_mekanik' => isset($post['lokasi_ventilasi_mekanik']) ? $post['lokasi_ventilasi_mekanik']: "",
            'mulai_ventilasi_mekanik' => !empty($post['mulai_ventilasi_mekanik']) ? $post['mulai_ventilasi_mekanik']: null,
            'sd_ventilasi_mekanik' => !empty($post['sd_ventilasi_mekanik']) ? $post['sd_ventilasi_mekanik']: null,
            'pemasangan_ventilasi_mekanik' => isset($post['pemasangan_ventilasi_mekanik']) ? $post['pemasangan_ventilasi_mekanik']: "",
            'infeksi_ventilasi_mekanik' => !empty($post['infeksi_ventilasi_mekanik']) ? $post['infeksi_ventilasi_mekanik']: null,
            'catatan_ventilasi_mekanik' => isset($post['catatan_ventilasi_mekanik']) ? $post['catatan_ventilasi_mekanik']: "",
            'lokasi_tube_endotrakeal' => isset($post['lokasi_tube_endotrakeal']) ? $post['lokasi_tube_endotrakeal']: "",
            'mulai_tube_endotrakeal' => !empty($post['mulai_tube_endotrakeal']) ? $post['mulai_tube_endotrakeal']: null,
            'sd_tube_endotrakeal' => !empty($post['sd_tube_endotrakeal']) ? $post['sd_tube_endotrakeal']: null,
            'pemasangan_tube_endotrakeal' => isset($post['pemasangan_tube_endotrakeal']) ? $post['pemasangan_tube_endotrakeal']: "",
            'infeksi_tube_endotrakeal' => !empty($post['infeksi_tube_endotrakeal']) ? $post['infeksi_tube_endotrakeal']: null,
            'catatan_tube_endotrakeal' => isset($post['catatan_tube_endotrakeal']) ? $post['catatan_tube_endotrakeal']: "",
            'lokasi_trakeostomi' => isset($post['lokasi_trakeostomi']) ? $post['lokasi_trakeostomi']: "",
            'mulai_trakeostomi' => !empty($post['mulai_trakeostomi']) ? $post['mulai_trakeostomi']: null,
            'sd_trakeostomi' => !empty($post['sd_trakeostomi']) ? $post['sd_trakeostomi']: null,
            'pemasangan_trakeostomi' => isset($post['pemasangan_trakeostomi']) ? $post['pemasangan_trakeostomi']: "",
            'infeksi_trakeostomi' => !empty($post['infeksi_trakeostomi']) ? $post['infeksi_trakeostomi']: null,
            'catatan_trakeostomi' => isset($post['catatan_trakeostomi']) ? $post['catatan_trakeostomi']: "",
            'lokasi_uretra' => isset($post['lokasi_uretra']) ? $post['lokasi_uretra']: "",
            'mulai_uretra' => !empty($post['mulai_uretra']) ? $post['mulai_uretra']: null,
            'sd_uretra' => !empty($post['sd_uretra']) ? $post['sd_uretra']: null,
            'pemasangan_uretra' => isset($post['pemasangan_uretra']) ? $post['pemasangan_uretra']: "",
            'infeksi_uretra' => !empty($post['infeksi_uretra']) ? $post['infeksi_uretra']: null,
            'catatan_uretra' => isset($post['catatan_uretra']) ? $post['catatan_uretra']: "",
            'pemakaian_antibiotika' => isset($post['pemakaian_antibiotika']) ? $post['pemakaian_antibiotika']: "",
            'tirah_baring' => isset($post['tirah_baring']) ? $post['tirah_baring']: "",
            'tanggal_keluar' => !empty($post['tanggal_keluar']) ? $post['tanggal_keluar']: null,
            'pindah_ke' => isset($post['pindah_ke']) ? $post['pindah_ke']: "",
            'diagnosa_akhir' => isset($post['diagnosa_akhir']) ? $post['diagnosa_akhir']: "",
            'infeksi' => isset($post['infeksi']) ? $post['infeksi']: "",
            'oleh' => $this->session->userdata('id')
          );

          // echo "<pre>".print_r($data)."</pre>";

          if (empty($post['id_surveilans'])) {
            $this->db->insert('keperawatan.tb_surveilans', $data);

            $getIdSurveilans = $this->db->insert_id();
          } else {
            $getIdSurveilans = $post['id_surveilans'];
          }

          $dataOperasi = array();
          $indexOperasi = 0;
          if (isset($post['tanggal_operasi'])) {
            foreach ($post['tanggal_operasi'] as $input) {
              if ($post['tanggal_operasi'][$indexOperasi] != "") {
                array_push(
                  $dataOperasi, array(
                    'id_surveilans' => $getIdSurveilans,
                    'tanggal_operasi' => $post['tanggal_operasi'][$indexOperasi],
                    'jam_operasi' => $post['jam_operasi'][$indexOperasi],
                    'menit_operasi' => $post['menit_operasi'][$indexOperasi],
                    'drain' => $post['drain'][$indexOperasi],
                    'asa_score' => $post['asa_score'][$indexOperasi],
                    'jenis_operasi' => $post['jenis_operasi'][$indexOperasi],
                    'kategori_operasi' => $post['kategori_operasi'][$indexOperasi],
                    'antibiotik_profilaksis' => $post['antibiotik_profilaksis'][$indexOperasi],
                    'dosis' => $post['dosis'][$indexOperasi]
                  )
                );
              }
              $indexOperasi++;
            }
          }

          // echo "<pre>".print_r($dataOperasi)."</pre>";

          $dataAntibiotika = array();
          $indexAntibiotika = 0;

          if (isset($post['nama_antibiotika'])) {
            foreach ($post['nama_antibiotika'] as $input) {
              if ($post['nama_antibiotika'][$indexAntibiotika] != "") {
                array_push(
                  $dataAntibiotika, array(
                    'id_surveilans' => $getIdSurveilans,
                    'nama_antibiotika' => $post['nama_antibiotika'][$indexAntibiotika],
                    'dosis_antibiotika' => $post['dosis_antibiotika'][$indexAntibiotika],
                    'tanggal_antibiotika1' => $post['tanggal_antibiotika1'][$indexAntibiotika],
                    'tanggal_antibiotika2' => $post['tanggal_antibiotika2'][$indexAntibiotika],
                    'pemberian_antibiotika' => $post['pemberian_antibiotika'][$indexAntibiotika]
                  )
                );
              }
              $indexAntibiotika++;
            }
          }

          $this->db->trans_begin();
        
          if (!empty($post['id_surveilans'])) {
            $this->db->where('keperawatan.tb_surveilans.id', $post['id_surveilans']);
            $this->db->update('keperawatan.tb_surveilans', $data);

            $this->db->delete('keperawatan.tb_surveilans_operasi', array('id_surveilans' => $post['id_surveilans']));
            foreach ($dataOperasi as $key => $value) {
              $this->db->replace('keperawatan.tb_surveilans_operasi', $value, 'id_surveilans');
            }

            $this->db->delete('keperawatan.tb_surveilans_antibiotika', array('id_surveilans' => $post['id_surveilans']));
            foreach ($dataAntibiotika as $key => $value) {
              $this->db->replace('keperawatan.tb_surveilans_antibiotika', $value, 'id_surveilans');
            }

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }
          else{
            if (isset($post['tanggal_operasi'])) {
              $this->db->insert_batch('keperawatan.tb_surveilans_operasi', $dataOperasi);
            }
            if (isset($post['nama_antibiotika'])) {
              $this->db->insert_batch('keperawatan.tb_surveilans_antibiotika', $dataAntibiotika);
            }
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->SurveilansModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->SurveilansModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editSurveilans" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
            $sub_array[] = $row -> nokun;
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->SurveilansModel->total_count(),
            "recordsFiltered"   => $this->SurveilansModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}