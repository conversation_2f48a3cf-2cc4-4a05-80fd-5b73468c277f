<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FesoModel extends MY_Model {

  public function simpanFeso($data)
  {
    $this->db->insert('db_layanan.tb_eso', $data);
    return $this->db->insert_id();
  }

  public function simpanFesoObat($data)
  {
    $this->db->insert('db_layanan.tb_eso_obat', $data);
  }

  public function simpanFesoForm($data)
  {
    $this->db->insert('db_layanan.tb_eso_form', $data);
  }

  public function batch_form($data){
		return $this->db->insert_batch('db_layanan.tb_eso_ceklist', $data);
	}

  public function batch_delete_form($id){
		$this->db->where('id_eso_form', $id);
		$this->db->delete('db_layanan.tb_eso_ceklist');
  }

  function listEso($norm)
    {
        // $id = $this->session->userdata('ses_id');
        $this->db->select("te.*,
                          pp.NORM nomr,
                          master.getNamaLengkapPegawai(ap.NIP) oleh_pengisi");
        $this->db->from('db_layanan.tb_eso te');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = te.nokun', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'LEFT');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = te.oleh', 'LEFT');
        $this->db->where('pp.NORM', $norm);
        $this->db->where('te.status', 1);
        $this->db->order_by('te.created_at', 'DESC');

        // $this->db->group_start();
        // $this->db->like('mb.KODE_SIMAK', $_POST['search']['value']);
        // $this->db->or_like("mb.NAMA_BARANG", $_POST['search']['value']);
        // $this->db->group_end();
    }

    function datatablesEso($norm){
      $this->listEso($norm);
      if($_POST["length"] != -1){
        $this->db->limit($_POST["length"], $_POST["start"]);
      }
      $query = $this->db->get();
      return $query->result();
    }

    function filter_count_eso($norm){
      $this->listEso($norm);
      $query = $this->db->get();
      return $query->num_rows();
    }

    function total_count_eso($norm){
      $this->listEso($norm);
      return $this->db->count_all_results();
    }
//-------------------------------------------------------------------------------------------------------------------
function listEsoObat($id)
    {
        // $id = $this->session->userdata('ses_id');
        $this->db->select("teo.*,
                          tef.id id_eso_form,
                          ib.ID id_obat,
                          ib.NAMA nama_obat,
                          refgen.DESKRIPSI nama_generik,
                          sedi.DESKRIPSI nama_sediaan,
                          te.nokun,
                          pp.NORM nomr,
                          master.getNamaLengkapPegawai(ap.NIP) oleh_pengisi");
        $this->db->from('db_layanan.tb_eso_obat teo');
        $this->db->join('db_layanan.tb_eso te', 'te.id = teo.id_eso', 'LEFT');
        $this->db->join('db_layanan.tb_eso_form tef', 'tef.id_eso_obat = teo.id', 'LEFT');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = te.nokun', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'LEFT');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = te.oleh', 'LEFT');
        $this->db->join('inventory.barang ib', 'ib.ID = teo.obat', 'LEFT');
        $this->db->join('master.referensi refgen', 'refgen.ID = ib.GENERIK AND refgen.JENIS = 42', 'LEFT');
        $this->db->join('master.referensi sedi', 'sedi.ID = ib.BENTUK_SEDIAAN AND sedi.JENIS = 118', 'LEFT');
        $this->db->where('teo.id_eso', $id);
        $this->db->where('teo.status', 1);
        $this->db->order_by('teo.created_at', 'DESC');

        // $this->db->group_start();
        // $this->db->like('mb.KODE_SIMAK', $_POST['search']['value']);
        // $this->db->or_like("mb.NAMA_BARANG", $_POST['search']['value']);
        // $this->db->group_end();
    }

    function datatablesEsoObat($id){
      $this->listEsoObat($id);
      if($_POST["length"] != -1){
        $this->db->limit($_POST["length"], $_POST["start"]);
      }
      $query = $this->db->get();
      return $query->result();
    }

    function filter_count_esoObat($id){
      $this->listEsoObat($id);
      $query = $this->db->get();
      return $query->num_rows();
    }

    function total_count_esoObat($id){
      $this->listEsoObat($id);
      return $this->db->count_all_results();
    }

    public function listObat()
    {
        if ($this->input->get('q')) {
            $this->db->like('ib.NAMA', $this->input->get('q'));
            $this->db->or_like("refgen.DESKRIPSI", $this->input->get('q'));
        }
        $this->db->select('ib.ID ID_OBAT,
                            ib.NAMA NAMA_OBAT,
                            refgen.DESKRIPSI GENERIK,
                            sedi.DESKRIPSI BENTUK_SEDIAAN');
        $this->db->from('inventory.barang ib');
        $this->db->join('master.referensi refgen', 'refgen.ID = ib.GENERIK AND refgen.JENIS = 42', 'left');
        $this->db->join('master.referensi sedi', 'sedi.ID = ib.BENTUK_SEDIAAN AND sedi.JENIS = 118', 'left');
        // $this->db->where('pengguna.ID', $saksi);
        $this->db->where('ib.STATUS', 1);
        $this->db->group_by('ib.ID');
        $this->db->limit(20);

        $query = $this->db->get();
        return $query;
    }

    function listObatPasien()
    {
      if ($this->input->get('q')) {
        $this->db->where('pk.NOPEN', $this->input->get('nopen'));
        $this->db->like('ib.NAMA', $this->input->get('q'));
        $this->db->or_like("refgen.DESKRIPSI", $this->input->get('q'));
      }
        $this->db->select('ib.ID ID_OBAT,
                            ib.NAMA NAMA_OBAT,
                            refgen.DESKRIPSI GENERIK,
                            sedi.DESKRIPSI BENTUK_SEDIAAN,
                            lf.TANGGAL');
        $this->db->from('layanan.farmasi lf');
        $this->db->join('inventory.barang ib', 'lf.FARMASI = ib.ID', 'left');
        $this->db->join('master.referensi refgen', 'refgen.ID = ib.GENERIK AND refgen.JENIS = 42', 'left');
        $this->db->join('master.referensi sedi', 'sedi.ID = ib.BENTUK_SEDIAAN AND sedi.JENIS = 118', 'left');
        $this->db->join('pendaftaran.kunjungan pk', 'lf.KUNJUNGAN = pk.NOMOR', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pk.NOPEN=pp.NOMOR', 'left');
        $this->db->join('pendaftaran.pendaftaran pmr', 'pp.NORM= pmr.NORM', 'left');
        // $this->db->where('pengguna.ID', $saksi);
        $this->db->where('lf.STATUS', 2);
        $this->db->where('pmr.NOMOR', $this->input->get('nopen'));
        $this->db->where('pk.STATUS IN ( 1, 2 )');
        $this->db->where('ib.KATEGORI != 10210');
        $this->db->group_by('ib.ID,lf.TANGGAL');
        $this->db->order_by('ib.NAMA');
        // $this->db->limit(20);

        $query = $this->db->get();
        // var_dump($_GET);
        // print_r($this->db->last_query());   
        return $query;
    }

    function listObatPasienDetail($nopen,$id)
    {
        if ($this->input->get('q')) {
            $this->db->like('ib.NAMA', $this->input->get('q'));
            $this->db->or_like("refgen.DESKRIPSI", $this->input->get('q'));
        }
        $this->db->select('ib.ID ID_OBAT,
                            ib.NAMA NAMA_OBAT,
                            refgen.DESKRIPSI GENERIK,
                            sedi.DESKRIPSI BENTUK_SEDIAAN,
                            lf.DOSIS,
	                          atpak.DESKRIPSI ATURAN_PAKAI');
        $this->db->from('layanan.farmasi lf');
        $this->db->join('inventory.barang ib', 'lf.FARMASI = ib.ID  AND `lf`.`STATUS` = 2 ', 'left');
        $this->db->join('master.referensi atpak', 'atpak.ID = lf.ATURAN_PAKAI AND atpak.JENIS = 41', 'left');
        $this->db->join('master.referensi refgen', 'refgen.ID = ib.GENERIK AND refgen.JENIS = 42', 'left');
        $this->db->join('master.referensi sedi', 'sedi.ID = ib.BENTUK_SEDIAAN AND sedi.JENIS = 118', 'left');
        $this->db->join('pendaftaran.kunjungan pk', 'lf.KUNJUNGAN = pk.NOMOR', 'left');
        // $this->db->where('pengguna.ID', $saksi);
        #$this->db->where('lf.STATUS', 2);
        #$this->db->where('pk.NOPEN', $nopen);
        $this->db->where('lf.FARMASI', $id);
        #$this->db->where('pk.STATUS IN ( 1, 2 )');
        #$this->db->where('ib.KATEGORI != 10210');
        $this->db->group_by('lf.FARMASI');
        #$this->db->order_by('ib.NAMA');
        // $this->db->limit(20);

        $query = $this->db->get();
        #print_r($this->db->last_query());  exit;
        return $query;
    }

    public function getEsoObat($id)
    {
      $query = $this->db->query("SELECT
                *
              FROM
                db_layanan.tb_eso_obat teo
              WHERE
                teo.id = $id AND teo.status = 1");
      return $query->row_array();
    }

    public function getEsoForm($id)
    {
      $query = $this->db->query("SELECT
                tef.*,
                teo.id id_obat_eso,
                teo.tanggal_mulai,
                teo.tanggal_stop,
                kt.id id_kecepatan,
                kt.deskripsi nama_kecepatan,
                kes.id id_kesudahan,
                kes.deskripsi nama_kesudahan,
                CASE
                  WHEN tef.obat_sebelumnya = 0 THEN 'Tidak Ada Data'
                  WHEN tef.obat_sebelumnya = 1 THEN 'Tidak'
                  WHEN tef.obat_sebelumnya = 2 THEN 'Ya'
                END nama_obat_sebelum,
                CASE
                  WHEN tef.reaksi = 0 THEN 'Tidak Ada Data'
                  WHEN tef.reaksi = 1 THEN 'Tidak'
                  WHEN tef.reaksi = 2 THEN 'Ya'
                END nama_reaksi
              FROM
                db_layanan.tb_eso_form tef
                LEFT JOIN db_layanan.tb_eso_obat teo ON teo.id = tef.id_eso_obat
                LEFT JOIN master.efek_samping_obat kt ON kt.id = tef.kecepatan_timbul AND kt.jenis = 3
                LEFT JOIN master.efek_samping_obat kes ON kes.id = tef.kesudahan AND kes.jenis = 4
              WHERE
                tef.id = $id AND tef.status = 1");
      return $query->row_array();
    }

    public function listKondisi()
    {
        if ($this->input->get('q')) {
            $this->db->like('eso.deskripsi', $this->input->get('q'));
        }
        $this->db->select('*');
        $this->db->from('master.efek_samping_obat eso');
        $this->db->where('eso.status', 1);
        $this->db->where('eso.jenis', 2);
        // $this->db->group_by('ib.ID');

        $query = $this->db->get();
        return $query;
    }

    public function listEfekSamping()
    {
        if ($this->input->get('q')) {
            $this->db->like('eso.deskripsi', $this->input->get('q'));
        }
        $this->db->select('*');
        $this->db->from('master.efek_samping_obat eso');
        $this->db->where('eso.status', 1);
        $this->db->where('eso.jenis', 1);
        // $this->db->group_by('ib.ID');

        $query = $this->db->get();
        return $query;
    }

    public function listKecepatan()
    {
        if ($this->input->get('q')) {
            $this->db->like('eso.deskripsi', $this->input->get('q'));
        }
        $this->db->select('*');
        $this->db->from('master.efek_samping_obat eso');
        $this->db->where('eso.status', 1);
        $this->db->where('eso.jenis', 3);
        // $this->db->group_by('ib.ID');

        $query = $this->db->get();
        return $query;
    }

    public function listKesudahan()
    {
        if ($this->input->get('q')) {
            $this->db->like('eso.deskripsi', $this->input->get('q'));
        }
        $this->db->select('*');
        $this->db->from('master.efek_samping_obat eso');
        $this->db->where('eso.status', 1);
        $this->db->where('eso.jenis', 4);
        // $this->db->group_by('ib.ID');

        $query = $this->db->get();
        return $query;
    }

    public function listProfesi()
    {
        if ($this->input->get('q')) {
            $this->db->like('eso.deskripsi', $this->input->get('q'));
        }
        $this->db->select('*');
        $this->db->from('master.efek_samping_obat eso');
        $this->db->where('eso.status', 1);
        $this->db->where('eso.jenis', 6);
        // $this->db->group_by('ib.ID');

        $query = $this->db->get();
        return $query;
    }

    public function getDataEfekSamping($id)
    {
      $query ="
      SELECT *
      FROM master.efek_samping_obat eso
      WHERE eso.id=? AND eso.jenis=?
      ";
      $bind = $this->db->query($query, array($id,1));
      return $bind->row_array();
    }
}
?>