<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pews extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','igd/PewsModel'));
  }

  public function simpanPews()
  {
    $post = $this->input->post();

    $data= array(
      'nomr'            => $post["nomr"],
      'nokun'           => $post["nokun"],
      'perilaku'        => $post["808"],
      'kardio_vaskuler' => $post["809"],
      'pernafasan'      => $post["810"],
      'oleh'            => $this->session->userdata("id"),
    );

    $this->PewsModel->simpanPews($data);
  }

  public function tablePews()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->POST('nomr');

    $tbl_pews = $this->PewsModel->tbl_pews($nomr);

    $no = 1;
    $data = array();
    foreach ($tbl_pews->result() as $tPws) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($tPws->TANGGAL)),
        $tPws->RUANGAN,
        $tPws->OLEH,
        $tPws->TOTALPEWS,
        "<a href='#' data-toggle='modal' data-target='#modalTotalPews' data-id='" . $tPws->ID . "' class='btn btn-sm btn-primary btn-block' data-backdrop='static' data-keyboard='false'><i class='fa fa-eye'></i> View</a>",
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $tbl_pews->num_rows(),
      "recordsFiltered" => $tbl_pews->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalTotalPews()
  {
    $idPews = $this->input->post('id');

    $deskTotalPews = $this->PewsModel->deskTotalPews($idPews);
    $deskTblPews = $this->PewsModel->deskTblPews($deskTotalPews['nomr'], $deskTotalPews['nokun'], date("Y-m-d", strtotime($deskTotalPews['tanggal'])));

    $data = array(
      'deskTotalPews' => $deskTotalPews,
      'deskTblPews' => $deskTblPews,
    );
    $this->load->view('Pengkajian/igd/pews/modalTotalPews', $data);
  }

}

/* End of file Pews.php */
/* Location: ./application/controllers/igd/Pews.php */
