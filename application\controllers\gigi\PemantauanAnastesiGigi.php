<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemantauanAnastesiGigi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','gigi/PemantauanAnastesiGigiModel'));
    }

    public function index() {
        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(5)),
        );

        $this->load->view('Pengkajian/gigi/pemantauananastesi',$data);
    }

    public function simpan()
    {
        $this->db->trans_begin();

        $post = $this->input->post();

        if($post['PAR'] == 'save'){
          $dataAnastesiGigi = array(
              'norm'        			            => $post['norm'],
              'nokun'        			            => $post['nokun'],
              'tgl_tindakan'              	   	=> $post['tglAnalok'],
              'tindakan_operasi'              	=> $post['tndAnalok'],
              'status_mental_pra_tindakan'        => $post['mentalAnalok'],
              'status_mental_pra_tindakan_desk'   => isset($post['mentalAnalokDll']) ? $post['mentalAnalokDll'] : NULL,
              'berat_badan'                       => $post['bbAnalok'],
              'tinggi_badan'    					=> $post['TBAnalok'],
              'tekanan_darah'        				=> $post['tdAnalok'],
              'respiratory_rate'        			=> $post['rrAnalok'],
              'nadi'             			        => $post['nAnalok'],
              'suhu'         		                => $post['sAnalok'],
              'hemoglobin'      					=> $post['hbAnalok'],
              'leukosit'             				=> $post['lAnalok'],
              'trombosit'          			    => $post['trAnalok'],
              'riwayat_alergi'          			=> $post['riwayatAnalok'],
              'riwayat_alergi_desk'          		=> isset($post['riwayatAnalokDll']) ? $post['riwayatAnalokDll'] : NULL,
              'obat_anastesi'          		    => $post['obat'],
              'dosis' 					        => $post['dosisAnalok'],
              'diencerkan'        			    => $post['encerAnalok'],
              'diencerkan_dosis'              	=> isset($post['encerAnalokDll']) ? $post['encerAnalokDll'] : NULL,
              'lokasi_pemberian'              	=> $post['lokasiAnalok'],
              'jam_lokasi_pemberian'              	=> $post['timelokasiAnalok'],
              'kejadian_selama_tindakan'          => $post['kejadianAnalok'],
              'selesai_tindakan'                  => $post['jamAnalok'],
              'status_mental_saat_tindakan'       => $post['kondisiAnalok'],
              // 'status_mental_saat_tindakan_desk'  => $post[''],
              'tanda_toksisitas'        			=> $post['toksikAnalok'],
              'tanda_toksisitas_desk'        		=> isset($post['toksikAnalokDll']) ? $post['toksikAnalokDll'] : NULL,
              'mual_muntah'             			=> $post['muntahAnalok'],
              'keluhan_nyeri'         		    => $post['nyeriAnalok'],
              'keluhan_nyeri_desk'      			=> isset($post['nyeriAnalokDll']) ? $post['nyeriAnalokDll'] : NULL,
              'pendarahan'             			=> $post['perdarahanAnalok'],
              'observasi'          			    => $post['observasiAnalok'],
              'edukasi_followup'          		=> $post['eduAnalok'],
              'jam_pasien_pindah'          		=> $post['pindahAnalok'],
              'lokasi_pasien_pindah'          	=> $post['manaAnalok'],
              'lokasi_pasien_pindah_desk' 		=> isset($post['manaAnalokDll']) ? $post['manaAnalokDll'] : NULL,
              'oleh'        			            => $this->session->userdata('id'),      
          );
          // echo "<pre>";print_r($dataAnastesiGigi);echo "</pre>";

          $this->db->insert('keperawatan.tb_pemantauan_anastesi_gigi',$dataAnastesiGigi);
          $id = $this->db->insert_id();

          $dataFsikologi = array();
          $jam = array_values(array_filter($post['pantauJamAnalok']));
          $index = 0;
          foreach($jam as $j){
          array_push($dataFsikologi, array(
                  'id_pag'            => $id,
                  'jam'               => $j,
                  'tekanan_darah'     => $post['pantauTdAnalok'][$index],
                  'nadi'              => $post['pantauNAnalok'][$index],
                  'respiratory_rate'  => $post['pantauRRAnalok'][$index],
              ));
              
              $index++;
          }

          $this->db->insert_batch('keperawatan.tb_pemantauan_anastesi_gigi_fsikologi', $dataFsikologi);
          // echo "<pre>";print_r($dataFsikologi);echo "</pre>";
        }elseif($post['PAR'] == 'update'){

          $dataAnastesiGigiUpdate = array(
              'tgl_tindakan'              	   	=> $post['tglAnalokEdit'],
              'tindakan_operasi'              	=> $post['tndAnalokEdit'],
              'status_mental_pra_tindakan'        => $post['mentalAnalokEdit'],
              'status_mental_pra_tindakan_desk'   => isset($post['mentalAnalokEditDll']) ? $post['mentalAnalokEditDll'] : NULL,
              'berat_badan'                       => $post['bbAnalokEdit'],
              'tinggi_badan'    					=> $post['TBAnalokEdit'],
              'tekanan_darah'        				=> $post['tdAnalokEdit'],
              'respiratory_rate'        			=> $post['rrAnalokEdit'],
              'nadi'             			        => $post['nAnalokEdit'],
              'suhu'         		                => $post['sAnalokEdit'],
              'hemoglobin'      					=> $post['hbAnalokEdit'],
              'leukosit'             				=> $post['lAnalokEdit'],
              'trombosit'          			    => $post['trAnalokEdit'],
              'riwayat_alergi'          			=> $post['riwayatAnalokEdit'],
              'riwayat_alergi_desk'          		=> isset($post['riwayatAnalokEditDll']) ? $post['riwayatAnalokEditDll'] : NULL,
              'obat_anastesi'          		    => $post['obatEdit'],
              'dosis' 					        => $post['dosisAnalokEdit'],
              'diencerkan'        			    => $post['encerAnalokEdit'],
              'diencerkan_dosis'              	=> isset($post['encerAnalokEditDll']) ? $post['encerAnalokEditDll'] : NULL,
              'lokasi_pemberian'              	=> $post['lokasiAnalokEdit'],
              'jam_lokasi_pemberian'              	=> $post['timelokasiAnalokEdit'],
              'kejadian_selama_tindakan'          => $post['kejadianAnalokEdit'],
              'selesai_tindakan'                  => $post['jamAnalokEdit'],
              'status_mental_saat_tindakan'       => $post['kondisiAnalokEdit'],
              // 'status_mental_saat_tindakan_desk'  => $post[''],
              'tanda_toksisitas'        			=> $post['toksikAnalokEdit'],
              'tanda_toksisitas_desk'        		=> isset($post['toksikAnalokEditDll']) ? $post['toksikAnalokEditDll'] : NULL,
              'mual_muntah'             			=> $post['muntahAnalokEdit'],
              'keluhan_nyeri'         		    => $post['nyeriAnalokEdit'],
              'keluhan_nyeri_desk'      			=> isset($post['nyeriAnalokEditDll']) ? $post['nyeriAnalokEditDll'] : NULL,
              'pendarahan'             			=> $post['perdarahanAnalokEdit'],
              'observasi'          			    => $post['observasiAnalokEdit'],
              'edukasi_followup'          		=> $post['eduAnalokEdit'],
              'jam_pasien_pindah'          		=> $post['pindahAnalokEdit'],
              'lokasi_pasien_pindah'          	=> $post['manaAnalokEdit'],
              'lokasi_pasien_pindah_desk' 		=> isset($post['manaAnalokEditDll']) ? $post['manaAnalokEditDll'] : NULL,
              'oleh_edit'        			            => $this->session->userdata('id'),      
          );
          // echo "<pre>";print_r($dataAnastesiGigiUpdate);echo "</pre>";
          $this->db->where('id', $post['idAnastesi']);
          $this->db->update('keperawatan.tb_pemantauan_anastesi_gigi', $dataAnastesiGigiUpdate);

          $dataInsert = [];
          $dataUpdate = [];

          $jamList = array_values(array_filter($post['pantauJamAnalokEdit']));
          $idList  = $post['idFsikologi'];

          foreach ($jamList as $i => $jam) {
            $id = $idList[$i];

            if ($id) {
                // Disiapkan untuk update
                $dataUpdate[] = [
                    'id'                => $id,
                    'jam'               => $jam,
                    'tekanan_darah'     => $post['pantauTdAnalokEdit'][$i],
                    'nadi'              => $post['pantauNAnalokEdit'][$i],
                    'respiratory_rate'  => $post['pantauRRAnalokEdit'][$i],
                ];
            } else {
                // Disiapkan untuk insert
                $dataInsert[] = [
                    'id_pag'            => $post['idAnastesi'],
                    'jam'               => $jam,
                    'tekanan_darah'     => $post['pantauTdAnalokEdit'][$i],
                    'nadi'              => $post['pantauNAnalokEdit'][$i],
                    'respiratory_rate'  => $post['pantauRRAnalokEdit'][$i],
                ];
            }
          }

          // Lakukan batch insert
          if (!empty($dataInsert)) {
              $this->db->insert_batch('keperawatan.tb_pemantauan_anastesi_gigi_fsikologi', $dataInsert);
          }

          // Lakukan batch update (harus ada field acuan unik: 'id')
          if (!empty($dataUpdate)) {
              $this->db->update_batch('keperawatan.tb_pemantauan_anastesi_gigi_fsikologi', $dataUpdate, 'id');
          }

        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function modalPemantauanAnastesi()
    {
        $id = $this->input->post('id');

        $data = array(
          'id' => $id,
          'getData' => $this->PemantauanAnastesiGigiModel->getData($id),
          'getDataFsikologi' => $this->PemantauanAnastesiGigiModel->getDataFsikologi($id),
        );

        $this->load->view('Pengkajian/gigi/edit_pemantauananastesi', $data);
    }

    function get_data_history(){
      $draw   = intval($this->input->POST("draw"));
      $start  = intval($this->input->POST("start"));
      $length = intval($this->input->POST("length"));
    
      $listHistory = $this->PemantauanAnastesiGigiModel->datatablesHistory($this->input->post('norm'));
    
      $data = array();
      $no = $_POST['start'];
      foreach ($listHistory as $LH) {
        $no++;

        $button = '<a href="#modalPemantauanAnastesi" class="btn btn-primary btn-block" data-id="'.$LH->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Edit</a>';

        $data[] = array(
          $no,
          $LH->nokun,
          $LH->DPJP,
          $LH->USER,
          $LH->created_at,
          $button,
        );
        
      }
    
      $output = array(
        "draw"            => $draw,
        "recordsTotal"    => $this->PemantauanAnastesiGigiModel->total_count($this->input->post('norm')),
        "recordsFiltered" => $this->PemantauanAnastesiGigiModel->filter_count($this->input->post('norm')),
        "data"            => $data
      );
      echo json_encode($output);
    }
}
