<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PersetujuanTindakanKemoterapiAmlAnak extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('tindakankemoanyelir/PersetujuanTindakanKemoAmlAnakModel'));
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->PersetujuanTindakanKemoAmlAnakModel->rules;
                $this->form_validation->set_rules($rules);

                if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataInformedConsent = array(
                        'nokun' => $post['nokun'],
                        'jenis_informed_consent' => '3034',
                        'dokter_pelaksana' => $post['dokter_pelaksana_tindakan'],
                        'penerima_informasi' => $post['penerima_informasi'],
                        'oleh' => $this->session->userdata("id"),
                    );
                    $this->db->insert('db_informed_consent.tb_informed_consent',$dataInformedConsent);

                    $idInformedConsent = $this->db->insert_id();

                    $dataPersetujuanTindakanAmlAnak = array(
                        'id_informed_consent' => $idInformedConsent,
                        'diagnosis_wd_dd' => isset($post['diagnosis_wd_dd']) ? implode(",",$post['diagnosis_wd_dd']) : NULL,
                        'dasar_diagnosis' => isset($post['dasar_diagnosis']) ? implode(",",$post['dasar_diagnosis']) : NULL,
                        'tindakan_kedokteran' => isset($post['tindakan_kedokteran']) ? implode(",",$post['tindakan_kedokteran']) : NULL,
                        'indikasi_tindakan' => isset($post['indikasi_tindakan']) ? implode(",",$post['indikasi_tindakan']) : NULL,
                        'tata_cara' => isset($post['tata_cara']) ? implode(",",$post['tata_cara']) : NULL,
                        'tujuan_tindakan' => $post['tujuan_tindakan'],
                        'tujuan_pengobatan' => isset($post['tujuan_pengobatan']) ? implode(",",$post['tujuan_pengobatan']) : NULL,
                        'risiko' => isset($post['risiko']) ? implode(",",$post['risiko']) : NULL,
                        'komplikasi' => isset($post['komplikasi']) ? implode(",",$post['komplikasi']) : NULL,
                        'prognosis' => isset($post['prognosis']) ? implode(",",$post['prognosis']) : NULL,
                        'alternatif' => isset($post['alternatif']) ? implode(",",$post['alternatif']) : NULL,
                        'lain_lain' => isset($post['lain_lain']) ? implode(",",$post['lain_lain']) : NULL,
                        'ttd_menerangkan' => file_get_contents($this->input->post('ttd_menerangkan')),
                        'ttd_menerima' => file_get_contents($this->input->post('ttd_menerima')),
                    );

                    $dataPersetujuanTidakanKedokteran = array(
                        'id_informed_consent' => $idInformedConsent,
                        'nama_keluarga' => $post['nama'],
                        'umur_keluarga' => $post['umur'],
                        'jk_keluarga' => $post['jenis_kelamin'],
                        'alamat_keluarga' => $post['alamat'],
                        'tindakan' => $post['tindakan'],
                        'hub_keluarga_dgn_pasien' => $post['hubungan'],
                        'ttd_menyatakan' => file_get_contents($this->input->post('ttd_menyatakan')),
                        'ttd_saksi_keluarga' => file_get_contents($this->input->post('ttd_keluarga')),
                        'ttd_saksi_rumah_sakit' => file_get_contents($this->input->post('ttd_rumahsakit')),
                        'saksi_keluarga' => $post['nama_keluarga'],
                        'saksi_rumah_sakit' => $post['nama_saksi_rs'],
                        'tanggal_persetujuan' => $post['tanggal'].' '.$post['jam']
                    );
                        //echo "<pre>";print_r($dataPersetujuanTidakanKedokteran);exit();

                    $this->db->insert('db_informed_consent.tb_pt_kemoaml_anak',$dataPersetujuanTindakanAmlAnak);
                    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran',$dataPersetujuanTidakanKedokteran);

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
                }else{
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            }else if($param == 'ambilAferesis'){
             $post = $this->input->post(NULL,TRUE);
             $dataAmlAnak = $this->PersetujuanTindakanKemoAmlAnakModel->get($post['nokun'], true);
             echo json_encode(array(
                'status' => 'success',
                'data' => $dataAmlAnak
            ));
         }else if($param == 'count'){
            $result = $this->PersetujuanTindakanKemoAmlAnakModel->get_count();;
            echo json_encode($result);
        }
    }
}

public function datatables(){
    $result = $this->PersetujuanTindakanKemoAmlAnakModel->datatables();
    $no    =1;
    $data = array();
    foreach ($result as $row){
        $sub_array = array();
        $sub_array[] = $no++;
        $sub_array[] = $row -> NOKUN;
        $sub_array[] = $row -> DPJP;      
        $sub_array[] = $row -> USER;
        $sub_array[] = $row -> TANGGAL;
        $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_pengkajian_aferesis" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';

        $data[] = $sub_array;
    }

    $output = array(
        "draw"              => intval($_POST["draw"]),  
        "recordsTotal"      => $this->PersetujuanTindakanKemoAmlAnakModel->total_count(),
        "recordsFiltered"   => $this->PersetujuanTindakanKemoAmlAnakModel->filter_count(),
        "data"              => $data
    );
    echo json_encode($output);
}
}