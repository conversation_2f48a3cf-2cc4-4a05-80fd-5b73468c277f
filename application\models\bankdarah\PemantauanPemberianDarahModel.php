<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemantauanPemberianDarahModel extends MY_Model
{
    protected $_table_name = 'keperawatan.tb_pemantauan_pemberian_darah';
    protected $_primary_key = 'id';
    protected $_order_by = 'tanggal_input';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.',
                'numeric' => '%s Wajib <PERSON>.'
            ),
        ),

        'bag_ke' => array(
            'field' => 'bag_ke',
            'label' => 'BAG KE',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'tanggal' => array(
            'field' => 'tanggal',
            'label' => 'Tanggal',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>isi.'
            ),
        ),

        'jam' => array(
            'field' => 'jam',
            'label' => 'Jam',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.'
            ),
        ),

        'jenis_tranfusi' => array(
            'field' => 'jenis_tranfusi',
            'label' => 'Jenis Tranfusi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.',
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'sistolik' => array(
            'field' => 'sistolik',
            'label' => 'Sistolik',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'diastolik' => array(
            'field' => 'diastolik',
            'label' => 'Diastolik',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'n' => array(
            'field' => 'n',
            'label' => 'Nadi',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'rr' => array(
            'field' => 'rr',
            'label' => 'Pernafasan',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        's' => array(
            'field' => 's',
            'label' => 'Suhu',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

    );

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select(
            'ppd.id, ppd.kunjungan, ppd.bag_ke, ppd.tanggal, ppd.jam, ppd.jenis_tranfusi, ppd.sistolik, ppd.diastolik,
            ppd.n, ppd.rr, ppd.s, ppd.sistolik15, ppd.diastolik15, ppd.n15, ppd.rr15, ppd.s15, ppd.sistolik30,
            ppd.diastolik30, ppd.n30, ppd.rr30, ppd.s30, `master`.getNamaLengkapPegawai(ap.NIP) oleh'
        );
        $this->db->from('keperawatan.tb_pemantauan_pemberian_darah ppd');
        $this->db->join('aplikasi.pengguna ap', 'ppd.oleh = ap.ID', 'LEFT');

        $this->db->where('ppd.status !=', '0');
        $this->db->where('ppd.kunjungan', $this->input->post('nokun'));
        $this->db->order_by('ppd.tanggal', 'asc');
        $this->db->order_by('ppd.jam', 'asc');

        if($this->input->post('id')){
            $this->db->where('ppd.id', $this->input->post('id'));
        }
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

}

/* End of file PemberianDanPemantauanDarahModel.php */
/* Location: ./application/models/bankDarah/PemberianDanPemantauanDarahModel.php */