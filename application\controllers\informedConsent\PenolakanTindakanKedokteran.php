<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PenolakanTindakanKedokteran extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','informedConsent/PenolakanTindakanKedokteranModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id = $this->uri->segment(3);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $getPengkajian = $this->PenolakanTindakanKedokteranModel->getPengkajian($id);

      $data = array(
        'id_informed_consent' => isset($id) ? $id : "",
        'getNomr' => $getNomr,
        'listDrUmum' => $this->masterModel->listDrUmum(),
        'listPegawai' => $this->masterModel->listAllPegawai(),
        'jenis_kelamin' => $this->masterModel->referensi(965),
        'tujuanPengobatan' => $this->masterModel->tujuanPengobatan(),
        'getPengkajian' => $getPengkajian
      );

        $this->load->view('Pengkajian/informedConsent/PenolakanTindakanKedokteran', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $this->db->trans_begin();
          $post = $this->input->post();

          $dataInformedConsent = array(
            'nokun' => $post['nokun'],
            'jenis_informed_consent' => 4924,
            'dokter_pelaksana' => $post['dokter_pelaksana'],
            'penerima_informasi' => $post['penerima_informasi'],
            'oleh' => $this->session->userdata['id'],
          );

          if(!empty($post['id_informed_consent'])){
            $id_ic = array('id' => $post['id_informed_consent']);
            $ubahInformedConcent = $this->PenolakanTindakanKedokteranModel->ubahInformedConcent($id_ic, $dataInformedConsent);
          } else {
            $idInformedConsent = $this->PenolakanTindakanKedokteranModel->simpanInformedConsent($dataInformedConsent);
          }
         
          if(!empty($post['id_informed_consent'])){
            $dataPenolakanTK = array(
              'id_informed_consent' => isset($post['id_informed_consent']) ? $post['id_informed_consent'] : "",
              'tanggal' => date('Y-m-d', strtotime($post['tanggal'])),
              'diagnosis' => $post['diagnosis'],
              'dasar_diagnosis' => $post['dasar_diagnosis'],
              'tindakan_kedokteran' => $post['tindakan_kedokteran'],
              'indikasi_tindakan' => $post['indikasi_tindakan'],
              'tata_cara' => $post['tata_cara'],
              'tujuan_tindakan' => $post['tujuan_tindakan'],
              'tujuan_pengobatan' => $post['tujuan_pengobatan'],
              'risiko' => $post['risiko'],
              'komplikasi' => $post['komplikasi'],
              'prognosis' => $post['prognosis'],
              'alternatif_risiko' => $post['alternatif_risiko'],
              'lainnya' => $post['lainnya'],
              'status' => '1',
            );
          } else {
            $dataPenolakanTK = array(
              'id_informed_consent' => $idInformedConsent,
              'tanggal' => date('Y-m-d', strtotime($post['tanggal'])),
              'diagnosis' => $post['diagnosis'],
              'dasar_diagnosis' => $post['dasar_diagnosis'],
              'tindakan_kedokteran' => $post['tindakan_kedokteran'],
              'indikasi_tindakan' => $post['indikasi_tindakan'],
              'tata_cara' => $post['tata_cara'],
              'tujuan_tindakan' => $post['tujuan_tindakan'],
              'tujuan_pengobatan' => $post['tujuan_pengobatan'],
              'risiko' => $post['risiko'],
              'komplikasi' => $post['komplikasi'],
              'prognosis' => $post['prognosis'],
              'alternatif_risiko' => $post['alternatif_risiko'],
              'lainnya' => $post['lainnya'],
              'ttd_menerangkan' => file_get_contents($this->input->post('ttd_menerangkan')),
              'ttd_menerima' => file_get_contents($this->input->post('ttd_menerima')),
              'status' => '1',
            );
          } 

          if(!empty($post['id_ptk'])){
            $id_ptk = array('id' => $post['id_ptk']);
            $ubahPenolakanTK = $this->PenolakanTindakanKedokteranModel->ubahPenolakanTK($id_ptk, $dataPenolakanTK);
          } else {
            $simpanPenolakanTK = $this->PenolakanTindakanKedokteranModel->simpanPenolakanTK($dataPenolakanTK);
          }
          
          if(!empty($post['id_ptk'])){
            $dataPenolakanTindakanKedokteran = array(
              'id_informed_consent' => $post['id_informed_consent'],
              'nama_keluarga' => $post['nama'],
              'umur_keluarga' => $post['umur'],
              'jk_keluarga' => $post['jenis_kelamin'],
              'alamat_keluarga' => $post['alamat'],
              'tindakan' => $post['tindakan'],
              'hub_keluarga_dgn_pasien' => $post['hubungan'],
              'tanggal_penolakan' => $post['tanggal_menolak'],
              'saksi_keluarga' => $post['nama_keluarga'],
              'saksi_rumah_sakit' => $post['nama_saksi_rs'],
            );
          } else {
            $dataPenolakanTindakanKedokteran = array(
              'id_informed_consent' => $idInformedConsent,
              'nama_keluarga' => $post['nama'],
              'umur_keluarga' => $post['umur'],
              'jk_keluarga' => $post['jenis_kelamin'],
              'alamat_keluarga' => $post['alamat'],
              'tindakan' => $post['tindakan'],
              'hub_keluarga_dgn_pasien' => $post['hubungan'],
              'tanggal_penolakan' => $post['tanggal_menolak'],
              'ttd_menyatakan' => file_get_contents($this->input->post('ttd_menyatakan')),
              'ttd_saksi_keluarga' => file_get_contents($this->input->post('ttd_keluarga')),
              'ttd_saksi_rumah_sakit' => file_get_contents($this->input->post('ttd_rumah_sakit')),
              'saksi_keluarga' => $post['nama_keluarga'],
              'saksi_rumah_sakit' => $post['nama_saksi_rs'],
            );
          }

          if(!empty($post['id_tptk'])){
            $id_tptk = array('id' => $post['id_tptk']);

            $ubahPenolakanTidakanKedokteran = $this->PenolakanTindakanKedokteranModel->ubahPenolakanTidakanKedokteran($id_tptk, $dataPenolakanTindakanKedokteran);
          } else {
            $this->PenolakanTindakanKedokteranModel->simpanPenolakanTidakanKedokteran($dataPenolakanTindakanKedokteran);
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success_simpan');
          }

        echo json_encode($result);

        }else if($param == 'count'){
          $result = $this->PenolakanTindakanKedokteranModel->get_count();
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->PenolakanTindakanKedokteranModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editPenolakanTindakanKedokteran" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> nokun;
            $sub_array[] = $row -> dokter_pelaksana;   
            $sub_array[] = $row -> oleh;
            $sub_array[] = $row -> status;
            $sub_array[] = $row -> tanggal;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PenolakanTindakanKedokteranModel->total_count(),
            "recordsFiltered"   => $this->PenolakanTindakanKedokteranModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}