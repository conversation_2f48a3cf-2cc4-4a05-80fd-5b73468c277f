<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Konseling extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/farmasi/KonselingModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_nokun_konseling = $this->uri->segment(3);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $getResumeMedis = $this->KonselingModel->getResumeMedis($getNomr['NOPEN']);
      $getRiwayatPengobatan = $this->KonselingModel->getRiwayatPengobatan($getNomr['NOPEN']);
      $getObatTerakhir = $this->KonselingModel->getObatTerakhir($getNomr['NOPEN']);
      $getPengkajian = $this->KonselingModel->getPengkajian($id_nokun_konseling);
      $getPengguna = $this->KonselingModel->getPengguna($id_nokun_konseling);

      $data = array(
        'nokun' => $nokun,
        'id_nokun_konseling' => $id_nokun_konseling,
        'getResumeMedis' => $getResumeMedis,
        'getRiwayatPengobatan' => $getRiwayatPengobatan,
        'getObatTerakhir' => $getObatTerakhir,
        'getNomr' => $getNomr,
        'getPengkajian' => $getPengkajian,
        'getPengguna' => $getPengguna
      );

      $this->load->view('rekam_medis/rawat_inap/farmasi/konseling', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
          
          $data = array();
          $index = 0;
          $this->db->trans_begin();
          
          if (isset($post['nama_obat'])) {
              foreach ($post['nama_obat'] as $input) {
                if ($post['nama_obat'][$index] != "") {
                    array_push(
                      $data,
                      array(
                        'id' => isset($post['id_konseling']) ? $post['id_konseling'][$index] : "",
                        'nokun' => $post['nokun'],
                        'keterangan' => $post['keterangan'][$index],
                        'id_obat' => $post['id_obat'][$index],
                        'nama_obat' => $post['nama_obat'][$index],
                        'kandungan_obat' => $post['kandungan_obat'][$index],
                        'jumlah' => $post['jumlah_obat'][$index],
                        'cara_penggunaan' => $post['penggunaan_obat'][$index],
                        'waktu_1' => $post['waktu_1'][$index],
                        'waktu_2' => $post['waktu_2'][$index],
                        'waktu_3' => $post['waktu_3'][$index],
                        'waktu_4' => $post['waktu_4'][$index],
                        'waktu_5' => $post['waktu_5'][$index],
                        'waktu_6' => $post['waktu_6'][$index],
                        'info_konseling' => $post['info_konseling'][$index],
                        'oleh' => $post['pengguna']
                      )
                    );
                }
                $index++;
              }



              if (!empty($post['id_nokun_konseling'])) {
                $this->db->delete('keperawatan.tb_konseling', array('nokun' => $post['nokun']));
                $this->db->insert_batch('keperawatan.tb_konseling', $data);

                if ($this->db->trans_status() === false) {
                  $this->db->trans_rollback();
                  $result = array('status' => 'failed');
                } else {
                  $this->db->trans_commit();
                  $result = array('status' => 'ubah');
                }
              } else {
                $this->db->insert_batch('keperawatan.tb_konseling', $data);
                if ($this->db->trans_status() === false) {
                  $this->db->trans_rollback();
                  $result = array('status' => 'failed');
                } else {
                  $this->db->trans_commit();
                  $result = array('status' => 'success');
                }
              }
          }


          echo json_encode($result);

        }else if($param == 'count'){
          $result = $this->KonselingModel->get_count();;
          echo json_encode($result);
        }

      }
    }

    public function datatables(){
      $result = $this->KonselingModel->datatables();

      $data = array();
      foreach ($result as $row){
          $sub_array = array();
          $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editKonseling" data-id="'.$row -> nokun.'"><i class="fa fa-eye"></i> Lihat</a><a href="/reports/simrskd/farmasi/FormulirKonseling.php?format=pdf&nokun='.$row -> nokun.'" class="btn btn-warning btn-block btn-sm" target="_blank"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak</span></a>';
          $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
          $sub_array[] = $row -> ruangan;
          $sub_array[] = $row -> user;

          $data[] = $sub_array;
      }

      $output = array(
          "draw"              => intval($_POST["draw"]),  
          "recordsTotal"      => $this->KonselingModel->total_count(),
          "recordsFiltered"   => $this->KonselingModel->filter_count(),
          "data"              => $data
      );
      echo json_encode($output);
    }

}