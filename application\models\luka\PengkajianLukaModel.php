<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengkajianlukaModel extends MY_Model{
	protected $_table_name = 'keperawatan.tb_pengkajian_luka';
	protected $_primary_key = 'kunjungan';
	protected $_order_by = 'kunjungan';
	protected $_order_by_type = 'DESC';

	public $rules = array(
		'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
		),

		'ruangan' => array(
            'field' => 'ruangan',
            'label' => 'Ruangan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
		),

		'diagnosis' => array(
            'field' => 'diagnosis',
            'label' => 'Diagnosis',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.'
                ),
		),
		
		'riwayat_alergi_luka' => array(
            'field' => 'riwayat_alergi_luka',
            'label' => 'Riwayat Alergi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),
		
		'keluhan_utama' => array(
            'field' => 'keluhan_utama',
            'label' => 'Keluhan Utama',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),
		
		'sistolik' => array(
            'field' => 'sistolik',
            'label' => 'Sistolik',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'distolik' => array(
            'field' => 'distolik',
            'label' => 'Distolik',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'pernapasan' => array(
            'field' => 'pernapasan',
            'label' => 'Pernapasan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'nadi' => array(
            'field' => 'nadi',
            'label' => 'Nadi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'suhu' => array(
            'field' => 'suhu',
            'label' => 'Suhu',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'skrining_nyeri' => array(
            'field' => 'skrining_nyeri',
            'label' => 'Skrining Nyeri',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'skrining_resiko_jatuh_pusing_luka' => array(
            'field' => 'skrining_resiko_jatuh_pusing_luka',
            'label' => 'keluhan pusing/vertigo',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'skrining_resiko_jatuh_berdiri_luka' => array(
            'field' => 'skrining_resiko_jatuh_berdiri_luka',
            'label' => 'Kesulitan saat berdiri/berjalan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'skrining_resiko_jatuh_6bulan_luka' => array(
            'field' => 'skrining_resiko_jatuh_6bulan_luka',
            'label' => 'Mengalami jatuh dalam 6 bulan terakhir',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'jenis_luka[]' => array(
            'field' => 'jenis_luka[]',
            'label' => 'Jenis Luka',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'psikososial' => array(
            'field' => 'psikososial',
            'label' => 'Psikososial',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'masalah_keperawatan_luka[]' => array(
            'field' => 'masalah_keperawatan_luka[]',
            'label' => 'Masalah Keperawatan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'pencucian_luka[]' => array(
            'field' => 'pencucian_luka[]',
            'label' => 'Pencucian Luka',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'frekuensi' => array(
            'field' => 'frekuensi',
            'label' => 'Frekuensi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),

		'kebutuhan_pembelajaran[]' => array(
            'field' => 'kebutuhan_pembelajaran[]',
            'label' => 'Kebutuhan Pembelajaran',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'kontrol_luka' => array(
            'field' => 'kontrol_luka',
            'label' => 'Kontrol Poliklinik Luka',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),		
    );
    
    public $rules_rawat_jalan = array(
		'luka_jalan_ruangan' => array(
            'field' => 'luka_jalan_ruangan',
            'label' => 'Ruangan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
    );
    
    public $rules_rawat_inap = array(
		'luka_inap_ruangan' => array(
            'field' => 'luka_inap_ruangan',
            'label' => 'Ruangan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
    );
    
    public $rules_riwayat_alergi = array(
		'alergi_luka_desc' => array(
            'field' => 'alergi_luka_desc',
            'label' => 'Sebutkan Riwayat Alergi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
	);

	public $rules_nyeri = array(
		'skor_nyeri' => array(
            'field' => 'skor_nyeri',
            'label' => 'Skala Nyeri',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'farmakologi' => array(
            'field' => 'farmakologi',
            'label' => 'Farmakologi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),

		'non_farmakologi' => array(
            'field' => 'non_farmakologi',
            'label' => 'Non Farmakologi',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),
		
		'efek_samping' => array(
            'field' => 'efek_samping',
            'label' => 'Efek Samping',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),
		
		'provocative' => array(
            'field' => 'provocative',
            'label' => 'Provocative',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'quality' => array(
            'field' => 'quality',
            'label' => 'Quality',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),

		'regio' => array(
            'field' => 'regio',
            'label' => 'Regio',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),

		'severity' => array(
            'field' => 'severity',
            'label' => 'Severity',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),

		'time' => array(
            'field' => 'time',
            'label' => 'Time',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
		),		
	);

	public $rules_efek_samping = array(
		'efek_samping_lain' => array(
            'field' => 'efek_samping_lain',
            'label' => 'Efek Samping',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
	);

	public $rules_quality = array(
		'quality_lainnya' => array(
            'field' => 'quality_lainnya',
            'label' => 'Quality Lainnya',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
	);

	public $rules_time = array(
		'durasi_nyeri' => array(
            'field' => 'durasi_nyeri',
            'label' => 'Durasi Nyeri',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
    );
    
    public $rules_luka_radiasi = array(
        'dosis_radiasi' => array(
            'field' => 'dosis_radiasi',
            'label' => 'Dosis [RADIASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'radiasi_ke' => array(
            'field' => 'radiasi_ke',
            'label' => 'Radiasi ke',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
		'lokasi_radiasi' => array(
            'field' => 'lokasi_radiasi',
            'label' => 'Lokasi [RADIASI]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'panjang_radiasi' => array(
            'field' => 'panjang_radiasi',
            'label' => 'Panjang [RADIASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'lebar_radiasi' => array(
            'field' => 'lebar_radiasi',
            'label' => 'Lebar [RADIASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'warna_dasar_luka_radiasi[]' => array(
            'field' => 'warna_dasar_luka_radiasi[]',
            'label' => 'Warna Dasar Luka [RADIASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'balutan_radiasi[]' => array(
            'field' => 'balutan_radiasi[]',
            'label' => 'Jenis Balutan [RADIASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'pendarahan_radiasi' => array(
            'field' => 'pendarahan_radiasi',
            'label' => 'Perdarahan [RADIASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'grade_radiasi' => array(
            'field' => 'grade_radiasi',
            'label' => 'Grade [RADIASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $rules_luka_ekstravasasi = array(
                
		'lokasi_ekstravasasi' => array(
            'field' => 'lokasi_ekstravasasi',
            'label' => 'Lokasi [EKSTRAVASASI]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        'tanggal_kemoterapi' => array(
            'field' => 'tanggal_kemoterapi',
            'label' => 'Lokasi [EKSTRAVASASI]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        'obat_ekstravasasi' => array(
            'field' => 'obat_ekstravasasi',
            'label' => 'Lokasi [EKSTRAVASASI]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'panjang_ekstravasasi' => array(
            'field' => 'panjang_ekstravasasi',
            'label' => 'Panjang [EKSTRAVASASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'lebar_ekstravasasi' => array(
            'field' => 'lebar_ekstravasasi',
            'label' => 'Lebar [EKSTRAVASASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'warna_dasar_luka_ekstravasasi[]' => array(
            'field' => 'warna_dasar_luka_ekstravasasi[]',
            'label' => 'Warna Dasar Luka [EKSTRAVASASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'balutan_ekstravasasi[]' => array(
            'field' => 'balutan_ekstravasasi[]',
            'label' => 'Jenis Balutan [EKSTRAVASASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $rules_luka_operasi = array(
                
		'lokasi_operasi' => array(
            'field' => 'lokasi_operasi',
            'label' => 'Lokasi [OPERASI]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        'tanggal_operasi' => array(
            'field' => 'tanggal_operasi',
            'label' => 'Tanggal Operasi [OPERASI]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        'jenis_operasi' => array(
            'field' => 'jenis_operasi',
            'label' => 'Lokasi [OPERASI]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'drain' => array(
            'field' => 'drain',
            'label' => 'Drain',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'jahitan[]' => array(
            'field' => 'jahitan[]',
            'label' => 'Jahitan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'tanda_infeksi' => array(
            'field' => 'tanda_infeksi',
            'label' => 'Tanda Infeksi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'ftsg' => array(
            'field' => 'ftsg',
            'label' => 'FTSG',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'stsg' => array(
            'field' => 'stsg',
            'label' => 'STSG',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'tubes_cateter' => array(
            'field' => 'tubes_cateter',
            'label' => 'Tubes Cateter',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'dehiscence' => array(
            'field' => 'dehiscence',
            'label' => 'Dehiscence',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $rules_nefrostomy = array(
		'nefrostomy[]' => array(
            'field' => 'nefrostomy[]',
            'label' => 'Nefrostomy',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'infeksi' => array(
            'field' => 'infeksi',
            'label' => 'Infeksi',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'rembes' => array(
            'field' => 'rembes',
            'label' => 'Rembes',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),	
    );

    public $rules_dehiscence = array(
        'panjang_operasi' => array(
            'field' => 'panjang_operasi',
            'label' => 'Panjang [OPERASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'lebar_operasi' => array(
            'field' => 'lebar_operasi',
            'label' => 'Lebar [OPERASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'kedalaman_operasi' => array(
            'field' => 'kedalaman_operasi',
            'label' => 'Kedalaman [OPERASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

		'warna_dasar_luka_operasi[]' => array(
            'field' => 'warna_dasar_luka_operasi[]',
            'label' => 'Warna Dasar Luka [OPERASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'balutan_operasi[]' => array(
            'field' => 'balutan_operasi[]',
            'label' => 'Jenis Balutan [OPERASI]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $rules_luka_tekan = array(
                
		'lokasi_tekan' => array(
            'field' => 'lokasi_tekan',
            'label' => 'Lokasi [TEKAN]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'panjang_tekan' => array(
            'field' => 'panjang_tekan',
            'label' => 'Panjang [TEKAN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'kedalaman_tekan' => array(
            'field' => 'kedalaman_tekan',
            'label' => 'Kedalaman [TEKAN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'lebar_tekan' => array(
            'field' => 'lebar_tekan',
            'label' => 'Lebar [TEKAN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'warna_dasar_luka_tekan[]' => array(
            'field' => 'warna_dasar_luka_tekan[]',
            'label' => 'Warna Dasar Luka [TEKAN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'balutan_tekan[]' => array(
            'field' => 'balutan_tekan[]',
            'label' => 'Jenis Balutan [TEKAN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'stadium_luka_tekan[]' => array(
            'field' => 'stadium_luka_tekan[]',
            'label' => 'Stadium Luka Tekan [TEKAN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $rules_luka_fistula = array(
                
		'lokasi_fistula' => array(
            'field' => 'lokasi_fistula',
            'label' => 'Lokasi [FISTULA]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),

        'produksi' => array(
            'field' => 'produksi',
            'label' => 'produksi/24 Jam',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
        
        'keluaran' => array(
            'field' => 'keluaran',
            'label' => 'Keluaran',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'manajemen' => array(
            'field' => 'manajemen',
            'label' => 'Manajemen',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
    );

    public $rules_keluaran = array(
		'keluaran_lainnya' => array(
            'field' => 'keluaran_lainnya',
            'label' => 'Keluaran Lainnya',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
		),	
    );

    public $rules_luka_lain = array(
                
		'lokasi_lain' => array(
            'field' => 'lokasi_lain',
            'label' => 'Lokasi [LAIN]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'panjang_lain' => array(
            'field' => 'panjang_lain',
            'label' => 'Panjang [LAIN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'lebar_lain' => array(
            'field' => 'lebar_lain',
            'label' => 'Lebar [LAIN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'kedalaman_lain' => array(
            'field' => 'kedalaman_lain',
            'label' => 'Kedalaman [LAIN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'warna_dasar_luka_lain[]' => array(
            'field' => 'warna_dasar_luka_lain[]',
            'label' => 'Warna Dasar Luka [LAIN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'balutan_lain[]' => array(
            'field' => 'balutan_lain[]',
            'label' => 'Jenis Balutan [LAIN]',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),

        'infeksi_luka_lain' => array(
            'field' => 'infeksi_luka_lain',
            'label' => 'Infeksi [LAIN]',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
    );

	function __construct(){
		parent::__construct();
    }
    
    function table_query()
    {
        $this->db->select('kp.kunjungan NOKUN, kp.tanggal TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('keperawatan.tb_pengkajian_luka kp');
        $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = kp.kunjungan','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
        $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
        $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
        $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
        $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
        $this->db->join('aplikasi.pengguna peng','peng.ID = kp.oleh','LEFT');

        $this->db->where('kp.STATUS !=','0');
        $this->db->where('p.NORM',$this->input->post('nomr'));
        $this->db->order_by('kp.TANGGAL', 'DESC');

        // if($this->input->post('id')){
        // 	$this->db->where('hph.ID', $this->input->post('id'));
        // }

        // if($this->input->post('status')){
        //     $this->db->where_in('hph.STATUS_LIS',$this->input->post('status'),FALSE);
        // }
        // else{
        //     $this->db->where('his.STATUS IS NULL');
        // }

        // if($this->input->post('search[value]')){
        //     $this->db->group_start();
        //     $this->db->like('hph.NORM', $this->input->post('search[value]'));
        //     $this->db->or_like('hph.NOMOR_LAB', $this->input->post('search[value]'));
        //     $this->db->group_end();
        //     // $this->db->where_in('his.STATUS',$this->input->post('status'));            
        // }
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
