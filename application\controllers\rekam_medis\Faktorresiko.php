<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Faktorresiko extends CI_Controller
{
	public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'rekam_medis/FaktorresikoModel'
    ));
  }

  public function index($nomr='')
  {
    // var_dump($nomr);exit;
    $data['nomr']=$nomr;
    $data['pertanyaanjawab'] = $this->FaktorresikoModel->pertanyaanNjawab($nomr);
    // $data['jawaban'] = $this->FaktorresikoModel->jawaban($nomr);
    // var_dump( $data['pertanyaan']);exit;

    $this->load->view('rekam_medis/faktorresiko/index', $data);
  }

  function simpanfaktorresiko(){
    $post = $this->input->post();
    $post['oleh']=$this->session->userdata('id');
    // var_dump($post);exit;
    $simpan = $this->FaktorresikoModel->saveJawaban($post);

    if($simpan){
      $result = array('status' => 'success');
    }else{
      $result = array('status' => 'failed');
    }
    echo json_encode($result);
  }

}