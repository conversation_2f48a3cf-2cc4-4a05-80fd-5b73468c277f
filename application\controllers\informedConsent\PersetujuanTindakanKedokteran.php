<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PersetujuanTindakanKedokteran extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Bangkok');
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'informedConsent/PersetujuanTindakanKedokteranModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'tujuanPengobatan' => $this->masterModel->tujuanPengobatan(),
      'getNomr' => $getNomr,
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/informedConsent/persetujuanTindakanKedokteran/index', $data);
  }

  public function indexRawatInap()
  {
    $nokun = $this->uri->segment(2);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'tujuanPengobatan' => $this->masterModel->tujuanPengobatan(),
      'getNomr' => $getNomr,
    );
    // echo "<pre>";print_r($data);exit();
    $this->load->view('Pengkajian/informedConsent/persetujuanTindakanKedokteran/index', $data);
  }

  // Simpan Persetujuan Tindakan Kedokteran
  public function simpanPTK()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $dataInformedConsent = array(
      'nokun' => $post['nokun'],
      'jenis_informed_consent' => 3026,
      'dokter_pelaksana' => $post['dokter_pelaksana'],
      'penerima_informasi' => $post['penerima_informasi'],
      'oleh' => $this->session->userdata['id'],
    );
    $idInformedConsent = $this->PersetujuanTindakanKedokteranModel->simpanInformedConsent($dataInformedConsent);

    $dataPTK = array(
      'id_informed_consent' => $idInformedConsent,
      'tanggal' => $post['tanggal'],
      'diagnosis' => $post['diagnosis'],
      'dasar_diagnosis' => $post['dasar_diagnosis'],
      'tindakan_kedokteran' => $post['tindakan_kedokteran'],
      'indikasi_tindakan' => $post['indikasi_tindakan'],
      'tata_cara' => $post['tata_cara'],
      'tujuan_tindakan' => $post['tujuan_tindakan'],
      'tujuan_pengobatan' => $post['tujuan_pengobatan'],
      'risiko' => $post['risiko'],
      'komplikasi' => $post['komplikasi'],
      'prognosis' => $post['prognosis'],
      'alternatif_risiko' => $post['alternatif_risiko'],
      'lainnya' => $post['lainnya'],
      'ttd_menerangkan' => file_get_contents($this->input->post('ttd_menerangkan')),
      'ttd_menerima' => file_get_contents($this->input->post('ttd_menerima')),
      'status' => '1',
    );
    /*echo "<pre>";print_r($data);exit();*/
    $simpanPTK = $this->PersetujuanTindakanKedokteranModel->simpanPTK($dataPTK);

    $dataPersetujuanTindakanKedokteran = array(
      'id_informed_consent' => $idInformedConsent,
      'nama_keluarga' => $post['nama'],
      'umur_keluarga' => $post['umur'],
      'jk_keluarga' => $post['jenis_kelamin'],
      'alamat_keluarga' => $post['alamat'],
      'tindakan' => $post['tindakan'],
      'hub_keluarga_dgn_pasien' => $post['hubungan'],
      'tanggal_persetujuan' => $post['tanggal_setuju'],
      'ttd_menyatakan' => file_get_contents($this->input->post('ttd_menyatakan')),
      'ttd_saksi_keluarga' => file_get_contents($this->input->post('ttd_keluarga')),
      'ttd_saksi_rumah_sakit' => file_get_contents($this->input->post('ttd_rumah_sakit')),
      'saksi_keluarga' => $post['nama_keluarga'],
      'saksi_rumah_sakit' => $post['nama_saksi_rs'],
    );
    $this->PersetujuanTindakanKedokteranModel->simpanPersetujuanTidakanKedokteran($dataPersetujuanTindakanKedokteran);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function historyPTK()
  {
    $draw = intval($this->input->post('draw'));

    $nomr = $this->input->post('nomr');
    $historyInformedConsentPTK = $this->PersetujuanTindakanKedokteranModel->historyInformedConsentPTK($nomr);

    $data = array();
    $no = 1;
    foreach ($historyInformedConsentPTK->result() as $h) {
      if ($h->status == 1) {
        $status = "<p class='text-success'>Disetujui</p>";
      } elseif ($h->status == 0) {
        $status = "<p class='text-danger'>Dibatalkan</p>";
      }
      $data[] = array(
        $no,
        $h->nokun,
        $h->dokter_pelaksana,
        $h->oleh,
        $status,
        date('d-m-Y H:i:s', strtotime($h->tanggal)),
        "<div class='btn-group' role='group'>
          <a href='#modal-history-ptk' class='btn btn-sm btn-custom modal-history-ptk waves-effect' data-toggle='modal' data-id='" . $h->id . "-" . $h->nokun . "' lihatHistoryPTK='" . $h->id . "-" . $h->nokun . "'>
            <i class='fas fa-pencil-alt'></i> Ubah
          </a>
          <a href='/reports/simrskd/PersetujuanTindakan/PersetujuanTindakanDokter.php?format=pdf&id=" . $h->id . "' class='btn btn-sm btn-warning waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak
          </a>
        </div>",
      );
      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $historyInformedConsentPTK->num_rows(),
      "recordsFiltered" => $historyInformedConsentPTK->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function modalPTK()
  {
    $id = $this->input->post('id');
    $nokun = $this->input->post('nokun');
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $detailPTK = $this->PersetujuanTindakanKedokteranModel->detailPTK($id);
    $data = array(
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'tujuanPengobatan' => $this->masterModel->tujuanPengobatan(),
      'getNomr' => $getNomr,
      'detailPTK' => $detailPTK,
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/informedConsent/persetujuanTindakanKedokteran/history/modal', $data);
  }

  public function batalPTK()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $data = array(
      'status' => 0,
    );
    $id = array('id' => $post['id_tic']);
    $this->PersetujuanTindakanKedokteranModel->ubahInformedConcent($id, $data);

    $data = array(
      'tgl_update' => date('Y-m-d H:i:s'),
      'status' => 0,
    );
    $id = array('id_informed_consent' => $post['id_tic']);
    $this->PersetujuanTindakanKedokteranModel->ubahPTK($id, $data);

    $data = array(
      'status_persetujuan' => 0,
    );
    $id = array('id_informed_consent' => $post['id_tic']);
    $this->PersetujuanTindakanKedokteranModel->ubahTPTK($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}
