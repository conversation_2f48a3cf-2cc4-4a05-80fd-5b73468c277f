<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class VerifPengkajian extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(5,$this->session->userdata('akses')) OR !in_array(7,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('masterModel');
  }

  public function index()
  {
    $pengguna = $this->masterModel->pengguna();

    $data = array(
      'title'   => 'Halaman Master',
      'isi'     => 'Master/verifPengkajian/index',
      'pengguna' => $pengguna->result(),
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function viewVerif()
  {
    $id = $this->input->post('id');
    $nama = $this->input->post('nama');
    $jenisPengkajian = $this->masterModel->jenisPengkajian();
    $cekVerifPengkajian = $this->masterModel->cekVerifPengkajian($id);
    $data = array(
      'id'   => $id,
      'nama'   => $nama,
      'jenisPengkajian'   => $jenisPengkajian,
      'cekVerifPengkajian'   => $cekVerifPengkajian,
    );
    $this->load->view('Master/verifPengkajian/modalViewPengkajian',$data);
  }

  public function simpanVerifPengkajian()
  {
    $id = $this->input->post('id');
    $user = $this->input->post('user');
    $cek = $this->input->post('cek');
    if($cek == 1){
      $data = array(
        'id_jenis_pengkajian'   => $id,
        'id_user'   => $user,
        'status'   => 1,
      );
    }elseif($cek == 0){
      $data = array(
        'id_jenis_pengkajian'   => $id,
        'id_user'   => $user,
        'status'   => 0,
      );
    }

    $this->db->replace('db_master.tb_verif_pengkajian', $data);
  }

}

/* End of file ManajemenPengguna.php */
/* Location: ./application/controllers/master/ManajemenPengguna.php */
