<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemberianDanPemantauanModel extends MY_Model
{
        protected $_table_name = 'keperawatan.tb_kesesuaian_formulir';
        protected $_primary_key = 'id';
        protected $_order_by = 'tanggal';
        protected $_order_by_type = 'DESC';

        public $rules = array(
                'nokun' => array(
                        'field' => 'nokun',
                        'label' => 'Nomor Kunjungan',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib <PERSON>isi.',
                                'numeric' => '%s Wajib <PERSON>ka.'
                        ),
                ),

                'ruangan' => array(
                        'field' => 'ruangan',
                        'label' => 'Ruangan',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'surat_ijin' => array(
                        'field' => 'surat_ijin',
                        'label' => 'Surat Ijin',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib <PERSON>isi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'kesesuaian_intruksi' => array(
                        'field' => 'kesesuaian_intruksi',
                        'label' => 'Kesesuaian Instruksi',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'jenis_darah' => array(
                        'field' => 'jenis_darah',
                        'label' => 'Jenis Darah',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'volume' => array(
                        'field' => 'volume',
                        'label' => 'Volume',
                        'rules' => 'trim|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.'
                        ),
                ),

                'jenis_darah_sesuai' => array(
                        'field' => 'jenis_darah_sesuai',
                        'label' => 'Jenis Darah Sesuai',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'golongan_darah_pemberian' => array(
                        'field' => 'golongan_darah_pemberian',
                        'label' => 'Golongan Darah',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'golongan_darah_sesuai' => array(
                        'field' => 'golongan_darah_sesuai',
                        'label' => 'Golongan Darah Sesuai',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'rhesus' => array(
                        'field' => 'rhesus',
                        'label' => 'Rhesus',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'formulir_pmi' => array(
                        'field' => 'formulir_pmi',
                        'label' => 'Formulir PMI',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

        );

        public $rules_kesesuaian_kantong = array(
                'nokun' => array(
                        'field' => 'nokun',
                        'label' => 'Nomor Kunjungan',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'nomor_kantong' => array(
                        'field' => 'nomor_kantong',
                        'label' => 'Nomor Kantong',
                        'rules' => 'trim|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.'
                        ),
                ),

                'golongan_darah_kesesuaian' => array(
                        'field' => 'golongan_darah_kesesuaian',
                        'label' => 'Golongan Darah',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'label_darah' => array(
                        'field' => 'label_darah',
                        'label' => 'Label Darah',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'identitas_pasien' => array(
                        'field' => 'identitas_pasien',
                        'label' => 'Identitas Darah',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'perawat1' => array(
                        'field' => 'perawat1',
                        'label' => 'Perawat 1',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

                'perawat2' => array(
                        'field' => 'perawat2',
                        'label' => 'Perawat 2',
                        'rules' => 'trim|numeric|required',
                        'errors' => array(
                                'required' => '%s Wajib Diisi.',
                                'numeric' => '%s Wajib Angka.'
                        ),
                ),

        );

        function __construct()
        {
                parent::__construct();
        }

        function table_query()
        {
                $this->db->select('kp.id ID, kp.kunjungan NOKUN, kp.tanggal TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
                $this->db->from('keperawatan.tb_kesesuaian_formulir kp');
                $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kp.kunjungan', 'LEFT');
                $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'LEFT');
                $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'LEFT');
                $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'LEFT');
                $this->db->join('master.diagnosa_masuk dm', 'dm.ID = p.DIAGNOSA_MASUK', 'LEFT');
                $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'LEFT');
                $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'LEFT');
                $this->db->join('aplikasi.pengguna peng', 'peng.ID = kp.oleh', 'LEFT');

                $this->db->where('kp.STATUS !=', '0');
                $this->db->where('p.NORM', $this->input->post('nomr'));
                $this->db->order_by('kp.TANGGAL', 'DESC');

                // if($this->input->post('id')){
                // 	$this->db->where('hph.ID', $this->input->post('id'));
                // }

                // if($this->input->post('status')){
                //     $this->db->where_in('hph.STATUS_LIS',$this->input->post('status'),FALSE);
                // }
                // else{
                //     $this->db->where('his.STATUS IS NULL');
                // }

                // if($this->input->post('search[value]')){
                //     $this->db->group_start();
                //     $this->db->like('hph.NORM', $this->input->post('search[value]'));
                //     $this->db->or_like('hph.NOMOR_LAB', $this->input->post('search[value]'));
                //     $this->db->group_end();
                //     // $this->db->where_in('his.STATUS',$this->input->post('status'));            
                // }
        }

        function get_table($single = TRUE)
        {
                $this->table_query();
                $query = $this->db->get();
                if ($single == TRUE) {
                        $method = 'row';
                } else {
                        $method = 'result';
                }
                return $query->$method();
        }

        function get_count()
        {
                $this->table_query();
                return $this->db->count_all_results();
        }

        public function cekPemantauanDarah()
        {       
                $nokun = $this->input->post('nokun');
                $jenis = $this->input->post('jenis');

                $query = $this->db->query("SELECT * FROM keperawatan.tb_pemantauan_pemberian_darah pd
                WHERE pd.kunjungan='$nokun' AND pd.jenis_tranfusi = $jenis AND pd.`status` = 1");

                return $query->result_array();
        }
}
