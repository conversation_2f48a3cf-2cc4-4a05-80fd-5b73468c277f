<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BundleVAP extends CI_Controller {
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/ruanganIntensif/BundleVAPModel'));
    }


    public function index()
    {
        $id_bundle_vap = $this->uri->segment(3);
        $data = array(
            'id_bundle_vap' => isset($id_bundle_vap) ? $id_bundle_vap : "",
            'nokun' => $this->uri->segment(2),
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'listRuangan' => $this->masterModel->ruanganRawatInap(),
            'listPSM' => $this->masterModel->referensi(1483),
            'listKebersihanTangan' => $this->masterModel->referensi(1484),
            'listPosisiKepala' => $this->masterModel->referensi(1485),
            'listMobilisasiMiring' => $this->masterModel->referensi(1486),
            'listOralHygiene' => $this->masterModel->referensi(1487),
            'listMakanan' => $this->masterModel->referensi(1488),
            'listSuction' => $this->masterModel->referensi(1489),
            'listSikatGigi' => $this->masterModel->referensi(1490),
            'listPepticUlcer' => $this->masterModel->referensi(1491),
            'listProfilaksis' => $this->masterModel->referensi(1492),
            'listReviewSedasi' => $this->masterModel->referensi(1493),
            'listControlCuff' => $this->masterModel->referensi(1494),
            'getPengkajian' => $this->BundleVAPModel->getPengkajian($id_bundle_vap)
        );
        $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/bundleVAP', $data); 
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $data = array(
            'id' => $post['id_bundle_vap'],
            'nokun' => $post['nokun'],
            'ruangan' => $post['ruangan'],
            'tanggal' => date('Y-m-d', strtotime($post['tanggal'])),
            'waktu' => $post['waktu'],
            'kebersihan_tangan' => $post['kebersihan_tangan'],
            'posisi_kepala' => $post['posisi_kepala'],
            'mobilisasi_miring' => $post['mobilisasi_miring'],
            'oral_hygiene' => $post['oral_hygiene'],
            'makanan' => $post['makanan'],
            'suction' => $post['suction'],
            'sikat_gigi' => $post['sikat_gigi'],
            'peptic_ulcer' => $post['peptic_ulcer'],
            'profilaksis' => $post['profilaksis'],
            'review_sedasi' => $post['review_sedasi'],
            'control_cuff' => $post['control_cuff'],
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_bundle_vap'])) {
            $this->db->replace('keperawatan.tb_bundle_vap', $data);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          }else{
            $this->db->insert('keperawatan.tb_bundle_vap', $data);

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->BundleVAPModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
      $result = $this->BundleVAPModel->datatables();

      $data = array();
      foreach ($result as $row){
          $sub_array = array();
          $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editBundleVAP" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
          $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
          $sub_array[] = $row -> total_score;   
          $sub_array[] = $row -> total_presentase.' %';   
          $sub_array[] = $row -> oleh;

          $data[] = $sub_array;
      }

      $output = array(
          "draw"              => intval($_POST["draw"]),  
          "recordsTotal"      => $this->BundleVAPModel->total_count(),
          "recordsFiltered"   => $this->BundleVAPModel->filter_count(),
          "data"              => $data
      );
      echo json_encode($output);
    }
    
}


/* End of file FormulirKriteriaRawatIntensif.php */
/* Location: ./application/controllers/igd/FormulirKriteriaRawatIntensif.php */
?>