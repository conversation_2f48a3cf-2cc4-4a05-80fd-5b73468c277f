<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Echoekg extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel'));
    }

    public function index_rawat_inap() {
      $nokun = $this->uri->segment(2);
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $historyEchoEKG = $this->pengkajianAwalModel->historyEchoEKG($getNomr['NORM']);

      $data = array(
        'historyEchoEKG' => $historyEchoEKG,
        'getNomr' => $getNomr
      ); 

      $this->load->view('Pengkajian/echoekg/index', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $dataStatusGizi = array(
            'id' => $post['id_sg'],
            'nokun' => $post['nokun'],
            'diagnosa_masuk' => $post['diagnosa_masuk'],
            'perbuhanan_bb' => isset($post['perbuhanan_bb']) ? $post['perbuhanan_bb'] : "",
            'lama_perubahan_bb' => isset($post['lama_perubahan_bb']) ? $post['lama_perubahan_bb'] : "",
            'bb_biasanya' => isset($post['bb_biasanya']) ? $post['bb_biasanya'] : "",
            'dlm_6_bln' => isset($post['dlm_6_bln']) ? $post['dlm_6_bln'] : "",
            'dlm_6_bln_persen' => isset($post['dlm_6_bln_persen']) ? $post['dlm_6_bln_persen'] : "",
            'imt_psgizi' => isset($post['imt_psgizi']) ? $post['imt_psgizi'] : "",
            'dlmduaminggu' => isset($post['dlmduaminggu']) ? $post['dlmduaminggu'] : "",
            'intake_makanan' => isset($post['intake_makanan']) ? $post['intake_makanan'] : "",
            'lama_intake_makanan' => isset($post['lama_intake_makanan']) ? $post['lama_intake_makanan'] : "",
            'intake_makanan_jenis' => isset($post['intake_makanan_jenis']) ? $post['intake_makanan_jenis'] : "",
            'gastrointestinal' => isset($post['gastrointestinal']) ? $post['gastrointestinal'] : "",
            'lama_gastrointestinal' => isset($post['lama_gastrointestinal']) ? $post['lama_gastrointestinal'] : "",
            'gastrointestinal_jenis' => isset($post['gastrointestinal_jenis']) ? json_encode($post['gastrointestinal_jenis']) : "",
            'fungsional' => isset($post['fungsional']) ? $post['fungsional'] : "",
            'lama_fungsional' => isset($post['lama_fungsional']) ? $post['lama_fungsional'] : "",
            'fungsional_jenis' => isset($post['fungsional_jenis']) ? $post['fungsional_jenis'] : "",
            'diagnosa_kebutuhan_gizi' => isset($post['diagnosa_kebutuhan_gizi']) ? $post['diagnosa_kebutuhan_gizi'] : "",
            'metabolik' => isset($post['metabolik']) ? $post['metabolik'] : "",
            'penilaian_fisik' => isset($post['penilaian_fisik']) ? json_encode($post['penilaian_fisik']) : "",
            'hilang_lemak' => isset($post['hilang_lemak']) ? $post['hilang_lemak'] : "",
            'hilang_masa' => isset($post['hilang_masa']) ? $post['hilang_masa'] : "",
            'oedema_pergelangan' => isset($post['oedema_pergelangan']) ? $post['oedema_pergelangan'] : "",
            'oedema_sacral' => isset($post['oedema_sacral']) ? $post['oedema_sacral'] : "",
            'ascites' => isset($post['ascites']) ? $post['ascites'] : "",
            'penilaian' => isset($post['penilaian']) ? $post['penilaian'] : "",
            'keterangan' => isset($post['keterangan']) ? $post['keterangan'] : "",
            'oleh' => $this->session->userdata('id')
          );

          // echo '<pre>'.print_r($dataStatusGizi).'</pre>';

          $dataTBBB = array(
            'data_source' => '21',
            'ref' => $post['id_sg'],
            'nomr' => isset($post['nomr']) ? $post['nomr'] : "",
            'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
            'jenis' => '2',
            'tb' => isset($post['tb']) ? $post['tb'] : "",
            'bb' => isset($post['bb_sekarang']) ? $post['bb_sekarang'] : "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_sg'])) {
            $this->db->replace('medis.tb_penilaian_status_gizi', $dataStatusGizi);
            $this->db->replace('db_pasien.tb_tb_bb', $dataTBBB);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('medis.tb_penilaian_status_gizi', $dataStatusGizi);
              $this->db->insert('db_pasien.tb_tb_bb', $dataTBBB);

              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->penilaianStatusGiziModel->get_count();;
          echo json_encode($result);
        }
      }
    }

}