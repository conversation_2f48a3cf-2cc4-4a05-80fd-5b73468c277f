<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PEWS extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'FormulirTriaseModel',
        'rekam_medis/rawat_inap/keperawatan/PEWSModel',
      )
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $pasien['NORM'];
    $idEmr = isset($this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr) ? $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr : null;
    $data = array(
      'pasien' => $pasien,
      'nomr' => $nomr,
      'idEmr' => $idEmr,
      'perilaku' => $this->masterModel->referensi(808),
      'kardioVaskuler' => $this->masterModel->referensi(809),
      'pernapasan' => $this->masterModel->referensi(810),
      'listPerawat' => $this->masterModel->listPerawat(),
      'history' => $this->PEWSModel->history($nomr, null),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/PEWS/index', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->PEWSModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $data = array(
            'nomr' => isset($post['nomr']) ? $post['nomr'] : null,
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'waktu' => isset($post['waktu']) ? $post['waktu'] : null,
            'perilaku' => isset($post['perilaku']) ? $post['perilaku'] : null,
            'kardio_vaskuler' => isset($post['kardio_vaskuler']) ? $post['kardio_vaskuler'] : null,
            'pernafasan' => isset($post['pernapasan']) ? $post['pernapasan'] : null,
            'skor' => isset($post['skor']) ? $post['skor'] : null,
            'perawat2' => isset($post['perawat2']) ? $post['perawat2'] : null,
            'oleh' => $this->session->userdata['id'],
            'status' => 1,
          );
          $this->PEWSModel->simpan($data);

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
          echo json_encode($result);
        }
      }
    }
  }

  public function keterangan()
  {
    $nokun = $this->input->post('nokun');
    $data = array(
      'history' => $this->PEWSModel->history(null, $nokun),
      'detailHistory' => $this->PEWSModel->detailHistory($nokun),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/PEWS/keterangan', $data);
  }
}

/* End of file PEWS.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/keperawatan/PEWS.php */