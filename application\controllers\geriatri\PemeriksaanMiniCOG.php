<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PemeriksaanMiniCOG extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }
    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $historyMiniCog = $this->pengkajianAwalModel->historyMiniCog($getNomr['NORM']);
    // $idAdl = $this->uri->segment(8);
    // $getAdl = $this->pengkajianAwalModel->get_adl($idAdl);

    // $history_adl = $this->pengkajianAwalModel->history_adl($getNomr['NORM']);

    $data = array(
      'getNomr' => $getNomr,
      'historyMiniCog' => $historyMiniCog
    );
    
    $this->load->view('Pengkajian/geriatri/pemeriksaanMiniCog/index', $data);
  }

}

