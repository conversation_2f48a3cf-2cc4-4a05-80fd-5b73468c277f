<?php
defined('BASEPATH') or exit('No direct script access allowed');

class OTKB extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');

    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'FormulirTriaseModel',
        'rekam_medis/rawat_inap/brakhiterapi/OTKBModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
        'rekam_medis/rawat_inap/keperawatan/EWSModel',
      )
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array(
      'brakhiterapi' => $this->masterModel->referensi(1360),
      'orientasiEdukasi' => $this->masterModel->referensi(1361),
      'periksaVitalPra' => $this->masterModel->referensi(1362),
      'IV' => $this->masterModel->referensi(1363),
      'suppositoria' => $this->masterModel->referensi(1364),
      'menyiapkan' => $this->masterModel->referensi(1365),
      'operatorAnestesi' => $this->masterModel->referensi(1366),
      'antarTindakan' => $this->masterModel->referensi(1367),
      'teknikAnestesi' => $this->masterModel->referensi(1368),
      'posisiPasien' => $this->masterModel->referensi(1369),
      'oksigenNasal' => $this->masterModel->referensi(1370),
      'monitoringVital' => $this->masterModel->referensi(1371),
      'mendampingiTindakan' => $this->masterModel->referensi(1372),
      'suction' => $this->masterModel->referensi(1373),
      'memberikanObat' => $this->masterModel->referensi(1374),
      'fiksasiAplikator' => $this->masterModel->referensi(1375),
      'jumlahKassa' => $this->masterModel->referensi(1376),
      'taliPengaman' => $this->masterModel->referensi(1377),
      'antarPasien' => $this->masterModel->referensi(1378),
      'antarMicroselectron' => $this->masterModel->referensi(1379),
      'angkatAplikator' => $this->masterModel->referensi(1380),
      'irigasiVagina' => $this->masterModel->referensi(1381),
      'ruangPemulihan' => $this->masterModel->referensi(1382),
      'memberikan' => $this->masterModel->referensi(1383),
      'periksaVitalPasca' => $this->masterModel->referensi(1384),
      'skriningNyeri' => $this->masterModel->referensi(1385),
      'skriningJatuh' => $this->masterModel->referensi(1386),
      'serahTerima' => $this->masterModel->referensi(1387),
    );
    if (isset($id)) {
      // Detail
      $data['id'] = $id;
      $data['detail'] = $this->OTKBModel->history(null, null, 'detail', $id);
      $nokun = $data['detail']['nokun'];
      $idEmr = isset($this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr) ? $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr : null;
      $data['tindakanKeperawatan'] = $this->OTKeperawatanModel->intervensi($idEmr);
      $data['isiTindakan'] = explode('-', $data['detail']['tindakan']);
      $data['isiMenyiapkan'] = explode('-', $data['detail']['menyiapkan']);
      $data['isiMendampingiTindakan'] = explode('-', $data['detail']['mendampingi_tindakan']);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/brakhiterapi/OTKB/detail', $data);
    } else {
      // Form tambah
      $nokun = isset($post['nokun']) ? $post['nokun'] : $this->uri->segment(2);
      $idEmr = isset($this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr) ? $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr : null;
      $pasien = $this->pengkajianAwalModel->getNomr($nokun);
      $data['pasien'] = $pasien;
      $data['tindakanKeperawatan'] = $this->OTKeperawatanModel->intervensi($idEmr);
      $data['jumlahKunjungan'] = $this->OTKBModel->history($nokun, null, 'jumlah', null);
      $data['jumlahTotal'] = $this->OTKBModel->history(null, $pasien['NORM'], 'jumlah', null);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/brakhiterapi/OTKB/index', $data);
    }
  }

  public function simpan($param)
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();

    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah') {
        $rules = $this->OTKBModel->rules;
        $this->form_validation->set_rules($rules);

        // Pra tindakan
        if (!empty($post['check_iv'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesIV);
        }
        if (!empty($post['suppositoria'])) {
          if ($post['suppositoria'] == 4533) {
            $this->form_validation->set_rules($this->OTKBModel->rulesKetSuppositoria);
          }
        }
        if (!empty($post['check_menyiapkan'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesMenyiapkan);
          if (!empty($post['menyiapkan'])) {
            if (in_array('4537', $post['menyiapkan'])) {
              $this->form_validation->set_rules($this->OTKBModel->rulesKetIntra);
            }
            if (in_array('4538', $post['menyiapkan'])) {
              $this->form_validation->set_rules($this->OTKBModel->rulesKetOvoid);
            }
            if (in_array('4541', $post['menyiapkan'])) {
              $this->form_validation->set_rules($this->OTKBModel->rulesKetDiameter);
              $this->form_validation->set_rules($this->OTKBModel->rulesJumlahDiameter);
            }
          }
        }

        // Intra tindakan
        if (!empty($post['check_teknik_anestesi'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesTeknikAnestesi);
          if (!empty($post['teknik_anestesi'])) {
            if ($post['teknik_anestesi'] == 4549) {
              $this->form_validation->set_rules($this->OTKBModel->rulesKetTeknikAnestesi);
            }
          }
        }
        if (!empty($post['check_posisi_pasien'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesPosisiPasien);
        }
        if (!empty($post['oksigen_nasal'])) {
          if ($post['oksigen_nasal'] == 4553) {
            $this->form_validation->set_rules($this->OTKBModel->rulesKetOksigenNasal);
          }
        }
        if (!empty($post['check_mendampingi_tindakan'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesMendampingiTindakan);
        }
        if (!empty($post['memberikan_obat'])) {
          if ($post['memberikan_obat'] == 4561) {
            $this->form_validation->set_rules($this->OTKBModel->rulesKetMemberikanObat);
          }
        }
        if (!empty($post['check_jumlah_kassa'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesKassaMasuk);
          $this->form_validation->set_rules($this->OTKBModel->rulesKassaKeluar);
        }
        if (!empty($post['check_antar_pasien'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesAntarPasien);
        }
        if (!empty($post['angkat_aplikator'])) {
          if ($post['angkat_aplikator'] == 4574) {
            $this->form_validation->set_rules($this->OTKBModel->rulesKetAngkatAplikator);
          }
        }

        // Pasca tindakan
        if (!empty($post['check_memberikan'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesMemberikan);
        }
        if (!empty($post['check_skrining_nyeri'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesSkriningNyeri);
          if (!empty($post['skrining_nyeri'])) {
            if ($post['skrining_nyeri'] != 4583) {
              $this->form_validation->set_rules($this->OTKBModel->rulesKetSkriningNyeri);
            }
          }
        }
        if (!empty($post['check_skrining_jatuh'])) {
          $this->form_validation->set_rules($this->OTKBModel->rulesSkriningJatuh);
        }
        if (!empty($post['serah_terima'])) {
          if ($post['serah_terima'] == 4592) {
            $this->form_validation->set_rules($this->OTKBModel->rulesKetSerahTerima);
          }
        }

        if ($this->form_validation->run() == true) {
          $dataSource = 16;
          $dataSourcePraTindakan = 43;
          $dataSourceIntraTindakan = 44;
          $dataSourcePascaTindakan = 45;
          $nokun = isset($post['nokun']) ? $post['nokun'] : null;
          $nomr = isset($post['nomr']) ? $post['nomr'] : null;
          $tanggal = isset($post['tanggal']) ? $post['tanggal'] : null;
          $jam = isset($post['pukul']) ? $post['pukul'] : null;
          $waktuPraTindakan = isset($post['waktu_pra_tindakan']) ? $post['waktu_pra_tindakan'] : null;
          $waktuIntraTindakan = isset($post['waktu_intra']) ? $post['waktu_intra'] : null;
          $waktuPascaTindakan = isset($post['waktu_pasca_tindakan']) ? $post['waktu_pasca_tindakan'] : null;
          $oleh = $this->session->userdata['id'];
          $status = 1;

          // Pemasukan
          $oral = isset($post['oral']) ? round($post['oral'], 2) : null;
          $parenteral = isset($post['parenteral']) ? round($post['parenteral']) : null;

          // Pengeluaran
          $muntah = isset($post['muntah']) ? round($post['muntah'], 2) : null;
          $bak = isset($post['bak']) ? round($post['bak'], 2) : null;
          $pendarahan = isset($post['pendarahan']) ? round($post['pendarahan'], 2) : null;

          // Balance
          $balance = 0;
          if (isset($balance)) {
            $balance = round($post['balance'], 2);
          } else {
            $pemasukan = $oral + $parenteral;
            $pengeluaran = $muntah + $bak + $pendarahan;
            $balance = $pemasukan - $pengeluaran;
          }

          // Simpan ke observasi
          $dataObservasi = array(
            'nokun' => $nokun,
            'tanggal' => $tanggal,
            'jam' => $jam,
            'oleh' => $oleh,
            'oral' => $oral,
            'parenteral' => $parenteral,
            'muntah' => $muntah,
            'bak' => $bak,
            'pendarahan' => $pendarahan,
            'balance' => $balance,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataObservasi);exit();
          $idObservasi = $this->OTKeperawatanModel->simpanObservasi($dataObservasi);

          // Simpan ke tanda vital
          $dataTandaVital = array(
            'data_source' => $dataSource,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'pukul' => $jam,
            'td_sistolik' => isset($post['td_sistolik']) ? round($post['td_sistolik'], 2) : null,
            'td_diastolik' => isset($post['td_diastolik']) ? round($post['td_diastolik'], 2) : null,
            'nadi' => isset($post['nadi']) ? round($post['nadi'], 2) : null,
            'pernapasan' => isset($post['pernapasan']) ? round($post['pernapasan'], 2) : null,
            'suhu' => isset($post['suhu']) ? round($post['suhu'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataTandaVital);exit();
          $this->OTKeperawatanModel->simpanTandaVital($dataTandaVital);

          // Simpan ke oksigen
          $dataOksigen = array(
            'data_source' => $dataSource,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'saturasi_o2' => isset($post['saturasi_o2']) ? round($post['saturasi_o2'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataOksigen);exit();
          $this->OTKeperawatanModel->simpanOksigen($dataOksigen);

          // Simpan ke tanda vital (pra tindakan)
          $dataTandaVitalPraTindakan = array(
            'data_source' => $dataSourcePraTindakan,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'pukul' => $waktuPraTindakan,
            'td_sistolik' => isset($post['td_sistolik_pra']) ? round($post['td_sistolik_pra'], 2) : null,
            'td_diastolik' => isset($post['td_diastolik_pra']) ? round($post['td_diastolik_pra'], 2) : null,
            'nadi' => isset($post['nadi_pra']) ? round($post['nadi_pra'], 2) : null,
            'pernapasan' => isset($post['pernapasan_pra']) ? round($post['pernapasan_pra'], 2) : null,
            'suhu' => isset($post['suhu_pra']) ? round($post['suhu_pra'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataTandaVital);exit();
          $this->OTKeperawatanModel->simpanTandaVital($dataTandaVitalPraTindakan);

          // Simpan ke oksigen (pra tindakan)
          $dataOksigenPraTindakan = array(
            'data_source' => $dataSourcePraTindakan,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'saturasi_o2' => isset($post['saturasi_o2_pra']) ? round($post['saturasi_o2_pra'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataOksigen);exit();
          $this->OTKeperawatanModel->simpanOksigen($dataOksigenPraTindakan);

          // Simpan ke tanda vital (intra tindakan)
          $dataTandaVitalIntraTindakan = array(
            'data_source' => $dataSourceIntraTindakan,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'pukul' => $waktuIntraTindakan,
            'td_sistolik' => isset($post['td_sistolik_intra']) ? round($post['td_sistolik_intra'], 2) : null,
            'td_diastolik' => isset($post['td_diastolik_intra']) ? round($post['td_diastolik_intra'], 2) : null,
            'nadi' => isset($post['nadi_intra']) ? round($post['nadi_intra'], 2) : null,
            'pernapasan' => isset($post['pernapasan_intra']) ? round($post['pernapasan_intra'], 2) : null,
            'suhu' => isset($post['suhu_intra']) ? round($post['suhu_intra'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataTandaVital);exit();
          $this->OTKeperawatanModel->simpanTandaVital($dataTandaVitalIntraTindakan);

          // Simpan ke oksigen (intra tindakan)
          $dataOksigenIntraTindakan = array(
            'data_source' => $dataSourceIntraTindakan,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'saturasi_o2' => isset($post['saturasi_o2_intra']) ? round($post['saturasi_o2_intra'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataOksigen);exit();
          $this->OTKeperawatanModel->simpanOksigen($dataOksigenIntraTindakan);

          // Simpan ke tanda vital (pasca tindakan)
          $dataTandaVitalPascaTindakan = array(
            'data_source' => $dataSourcePascaTindakan,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'pukul' => $waktuPascaTindakan,
            'td_sistolik' => isset($post['td_sistolik_pasca']) ? round($post['td_sistolik_pasca'], 2) : null,
            'td_diastolik' => isset($post['td_diastolik_pasca']) ? round($post['td_diastolik_pasca'], 2) : null,
            'nadi' => isset($post['nadi_pasca']) ? round($post['nadi_pasca'], 2) : null,
            'pernapasan' => isset($post['pernapasan_pasca']) ? round($post['pernapasan_pasca'], 2) : null,
            'suhu' => isset($post['suhu_pasca']) ? round($post['suhu_pasca'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataTandaVital);exit();
          $this->OTKeperawatanModel->simpanTandaVital($dataTandaVitalPascaTindakan);

          // Simpan ke oksigen (pasca tindakan)
          $dataOksigenPascaTindakan = array(
            'data_source' => $dataSourcePascaTindakan,
            'ref' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'saturasi_o2' => isset($post['saturasi_o2_pasca']) ? round($post['saturasi_o2_pasca'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataOksigen);exit();
          $this->OTKeperawatanModel->simpanOksigen($dataOksigenPascaTindakan);

          // Simpan ke tindakan
          $indexTindakan = 0;
          $dataTindakan = array();
          if (isset($post['tindakan_keperawatan'])) {
            foreach ($post['tindakan_keperawatan'] as $tk) {
              $dataTindakan[$indexTindakan] = array(
                'id_observasi_tindakan' => $idObservasi,
                'id_pak' => $tk,
                'status' => $status,
              );
              $indexTindakan++;
            }
            // echo '<pre>';print_r($dataTindakan);exit();
            $this->OTKeperawatanModel->simpanTindakan($dataTindakan, $idObservasi);
          }

          // Simpan ke observasi dan tindakan keperwatan brakhiterapi
          $data = array(
            'ref' => $idObservasi,
            'brakhiterapi' => isset($post['brakhiterapi']) ? $post['brakhiterapi'] : null,
            'waktu_pra_tindakan' => $waktuPraTindakan,
            'orientasi_edukasi' => isset($post['orientasi_edukasi']) ? $post['orientasi_edukasi'] : null,
            'periksa_vital_pra' => isset($post['periksa_vital_pra']) ? $post['periksa_vital_pra'] : null,
            'iv' => isset($post['iv']) ? $post['iv'] : null,
            'suppositoria' => isset($post['suppositoria']) ? $post['suppositoria'] : null,
            'ket_suppositoria' => isset($post['ket_suppositoria']) ? $post['ket_suppositoria'] : null,
            'menyiapkan' => isset($post['menyiapkan']) ? implode('-', $post['menyiapkan']) : null,
            'ket_intra' => isset($post['ket_intra']) ? round($post['ket_intra'], 2) : null,
            'ket_ovoid' => isset($post['ket_ovoid']) ? $post['ket_ovoid'] : null,
            'ket_diameter' => isset($post['ket_diameter']) ? $post['ket_diameter'] : null,
            'jumlah_diameter' => isset($post['jumlah_diameter']) ? $post['jumlah_diameter'] : null,
            'operator_anestesi' => isset($post['operator_anestesi']) ? $post['operator_anestesi'] : null,
            'antar_tindakan' => isset($post['antar_tindakan']) ? $post['antar_tindakan'] : null,
            'waktu_intra' => $waktuIntraTindakan,
            'teknik_anestesi' => isset($post['teknik_anestesi']) ? $post['teknik_anestesi'] : null,
            'ket_teknik_anestesi' => isset($post['ket_teknik_anestesi']) ? $post['ket_teknik_anestesi'] : null,
            'posisi_pasien' => isset($post['posisi_pasien']) ? $post['posisi_pasien'] : null,
            'oksigen_nasal' => isset($post['oksigen_nasal']) ? $post['oksigen_nasal'] : null,
            'ket_oksigen_nasal' => isset($post['ket_oksigen_nasal']) ? $post['ket_oksigen_nasal'] : null,
            'monitoring_vital' => isset($post['monitoring_vital']) ? $post['monitoring_vital'] : null,
            'mendampingi_tindakan' => isset($post['mendampingi_tindakan']) ? implode('-', $post['mendampingi_tindakan']) : null,
            'suction' => isset($post['suction']) ? $post['suction'] : null,
            'memberikan_obat' => isset($post['memberikan_obat']) ? $post['memberikan_obat'] : null,
            'ket_memberikan_obat' => isset($post['ket_memberikan_obat']) ? $post['ket_memberikan_obat'] : null,
            'fiksasi_aplikator' => isset($post['fiksasi_aplikator']) ? $post['fiksasi_aplikator'] : null,
            'kassa_masuk' => isset($post['kassa_masuk']) ? $post['kassa_masuk'] : null,
            'kassa_keluar' => isset($post['kassa_keluar']) ? $post['kassa_keluar'] : null,
            'tali_pengaman' => isset($post['tali_pengaman']) ? $post['tali_pengaman'] : null,
            'antar_pasien' => isset($post['antar_pasien']) ? $post['antar_pasien'] : null,
            'antar_microselectron' => isset($post['antar_microselectron']) ? $post['antar_microselectron'] : null,
            'angkat_aplikator' => isset($post['angkat_aplikator']) ? $post['angkat_aplikator'] : null,
            'ket_angkat_aplikator' => isset($post['ket_angkat_aplikator']) ? $post['ket_angkat_aplikator'] : null,
            'irigasi_vagina' => isset($post['irigasi_vagina']) ? $post['irigasi_vagina'] : null,
            'ruang_pemulihan' => isset($post['ruang_pemulihan']) ? $post['ruang_pemulihan'] : null,
            'waktu_pasca_tindakan' => $waktuPascaTindakan,
            'memberikan' => isset($post['memberikan']) ? $post['memberikan'] : null,
            'periksa_vital_pasca' => isset($post['periksa_vital_pasca']) ? $post['periksa_vital_pasca'] : null,
            'skrining_nyeri' => isset($post['skrining_nyeri']) ? $post['skrining_nyeri'] : null,
            'ket_skrining_nyeri' => isset($post['ket_skrining_nyeri']) ? $post['ket_skrining_nyeri'] : null,
            'skrining_jatuh' => isset($post['skrining_jatuh']) ? $post['skrining_jatuh'] : null,
            'serah_terima' => isset($post['serah_terima']) ? $post['serah_terima'] : null,
            'ket_serah_terima' => isset($post['ket_serah_terima']) ? $post['ket_serah_terima'] : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataOksigen);exit();
          $this->OTKBModel->simpan($data);

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function historyKunjungan()
  {
    $nokun = $this->input->post('nokun');
    $data = array(
      'history' => $this->OTKBModel->history($nokun, null, 'history', null),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/brakhiterapi/OTKB/history', $data);
  }

  public function historyTotal()
  {
    $nomr = $this->input->post('nomr');
    $data = array(
      'history' => $this->OTKBModel->history(null, $nomr, 'history', null),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/brakhiterapi/OTKB/history', $data);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $idObservasi = $post['id'];
    $dataSource = 16;
    $dataSourcePraTindakan = 43;
    $dataSourceIntraTindakan = 44;
    $dataSourcePascaTindakan = 45;

    $dataObservasi = array(
      'updated_at' => date('Y-m-d H:i:s'),
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahObservasi($idObservasi, $dataObservasi);

    $data = array('status' => 0);
    $this->OTKeperawatanModel->ubahTandaVital($idObservasi, $dataSource, $data);
    $this->OTKeperawatanModel->ubahTandaVital($idObservasi, $dataSourcePraTindakan, $data);
    $this->OTKeperawatanModel->ubahTandaVital($idObservasi, $dataSourceIntraTindakan, $data);
    $this->OTKeperawatanModel->ubahTandaVital($idObservasi, $dataSourcePascaTindakan, $data);
    $this->OTKeperawatanModel->ubahOksigen($idObservasi, $dataSource, $data);
    $this->OTKeperawatanModel->ubahOksigen($idObservasi, $dataSourcePraTindakan, $data);
    $this->OTKeperawatanModel->ubahOksigen($idObservasi, $dataSourceIntraTindakan, $data);
    $this->OTKeperawatanModel->ubahOksigen($idObservasi, $dataSourcePascaTindakan, $data);
    $this->OTKeperawatanModel->ubahTindakan($idObservasi, $data);

    $dataOTKB = array('status' => 0);
    $this->OTKBModel->ubah($idObservasi, $dataOTKB);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}

/* End of file OTKB.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/brakhiterapi/OTKB.php */