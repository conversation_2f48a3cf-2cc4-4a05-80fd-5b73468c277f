<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Monitor CPPT Controller
 * 
 * Controller untuk men<PERSON><PERSON> monitoring CPPT dengan security fixes
 * dan implementasi Plan of Care
 * 
 * <AUTHOR> Development Team
 * @version 2.0
 */
class MonitorCppt extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        
        // Check if user is logged in
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        // Load required models
        $this->load->model('MonitorCpptModel');
        $this->load->model('masterModel');
        
        // Load helper for security
        $this->load->helper(['security', 'html']);
        
        // Set timezone
        date_default_timezone_set('Asia/Jakarta');
    }

    /**
     * Main index page for Monitor CPPT with tabs
     */
    public function index()
    {
        $data = [
            'title' => 'Monitor CPPT',
            'isi' => 'MonitorCPPT/index',
            'breadcrumb' => 'Monitor CPPT'
        ];
        
        $this->load->view('layout/wrapper', $data);
    }

    /**
     * Get data for DataTables AJAX request (Monitor CPPT Tab)
     * 
     * Mengambil data dengan security validation dan error handling
     */
    public function get_data()
    {
        // Set content type to JSON
        $this->output->set_content_type('application/json');
        
        try {
            // Validate and sanitize DataTables parameters
            $draw = $this->_validateInteger($this->input->post('draw'), 1);
            $start = $this->_validateInteger($this->input->post('start'), 0);
            $length = $this->_validateInteger($this->input->post('length'), 25);
            
            // Sanitize search value
            $searchInput = $this->input->post('search');
            $searchValue = isset($searchInput['value']) ? $this->_sanitizeInput($searchInput['value']) : '';
            
            // Get ordering parameters with validation
            $orderColumn = '';
            $orderDir = 'asc';
            $orderInput = $this->input->post('order');
            if (!empty($orderInput) && is_array($orderInput)) {
                $orderColumnIndex = $this->_validateInteger($orderInput[0]['column'] ?? 0, 0);
                $orderDir = in_array($orderInput[0]['dir'] ?? 'asc', ['asc', 'desc']) ? $orderInput[0]['dir'] : 'asc';
                
                // Map column index to actual column name
                $columns = ['nomor_pendaftaran', 'NORM', 'pasien', 'nomor_pendaftaran', 'tanggal_rawat_berjalan', 'DPJP'];
                $orderColumn = $columns[$orderColumnIndex] ?? '';
            }

            // Get data from model with security parameters
            $list = $this->MonitorCpptModel->get_filtered_data($start, $length, $searchValue, $orderColumn, $orderDir);
            $totalRecords = $this->MonitorCpptModel->count_all();
            $filteredRecords = $this->MonitorCpptModel->count_filtered($searchValue);

            // Process data for DataTables with XSS protection
            $data = [];
            $no = $start + 1;
            
            foreach ($list as $item) {
                $row = [];
                $row[] = $no++;
                $row[] = $this->_escapeOutput($item->NORM ?? '');
                $row[] = $this->_escapeOutput($item->pasien ?? '');
                $row[] = $this->_escapeOutput($item->nomor_pendaftaran ?? '');
                $row[] = $this->_escapeOutput($item->tanggal_rawat_berjalan_formatted ?? '');
                $row[] = $this->_escapeOutput($item->RUANGAN_RAWAT ?? '');
                $row[] = $this->_escapeOutput($item->DPJP ?? '');
                
                // Format CPPT status with badge (safe HTML)
                $cpptStatus = $this->_escapeOutput($item->CPPT ?? 'Tidak Ada CPPT');
                if ($cpptStatus == 'Ada CPPT') {
                    $row[] = '<span class="badge badge-success">' . $cpptStatus . '</span>';
                } else {
                    $row[] = '<span class="badge badge-warning">' . $cpptStatus . '</span>';
                }
                
                // Add action buttons for Plan of Care
                $actions = $this->_generateActionButtons($item->NORM ?? '');
                $row[] = $actions;
                
                $data[] = $row;
            }

            // Prepare response
            $response = [
                "draw" => $draw,
                "recordsTotal" => $totalRecords,
                "recordsFiltered" => $filteredRecords,
                "data" => $data
            ];

            echo json_encode($response);
            
        } catch (Exception $e) {
            // Log error and return safe error response
            log_message('error', 'MonitorCppt::get_data - ' . $e->getMessage());
            
            $response = [
                "draw" => $draw ?? 1,
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => [],
                "error" => "Terjadi kesalahan dalam memuat data"
            ];
            echo json_encode($response);
        }
    }

    /**
     * Get Plan of Care data - Show ALL data as requested by user
     */
    public function get_plan_of_care_data()
    {
        $this->output->set_content_type('application/json');
        
        try {
            // Get pagination parameters from request
            $start = $this->_validateInteger($this->input->post('start'), 0);
            $length = $this->_validateInteger($this->input->post('length'), 100);
            
            // Get ALL Plan of Care data (no nomr filter as requested) with pagination
            $planOfCareData = $this->MonitorCpptModel->historyPlanOfCare(null, $length, $start);

            // Process data with XSS protection
            $data = [];
            $no = $start + 1;
            
            foreach ($planOfCareData as $item) {
                $row = [];
                $row[] = $no++;
                $row[] = $this->_escapeOutput($item->nomr ?? ''); // Tambah kolom nomr
                $row[] = $this->_escapeOutput($item->nama_pasien ?? ''); // Tambah nama pasien
                $row[] = $this->_escapeOutput($item->tanggal_formatted ?? '');
                $row[] = $this->_escapeOutput($item->diagnosis ?? '');
                $row[] = $this->_escapeOutput($item->planning ?? '');
                $row[] = $this->_escapeOutput($item->target_outcome ?? '');
                $row[] = $this->_escapeOutput($item->evaluation ?? '');
                $row[] = $this->_formatStatus($item->status ?? '');
                $row[] = $this->_escapeOutput($item->pembuat ?? '');
                
                // Add detail button
                $row[] = '<button type="button" class="btn btn-sm btn-info" onclick="viewPlanDetail(' .
                         (int)$item->id . ')" title="Lihat Detail">
                         <i class="fas fa-eye"></i></button>';
                
                $data[] = $row;
            }

            // Get total records for pagination (this is an approximation since we don't have a separate count method)
            $totalRecords = count($data) < $length ? $start + count($data) : $start + $length + 1;

            $response = [
                "success" => true,
                "data" => $data,
                "total_records" => $totalRecords
            ];

            echo json_encode($response);
            
        } catch (Exception $e) {
            log_message('error', 'MonitorCppt::get_plan_of_care_data - ' . $e->getMessage() . ' - Stack trace: ' . $e->getTraceAsString());
            
            $response = [
                "success" => false,
                "message" => "Terjadi kesalahan dalam memuat data Plan of Care: " . $e->getMessage()
            ];
            echo json_encode($response);
        }
    }

    /**
     * Get Plan of Care detail by ID
     */
    public function get_plan_detail()
    {
        $this->output->set_content_type('application/json');
        
        try {
            // Validate and sanitize input
            $id = $this->_validateInteger($this->input->post('id'), 0);
            
            if ($id <= 0) {
                throw new Exception('ID Plan of Care tidak valid');
            }

            // Get Plan of Care detail
            $detail = $this->MonitorCpptModel->HistoryDetailPlanOfCare($id);
            
            if (!$detail) {
                throw new Exception('Data Plan of Care tidak ditemukan');
            }

            // Prepare response with XSS protection
            $response = [
                "success" => true,
                "data" => [
                    "id" => (int)$detail->id,
                    "nomr" => $this->_escapeOutput($detail->nomr),
                    "nama_pasien" => $this->_escapeOutput($detail->nama_pasien),
                    "tanggal" => $this->_escapeOutput($detail->tanggal_formatted),
                    "diagnosis" => $this->_escapeOutput($detail->diagnosis),
                    "planning" => $this->_escapeOutput($detail->planning),
                    "target_outcome" => $this->_escapeOutput($detail->target_outcome),
                    "evaluation" => $this->_escapeOutput($detail->evaluation),
                    "notes" => $this->_escapeOutput($detail->notes ?? ''),
                    "status" => $this->_escapeOutput($detail->status),
                    "pembuat" => $this->_escapeOutput($detail->pembuat),
                    "created_at" => $this->_escapeOutput($detail->created_at_formatted),
                    "updated_at" => $this->_escapeOutput($detail->updated_at_formatted)
                ]
            ];

            echo json_encode($response);
            
        } catch (Exception $e) {
            log_message('error', 'MonitorCppt::get_plan_detail - ' . $e->getMessage());
            
            $response = [
                "success" => false,
                "message" => $e->getMessage()
            ];
            echo json_encode($response);
        }
    }

    /**
     * Get all data for export without server-side pagination
     */
    public function get_all_data_for_export()
    {
        $this->output->set_content_type('application/json');
        
        try {
            // Get search value from request
            $searchInput = $this->input->post('search');
            $searchValue = isset($searchInput['value']) ? $this->_sanitizeInput($searchInput['value']) : '';
            
            // Get ordering parameters with validation
            $orderColumn = '';
            $orderDir = 'asc';
            $orderInput = $this->input->post('order');
            if (!empty($orderInput) && is_array($orderInput)) {
                $orderColumnIndex = $this->_validateInteger($orderInput[0]['column'] ?? 0, 0);
                $orderDir = in_array($orderInput[0]['dir'] ?? 'asc', ['asc', 'desc']) ? $orderInput[0]['dir'] : 'asc';
                
                // Map column index to actual column name
                $columns = ['nomor_pendaftaran', 'NORM', 'pasien', 'nomor_pendaftaran', 'tanggal_rawat_berjalan', 'DPJP'];
                $orderColumn = $columns[$orderColumnIndex] ?? '';
            }

            // Get all data without pagination
            $list = $this->MonitorCpptModel->get_filtered_data(0, -1, $searchValue, $orderColumn, $orderDir);
            $totalRecords = $this->MonitorCpptModel->count_all();
            $filteredRecords = $this->MonitorCpptModel->count_filtered($searchValue);

            // Process data for export with XSS protection
            $data = [];
            $no = 1;
            
            foreach ($list as $item) {
                $row = [];
                $row[] = $no++;
                $row[] = $this->_escapeOutput($item->NORM ?? '');
                $row[] = $this->_escapeOutput($item->pasien ?? '');
                $row[] = $this->_escapeOutput($item->nomor_pendaftaran ?? '');
                $row[] = $this->_escapeOutput($item->tanggal_rawat_berjalan_formatted ?? '');
                $row[] = $this->_escapeOutput($item->RUANGAN_RAWAT ?? '');
                $row[] = $this->_escapeOutput($item->DPJP ?? '');
                
                // Format CPPT status with badge (safe HTML)
                $cpptStatus = $this->_escapeOutput($item->CPPT ?? 'Tidak Ada CPPT');
                if ($cpptStatus == 'Ada CPPT') {
                    $row[] = '<span class="badge badge-success">' . $cpptStatus . '</span>';
                } else {
                    $row[] = '<span class="badge badge-warning">' . $cpptStatus . '</span>';
                }
                
                $data[] = $row;
            }

            // Prepare response
            $response = [
                "draw" => 1,
                "recordsTotal" => $totalRecords,
                "recordsFiltered" => $filteredRecords,
                "data" => $data
            ];

            echo json_encode($response);
            
        } catch (Exception $e) {
            // Log error and return safe error response
            log_message('error', 'MonitorCppt::get_all_data_for_export - ' . $e->getMessage());
            
            $response = [
                "draw" => 1,
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => [],
                "error" => "Terjadi kesalahan dalam memuat data untuk ekspor"
            ];
            echo json_encode($response);
        }
    }

    /**
     * Legacy export method - now handled by client-side SheetJS
     * Kept for backward compatibility, redirects to main page
     */
    public function export_excel()
    {
        // Redirect back to main page since export is now handled client-side
        redirect('MonitorCppt');
    }

    /**
     * Validate integer input with default value
     * 
     * @param mixed $value Input value to validate
     * @param int $default Default value if validation fails
     * @return int Validated integer
     */
    private function _validateInteger($value, $default = 0)
    {
        return is_numeric($value) && $value >= 0 ? (int)$value : $default;
    }

    /**
     * Sanitize input to prevent XSS and injection
     * 
     * @param string $input Input string to sanitize
     * @return string Sanitized string
     */
    private function _sanitizeInput($input)
    {
        if (empty($input)) {
            return '';
        }
        
        // Remove potentially dangerous characters
        $input = trim($input);
        $input = strip_tags($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        return $input;
    }

    /**
     * Escape output to prevent XSS
     * 
     * @param string $output Output string to escape
     * @return string Escaped string
     */
    private function _escapeOutput($output)
    {
        return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Format status with appropriate styling
     * 
     * @param string $status Status value
     * @return string Formatted status HTML
     */
    private function _formatStatus($status)
    {
        $status = $this->_escapeOutput($status);
        
        switch ($status) {
            case 'active':
                return '<span class="badge badge-success">Aktif</span>';
            case 'completed':
                return '<span class="badge badge-primary">Selesai</span>';
            case 'cancelled':
                return '<span class="badge badge-danger">Dibatalkan</span>';
            default:
                return '<span class="badge badge-secondary">' . $status . '</span>';
        }
    }

    /**
     * Generate action buttons for each row
     * 
     * @param string $nomr Patient medical record number
     * @return string Action buttons HTML
     */
    private function _generateActionButtons($nomr)
    {
        $nomr = $this->_escapeOutput($nomr);
        
        return '<button type="button" class="btn btn-sm btn-info" onclick="viewPlanOfCare(\'' . 
               $nomr . '\')" title="Lihat Plan of Care">
               <i class="fas fa-clipboard-list"></i></button>';
    }

    /**
     * Search patients for Select2 dropdown in Plan of Care tab
     */
    public function search_patients()
    {
        $this->output->set_content_type('application/json');
        
        try {
            // Validate and sanitize input
            $term = $this->_sanitizeInput($this->input->post('term'));
            $page = $this->_validateInteger($this->input->post('page'), 1);
            
            if (strlen($term) < 2) {
                echo json_encode(['items' => [], 'total_count' => 0]);
                return;
            }

            // Search patients with security parameters
            $patients = $this->MonitorCpptModel->searchActivePatients($term, $page);
            
            // Format results for Select2
            $items = [];
            foreach ($patients as $patient) {
                $items[] = [
                    'id' => $this->_escapeOutput($patient->NORM),
                    'text' => $this->_escapeOutput($patient->NORM . ' - ' . $patient->nama_pasien)
                ];
            }

            $response = [
                'items' => $items,
                'total_count' => count($items)
            ];

            echo json_encode($response);
            
        } catch (Exception $e) {
            log_message('error', 'MonitorCppt::search_patients - ' . $e->getMessage());
            
            $response = [
                'items' => [],
                'total_count' => 0
            ];
            echo json_encode($response);
        }
    }

    /**
     * Check if user has export permission
     *
     * @return bool True if user has permission
     */
    private function _hasExportPermission()
    {
        // Get user role/permissions from session
        $userRole = $this->session->userdata('logged_in') == true;
        
        // Define roles that can export
        $allowedRoles = [1, 2];
        
        return in_array($userRole, $allowedRoles);
    }
}
