<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON><PERSON> extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if(!in_array(49,$this->session->userdata('akses'))){
            redirect('login');
        }

        $this->load->model(array('MasterModel','whatsapp/WhatsappDokterModel'));
        $this->load->library('whatsapp');
    }

    public function index() {

        // $ruangan = $this->masterModel->ruanganRskd();
        $data = array(
        'title' => 'Halaman WhatsApp Dokter',
        'isi'   => 'Whatsapp/dokter'
        );
        $this->load->view('layout/wrapper',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->WhatsappDokterModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $nomor = '+62'.substr(trim($post['nomor']), 1);
                    $selamat = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")));
                    $pasien = explode("^",$post['pasien']);

                    $res = $this->whatsapp->send($nomor, array($selamat,"kami sampaikan",$post['pesan']." atas nama pasien ".$pasien[1]."/ ".$pasien[0]."-".$post['ruangan']));
                    $res = json_encode($res['data']['data']);
                    $message = json_decode($res);
                    // $this->db->trans_begin();

                    // if ($this->db->trans_status() === false) {
                    //     $this->db->trans_rollback();
                    //     $result = array('status' => 'failed');
                    // } else {
                    //     $this->db->trans_commit();
                    //     $result = array('status' => 'success');
                    // }

                    if($message[0]->status == 'failed') {
            			echo json_encode(array('status' => 'failed', 'message' => $message[0]->message));
                        exit;
                    }
                    
                    $data = array(
                        'ID_DOKTER' => $post['dokter'],
                        'NOMOR' => $post['nomor'],
                        'NORM' => $pasien[0],
                        'PESAN' => $post['pesan'],
                        'KIRIM' => $message[0]->status,
                        'OLEH' => $this->session->userdata("id"),
                    );

                    $this->db->insert('layanan.wa_dokter', $data);

                    if($this->db->affected_rows() > 0){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }

    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->WhatsappDokterModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            // $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_pengkajian_luka" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> DOKTER;
            $sub_array[] = $row -> PASIEN;      
            $sub_array[] = $row -> PESAN;
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> OLEH;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->WhatsappDokterModel->total_count(),
            "recordsFiltered"   => $this->WhatsappDokterModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function dokter()
    {
        $result = $this->MasterModel->listDr();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID_DOKTER'];
            $sub_array['text'] = $row['DOKTER'];
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
        );
        echo json_encode($data);
    }

    public function pasien()
    {
        $result = $this->MasterModel->pasien();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['NORM']."^".$row['NAMA'];
            $sub_array['text'] = $row['NAMA'];
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
        );
        echo json_encode($data);
    }

    public function nomor()
    {
        $id = $this->input->post('id');
        $data = $this->MasterModel->nomorDokter($id);

        echo json_encode($data->NOMOR);
    }

    public function send(){
        $this->whatsapp->send('6281298652366', 'pesan');
    }
}