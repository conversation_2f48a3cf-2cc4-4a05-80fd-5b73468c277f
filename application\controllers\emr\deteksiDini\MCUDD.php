<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MCUDD extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            array(
                'masterModel',
                'pengkajianAwalModel',
                'emr/deteksiDini/MCUDDModel',
                'rekam_medis/TbBbModel',
                'rekam_medis/TandaVitalModel',
                'emr/deteksiDini/RDDModel',
                'emr/deteksiDini/RDDSitologiModel',
                'emr/deteksiDini/RDDHistologiModel',
                'emr/deteksiDini/RDDRadiologiModel',
                'emr/deteksiDini/RPKUModel',
                'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel'
            )
        );
    }

    public function index()
    {
        $id = $this->input->post('id') ?? null;
        $nomr = $this->uri->segment(5);
        $nopen = $this->uri->segment(6);
        $nokun = $this->uri->segment(7);
        $data = array(
            'nomr' => $nomr,
            'nopen' => $nopen,
            'nokun' => $nokun,
            'jumlah' => $this->MCUDDModel->jumlah($nomr)
        );

        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/emr/deteksiDini/MCUDD/index', $data);
    }

    public function simpan()
    {
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->MCUDDModel->rules);
            if ($this->form_validation->run() == true) {
                $post = $this->input->post();

                // Mulai ambil data
                $data = array(
                    'nokun' => $post['nokun'] ?? null,
                    'tanggal' => $post['tanggal'] ?? null,
                    'no_mcu' => $post['no_mcu'] ?? null,
                    'penilaian' => $post['penilaian'] ?? null,
                    'kesimpulan' => $post['kesimpulan'] ?? null,
                    'saran' => $post['saran'] ?? null,
                    'catatan' => $post['catatan'] ?? null,
                );
                // Akhir ambil data

                // Mulai proses
                if (!empty($post['id'])) { // Ubah data
                    $this->MCUDDModel->ubah($post['id'], $data);
                } else { // Simpan data
                    $data['status'] = 1;
                    $data['oleh'] = $this->session->userdata['id'];
                    // echo '<pre>';print_r($data);exit();
                    $this->MCUDDModel->simpan($data);
                }
                // Akhir proses

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
            } else {
                $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
            }
            echo json_encode($result);
        }
    }

    public function history()
    {
        $post = $this->input->post();
        $data = array(
            'nomr' => $post['nomr'],
        );
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/emr/deteksiDini/MCUDD/history', $data);
    }

    public function tabel()
    {
        $draw = intval($this->input->post('draw'));
        $nomr = $this->input->post('nomr');
        $history = $this->MCUDDModel->history($nomr);
        $data = array();
        $no = 1;
        $disabled = null;
        $status = null;

        foreach ($history->result() as $h) {
            if ($h->status == 0) {
                $disabled = 'disabled';
                $status = '<p class="text-danger">Dibatalkan</p>';
            } elseif ($h->status == 1) {
                $disabled = null;
                $status = '<p class="text-success">Diterima</p>';
            }

            $data[] = array(
                $no++,
                date('d-m-Y', strtotime($h->tanggal)),
                $h->no_mcu,
                $h->pengirim,
                $status,
                "<div class='btn-group' role='group'>
                    <button type='button' class='btn btn-sm btn-danger waves-effect tbl-batal-mcudd' data-id='" . $h->id . "' $disabled>
                        <i class='fa fa-window-close'></i> Batal
                    </button>
                    <button type='button' class='btn btn-sm btn-warning waves-effect tbl-ubah-mcudd' data-id='" . $h->id . "' $disabled>
                        <i class='fas fa-pencil-alt'></i> Ubah
                    </button>
                </div>",
            );
        }

        $output = array(
            'draw' => $draw,
            'recordsTotal' => $history->num_rows(),
            'recordsFiltered' => $history->num_rows(),
            'data' => $data
        );
        echo json_encode($output);
    }

    public function ambil()
    {
        $data = $this->MCUDDModel->detail($this->input->post('id'));
        // echo '<pre>';print_r($data);exit();
        echo json_encode(
            array(
                'status' => 'success',
                'detail' => $data,
            )
        );
    }

    public function batal()
    {
        $this->db->trans_begin();
        $id = $this->input->post('id');
        $data = array('status' => 0);
        $this->MCUDDModel->ubah($id, $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }
        echo json_encode($result);
    }
    public function rpku()
    {
        $nokun = $this->uri->segment(7);
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);
        $nomr = $pasien['NORM'];
        $statusPengguna = $_SESSION['status'];
        $id_pengguna = $this->session->userdata('id');
        $olehDok = $this->session->userdata('iddokter');
        $tandaVitalCppt = $this->pengkajianAwalModel->tandaVitalCppt($nokun);

        // Ambil data RPKU berdasarkan nokun
        $dataRPKU = $this->RPKUModel->getByNokun($nokun);

        $data = [
            'nomr' => $nomr,
            'nokun' => $nokun,
            'pasien' => $pasien,
            'id_pengguna' => $id_pengguna,
            'statusPengguna' => $statusPengguna,
            'olehDok' => $olehDok,
            'tandaVitalCppt' => $tandaVitalCppt,
            'dataRPKU' => $dataRPKU,
            'jumlah' => $this->RPKUModel->jumlah($nomr)
        ];

        $this->load->view('Pengkajian/emr/deteksiDini/MCUDD/rpku', $data);
    }
    public function simpanRPKU()
    {
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->form_validation->set_rules($this->RPKUModel->rules);
            if ($this->form_validation->run() == true) {
                $post = $this->input->post();
                $statusPengguna = $post['statusPengguna'] ?? $this->session->userdata('statusPengguna');

                // Mulai ambil data
                $data = array(
                    'id_pengguna' => $post['id_pengguna'] ?? null,
                    'olehDok' => $post['olehDok'] ?? null,
                    'nokun' => $post['nokun'] ?? null,
                    'nomr' => $post['nomr'] ?? null,
                    'tanggal' => $post['tanggal'] ?? null,
                    'waktu' => $post['waktu'] ?? null,
                    'jenis_pemeriksaan' => $post['jenis_pemeriksaan'] ?? null,
                    'alergi' => $post['alergi'] ?? null,
                    'alergi_keterangan' => $post['alergi_keterangan'] ?? null,
                    'keluhan' => $post['keluhan'] ?? null,
                    'penyakit_sekarang' => $post['penyakit_sekarang'] ?? null,
                    'penyakit_dahulu' => $post['penyakit_dahulu'] ?? null,
                    'jenis_pekerjaan' => $post['jenis_pekerjaan'] ?? null,
                    'unit_kerja' => $post['unit_kerja'] ?? null,
                    'lama_bekerja_bulan' => $post['lama_bekerja_bulan'] ?? null,
                    'lama_bekerja_tahun' => $post['lama_bekerja_tahun'] ?? null,
                    'riwayat_psiko_sosial' => $post['riwayat_psiko_sosial'] ?? null,
                    'riwayat_imunisasi_dewasa' => $post['riwayat_imunisasi_dewasa'] ?? null,
                    'tb' => $post['tb'] ?? null,
                    'bb' => $post['bb'] ?? null,
                    'imt' => $post['imt'] ?? null,
                    'td_sistolik' => $post['td_sistolik'] ?? null,
                    'td_diastolik' => $post['td_diastolik'] ?? null,
                    'nadi' => $post['nadi'] ?? null,
                    'pernapasan' => $post['pernapasan'] ?? null,
                    'suhu' => $post['suhu'] ?? null,
                    'kulit' => $post['kulit'] ?? null,
                    'mata' => $post['mata'] ?? null,
                    'tht' => $post['tht'] ?? null,
                    'toraks' => $post['toraks'] ?? null,
                    'jantung' => $post['jantung'] ?? null,
                    'paru' => $post['paru'] ?? null,
                    'abdomen' => $post['abdomen'] ?? null,
                    'ekstremitas' => $post['ekstremitas'] ?? null,
                    'laboratorium' => $post['laboratorium'] ?? null,
                    'laboratorium_keterangan' => $post['laboratorium_keterangan'] ?? null,
                    'radiologi' => $post['radiologi'] ?? null,
                    'radiologi_keterangan' => $post['radiologi_keterangan'] ?? null,
                    'ekg' => $post['ekg'] ?? null,
                    'ekg_keterangan' => $post['ekg_keterangan'] ?? null,
                    'lain' => $post['lain'] ?? null,
                    'lain_keterangan' => $post['lain_keterangan'] ?? null,
                    'kesimpulan' => $post['kesimpulan'] ?? null,
                    'saran' => $post['saran'] ?? null,
                );
                // Akhir ambil data

                // Filter data berdasarkan statusPengguna
                if ($statusPengguna == 1) {
                    $allowedFields = array(
                        'olehDok',
                        'nokun',
                        'nomr',
                        'tanggal',
                        'waktu',
                        'keluhan',
                        'penyakit_sekarang',
                        'penyakit_dahulu',
                        'kulit',
                        'mata',
                        'tht',
                        'toraks',
                        'jantung',
                        'paru',
                        'abdomen',
                        'ekstremitas',
                        'laboratorium',
                        'laboratorium_keterangan',
                        'radiologi',
                        'radiologi_keterangan',
                        'ekg',
                        'ekg_keterangan',
                        'lain',
                        'lain_keterangan',
                        'kesimpulan',
                        'saran'
                    );
                } elseif ($statusPengguna == 2) {
                    $allowedFields = array(
                        'nokun',
                        'nomr',
                        'tanggal',
                        'waktu',
                        'id_pengguna',
                        'jenis_pemeriksaan',
                        'alergi',
                        'alergi_keterangan',
                        'keluhan',
                        'penyakit_sekarang',
                        'penyakit_dahulu',
                        'jenis_pekerjaan',
                        'unit_kerja',
                        'lama_bekerja_bulan',
                        'lama_bekerja_tahun',
                        'riwayat_psiko_sosial',
                        'riwayat_imunisasi_dewasa',
                        'tb',
                        'bb',
                        'imt',
                        'td_sistolik',
                        'td_diastolik',
                        'nadi',
                        'pernapasan',
                        'suhu'
                    );
                } else {
                    $allowedFields = array();
                }

                // Hapus field yang tidak diizinkan
                foreach ($data as $key => $value) {
                    if (!in_array($key, $allowedFields)) {
                        unset($data[$key]);
                    }
                }

                // Mulai proses
                if (!empty($post['id'])) { // Ubah data
                    $this->RPKUModel->ubah($post['id'], $data, $statusPengguna);
                } else { // Simpan data
                    $data['status'] = 1;
                    $data['olehDok'] = $this->session->userdata('iddokter');
                    $this->RPKUModel->simpan($data, $statusPengguna);
                }
                // Akhir proses

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
            } else {
                $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
            }
            echo json_encode($result);
        }
    }

    public function getDataByNokun($nokun)
    {
        $data = $this->RPKUModel->getByNokun($nokun);
        echo json_encode($data);
    }

    public function historyRPKU()
    {
        $post = $this->input->post();
        $data = array(
            'nomr' => $post['nomr'],
        );
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/emr/deteksiDini/MCUDD/historyRPKU', $data);
    }

    public function tabelRPKU()
    {
        $draw = intval($this->input->post('draw'));
        $nomr = $this->input->post('nomr');
        $history = $this->RPKUModel->history($nomr);
        $data = array();
        $no = 1;
        $disabled = null;
        $status = null;

        foreach ($history->result() as $h) {
            if ($h->status == 0) {
                $disabled = 'disabled';
                $status = '<p class="text-danger">Dibatalkan</p>';
            } elseif ($h->status == 1) {
                $disabled = null;
                $status = '<p class="text-success">Diterima</p>';
            }

            // Tambahkan tombol cetak jika status == 1
            $cetakButton = '';
            if ($h->status == 1) {
                $cetakButton = "<button type='button' class='btn btn-sm btn-info waves-effect tbl-cetak-rpku' onclick=\"window.open('http://192.168.7.103/reports/simrskd/resumemedispku/resume.php?format=pdf&ID=" . $h->id . "', '_blank')\">
                                    <i class='fa fa-print'></i> Cetak
                                </button>";
            }

            $data[] = array(
                $no++,
                date('d-m-Y', strtotime($h->tanggal)),
                $h->nokun,
                $h->id_pengguna,
                $h->olehDok,
                $status,
                "<div class='btn-group' role='group'>
                    <button type='button' class='btn btn-sm btn-danger waves-effect tbl-batal-rpku' data-id='" . $h->id . "' $disabled>
                        <i class='fa fa-window-close'></i> Batal
                    </button>
                    <button type='button' class='btn btn-sm btn-warning waves-effect tbl-ubah-rpku' data-id='" . $h->id . "' $disabled>
                        <i class='fas fa-pencil-alt'></i> Ubah
                    </button>
                    $cetakButton
                </div>",
            );
        }

        $output = array(
            'draw' => $draw,
            'recordsTotal' => $history->num_rows(),
            'recordsFiltered' => $history->num_rows(),
            'data' => $data
        );
        echo json_encode($output);
    }

    public function batalRPKU()
    {
        $id = $this->input->post('id');
        $this->RPKUModel->batal($id);
        echo json_encode(array('status' => 'success'));
    }

    public function ambilRPKU()
    {
        $id = $this->input->post('id');
        $data = $this->RPKUModel->getRPKUById($id);

        if ($data) {
            echo json_encode(array('status' => 'success', 'detail' => $data));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }
}

/* End of file MCUDD.php */
/* Location: ./application/controllers/emr/deteksiDini/MCUDD.php */