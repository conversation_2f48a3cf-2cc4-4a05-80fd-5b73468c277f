<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DPendapatanPerInst extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('keuanganModel','masterModel'));
  }

  public function index()
  {
    $dashboardRuangan = $this->keuanganModel->dashboardRuangan();
    $pendapatanAll    = $this->keuanganModel->pendapatanAll();
    
    $data = array(
      'title'            => 'Halaman Sistem Informasi Manajemen Anggaran',
      'isi'              => 'Keuangan/dashboard/dashboardPendapatanPerInstalasi',
      'dashboardRuangan' => $dashboardRuangan,
      'pendapatanAll'    => $pendapatanAll,
    );
    // echo "<pre>";print_r($data);exit();
    $this->load->view('layout/wrapper',$data);
  }

}

/* End of file DPendapatanPerInst.php */
/* Location: ./application/controllers/keuangan/DPendapatanPerInst.php */