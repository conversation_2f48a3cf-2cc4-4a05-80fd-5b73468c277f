<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TandaVital_SA extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function simpanFormTandaVital_SA()
    {
        $kunjungan = $this->input->post("nokun");
        $napas = $this->input->post("napas");
        $nadi = $this->input->post("nadi");
        $sistolik = $this->input->post("sistolik");
        $diastolik = $this->input->post("diastolik");
        $waktu = $this->input->post("waktu");

        $data = array(
            'nokun' => $kunjungan,
            'napas' => $napas,
            'nadi' => $nadi,
            'sistolik' => $sistolik,
            'diastolik' => $diastolik,
            'waktu' => $waktu,
        );

        $tandavital_sa = $this->pengkajianAwalModel->insertFormTandaVital_SA($data);
    }

    public function lihatHistoryTandaVital_SA()
    {
        $id = $this->input->post('id');

        $HistoryTandaVital_SA = $this->pengkajianAwalModel->HistoryDetailTandaVital_SA($id);
        $waktu = array();
        $napas = array();
        $nadi = array();
        $sistolik = array();
        $diastolik = array();
        foreach ($HistoryTandaVital_SA as $historyTandaVital_SA):
            array_push($waktu,$historyTandaVital_SA['waktu']);
            array_push($napas,$historyTandaVital_SA['napas']);
            array_push($nadi,$historyTandaVital_SA['nadi']);
            array_push($sistolik,$historyTandaVital_SA['sistolik']);
            array_push($diastolik,$historyTandaVital_SA['diastolik']);

        endforeach;
        echo '<div id="chartTandaVital_SA"></div>';
        echo "<script>

                $('#chartTandaVital_SA').highcharts({
                    chart: {
                        type: 'spline'
                    },
                
                    legend: {
                        symbolWidth: 40
                    },
                
                    title: {
                        text: 'Monitoring Tanda Vital Status Anestesia'
                    },
                
                    yAxis: {
                        title: {
                            text: 'Nilai'
                        }
                    },
                
                    xAxis: {
                        title: {
                            text: 'Waktu'
                        },
                        accessibility: {
                            description: 'Waktu Monitoring Tanda Vital Status Anestesia'
                        },
                        categories: ".json_encode($waktu).",
                    },
                
                    tooltip: {
                        split: true
                    },
                
                    plotOptions: {
                        series: {
                            point: {
                                events: {
                                    click: function () {
                                        window.location.href = this.series.options.website;
                                    }
                                }
                            },
                            cursor: 'pointer'
                        }
                    },
                
                    series: [
                        {
                            name: 'Napas',
                            data: ".json_encode($napas, JSON_NUMERIC_CHECK).",
                            marker: {
                                symbol: 'triangle'
                            }
                        }, {
                            name: 'Nadi',
                            data: ".json_encode($nadi, JSON_NUMERIC_CHECK).",
                            color: Highcharts.getOptions().colors[6],
                            marker: {
                                symbol: 'circle'
                            }

                        }, {
                            name: 'Sistolik',                            
                            data: ".json_encode($sistolik, JSON_NUMERIC_CHECK).",
                            website: 'http://www.apple.com/accessibility/osx/voiceover',
                            color: Highcharts.getOptions().colors[2],
                            marker: {
                                symbol: 'url(".base_url()."/assets/admin/assets/images/sistolik.png)'
                            }

                        }, {
                            name: 'Diastolik',                            
                            data: ".json_encode($diastolik, JSON_NUMERIC_CHECK).",
                            website: 'http://www.apple.com/accessibility/osx/voiceover',
                            color: Highcharts.getOptions().colors[2],
                            marker: {
                                symbol: 'url(".base_url()."/assets/admin/assets/images/diastolik.png)'
                            }
                        }
                    ],
                
                    responsive: {
                        rules: [{
                            condition: {
                                maxWidth: 500
                            },
                            chartOptions: {
                                legend: {
                                    itemWidth: 150
                                }
                            }
                        }]
                    }
                });
            </script>";
    }

}

?>