<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SkriningCovid19 extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/covid19/SkriningCovid19Model'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_sc = $this->uri->segment(3);
      
      $data = array(
        'nokun' => $nokun,
        'id_sc' => isset($id_sc) ? $id_sc : "",
        'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
        'getPengkajian' => $this->SkriningCovid19Model->getPengkajian($id_sc),
        'listPengisi' => $this->masterModel->referensi(1570),
        'listDiagnosaCovid' => $this->masterModel->referensi(1571),
        'listIsolasiMandiri' => $this->masterModel->referensi(1572),
        'listMenungguHasil' => $this->masterModel->referensi(1573),
        'listRiwayatDemam' => $this->masterModel->referensi(1574),
        'listGejala' => $this->masterModel->referensi(1575),
        'listRiwayatBepergian' => $this->masterModel->referensi(1576),
        'listKontakBepergian' => $this->masterModel->referensi(1577),
        'listKontakPasienSuspek' => $this->masterModel->referensi(1578),
        'listSesakNapas' => $this->masterModel->referensi(1581),
        'listKontakKematian' => $this->masterModel->referensi(1579),
        'listPegawai' => $this->masterModel->listAllPegawai()
      );
      $this->load->view('rekam_medis/rawat_inap/covid19/skriningCovid19', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
          $ttd_petugas = $this->input->post('ttd_petugas');
          $ttd_3p = $this->input->post('ttd_3p');
          $data = array(
            'nokun' => $post['nokun'],
            'nama' => isset($post['nama_pengisi']) ? $post['nama_pengisi']: "",
            'pengisi_formulir' => isset($post['kategori_pengisi']) ? $post['kategori_pengisi']: "",
            'suhu' => isset($post['suhu']) ? $post['suhu']: "",
            'pilih1' => isset($post['diagnosa_covid']) ? $post['diagnosa_covid']: "",
            'pilih2' => isset($post['isolasi_mandiri']) ? $post['isolasi_mandiri']: "",
            'pilih3' => isset($post['menunggu_hasil']) ? $post['menunggu_hasil']: "",
            'pilih4' => isset($post['riwayat_demam']) ? $post['riwayat_demam']: "",
            'pilih5' => isset($post['gejala']) ? $post['gejala']: "",
            'pilih6' => isset($post['sesak_napas']) ? $post['sesak_napas']: "",
            'pilih7' => isset($post['riwayat_bepergian']) ? $post['riwayat_bepergian']: "",
            'pilih8' => isset($post['kontak_bepergian']) ? $post['kontak_bepergian']: "",
            'pilih9' => isset($post['kontak_suspek']) ? $post['kontak_suspek']: "",
            'pilih10' => isset($post['kontak_kematian']) ? $post['kontak_kematian']: "",
            'ttd_petugas' => isset($ttd_petugas) ? file_get_contents($ttd_petugas) : "",
            'ttd_3p' => isset($ttd_3p) ? file_get_contents($ttd_3p) : "",
            'nama_petugas' => isset($post['nama_petugas']) ? $post['nama_petugas']: "",
            'nama_3p' => isset($post['nama_3p']) ? $post['nama_3p']: "",
            'tgl_1' => isset($post['tanggal']) ? date('Y-m-d', strtotime($post['tanggal'])): "",
            'waktu' => isset($post['waktu']) ? $post['waktu']: "",
            'oleh' => $this->session->userdata('id')
          );

          $dataUbah = array(
            'nokun' => $post['nokun'],
            'nama' => isset($post['nama_pengisi']) ? $post['nama_pengisi']: "",
            'pengisi_formulir' => isset($post['kategori_pengisi']) ? $post['kategori_pengisi']: "",
            'suhu' => isset($post['suhu']) ? $post['suhu']: "",
            'pilih1' => isset($post['diagnosa_covid']) ? $post['diagnosa_covid']: "",
            'pilih2' => isset($post['isolasi_mandiri']) ? $post['isolasi_mandiri']: "",
            'pilih3' => isset($post['menunggu_hasil']) ? $post['menunggu_hasil']: "",
            'pilih4' => isset($post['riwayat_demam']) ? $post['riwayat_demam']: "",
            'pilih5' => isset($post['gejala']) ? $post['gejala']: "",
            'pilih6' => isset($post['sesak_napas']) ? $post['sesak_napas']: "",
            'pilih7' => isset($post['riwayat_bepergian']) ? $post['riwayat_bepergian']: "",
            'pilih8' => isset($post['kontak_bepergian']) ? $post['kontak_bepergian']: "",
            'pilih9' => isset($post['kontak_suspek']) ? $post['kontak_suspek']: "",
            'pilih10' => isset($post['kontak_kematian']) ? $post['kontak_kematian']: "",
            'nama_petugas' => isset($post['nama_petugas']) ? $post['nama_petugas']: "",
            'nama_3p' => isset($post['nama_3p']) ? $post['nama_3p']: "",
            'tgl_1' => isset($post['tanggal']) ? date('Y-m-d', strtotime($post['tanggal'])): "",
            'waktu' => isset($post['waktu']) ? $post['waktu']: "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_sc'])) {
            $this->db->where('db_layanan.tb_formulir_skrining.id', $post['id_sc']);
            $this->db->update('db_layanan.tb_formulir_skrining', $dataUbah);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('db_layanan.tb_formulir_skrining', $data);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->SkriningCovid19Model->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->SkriningCovid19Model->datatables();

        $data = array();
        foreach ($result as $row){
            if($row -> status_suhu == 0 || $row -> status_suspek == 0){
              $label = '<div id="changeColorSkriningCovid" style="border-radius:50px;width:50px;height:50px;background-color:red;"></div>';
            }else{
              $label = '<div id="changeColorSkriningCovid" style="border-radius:50px;width:50px;height:50px;background-color:green;"></div>';
            }
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editSkriningCovid19" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $label;
            $sub_array[] = $row -> suhu;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->SkriningCovid19Model->total_count(),
            "recordsFiltered"   => $this->SkriningCovid19Model->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}