<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class CekPIRIModel extends MY_Model {

  public function simpanCekPIRI($data)
  {
    $this->db->insert('keperawatan.tb_ceklis_pelayanan_instalasi_ri', $data);
  }

  public function listHistoryCekPIRI($nomr)
  {
    $query = $this->db->query("SELECT
                                  pp.NORM,
                                  cpir.id,
                                  cpir.nokun,
                                  cpir.petugas_admisi_igd PETUGAS_IGD,
                                  cpir.petugas_ruangan_ri PETUGAS_RUANGAN,
                                  cpir.created_at tanggal,
                                  cpir.status_ri_igd,
                                  ap.NAMA  
                                FROM
                                  keperawatan.tb_ceklis_pelayanan_instalasi_ri cpir
                                  LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = cpir.nokun
                                  LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                                  LEFT JOIN aplikasi.pengguna ap ON ap.ID = cpir.oleh
                                WHERE
                                  pp.NORM = '$nomr' 
                                  AND cpir.`status_ri_igd` = 1
                              ");
    return $query;
  }

  public function getCekPIRI($id)
  {
    $query = $this->db->query("SELECT
                                cpir.*,
                                cpir.created_at tanggal,
                                ap.NAMA
                              FROM
                                keperawatan.tb_ceklis_pelayanan_instalasi_ri cpir
                                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = cpir.nokun
                                LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
                                LEFT JOIN aplikasi.pengguna ap ON ap.ID = cpir.oleh
                              WHERE
                                cpir.id = $id 
                                AND cpir.status_ri_igd = 1
                              ");
    return $query->row_array();
  }

  // public function updateInformedConcent($data,$id)
  // {
  //   $this->db->where('id', $id);
  //   $this->db->update('db_informed_consent.tb_informed_consent', $data);
  // }

  public function updateCekPIRI($data,$idCekPIRI)
  {
    $this->db->where('id', $idCekPIRI);
    $this->db->update('keperawatan.tb_ceklis_pelayanan_instalasi_ri', $data);
  }

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
?>
