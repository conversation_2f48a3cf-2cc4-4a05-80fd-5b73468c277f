<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CPAOModel extends MY_Model
{
  protected $_table_name = 'medis.tb_cpao';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s <PERSON>ajib <PERSON>.',
        'numeric' => '%s <PERSON>ajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function instrumen()
  {
    $this->db->select('id, nama, kd_grafir');
    $this->db->from('master.instrumen');
    $this->db->order_by('nama');
    $this->db->where('status', 1);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function instrumenAlatOperasi($id)
  {
    $this->db->select('ia.id, a.nama nama_alat_operasi, ia.no_katalog, ia.jumlah, ia.keterangan');
    $this->db->from('master.instrumen_alat_operasi ia');
    $this->db->join('master.instrumen i', 'i.id = ia.id_instrumen', 'left');
    $this->db->join('master.alat_operasi a', 'a.id = ia.id_alat_operasi', 'left');
    $this->db->where('ia.status', 1);
    $this->db->where('ia.id_instrumen', $id);
    $query = $this->db->get();
    return $query->result();
  }

  public function simpan($data)
  {
    $this->db->insert('medis.tb_cpao', $data);
    return $this->db->insert_id();
  }

  public function simpanAlat($dataAlat)
  {
    $this->db->insert_batch('medis.tb_cpao_alat', $dataAlat);
  }

  public function simpanKassa($dataKassa)
  {
    $this->db->insert('medis.tb_cpao_kassa', $dataKassa);
  }

  public function simpanTambahan($dataTambahan)
  {
    $this->db->insert('medis.tb_cpao_tambahan', $dataTambahan);
  }

  public function ubah($data, $id)
  {
    $this->db->where('medis.tb_cpao.id', $id);
    $this->db->update('medis.tb_cpao', $data);
  }

  public function ubahAlat($dataAlat, $idCPAOAlat)
  {
    $this->db->where('medis.tb_cpao_alat.id_cpao', $idCPAOAlat);
    $this->db->update('medis.tb_cpao_alat', $dataAlat);
  }

  public function ubahTambahan($dataTambahan, $idCPAOTambahan)
  {
    $this->db->where('medis.tb_cpao_tambahan.id_cpao', $idCPAOTambahan);
    $this->db->update('medis.tb_cpao_tambahan', $dataTambahan);
  }

  public function ubahKassa($dataKassa, $idCPAOKassa)
  {
    $this->db->where('medis.tb_cpao_kassa.id_cpao', $idCPAOKassa);
    $this->db->update('medis.tb_cpao_kassa', $dataKassa);
  }

  public function history($nokun, $param)
  {
    if (isset($param)) {
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->select('c.id');
      } elseif ($param == 'tabel') {
        // Tabel history
        $this->db->select(
          'c.id, c.tanggal, c.jam, i.nama instrumen, master.getNamaLengkapPegawai(p.NIP) pengisi, c.created_at,
          c.status'
        );
      }
    }
    $this->db->from('medis.tb_cpao c');
    if (isset($param)) {
      $this->db->join('master.instrumen i', 'i.id = c.id_instrumen');
      $this->db->join('aplikasi.pengguna p', 'p.ID = c.oleh', 'left');
      $this->db->where('c.nokun', $nokun);
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->where('c.status', 1);
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        // Tabel history
        $this->db->order_by('c.tanggal', 'desc');
        return $this->db->get();
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  public function detail($id)
  {
    $this->db->select(
      'c.id, c.id_instrumen, c.tanggal, c.jam, c.tindakan_operasi, t.suction_pre_op, t.suction_tambahan,
      t.k_medium_pre_op, t.k_medium_tambahan, t.retraktor_pre_op, t.retraktor_tambahan, t.l_hak_pre_op,
      t.l_hak_tambahan, t.jarum_lepas_pre_op, t.jarum_lepas_tambahan, t.nm_tambahan_6, t.tambahan_6_pre_op,
      t.tambahan_6_tambahan, t.nm_tambahan_7, t.tambahan_7_pre_op, t.tambahan_7_tambahan, t.nm_tambahan_8,
      t.tambahan_8_pre_op, t.tambahan_8_tambahan, k.kassa_besar_masuk_1, k.kassa_besar_masuk_2, k.kassa_besar_masuk_3,
      k.kassa_besar_masuk_4, k.kassa_besar_masuk_5, k.kassa_besar_masuk_6, k.kassa_besar_masuk_7,
      k.kassa_besar_masuk_8, k.kassa_besar_masuk_9, k.kassa_besar_masuk_10, k.kassa_besar_keluar_1,
      k.kassa_besar_keluar_2, k.kassa_besar_keluar_3, k.kassa_besar_keluar_4, k.kassa_besar_keluar_5,
      k.kassa_besar_keluar_6, k.kassa_besar_keluar_7, k.kassa_besar_keluar_8, k.kassa_besar_keluar_9,
      k.kassa_besar_keluar_10, k.deskripsi_kassa_besar, k.kassa_kecil_masuk_1, k.kassa_kecil_masuk_2,
      k.kassa_kecil_masuk_3, k.kassa_kecil_masuk_4, k.kassa_kecil_masuk_5, k.kassa_kecil_masuk_6,
      k.kassa_kecil_masuk_7, k.kassa_kecil_masuk_8, k.kassa_kecil_masuk_9, k.kassa_kecil_masuk_10,
      k.kassa_kecil_keluar_1, k.kassa_kecil_keluar_2, k.kassa_kecil_keluar_3, k.kassa_kecil_keluar_4,
      k.kassa_kecil_keluar_5, k.kassa_kecil_keluar_6, k.kassa_kecil_keluar_7, k.kassa_kecil_keluar_8,
      k.kassa_kecil_keluar_9, k.kassa_kecil_keluar_10, k.deskripsi_kassa_kecil, k.depper_besar_masuk_1,
      k.depper_besar_masuk_2, k.depper_besar_masuk_3, k.depper_besar_masuk_4, k.depper_besar_masuk_5,
      k.depper_besar_masuk_6, k.depper_besar_masuk_7, k.depper_besar_masuk_8, k.depper_besar_masuk_9,
      k.depper_besar_masuk_10, k.deskripsi_depper_besar, k.depper_besar_keluar_1, k.depper_besar_keluar_2,
      k.depper_besar_keluar_3, k.depper_besar_keluar_4, k.depper_besar_keluar_5, k.depper_besar_keluar_6,
      k.depper_besar_keluar_7, k.depper_besar_keluar_8, k.depper_besar_keluar_9, k.depper_besar_keluar_10,
      k.deskripsi_depper_besar, k.depper_kecil_masuk_1, k.depper_kecil_masuk_2, k.depper_kecil_masuk_3,
      k.depper_kecil_masuk_4, k.depper_kecil_masuk_5, k.depper_kecil_masuk_6, k.depper_kecil_masuk_7,
      k.depper_kecil_masuk_8, k.depper_kecil_masuk_9, k.depper_kecil_masuk_10, k.deskripsi_depper_kecil,
      k.depper_kecil_keluar_1, k.depper_kecil_keluar_2, k.depper_kecil_keluar_3, k.depper_kecil_keluar_4,
      k.depper_kecil_keluar_5, k.depper_kecil_keluar_6, k.depper_kecil_keluar_7, k.depper_kecil_keluar_8,
      k.depper_kecil_keluar_9, k.depper_kecil_keluar_10, k.deskripsi_depper_kecil, k.nm_kassa_5, k.kassa_5_masuk_1,
      k.kassa_5_masuk_2, k.kassa_5_masuk_3, k.kassa_5_masuk_4, k.kassa_5_masuk_5, k.kassa_5_masuk_6, k.kassa_5_masuk_7,
      k.kassa_5_masuk_8, k.kassa_5_masuk_9, k.kassa_5_masuk_10, k.kassa_5_keluar_1, k.kassa_5_keluar_2,
      k.kassa_5_keluar_3, k.kassa_5_keluar_4, k.kassa_5_keluar_5, k.kassa_5_keluar_6, k.kassa_5_keluar_7,
      k.kassa_5_keluar_8, k.kassa_5_keluar_9, k.kassa_5_keluar_10, k.deskripsi_kassa_5, k.nm_kassa_6,
      k.kassa_6_masuk_1, k.kassa_6_masuk_2, k.kassa_6_masuk_3, k.kassa_6_masuk_4, k.kassa_6_masuk_5, k.kassa_6_masuk_6,
      k.kassa_6_masuk_7, k.kassa_6_masuk_8, k.kassa_6_masuk_9, k.kassa_6_masuk_10, k.kassa_6_keluar_1,
      k.kassa_6_keluar_2, k.kassa_6_keluar_3, k.kassa_6_keluar_4, k.kassa_6_keluar_5, k.kassa_6_keluar_6,
      k.kassa_6_keluar_7, k.kassa_6_keluar_8, k.kassa_6_keluar_9, k.kassa_6_keluar_10, k.deskripsi_kassa_6, c.alat,
      c.ket_alat, c.kassa, c.ket_kassa, c.xray, c.ket_xray, c.dok_operator, c.instrumentator, c.instrumentator_2,
      c.onloop'
    );
    $this->db->from('medis.tb_cpao c');
    // $this->db->join('medis.tb_cpao_alat a', 'a.id_cpao = c.id', 'left');
    $this->db->join('medis.tb_cpao_tambahan t', 't.id_cpao = c.id', 'left');
    $this->db->join('medis.tb_cpao_kassa k', 'k.id_cpao = c.id', 'left');
    $this->db->where('c.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function detailAlat($id)
  {
    $this->db->select(
      'ca.id, ca.id_alat, a.nama nama_alat_operasi, ca.nama, ia.no_katalog, ia.jumlah, ca.pendek_pre_op,
      ca.pendek_tambahan, ca.sedang_pre_op, ca.sedang_tambahan, ca.panjang_pre_op,ca.panjang_tambahan'
    );
    $this->db->from('medis.tb_cpao_alat ca');
    $this->db->join('master.instrumen_alat_operasi ia', 'ia.id = ca.id_alat', 'left');
    $this->db->join('master.alat_operasi a', 'a.id = ia.id_alat_operasi', 'left');
    $this->db->where('ca.id_cpao', $id);
    $this->db->order_by('id');
    $query = $this->db->get();
    return $query->result_array();
  }
}

// End of file CPAOModel.php
// Location: ./application/models/rekam_medis/rawat_inap/operasi/CPAOModel.php