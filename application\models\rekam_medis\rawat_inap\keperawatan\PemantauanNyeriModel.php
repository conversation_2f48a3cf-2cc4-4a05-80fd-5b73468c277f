<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemantauanNyeriModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_skrining_nyeri';
  protected $_primary_key = 'id';
  protected $_order_by = 'created_at';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib Angka.'
      ),
    ),

    'skrining_nyeri' => array(
      'field' => 'skrining_nyeri',
      'label' => 'Skrining Nyeri',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Angka.'
      ),
    ),
  );

  public $rules_nyeri = array(
    'skor_nyeri' => array(
      'field' => 'skor_nyeri',
      'label' => 'Skala Nyeri',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Angka.'
      ),
    ),

    'farmakologi' => array(
      'field' => 'farmakologi',
      'label' => 'Farmakologi',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.'
      ),
    ),

    'non_farmakologi' => array(
      'field' => 'non_farmakologi',
      'label' => 'Non Farmakologi',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.'
      ),
    ),

    'efek_samping' => array(
      'field' => 'efek_samping',
      'label' => 'Efek Samping',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Angka.'
      ),
    ),
  );

  public $rules_efek_samping = array(
    'efek_samping_lain' => array(
      'field' => 'efek_samping_lain',
      'label' => 'Efek Samping',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.'
      ),
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_skrining_nyeri', $data);
    return $this->db->insert_id();
  }

  public function ubah($ref, $dataSource, $data)
  {
    $this->db->where('keperawatan.tb_skrining_nyeri.ref', $ref);
    $this->db->where('keperawatan.tb_skrining_nyeri.data_source', $dataSource);
    $this->db->update('keperawatan.tb_skrining_nyeri', $data);
  }

  function table_query()
  {
    $this->db->select(
      'kp.id ID,kp.nokun NOKUN, kp.created_at TANGGAL, master.getNamaLengkapPegawai(peng.NIP) USER,
      master.getNamaLengkapPegawai(dpjp.NIP) DPJP, rk.DESKRIPSI RUANGAN_KUNJUNGAN, p.NORM,
      master.getNamaLengkap(p.NORM) NAMA_PASIEN, vm.variabel METODE, vs.variabel SKOR, ds.deskripsi DATA,
      kp.metode id_metode,kp.skor id_skor,kp.farmakologi FARMAKOLOGI,kp.non_farmakologi NON_FARMAKOLOGI'
    );
    $this->db->from('keperawatan.tb_skrining_nyeri kp');
    $this->db->join('db_master.variabel vm', 'vm.id_variabel = kp.metode AND vm.id_referensi = 7', 'LEFT');
    $this->db->join('db_master.tb_data_source ds', ' kp.data_source = ds.id', 'LEFT');
    $this->db->join('db_master.variabel vs', 'kp.skor = vs.id_variabel', 'LEFT');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = kp.nokun', 'LEFT');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'LEFT');
    $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'LEFT');
    $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'LEFT');
    $this->db->join('master.diagnosa_masuk dm', 'dm.ID = p.DIAGNOSA_MASUK', 'LEFT');
    $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'LEFT');
    $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'LEFT');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = kp.created_by', 'LEFT');

    $this->db->where('kp.STATUS !=', '0');
    $this->db->order_by('kp.created_at', 'DESC');

    if ($this->input->post('nomr')) {
      $this->db->where('p.NORM', $this->input->post('nomr'));
    }

    if ($this->input->post('id')) {
      $this->db->where('kp.id', $this->input->post('id'));
    }

    if ($this->input->post('nokun')) {
      $this->db->where('kp.nokun', $this->input->post('nokun'));
    }

    if ($this->input->post('nopen')) {
      $this->db->where('pk.nopen', $this->input->post('nopen'));
    }
  }

  function get_table($single = TRUE)
  {
    $this->table_query();
    $query = $this->db->get();
    if ($single == TRUE) {
      $method = 'row';
    } else {
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count()
  {
    $this->table_query();
    return $this->db->count_all_results();
  }
}

/* End of file PemantauanNyeriModel */
/* Location: ./application/models/transferRuangan/PemantauanNyeriModel.php */