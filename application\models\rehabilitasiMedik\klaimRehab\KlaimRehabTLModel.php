<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KlaimRehabTLModel extends MY_Model
{
    protected $_table = 'medis.tb_klaim_rehab_tata_laksana';
    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'tata_laksana[]',
                'label' => 'Tata laksana KFR (ICD-9)',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ],
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert_batch($this->_table, $data);
    }

    function data_tersimpan($id)
    {
        $this->db->select('icd9.CODE, icd9.STR');
        $this->db->from('medis.tb_klaim_rehab_tata_laksana krtl');
        $this->db->join('master.mrconso icd9', "icd9.CODE = krtl.tata_laksana AND icd9.SAB = 'ICD9CM_2005'", 'left');
        $this->db->group_by('icd9.CODE');
        $this->db->where('krtl.id_klaim_rehab', $id);
        $query = $this->db->get();
        return $query->result();
    }
}

/* End of file KlaimRehabTLModel.php */
/* Location: ./application/models/rehabilitasiMedik/klaimRehab/KlaimRehabTLModel.php */