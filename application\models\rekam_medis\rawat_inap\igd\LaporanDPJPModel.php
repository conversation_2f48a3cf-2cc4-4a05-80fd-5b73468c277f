<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class LaporanDPJPModel extends MY_Model {
    public function tableMasalah($nokun)
    {
        $query = $this->db->query("SELECT ld.id, ld.nokun, ld.tanggal, ld.jam, ld.masalah, ld.instruksi_dpjp, ld.tanggal_respon, ld.jam_respon, master.getNamaLengkapPegawai(d.NIP) dokter, master.getNamaLengkapPegawai(oleh_m.NIP) input_masalah, master.getNamaLengkapPegawai(oleh_r.NIP) input_respon 
                            FROM keperawatan.tb_laporan_dpjp ld
                            LEFT JOIN master.dokter d ON ld.dokter = d.ID
                            LEFT JOIN aplikasi.pengguna oleh_m ON ld.oleh_masalah = oleh_m.ID
                            LEFT JOIN aplikasi.pengguna oleh_r ON ld.oleh_respon = oleh_r.ID 
                            WHERE ld.nokun = '$nokun' AND ld.status IN (1,2) ORDER BY ld.created_at DESC
                        ");
                        
        return $query->result_array();
    }

    public function getDetailLaporanDPJP($id_masalah)
    {
        $query = $this->db->query("SELECT * FROM keperawatan.tb_laporan_dpjp d 
        WHERE d.id = '$id_masalah'");

        return $query->row_array();
    }

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
