<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ReaksiTransfusiModel extends MY_Model{
	protected $_table_name = 'keperawatan.tb_reaksi_transfusi';
	protected $_primary_key = 'kunjungan';
	protected $_order_by = 'kunjungan';
	protected $_order_by_type = 'DESC';

    public $rules = array(
		'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>isi.',
                        'numeric' => '%s Wajib <PERSON>ka.'
                ),
        ),
        
        'nomor_kantong' => array(
            'field' => 'nomor_kantong',
            'label' => 'Nomor Kantong',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
        
        'reaksi_tindakan' => array(
            'field' => 'reaksi_tindakan',
            'label' => '<PERSON><PERSON><PERSON>',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.'
                ),
        ),

        'tindakan' => array(
            'field' => 'tindakan',
            'label' => 'Tindakan Yang Dilakukan',
            'rules' => 'trim|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.'
                ),
        ),
		
    );

	function __construct(){
		parent::__construct();
    }
    
    function table_query()
    {
        $this->db->select('rt.id, rt.kunjungan, rt.nomor_kantong, rt.reaksi_tindakan, rt.tindakan
        ,rt.tanggal, `master`.getNamaLengkapPegawai(ap.NIP) oleh');
        $this->db->from('keperawatan.tb_reaksi_transfusi rt');
        $this->db->join('aplikasi.pengguna ap','rt.oleh = ap.ID','LEFT');

        $this->db->where('rt.status !=','0');
        $this->db->where('rt.kunjungan',$this->input->post('nokun'));
        $this->db->order_by('rt.tanggal', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

}
