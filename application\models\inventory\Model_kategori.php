<?php
class Model_kategori extends CI_Model{   


    function tampilkan_data(){

        return $this->db->get('inventory.kategori');
    }

    public function tampil_data(){
        $sql = $this->db->query("SELECT * FROM inventory.kategori");
        return $sql->result_array();
    }

    function tampilkan_data_paging($halaman,$batas)
    {
        return $this->db->query("select * from inventory.kategori_barang");
    }

    function post(){
        $data=array(
            'nama_kategori'=>  $this->input->post('kategori')
        );
        $this->db->insert('inventory.kategori_barang',$data);
    }


    function edit($data,$id)
    {
        $ID  =   $this->input->post('ID');
        $NAMA     =   $this->input->post('NAMA');
//echo "<pre>";print_r($_POST);exit();
        $data       =   array('NAMA'=>$NAMA);

        $this->db->where('ID',$this->input->post('ID'));
        $this->db->update('inventory.kategori',$data);
    }


    function get_one($id)
    {
        $param  =   array('ID'=>$id);
        return $this->db->get_where('inventory.kategori',$param);
    }


    function delete($id)
    {
        $this->db->where('kategori_id',$id);
        $this->db->delete('inventory.kategori_barang');
    }
}