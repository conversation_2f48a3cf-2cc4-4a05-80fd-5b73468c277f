<?php
class Model_satuan extends CI_Model{
	   public function __construct()
    {
        parent::__construct();

	}
    function tampilkan_data(){
        return $this->db->get('invenumum.satuan_');
    }

    public function tampil_data(){
        $sql = $this->db->query("SELECT * FROM invenumum.satuan_");
        return $sql->result_array();
    }

    function tampilkan_data_paging($halaman,$batas)
    {
        return $this->db->query("select * from inventory.satuan");
    }

    function tambah($data){
        $this->db->insert('invenumum.satuan_', $data);
        return TRUE;
    }

        function ubah($data, $id){
        $this->db->where('ID_SATUAN',$id);
        $this->db->update('invenumum.satuan_', $data);
        return TRUE;
    }
}