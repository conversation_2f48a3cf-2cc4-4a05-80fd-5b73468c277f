<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>njungan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('KunjunganModel'));
    }

    public function index() {

        // $ruangan = $this->masterModel->ruanganRskd();
        $data = array(
        'title' => 'Halaman WhatsApp Pasien',
        'isi'   => 'Whatsapp/pasien'
        );
        $this->load->view('layout/wrapper',$data);
    }

    public function datatables(){
        $result = $this->KunjunganModel->datatables();

        $data = array();
        $i = 1;
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $i;      
            $sub_array[] = $row -> NOPEN;      
            $sub_array[] = $row -> TGLREG;
            $sub_array[] = $row -> UNITPELAYANAN;
            $sub_array[] = $row -> STATUS_KUNJUNGAN;
            $sub_array[] = $row -> JENIS_KUNJUNGAN == 3 ? '<a href="'.base_url('PengkajianRawatInap/').$row -> NOKUN.'" class="btn btn-primary btn-block btn-sm"><i class="fa fa-eye"></i> Lihat</a>' : '<span class="badge badge-info">Coming Soon</span>';

            // $sub_array[] = $row -> OLEH;

            $i++;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->KunjunganModel->total_count(),
            "recordsFiltered"   => $this->KunjunganModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function history(){
        $resultPendaftaran = $this->KunjunganModel->pendaftaran();
        
        $data = array();
        foreach ($resultPendaftaran->result() as $dataPendaftaran) {
            $_POST['nomor'] = $dataPendaftaran -> NOMOR;
            $resultKunjungan = $this->KunjunganModel->kunjungan();
            $sub_pendaftaran = array();
            $sub_pendaftaran ['NOMOR'] = $dataPendaftaran -> NOMOR;
            $sub_pendaftaran ['TANGGAL'] = $dataPendaftaran -> TANGGAL;
            $sub_pendaftaran ['DESKRIPSI'] = $dataPendaftaran -> DESKRIPSI;
            $sub_pendaftaran ['RUANGAN_TUJUAN'] = $dataPendaftaran -> RUANGAN_TUJUAN;
            $sub_pendaftaran ['STATUS'] = $dataPendaftaran -> STATUS;

            if($resultKunjungan->num_rows() > 0){
                $data_kunjungan = array();
                foreach ($resultKunjungan->result() as $dataKunjungan) {
                    $data_kunjungan[] = $dataKunjungan;
                }
                $sub_pendaftaran ['KUNJUNGAN'] = $data_kunjungan;
            }

            $data[] = $sub_pendaftaran;

        }

        echo json_encode($data);
    }
}