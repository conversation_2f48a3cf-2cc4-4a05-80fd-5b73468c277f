<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class KonsulRehabMedikModel extends CI_Model
{
  public function insertRehabMedik($data)
  {
    $this->db->insert('medis.tb_rehab_medik', $data);
    return $this->db->insert_id();
  }

  public function tampilHistoryRehabMedik()
  {
    $this->db->select(
      "rm.id ID_REHAB_MEDIK, rm.tanggal TANGGAL, r.DESKRIPSI RUANGAN_AWAL, reh.ID ID_DOKTER_REHAB,
      master.getNamaLengkapPegawai(reh.NIP) DOKTER_REHAB,
      rm.`status` STATUS, IF(rm.`status`= 1, 'Selesai', 'Dibatalkan') STATUS_DESKRIPSI"
    );
    $this->db->from('medis.tb_rehab_medik rm');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = rm.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'left');
    $this->db->join('master.ruangan r', 'r.ID = rm.ruangan_awal', 'left');
    $this->db->join('master.dokter reh', 'reh.ID = rm.dokter_rehab_medik', 'left');
    $this->db->where('p.NORM', $this->uri->segment(4));
    $this->db->order_by('ID_REHAB_MEDIK');

    $query = $this->db->get();
    return $query->result_array();
  }

  public function detailHistoryRehabMedik($id)
  {
    $this->db->select(
      "rm.id ID_REHAB_MEDIK, r.DESKRIPSI ruangan, rm.dokter_pengirim, rm.dokter_rehab_medik, rm.goal_rehab, rm.transportasi,
      rm.transportasi_lainnya, rm.perhatian_khusus, rm.pelayanan_rehab_medik, rm.pelayanan_rehab_medik_lainnya,
      rm.ikhtisar_klinik, rm.program_rehab"
    );
    $this->db->from('medis.tb_rehab_medik rm');
    $this->db->join('master.ruangan r', 'r.ID = rm.ruangan_awal', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = rm.kunjungan', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'left');
    $this->db->where('rm.id', $id);

    $query = $this->db->get();
    return $query->result_array();
  }

  public function ubahRehabMedik($where, $data, $table)
  {
    $this->db->where($where);
    $this->db->update($table, $data);
  }
}