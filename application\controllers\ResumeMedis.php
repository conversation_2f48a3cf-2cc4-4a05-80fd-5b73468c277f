<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ResumeMedis extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('ResumeMedisModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $result =  $this->ResumeMedisModel->get_pasien();
        $result2 = $this->ResumeMedisModel->check_eresume_igd();

        $nomr  = $this->uri->segment(3);
        $nokun = $this->uri->segment(4);

        $data = array();
        foreach ($result['his'] as $row) {
            $cek_pp =  $this->ResumeMedisModel->cek_pp($row->NOMOR);
            $cek_pm = $this->ResumeMedisModel->cek_pm($row->NOMOR);
            $sub_array = array();
            $sub_array['NOMOR'] = $row->NOMOR;
            $sub_array['NORM'] = $row->NORM;
            $sub_array['RUANGAN'] = $row->ruang_rawat_terakhir;
            $sub_array['DESKRIPSI'] = $row->DESKRIPSI;
            $sub_array['DIAGNOSIS'] = $row->diagnosa_utama;
            $sub_array['TATA_LAKSANA'] = $row->tata_laksana;
            $sub_array['CATATAN_PENTING'] = $row->CATATAN_PENTING_LAINNYA;
            $sub_array['ID_RESUME'] = $row->ID_RESUME;
            $sub_array['JENIS']  = $row->ID;
            $sub_array['STATUS_IGD'] = $row->STATUS_IGD;
            $sub_array['TANGGAL'] = $row->tanggal_masuk_keluar;
            $sub_array['NAMADOKTER'] = $row->NAMADOKTER;
            $sub_array['STATUS'] = $row->STATUS;
            $sub_array['STAT'] = empty($cek_pp['row']) ? '0' : $cek_pp['row']->status;
            $sub_array['TGL'] = isset($cek_pp['res']['tgl_input']) ? date_format(date_create($cek_pp['res']['tgl_input']), "d-m-Y H:i:s") : null;
            $sub_array['PP'] = $cek_pp['num'];
            $sub_array['PM'] = $cek_pm;
            $data[] = $sub_array;
        }

        $data = array(
            'result2' => $result2,
            'dpasien' => $data,
            'num' => $result['num'],
            'nama' => $result['nama'],
            'isi' => 'resume_medis/getpasien',
            'nomr' => $nomr,
            'nokun' => $nokun,
            'getNomr' => $this->pengkajianAwalModel->getNomr($nokun)
        );

        $this->load->view('Pengkajian/resumeMedis/index', $data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $data = array(
                    'CATATAN_PENTING_LAINNYA' => $this->input->post('catatanPenting'),
                );
                if ($this->input->post('jenis') == 1) {
                    $this->db->where('ID_ERESUME_IGD', $this->input->post('id'));
                    $this->db->update('eresume_igd.eresume_igd', $data);
                    $result = array('status' => 'success');
                }

                if ($this->input->post('jenis') == 2) {
                    $this->db->where('id', $this->input->post('id'));
                    $this->db->update('resume_medis.resume_medis', $data);
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            }
        }
    }
}

// End of file ResumeMedis.php
// Location: ./application/controllers/ResumeMedis.php