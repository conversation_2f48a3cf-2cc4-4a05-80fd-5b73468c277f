<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormPemantauanAnestesiLokal extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $id_pal = $this->uri->segment(7);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $kesadaranAnestesiLokal = $this->masterModel->referensi(394);
    $riwayatAlergi = $this->masterModel->referensi(2);
    $obatAnestesiLokal = $this->masterModel->referensi(396);
    $diencerkan = $this->masterModel->referensi(397);
    $penggunaanAdrenalin = $this->masterModel->referensi(398);
    $konversiAnestesi = $this->masterModel->referensi(399);
    $kesadaranUmumPascaPasien = $this->masterModel->referensi(467);
    $tandaToksisitas = $this->masterModel->referensi(462);
    $mualMuntah = $this->masterModel->referensi(463);
    $keluhanNyeri = $this->masterModel->referensi(464);
    $perdarahanPascaPasien = $this->masterModel->referensi(465);
    $pasienPindah = $this->masterModel->referensi(466);

    $historyPAL = $this->pengkajianAwalModel->historyPAL($nomr);
    $getPALPemantauan = "";
    if ($id_pal != "") {
      $getPAL = $this->pengkajianAwalModel->getPAL($id_pal);
      $getPALPemantauan = $this->pengkajianAwalModel->getPALPemantauan($id_pal);
    }

    $data = array(
      'getNomr' => $getNomr,
      'kesadaranAnestesiLokal' => $kesadaranAnestesiLokal,
      'riwayatAlergi' => $riwayatAlergi,
      'obatAnestesiLokal' => $obatAnestesiLokal,
      'diencerkan' => $diencerkan,
      'penggunaanAdrenalin' => $penggunaanAdrenalin,
      'getPAL' => $id_pal != "" ? $getPAL : "",
      'getPALPemantauan' => $id_pal != "" ? $getPALPemantauan : "",
      'konversiAnestesi' => $konversiAnestesi,
      'historyPAL' => $historyPAL,
      'kesadaranUmumPascaPasien' => $kesadaranUmumPascaPasien,
      'tandaToksisitas' => $tandaToksisitas,
      'mualMuntah' => $mualMuntah,
      'keluhanNyeri' => $keluhanNyeri,
      'perdarahanPascaPasien' => $perdarahanPascaPasien,
      'pasienPindah' => $pasienPindah,
    );

    $this->load->view('Pengkajian/pemantauanAnestesiLokal/index', $data);
  }

  public function action_pal($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $getid_pal = !empty($post['id_pal']) ? $post['id_pal'] : $this->pengkajianAwalModel->getIdEmr();
        

        $dataPAL = array(
          'id_anestesi_lokal' => $getid_pal,
          'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
          //
          // 'pukul' => isset($post['pukulKesInv']) ? $post['pukulKesInv'] : null,
          'kesadaran' => isset($post['kesadaranPraPAL']) ? json_encode($post['kesadaranPraPAL']) : "",
          'kesadaran_lain' => isset($post['kesadaranPraLainnya']) ? $post['kesadaranPraLainnya'] : null,
          'bb' => isset($post['bb_PAL']) ? $post['bb_PAL'] : null,
          'tb' => isset($post['tb_PAL']) ? $post['tb_PAL'] : null,
          'td_1' => isset($post['td_PAL_sistolik']) ? $post['td_PAL_sistolik'] : null,
          'td_2' => isset($post['tb_PAL_diastolik']) ? $post['tb_PAL_diastolik'] : null,
          'rr' => isset($post['rr_PAL']) ? $post['rr_PAL'] : null,
          'n' => isset($post['n_PAL']) ? $post['n_PAL'] : null,
          's' => isset($post['s_PAL']) ? $post['s_PAL'] : null,
          //
          'hb' => isset($post['hb_PAL']) ? $post['hb_PAL'] : null,
          't' => isset($post['t_PAL']) ? $post['t_PAL'] : null,
          'ht' => isset($post['ht_PAL']) ? $post['ht_PAL'] : null,
          'lab_lainnya' => isset($post['lainnyaLab_PAL']) ? $post['lainnyaLab_PAL'] : null,
          //
          'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : null,
          'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
          'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : null,
          //
          'obat_anestesi' => isset($post['obatAnestesiLokalPAL']) ? $post['obatAnestesiLokalPAL'] : null,
          'obat_anestesi_lain' => isset($post['obatAnestesiLokalLainnya']) ? $post['obatAnestesiLokalLainnya'] : null,
          'dosis' => isset($post['dosisJumlahObatGunaPAL']) ? $post['dosisJumlahObatGunaPAL'] : null,
          'diencerkan' => isset($post['encerkanPAL']) ? $post['encerkanPAL'] : null,
          'isi_diencerkan' => isset($post['obatDiencerkanLainnya']) ? $post['obatDiencerkanLainnya'] : null,
          'penggunaan_adrenalin' => isset($post['penggunaAdrenalinPAL']) ? $post['penggunaAdrenalinPAL'] : null,
          'isi_penggunaan_adrenalin' => isset($post['PenggunaanAdrenalinLainnya']) ? $post['PenggunaanAdrenalinLainnya'] : null,
          'lokasi_pemberian' => isset($post['lokasiPemberianPAL']) ? $post['lokasiPemberianPAL'] : null,
          'pukul_pemberian' => isset($post['jamPemberianPAL']) ? $post['jamPemberianPAL'] : null,
          //
          'kejadian_konversi' => isset($post['konversiAnestesiPAL']) ? $post['konversiAnestesiPAL'] : null,
          'isi_kejadian_konversi' => isset($post['konversiLainnya']) ? $post['konversiLainnya'] : null,
          'jumlah_perdarahan' => isset($post['jumlahPerdarahanPAL']) ? $post['jumlahPerdarahanPAL'] : null,
          'selesai_tindakan' => isset($post['selesaiTindakanPAL']) ? $post['selesaiTindakanPAL'] : null,
          //
          'kesadaran_pasca' => isset($post['kesadaranPascaPAL']) ? json_encode($post['kesadaranPascaPAL']) : "",
          'kesadaran_pasca_lain' => isset($post['kesadaranPascaLainnya']) ? $post['kesadaranPascaLainnya'] : null,
          'toksisitas' => isset($post['tandaToksisitasPAL']) ? $post['tandaToksisitasPAL'] : null,
          'isi_toksisitas' => isset($post['tandaToksisitasPAL_lainnya']) ? $post['tandaToksisitasPAL_lainnya'] : null,
          'mual_muntah' => isset($post['mualMuntahPAL']) ? $post['mualMuntahPAL'] : null,
          'isi_mual_muntah' => isset($post['mualMuntahPAL_lainnya']) ? $post['mualMuntahPAL_lainnya'] : null,
          'keluhan_nyeri' => isset($post['keluhanNyeriPAL']) ? $post['keluhanNyeriPAL'] : null,
          'isi_keluhan_nyeri' => isset($post['keluhanNyeri_lainnya']) ? $post['keluhanNyeri_lainnya'] : null,
          'perdarahan' => isset($post['perdarahanPascaPasienPAL']) ? $post['perdarahanPascaPasienPAL'] : null,
          //
          'observasi' => isset($post['rencanaObservasiPAL']) ? $post['rencanaObservasiPAL'] : null,
          'edukasi' => isset($post['rencanaEdukasiPAL']) ? $post['rencanaEdukasiPAL'] : null,
          'pasien_pindah' => isset($post['jamPindahPasienPAL']) ? $post['jamPindahPasienPAL'] : null,
          'ke' => isset($post['pasienPindahPAL']) ? $post['pasienPindahPAL'] : null,
          'isi_ke' => isset($post['pasienPindah_lainnya']) ? $post['pasienPindah_lainnya'] : null,
          //
          'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

if (!empty($post['id_pal'])) {
  $this->db->replace('keperawatan.tb_anestesi_lokal', $dataPAL);
  $result = array('status' => 'success', 'pesan' => 'ubah');
}else {
  $result = array('status' => 'failed');
  $this->db->insert('keperawatan.tb_anestesi_lokal', $dataPAL);
  // if (isset($post['hasilSttsFisioJam'])) {
  //   $this->db->insert_batch('keperawatan.tb_anestesi_lokal_pemantauan', $dataPemantauan);
  // }
  $result = array('status' => 'success');
}
echo json_encode($result);
}
}
}

public function viewEditPemantauanAnestesiLokal()
{
  $id = $this->input->post('id');
  $nokun = $this->input->post('nokun');
  $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
  $getPAL = $this->pengkajianAwalModel->getPAL($id);
  $getPALPemantauan = $this->pengkajianAwalModel->getPALPemantauan($id);
  $kesadaranAnestesiLokal = $this->masterModel->referensi(394);
  $riwayatAlergiAnestesiLokal = $this->masterModel->referensi(395);
  $obatAnestesiLokal = $this->masterModel->referensi(396);
  $diencerkan = $this->masterModel->referensi(397);
  $penggunaanAdrenalin = $this->masterModel->referensi(398);
  $konversiAnestesi = $this->masterModel->referensi(399);
  $kesadaranUmumPascaPasien = $this->masterModel->referensi(467);
  $tandaToksisitas = $this->masterModel->referensi(462);
  $mualMuntah = $this->masterModel->referensi(463);
  $keluhanNyeri = $this->masterModel->referensi(464);
  $perdarahanPascaPasien = $this->masterModel->referensi(465);
  $pasienPindah = $this->masterModel->referensi(466);
  $riwayatAlergi = $this->masterModel->referensi(2);

  $dataEdit = array(
    'getPAL' => $getPAL,
    'kesadaranAnestesiLokal' => $kesadaranAnestesiLokal,
    'riwayatAlergiAnestesiLokal' => $riwayatAlergiAnestesiLokal,
    'obatAnestesiLokal' => $obatAnestesiLokal,
    'diencerkan' => $diencerkan,
    'penggunaanAdrenalin' => $penggunaanAdrenalin,
    'konversiAnestesi' => $konversiAnestesi,
    'kesadaranUmumPascaPasien' => $kesadaranUmumPascaPasien,
    'tandaToksisitas' => $tandaToksisitas,
    'mualMuntah' => $mualMuntah,
    'keluhanNyeri' => $keluhanNyeri,
    'perdarahanPascaPasien' => $perdarahanPascaPasien,
    'pasienPindah' => $pasienPindah,
    'getPALPemantauan' => $getPALPemantauan,
    'riwayatAlergi' => $riwayatAlergi,
    'getNomr' => $getNomr,
  );

  $this->load->view('Pengkajian/pemantauanAnestesiLokal/modalViewEditPemantauanAnestesiLokal', $dataEdit);
}

public function viewInputTvPemantauanAnestesiLokal()
{
  $id = $this->input->post('id');
  $getPAL = $this->pengkajianAwalModel->getPAL($id);
  $nomr = $this->input->post('nomr');
  $historyTvPAL = $this->pengkajianAwalModel->historyTvPAL($id);

  $data = array(
    'getPAL' => $getPAL,
    'nomr' => $nomr,
    'historyTvPAL' => $historyTvPAL,
  );

  $this->load->view('Pengkajian/pemantauanAnestesiLokal/modalviewInputTvPemantauanAnestesiLokal', $data);
}

public function inputTvPemantauanAnestesiLokal()
{
  $post = $this->input->post();
  $id_pal = $this->input->post('id_pal_edit');
  $nomr = $this->input->post('nomr');
  $nokun = $this->input->post('nokun_edit');
  $oleh = $this->input->post('pengisi_edit');
  $jamSttsFisio = $this->input->post('jamSttsFisioPALInputTv');
  $ekgSttsFisioInputTv = $this->input->post('ekgSttsFisioInputTv');
  $tdSttsFisioSistolikInputTv = $this->input->post('tdSttsFisioSistolikInputTv');
  $tdSttsFisioDiastolikInputTv = $this->input->post('tdSttsFisioDiastolikInputTv');
  $nadiSttsFisioInputTv = $this->input->post('nadiSttsFisioInputTv');
  $rrSttsFisioInputTv = $this->input->post('rrSttsFisioInputTv');
  $spo2SttsFisioInputTv = $this->input->post('spo2SttsFisioInputTv');

  $data = array(
    'id_anestesi_lokal' => $id_pal,
    'pukul' => $jamSttsFisio,
    'ekg' => $ekgSttsFisioInputTv,
  );
// echo "<pre>";print_r($data);echo "</pre>";
$getPemantauanTv = $this->pengkajianAwalModel->simpanPemantauanAnestesiLokal($data);

$dataTandaVital = array(
    'data_source' => 34,
    'ref' => $getPemantauanTv,
    'nomr' => $nomr,
    'nokun' => $nokun,
    'td_sistolik' => $tdSttsFisioSistolikInputTv,
    'td_diastolik' => $tdSttsFisioDiastolikInputTv,
    'nadi' => $nadiSttsFisioInputTv,
    'pernapasan' => $rrSttsFisioInputTv,
    'oleh' => $oleh,
  );

// echo "<pre>";print_r($dataTandaVital);echo "</pre>";
$this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);

$dataSaturasi = array(
    'data_source' => 34,
    'ref' => $getPemantauanTv,
    'nomr' => $nomr,
    'nokun' => $nokun,
    'saturasi_o2' => $spo2SttsFisioInputTv,
    'oleh' => $oleh,
  );

  // echo "<pre>";print_r($dataSaturasi);echo "</pre>";exit();
$this->db->insert('db_pasien.tb_o2', $dataSaturasi);
}

public function hentiTvPemantauanAnestesiLokal()
{
  $id_pal = $this->input->post('id');

  $data = array(
    'status' => 0,
  );

  $this->db->where('id', $id_pal);
  $this->db->update('keperawatan.tb_anestesi_lokal_pemantauan_tv', $data);

  $dataTandaVital = array(
    'status' => 0,
  );

  $this->db->where('data_source', 34);
  $this->db->where('ref', $id_pal);
  $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);

  $datao2 = array(
    'status' => 0,
  );

  $this->db->where('data_source', 34);
  $this->db->where('ref', $id_pal);
  $this->db->update('db_pasien.tb_o2', $datao2);
}

public function EditPemantauanAnestesiLokal()
{
  $getid_pal = $this->input->post("id_pal_edit");
  $nokun = $this->input->post("nokun_edit");
  $kesadaranPraPAL_edit = $this->input->post("kesadaranPraPAL_edit");
  $kesadaranPraLainnya_edit = $this->input->post("kesadaranPraLainnya_edit");
  $bb_PAL_edit = $this->input->post("bb_PAL_edit");
  $tb_PAL_edit = $this->input->post("tb_PAL_edit");
  $td_PAL_sistolik_edit = $this->input->post("td_PAL_sistolik_edit");
  $tb_PAL_diastolik_edit = $this->input->post("tb_PAL_diastolik_edit");
  $rr_PAL_edit = $this->input->post("rr_PAL_edit");
  $n_PAL_edit = $this->input->post("n_PAL_edit");
  $s_PAL_edit = $this->input->post("s_PAL_edit");
  $hb_PAL_edit = $this->input->post("hb_PAL_edit");
  $t_PAL_edit = $this->input->post("t_PAL_edit");
  $ht_PAL_edit = $this->input->post("ht_PAL_edit");
  $lainnyaLab_PAL_edit = $this->input->post("lainnyaLab_PAL_edit");
  $riwayat_alergi_edit = $this->input->post("riwayat_alergi_edit");
  $riwayat_alergi_desk_edit = $this->input->post("riwayat_alergi_desk_edit");
  $reaksi_alergi_edit = $this->input->post("reaksi_alergi_edit");
  $obatAnestesiLokalPAL_edit = $this->input->post("obatAnestesiLokalPAL_edit");
  $obatAnestesiLokalLainnya_edit = $this->input->post("obatAnestesiLokalLainnya_edit");
  $dosisJumlahObatGunaPAL_edit = $this->input->post("dosisJumlahObatGunaPAL_edit");
  $encerkanPAL_edit = $this->input->post("encerkanPAL_edit");
  $obatDiencerkanLainnya_edit = $this->input->post("obatDiencerkanLainnya_edit");
  $penggunaAdrenalinPAL_edit = $this->input->post("penggunaAdrenalinPAL_edit");
  $PenggunaanAdrenalinLainnya_edit = $this->input->post("PenggunaanAdrenalinLainnya_edit");
  $lokasiPemberianPAL_edit = $this->input->post("lokasiPemberianPAL_edit");
  $jamPemberianPAL_edit = $this->input->post("jamPemberianPAL_edit");
  $konversiAnestesiPAL_edit = $this->input->post("konversiAnestesiPAL_edit");
  $konversiLainnya_edit = $this->input->post("konversiLainnya_edit");
  $jumlahPerdarahanPAL_edit = $this->input->post("jumlahPerdarahanPAL_edit");
  $selesaiTindakanPAL_edit = $this->input->post("selesaiTindakanPAL_edit");
  $kesadaranPascaPAL_edit = $this->input->post("kesadaranPascaPAL_edit");
  $kesadaranPascaLainnya_edit = $this->input->post("kesadaranPascaLainnya_edit");
  $tandaToksisitasPAL_edit = $this->input->post("tandaToksisitasPAL_edit");
  $tandaToksisitasPAL_lainnya_edit = $this->input->post("tandaToksisitasPAL_lainnya_edit");
  $mualMuntahPAL_edit = $this->input->post("mualMuntahPAL_edit");
  $mualMuntahPAL_lainnya_edit = $this->input->post("mualMuntahPAL_lainnya_edit");
  $keluhanNyeriPAL_edit = $this->input->post("keluhanNyeriPAL_edit");
  $keluhanNyeri_lainnya_edit = $this->input->post("keluhanNyeri_lainnya_edit");
  $perdarahanPascaPasienPAL_edit = $this->input->post("perdarahanPascaPasienPAL_edit");
  $rencanaObservasiPAL_edit = $this->input->post("rencanaObservasiPAL_edit");
  $rencanaEdukasiPAL_edit = $this->input->post("rencanaEdukasiPAL_edit");
  $jamPindahPasienPAL_edit = $this->input->post("jamPindahPasienPAL_edit");
  $pasienPindahPAL_edit = $this->input->post("pasienPindahPAL_edit");
  $pasienPindah_lainnya_edit = $this->input->post("pasienPindah_lainnya_edit");
  $pengisi_edit = $this->input->post("pengisi_edit");
    // array
  $hasilSttsFisioJam_edit = $this->input->post("hasilSttsFisioJam_edit");
  $hasilSttsFisiotdSis_edit = $this->input->post("hasilSttsFisiotdSis_edit");
  $hasilSttsFisiotdDias_edit = $this->input->post("hasilSttsFisiotdDias_edit");
  $hasilSttsFisioNadi_edit = $this->input->post("hasilSttsFisioNadi_edit");
  $hasilSttsFisioRr_edit = $this->input->post("hasilSttsFisioRr_edit");
  $hasilSttsFisioSpo2_edit = $this->input->post("hasilSttsFisioSpo2_edit");
  $hasilSttsFisioEkg_edit = $this->input->post("hasilSttsFisioEkg_edit");

  $dataPAL = array(
    'id_anestesi_lokal' => $getid_pal,
    'nokun' => $nokun,
    'kesadaran' => isset($kesadaranPraPAL_edit) ? json_encode($kesadaranPraPAL_edit) : "",
    'kesadaran_lain' => isset($kesadaranPraLainnya_edit) ? $kesadaranPraLainnya_edit : null,
    'bb' => isset($bb_PAL_edit) ? $bb_PAL_edit : null,
    'tb' => isset($tb_PAL_edit) ? $tb_PAL_edit : null,
    'td_1' => isset($td_PAL_sistolik_edit) ? $td_PAL_sistolik_edit : null,
    'td_2' => isset($tb_PAL_diastolik_edit) ? $tb_PAL_diastolik_edit : null,
    'rr' => isset($rr_PAL_edit) ? $rr_PAL_edit : null,
    'n' => isset($n_PAL_edit) ? $n_PAL_edit : null,
    's' => isset($s_PAL_edit) ? $s_PAL_edit : null,
      //
    'hb' => isset($hb_PAL_edit) ? $hb_PAL_edit : null,
    't' => isset($t_PAL_edit) ? $t_PAL_edit : null,
    'ht' => isset($ht_PAL_edit) ? $ht_PAL_edit : null,
    'lab_lainnya' => isset($lainnyaLab_PAL_edit) ? $lainnyaLab_PAL_edit : null,
      //
    'alergi' => isset($riwayat_alergi_edit) ? $riwayat_alergi_edit : null,
    'isi_alergi' => isset($riwayat_alergi_desk_edit) ? json_encode($riwayat_alergi_desk_edit) : "",
    'reaksi_alergi' => isset($reaksi_alergi_edit) ? $reaksi_alergi_edit : null,
      //
    'obat_anestesi' => isset($obatAnestesiLokalPAL_edit) ? $obatAnestesiLokalPAL_edit : null,
    'obat_anestesi_lain' => isset($obatAnestesiLokalLainnya_edit) ? $obatAnestesiLokalLainnya_edit : null,
    'dosis' => isset($dosisJumlahObatGunaPAL_edit) ? $dosisJumlahObatGunaPAL_edit : null,
    'diencerkan' => isset($encerkanPAL_edit) ? $encerkanPAL_edit : null,
    'isi_diencerkan' => isset($obatDiencerkanLainnya_edit) ? $obatDiencerkanLainnya_edit : null,
    'penggunaan_adrenalin' => isset($penggunaAdrenalinPAL_edit) ? $penggunaAdrenalinPAL_edit : null,
    'isi_penggunaan_adrenalin' => isset($PenggunaanAdrenalinLainnya_edit) ? $PenggunaanAdrenalinLainnya_edit : null,
    'lokasi_pemberian' => isset($lokasiPemberianPAL_edit) ? $lokasiPemberianPAL_edit : null,
    'pukul_pemberian' => isset($jamPemberianPAL_edit) ? $jamPemberianPAL_edit : null,
      //
    'kejadian_konversi' => isset($konversiAnestesiPAL_edit) ? $konversiAnestesiPAL_edit : null,
    'isi_kejadian_konversi' => isset($konversiLainnya_edit) ? $konversiLainnya_edit : null,
    'jumlah_perdarahan' => isset($jumlahPerdarahanPAL_edit) ? $jumlahPerdarahanPAL_edit : null,
    'selesai_tindakan' => isset($selesaiTindakanPAL_edit) ? $selesaiTindakanPAL_edit : null,
      //
    'kesadaran_pasca' => isset($kesadaranPascaPAL_edit) ? json_encode($kesadaranPascaPAL_edit) : "",
    'kesadaran_pasca_lain' => isset($kesadaranPascaLainnya_edit) ? $kesadaranPascaLainnya_edit : null,
    'toksisitas' => isset($tandaToksisitasPAL_edit) ? $tandaToksisitasPAL_edit : null,
    'isi_toksisitas' => isset($tandaToksisitasPAL_lainnya_edit) ? $tandaToksisitasPAL_lainnya_edit : null,
    'mual_muntah' => isset($mualMuntahPAL_edit) ? $mualMuntahPAL_edit : null,
    'isi_mual_muntah' => isset($mualMuntahPAL_lainnya_edit) ? $mualMuntahPAL_lainnya_edit : null,
    'keluhan_nyeri' => isset($keluhanNyeriPAL_edit) ? $keluhanNyeriPAL_edit : null,
    'isi_keluhan_nyeri' => isset($keluhanNyeri_lainnya_edit) ? $keluhanNyeri_lainnya_edit : null,
    'perdarahan' => isset($perdarahanPascaPasienPAL_edit) ? $perdarahanPascaPasienPAL_edit : null,
      //
    'observasi' => isset($rencanaObservasiPAL_edit) ? $rencanaObservasiPAL_edit : null,
    'edukasi' => isset($rencanaEdukasiPAL_edit) ? $rencanaEdukasiPAL_edit : null,
    'pasien_pindah' => isset($jamPindahPasienPAL_edit) ? $jamPindahPasienPAL_edit : null,
    'ke' => isset($pasienPindahPAL_edit) ? $pasienPindahPAL_edit : null,
    'isi_ke' => isset($pasienPindah_lainnya_edit) ? $pasienPindah_lainnya_edit : null,
      //
    'status' => '1',
    'oleh' => isset($pengisi_edit) ? $pengisi_edit : null,
  );

  $dataPemantauan = array();
  $indexPemantauan = 0;
  if (isset($hasilSttsFisioJam_edit)) {
    foreach ($hasilSttsFisioJam_edit as $input) {
      if ($hasilSttsFisioJam_edit[$indexPemantauan] != "") {
        array_push(
          $dataPemantauan, array(
            'id_anestesi_lokal' => $getid_pal,
            'pukul' => $hasilSttsFisioJam_edit[$indexPemantauan],
            'td_1' => $hasilSttsFisiotdSis_edit[$indexPemantauan],
            'td_2' => $hasilSttsFisiotdDias_edit[$indexPemantauan],
            'n' => $hasilSttsFisioNadi_edit[$indexPemantauan],
            'rr' => $hasilSttsFisioRr_edit[$indexPemantauan],
            'sp' => $hasilSttsFisioSpo2_edit[$indexPemantauan],
            'ekg' => $hasilSttsFisioEkg_edit[$indexPemantauan],
          )
        );
      }
      $indexPemantauan++;
    }
  }

  $this->db->trans_begin();

  $this->db->replace('keperawatan.tb_anestesi_lokal', $dataPAL);
  $this->db->delete('keperawatan.tb_anestesi_lokal_pemantauan', array('id_anestesi_lokal' => $getid_pal));
  foreach ($dataPemantauan as $key => $value) {
    $this->db->replace('keperawatan.tb_anestesi_lokal_pemantauan', $value, 'id_pal');
  }
  if ($this->db->trans_status() === false) {
    $this->db->trans_rollback();
    $result = array('status' => 'failed');
  } else {
    $this->db->trans_commit();
    $result = array('status' => 'success');
  }

  echo json_encode($result);
}
}


/* End of file FormPemantauanAnestesiLokal.php */
/* Location: ./application/controllers/pengkajianprorad/FormPemantauanAnestesiLokal.php */
