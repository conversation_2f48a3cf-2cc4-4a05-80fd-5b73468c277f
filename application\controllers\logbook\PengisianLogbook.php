<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengisianLogbook extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'logbook/LogbookModel'));
  }

  public function index()
  {
    $oleh = $this->session->userdata('id');
    $namaPegawai = $this->LogbookModel->namaPegawai($oleh);
    $pilihListPengkajian = $this->LogbookModel->pilihListPengkajian($namaPegawai['id_pegawai_simpeg']);
    $cekDataLinkSimpeg = $this->LogbookModel->cekDataLinkSimpeg($oleh);
    $historyPengisianLogbookPengkajian = $this->LogbookModel->historyPengisianLogbookPengkajian($oleh);
    $historyPengisianLogbookAsuhan = $this->LogbookModel->historyPengisianLogbookAsuhan($oleh);
    $historyPengisianLogbookObservasi = $this->LogbookModel->historyPengisianLogbookObservasi($oleh);
    $historyPengisianLogbookCppt = $this->LogbookModel->historyPengisianLogbookCppt($oleh);
    $historyPengisianLogbookDiagnosa = $this->LogbookModel->historyPengisianLogbookDiagnosa($oleh);

    $data = array(
      'title'       => 'Pengisian Logbook',
      'isi'         => 'Logbook/index',
      'namaPegawai' => $namaPegawai,
      'pilihListPengkajian' => $pilihListPengkajian,
      'cekLink' => $cekDataLinkSimpeg['JUMLAH'],
      'historyPengisianLogbookPengkajian' => $historyPengisianLogbookPengkajian,
      'historyPengisianLogbookAsuhan' => $historyPengisianLogbookAsuhan,
      'historyPengisianLogbookObservasi' => $historyPengisianLogbookObservasi,
      'historyPengisianLogbookCppt' => $historyPengisianLogbookCppt,
      'historyPengisianLogbookDiagnosa' => $historyPengisianLogbookDiagnosa,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function viewTblIsianLogbook()
  {
    $idKinerja = $this->input->post('kinerja');
    $kuantitas = explode("^", $idKinerja);
    $mapping = $this->LogbookModel->jumlahKinerjaJenis($kuantitas[0]);
    $jenisMapping = $this->LogbookModel->mappingKinerjaJenis($kuantitas[0]);
    if($jenisMapping['JENIS'] == 1){
      $logbook = $this->LogbookModel->kinerjaListLogbookJenisSatu($mapping['kinerja']);
      $cek = $this->LogbookModel->cekJenisSatu($mapping['kinerja']);
    }else if($jenisMapping['JENIS'] == 2){
      $logbook = $this->LogbookModel->kinerjaListLogbookJenisDua($mapping['kinerja']);
      $cek = $this->LogbookModel->cekJenisDua($mapping['kinerja']);
    }else if($jenisMapping['JENIS'] == 3){
      $logbook = $this->LogbookModel->kinerjaListLogbookJenisTiga($mapping['kinerja']);
      $cek = $this->LogbookModel->cekJenisTiga($mapping['kinerja']);
    }else if($jenisMapping['JENIS'] == 4){
      $logbook = $this->LogbookModel->kinerjaListLogbookJenisEmpat($mapping['kinerja']);
      $cek = $this->LogbookModel->cekJenisEmpat($mapping['kinerja']);
    }else if($jenisMapping['JENIS'] == 5){
      $logbook = $this->LogbookModel->kinerjaListLogbookJenisLima($mapping['kinerja']);
      $cek = $this->LogbookModel->cekJenisLima($mapping['kinerja']);
    }

    $data = array(
      'idKinerja' => $kuantitas[0],
      'logbook' => $logbook,
      'jenisMapping' => $jenisMapping,
      'cek' => $cek,
    );

    $this->load->view('Logbook/tblIsianLogbook',$data);
  }

  public function viewDetailHisPengisianLogbook()
  {
    $idlog = $this->input->post('idlog');
    $historyDetailKinPengisianLogbookPengkajian = $this->LogbookModel->historyDetailKinPengisianLogbookPengkajian($idlog);

     $data = array(
      'idlog' => $idlog,
      'historyDetailKinPengisianLogbookPengkajian' => $historyDetailKinPengisianLogbookPengkajian
    );

    $this->load->view('Logbook/viewDetailHisPengisianLogbook', $data);
  }

  public function viewDetailHisPengisianLogbookAsuhan()
  {
    $idlog = $this->input->post('idlog');
    $historyDetailKinPengisianLogbookAsuhan = $this->LogbookModel->historyDetailKinPengisianLogbookAsuhan($idlog);

     $data = array(
      'idlog' => $idlog,
      'historyDetailKinPengisianLogbookAsuhan' => $historyDetailKinPengisianLogbookAsuhan
    );

    $this->load->view('Logbook/viewDetailHisPengisianLogbookAsuhan', $data);
  }

  public function viewDetailHisPengisianLogbookObservasi()
  {
    $idlog = $this->input->post('idlog');
    $historyDetailKinPengisianLogbookObservasi = $this->LogbookModel->historyDetailKinPengisianLogbookObservasi($idlog);

     $data = array(
      'idlog' => $idlog,
      'historyDetailKinPengisianLogbookObservasi' => $historyDetailKinPengisianLogbookObservasi
    );

    $this->load->view('Logbook/viewDetailHisPengisianLogbookObservasi', $data);
  }

  public function viewDetailHisPengisianLogbookCppt()
  {
    $idlog = $this->input->post('idlog');
    $historyDetailKinPengisianLogbookCppt = $this->LogbookModel->historyDetailKinPengisianLogbookCppt($idlog);

     $data = array(
      'idlog' => $idlog,
      'historyDetailKinPengisianLogbookCppt' => $historyDetailKinPengisianLogbookCppt
    );

    $this->load->view('Logbook/viewDetailHisPengisianLogbookCppt', $data);
  }

  public function viewDetailHisPengisianLogbookDiagnosa()
  {
    $idlog = $this->input->post('idlog');
    $historyDetailKinPengisianLogbookDiagnosa = $this->LogbookModel->historyDetailKinPengisianLogbookDiagnosa($idlog);

     $data = array(
      'idlog' => $idlog,
      'historyDetailKinPengisianLogbookDiagnosa' => $historyDetailKinPengisianLogbookDiagnosa
    );

    $this->load->view('Logbook/viewDetailHisPengisianLogbookDiagnosa', $data);
  }

  public function simpanPengisianLogbook()
  {
    $post = $this->input->post();
    $oleh = $this->session->userdata('id');
    $kuantitas = explode("^", $post['pilihPengisianLogbook']);
    $pegawai = $this->LogbookModel->namaPegawai($oleh);
    $id_pegawai = $pegawai['id_pegawai_simpeg'];
    $tglHariIni = date('Y-m-d H:i:s');
    $ceklisanLogbook = $_POST['cekLogbook'];
    $jenisLogbookKondisi = $_POST['jenisLogbookKondisi'];
    $jumlahCeklisan = count($ceklisanLogbook);
    $listKinerja = $this->LogbookModel->listKinerja($id_pegawai, $kuantitas[0]);

    if($jenisLogbookKondisi == 3)
    {
      $data = array(
          'kinerja' => isset($kuantitas[0]) ? $kuantitas[0] : "",
          'keterangan_kinerja' => isset($kuantitas[1]) ? $kuantitas[1] : "",
          'oleh' => $oleh,
          'status' => 1,
        );
        // echo "<pre>"; print_r($data); echo "</pre>"; exit();
        $getIdLog = $this->LogbookModel->simpanLogKinerja($data);

      $queryGabunganInsertObservasi = $this->LogbookModel->queryGabunganInsertObservasi($kuantitas[0]);
      foreach($queryGabunganInsertObservasi as $coba)
      {
        $cekIdLogbook = $this->LogbookModel->cekIdLogbook();
          $data = array(
            'ID' => $cekIdLogbook['ID_LOGBOOK'],
            'PEGAWAI' => $id_pegawai,
            'TANGGAL' => $tglHariIni,
            'KEGIATAN' => $kuantitas[0],
            'AKTIVITAS' => $coba['ISI_LOGBOOK'],
            'JUMLAH' => $coba['JUMLAH'],
            'STATUS' => 4,
            'OLEH' => $id_pegawai,
            'TGL_UBAH' => $tglHariIni,
            'jenis_kegiatan' => 1,
          );
        $simpanGabungLogObservasiTindakan = $this->LogbookModel->simpanGabungLogObservasiTindakan($data);
      }

      $dataKinerja = array();
      $indexKinerja = 0;
      if (isset($post['cekLogbook'])) {
        foreach ($post['cekLogbook'] as $input) {
          if ($post['cekLogbook'][$indexKinerja] != "") {
            array_push(
              $dataKinerja, array(
                'id_log' => $getIdLog,
                'jenis' => $post['jenisLogbook'][$indexKinerja],
                'ref' => $post['cekLogbook'][$indexKinerja],
                'status' => 1,
              )
            );
          }
          $indexKinerja++;
        }
      }

      if (isset($post['cekLogbook'])) {
        $this->db->insert_batch('keperawatan.tb_log_kinerja_detail', $dataKinerja);
      }
    }else{
      $cekIdLogbook = $this->LogbookModel->cekIdLogbook();
      $data = array(
        'kinerja' => isset($kuantitas[0]) ? $kuantitas[0] : "",
        'keterangan_kinerja' => isset($kuantitas[1]) ? $kuantitas[1] : "",
        'oleh' => $oleh,
        'status' => 1,
      );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $getIdLog = $this->LogbookModel->simpanLogKinerja($data);
    
    $dataSimpeg = array(
      'ID' => $cekIdLogbook['ID_LOGBOOK'],
      'PEGAWAI' => $id_pegawai,
      'TANGGAL' => $tglHariIni,
      'KEGIATAN' => isset($kuantitas[0]) ? $kuantitas[0] : "",
      'AKTIVITAS' => $listKinerja['INDIKATOR'],
      'JUMLAH' => $jumlahCeklisan,
      'STATUS' => 4,
      'OLEH' => $id_pegawai,
      'TGL_UBAH' => $tglHariIni,
      'jenis_kegiatan' => 1,
    );
    // echo "<pre>"; print_r($dataSimpeg); echo "</pre>"; exit();
    $simpanSimpeg = $this->LogbookModel->simpanSimpeg($dataSimpeg);    

    $dataKinerja = array();
    $indexKinerja = 0;
    if (isset($post['cekLogbook'])) {
      foreach ($post['cekLogbook'] as $input) {
        if ($post['cekLogbook'][$indexKinerja] != "") {
          array_push(
            $dataKinerja, array(
              'id_log' => $getIdLog,
              'jenis' => $post['jenisLogbook'][$indexKinerja],
              'ref' => $post['cekLogbook'][$indexKinerja],
              'status' => 1,
            )
          );
        }
        $indexKinerja++;
      }
    }

    // echo "<pre>"; print_r($dataKinerja); echo "</pre>"; exit();

    if (isset($post['cekLogbook'])) {
      $this->db->insert_batch('keperawatan.tb_log_kinerja_detail', $dataKinerja);
    }
      
    }
  }

}

/* End of file Profile.php */
/* Location: ./application/controllers/Profile.php */
