<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PerencanaanAKModel extends CI_Model
{
  public function simpan($data)
  {
    $this->db->insert_batch('keperawatan.tb_pak', $data);
  }

  public function ambil($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", ID, \"'\" SEPARATOR ','), ']') AS id_diagnosis
      FROM (
        SELECT ak.DESKRIPSI, ak.ID_VARIABEL ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1
        GROUP BY ak.ID
        UNION
        SELECT ak.DESKRIPSI, ak.ID_VARIABEL ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1
        GROUP BY ak.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function diagnosis($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", ID, \"'\" SEPARATOR ','), ']') AS diagnosis
      FROM (
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 1
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 1
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function idDiagnosisLain($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", ID, \"'\" SEPARATOR ','), ']') AS id_diagnosis_lain
      FROM (
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 1 AND p.lain_lain != ''
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 1 AND p.lain_lain != ''
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function diagnosisLain($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", lain_lain, \"'\" SEPARATOR ','), ']') AS diagnosis_lain
      FROM (
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 1 AND p.lain_lain != ''
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 1 AND p.lain_lain != ''
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function idHasil($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", ID,\"'\" SEPARATOR ','), ']') AS id_hasil
      FROM (
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function hasil($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", DESKRIPSI,\"'\" SEPARATOR ','), ']') AS hasil
      FROM (
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }
  public function idHasilLain($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", ID,\"'\" SEPARATOR ','), ']') AS id_hasil_lain
      FROM (
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2 AND p.lain_lain != ''
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2 AND p.lain_lain != ''
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function hasilLain($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", lain_lain, \"'\" SEPARATOR ','), ']') AS hasil_lain
      FROM (
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2 AND p.lain_lain != ''
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 2 AND p.lain_lain != ''
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function idIntervensi($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", ID, \"'\" SEPARATOR ','), ']') AS id_intervensi
      FROM (
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function intervensi($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", DESKRIPSI, \"'\" SEPARATOR ','), ']') AS intervensi
      FROM (
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function idIntervensiLain($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", ID, \"'\" SEPARATOR ','), ']') AS id_intervensi_lain
      FROM (
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3 AND p.lain_lain != ''
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3 AND p.lain_lain != ''
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function intervensiLain($nokun)
  {
    $query = $this->db->query(
      "SELECT CONCAT('[', GROUP_CONCAT(\"'\", lain_lain, \"'\" SEPARATOR ','), ']') AS intervensi_lain
      FROM (
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_pak p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        WHERE p.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3 AND p.lain_lain != ''
        GROUP BY akd.ID
        UNION
        SELECT akd.DESKRIPSI, akd.ID, p.lain_lain
        FROM keperawatan.tb_perencanaan_asuhan_keperawatan p
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON p.id_asuhan_keperawatan_detil = akd.ID
        LEFT JOIN db_master.tb_asuhan_keperawatan ak ON ak.ID = akd.ID_ASUHAN
        LEFT JOIN keperawatan.tb_keperawatan k ON k.ID_EMR = p.id_emr
        WHERE k.nokun = '$nokun' AND p.status = 1 AND akd.JENIS = 3 AND p.lain_lain != ''
        GROUP BY akd.ID
      ) a
      ORDER BY ID"
    );
    return $query->row_array();
  }

  public function ubah($id, $data)
  {
    $this->db->where('keperawatan.tb_pak.id', $id);
    $this->db->update('keperawatan.tb_pak', $data);
  }

  public function verifikasi($data, $jenis)
  {
    if ($jenis == 1) {
      $this->db->insert('keperawatan.tb_perencanaan_asuhan_keperawatan_verifikasi', $data);
    } else if ($jenis == 2) {
      $this->db->insert('keperawatan.tb_pak_verifikasi', $data);
    }
  }
}

/* End of file PerencanaanAKModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/keperawatan/PerencanaanAKModel.php */