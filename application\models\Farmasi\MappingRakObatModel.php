<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MappingRakObatModel extends CI_Model {

  public function depoFarmasi()
  {
    $query  = $this->db->query("SELECT r.ID, r.DESKRIPSI
                               FROM master.ruangan r
                               WHERE r.JENIS=5 AND r.JENIS_KUNJUNGAN=11 AND r.`STATUS`=1");
    return $query->result_array();
  }

  public function ruanganPenyimpanan($id)
  {
    $query  = $this->db->query("SELECT r.ID ID_DEPO, r.DESKRIPSI NAMA_DEPO_FARMASI
                               , rp.id ID_RUANG_PENYIMPANAN, rp.deskripsi NAMA_RUANGAN_PENYIMPANAN
                               , rp.keterangan KETERANGAN

                               FROM db_master.tb_ruangan_penyimpanan rp
                               LEFT JOIN master.ruangan r ON r.ID = rp.depo
                               WHERE rp.depo='$id' AND rp.`status`=1");

    return $query->result_array();
  }

  public function simpanMasterFarmasi($data)
  {
    $this->db->trans_begin();
    $this->db->insert('db_master.tb_rak_gudang', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyMasterFarmasi()
  {
    $query  = $this->db->query("SELECT trg.`*`, master.getNamaLengkapPegawai(ap.NIP)OLEH,
                               rgd.deskripsi RUANGANPENYIMPANAN, mr.DESKRIPSI NAMADEPO
                               FROM db_master.tb_rak_gudang trg
                               LEFT JOIN db_master.tb_ruangan_penyimpanan rgd ON rgd.id = trg.id_ruangan_penyimpanan
                               LEFT JOIN master.ruangan mr ON mr.ID=rgd.depo
                               LEFT JOIN aplikasi.pengguna ap ON ap.ID = trg.oleh
                               ORDER BY trg.id DESC");

    return $query;
  }

  public function getMasterFarmasi($id)
  {
    $query  = $this->db->query("SELECT trg.`*`, master.getNamaLengkapPegawai(ap.NIP)OLEH,
                               rgd.deskripsi RUANGANPENYIMPANAN, mr.DESKRIPSI NAMADEPO, rgd.depo IDRUANGAN
                               FROM db_master.tb_rak_gudang trg
                               LEFT JOIN db_master.tb_ruangan_penyimpanan rgd ON rgd.id = trg.id_ruangan_penyimpanan
                               LEFT JOIN master.ruangan mr ON mr.ID=rgd.depo
                               LEFT JOIN aplikasi.pengguna ap ON ap.ID = trg.oleh
                               WHERE trg.id='$id'");

    return $query->row_array();
  }

  public function obat($idDepo)
  {
    $this->db->select("br.RUANGAN, mr.DESKRIPSI, br.ID ID_BARANG_RUANGAN, ib.NAMA, br.STOK
                      , ik.NAMA SUBKELOMPOK
                      , forna.DESKRIPSI FORMULARIUM
                      , refkelger.DESKRIPSI KELAS_TERAPI
                      , subkel.DESKRIPSI SUB_KELAS_TERAPI");
    $this->db->from('inventory.barang_ruangan br');
    $this->db->join('inventory.barang ib', 'ib.ID = br.BARANG', 'left');
    $this->db->join('master.ruangan mr', 'mr.ID = br.RUANGAN', 'left');
    $this->db->join('inventory.harga_barang hb', 'hb.BARANG=ib.ID AND hb.`STATUS`=1', 'left');
    $this->db->join('inventory.satuan st', 'ib.SATUAN = st.ID', 'left');
    $this->db->join('inventory.satuan st2', 'ib.SATUAN_KONVERSI = st2.ID', 'left');
    $this->db->join('inventory.kategori ik', 'ib.KATEGORI = ik.ID', 'left');
    $this->db->join('master.referensi mk', 'ib.KELOMPOK = mk.ID and mk.JENIS = 73', 'left');
    $this->db->join('master.referensi forna', 'forna.ID = ib.FORMULARIUM AND forna.JENIS=80', 'left');
    $this->db->join('master.referensi forrs', 'forrs.ID = ib.FORMULARIUM_RS AND forrs.JENIS=83', 'left');
    $this->db->join('master.referensi refkelger', 'refkelger.ID = ib.KELAS_TERAPI AND refkelger.JENIS=72', 'left');
    $this->db->join('master.referensi subkel', 'subkel.ID = ib.SUB_KELAS_TERAPI AND subkel.JENIS=85', 'left');

    $this->db->where(array('br.RUANGAN'=>$idDepo, 'br.`STATUS`'=> 1));
    $this->db->order_by('SUBKELOMPOK', 'desc');
    $this->db->order_by('NAMA', 'desc');
    $this->db->limit(20);

    if($this->input->get('q')){
      $this->db->like('ib.NAMA', $this->input->get('q'));
    }

    $query  = $this->db->get();
    return $query->result_array();
  }

  public function historyPerak($idRak)
  {
    $query  = $this->db->query("SELECT mo.id ID_INPUT, mr.DESKRIPSI DEPO, rp.deskripsi LOKASI_GUDANG,
                               rg.nama_rak NAMA_RAK
                               , rg.keterangan_rak KETERANGAN_RAK
                               , ib.NAMA NAMA_OBAT, ik.NAMA KATEGORI
                               , mo.slot SLOT_PENYIMPANAN, mo.created_at TANGGAL_INPUT, master.getNamaLengkapPegawai(peng.NIP) USER
                               , IF(mo.`status`=1, 'Aktif', 'Non-Aktif') STATUS

                               FROM db_layanan.tb_mapping_obat mo

                               LEFT JOIN db_master.tb_rak_gudang rg ON rg.id = mo.id_rak_gudang
                               LEFT JOIN db_master.tb_ruangan_penyimpanan rp ON rp.id = rg.id_ruangan_penyimpanan
                               LEFT JOIN aplikasi.pengguna peng ON peng.ID = mo.oleh
                               LEFT JOIN inventory.barang_ruangan br ON br.ID = mo.id_barang_ruangan
                               LEFT JOIN inventory.barang ib ON ib.ID = br.BARANG
                               LEFT JOIN master.ruangan mr ON mr.ID = br.RUANGAN
                               LEFT JOIN inventory.harga_barang hb ON hb.BARANG=ib.ID AND hb.`STATUS`=1
                               LEFT JOIN inventory.satuan st ON ib.SATUAN = st.ID
                               LEFT JOIN inventory.satuan st2 ON ib.SATUAN_KONVERSI = st2.ID
                               LEFT JOIN inventory.kategori ik ON ib.KATEGORI = ik.ID
                               LEFT JOIN master.referensi mk ON ib.KELOMPOK = mk.ID and mk.JENIS = 73
                               LEFT JOIN master.referensi forna ON forna.ID = ib.FORMULARIUM AND forna.JENIS=80
                               LEFT JOIN master.referensi forrs ON forrs.ID = ib.FORMULARIUM_RS AND forrs.JENIS=83
                               LEFT JOIN master.referensi refkelger ON refkelger.ID = ib.KELAS_TERAPI AND refkelger.JENIS=72
                               LEFT JOIN master.referensi subkel ON subkel.ID = ib.SUB_KELAS_TERAPI AND subkel.JENIS=85

                               WHERE rg.id='$idRak'

                               ORDER BY mo.slot ASC, ib.NAMA ASC ");

    return $query;
  }

  public function simpanModalRak($data)
  {
    $this->db->trans_begin();
    $this->db->insert('db_layanan.tb_mapping_obat', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function tblhistoryPerak()
  {
    $query  = $this->db->query("SELECT mo.id ID_INPUT, mr.DESKRIPSI DEPO, rp.deskripsi LOKASI_GUDANG,
                               rg.nama_rak NAMA_RAK
                               , rg.keterangan_rak KETERANGAN_RAK
                               , ib.NAMA NAMA_OBAT, ik.NAMA KATEGORI
                               , mo.slot SLOT_PENYIMPANAN, mo.created_at TANGGAL_INPUT, master.getNamaLengkapPegawai(peng.NIP) USER
                               , IF(mo.`status`=1, 'Aktif', 'Non-Aktif') STATUS

                               FROM db_layanan.tb_mapping_obat mo

                               LEFT JOIN db_master.tb_rak_gudang rg ON rg.id = mo.id_rak_gudang
                               LEFT JOIN db_master.tb_ruangan_penyimpanan rp ON rp.id = rg.id_ruangan_penyimpanan
                               LEFT JOIN aplikasi.pengguna peng ON peng.ID = mo.oleh
                               LEFT JOIN inventory.barang_ruangan br ON br.ID = mo.id_barang_ruangan
                               LEFT JOIN inventory.barang ib ON ib.ID = br.BARANG
                               LEFT JOIN master.ruangan mr ON mr.ID = br.RUANGAN
                               LEFT JOIN inventory.harga_barang hb ON hb.BARANG=ib.ID AND hb.`STATUS`=1
                               LEFT JOIN inventory.satuan st ON ib.SATUAN = st.ID
                               LEFT JOIN inventory.satuan st2 ON ib.SATUAN_KONVERSI = st2.ID
                               LEFT JOIN inventory.kategori ik ON ib.KATEGORI = ik.ID
                               LEFT JOIN master.referensi mk ON ib.KELOMPOK = mk.ID and mk.JENIS = 73
                               LEFT JOIN master.referensi forna ON forna.ID = ib.FORMULARIUM AND forna.JENIS=80
                               LEFT JOIN master.referensi forrs ON forrs.ID = ib.FORMULARIUM_RS AND forrs.JENIS=83
                               LEFT JOIN master.referensi refkelger ON refkelger.ID = ib.KELAS_TERAPI AND refkelger.JENIS=72
                               LEFT JOIN master.referensi subkel ON subkel.ID = ib.SUB_KELAS_TERAPI AND subkel.JENIS=85

                               ORDER BY mo.slot ASC, ib.NAMA ASC ");

    return $query;
  }

}

/* End of file MappingRakObatModel.php */
/* Location: ./application/models/Farmasi/MappingRakObatModel.php */
