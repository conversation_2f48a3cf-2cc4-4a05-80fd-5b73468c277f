<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SurKetEmModel extends CI_Model {

	public function simpanData($data)
	{
		$this->db->insert('medis.tb_surat_keterangan_emergency', $data);
		return $this->db->insert_id();
	}

  public function diagnosa_utama()
	{
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD10_1998'
			GROUP BY CODE LIMIT 10");
		return $query->result_array();
	}

	public function tindakan_procedure()
	{
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD9CM_2005' 
			GROUP BY CODE LIMIT 10");
		return $query->result_array();
	}

	public function icd10()
	{
		$q = $this->input->get('q');
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD10_1998' AND (CODE LIKE '%". $q ."%' OR STR LIKE '%". $q ."%')
			GROUP BY CODE LIMIT 10");

		return $query->result();
	}

	public function icd9()
	{
		$q = $this->input->get('q');
		$query = $this->db->query("SELECT CODE,STR
			FROM master.mrconso
			WHERE SAB = 'ICD9CM_2005' AND (CODE LIKE '%". $q ."%' OR STR LIKE '%". $q ."%')
			GROUP BY CODE LIMIT 10");

		return $query->result();
	}

	public function historySurKetEm($norm)
	{
		$query = $this->db->query('SELECT kp.id ID_SKE, kp.nokun NOKUN, kp.created_at TANGGAL_SKE
            , master.getNamaLengkapPegawai(peng.NIP) USER
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , rk.DESKRIPSI RUANGAN_KUNJUNGAN
            , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , p.NOMOR NOPEN

            FROM medis.tb_surat_keterangan_emergency kp

            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
            LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
            LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
            LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.oleh

            WHERE kp.status=1 AND p.NORM="' . $norm . '"

            ORDER BY kp.created_at DESC');
		return $query->result_array();
	}

	public function terapiObat($nopen)
	{
		$query = $this->db->query('SELECT p.NORM, p.NOMOR NOPEN
			, pku.NOMOR NOKUN, ib.NAMA NAMA_OBAT, lf.JUMLAH
			, IF(ref.DESKRIPSI IS NULL, lf.ATURAN_PAKAI, ref.DESKRIPSI) ATURANPAKAI, lf.DOSIS
			, lf.KETERANGAN, CONCAT(lf.RACIKAN,lf.GROUP_RACIKAN) RACIKAN, lf.PETUNJUK_RACIKAN,
			lf.`STATUS` STATUSLAYANAN

			FROM pendaftaran.pendaftaran p

			LEFT JOIN pendaftaran.kunjungan pku ON pku.NOPEN = p.NOMOR
			LEFT JOIN layanan.order_resep ore ON ore.NOMOR = pku.REF
			LEFT JOIN layanan.farmasi lf ON lf.KUNJUNGAN = pku.NOMOR
			LEFT JOIN master.referensi ref ON ref.ID=lf.ATURAN_PAKAI AND ref.JENIS=41
			LEFT JOIN inventory.barang ib ON ib.ID = lf.FARMASI

			WHERE ore.RESEP_PASIEN_PULANG=1 and
			p.`STATUS`!=0 and pku.`STATUS`!=0 and lf.`STATUS`=2
			AND p.NOMOR= "' . $nopen . '"

			ORDER BY lf.RACIKAN, lf.GROUP_RACIKAN');
		return $query->result_array();
	}

	public function getHistorySurKetEm($idSur)
	{
		$query = $this->db->query('SELECT sv.id ID_SKE
			, sv.nokun NOKUN, sv.created_at TANGGAL
			, master.getNamaLengkapPegawai(dpjp.NIP) DPJP
			, rk.DESKRIPSI RUANGAN_KUNJUNGAN
			, p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
			, sv.`*`
			, tv.`*`

			FROM medis.tb_surat_keterangan_emergency sv
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = sv.nokun
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
			LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
			LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
			LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
			LEFT JOIN aplikasi.pengguna peng ON peng.ID = sv.oleh
			LEFT JOIN db_pasien.tb_tanda_vital tv ON tv.data_source = 24 AND tv.ref = sv.id
			WHERE sv.id="' . $idSur . '"');
		return $query->row_array();
	}

}

/* End of file ProfileModel.php */
/* Location: ./application/models/ProfileModel.php */
