<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PPRAModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
    }
    
    function jawab_ppra($nomr)
    {
        $this->db->select('ppra.id, ppra.tanggal, ppra.jam, masru.DESKRIPSI ruangan, master.getNamaLengkapPegawai(dok.NIP) DPJP, ppra.status');
        $this->db->from('keperawatan.tb_ppra_kirim ppra');
        $this->db->join('master.ruangan masru','ppra.ruangan = masru.ID','LEFT');
        $this->db->join('master.dokter dok','ppra.dpjp = dok.ID','LEFT');
        $this->db->join('pendaftaran.kunjungan kun','ppra.nokun = kun.NOMOR','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','kun.NOPEN = pen.NOMOR','LEFT');
        $this->db->where_in('ppra.status',1);
        $this->db->where('pen.NORM',$nomr);
        $this->db->order_by('ppra.created_at', 'DESC');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    function history_kirim($id_kirim_ppra)
    {
        $query = $this->db->query("SELECT k.*, vital.td_sistolik TD_SISTOLIK, vital.td_diastolik TD_DIASTOLIK, vital.nadi NADI, vital.pernapasan PERNAPASAN, vital.suhu SUHU, vital.nomr, var.variabel oksigenasi_desc,sadar.kesadaran
            FROM keperawatan.tb_ppra_kirim k     
            LEFT JOIN db_pasien.tb_tanda_vital vital ON k.id = vital.ref AND vital.data_source = 37
            LEFT JOIN db_pasien.tb_kesadaran sadar ON k.id = sadar.ref AND sadar.data_source = 37
            LEFT JOIN db_master.variabel var ON var.id_variabel = k.oksigenasi
            WHERE k.id = '$id_kirim_ppra' AND k.status != 0 ");

        return $query->row_array();
    }

    function history_jawab($id_jawab_ppra)
    {
        $query = $this->db->query("SELECT * FROM keperawatan.tb_ppra_jawab j WHERE j.id = '$id_jawab_ppra' AND j.status != 0");

        return $query->row_array();
    }

	function history_ppra($nomr)
    {
        $this->db->select('ppra.id, j.id id_jawab, ppra.tanggal, ppra.jam, masru.DESKRIPSI ruangan, master.getNamaLengkapPegawai(dok.NIP) DPJP, ppra.status');
        $this->db->from('keperawatan.tb_ppra_kirim ppra');
        $this->db->join('keperawatan.tb_ppra_jawab j','ppra.id = j.id_kirim_ppra','LEFT');
        $this->db->join('master.ruangan masru','ppra.ruangan = masru.ID','LEFT');
        $this->db->join('master.dokter dok','ppra.dpjp = dok.ID','LEFT');
        $this->db->join('pendaftaran.kunjungan kun','ppra.nokun = kun.NOMOR','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','kun.NOPEN = pen.NOMOR','LEFT');
        $this->db->where('ppra.status',2);
        $this->db->where('j.status',1);
        $this->db->where('pen.NORM',$nomr);
        $this->db->order_by('ppra.created_at', 'DESC');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    function getPengkajian($id_kirim)
    {
        $query = $this->db->query("SELECT k.*, k.antimikroba, k.dosis, j.rekomendasi, j.disetujui_dgn_catatan
        , j.evaluasi, j.nama_kpra, vital.td_sistolik TD_SISTOLIK, vital.td_diastolik TD_DIASTOLIK, vital.nadi NADI, vital.pernapasan PERNAPASAN, vital.suhu SUHU, j.acc
            FROM keperawatan.tb_ppra_kirim k     
                LEFT JOIN keperawatan.tb_ppra_jawab j ON k.id = j.id_kirim_ppra
                LEFT JOIN db_pasien.tb_tanda_vital vital ON k.id = vital.ref AND vital.data_source = 37
            WHERE j.`status` = 1 AND k.id = '$id_kirim' ");

        return $query->row_array();
    }

    function getDarahLengkap($id_lab_dl)
    {
        $this->db->select('hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN 
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL');
        $this->db->from('lis.hasil_log hlo');
        $this->db->join('pendaftaran.kunjungan pendK','pendK.NOMOR = hlo.HIS_NO_LAB','LEFT');
        $this->db->join('master.tindakan mt','mt.ID = hlo.HIS_KODE_TEST','LEFT');
        $this->db->join('layanan.order_lab orla','orla.NOMOR=pendK.REF','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pendK.NOPEN','LEFT');
        $this->db->where_in('hlo.ID',$id_lab_dl);
        $this->db->where_in('mt.ID',[1439,1435,1430]);
        $this->db->group_by('mt.NAMA, hlo.LIS_NAMA_TEST');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    function getFungsiHati($id_lab_fh)
    {
        $this->db->select('hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN 
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL');
        $this->db->from('lis.hasil_log hlo');
        $this->db->join('pendaftaran.kunjungan pendK','pendK.NOMOR = hlo.HIS_NO_LAB','LEFT');
        $this->db->join('master.tindakan mt','mt.ID = hlo.HIS_KODE_TEST','LEFT');
        $this->db->join('layanan.order_lab orla','orla.NOMOR=pendK.REF','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pendK.NOPEN','LEFT');
        $this->db->where_in('hlo.ID',$id_lab_fh);
        $this->db->where_in('mt.ID',[1482,1481,1484,1493,1485,1487,1488,1490,5309,169]);
        $this->db->group_by('mt.NAMA, hlo.LIS_NAMA_TEST');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    function getFungsiGinjal($id_lab_fg)
    {
        $this->db->select('hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN 
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL');
        $this->db->from('lis.hasil_log hlo');
        $this->db->join('pendaftaran.kunjungan pendK','pendK.NOMOR = hlo.HIS_NO_LAB','LEFT');
        $this->db->join('master.tindakan mt','mt.ID = hlo.HIS_KODE_TEST','LEFT');
        $this->db->join('layanan.order_lab orla','orla.NOMOR=pendK.REF','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pendK.NOPEN','LEFT');
        $this->db->where_in('hlo.ID',$id_lab_fg);
        $this->db->where_in('mt.ID',[1511,1515,1517,1521,1549]);
        $this->db->group_by('mt.NAMA, hlo.LIS_NAMA_TEST');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    function getMarkerInfeksi($id_lab_mi)
    {
        $this->db->select('hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN 
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL');
        $this->db->from('lis.hasil_log hlo');
        $this->db->join('pendaftaran.kunjungan pendK','pendK.NOMOR = hlo.HIS_NO_LAB','LEFT');
        $this->db->join('master.tindakan mt','mt.ID = hlo.HIS_KODE_TEST','LEFT');
        $this->db->join('layanan.order_lab orla','orla.NOMOR=pendK.REF','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pendK.NOPEN','LEFT');
        $this->db->where_in('hlo.ID',$id_lab_mi);
        $this->db->where_in('mt.ID',[5232,5233,5279,5300,5301,5302
                                    ,5242,1183,447,1181,1390,1375
                                    ,1581,449,127,1405,5317,1179
                                    ,131,560,76,55,63,2102,1606,5368]);
        
        $query = $this->db->get();
        return $query->result_array();
    }

    function getKultur($id_lab_k)
    {
        $this->db->select('hlo.ID ID_HASIL, orla.TANGGAL TANGGAL_ORDER
        , pendK.MASUK TGL_MASUK_LAB
        , hlo.HIS_NO_LAB NOKUN_LAB, p.NOMOR NOPENS, p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , mt.ID ID_TINDAKAN_SIMPEL, mt.NAMA TINDAKAN_SIMPEL
        , hlo.LIS_NAMA_TEST PARAMETER, hlo.LIS_NILAI_NORMAL NILAI_RUJUKAN, hlo.LIS_SATUAN
        , hlo.LIS_HASIL, hlo.LIS_CATATAN, hlo.LIS_TANGGAL TGL_HASIL');
        $this->db->from('lis.hasil_log hlo');
        $this->db->join('pendaftaran.kunjungan pendK','pendK.NOMOR = hlo.HIS_NO_LAB','LEFT');
        $this->db->join('master.tindakan mt','mt.ID = hlo.HIS_KODE_TEST','LEFT');
        $this->db->join('layanan.order_lab orla','orla.NOMOR=pendK.REF','LEFT');
        $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pendK.NOPEN','LEFT');
        $this->db->where_in('hlo.ID',$id_lab_k);
        $this->db->where_in('mt.ID',[1541,1560,1563,1569]);
        
        $query = $this->db->get();
        return $query->result_array();
    }

    function getIdDokter($id_pengguna)
    {
        $query = $this->db->query("SELECT d.ID FROM aplikasi.pengguna p
                                LEFT JOIN master.dokter d ON p.NIP = d.NIP
                                WHERE p.ID = '$id_pengguna'");

        return $query->row_array();
    }

    function getDataDokter($id_dokter) 
    {
        $query = $this->db->query("SELECT master.getNamaLengkapPegawai(d.NIP) NAMA, k.NOMOR 
                        FROM master.dokter d
                        LEFT JOIN master.kontak_pegawai k ON d.NIP = k.`NIP`
                        WHERE d.ID = '$id_dokter'");

        return $query->row_array();
    }

    function jumlahNotifikasi($id_pengguna)
    {
        $query = $this->db->query("SELECT k.id
            FROM keperawatan.tb_ppra_kirim k
            LEFT JOIN pendaftaran.kunjungan kun ON k.nokun = kun.NOMOR 
            LEFT JOIN pendaftaran.pendaftaran pen ON kun.NOPEN = pen.NOMOR
            LEFT JOIN aplikasi.pengguna peng ON k.oleh = peng.ID
            LEFT JOIN master.dokter dok ON k.konsul_dokter = dok.ID
            LEFT JOIN aplikasi.pengguna pengdok ON dok.NIP = pengdok.NIP
        WHERE k.status = 1 AND pengdok.ID = '$id_pengguna'");
    
        return $query->num_rows();
    }

    public function notifikasiPPRA($id_pengguna)
    {
        $query = $this->db->query("SELECT k.id ID_KONSUL, k.nokun, pen.NORM, master.getNamaLengkap(pen.NORM) NAMA_PASIEN
        , master.getNamaLengkapPegawai(peng.NIP) PENGIRIM, k.TANGGAL, k.JAM
        , master.getNamaLengkapPegawai(dok.NIP) TUJUAN
            FROM keperawatan.tb_ppra_kirim k
            LEFT JOIN pendaftaran.kunjungan kun ON k.nokun = kun.NOMOR 
            LEFT JOIN pendaftaran.pendaftaran pen ON kun.NOPEN = pen.NOMOR
            LEFT JOIN aplikasi.pengguna peng ON k.oleh = peng.ID
            LEFT JOIN master.dokter dok ON k.konsul_dokter = dok.ID
            LEFT JOIN aplikasi.pengguna pengdok ON dok.NIP = pengdok.NIP
        WHERE k.status = 1 AND pengdok.ID = '$id_pengguna'");

        return $query->result();
    }

    public function formJawabKonsul($id_konsul)
    { 
        $query = $this->db->query("SELECT pen.NORM norm, master.getNamaLengkap(pen.NORM) pasien, k.*, vital.td_sistolik TD_SISTOLIK, vital.td_diastolik TD_DIASTOLIK, vital.nadi NADI, vital.pernapasan PERNAPASAN, vital.suhu SUHU, vital.nomr
        FROM keperawatan.tb_ppra_kirim k     
            LEFT JOIN db_pasien.tb_tanda_vital vital ON k.id = vital.ref AND vital.data_source = 37
            LEFT JOIN pendaftaran.kunjungan kun ON k.nokun = kun.NOMOR
            LEFT JOIN pendaftaran.pendaftaran pen ON kun.NOPEN = pen.NOMOR
        WHERE k.id = '$id_konsul' AND k.status != 0 ");

        return $query->result_array();
    }

    public function ppra_tvterakhir($nomr)
    { 
        $squery="SELECT k.oksigenasi,k.saturasi, vital.td_sistolik, vital.td_diastolik, vital.pernapasan,vital.nadi,vital.suhu, vital.created_at tanggal, `master`.getNamaLengkapPegawai(peng.NIP) oleh_desc
        FROM keperawatan.tb_ppra_kirim k     
        LEFT JOIN db_pasien.tb_tanda_vital vital ON k.id = vital.ref AND vital.data_source = 37
        LEFT JOIN db_master.variabel var ON var.id_variabel = k.oksigenasi
        LEFT JOIN aplikasi.pengguna peng ON vital.oleh=peng.ID
        WHERE vital.nomr = '$nomr' AND k.status >0
        AND vital.id IS NOT NULL
        ORDER by k.created_at DESC limit 1";
        // echo $squery;
        $query = $this->db->query($squery);

        // $query = $this->db->query("SELECT td_sistolik, td_diastolik, pernapasan,nadi,suhu
        // FROM db_pasien.tb_tanda_vital vital   
        // WHERE vital.nokun = '$nokun' AND vital.status = 1 
        // ORDER by created_at DESC limit 1");

        return $query->row_array();
    }

}
