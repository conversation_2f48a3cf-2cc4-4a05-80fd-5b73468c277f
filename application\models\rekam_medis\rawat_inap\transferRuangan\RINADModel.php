<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RINADModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_rinad';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Angka',
      )
    ),
  );

  public function __construct()
  {
    parent::__construct();
  }

  public function replace($data)
  {
    $this->db->replace('keperawatan.tb_rinad', $data);
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_rinad.id', $id);
    $this->db->update('keperawatan.tb_rinad', $data);
  }

  public function history($nokun, $param)
  {
    if ($param == 'jumlah') {
      $this->db->select('r.id');
    } elseif ($param == 'tabel') {
      $this->db->select(
        'r.id, r.tanggal, r.jam, r.diizinkan_masuk, r.diizinkan_keluar, master.getNamaLengkapPegawai(peng.NIP) pengisi,
        r.updated_at, r.status'
      );
    }
    $this->db->from('keperawatan.tb_rinad r');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = r.oleh', 'left');
    $this->db->where('r.nokun', $nokun);
    if ($param == 'jumlah') {
      $this->db->where('r.status', 1);
      $query = $this->db->get();
      return $query->num_rows();
    } elseif ($param == 'tabel') {
      $this->db->order_by('r.updated_at', 'DESC');
      $query = $this->db->get();
      return $query;
    } else {
      return null;
    }
  }

  public function detail($id)
  {
    $this->db->select(
      'r.id, r.tanggal, r.jam, r.kontak_langsung, r.droplet, r.sida, r.diizinkan_masuk, r.tidak_menular, r.negatif_mrsa,
      r.bukan_isolasi, r.perawatan_intensif, r.meninggal, r.diizinkan_keluar,
      master.getNamaLengkapPegawai(peng.NIP) pengisi, r.updated_at, r.status'
    );
    $this->db->from('keperawatan.tb_rinad r');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = r.oleh', 'left');
    $this->db->where('r.id', $id);
    $query = $this->db->get();
    return $query->result_array();
  }
}