<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanOperasiModel extends MY_Model
{
  protected $_table_name = 'medis.tb_laporan_operasi';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = [
    'nokun' => [
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      ]
    ],

    'radio_diagnosis_pra_bedah' => [
      'field' => 'radio_diagnosis_pra_bedah',
      'label' => 'Diagnosis pra bedah',
      'rules' => 'trim|required',
      'errors' => [
        'required' => '%s wajib diisi',
      ]
    ],

    'radio_diagnosis_pasca_bedah' => [
      'field' => 'radio_diagnosis_pasca_bedah',
      'label' => 'Diagnosis pasca bedah',
      'rules' => 'trim|required',
      'errors' => [
        'required' => '%s wajib diisi',
      ]
    ],

    'tujuan_operasi' => [
      'field' => 'tujuan_operasi',
      'label' => 'Tujuan operasi',
      'rules' => 'trim|required',
      'errors' => [
        'required' => '%s wajib diisi',
      ]
    ],

    'jenis_tindakan' => [
      'field' => 'jenis_tindakan',
      'label' => 'Tindakan operasi yang dilakukan',
      'rules' => 'trim|required',
      'errors' => [
        'required' => '%s wajib diisi',
      ]
    ],

    'deskripsi_operasi_1' => [
      'field' => 'deskripsi_operasi_1',
      'label' => 'Deskripsi atau uraian operasi dokter 1',
      'rules' => 'trim|required',
      'errors' => [
        'required' => '%s wajib diisi',
      ]
    ],

    'riwayat_penyakit_sekarang' => [
      'field' => 'riwayat_penyakit_sekarang',
      'label' => 'Data riwayat penyakit sekarang',
      'rules' => 'trim|required',
      'errors' => [
        'required' => '%s wajib diisi',
      ]
    ],
  ];

  public function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('medis.tb_laporan_operasi', $data);
    return $this->db->insert_id();
  }

  public function simpanPraBedah($data)
  {
    $this->db->insert_batch('medis.tb_laporan_operasi_pra_bedah', $data);
  }

  public function simpanPascaBedah($data)
  {
    $this->db->insert_batch('medis.tb_laporan_operasi_pasca_bedah', $data);
  }

  public function simpanTindakan($data)
  {
    $this->db->insert_batch('medis.tb_laporan_operasi_detail', $data);
  }

  public function simpanTransfusi($data)
  {
    $this->db->insert_batch('medis.tb_laporan_operasi_transfusi', $data);
  }

  public function simpanLokalis($data)
  {
    $this->db->insert('medis.tb_laporan_operasi_lokalis', $data);
  }

  public function history($nomr, $nokun, $param, $id)
  {
    if (isset($id)) { // Detail Laporan Operasi
      $this->db->select(
        "lo.id, lo.nokun, p.NORM, wlo.id id_wlo, lo.tgl_operasi, lo.dokter_bedah, lo.asisten_operator,
        lo.asisten_operator_lainnya, lo.perawat_instrumentator, lo.perawat_sirkuler, lo.dokter_anestesi,
        lo.jenis_anestesi, lo.teknik_anestesi_lokal, lo.keterangan_teknik, lo.lokasi, lo.obat_anestesi,
        lo.respon_hipersensitivitas, lo.isi_respon_hipersensitivitas, lo.kejadian_toksikasi, lo.isi_kejadian_toksikasi,
        lo.kategori_operasi, lo.lok_pengambilan_sampel, pra_bedah.CODE id_pra_bedah, pra_bedah.STR diagnosis_pra_bedah,
        lo.diagnosis_pra_bedah_lainnya, lo.letak_tumor_primer, lo.tgl_operasi, lo.jam_mulai, lo.jam_selesai,
        pasca_bedah.CODE id_pasca_bedah, pasca_bedah.STR diagnosis_pasca_bedah, lo.diagnosis_pasca_bedah_lainnya,
        lo.sifat_operasi, lo.tujuan_operasi, lo.jenis_pembedahan, lo.antibiotik_propilaksis,
        lo.jenis_antibiotik_propilaksis, lo.waktu_antibiotik_propilaksis, lo.tindakan_operasi_lainnya,
        lo.deskripsi_operasi_1, lo.deskripsi_operasi_2, lo.deskripsi_operasi_3, lo.komplikasi, lo.isi_komplikasi,
        lo.jml_kehilangan_darah, lo.jenis_transfusi, lo.volume_transfusi, lo.spesimen, lo.isi_spesimen,
        lo.pemasangan_implan, lo.nama_implan, lo.seri_implan, lo.ket_riwayat_penyakit_sekarang,
        GROUP_CONCAT(DISTINCT CONCAT_WS(' - ', tindakan.CODE, tindakan.STR)) AS tindakan_operasi,
        GROUP_CONCAT(DISTINCT CONCAT_WS(' - ', pra_bedah_multiple.CODE, pra_bedah_multiple.STR)) AS diagnosis_pra_bedah_multiple,
        GROUP_CONCAT(DISTINCT CONCAT_WS(' - ', pasca_bedah_multiple.CODE, pasca_bedah_multiple.STR)) AS diagnosis_pasca_bedah_multiple"
      );
    } elseif ($param == 'rapo') { // Ambil diagnosis pasca operasi dan tindakan operasi untuk Rencana Asuhan Pasca Operasi
      $this->db->select(
        "pasca_bedah.CODE id_pasca_bedah, pasca_bedah.STR diagnosis_pasca_bedah, lo.diagnosis_pasca_bedah_lainnya,
        lo.tindakan_operasi_lainnya, GROUP_CONCAT(DISTINCT CONCAT_WS(' - ', tindakan.CODE, tindakan.STR)) AS tindakan"
      );
    } elseif ($param == 'jumlah') { // Jumlah Laporan Operasi
      $this->db->select('lo.id');
    } elseif ($param == 'tabel') { // History Laporan Operasi
      $this->db->select(
        "lo.id, lo.nokun, lo.tgl_operasi, master.getNamaLengkapPegawai(peng.NIP) pengisi, rk.DESKRIPSI ruang,
        master.getNamaLengkapPegawai(dpjp.NIP) DPJP, lo.created_at, master.getNamaLengkap(p.NORM) pasien, lo.status,
        pra_bedah.CODE id_pra_bedah, pra_bedah.STR diagnosis_pra_bedah, lo.diagnosis_pra_bedah_lainnya,
        pasca_bedah.CODE id_pasca_bedah, pasca_bedah.STR diagnosis_pasca_bedah, lo.diagnosis_pasca_bedah_lainnya,
        GROUP_CONCAT(DISTINCT CONCAT_WS(' - ', pra_bedah_multiple.CODE, pra_bedah_multiple.STR)) AS diagnosis_pra_bedah_multiple,
        GROUP_CONCAT(DISTINCT CONCAT_WS(' - ', pasca_bedah_multiple.CODE, pasca_bedah_multiple.STR)) AS diagnosis_pasca_bedah_multiple"
      );
    }

    $this->db->from('medis.tb_laporan_operasi lo');

    if ($param != 'rapo') { // Rencana Asuhan Pasca Operasi tidak membutuhkan diagnosis pra operasi
      $this->db->join(
        'master.mrconso pra_bedah',
        "pra_bedah.CODE = lo.diagnosis_pra_bedah AND pra_bedah.SAB = 'ICD10_1998'",
        'left'
      );
      $this->db->join('medis.tb_laporan_operasi_pra_bedah lopr', 'lopr.id_laporan_operasi = lo.id AND lopr.status = 1', 'left');
      $this->db->join(
        'master.mrconso pra_bedah_multiple',
        "pra_bedah_multiple.CODE = lopr.diagnosis_pra_bedah_multiple AND pra_bedah_multiple.SAB = 'ICD10_1998'",
        'left'
      );
    }

    $this->db->join(
      'master.mrconso pasca_bedah',
      "pasca_bedah.CODE = lo.diagnosis_pasca_bedah AND pasca_bedah.SAB = 'ICD10_1998'",
      'left'
    );
    $this->db->join('medis.tb_laporan_operasi_pasca_bedah lopc', 'lopc.id_laporan_operasi = lo.id AND lopc.status = 1', 'left');
    $this->db->join(
      'master.mrconso pasca_bedah_multiple',
      "pasca_bedah_multiple.CODE = lopc.diagnosis_pasca_bedah_multiple AND pasca_bedah_multiple.SAB = 'ICD10_1998'",
      'left'
    );
    $this->db->join('medis.tb_laporan_operasi_detail lod', 'lod.id_laporan_operasi = lo.id AND lod.status = 1', 'left');
    $this->db->join(
      'master.mrconso tindakan',
      "tindakan.CODE = lod.tindakan_operasi AND tindakan.SAB = 'ICD9CM_2005'",
      'left'
    );

    $this->db->join('medis.tb_waiting_list_operasi wlo', 'wlo.id = lo.id_waiting_list', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = lo.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'left');
    $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'left');
    $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = lo.oleh');
    if (isset($id)) { // Detail Laporan Operasi
      $this->db->group_by('id_pra_bedah', 'id_pasca_bedah');
      $this->db->where('lo.id', $id);
      $query = $this->db->get();
      return $query->row_array();
    } elseif ($param == 'rapo') { // Ambil Diagnosis Pasca Operasi dan Tindakan Operasi untuk Rencana Asuhan Pasca Operasi
      $this->db->where('lo.nokun', $nokun);
      $this->db->where('lo.status !=', 0);
      $this->db->group_by('id_pasca_bedah');
      $this->db->order_by('lo.id', 'DESC');
      $this->db->limit(1);
      $query = $this->db->get();
      return $query->row_array();
    } else {
      $this->db->group_by('id', 'id_pra_bedah', 'id_pasca_bedah');
      $this->db->where('p.NORM', $nomr);
      $this->db->order_by('lo.id', 'DESC');
      $query = $this->db->get();
      if ($param == 'jumlah') {
        return $query->num_rows(); // Jumlah Laporan Operasi
      } elseif ($param == 'tabel') {
        $this->db->order_by('lo.tgl_operasi', 'DESC');
        return $query; // Tabel Laporan Operasi
      } else {
        return null;
      }
    }
  }

  public function praBedah($id)
  {
    $this->db->select('lopr.diagnosis_pra_bedah_multiple');
    $this->db->from('medis.tb_laporan_operasi_pra_bedah lopr');
    $this->db->where('lopr.id_laporan_operasi', $id);
    $this->db->where('lopr.status', 1);
    $this->db->order_by('lopr.id', 'asc');
    $query = $this->db->get();
    return $query->result_array();
  }

  public function pascaBedah($id)
  {
    $this->db->select('lopc.diagnosis_pasca_bedah_multiple');
    $this->db->from('medis.tb_laporan_operasi_pasca_bedah lopc');
    $this->db->where('lopc.id_laporan_operasi', $id);
    $this->db->where('lopc.status', 1);
    $this->db->order_by('lopc.id', 'asc');
    $query = $this->db->get();
    return $query->result_array();
  }

  public function tindakan($id)
  {
    $this->db->select('lod.tindakan_operasi');
    $this->db->from('medis.tb_laporan_operasi_detail lod');
    $this->db->where('lod.id_laporan_operasi', $id);
    $this->db->where('lod.status', 1);
    $this->db->order_by('lod.id', 'asc');
    $query = $this->db->get();
    return $query->result_array();
  }

  public function historyTransfusi($id)
  {
    $this->db->select('lot.jenis_transfusi, lot.volume_transfusi');
    $this->db->from('medis.tb_laporan_operasi_transfusi lot');
    $this->db->where('lot.id_laporan_operasi', $id);
    $this->db->where('lot.status', 1);
    $this->db->order_by('lot.id', 'asc');
    $query = $this->db->get();
    return $query->result_array();
  }

  public function historyLokalis($id)
  {
    $this->db->select('lol.id, lol.judul, lol.created_at');
    $this->db->from('medis.tb_laporan_operasi_lokalis lol');
    $this->db->where('lol.id_laporan_operasi', $id);
    $this->db->where('lol.status', 1);
    $this->db->order_by('lol.id', 'asc');
    $query = $this->db->get();
    return $query->result_array();
  }

  public function hasilFotoLokalis($id)
  {
    $this->db->select('lol.id, lol.judul, lol.data, lol.catatan, lol.created_at');
    $this->db->from('medis.tb_laporan_operasi_lokalis lol');
    $this->db->where('lol.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function ubah($data, $id)
  {
    $this->db->where('medis.tb_laporan_operasi.id', $id);
    $this->db->update('medis.tb_laporan_operasi', $data);
  }

  public function ubahPraBedah($data, $id)
  {
    $this->db->where('medis.tb_laporan_operasi_pra_bedah.id_laporan_operasi', $id);
    $this->db->update('medis.tb_laporan_operasi_pra_bedah', $data);
  }

  public function ubahPascaBedah($data, $id)
  {
    $this->db->where('medis.tb_laporan_operasi_pasca_bedah.id_laporan_operasi', $id);
    $this->db->update('medis.tb_laporan_operasi_pasca_bedah', $data);
  }

  public function ubahDetail($data, $id)
  {
    $this->db->where('medis.tb_laporan_operasi_detail.id_laporan_operasi', $id);
    $this->db->update('medis.tb_laporan_operasi_detail', $data);
  }

  public function ubahTransfusi($data, $id)
  {
    $this->db->where('medis.tb_laporan_operasi_transfusi.id_laporan_operasi', $id);
    $this->db->update('medis.tb_laporan_operasi_transfusi', $data);
  }

  public function ubahLokalisIdLapOperasi($data, $id)
  {
    $this->db->where('medis.tb_laporan_operasi_lokalis.id_laporan_operasi', $id);
    $this->db->update('medis.tb_laporan_operasi_lokalis', $data);
  }

  public function ubahLokalis($data, $id)
  {
    $this->db->where('medis.tb_laporan_operasi_lokalis.id', $id);
    $this->db->update('medis.tb_laporan_operasi_lokalis', $data);
  }
}

// End of file LaporanOperasiModel.php
// Location: ./application/models/operasi/LaporanOperasiModel.php