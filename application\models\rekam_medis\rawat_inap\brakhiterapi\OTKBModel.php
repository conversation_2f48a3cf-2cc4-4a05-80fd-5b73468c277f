<?php
defined('BASEPATH') or exit('No direct script access allowed');

class OTKBModel extends MY_Model
{

  protected $_table_name = 'keperawatan.tb_observasi_tindakan';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'tanggal' => array(
      'field' => 'tanggal',
      'label' => 'Tanggal',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
      )
    ),

    'pukul' => array(
      'field' => 'pukul',
      'label' => 'Pukul',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s <PERSON>ajib <PERSON>',
      )
    ),

    'td_sistolik' => array(
      'field' => 'td_sistolik',
      'label' => 'Tekanan <PERSON>',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'td_diastolik' => array(
      'field' => 'td_diastolik',
      'label' => 'Tekanan Darah Diastolik',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'nadi' => array(
      'field' => 'nadi',
      'label' => 'Nadi',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'pernapasan' => array(
      'field' => 'pernapasan',
      'label' => 'Pernapasan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'suhu' => array(
      'field' => 'suhu',
      'label' => 'Suhu',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'saturasi_o2' => array(
      'field' => 'saturasi_o2',
      'label' => 'Saturasi O<sub>2</sub>',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'oral' => array(
      'field' => 'oral',
      'label' => 'Oral',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'parenteral' => array(
      'field' => 'parenteral',
      'label' => 'Parenteral',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'muntah' => array(
      'field' => 'muntah',
      'label' => 'Muntah',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'bak' => array(
      'field' => 'bak',
      'label' => 'BAK',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),

    'pendarahan' => array(
      'field' => 'pendarahan',
      'label' => 'Pendarahan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi',
        'numeric' => '%s Wajib Angka',
      )
    ),
  );

  // Pra Tindakan
  public $rulesIV = array(
    'iv' => array(
      'field' => 'iv',
      'label' => 'Lokasi tangan pemasangan <i>IV Line</i>',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetSuppositoria = array(
    'ket_suppositoria' => array(
      'field' => 'ket_suppositoria',
      'label' => 'Keterangan memberikan obat suppositoria',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  public $rulesMenyiapkan = array(
    'menyiapkan' => array(
      'field' => 'menyiapkan[]',
      'label' => 'Menyiapkan...',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  public $rulesKetIntra = array(
    'ket_intra' => array(
      'field' => 'ket_intra',
      'label' => 'Keterangan intra',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetOvoid = array(
    'ket_ovoid' => array(
      'field' => 'ket_ovoid',
      'label' => 'Keterangan ovoid',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  public $rulesKetDiameter = array(
    'ket_diameter' => array(
      'field' => 'ket_diameter',
      'label' => 'Keterangan diameter',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  public $rulesJumlahDiameter = array(
    'jumlah_diameter' => array(
      'field' => 'jumlah_diameter',
      'label' => 'Jumlah diameter',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  // Intra Tindakan
  public $rulesTeknikAnestesi = array(
    'teknik_anestesi' => array(
      'field' => 'teknik_anestesi',
      'label' => 'Teknik anestesi',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetTeknikAnestesi = array(
    'ket_teknik_anestesi' => array(
      'field' => 'ket_teknik_anestesi',
      'label' => 'Keterangan teknik anestesi',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  public $rulesPosisiPasien = array(
    'posisi_pasien' => array(
      'field' => 'posisi_pasien',
      'label' => 'Mengatur posisi pasien',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetOksigenNasal = array(
    'ket_oksigen_nasal' => array(
      'field' => 'ket_oksigen_nasal',
      'label' => 'Keterangan memasang oksigen nasal',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  public $rulesMendampingiTindakan = array(
    'mendampingi_tindakan' => array(
      'field' => 'mendampingi_tindakan[]',
      'label' => 'Mendampingi tindakan sebagai...',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetMemberikanObat = array(
    'ket_memberikan_obat' => array(
      'field' => 'ket_memberikan_obat',
      'label' => 'Keterangan memberikan obat sesuai instruksi',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  public $rulesKassaMasuk = array(
    'kassa_masuk' => array(
      'field' => 'kassa_masuk',
      'label' => 'Jumlah kassa masuk',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKassaKeluar = array(
    'kassa_keluar' => array(
      'field' => 'kassa_keluar',
      'label' => 'Jumlah kassa keluar',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesAntarPasien = array(
    'antar_pasien' => array(
      'field' => 'antar_pasien',
      'label' => 'Mengantar pasien ke ruang....',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetAngkatAplikator = array(
    'ket_angkat_aplikator' => array(
      'field' => 'ket_angkat_aplikator',
      'label' => 'Tanggal pengangkatan',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  // Pasca Tindakan
  public $rulesMemberikan = array(
    'ruang_memberikan' => array(
      'field' => 'memberikan',
      'label' => 'Memberikan....',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesSkriningNyeri = array(
    'skrining_nyeri' => array(
      'field' => 'skrining_nyeri',
      'label' => 'Melakukan skrining nyeri',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetSkriningNyeri = array(
    'ket_skrining_nyeri' => array(
      'field' => 'ket_skrining_nyeri',
      'label' => 'Keterangan melakukan skrining nyeri',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesSkriningJatuh = array(
    'skrining_jatuh' => array(
      'field' => 'skrining_jatuh',
      'label' => 'Melakukan skrining jatuh',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s wajib diisi',
        'numeric' => '%s wajib angka',
      )
    ),
  );

  public $rulesKetSerahTerima = array(
    'ket_serah_terima' => array(
      'field' => 'ket_serah_terima',
      'label' => 'Keterangan melakukan serah terima pasien',
      'rules' => 'trim|required',
      'errors' => array(
        'required' => '%s wajib diisi',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_otk_brakhiterapi', $data);
  }

  public function ubah($idObservasi, $dataOTKB)
  {
    $this->db->where('keperawatan.tb_otk_brakhiterapi.ref', $idObservasi);
    $this->db->update('keperawatan.tb_otk_brakhiterapi', $dataOTKB);
  }

  public function history($nokun, $nomr, $param, $id)
  {
    if (isset($id)) {
      // Detail
      $this->db->select(
        "ot.id, ot.nokun, ot.tanggal, ot.jam, tv.td_sistolik, tv.td_diastolik, tv.nadi, tv.pernapasan, tv.suhu,
        o2.saturasi_o2, ot.oral, ot.parenteral, ot.muntah, ot.bak, ot.pendarahan, ot.balance, ot.status,
        master.getNamaLengkapPegawai(peng.NIP) perawat, otkb.brakhiterapi, otkb.waktu_pra_tindakan,
        tv_pra.td_sistolik td_sistolik_pra, tv_pra.td_diastolik td_diastolik_pra, tv_pra.nadi nadi_pra,
        tv_pra.pernapasan pernapasan_pra, tv_pra.suhu suhu_pra, o2_pra.saturasi_o2 saturasi_o2_pra,
        otkb.orientasi_edukasi, otkb.periksa_vital_pra, otkb.iv, otkb.suppositoria, otkb.ket_suppositoria,
        otkb.menyiapkan, otkb.ket_intra, otkb.ket_ovoid, otkb.ket_diameter, otkb.jumlah_diameter,
        otkb.operator_anestesi, otkb.antar_tindakan, otkb.waktu_intra, tv_intra.td_sistolik td_sistolik_intra,
        tv_intra.td_diastolik td_diastolik_intra, tv_intra.nadi nadi_intra, tv_intra.pernapasan pernapasan_intra,
        tv_intra.suhu suhu_intra, o2_intra.saturasi_o2 saturasi_o2_intra, otkb.teknik_anestesi,
        otkb.ket_teknik_anestesi, otkb.posisi_pasien, otkb.oksigen_nasal, otkb.ket_oksigen_nasal,
        otkb.monitoring_vital, otkb.mendampingi_tindakan, otkb.suction, otkb.memberikan_obat, otkb.ket_memberikan_obat,
        otkb.fiksasi_aplikator, otkb.kassa_masuk, otkb.kassa_keluar, otkb.tali_pengaman, otkb.antar_pasien,
        otkb.antar_microselectron, otkb.angkat_aplikator, otkb.ket_angkat_aplikator, otkb.irigasi_vagina,
        otkb.ruang_pemulihan, otkb.waktu_pasca_tindakan, tv_pasca.td_sistolik td_sistolik_pasca,
        tv_pasca.td_diastolik td_diastolik_pasca, tv_pasca.nadi nadi_pasca, tv_pasca.pernapasan pernapasan_pasca,
        tv_pasca.suhu suhu_pasca, o2_pasca.saturasi_o2 saturasi_o2_pasca, otkb.memberikan, otkb.periksa_vital_pasca,
        otkb.skrining_nyeri, otkb.ket_skrining_nyeri, otkb.skrining_jatuh, otkb.serah_terima, otkb.ket_serah_terima,
        (
          SELECT GROUP_CONCAT(otd.id_pak SEPARATOR '-')
          FROM keperawatan.tb_observasi_tindakan_detail otd
          WHERE otd.id_observasi_tindakan = ot.id
        ) tindakan"
      );
    } elseif (isset($param)) {
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->select('ot.id');
      } elseif ($param == 'history') {
        // Tabel history
        $this->db->select('ot.id, ot.tanggal, ot.jam, master.getNamaLengkapPegawai(peng.NIP) perawat, ot.status');
      }
    }
    $this->db->from('keperawatan.tb_observasi_tindakan ot');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'ot.id = tv.ref AND tv.data_source = 16', 'left');
    $this->db->join('db_pasien.tb_o2 o2', 'ot.id = o2.ref AND o2.data_source = 16', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv_pra', 'ot.id = tv_pra.ref AND tv_pra.data_source = 43', 'left');
    $this->db->join('db_pasien.tb_o2 o2_pra', 'ot.id = o2_pra.ref AND o2_pra.data_source = 43', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv_intra', 'ot.id = tv_intra.ref AND tv_intra.data_source = 44', 'left');
    $this->db->join('db_pasien.tb_o2 o2_intra', 'ot.id = o2_intra.ref AND o2_intra.data_source = 44', 'left');
    $this->db->join('db_pasien.tb_tanda_vital tv_pasca', 'ot.id = tv_pasca.ref AND tv_pasca.data_source = 45', 'left');
    $this->db->join('db_pasien.tb_o2 o2_pasca', 'ot.id = o2_pasca.ref AND o2_pasca.data_source = 45', 'left');
    $this->db->join('keperawatan.tb_otk_brakhiterapi otkb', 'ot.id = otkb.ref', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = ot.oleh', 'left');
    if (isset($id)) {
      // Detail
      $this->db->where('ot.id', $id);
      $query = $this->db->get();
      return $query->row_array();
    } elseif (isset($param)) {
      if (isset($nokun)) {
        $this->db->where('ot.nokun', $nokun);
      } elseif (isset($nomr)) {
        $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = ot.NOKUN', 'left');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k.NOPEN', 'left');
        $this->db->where('p.NORM', $nomr);
      }
      if ($param == 'jumlah') {
        // Jumlah
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'history') {
        // Tabel history
        $this->db->order_by('otkb.updated_at', 'desc');
        $query = $this->db->get();
        return $query->result_array();
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}

/* End of file OTKBModel.php */
/* Location: ./application/models/rekam_medis/brakhiterapi/OTKBModel.php */