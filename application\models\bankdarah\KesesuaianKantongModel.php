<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KesesuaianKantongModel extends MY_Model
{
    protected $_table_name = 'keperawatan.tb_kesesuaian_kantong';
    protected $_primary_key = 'id';
    protected $_order_by = 'tanggal';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>isi.',
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'nomor_kantong' => array(
            'field' => 'nomor_kantong',
            'label' => 'Nomor Kantong',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.'
            ),
        ),

        'golongan_darah_kesesuaian' => array(
            'field' => 'golongan_darah_kesesuaian',
            'label' => '<PERSON><PERSON><PERSON> Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.',
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'label_darah' => array(
            'field' => 'label_darah',
            'label' => 'Label Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.',
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'identitas_pasien' => array(
            'field' => 'identitas_pasien',
            'label' => 'Identitas Darah',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.',
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'perawat1' => array(
            'field' => 'perawat1',
            'label' => 'Perawat 1',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.',
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'perawat2' => array(
            'field' => 'perawat2',
            'label' => 'Perawat 2',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.',
                'numeric' => '%s Wajib Angka.'
            ),
        ),

    );

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select('kk.id, kk.kunjungan, kk.nomor_kantong, gd.variabel golongan_darah
		, ld.variabel label_darah, ip.variabel identitas_pasien
		,`master`.getNamaLengkapPegawai(kk.perawat_1) perawat_1
		,`master`.getNamaLengkapPegawai(kk.perawat_2) perawat_2');
        $this->db->from('keperawatan.tb_kesesuaian_kantong kk');
        $this->db->join('db_master.variabel gd', 'kk.golongan_darah = gd.id_variabel AND gd.id_referensi = 756', 'LEFT');
        $this->db->join('db_master.variabel ld', 'kk.label_darah = ld.id_variabel AND ld.id_referensi = 886', 'LEFT');
        $this->db->join('db_master.variabel ip', 'kk.identitas_pasien = ip.id_variabel AND ip.id_referensi = 887', 'LEFT');

        $this->db->where('kk.status !=', '0');
        $this->db->where('kk.kunjungan', $this->input->post('nokun'));
        $this->db->order_by('kk.tanggal', 'DESC');
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}
