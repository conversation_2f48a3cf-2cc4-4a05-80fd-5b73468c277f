<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormSkriningPasienBaru extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(
      array(
        'masterModel',
        'rekam_medis/SkriningPasienBaruModel',
      )
    );
  }

  public function index()
  {
    // $nomr = $this->uri->segment(2);
    // $dataPasien = $this->NapakModel->dataDiriPasien($nomr);
    $data = array(
      'title' => 'Napak',
      'isi' => 'rekam_medis/skriningPasienBaru/index',
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('layout/wrapper', $data);
  }

  public function get_data_list_pasien(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));

    $listPasien = $this->SkriningPasienBaruModel->listPasienSkriningBaru();

		$data = array();
		$no = 1;
		foreach ($listPasien->result() as $LP) {
		
			$button = '<a href="#modal_tambah_skrining" class="btn btn-primary btn-block" data-id="'.$LP->ID.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"> Form</a>';
			
      $data[] = array(
          // $no,
          $LP->NAMA,
          $LP->NOMR,
          $LP->TGL_LAHIR,
          $LP->TELP_SELULER,
          $LP->TGL_DAFTAR,
          $LP->JAMINAN,
          $button
      );
      $no++;
		}
	
		$output = array(
        "draw"            => $draw,
        "recordsTotal"    => $listPasien->num_rows(),
        "recordsFiltered" => $listPasien->num_rows(),
        "data"            => $data
		);
		echo json_encode($output);
	}


  public function modalTambah()
  {
    $id = $this->input->post('id');
    $dataUser = $this->SkriningPasienBaruModel->listDataPasienSkriningBaru($id)->row_array();
    // $skrin = $this->SkriningPasienBaruModel->getDNR($id);

    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'dataUser' 	=> $dataUser,
      'SMF' => $this->masterModel->listSMF(),
      'HasilPeriksa' => $this->masterModel->referensi(10),
      'stadiumSkriningBaru' => $this->masterModel->stadium(),
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'Timja' => $this->masterModel->referensiTimja(102, null),
      'poliklinikSkriningBaru' => $this->masterModel->ruanganRawatJalan(),
    );

    $this->load->view('rekam_medis/skriningPasienBaru/modal_tambah_skrining', $data);
  }


  public function simpanskriningPasienBaru()
  {
    $this->db->trans_begin();

    $post = $this->input->post();
    $created_at = date("Y-m-d H:i:s");
    // $date = $this->input->post('datePickerDNR');
    // $date1 = $this->input->post('datePickerDokterDNR');
    // $tglPersetujuanKeluarga = date('Y-m-d h:i', strtotime($date));
    // $tglPersetujuanDokter = date('Y-m-d h:i', strtotime($date1));

    $dataSkriningPasienBaru = array(
      'ID'        			            => $post['id_SkriningPasienBaru'],
      'NAMA_PASIEN'        			    => $post['NAMA_PASIEN'],
      'NORM'        			          => $post['NORM'],
      'RS_RUJUKAN_ASAL'             => isset($post['rsAsal-SkriningBaru']) ? $post['rsAsal-SkriningBaru'] : null,
      'SMF'              		        => isset($post['spesialisSkriningBaru']) ? $post['spesialisSkriningBaru'] : null,
      'ALASAN_DIRUJUK'              => isset($post['alasan-SkriningBaru']) ? $post['alasan-SkriningBaru'] : null,
      'DOKTER_PERUJUK'              => isset($post['dokterPerujuk-SkriningBaru']) ? $post['dokterPerujuk-SkriningBaru'] : null,
      'RIWAYAT_TATA_LAKSANA'        => isset($post['riwayat-SkriningBaru']) ? $post['riwayat-SkriningBaru'] : null,
      'LABORATORIUM'                => isset($post['laboratorium']) ? $post['laboratorium'] : null,
      'LAB_DESKRIPSI'               => isset($post['laboratoriumDeskripsi-SkriningBaru']) ? $post['laboratoriumDeskripsi-SkriningBaru'] : null,
      'PATOLOGI_ANATOMI'            => isset($post['anatomi']) ? $post['anatomi'] : null,
      'PA_DESKRIPSI'                => isset($post['anatomiDeskripsi-SkriningBaru']) ? $post['anatomiDeskripsi-SkriningBaru'] : null,
      'RADIODIAGNOSTIK'             => isset($post['radiodiagnostik']) ? $post['radiodiagnostik'] : null,
      'RADIODIAGNOSTIK_DESKRIPSI'   => isset($post['radiodiagnostikDeskripsi-SkriningBaru']) ? $post['radiodiagnostikDeskripsi-SkriningBaru'] : null,
      // 'DIAGNOSIS'                   => implode(',',$post["diagnosis_SkriningBaru"]),
      'STADIUM'                     => isset($post['stadiumSkriningBaru']) ? $post['stadiumSkriningBaru'] : null,
      'KESIMPULAN'                  => isset($post['kesimpulan_SkriningBaru']) ? $post['kesimpulan_SkriningBaru'] : null,
      'KESIMPULAN_DITERIMA'         => isset($post['dimana_SkriningBaru']) ? $post['dimana_SkriningBaru'] : null,
      'TIMJA'                       => isset($post['timjaSkriningBaru']) ? $post['timjaSkriningBaru'] : null,
      'DPJP'                        => isset($post['dpjpSkriningBaru']) ? $post['dpjpSkriningBaru'] : null,
      'POLI'                        => isset($post['poliklinikSkriningBaru']) ? $post['poliklinikSkriningBaru'] : null,
      'CATATAN_KHUSUS'              => isset($post['catatan-SkriningBaru']) ? $post['catatan-SkriningBaru'] : null,
      'KESIMPULAN_TIDAK_DITERIMA'   => isset($post['tidakSarana_SkriningBaru']) ? $post['tidakSarana_SkriningBaru'] : null,
      'KESIMPULAN_LAINNYA'          => isset($post['deskLainnya-SkriningBaru']) ? $post['deskLainnya-SkriningBaru'] : null,
      'CREATED_AT'                  => $created_at,
      'CREATED_BY'                  => $this->session->userdata('id'),
      'STATUS' 					            => 1
    );
    // echo "<pre>";print_r($dataSkriningPasienBaru);echo "</pre>";exit();

    $this->db->insert('db_layanan.tb_skrining_pasienbaru',$dataSkriningPasienBaru);
    $idSkriningBaru = $this->db->insert_id();

    //Diagnosa Penyakit NAPAK
    $dataDiagnosa = array();
    $diagnosa = $post['diagnosis_SkriningBaru'];

    $index = 0;
    foreach($diagnosa as $d){
    array_push($dataDiagnosa, array(
        'ID_SKRINING'      => $idSkriningBaru,
        'DIAGNOSIS'        => $d,
        'CREATED_AT'       => $created_at,
        'CREATED_BY'       => $this->session->userdata('id'),
      ));
      
      $index++;
    }
    // echo "<pre>";print_r($dataDiagnosa);echo "</pre>";exit();
    $this->db->insert_batch('db_layanan.tb_diagnosa_skrining_pasienbaru', $dataDiagnosa);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function get_data_history_pasien(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));

    $listPasien = $this->SkriningPasienBaruModel->historyPasienSkriningBaru();

		$data = array();
		$no = 1;
		foreach ($listPasien->result() as $LP) {
		
			$button = '<a href="#modal_lihat_skrining" class="btn btn-primary btn-block" data-id="'.$LP->ID_SKRINING_EMR.'"  data-toggle="modal" data-backdrop="static" data-keyboard="false"> Detail</a>';
			
      $data[] = array(
          // $no,
          $LP->NAMA_PASIEN,
          $LP->NORM,
          $LP->STATUS_PASIEN,
          $LP->TGL_STATUS,
          $button
      );
      $no++;
		}
	
		$output = array(
        "draw"            => $draw,
        "recordsTotal"    => $listPasien->num_rows(),
        "recordsFiltered" => $listPasien->num_rows(),
        "data"            => $data
		);
		echo json_encode($output);
	}

  public function modalLihat()
  {
    $id = $this->input->post('id');
    $dataPasienSkrining = $this->SkriningPasienBaruModel->dataHistoryPasienSkriningBaru($id)->row_array();
    $diagnosa = $this->SkriningPasienBaruModel->diagnosa($id)->result_array();
    // $skrin = $this->SkriningPasienBaruModel->getDNR($id);

    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'dataPasienSkrining' 	=> $dataPasienSkrining,
      'diagnosa' => $diagnosa,
      // 'HasilPeriksa' => $this->masterModel->referensi(10),
      // 'stadiumSkriningBaru' => $this->masterModel->stadium(),
      // 'listDrUmum' => $this->masterModel->listDrUmum(),
      // 'Timja' => $this->masterModel->referensiTimja(102, null),
      // 'poliklinikSkriningBaru' => $this->masterModel->ruanganRawatJalan(),
    );

    $this->load->view('rekam_medis/skriningPasienBaru/modal_lihat_skrining', $data);
  }


}
?>