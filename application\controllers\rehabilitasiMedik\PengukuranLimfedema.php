<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengukuranLimfedema extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'rehabilitasiMedik/PengukuranLimfedemaModel',
        'rehabilitasiMedik/PengukuranLimfedema1Model'
      ]
    );
  }

  public function index()
  {
    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $data = [
      'nomr' => $nomr,
      'nokun' => $nokun,
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'dataTersimpan' => $this->PengukuranLimfedema1Model->dataTersimpan($nomr) ?? null,
      'jumlah' => $this->PengukuranLimfedema1Model->dataTersimpan($nomr, 1) ?? null,
      'karnofskyIndex' => $this->PengukuranLimfedemaModel->karnofskyKategori(),
      'bagian' => $this->masterModel->referensi(1806),
      'sisi' => $this->masterModel->referensi(1807),
      'tandaRadang' => $this->masterModel->referensi(1804),
      'pitting' => $this->masterModel->referensi(1805),
      'nyeri' => $this->masterModel->referensi(729),
    ];
    // echo '<pre>';print_r($data);exit();

    $this->load->view('Pengkajian/rehabilitasiMedik/limfedema/index', $data);
  }

  public function panjangBagian()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $data = $this->PengukuranLimfedemaModel->panjangBagian($post['id']);
    // echo '<pre>';print_r($data);exit();
    echo json_encode(
      [
        'status' => 'success',
        'detail' => $data,
      ]
    );
  }

  public function simpan()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $oleh = $this->session->userdata('id');

    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      // Mulai rules
      $this->form_validation->set_rules($this->PengukuranLimfedema1Model->rules());
      if (empty($post['id_limfedema'])) {
        $this->form_validation->set_rules($this->PengukuranLimfedema1Model->rulesSimpan());

        // Mulai bagian
        if ($post['bagian'] == 6076) { // Bagian lengan
          $this->form_validation->set_rules($this->PengukuranLimfedemaModel->rulesLengan());
        } elseif ($post['bagian'] == 6077) { // Bagian tungkai
          $this->form_validation->set_rules($this->PengukuranLimfedemaModel->rulesTungkai());
        }
        // Akhir bagian
      }
      // Akhir rules

      if ($this->form_validation->run() == true) {
        // Mulai data pengukuran limfedema
        if (empty($post['id_limfedema'])) {
          $dataPL1 = [
            'nomr' => $post['nomr'],
            'tanggal' => $post['tanggal'],
            'bagian' => $post['bagian'],
            'oleh' => $oleh,
            'status' => 1,
          ];
          $idLimfedema = $this->PengukuranLimfedema1Model->simpan($dataPL1);
        } else {
          $idLimfedema = $post['id_limfedema'];
        }
        // echo '<pre>';print_r($dataPL1);exit();
        // Akhir data pengukuran limfedema

        $dataPL = [
          'id_limfedema' => $idLimfedema,
          'kunjungan' => $post['nokun'],
          'tanggal' => $post['tanggal'],
          'sisi' => json_encode($post['sisi']),
          'panjang_bagian_lengan1' => $post['panjang_bagian_lengan1'] ?? null,
          'lengan_kanan1' => $post['lengan_kanan1'] ?? null,
          'lengan_kiri1' => $post['lengan_kiri1'] ?? null,
          'panjang_bagian_lengan2' => $post['panjang_bagian_lengan2'] ?? null,
          'lengan_kanan2' => $post['lengan_kanan2'] ?? null,
          'lengan_kiri2' => $post['lengan_kiri2'] ?? null,
          'panjang_bagian_lengan3' => $post['panjang_bagian_lengan3'] ?? null,
          'lengan_kanan3' => $post['lengan_kanan3'] ?? null,
          'lengan_kiri3' => $post['lengan_kiri3'] ?? null,
          'panjang_bagian_lengan4' => $post['panjang_bagian_lengan4'] ?? null,
          'lengan_kanan4' => $post['lengan_kanan4'] ?? null,
          'lengan_kiri4' => $post['lengan_kiri4'] ?? null,
          'panjang_bagian_lengan5' => $post['panjang_bagian_lengan5'] ?? null,
          'lengan_kanan5' => $post['lengan_kanan5'] ?? null,
          'lengan_kiri5' => $post['lengan_kiri5'] ?? null,
          'panjang_bagian_lengan6' => $post['panjang_bagian_lengan6'] ?? null,
          'lengan_kanan6' => $post['lengan_kanan6'] ?? null,
          'lengan_kiri6' => $post['lengan_kiri6'] ?? null,
          'panjang_bagian_lengan7' => $post['panjang_bagian_lengan7'] ?? null,
          'lengan_kanan7' => $post['lengan_kanan7'] ?? null,
          'lengan_kiri7' => $post['lengan_kiri7'] ?? null,
          'panjang_bagian_lengan8' => $post['panjang_bagian_lengan7'] ?? null,
          'lengan_kanan8' => $post['lengan_kanan8'] ?? null,
          'lengan_kiri8' => $post['lengan_kiri8'] ?? null,
          'keluhan_lengan' => $post['keluhan_lengan'] ?? null,
          'tanda_radang_lengan' => $post['tanda_radang_lengan'] ?? null,
          'pitting_lengan' => $post['pitting_lengan'] ?? null,
          'nyeri_lengan' => $post['nyeri_lengan'] ?? null,
          'keterangan_lengan' => $post['keterangan_lengan'] ?? null,
          'panjang_bagian_tungkai1' => $post['panjang_bagian_tungkai1'] ?? null,
          'tungkai_kanan1' => $post['tungkai_kanan1'] ?? null,
          'tungkai_kiri1' => $post['tungkai_kiri1'] ?? null,
          'panjang_bagian_tungkai2' => $post['panjang_bagian_tungkai2'] ?? null,
          'tungkai_kanan2' => $post['tungkai_kanan2'] ?? null,
          'tungkai_kiri2' => $post['tungkai_kiri2'] ?? null,
          'panjang_bagian_tungkai3' => $post['panjang_bagian_tungkai3'] ?? null,
          'tungkai_kanan3' => $post['tungkai_kanan3'] ?? null,
          'tungkai_kiri3' => $post['tungkai_kiri3'] ?? null,
          'panjang_bagian_tungkai4' => $post['panjang_bagian_tungkai4'] ?? null,
          'tungkai_kanan4' => $post['tungkai_kanan4'] ?? null,
          'tungkai_kiri4' => $post['tungkai_kiri4'] ?? null,
          'panjang_bagian_tungkai5' => $post['panjang_bagian_tungkai5'] ?? null,
          'tungkai_kanan5' => $post['tungkai_kanan5'] ?? null,
          'tungkai_kiri5' => $post['tungkai_kiri5'] ?? null,
          'panjang_bagian_tungkai6' => $post['panjang_bagian_tungkai6'] ?? null,
          'tungkai_kanan6' => $post['tungkai_kanan6'] ?? null,
          'tungkai_kiri6' => $post['tungkai_kiri6'] ?? null,
          'panjang_bagian_tungkai7' => $post['panjang_bagian_tungkai7'] ?? null,
          'tungkai_kanan7' => $post['tungkai_kanan7'] ?? null,
          'tungkai_kiri7' => $post['tungkai_kiri7'] ?? null,
          'panjang_bagian_tungkai8' => $post['panjang_bagian_tungkai8'] ?? null,
          'tungkai_kanan8' => $post['tungkai_kanan8'] ?? null,
          'tungkai_kiri8' => $post['tungkai_kiri8'] ?? null,
          'panjang_bagian_tungkai9' => $post['panjang_bagian_tungkai9'] ?? null,
          'tungkai_kanan9' => $post['tungkai_kanan9'] ?? null,
          'tungkai_kiri9' => $post['tungkai_kiri9'] ?? null,
          'panjang_bagian_tungkai10' => $post['panjang_bagian_tungkai10'] ?? null,
          'tungkai_kanan10' => $post['tungkai_kanan10'] ?? null,
          'tungkai_kiri10' => $post['tungkai_kiri10'] ?? null,
          'keluhan_tungkai' => $post['keluhan_tungkai'] ?? null,
          'tanda_radang_tungkai' => $post['tanda_radang_tungkai'] ?? null,
          'pitting_tungkai' => $post['pitting_tungkai'] ?? null,
          'nyeri_tungkai' => $post['nyeri_tungkai'] ?? null,
          'keterangan_tungkai' => $post['keterangan_tungkai'] ?? null,
          'dokter_pengirim' => $oleh,
          'status' => 1,
        ];
        // echo '<pre>';print_r($dataPL);exit();
        $this->PengukuranLimfedemaModel->simpan($dataPL);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = ['status' => 'failed'];
        } else {
          $this->db->trans_commit();
          $result = ['status' => 'success'];
        }
      } else {
        $result = ['status' => 'failed', 'errors' => $this->form_validation->error_array()];
      }
      echo json_encode($result);
    }
  }

  public function history()
  {
    $nomr = $this->input->POST('nomr');
    $data = [
      'nomr' => $nomr,
      'dataTersimpan' => $this->PengukuranLimfedema1Model->dataTersimpan($nomr) ?? null,
    ];
    $this->load->view('Pengkajian/rehabilitasiMedik/limfedema/history/index', $data);
  }

  public function bagian()
  {
    $idLimfedema = $this->input->post('id_limfedema');
    $bagian = $this->input->post('bagian');
    $data = [
      'detail' => $this->PengukuranLimfedemaModel->history($idLimfedema),
      'jumlah' => $this->PengukuranLimfedemaModel->history($idLimfedema, true),
    ];
    // echo '<pre>';print_r($data);exit();

    if ($bagian == '6076') {
      $this->load->view('Pengkajian/rehabilitasiMedik/limfedema/history/lengan', $data);
    } elseif ($bagian == '6077') {
      $this->load->view('Pengkajian/rehabilitasiMedik/limfedema/history/tungkai', $data);
    }
  }

  public function ubah()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $data = [
      'tanggal' => $post['tanggal'] ?? null,
      'panjang_bagian_lengan1' => $post['panjang_bagian_lengan1'] ?? null,
      'lengan_kanan1' => $post['lengan_kanan1'] ?? null,
      'lengan_kiri1' => $post['lengan_kiri1'] ?? null,
      'panjang_bagian_lengan2' => $post['panjang_bagian_lengan2'] ?? null,
      'lengan_kanan2' => $post['lengan_kanan2'] ?? null,
      'lengan_kiri2' => $post['lengan_kiri2'] ?? null,
      'panjang_bagian_lengan3' => $post['panjang_bagian_lengan3'] ?? null,
      'lengan_kanan3' => $post['lengan_kanan3'] ?? null,
      'lengan_kiri3' => $post['lengan_kiri3'] ?? null,
      'panjang_bagian_lengan4' => $post['panjang_bagian_lengan4'] ?? null,
      'lengan_kanan4' => $post['lengan_kanan4'] ?? null,
      'lengan_kiri4' => $post['lengan_kiri4'] ?? null,
      'panjang_bagian_lengan5' => $post['panjang_bagian_lengan5'] ?? null,
      'lengan_kanan5' => $post['lengan_kanan5'] ?? null,
      'lengan_kiri5' => $post['lengan_kiri5'] ?? null,
      'panjang_bagian_lengan6' => $post['panjang_bagian_lengan6'] ?? null,
      'lengan_kanan6' => $post['lengan_kanan6'] ?? null,
      'lengan_kiri6' => $post['lengan_kiri6'] ?? null,
      'panjang_bagian_lengan7' => $post['panjang_bagian_lengan7'] ?? null,
      'lengan_kanan7' => $post['lengan_kanan7'] ?? null,
      'lengan_kiri7' => $post['lengan_kiri7'] ?? null,
      'panjang_bagian_lengan8' => $post['panjang_bagian_lengan7'] ?? null,
      'lengan_kanan8' => $post['lengan_kanan8'] ?? null,
      'lengan_kiri8' => $post['lengan_kiri8'] ?? null,
      'keluhan_lengan' => $post['keluhan_lengan'] ?? null,
      'tanda_radang_lengan' => $post['tanda_radang_lengan'] ?? null,
      'pitting_lengan' => $post['pitting_lengan'] ?? null,
      'nyeri_lengan' => $post['nyeri_lengan'] ?? null,
      'keterangan_lengan' => $post['keterangan_lengan'] ?? null,
      'panjang_bagian_tungkai1' => $post['panjang_bagian_tungkai1'] ?? null,
      'tungkai_kanan1' => $post['tungkai_kanan1'] ?? null,
      'tungkai_kiri1' => $post['tungkai_kiri1'] ?? null,
      'panjang_bagian_tungkai2' => $post['panjang_bagian_tungkai2'] ?? null,
      'tungkai_kanan2' => $post['tungkai_kanan2'] ?? null,
      'tungkai_kiri2' => $post['tungkai_kiri2'] ?? null,
      'panjang_bagian_tungkai3' => $post['panjang_bagian_tungkai3'] ?? null,
      'tungkai_kanan3' => $post['tungkai_kanan3'] ?? null,
      'tungkai_kiri3' => $post['tungkai_kiri3'] ?? null,
      'panjang_bagian_tungkai4' => $post['panjang_bagian_tungkai4'] ?? null,
      'tungkai_kanan4' => $post['tungkai_kanan4'] ?? null,
      'tungkai_kiri4' => $post['tungkai_kiri4'] ?? null,
      'panjang_bagian_tungkai5' => $post['panjang_bagian_tungkai5'] ?? null,
      'tungkai_kanan5' => $post['tungkai_kanan5'] ?? null,
      'tungkai_kiri5' => $post['tungkai_kiri5'] ?? null,
      'panjang_bagian_tungkai6' => $post['panjang_bagian_tungkai6'] ?? null,
      'tungkai_kanan6' => $post['tungkai_kanan6'] ?? null,
      'tungkai_kiri6' => $post['tungkai_kiri6'] ?? null,
      'panjang_bagian_tungkai7' => $post['panjang_bagian_tungkai7'] ?? null,
      'tungkai_kanan7' => $post['tungkai_kanan7'] ?? null,
      'tungkai_kiri7' => $post['tungkai_kiri7'] ?? null,
      'panjang_bagian_tungkai8' => $post['panjang_bagian_tungkai8'] ?? null,
      'tungkai_kanan8' => $post['tungkai_kanan8'] ?? null,
      'tungkai_kiri8' => $post['tungkai_kiri8'] ?? null,
      'panjang_bagian_tungkai9' => $post['panjang_bagian_tungkai9'] ?? null,
      'tungkai_kanan9' => $post['tungkai_kanan9'] ?? null,
      'tungkai_kiri9' => $post['tungkai_kiri9'] ?? null,
      'panjang_bagian_tungkai10' => $post['panjang_bagian_tungkai10'] ?? null,
      'tungkai_kanan10' => $post['tungkai_kanan10'] ?? null,
      'tungkai_kiri10' => $post['tungkai_kiri10'] ?? null,
      'keluhan_tungkai' => $post['keluhan_tungkai'] ?? null,
      'tanda_radang_tungkai' => $post['tanda_radang_tungkai'] ?? null,
      'pitting_tungkai' => $post['pitting_tungkai'] ?? null,
      'nyeri_tungkai' => $post['nyeri_tungkai'] ?? null,
      'keterangan_tungkai' => $post['keterangan_tungkai'] ?? null,
      'tgl_update' => date('Y-m-d H:i:s'),
    ];
    $id = $post['id'];
    // echo '<pre>';print_r($data);exit();

    $this->PengukuranLimfedemaModel->ubah($id, $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = $post['id'];
    // echo '<pre>';print_r($id);exit();

    $data = ['status' => 0,];
    $this->PengukuranLimfedemaModel->ubah($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = ['status' => 'failed'];
    } else {
      $this->db->trans_commit();
      $result = ['status' => 'success'];
    }
    echo json_encode($result);
  }

  public function detail()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $id = $post['id'];
    $nomr = $post['nomr'];
    $detail = $this->PengukuranLimfedemaModel->detail($id);
    $data = [
      'urutan' => $post['urutan'],
      'detail' => $detail,
      'detailSisi' => json_decode($detail['sisi']),
      'dataTersimpan' => $this->PengukuranLimfedema1Model->dataTersimpan($nomr),
      'karnofskyIndex' => $this->PengukuranLimfedemaModel->karnofskyKategori(),
      'bagian' => $this->masterModel->referensi(1806),
      'sisi' => $this->masterModel->referensi(1807),
      'tandaRadang' => $this->masterModel->referensi(1804),
      'pitting' => $this->masterModel->referensi(1805),
      'nyeri' => $this->masterModel->referensi(729),
    ];
    // echo '<pre>';print_r($data);exit();

    $this->load->view('Pengkajian/rehabilitasiMedik/limfedema/history/detail', $data);
  }
}

/* End of file PengukuranLimfedema.php */
/* Location: ./application/controllers/rehabilitasiMedik/PengukuranLimfedema.php */