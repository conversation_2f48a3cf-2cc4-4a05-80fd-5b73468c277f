<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AsesmenRestrain extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/AsesmenRestrainModel'));
  }

  public function index()
  {
    $norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->AsesmenRestrainModel->getNomrRawatInap($nopen);
    $getIdEmr = $getNomr['ID_EMR_ASESMEN_RESTRAIN'];
    $getPengkajian = $this->AsesmenRestrainModel->getPengkajian($getIdEmr);
    $data = array(
      'nopen' => $nopen,
      'nokun' => $nokun,
      'norm' => $norm,
      'getPengkajian' => $getPengkajian,
      'pasien' => $getNomr,
      'listPenggunaanRestrain' => $this->masterModel->referensi(1196),
      'listInformed' => $this->masterModel->referensi(1197),
      'listJenisRestrain' => $this->masterModel->referensi(1198),
      'listBerlakuRestrain' => $this->masterModel->referensi(1199),
      'listMaskep' => $this->masterModel->referensi(148),
      'listRestrainDihentikan' => $this->masterModel->referensi(1200),
      'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
      'listDr' => $this->masterModel->listDr()
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/asesmenRestrain', $data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();
        $idRefEmr = $this->input->post('idemr');

        $dataKeperawatan = array(
          'id_emr' => $getIdEmr,
          'nopen' => $post['nopen'],
          'nokun' => $post['nokun'],
          'jenis' => 11,
          'diagnosa_masuk' => $post['rujukanRiAR'],
          'created_by' => $this->session->userdata('id'),
          'flag' => '1',
        );

        // echo "<pre>data keperawatan ";print_r($dataKeperawatan);echo "</pre>";

        $dataAsesmenRestrain = array(
          'id_emr' => $getIdEmr,
          'alasan_restrain' => isset($post['alasan_restrain']) ? json_encode($post['alasan_restrain']) : "",
          'alasan_restrain_lain' => isset($post['alasan_restrain_lain']) ? $post['alasan_restrain_lain'] : "",
          'informed_consent' => isset($post['informed_consent']) ? $post['informed_consent'] : "",
          'jenis_restrain' => isset($post['jenis_restrain']) ? json_encode($post['jenis_restrain']) : "",
          'jenis_restrain_lainnya' => isset($post['jenis_restrain_lainnya']) ? $post['jenis_restrain_lainnya'] : "",
          'berlaku_restrain' => isset($post['berlaku_restrain']) ? $post['berlaku_restrain'] : "",
          'sebutkan_berlaku_restrain' => isset($post['sebutkan_berlaku_restrain']) ? $post['sebutkan_berlaku_restrain'] : "",
          'masalah_keperawatan' => isset($post['masalah_keperawatan']) ? json_encode($post['masalah_keperawatan']) : "",
          'sebutkan_masalah_keperawatan' => isset($post['sebutkan_masalah_keperawatan']) ? $post['sebutkan_masalah_keperawatan'] : "",
          'restrain_dihentikan' => isset($post['restrain_dihentikan']) ? json_encode($post['restrain_dihentikan']) : ""
        );

        // echo "<pre>data riwayat kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $this->db->trans_begin();
        
        if (!empty($post['idemr'])) {
          $this->db->replace('keperawatan.tb_asesmen_restrain', $dataAsesmenRestrain);
          $this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan);

          $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $idRefEmr));
          $dataAsuhanKeperawatan = array();
          $index = 0;
          $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
          if (isset($post['asuhanKeperawatan'])) {
            foreach ($post['asuhanKeperawatan'] as $input) {
              if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                  $dataAsuhanKeperawatan, array(
                    'id_emr' => $getIdEmr,
                    'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                    'lain_lain' => isset($post[$id]) ? $post[$id] : null
                  )
                );
              }
              $index++;
            }
            $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success_ubah');
          }
  
          echo json_encode($result);
        }else{
          
            $this->db->insert('keperawatan.tb_asesmen_restrain', $dataAsesmenRestrain);
            $this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan);

            $dataAsuhanKeperawatan = array();
            $index = 0;
            $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
            if (isset($post['asuhanKeperawatan'])) {
              foreach ($post['asuhanKeperawatan'] as $input) {
                if ($post['asuhanKeperawatan'][$index] != "") {
                  $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                  array_push(
                    $dataAsuhanKeperawatan, array(
                      'id_emr' => $getIdEmr,
                      'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                      'lain_lain' => isset($post[$id]) ? $post[$id] : null
                    )
                  );
                }
                $index++;
              }
              $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
            }

            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
        }
        // echo "<pre>data kesadaran ";print_r($dataKesedaran);echo "</pre>";
      }
      else if($param == 'count'){
        $result = $this->AsesmenRestrainModel->get_count();;
        echo json_encode($result);
      }
    }
  }

  
  public function asuhanKeperawatan_edit()
  {
      $id = $this->input->post('id');
      $idemr = $this->input->post('idemr');

      $resultAsuhanKeperawatan = $this->masterModel->asuhanKeperawatan($id);
      $resultAsuhanKeperawatanDetil = $this->masterModel->asuhanKeperawatanDetil($resultAsuhanKeperawatan->ID);
      $getPengkajian = $this->AsesmenRestrainModel->getPengkajian($idemr);

      $data = array(
          'titleAsuhanKeperawatan' => $resultAsuhanKeperawatan->DESKRIPSI,
          'DataAsuhanKeperawatan' => $resultAsuhanKeperawatanDetil,
          'getPengkajian' => $getPengkajian,
      );

      $this->load->view('Pengkajian/emr/asuhanKeperawatan/asuhanKeperawatan_edit', $data);
  }

    public function datatables(){
      
      $result = $this->AsesmenRestrainModel->historyPengkajian();

      $data = array();
      foreach ($result as $row){
        $tombolCetak = '<a class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_MEDIS.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Medis</span></a>';
        $tombolCetak .= '<a href="/reports/simrskd/Rawatinap/PengkajianRIeperawatanDewasa.php?format=pdf&idEmr='.$row -> ID_EMR_PERAWAT.'" target="_blank" class="btn btn-warning btn-block btn-sm" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-print" style="color:black;"></i> <span style="color:black;">Cetak Perawat</span></a>';
        $action = "";
        $verif = "";
        $userLogin = $this->session->userdata('status');
        $jenisPengkajian = $row -> JENIS_PENGKAJIAN_KEPERAWATAN;

        // KONDISI UNTUK ADA PENGKAJIAN PERAWAT
        if($row -> ID_EMR_PERAWAT != null){
          if($userLogin == 2){
            if($row -> STATUS_VERIFIKASI == 0){
              $verif = $row -> INFO_VERIFIKASI;
            }elseif($row -> STATUS_VERIFIKASI == 1){
              $verif = $row -> INFO_VERIFIKASI;
            }

            if($jenisPengkajian == 5){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
            }elseif($jenisPengkajian == 6){
              $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
            }elseif($jenisPengkajian == 7){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
            }elseif($jenisPengkajian == 11){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
              $action .= '<a href="#lihatPemantauanAsesmenRestrain" class="btn btn-primary btn-block btn-sm showPemantauanAsesmenRestrain" data-toggle="modal" data-idpemasres="'.$row -> ID_EMR_PERAWAT.'" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> Pemantauan Restrain</a>';
            }else{
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
            }
          }elseif($userLogin == 1){
            if($row -> STATUS_VERIFIKASI == 0){
              $verif = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'">Verif</button>';
            }elseif($row -> STATUS_VERIFIKASI == 1){
              $verif = $row -> INFO_VERIFIKASI;
            }

            if($jenisPengkajian == 5){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm  verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan</button>';
            }elseif($jenisPengkajian == 6){
              $action = '<a href="" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Anak</a>';
            }elseif($jenisPengkajian == 7){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianRiRemaja" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Keperawatan Remaja</button>';
            }elseif($jenisPengkajian == 11){
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editAsesmenRestrain" data-id="'.$row -> ID_EMR_PERAWAT.'"><i class="fa fa-eye"></i> View Asesmen Restrain</button>';
            }
            else{
              $action = '<button type="button" class="btn btn-primary btn-block btn-sm editPengkajianPerawat" data-id="'.$row -> ID_EMR_PERAWAT.'" disabled><i class="fa fa-eye"></i> View Keperawatan Rawat Jalan</button>';
            }
          }

        }

        // KONDISI UNTUK ADA PENGKAJIAN MEDIS
        if($row -> ID_EMR_MEDIS != null){
          if($userLogin == 1){
            if($row -> STATUS_VERIFIKASI == 0){
              if($row -> ID_EMR_PERAWAT != null){
                $verif = '<button type="button" class="btn btn-primary btn-block btn-sm verif-perawat" data-id="'.$row -> ID_EMR_PERAWAT.'">Verif</button>';
              }else{
                $verif = $row -> INFO_VERIFIKASI;
              }
            }elseif($row -> STATUS_VERIFIKASI == 1){
              $verif = '<h4 style="text-align: center; vertical-align: middle;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
            }

            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="1"><i class="fa fa-eye"></i>  View Medis</button>';
          }elseif($userLogin == 2){
            $action .= '<button type="button" class="btn btn-purple btn-block btn-sm editPengkajianRIMedisDewasa" data-id="'.$row -> NOPEN.'" data-status="2"><i class="fa fa-eye"></i>  View Medis</button>';
          }
        }

          $sub_array = array();
          $sub_array[] = $row -> INFO;
          $sub_array[] = $verif;
          $sub_array[] = $row -> RUANGAN;
          $sub_array[] = $row -> TANGGAL_KUNJUNGAN;
          $sub_array[] = $row -> DPJP;
          $sub_array[] = $action;
          $sub_array[] = $tombolCetak;
          $sub_array[] = $row -> USER_MEDIS;
          $sub_array[] = $row -> USER_PERAWAT;   
          $data[] = $sub_array;
      }

      $output = array(
          "draw" => intval($this->input->post("draw")),
          "data"              => $data
      );
      echo json_encode($output);
    }

    
  public function pemantauanAssesmenRestrain()
  {
    $idemr = $this->input->post('id');
    $historyPemantauanAsesmenRestrain = $this->AsesmenRestrainModel->historyPemantauanAsesmenRestrain($idemr);

    $norm = $this->uri->segment(7);
    $nopen = $this->uri->segment(8);
    $nokun = $this->uri->segment(9);
    $getNomr = $this->AsesmenRestrainModel->getNomrRawatInap($nopen);
    $getIdEmr = $getNomr['ID_EMR_ASESMEN_RESTRAIN'];
    $getPengkajian = $this->AsesmenRestrainModel->getPengkajian($getIdEmr);

    
    $data = array(
      "idemr" => $idemr,
      'listAlatPenghalang' => $this->masterModel->referensi(1201),
      'listJalanNapas' => $this->masterModel->referensi(1210),
      'listKondisiKulit' => $this->masterModel->referensi(1203),
      'listCRT' => $this->masterModel->referensi(1211),
      'getPengkajian' => $getPengkajian,
      'historyPemantauanAsesmenRestrain' => $historyPemantauanAsesmenRestrain
    );

    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/pemantauanAsesmenRestrain',$data);    
  }

  public function action_pemantauan($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $id_emr = $this->input->post('idemr');
        $id_pemantauan = $this->input->post('id_pemantauan');

        $dataPemantauan = array(
          'id' => isset($post['id_pemantauan']) ? $post['id_pemantauan'] : "",
          'id_emr' => $id_emr,
          'tanggal' => isset($post['tanggal']) ? date('Y-m-d', strtotime($post['tanggal']))  : "",
          'pukul' => isset($post['pukul']) ? $post['pukul'] : "",
          'jenis_restrain' => isset($post['jenis_restrain']) ? $post['jenis_restrain'] : "",
          'alat_penghalang' => isset($post['alat_penghalang']) ? json_encode($post['alat_penghalang']) : "",
          'jalan_napas' => isset($post['jalan_napas']) ? $post['jalan_napas'] : "",
          'kondisi_kulit' => isset($post['kondisi_kulit']) ? $post['kondisi_kulit'] : "",
          'crt' => isset($post['crt']) ? $post['crt'] : "",
          'oleh' =>  $this->session->userdata('id')
        );

        // echo "<pre>data riwayat kesehatan ";print_r($dataRiwayatKesehatan);echo "</pre>";

        $this->db->trans_begin();
        
        if (!empty($post['id_pemantauan'])) {
          $this->db->replace('keperawatan.tb_asesmen_restrain_pemantauan', $dataPemantauan);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success_ubah');
          }
  
          echo json_encode($result);
        }else{
            $this->db->insert('keperawatan.tb_asesmen_restrain_pemantauan', $dataPemantauan);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
        }
        //  echo "<pre>data Pemantauan ";print_r($dataPemantauan);echo "</pre>";
      }
      else if($param == 'count'){
        $result = $this->AsesmenRestrainModel->get_count();;
        echo json_encode($result);
      }
    }
  }

  public function lihatHistoryPemantauanAsesmenRestrain()
  {
    $id_pemantauan = $this->input->post('id');
    $getPengkajian = $this->AsesmenRestrainModel->getPengkajian_Pemantauan($id_pemantauan);

    $data = array(
      'id' => $id_pemantauan,
      'listAlatPenghalang' => $this->masterModel->referensi(1201),
      'listJalanNapas' => $this->masterModel->referensi(1210),
      'listKondisiKulit' => $this->masterModel->referensi(1203),
      'listCRT' => $this->masterModel->referensi(1211),
      'getPengkajian' => $getPengkajian
    );
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/viewEditPemantauanAsesmenRestrain', $data); 
  }
}