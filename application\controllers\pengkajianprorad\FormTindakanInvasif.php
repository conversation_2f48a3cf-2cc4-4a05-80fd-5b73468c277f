<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormTindakanInvasif extends CI_Controller {

  public function __construct(){
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
  }

  if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
  }

  date_default_timezone_set("Asia/Bangkok");
  $this->load->model(array('masterModel', 'pengkajianAwalModel'));
}

public function index()
{
  $nomr = $this->uri->segment(4);
  $nokun = $this->uri->segment(6);
  $id_tindakaninvasif = $this->uri->segment(7);
  $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
  $anamnesis = $this->masterModel->referensi(54);
  $riwayatpenggunaanobat = $this->masterModel->referensi(288);
  $riwayatAlergi = $this->masterModel->referensi(2);
  $performanceStatus = $this->masterModel->referensi(30);
  $kunjungan_pk = $this->pengkajianAwalModel->kunjungan_pk($nomr);
  $sitologi = $this->pengkajianAwalModel->sitologi($nomr);
  $histologi = $this->pengkajianAwalModel->histologi($nomr);
  $tindakan_rad = $this->pengkajianAwalModel->tindakan_rad($nomr);
  $formAsuhanKeperawatan = $this->masterModel->referensi(148);
  $sisiTubuh = $this->masterModel->referensi(49);
  $stadium = $this->masterModel->stadium();
  $persiapandarah = $this->masterModel->referensi(324);
  $persiapanalatkhusus = $this->masterModel->referensi(325);
  $diet = $this->masterModel->referensi(56);
  $jenisDiet = $this->masterModel->referensi(57);
  $kesadaran = $this->masterModel->referensi(5);
  $skriningNyeri = $this->masterModel->referensi(7);
  $skriningResikoJatuhPusing = $this->masterModel->referensi(120);
  $skriningResikoJatuhBerdiri = $this->masterModel->referensi(121);
  $skriningResikoJatuh6Bulan = $this->masterModel->referensi(122);
  $alatBantu = $this->masterModel->referensi(19);
  $pendidikan = $this->masterModel->referensi(24);
  $bahasaSehari = $this->masterModel->referensi(25);
  $perluPenerjemah = $this->masterModel->referensi(26);
  $kesediaanInformasi = $this->masterModel->referensi(27);
  $hambatan = $this->masterModel->referensi(28);
  $kebutuhanPembelajaran = $this->masterModel->referensi(29);
  $skalaNyeriNRS = $this->masterModel->referensi(114);
  $skalaNyeriWBR = $this->masterModel->referensi(115);
  $skalaNyeriFLACC = $this->masterModel->referensi(123);
  $skalaNyeriBPS = $this->masterModel->referensi(133);
  $efeksampingNRS = $this->masterModel->referensi(118);
  $efeksampingWBR = $this->masterModel->referensi(119);
  $efeksampingFLACC = $this->masterModel->referensi(131);
  $efeksampingBPS = $this->masterModel->referensi(134);
  $statusnyeriNRS = $this->masterModel->referensi(136);
  $statusnyeriWBR = $this->masterModel->referensi(136);
  $statusnyeriFLACC = $this->masterModel->referensi(136);
  $statusnyeriBPS = $this->masterModel->referensi(136);
  $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
  $pengkajianNyeriQuality = $this->masterModel->referensi(9);
  $pengkajianNyeriTime = $this->masterModel->referensi(12);

  $getIDEMR_InvasifKP = "";
  $hPengkajianTindakanInvasif = $this->pengkajianAwalModel->historyPengkajianTindakanInvasif($nomr);
  if ($id_tindakaninvasif != "" && $getNomr['ID_RUANGAN'] == 105060101 || $id_tindakaninvasif != "" && $getNomr['ID_RUANGAN'] == 105100101) {
    $getPengkajianTindakanInvasif = $this->pengkajianAwalModel->getPengkajianTindakanInvasif($id_tindakaninvasif);
    $getIDEMR_InvasifKP = $this->pengkajianAwalModel->getIDEMR_InvasifKP($id_tindakaninvasif);
  }
  
  $getIDEMR_Invasif = "";
  $hPengkajianTindakanInvasifMedis = $this->pengkajianAwalModel->historyPengkajianTindakanInvasifMedisNew($nomr);
  if ($this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105060101 || $this->uri->segment(7) != "" && $getNomr['ID_RUANGAN'] == 105100101) {
    $id_tindakaninvasifmedis = $this->uri->segment(7);
    $getPengkajianTindakanInvasifMedis = $this->pengkajianAwalModel->getPengkajianTindakanInvasifMedis($id_tindakaninvasifmedis);
    $getIDEMR_Invasif = $this->pengkajianAwalModel->getIDEMR_Invasif($id_tindakaninvasifmedis);
  }


  $data = array(
      'getNomr' => $getNomr,
      'anamnesis' => $anamnesis,
      'pendidikan' => $pendidikan,
      'riwayatpenggunaanobat' => $riwayatpenggunaanobat,
      'riwayatAlergi' => $riwayatAlergi,
      'getPengkajianTindakanInvasif' => $id_tindakaninvasif != "" ? $getPengkajianTindakanInvasif : "",
      'getIDEMR_InvasifKP' => $id_tindakaninvasif != "" ? $getIDEMR_InvasifKP : "",
      'performanceStatus' => $performanceStatus,
      'kunjungan_pk' => $kunjungan_pk,
      'hPengkajianTindakanInvasif' => $hPengkajianTindakanInvasif,
      'sitologi' => $sitologi,
      'histologi' => $histologi,
      'kesadaran' => $kesadaran,
      'skriningNyeri' => $skriningNyeri,
      'perluPenerjemah' => $perluPenerjemah,
      'kebutuhanPembelajaran' => $kebutuhanPembelajaran,
      'tindakan_rad' => $tindakan_rad,
      'skriningResikoJatuhPusing' => $skriningResikoJatuhPusing,
      'sisiTubuh' => $sisiTubuh,
      'kesediaanInformasi' => $kesediaanInformasi,
      'bahasaSehari' => $bahasaSehari,
      'skriningResikoJatuh6Bulan' => $skriningResikoJatuh6Bulan,
      'skriningResikoJatuhBerdiri' => $skriningResikoJatuhBerdiri,
      'formAsuhanKeperawatan' => $formAsuhanKeperawatan,
      'stadium' => $stadium,
      'hambatan' => $hambatan,
      'persiapandarah' => $persiapandarah,
      'persiapanalatkhusus' => $persiapanalatkhusus,
      'diet' => $diet,
      'alatBantu' => $alatBantu,
      'jenisDiet' => $jenisDiet,
      'skalaNyeriNRS' => $skalaNyeriNRS,
      'skalaNyeriWBR' => $skalaNyeriWBR,
      'skalaNyeriFLACC' => $skalaNyeriFLACC,
      'skalaNyeriBPS' => $skalaNyeriBPS,
      'efeksampingNRS' => $efeksampingNRS,
      'efeksampingWBR' => $efeksampingWBR,
      'efeksampingFLACC' => $efeksampingFLACC,
      'efeksampingBPS' => $efeksampingBPS,
      'statusnyeriNRS' => $statusnyeriNRS,
      'statusnyeriWBR' => $statusnyeriWBR,
      'statusnyeriFLACC' => $statusnyeriFLACC,
      'statusnyeriBPS' => $statusnyeriBPS,
      'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
      'pengkajianNyeriQuality' => $pengkajianNyeriQuality,
      'pengkajianNyeriTime' => $pengkajianNyeriTime,
      'hPengkajianTindakanInvasifMedis' => $hPengkajianTindakanInvasifMedis,
      'getPengkajianTindakanInvasifMedis' => $getPengkajianTindakanInvasifMedis,
    );

    // print_r($data);exit();
    $this->load->view('Pengkajian/pengkajianProRad/tindakaninvasif', $data);
}

public function action_keperawatan($param)
{
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        if ($param == 'tambah' || $param == 'ubah') {
            $post = $this->input->post();

            $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();

            $dataAnamnesisKeperawatan = array(
                'id_emr' => $getIdEmr,
                'id_auto_allo' => isset($post['anamnesis']) ? $post['anamnesis'] : "",
                'allo_nama' => isset($post['nama']) ? $post['nama'] : "",
                'hubungan_dengan_pasien' => isset($post['hubunganDenganPasien']) ? $post['hubunganDenganPasien'] : "",
            );

            $dataKeperawatan = array(
                'id_emr' => $getIdEmr,
                'nopen' => $post['nopen'],
                'nokun' => $post['nokun'],
                'created_by' => $this->session->userdata('id'),
                'flag' => '1',
                'jenis' => '2',
            );

            $dataRiwayatKesehatan = array(
                'id_emr' => $getIdEmr,
                'riwayat_kanker' => isset($post['riwayat_kanker_keluarga']) ? $post['riwayat_kanker_keluarga'] : "",
                'isi_kanker' => isset($post['riwayat_kanker_keluarga_desk']) ? json_encode($post['riwayat_kanker_keluarga_desk']) : "",

                'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
                'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
                'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : "",

                'riwayat_transfusi' => isset($post['riwayat_transfusi_darah']) ? $post['riwayat_transfusi_darah'] : "",
                'reaksi_transfusi' => isset($post['riwayat_transfusi_darah_desk']) ? $post['riwayat_transfusi_darah_desk'] : "",
                'isi_reaksi_transfusi' => isset($post['riwayat_td_reaksi_alergi']) ? json_encode($post['riwayat_td_reaksi_alergi']) : "",

                'kebiasaan' => isset($post['kebiasaan_merokok']) ? $post['kebiasaan_merokok'] : "",
                'isi_kebiasaan' => isset($post['kebiasaan_merokok_desk']) ? json_encode($post['kebiasaan_merokok_desk']) : "",

                'riwayat_metabolik' => isset($post['riwayat_metabolik_keluarga']) ? $post['riwayat_metabolik_keluarga'] : "",
                'isi_metabolik' => isset($post['riwayat_metabolik_keluarga_desk']) ? json_encode($post['riwayat_metabolik_keluarga_desk']) : "",

                'deteksidini' => isset($post['riwayat_deteksi_dini_kanker']) ? $post['riwayat_deteksi_dini_kanker'] : "",
                'isi_deteksidini' => isset($post['riwayat_deteksi_dini_kanker_desk']) ? json_encode($post['riwayat_deteksi_dini_kanker_desk']) : "",

                    // Radioterapi
                'riwayat_penyakit_keluarga' => isset($post['riwayat_penyakit_dalam_keluarga']) ? $post['riwayat_penyakit_dalam_keluarga'] : "",
                'penyakit_keluarga_lain' => isset($post['riwayat_penyakit_dalam_keluarga_desk']) ? json_encode($post['riwayat_penyakit_dalam_keluarga_desk']) : "",
                'riwayat_cabut_gigi' => isset($post['riwayat_cabut_gigi']) ? json_encode($post['riwayat_cabut_gigi']) : "",

                    //Prosedure
                'riwayat_pengobatan' => isset($post['riwayat_pengobatan']) ? json_encode($post['riwayat_pengobatan']) : "",

                    // Anyelir
                'riwayat_ekstravasasi' => isset($post['riwayat_ekstravasasi']) ? $post['riwayat_ekstravasasi'] : "",
                'isi_ekstravasasi' => isset($post['riwayat_ekstravasasi_desc']) ? $post['riwayat_ekstravasasi_desc'] : "",
                'pilih_ekstravasasi_foto' => isset($post['riwayat_ekstravasasi_foto']) ? $post['riwayat_ekstravasasi_foto'] : "",
                'ekstravasasi_depan' => isset($post['ekstravasasi_depan']) ? $post['ekstravasasi_depan'] : "",
                'ekstravasasi_belakang' => isset($post['ekstravasasi_belakang']) ? $post['ekstravasasi_belakang'] : "",
                'hasil_laboratorium' => isset($post['hasil_laboratorium']) ? $post['hasil_laboratorium'] : "",
                'isi_laboratorium' => isset($post['hasil_laboratorium_desk']) ? json_encode($post['hasil_laboratorium_desk']) : "",
                'hasil_BMP' => isset($post['hasil_bmp_terakhir']) ? $post['hasil_bmp_terakhir'] : "",
                'isi_BMP' => isset($post['hasil_bmp_terakhir_desk']) ? json_encode($post['hasil_bmp_terakhir_desk']) : "",
                'kemoterapi' => isset($post['hasil_kemoterapi']) ? $post['hasil_kemoterapi'] : "",
                'isi_kemoterapi' => isset($post['kemoterapi_terdahulu_desk']) ? json_encode($post['kemoterapi_terdahulu_desk']) : "",
                'tindakan_perawatan' => isset($post['tindakan_perawatan_terakhir']) ? $post['tindakan_perawatan_terakhir'] : "",
                'perawatan_lain' => isset($post['tindakan_perawatan_terakhir_desk']) ? json_encode($post['tindakan_perawatan_terakhir_desk']) : "",
                'riwayat_graft' => isset($post['riwayat_gvhd']) ? $post['riwayat_gvhd'] : "",
            );

$dataPemeriksaanKesehatan = array(
    'id_emr' => $getIdEmr,
    'keluhan_pasien' => isset($post['keluhan_pasien']) ? $post['keluhan_pasien'] : "",
    'sedang_hamil' => isset($post['pasien_hamil_rad']) ? $post['pasien_hamil_rad'] : "",
    'hamil_jelaskan' => isset($post['pasienHamilLainnya_rad']) ? $post['pasienHamilLainnya_rad'] : "",
    'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : "",
    'tekanan_darah' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
    'per_tekanan_darah' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
    'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
    'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
    'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
    'nyeri' => isset($post['skrining_nyeri']) ? $post['skrining_nyeri'] : "",
    'provocative' => isset($post['propocative']) ? $post['propocative'] : "",
    'quality' => isset($post['quality']) ? $post['quality'] : "",
    'sebutkan_quality' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : "",
    'isi_regio' => isset($post['regio']) ? $post['regio'] : "",
    'severity' => isset($post['severity']) ? $post['severity'] : "",
    'time' => isset($post['time']) ? $post['time'] : "",
    'isi_time' => isset($post['durasi_nyeri']) ? $post['durasi_nyeri'] : "",
    'psikologis' => isset($post['psikologis']) ? $post['psikologis'] : "",
    'isi_psikologi' => isset($post['psikologis_lainnya']) ? $post['psikologis_lainnya'] : "",
    'hub_keluarga' => isset($post['sdeh']) ? $post['sdeh'] : "",
    'nafkah_utama' => isset($post['sdepn']) ? $post['sdepn'] : "",
    'tinggal' => isset($post['sdets']) ? $post['sdets'] : "",
    'keyakinan' => isset($post['spiritualkultural']) ? $post['spiritualkultural'] : "",
    'sebutkan_keyakinan' => isset($post['spiritualkultural_lainnya']) ? $post['spiritualkultural_lainnya'] : "",
    'pengobatan_alternatif' => isset($post['pengobatan_alternatif']) ? $post['pengobatan_alternatif'] : "",
    'sebutkan_pengobatan_alternatif' => isset($post['pengobatan_alternatif_desk']) ? json_encode($post['pengobatan_alternatif_desk']) : "",
    'pengobatan_bertentangan' => isset($post['pengobatan_bertentangan']) ? $post['pengobatan_bertentangan'] : "",
    'sebutkan_pengobatan_bertentangan' => isset($post['pengobatan_bertentangan_desk']) ? json_encode($post['pengobatan_bertentangan_desk']) : "",
    'status_fungsionl' => isset($post['statusfungsional']) ? $post['statusfungsional'] : "",
    'vertigo' => isset($post['skriningresikojatuhpusing']) ? $post['skriningresikojatuhpusing'] : "",
    'sulit_berdiri' => isset($post['skriningresikojatuhberdiri']) ? $post['skriningresikojatuhberdiri'] : "",
    'jatuh_dlm_6' => isset($post['skriningresikojatuh6bulan']) ? $post['skriningresikojatuh6bulan'] : "",
    'tb' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
    'bb' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
    'penurunan_bb' => isset($post['komponenpenilaian']) ? $post['komponenpenilaian'] : "",
    'total_penurunan_bb' => isset($post['totalpenurunanbb']) ? $post['totalpenurunanbb'] : "",
    'asupan_berkurang' => isset($post['komponenpenilaianasupan']) ? $post['komponenpenilaianasupan'] : "",
    'total_asupan_berkurang' => isset($post['totalpenurunanasupan']) ? $post['totalpenurunanasupan'] : "",
    'total_komponen_penilaian' => isset($post['total_skor_komponen_penilaian']) ? $post['total_skor_komponen_penilaian'] : "",

                    // Anyelir
    'skala_nyeri' => isset($post['skala_nyeri']) ? $post['skala_nyeri'] : "",
    'skala_lelah' => isset($post['skala_lelah']) ? $post['skala_lelah'] : "",
    'skala_mual' => isset($post['skala_mual']) ? $post['skala_mual'] : "",
    'skala_depresi' => isset($post['skala_depresi']) ? $post['skala_depresi'] : "",
    'skala_cemas' => isset($post['skala_cemas']) ? $post['skala_cemas'] : "",
    'skala_mengantuk' => isset($post['skala_mengantuk']) ? $post['skala_mengantuk'] : "",
    'skala_nafsu_makan' => isset($post['skala_nafsuMakan']) ? $post['skala_nafsuMakan'] : "",
    'skala_sehat' => isset($post['skala_sehat']) ? $post['skala_sehat'] : "",
    'skala_sesak_napas' => isset($post['skala_sesakNapas']) ? $post['skala_sesakNapas'] : "",
    'skala_masalah' => isset($post['skala_masalah']) ? $post['skala_masalah'] : "",

                    //Anak
    'lingkar_kepala' => isset($post['lingkarKepala']) ? $post['lingkarKepala'] : "",
    'bentuk_kepala' => isset($post['bentuk_kepala_anak']) ? $post['bentuk_kepala_anak'] : "",
    'isi_kelainan_kepala' => isset($post['bentuk_kepala_anak_desk']) ? json_encode($post['bentuk_kepala_anak_desk']) : "",
    'irama' => isset($post['irama_pernapasan']) ? $post['irama_pernapasan'] : "",
    'reaksi_dada' => isset($post['retraksi_dada']) ? $post['retraksi_dada'] : "",
    'alat_bantu_napas' => isset($post['alat_bantu_napas_anak']) ? $post['alat_bantu_napas_anak'] : "",
    'bantu_napas_lain' => isset($post['alat_bantu_napas_desk']) ? json_encode($post['alat_bantu_napas_desk']) : "",
    'sianosis' => isset($post['sianosis']) ? $post['sianosis'] : "",
    'pucat' => isset($post['pucat']) ? $post['pucat'] : "",
                    // 'pembelajaran_pasien' => isset($post['kebutuhanPembelajaranRad']) ? json_encode($post['kebutuhanPembelajaranRad']) : "",
    'capillary_refill_test' => isset($post['capillary_refill_test']) ? $post['capillary_refill_test'] : "",

    'akral' => isset($post['akral']) ? $post['akral'] : "",
    'pembesaran_kelenjar' => isset($post['pembesaran_kelenjar_getah_bening_anak']) ? $post['pembesaran_kelenjar_getah_bening_anak'] : "",
    'isi_pembesar_kelenjar' => isset($post['pembesaran_kelenjar_getah_bening_anak_desk']) ? json_encode($post['pembesaran_kelenjar_getah_bening_anak_desk']) : "",
    'gangguan_neurologi' => isset($post['gangguan_neurologi']) ? $post['gangguan_neurologi'] : "",
    'mata' => isset($post['neurologi_mata_anak']) ? $post['neurologi_mata_anak'] : "",
    'isi_abnormal' => isset($post['neurologi_mata_anak_desk']) ? json_encode($post['neurologi_mata_anak_desk']) : "",
    'mulut' => isset($post['mulut_anak']) ? $post['mulut_anak'] : "",
    'isi_mulut' => isset($post['mulut_anak_desk']) ? json_encode($post['mulut_anak_desk']) : "",
    'abdomen' => isset($post['abdomen_anak']) ? $post['abdomen_anak'] : "",
    'isi_abdomen' => isset($post['abdomen_anak_desk']) ? json_encode($post['abdomen_anak_desk']) : "",

    'asites' => isset($post['asites_anak']) ? $post['asites_anak'] : "",
    'isi_asites' => isset($post['asites_anak_desk']) ? json_encode($post['asites_anak_desk']) : "",
    'defekasi' => isset($post['defekasi_anak']) ? $post['defekasi_anak'] : "",
                    // 'isi_defekasi' => isset($post['skala_depresi']) ? $post['skala_depresi'] : "",
    'fases' => isset($post['karakteristik_feses_anak']) ? $post['karakteristik_feses_anak'] : "",
    'fases_lain' => isset($post['karakteristik_feses_anak_desk']) ? json_encode($post['karakteristik_feses_anak_desk']) : "",
    'urin' => isset($post['urin']) ? $post['urin'] : "",
    'rectal' => isset($post['rectal_anak']) ? $post['rectal_anak'] : "",
    'isi_rectal' => isset($post['rectal_anak_desk']) ? json_encode($post['rectal_anak_desk']) : "",
    'genetalia' => isset($post['genetalia_anak']) ? $post['genetalia_anak'] : "",

    'isi_genetalia' => isset($post['genetalia_anak_desk']) ? json_encode($post['genetalia_anak_desk']) : "",
    'kulit' => isset($post['kulit']) ? $post['kulit'] : "",
    'warna_kulit' => isset($post['warna_kulit']) ? $post['warna_kulit'] : "",
    'luka' => isset($post['luka']) ? $post['luka'] : "",
    'lokasi_luka' => isset($post['lokasi_luka']) ? $post['lokasi_luka'] : "",
    'kelainan_tulang' => isset($post['kelainan_tulang_anak']) ? $post['kelainan_tulang_anak'] : "",
    'isi_kelainan_tulang' => isset($post['kelainan_tulang_anak_desk']) ? json_encode($post['kelainan_tulang_anak_desk']) : "",
    'gerakan_anak' => isset($post['gerakan_anak']) ? $post['gerakan_anak'] : "",
    'isi_gerakan_anak' => isset($post['gerakan_anak_desk']) ? json_encode($post['gerakan_anak_desk']) : "",
    'status_mental' => isset($post['status_mental']) ? json_encode($post['status_mental']) : "",

    'tempat_tinggal' => isset($post['tempat_tinggal']) ? $post['tempat_tinggal'] : "",
    'isi_tempat_tinggal' => isset($post['tempat_tinggal_desk']) ? json_encode($post['tempat_tinggal_desk']) : "",
    'pengasuh' => isset($post['pengasuh']) ? $post['pengasuh'] : "",
    'jenis_sekolah' => isset($post['jenis_sekolah']) ? $post['jenis_sekolah'] : "",
    'tampak_kurus' => isset($post['strongKidsKurus']) ? $post['strongKidsKurus'] : "",
    'penurunan_bb_anak' => isset($post['strongKidsTurunBerat']) ? $post['strongKidsTurunBerat'] : "",
    'kondisi' => isset($post['strongKidsKondisi']) ? $post['strongKidsKondisi'] : "",
    'resiko_malnutrisi' => isset($post['strongKidsMalnutrisi']) ? $post['strongKidsMalnutrisi'] : "",
);

$dataPerencanaanTindakan = array(
    'id_emr' => $getIdEmr,
    'jenis_pemeriksaan' => isset($post['jenis_pemeriksaan_rad']) ? $post['jenis_pemeriksaan_rad'] : "",
    'jenis_tindakan' => isset($post['tindakanRad']) ? $post['tindakanRad'] : "",
    'ber_obat_kontras' => isset($post['pemberiObatKontrasRad']) ? $post['pemberiObatKontrasRad'] : "",
    'dosis_berikan' => isset($post['dosisYangDiberikanRad']) ? $post['dosisYangDiberikanRad'] : "",
    'ket_dosis_berikan' => isset($post['dosisYangDiberikanLainnya_Rad']) ? $post['dosisYangDiberikanLainnya_Rad'] : "",
    'cara_pembe_ob' => isset($post['caraPemberianRad']) ? $post['caraPemberianRad'] : "",
    'ket_cara_pembe_ob' => isset($post['caraPemberianLainnya_Rad']) ? $post['caraPemberianLainnya_Rad'] : "",
    'pemeriksaan_fungsi_ginjal' => isset($post['pemeriksaanFungsiGinjalRad']) ? $post['pemeriksaanFungsiGinjalRad'] : "",
    'ket_pemeriksaan_fungsi_ginjal' => isset($post['pemeriksaanFungsiGinjalLainnya_Rad']) ? $post['pemeriksaanFungsiGinjalLainnya_Rad'] : "",
    'pemeriksaan_guldar' => isset($post['pemeriksaanGulaDarahRad']) ? $post['pemeriksaanGulaDarahRad'] : "",
    'ket_pemeriksaan_guldar' => isset($post['pemeriksaanGulaDarahLainnya_Rad']) ? $post['pemeriksaanGulaDarahLainnya_Rad'] : "",
    'pemasangan_mon_sat' => isset($post['pemasanganMonitorSaturasiRad']) ? $post['pemasanganMonitorSaturasiRad'] : "",
    'ket_pemasangan_mon_sat' => isset($post['pemasanganMonitorSaturasiLainnya_Rad']) ? $post['pemasanganMonitorSaturasiLainnya_Rad'] : "",
    'pemberian_oksigenasi' => isset($post['pemberianOksigenasiRad']) ? $post['pemberianOksigenasiRad'] : "",
    'ket_pemberian_oksigenasi' => isset($post['pemberianOksigenasiLainnya_Rad']) ? $post['pemberianOksigenasiLainnya_Rad'] : "",
    'tindakan_anestesi_sed' => isset($post['tindakanAnestesiSedasiRad']) ? $post['tindakanAnestesiSedasiRad'] : "",
    'ket_tindakan_anestesi_sed' => isset($post['tindakanAnestesiSedasiLainnyaRad']) ? $post['tindakanAnestesiSedasiLainnyaRad'] : "",
);

$dataAlatBantu = array();
$indexAlatBantu = 0;
if (isset($post['alatbantu'])) {
    foreach ($post['alatbantu'] as $input) {
        if ($post['alatbantu'][$indexAlatBantu] != "") {
            array_push(
                $dataAlatBantu, array(
                    'id_emr' => $getIdEmr,
                    'id_variabel' => $post['alatbantu'][$indexAlatBantu],
                    'keterangan' => isset($post['alatbantu_lainnya']) ? ($post['alatbantu'][$indexAlatBantu] == 58 ? $post['alatbantu_lainnya'] : "") : "",
                )
            );
        }
        $indexAlatBantu++;
    }
}

$dataEdukasiKeperawatan = array(
    'id_emr' => $getIdEmr,
    'tingkat_pendidikan' => isset($post['tingkat_pendidikan']) ? $post['tingkat_pendidikan'] : "",
    'bahasa' => isset($post['bahasa_sehari_hari']) ? $post['bahasa_sehari_hari'] : "",
    'bahasa_lain' => isset($post['bahasa_lainnya']) ? $post['bahasa_lainnya'] : "",
    'bahasa_daerah' => isset($post['bahasa_daerah']) ? $post['bahasa_daerah'] : "",
    'penerjemah' => isset($post['perlu_penerjemah']) ? $post['perlu_penerjemah'] : "",
    'penerjemah_lain' => isset($post['penerjemah_lainnya']) ? $post['penerjemah_lainnya'] : "",
    'informasi' => isset($post['kesedian_informasi']) ? $post['kesedian_informasi'] : "",
);

$dataHambatan = array();
$indexHambatan = 0;
if (isset($post['hambatan'])) {
    foreach ($post['hambatan'] as $input) {
        if ($post['hambatan'][$indexHambatan] != "") {
            array_push(
                $dataHambatan, array(
                    'id_emr' => $getIdEmr,
                    'id_variabel' => $post['hambatan'][$indexHambatan],
                    'keterangan' => isset($post['hambatan_lainnya']) ? ($post['hambatan'][$indexHambatan] == 106 ? $post['hambatan_lainnya'] : "") : "",
                )
            );
        }
        $indexHambatan++;
    }
}

$dataKebutuhanPembelajaran = array();
$indexKebutuhanPembelajaran = 0;
if (isset($post['kebutuhan_pembelajaran'])) {
    foreach ($post['kebutuhan_pembelajaran'] as $input) {
        if ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] != "") {
            array_push(
                $dataKebutuhanPembelajaran, array(
                    'id_emr' => $getIdEmr,
                    'id_variabel' => $post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran],
                    'keterangan' => isset($post['kebutuhan_pembelajaran_lainnya']) ? ($post['kebutuhan_pembelajaran'][$indexKebutuhanPembelajaran] == 104 ? $post['kebutuhan_pembelajaran_lainnya'] : "") : "",
                )
            );
        }
        $indexKebutuhanPembelajaran++;
    }
}

$dataPemantauanNyeri = array(
    'id_emr' => $getIdEmr,
    'metode' => isset($post['skrining_nyeri']) ? $post['skrining_nyeri'] : "",
    'skor' => isset($post['skor_nyeri_TIR']) ? $post['skor_nyeri_TIR'] : "",
    'farmakologi' => $post['farmakologi'],
    'non_farmakologi' => $post['non_farmakologi'],
    'efek_samping' => isset($post['efek_samping']) ? $post['efek_samping'] : "",
    'keterangan_efek_samping' => isset($post['efek_samping_lain']) ? $post['efek_samping_lain'] : "",
);

$dataKunjunganigd = array(
    'id_emr' => $getIdEmr,
    'jenis_kunjungan' => isset($post['kunjungan']) ? $post['kunjungan'] : "",
    'rujukan' => isset($post['jenis_kunjungan_desc']) ? $post['jenis_kunjungan_desc'] : "",
    'nama_pengantar' => isset($post['nama_pengantar']) ? $post['nama_pengantar'] : "",
    'dibawa_dengan' => isset($post['dibawa_dengan']) ? $post['dibawa_dengan'] : "",
    'hubungan_pasien' => isset($post['hubungan_pasien']) ? $post['hubungan_pasien'] : "",
);

$dataRiwayatKelahiran = array(
    'id_emr' => $getIdEmr,
    'anak_ke' => isset($post['anak_ke']) ? $post['anak_ke'] : "",
    'saudara' => isset($post['jml_saudara']) ? $post['jml_saudara'] : "",
    'cara_lahir' => isset($post['cara_kelahiran']) ? $post['cara_kelahiran'] : "",
    'kondisi_lahir' => isset($post['kondisi_lahir']) ? $post['kondisi_lahir'] : "",
    'berat_badan' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
    'panjang_badan' => isset($post['panjang_badan']) ? $post['panjang_badan'] : "",
    'lingkar_kepala' => isset($post['lingkar_kepala_lahir']) ? $post['lingkar_kepala_lahir'] : "",
    'kelainan_bawaan' => isset($post['kelainan_bawaan_anak']) ? $post['kelainan_bawaan_anak'] : "",
    'isi_kelainan' => isset($post['kelainan_bawaan_anak_desk']) ? json_encode($post['kelainan_bawaan_anak_desk']) : "",
    'imunisasi' => isset($post['riwayat_imunisasi_dasar_anak']) ? $post['riwayat_imunisasi_dasar_anak'] : "",
    'isi_imunisasi' => isset($post['riwayat_imunisasi_dasar_anak_desk']) ? json_encode($post['riwayat_imunisasi_dasar_anak_desk']) : "",
    'riwayat_tumbuh_kembang' => isset($post['riwayat_tumbuh_kembang']) ? $post['riwayat_tumbuh_kembang'] : "",
    'tengkurap' => isset($post['usia_tengkurap']) ? $post['usia_tengkurap'] : "",
    'berjalan' => isset($post['usia_berjalan']) ? $post['usia_berjalan'] : "",
    'duduk' => isset($post['usia_duduk']) ? $post['usia_duduk'] : "",
    'bicara' => isset($post['usia_bicara']) ? $post['usia_bicara'] : "",
    'berdiri' => isset($post['usia_berdiri']) ? $post['usia_berdiri'] : "",
    'tumbuh_gigi' => isset($post['usia_tumbuh_gigi']) ? $post['usia_tumbuh_gigi'] : "",
);

$dataMedis = array(
    'id_emr' => $getIdEmr,
    'nopen' => $post['nopen'],
    'nokun' => $post['nokun'],
    'jenis' => 2,
    'flag' => '0',
);

if (!empty($post['idemr'])) {
    $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
    $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanKesehatan);
    $this->db->replace('keperawatan.tb_perencanaan_tindakan', $dataPerencanaanTindakan);
    $this->db->replace('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
    $this->db->replace('keperawatan.tb_riwayat_kelahiran', $dataRiwayatKelahiran);
    $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesisKeperawatan);
    if (isset($post['skrining_nyeri'])) {
        if ($post['skrining_nyeri'] != 17) {
            $this->db->replace('keperawatan.tb_pemantauan_nyeri', $dataPemantauanNyeri);
        }
    }

    $this->db->delete('keperawatan.tb_hambatan', array('id_emr' => $getIdEmr));
    $this->db->delete('keperawatan.tb_kebutuhan_pembelajaran', array('id_emr' => $getIdEmr));
    $this->db->delete('keperawatan.tb_alat_bantu', array('id_emr' => $getIdEmr));

    foreach ($dataKebutuhanPembelajaran as $key => $value) {
        $this->db->replace('keperawatan.tb_kebutuhan_pembelajaran', $value, 'id_emr');
    }

    foreach ($dataHambatan as $key => $value) {
        $this->db->replace('keperawatan.tb_hambatan', $value, 'id_emr');
    }

    foreach ($dataAlatBantu as $key => $value) {
        $this->db->replace('keperawatan.tb_alat_bantu', $value, 'id_emr');
    }

    $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $getIdEmr));
    $dataAsuhanKeperawatan = array();
    $index = 0;
    $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
    if (isset($post['asuhanKeperawatan'])) {
        foreach ($post['asuhanKeperawatan'] as $input) {
            if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                    $dataAsuhanKeperawatan, array(
                        'id_emr' => $getIdEmr,
                        'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                        'lain_lain' => isset($post[$id]) ? $post[$id] : null
                    )
                );
            }
            $index++;
        }
        $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
    }
    if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
        $result = array('status' => 'success', 'pesan' => 'ubah');
    }
} else {
    $result = array('status' => 'failed');
    $this->db->insert('medis.tb_medis', $dataMedis);
    $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
    $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanKesehatan);
    $this->db->insert('keperawatan.tb_perencanaan_tindakan', $dataPerencanaanTindakan);
    $this->db->insert('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
    $this->db->insert('keperawatan.tb_riwayat_kelahiran', $dataRiwayatKelahiran);
    $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesisKeperawatan);
    if (isset($post['alatbantu'])) {
        $this->db->insert_batch('keperawatan.tb_alat_bantu', $dataAlatBantu);
    }
    if (isset($post['hambatan'])) {
        $this->db->insert_batch('keperawatan.tb_hambatan', $dataHambatan);
    }
    if (isset($post['kebutuhan_pembelajaran'])) {
        $this->db->insert_batch('keperawatan.tb_kebutuhan_pembelajaran', $dataKebutuhanPembelajaran);
    }
    if (isset($post['kunjungan'])) {
        $this->db->insert('keperawatan.tb_kunjungan_igd', $dataKunjunganigd);
    }
    if (isset($post['skrining_nyeri'])) {
        if ($post['skrining_nyeri'] != 17) {
            $this->db->insert('keperawatan.tb_pemantauan_nyeri', $dataPemantauanNyeri);
        }
    }

    $dataAsuhanKeperawatan = array();
    $index = 0;
    $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
    if (isset($post['asuhanKeperawatan'])) {
        foreach ($post['asuhanKeperawatan'] as $input) {
            if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                    $dataAsuhanKeperawatan, array(
                        'id_emr' => $getIdEmr,
                        'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                        'lain_lain' => isset($post[$id]) ? $post[$id] : null
                    )
                );
            }
            $index++;
        }
        $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
    }
    if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
        $result = array('status' => 'success');
    }
}

echo json_encode($result);
}
}
}

public function action_medis($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $post = $this->input->post();

                $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();

                $dataMedis = array(
                    'id_emr' => $getIdEmr,
                    'nopen' => $post['nopen'],
                    'nokun' => $post['nokun'],
                    'created_by' => $this->session->userdata('id'),
                    'tanggal_pulang' => isset($post['tanggal_keluar']) ? $post['tanggal_keluar'] : null,
                    'waktu_pulang' => isset($post['waktu_keluar']) ? $post['waktu_keluar'] : null,
                    'protokol_kemo' => isset($post['protokol_kemo']) ? $post['protokol_kemo'] : "",
                    'siklus_ke' => isset($post['siklus_ke']) ? $post['siklus_ke'] : "",
                    'flag' => '1',
                    'jenis' => '2',
                );

                $dataAnamnesa = array(
                    'id_emr' => $getIdEmr,
                    'id_auto_allo' => $post['anamnesis'],
                    'allo_nama' => isset($post['nama']) ? $post['nama'] : "",
                    'hubungan_dengan_pasien' => isset($post['hubunganDenganPasien']) ? $post['hubunganDenganPasien'] : "",
                    'keluhan_utama' => isset($post['keluhanUtama']) ? $post['keluhanUtama'] : "",
                    'riwayat_sakit_sekarang' => isset($post['riwayatPenyakitSekarang']) ? $post['riwayatPenyakitSekarang'] : "",
                    'riwayat_penyakit_dahulu' => isset($post['riwayatPenyakitDahulu']) ? $post['riwayatPenyakitDahulu'] : "",
                    'desk_riwayat_penyakit_dahulu' => isset($post['deskRiwayatPenyakitDahulu']) ? $post['deskRiwayatPenyakitDahulu'] : "",
                    'riwayat_tata_laksana_sebelumnya' => isset($post['riwayatTatalaksanaSebelumnya']) ? $post['riwayatTatalaksanaSebelumnya'] : "",
                    'riwayat_penggunaan_obat' => isset($post['riwayatpenggunaanobatMedInv']) ? $post['riwayatpenggunaanobatMedInv'] : "",
                    'desk_riwayat_penggunaan_obat' => isset($post['deskriwayatpenggunaanobat']) ? $post['deskriwayatpenggunaanobat'] : "",
                    // 'riwayat_alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
                    // 'desk_riwayat_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
                );

                $dataRiwayatAlergi = array(
                    'id_emr' => $getIdEmr,
                    'alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : "",
                    'isi_alergi' => isset($post['riwayat_alergi_desk']) ? json_encode($post['riwayat_alergi_desk']) : "",
                    'reaksi_alergi' => isset($post['reaksi_alergi']) ? $post['reaksi_alergi'] : "",
                );

                $dataKeperawatan = array(
                    'id_emr' => $getIdEmr,
                    'nopen' => $post['nopen'],
                    'nokun' => $post['nokun'],
                    'flag' => 0,
                    'jenis' => 2,
                );

                if (isset($post['status_lokalis_not']) == "on") {
                    $lokaslisNot = 1;
                } else {
                    $lokaslisNot = 0;
                }

                $dataPemeriksaanFisik = array(
                    'id_emr' => $getIdEmr,
                    'performance_status' => isset($post['performanceStatus']) ? $post['performanceStatus'] : "",
                    'performance_status_anak' => isset($post['performanceStatus_anak']) ? $post['performanceStatus_anak'] : "",
                    'performance_status_anak_score' => isset($post['performanceStatus_anak_skor']) ? $post['performanceStatus_anak_skor'] : "",
                    'mata' => isset($post['mata']) ? $post['mata'] : "",
                    'mata_lainnya' => isset($post['mataLainya']) ? $post['mataLainya'] : "",
                    'leher' => isset($post['leher']) ? $post['leher'] : "",
                    'desk_leher_tidak_normal' => isset($post['leherTdkNormal']) ? $post['leherTdkNormal'] : "",
                    'irama_jantung' => isset($post['iramaJantung']) ? $post['iramaJantung'] : "",
                    'desk_irama_jantung' => isset($post['iramaJantungTdkTerartur']) ? $post['iramaJantungTdkTerartur'] : "",
                    'suara_napas' => isset($post['dadaSuaraNafas']) ? $post['dadaSuaraNafas'] : "",
                    'desk_suara_napas' => isset($post['suaraNafasTdkNormal']) ? $post['suaraNafasTdkNormal'] : "",
                    'perut_hati' => isset($post['perutHati']) ? $post['perutHati'] : "",
                    'desk_hati' => isset($post['perutHatiTeraba']) ? $post['perutHatiTeraba'] : "",
                    'perut_limpa' => isset($post['perutLimpa']) ? $post['perutLimpa'] : "",
                    'desk_limpa' => isset($post['perutLimpaTeraba']) ? $post['perutLimpaTeraba'] : "",
                    'eks_atas_kanan' => isset($post['ekstremitasAtasKanan']) ? $post['ekstremitasAtasKanan'] : "",
                    'desk_eks_atas_kanan' => isset($post['deskEkstremitasAtasKanan']) ? $post['deskEkstremitasAtasKanan'] : "",
                    'eks_atas_kiri' => isset($post['ekstremitasAtasKiri']) ? $post['ekstremitasAtasKiri'] : "",
                    'desk_eks_atas_kiri' => isset($post['deskEkstremitasAtasKiri']) ? $post['deskEkstremitasAtasKiri'] : "",
                    'eks_bawah_kanan' => isset($post['ekstremitasBawahKanan']) ? $post['ekstremitasBawahKanan'] : "",
                    'desk_eks_bawah_kanan' => isset($post['deskEkstremitasBawahKanan']) ? $post['deskEkstremitasBawahKanan'] : "",
                    'eks_bawah_kiri' => isset($post['ekstremitasBawahKiri']) ? $post['ekstremitasBawahKiri'] : "",
                    'desk_eks_bawah_kiri' => isset($post['deskEkstremitasBawahKiri']) ? $post['deskEkstremitasBawahKiri'] : "",
                    'turgor' => isset($post['kulitTurgor']) ? $post['kulitTurgor'] : "",
                    'sianosis' => isset($post['kulitSianosis']) ? $post['kulitSianosis'] : "",
                    'refleks' => isset($post['refleks']) ? $post['refleks'] : "",
                    'desk_refleks' => isset($post['desRefleks']) ? $post['desRefleks'] : "",
                    'kgb' => isset($post['kelenjarGetahBening']) ? $post['kelenjarGetahBening'] : "",
                    'desk_kgb' => isset($post['deskKelenjarGetahBening']) ? $post['deskKelenjarGetahBening'] : "",
                    'tumor' => isset($post['tumor']) ? $post['tumor'] : "",
                    'jenis_tumor' => isset($post['deskTumor']) ? $post['deskTumor'] : "",
                    'na_lokalis' => isset($lokaslisNot) ? $lokaslisNot : "",
                    'pemeriksaan_fisik_igd' => isset($post['pemeriksaan_fisik_igd']) ? $post['pemeriksaan_fisik_igd'] : "",
                    'klinis' => isset($post['klinis_fisik']) ? $post['klinis_fisik'] : "",
                    'tinggi_badan' => isset($post['tinggi_badan']) ? $post['tinggi_badan'] : "",
                    'berat_badan' => isset($post['berat_badan']) ? $post['berat_badan'] : "",
                    'luas_permukaan_badan' => isset($post['luas_berat_badan']) ? $post['luas_berat_badan'] : "",
                    'bb_u' => isset($post['bb_u']) ? $post['bb_u'] : "",
                    'tb_u' => isset($post['tb_u']) ? $post['tb_u'] : "",
                    'bb_tb' => isset($post['bb_tb']) ? $post['bb_tb'] : "",
                    'imt' => isset($post['imt']) ? $post['imt'] : "",
                    'status_gizi' => isset($post['status_gizi']) ? $post['status_gizi'] : "",
                );

                // if ($post['penunjangLainya'] != "") {
                //     $dataPemeriksaanPenunjang = array(
                //         'id_emr' => $getIdEmr,
                //         'penunjangLainya' => $post['penunjangLainya'],
                //     );
                // }

                $dataPenunjangLainya = array(
                    'id_emr' => $getIdEmr,
                    'histopatologi' => isset($post['histopatologiMedis']) ? $post['histopatologiMedis'] : "",
                    'batas_sayatan' => isset($post['batas_sayatan']) ? $post['batas_sayatan'] : "",
                    'lvnsi' => isset($post['lvsi']) ? $post['lvsi'] : "",
                    'penunjang_lain' => isset($post['penunjangLainya']) ? $post['penunjangLainya'] : "",
                    'differensiasi' => isset($post['diferensiasi']) ? $post['diferensiasi'] : "",
                    'jumlah_kgb_diangkat' => isset($post['jumlah_kgb']) ? $post['jumlah_kgb'] : "",
                    'derajat_keganasan' => isset($post['derajat_keganasan']) ? $post['derajat_keganasan'] : "",
                    'tgl_penunjang_anyelir' => isset($post['tgl_pemeriksaan_penunjang']) ? $post['tgl_pemeriksaan_penunjang'] : "",
                );

                if (isset($post['klasifikasi_not']) == "on") {
                    $klasifikasiNot = 1;
                } else {
                    $klasifikasiNot = 0;
                }

                $dataMedisKeperawatan = array(
                    'id_emr' => $getIdEmr,
                    'desk_diagnosa_medis' => isset($post['deskDiagnosisMedis']) ? $post['deskDiagnosisMedis'] : "",
                    'desk_diagnosa_kanker' => isset($post['deskDiagnosaKanker']) ? $post['deskDiagnosaKanker'] : "",
                    'sisi_tubuh' => isset($post['sisiTubuh']) ? $post['sisiTubuh'] : "",
                    'klasifikasi_t' => isset($post['klasifikasi_T']) ? $post['klasifikasi_T'] : "",
                    'klasifikasi_n' => isset($post['klasifikasi_N']) ? $post['klasifikasi_N'] : "",
                    'klasifikasi_m' => isset($post['klasifikasi_M']) ? $post['klasifikasi_M'] : "",
                    'klasifikasi_not' => isset($klasifikasiNot) ? $klasifikasiNot : "",
                    'stadium' => isset($post['stadium']) ? $post['stadium'] : "",
                    'tujuan_pengobatan' => isset($post['tujuanPengobatan']) ? $post['tujuanPengobatan'] : "",
                    'jika_ada_masalah_medis' => isset($post['masalahKesehatan']) ? $post['masalahKesehatan'] : "",
                    'jika_ada_masalah_lainnya' => isset($post['deskJikaAdaMasalahLain']) ? $post['deskJikaAdaMasalahLain'] : "",
                );

                $dataPerencanaan = array(
                    'id_emr' => $getIdEmr,
                    'pemeriksaan_penunjang' => isset($post['pemeriksaanPenunjang']) ? $post['pemeriksaanPenunjang'] : "",
                    'terapi_tindakan' => isset($post['terapiTindakan']) ? $post['terapiTindakan'] : "",
                    'rencana_tindakan' => isset($post['rencana_tindakan']) ? $post['rencana_tindakan'] : "",
                    'lama_tindakan' => isset($post['lama_tindakan']) ? $post['lama_tindakan'] : "",
                    'persiapan_darah' => isset($post['persiapandarah']) ? $post['persiapandarah'] : "",
                    'isi_persiapan_darah' => isset($post['deskpersiapandarah']) ? $post['deskpersiapandarah'] : "",
                    'persiapan_alat_khusus' => isset($post['persiapanalatkhusus']) ? $post['persiapanalatkhusus'] : "",
                    'isi_persiapan_alat_khusus' => isset($post['deskpersiapanalatkhusus']) ? $post['deskpersiapanalatkhusus'] : "",
                    'lain_lain_prosedur' => isset($post['lain_lain_prosedur']) ? $post['lain_lain_prosedur'] : "",
                    'target_terukur' => isset($post['target_terukur']) ? $post['target_terukur'] : "",
                    'diet' => isset($post['diet']) ? $post['diet'] : "",
                    'jenis_diet' => isset($post['jenisDiet']) ? $post['jenisDiet'] : "",
                    'diet_lainnya' => isset($post['dietLainya']) ? $post['dietLainya'] : "",
                    'terapi_medikemantosa' => isset($post['terapi_medikamentosa']) ? $post['terapi_medikamentosa'] : "",
                    'target_pengobatan' => isset($post['target_pengobatan']) ? $post['target_pengobatan'] : "",
                    'tindak_lanjut_lain' => isset($post['tindak_lanjut_lain']) ? $post['tindak_lanjut_lain'] : "",
                    'desk_tindak_lanjut_lain' => isset($post['desk_tindak_lanjut_lain']) ? $post['desk_tindak_lanjut_lain'] : "",
                );

                if (isset($post['brakhiterapi']) == "on") {
                    $brakhiterapiNot = 1;
                } else {
                    $brakhiterapiNot = 0;
                }

                $dataInstruksiMedis = array(
                    'id_emr' => $getIdEmr,
                    'simulator' => isset($post['simulator']) ? $post['simulator'] : "",
                    'ct_simulator' => isset($post['ctsimulator']) ? $post['ctsimulator'] : "",
                    'ct_simulator_kontras' => isset($post['ct_simulator_kontras']) ? $post['ct_simulator_kontras'] : "",
                    'desk_ct_simulator' => isset($post['deskCtSimulator']) ? $post['deskCtSimulator'] : "",
                    'brakhiterapi_not_applicable' => isset($brakhiterapiNot) ? $brakhiterapiNot : "",
                    'penempatan_sumber' => isset($post['penempatanSumber']) ? $post['penempatanSumber'] : "",
                    'teknik' => isset($post['brakhiterapiTeknik']) ? $post['brakhiterapiTeknik'] : "",
                    'teknik_desk' => isset($post['brakhiterapiTeknik_lain']) ? $post['brakhiterapiTeknik_lain'] : "",
                    'lokasi' => isset($post['lokasi_brak']) ? $post['lokasi_brak'] : "",
                    'aplikator_yg_digunakan' => isset($post['aplikator_brak']) ? $post['aplikator_brak'] : "",
                    'jumlah_fraksi' => isset($post['jumlah_fraksi_brak']) ? $post['jumlah_fraksi_brak'] : "",
                    'dosis_per_fraksi' => isset($post['dosis_brak']) ? $post['dosis_brak'] : "",
                );

                $dataInstruksiMedisRadiasi = array(
                    'id_emr' => $getIdEmr,
                    'radiasi_eksterna_teknik' => isset($post['radiasiteknik']) ? $post['radiasiteknik'] : "",
                    'radiasi_eksterna_jenis' => isset($post['radiasijenis']) ? $post['radiasijenis'] : "",

                    'lapangan_dosis_radiasi_1' => isset($post['lapangan_dosis_1']) ? $post['lapangan_dosis_1'] : "",
                    'jenis_lapangan_dosis_radiasi_1' => isset($post['lokal_loko_1']) ? $post['lokal_loko_1'] : "",
                    'lokoregional_1' => isset($post['skor_lokal_loko_1']) ? $post['skor_lokal_loko_1'] : "",
                    'lapangan_lainnya_1' => isset($post['deskLapanganDosisLainnya_1']) ? $post['deskLapanganDosisLainnya_1'] : "",
                    'dosis_per_fraksi_1' => isset($post['dosis_fraksi_1']) ? $post['dosis_fraksi_1'] : "",
                    'jumlah_fraksi_1' => isset($post['jumlah_fraksi_1']) ? $post['jumlah_fraksi_1'] : "",
                    'dosis_total_1' => isset($post['dosis_total_1']) ? $post['dosis_total_1'] : "",
                    'jumlah_fraksi_minggu_1' => isset($post['jumlah_fraksi_minggu_1']) ? $post['jumlah_fraksi_minggu_1'] : "",
                    'jenis_fraksinasi_1' => isset($post['jenisFraksinasi_1']) ? $post['jenisFraksinasi_1'] : "",
                    'jenis_fraksinasi_lainnya_1' => isset($post['deskJenisFraksinasi_1']) ? $post['deskJenisFraksinasi_1'] : "",

                    'lapangan_dosis_radiasi_2' => isset($post['lapangan_dosis_2']) ? $post['lapangan_dosis_2'] : "",
                    'jenis_lapangan_dosis_radiasi_2' => isset($post['lokal_loko_2']) ? $post['lokal_loko_2'] : "",
                    'lokoregional_2' => isset($post['skor_lokal_loko_2']) ? $post['skor_lokal_loko_2'] : "",
                    'lapangan_lainnya_2' => isset($post['deskLapanganDosisLainnya_2']) ? $post['deskLapanganDosisLainnya_2'] : "",
                    'dosis_per_fraksi_2' => isset($post['dosis_fraksi_2']) ? $post['dosis_fraksi_2'] : "",
                    'jumlah_fraksi_2' => isset($post['jumlah_fraksi_2']) ? $post['jumlah_fraksi_2'] : "",
                    'dosis_total_2' => isset($post['dosis_total_2']) ? $post['dosis_total_2'] : "",
                    'jumlah_fraksi_minggu_2' => isset($post['jumlah_fraksi_minggu_2']) ? $post['jumlah_fraksi_minggu_2'] : "",
                    'jenis_fraksinasi_2' => isset($post['jenisFraksinasi_2']) ? $post['jenisFraksinasi_2'] : "",
                    'jenis_fraksinasi_lainnya_2' => isset($post['deskJenisFraksinasi_2']) ? $post['deskJenisFraksinasi_2'] : "",

                    'lapangan_dosis_radiasi_2' => isset($post['lapangan_dosis_2']) ? $post['lapangan_dosis_2'] : "",
                    'jenis_lapangan_dosis_radiasi_2' => isset($post['lokal_loko_2']) ? $post['lokal_loko_2'] : "",
                    'lokoregional_2' => isset($post['skor_lokal_loko_2']) ? $post['skor_lokal_loko_2'] : "",
                    'lapangan_lainnya_2' => isset($post['deskLapanganDosisLainnya_2']) ? $post['deskLapanganDosisLainnya_2'] : "",
                    'dosis_per_fraksi_2' => isset($post['dosis_fraksi_2']) ? $post['dosis_fraksi_2'] : "",
                    'jumlah_fraksi_2' => isset($post['jumlah_fraksi_2']) ? $post['jumlah_fraksi_2'] : "",
                    'dosis_total_2' => isset($post['dosis_total_2']) ? $post['dosis_total_2'] : "",
                    'jumlah_fraksi_minggu_2' => isset($post['jumlah_fraksi_minggu_2']) ? $post['jumlah_fraksi_minggu_2'] : "",
                    'jenis_fraksinasi_2' => isset($post['jenisFraksinasi_2']) ? $post['jenisFraksinasi_2'] : "",
                    'jenis_fraksinasi_lainnya_2' => isset($post['deskJenisFraksinasi_2']) ? $post['deskJenisFraksinasi_2'] : "",

                    'lapangan_dosis_radiasi_3' => isset($post['lapangan_dosis_3']) ? $post['lapangan_dosis_3'] : "",
                    'jenis_lapangan_dosis_radiasi_3' => isset($post['lokal_loko_3']) ? $post['lokal_loko_3'] : "",
                    'lokoregional_3' => isset($post['skor_lokal_loko_3']) ? $post['skor_lokal_loko_3'] : "",
                    'lapangan_lainnya_3' => isset($post['deskLapanganDosisLainnya_3']) ? $post['deskLapanganDosisLainnya_3'] : "",
                    'dosis_per_fraksi_3' => isset($post['dosis_fraksi_3']) ? $post['dosis_fraksi_3'] : "",
                    'jumlah_fraksi_3' => isset($post['jumlah_fraksi_3']) ? $post['jumlah_fraksi_3'] : "",
                    'dosis_total_3' => isset($post['dosis_total_3']) ? $post['dosis_total_3'] : "",
                    'jumlah_fraksi_minggu_3' => isset($post['jumlah_fraksi_minggu_3']) ? $post['jumlah_fraksi_minggu_3'] : "",
                    'jenis_fraksinasi_3' => isset($post['jenisFraksinasi_3']) ? $post['jenisFraksinasi_3'] : "",
                    'jenis_fraksinasi_lainnya_3' => isset($post['deskJenisFraksinasi_3']) ? $post['deskJenisFraksinasi_3'] : "",

                    'radiasi_eksterna_lain' => isset($post['lain_lain_radioterapi']) ? $post['lain_lain_radioterapi'] : "",
                );

                if (!empty($post['idemr'])) {
                    $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatAlergi);
                    $this->db->replace('medis.tb_anamnesa', $dataAnamnesa);
                    $this->db->replace('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
                    // if ($post['penunjangLainya'] != "") {
                    //     $this->db->replace('medis.tb_pemeriksaan_penunjang', $dataPemeriksaanPenunjang);
                    // }
                    $this->db->replace('medis.tb_penunjang_lain', $dataPenunjangLainya);
                    $this->db->replace('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
                    $this->db->replace('medis.tb_perencanaan', $dataPerencanaan);
                    $this->db->replace('medis.tb_instruksi_medis_radioterapi', $dataInstruksiMedis);
                    $this->db->replace('medis.tb_instruksi_medis_radiasi_eks', $dataInstruksiMedisRadiasi);
                    if ($this->db->replace('medis.tb_medis', $dataMedis)) {
                        $result = array('status' => 'success', 'pesan' => 'ubah');
                    }
                } else {
                    $result = array('status' => 'failed');
                    $this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan);
                    $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatAlergi);
                    $this->db->insert('medis.tb_anamnesa', $dataAnamnesa);
                    $this->db->insert('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
                    // if ($post['penunjangLainya'] != "") {
                    //     $this->db->insert('medis.tb_pemeriksaan_penunjang', $dataPemeriksaanPenunjang);
                    // }
                    $this->db->insert('medis.tb_penunjang_lain', $dataPenunjangLainya);
                    $this->db->insert('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
                    $this->db->insert('medis.tb_perencanaan', $dataPerencanaan);
                    $this->db->insert('medis.tb_instruksi_medis_radioterapi', $dataInstruksiMedis);
                    $this->db->insert('medis.tb_instruksi_medis_radiasi_eks', $dataInstruksiMedisRadiasi);

                    if ($this->db->insert('medis.tb_medis', $dataMedis)) {
                        $result = array('status' => 'success');
                    }
                }
                echo json_encode($result);
            }
        }

}

public function viewEditTindakanInvasif()
{
    $id = $this->input->post('id');
    $view = $this->input->post('view');
    $nomr = $this->input->post('nomr');
    $nokun2 = $this->input->post('nokun2');
    $nopen = $this->input->post('nopen');
    $getPengkajianTindakanInvasif = $this->pengkajianAwalModel->getPengkajianTindakanInvasif($id);
    $getPengkajianTindakanInvasifMedis = $this->pengkajianAwalModel->getPengkajianTindakanInvasifMedis($id);
    $riwayatAlergi = $this->masterModel->referensi(2);
    $kesadaran = $this->masterModel->referensi(5);
    $getPengkajian = $this->pengkajianAwalModel->getPengkajian($id);
    $skriningNyeri = $this->masterModel->referensi(7);
    $skriningResikoJatuhPusing = $this->masterModel->referensi(120);
    $skriningResikoJatuhBerdiri = $this->masterModel->referensi(121);
    $skriningResikoJatuh6Bulan = $this->masterModel->referensi(122);
    $alatBantu = $this->masterModel->referensi(19);
    $pendidikan = $this->masterModel->referensi(24);
    $bahasaSehari = $this->masterModel->referensi(25);
    $perluPenerjemah = $this->masterModel->referensi(26);
    $kesediaanInformasi = $this->masterModel->referensi(27);
    $hambatan = $this->masterModel->referensi(28);
    $kebutuhanPembelajaran = $this->masterModel->referensi(29);
    $formAsuhanKeperawatan = $this->masterModel->referensi(148);
    $skalaNyeriWBR = $this->masterModel->referensi(115);
    $skalaNyeriNRS = $this->masterModel->referensi(114);
    $efeksampingNRS = $this->masterModel->referensi(118);
    $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
    $pengkajianNyeriQuality = $this->masterModel->referensi(9);
    $pengkajianNyeriTime = $this->masterModel->referensi(12);
    $skalaNyeriFLACC = $this->masterModel->referensi(123);
    $skalaNyeriBPS = $this->masterModel->referensi(133);
    $performanceStatus = $this->masterModel->referensi(30);
    $riwayatpenggunaanobat = $this->masterModel->referensi(288);
    $sisiTubuh = $this->masterModel->referensi(49);
    $stadium = $this->masterModel->stadium();
    $persiapandarah = $this->masterModel->referensi(324);
    $persiapanalatkhusus = $this->masterModel->referensi(325);
    $diet = $this->masterModel->referensi(56);
    $kunjungan_pk = $this->pengkajianAwalModel->kunjungan_pk($nomr);
    $sitologi = $this->pengkajianAwalModel->sitologi($nomr);
    $histologi = $this->pengkajianAwalModel->histologi($nomr);
    $tindakan_rad = $this->pengkajianAwalModel->tindakan_rad($nomr);



    $dataEdit = array(
        'getPengkajianTindakanInvasif_edit' => $getPengkajianTindakanInvasif,
        'getPengkajianTindakanInvasifMedis_edit' => $getPengkajianTindakanInvasifMedis,
        'riwayatAlergi' => $riwayatAlergi,
        'kunjungan_pk' => $kunjungan_pk,
        'sitologi' => $sitologi,
        'histologi' => $histologi,
        'tindakan_rad' => $tindakan_rad,
        'view' => $view,
        'nomr' => $nomr,
        'nokun2' => $nokun2,
        'nopen' => $nopen,
        'kesadaran' => $kesadaran,
        'performanceStatus' => $performanceStatus,
        'riwayatpenggunaanobat' => $riwayatpenggunaanobat,
        'sisiTubuh' => $sisiTubuh,
        'stadium' => $stadium,
        'persiapandarah' => $persiapandarah,
        'persiapanalatkhusus' => $persiapanalatkhusus,
        'diet' => $diet,
        'getPengkajian' => $getPengkajian,
        'skriningNyeri' => $skriningNyeri,
        'skriningResikoJatuhPusing' => $skriningResikoJatuhPusing,
        'skriningResikoJatuhBerdiri' => $skriningResikoJatuhBerdiri,
        'skriningResikoJatuh6Bulan' => $skriningResikoJatuh6Bulan,
        'alatBantu' => $alatBantu,
        'pendidikan' => $pendidikan,
        'bahasaSehari' => $bahasaSehari,
        'perluPenerjemah' => $perluPenerjemah,
        'kesediaanInformasi' => $kesediaanInformasi,
        'hambatan' => $hambatan,
        'kebutuhanPembelajaran' => $kebutuhanPembelajaran,
        'formAsuhanKeperawatan' => $formAsuhanKeperawatan,
        'skalaNyeriWBR' => $skalaNyeriWBR,
        'skalaNyeriNRS' => $skalaNyeriNRS,
        'efeksampingNRS' => $efeksampingNRS,
        'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
        'pengkajianNyeriQuality' => $pengkajianNyeriQuality,
        'pengkajianNyeriTime' => $pengkajianNyeriTime,
        'skalaNyeriFLACC' => $skalaNyeriFLACC,
        'skalaNyeriBPS' => $skalaNyeriBPS,

    );

    $this->load->view('Pengkajian/pengkajianProRad/modalViewEditTindakanInvasif', $dataEdit);
}

public function EditTindakanInvasif($param)
{
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        if ($param == 'edit') {
            $post = $this->input->post();

            $getIdEmr = $this->input->post("idemr_edit");
            $anamnesis = $this->input->post("anamnesis_edit");
            $nama = $this->input->post("nama_edit");
            $hubunganDenganPasien = $this->input->post("hubunganDenganPasien_edit");

            $dataAnamnesisKeperawatan = array(
                'id_emr' => $getIdEmr,
                'id_auto_allo' => isset($anamnesis) ? $anamnesis : "",
                'allo_nama' => isset($nama) ? $nama : "",
                'hubungan_dengan_pasien' => isset($hubunganDenganPasien) ? $hubunganDenganPasien : "",
            );

            $nopen = $this->input->post("nopen_edit");
            $nokun = $this->input->post("nokun_edit");

            $dataKeperawatan = array(
                'id_emr' => $getIdEmr,
                'nopen' => $nopen,
                'nokun' => $nokun,
                'created_by' => $this->session->userdata('id'),
                'flag' => '1',
                'jenis' => '2',
            );

            $riwayat_alergi = $this->input->post("riwayat_alergi_edit");
            $riwayat_alergi_desk = $this->input->post("riwayat_alergi_desk_edit");
            $reaksi_alergi = $this->input->post("reaksi_alergi_edit");
            $riwayat_cabut_gigi = $this->input->post("riwayat_cabut_gigi_edit");
            $riwayat_pengobatan = $this->input->post("riwayat_pengobatan_edit");
            $dataRiwayatKesehatan = array(
                'id_emr' => $getIdEmr,
                'riwayat_kanker' => isset($post['riwayat_kanker_keluarga']) ? $post['riwayat_kanker_keluarga'] : "",
                'isi_kanker' => isset($post['riwayat_kanker_keluarga_desk']) ? json_encode($post['riwayat_kanker_keluarga_desk']) : "",

                'alergi' => isset($riwayat_alergi) ? $riwayat_alergi : "",
                'isi_alergi' => isset($riwayat_alergi_desk) ? json_encode($riwayat_alergi_desk) : "",
                'reaksi_alergi' => isset($reaksi_alergi) ? $reaksi_alergi : "",

                'riwayat_transfusi' => isset($post['riwayat_transfusi_darah']) ? $post['riwayat_transfusi_darah'] : "",
                'reaksi_transfusi' => isset($post['riwayat_transfusi_darah_desk']) ? $post['riwayat_transfusi_darah_desk'] : "",
                'isi_reaksi_transfusi' => isset($post['riwayat_td_reaksi_alergi']) ? json_encode($post['riwayat_td_reaksi_alergi']) : "",

                'kebiasaan' => isset($post['kebiasaan_merokok']) ? $post['kebiasaan_merokok'] : "",
                'isi_kebiasaan' => isset($post['kebiasaan_merokok_desk']) ? json_encode($post['kebiasaan_merokok_desk']) : "",

                'riwayat_metabolik' => isset($post['riwayat_metabolik_keluarga']) ? $post['riwayat_metabolik_keluarga'] : "",
                'isi_metabolik' => isset($post['riwayat_metabolik_keluarga_desk']) ? json_encode($post['riwayat_metabolik_keluarga_desk']) : "",

                'deteksidini' => isset($post['riwayat_deteksi_dini_kanker']) ? $post['riwayat_deteksi_dini_kanker'] : "",
                'isi_deteksidini' => isset($post['riwayat_deteksi_dini_kanker_desk']) ? json_encode($post['riwayat_deteksi_dini_kanker_desk']) : "",

                    // Radioterapi
                'riwayat_penyakit_keluarga' => isset($post['riwayat_penyakit_dalam_keluarga']) ? $post['riwayat_penyakit_dalam_keluarga'] : "",
                'penyakit_keluarga_lain' => isset($post['riwayat_penyakit_dalam_keluarga_desk']) ? json_encode($post['riwayat_penyakit_dalam_keluarga_desk']) : "",
                'riwayat_cabut_gigi' => isset($riwayat_cabut_gigi) ? json_encode($riwayat_cabut_gigi) : "",

                    //Prosedure
                'riwayat_pengobatan' => isset($riwayat_pengobatan) ? json_encode($riwayat_pengobatan) : "",

                    // Anyelir
                'riwayat_ekstravasasi' => isset($post['riwayat_ekstravasasi']) ? $post['riwayat_ekstravasasi'] : "",
                'isi_ekstravasasi' => isset($post['riwayat_ekstravasasi_desc']) ? $post['riwayat_ekstravasasi_desc'] : "",
                'pilih_ekstravasasi_foto' => isset($post['riwayat_ekstravasasi_foto']) ? $post['riwayat_ekstravasasi_foto'] : "",
                'ekstravasasi_depan' => isset($post['ekstravasasi_depan']) ? $post['ekstravasasi_depan'] : "",
                'ekstravasasi_belakang' => isset($post['ekstravasasi_belakang']) ? $post['ekstravasasi_belakang'] : "",
                'hasil_laboratorium' => isset($post['hasil_laboratorium']) ? $post['hasil_laboratorium'] : "",
                'isi_laboratorium' => isset($post['hasil_laboratorium_desk']) ? json_encode($post['hasil_laboratorium_desk']) : "",
                'hasil_BMP' => isset($post['hasil_bmp_terakhir']) ? $post['hasil_bmp_terakhir'] : "",
                'isi_BMP' => isset($post['hasil_bmp_terakhir_desk']) ? json_encode($post['hasil_bmp_terakhir_desk']) : "",
                'kemoterapi' => isset($post['hasil_kemoterapi']) ? $post['hasil_kemoterapi'] : "",
                'isi_kemoterapi' => isset($post['kemoterapi_terdahulu_desk']) ? json_encode($post['kemoterapi_terdahulu_desk']) : "",
                'tindakan_perawatan' => isset($post['tindakan_perawatan_terakhir']) ? $post['tindakan_perawatan_terakhir'] : "",
                'perawatan_lain' => isset($post['tindakan_perawatan_terakhir_desk']) ? json_encode($post['tindakan_perawatan_terakhir_desk']) : "",
                'riwayat_graft' => isset($post['riwayat_gvhd']) ? $post['riwayat_gvhd'] : "",
            );

$kesadaran = $this->input->post("kesadaran_edit");
$tekanan_darah_1 = $this->input->post("tekanan_darah_1_edit");
$tekanan_darah_2 = $this->input->post("tekanan_darah_2_edit");
$pernapasan = $this->input->post("pernapasan_edit");
$nadi = $this->input->post("nadi_edit");
$suhu = $this->input->post("suhu_edit");
$skrining_nyeri = $this->input->post("skrining_nyeri_edit");
$propocative = $this->input->post("propocative_edit");
$quality = $this->input->post("quality_edit");
$quality_lainnya = $this->input->post("quality_lainnya_edit");
$regio = $this->input->post("regio_edit");
$severity = $this->input->post("severity_edit");
$time = $this->input->post("time_edit");
$durasi_nyeri = $this->input->post("durasi_nyeri_edit");
$skriningresikojatuhpusing = $this->input->post("skriningresikojatuhpusing_edit");
$skriningresikojatuhberdiri = $this->input->post("skriningresikojatuhberdiri_edit");
$skriningresikojatuh6bulan = $this->input->post("skriningresikojatuh6bulan_edit");
$tinggi_badan = $this->input->post("tinggi_badan_edit");
$berat_badan = $this->input->post("berat_badan_edit");

$dataPemeriksaanKesehatan = array(
    'id_emr' => $getIdEmr,
    'keluhan_pasien' => isset($post['keluhan_pasien']) ? $post['keluhan_pasien'] : "",
    'sedang_hamil' => isset($post['pasien_hamil_rad']) ? $post['pasien_hamil_rad'] : "",
    'hamil_jelaskan' => isset($post['pasienHamilLainnya_rad']) ? $post['pasienHamilLainnya_rad'] : "",
    'kesadaran' => isset($kesadaran) ? $kesadaran : "",
    'tekanan_darah' => isset($tekanan_darah_1) ? $tekanan_darah_1 : "",
    'per_tekanan_darah' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
    'pernapasan' => isset($pernapasan) ? $pernapasan : "",
    'nadi' => isset($nadi) ? $nadi : "",
    'suhu' => isset($suhu) ? $suhu : "",
    'nyeri' => isset($skrining_nyeri) ? $skrining_nyeri : "",
    'provocative' => isset($propocative) ? $propocative : "",
    'quality' => isset($quality) ? $quality : "",
    'sebutkan_quality' => isset($quality_lainnya) ? $quality_lainnya : "",
    'isi_regio' => isset($regio) ? $regio : "",
    'severity' => isset($severity) ? $severity : "",
    'time' => isset($time) ? $time : "",
    'isi_time' => isset($durasi_nyeri) ? $durasi_nyeri : "",
    'psikologis' => isset($post['psikologis']) ? $post['psikologis'] : "",
    'isi_psikologi' => isset($post['psikologis_lainnya']) ? $post['psikologis_lainnya'] : "",
    'hub_keluarga' => isset($post['sdeh']) ? $post['sdeh'] : "",
    'nafkah_utama' => isset($post['sdepn']) ? $post['sdepn'] : "",
    'tinggal' => isset($post['sdets']) ? $post['sdets'] : "",
    'keyakinan' => isset($post['spiritualkultural']) ? $post['spiritualkultural'] : "",
    'sebutkan_keyakinan' => isset($post['spiritualkultural_lainnya']) ? $post['spiritualkultural_lainnya'] : "",
    'pengobatan_alternatif' => isset($post['pengobatan_alternatif']) ? $post['pengobatan_alternatif'] : "",
    'sebutkan_pengobatan_alternatif' => isset($post['pengobatan_alternatif_desk']) ? json_encode($post['pengobatan_alternatif_desk']) : "",
    'pengobatan_bertentangan' => isset($post['pengobatan_bertentangan']) ? $post['pengobatan_bertentangan'] : "",
    'sebutkan_pengobatan_bertentangan' => isset($post['pengobatan_bertentangan_desk']) ? json_encode($post['pengobatan_bertentangan_desk']) : "",
    'status_fungsionl' => isset($post['statusfungsional']) ? $post['statusfungsional'] : "",
    'vertigo' => isset($skriningresikojatuhpusing) ? $skriningresikojatuhpusing : "",
    'sulit_berdiri' => isset($skriningresikojatuhberdiri) ? $skriningresikojatuhberdiri : "",
    'jatuh_dlm_6' => isset($skriningresikojatuh6bulan) ? $skriningresikojatuh6bulan : "",
    'tb' => isset($tinggi_badan) ? $tinggi_badan : "",
    'bb' => isset($berat_badan) ? $berat_badan : "",
    'penurunan_bb' => isset($post['komponenpenilaian']) ? $post['komponenpenilaian'] : "",
    'total_penurunan_bb' => isset($post['totalpenurunanbb']) ? $post['totalpenurunanbb'] : "",
    'asupan_berkurang' => isset($post['komponenpenilaianasupan']) ? $post['komponenpenilaianasupan'] : "",
    'total_asupan_berkurang' => isset($post['totalpenurunanasupan']) ? $post['totalpenurunanasupan'] : "",
    'total_komponen_penilaian' => isset($post['total_skor_komponen_penilaian']) ? $post['total_skor_komponen_penilaian'] : "",

                    // Anyelir
    'skala_nyeri' => isset($post['skala_nyeri']) ? $post['skala_nyeri'] : "",
    'skala_lelah' => isset($post['skala_lelah']) ? $post['skala_lelah'] : "",
    'skala_mual' => isset($post['skala_mual']) ? $post['skala_mual'] : "",
    'skala_depresi' => isset($post['skala_depresi']) ? $post['skala_depresi'] : "",
    'skala_cemas' => isset($post['skala_cemas']) ? $post['skala_cemas'] : "",
    'skala_mengantuk' => isset($post['skala_mengantuk']) ? $post['skala_mengantuk'] : "",
    'skala_nafsu_makan' => isset($post['skala_nafsuMakan']) ? $post['skala_nafsuMakan'] : "",
    'skala_sehat' => isset($post['skala_sehat']) ? $post['skala_sehat'] : "",
    'skala_sesak_napas' => isset($post['skala_sesakNapas']) ? $post['skala_sesakNapas'] : "",
    'skala_masalah' => isset($post['skala_masalah']) ? $post['skala_masalah'] : "",

                    //Anak
    'lingkar_kepala' => isset($post['lingkarKepala']) ? $post['lingkarKepala'] : "",
    'bentuk_kepala' => isset($post['bentuk_kepala_anak']) ? $post['bentuk_kepala_anak'] : "",
    'isi_kelainan_kepala' => isset($post['bentuk_kepala_anak_desk']) ? json_encode($post['bentuk_kepala_anak_desk']) : "",
    'irama' => isset($post['irama_pernapasan']) ? $post['irama_pernapasan'] : "",
    'reaksi_dada' => isset($post['retraksi_dada']) ? $post['retraksi_dada'] : "",
    'alat_bantu_napas' => isset($post['alat_bantu_napas_anak']) ? $post['alat_bantu_napas_anak'] : "",
    'bantu_napas_lain' => isset($post['alat_bantu_napas_desk']) ? json_encode($post['alat_bantu_napas_desk']) : "",
    'sianosis' => isset($post['sianosis']) ? $post['sianosis'] : "",
    'pucat' => isset($post['pucat']) ? $post['pucat'] : "",
                    // 'pembelajaran_pasien' => isset($post['kebutuhanPembelajaranRad']) ? json_encode($post['kebutuhanPembelajaranRad']) : "",
    'capillary_refill_test' => isset($post['capillary_refill_test']) ? $post['capillary_refill_test'] : "",

    'akral' => isset($post['akral']) ? $post['akral'] : "",
    'pembesaran_kelenjar' => isset($post['pembesaran_kelenjar_getah_bening_anak']) ? $post['pembesaran_kelenjar_getah_bening_anak'] : "",
    'isi_pembesar_kelenjar' => isset($post['pembesaran_kelenjar_getah_bening_anak_desk']) ? json_encode($post['pembesaran_kelenjar_getah_bening_anak_desk']) : "",
    'gangguan_neurologi' => isset($post['gangguan_neurologi']) ? $post['gangguan_neurologi'] : "",
    'mata' => isset($post['neurologi_mata_anak']) ? $post['neurologi_mata_anak'] : "",
    'isi_abnormal' => isset($post['neurologi_mata_anak_desk']) ? json_encode($post['neurologi_mata_anak_desk']) : "",
    'mulut' => isset($post['mulut_anak']) ? $post['mulut_anak'] : "",
    'isi_mulut' => isset($post['mulut_anak_desk']) ? json_encode($post['mulut_anak_desk']) : "",
    'abdomen' => isset($post['abdomen_anak']) ? $post['abdomen_anak'] : "",
    'isi_abdomen' => isset($post['abdomen_anak_desk']) ? json_encode($post['abdomen_anak_desk']) : "",

    'asites' => isset($post['asites_anak']) ? $post['asites_anak'] : "",
    'isi_asites' => isset($post['asites_anak_desk']) ? json_encode($post['asites_anak_desk']) : "",
    'defekasi' => isset($post['defekasi_anak']) ? $post['defekasi_anak'] : "",
                    // 'isi_defekasi' => isset($post['skala_depresi']) ? $post['skala_depresi'] : "",
    'fases' => isset($post['karakteristik_feses_anak']) ? $post['karakteristik_feses_anak'] : "",
    'fases_lain' => isset($post['karakteristik_feses_anak_desk']) ? json_encode($post['karakteristik_feses_anak_desk']) : "",
    'urin' => isset($post['urin']) ? $post['urin'] : "",
    'rectal' => isset($post['rectal_anak']) ? $post['rectal_anak'] : "",
    'isi_rectal' => isset($post['rectal_anak_desk']) ? json_encode($post['rectal_anak_desk']) : "",
    'genetalia' => isset($post['genetalia_anak']) ? $post['genetalia_anak'] : "",

    'isi_genetalia' => isset($post['genetalia_anak_desk']) ? json_encode($post['genetalia_anak_desk']) : "",
    'kulit' => isset($post['kulit']) ? $post['kulit'] : "",
    'warna_kulit' => isset($post['warna_kulit']) ? $post['warna_kulit'] : "",
    'luka' => isset($post['luka']) ? $post['luka'] : "",
    'lokasi_luka' => isset($post['lokasi_luka']) ? $post['lokasi_luka'] : "",
    'kelainan_tulang' => isset($post['kelainan_tulang_anak']) ? $post['kelainan_tulang_anak'] : "",
    'isi_kelainan_tulang' => isset($post['kelainan_tulang_anak_desk']) ? json_encode($post['kelainan_tulang_anak_desk']) : "",
    'gerakan_anak' => isset($post['gerakan_anak']) ? $post['gerakan_anak'] : "",
    'isi_gerakan_anak' => isset($post['gerakan_anak_desk']) ? json_encode($post['gerakan_anak_desk']) : "",
    'status_mental' => isset($post['status_mental']) ? json_encode($post['status_mental']) : "",

    'tempat_tinggal' => isset($post['tempat_tinggal']) ? $post['tempat_tinggal'] : "",
    'isi_tempat_tinggal' => isset($post['tempat_tinggal_desk']) ? json_encode($post['tempat_tinggal_desk']) : "",
    'pengasuh' => isset($post['pengasuh']) ? $post['pengasuh'] : "",
    'jenis_sekolah' => isset($post['jenis_sekolah']) ? $post['jenis_sekolah'] : "",
    'tampak_kurus' => isset($post['strongKidsKurus']) ? $post['strongKidsKurus'] : "",
    'penurunan_bb_anak' => isset($post['strongKidsTurunBerat']) ? $post['strongKidsTurunBerat'] : "",
    'kondisi' => isset($post['strongKidsKondisi']) ? $post['strongKidsKondisi'] : "",
    'resiko_malnutrisi' => isset($post['strongKidsMalnutrisi']) ? $post['strongKidsMalnutrisi'] : "",
);

$dataPerencanaanTindakan = array(
    'id_emr' => $getIdEmr,
    'jenis_pemeriksaan' => isset($post['jenis_pemeriksaan_rad']) ? $post['jenis_pemeriksaan_rad'] : "",
    'jenis_tindakan' => isset($post['tindakanRad']) ? $post['tindakanRad'] : "",
    'ber_obat_kontras' => isset($post['pemberiObatKontrasRad']) ? $post['pemberiObatKontrasRad'] : "",
    'dosis_berikan' => isset($post['dosisYangDiberikanRad']) ? $post['dosisYangDiberikanRad'] : "",
    'ket_dosis_berikan' => isset($post['dosisYangDiberikanLainnya_Rad']) ? $post['dosisYangDiberikanLainnya_Rad'] : "",
    'cara_pembe_ob' => isset($post['caraPemberianRad']) ? $post['caraPemberianRad'] : "",
    'ket_cara_pembe_ob' => isset($post['caraPemberianLainnya_Rad']) ? $post['caraPemberianLainnya_Rad'] : "",
    'pemeriksaan_fungsi_ginjal' => isset($post['pemeriksaanFungsiGinjalRad']) ? $post['pemeriksaanFungsiGinjalRad'] : "",
    'ket_pemeriksaan_fungsi_ginjal' => isset($post['pemeriksaanFungsiGinjalLainnya_Rad']) ? $post['pemeriksaanFungsiGinjalLainnya_Rad'] : "",
    'pemeriksaan_guldar' => isset($post['pemeriksaanGulaDarahRad']) ? $post['pemeriksaanGulaDarahRad'] : "",
    'ket_pemeriksaan_guldar' => isset($post['pemeriksaanGulaDarahLainnya_Rad']) ? $post['pemeriksaanGulaDarahLainnya_Rad'] : "",
    'pemasangan_mon_sat' => isset($post['pemasanganMonitorSaturasiRad']) ? $post['pemasanganMonitorSaturasiRad'] : "",
    'ket_pemasangan_mon_sat' => isset($post['pemasanganMonitorSaturasiLainnya_Rad']) ? $post['pemasanganMonitorSaturasiLainnya_Rad'] : "",
    'pemberian_oksigenasi' => isset($post['pemberianOksigenasiRad']) ? $post['pemberianOksigenasiRad'] : "",
    'ket_pemberian_oksigenasi' => isset($post['pemberianOksigenasiLainnya_Rad']) ? $post['pemberianOksigenasiLainnya_Rad'] : "",
    'tindakan_anestesi_sed' => isset($post['tindakanAnestesiSedasiRad']) ? $post['tindakanAnestesiSedasiRad'] : "",
    'ket_tindakan_anestesi_sed' => isset($post['tindakanAnestesiSedasiLainnyaRad']) ? $post['tindakanAnestesiSedasiLainnyaRad'] : "",
);

$alatbantu = $this->input->post("alatbantu_edit");
$alatbantu_lainnya = $this->input->post("alatbantu_lainnya_edit");

$dataAlatBantu = array();
$indexAlatBantu = 0;
if (isset($alatbantu)) {
    foreach ($alatbantu as $input) {
        if ($alatbantu[$indexAlatBantu] != "") {
            array_push(
                $dataAlatBantu, array(
                    'id_emr' => $getIdEmr,
                    'id_variabel' => $alatbantu[$indexAlatBantu],
                    'keterangan' => isset($alatbantu_lainnya) ? ($alatbantu[$indexAlatBantu] == 58 ? $alatbantu_lainnya : "") : "",
                )
            );
        }
        $indexAlatBantu++;
    }
}

$tingkat_pendidikan = $this->input->post("tingkat_pendidikan_edit");
$bahasa_sehari_hari = $this->input->post("bahasa_sehari_hari_edit");
$bahasa_lainnya = $this->input->post("bahasa_lainnya_edit");
$perlu_penerjemah = $this->input->post("perlu_penerjemah_edit");
$penerjemah_lainnya = $this->input->post("penerjemah_lainnya_edit");
$kesedian_informasi = $this->input->post("kesedian_informasi_edit");
$bahasa_daerah = $this->input->post("bahasa_daerah_edit");

$dataEdukasiKeperawatan = array(
    'id_emr' => $getIdEmr,
    'tingkat_pendidikan' => isset($tingkat_pendidikan) ? $tingkat_pendidikan : "",
    'bahasa' => isset($bahasa_sehari_hari) ? $bahasa_sehari_hari : "",
    'bahasa_lain' => isset($bahasa_lainnya) ? $bahasa_lainnya : "",
    'bahasa_daerah' => isset($bahasa_daerah) ? $bahasa_daerah : "",
    'penerjemah' => isset($perlu_penerjemah) ? $perlu_penerjemah : "",
    'penerjemah_lain' => isset($penerjemah_lainnya) ? $penerjemah_lainnya : "",
    'informasi' => isset($kesedian_informasi) ? $kesedian_informasi : "",
);

$hambatan = $this->input->post("hambatan_edit");
$hambatan_lainnya = $this->input->post("hambatan_lainnya_edit");

$dataHambatan = array();
$indexHambatan = 0;
if (isset($hambatan)) {
    foreach ($hambatan as $input) {
        if ($hambatan[$indexHambatan] != "") {
            array_push(
                $dataHambatan, array(
                    'id_emr' => $getIdEmr,
                    'id_variabel' => $hambatan[$indexHambatan],
                    'keterangan' => isset($hambatan_lainnya) ? ($hambatan[$indexHambatan] == 106 ? $hambatan_lainnya : "") : "",
                )
            );
        }
        $indexHambatan++;
    }
}

$kebutuhan_pembelajaran = $this->input->post("kebutuhan_pembelajaran_edit");
$kebutuhan_pembelajaran_lainnya = $this->input->post("kebutuhan_pembelajaran_lainnya_edit");

$dataKebutuhanPembelajaran = array();
$indexKebutuhanPembelajaran = 0;
if (isset($kebutuhan_pembelajaran)) {
    foreach ($kebutuhan_pembelajaran as $input) {
        if ($kebutuhan_pembelajaran[$indexKebutuhanPembelajaran] != "") {
            array_push(
                $dataKebutuhanPembelajaran, array(
                    'id_emr' => $getIdEmr,
                    'id_variabel' => $kebutuhan_pembelajaran[$indexKebutuhanPembelajaran],
                    'keterangan' => isset($kebutuhan_pembelajaran_lainnya) ? ($kebutuhan_pembelajaran[$indexKebutuhanPembelajaran] == 104 ? $kebutuhan_pembelajaran_lainnya : "") : "",
                )
            );
        }
        $indexKebutuhanPembelajaran++;
    }
}

$skrining_nyeri = $this->input->post("skrining_nyeri_edit");
$farmakologi = $this->input->post("farmakologi_edit");
$non_farmakologi = $this->input->post("non_farmakologi_edit");
$efek_samping = $this->input->post("efek_samping_edit");
$efek_samping_lain = $this->input->post("efek_samping_lain_edit");
$skor_nyeri_TIR = $this->input->post("skor_nyeri_TIR_edit");

$dataPemantauanNyeri = array(
    'id_emr' => $getIdEmr,
    'metode' => isset($skrining_nyeri) ? $skrining_nyeri : "",
    'skor' => isset($skor_nyeri_TIR) ? $skor_nyeri_TIR : "",
    'farmakologi' => $farmakologi,
    'non_farmakologi' => $non_farmakologi,
    'efek_samping' => isset($efek_samping) ? $efek_samping : "",
    'keterangan_efek_samping' => isset($efek_samping_lain) ? $efek_samping_lain : "",
);

$dataKunjunganigd = array(
    'id_emr' => $getIdEmr,
    'jenis_kunjungan' => isset($post['kunjungan']) ? $post['kunjungan'] : "",
    'rujukan' => isset($post['jenis_kunjungan_desc']) ? $post['jenis_kunjungan_desc'] : "",
    'nama_pengantar' => isset($post['nama_pengantar']) ? $post['nama_pengantar'] : "",
    'dibawa_dengan' => isset($post['dibawa_dengan']) ? $post['dibawa_dengan'] : "",
    'hubungan_pasien' => isset($post['hubungan_pasien']) ? $post['hubungan_pasien'] : "",
);

$berat_badan = $this->input->post("berat_badan_edit");

$dataRiwayatKelahiran = array(
    'id_emr' => $getIdEmr,
    'anak_ke' => isset($post['anak_ke']) ? $post['anak_ke'] : "",
    'saudara' => isset($post['jml_saudara']) ? $post['jml_saudara'] : "",
    'cara_lahir' => isset($post['cara_kelahiran']) ? $post['cara_kelahiran'] : "",
    'kondisi_lahir' => isset($post['kondisi_lahir']) ? $post['kondisi_lahir'] : "",
    'berat_badan' => isset($berat_badan) ? $berat_badan : "",
    'panjang_badan' => isset($post['panjang_badan']) ? $post['panjang_badan'] : "",
    'lingkar_kepala' => isset($post['lingkar_kepala_lahir']) ? $post['lingkar_kepala_lahir'] : "",
    'kelainan_bawaan' => isset($post['kelainan_bawaan_anak']) ? $post['kelainan_bawaan_anak'] : "",
    'isi_kelainan' => isset($post['kelainan_bawaan_anak_desk']) ? json_encode($post['kelainan_bawaan_anak_desk']) : "",
    'imunisasi' => isset($post['riwayat_imunisasi_dasar_anak']) ? $post['riwayat_imunisasi_dasar_anak'] : "",
    'isi_imunisasi' => isset($post['riwayat_imunisasi_dasar_anak_desk']) ? json_encode($post['riwayat_imunisasi_dasar_anak_desk']) : "",
    'riwayat_tumbuh_kembang' => isset($post['riwayat_tumbuh_kembang']) ? $post['riwayat_tumbuh_kembang'] : "",
    'tengkurap' => isset($post['usia_tengkurap']) ? $post['usia_tengkurap'] : "",
    'berjalan' => isset($post['usia_berjalan']) ? $post['usia_berjalan'] : "",
    'duduk' => isset($post['usia_duduk']) ? $post['usia_duduk'] : "",
    'bicara' => isset($post['usia_bicara']) ? $post['usia_bicara'] : "",
    'berdiri' => isset($post['usia_berdiri']) ? $post['usia_berdiri'] : "",
    'tumbuh_gigi' => isset($post['usia_tumbuh_gigi']) ? $post['usia_tumbuh_gigi'] : "",
);

$nopen = $this->input->post("nopen_edit");
$nokun = $this->input->post("nokun_edit");

$dataMedis = array(
    'id_emr' => $getIdEmr,
    'nopen' => $nopen,
    'nokun' => $nokun,
    'jenis' => 2,
    'flag' => '0',
);

if (!empty($getIdEmr)) {
    $this->db->replace('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
    $this->db->replace('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanKesehatan);
    $this->db->replace('keperawatan.tb_perencanaan_tindakan', $dataPerencanaanTindakan);
    $this->db->replace('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
    $this->db->replace('keperawatan.tb_riwayat_kelahiran', $dataRiwayatKelahiran);
    $this->db->replace('keperawatan.tb_anamnesa_perawat', $dataAnamnesisKeperawatan);
    if (isset($skrining_nyeri)) {
        if ($skrining_nyeri != 17) {
            $this->db->replace('keperawatan.tb_pemantauan_nyeri', $dataPemantauanNyeri);
        }
    }

    $this->db->delete('keperawatan.tb_hambatan', array('id_emr' => $getIdEmr));
    $this->db->delete('keperawatan.tb_kebutuhan_pembelajaran', array('id_emr' => $getIdEmr));
    $this->db->delete('keperawatan.tb_alat_bantu', array('id_emr' => $getIdEmr));

    foreach ($dataKebutuhanPembelajaran as $key => $value) {
        $this->db->replace('keperawatan.tb_kebutuhan_pembelajaran', $value, 'id_emr');
    }

    foreach ($dataHambatan as $key => $value) {
        $this->db->replace('keperawatan.tb_hambatan', $value, 'id_emr');
    }

    foreach ($dataAlatBantu as $key => $value) {
        $this->db->replace('keperawatan.tb_alat_bantu', $value, 'id_emr');
    }

    $this->db->delete('keperawatan.tb_perencanaan_asuhan_keperawatan', array('id_emr' => $getIdEmr));
    $dataAsuhanKeperawatan = array();
    $index = 0;
    $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
    if (isset($post['asuhanKeperawatan'])) {
        foreach ($post['asuhanKeperawatan'] as $input) {
            if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                    $dataAsuhanKeperawatan, array(
                        'id_emr' => $getIdEmr,
                        'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                        'lain_lain' => isset($post[$id]) ? $post[$id] : null
                    )
                );
            }
            $index++;
        }
        $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
    }
    if ($this->db->replace('keperawatan.tb_keperawatan', $dataKeperawatan)) {
        $result = array('status' => 'success', 'pesan' => 'ubah');
    }
} else {
    $result = array('status' => 'failed');
    $this->db->insert('medis.tb_medis', $dataMedis);
    $this->db->insert('keperawatan.tb_riwayat_kesehatan', $dataRiwayatKesehatan);
    $this->db->insert('keperawatan.tb_pemeriksaan_fisik', $dataPemeriksaanKesehatan);
    $this->db->insert('keperawatan.tb_perencanaan_tindakan', $dataPerencanaanTindakan);
    $this->db->insert('keperawatan.tb_edukasi_keperawatan', $dataEdukasiKeperawatan);
    $this->db->insert('keperawatan.tb_riwayat_kelahiran', $dataRiwayatKelahiran);
    $this->db->insert('keperawatan.tb_anamnesa_perawat', $dataAnamnesisKeperawatan);
    if (isset($post['alatbantu'])) {
        $this->db->insert_batch('keperawatan.tb_alat_bantu', $dataAlatBantu);
    }
    if (isset($post['hambatan'])) {
        $this->db->insert_batch('keperawatan.tb_hambatan', $dataHambatan);
    }
    if (isset($post['kebutuhan_pembelajaran'])) {
        $this->db->insert_batch('keperawatan.tb_kebutuhan_pembelajaran', $dataKebutuhanPembelajaran);
    }
    if (isset($post['kunjungan'])) {
        $this->db->insert('keperawatan.tb_kunjungan_igd', $dataKunjunganigd);
    }
    if (isset($skrining_nyeri)) {
        if ($skrining_nyeri != 17) {
            $this->db->insert('keperawatan.tb_pemantauan_nyeri', $dataPemantauanNyeri);
        }
    }

    $dataAsuhanKeperawatan = array();
    $index = 0;
    $lain = array(170, 180, 265, 286, 291, 299, 321, 329, 353, 374, 403, 407, 430, 436, 459, 465, 494, 574, 607, 632, 690, 695, 721, 749, 766, 785, 171, 173, 174);
    if (isset($post['asuhanKeperawatan'])) {
        foreach ($post['asuhanKeperawatan'] as $input) {
            if ($post['asuhanKeperawatan'][$index] != "") {
                $id = "asuhanLainya" . $post['asuhanKeperawatan'][$index];
                array_push(
                    $dataAsuhanKeperawatan, array(
                        'id_emr' => $getIdEmr,
                        'id_asuhan_keperawatan_detil' => $post['asuhanKeperawatan'][$index],
                        'lain_lain' => isset($post[$id]) ? $post[$id] : null
                    )
                );
            }
            $index++;
        }
        $this->db->insert_batch('keperawatan.tb_perencanaan_asuhan_keperawatan', $dataAsuhanKeperawatan);
    }
    if ($this->db->insert('keperawatan.tb_keperawatan', $dataKeperawatan)) {
        $result = array('status' => 'success');
    }
}

echo json_encode($result);
}
}
}

}

/* End of file tindakanInvasif.php */
/* Location: ./application/controllers/pengkajianProRad/tindakanInvasif.php */
