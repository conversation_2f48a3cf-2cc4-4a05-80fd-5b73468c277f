<?php
defined('BASEPATH') or exit('No direct script access allowed');

class OTKeperawatan extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');

    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'FormulirTriaseModel',
        'rekam_medis/TbBbModel',
        'rekam_medis/KesadaranModel',
        'rekam_medis/TandaVitalModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
        'rekam_medis/rawat_inap/keperawatan/EWSModel',
      )
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $idEmr = isset($this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr) ? $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr : null;
    $getPengkajianRawatInap = $this->pengkajianAwalModel->getPengkajianRawatInap($idEmr);
    $idAsuhanKeperawatan = explode('","', trim($getPengkajianRawatInap['ID_ASUHAN_KEPERAWATAN'], '["]'));
    $jmlAsuhanKeperawatan = count($idAsuhanKeperawatan);
    $tindakanKeperawatan = array();

    // Ambil tindakan keperawatan
    for ($i = 0; $i < $jmlAsuhanKeperawatan; $i++) {
      foreach ($idAsuhanKeperawatan as $idak) {
        $tindakanKeperawatan[$idak] = $this->OTKeperawatanModel->tindakanKeperawatan($idak);
      }
    }

    $data = array(
      'nokun' => $nokun,
      'pasien' => $pasien,
      'idEmr' => $idEmr,
      'getPengkajianRawatInap' => $getPengkajianRawatInap,
      'getPengkajian' => $this->pengkajianAwalModel->getPengkajian($pasien['NORM']),
      'vitalTriaseTerbaru' => $this->FormulirTriaseModel->vitalTriaseTerbaru($nokun),
      'pilihanCPPT' => $this->masterModel->referensi(1407),
      'kesadaran' => $this->masterModel->referensi(5),
      'ews' => $this->masterModel->referensi(1162),
      'jenisBAB' => $this->masterModel->referensi(1691),
      'dinas' => $this->masterModel->referensi(654),
      'intervensi' => $this->OTKeperawatanModel->intervensi($idEmr),
      'idAsuhanKeperawatan' => $idAsuhanKeperawatan,
      'tindakanKeperawatan' => $tindakanKeperawatan,
      'penggunaan' => $this->masterModel->referensi(129),
      'history' => $this->pengkajianAwalModel->historyPAK($pasien['NORM']),
    );
    if ($pasien['ID_RUANGAN'] == '105020101' || $pasien['ID_RUANGAN'] == '105020102') {
      $data['tindakanKeperawatanAnyelir'] = $this->masterModel->referensi(631);
    }
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/index', $data);
  }

  public function simpan($param)
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();

    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah') {
        $rules = $this->OTKeperawatanModel->rules;
        $this->form_validation->set_rules($rules);

        if ($this->form_validation->run() == true) {
          $dataSource = 5;
          $nokun = isset($post['nokun']) ? $post['nokun'] : null;
          $nomr = isset($post['nomr']) ? $post['nomr'] : null;
          $tanggal = isset($post['tanggal']) ? $post['tanggal'] : null;
          $jam = isset($post['jam']) ? $post['jam'] : null;
          $kesadaran = isset($post['kesadaran']) ? $post['kesadaran'] : null;
          $ews = isset($post['ews']) ? $post['ews'] : null;
          $tdSistolik = isset($post['td_sistolik']) ? round($post['td_sistolik'], 2) : null;
          $nadi = isset($post['nadi']) ? round($post['nadi'], 2) : null;
          $pernapasan = isset($post['pernapasan']) ? round($post['pernapasan'], 2) : null;
          $suhu = isset($post['suhu']) ? round($post['suhu'], 2) : null;
          $saturasiO2 = isset($post['saturasi_o2']) ? round($post['saturasi_o2'], 2) : null;
          $penggunaanO2 = isset($post['penggunaan_o2']) ? $post['penggunaan_o2'] : null;
          $tb = isset($post['tb']) ? round($post['tb'], 2) : null;
          $bb = isset($post['bb']) ? round($post['bb'], 2) : null;
          $balance = null;
          $oleh = $this->session->userdata['id'];
          $status = 1;

          // Mulai pemasukan
          $oral = isset($post['oral']) ? round($post['oral'], 2) : null;
          $oral2 = isset($post['oral_2']) ? round($post['oral_2'], 2) : null;
          $oral3 = isset($post['oral_3']) ? round($post['oral_3'], 2) : null;
          $ngtPemasukan = isset($post['ngt_pemasukan']) ? round($post['ngt_pemasukan'], 2) : null;
          $ngtPemasukan2 = isset($post['ngt_pemasukan_2']) ? round($post['ngt_pemasukan_2'], 2) : null;
          $ngtPemasukan3 = isset($post['ngt_pemasukan_3']) ? round($post['ngt_pemasukan_3'], 2) : null;
          $transfusi = isset($post['transfusi']) ? round($post['transfusi'], 2) : null;
          $transfusi2 = isset($post['transfusi_2']) ? round($post['transfusi_2'], 2) : null;
          $transfusi3 = isset($post['transfusi_3']) ? round($post['transfusi_3'], 2) : null;
          $parenteral = isset($post['parenteral']) ? round($post['parenteral'], 2) : null;
          $parenteral2 = isset($post['parenteral_2']) ? round($post['parenteral_2'], 2) : null;
          $parenteral3 = isset($post['parenteral_3']) ? round($post['parenteral_3'], 2) : null;
          $parenteral4 = isset($post['parenteral_4']) ? round($post['parenteral_4'], 2) : null;
          $parenteral5 = isset($post['parenteral_5']) ? round($post['parenteral_5'], 2) : null;
          // Akhir pemasukan

          // Mulai pengeluaran
          $muntah = isset($post['muntah']) ? round($post['muntah'], 2) : null;
          $ngtPengeluaran = isset($post['ngt_pengeluaran']) ? round($post['ngt_pengeluaran'], 2) : null;
          $bak = isset($post['bak']) ? round($post['bak'], 2) : null;
          $jenisBab = isset($post['jenis_bab']) ? $post['jenis_bab'] : null;
          $bab = null;
          if ($jenisBab == 5553) {
            // Jika jenis BAB padat, maka ambil input BAB padat
            $bab = isset($post['bab_padat']) ? round($post['bab_padat'], 2) : null;
          } elseif ($jenisBab == 5554) {
            // Jika jenis BAB cair, maka ambil input BAB cair
            $bab = isset($post['bab_cair']) ? round($post['bab_cair'], 2) : null;
          }
          $pendarahan = isset($post['pendarahan']) ? round($post['pendarahan'], 2) : null;
          $wsdDrain = isset($post['wsd_drain']) ? round($post['wsd_drain'], 2) : null;
          $wsdDrain2 = isset($post['wsd_drain_2']) ? round($post['wsd_drain_2'], 2) : null;
          $wsdDrain3 = isset($post['wsd_drain_3']) ? round($post['wsd_drain_3'], 2) : null;
          $wsdDrain4 = isset($post['wsd_drain_4']) ? round($post['wsd_drain_4'], 2) : null;
          $iwl = isset($post['iwl']) ? round($post['iwl'], 2) : null;
          // Akhir pengeluaran

          // Mulai balance
          if (!empty($post['balance'])) {
            $balance = round($post['balance'], 2);
          } else {
            $pemasukan = $oral + $oral2 + $oral3 + $ngtPemasukan + $ngtPemasukan2 + $ngtPemasukan3 + $transfusi + $transfusi2 + $transfusi3 + $parenteral + $parenteral2 + $parenteral3 + $parenteral4 + $parenteral5;
            if ($jenisBab == 5553) {
              // Jika jenis BAB padat, maka BAB tidak dihitung
              $pengeluaran = $muntah + $ngtPengeluaran + $bak + $pendarahan + $wsdDrain + $wsdDrain2 + $wsdDrain3 + $wsdDrain4;
            } elseif ($jenisBab == 5554) {
              // Jika jenis BAB cair, maka BAB dihitung
              $pengeluaran = $muntah + $ngtPengeluaran + $bak + $bab + $pendarahan + $wsdDrain + $wsdDrain2 + $wsdDrain3 + $wsdDrain4;
            }
            $hitung = $pemasukan - ($pengeluaran + $iwl);
            $balance = round($hitung, 2);
          }
          // echo '<pre>';print_r($balance);exit();
          // Akhir balance

          // Simpan ke Observasi
          $dataObservasi = array(
            'nokun' => $nokun,
            'balance_24_jam' => isset($post['balance_24_jam']) ? $post['balance_24_jam'] : null,
            'tanggal' => $tanggal,
            'jam' => $jam,
            'oleh' => $oleh,
            'imt' => isset($post['imt']) ? round($post['imt'], 2) : round(($bb / ($tb / 100 * $tb / 100)), 2),
            'lingkar_perut' => isset($post['lingkar_perut']) ? round($post['lingkar_perut'], 2) : null,
            'cvp' => isset($post['cvp']) ? $post['cvp'] : null,
            'wsd' => isset($post['wsd']) ? $post['wsd'] : null,
            'perifer' => isset($post['perifer']) ? $post['perifer'] : null,
            'oral' => $oral,
            'ket_oral' => isset($post['ket_oral']) ? $post['ket_oral'] : null,
            'oral_2' => $oral2,
            'ket_oral_2' => isset($post['ket_oral_2']) ? $post['ket_oral_2'] : null,
            'oral_3' => $oral3,
            'ket_oral_3' => isset($post['ket_oral_3']) ? $post['ket_oral_3'] : null,
            'ngt_pemasukan' => $ngtPemasukan,
            'ket_ngt_pemasukan' => isset($post['ket_ngt_pemasukan']) ? $post['ket_ngt_pemasukan'] : null,
            'ngt_pemasukan_2' => $ngtPemasukan2,
            'ket_ngt_pemasukan_2' => isset($post['ket_ngt_pemasukan_2']) ? $post['ket_ngt_pemasukan_2'] : null,
            'ngt_pemasukan_3' => $ngtPemasukan3,
            'ket_ngt_pemasukan_3' => isset($post['ket_ngt_pemasukan_3']) ? $post['ket_ngt_pemasukan_3'] : null,
            'transfusi' => $transfusi,
            'ket_transfusi' => isset($post['ket_transfusi']) ? $post['ket_transfusi'] : null,
            'transfusi_2' => $transfusi2,
            'ket_transfusi_2' => isset($post['ket_transfusi_2']) ? $post['ket_transfusi_2'] : null,
            'transfusi_3' => $transfusi3,
            'ket_transfusi_3' => isset($post['ket_transfusi_3']) ? $post['ket_transfusi_3'] : null,
            'parenteral' => $parenteral,
            'ket_parenteral' => isset($post['ket_parenteral']) ? $post['ket_parenteral'] : null,
            'parenteral_2' => $parenteral2,
            'ket_parenteral_2' => isset($post['ket_parenteral_2']) ? $post['ket_parenteral_2'] : null,
            'parenteral_3' => $parenteral3,
            'ket_parenteral_3' => isset($post['ket_parenteral_3']) ? $post['ket_parenteral_3'] : null,
            'parenteral_4' => $parenteral4,
            'ket_parenteral_4' => isset($post['ket_parenteral_4']) ? $post['ket_parenteral_4'] : null,
            'parenteral_5' => $parenteral5,
            'ket_parenteral_5' => isset($post['ket_parenteral_5']) ? $post['ket_parenteral_5'] : null,
            'muntah' => $muntah,
            'ket_muntah' => isset($post['ket_muntah']) ? $post['ket_muntah'] : null,
            'ngt_pengeluaran' => $ngtPengeluaran,
            'ket_ngt_pengeluaran' => isset($post['ket_ngt_pengeluaran']) ? $post['ket_ngt_pengeluaran'] : null,
            'bak' => $bak,
            'ket_bak' => isset($post['ket_bak']) ? $post['ket_bak'] : null,
            'jenis_bab' => $jenisBab,
            'bab' => $bab,
            'ket_bab' => isset($post['ket_bab']) ? $post['ket_bab'] : null,
            'pendarahan' => $pendarahan,
            'ket_pendarahan' => isset($post['ket_pendarahan']) ? $post['ket_pendarahan'] : null,
            'wsd_drain' => $wsdDrain,
            'ket_wsd_drain' => isset($post['ket_wsd_drain']) ? $post['ket_wsd_drain'] : null,
            'wsd_drain_2' => $wsdDrain2,
            'ket_wsd_drain_2' => isset($post['ket_wsd_drain_2']) ? $post['ket_wsd_drain_2'] : null,
            'wsd_drain_3' => $wsdDrain3,
            'ket_wsd_drain_3' => isset($post['ket_wsd_drain_3']) ? $post['ket_wsd_drain_3'] : null,
            'wsd_drain_4' => $wsdDrain4,
            'ket_wsd_drain_4' => isset($post['ket_wsd_drain_4']) ? $post['ket_wsd_drain_4'] : null,
            'dinas' => isset($post['dinas']) ? $post['dinas'] : null,
            'jam_dinas' => isset($post['jam_dinas']) ? $post['jam_dinas'] : null,
            'tindakan_keperawatan' => isset($post['tindakan_keperawatan_lainnya']) ? $post['tindakan_keperawatan_lainnya'] : null,
            'waktu_tindakan_keperawatan_lainnya' => isset($post['waktu_tindakan_keperawatan_lainnya']) ? $post['waktu_tindakan_keperawatan_lainnya'] : null,
            'iwl' => $iwl,
            'balance' => $balance,
            'igds' => isset($post['igds']) ? $post['igds'] : null,
            'lainnya' => isset($post['lainnya']) ? $post['lainnya'] : null,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataObservasi);exit();
          $idObservasi = $this->OTKeperawatanModel->simpanObservasi($dataObservasi);

          // Simpan ke Kesadaran
          $dataKesadaran = array(
            'data_source' => $dataSource,
            'id_otk' => $idObservasi,
            'nokun' => $nokun,
            'nomr' => $nomr,
            'kesadaran' => $kesadaran,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataKesadaran);exit();
          $idKesadaran = $this->OTKeperawatanModel->simpanKesadaran($dataKesadaran);

          // Simpan ke Tanda Vital
          $dataTandaVital = array(
            'data_source' => $dataSource,
            'id_otk' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'td_sistolik' => $tdSistolik,
            'td_diastolik' => isset($post['td_diastolik']) ? round($post['td_diastolik'], 2) : null,
            'nadi' => $nadi,
            'pernapasan' => $pernapasan,
            'suhu' => $suhu,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataTandaVital);exit();
          $idTandaVital = $this->OTKeperawatanModel->simpanTandaVital($dataTandaVital);

          // Simpan ke Tinggi dan Berat Badan
          $data = array(
            'data_source' => $dataSource,
            'id_otk' => $idObservasi,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'tb' => $tb,
            'bb' => $bb,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($data);exit();
          $this->TbBbModel->insert($data);

          // Simpan ke Intervensi
          $indexIntervensi = 0;
          $dataIntervensi = array();
          if (isset($post['intervensi'])) {
            foreach ($post['intervensi'] as $i) {
              $dataIntervensi[$indexIntervensi] = array(
                'id_observasi_tindakan' => $idObservasi,
                'id_intervensi' => $i,
                'status' => $status,
              );
              $indexIntervensi++;
            }
            // echo '<pre>';print_r($dataIntervensi);exit();
            $this->OTKeperawatanModel->simpanIntervensi($dataIntervensi, $idObservasi);
          }

          // Simpan ke Tindakan
          $indexTindakan = 0;
          $dataTindakan = array();
          if (isset($post['tindakan_keperawatan'])) {
            foreach ($post['tindakan_keperawatan'] as $tk) {
              $dataTindakan[$indexTindakan] = array(
                'id_observasi_tindakan' => $idObservasi,
                'id_pak' => $post['tindakan_keperawatan'][$indexTindakan],
                'waktu' => $post['waktu_tindakan_keperawatan'][$indexTindakan],
                'status' => $status,
              );
              $indexTindakan++;
            }
            // echo '<pre>';print_r($dataTindakan);exit();
            $this->OTKeperawatanModel->simpanTindakan($dataTindakan, $idObservasi);
          }

          // Simpan ke Anyelir
          if ($post['id_ruang'] == '105020101') {
            $indexAnyelir = 0;
            $dataAnyelir = array();
            if (isset($post['tindakan_keperawatan_anyelir'])) {
              foreach ($post['tindakan_keperawatan_anyelir'] as $tka) {
                $dataAnyelir[$indexAnyelir] = array(
                  'id_observasi_tindakan' => $idObservasi,
                  'kunjungan' => $nokun,
                  'tanggal' => $tanggal,
                  'tindakan' => $tka,
                  'oleh' => $oleh,
                );
                $indexAnyelir++;
              }
            }
            // echo '<pre>';print_r($dataAnyelir);exit();
            $this->pengkajianAwalModel->insertBatchKeperawatanAnyelir($dataAnyelir, $idObservasi);
          }

          // Simpan ke Oksigen
          if ($ews == '3863') {
            $dataOksigen = array(
              'data_source' => $dataSource,
              'id_otk' => $idObservasi,
              'nomr' => $nomr,
              'nokun' => $nokun,
              'tanggal' => isset($post['tanggal_ews']) ? $post['tanggal_ews'] : null,
              'waktu' => isset($post['waktu_ews']) ? $post['waktu_ews'] : null,
              'saturasi_o2' => $saturasiO2,
              'penggunaan_o2' => $penggunaanO2,
              'oleh' => $oleh,
              'status' => $status,
            );
            // echo '<pre>';print_r($dataOksigen);exit();
            $idOksigen = $this->OTKeperawatanModel->simpanOksigen($dataOksigen);

            // Mulai penghitungan EWS
            if (isset($post['score_ews'])) {
              $totalSkor = $post['score_ews'];
            } else {
              // Penilaian Kesadaran
              $skorKesadaran = null;
              if ($kesadaran == 9) {
                $skorKesadaran = 0;
              } else {
                $skorKesadaran = 3;
              }

              // Penilaian Tekanan Darah Sistolik
              $skorSistolik = null;
              if ($tdSistolik >= 180) {
                $skorSistolik = 3;
              } elseif ($tdSistolik >= 170 && $tdSistolik <= 179.99) {
                $skorSistolik = 2;
              } elseif ($tdSistolik >= 150 && $tdSistolik <= 169.99) {
                $skorSistolik = 1;
              } elseif ($tdSistolik >= 101 && $tdSistolik <= 149.99) {
                $skorSistolik = 0;
              } elseif ($tdSistolik >= 81 && $tdSistolik <= 100.99) {
                $skorSistolik = 1;
              } elseif ($tdSistolik >= 71 && $tdSistolik <= 80.99) {
                $skorSistolik = 2;
              } elseif ($tdSistolik <= 70.99) {
                $skorSistolik = 3;
              }

              // Penilaian Nadi
              $skorNadi = null;
              if ($nadi >= 130) {
                $skorNadi = 3;
              } elseif ($nadi >= 111 && $nadi <= 129.99) {
                $skorNadi = 2;
              } elseif ($nadi >= 101 && $nadi <= 110.99) {
                $skorNadi = 1;
              } elseif ($nadi >= 60 && $nadi <= 100.99) {
                $skorNadi = 0;
              } elseif ($nadi >= 51 && $nadi <= 59.99) {
                $skorNadi = 1;
              } elseif ($nadi >= 40 && $nadi <= 50.99) {
                $skorNadi = 2;
              } elseif ($nadi < 40) {
                $skorNadi = 3;
              }

              // Penilaian Pernapasan
              $skorPernapasan = null;
              if ($pernapasan >= 25) {
                $skorPernapasan = 3;
              } elseif ($pernapasan >= 21 && $pernapasan <= 24.99) {
                $skorPernapasan = 2;
              } elseif ($pernapasan >= 12 && $pernapasan <= 20.99) {
                $skorPernapasan = 0;
              } elseif ($pernapasan >= 9 && $pernapasan <= 11.99) {
                $skorPernapasan = 1;
              } elseif ($pernapasan <= 8.99) {
                $skorPernapasan = 3;
              }

              // Penilaian Suhu
              $skorSuhu = null;
              if ($suhu >= 39) {
                $skorSuhu = 2;
              } elseif ($suhu >= 38 && $suhu <= 38.99) {
                $skorSuhu = 1;
              } elseif ($suhu >= 36 && $suhu <= 37.99) {
                $skorSuhu = 0;
              } elseif ($suhu <= 35.99) {
                $skorSuhu = 3;
              }

              // Penilaian Saturasi O2
              $skorSaturasiO2 = null;
              if ($saturasiO2 >= 96) {
                $skorSaturasiO2 = 0;
              } elseif ($saturasiO2 >= 94 && $saturasiO2 <= 95.99) {
                $skorSaturasiO2 = 1;
              } elseif ($saturasiO2 >= 92 && $saturasiO2 <= 93.99) {
                $skorSaturasiO2 = 2;
              } elseif ($saturasiO2 <= 91.99) {
                $skorSaturasiO2 = 3;
              }

              // Penilaian Penggunaan O2
              $skorPenggunaanO2 = null;
              if ($penggunaanO2 == 388) {
                $skorPenggunaanO2 = 2;
              } elseif ($penggunaanO2 == 389) {
                $skorPenggunaanO2 = 0;
              }

              // Total Skor EWS
              $totalSkor = $skorKesadaran + $skorSistolik + $skorNadi + $skorPernapasan + $skorSuhu + $skorSaturasiO2 + $skorPenggunaanO2;
            }
            // Akhir penghitungan EWS

            // Simpan ke EWS
            $dataEWS = array(
              'id_tanda_vital' => $idTandaVital,
              'id_kesadaran' => $idKesadaran,
              'id_o2' => $idOksigen,
              'data_source' => $dataSource,
              'tanggal' => isset($post['tanggal_ews']) ? $post['tanggal_ews'] : $tanggal,
              'jam' => isset($post['waktu_ews']) ? $post['waktu_ews'] : $tanggal,
              'score_ews' => $totalSkor,
              'id_otk' => $idObservasi,
              'nokun' => $nokun,
              'oleh' => $oleh,
              'status' => $status,
            );
            // echo '<pre>';print_r($dataEWS);exit();
            $this->EWSModel->simpanEWS($dataEWS);
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function balancePerHari()
  {
    $nomr = $this->input->post('nomr');
    $jenis = 5770;
    $data = array(
      'sidebar' => false,
      'jenis' => $jenis,
      'idTabel' => 'tabel-balance-ot-keperawatan',
      'balance' => $this->OTKeperawatanModel->balance($jenis, $nomr),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/balance', $data);
  }

  public function balancePerDinas()
  {
    $nomr = $this->input->post('nomr');
    $jenis = 5769;
    $data = array(
      'sidebar' => false,
      'jenis' => $jenis,
      'idTabel' => 'tabel-balance-ot-keperawatan',
      'balance' => $this->OTKeperawatanModel->balance($jenis, $nomr),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/balance', $data);
  }

  public function ambilBalance24Jam()
  {
    $post = $this->input->post();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $nokun = isset($post['nokun']) ? $post['nokun'] : null;
      $tanggalMulai = isset($post['tanggalMulai']) ? $post['tanggalMulai'] : null;
      $tanggalAkhir = isset($post['tanggalAkhir']) ? $post['tanggalAkhir'] : null;
      $result = $this->OTKeperawatanModel->balance('24 jam', null, $nokun, $tanggalMulai, $tanggalAkhir);
      echo json_encode(
        array(
          'status' => 'success',
          'data' => $result[0]
        )
      );
    }
  }

  public function historyKunjungan()
  {
    $nokun = $this->input->post('nokun');
    $jenisView = 1;
    if ($nokun == null) {
      $nokun = $this->uri->segment(8);
      $jenisView = 2;
    }
    $data = array(
      'nokun' => $nokun,
      'nomr' => null,
    );
    // echo '<pre>';print_r($data);exit();
    if ($jenisView == 1) {
      $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/history', $data);
    } elseif ($jenisView == 2) {
      $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/historySide', $data);
    }
  }

  public function historyTotal()
  {
    $nomr = $this->input->post('nomr');
    $data = array(
      'nokun' => null,
      'nomr' => $nomr,
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/history', $data);
  }

  public function history($param)
  {
    $post = $this->input->post();
    if ($param == 'kunjungan') {
      $data = array(
        'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
        'nomr' => null,
      );
    } elseif ($param == 'total') {
      $data = array(
        'nokun' => null,
        'nomr' => isset($post['nomr']) ? $post['nomr'] : null,
      );
    }
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/history', $data);
  }

  public function tabelHistory()
  {
    $post = $this->input->post();
    $nokun = isset($post['nokun']) || $post('nokun') != '' ? $post['nokun'] : null;
    $nomr = isset($post['nomr']) || $post('nomr') != '' ? $post['nomr'] : null;
    // echo '<pre>';print_r($nomr);exit();
    $history = $this->OTKeperawatanModel->ambilHistory($nokun, $nomr);
    $data = array();
    $no = $_POST['start'];
    $cvp = null;
    $lingkarPerut = null;
    $penggunaanO2 = null;
    $wsd = null;
    $satuanBAB = null;
    $pemasukan = null;
    $pengeluaran = null;
    $hitung = null;
    $balance = null;
    $balance24jam = null;
    $status = null;
    $disabled = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history as $h) {
      // Mulai periksa lingkar perut
      if (isset($h->cvp) && $h->cvp != '') {
        $lingkarPerut = floatval($h->lingkar_perut) . ' cm';
        $cvp = $h->cvp;
      } else {
        $lingkarPerut = '-';
        $cvp = '-';
      }
      // Akhir periksa lingkar perut

      // Mulai periksa penggunaan oksigen
      if ($h->penggunaan_o2 == 388) {
        $penggunaanO2 = 'Ya';
      } elseif ($h->penggunaan_o2 == 389) {
        $penggunaanO2 = 'Tidak';
      } else {
        $penggunaanO2 = '-';
      }
      // Akhir periksa penggunaan oksigen

      // Mulai periksa WSD
      if (isset($h->wsd) && $h->wsd != '') {
        $wsd = $h->wsd;
      } else {
        $wsd = '-';
      }
      // Akhir periksa WSD

      $pemasukan = $h->total_oral + $h->total_ngt_pemasukan + $h->total_transfusi + $h->total_parenteral;
      $pengeluaran = $h->muntah + $h->ngt_pengeluaran + $h->bak + $h->pendarahan + $h->total_wsd_drain;

      // Mulai periksa BAB
      if ($h->jenis_bab == 5553) {
        $satuanBAB = ' gr';
      } elseif ($h->jenis_bab == 5554) {
        $satuanBAB = ' cc';
      } else {
        $satuanBAB = null;
      }
      // Akhir periksa BAB

      // Mulai periksa balance
      if ($h->balance != null) {
        if ($h->jenis_bab == 5554) { // Jika jenis BAB cair, maka BAB dihitung
          $pengeluaran = $pengeluaran + $h->bab;
        }

        $hitung = $pemasukan - ($pengeluaran + $h->iwl);
        $balance = round($hitung, 2);
      } else {
        $balance = $h->balance;
      }
      // Akhir periksa balance

      // Mulai periksa balance 24 jam
      if (isset($h->balance_24_jam) && $h->balance_24_jam != null) {
        $balance24jam = "<i class='fa fa-check'></i>";
      } else {
        $balance24jam = '-';
      }
      // Akhir periksa balance 24 jam

      // Mulai periksa status
      if ($h->status == 0) {
        $status = "<p class='text-danger'>Dibatalkan</p>";
        $disabled = 'disabled';
      } elseif ($h->status == 1) {
        $status = "<p class='text-success'>Aktif</p>";
        $disabled = null;
      }
      // Akhir periksa status

      // Mulai data
      $row = array();
      $row[] = ++$no . '.';
      $row[] = date('d/m/Y', strtotime($h->tanggal));
      $row[] = date('H:i', strtotime($h->jam));
      $row[] = isset($h->kesadaran) ? $h->kesadaran : '-';
      $row[] = floatval($h->td_sistolik) . ' mmHg';
      $row[] = floatval($h->td_diastolik) . ' mmHg';
      $row[] = floatval($h->nadi) . ' ×/menit';
      $row[] = floatval($h->pernapasan) . ' ×/menit';
      $row[] = floatval($h->suhu) . ' °C';
      $row[] = $lingkarPerut;
      // Mulai EWS
      $row[] = isset($h->tanggal_ews) ? date('d/m/Y', strtotime($h->tanggal_ews)) : '-';
      $row[] = isset($h->waktu_ews) ? date('H:i', strtotime($h->waktu_ews)) : '-';
      $row[] = isset($h->saturasi_o2) ? floatval($h->saturasi_o2) . '%' : '-';
      $row[] = $penggunaanO2;
      $row[] = isset($h->score_ews) ? $h->score_ews : '-';
      // Akhir EWS
      $row[] = $cvp;
      $row[] = $wsd;
      // Mulai pemasukan
      $row[] = floatval($h->total_oral) . ' cc';
      $row[] = floatval($h->total_ngt_pemasukan) . ' cc';
      $row[] = floatval($h->total_transfusi) . ' cc';
      $row[] = floatval($h->total_parenteral) . ' cc';
      $row[] = $pemasukan . ' cc';
      // Akhir pemasukan
      // Mulai pengeluaran
      $row[] = floatval($h->muntah) . ' cc';
      $row[] = floatval($h->ngt_pengeluaran) . ' cc';
      $row[] = floatval($h->bak) . ' cc';
      $row[] = floatval($h->bab) . $satuanBAB;
      $row[] = floatval($h->pendarahan) . ' cc';
      $row[] = floatval($h->total_wsd_drain) . ' cc';
      $row[] = $pengeluaran . ' cc';
      // Akhir pengeluaran
      // Mulai dinas
      $row[] = isset($h->dinas) ? $h->dinas : '-';
      $row[] = date('H:i', strtotime($h->jam_dinas));
      // Akhir dinas
      $row[] = floatval($h->iwl) . ' cc';
      $row[] = $balance . ' cc';
      $row[] = $balance24jam;
      $row[] = $h->perawat;
      $row[] = $status;
      $row[] = "<div class='btn-group' role='group'>
                  <button type='button' href='#modal-batal-ot-keperawatan' class='btn btn-sm btn-danger waves-effect tbl-batal-ot-keperawatan' data-toggle='modal' data-id='" . $h->id . "' " . $disabled . ">
                    <i class='fa fa-window-close'></i> Batal
                  </button>
                  <button type='button' href='#modal-detail-ot-keperawatan' class='btn btn-sm btn-primary waves-effect tbl-detail-ot-keperawatan' data-toggle='modal' data-id='" . $h->id . "' " . $disabled . ">
                    <i class='fa fa-list'></i> Detail
                  </button>
                  <button type='button' href='#modal-cairan-ot-keperawatan' class='btn btn-sm btn-info waves-effect tbl-cairan-ot-keperawatan' data-toggle='modal' data-id='" . $h->id . "' " . $disabled . ">
                    <i class='fa fa-tint'></i> Detail Cairan
                  </button>
                </div>";
      $data[] = $row;
      // Akhir data
    }

    $output = array(
      'draw' => $_POST['draw'],
      'recordsTotal' => $this->OTKeperawatanModel->hitungSemuaHistory($nokun, $nomr),
      'recordsFiltered' => $this->OTKeperawatanModel->hitungTersaringHistory($nokun, $nomr),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function detail()
  {
    $id = $this->input->post('id');
    $data = array(
      'detailIntervensi' => $this->OTKeperawatanModel->detailIntervensi($id),
      'detailTindakan' => $this->OTKeperawatanModel->detailTindakan($id),
      'detailTindakanLain' => $this->OTKeperawatanModel->detailTindakanLain($id),
      'detailAnyelir' => $this->OTKeperawatanModel->detailAnyelir($id),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/detail', $data);
  }

  public function cairan()
  {
    $id = $this->input->post('id');
    $jenis = $this->input->post('jenis');
    $data = array(
      'cairan' => $this->OTKeperawatanModel->cairan($id),
      'jenisBAB' => $this->masterModel->referensi(1691),
    );
    // echo '<pre>';print_r($data);exit();
    if ($jenis == 2) {
      $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/cairanSide', $data);
    } else {
      $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/cairan', $data);
    }
  }

  public function ubahCairan()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $data = array();
      $post = $this->input->post();
      // echo '<pre>';print_r($post);exit();
      $id = $post['id'];
      if (isset($id)) {
        // Mulai pemasukan
        $oral = isset($post['oral']) ? round($post['oral'], 2) : null;
        $oral2 = isset($post['oral_2']) ? round($post['oral_2'], 2) : null;
        $oral3 = isset($post['oral_3']) ? round($post['oral_3'], 2) : null;
        $ngtPemasukan = isset($post['ngt_pemasukan']) ? round($post['ngt_pemasukan'], 2) : null;
        $ngtPemasukan2 = isset($post['ngt_pemasukan_2']) ? round($post['ngt_pemasukan_2'], 2) : null;
        $ngtPemasukan3 = isset($post['ngt_pemasukan_3']) ? round($post['ngt_pemasukan_3'], 2) : null;
        $transfusi = isset($post['transfusi']) ? round($post['transfusi'], 2) : null;
        $transfusi2 = isset($post['transfusi_2']) ? round($post['transfusi_2'], 2) : null;
        $transfusi3 = isset($post['transfusi_3']) ? round($post['transfusi_3'], 2) : null;
        $parenteral = isset($post['parenteral']) ? round($post['parenteral'], 2) : null;
        $parenteral2 = isset($post['parenteral_2']) ? round($post['parenteral_2'], 2) : null;
        $parenteral3 = isset($post['parenteral_3']) ? round($post['parenteral_3'], 2) : null;
        $parenteral4 = isset($post['parenteral_4']) ? round($post['parenteral_4'], 2) : null;
        $parenteral5 = isset($post['parenteral_5']) ? round($post['parenteral_5'], 2) : null;
        // Akhir pemasukan

        // Mulai pengeluaran
        $muntah = isset($post['muntah']) ? round($post['muntah'], 2) : null;
        $ngtPengeluaran = isset($post['ngt_pengeluaran']) ? round($post['ngt_pengeluaran'], 2) : null;
        $bak = isset($post['bak']) ? round($post['bak'], 2) : null;
        $jenisBab = isset($post['jenis_bab']) ? $post['jenis_bab'] : null;
        $bab = isset($post['bab']) ? round($post['bab'], 2) : null;
        $pendarahan = isset($post['pendarahan']) ? round($post['pendarahan'], 2) : null;
        $wsdDrain = isset($post['wsd_drain']) ? round($post['wsd_drain'], 2) : null;
        $wsdDrain2 = isset($post['wsd_drain_2']) ? round($post['wsd_drain_2'], 2) : null;
        $wsdDrain3 = isset($post['wsd_drain_3']) ? round($post['wsd_drain_3'], 2) : null;
        $wsdDrain4 = isset($post['wsd_drain_4']) ? round($post['wsd_drain_4'], 2) : null;
        $iwl = isset($post['iwl']) ? round($post['iwl'], 2) : null;
        // Akhir pengeluaran

        // Mulai balance
        if (!empty($post['balance'])) {
          $balance = round($post['balance'], 2);
        } else {
          $pemasukan = $oral + $oral2 + $oral3 + $ngtPemasukan + $ngtPemasukan2 + $ngtPemasukan3 + $transfusi + $transfusi2 + $transfusi3 + $parenteral + $parenteral2 + $parenteral3 + $parenteral4 + $parenteral5;
          if ($jenisBab == 5553) {
            // Jika jenis BAB padat, maka BAB tidak dihitung
            $pengeluaran = $muntah + $ngtPengeluaran + $bak + $pendarahan + $wsdDrain + $wsdDrain2 + $wsdDrain3 + $wsdDrain4;
          } elseif ($jenisBab == 5554) {
            // Jika jenis BAB cair, maka BAB dihitung
            $pengeluaran = $muntah + $ngtPengeluaran + $bak + $bab + $pendarahan + $wsdDrain + $wsdDrain2 + $wsdDrain3 + $wsdDrain4;
          }
          $hitung = $pemasukan - $pengeluaran - $iwl;
          $balance = round($hitung, 2);
        }
        // echo '<pre>';print_r($balance);exit();
        // Akhir balance

        $data = array(
          'oral' => $oral,
          'ket_oral' => isset($post['ket_oral']) ? $post['ket_oral'] : null,
          'oral_2' => $oral2,
          'ket_oral_2' => isset($post['ket_oral_2']) ? $post['ket_oral_2'] : null,
          'oral_3' => $oral3,
          'ket_oral_3' => isset($post['ket_oral_3']) ? $post['ket_oral_3'] : null,
          'ngt_pemasukan' => $ngtPemasukan,
          'ket_ngt_pemasukan' => isset($post['ket_ngt_pemasukan']) ? $post['ket_ngt_pemasukan'] : null,
          'ngt_pemasukan_2' => $ngtPemasukan2,
          'ket_ngt_pemasukan_2' => isset($post['ket_ngt_pemasukan_2']) ? $post['ket_ngt_pemasukan_2'] : null,
          'ngt_pemasukan_3' => $ngtPemasukan3,
          'ket_ngt_pemasukan_3' => isset($post['ket_ngt_pemasukan_3']) ? $post['ket_ngt_pemasukan_3'] : null,
          'transfusi' => $transfusi,
          'ket_transfusi' => isset($post['ket_transfusi']) ? $post['ket_transfusi'] : null,
          'transfusi_2' => $transfusi2,
          'ket_transfusi_2' => isset($post['ket_transfusi_2']) ? $post['ket_transfusi_2'] : null,
          'transfusi_3' => $transfusi3,
          'ket_transfusi_3' => isset($post['ket_transfusi_3']) ? $post['ket_transfusi_3'] : null,
          'parenteral' => $parenteral,
          'ket_parenteral' => isset($post['ket_parenteral']) ? $post['ket_parenteral'] : null,
          'parenteral_2' => $parenteral2,
          'ket_parenteral_2' => isset($post['ket_parenteral_2']) ? $post['ket_parenteral_2'] : null,
          'parenteral_3' => $parenteral3,
          'ket_parenteral_3' => isset($post['ket_parenteral_3']) ? $post['ket_parenteral_3'] : null,
          'parenteral_4' => $parenteral4,
          'ket_parenteral_4' => isset($post['ket_parenteral_4']) ? $post['ket_parenteral_4'] : null,
          'parenteral_5' => $parenteral5,
          'ket_parenteral_5' => isset($post['ket_parenteral_5']) ? $post['ket_parenteral_5'] : null,
          'muntah' => $muntah,
          'ket_muntah' => isset($post['ket_muntah']) ? $post['ket_muntah'] : null,
          'ngt_pengeluaran' => $ngtPengeluaran,
          'ket_ngt_pengeluaran' => isset($post['ket_ngt_pengeluaran']) ? $post['ket_ngt_pengeluaran'] : null,
          'bak' => $bak,
          'ket_bak' => isset($post['ket_bak']) ? $post['ket_bak'] : null,
          'jenis_bab' => $jenisBab,
          'bab' => $bab,
          'ket_bab' => isset($post['ket_bab']) ? $post['ket_bab'] : null,
          'pendarahan' => $pendarahan,
          'ket_pendarahan' => isset($post['ket_pendarahan']) ? $post['ket_pendarahan'] : null,
          'wsd_drain' => $wsdDrain,
          'ket_wsd_drain' => isset($post['ket_wsd_drain']) ? $post['ket_wsd_drain'] : null,
          'wsd_drain_2' => $wsdDrain2,
          'ket_wsd_drain_2' => isset($post['ket_wsd_drain_2']) ? $post['ket_wsd_drain_2'] : null,
          'wsd_drain_3' => $wsdDrain3,
          'ket_wsd_drain_3' => isset($post['ket_wsd_drain_3']) ? $post['ket_wsd_drain_3'] : null,
          'wsd_drain_4' => $wsdDrain4,
          'ket_wsd_drain_4' => isset($post['ket_wsd_drain_4']) ? $post['ket_wsd_drain_4'] : null,
          'iwl' => $iwl,
          'balance' => $balance,
        );
        // echo '<pre>';print_r($data);exit();
        $this->OTKeperawatanModel->ubahObservasi($id, $data);
      }
      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $idObservasi = $post['id'];
    $dataSource = 5;

    $dataObservasi = array(
      'updated_at' => date('Y-m-d H:i:s'),
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahObservasi($idObservasi, $dataObservasi);

    $dataKesadaran = array(
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahKesadaran($idObservasi, $dataSource, $dataKesadaran);

    $dataTandaVital = array(
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahTandaVital($idObservasi, $dataSource, $dataTandaVital);

    $dataTbBb = array(
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahTbBb($idObservasi, $dataSource, $dataTbBb);

    $dataOksigen = array(
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahOksigen($idObservasi, $dataSource, $dataOksigen);

    $dataEWS = array(
      'status' => 0,
    );
    $this->EWSModel->ubahEWS($idObservasi, $dataEWS);

    $dataTindakan = array(
      'status' => 0,
    );
    $this->OTKeperawatanModel->ubahTindakan($idObservasi, $dataTindakan);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function sidebar()
  {
    $this->db->trans_begin();
    $data = array(
      'nomr' => $this->uri->segment(6),
      'nokun' => $this->uri->segment(8),
      'tabelObservasi' => $this->masterModel->referensi(1746),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/sidebar/index', $data);
  }

  public function isiSidebar()
  {
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    $jenis = $post['jenis'];

    $data = array(
      'sidebar' => true,
      'jenis' => $jenis,
      'idTabel' => 'tabel-balance-sidebar-ot-keperawatan',
      'balance' => $this->OTKeperawatanModel->balance($jenis, $post['nomr']),
    );

    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/OTKeperawatan/balance', $data);
  }
}

/* End of file OTKeperawatan.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/keperawatan/OTKeperawatan.php */