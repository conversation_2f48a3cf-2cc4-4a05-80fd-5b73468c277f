<?php
defined('BASEPATH') or exit('No direct script access allowed');

class AlatOperasi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/rawat_inap/operasi/AlatOperasiModel'
      )
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array(
      'alat' => $this->masterModel->referensi(1562),
      'kassa' => $this->masterModel->referensi(1563),
      'xray' => $this->masterModel->referensi(1564),
      'listDr' => $this->masterModel->listDrUmum(),
      'listPerawat' => $this->masterModel->listPerawat(),
    );
    if (isset($id)) {
      // Form detail
      $data['id'] = $id;
      $data['detail'] = $this->AlatOperasiModel->history(null, null, $id);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/operasi/AlatOperasi/detail', $data);
    } else {
      // Form tambah
      $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
      $data['pasien'] = $pasien;
      $data['nokun'] = $pasien['NOKUN'];
      $data['jumlah'] = $this->AlatOperasiModel->history($pasien['NOKUN'], 'jumlah', null);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/operasi/AlatOperasi/index', $data);
    }
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->AlatOperasiModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();
          $id = isset($post['id']) ? $post['id'] : null;
          $idInstrumen = isset($post['id_instrumen']) ? $post['id_instrumen'] : null;
          $idTambahan = isset($post['id_tambahan']) ? $post['id_tambahan'] : null;
          $idKassa = isset($post['id_kassa']) ? $post['id_kassa'] : null;

          // Mulai data instrumen alat operasi
          $dataInstrumen = array(
            'duk_klem_pendek_pre_op' => isset($post['duk_klem_pendek_pre_op']) ? $post['duk_klem_pendek_pre_op'] : null,
            'duk_klem_pendek_tambahan' => isset($post['duk_klem_pendek_tambahan']) ? $post['duk_klem_pendek_tambahan'] : null,
            'duk_klem_sedang_pre_op' => isset($post['duk_klem_sedang_pre_op']) ? $post['duk_klem_sedang_pre_op'] : null,
            'duk_klem_sedang_tambahan' => isset($post['duk_klem_sedang_tambahan']) ? $post['duk_klem_sedang_tambahan'] : null,
            'duk_klem_panjang_pre_op' => isset($post['duk_klem_panjang_pre_op']) ? $post['duk_klem_panjang_pre_op'] : null,
            'duk_klem_panjang_tambahan' => isset($post['duk_klem_panjang_tambahan']) ? $post['duk_klem_panjang_tambahan'] : null,
            'scalpel_pendek_pre_op' => isset($post['scalpel_pendek_pre_op']) ? $post['scalpel_pendek_pre_op'] : null,
            'scalpel_pendek_tambahan' => isset($post['scalpel_pendek_tambahan']) ? $post['scalpel_pendek_tambahan'] : null,
            'scalpel_sedang_pre_op' => isset($post['scalpel_sedang_pre_op']) ? $post['scalpel_sedang_pre_op'] : null,
            'scalpel_sedang_tambahan' => isset($post['scalpel_sedang_tambahan']) ? $post['scalpel_sedang_tambahan'] : null,
            'scalpel_panjang_pre_op' => isset($post['scalpel_panjang_pre_op']) ? $post['scalpel_panjang_pre_op'] : null,
            'scalpel_panjang_tambahan' => isset($post['scalpel_panjang_tambahan']) ? $post['scalpel_panjang_tambahan'] : null,
            'anatomis_pendek_pre_op' => isset($post['anatomis_pendek_pre_op']) ? $post['anatomis_pendek_pre_op'] : null,
            'anatomis_pendek_tambahan' => isset($post['anatomis_pendek_tambahan']) ? $post['anatomis_pendek_tambahan'] : null,
            'anatomis_sedang_pre_op' => isset($post['anatomis_sedang_pre_op']) ? $post['anatomis_sedang_pre_op'] : null,
            'anatomis_sedang_tambahan' => isset($post['anatomis_sedang_tambahan']) ? $post['anatomis_sedang_tambahan'] : null,
            'anatomis_panjang_pre_op' => isset($post['anatomis_panjang_pre_op']) ? $post['anatomis_panjang_pre_op'] : null,
            'anatomis_panjang_tambahan' => isset($post['anatomis_panjang_tambahan']) ? $post['anatomis_panjang_tambahan'] : null,
            'diatermi_pendek_pre_op' => isset($post['diatermi_pendek_pre_op']) ? $post['diatermi_pendek_pre_op'] : null,
            'diatermi_pendek_tambahan' => isset($post['diatermi_pendek_tambahan']) ? $post['diatermi_pendek_tambahan'] : null,
            'diatermi_sedang_pre_op' => isset($post['diatermi_sedang_pre_op']) ? $post['diatermi_sedang_pre_op'] : null,
            'diatermi_sedang_tambahan' => isset($post['diatermi_sedang_tambahan']) ? $post['diatermi_sedang_tambahan'] : null,
            'diatermi_panjang_pre_op' => isset($post['diatermi_panjang_pre_op']) ? $post['diatermi_panjang_pre_op'] : null,
            'diatermi_panjang_tambahan' => isset($post['diatermi_panjang_tambahan']) ? $post['diatermi_panjang_tambahan'] : null,
            'cirurgis_pendek_pre_op' => isset($post['cirurgis_pendek_pre_op']) ? $post['cirurgis_pendek_pre_op'] : null,
            'cirurgis_pendek_tambahan' => isset($post['cirurgis_pendek_tambahan']) ? $post['cirurgis_pendek_tambahan'] : null,
            'cirurgis_sedang_pre_op' => isset($post['cirurgis_sedang_pre_op']) ? $post['cirurgis_sedang_pre_op'] : null,
            'cirurgis_sedang_tambahan' => isset($post['cirurgis_sedang_tambahan']) ? $post['cirurgis_sedang_tambahan'] : null,
            'cirurgis_panjang_pre_op' => isset($post['cirurgis_panjang_pre_op']) ? $post['cirurgis_panjang_pre_op'] : null,
            'cirurgis_panjang_tambahan' => isset($post['cirurgis_panjang_tambahan']) ? $post['cirurgis_panjang_tambahan'] : null,
            'benang_pendek_pre_op' => isset($post['benang_pendek_pre_op']) ? $post['benang_pendek_pre_op'] : null,
            'benang_pendek_tambahan' => isset($post['benang_pendek_tambahan']) ? $post['benang_pendek_tambahan'] : null,
            'benang_sedang_pre_op' => isset($post['benang_sedang_pre_op']) ? $post['benang_sedang_pre_op'] : null,
            'benang_sedang_tambahan' => isset($post['benang_sedang_tambahan']) ? $post['benang_sedang_tambahan'] : null,
            'benang_panjang_pre_op' => isset($post['benang_panjang_pre_op']) ? $post['benang_panjang_pre_op'] : null,
            'benang_panjang_tambahan' => isset($post['benang_panjang_tambahan']) ? $post['benang_panjang_tambahan'] : null,
            'jaringan_pendek_pre_op' => isset($post['jaringan_pendek_pre_op']) ? $post['jaringan_pendek_pre_op'] : null,
            'jaringan_pendek_tambahan' => isset($post['jaringan_pendek_tambahan']) ? $post['jaringan_pendek_tambahan'] : null,
            'jaringan_sedang_pre_op' => isset($post['jaringan_sedang_pre_op']) ? $post['jaringan_sedang_pre_op'] : null,
            'jaringan_sedang_tambahan' => isset($post['jaringan_sedang_tambahan']) ? $post['jaringan_sedang_tambahan'] : null,
            'jaringan_panjang_pre_op' => isset($post['jaringan_panjang_pre_op']) ? $post['jaringan_panjang_pre_op'] : null,
            'jaringan_panjang_tambahan' => isset($post['jaringan_panjang_tambahan']) ? $post['jaringan_panjang_tambahan'] : null,
            'kocher_pendek_pre_op' => isset($post['kocher_pendek_pre_op']) ? $post['kocher_pendek_pre_op'] : null,
            'kocher_pendek_tambahan' => isset($post['kocher_pendek_tambahan']) ? $post['kocher_pendek_tambahan'] : null,
            'kocher_sedang_pre_op' => isset($post['kocher_sedang_pre_op']) ? $post['kocher_sedang_pre_op'] : null,
            'kocher_sedang_tambahan' => isset($post['kocher_sedang_tambahan']) ? $post['kocher_sedang_tambahan'] : null,
            'kocher_panjang_pre_op' => isset($post['kocher_panjang_pre_op']) ? $post['kocher_panjang_pre_op'] : null,
            'kocher_panjang_tambahan' => isset($post['kocher_panjang_tambahan']) ? $post['kocher_panjang_tambahan'] : null,
            'naldvoeder_pendek_pre_op' => isset($post['naldvoeder_pendek_pre_op']) ? $post['naldvoeder_pendek_pre_op'] : null,
            'naldvoeder_pendek_tambahan' => isset($post['naldvoeder_pendek_tambahan']) ? $post['naldvoeder_pendek_tambahan'] : null,
            'naldvoeder_sedang_pre_op' => isset($post['naldvoeder_sedang_pre_op']) ? $post['naldvoeder_sedang_pre_op'] : null,
            'naldvoeder_sedang_tambahan' => isset($post['naldvoeder_sedang_tambahan']) ? $post['naldvoeder_sedang_tambahan'] : null,
            'naldvoeder_panjang_pre_op' => isset($post['naldvoeder_panjang_pre_op']) ? $post['naldvoeder_panjang_pre_op'] : null,
            'naldvoeder_panjang_tambahan' => isset($post['naldvoeder_panjang_tambahan']) ? $post['naldvoeder_panjang_tambahan'] : null,
            'langen_beck_pendek_pre_op' => isset($post['langen_beck_pendek_pre_op']) ? $post['langen_beck_pendek_pre_op'] : null,
            'langen_beck_pendek_tambahan' => isset($post['langen_beck_pendek_tambahan']) ? $post['langen_beck_pendek_tambahan'] : null,
            'langen_beck_sedang_pre_op' => isset($post['langen_beck_sedang_pre_op']) ? $post['langen_beck_sedang_pre_op'] : null,
            'langen_beck_sedang_tambahan' => isset($post['langen_beck_sedang_tambahan']) ? $post['langen_beck_sedang_tambahan'] : null,
            'langen_beck_panjang_pre_op' => isset($post['langen_beck_panjang_pre_op']) ? $post['langen_beck_panjang_pre_op'] : null,
            'langen_beck_panjang_tambahan' => isset($post['langen_beck_panjang_tambahan']) ? $post['langen_beck_panjang_tambahan'] : null,
            'preparir_pendek_pre_op' => isset($post['preparir_pendek_pre_op']) ? $post['preparir_pendek_pre_op'] : null,
            'preparir_pendek_tambahan' => isset($post['preparir_pendek_tambahan']) ? $post['preparir_pendek_tambahan'] : null,
            'preparir_sedang_pre_op' => isset($post['preparir_sedang_pre_op']) ? $post['preparir_sedang_pre_op'] : null,
            'preparir_sedang_tambahan' => isset($post['preparir_sedang_tambahan']) ? $post['preparir_sedang_tambahan'] : null,
            'preparir_panjang_pre_op' => isset($post['preparir_panjang_pre_op']) ? $post['preparir_panjang_pre_op'] : null,
            'preparir_panjang_tambahan' => isset($post['preparir_panjang_tambahan']) ? $post['preparir_panjang_tambahan'] : null,
            'klem_lurus_pendek_pre_op' => isset($post['klem_lurus_pendek_pre_op']) ? $post['klem_lurus_pendek_pre_op'] : null,
            'klem_lurus_pendek_tambahan' => isset($post['klem_lurus_pendek_tambahan']) ? $post['klem_lurus_pendek_tambahan'] : null,
            'klem_lurus_sedang_pre_op' => isset($post['klem_lurus_sedang_pre_op']) ? $post['klem_lurus_sedang_pre_op'] : null,
            'klem_lurus_sedang_tambahan' => isset($post['klem_lurus_sedang_tambahan']) ? $post['klem_lurus_sedang_tambahan'] : null,
            'klem_lurus_panjang_pre_op' => isset($post['klem_lurus_panjang_pre_op']) ? $post['klem_lurus_panjang_pre_op'] : null,
            'klem_lurus_panjang_tambahan' => isset($post['klem_lurus_panjang_tambahan']) ? $post['klem_lurus_panjang_tambahan'] : null,
            'liver_hak_pendek_pre_op' => isset($post['liver_hak_pendek_pre_op']) ? $post['liver_hak_pendek_pre_op'] : null,
            'liver_hak_pendek_tambahan' => isset($post['liver_hak_pendek_tambahan']) ? $post['liver_hak_pendek_tambahan'] : null,
            'liver_hak_sedang_pre_op' => isset($post['liver_hak_sedang_pre_op']) ? $post['liver_hak_sedang_pre_op'] : null,
            'liver_hak_sedang_tambahan' => isset($post['liver_hak_sedang_tambahan']) ? $post['liver_hak_sedang_tambahan'] : null,
            'liver_hak_panjang_pre_op' => isset($post['liver_hak_panjang_pre_op']) ? $post['liver_hak_panjang_pre_op'] : null,
            'liver_hak_panjang_tambahan' => isset($post['liver_hak_panjang_tambahan']) ? $post['liver_hak_panjang_tambahan'] : null,
            'tampon_pendek_pre_op' => isset($post['tampon_pendek_pre_op']) ? $post['tampon_pendek_pre_op'] : null,
            'tampon_pendek_tambahan' => isset($post['tampon_pendek_tambahan']) ? $post['tampon_pendek_tambahan'] : null,
            'tampon_sedang_pre_op' => isset($post['tampon_sedang_pre_op']) ? $post['tampon_sedang_pre_op'] : null,
            'tampon_sedang_tambahan' => isset($post['tampon_sedang_tambahan']) ? $post['tampon_sedang_tambahan'] : null,
            'tampon_panjang_pre_op' => isset($post['tampon_panjang_pre_op']) ? $post['tampon_panjang_pre_op'] : null,
            'tampon_panjang_tambahan' => isset($post['tampon_panjang_tambahan']) ? $post['tampon_panjang_tambahan'] : null,
            'klem_usus_pendek_pre_op' => isset($post['klem_usus_pendek_pre_op']) ? $post['klem_usus_pendek_pre_op'] : null,
            'klem_usus_pendek_tambahan' => isset($post['klem_usus_pendek_tambahan']) ? $post['klem_usus_pendek_tambahan'] : null,
            'klem_usus_sedang_pre_op' => isset($post['klem_usus_sedang_pre_op']) ? $post['klem_usus_sedang_pre_op'] : null,
            'klem_usus_sedang_tambahan' => isset($post['klem_usus_sedang_tambahan']) ? $post['klem_usus_sedang_tambahan'] : null,
            'klem_usus_panjang_pre_op' => isset($post['klem_usus_panjang_pre_op']) ? $post['klem_usus_panjang_pre_op'] : null,
            'klem_usus_panjang_tambahan' => isset($post['klem_usus_panjang_tambahan']) ? $post['klem_usus_panjang_tambahan'] : null,
            'bebkok_pendek_pre_op' => isset($post['bebkok_pendek_pre_op']) ? $post['bebkok_pendek_pre_op'] : null,
            'bebkok_pendek_tambahan' => isset($post['bebkok_pendek_tambahan']) ? $post['bebkok_pendek_tambahan'] : null,
            'bebkok_sedang_pre_op' => isset($post['bebkok_sedang_pre_op']) ? $post['bebkok_sedang_pre_op'] : null,
            'bebkok_sedang_tambahan' => isset($post['bebkok_sedang_tambahan']) ? $post['bebkok_sedang_tambahan'] : null,
            'bebkok_panjang_pre_op' => isset($post['bebkok_panjang_pre_op']) ? $post['bebkok_panjang_pre_op'] : null,
            'bebkok_panjang_tambahan' => isset($post['bebkok_panjang_tambahan']) ? $post['bebkok_panjang_tambahan'] : null,
            'spatel_pendek_pre_op' => isset($post['spatel_pendek_pre_op']) ? $post['spatel_pendek_pre_op'] : null,
            'spatel_pendek_tambahan' => isset($post['spatel_pendek_tambahan']) ? $post['spatel_pendek_tambahan'] : null,
            'spatel_sedang_pre_op' => isset($post['spatel_sedang_pre_op']) ? $post['spatel_sedang_pre_op'] : null,
            'spatel_sedang_tambahan' => isset($post['spatel_sedang_tambahan']) ? $post['spatel_sedang_tambahan'] : null,
            'spatel_panjang_pre_op' => isset($post['spatel_panjang_pre_op']) ? $post['spatel_panjang_pre_op'] : null,
            'spatel_panjang_tambahan' => isset($post['spatel_panjang_tambahan']) ? $post['spatel_panjang_tambahan'] : null,
            'mekolik_pendek_pre_op' => isset($post['mekolik_pendek_pre_op']) ? $post['mekolik_pendek_pre_op'] : null,
            'mekolik_pendek_tambahan' => isset($post['mekolik_pendek_tambahan']) ? $post['mekolik_pendek_tambahan'] : null,
            'mekolik_sedang_pre_op' => isset($post['mekolik_sedang_pre_op']) ? $post['mekolik_sedang_pre_op'] : null,
            'mekolik_sedang_tambahan' => isset($post['mekolik_sedang_tambahan']) ? $post['mekolik_sedang_tambahan'] : null,
            'mekolik_panjang_pre_op' => isset($post['mekolik_panjang_pre_op']) ? $post['mekolik_panjang_pre_op'] : null,
            'mekolik_panjang_tambahan' => isset($post['mekolik_panjang_tambahan']) ? $post['mekolik_panjang_tambahan'] : null,
          );
          // echo '<pre>';print_r($dataInstrumen);exit();
          if (isset($idInstrumen)) {
            $this->AlatOperasiModel->ubahInstrumen($dataInstrumen, $idInstrumen);
          } else {
            $idInstrumen = $this->AlatOperasiModel->simpanInstrumen($dataInstrumen);
          }
          // Selesai data instrumen alat operasi

          // Mulai data tambahan alat operasi
          $dataTambahan = array(
            'suction_pre_op' => isset($post['suction_pre_op']) ? $post['suction_pre_op'] : null,
            'suction_tambahan' => isset($post['suction_tambahan']) ? $post['suction_tambahan'] : null,
            'k_medium_pre_op' => isset($post['k_medium_pre_op']) ? $post['k_medium_pre_op'] : null,
            'k_medium_tambahan' => isset($post['k_medium_tambahan']) ? $post['k_medium_tambahan'] : null,
            'retraktor_pre_op' => isset($post['retraktor_pre_op']) ? $post['retraktor_pre_op'] : null,
            'retraktor_tambahan' => isset($post['retraktor_tambahan']) ? $post['retraktor_tambahan'] : null,
            'l_hak_pre_op' => isset($post['l_hak_pre_op']) ? $post['l_hak_pre_op'] : null,
            'l_hak_tambahan' => isset($post['l_hak_tambahan']) ? $post['l_hak_tambahan'] : null,
            'jarum_lepas_pre_op' => isset($post['jarum_lepas_pre_op']) ? $post['jarum_lepas_pre_op'] : null,
            'jarum_lepas_tambahan' => isset($post['jarum_lepas_tambahan']) ? $post['jarum_lepas_tambahan'] : null,
            'nm_tambahan_6' => isset($post['nm_tambahan_6']) ? $post['nm_tambahan_6'] : null,
            'tambahan_6_pre_op' => isset($post['tambahan_6_pre_op']) ? $post['tambahan_6_pre_op'] : null,
            'tambahan_6_tambahan' => isset($post['tambahan_6_tambahan']) ? $post['tambahan_6_tambahan'] : null,
            'nm_tambahan_7' => isset($post['nm_tambahan_7']) ? $post['nm_tambahan_7'] : null,
            'tambahan_7_pre_op' => isset($post['tambahan_7_pre_op']) ? $post['tambahan_7_pre_op'] : null,
            'tambahan_7_tambahan' => isset($post['tambahan_7_tambahan']) ? $post['tambahan_7_tambahan'] : null,
            'nm_tambahan_8' => isset($post['nm_tambahan_8']) ? $post['nm_tambahan_8'] : null,
            'tambahan_8_pre_op' => isset($post['tambahan_8_pre_op']) ? $post['tambahan_8_pre_op'] : null,
            'tambahan_8_tambahan' => isset($post['tambahan_8_tambahan']) ? $post['tambahan_8_tambahan'] : null,
          );
          // echo '<pre>';print_r($dataTambahan);exit();
          if (isset($idTambahan)) {
            $this->AlatOperasiModel->ubahTambahan($dataTambahan, $idTambahan);
          } else {
            $idTambahan = $this->AlatOperasiModel->simpanTambahan($dataTambahan);
          }
          // Selesai data tambahan alat operasi

          // Mulai data kassa alat operasi
          $dataKassa = array(
            'kassa_besar_masuk_1' => isset($post['kassa_besar_masuk_1']) ? $post['kassa_besar_masuk_1'] : null,
            'kassa_besar_masuk_2' => isset($post['kassa_besar_masuk_2']) ? $post['kassa_besar_masuk_2'] : null,
            'kassa_besar_masuk_3' => isset($post['kassa_besar_masuk_3']) ? $post['kassa_besar_masuk_3'] : null,
            'kassa_besar_masuk_4' => isset($post['kassa_besar_masuk_4']) ? $post['kassa_besar_masuk_4'] : null,
            'kassa_besar_masuk_5' => isset($post['kassa_besar_masuk_5']) ? $post['kassa_besar_masuk_5'] : null,
            'kassa_besar_masuk_6' => isset($post['kassa_besar_masuk_6']) ? $post['kassa_besar_masuk_6'] : null,
            'kassa_besar_masuk_7' => isset($post['kassa_besar_masuk_7']) ? $post['kassa_besar_masuk_7'] : null,
            'kassa_besar_masuk_8' => isset($post['kassa_besar_masuk_8']) ? $post['kassa_besar_masuk_8'] : null,
            'kassa_besar_masuk_9' => isset($post['kassa_besar_masuk_9']) ? $post['kassa_besar_masuk_9'] : null,
            'kassa_besar_masuk_10' => isset($post['kassa_besar_masuk_10']) ? $post['kassa_besar_masuk_10'] : null,
            'kassa_besar_keluar_1' => isset($post['kassa_besar_keluar_1']) ? $post['kassa_besar_keluar_1'] : null,
            'kassa_besar_keluar_2' => isset($post['kassa_besar_keluar_2']) ? $post['kassa_besar_keluar_2'] : null,
            'kassa_besar_keluar_3' => isset($post['kassa_besar_keluar_3']) ? $post['kassa_besar_keluar_3'] : null,
            'kassa_besar_keluar_4' => isset($post['kassa_besar_keluar_4']) ? $post['kassa_besar_keluar_4'] : null,
            'kassa_besar_keluar_5' => isset($post['kassa_besar_keluar_5']) ? $post['kassa_besar_keluar_5'] : null,
            'kassa_besar_keluar_6' => isset($post['kassa_besar_keluar_6']) ? $post['kassa_besar_keluar_6'] : null,
            'kassa_besar_keluar_7' => isset($post['kassa_besar_keluar_7']) ? $post['kassa_besar_keluar_7'] : null,
            'kassa_besar_keluar_8' => isset($post['kassa_besar_keluar_8']) ? $post['kassa_besar_keluar_8'] : null,
            'kassa_besar_keluar_9' => isset($post['kassa_besar_keluar_9']) ? $post['kassa_besar_keluar_9'] : null,
            'kassa_besar_keluar_10' => isset($post['kassa_besar_keluar_10']) ? $post['kassa_besar_keluar_10'] : null,
            'deskripsi_kassa_besar' => isset($post['deskripsi_kassa_besar']) ? $post['deskripsi_kassa_besar'] : null,
            'kassa_kecil_masuk_1' => isset($post['kassa_kecil_masuk_1']) ? $post['kassa_kecil_masuk_1'] : null,
            'kassa_kecil_masuk_2' => isset($post['kassa_kecil_masuk_2']) ? $post['kassa_kecil_masuk_2'] : null,
            'kassa_kecil_masuk_3' => isset($post['kassa_kecil_masuk_3']) ? $post['kassa_kecil_masuk_3'] : null,
            'kassa_kecil_masuk_4' => isset($post['kassa_kecil_masuk_4']) ? $post['kassa_kecil_masuk_4'] : null,
            'kassa_kecil_masuk_5' => isset($post['kassa_kecil_masuk_5']) ? $post['kassa_kecil_masuk_5'] : null,
            'kassa_kecil_masuk_6' => isset($post['kassa_kecil_masuk_6']) ? $post['kassa_kecil_masuk_6'] : null,
            'kassa_kecil_masuk_7' => isset($post['kassa_kecil_masuk_7']) ? $post['kassa_kecil_masuk_7'] : null,
            'kassa_kecil_masuk_8' => isset($post['kassa_kecil_masuk_8']) ? $post['kassa_kecil_masuk_8'] : null,
            'kassa_kecil_masuk_9' => isset($post['kassa_kecil_masuk_9']) ? $post['kassa_kecil_masuk_9'] : null,
            'kassa_kecil_masuk_10' => isset($post['kassa_kecil_masuk_10']) ? $post['kassa_kecil_masuk_10'] : null,
            'kassa_kecil_keluar_1' => isset($post['kassa_kecil_keluar_1']) ? $post['kassa_kecil_keluar_1'] : null,
            'kassa_kecil_keluar_2' => isset($post['kassa_kecil_keluar_2']) ? $post['kassa_kecil_keluar_2'] : null,
            'kassa_kecil_keluar_3' => isset($post['kassa_kecil_keluar_3']) ? $post['kassa_kecil_keluar_3'] : null,
            'kassa_kecil_keluar_4' => isset($post['kassa_kecil_keluar_4']) ? $post['kassa_kecil_keluar_4'] : null,
            'kassa_kecil_keluar_5' => isset($post['kassa_kecil_keluar_5']) ? $post['kassa_kecil_keluar_5'] : null,
            'kassa_kecil_keluar_6' => isset($post['kassa_kecil_keluar_6']) ? $post['kassa_kecil_keluar_6'] : null,
            'kassa_kecil_keluar_7' => isset($post['kassa_kecil_keluar_7']) ? $post['kassa_kecil_keluar_7'] : null,
            'kassa_kecil_keluar_8' => isset($post['kassa_kecil_keluar_8']) ? $post['kassa_kecil_keluar_8'] : null,
            'kassa_kecil_keluar_9' => isset($post['kassa_kecil_keluar_9']) ? $post['kassa_kecil_keluar_9'] : null,
            'kassa_kecil_keluar_10' => isset($post['kassa_kecil_keluar_10']) ? $post['kassa_kecil_keluar_10'] : null,
            'deskripsi_kassa_kecil' => isset($post['deskripsi_kassa_kecil']) ? $post['deskripsi_kassa_kecil'] : null,
            'depper_besar_masuk_1' => isset($post['depper_besar_masuk_1']) ? $post['depper_besar_masuk_1'] : null,
            'depper_besar_masuk_2' => isset($post['depper_besar_masuk_2']) ? $post['depper_besar_masuk_2'] : null,
            'depper_besar_masuk_3' => isset($post['depper_besar_masuk_3']) ? $post['depper_besar_masuk_3'] : null,
            'depper_besar_masuk_4' => isset($post['depper_besar_masuk_4']) ? $post['depper_besar_masuk_4'] : null,
            'depper_besar_masuk_5' => isset($post['depper_besar_masuk_5']) ? $post['depper_besar_masuk_5'] : null,
            'depper_besar_masuk_6' => isset($post['depper_besar_masuk_6']) ? $post['depper_besar_masuk_6'] : null,
            'depper_besar_masuk_7' => isset($post['depper_besar_masuk_7']) ? $post['depper_besar_masuk_7'] : null,
            'depper_besar_masuk_8' => isset($post['depper_besar_masuk_8']) ? $post['depper_besar_masuk_8'] : null,
            'depper_besar_masuk_9' => isset($post['depper_besar_masuk_9']) ? $post['depper_besar_masuk_9'] : null,
            'depper_besar_masuk_10' => isset($post['depper_besar_masuk_10']) ? $post['depper_besar_masuk_10'] : null,
            'deskripsi_depper_besar' => isset($post['deskripsi_depper_besar']) ? $post['deskripsi_depper_besar'] : null,
            'depper_besar_keluar_1' => isset($post['depper_besar_keluar_1']) ? $post['depper_besar_keluar_1'] : null,
            'depper_besar_keluar_2' => isset($post['depper_besar_keluar_2']) ? $post['depper_besar_keluar_2'] : null,
            'depper_besar_keluar_3' => isset($post['depper_besar_keluar_3']) ? $post['depper_besar_keluar_3'] : null,
            'depper_besar_keluar_4' => isset($post['depper_besar_keluar_4']) ? $post['depper_besar_keluar_4'] : null,
            'depper_besar_keluar_5' => isset($post['depper_besar_keluar_5']) ? $post['depper_besar_keluar_5'] : null,
            'depper_besar_keluar_6' => isset($post['depper_besar_keluar_6']) ? $post['depper_besar_keluar_6'] : null,
            'depper_besar_keluar_7' => isset($post['depper_besar_keluar_7']) ? $post['depper_besar_keluar_7'] : null,
            'depper_besar_keluar_8' => isset($post['depper_besar_keluar_8']) ? $post['depper_besar_keluar_8'] : null,
            'depper_besar_keluar_9' => isset($post['depper_besar_keluar_9']) ? $post['depper_besar_keluar_9'] : null,
            'depper_besar_keluar_10' => isset($post['depper_besar_keluar_10']) ? $post['depper_besar_keluar_10'] : null,
            'deskripsi_depper_besar' => isset($post['deskripsi_depper_besar']) ? $post['deskripsi_depper_besar'] : null,
            'depper_kecil_masuk_1' => isset($post['depper_kecil_masuk_1']) ? $post['depper_kecil_masuk_1'] : null,
            'depper_kecil_masuk_2' => isset($post['depper_kecil_masuk_2']) ? $post['depper_kecil_masuk_2'] : null,
            'depper_kecil_masuk_3' => isset($post['depper_kecil_masuk_3']) ? $post['depper_kecil_masuk_3'] : null,
            'depper_kecil_masuk_4' => isset($post['depper_kecil_masuk_4']) ? $post['depper_kecil_masuk_4'] : null,
            'depper_kecil_masuk_5' => isset($post['depper_kecil_masuk_5']) ? $post['depper_kecil_masuk_5'] : null,
            'depper_kecil_masuk_6' => isset($post['depper_kecil_masuk_6']) ? $post['depper_kecil_masuk_6'] : null,
            'depper_kecil_masuk_7' => isset($post['depper_kecil_masuk_7']) ? $post['depper_kecil_masuk_7'] : null,
            'depper_kecil_masuk_8' => isset($post['depper_kecil_masuk_8']) ? $post['depper_kecil_masuk_8'] : null,
            'depper_kecil_masuk_9' => isset($post['depper_kecil_masuk_9']) ? $post['depper_kecil_masuk_9'] : null,
            'depper_kecil_masuk_10' => isset($post['depper_kecil_masuk_10']) ? $post['depper_kecil_masuk_10'] : null,
            'deskripsi_depper_kecil' => isset($post['deskripsi_depper_kecil']) ? $post['deskripsi_depper_kecil'] : null,
            'depper_kecil_keluar_1' => isset($post['depper_kecil_keluar_1']) ? $post['depper_kecil_keluar_1'] : null,
            'depper_kecil_keluar_2' => isset($post['depper_kecil_keluar_2']) ? $post['depper_kecil_keluar_2'] : null,
            'depper_kecil_keluar_3' => isset($post['depper_kecil_keluar_3']) ? $post['depper_kecil_keluar_3'] : null,
            'depper_kecil_keluar_4' => isset($post['depper_kecil_keluar_4']) ? $post['depper_kecil_keluar_4'] : null,
            'depper_kecil_keluar_5' => isset($post['depper_kecil_keluar_5']) ? $post['depper_kecil_keluar_5'] : null,
            'depper_kecil_keluar_6' => isset($post['depper_kecil_keluar_6']) ? $post['depper_kecil_keluar_6'] : null,
            'depper_kecil_keluar_7' => isset($post['depper_kecil_keluar_7']) ? $post['depper_kecil_keluar_7'] : null,
            'depper_kecil_keluar_8' => isset($post['depper_kecil_keluar_8']) ? $post['depper_kecil_keluar_8'] : null,
            'depper_kecil_keluar_9' => isset($post['depper_kecil_keluar_9']) ? $post['depper_kecil_keluar_9'] : null,
            'depper_kecil_keluar_10' => isset($post['depper_kecil_keluar_10']) ? $post['depper_kecil_keluar_10'] : null,
            'deskripsi_depper_kecil' => isset($post['deskripsi_depper_kecil']) ? $post['deskripsi_depper_kecil'] : null,
            'nm_kassa_5' => isset($post['nm_kassa_5']) ? $post['nm_kassa_5'] : null,
            'kassa_5_masuk_1' => isset($post['kassa_5_masuk_1']) ? $post['kassa_5_masuk_1'] : null,
            'kassa_5_masuk_2' => isset($post['kassa_5_masuk_2']) ? $post['kassa_5_masuk_2'] : null,
            'kassa_5_masuk_3' => isset($post['kassa_5_masuk_3']) ? $post['kassa_5_masuk_3'] : null,
            'kassa_5_masuk_4' => isset($post['kassa_5_masuk_4']) ? $post['kassa_5_masuk_4'] : null,
            'kassa_5_masuk_5' => isset($post['kassa_5_masuk_5']) ? $post['kassa_5_masuk_5'] : null,
            'kassa_5_masuk_6' => isset($post['kassa_5_masuk_6']) ? $post['kassa_5_masuk_6'] : null,
            'kassa_5_masuk_7' => isset($post['kassa_5_masuk_7']) ? $post['kassa_5_masuk_7'] : null,
            'kassa_5_masuk_8' => isset($post['kassa_5_masuk_8']) ? $post['kassa_5_masuk_8'] : null,
            'kassa_5_masuk_9' => isset($post['kassa_5_masuk_9']) ? $post['kassa_5_masuk_9'] : null,
            'kassa_5_masuk_10' => isset($post['kassa_5_masuk_10']) ? $post['kassa_5_masuk_10'] : null,
            'kassa_5_keluar_1' => isset($post['kassa_5_keluar_1']) ? $post['kassa_5_keluar_1'] : null,
            'kassa_5_keluar_2' => isset($post['kassa_5_keluar_2']) ? $post['kassa_5_keluar_2'] : null,
            'kassa_5_keluar_3' => isset($post['kassa_5_keluar_3']) ? $post['kassa_5_keluar_3'] : null,
            'kassa_5_keluar_4' => isset($post['kassa_5_keluar_4']) ? $post['kassa_5_keluar_4'] : null,
            'kassa_5_keluar_5' => isset($post['kassa_5_keluar_5']) ? $post['kassa_5_keluar_5'] : null,
            'kassa_5_keluar_6' => isset($post['kassa_5_keluar_6']) ? $post['kassa_5_keluar_6'] : null,
            'kassa_5_keluar_7' => isset($post['kassa_5_keluar_7']) ? $post['kassa_5_keluar_7'] : null,
            'kassa_5_keluar_8' => isset($post['kassa_5_keluar_8']) ? $post['kassa_5_keluar_8'] : null,
            'kassa_5_keluar_9' => isset($post['kassa_5_keluar_9']) ? $post['kassa_5_keluar_9'] : null,
            'kassa_5_keluar_10' => isset($post['kassa_5_keluar_10']) ? $post['kassa_5_keluar_10'] : null,
            'deskripsi_kassa_5' => isset($post['deskripsi_kassa_5']) ? $post['deskripsi_kassa_5'] : null,
            'nm_kassa_6' => isset($post['nm_kassa_6']) ? $post['nm_kassa_6'] : null,
            'kassa_6_masuk_1' => isset($post['kassa_6_masuk_1']) ? $post['kassa_6_masuk_1'] : null,
            'kassa_6_masuk_2' => isset($post['kassa_6_masuk_2']) ? $post['kassa_6_masuk_2'] : null,
            'kassa_6_masuk_3' => isset($post['kassa_6_masuk_3']) ? $post['kassa_6_masuk_3'] : null,
            'kassa_6_masuk_4' => isset($post['kassa_6_masuk_4']) ? $post['kassa_6_masuk_4'] : null,
            'kassa_6_masuk_5' => isset($post['kassa_6_masuk_5']) ? $post['kassa_6_masuk_5'] : null,
            'kassa_6_masuk_6' => isset($post['kassa_6_masuk_6']) ? $post['kassa_6_masuk_6'] : null,
            'kassa_6_masuk_7' => isset($post['kassa_6_masuk_7']) ? $post['kassa_6_masuk_7'] : null,
            'kassa_6_masuk_8' => isset($post['kassa_6_masuk_8']) ? $post['kassa_6_masuk_8'] : null,
            'kassa_6_masuk_9' => isset($post['kassa_6_masuk_9']) ? $post['kassa_6_masuk_9'] : null,
            'kassa_6_masuk_10' => isset($post['kassa_6_masuk_10']) ? $post['kassa_6_masuk_10'] : null,
            'kassa_6_keluar_1' => isset($post['kassa_6_keluar_1']) ? $post['kassa_6_keluar_1'] : null,
            'kassa_6_keluar_2' => isset($post['kassa_6_keluar_2']) ? $post['kassa_6_keluar_2'] : null,
            'kassa_6_keluar_3' => isset($post['kassa_6_keluar_3']) ? $post['kassa_6_keluar_3'] : null,
            'kassa_6_keluar_4' => isset($post['kassa_6_keluar_4']) ? $post['kassa_6_keluar_4'] : null,
            'kassa_6_keluar_5' => isset($post['kassa_6_keluar_5']) ? $post['kassa_6_keluar_5'] : null,
            'kassa_6_keluar_6' => isset($post['kassa_6_keluar_6']) ? $post['kassa_6_keluar_6'] : null,
            'kassa_6_keluar_7' => isset($post['kassa_6_keluar_7']) ? $post['kassa_6_keluar_7'] : null,
            'kassa_6_keluar_8' => isset($post['kassa_6_keluar_8']) ? $post['kassa_6_keluar_8'] : null,
            'kassa_6_keluar_9' => isset($post['kassa_6_keluar_9']) ? $post['kassa_6_keluar_9'] : null,
            'kassa_6_keluar_10' => isset($post['kassa_6_keluar_10']) ? $post['kassa_6_keluar_10'] : null,
            'deskripsi_kassa_6' => isset($post['deskripsi_kassa_6']) ? $post['deskripsi_kassa_6'] : null,
          );
          // echo '<pre>';print_r($dataKassa);exit();
          if (isset($idKassa)) {
            $this->AlatOperasiModel->ubahKassa($dataKassa, $idKassa);
          } else {
            $idKassa = $this->AlatOperasiModel->simpanKassa($dataKassa);
          }
          // Selesai data kassa alat operasi

          // Mulai data alat operasi
          $data = array(
            'id_instrumen' => isset($idInstrumen) ? $idInstrumen : null,
            'id_tambahan' => isset($idTambahan) ? $idTambahan : null,
            'id_kassa' => isset($idKassa) ? $idKassa : null,
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'tindakan_operasi' => isset($post['tindakan_operasi']) ? $post['tindakan_operasi'] : null,
            'set_instrumen' => isset($post['set_instrumen']) ? $post['set_instrumen'] : null,
            'alat' => isset($post['alat']) ? $post['alat'] : null,
            'ket_alat' => isset($post['ket_alat']) ? $post['ket_alat'] : null,
            'kassa' => isset($post['kassa']) ? $post['kassa'] : null,
            'ket_kassa' => isset($post['ket_kassa']) ? $post['ket_kassa'] : null,
            'xray' => isset($post['xray']) ? $post['xray'] : null,
            'ket_xray' => isset($post['ket_xray']) ? $post['ket_xray'] : null,
            'dok_operator' => isset($post['dok_operator']) ? $post['dok_operator'] : null,
            'instrumentator' => isset($post['instrumentator']) ? json_encode($post['instrumentator']) : null,
            'instrumentator_2' => isset($post['instrumentator_2']) ? $post['instrumentator_2'] : null,
            'onloop' => isset($post['onloop']) ? json_encode($post['onloop']) : null,
            'created_at' => date('Y-m-d H:i:s'),
            'status' => '1',
            'oleh' => $this->session->userdata['id'],
          );
          // echo '<pre>';print_r($data);exit();
          if (isset($id)) {
            $this->AlatOperasiModel->ubah($data, $id);
          } else {
            $this->AlatOperasiModel->simpan($data);
          }
          // Selesai data alat operasi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->AlatOperasiModel->history($nokun, 'tabel', null);
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H:i', strtotime($h->jam)),
        $h->pengisi,
        date('d-m-Y, H:i:s', strtotime($h->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-alat-operasi' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-alat-operasi' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-alat-operasi' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-alat-operasi' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/operasi/AlatOperasi/history', $data);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array('status' => 0);
    $this->AlatOperasiModel->ubah($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}
