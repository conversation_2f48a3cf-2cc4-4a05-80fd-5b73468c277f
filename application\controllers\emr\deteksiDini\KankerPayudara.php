<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KankerPayudara extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/TbBbModel',
        'emr/deteksiDini/KankerPayudaraModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
      )
    );
  }

  public function index()
  {
    $id = $this->input->post('id');
    $param = $this->input->post('param');
    $nomr = $this->uri->segment(5);
    $data = array(
      'id_pengguna' => $this->session->userdata('id'),
      'nomr' => $nomr,
      'nopen' => $this->uri->segment(6),
      'nokun' => $this->uri->segment(7),
      'risk1' => $this->masterModel->referensi(1723),
      'risk2' => $this->masterModel->referensi(1417),
      'risk3' => $this->masterModel->referensi(1418),
      'risk4' => $this->masterModel->referensi(1419),
      'risk5' => $this->masterModel->referensi(1420),
      'risk6' => $this->masterModel->referensi(1421),
      'risk7' => $this->masterModel->referensi(1422),
      'risk8' => $this->masterModel->referensi(1423),
      'risk9' => $this->masterModel->referensi(1424),
      'risk10' => $this->masterModel->referensi(1425),
      'risk11' => $this->masterModel->referensi(1426),
      'risk12' => $this->masterModel->referensi(1427),
      'risk14' => $this->masterModel->referensi(1428),
      'risk15' => $this->masterModel->referensi(1429),
      'gejala1' => $this->masterModel->referensi(1440),
      'gejala2' => $this->masterModel->referensi(1441),
      'gejala4' => $this->masterModel->referensi(1442),
      'gejala5' => $this->masterModel->referensi(1408),
      'gejala6' => $this->masterModel->referensi(1443),
      'gejala7' => $this->masterModel->referensi(1444),
      'gejala8' => $this->masterModel->referensi(1409),
      'gejala9' => $this->masterModel->referensi(1445),
      'gejala10' => $this->masterModel->referensi(1410),
      'gejala11' => $this->masterModel->referensi(1411),
      'gejala12' => $this->masterModel->referensi(1412),
      'gejala13' => $this->masterModel->referensi(1413),
      'gejala14' => $this->masterModel->referensi(1414),
      'gejala15' => $this->masterModel->referensi(1415),
      'gejala16' => $this->masterModel->referensi(1446),
    );
    // echo '<pre>';print_r($data);exit();
    if (isset($id)) {
      $data['detail'] = $this->KankerPayudaraModel->detail($id);
      if (isset($param)) {
        if ($param == 'dokter') {
          // Form dokter
          $data['simetri'] = $this->masterModel->referensi(1431);
          $data['bentuk'] = $this->masterModel->referensi(1432);
          $data['kulit'] = $this->masterModel->referensi(1433);
          $data['areolaPapilla'] = $this->masterModel->referensi(1434);
          $data['benjolanTeraba'] = $this->masterModel->referensi(1435);
          $data['KGBAksilaTeraba'] = $this->masterModel->referensi(1436);
          $data['KGBAksilaSoliterMultipel'] = $this->masterModel->referensi(1437);
          $data['KGBSupraklavikulaTeraba'] = $this->masterModel->referensi(1447);
          $data['KGBSupraklavikulaSoliterMultipel'] = $this->masterModel->referensi(1448);
          $data['rencanaPemeriksaanPenunjang'] = $this->masterModel->referensi(1449);
          // echo '<pre>';print_r($data);exit();
          $this->load->view('Pengkajian/emr/deteksiDini/KankerPayudara/dokter', $data);
        } elseif ($param == 'perawat') {
          // Form perawat
          // echo '<pre>';print_r($data);exit();
          $this->load->view('Pengkajian/emr/deteksiDini/KankerPayudara/perawat', $data);
        }
      }
    } else {
      if ($_SESSION['status'] == 1) {
        $data = array('nomr' => $nomr,);
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/emr/deteksiDini/KankerPayudara/history', $data);
      } elseif ($_SESSION['status'] == 2) {
        $data['jumlah'] = $this->KankerPayudaraModel->jumlah($nomr);
        $this->load->view('Pengkajian/emr/deteksiDini/KankerPayudara/index', $data);
      }
    }
  }

  public function gambar()
  {
    $id = $this->input->post('id');
    $nomr = $this->uri->segment(5);
    $data = array(
      'id_pengguna' => $this->session->userdata('id'),
      'nomr' => $nomr,
      'gambar' => $this->KankerPayudaraModel->gambar($id),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/emr/deteksiDini/KankerPayudara/gambar', $data);
  }

  public function simpan($param)
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo '<pre>';print_r($post);exit();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $nokun = isset($post['nokun']) ? $post['nokun'] : null;
      $nomr = isset($post['nomr']) ? $post['nomr'] : null;
      $oleh = $this->session->userdata['id'];
      $status = 1;
      $dataSource = 22;
      $idDeteksiDini = isset($post['id']) ? $post['id'] : null;

      // Mulai jenis pengisi
      if ($_SESSION['status'] == 1) {
        $perawat = isset($post['perawat']) ? ($post['perawat'] != 0 ? $post['perawat'] : null) : null;
        $dokter = isset($post['dokter']) ? ($post['dokter'] != 0 ? $post['dokter'] : $oleh) : $oleh;
      } elseif ($_SESSION['status'] == 2) {
        $perawat = isset($post['perawat']) ? ($post['perawat'] != 0 ? $post['perawat'] : $oleh) : $oleh;
        $dokter = isset($post['dokter']) ? ($post['dokter'] != 0 ? $post['dokter'] : null) : null;
      }
      // Akhir jenis pengisi

      if (isset($param)) {
        $rules = $this->KankerPayudaraModel->rules;
        $this->form_validation->set_rules($rules);

        // Mulai rules tanggal
        if (isset($post['tanggal'])) {
          $this->form_validation->set_rules($this->KankkerPayudaraModel->rulesTanggal);
        }
        // Akhir rules tanggal

        // Mulai rules waktu
        if (isset($post['waktu'])) {
          $this->form_validation->set_rules($this->KankkerPayudaraModel->rulesWaktu);
        }
        // Akhir rules waktu

        // Mulai rules riwayat menyusui
        if (isset($post['risk_11'])) {
          if ($post['risk_11'] == 4704) {
            $this->form_validation->set_rules($this->KankerPayudaraModel->rulesRiwayatMenyusui);
          }
        }
        // Akhir rules riwayat menyusui

        // Mulai rules alasan datang ke Deteksi Dini Kanker Dharmais
        if (isset($post['risk_15'])) {
          if ($post['risk_15'] == 5676) {
            $this->form_validation->set_rules($this->KankerPayudaraModel->rulesAlasanDatang);
          }
        }
        // Akhir rules alasan datang ke Deteksi Dini Kanker Dharmais

        // Mulai rules sedang menstruasi
        if (isset($post['gejala_2'])) {
          if ($post['gejala_2'] == 4739) {
            $this->form_validation->set_rules($this->KankerPayudaraModel->rulesSedangMenyusui);
          }
        }
        // Akhir rules sedang menstruasi

        if ($this->form_validation->run() == true) {
          $tb = isset($post['tb']) ? round($post['tb'], 2) : null;
          $bb = isset($post['bb']) ? round($post['bb'], 2) : null;
          $tdSistolik = isset($post['td_sistolik']) ? $post['td_sistolik'] : null;
          $tdDiastolik = isset($post['td_diastolik']) ? $post['td_diastolik'] : null;
          $nadi = isset($post['nadi']) ? $post['nadi'] : null;
          $pernapasan = isset($post['pernapasan']) ? $post['pernapasan'] : null;
          $suhu = isset($post['suhu']) ? $post['suhu'] : null;

          if ($param == 'gambar') {
            // Simpan ke Gambar Deteksi Dini Payudara
            $dataGambar = array(
              'pemeriksaan' => isset($post['pemeriksaan']) ? file_get_contents($post['pemeriksaan']) : null,
              'oleh' => $oleh,
              'status' => $status,
            );
            $idPemeriksaan = $this->KankerPayudaraModel->simpanGambar($dataGambar);

            // Simpan ke Deteksi Dini Payudara
            $dataDD = array(
              'perawat' => $perawat,
              'dokter' => $dokter,
              'id_pemeriksaan' => $idPemeriksaan,
            );
            // echo '<pre>';print_r($post);exit();
            $this->KankerPayudaraModel->ubah($dataDD, $idDeteksiDini);
          } elseif ($param == 'tambah' || $param == 'perawat') {
            // Simpan ke Deteksi Dini Payudara
            $dataDD = array(
              'nokun' => $nokun,
              'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
              'waktu' => isset($post['waktu']) ? $post['waktu'] : null,
              'risk_1' => isset($post['risk_1']) ? $post['risk_1'] : null,
              'risk_2' => isset($post['risk_2']) ? $post['risk_2'] : null,
              'risk_3' => isset($post['risk_3']) ? $post['risk_3'] : null,
              'risk_4' => isset($post['risk_4']) ? $post['risk_4'] : null,
              'risk_5' => isset($post['risk_5']) ? $post['risk_5'] : null,
              'risk_6' => isset($post['risk_6']) ? $post['risk_6'] : null,
              'risk_7' => isset($post['risk_7']) ? $post['risk_7'] : null,
              'risk_8' => isset($post['risk_8']) ? $post['risk_8'] : null,
              'risk_9' => isset($post['risk_9']) ? $post['risk_9'] : null,
              'risk_10' => isset($post['risk_10']) ? $post['risk_10'] : null,
              'risk_11' => isset($post['risk_11']) ? $post['risk_11'] : null,
              'risk_12' => isset($post['risk_12']) ? $post['risk_12'] : null,
              'risk_13' => isset($post['risk_13']) ? $post['risk_13'] : null,
              'risk_14' => isset($post['risk_14']) ? $post['risk_14'] : null,
              'risk_15' => isset($post['risk_15']) ? $post['risk_15'] : null,
              'ket_risk_15' => isset($post['ket_risk_15']) ? $post['ket_risk_15'] : null,
              'nilai_risk' => isset($post['nilai_risk']) ? $post['nilai_risk'] : null,
              'gejala_1' => isset($post['gejala_1']) ? $post['gejala_1'] : null,
              'gejala_2' => isset($post['gejala_2']) ? $post['gejala_2'] : null,
              'gejala_3' => isset($post['gejala_3']) ? $post['gejala_3'] : null,
              'gejala_4' => isset($post['gejala_4']) ? $post['gejala_4'] : null,
              'gejala_5' => isset($post['gejala_5']) ? $post['gejala_5'] : null,
              'gejala_6' => isset($post['gejala_6']) ? $post['gejala_6'] : null,
              'gejala_7' => isset($post['gejala_7']) ? $post['gejala_7'] : null,
              'gejala_8' => isset($post['gejala_8']) ? $post['gejala_8'] : null,
              'gejala_9' => isset($post['gejala_9']) ? $post['gejala_9'] : null,
              'gejala_10' => isset($post['gejala_10']) ? $post['gejala_10'] : null,
              'gejala_11' => isset($post['gejala_11']) ? $post['gejala_11'] : null,
              'gejala_12' => isset($post['gejala_12']) ? $post['gejala_12'] : null,
              'gejala_13' => isset($post['gejala_13']) ? $post['gejala_13'] : null,
              'gejala_14' => isset($post['gejala_14']) ? $post['gejala_14'] : null,
              'gejala_15' => isset($post['gejala_15']) ? $post['gejala_15'] : null,
              'gejala_16' => isset($post['gejala_16']) ? $post['gejala_16'] : null,
              'gejala_17' => isset($post['gejala_17']) ? $post['gejala_17'] : null,
              'imt' => isset($post['imt']) ? round($post['imt'], 2) : round(($bb / ($tb / 100 * $tb / 100)), 2),
              'kategori_tubuh' => isset($post['kategori_tubuh']) ? $post['kategori_tubuh'] : null,
              'status' => $status,
            );

            if ($param == 'tambah') {
              // Data pengisi
              if ($_SESSION['status'] == 1) {
                $dataDD['dokter'] = $oleh;
              } elseif ($_SESSION['status'] == 2) {
                $dataDD['perawat'] = $oleh;
              }
              // echo '<pre>';print_r($dataDD);exit();
              $idDeteksiDini = $this->KankerPayudaraModel->simpan($dataDD);

              // Simpan ke Tinggi dan Berat Badan
              $data = array(
                'data_source' => $dataSource,
                'ref' => $idDeteksiDini,
                'nomr' => $nomr,
                'nokun' => $nokun,
                'tb' => $tb,
                'bb' => $bb,
                'oleh' => $oleh,
                'status' => $status,
              );
              // echo '<pre>';print_r($data);exit();
              $this->TbBbModel->insert($data);

              // Simpan ke Tanda Vital
              $data = array(
                'data_source' => $dataSource,
                'ref' => $idDeteksiDini,
                'nomr' => $nomr,
                'nokun' => $nokun,
                'td_sistolik' => $tdSistolik,
                'td_diastolik' => $tdDiastolik,
                'nadi' => $nadi,
                'pernapasan' => $pernapasan,
                'suhu' => $suhu,
                'oleh' => $oleh,
                'status' => $status,
              );
              // echo '<pre>';print_r($data);exit();
              $this->OTKeperawatanModel->simpanTandaVital($data);
            } elseif ($param == 'perawat') {
              // Simpan ke Deteksi Dini Payudara
              $dataDD['id'] = $idDeteksiDini;
              $dataDD['perawat'] = $perawat;
              // echo '<pre>';print_r($dataDD);exit();
              $this->KankerPayudaraModel->replace($dataDD);

              // Simpan ke Tanda Vital
              if (isset($post['tv'])) {
                $idTV = isset($post['tv']) ? $post['tv'] : null;
                $data = array(
                  'td_sistolik' => $tdSistolik,
                  'td_diastolik' => $tdDiastolik,
                  'nadi' => $nadi,
                  'pernapasan' => $pernapasan,
                  'suhu' => $suhu,
                  'oleh' => $oleh,
                  'status' => $status,
                );
                // echo '<pre>';print_r($data);exit();
                $this->OTKeperawatanModel->ubahTandaVital($idTV, $dataSource, $data);
              } else {
                $data = array(
                  'data_source' => $dataSource,
                  'ref' => $idDeteksiDini,
                  'nomr' => $nomr,
                  'nokun' => $nokun,
                  'td_sistolik' => $tdSistolik,
                  'td_diastolik' => $tdDiastolik,
                  'nadi' => $nadi,
                  'pernapasan' => $pernapasan,
                  'suhu' => $suhu,
                  'oleh' => $oleh,
                  'status' => $status,
                );
                // echo '<pre>';print_r($data);exit();
                $this->OTKeperawatanModel->simpanTandaVital($data);
              }

              // Simpan ke Tinggi dan Berat Badan
              if (isset($post['tbb'])) {
                $idTBBB = isset($post['tbb']) ? $post['tbb'] : null;
                $data = array(
                  'tb' => $tb,
                  'bb' => $bb,
                  'oleh' => $oleh,
                  'status' => $status,
                );
                // echo '<pre>';print_r($data);exit();
                $this->TbBbModel->ubah($idTBBB, $data);
              } else {
                $data = array(
                  'data_source' => $dataSource,
                  'ref' => $idDeteksiDini,
                  'nomr' => $nomr,
                  'nokun' => $nokun,
                  'tb' => $tb,
                  'bb' => $bb,
                  'oleh' => $oleh,
                  'status' => $status,
                );
                // echo '<pre>';print_r($data);exit();
                $this->TbBbModel->insert($data);
              }
            }
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } elseif ($param == 'dokter') {
          // Simpan ke Deteksi Dini Payudara
          $dataDD = array(
            'id_pemeriksaan' => isset($post['id_pemeriksaan']) ? $post['id_pemeriksaan'] : null,
            // 'simetri' => isset($post['simetri']) ? $post['simetri'] : null,
            // 'bentuk' => isset($post['bentuk']) ? $post['bentuk'] : null,
            // 'kulit' => isset($post['kulit']) ? $post['kulit'] : null,
            // 'areola_papilla' => isset($post['areola_papilla']) ? $post['areola_papilla'] : null,
            // 'benjolan_teraba' => isset($post['benjolan_teraba']) ? $post['benjolan_teraba'] : null,
            // 'panjang_benjolan' => isset($post['panjang_benjolan']) ? $post['panjang_benjolan'] : null,
            // 'lebar_benjolan' => isset($post['lebar_benjolan']) ? $post['lebar_benjolan'] : null,
            // 'tinggi_benjolan' => isset($post['tinggi_benjolan']) ? $post['tinggi_benjolan'] : null,
            // 'konsistensi' => isset($post['konsistensi']) ? $post['konsistensi'] : null,
            // 'batas' => isset($post['batas']) ? $post['batas'] : null,
            // 'mobilisasi' => isset($post['mobilisasi']) ? $post['mobilisasi'] : null,
            // 'warna' => isset($post['warna']) ? $post['warna'] : null,
            // 'nyeri' => isset($post['nyeri']) ? $post['nyeri'] : null,
            // 'kgb_aksila_teraba' => isset($post['kgb_aksila_teraba']) ? $post['kgb_aksila_teraba'] : null,
            // 'kgb_aksila_soliter_multipel' => isset($post['kgb_aksila_soliter_multipel']) ? $post['kgb_aksila_soliter_multipel'] : null,
            // 'kgb_supraklavikula_teraba' => isset($post['kgb_supraklavikula_teraba']) ? $post['kgb_supraklavikula_teraba'] : null,
            // 'kgb_supraklavikula_soliter_multipel' => isset($post['kgb_supraklavikula_soliter_multipel']) ? $post['kgb_supraklavikula_soliter_multipel'] : null,
            // 'keterangan_lain' => isset($post['keterangan_lain']) ? $post['keterangan_lain'] : null,
            // 'rencana_pemeriksaan_penunjang' => isset($post['rencana_pemeriksaan_penunjang']) ? $post['rencana_pemeriksaan_penunjang'] : null,
            'payudara_text' => isset($post['payudara_text']) ? $post['payudara_text'] : null,
            'dokter' => $dokter,
          );
          // echo '<pre>';print_r($dataDD);exit();
          $this->KankerPayudaraModel->ubah($idDeteksiDini, $dataDD);

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function tabel()
  {
    $draw = intval($this->input->POST('draw'));
    $nomr = $this->input->POST('nomr');
    $history = $this->KankerPayudaraModel->history($nomr);
    $data = array();
    $no = 1;
    $dokter = null;
    $disabled = null;
    $status = null;

    foreach ($history->result() as $h) {
      if (isset($h->dokter)) {
        $dokter = '-';
      } else {
        $dokter = $h->dokter;
      }

      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status = 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        $h->perawat,
        $dokter,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H:i', strtotime($h->waktu)),
        $h->nilai_risk,
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-dd-payudara' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-dd-payudara' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-dokter-dd-payudara' class='btn btn-sm btn-info waves-effect' id='tbl-dokter-dd-payudara' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-user-md'></i> Dokter
          </button>
          <button type='button' href='#modal-perawat-dd-payudara' class='btn btn-sm btn-pink waves-effect' id='tbl-perawat-dd-payudara' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-user-nurse'></i> Perawat
          </button>
          <button type='button' href='#modal-gambar-dd-payudara' class='btn btn-sm btn-custom waves-effect' id='tbl-gambar-dd-payudara' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-image'></i> Gambar
          </button>
          <a href='/reports/simrskd/deteksidini/formanamnesis.php?format=pdf&id=" . $h->nokun . "' class='btn btn-sm btn-warning waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak
          </a>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nomr' => $post['nomr'],);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/emr/deteksiDini/KankerPayudara/history', $data);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array('status' => 0,);
    $this->KankerPayudaraModel->ubah($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}
