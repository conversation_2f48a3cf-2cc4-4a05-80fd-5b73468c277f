<?php
defined('BASEPATH') or exit('No direct script access allowed');

class IIKPP extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
        $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/paliatif/IIKPPModel'));
    }

    public function index()
    {
        $norm                   = $this->uri->segment(6);
        $nopen                  = $this->uri->segment(7);
        $nokun                  = $this->uri->segment(8);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        // $id_ruangan             = substr($nokun, 0, 9);
        // $hPaliatif              = $this->pengkajianAwalModel->historyPaliatifRI();
        // $getNomr                = $this->PengkajianPaliatifRIModel->getNomrPalRI($nopen);
        // $getIdEmr               = $getNomr['ID_EMR_KEPERAWATAN_DEWASA_RI'];
        // $getPengkajianPaliatif  = $this->PengkajianPaliatifRIModel->getPengkajianPaliatif($getIdEmr);
        $data = array(
            'norm'                          => $this->uri->segment(6),
            'nopen'                         => $this->uri->segment(7),
            'nokun'                         => $this->uri->segment(8),
            // 'HubunganPasien'                => $this->masterModel->referensi(1610),
            // 'getPengkajianPaliatif'         => $getPengkajianPaliatif,
            'getNomr'                       => $getNomr,
            // 'getIdEmr'                      => $getIdEmr,
            // 'kunjungan_pk'                  => $this->pengkajianAwalModel->kunjungan_pk($norm),
            // 'listVBPJS'                     => $this->pengkajianAwalModel->listVBPJS($norm),
            // 'id_ruangan'                    => $id_ruangan,
            // 'tindakan_rad'                  => $this->pengkajianAwalModel->tindakan_rad($norm),
            // 'hPaliatif'                     => $hPaliatif,
        );
        $this->load->view('rekam_medis/rawat_inap/paliatif/IIKPP/index', $data);
    }

    public function simpanIIKPP()
    {
        $this->db->trans_begin();
        date_default_timezone_set('Asia/Jakarta');

        $post = $this->input->post();

        if($post['PAR'] == 'insert'){
            $dataIIKPP = array (
                'norm'           => $post['norm'],
                'nopen'          => $post['nopen'],
                'nokun'          => $post['nokun'],
                'medis_1'        => $post['IIKPa'],
                'medis_2'        => $post['IIKPb'],
                'medis_3'        => $post['IIKPc'],
                'medis_4'        => $post['IIKPd'],
                'medis_5'        => $post['IIKPe'],
                'medis_6'        => $post['IIKPf'],
                'medis_7'        => $post['IIKPg'],
                'medis_8'        => $post['IIKPh'],
                'medis_9'        => $post['IIKPi'],
                'medis_10'       => $post['IIKPj'],
                'medis_11'       => $post['IIKPk'],
                'medis_12'       => $post['IIKPl'],
                'medis_13'       => $post['IIKPm'],
                'medis_14'       => $post['IIKPn'],
                'medis_15'       => $post['IIKPo'],
                'non_medis_A'    => $post['IIKPp'],
                'non_medis_B'    => $post['IIKPq'],
                'non_medis_C'    => $post['IIKPr'],
                'non_medis_D'    => $post['IIKPs'],
                'non_medis_E'    => $post['IIKPt'],
                'kesimpulan'     => $post['IIKPKesimpulan'],
                'created_at'     => date('Y-m-d H:i:s'),
                'created_by'     => $this->session->userdata('id'),
            );
    
            // echo "<pre>";print_r($dataIIKPP);echo "</pre>";
            $this->db->insert('medis.tb_instrumen_keperawatan_paliatif', $dataIIKPP);
            
            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }

        }elseif($post['PAR'] == 'update'){
            $dataIIKPPEdit = array (
                'medis_1'        => $post['IIKP_Edita'],
                'medis_2'        => $post['IIKP_Editb'],
                'medis_3'        => $post['IIKP_Editc'],
                'medis_4'        => $post['IIKP_Editd'],
                'medis_5'        => $post['IIKP_Edite'],
                'medis_6'        => $post['IIKP_Editf'],
                'medis_7'        => $post['IIKP_Editg'],
                'medis_8'        => $post['IIKP_Edith'],
                'medis_9'        => $post['IIKP_Editi'],
                'medis_10'       => $post['IIKP_Editj'],
                'medis_11'       => $post['IIKP_Editk'],
                'medis_12'       => $post['IIKP_Editl'],
                'medis_13'       => $post['IIKP_Editm'],
                'medis_14'       => $post['IIKP_Editn'],
                'medis_15'       => $post['IIKP_Edito'],
                'non_medis_A'    => $post['IIKP_Editp'],
                'non_medis_B'    => $post['IIKP_Editq'],
                'non_medis_C'    => $post['IIKP_Editr'],
                'non_medis_D'    => $post['IIKP_Edits'],
                'non_medis_E'    => $post['IIKP_Editt'],
                'kesimpulan'     => $post['IIKP_EditKesimpulan'],
                'updated_at'     => date('Y-m-d H:i:s'),
                'updated_by'     => $this->session->userdata('id'),
            );
            $this->db->where('id', $post['idIIKPP']);
            $this->db->update('medis.tb_instrumen_keperawatan_paliatif', $dataIIKPPEdit);

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }
        }

        echo json_encode($result);
    }


    public function historyIIKPP()
    {
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));

        $nomr = $this->input->post('nomr');
        // $nomr = $this->uri->segment(6);
        $listIIKPP = $this->IIKPPModel->listHistoryIIKPP($nomr);

        $data = array();
        $no = 1;
        foreach ($listIIKPP->result() as $IIKPP) {

            if($IIKPP->kesimpulan == 1){
                $pal = 'Ya';
            }else{
                $pal = 'Tidak';
            }

            $button = '<button type="button" href="#modalIIKPP" class="btn btn-primary btn-block" data-id="'.$IIKPP->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false" ><i class="fa fa-pencil-square-o"></i> View</button>';
            
            $data[] = array(
            $no,
            $IIKPP->nokun,
            $IIKPP->created_at,
            ($IIKPP->updated_at != NULL || $IIKPP->updated_at != "") ? $IIKPP->updated_at : '-',
            $pal,
            $IIKPP->OLEH,
            ($IIKPP->updated_by != NULL || $IIKPP->updated_by != "") ? $IIKPP->OLEH_UPDATE : '-',
            $button,

            );
            $no++;
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listIIKPP->num_rows(),
            "recordsFiltered" => $listIIKPP->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }

    public function modalIIKPP()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array(
            'id' => $id,
            'gpIIKPP' => $this->IIKPPModel->getIIKPP($id),
            'getNomr' => $getNomr,
        );

        $this->load->view('rekam_medis/rawat_inap/paliatif/IIKPP/view', $data);
    }

}
