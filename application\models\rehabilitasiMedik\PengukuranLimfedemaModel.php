<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengukuranLimfedemaModel extends MY_Model
{
  protected $_table_name = 'medis.tb_pengukuran_limfedema pl';
  protected $_primary_key = 'pl.id';
  protected $_order_by = 'pl.id';
  protected $_order_by_type = 'DESC';

  public function __construct()
  {
    parent::__construct();
  }

  public function rulesLengan()
  {
    return [
      [
        'field' => 'tanda_radang_lengan',
        'label' => 'Tanda radang lengan',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
      [
        'field' => 'pitting_lengan',
        'label' => 'Pitting lengan',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
      [
        'field' => 'nyeri_lengan',
        'label' => 'Nyeri lengan',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
    ];
  }

  public function rulesTungkai()
  {
    return [
      [
        'field' => 'tanda_radang_tungkai',
        'label' => 'Tanda radang tungkai',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
      [
        'field' => 'pitting_tungkai',
        'label' => 'Pitting tungkai',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
      [
        'field' => 'nyeri_tungkai',
        'label' => 'Nyeri tungkai',
        'rules' => 'trim|required',
        'errors' => [
          'required' => '%s wajib diisi',
        ]
      ],
    ];
  }

  public function karnofskyKategori()
  {
    $this->db->select('ka.id id_kategori, ka.deskripsi');
    $this->db->from('db_master.tb_karnofsky_kategori ka');

    $query = $this->db->get();
    $return = array();

    foreach ($query->result() as $kategori) {
      $return[$kategori->id_kategori] = $kategori;
      $return[$kategori->id_kategori]->subs = $this->karnofskyKriteria($kategori->id_kategori); // Get the categories sub categories
    }

    return $return;
  }

  public function karnofskyKriteria($id_kategori)
  {
    $this->db->select('kr.id id_kriteria, kr.index, kr.kriteria');
    $this->db->from('db_master.tb_karnofsky_kriteria kr');
    $this->db->where('id_karnofsky_kategori', $id_kategori);

    $query = $this->db->get();
    return $query->result();
  }

  public function simpan($data)
  {
    $this->db->insert('medis.tb_pengukuran_limfedema', $data);
    return $this->db->insert_id();
  }

  function panjangBagian($id)
  {
    $this->db->select(
      'pl.panjang_bagian_lengan1, pl.panjang_bagian_lengan2, pl.panjang_bagian_lengan3, pl.panjang_bagian_lengan4,
      pl.panjang_bagian_lengan5, pl.panjang_bagian_lengan6, pl.panjang_bagian_lengan7, pl.panjang_bagian_lengan8,
      pl.panjang_bagian_tungkai1, pl.panjang_bagian_tungkai2, pl.panjang_bagian_tungkai3, pl.panjang_bagian_tungkai4,
      pl.panjang_bagian_tungkai5, pl.panjang_bagian_tungkai6, pl.panjang_bagian_tungkai7, pl.panjang_bagian_tungkai8,
      pl.panjang_bagian_tungkai9, pl.panjang_bagian_tungkai10'
    );
    $this->db->from($this->_table_name);
    $this->db->where('pl.id_limfedema', $id);
    $this->db->order_by('created_at', 'asc');
    $query = $this->db->get();
    return $query->row_array();
  }

  public function tampilHistoryPengukuranLimfedema()
  {
    $this->db->select(
      'pl.id id_limfedema, pl.kunjungan, pl.lengan_kanan1, pl.lengan_kiri1, pl.lengan_kanan2, pl.lengan_kiri2,
      pl.lengan_kanan3, pl.lengan_kiri3, pl.lengan_kanan4, pl.lengan_kiri4, pl.lengan_kanan5, pl.lengan_kiri5,
      pl.lengan_kanan6, pl.lengan_kiri6, pl.lengan_kanan7, pl.lengan_kiri7, pl.lengan_kanan8, pl.lengan_kiri8,
      keluhan_lengan, tanda_radang_lengan, pitting_lengan, nyeri_lengan, keterangan_lengan, pl.tungkai_kanan1,
      pl.tungkai_kiri1, pl.tungkai_kanan2, pl.tungkai_kiri2, pl.tungkai_kanan3, pl.tungkai_kiri3, pl.tungkai_kanan4,
      pl.tungkai_kiri4, pl.tungkai_kanan5, pl.tungkai_kiri5, pl.tungkai_kanan6, pl.tungkai_kiri6, pl.tungkai_kanan7,
      pl.tungkai_kiri7, pl.tungkai_kanan8, pl.tungkai_kiri8, pl.tungkai_kanan9, pl.tungkai_kiri9, pl.tungkai_kanan10,
      pl.tungkai_kiri10, keluhan_tungkai, tanda_radang_tungkai, pitting_tungkai, nyeri_tungkai, keterangan_tungkai,
      master.getNamaLengkapPegawai(dok.NIP) dokter, pl.tanggal'
    );
    $this->db->from($this->_table_name);
    $this->db->join('aplikasi.pengguna a', 'a.ID = pl.dokter_pengirim');
    $this->db->join('master.dokter dok', 'dok.NIP = a.NIP', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = pl.kunjungan', 'left');
    $this->db->where('pl.kunjungan', $this->uri->segment(5));
    $this->db->order_by('pl.tanggal', 'ASC');

    $query = $this->db->get();
    return $query->result_array();
  }

  public function history($id_limfedema, $jumlah = false)
  {
    $this->db->select(
      'pl.id, pl.tanggal, pl.sisi, pl.panjang_bagian_lengan1, pl.lengan_kanan1, pl.lengan_kiri1,
      pl.panjang_bagian_lengan2, pl.lengan_kanan2, pl.lengan_kiri2, pl.panjang_bagian_lengan3, pl.lengan_kanan3,
      pl.lengan_kiri3, pl.panjang_bagian_lengan4, pl.lengan_kanan4, pl.lengan_kiri4, pl.panjang_bagian_lengan5,
      pl.lengan_kanan5, pl.lengan_kiri5, pl.panjang_bagian_lengan6, pl.lengan_kanan6, pl.lengan_kiri6,
      pl.panjang_bagian_lengan7, pl.lengan_kanan7, pl.lengan_kiri7, pl.panjang_bagian_lengan8, pl.lengan_kanan8,
      pl.lengan_kiri8, tr_lengan.variabel tanda_radang_lengan, p_lengan.variabel pitting_lengan,
      n_lengan.variabel nyeri_lengan, pl.keterangan_lengan, pl.panjang_bagian_tungkai1, pl.tungkai_kanan1,
      pl.tungkai_kiri1, pl.panjang_bagian_tungkai2, pl.tungkai_kanan2, pl.tungkai_kiri2, pl.panjang_bagian_tungkai3,
      pl.tungkai_kanan3, pl.tungkai_kiri3, pl.panjang_bagian_tungkai4, pl.tungkai_kanan4, pl.tungkai_kiri4,
      pl.panjang_bagian_tungkai5, pl.tungkai_kanan5, pl.tungkai_kiri5, pl.panjang_bagian_tungkai6, pl.tungkai_kanan6,
      pl.tungkai_kiri6, pl.panjang_bagian_tungkai7, pl.tungkai_kanan7, pl.tungkai_kiri7, pl.panjang_bagian_tungkai8,
      pl.tungkai_kanan8, pl.tungkai_kiri8, pl.panjang_bagian_tungkai9, pl.tungkai_kanan9, pl.tungkai_kiri9,
      pl.panjang_bagian_tungkai10, pl.tungkai_kanan10, pl.tungkai_kiri10, tr_tungkai.variabel tanda_radang_tungkai,
      p_tungkai.variabel pitting_tungkai, n_tungkai.variabel nyeri_tungkai, pl.keterangan_tungkai,
      master.getNamaLengkapPegawai(dok.NIP) oleh, pl.created_at'
    );
    $this->db->from($this->_table_name);
    $this->db->join('aplikasi.pengguna a', 'a.ID = pl.dokter_pengirim');
    $this->db->join('master.dokter dok', 'dok.NIP = a.NIP', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = pl.kunjungan', 'left');
    $this->db->join('db_master.variabel tr_lengan', 'tr_lengan.id_variabel = pl.tanda_radang_lengan', 'left');
    $this->db->join('db_master.variabel p_lengan', 'p_lengan.id_variabel = pl.pitting_lengan', 'left');
    $this->db->join('db_master.variabel n_lengan', 'n_lengan.id_variabel = pl.nyeri_lengan', 'left');
    $this->db->join('db_master.variabel tr_tungkai', 'tr_tungkai.id_variabel = pl.tanda_radang_tungkai', 'left');
    $this->db->join('db_master.variabel p_tungkai', 'p_tungkai.id_variabel = pl.pitting_tungkai', 'left');
    $this->db->join('db_master.variabel n_tungkai', 'n_tungkai.id_variabel = pl.nyeri_tungkai', 'left');
    $this->db->where('pl.status', 1);
    $this->db->where('pl.id_limfedema', $id_limfedema);
    $this->db->order_by('pl.created_at', 'asc');

    $query = $this->db->get();
    if ($jumlah) {
      return $query->num_rows();
    } else {
      return $query->result_array();
    }
  }

  public function ubah($id, $data)
  {
    $this->db->where('id', $id);
    $this->db->update('medis.tb_pengukuran_limfedema', $data);
  }

  public function detail($id)
  {
    $this->db->select(
      'pl.id, pl.id_limfedema, pl.tanggal, pl.sisi, pl.panjang_bagian_lengan1, pl.lengan_kanan1, pl.lengan_kiri1,
      pl.panjang_bagian_lengan2, pl.lengan_kanan2, pl.lengan_kiri2, pl.panjang_bagian_lengan3, pl.lengan_kanan3,
      pl.lengan_kiri3, pl.panjang_bagian_lengan4, pl.lengan_kanan4, pl.lengan_kiri4, pl.panjang_bagian_lengan5,
      pl.lengan_kanan5, pl.lengan_kiri5, pl.panjang_bagian_lengan6, pl.lengan_kanan6, pl.lengan_kiri6,
      pl.panjang_bagian_lengan7, pl.lengan_kanan7, pl.lengan_kiri7, pl.panjang_bagian_lengan8, pl.lengan_kanan8,
      pl.lengan_kiri8, pl.tanda_radang_lengan, pl.pitting_lengan, pl.nyeri_lengan, pl.keterangan_lengan,
      pl.panjang_bagian_tungkai1, pl.tungkai_kanan1, pl.tungkai_kiri1, pl.panjang_bagian_tungkai2, pl.tungkai_kanan2,
      pl.tungkai_kiri2, pl.panjang_bagian_tungkai3, pl.tungkai_kanan3, pl.tungkai_kiri3, pl.panjang_bagian_tungkai4,
      pl.tungkai_kanan4, pl.tungkai_kiri4, pl.panjang_bagian_tungkai5, pl.tungkai_kanan5, pl.tungkai_kiri5,
      pl.panjang_bagian_tungkai6, pl.tungkai_kanan6, pl.tungkai_kiri6, pl.panjang_bagian_tungkai7, pl.tungkai_kanan7,
      pl.tungkai_kiri7, pl.panjang_bagian_tungkai8, pl.tungkai_kanan8, pl.tungkai_kiri8, pl.panjang_bagian_tungkai9,
      pl.tungkai_kanan9, pl.tungkai_kiri9, pl.panjang_bagian_tungkai10, pl.tungkai_kanan10, pl.tungkai_kiri10,
      pl.tanda_radang_tungkai, pl.pitting_tungkai, pl.nyeri_tungkai, pl.keterangan_tungkai, pl1.bagian'
    );
    $this->db->from($this->_table_name);
    $this->db->join('medis.tb_pengukuran_limfedema1 pl1', 'pl1.id = pl.id_limfedema', 'left');
    $this->db->where('pl.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file PengukuranLimfedemaModel.php */
/* Location: ./application/models/rehabilitasiMedik/PengukuranLimfedemaModel.php */