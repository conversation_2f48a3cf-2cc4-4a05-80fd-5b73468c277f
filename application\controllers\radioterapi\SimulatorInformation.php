<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SimulatorInformation extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'radioterapi/simulatorInformationModel'));
  }

  public function statusSimulatorInformation()
  {
    $nomr  = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $gsi   = $this->masterModel->gambar_simulator();

    $data = array(
      'title' => 'Bagian Tubuh Status Lokalis',
      'isi'   => 'Pengkajian/emr/statusLokalis/statusSimulatorInformation',
      'nomr'  => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'gsi'   => $gsi,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function tampilGambar()
  {
    $idGambar = $this->input->post('idGambar');

    $siGb = $this->masterModel->getPictSimulatorInforation($idGambar);

    $gbSi = base_url("assets/admin/assets/images/simulator_information/" . $siGb['file']);

    echo '
    <div class="demoSiteMarking" id="colors_demoSiteMarking">
    <div class="toolsSiteMarking">
    <a href="#my_canvassimulator_information" data-tool="marker">Marker</a>
    <a href="#my_canvassimulator_information" data-tool="eraser">Eraser</a>
    </div>';
    echo "
    <div class='form-group'>
    <canvas id='my_canvassimulator_information' width='450' height='450' style='background:url($gbSi) no-repeat;'></canvas>
    <input type='hidden' name='img_valsimulator_information' id='img_valsimulator_information' value='' />
    </div></div>";
  }

  public function simpanFotoSimulatorInformation()
  {
    $nomr       = $this->input->post('nomr');
    $nopen      = $this->input->post('nopen');
    $nokun      = $this->input->post('nokun');
    $idPengguna = $this->session->userdata('id');;

    $data = array(
      'nomr'       => $nomr,
      'nopen'      => $nopen,
      'nokun'      => $nokun,
      'idPengguna' => $idPengguna,
      'judul'      => $this->input->post('judulSimulatorInformation'),
      'data'       => file_get_contents($this->input->post('img_valsimulator_information')),
      'catatan'    => $this->input->post('catatanSimulatorInformation'),
    );
    $this->simulatorInformationModel->simpanFotoSimulatorInformation($data);
    redirect(base_url("radioterapi/SimulatorInformation/statusSimulatorInformation/" . $nomr . "/" . $nopen . "/" . $nokun . ""));
  }

  public function tblHistorySimulatorInformation()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listSimulatorInformation = $this->simulatorInformationModel->listsimulator_infotmation($nomr);

    $data = array();
    $no = 1;
    foreach ($listSimulatorInformation->result() as $historySi) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s", strtotime($historySi->tanggal)),
        $historySi->judul,
        '<input type="checkbox" class="cekstatusSi" value="' . $historySi->id . '">',
        '<a href="#" class="clickSi btn-sm btn-block btn-primary" data-id="' . $historySi->id . '" ><i class="fa fa-eye"></i> View</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listSimulatorInformation->num_rows(),
      "recordsFiltered" => $listSimulatorInformation->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function updateStatusSimulatorInformation()
  {
    $id = $this->input->post('id');
    $data = array(
      'status' => 0,
    );
    $this->simulatorInformationModel->updateStatusSm($id, $data);
  }

  public function keteranganSimulatorInformation()
  {
    $id = $this->input->post('id');
    $hasilFotoSi = $this->simulatorInformationModel->hasilFotoSi($id);

    echo '
    <div class="row">
    <div class="col-md-12">
    <div class="form-group">
    <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . date("d-m-Y H:i:s", strtotime($hasilFotoSi['tanggal'])) . '</span> ]</h4>
    </div>
    </div>
    </div>

    <div class="row">
    <div class="col-md-12">
    <div class="form-group">
    <label for="judulSiteMarking">Judul Site Marking</label>
    <input type="text" class="form-control" placeholder="[ Judul Site Marking ]" value="' . $hasilFotoSi['judul'] . '" readonly>
    </div>
    </div>
    </div>

    <div class="row">
    <div class="col-md-6">
    <div class="form-group">
    <label>Hasil Foto Site Marking</label><br>
    <img src="data:image;base64,' . base64_encode($hasilFotoSi['data']) . '">
    </div>
    </div>
    <div class="col-md-6">
    <div class="form-group">
    <label for="catatan">Catatan</label>
    <textarea class="form-control" cols="15" rows="10" placeholder="[Catatan ]"  readonly>' . $hasilFotoSi['catatan'] . '</textarea>
    </div>
    </div>
    </div>';
  }

  public function simpanFSimulatorInformationDr()
  {
    $config['upload_path'] = "../../../../mnt/upload_emr/uploadSimulator";
    // $config['upload_path']   = "./bankdata";
    $config['allowed_types'] = 'jpeg|jpg|png';
    $config['encrypt_name']  = true;
    // $config['max_size'] = 2048;

    $this->load->library('upload', $config);

    if (empty($_FILES['uploadSimulator']['name'])) {
      $this->db->trans_begin();
      $post   = $this->input->post();

      if (isset($post["status_gambarSi_not"]) == 'on') {
        $simulatorGambar = 1;
      } else {
        $simulatorGambar = 0;
      }

      $dataSimulatorInformationDr = array(
        'nokun'           => $post["nokun"],
        'diagnosa'        => $post["diagnosaSi"],
        'block'           => isset($post["directionBlockSi"]) ? $post["directionBlockSi"] : 0,
        'deskBlock'       => isset($post["descBlock"]) ? $post["descBlock"] : 0,
        'simulatorGambar' => $simulatorGambar,
        'oleh'            => $this->session->userdata('id'),
      );
      // echo "<pre>"; print_r($dataSimulatorInformationDr); echo "</pre>"; exit();
      $id = $this->simulatorInformationModel->simpanSimulatorInformationDr($dataSimulatorInformationDr);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }

      echo json_encode($result);
    } else {
      if (!$this->upload->do_upload("uploadSimulator")) {
        $error = array('error' => $this->upload->display_errors());
        $this->session->set_flashdata('error', $error['error']);
      } else {
        $this->db->trans_begin();
        $result = $this->upload->data();
        $post   = $this->input->post();

        if (isset($post["status_gambarSi_not"]) == 'on') {
          $simulatorGambar = 1;
        } else {
          $simulatorGambar = 0;
        }

        $dataSimulatorInformationDr = array(
          'nokun'           => $post["nokun"],
          'diagnosa'        => $post["diagnosaSi"],
          'block'           => isset($post["directionBlockSi"]) ? $post["directionBlockSi"] : 0,
          'deskBlock'       => isset($post["descBlock"]) ? $post["descBlock"] : 0,
          'simulatorGambar' => $simulatorGambar,
          'oleh'            => $this->session->userdata('id'),
        );
        $id = $this->simulatorInformationModel->simpanSimulatorInformationDr($dataSimulatorInformationDr);

        $dataUpload = array(
          'IDSIMULATOR'   => $id,
          'NAME_PIC'      => $result['file_name'],
          'TYPE'          => $result['file_ext'],
          'SIZE'          => $result['file_size'],
          'ORIGINAL_NAME' => $result['client_name'],
          'OLEH'          => $this->session->userdata('id'),
        );

        $this->simulatorInformationModel->insertTakePhoto($dataUpload);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      }
    }
  }

  public function simpanFSimulatorInformationRadiografer()
  {
    $post = $this->input->post();

    $this->db->trans_begin(); // Mulai transaksi

    $data = array(
      'nokun'               => $post["nokun"] ?? null,
      'id_simulator_dokter' => $post["idsidr"] ?? null,
      'treamentField'       => $post["treatmentFieldSi"] ?? 0,
      'desk_treamentField'  => $post["descTeatment"] ?? null,
      'positionofPastient'  => $post["positonPatientTspSi"] ?? 0,
      'positionofPastient2'  => $post["positonPatientTspSi2"] ?? 0,
      'mask'                => $post["maskSi"] ?? 0,
      'headerstNo'          => $post["Headerst_No"] ?? null,
      'neckExtention'       => $post["neckExtentionSi"] ?? 0,
      'mouthBite'           => $post["mouthBiteSi"] ?? 0,
      'kneeRest'            => $post["kneeRestSi"] ?? 0,
      'matras'              => $post["matrasSi"] ?? 0,
      'armPos'              => $post["armPosSi"] ?? 0,
      'waxTissue'           => $post["waxTissue"] ?? null,
      'setUp'               => $post["setUp"] ?? 0,
      'desk_setUp'          => $post["setUpDesk"] ?? null,
      'linacMachine'        => $post["linacMachineSi"] ?? 0,
      'oleh'                => $this->session->userdata('id') ?? null,
      'oleh2'               => $post["oleh2"] ?? null,
    );


    $id_simulator = $this->simulatorInformationModel->simpanSimulatorInformationRadiografer($data);

    if (!$id_simulator) {
      $error = $this->db->error();
      $this->db->trans_rollback();
      echo json_encode([
        'status'    => 'error',
        'message'   => 'Gagal menyimpan data simulator.',
        'db_error'  => $error
      ]);
      return;
    }

    $uploadPath = '../upload_emr/radioterapi/simulatorinformation/';
    if (!is_dir($uploadPath)) {
      mkdir($uploadPath, 0755, true);
    }

    $this->load->library('upload');

    if (!empty($post['setupNote'])) {
      foreach ($post['setupNote'] as $key => $setupNoteText) {
        $filePath = null;
        $fileName = null;

        if (!empty($_FILES['setupNoteimage']['name'][$key])) {
          $_FILES['file']['name']     = $_FILES['setupNoteimage']['name'][$key];
          $_FILES['file']['type']     = $_FILES['setupNoteimage']['type'][$key];
          $_FILES['file']['tmp_name'] = $_FILES['setupNoteimage']['tmp_name'][$key];
          $_FILES['file']['error']    = $_FILES['setupNoteimage']['error'][$key];
          $_FILES['file']['size']     = $_FILES['setupNoteimage']['size'][$key];

          $config['upload_path']   = $uploadPath;
          $config['allowed_types'] = 'jpg|jpeg|png|pdf|doc|docx';
          $config['max_size']      = 2048;
          $randomString            = bin2hex(random_bytes(5));
          $newFileName             = time() . '_' . $randomString;
          $config['file_name']     = $newFileName;

          $this->upload->initialize($config);

          if (!$this->upload->do_upload('file')) {
            $this->db->trans_rollback();
            echo json_encode([
              'status' => 'error',
              'message' => 'Gagal mengupload file: ' . $this->upload->display_errors()
            ]);
            return;
          }

          $fileData = $this->upload->data();
          $filePath = $uploadPath;
          $fileName = $fileData['file_name'];
        }

        $detailData = array(
          'id_si_rad'   => $id_simulator,
          'note'        => $setupNoteText,
          'file_path'   => $filePath,
          'file_name'   => $fileName,
          'status'      => 1,
          'updated_by'  => $this->session->userdata('id'),
        );

        $insertDetail = $this->simulatorInformationModel->simpanSimulatorInformationRadiograferDetail($detailData);

        if (!$insertDetail) {
          $error = $this->db->error();
          echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan detail simulator.', 'db_error' => $error]);
          return;
        }
      }
    }

    $this->db->trans_commit();
    echo json_encode(['status' => 'success', 'message' => 'Data berhasil disimpan.']);
  }


  public function simpanFSimulatorInformationFisikaMedis()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $data = array(
      'nokun'                 => $post["nokun"],
      'id_simulator_dokter'   => $post["idsidr"],
      'phase1'                => $post["phase1"],
      'phase2'                => $post["phase2"],
      'phase3'                => $post["phase3"],
      'brachy'                => $post["branchy"],
      'note'                  => $post["noteSi"],
      'oleh'                  => $this->session->userdata('id'),
      'oleh2'                 => $post['oleh2']
    );
    // echo "<pre>";print_r($data);exit();
    $id = $this->simulatorInformationModel->simpanSimulatorInformationFisikaMedis($data);

    $dataTable = array();
    $indexTable = 0;

    if (isset($post['dateSi'])) {
      foreach ($post['dateSi'] as $inputTable) {
        if ($post['dateSi'][$indexTable] != "") {
          array_push(
            $dataTable,
            array(
              'idTbSimulatorFisikaMedis' => $id,
              'date'                     => $post["dateSi"][$indexTable],
              'siteField'                => $post["siteSi"][$indexTable],
              'energy'                   => $post["energySi"][$indexTable],
              'field'                    => $post["fieldSi"][$indexTable],
              'sepDept'                  => $post["sepDepSi"][$indexTable],
              'gantrAngel'               => $post["gantrySi"][$indexTable],
              'collAngle'                => $post["collSi"][$indexTable],
              'dosePerfraction'          => $post["DoseperSi"][$indexTable],
              'treatment'                => $post["treatmentSi"][$indexTable],
            )
          );
        }
        $indexTable++;
      }
      $this->db->insert_batch('medis.tb_simulatorInformationFisikaMedisDetail', $dataTable);
    }

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historySimulatorInformation()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->POST('nomr');

    $listHistorySi = $this->simulatorInformationModel->historySimulatorInformation($nomr);

    $data = array();
    $no = 1;
    foreach ($listHistorySi->result() as $lhs) {
      $profesi = $this->session->userdata('profesi');

      // Dokter
      $disableDr = (empty($lhs->IDSIDR) && $profesi != 11);
      $styleDr = $disableDr ? 'pointer-events: none; opacity: 0.5;' : '';
      $textDr = $disableDr ? 'Belum Ada Form' : 'Form Dokter';
      $btnDr = '<a href="#modalSiDr" class="btn btn-purple btn-block" 
                    style="' . $styleDr . '" 
                    data-id="' . (!empty($lhs->IDSIDR) ? $lhs->IDSIDR : '') . '" 
                    data-toggle="modal" 
                    data-backdrop="static" 
                    data-keyboard="false">
                        <i class="fa fa-file"></i> ' . $textDr . '
                    </a><br>'.(!empty($lhs->TANGGALSIDR) ? $lhs->TANGGALSIDR : '');

      // Radiografer
      $disableRad = (empty($lhs->IDSIRAD) && $profesi != 8);
      $styleRad = $disableRad ? 'pointer-events: none; opacity: 0.5;' : '';
      $textRad = $disableRad ? 'Belum Ada Form' : 'Form Radiografer';
      $btnRad = '<a href="#modalSiRad" class="btn btn-success btn-block" 
                    style="' . $styleRad . '" 
                    data-idsimdoc="' . (!empty($lhs->IDSIDR) ? $lhs->IDSIDR : '') . '" 
                    data-id="' . (!empty($lhs->IDSIRAD) ? $lhs->IDSIRAD : '') . '" 
                    data-toggle="modal" 
                    data-backdrop="static" 
                    data-keyboard="false">
                        <i class="fa fa-file"></i> ' . $textRad . '
                    </a><br>'.(!empty($lhs->TANGGALSIRAD) ? $lhs->TANGGALSIRAD : '');

      // Fisika Medis
      $disableFis = (empty($lhs->IDSIFIS) && $profesi != 17);
      $styleFis = $disableFis ? 'pointer-events: none; opacity: 0.5;' : '';
      $textFis = $disableFis ? 'Belum Ada Form' : 'Form Fisika Medis';
      $btnFis = '<a href="#modalSiFis" class="btn btn-info btn-block" 
                    style="' . $styleFis . '" 
                    data-id="' . (!empty($lhs->IDSIFIS) ? $lhs->IDSIFIS : '') . '" 
                    data-idsimdoc="' . (!empty($lhs->IDSIDR) ? $lhs->IDSIDR : '') . '" 
                    data-toggle="modal" 
                    data-backdrop="static" 
                    data-keyboard="false">
                        <i class="fa fa-file"></i> ' . $textFis . '
                    </a><br>'.(!empty($lhs->TANGGALSIFIS) ? $lhs->TANGGALSIFIS : '');

      // Status Verifikasi
      if ($lhs->STATUS == 1) {
        if ($profesi == 11 && !empty($lhs->IDSIRAD) && !empty($lhs->IDSIFIS)) {
          if ($lhs->oleh_dr == $this->session->userdata('id')) {
            $statusVerif = '<a class="btn btn-success btn-block btnVerifSi" data-id="' . $lhs->IDSIDR . '">
                                <i class="fa fa-check"></i> Verif
                            </a>';
          } else {
            $statusVerif = 'Belum Verifikasi Dokter';
          }
        } elseif ($profesi != 11 && !empty($lhs->IDSIRAD) && !empty($lhs->IDSIFIS)) {
          $statusVerif = 'Belum Verifikasi Dokter';
        } elseif (empty($lhs->IDSIRAD) || empty($lhs->IDSIFIS)) {
          $statusVerif = 'Form Belum Lengkap';
        }
      } elseif ($lhs->STATUS == 2) {
        $statusVerif = 'Sudah Terverifikasi Dokter';
      } else {
        $statusVerif = 'Belum Verifikasi Dokter';
      }

      $data[] = array(
        $no,
        $lhs->nokun,
        date("d-m-Y", strtotime($lhs->TANGGALSIDR)),
        $btnDr,
        $btnRad,
        $btnFis,
        '<a href="/reports/simrskd/ctsimulator/informasictsimulator.php?format=pdf&nokun=' . $lhs->nokun . '" class="btn btn-warning btn-block" target="_blank">
                  <i class="fa fa-print"></i> View
               </a>',
        $statusVerif
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listHistorySi->num_rows(),
      "recordsFiltered" => $listHistorySi->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }


  public function modalSiDr()
  {
    $id = $this->input->post('id');
    $gsiDr = $this->simulatorInformationModel->getSimulatorInformationDr($id);

    $data = array(
      'id'              => $id,
      'gsiDr'           => $gsiDr,
      'Direction_Block' => $this->masterModel->referensi(714),
    );

    $this->load->view('Pengkajian/radioTerapi/editSimulatorInformationDr', $data);
  }

  public function modalSiRad()
  {
    $id = $this->input->post('id');
    $idsimdoc = $this->input->post('idsimdoc');
    $nokun = $this->input->post('nokun');

    // Ambil data referensi sekali saja
    $data = array(
      'linacMachine'        => $this->masterModel->referensi(696),
      'Position_of_Patient' => $this->masterModel->referensi(678),
      'Mask'                => $this->masterModel->referensi(679),
      'Neck_Extention'      => $this->masterModel->referensi(684),
      'Mouth_Bite'          => $this->masterModel->referensi(685),
      'Arm_Pos'             => $this->masterModel->referensi(687),
      'Knee_Rest'           => $this->masterModel->referensi(689),
      'Matras'              => $this->masterModel->referensi(692),
      'Treatment_Field'     => $this->masterModel->referensi(713),
      'Set_Up'              => $this->masterModel->referensi(716),
      'wax_tissue'          => $this->masterModel->referensi(1857)
    );
    $data['radiografer'] = $this->masterModel->radiografer();

    // Jika id ada, ambil data dari model dan tambahkan ke $data
    if (!empty($id)) {
      $data['id'] = $id;
      $data['gsiRad']       = $this->simulatorInformationModel->getSimulatorInformationRad($id);
      $data['siRadDetail']  = $this->simulatorInformationModel->getSiRadDetail($id);
    } else {
      $data_si_dr = $this->simulatorInformationModel->getSimulatorInformationDr($idsimdoc);
      $data['datSiDr'] = $data_si_dr;
      $data['getNomr'] = $this->pengkajianAwalModel->getNomr($nokun);
    }

    // Tentukan view yang akan dimuat berdasarkan kondisi
    $view = empty($id) ? 'Pengkajian/radioTerapi/addSimulatorInformationRad' : 'Pengkajian/radioTerapi/editSimulatorInformationRad';

    // Load view
    $this->load->view($view, $data);
  }

  public function modalSiFis()
  {
    $id = $this->input->post('id');
    $idsimdoc = $this->input->post('idsimdoc');
    $nokun = $this->input->post('nokun');
    $gsiFis = $this->simulatorInformationModel->getSimulatorInformationFis($id);
    $listbSiEdit = $this->simulatorInformationModel->tb_simulatorInformationFisikaMedisDetail($id);
    $fismed = $this->masterModel->fismed();
    // print_r($fismed);
    $data['fismed'] = $fismed;
    if (!empty($id)) {
      $data = [
        'id'          => $id,
        'gsiFis'      => $gsiFis,
        'listbSiEdit' => $listbSiEdit,
        'fismed'      => $fismed,
      ];
    } else {
      $data_si_dr = $this->simulatorInformationModel->getSimulatorInformationDr($idsimdoc);
      $data['datSiDr'] = $data_si_dr;
      $data['getNomr'] = $this->pengkajianAwalModel->getNomr($nokun);
    }
    $view = empty($id) ? 'Pengkajian/radioTerapi/addSimulatorInformationFis' : 'Pengkajian/radioTerapi/editSimulatorInformationFis';
    $this->load->view($view, $data);
  }

  public function updateFSimulatorInformationDr()
  {
    $id = $this->input->post('id');
    $post = $this->input->post();

    if (isset($post["status_gambarSi_notEdit"]) == 1) {
      $simulatorGambar = 1;
    } else {
      $simulatorGambar = 0;
    }

    $data = array(
      'diagnosa'        => $post["diagnosaSiEdit"],
      'block'           => $post["directionBlockSiEdit"],
      'deskBlock'       => $post["descBlockEdit"],
      'simulatorGambar' => $simulatorGambar,
    );
    $this->simulatorInformationModel->updateSimulatorInformationDr($data, $id);
  }

  public function updateFSimulatorInformationRadiografer()
  {
    $id = $this->input->post('id');
    $post = $this->input->post();

    // print_r($post);
    // exit();

    // Mulai transaksi
    $this->db->trans_begin();

    // Data utama
    $data = array(
      'treamentField'       => $post["treatmentFieldSiEdit"] ?? null,
      'desk_treamentField'  => $post["descTeatmentEdit"] ?? null,
      'positionofPastient'  => $post["positonPatientTspSiEdit"] ?? null,
      'positionofPastient2' => $post["positonPatientTspSi2Edit"] ?? null,
      'mask'                => $post["maskSiEdit"] ?? null,
      'headerstNo'          => $post["Headerst_NoEdit"] ?? null,
      'neckExtention'       => $post["neckExtentionSiEdit"] ?? null,
      'mouthBite'           => $post["mouthBiteSiEdit"] ?? null,
      'kneeRest'            => $post["kneeRestSiEdit"] ?? null,
      'matras'              => $post["matrasSiEdit"] ?? null,
      'armPos'              => $post["armPosSiEdit"] ?? null,
      'waxTissue'           => $post["waxTissueEdit"] ?? null,
      'setUp'               => $post["setUpEdit"] ?? null,
      'desk_setUp'          => $post["setUpDeskEdit"] ?? null,
      'linacMachine'        => $post["linacMachineSiEdit"] ?? null,
      'oleh2'               => $post["oleh2"] ?? null,
    );

    // Update data utama
    $update = $this->simulatorInformationModel->updateSimulatorInformationRadiografer($data, $id);
    if ($update === false) {
      $this->db->trans_rollback();
      echo json_encode(['status' => 'failed', 'message' => 'Gagal update data utama']);
      return;
    }

    // Hapus detail yang dihapus
    $deletedIds = $this->input->post('deletedIds') ?? [];
    if (!empty($deletedIds)) {
      $dataraddetail = ['status' => 0];
      $this->simulatorInformationModel->udtSiRadDetail($deletedIds, $dataraddetail);
    }


    // Simpan catatan setup (jika ada)
    if (!empty($post['setupNote'])) {
      $uploadPath = '../upload_emr/radioterapi/simulatorinformation/';
      if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
      }

      foreach ($post['setupNote'] as $key => $setupNoteText) {
        $filePath = null;
        $fileName = null;

        if (!empty($_FILES['setupNoteimage']['name'][$key])) {
          $_FILES['file']['name']     = $_FILES['setupNoteimage']['name'][$key];
          $_FILES['file']['type']     = $_FILES['setupNoteimage']['type'][$key];
          $_FILES['file']['tmp_name'] = $_FILES['setupNoteimage']['tmp_name'][$key];
          $_FILES['file']['error']    = $_FILES['setupNoteimage']['error'][$key];
          $_FILES['file']['size']     = $_FILES['setupNoteimage']['size'][$key];

          $config['upload_path']   = $uploadPath;
          $config['allowed_types'] = 'jpg|jpeg|png|pdf|doc|docx';
          $config['max_size']      = 2048;
          $randomString            = bin2hex(random_bytes(5));
          $newFileName             = time() . '_' . $randomString;
          $config['file_name']     = $newFileName;

          $this->upload->initialize($config);

          if (!$this->upload->do_upload('file')) {
            $this->db->trans_rollback();
            echo json_encode([
              'status' => 'error',
              'message' => 'Gagal mengupload file: ' . $this->upload->display_errors()
            ]);
            return;
          }

          $fileData = $this->upload->data();
          $filePath = $uploadPath;
          $fileName = $fileData['file_name'];
        }

        $detailData = array(
          'id_si_rad'  => $id,
          'note'       => $setupNoteText,
          'file_path'  => $filePath,
          'file_name'  => $fileName,
          'status'     => 1,
          'updated_by' => $this->session->userdata('id'),
        );

        $insertDetail = $this->simulatorInformationModel->simpanSimulatorInformationRadiograferDetail($detailData);
        if (!$insertDetail) {
          $this->db->trans_rollback();
          echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan detail simulator.']);
          return;
        }
      }
    }

    // Jika semua proses berhasil, commit transaksi
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      echo json_encode(['status' => 'failed', 'message' => 'Terjadi kesalahan saat menyimpan data']);
    } else {
      $this->db->trans_commit();
      echo json_encode(['status' => 'success']);
    }
  }



  public function updateFSimulatorInformationFisikaMedis()
  {
    $this->db->trans_begin();
    $id = $this->input->post('id');
    $post = $this->input->post();

    $data = array(
      'phase1' => $post["phase1Edit"],
      'phase2' => $post["phase2Edit"],
      'phase3' => $post["phase3Edit"],
      'brachy' => $post["branchyEdit"],
      'note'   => $post["noteSiEdit"],
      'oleh2'   => $post["oleh2"],
    );

    $this->simulatorInformationModel->updateSimulatorInformationFisikaMedis($data, $id);
    $this->db->delete('medis.tb_simulatorInformationFisikaMedisDetail', array('idTbSimulatorFisikaMedis' => $id));
    $dataTable = array();
    $indexTable = 0;

    if (isset($post['dateSiEdit'])) {
      foreach ($post['dateSiEdit'] as $inputTable) {
        if ($post['dateSiEdit'][$indexTable] != "") {
          array_push(
            $dataTable,
            array(
              'idTbSimulatorFisikaMedis' => $id,
              'date'                     => $post["dateSiEdit"][$indexTable],
              'siteField'                => $post["siteSiEdit"][$indexTable],
              'energy'                   => $post["energySiEdit"][$indexTable],
              'field'                    => $post["fieldSiEdit"][$indexTable],
              'sepDept'                  => $post["sepDepSiEdit"][$indexTable],
              'gantrAngel'               => $post["gantrySiEdit"][$indexTable],
              'collAngle'                => $post["collSiEdit"][$indexTable],
              'dosePerfraction'          => $post["DoseperSiEdit"][$indexTable],
              'treatment'                => $post["treatmentSiEdit"][$indexTable],
            )
          );
        }
        $indexTable++;
      }
      $this->db->insert_batch('medis.tb_simulatorInformationFisikaMedisDetail', $dataTable);
    }

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyUploadSimulatorInformation()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->POST('nomr');

    $listHistoryUploadSi = $this->simulatorInformationModel->historyUploadSimulatorInformation($nomr);

    $data = array();
    $no = 1;
    foreach ($listHistoryUploadSi->result() as $lhus) {
      $data[] = array(
        $no,
        $lhus->ORIGINAL_NAME,
        date("d-m-Y H:i:s", strtotime($lhus->TANGGAL)),
        $lhus->OLEH,
        '<a href="#viewFileSimulator" class="btn btn-warning btn-block btn-sm" data-toggle="modal" data-id="' . $lhus->ID . '" data-backdrop="static" data-keyboard="false"><i class="fa fa-eye"></i> View</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listHistoryUploadSi->num_rows(),
      "recordsFiltered" => $listHistoryUploadSi->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function getFileSimulator()
  {
    $id = $this->input->post('id');
    $embed = $this->simulatorInformationModel->downloadFileSimulator($id);
?>
    <embed class="media" src="../../../../mnt/upload_emr/uploadSimulator/<?php echo $embed['NAME_PIC'] ?>" width="100%;" height="700px;"></embed>

    <!-- <embed class="media" src="<?= base_url() ?>bankdata/<?php echo $embed['NAME_PIC'] ?>" width="100%;" height="700px;"></embed> -->
<?php
  }


  public function verifSimulator()
  {
    $id = $this->input->post('id'); // Ambil ID dari request POST
    if (!$id) {
      echo json_encode(['status' => 'failed', 'message' => 'ID tidak ditemukan']);
      return;
    }
    // echo"$id";
    // exit();

    $data = ['status' => 2]; // Set status menjadi 2// Sesuaikan dengan nama model
    $this->simulatorInformationModel->updateSimulatorInformationDr($data, $id);
  }

  public function checkSiDr()
  {
    $nokun = $this->input->post('nokun'); // Ambil data nokun dari request
    $result = $this->simulatorInformationModel->getSiDrByNokun($nokun);

    if ($result) {
      echo json_encode(['status' => 'exist']); // Data ditemukan
    } else {
      echo json_encode(['status' => 'not_found']); // Data tidak ditemukan
    }
  }
}

/* End of file SimulatorInformation.php */
/* Location: ./application/controllers/radioterapi/SimulatorInformation.php */
