<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FlapModel extends MY_Model
{
    protected $_table_name = 'keperawatan.tb_flap';
    protected $_primary_key = 'nopen';
    protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>.',
                'numeric' => '%s Wajib <PERSON>.'
            ),
        ),
    );

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select(
            'f.id ID, f.nopen NOPEN, f.tanggal TANGGAL, master.getNamaLengkapPegawai(peng.NIP) USER,
            master.getNamaLengkapPegawai(dpjp.NIP) DPJP, rk.DESKRIPSI RUANG, f.total SKOR, p.NORM,
            master.getNamaLengkap(p.NORM) NAMA_PASIEN'
        );
        $this->db->from('keperawatan.tb_flap f');
        // $this->db->join('pendaftaran.kunjungan pk','pk.NOPEN = f.nopen','LEFT');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = f.nopen', 'LEFT');
        $this->db->join('master.ruangan rk', 'rk.ID = f.ruang', 'LEFT');
        $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('master.diagnosa_masuk dm', 'dm.ID = p.DIAGNOSA_MASUK', 'LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'LEFT');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = f.oleh', 'LEFT');
        $this->db->where('f.STATUS !=', '0');
        $this->db->where('p.NORM', $this->input->post('nomr'));
        $this->db->order_by('f.tanggal', 'DESC');
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function jumlah()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function isi($id)
    {
        $this->db->select('id, nopen, nokun, tanggal, warna, suhu, crt, turgor, kesimpulan, total, ruang, keterangan');
        $this->db->from('keperawatan.tb_flap');
        $this->db->where('id', $id);
        $query = $this->db->get();
        return $query->row_array();
    }
}
