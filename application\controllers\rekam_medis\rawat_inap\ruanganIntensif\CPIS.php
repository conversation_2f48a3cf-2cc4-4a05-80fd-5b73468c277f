<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CPIS extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/ruanganIntensif/CPISModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_cpis = $this->uri->segment(3);
      $getPengkajian = $this->CPISModel->getPengkajian($id_cpis);
      
      $data = array(
        'nokun' => $nokun,
        'idcpis' => $id_cpis,
        'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
        'listTemp' => $this->masterModel->referensi(1294),
        'listJmlLeukosit' => $this->masterModel->referensi(1295),
        'listTrachealSekret' => $this->masterModel->referensi(1296),
        'listOksigenasi' => $this->masterModel->referensi(1297),
        'listRontgenThorax' => $this->masterModel->referensi(1298),
        'listKulturSputum' => $this->masterModel->referensi(1299),
        'getPengkajian' => $getPengkajian,
      );
      $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/cpis',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
         //echo "<pre>";print_r($post['id_cpis']);echo "</pre>";
          
          $dataCPIS = array(
            'id' => isset($post['id_cpis']) ? $post['id_cpis']: "",
            'nokun' => $post['nokun'],
            'hari_ke' => isset($post['hari_ke']) ? $post['hari_ke'] : "",
            'temp' => isset($post['temp']) ? $post['temp'] : "",
            'jml_leukosit' => isset($post['jml_leukosit']) ? $post['jml_leukosit'] : "",
            'tracheal_sekret' => isset($post['tracheal_sekret']) ? $post['tracheal_sekret'] : "",
            'oksigenasi' => isset($post['oksigenasi']) ? $post['oksigenasi'] : "",
            'rontgen_thorax' => isset($post['rontgen_thorax']) ? $post['rontgen_thorax'] : "",
            'kultur_sputum' => isset($post['kultur_sputum']) ? $post['kultur_sputum'] : "",
            'totalCPIS' => isset($post['totalCPIS']) ? $post['totalCPIS'] : "",
            'cholhexidine' => isset($post['cholhexidine']) ? $post['cholhexidine'] : "",
            'riwayat_pneumonia' => isset($post['riwayat_pneumonia']) ? $post['riwayat_pneumonia'] : "",
            'penderita_hiv' => isset($post['penderita_hiv']) ? $post['penderita_hiv'] : "",
            'kortikosteroid' => isset($post['kortikosteroid']) ? $post['kortikosteroid'] : "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_cpis'])) {
            $this->db->replace('keperawatan.tb_cpis', $dataCPIS);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_cpis', $dataCPIS);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->CPISModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->CPISModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a href="#lihatHistoryCPIS" class="btn btn-primary btn-block btn-sm showHistoryCPIS" data-toggle="modal" data-id="'.$row -> nokun.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> created_at;
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->CPISModel->total_count(),
            "recordsFiltered"   => $this->CPISModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function historyCPIS()
    {
      $nokun = $this->input->post('id');
      $getHistoryCPIS = $this->CPISModel->getHistoryCPIS($nokun);
      $data = array(
        'getHistoryCPIS' => $getHistoryCPIS
      );

      $this->load->view('rekam_medis/rawat_inap/ruanganIntensif/detailCPIS', $data);
    }
}