<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DashboardModel extends CI_Model {

  public function dataBelanjaPerInst($tgl1, $tgl2, $instalasi){
    $query = $this->db->query("CALL db_keuangan.RealisasiDanTargetBelanja('$tgl1', '$tgl2', $instalasi)");
    return $query->result_array();
  }  

}

/* End of file DashboardModel.php */
/* Location: ./application/models/keuangan/DashboardModel.php */