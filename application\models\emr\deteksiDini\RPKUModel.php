<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RPKUModel extends MY_Model
{
    protected $_table_name = 'medis.tb_rpku';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nokun' => array(
            'field' => 'nokun',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s wajib diisi',
                'numeric' => '%s wajib angka',
            )
        ),
        'tanggal' => array(
            'field' => 'tanggal',
            'label' => 'Tanggal',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s wajib diisi',
            )
        ),
        'waktu' => array(
            'field' => 'waktu',
            'label' => 'Waktu',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s wajib diisi',
            )
        ),
    );

    public function __construct()
    {
        $this->load->database();
    }

    public function simpan($data, $statusPengguna)
    {
        // Hanya simpan data yang relevan untuk statusPengguna == 1
        if ($statusPengguna == 1) {
            $data = array_intersect_key($data, array_flip([
                'olehDok',
                'nokun',
                'nomr',
                'tanggal',
                'waktu',
                'keluhan',
                'penyakit_sekarang',
                'penyakit_dahulu',
                'kulit',
                'mata',
                'tht',
                'toraks',
                'jantung',
                'paru',
                'abdomen',
                'ekstremitas',
                'laboratorium',
                'laboratorium_keterangan',
                'radiologi',
                'radiologi_keterangan',
                'ekg',
                'ekg_keterangan',
                'lain',
                'lain_keterangan',
                'kesimpulan',
                'saran'
            ]));
        }
        // Hanya simpan data yang relevan untuk statusPengguna == 2
        elseif ($statusPengguna == 2) {
            $data = array_intersect_key($data, array_flip([
                'nokun',
                'nomr',
                'tanggal',
                'waktu',
                'id_pengguna',
                'jenis_pemeriksaan',
                'alergi',
                'alergi_keterangan',
                'keluhan',
                'penyakit_sekarang',
                'penyakit_dahulu',
                'jenis_pekerjaan',
                'unit_kerja',
                'lama_bekerja_bulan',
                'lama_bekerja_tahun',
                'riwayat_psiko_sosial',
                'riwayat_imunisasi_dewasa',
                'tb',
                'bb',
                'imt',
                'td_sistolik',
                'td_diastolik',
                'nadi',
                'pernapasan',
                'suhu',
            ]));
        }
        return $this->db->insert('medis.tb_rpku', $data);
    }

    public function ubah($id, $data, $statusPengguna)
    {
        // Hanya update data yang relevan untuk statusPengguna == 1
        if ($statusPengguna == 1) {
            $data = array_intersect_key($data, array_flip([
                'olehDok',
                'nokun',
                'nomr',
                'tanggal',
                'waktu',
                'keluhan',
                'penyakit_sekarang',
                'penyakit_dahulu',
                'kulit',
                'mata',
                'tht',
                'toraks',
                'jantung',
                'paru',
                'abdomen',
                'ekstremitas',
                'laboratorium',
                'laboratorium_keterangan',
                'radiologi',
                'radiologi_keterangan',
                'ekg',
                'ekg_keterangan',
                'lain',
                'lain_keterangan',
                'kesimpulan',
                'saran'
            ]));
        }
        // Hanya update data yang relevan untuk statusPengguna == 2
        elseif ($statusPengguna == 2) {
            $data = array_intersect_key($data, array_flip([
                'nokun',
                'nomr',
                'tanggal',
                'waktu',
                'id_pengguna',
                'jenis_pemeriksaan',
                'alergi',
                'alergi_keterangan',
                'keluhan',
                'penyakit_sekarang',
                'penyakit_dahulu',
                'jenis_pekerjaan',
                'unit_kerja',
                'lama_bekerja_bulan',
                'lama_bekerja_tahun',
                'riwayat_psiko_sosial',
                'riwayat_imunisasi_dewasa',
                'tb',
                'bb',
                'imt',
                'td_sistolik',
                'td_diastolik',
                'nadi',
                'pernapasan',
                'suhu',
            ]));
        }
        $this->db->where('id', $id);
        return $this->db->update('medis.tb_rpku', $data);
    }

    public function getByNokun($nokun)
    {
        return $this->db->get_where('medis.tb_rpku', array('nokun' => $nokun, 'status' => 1))->row_array();
    }

    public function jumlah($nomr)
    {
        $this->db->select('m.id');
        $this->db->from('medis.tb_rpku m');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = m.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = m.olehDok', 'left');
        $this->db->where('m.status', 1);
        $this->db->where('pp.NORM', $nomr);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function history($nomr)
    {
        $this->db->select('m.id, m.tanggal, m.nokun, master.getNamaLengkapPegawai(pen.NIP) id_pengguna, master.getNamaLengkapPegawai(dok.NIP) olehDok, m.status');
        $this->db->from('medis.tb_rpku m');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = m.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = m.olehDok', 'left');
        $this->db->join('master.dokter dok', 'dok.ID = m.olehDok', 'left');
        $this->db->join('aplikasi.pengguna pen', 'pen.ID = m.id_pengguna', 'left');
        $this->db->where('pp.NORM', $nomr);
        $this->db->order_by('m.id', 'desc');
        return $this->db->get();
    }

    public function batal($id)
    {
        $this->db->where('id', $id);
        $this->db->update('medis.tb_rpku', array('status' => 0));
    }

    public function getRPKUById($id)
    {
        $this->db->where('id', $id);
        return $this->db->get('medis.tb_rpku')->row_array();
    }
}
