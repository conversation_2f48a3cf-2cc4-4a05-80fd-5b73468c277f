<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ValidasiMalnutrisiDewasa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','gizi/validasiMalnutrisiDewasaModel'));
    }

    public function index() {

        $data = array(
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(2)),
            'asupanEnergi' => $this->masterModel->referensi(1202),
            'penurunanLemak' => $this->masterModel->referensi(1204),
            'penurunanOtot' => $this->masterModel->referensi(1205),
            'akumulasiCairan' => $this->masterModel->referensi(1206),
            'diagnosis' => $this->masterModel->referensi(1207),
            'jalur' => $this->masterModel->referensi(1208),
            'enteral' => $this->masterModel->referensi(1209),
        );

        $this->load->view('PengkajianRi/gizi/validasiMalnutrisiDewasa',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->validasiMalnutrisiDewasaModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataValidasiMalnutrisi = array(
                        'nokun' => $post['nokun'],
                        'tb_who' => $post['tinggi_badan_who'],
                        'bb_who' => $post['berat_badan_who'],
                        'imt_who' => $post['imt_who'],
                        'tb_espen' => $post['tinggi_badan_espen'],
                        'bb_espen' => $post['berat_badan_espen'],
                        'imt_espen' => $post['imt_espen'],
                        'pbb_espen_kg' => $post['kg_espen'],
                        'pbb_espen_persen' => $post['persen_espen'],
                        'pbb_espen_bulan' => $post['dalam_espen'],
                        'asupan_energi' => $post['asupan_energi'],
                        'pbb_aspen_kg' => $post['dalam_aspen'],
                        'pbb_aspen_bulan' => $post['bulan_aspen'],
                        'p_massa_lemak_tubuh' => $post['penurunan_lemak'],
                        'p_massa_otot' => $post['penurunan_otot'],
                        'akumulasi_cairan' => $post['akumulasi_cairan'],
                        'diagnosis' => $post['diagnosis'],
                        'kebutuhan_energi' => $post['kebutuhan_energi'],
                        'kebutuhan_protein' => $post['kebutuhan_protein'],
                        'kebutuhan_lemak_persen' => $post['kebutuhan_lemak_persen'],
                        'kebutuhan_lemak' => $post['kebutuhan_lemak_gram'],
                        'jalur' => json_encode($post['jalur']),
                        'enteral' => $post['enteral'],
                        'bentuk' => $post['bentuk'],
                        'suplementasi' => $post['suplementasi'],
                        'evaluasi' => $post['evaluasi'],
                        'oleh' => $this->session->userdata("id"),
                    );

                    $this->db->replace('medis.tb_validasi_malnutrisi', $dataValidasiMalnutrisi);

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataValidasiMalnutrisi = $this->validasiMalnutrisiDewasaModel->get($post['nokun'], true);
                
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataValidasiMalnutrisi
                ));
            }else if($param == 'count'){
                $result = $this->validasiMalnutrisiDewasaModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->validasiMalnutrisiDewasaModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_validasi_malnutrisi" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a><a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/validasimalnutrisi/validasimalnutrisi.php?format=pdf&nokun='.$row -> NOKUN.'" target="_blank"><i class="fa fa-print"></i> Cetak</a>';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->validasiMalnutrisiDewasaModel->total_count(),
            "recordsFiltered"   => $this->validasiMalnutrisiDewasaModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}