<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RkaklRuangan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('keuangan/RkaklRuanganModel'));
    }

    public function index() {

        $data = array(
            'title'       => 'Halaman Master RKAKL RUANGAN',
            'isi'         => 'Keuangan_new/master/rkakl_ruangan'
        );
      
        $this->load->view('layout/wrapper',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->RkaklRuanganModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataRkakl = array(
                        'MAK' => $post['program'],
                        'URAIAN' => $post['uraian'],
                        'VOLUME' => $post['volume'],
                        'SATUAN' => $post['satuan'],
                        'HARGA_SATUAN' => $post['harga_satuan'],
                        'PAGU_AWAL' => $post['pagu_awal'],
                        'SUMBER_DANA' => $post['sumber_dana'],
                        'PERIODE' => $post['periode'],
                        'KATEGORI' => $post['kategori'],
                        'OLEH' => $this->session->userdata("id"),
                    );

                    $this->db->replace('db_keuangan.rkakl', $dataRkakl);

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataRkaklIns = $this->RkaklRuanganModel->get($post['id'], true);
      
                echo json_encode(array(
                  'status' => 'success',
                  'data' => $dataRkaklIns
                ));
            }else if($param == 'count'){
                $result = $this->RkaklRuanganModel->get_count();;
                echo json_encode($result);
            }else if($param == 'get'){
                $rkklInstalasi = $this->RkaklRuanganModel->get_table(FALSE);
      
                $data = array();
                foreach($rkklInstalasi as $datarkklInstalasi){
                  $sub_array = array();
                  $sub_array['id'] = $datarkklInstalasi -> ID;
                  $sub_array['text'] = $datarkklInstalasi -> DESKRIPSI;
      
                  $data[] = $sub_array;
                }
                echo json_encode($data);
            }
    	}
    }

    public function datatables(){
        $result = $this->RkaklRuanganModel->datatables();
        $no=1;
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $row -> MAK;
            $sub_array[] = $row -> URAIAN;      
            $sub_array[] = $row -> LEVEL;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->RkaklRuanganModel->total_count(),
            "recordsFiltered"   => $this->RkaklRuanganModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}