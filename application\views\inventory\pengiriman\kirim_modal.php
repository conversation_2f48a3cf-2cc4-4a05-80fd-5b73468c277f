<div class="row">
  <div class="col-12">
    <div class="page-title-box">
      <div class="page-title-right">
        <ol class="breadcrumb m-0">
          <li class="breadcrumb-item"><a href="javascript: void(0);">Adminto</a></li>
          <li class="breadcrumb-item"><a href="javascript: void(0);">Pages</a></li>
          <li class="breadcrumb-item active">Starter Page</li>
        </ol>
      </div>
      <h4 class="page-title">List Permintaan Barang Baru</h4>
    </div>
  </div>
</div>     
<!-- end page title --> 
<div class="row">
  <div class="col-12">   
    <div class="card-box">
      <?php echo form_open('pengiriman/post'); ?>
      <div class="table-responsive">
        <table class="table table-bordered" cellspacing="0" width="100%" id="">
          <thead>
            <tr>
              <th width="5%">No</th>
              <th width="40%"><PERSON><PERSON></th>
              <th width="10%">Satuan</th>            
              <th width="15%">Jumlah permintaan</th>
              <th width="15%">Jumlah dikirim</th>
            </tr>
          </thead>
          <tbody>
            <?php
          $no = 1; //untuk menampilkan no
          foreach($record as $row){
             ?>
            <tr>
            <td><?=$no?></td>
              <input class='form-control' type='hidden' value="<?php echo $row['NOMOR']?>" name='NOMOR' >
            <input class='form-control' type='hidden' value="<?php echo $row['ID_DETAIL']?>" name='ID_DETAIL[]' >
             <input class='form-control' type='hidden' value="<?php echo $row['TUJUAN']?>" name='ASAL' >
              <input class='form-control' type='hidden' value="<?php echo $row['ASAL']?>" name='TUJUAN' >
            <td><input class='form-control' type='text' value="<?php echo $row['NAMA']?>" name=''/ disabled ></td>
            <td><input class='form-control' type='text' value="<?=$row['SATUAN'] ?>" name=''/ disabled ></td>        
            <td><input class='form-control' type='text' value="<?=$row['JUMLAH'] ?>" name=''/ disabled ></td>
            <td> <input class='form-control' type='number' name='JUMLAH[]'/ required >
            </td>
            </tr>
            <?php
            $no++;
          }
          ?>
        </tbody>
      </table>
    </div>
    <button type="submit" name="submit" value="submit" class="btn btn-primary">Simpan</button>
            </form>
  </div><!-- end col -->
</div>
</div>