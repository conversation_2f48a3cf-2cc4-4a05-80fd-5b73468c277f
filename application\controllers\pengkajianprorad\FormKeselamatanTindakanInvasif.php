<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FormKeselamatanTindakanInvasif extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $ruangan = 1;
    $nomr = $this->uri->segment(4);
    $nokun = $this->uri->segment(6);
    $id_kti = $this->uri->segment(7);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $penandaanSisiLokasi = $this->masterModel->referensi(415);
    $inforConTin = $this->masterModel->referensi(416);
    $inforConTinAnesSed = $this->masterModel->referensi(417);
    $pemPenunjangPanoramic = $this->masterModel->referensi(1861);
    $pemPenunjangFoto = $this->masterModel->referensi(419);
    $pemPenunjangCtScan = $this->masterModel->referensi(420);
    $pemPenunjangUSG = $this->masterModel->referensi(421);
    $pemPenunjangMRI = $this->masterModel->referensi(422);
    $pemPenunjangLab = $this->masterModel->referensi(423);
    $persediaanDarah = $this->masterModel->referensi(425);
    $ketersediaanImplant = $this->masterModel->referensi(426);
    $benarSisiLokasiTin = $this->masterModel->referensi(427);
    $khususPerhatikan = $this->masterModel->referensi(428);
    $instrumenSudahBenar = $this->masterModel->referensi(429);
    $fotoRadiologiSesuai = $this->masterModel->referensi(430);
    $kelengkapanKasaJarum = $this->masterModel->referensi(431);
    $spesimenBeriLabel = $this->masterModel->referensi(432);
    $namaImplanDanLokasi = $this->masterModel->referensi(433);
    $peralatanYangPerlu = $this->masterModel->referensi(434);
    $masalahHarusPerhatikan = $this->masterModel->referensi(435);

    $getKTI = "";
    $historyKTI = $this->pengkajianAwalModel->historyKTI($nomr);
    if ($id_kti != "") {
      $getKTI = $this->pengkajianAwalModel->getKTI($id_kti);
    }

    $data = array(
      'ruangan' => $ruangan,
      'getNomr' => $getNomr,
      'penandaanSisiLokasi' => $penandaanSisiLokasi,
      'inforConTin' => $inforConTin,
      'inforConTinAnesSed' => $inforConTinAnesSed,
      'pemPenunjangPanoramic' => $pemPenunjangPanoramic,
      'pemPenunjangFoto' => $pemPenunjangFoto,
      'pemPenunjangCtScan' => $pemPenunjangCtScan,
      'pemPenunjangUSG' => $pemPenunjangUSG,
      'pemPenunjangMRI' => $pemPenunjangMRI,
      'pemPenunjangLab' => $pemPenunjangLab,
      'persediaanDarah' => $persediaanDarah,
      'getKTI' => $getKTI,
      'ketersediaanImplant' => $ketersediaanImplant,
      'historyKTI' => $historyKTI,
      'benarSisiLokasiTin' => $benarSisiLokasiTin,
      'khususPerhatikan' => $khususPerhatikan,
      'instrumenSudahBenar' => $instrumenSudahBenar,
      'fotoRadiologiSesuai' => $fotoRadiologiSesuai,
      'kelengkapanKasaJarum' => $kelengkapanKasaJarum,
      'spesimenBeriLabel' => $spesimenBeriLabel,
      'namaImplanDanLokasi' => $namaImplanDanLokasi,
      'peralatanYangPerlu' => $peralatanYangPerlu,
      'masalahHarusPerhatikan' => $masalahHarusPerhatikan,
    );

    $this->load->view('Pengkajian/keselamatanTindakanInvasif/index', $data);
  }

  public function viewInputTimeSignOutKesInv()
  {
    $idkes = $this->input->post('idkes');
    $nokun = $this->input->post('nokun');
    // $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $penandaanSisiLokasi = $this->masterModel->referensi(415);
    $inforConTin = $this->masterModel->referensi(416);
    $inforConTinAnesSed = $this->masterModel->referensi(417);
    $pemPenunjangPanoramic = $this->masterModel->referensi(1861);
    $pemPenunjangFoto = $this->masterModel->referensi(419);
    $pemPenunjangCtScan = $this->masterModel->referensi(420);
    $pemPenunjangUSG = $this->masterModel->referensi(421);
    $pemPenunjangMRI = $this->masterModel->referensi(422);
    $pemPenunjangLab = $this->masterModel->referensi(423);
    $persediaanDarah = $this->masterModel->referensi(425);
    $ketersediaanImplant = $this->masterModel->referensi(426);
    $benarSisiLokasiTin = $this->masterModel->referensi(427);
    $khususPerhatikan = $this->masterModel->referensi(428);
    $instrumenSudahBenar = $this->masterModel->referensi(429);
    $fotoRadiologiSesuai = $this->masterModel->referensi(430);
    $kelengkapanKasaJarum = $this->masterModel->referensi(431);
    $spesimenBeriLabel = $this->masterModel->referensi(432);
    $namaImplanDanLokasi = $this->masterModel->referensi(433);
    $peralatanYangPerlu = $this->masterModel->referensi(434);
    $masalahHarusPerhatikan = $this->masterModel->referensi(435);
    $getKTI = $this->pengkajianAwalModel->getKTI($idkes);

    $data = array(
      'idkes' => $idkes,
      'nokun' => $nokun,
      // 'getNomr' => $getNomr,
      'penandaanSisiLokasi' => $penandaanSisiLokasi,
      'inforConTin' => $inforConTin,
      'inforConTinAnesSed' => $inforConTinAnesSed,
      'pemPenunjangPanoramic' => $pemPenunjangPanoramic,
      'pemPenunjangFoto' => $pemPenunjangFoto,
      'pemPenunjangCtScan' => $pemPenunjangCtScan,
      'pemPenunjangUSG' => $pemPenunjangUSG,
      'pemPenunjangMRI' => $pemPenunjangMRI,
      'pemPenunjangLab' => $pemPenunjangLab,
      'persediaanDarah' => $persediaanDarah,
      'getKTI' => $getKTI,
      'ketersediaanImplant' => $ketersediaanImplant,
      'benarSisiLokasiTin' => $benarSisiLokasiTin,
      'khususPerhatikan' => $khususPerhatikan,
      'instrumenSudahBenar' => $instrumenSudahBenar,
      'fotoRadiologiSesuai' => $fotoRadiologiSesuai,
      'kelengkapanKasaJarum' => $kelengkapanKasaJarum,
      'spesimenBeriLabel' => $spesimenBeriLabel,
      'namaImplanDanLokasi' => $namaImplanDanLokasi,
      'peralatanYangPerlu' => $peralatanYangPerlu,
      'masalahHarusPerhatikan' => $masalahHarusPerhatikan,
      'listPerawat' => $this->masterModel->listPerawat(),
      'listRadiografer' => $this->masterModel->listRadiografer2(),
      'listFisikawanMedis' => $this->masterModel->listFisikawanMedis(),
      'listDrAnestesi' => $this->masterModel->listDrAnestesi(),
      'listDrPelaksana' => $this->masterModel->listDrPelaksana(),
    );

    $this->load->view('Pengkajian/keselamatanTindakanInvasif/viewInputTimeSignOutKesInv', $data);
  }

  public function inputTimeSignOutKesInv($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $getid_kti = $post['idkes'];

        if (isset($post['sebutNamaPeranTimeOut']) == "on") {
          $menyebutkan_nama = 1;
        } else {
          $menyebutkan_nama = 0;
        }
        if (isset($post['benarIdentitasTimeOut']) == "on") {
          $benar_identitas_pasien = 1;
        } else {
          $benar_identitas_pasien = 0;
        }
        if (isset($post['benarTindakanTimeOut']) == "on") {
          $benar_tindakan = 1;
        } else {
          $benar_tindakan = 0;
        }
        if (isset($post['namaTindakanSignOut']) == "on") {
          $nama_tindakan = 1;
        } else {
          $nama_tindakan = 0;
        }

        $dataKTI = array(
          'id_kti' => $getid_kti,
          'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
          'menyebutkan_nama' => $menyebutkan_nama,
          'pukul_timeout_inv' => isset($post['pukulTimeOutInv']) ? $post['pukulTimeOutInv'] : null,
          'benar_identitas_pasien' => $benar_identitas_pasien,
          'benar_tindakan' => $benar_tindakan,
          'benar_sisi_lokasi_tindakan' => isset($post['benarSisiLokasiTin']) ? $post['benarSisiLokasiTin'] : null,
          'hal_khusus' => isset($post['khususPerhatikan']) ? $post['khususPerhatikan'] : null,
          'isi_hal_khusus' => isset($post['deskKhususPerhatikan']) ? $post['deskKhususPerhatikan'] : null,
          'langkah_yang_dilakukan' => isset($post['jikaAdaLangkahApaTimeOut']) ? $post['jikaAdaLangkahApaTimeOut'] : null,
          'instrumen' => isset($post['instrusudahBenar']) ? $post['instrusudahBenar'] : null,
          'foto_radiologi' => isset($post['fotRadioSesuai']) ? $post['fotRadioSesuai'] : null,
          'pukul_signout_inv' => isset($post['pukulSignOutInv']) ? $post['pukulSignOutInv'] : null,
          'nama_tindakan' => $nama_tindakan,
          'kelengkapan_kasa' => isset($post['kelengkapanKapKasaJarum']) ? $post['kelengkapanKapKasaJarum'] : null,
          'spesimen' => isset($post['spesimenBeriLab']) ? $post['spesimenBeriLab'] : null,
          'nama_implant' => isset($post['namaImplanLok']) ? $post['namaImplanLok'] : null,
          'isi_implant' => isset($post['deskNamaImplanDanLokasi']) ? $post['deskNamaImplanDanLokasi'] : null,
          'masalah_peralatan' => isset($post['peralatanygPrl']) ? $post['peralatanygPrl'] : null,
          'masalah_yang_harus_diperhatikan' => isset($post['mslhHrsPerhatikan']) ? $post['mslhHrsPerhatikan'] : null,
          'isi_masalah_yang_harus_diperhatikan' => isset($post['deskMasalahHarusPerhatikan']) ? $post['deskMasalahHarusPerhatikan'] : null,
          'isi_peralatan_yang_perlu' => isset($post['deskperalatanYangPerlu']) ? $post['deskperalatanYangPerlu'] : null,
          'dokter_pelaksana' => isset($post['dokter_pelaksana']) ? $post['dokter_pelaksana'] : null,
          'dokter_anestesi' => isset($post['dokter_anestesi']) ? $post['dokter_anestesi'] : null,
          'perawat_sirkuler' => isset($post['perawat_sirkuler']) ? $post['perawat_sirkuler'] : null,
          'radiografer' => isset($post['perawat_radiografer']) ? $post['perawat_radiografer'] : null,
          'fisikawan_medis' => isset($post['perawat_fisikawanMedis']) ? $post['perawat_fisikawanMedis'] : null,
          'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

          // echo "<pre>";print_r($dataKTI);echo "</pre>";exit();

          $this->db->where('tb_keselamatan_tindakan_invasif.id_kti', $getid_kti);
          $this->db->update('keperawatan.tb_keselamatan_tindakan_invasif', $dataKTI);

      }
    }
  }

  public function action_keselamatantindakaninvasif($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $getid_kti = !empty($post['id_kti']) ? $post['id_kti'] : $this->pengkajianAwalModel->getIdEmr();
        if (isset($post['identitasPasien']) == "on") {
          $identitas_pasien = 1;
        } else {
          $identitas_pasien = 0;
        }
        if (isset($post['rencanaTindakan']) == "on") {
          $rencana_tindakan = 1;
        } else {
          $rencana_tindakan = 0;
        }

        if (isset($post['sebutNamaPeranTimeOut']) == "on") {
          $menyebutkan_nama = 1;
        } else {
          $menyebutkan_nama = 0;
        }
        if (isset($post['benarIdentitasTimeOut']) == "on") {
          $benar_identitas_pasien = 1;
        } else {
          $benar_identitas_pasien = 0;
        }
        if (isset($post['benarTindakanTimeOut']) == "on") {
          $benar_tindakan = 1;
        } else {
          $benar_tindakan = 0;
        }
        if (isset($post['namaTindakanSignOut']) == "on") {
          $nama_tindakan = 1;
        } else {
          $nama_tindakan = 0;
        }

        $dataKTI = array(
          'id_kti' => $getid_kti,
          'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
          'pukul' => isset($post['pukulKesInv']) ? $post['pukulKesInv'] : null,
          'tekanan_darah_1' => isset($post['tekananDarSistolikKesInv']) ? $post['tekananDarSistolikKesInv'] : null,
          'tekanan_darah_2' => isset($post['tekananDarDiastolikKesInv']) ? $post['tekananDarDiastolikKesInv'] : null,
          'nadi' => isset($post['NadiKesInvasif']) ? $post['NadiKesInvasif'] : null,
          'napas' => isset($post['pernafasanKesInvasif']) ? $post['pernafasanKesInvasif'] : null,
          'suhu' => isset($post['suhuKesInvasif']) ? $post['suhuKesInvasif'] : null,
          'diagnosis' => isset($post['diagnosisKeselamatanInvasif']) ? $post['diagnosisKeselamatanInvasif'] : null,
          'identitas_pasien' => $identitas_pasien,
          'desk_rencana_tindakan' => isset($post['deskRencanaTindakanKti']) ? $post['deskRencanaTindakanKti'] : null,
          'rencana_tindakan' => $rencana_tindakan,
          'desk_penandaan_sisi_tindakan' => isset($post['deskPenandaanSisiTindakanKti']) ? $post['deskPenandaanSisiTindakanKti'] : null,
          'penandaan_sisi_tindakan' => isset($post['penandaSisLok']) ? $post['penandaSisLok'] : null,
          'informed_consent_tindakan' => isset($post['informedConTin']) ? $post['informedConTin'] : null,
          'informed_consent_anatesi_sedasi' => isset($post['informedConTinAS']) ? $post['informedConTinAS'] : null,
          'panoramic' => isset($post['pemPenPano']) ? $post['pemPenPano'] : null,
          'panoramic_desk' => isset($post['panoramic_desk']) ? $post['panoramic_desk'] : null,
          'foto_thoraks' => isset($post['pemPenFoto']) ? $post['pemPenFoto'] : null,
          'ctscan' => isset($post['pemPenCtScan']) ? $post['pemPenCtScan'] : null,
          'usg' => isset($post['pemPenunjangUSG']) ? $post['pemPenunjangUSG'] : null,
          'mri' => isset($post['pemPenunjangMRI']) ? $post['pemPenunjangMRI'] : null,
          'lab' => isset($post['pemPenunjangLaboratorium']) ? $post['pemPenunjangLaboratorium'] : null,
          'pemeriksaaan_penunjang_lainnya' => isset($post['pemeriksaanPenunjangLainnya']) ? $post['pemeriksaanPenunjangLainnya'] : null,
          'persediaan_darah' => isset($post['persediDarh']) ? $post['persediDarh'] : null,
          'golongan_darah' => isset($post['deskGolonganDarah']) ? $post['deskGolonganDarah'] : null,
          'prc' => isset($post['deskPRC']) ? $post['deskPRC'] : null,
          'ffp' => isset($post['deskFFP']) ? $post['deskFFP'] : null,
          'lainnya_persediaan_darah' => isset($post['deskLainnyaPersediaanDarah']) ? $post['deskLainnyaPersediaanDarah'] : null,

          'ketersediaan_implant' => isset($post['ketersediaanImp']) ? $post['ketersediaanImp'] : null,
          'isi_ketersediaan_implant' => isset($post['deskKetersediaanImplant']) ? $post['deskKetersediaanImplant'] : null,

          'menyebutkan_nama' => $menyebutkan_nama,
          'pukul_timeout_inv' => isset($post['pukulTimeOutInv']) ? $post['pukulTimeOutInv'] : null,
          'benar_identitas_pasien' => $benar_identitas_pasien,

          'benar_tindakan' => $benar_tindakan,
          'benar_sisi_lokasi_tindakan' => isset($post['benarSisiLokasiTin']) ? $post['benarSisiLokasiTin'] : null,
          'hal_khusus' => isset($post['khususPerhatikan']) ? $post['khususPerhatikan'] : null,
          'isi_hal_khusus' => isset($post['deskKhususPerhatikan']) ? $post['deskKhususPerhatikan'] : null,
          'langkah_yang_dilakukan' => isset($post['jikaAdaLangkahApaTimeOut']) ? $post['jikaAdaLangkahApaTimeOut'] : null,
          'instrumen' => isset($post['instrusudahBenar']) ? $post['instrusudahBenar'] : null,
          'foto_radiologi' => isset($post['fotRadioSesuai']) ? $post['fotRadioSesuai'] : null,

          'pukul_signout_inv' => isset($post['pukulSignOutInv']) ? $post['pukulSignOutInv'] : null,
          'nama_tindakan' => $nama_tindakan,
          'kelengkapan_kasa' => isset($post['kelengkapanKapKasaJarum']) ? $post['kelengkapanKapKasaJarum'] : null,
          'spesimen' => isset($post['spesimenBeriLab']) ? $post['spesimenBeriLab'] : null,
          'nama_implant' => isset($post['namaImplanLok']) ? $post['namaImplanLok'] : null,
          'isi_implant' => isset($post['deskNamaImplanDanLokasi']) ? $post['deskNamaImplanDanLokasi'] : null,
          'masalah_peralatan' => isset($post['peralatanygPrl']) ? $post['peralatanygPrl'] : null,

          'masalah_yang_harus_diperhatikan' => isset($post['mslhHrsPerhatikan']) ? $post['mslhHrsPerhatikan'] : null,
          'isi_masalah_yang_harus_diperhatikan' => isset($post['deskMasalahHarusPerhatikan']) ? $post['deskMasalahHarusPerhatikan'] : null,
          'isi_peralatan_yang_perlu' => isset($post['deskperalatanYangPerlu']) ? $post['deskperalatanYangPerlu'] : null,
          'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

        // print_r($dataKTI);exit();

if (!empty($post['id_kti'])) {
  $this->db->where('tb_keselamatan_tindakan_invasif.id_kti', $post['id_kti']);
  $this->db->update('keperawatan.tb_keselamatan_tindakan_invasif', $dataKTI);
  $result = array('status' => 'success', 'pesan' => 'ubah');
}else {
  $result = array('status' => 'failed');
  $this->db->insert('keperawatan.tb_keselamatan_tindakan_invasif', $dataKTI);
  $result = array('status' => 'success');
}
echo json_encode($result);
}
}
}
}


/* End of file FormKeselamatanTindakanInvasif.php */
/* Location: ./application/controllers/pengkajianprorad/FormKeselamatanTindakanInvasif.php */
