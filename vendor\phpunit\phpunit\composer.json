{"name": "phpunit/phpunit", "description": "The PHP Unit Testing framework.", "type": "library", "keywords": ["phpunit", "xunit", "testing"], "homepage": "https://phpunit.de/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues"}, "prefer-stable": true, "require": {"php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "phpspec/prophecy": "^1.6.2", "symfony/yaml": "~2.1|~3.0|~4.0", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "^1.0.6|^2.0.1", "myclabs/deep-copy": "~1.3", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-libxml": "*"}, "require-dev": {"ext-PDO": "*"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "config": {"platform": {"php": "5.6.0"}, "optimize-autoloader": true, "sort-packages": true}, "suggest": {"phpunit/php-invoker": "~1.1", "ext-xdebug": "*"}, "bin": ["phpunit"], "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/"], "files": ["src/Framework/Assert/Functions.php", "tests/_files/CoveredFunction.php"]}, "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}}