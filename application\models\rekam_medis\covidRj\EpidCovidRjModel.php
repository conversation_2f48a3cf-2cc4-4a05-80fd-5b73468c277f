<?php
defined('BASEPATH') or exit('No direct script access allowed');

class EpidCovidRjModel extends MY_Model
{
    protected $_table_name = 'keperawatan.tb_epidemologi_covid';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(

        'norm' => array(
            'field' => 'norm',
            'label' => 'NORM',
            'rules' => 'trim|required',
            'errors' => array(
                'required' => '%s Wajib Diisi.'
            ),
        ),
    );


    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select('po.id ID,po.nokun NOKUN, po.tanggal TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.<PERSON>R<PERSON>, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('db_layanan.tb_planofcare po');
        $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = po.nokun', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('master.diagnosa_masuk dm', 'dm.ID = p.DIAGNOSA_MASUK', 'LEFT');
        $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'LEFT');
        $this->db->join('master.ruangan rk', 'rk.ID = pk.RUANGAN', 'LEFT');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = po.oleh', 'LEFT');

        $this->db->where('po.STATUS !=', '0');
        $this->db->where('p.NORM', $this->input->post('nomr'));
        $this->db->order_by('po.id', 'DESC');
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function insertEpidCovid($dataEpidCovid)
    {
        $this->db->insert('keperawatan.tb_epidemologi_covid', $dataEpidCovid);
        return $this->db->insert_id();
    }

    public function historyEpidCovid($norm)
    {
        $query = $this->db->query('SELECT va.variabel KRITERIA, vr.variabel STATUS_PASIEN, poc.id ID, p.NOMOR NOPEN, poc.nokun NOKUN , p.NORM, poc.`*`
        FROM keperawatan.tb_epidemologi_covid poc
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = poc.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        left JOIN db_master.variabel va ON va.id_variabel = poc.kriteria_kasus
        left JOIN db_master.variabel vr ON vr.id_variabel = poc.status_pasien
        WHERE p.NORM="' . $norm . '"');
        return $query->result_array();
    }

    public function getDataEpidCovid($id)
    {
        $query = $this->db->query('SELECT poc.id ID, p.NOMOR NOPEN, poc.nokun NOKUN , p.NORM, poc.`*`
        FROM keperawatan.tb_epidemologi_covid poc
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = poc.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        WHERE poc.id="' . $id . '"');
        return $query->row_array();
    }

    public function historyDetailKontakEratArr($id)
    {
        $query = $this->db->query(
            "SELECT * FROM keperawatan.tb_kontak_epid_covid ko
            WHERE ko.id_epid_covid='$id'"
        );
        return $query->result_array();
    }

    public function dataDiriPasien($nomr)
    {
        $query = $this->db->query("SELECT mp.`*`, master.getNamaLengkap(mp.NORM)NAMAPASIEN, DATE_FORMAT(mp.TANGGAL_LAHIR,'%d-%m-%Y') TGL_LAHIR, 
        IF(mp.JENIS_KELAMIN=1,'Laki-laki','Perempuan') JENKEL
        , CONCAT(CONCAT(master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR), ' Tahun'),', ',
        CONCAT(master.getCariUmurBulan(pp.TANGGAL, mp.TANGGAL_LAHIR), ' Bulan')) UMUR, wil.DESKRIPSI KOTA
        , mp.ALAMAT, mp.RT, mp.RW, pek.DESKRIPSI PEKERJAAN, mk.NOMOR TELPON, kip.NOMOR NIK
        FROM master.pasien mp
        LEFT JOIN pendaftaran.pendaftaran pp ON pp.NORM = mp.NORM
        LEFT JOIN master.kontak_pasien mk ON mk.NORM = mp.NORM
        LEFT JOIN master.wilayah wil ON wil.ID=mp.WILAYAH AND wil.JENIS=4
        LEFT JOIN master.referensi pek ON pek.ID=mp.PEKERJAAN AND pek.JENIS=4
        LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM=mp.NORM
        WHERE mp.NORM = '$nomr'
        ORDER BY pp.NOMOR DESC
        LIMIT 1");
        return $query->row_array();
    }
}
