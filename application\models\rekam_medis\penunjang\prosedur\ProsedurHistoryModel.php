<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProsedurHistoryModel extends MY_Model
{
    protected $_table_name = '';
    protected $_primary_key = '';
    protected $_order_by = '';
    protected $_order_by_type = '';

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
      $norm = $this->input->post('norm');
      $this->db->select("orla.NOMOR NOMOR_ORDER, orla.TANGGAL TANGGAL_ORDER
      , ras.DESKRIPSI RUANG_ASAL
      , rut.ID ID_TUJUAN, rut.DESKRIPSI RUANG_TUJUAN, orla.TANGGAL_RENCANA_TINDAKAN TANGGAL_RENCANA
      , orla.`STATUS`
      , IF(orla.`STATUS`=1, 'Terkirim/Belum Diterima', IF(orla.`STATUS`=2, 'Final','Dibatalkan')) STATUS_ORDER");
      $this->db->from("layanan.order_prosedur_diagnostik orla");
      $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = orla.KUNJUNGAN','LEFT');
      $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
      $this->db->join('master.ruangan ras','ras.ID = pk.RUANGAN','LEFT');
      $this->db->join('master.dokter dok','dok.ID = orla.DOKTER_ASAL','LEFT');
      $this->db->join('pendaftaran.kunjungan pkut','pkut.REF = orla.NOMOR','LEFT');
      $this->db->join('master.ruangan rut','rut.ID = orla.TUJUAN','LEFT');
      $this->db->where('p.NORM', $norm);
      $this->db->order_by('orla.TANGGAL DESC');

      if (isset($_POST['status'])) {
        $this->db->where('orla.STATUS', $this->input->post('status'));
      }
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}
