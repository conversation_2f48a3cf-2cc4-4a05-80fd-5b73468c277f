<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Iadl extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }
    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $history_iadl = $this->pengkajianAwalModel->history_iadl($getNomr['NORM']);
    $id_iadl = $this->uri->segment(8);
    $get_iadl = $this->pengkajianAwalModel->get_iadl($id_iadl);

    $data = array(
      'nokun' => $nokun,
      'getNomr' => $getNomr,
      'history_iadl' => $history_iadl,
      'get_iadl' => $get_iadl
    );
    $this->load->view('Pengkajian/geriatri/InstrumentalActivitiesOfDailyLivingLawton/index', $data);
  }

  public function action_iadl($param){
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
      if ($param == 'tambah' || $param == 'ubah'){
        $post = $this->input->post();
        $get_id_iadl = !empty($post['id_iadl']) ? $post['id_iadl'] : $this->pengkajianAwalModel->getIdEmr();
        $data_iadl = array(
          'id_iadl'  => $get_id_iadl,
          'nokun'    => isset($post['nokun']) ? $post['nokun'] : null,
          'dapat_menggunakan_telepon_1' => isset($post['dapat_menggunakan_telepon_1']) ? $post['dapat_menggunakan_telepon_1'] : null,
          'dapat_menggunakan_telepon_2' => isset($post['dapat_menggunakan_telepon_2']) ? $post['dapat_menggunakan_telepon_2'] : null,
          'dapat_menggunakan_telepon_3' => isset($post['dapat_menggunakan_telepon_3']) ? $post['dapat_menggunakan_telepon_3'] : null,
          'dapat_menggunakan_telepon_4' => isset($post['dapat_menggunakan_telepon_4']) ? $post['dapat_menggunakan_telepon_4'] : null,
          'mampu_pergi_ke_suatu_tempat_1' => isset($post['mampu_pergi_ke_suatu_tempat_1']) ? $post['mampu_pergi_ke_suatu_tempat_1'] : null,
          'mampu_pergi_ke_suatu_tempat_2' => isset($post['mampu_pergi_ke_suatu_tempat_2']) ? $post['mampu_pergi_ke_suatu_tempat_2'] : null,
          'mampu_pergi_ke_suatu_tempat_3' => isset($post['mampu_pergi_ke_suatu_tempat_3']) ? $post['mampu_pergi_ke_suatu_tempat_3'] : null,
          'mampu_pergi_ke_suatu_tempat_4' => isset($post['mampu_pergi_ke_suatu_tempat_4']) ? $post['mampu_pergi_ke_suatu_tempat_4'] : null,
          'dapat_berbelanja_1' => isset($post['dapat_berbelanja_1']) ? $post['dapat_berbelanja_1'] : null,
          'dapat_berbelanja_2' => isset($post['dapat_berbelanja_2']) ? $post['dapat_berbelanja_2'] : null,
          'dapat_berbelanja_3' => isset($post['dapat_berbelanja_3']) ? $post['dapat_berbelanja_3'] : null,
          'dapat_menyiapkan_makanan_1' => isset($post['dapat_menyiapkan_makanan_1']) ? $post['dapat_menyiapkan_makanan_1'] : null,
          'dapat_menyiapkan_makanan_2' => isset($post['dapat_menyiapkan_makanan_2']) ? $post['dapat_menyiapkan_makanan_2'] : null,
          'dapat_menyiapkan_makanan_3' => isset($post['dapat_menyiapkan_makanan_3']) ? $post['dapat_menyiapkan_makanan_3'] : null,
          'dapat_menyiapkan_makanan_4' => isset($post['dapat_menyiapkan_makanan_4']) ? $post['dapat_menyiapkan_makanan_4'] : null,
          'dapat_melakukan_pekerjaan_rumah_tangga_1' => isset($post['dapat_melakukan_pekerjaan_rumah_tangga_1']) ? $post['dapat_melakukan_pekerjaan_rumah_tangga_1'] : null,
          'dapat_melakukan_pekerjaan_rumah_tangga_2' => isset($post['dapat_melakukan_pekerjaan_rumah_tangga_2']) ? $post['dapat_melakukan_pekerjaan_rumah_tangga_2'] : null,
          'dapat_melakukan_pekerjaan_rumah_tangga_3' => isset($post['dapat_melakukan_pekerjaan_rumah_tangga_3']) ? $post['dapat_melakukan_pekerjaan_rumah_tangga_3'] : null,
          'dapat_melakukan_pekerjaan_rumah_tangga_4' => isset($post['dapat_melakukan_pekerjaan_rumah_tangga_4']) ? $post['dapat_melakukan_pekerjaan_rumah_tangga_4'] : null,
          'dapat_mencuci_pakaian_1' => isset($post['dapat_mencuci_pakaian_1']) ? $post['dapat_mencuci_pakaian_1'] : null,
          'dapat_mencuci_pakaian_2' => isset($post['dapat_mencuci_pakaian_2']) ? $post['dapat_mencuci_pakaian_2'] : null,
          'dapat_mencuci_pakaian_3' => isset($post['dapat_mencuci_pakaian_3']) ? $post['dapat_mencuci_pakaian_3'] : null,
          'dapat_mengatur_obat_obatan_1' => isset($post['dapat_mengatur_obat_obatan_1']) ? $post['dapat_mengatur_obat_obatan_1'] : null,
          'dapat_mengatur_obat_obatan_2' => isset($post['dapat_mengatur_obat_obatan_2']) ? $post['dapat_mengatur_obat_obatan_2'] : null,
          'dapat_mengatur_keuangan_1' => isset($post['dapat_mengatur_keuangan_1']) ? $post['dapat_mengatur_keuangan_1'] : null,
          'dapat_mengatur_keuangan_2' => isset($post['dapat_mengatur_keuangan_2']) ? $post['dapat_mengatur_keuangan_2'] : null,
          'dapat_mengatur_keuangan_3' => isset($post['dapat_mengatur_keuangan_3']) ? $post['dapat_mengatur_keuangan_3'] : null,
          'total_iadl' => isset($post['total_iadl']) ? $post['total_iadl'] : null,
          'created_at' => date('Y-m-d H:i:s'),
          'status' => '1',
          'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,
        );

        if (!empty($post['id_iadl'])) {
          $this->db->replace('db_layanan.tb_geriatri_iadl', $data_iadl);
          $result = array('status' => 'success', 'pesan' => 'ubah');
        }else {
          $this->db->insert('db_layanan.tb_geriatri_iadl', $data_iadl);
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }

    }
  }

        public function view_iadl()
      {
        $id         = $this->input->post('id');
        $get_iadl = $this->pengkajianAwalModel->get_iadl($id);

        $data = array(
          'get_iadl' => $get_iadl,
        );
        $this->load->view('Pengkajian/geriatri/InstrumentalActivitiesOfDailyLivingLawton/view',$data);
      }
}


/* End of file Pra_anestesi.php */
/* Location: ./application/controllers/geriatri/InstrumentalActivitiesOfDailyLivingLawton/Iadl.php */
