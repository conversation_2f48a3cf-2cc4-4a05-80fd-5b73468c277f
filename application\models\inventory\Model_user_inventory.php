<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Model_user_inventory extends CI_Model {
	public function __construct()
	{
		parent::__construct();
		$this->load->database();
	}

	public function datauser()
	{
		$query  = $this->db->query("SELECT ui.ID, p.NAMA, ui.STATUS, mi.DESKRIPSI UNIT
			FROM invenumum.user_inventory ui
			LEFT JOIN aplikasi.pengguna p ON p.ID=ui.USER 
			LEFT JOIN invenumum.master_instalasi mi ON mi.ID=ui.UNIT
			where ui.STATUS=1");
		return $query;
	}

	public function datapengguna()
	{
		$query  = $this->db->query("SELECT * FROM aplikasi.pengguna where STATUS=1");
		return $query;
	}

	public function instalasi()
	{
		$query  = $this->db->query("SELECT * FROM invenumum.master_instalasi WHERE STATUS=1");
		return $query;
	}


	public function modaldatauser($id)
	{
		$query = $this->db->query("SELECT ui.ID, ui.USER USER, p.NAMA, ui.STATUS, mi.DESKRIPSI UNIT, mi.ID ID_UNIT
			FROM invenumum.user_inventory ui
			LEFT JOIN aplikasi.pengguna p ON p.ID=ui.USER 
			LEFT JOIN invenumum.master_instalasi mi ON mi.ID=ui.UNIT
			where ui.ID='$id'");
		return $query->result_array();
	}

	public function update($id, $data)
	{
		$this->db->where('ID', $id);
		$this->db->update('invenumum.user_inventory', $data);
	}

	function post($data)
	{
		$this->db->insert('invenumum.user_inventory',$data);
	}

	public function sBarangAktif($id, $data)
	{
		$this->db->where('ID_BARANG_GUDANG', $id);
		$this->db->update('invenumum.barang_gudang', $data);
	}

	public function sBarangNonAktif($id, $data)
	{
		$this->db->where('ID_BARANG_GUDANG', $id);
		$this->db->update('invenumum.barang_gudang', $data);
	}

}
