<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PTTD extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'informedConsent/PersetujuanTindakanKedokteranModel',
        'rekam_medis/rawat_inap/informedConsent/PTTDModel'
      )
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array(
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'dasarDiagnosis' => $this->masterModel->referensi(988),
      'indikasiTindakan' => $this->masterModel->referensi(989),
      'tujuanTindakan' => $this->masterModel->referensi(990),
      'tujuanPengobatan' => $this->masterModel->referensi(991),
      'risiko' => $this->masterModel->referensi(992),
      'komplikasi' => $this->masterModel->referensi(993),
      'prognosis' => $this->masterModel->referensi(994),
      'alternatifRisiko' => $this->masterModel->referensi(995),
      'jenisKelamin' => $this->masterModel->referensi(965),
    );
    if (isset($id)) {
      // Form ubah
      $data['id'] = $id;
      $detail = $this->PTTDModel->history(null, null, $id);
      $data['detail'] = $detail;
      $nokun = $detail['nokun'];
      $data['pasien'] = $this->pengkajianAwalModel->getNomr($nokun);
      $data['isiDasarDiagnosis'] = explode('-', $data['detail']['dasar_diagnosis']);
      $data['isiIndikasiTindakan'] = explode('-', $data['detail']['indikasi_tindakan']);
      $data['isiTujuanTindakan'] = explode('-', $data['detail']['tujuan_tindakan']);
      $data['isiTujuanPengobatan'] = explode('-', $data['detail']['tujuan_pengobatan']);
      $data['isiPrognosis'] = explode('-', $data['detail']['prognosis']);
      $data['isiAlternatifRisiko'] = explode('-', $data['detail']['alternatif_risiko']);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/informedConsent/PTTD/detail', $data);
    } else {
      // Form tambah
      $nokun = isset($post['nokun']) ? $post['nokun'] : $this->uri->segment(2);
      $pasien = $this->pengkajianAwalModel->getNomr($nokun);
      $nomr = $pasien['NORM'];
      $data['nokun'] = $nokun;
      $data['nomr'] = $nomr;
      $data['pasien'] = $pasien;
      $data['jumlah'] = $this->PTTDModel->history($nomr, 'jumlah', null);
      // echo '<pre>';print_r($data);exit();
      $this->load->view('rekam_medis/rawat_inap/informedConsent/PTTD/index', $data);
    }
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->PTTDModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $status = 1;
          // echo '<pre>';print_r($post);exit();

          // Start data Informed Consent
          $dataInformedConsent = array(
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'jenis_informed_consent' => 3027,
            'dokter_pelaksana' => isset($post['dokter_pelaksana']) ? $post['dokter_pelaksana'] : null,
            'penerima_informasi' => isset($post['penerima_informasi']) ? $post['penerima_informasi'] : null,
            'oleh' => $this->session->userdata['id'],
            'status' => $status,
          );
          $idInformedConsent = $this->PersetujuanTindakanKedokteranModel->simpanInformedConsent($dataInformedConsent);
          // End data Informed Consent

          // Start data Persetujuan Tindakan Transfusi Darah
          $dataPTTD = array(
            'id_informed_consent' => $idInformedConsent,
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'diagnosis' => isset($post['diagnosis']) ? $post['diagnosis'] : null,
            'dasar_diagnosis' => isset($post['dasar_diagnosis']) ? implode('-', $post['dasar_diagnosis']) : null,
            'tindakan_kedokteran' => isset($post['tindakan_kedokteran']) ? $post['tindakan_kedokteran'] : null,
            'indikasi_tindakan' => isset($post['indikasi_tindakan']) ? implode('-', $post['indikasi_tindakan']) : null,
            'indikasi_tindakan_lainnya' => isset($post['indikasi_tindakan_lainnya']) ? $post['indikasi_tindakan_lainnya'] : null,
            'tata_cara' => isset($post['tata_cara']) ? $post['tata_cara'] : null,
            'tujuan_tindakan' => isset($post['tujuan_tindakan']) ? implode('-', $post['tujuan_tindakan']) : null,
            'tujuan_tindakan_lainnya' => isset($post['tujuan_tindakan_lainnya']) ? $post['tujuan_tindakan_lainnya'] : null,
            'tujuan_pengobatan' => isset($post['tujuan_pengobatan']) ? implode('-', $post['tujuan_pengobatan']) : null,
            'risiko' => isset($post['risiko']) ? $post['risiko'] : null,
            'komplikasi' => isset($post['komplikasi']) ? $post['komplikasi'] : null,
            'prognosis' => isset($post['prognosis']) ? implode('-', $post['prognosis']) : null,
            'prognosis_lainnya' => isset($post['prognosis_lainnya']) ? $post['prognosis_lainnya'] : null,
            'alternatif_risiko' => isset($post['alternatif_risiko']) ? implode('-', $post['alternatif_risiko']) : null,
            'alternatif_risiko_lainnya' => isset($post['alternatif_risiko_lainnya']) ? $post['alternatif_risiko_lainnya'] : null,
            'lainnya' => isset($post['lainnya']) ? $post['lainnya'] : null,
            'ttd_menerangkan' => isset($post['ttd_menerangkan']) ? file_get_contents($post['ttd_menerangkan']) : null,
            'ttd_menerima' => isset($post['ttd_menerima']) ? file_get_contents($post['ttd_menerima']) : null,
            'status' => $status,
          );
          $this->PTTDModel->simpan($dataPTTD);
          // End data Persetujuan Tindakan Transfusi Darah

          // Start data Persetujuan Tindakan Kedokteran
          $dataPersetujuanTindakanKedokteran = array(
            'id_informed_consent' => $idInformedConsent,
            'nama_keluarga' => isset($post['nama']) ? $post['nama'] : null,
            'umur_keluarga' => isset($post['umur']) ? $post['umur'] : null,
            'jk_keluarga' => isset($post['jenis_kelamin']) ? $post['jenis_kelamin'] : null,
            'alamat_keluarga' => isset($post['alamat']) ? $post['alamat'] : null,
            'tindakan' => isset($post['tindakan']) ? $post['tindakan'] : null,
            'hub_keluarga_dgn_pasien' => isset($post['hubungan']) ? $post['hubungan'] : null,
            'tanggal_persetujuan' => isset($post['tanggal_setuju']) ? $post['tanggal_setuju'] : null,
            'ttd_menyatakan' => isset($post['ttd_menyatakan']) ? file_get_contents($this->input->post('ttd_menyatakan')) : null,
            'ttd_saksi_keluarga' => isset($post['ttd_keluarga']) ? file_get_contents($this->input->post('ttd_keluarga')) : null,
            'ttd_saksi_rumah_sakit' => isset($post['ttd_rumah_sakit']) ? file_get_contents($this->input->post('ttd_rumah_sakit')) : null,
            'saksi_keluarga' => isset($post['nama_keluarga']) ? $post['nama_keluarga'] : null,
            'saksi_rumah_sakit' => isset($post['nama_saksi_rs']) ? $post['nama_saksi_rs'] : null,
          );
          $this->PersetujuanTindakanKedokteranModel->simpanPersetujuanTidakanKedokteran($dataPersetujuanTindakanKedokteran);
          // End data Persetujuan Tindakan Kedokteran

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
          echo json_encode($result);
        }
      }
    }
  }

  public function tabel()
  {
    $draw = intval($this->input->POST('draw'));
    $nomr = $this->input->POST('nomr');
    $history = $this->PTTDModel->history($nomr, 'tabel', null);
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($history);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Disetujui</p>';
      }

      $data[] = array(
        $no,
        $h->nokun,
        $h->dokter_pelaksana,
        $h->oleh,
        $status,
        date('d-m-Y H:i:s', strtotime($h->tanggal)),
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-pttd' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-pttd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-pttd' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-pttd' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nomr' => $post['nomr']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/informedConsent/PTTD/history', $data);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $data = array(
      'status' => 0,
    );
    $id = array('id' => $post['id']);
    $this->PersetujuanTindakanKedokteranModel->ubahInformedConcent($id, $data);

    $data = array(
      'tgl_update' => date('Y-m-d H:i:s'),
      'status' => 0,
    );
    $id = array('id_informed_consent' => $post['id']);
    $this->PTTDModel->ubah($id, $data);

    $data = array(
      'status_persetujuan' => 0,
    );
    $id = array('id_informed_consent' => $post['id']);
    $this->PersetujuanTindakanKedokteranModel->ubahTPTK($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}

/* End of file PTTD.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/informedConsent/PTTD.php */