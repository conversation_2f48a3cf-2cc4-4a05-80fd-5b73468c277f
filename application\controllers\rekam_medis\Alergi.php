<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Alergi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
        redirect('login');
    }

    $this->load->model(array('rekam_medis/AlergiModel'));
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if($param == 'ambil'){
          $result = $this->AlergiModel->get_table(FALSE);

          echo json_encode(array(
          'status' => 'success',
          'data'   => $result
          ));
      }
    }
  }

}
