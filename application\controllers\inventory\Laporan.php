<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>poran extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('inventory/Model_permintaan','masterModel','inventory/Model_barang','inventory/Model_gudang'));
  }

  public function index()
  {
   {
      $barang     = $this->Model_barang->tampilkan_data();
      $ruangan    = $this->Model_permintaan->tampilkan_ruangan()->result();
      $gudang     = $this->Model_permintaan->tampilkan_gudang()->result();
      $data = array(
      'title'   => 'Halaman Laporan Inventory',
      'isi'     => 'inventory/Laporan/LaporanInventory',
      'gudang'  => $gudang,
    );
    $this->load->view('layout/wrapper',$data);
  }
  }

  public function rekaplaporan()
  {
    $nomr = $this->input->post('nomr');
    $namaPasien  = $this->laporanModel->namaPasien($nomr);
    $allLaporanCppt = $this->laporanModel->allLaporanCppt($nomr);

    $laporanCppt = array();
    foreach ($allLaporanCppt as $alc ) {
      $resultLaporanDokter = $this->laporanModel->laporanCppt(1,$alc['nokun']);
      $resultLaporanPerawat = $this->laporanModel->laporanCppt(2,$alc['nokun']);

      $sub_array = array();
      $sub_array['nokun']         = $alc['nokun'];
      $sub_array['NAMAPASIEN']    = $alc['NAMAPASIEN'];
      $sub_array['NORM']          = $alc['NORM'];
      $sub_array['TANGGAL_LAHIR'] = $alc['TANGGAL_LAHIR'];
      $sub_array['isi'] = array();
      foreach ($resultLaporanPerawat as $perawat) {
        array_push($sub_array['isi'],$perawat);
      }
      foreach ($resultLaporanDokter as $dataDokter) {
        array_push($sub_array['isi'],$dataDokter);
      }

      $laporanCppt[] = $sub_array;
    }

    $data = array(
      'title'          => 'Halaman View All Cppt',
      'isi'            => 'Laporan/ViewAllCppt/index',
      'nomr'           => $nomr,
      'namaPasien'     => $namaPasien,
      'allLaporanCppt' => $allLaporanCppt,
      'laporanCppt'    => $laporanCppt
    );


    $this->load->view('layout/wrapper',$data);
  }

}

