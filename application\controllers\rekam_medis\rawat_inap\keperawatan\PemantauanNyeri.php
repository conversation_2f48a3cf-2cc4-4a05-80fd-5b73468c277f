<?php
defined('BASEPATH') or exit('No direct script access allowed');

class pemantauanNyeri extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/keperawatan/pemantauanNyeriModel'));
    }

    public function index() {

        $data = array(
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(4)),
            'skriningNyeri' => $this->masterModel->referensi(7),
            'skalaNyeriNRS' => $this->masterModel->referensi(114),
            'skalaNyeriWBR' => $this->masterModel->referensi(115),
            'skalaNyeriFLACC' => $this->masterModel->referensi(123),
            'skalaNyeriBPS' => $this->masterModel->referensi(133),
            'efeksampingNRS' => $this->masterModel->referensi(118),
        );

        // echo $this->uri->segment(4);
        $this->load->view('rekam_medis/rawat_inap/keperawatan/pemantauanNyeri/index',$data);
    }

    public function indexRJ() {

        $data = array(
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(6)),
            'skriningNyeri' => $this->masterModel->referensi(7),
            'skalaNyeriNRS' => $this->masterModel->referensi(114),
            'skalaNyeriWBR' => $this->masterModel->referensi(115),
            'skalaNyeriFLACC' => $this->masterModel->referensi(123),
            'skalaNyeriBPS' => $this->masterModel->referensi(133),
            'efeksampingNRS' => $this->masterModel->referensi(118),
        );

        // echo $this->uri->segment(4);
        $this->load->view('rekam_medis/rawat_inap/keperawatan/pemantauanNyeri/index',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->pemantauanNyeriModel->rules;
                $this->form_validation->set_rules($rules);
                if($this->input->post('skrining_nyeri') != 17){
                    $this->form_validation->set_rules($this->pemantauanNyeriModel->rules_nyeri);
                    if($this->input->post('efek_samping') != 337){
                        $this->form_validation->set_rules($this->pemantauanNyeriModel->rules_efek_samping);
                    }
                }

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();

                    $datapemantauanNyeri = array(
                        'nokun' => $post['nokun'],
                        'data_source' => 4,
                        'metode' => $post['skrining_nyeri'],
                        'skor' => $post['skrining_nyeri'] != 17 ? $post['skor_nyeri'] : "",
                        'farmakologi' => $post['skrining_nyeri'] != 17 ? $post['farmakologi'] : null,
                        'non_farmakologi' => $post['skrining_nyeri'] != 17 ? $post['non_farmakologi'] : null,
                        'efek_samping' => $post['skrining_nyeri'] != 17 ? $post['efek_samping'] : "",
                        'ket_efek_samping' => $post['skrining_nyeri'] != 17 && $post['efek_samping'] == 338 ? $post['efek_samping_lain'] : "",
                        'created_at' => $post['tanggal'],
                        'created_by' => $this->session->userdata("id"),
                    );

                    if(!empty($post['id'])){
    					$this->pemantauanNyeriModel->update($datapemantauanNyeri, array('id' => $post['id']));
                        $result = array('status' => 'success');
    				}else{
	    				if($this->pemantauanNyeriModel->insert($datapemantauanNyeri)){
                            $result = array('status' => 'success');
	    				}else{
	    					$result = array('status' => 'failed');
	    				}
    				}
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataPemantauanNyeri = $this->pemantauanNyeriModel->get($post['id'], true);
                
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataPemantauanNyeri
                ));
            }else if($param == 'ambilNyeri'){
    			$post = $this->input->post(NULL,TRUE);
                $dataPemantauanNyeri = $this->pemantauanNyeriModel->get_table(true);
                
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataPemantauanNyeri
                ));
            }else if($param == 'count'){
                $result = $this->pemantauanNyeriModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->pemantauanNyeriModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_pemantauan_nyeri" data-id="'.$row -> ID.'"><i class="fa fa-eye"></i> Lihat</a><!--<a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/validasimalnutrisi/validasimalnutrisi.php?format=pdf&nopen='.$row -> NOKUN.'" target="_blank"><i class="fa fa-print"></i> Cetak</a>-->';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> DATA;
            $sub_array[] = $row -> METODE;
            $sub_array[] = $row -> SKOR;
            $sub_array[] = $row -> FARMAKOLOGI;
            $sub_array[] = $row -> NON_FARMAKOLOGI;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->pemantauanNyeriModel->total_count(),
            "recordsFiltered"   => $this->pemantauanNyeriModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}