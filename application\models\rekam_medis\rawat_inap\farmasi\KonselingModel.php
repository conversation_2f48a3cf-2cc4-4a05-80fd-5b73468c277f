<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class KonselingModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
    }
    
    function getResumeMedis($nopen)
    {
        $query = $this->db->query("
            SELECT pd.NOMOR NOPEN
            , master.getNamaLengkap(p.NORM) NAMALENGKAP
            , p.NORM
            , DATE_FORMAT(p.TANGGAL_LAHIR,'%d-%m-%Y') TANGGAL_LAHIR
            , CONCAT_WS('',p.ALAMAT,' / ',kontak.NOMOR) ALAMAT_HP
            , CONCAT(DATE_FORMAT(p.TANGGAL_LAHIR,'%d-%m-%Y'),' (',master.getCariUmur(pd.TANGGAL,p.TANGGAL_LAHIR),')') TGL_LAHIR
            , CONCAT(master.getTempatLahir(p.TEMPAT_LAHIR),', ',DATE_FORMAT(p.TANGGAL_LAHIR,'%d-%m-%Y')) TTL
            , rjk.DESKRIPSI JENISKELAMIN
            , rjk.ID ID_JK
            , DATE_FORMAT(pd.TANGGAL,'%d-%m-%Y') TANGGAL_MASUK
            , DATE_FORMAT(pl.TANGGAL,'%d-%m-%Y') TANGGAL_KELUAR
            , (SELECT ruan.ID FROM pendaftaran.kunjungan pkunj
            LEFT JOIN master.ruangan ruan ON ruan.ID = pkunj.RUANGAN
            WHERE pkunj.NOPEN=pd.NOMOR AND ruan.JENIS_KUNJUNGAN=3
            AND pkunj.`STATUS`!=0
            ORDER BY pkunj.MASUK DESC LIMIT 1
            ) ID_RUANG_RAWAT_TERAKHIR
            , (SELECT ruan.DESKRIPSI FROM pendaftaran.kunjungan pkunj
            LEFT JOIN master.ruangan ruan ON ruan.ID = pkunj.RUANGAN
            WHERE pkunj.NOPEN=pd.NOMOR AND ruan.JENIS_KUNJUNGAN=3
            AND pkunj.`STATUS`!=0
            ORDER BY pkunj.MASUK DESC LIMIT 1
            ) RUANG_RAWAT_TERAKHIR
            , (SELECT rikes.isi_alergi FROM keperawatan.tb_keperawatan kep 
            LEFT JOIN keperawatan.tb_riwayat_kesehatan rikes ON kep.id_emr = rikes.id_emr
            WHERE kep.nopen = '$nopen' LIMIT 1) ALERGI
            , ref.DESKRIPSI CARABAYAR, pj.NOMOR SEP
            , (SELECT jbr.KODE FROM master.jenis_berkas_rm jbr WHERE jbr.JENIS=r.JENIS_KUNJUNGAN AND jbr.ID=3) KODEMR1
            , tpdok.ID ID_DPJP
            , master.getNamaLengkapPegawai(tpdok.NIP) DPJP
            , pd.STATUS
            , md.ICD
            , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=md.ICD limit 1) DIAGNOSA
            #,md.DIAGNOSA
            , DATE_FORMAT(pm.TANGGAL,'%d-%m-%Y') TANGGAL_MENINGGAL
            , (SELECT  DATE_FORMAT(tm.TANGGAL,'%d-%m-%Y %H:%i:%s') FROM layanan.tindakan_medis tm

            LEFT JOIN pendaftaran.kunjungan pkuns ON pkuns.NOMOR = tm.KUNJUNGAN
            LEFT JOIN pendaftaran.pendaftaran pen ON pen.NOMOR = pkuns.NOPEN
            LEFT JOIN master.tindakan tins ON tins.ID = tm.TINDAKAN

            WHERE pen.NOMOR=pd.NOMOR AND tm.`STATUS`=1 AND pkuns.`STATUS`!=0 AND tins.ID=4611

            ORDER BY tm.TANGGAL asc

            LIMIT 1) tgl_ventilator
            
        , (SELECT master.getNamaLengkapPegawai(ap.NIP) FROM layanan.order_resep ore
            LEFT JOIN pendaftaran.kunjungan pkfa ON pkfa.REF = ore.NOMOR
            LEFT JOIN layanan.farmasi lf ON lf.KUNJUNGAN=pkfa.NOMOR
            LEFT JOIN aplikasi.pengguna ap ON ap.ID=lf.OLEH
        
            WHERE ore.KUNJUNGAN=pk.NOMOR AND ore.`STATUS`!=0
            ORDER BY ore.TANGGAL DESC
            LIMIT 1) USR_FARMASI	
            

            FROM master.pasien p
            LEFT JOIN master.kontak_pasien kontak ON p.NORM = kontak.NORM
            LEFT JOIN master.referensi rjk ON p.JENIS_KELAMIN=rjk.ID AND rjk.JENIS=2
            , pendaftaran.pendaftaran pd
            LEFT JOIN pendaftaran.penjamin pj ON pd.NOMOR=pj.NOPEN
            LEFT JOIN master.referensi ref ON pj.JENIS=ref.ID AND ref.JENIS=10
            LEFT JOIN master.kartu_asuransi_pasien kap ON pd.NORM=kap.NORM AND ref.ID=kap.JENIS AND ref.JENIS=10
            LEFT JOIN layanan.pasien_pulang pl ON pd.NOMOR=pl.NOPEN AND pl.`STATUS`=1
            LEFT JOIN master.referensi keadaan ON keadaan.ID = pl.KEADAAN AND keadaan.JENIS=46
            LEFT JOIN master.referensi cara ON cara.ID = pl.CARA AND cara.JENIS=45
            LEFT JOIN pendaftaran.kunjungan pk ON pl.KUNJUNGAN=pk.NOMOR AND pk.`STATUS`!=0
            LEFT JOIN layanan.pasien_meninggal pm ON pk.NOMOR=pm.KUNJUNGAN AND pm.`STATUS`=1
            

            LEFT JOIN master.ruangan u ON pk.RUANGAN=u.ID AND u.JENIS=5

            LEFT JOIN `master`.diagnosa_masuk md ON pd.DIAGNOSA_MASUK = md.ID
            LEFT JOIN inacbg.hasil_grouping ihg ON pd.NOMOR=ihg.NOPEN
            LEFT JOIN pendaftaran.tujuan_pasien ptp ON pd.NOMOR=ptp.NOPEN AND ptp.STATUS IN (1,2)
            LEFT JOIN master.dokter tpdok ON tpdok.ID=ptp.DOKTER AND tpdok.STATUS=1
            , pendaftaran.tujuan_pasien tp
            LEFT JOIN master.ruangan r ON tp.RUANGAN=r.ID AND r.JENIS=5
        
            
            , (SELECT mp.NAMA, ai.PPK
            FROM aplikasi.instansi ai
            , master.ppk mp
            WHERE ai.PPK=mp.ID) inst

            WHERE p.NORM=pd.NORM AND pd.NOMOR=tp.NOPEN AND pd.NOMOR = '$nopen'
        
            GROUP BY pd.NOMOR
        ");

        return $query->row_array();
    }

    function getRiwayatPengobatan($nopen) 
    {
        $query = $this->db->query("SELECT pk.NOPEN,ib.ID ,ib.NAMA NAMAOBAT, SUM(lf.JUMLAH) JUMLAH
                            , ref.DESKRIPSI, lf.DOSIS
                            , IF(ref.DESKRIPSI IS NULL, lf.ATURAN_PAKAI, ref.DESKRIPSI) ATURANPAKAI ,
                            lf.KETERANGAN, CONCAT(lf.RACIKAN,lf.GROUP_RACIKAN) RACIKAN, lf.PETUNJUK_RACIKAN
                            , generik.DESKRIPSI GENERIK, lf.`STATUS` STATUSLAYANAN
                            , IF(rto.OBAT IS NULL, 0,1) STATUS
                            FROM layanan.farmasi lf
                            LEFT JOIN inventory.barang ib ON  lf.FARMASI=ib.ID
                            LEFT JOIN master.referensi generik ON ib.GENERIK = generik.ID AND generik.JENIS=42
                            LEFT JOIN master.referensi ref ON ref.ID=lf.ATURAN_PAKAI AND ref.JENIS=41
                            LEFT JOIN pendaftaran.kunjungan pk ON lf.KUNJUNGAN=pk.NOMOR
                            LEFT JOIN resume_medis.terapi_obat rto ON ib.ID = rto.OBAT AND pk.NOPEN = rto.NOPEN
                            LEFT JOIN layanan.order_resep o ON o.NOMOR=pk.REF
                            LEFT JOIN master.dokter md ON o.DOKTER_DPJP=md.ID
                            LEFT JOIN master.pegawai mp ON md.NIP=mp.NIP
                            LEFT JOIN pendaftaran.kunjungan asal ON o.KUNJUNGAN=asal.NOMOR
                            LEFT JOIN master.ruangan r ON asal.RUANGAN=r.ID AND r.JENIS=5
                            LEFT JOIN master.referensi jenisk ON r.JENIS_KUNJUNGAN=jenisk.ID AND jenisk.JENIS=15 , pendaftaran.pendaftaran pp
                            LEFT JOIN master.pasien ps ON pp.NORM=ps.NORM

                            , pembayaran.rincian_tagihan rt

                            WHERE lf.`STATUS`=2 AND  pk.`STATUS` IN (1,2) AND pk.NOPEN=pp.NOMOR
                            AND pk.NOPEN= '$nopen' AND lf.ID=rt.REF_ID AND rt.JENIS=4 AND ib.KATEGORI != 10210
                            GROUP BY ib.ID
                            ORDER BY lf.RACIKAN, lf.GROUP_RACIKAN");

        return $query->result_array();

    }

    function getObatTerakhir($nopen) 
    {
        $query = $this->db->query("SELECT ib.ID, p.NORM, p.NOMOR NOPEN
                , pku.NOMOR NOKUN, ib.NAMA NAMA_OBAT, generik.DESKRIPSI GENERIK, lf.JUMLAH
                , IF(ref.DESKRIPSI IS NULL, lf.ATURAN_PAKAI, ref.DESKRIPSI) ATURANPAKAI, lf.DOSIS
                , lf.KETERANGAN, CONCAT(lf.RACIKAN,lf.GROUP_RACIKAN) RACIKAN, lf.PETUNJUK_RACIKAN,
                lf.`STATUS` STATUSLAYANAN

                FROM pendaftaran.pendaftaran p

                LEFT JOIN pendaftaran.kunjungan pku ON pku.NOPEN = p.NOMOR
                LEFT JOIN layanan.order_resep ore ON ore.NOMOR = pku.REF
                LEFT JOIN layanan.farmasi lf ON lf.KUNJUNGAN = pku.NOMOR
                LEFT JOIN master.referensi ref ON ref.ID=lf.ATURAN_PAKAI AND ref.JENIS=41
                LEFT JOIN inventory.barang ib ON ib.ID = lf.FARMASI
                LEFT JOIN master.referensi generik ON ib.GENERIK = generik.ID AND generik.JENIS=42

                WHERE ore.RESEP_PASIEN_PULANG=1 and
                p.`STATUS`!=0 and pku.`STATUS`!=0 and lf.`STATUS`=2
                AND p.NOMOR= '$nopen'

                ORDER BY lf.RACIKAN, lf.GROUP_RACIKAN
        ");
        
        return $query->result_array();

    }

    function table_query()
    {
        $this->db->select('kons.id, kons.nokun, kons.created_at tanggal, ru.DESKRIPSI ruangan, peng.NAMA user');
        $this->db->from('keperawatan.tb_konseling kons');
        $this->db->join('pendaftaran.kunjungan kun','kun.NOMOR = kons.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','pen.NOMOR = kun.NOPEN','LEFT');
        $this->db->join('master.ruangan ru','kun.RUANGAN = ru.ID','LEFT');
        $this->db->join('aplikasi.pengguna peng','kons.oleh = peng.ID','LEFT');
        $this->db->where('kons.STATUS !=','0');
        $this->db->where('pen.NORM',$this->input->post('nomr'));
        $this->db->group_by('kons.nokun');
        $this->db->order_by('kons.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    function getPengkajian($id_nokun_konseling)
    {
        $query = $this->db->query("SELECT * FROM keperawatan.tb_konseling kons
        WHERE kons.nokun = '$id_nokun_konseling' AND kons.`status` = 1 ");

        return $query->result_array();
    }

    function getPengguna($id_nokun_konseling)
    {
        $query = $this->db->query("SELECT master.getNamaLengkapPegawai(peng.NIP) NAMA FROM keperawatan.tb_konseling kons
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = kons.oleh
        WHERE kons.nokun = '$id_nokun_konseling' AND kons.`status` = 1 LIMIT 1");

        return $query->row_array();
    }


}
