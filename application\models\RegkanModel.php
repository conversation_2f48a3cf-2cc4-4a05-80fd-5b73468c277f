<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RegkanModel extends CI_Model {

  public function listPasien()
  {
    $this->db->select('pen.NORM, master.getNama<PERSON><PERSON><PERSON>p(pen.NORM) NAMA_PASIEN, pen.TANGGAL TANGGAL_DAFTAR, enc.encounter ID_ENCOUNTER, enc.id_ihs_pasien ID_IHS_PASIEN, con2.id_encounter ID_ENCOUNTER_VER, con2.kode_perluasan_tumor KODE_PERLUASAN_TUMOR
      , con2.kode_lateralitas_tumor KODE_LATERALITAS_TUMOR, con2.kode_metastasis_jauh_1 KODE_METAS_1
      , con2.kode_metastasis_jauh_2 KODE_METAS_2, con2.kode_metastasis_jauh_3 KODE_METAS_3
      , con2.kode_metastasis_jauh_4 KODE_METAS_4');
         $this->db->from('pendaftaran.pendaftaran pen');
         $this->db->join('ihs.tb_encounter enc', 'enc.nopen = pen.NOMOR', 'left');
         $this->db->join('pendaftaran.kunjungan pk', 'pk.NOPEN = pen.NOMOR', 'left');
         $this->db->join('db_regkan.tb_condition_v2 con2', 'con2.id_encounter = enc.encounter AND con2.jenis_condition=6', 'left');
         $this->db->where('enc.encounter IS NOT NULL');
         $this->db->where_in('pen.STATUS', [1, 2]);
         $this->db->where('pk.REF IS NULL');
         $this->db->where('(SELECT db_regkan.getDiagnosaKonfirmasi(pen.NORM)) IS NOT NULL');
         $this->db->where('pk.MASUK >=', date('Y') . '-01-01 00:00:00');
         $this->db->where('pk.MASUK <=', date('Y-m-d H:i:s'));
         /*$this->db->where("NOT EXISTS (
          SELECT 1
          FROM db_regkan.tb_condition_v2 cv2
          WHERE cv2.id_encounter = enc.encounter AND cv2.jenis_condition = 6
          LIMIT 1
        )");*/
         $this->db->limit(100);

    if (!empty($_POST['search']['value'])) {
        $this->db->group_start();
        $this->db->like('pen.NORM', $_POST['search']['value']);
        // $this->db->or_like("k.RUANGAN", $_POST['search']['value']);
        $this->db->group_end();
    }
  }

  function dataTableListPasien(){
    $this->listPasien();
    if($_POST["length"] != -1){
      $this->db->limit($_POST["length"], $_POST["start"]);
    }
    $query = $this->db->get();
    return $query->result();
  }

  function filter_countListPasien(){
    $this->listPasien();
    $query = $this->db->get();
    return $query->num_rows();
  }

  function total_countListPasien(){
    $this->listPasien();
    return $this->db->count_all_results();
  }

}