<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Rujukan extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/TbBbModel',
        'HIV/RujukanModel'
      )
    );
  }

  public function index($id = null)
  {
    $nokun = $this->uri->segment(3);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'id' => $id,
      'pasien' => $pasien,
      'regNas' => $this->RujukanModel->ambilRegNas($pasien['NORM']),
      'stadiumKlinis' => $this->masterModel->referensi(1758),
      'pilihanCPPT' => $this->masterModel->referensi(1407),
      'statusFungsional' => $this->masterModel->referensi(1749),
      'statusTB' => $this->masterModel->referensi(1752),
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'pegawai' => $this->masterModel->listAllPegawai(),
      'jumlah' => $this->RujukanModel->history($pasien['NOKUN'], 'jumlah'),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/HIV/Rujukan/index', $data);
  }

  public function simpan()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->RujukanModel->rules;
      $this->form_validation->set_rules($rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = isset($post['id']) ? $post['id'] : null;
        $idRegNas = null;
        $nokun = isset($post['nokun']) ? $post['nokun'] : null;
        $nomr = isset($post['nomr']) ? $post['nomr'] : null;
        $oleh = $this->session->userdata['id'];

        // Mulai data registrasi nasional
        if ($post['id_reg_nas'] != null || !empty($post['id_reg_nas'])) {
          $idRegNas = $post['id_reg_nas'];
        } else {
          $dataRegNas = array(
            'nomr' => $nomr,
            'no_reg_nas' => isset($post['no_reg_nas']) ? $post['no_reg_nas'] : null,
            'oleh' => $oleh,
            'status' => 1,
          );
          $idRegNas = $this->RujukanModel->simpanRegNas($dataRegNas);
        }
        // Akhir data registrasi nasional

        // Mulai data
        $data = array(
          'nokun' => $nokun,
          'id_reg_nas' => $idRegNas,
          'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
          'waktu' => isset($post['waktu']) ? $post['waktu'] : null,
          'tanggal_tes_hiv' => isset($post['tanggal_tes_hiv']) ? $post['tanggal_tes_hiv'] : null,
          'tempat_tes_hiv' => isset($post['tempat_tes_hiv']) ? $post['tempat_tes_hiv'] : null,
          'stadium_klinis' => isset($post['stadium_klinis']) ? $post['stadium_klinis'] : null,
          'status_fungsional' => isset($post['status_fungsional']) ? $post['status_fungsional'] : null,
          'rejimen' => isset($post['rejimen']) ? $post['rejimen'] : null,
          'io' => isset($post['io']) ? $post['io'] : null,
          'profilaksis_io' => isset($post['profilaksis_io']) ? $post['profilaksis_io'] : null,
          'status_tb' => isset($post['status_tb']) ? $post['status_tb'] : null,
          'dokter' => isset($post['dokter']) ? $post['dokter'] : null,
          'no_register' => isset($post['no_register']) ? $post['no_register'] : null,
          'tanggal_melapor' => isset($post['tanggal_melapor']) ? $post['tanggal_melapor'] : null,
          'tempat_berobat_baru' => isset($post['tempat_berobat_baru']) ? $post['tempat_berobat_baru'] : null,
          'tanggal_pengembalian' => isset($post['tanggal_pengembalian']) ? $post['tanggal_pengembalian'] : null,
          'pengisi_pengembalian' => isset($post['pengisi_pengembalian']) ? $post['pengisi_pengembalian'] : null,
          'oleh' => $oleh,
          'status' => 1,
        );
        // Akhir data

        // Mulai simpan
        // echo '<pre>';print_r($data);exit();
        if (!empty($id)) {
          $this->RujukanModel->ubah($data, $id);
        } else {
          $idBaru = $this->RujukanModel->simpan($data);
        }
        // Akhir simpan

        // Mulai simpan tinggi dan berat badan
        $data = array(
          'data_source' => 48,
          'ref' => !empty($id) ? $id : $idBaru,
          'nomr' => $nomr,
          'nokun' => $nokun,
          'tb' => isset($post['tb']) ? $post['tb'] : null,
          'bb' => isset($post['bb']) ? $post['bb'] : null,
          'oleh' => $oleh,
          'status' => 1,
        );
        // echo '<pre>';print_r($data);exit();
        if (!empty($id)) {
          $this->TbBbModel->ubahRef($data, $id);
        } else {
          $this->TbBbModel->insert($data);
        }
        // Akhir simpan tinggi dan berat badan

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/HIV/Rujukan/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->RujukanModel->history($nokun, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      // Mulai periksa status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }
      // Akhir periksa status

      // Mulai data
      $data[] = array(
        $no++,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->waktu)),
        $h->pengisi,
        date('d-m-Y, H:i:s', strtotime($h->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-rart' class='btn btn-sm btn-danger waves-effect tbl-batal-rart' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' class='btn btn-sm btn-primary waves-effect tbl-detail-rart' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      // Akhir data
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array('status' => 0);
    $this->RujukanModel->ubah($data, $id);
    $this->TbBbModel->ubahRef($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detail()
  {
    $post = $this->input->post(null, true);
    $detail = $this->RujukanModel->detail($post['id']);
    // echo '<pre>';print_r($detail);exit();
    echo json_encode(
      array(
        'status' => 'succes',
        'data' => $detail,
      )
    );
  }
}

/* End of file Rujukan.php */
/* Location: ./application/controllers/HIV/Rujukan.php */