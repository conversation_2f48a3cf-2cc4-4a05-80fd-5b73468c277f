<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SofaKeluarModel extends MY_Model
{
    protected $_table_name = 'keperawatan.tb_sofa_keluar';
    protected $_primary_key = 'nopen';
    protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>.',
                'numeric' => '%s Wajib <PERSON>.'
            ),
        ),
    );

    function __construct()
    {
        parent::__construct();
    }

    public function get_sofa($nokun)
    {
        $query = $this->db->query("select * from keperawatan.tb_sofa_keluar sf
        left join pendaftaran.pendaftaran pp ON sf.nopen=pp.NOMOR
        left join pendaftaran.kunjungan pk On pk.NOPEN=pp.NOMOR
        where pk.NOMOR= '$nokun'");
        return $query->row_array();
    }

    function table_query()
    {
        $this->db->select('sf.nopen NOPEN, sf.created_at TANGGAL
        , master.getNamaLengkapPegawai(peng.NIP) USER
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , rk.DESKRIPSI RUANGAN_KUNJUNGAN
        , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN');
        $this->db->from('keperawatan.tb_sofa_keluar sf');
        $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = sf.nopen', 'LEFT');
        $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = p.NOMOR', 'LEFT');
        $this->db->join('master.diagnosa_masuk dm', 'dm.ID = p.DIAGNOSA_MASUK', 'LEFT');
        $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'LEFT');
        $this->db->join('master.ruangan rk', 'rk.ID = tp.RUANGAN', 'LEFT');
        $this->db->join('aplikasi.pengguna peng', 'peng.ID = sf.oleh', 'LEFT');

        $this->db->where('sf.STATUS !=', '0');
        $this->db->where('p.NORM', $this->input->post('nomr'));
        $this->db->order_by('sf.created_at', 'DESC');
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}
