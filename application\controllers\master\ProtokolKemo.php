<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProtokolKemo extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(39, $this->session->userdata('akses'))){
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel'));
    }

    public function index()
    {
		$listProtokol = $this->masterModel->protokolKemo();
        $data = array(
            'title'              => 'Halaman Master',
            'isi'                => 'Master/protokolKemo/index',
			'listProtokol' 		 => $listProtokol,
        );

        $this->load->view('layout/wrapper', $data);
    }

	 public function listProtokolKemo()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listProtokol = $this->masterModel->protokolKemo();

     //echo "<pre>";print_r($listProtokol);exit();
    $data  = array();
    $no    =1;
    foreach($listProtokol->result() as $lp) {
      $data[] = array(
        $no,
        $lp->NAMA,
        $lp->TANGGAL,
        '<a href="#detailProtokol" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-id="'.$lp->ID.'" data-nama="' . $lp->NAMA . '"><i class="fas fa-edit"></i> Detail</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listProtokol->num_rows(),
      "recordsFiltered" => $listProtokol->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

   public function simpanProtokol()
  {

		 $nama      		=  $this->input->post("nama");
		 $ket				=  $this->input->post("keterangan");
		 $tanggal           = date("Y-m-d H:i:s");
		 $oleh              = $this->session->userdata("id");
		 $status 			= 1;

		 $data = array(
				  'NAMA'           	=> $nama,
				  'TANGGAL'      	=> $tanggal,
				  'KETERANGAN'     	=> $ket,
				  'OLEH'     		=> $oleh ,
				  'STATUS'     		=> $status,
				);
		  $this->db->insert('db_master.tb_protokol_kemo', $data);
		  $result = array('status' => 'success');
		  return $result;
  }

     public function simpanDetilProtokol()
  {
		 $idPaket			 =  $this->input->post("id_paket");
		 $farmasi      		 =  $this->input->post("farmasi");
		 $kecepatan			 =  $this->input->post("kecepatan");
		 $jenisObat          =  $this->input->post("jenis_obat");
		 $pengenceran        =  $this->input->post("pengenceran");
		 $aksesPemberianObat =  $this->input->post("akses_pemberian_obat");
		 $status 			 = 1;

		 $data = array(
				  'PAKET'           	=> $idPaket,
				  'JENIS'      			=> $jenisObat,
				  'FARMASI'     		=> $farmasi,
				  'PENGENCERAN'     	=> $pengenceran ,
				  'AKSES_PEMBERIAN'     => $aksesPemberianObat,
				  'KECEPATAN'     		=> $kecepatan,
				  'KETERANGAN'     		=> "",
				  'STATUS'     			=> $status,
				);
		   $this->db->insert('db_master.tb_protokol_kemo_detil', $data);
		  $result = array('status' => 'success');
		  return $result;
  }

    public function detailProtokol()
  {
		$id = $this->input->post('id');
		$detailDataOBat = $this->masterModel->detailObatProtokol($id);
		$nama = $this->input->post('nama');
		// echo "<pre>";print_r($detailDataOBat);exit();
		 //print_r($detailDataOBat);exit();
		$data = array(
		  'id'    			 => $id,
		  'namaProtokol'     => $nama,
		  'detailObat'       => $detailDataOBat,
		);
		//echo "<pre>";print_r($detailDataOBat);exit();

		$this->load->view('Master/protokolKemo/modalDetailProtokol',$data);

  }

  public function dataFormID(){
	   $id 					= $this->input->post('id');
	   $nama 				= $this->input->post('nama');
	   $listObat 			= $this->masterModel->listObat();
	   $refPemberianObat 	= $this->masterModel->refPemberianObatProtokol();
	   $lisPengenceranObat	= $this->masterModel->listPengenceranObat();
		$data = array(
		  'id'    			 	=> $id,
		  'namaProtokol'     	=> $nama,
		  'listObatProtokol' 	=> $listObat,
		  'refPemberianObat' 	=> $refPemberianObat,
		  'lisObatPengenceran'	=> $lisPengenceranObat,
		);
		$this->load->view('Master/protokolKemo/modalFormProtokol',$data);
  }


    ///////////////////////////////////////////////////////////// END/////////////////////////////////////////////////////////////////////////
}

/* End of file ProtokolKemo.php */
/* Location: ./application/controllers/master/ProtokolKemo.php */
