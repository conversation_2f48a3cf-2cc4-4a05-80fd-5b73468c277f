<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class FKRIModel extends CI_Model {
    public function historyFKPKRI($id_fkri) {
        $query = $this->db->query(
            "SELECT f.id, f.tanggal, r.DESKRIPSI ruangan FROM medis.tb_igd_fkpkri f
            LEFT JOIN master.ruangan r ON f.ruangan = r.ID AND r.JENIS = 5
            WHERE f.id_fkri = '$id_fkri'"
          );
          return $query->result_array();
    }

    public function detailFKPKRI($id) {
        $query = $this->db->query(
            "SELECT r.DESKRIPSI nama_ruangan, f.`*` FROM medis.tb_igd_fkpkri f
            LEFT JOIN master.ruangan r ON f.ruangan = r.ID AND r.JENIS = 5
            WHERE f.id = '$id'"
        );

        return $query->row_array();
    }

    public function getTekananDarah($nopen) {
        $query = $this->db->query(
            "SELECT tv.nomr, tv.nokun, pk.NOPEN, tv.td_sistolik, tv.td_diastolik
            , tv.nadi, tv.pernapasan, tv.suhu, tv.created_at
            FROM db_pasien.tb_tanda_vital tv
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tv.nokun
            WHERE pk.NOPEN='$nopen' AND tv.status=1
            ORDER BY tv.created_at DESC
            LIMIT 1"
        );

        return $query->row_array();
    }
}