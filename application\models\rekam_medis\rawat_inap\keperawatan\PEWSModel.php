<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PEWSModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_pews';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = [
    'ref' => [
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      ]
    ],
  ];

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_pews', $data);
  }

  public function history($nomr, $nokun)
  {
    $this->db->select(
      'p.id, p.nokun, rk.DESKRIPSI ruang, (
        SELECT pu.tanggal
        FROM keperawatan.tb_pews pu
        LEFT JOIN db_master.variabel pru ON pru.id_variabel = pu.perilaku
        LEFT JOIN aplikasi.pengguna pengu ON pengu.ID = pu.oleh
        LEFT JOIN master.pegawai pegu ON pegu.NIP = pu.perawat2
        WHERE pu.status = 1 AND pu.nokun = p.nokun
        ORDER BY pu.tanggal DESC, pu.waktu DESC
        LIMIT 1
      ) tanggal, (
        SELECT pu.waktu
        FROM keperawatan.tb_pews pu
        LEFT JOIN db_master.variabel pru ON pru.id_variabel = pu.perilaku
        LEFT JOIN aplikasi.pengguna pengu ON pengu.ID = pu.oleh
        LEFT JOIN master.pegawai pegu ON pegu.NIP = pu.perawat2
        WHERE pu.status = 1 AND pu.nokun = p.nokun
        ORDER BY pu.tanggal DESC, pu.waktu DESC
        LIMIT 1
      ) waktu, (
        SELECT pu.skor
        FROM keperawatan.tb_pews pu
        LEFT JOIN db_master.variabel pru ON pru.id_variabel = pu.perilaku
        LEFT JOIN aplikasi.pengguna pengu ON pengu.ID = pu.oleh
        LEFT JOIN master.pegawai pegu ON pegu.NIP = pu.perawat2
        WHERE pu.status = 1 AND pu.nokun = p.nokun
        ORDER BY pu.tanggal DESC, pu.waktu DESC
        LIMIT 1
      ) skor, (
        SELECT master.getNamaLengkapPegawai(pengu.NIP) oleh
        FROM keperawatan.tb_pews pu
        LEFT JOIN db_master.variabel pru ON pru.id_variabel = pu.perilaku
        LEFT JOIN aplikasi.pengguna pengu ON pengu.ID = pu.oleh
        LEFT JOIN master.pegawai pegu ON pegu.NIP = pu.perawat2
        WHERE pu.status = 1 AND pu.nokun = p.nokun
        ORDER BY pu.tanggal DESC, pu.waktu DESC
        LIMIT 1
      ) oleh, (
        SELECT master.getNamaLengkapPegawai(pegu.NIP) oleh
        FROM keperawatan.tb_pews pu
        LEFT JOIN db_master.variabel pru ON pru.id_variabel = pu.perilaku
        LEFT JOIN aplikasi.pengguna pengu ON pengu.ID = pu.oleh
        LEFT JOIN master.pegawai pegu ON pegu.NIP = pu.perawat2
        WHERE pu.status = 1 AND pu.nokun = p.nokun
        ORDER BY pu.tanggal DESC, pu.waktu DESC
        LIMIT 1
      ) perawat2, (
        SELECT pu.status
        FROM keperawatan.tb_pews pu
        LEFT JOIN db_master.variabel pru ON pru.id_variabel = pu.perilaku
        LEFT JOIN aplikasi.pengguna pengu ON pengu.ID = pu.oleh
        LEFT JOIN master.pegawai pegu ON pegu.NIP = pu.perawat2
        WHERE pu.status = 1 AND pu.nokun = p.nokun
        ORDER BY pu.tanggal DESC, pu.waktu DESC
        LIMIT 1
      ) status'
    );
    $this->db->from('keperawatan.tb_pews p');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = p.oleh', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = p.perawat2', 'left');
    $this->db->join('pendaftaran.kunjungan k', 'k.NOMOR = p.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pen', 'pen.NOMOR = k.NOPEN', 'left');
    $this->db->join('pendaftaran.tujuan_pasien tp', 'tp.NOPEN = pen.NOMOR', 'left');
    $this->db->join('master.ruangan r', 'r.ID = tp.RUANGAN', 'left');
    $this->db->join('master.ruangan rk', 'rk.ID = k.RUANGAN', 'left');
    $this->db->where('p.status', 1);
    if (isset($nomr)) {
      $this->db->where('p.nomr', $nomr);
      $this->db->group_by('p.nokun');
      $this->db->order_by('k.MASUK', 'desc');
      $query = $this->db->get();
      return $query->result_array();
    } elseif (isset($nokun)) {
      $this->db->where('p.nokun', $nokun);
      $query = $this->db->get();
      return $query->row_array();
    }
  }

  public function detailHistory($nokun)
  {
    $this->db->select(
      'p.id, p.nokun, p.tanggal, p.waktu, pr.variabel perilaku, kv.variabel kardio_vaskuler, pn.variabel pernapasan,
      p.skor, master.getNamaLengkapPegawai(peng.NIP) oleh, peg.NAMA perawat2, p.status'
    );
    $this->db->from('keperawatan.tb_pews p');
    $this->db->join('db_master.variabel pr', 'pr.id_variabel = p.perilaku', 'left');
    $this->db->join('db_master.variabel kv', 'kv.id_variabel = p.kardio_vaskuler', 'left');
    $this->db->join('db_master.variabel pn', 'pn.id_variabel = p.pernafasan', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = p.oleh', 'left');
    $this->db->join('master.pegawai peg', 'peg.NIP = p.perawat2', 'left');
    $this->db->where('p.status', 1);
    $this->db->where('p.nokun', $nokun);
    $this->db->order_by('p.tanggal', 'desc');
    $this->db->order_by('p.waktu', 'desc');
    $query = $this->db->get();
    return $query->result_array();
  }
}

// End of file PEWSModel.php
// Location: ./application/models/rekam_medis/rawat_inap/keperawatan/PEWSModel.php