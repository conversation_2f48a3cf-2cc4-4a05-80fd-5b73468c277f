<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MerujukPasien extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  ///////////// Modal view Merujuk Pasien ////////////////
    public function hisDetMerujukPasien()
    {
        $nokun_merujuk_pasien = $this->input->post('nokun');
        $nomr = $this->input->post('nomr');
        $detMerujukPasien = $this->pengkajianAwalModel->getmerujukpasien($nokun_merujuk_pasien);
        $listDrUmum = $this->masterModel->listDrUmum();
        $riwayatPengobatan = $this->masterModel->referensi(329);
        $riwayatAlergi = $this->masterModel->referensi(2);
        $riwayatPenyakitMenular = $this->masterModel->referensi(338);
        $kondisiSaatIni = $this->masterModel->referensi(330);
        $score = $this->masterModel->referensi(331);
        $skriningNyeri = $this->masterModel->referensi(7);
        $kesadaran = $this->masterModel->referensi(5);
        $skalaNyeriNRS = $this->masterModel->referensi(114);
        $skalaNyeriWBR = $this->masterModel->referensi(115);
        $skalaNyeriFLACC = $this->masterModel->referensi(123);
        $skalaNyeriBPS = $this->masterModel->referensi(133);
        $efeksampingNRS = $this->masterModel->referensi(118);
        $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
        $pengkajianNyeriQuality = $this->masterModel->referensi(9);
        $pengkajianNyeriTime = $this->masterModel->referensi(12);
        $skriningResikoJatuhPusing = $this->masterModel->referensi(120);
        $skriningResikoJatuhBerdiri = $this->masterModel->referensi(121);
        $skriningResikoJatuh6Bulan = $this->masterModel->referensi(122);
        $kondisiKulit = $this->masterModel->referensi(844);
        $mobilisasiMerujukPasien = $this->masterModel->referensi(845);
        $alatBantu = $this->masterModel->referensi(19);

        $kunjungan_pk = $this->pengkajianAwalModel->kunjungan_pk($nomr);
        $sitologi = $this->pengkajianAwalModel->sitologi($nomr);
        $histologi = $this->pengkajianAwalModel->histologi($nomr);
        $tindakan_rad = $this->pengkajianAwalModel->tindakan_rad($nomr);

        $data = array(
            'nokun' => $nokun_merujuk_pasien,
            'detMerujukPasien' => $detMerujukPasien,
            'riwayatPengobatan' => $riwayatPengobatan,
            'riwayatAlergi' => $riwayatAlergi,
            'riwayatPenyakitMenular' => $riwayatPenyakitMenular,
            'kondisiSaatIni' => $kondisiSaatIni,
            'kesadaran' => $kesadaran,
            'score' => $score,
            'skriningNyeri' => $skriningNyeri,
            'skalaNyeriNRS' => $skalaNyeriNRS,
            'skalaNyeriWBR' => $skalaNyeriWBR,
            'skalaNyeriFLACC' => $skalaNyeriFLACC,
            'skalaNyeriBPS' => $skalaNyeriBPS,
            'efeksampingNRS' => $efeksampingNRS,
            'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
            'pengkajianNyeriQuality' => $pengkajianNyeriQuality,
            'pengkajianNyeriTime' => $pengkajianNyeriTime,
            'skriningResikoJatuhPusing' => $skriningResikoJatuhPusing,
            'skriningResikoJatuhBerdiri' => $skriningResikoJatuhBerdiri,
            'skriningResikoJatuh6Bulan' => $skriningResikoJatuh6Bulan,
            'kondisiKulit' => $kondisiKulit,
            'mobilisasiMerujukPasien' => $mobilisasiMerujukPasien,
            'alatBantu' => $alatBantu,
            'kunjungan_pk' => $kunjungan_pk,
            'sitologi' => $sitologi,
            'histologi' => $histologi,
            'tindakan_rad' => $tindakan_rad,
            'listDrUmum' => $listDrUmum,
        );

        $this->load->view('Pengkajian/igd/formulirMerujukPasien/modalViewEditMerujukPasien', $data);
    }

  public function action_merujuk_pasien(){
    $nokun = $this->input->post('nokun');
    $nokun_edit = $this->input->post('nokun_edit');
    $post = $this->input->post();
    $kersMerujukPasien = $this->input->post('keRsMerujukPasien');
    $diagnosisMerujukPasien = $this->input->post('diagnosisMerujukPasien');
    $dpjpMerujukPasien = $this->input->post('dpjp');
    $alasanMerujukPasien = $this->input->post('alasanMerujukPasien');
    $penerimaInformasiMerujukPasien = $this->input->post('penerimaInformasiMerujukPasien');
    $riwayatPengobatan = $this->input->post('riwayat_pengobatan');
    $riwayatPengoabtanLainnya = $this->input->post('riwayatPengobatanMerujukPasienLainnya');
    $alergiMerujukPasien = $this->input->post('riwayat_alergi');
    $isiAlergiMerujukPasien = $this->input->post('riwayat_alergi_desk');
    $reaksiAlergiMerujukPasien = $this->input->post('reaksi_alergi');
    $riwayatPenyakitMenular = $this->input->post('riwayat_penyakit_menular_merujuk_pasien');
    $riwayatPenyakitMenularLainnya = $this->input->post('riwayatMenularMerujukPasienLainnya');
    $kondisiSaatIni = $this->input->post('kondisi_saat_ini_merujuk_pasien');
    $kondisiSaatIniLainnya = $this->input->post('kondisiSaatIniMerujukPasienLainnya');
    $alasanPemindahan = $this->input->post('alasanPemindahanMerujukPasien');
    $kesadaranMerujukPasien = $this->input->post('kesadaran');
    $tekananDarahSistolik = $this->input->post('tekanan_darah_1');
    $tekananDarahDiastolik = $this->input->post('tekanan_darah_2');
    $pernapasan = $this->input->post('pernapasan');
    $nadi = $this->input->post('nadi');
    $suhu = $this->input->post('suhu');
    $score = $this->input->post('score_merujuk_pasien');
    $scoreLainnya = $this->input->post('scoreMerujukPasienLainnya');
    $nyeri = $this->input->post('skrining_nyeri');
    $skala_nyeri = $this->input->post('skor_nyeri');
    $farmakologi = $this->input->post('farmakologi');
    $non_farmakologi = $this->input->post('non_farmakologi');
    $efek_samping = $this->input->post('efek_samping');
    $efek_samping_lain = $this->input->post('efek_samping_lain');
    $propocative = $this->input->post('propocative');
    $quality = $this->input->post('quality');
    $quality_lainnya = $this->input->post('quality_lainnya');
    $regio = $this->input->post('regio');
    $severity = $this->input->post('severity');
    $time = $this->input->post('time');
    $time_lainnya = $this->input->post('durasi_nyeri');
    $skrining_risiko_a = $this->input->post('skriningresikojatuhpusing_MerujukPasien');
    $skrining_risiko_b = $this->input->post('skriningresikojatuhberdiri_MerujukPasien');
    $skrining_risiko_c = $this->input->post('skriningresikojatuh6bulan_MerujukPasien');
    $kondisi_kulit = $this->input->post('kondisi_kulit_merujuk_pasien');
    $kondisi_kulit_lain = $this->input->post('kondisiKulitMerujukPasienLainnya');
    $mobilisasi = $this->input->post('mobilisasi_merujuk_pasien');
    $cairan_terakhir_input = $this->input->post('balanceInputMerujukPasien');
    $cairan_terakhir_output = $this->input->post('balanceOutputMerujukPasien');
    $pemeriksaan_dari_luar = $this->input->post('penunjangLainya');
    $oleh = $this->session->userdata("id");

    $dataMerujukPasien= array(
      'nokun' => $nokun,
      'kers_merujuk_pasien' => $kersMerujukPasien,
      'diagnosis_merujuk_pasien' => $diagnosisMerujukPasien,
      'dpjp_perujuk' => $dpjpMerujukPasien,
      'alasan_merujuk' => $alasanMerujukPasien,
      'penerima_informasi' => $penerimaInformasiMerujukPasien,
      'riwayat_pengobatan' => $riwayatPengobatan,
      'riwayat_pengobatan_lainnya' => $riwayatPengoabtanLainnya,
      'alergi' => $alergiMerujukPasien,
      'isi_alergi' => json_encode($isiAlergiMerujukPasien),
      'reaksi_alergi' => $reaksiAlergiMerujukPasien,
      'riwayat_penyakit_menular' => $riwayatPenyakitMenular,
      'riwayat_penyakit_menular_lainnya' => $riwayatPenyakitMenularLainnya,
      'kondisi_saat_ini' => $kondisiSaatIni,
      'kondisi_saat_ini_lainnya' => $kondisiSaatIniLainnya,
      'alasan_pemindahan' => $alasanPemindahan,
      'kesadaran' => $kesadaranMerujukPasien,
      'tekanan_darah_sistolik' => $tekananDarahSistolik,
      'tekanan_darah_diastolik' => $tekananDarahDiastolik,
      'pernapasan' => $pernapasan,
      'nadi' => $nadi,
      'suhu' => $suhu,
      'score' => $score,
      'score_lainnya' => $scoreLainnya,
      'nyeri' => $nyeri,
      'skala_nyeri' => $skala_nyeri,
      'farmakologi' => $farmakologi,
      'non_farmakologi' => $non_farmakologi,
      'efek_samping' => $efek_samping,
      'efek_samping_lainnya' => $efek_samping_lain,
      'provocative' => $propocative,
      'quality' => $quality,
      'quality_lainnya' => $quality_lainnya,
      'regio' => $regio,
      'severity' => $severity,
      'time' => $time,
      'time_lain' => $time_lainnya,
      'skrining_risiko_a' => $skrining_risiko_a,
      'skrining_risiko_b' => $skrining_risiko_b,
      'skrining_risiko_c' => $skrining_risiko_c,
      'kondisi_kulit' => $kondisi_kulit,
      'kondisi_kulit_lain' => $kondisi_kulit_lain,
      'mobilisasi' => $mobilisasi,
      'cairan_terakhir_input' => $cairan_terakhir_input,
      'cairan_terakhir_output' => $cairan_terakhir_output,
      'pemeriksaan_dari_luar' => $pemeriksaan_dari_luar,
      'oleh' => $oleh,
    );

    $dataAlatBantu = array();
    $indexAlatBantu = 0;
    if (isset($post['alatbantu_MerujukPasien'])) {
      foreach ($post['alatbantu_MerujukPasien'] as $input) {
        if ($post['alatbantu_MerujukPasien'][$indexAlatBantu] != "") {
          array_push(
            $dataAlatBantu, array(
              'nokun' => $nokun,
              'id_variabel' => $post['alatbantu_MerujukPasien'][$indexAlatBantu],
              'keterangan' => isset($post['alatbantu_lainnya']) ? ($post['alatbantu_MerujukPasien'][$indexAlatBantu] == 58 ? $post['alatbantu_lainnya'] : "") : "",
            )
          );
        }
        $indexAlatBantu++;
      }
    }

    $this->db->trans_begin();
    if (!empty($nokun)) {
      $this->db->replace('medis.tb_merujuk_pasien', $dataMerujukPasien);
      $this->db->delete('medis.tb_alat_bantu', array('nokun' => $nokun));
      foreach ($dataAlatBantu as $key => $value) {
        $this->db->replace('medis.tb_alat_bantu', $value, 'nokun');
      }
    }else {
      $this->db->insert('medis.tb_merujuk_pasien', $dataMerujukPasien);
      if (isset($post['alatbantu_MerujukPasien'])) {
        $this->db->insert_batch('medis.tb_alat_bantu', $dataAlatBantu);
      }

    }

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }
}


/* End of file PendaftaranOperasi.php */
/* Location: ./application/controllers/operasi/PendaftaranOperasi.php */
