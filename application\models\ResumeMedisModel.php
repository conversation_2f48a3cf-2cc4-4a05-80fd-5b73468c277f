<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ResumeMedisModel extends MY_Model{

    public function get_pasien()
	{
        $norm = $this->uri->segment(3);
        $query = $this->db->query("SELECT *
        FROM (
        SELECT 1 JENIZ, 'RESUME IGD' JENIZ_DESC, eresume_igd.ID_ERESUME_IGD ID_RESUME, `eresume_igd`.`STATUS` AS STATUS_IGD, `pp`.`NOMOR`, `pp`.`NORM`, `pj`.`NOMOR` `SEP`, `pj`.`<PERSON><PERSON><PERSON>`, `master`.getNamaLengkap(pp.NORM) NAMA_PASIEN, `mrf`.`DESKRIPSI`, `pp`.`TANGGAL`, `pp`.`STATUS`, `mr`.`ID`
        , IF(eresume_igd.diagnosis_utama_des='', eresume_igd.catatan_utama, eresume_igd.diagnosis_utama_des) diagnosa_utama
        , SUBSTR(CONCAT('• ',
        REPLACE(eresume_igd.tindakan_procedure_des,'\n','\n• ')), 1, LENGTH(CONCAT('• ',
        REPLACE(eresume_igd.tindakan_procedure_des,'\n','\n• '))) -2) tata_laksana
        , CONCAT('Tgl Masuk: ', DATE_FORMAT(eresume_igd.tgl_masuk,'%d-%m-%Y')
                , ' | ',' Tgl Keluar: ', DATE_FORMAT(IF(eresume_igd.tgl_keluar IS NULL, eresume_igd.TGL_INPUT, eresume_igd.TGL_KELUAR),'%d-%m-%Y')) tanggal_masuk_keluar
        , master.getNamaLengkapPegawai(dok.NIP) NAMADOKTER
        , eresume_igd.CATATAN_PENTING_LAINNYA
        , eresume_igd.ruang_rawat_terakhir
        FROM `pendaftaran`.`pendaftaran` `pp`
        LEFT JOIN `pendaftaran`.`tujuan_pasien` `pt` ON `pp`.`NOMOR` = `pt`.`NOPEN`
        LEFT JOIN `master`.`ruangan` `mr` ON `pt`.`RUANGAN` = `mr`.`ID`
        LEFT JOIN `master`.`referensi` `mrf` ON `mr`.`JENIS_KUNJUNGAN` = `mrf`.`ID` AND `mrf`.`JENIS` = 15
        LEFT JOIN `pendaftaran`.`penjamin` `pj` ON `pp`.`NOMOR` = `pj`.`NOPEN`
        LEFT JOIN eresume_igd.eresume_igd ON eresume_igd.NOPEN = pp.NOMOR
        LEFT JOIN master.dokter dok ON dok.ID = eresume_igd.ID_DPJP
        WHERE `pt`.`STATUS` = '2' AND `pp`.`NORM` = '$norm'
        AND mr.JENIS_KUNJUNGAN=2
         #ORDER BY `pp`.`NOMOR` DES
        UNION
        SELECT 2 JENIZ, 'RESUME RI' JENIZ_DESC, eresume_igd.id ID_ERESUME , `eresume_igd`.`STATUS` AS STATUS_IGD, `pp`.`NOMOR`, `pp`.`NORM`, `pj`.`NOMOR` `SEP`, `pj`.`JENIS`, `master`.getNamaLengkap(pp.NORM) NAMA_PASIEN, mrf.`DESKRIPSI`, kuns.MASUK, kuns.`STATUS`, rukun.`ID`
        , IF(eresume_igd.diagnosis_utama_des='', eresume_igd.catatan_utama, eresume_igd.diagnosis_utama_des) diagnosa_utama
        , SUBSTR(CONCAT('• ',
        REPLACE(eresume_igd.tindakan_procedure_des,'\n','\n• ')), 1, LENGTH(CONCAT('• ',
        REPLACE(eresume_igd.tindakan_procedure_des,'\n','\n• '))) -2) tata_laksana
        , CONCAT('Tgl Masuk: ', DATE_FORMAT(eresume_igd.tgl_masuk,'%d-%m-%Y')
                , ' | ',' Tgl Keluar: ', DATE_FORMAT(IF(eresume_igd.tgl_keluar IS NULL, eresume_igd.TGL_INPUT, eresume_igd.TGL_KELUAR),'%d-%m-%Y')) tanggal_masuk_keluar
        , master.getNamaLengkapPegawai(dok.NIP) NAMADOKTER
        , eresume_igd.CATATAN_PENTING_LAINNYA
        , eresume_igd.ruang_rawat_terakhir
        FROM `pendaftaran`.`pendaftaran` `pp`
        LEFT JOIN `pendaftaran`.`tujuan_pasien` `pt` ON `pp`.`NOMOR` = `pt`.`NOPEN`
        LEFT JOIN pendaftaran.kunjungan kuns ON kuns.NOPEN = pp.NOMOR
        LEFT JOIN master.ruangan rukun ON rukun.ID = kuns.RUANGAN
        LEFT JOIN `master`.`ruangan` `mr` ON `pt`.`RUANGAN` = `mr`.`ID`
        LEFT JOIN `master`.`referensi` `mrf` ON rukun.`JENIS_KUNJUNGAN` = `mrf`.`ID` AND `mrf`.`JENIS` = 15
        LEFT JOIN `pendaftaran`.`penjamin` `pj` ON `pp`.`NOMOR` = `pj`.`NOPEN`
        LEFT JOIN resume_medis.resume_medis eresume_igd ON eresume_igd.nopen = pp.NOMOR
        LEFT JOIN master.dokter dok ON dok.ID = eresume_igd.id_dpjp
        WHERE `pt`.`STATUS` = '2' AND `pp`.`NORM` = '$norm' #AND mr.JENIS_KUNJUNGAN!=2 
        AND rukun.JENIS_KUNJUNGAN=3) a
        GROUP BY a.NOMOR
        ORDER BY NOMOR DESC
        ");
		$res = array(
			'his' => $query->result(),
			'nama' => $query->row_array(),
			'num' => $query->num_rows()
		);
		return $res;
    }
    
    public function check_eresume_igd()
    {
        $mr = $this->uri->segment(3);
        $query = $this->db->query("SELECT eresume_igd.NOPEN, eresume_igd.STATUS FROM eresume_igd.eresume_igd WHERE eresume_igd.NORM='$mr'");
        return $query->result_array();
    }

    public function cek_pp($nopen)
	{
		$query = $this->db->query("SELECT * FROM  resume_medis.resume_medis rr WHERE rr.nopen = $nopen");
        $res = array('row' => $query->row(),'num' => $query->num_rows(),'res' => $query->row_array(),);
        
		return $res;
	}

	public function cek_pm($nopen)
	{
		$query = $this->db->query("SELECT rp.nopen FROM resume_medis.pasien_meninggal rp WHERE rp.nopen = $nopen");

		return $query->num_rows();
	}


}
