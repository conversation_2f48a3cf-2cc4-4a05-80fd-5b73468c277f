<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ct_simulator extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','radioterapi/Ct_simulatorModel','radioterapi/RadioterapiModel'));
    $this->load->library('upload');
  }

  
  public function index($nomr='',$nopen='',$nokun='')
  {

    // echo 'aaa';exit;
      // $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(5));
       //Radioterapi CT-Simulator
      $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
      $listKunjungan = $this->RadioterapiModel->listKunjunganRadioterapi($nomr);
      $data = array(
        
        'nomr' => $nomr,
        'getNomr' => $getNomr,
        'cekisictsimulator' => $this->Ct_simulatorModel->cekisiCtSimulator($nomr,$nokun),
        'Position_of_Patient' => $this->masterModel->referensi(678),
        'Mask' => $this->masterModel->referensi(1856),
        'Contrast' => $this->masterModel->referensi(680),
        'Slicethickness' => $this->Ct_simulatorModel->referensi(681),
        'Base_Plate' => $this->masterModel->referensi(682),
        'Fixation' => $this->masterModel->referensi(683),
        'Neck_Extention' => $this->masterModel->referensi(684),
        'Mouth_Bite' => $this->masterModel->referensi(685),
        'Scar_Marking' => $this->masterModel->referensi(686),
        'Arm_Pos' => $this->masterModel->referensi(687),
        'Breastboard' => $this->masterModel->referensi(688),
        'Knee_Rest' => $this->masterModel->referensi(689),
        'Bolus' => $this->masterModel->referensi(690),
        'Vac_Lock' => $this->masterModel->referensi(691),
        'Matras' => $this->masterModel->referensi(692),
        'In_Case_Of_Pelvic_Treatment_bladeer' => $this->masterModel->referensi(693),
        'referenceOfLaser' => $this->masterModel->referensi(700),
        'linacMachine' => $this->masterModel->referensi(696),
        'imagefusion' => $this->masterModel->referensi(1852),
        'face' => $this->masterModel->referensi(1853),
        'supportingpatientimage' => $this->masterModel->referensi(1854),
        'bladder' => $this->masterModel->referensi(1855),
        'radiografer' => $this->masterModel->radiografer(),
        'fismed' => $this->masterModel->fismed(),
        'initialLat' => $this->masterModel->referensi(701),
        'initialVrt' => $this->masterModel->referensi(702),
        'initialLong' => $this->masterModel->referensi(703),
        'coordinatesX' => $this->masterModel->referensi(704),
        'coordinatesY' => $this->masterModel->referensi(705),
        'coordinatesZ' => $this->masterModel->referensi(706),
        'SignX' => $this->masterModel->referensi(707),
        'SignY' => $this->masterModel->referensi(708),
        'SignZ' => $this->masterModel->referensi(709),
        'CouchMovementX' => $this->masterModel->referensi(710),
        'CouchMovementY' => $this->masterModel->referensi(711),
        'CouchMovementZ' => $this->masterModel->referensi(712),
        'Treatment_Field' => $this->masterModel->referensi(713),
        'Direction_Block' => $this->masterModel->referensi(714),
        'Set_Up' => $this->masterModel->referensi(716),
        'FootRate' => $this->masterModel->referensi(999),
        'approTerapi' => $this->pengkajianAwalModel->approTerapi($nokun),
        'cekDokter' => $this->Ct_simulatorModel->getIsianCtDokter($nokun)->num_rows(),
        'cekRadiografer' => $this->Ct_simulatorModel->getIsianCtRadiografer($nokun)->num_rows(),
        'cekFisikaMedis' => $this->Ct_simulatorModel->getIsianCtFisikaMedis($nokun)->num_rows(),
        'cekSetupNote' => $this->Ct_simulatorModel->getIsianCtSetupNote($nokun)->num_rows(),
        'cekIsoCenter' => $this->Ct_simulatorModel->getIsianIsoCenter($nokun)->num_rows(),
        'isianDokter' => $this->Ct_simulatorModel->getIsianCtDokter($nokun)->row_array(),
        'isianRadiografer' => $this->Ct_simulatorModel->getIsianCtRadiografer($nokun)->row_array(),
        'isianFisikaMedis' => $this->Ct_simulatorModel->getIsianCtFisikaMedis($nokun)->row_array(),
        'isianSetupNote' => $this->Ct_simulatorModel->getIsianCtSetupNote($nokun)->result_array(),
        'isianIsoCenter' => $this->Ct_simulatorModel->getIsianIsoCenter($nokun)->result_array(),
        'listKunjungan' => $listKunjungan
      );

      // $data = array(
      //     'getNomr' => $pasien,
      //     'farmasi_eresep' => $this->masterModel->farmasi(),
      //     'obat' => $this->EresepModel->getobat($pasien['ID_TUJUAN_FARMASI'], $pasien['IDPENJAMIN']),
      //     'getTbBbAlergi' => $this->pengkajianAwalModel->getTbBbAlergi($pasien['NORM']),
      //     'jalur_pemberiaan' => $this->masterModel->referensi(901),
      //     'adapenunjang' => $this->EresepModel->checkpenunjang($pasien['NOKUN']),
      // );
      $this->load->view('Pengkajian/radioTerapi/ct_simulator.php', $data);
      // $this->load->view('Pengkajian/eresep/form_eresep', $data);
  }

  public function simpanCtSimulatorDokter()
  {
    $data= array(
      'nokun'       => $this->input->post('nokun'),
      'nomr'       => $this->input->post('nomr'),
      'ctScanRange' => $this->input->post('ctScanRange'),
      'ctScanTo'    => $this->input->post('ctScanTo'),
      // 'bladeer'     => $this->input->post('bladeer'),
      'verifdokter' => 0,
      'deskripsi'   => $this->input->post('ctSimulatorDokter'),
      'oleh'        => $this->session->userdata('id'),
    );

    $this->Ct_simulatorModel->simpanCt_simulatorDr($data);
  }

  public function simpanCtSimulatorRadiografer()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    // print_r($post);
    // exit();

    // if(empty($post["mask"])) {
    //   $mask = 0;
    // } else {
    //   $mask = $post["mask"];
    // }
    // if(empty($post["contrast"])) {
    //   $contrast = 0;
    // } else {
    //   $contrast = $post["contrast"];
    // }
    // if(empty($post["slicethickness"])) {
    //   $slicethickness = 0;
    // } else {
    //   $slicethickness = $post["slicethickness"];
    // }
    // if(empty($post["basePlate"])) {
    //   $basePlate = 0;
    // } else {
    //   $basePlate = $post["basePlate"];
    // }
    // if(empty($post["fixation"])) {
    //   $fixation = 0;
    // } else {
    //   $fixation = $post["fixation"];
    // }
    // if(empty($post["neckExtention"])) {
    //   $neckExtention = 0;
    // } else {
    //   $neckExtention = $post["neckExtention"];
    // }
    // if(empty($post["mouthBite"])) {
    //   $mouthBite = 0;
    // } else {
    //   $mouthBite = $post["mouthBite"];
    // }
    // if(empty($post["scarMarking"])) {
    //   $scarMarking = 0;
    // } else {
    //   $scarMarking = $post["scarMarking"];
    // }
    // if(empty($post["armPos"])) {
    //   $armPos = 0;
    // } else {
    //   $armPos = $post["armPos"];
    // }
    // if(empty($post["breasboard"])) {
    //   $breasboard = 0;
    // } else {
    //   $breasboard = $post["breasboard"];
    // }
    // if(empty($post["kneeRest"])) {
    //   $kneeRest = 0;
    // } else {
    //   $kneeRest = $post["kneeRest"];
    // }
    // if(empty($post["bolus"])) {
    //   $bolus = 0;
    // } else {
    //   $bolus = $post["bolus"];
    // }
    // if(empty($post["vacLock"])) {
    //   $vacLock = 0;
    // } else {
    //   $vacLock = $post["vacLock"];
    // }
    // if(empty($post["matras"])) {
    //   $matras = 0;
    // } else {
    //   $matras = $post["matras"];
    // }
    // if(empty($post["footRate"])) {
    //   $footRate = 0;
    // } else {
    //   $footRate = $post["linacMachine"];
    // }
    // if(empty($post["linacMachine"])) {
    //   $linacMachine = 0;
    // } else {
    //   $linacMachine = $post["linacMachine"];
    // }

    $data= array(
      'nokun'            => $post["nokun"]?? null,
      'nomr'             => $post["nomr"]?? null,
      'idCtSimDr'        => $post["idCtSimDr"]?? null,
      'positionPatient'  => implode(',', $post["positionPatient"])?? null,
      'mask'             => $post["mask"]?? null,
      'contrast'         => $post["contrast"]?? null,
      'slicethickness'   => $post["slicethickness"]?? null,
      'basePlate'        => $post["basePlate"]?? null,
      'headrestNo'       => $post["headrestNo"]?? null,
      'fixation'         => $post["fixation"]?? null,
      'neckExtention'    => $post["neckExtention"]?? null,
      'mouthBite'        => $post["mouthBite"]?? null,
      'scarMarking'      => $post["scarMarking"]?? null,
      'armPos'           => $post["armPos"]?? null,
      'breasboard'       => $post["breasboard"]?? null,
      'indexPosition'    => $post["indexPosition"]?? null,
      'kneeRest'         => $post["kneeRest"]?? null,
      'bolus'            => $post["bolus"]?? null,
      'vacLock'          => $post["vacLock"]?? null,
      'matras'           => $post["matras"]?? null,
      'footRate'         => $post["footRate"]?? null,
      'referenceOfLaser' => isset($post["referenceOfLaser"]) ? implode(',', $post["referenceOfLaser"]) : 0,
      'rol_right_ket'    => $post["referenceOfLaserText"][0]?? null,
      'rol_left_ket'     => $post["referenceOfLaserText"][1]?? null,
      'linacMachine'     => $post["linacMachine"]?? null,
      'imagefusion'     => $post['imagefusion']?? null,
      'face'            => $post['face']?? null,
      'supportpatientimage'     => $post['supportingpatientimage']?? null,
      'ket' => $post['ket'],
      'bladder'         => $post['bladder']?? null,
      'oleh'             => $this->session->userdata("id"),
      'oleh2'         => $post['oleh2']?? null,
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $id = $this->Ct_simulatorModel->simpanCt_simulatorRadiografer($data);
    // $id = 3;
    if($id>0){
          // Cek apakah file di-upload
          $setupNote= $post["setupNote"]?? array();
          $setupNoteimage= $_FILES["setupNoteimage"]?? array();
          // var_dump(' $setupNote');
          // var_dump(count($setupNote)>0);
          if(count($setupNote)>0){
            $uploadPath = '../upload_emr/radioterapi/';
              $namagambar=null;
              for ($i = 0; $i < count($setupNote); $i++) {

                // var_dump($setupNoteimage);exit;
                // var_dump($setupNoteimage['name'][$i]);
                if(isset($setupNoteimage['name'][$i])){
                $fileNameParts = pathinfo($setupNoteimage['name'][$i]);
                $fileExtension = strtolower($fileNameParts['extension']);
                $namagambar="image_".$id."_".$i."_".date("Ymd").".".$fileExtension;


                $_FILES['image']['name'] = $namagambar;
                $_FILES['image']['type'] = $setupNoteimage['type'][$i];
                $_FILES['image']['tmp_name'] = $setupNoteimage['tmp_name'][$i];
                $_FILES['image']['error'] = $setupNoteimage['error'][$i];
                $_FILES['image']['size'] = $setupNoteimage['size'][$i];
    
                if (!is_dir($uploadPath)) {
                  mkdir($uploadPath, 0755, true); // Membuat direktori secara rekursif
                }
                $config['upload_path'] = $uploadPath;
                $config['allowed_types'] = 'jpg|jpeg|png';
                $config['max_size'] = 2048 * 6; // Maksimal ukuran file dalam KB
                $config['overwrite'] = TRUE; 
    
                $this->upload->initialize($config);
    
                if ($this->upload->do_upload('image')) {
                    $data = $this->upload->data();
                    // echo "File berhasil di-upload: " . $data['file_name'] . "<br>";
                } else {
                    // echo "Error saat meng-upload file: " . $this->upload->display_errors() . "<br>";
                }

              }
              
                $dataimage= array(
                  'idradiografi'            => $id,
                  'note'       => $setupNote[$i],
                  'gambar'       => $namagambar
                );
                // var_dump($dataimage);
              $iditem = $this->Ct_simulatorModel->simpanCt_simulatorRadiograferImage($dataimage);
                
            
                
              }
          }
    
     
    }

    // $dataTreatmentTable = array(
    //   'id_ctSimulatorRadiografer' => $id,
    //   'initialLat'                => $post['initialLat'],
    //   'valueTreatmentLat'         => $post['valueTreatmentLat'],
    //   'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLat'],
    //   'initialVrt'                => $post['initialVrt'],
    //   'valueTreatmentVrt'         => $post['valueTreatmentVrt'],
    //   'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrt'],
    //   'initialLong'               => $post['initialLong'],
    //   'valueTreatmentLong'        => $post['valueTreatmentLong'],
    //   'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLong'],
    // );

    // $this->Ct_simulatorModel->simpanTreatment_radiografer($dataTreatmentTable);

    // if (isset($post['initialLat2']) && isset($post['initialVrt2']) && isset($post['initialLong2'])) {
    //   $dataTreatmentTable2 = array(
    //     'id_ctSimulatorRadiografer' => $id,
    //     'initialLat'                => $post['initialLat2'],
    //     'valueTreatmentLat'         => $post['valueTreatmentLat2'],
    //     'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLat2'],
    //     'initialVrt'                => $post['initialVrt2'],
    //     'valueTreatmentVrt'         => $post['valueTreatmentVrt2'],
    //     'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrt2'],
    //     'initialLong'               => $post['initialLong2'],
    //     'valueTreatmentLong'        => $post['valueTreatmentLong2'],
    //     'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLong2'],
    //   );
    //   $this->Ct_simulatorModel->simpanTreatment_radiografer($dataTreatmentTable2);
    // }

    // if (isset($post['initialLat2a']) && isset($post['initialVrt2a']) && isset($post['initialLong2a'])) {
    //   $dataTreatmentTable2a = array(
    //     'id_ctSimulatorRadiografer' => $id,
    //     'initialLat'                => $post['initialLat2a'],
    //     'valueTreatmentLat'         => $post['valueTreatmentLat2a'],
    //     'finalCouchTreatmentLat'    => $post['finalCouchTreatmentLat2a'],
    //     'initialVrt'                => $post['initialVrt2a'],
    //     'valueTreatmentVrt'         => $post['valueTreatmentVrt2a'],
    //     'finalCouchTreatmentVrt'    => $post['finalCouchTreatmentVrt2a'],
    //     'initialLong'               => $post['initialLong2a'],
    //     'valueTreatmentLong'        => $post['valueTreatmentLong2a'],
    //     'finalCouchTreatmentLong'   => $post['finalCouchTreatmentLong2a'],
    //   );
    //   $this->Ct_simulatorModel->simpanTreatment_radiografer($dataTreatmentTable2a);
    // }


    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function simpanCtSimulatorFisikaMedis()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    // print_r($post);
    // exit();
    
    $data= array(
      'nokun'                  => $post["nokun"],
      'idCtSimDr'              => $post["idCtSimDr"]?? null,
      'nomr'                   => $post["nomr"]?? null,
      'tglVerificationMachine' => $post["tglVerificationMachine"],
      'coordinatesX'           => $post["coordinatesX1"],
      'coordinatesY'           => $post["coordinatesY1"],
      'coordinatesZ'           => $post["coordinatesZ1"],
      'SignX'                  => isset($post["SignX"]) ? $post["SignX"] : 0,
      'SignY'                  => isset($post["SignY"]) ? $post["SignY"] : 0,
      'SignZ'                  => isset($post["SignZ"]) ? $post["SignZ"] : 0,
      'CouchMovementX'         => isset($post["CouchMovementX"]) ? $post["CouchMovementX"] : 0,
      'CouchMovementY'         => isset($post["CouchMovementY"]) ? $post["CouchMovementY"] : 0,
      'CouchMovementZ'         => isset($post["CouchMovementZ"]) ? $post["CouchMovementZ"] : 0,
      'oleh'                   => $this->session->userdata("id"),
      'oleh2'                   => $post['oleh2']?? null,
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $id = $this->Ct_simulatorModel->simpanCt_simulatorFisikaMedis($data);

    $dataIsocenter= array(
      'idCtSimulatorFisikaMedis' => $id,
      'treatmentField'           => $post["treatmentField"],
      'valueX'                   => $post["valueX"],
      'coordinatesX'             => $post["coordinatesX"],
      'valueY'                   => $post["valueY"],
      'coordinatesY'             => $post["coordinatesY"],
      'valueZ'                   => $post["valueZ"],
      'coordinatesZ'             => $post["coordinatesZ"],
      'tpsAp'                    => $post["tpsAp"],
      'tpsRl'                    => $post["tpsRl"],
      'tpsLl'                    => $post["tpsLl"],
      'actualAp'                 => $post["actualAp"],
      'actualRl'                 => $post["actualRl"],
      'actualLl'                 => $post["actualLl"],
    );

    $this->Ct_simulatorModel->simpanIsocenter_FisikaMedis($dataIsocenter);

    if (isset($post['treatmentField2'])) {
      $dataIsocenter2= array(
        'idCtSimulatorFisikaMedis' => $id,
        'treatmentField'           => $post["treatmentField2"],
        'valueX'                   => $post["valueX2"],
        'coordinatesX'             => $post["coordinatesX2"],
        'valueY'                   => $post["valueY2"],
        'coordinatesY'             => $post["coordinatesY2"],
        'valueZ'                   => $post["valueZ2"],
        'coordinatesZ'             => $post["coordinatesZ2"],
        'tpsAp'                    => $post["tpsAp2"],
        'tpsRl'                    => $post["tpsRl2"],
        'tpsLl'                    => $post["tpsLl2"],
        'actualAp'                 => $post["actualAp2"],
        'actualRl'                 => $post["actualRl2"],
        'actualLl'                 => $post["actualLl2"],
      );
      $this->Ct_simulatorModel->simpanIsocenter_FisikaMedis($dataIsocenter2);
    }

    if (isset($post['treatmentField2a'])) {
      $dataIsocenter2a= array(
        'idCtSimulatorFisikaMedis' => $id,
        'treatmentField'           => $post["treatmentField2a"],
        'valueX'                   => $post["valueX2a"],
        'coordinatesX'             => $post["coordinatesX2a"],
        'valueY'                   => $post["valueY2a"],
        'coordinatesY'             => $post["coordinatesY2a"],
        'valueZ'                   => $post["valueZ2a"],
        'coordinatesZ'             => $post["coordinatesZ2a"],
        'tpsAp'                    => $post["tpsAp2a"],
        'tpsRl'                    => $post["tpsRl2a"],
        'tpsLl'                    => $post["tpsLl2a"],
        'actualAp'                 => $post["actualAp2a"],
        'actualRl'                 => $post["actualRl2a"],
        'actualLl'                 => $post["actualLl2a"],
      );
      $this->Ct_simulatorModel->simpanIsocenter_FisikaMedis($dataIsocenter2a);
    }

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function verifCtSimulator()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    
    $data= array(
      'verifdokter'=> 1,
      'olehverif' => $this->session->userdata('id'),
      'tglverif' => date('Y-m-d H:i:s')
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->db->where('id', $this->input->post('id'));
    $this->db->update('medis.tb_ctSimulatorDokter', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    // $id = $this->Ct_simulatorModel->verifCt_simulator($data,$post['id']);

    // return $id;
  }

  public function historyCtSimulator()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $nokun = $this->input->post('nokun');
    $listCtSimulator = $this->Ct_simulatorModel->listHistoryCtSimulatorNew($nomr);

    $cekDokter = $this->Ct_simulatorModel->getIsianCtDokter($nokun)->num_rows();
    $cekRadiografer = $this->Ct_simulatorModel->getIsianCtRadiografer($nokun)->num_rows();
    $cekFisikaMedis = $this->Ct_simulatorModel->getIsianCtFisikaMedis($nokun)->num_rows();  

    $data = array();
    $no = 1;

    // if($this->session->userdata('profesi') == 11 && $historyCT['verifdokter']==0 && ($cekDokter > 0 && $cekRadiografer > 0 && $cekFisikaMedis > 0)){
    //   $btnVerif = '<a data-id='.$historyCT['dokter_id'].'" class="btn btn-success btn-sm  btn-block btnverifdokter" target="_blank"><i class="fa fa-check"></i> Verif</a>';
    // }elseif($historyCT['verifdokter']==1){
    //   $btnVerif = '<span class="text-success">Sudah diverif </span>';
    // }else{
    //   $btnVerif = '-';
    // }
    // print_r($listCtSimulator->result_array());
    foreach ($listCtSimulator->result_array() as $historyCT) {
        $profesi = $this->session->userdata('profesi');
    
        // Dokter
        $disableDr = (empty($historyCT['dokter_id']) && $profesi != 11);
        $styleDr = $disableDr ? 'pointer-events: none; opacity: 0.5;' : '';
        $textDr = $disableDr ? 'Belum Ada Form' : 'Form Dokter';
        $btnDr = '<a href="#modalCtDr" class="btn btn-purple btn-block" 
                        style="' . $styleDr . '" 
                        data-id="' . (!empty($historyCT['dokter_id']) ? $historyCT['dokter_id'] : '') . '" 
                        data-toggle="modal" 
                        data-backdrop="static" 
                        data-keyboard="false">
                            <i class="fa fa-file"></i> ' . $textDr . '
                        </a>';
    
        // Radiografer
        $disableRad = (empty($historyCT['radiografer_id']) && $profesi != 8);
        $styleRad = $disableRad ? 'pointer-events: none; opacity: 0.5;' : '';
        $textRad = $disableRad ? 'Belum Ada Form' : 'Form Radiografer';
        $btnRad = '<a href="#modalCtRad" class="btn btn-success btn-block" 
                        style="' . $styleRad . '" 
                        data-id="' . (!empty($historyCT['radiografer_id']) ? $historyCT['radiografer_id'] : '') . '" 
                        data-idctdr="' . (!empty($historyCT['dokter_id']) ? $historyCT['dokter_id'] : '') . '"
                        data-toggle="modal" 
                        data-backdrop="static" 
                        data-keyboard="false">
                            <i class="fa fa-file"></i> ' . $textRad . '
                        </a>';
    
        // Fisika Medis
        $disableFis = (empty($historyCT['fisikamedis_id']) && $profesi != 17);
        $styleFis = $disableFis ? 'pointer-events: none; opacity: 0.5;' : '';
        $textFis = $disableFis ? 'Belum Ada Form' : 'Form Fisika Medis';
        $btnFis = '<a href="#modalCtFis" class="btn btn-info btn-block" 
                        style="' . $styleFis . '" 
                        data-id="' . (!empty($historyCT['fisikamedis_id']) ? $historyCT['fisikamedis_id'] : '') . '"
                        data-idctdr="' . (!empty($historyCT['dokter_id']) ? $historyCT['dokter_id'] : '') . '" 
                        data-toggle="modal" 
                        data-backdrop="static" 
                        data-keyboard="false">
                            <i class="fa fa-file"></i> ' . $textFis . '
                        </a>';
          // Status Verifikasi
          if ($historyCT['verifdokter'] == 0) {
            if ($profesi == 11 && !empty($historyCT['radiografer_id']) && !empty($historyCT['fisikamedis_id'])) {
                // Tambahkan pengecekan apakah historyCT['oleh_id'] sama dengan session userdata('id')
                if ($historyCT['oleh_id'] == $this->session->userdata('id')) {
                    $statusVerif = '<a class="btn btn-success btn-block btnverifdokter" data-id="' . $historyCT['dokter_id'] . '"><i class="fa fa-check"></i> Verif</a>';
                } else {
                    $statusVerif = 'Belum Verifikasi Dokter';
                }
            } elseif ($profesi != 11 && !empty($historyCT['radiografer_id']) && !empty($historyCT['fisikamedis_id'])) {
                $statusVerif = 'Belum Verifikasi Dokter';
            } elseif (empty($historyCT['radiografer_id']) || empty($historyCT['fisikamedis_id'])) {
                $statusVerif = 'Form Belum Lengkap';
            }
          } elseif ($historyCT['verifdokter'] == 1) {
            $statusVerif = '<span class="text-success">Sudah diverif <b>' . $historyCT['dokter_verif'] . '</b></span><br>' . $historyCT['dokter_tglverif'];
          } else {
            $statusVerif = 'Belum Verifikasi Dokter';
          }

    
        $data[] = array(
            $no,
            $historyCT['nokun'],
            $btnDr . ' ' . $historyCT['dokter_tanggal'],
            $btnRad . ' ' . $historyCT['radiografer_tanggal'],
            $btnFis . ' ' . $historyCT['fisikamedis_tanggal'],
            '<a href="/reports/simrskd/ctsimulator/ctsimulator.php?format=pdf&nokun=' . $historyCT['nokun'] . '" class="btn btn-warning btn-sm btn-block" target="_blank"><i class="fa fa-print"></i> View</a>',
            $statusVerif
        );
        $no++;
    }
  

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listCtSimulator->num_rows(),
      "recordsFiltered" => $listCtSimulator->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalCtDr()
  {
    $id = $this->input->post('id');
    $gCtDr = $this->Ct_simulatorModel->getCtSimulatorDr($id);

    $data = array(
      'id'    => $id,
      'gCtDr' => $gCtDr,
      'In_Case_Of_Pelvic_Treatment_bladeer' => $this->masterModel->referensi(693),
    );

    $this->load->view('Pengkajian/radioTerapi/editCtSimulatorDr', $data);
  }

  public function modalCtRad()
  {
    $id = $this->input->post('id') ?? null;
    $nokun = $this->input->post('nokun') ?? null;
    $idctdr = $this->input->post('idctdr') ?? null;
    // Jika ID ada, ambil data terkait
    if (!empty($id)) {
        $gCtRad = $this->Ct_simulatorModel->getCtSimulatorRad($id);
        $explode_referenceOfLaser = explode(',', $gCtRad['referenceOfLaser']);
        $gCtTreat = $this->Ct_simulatorModel->getTreatmentRadiografer($id);
        $totalTreatment = count($gCtTreat);

        $gCtid = array_column($gCtTreat, 'id');
        $gCtinitialLat = array_column($gCtTreat, 'initialLat');
        $gCtvalueTreatmentLat = array_column($gCtTreat, 'valueTreatmentLat');
        $gCtfinalCouchTreatmentLat = array_column($gCtTreat, 'finalCouchTreatmentLat');
        $gCtinitialVrt = array_column($gCtTreat, 'initialVrt');
        $gCtvalueTreatmentVrt = array_column($gCtTreat, 'valueTreatmentVrt');
        $gCtfinalCouchTreatmentVrt = array_column($gCtTreat, 'finalCouchTreatmentVrt');
        $gCtinitialLong = array_column($gCtTreat, 'initialLong');
        $gCtvalueTreatmentLong = array_column($gCtTreat, 'valueTreatmentLong');
        $gCtfinalCouchTreatmentLong = array_column($gCtTreat, 'finalCouchTreatmentLong');
    } else {
        // Jika ID kosong, data spesifik yang bergantung pada ID tidak boleh dipanggil
        $gCtRad = [];
        $explode_referenceOfLaser = [];
        $totalTreatment = 0;
        $gCtid = $gCtinitialLat = $gCtvalueTreatmentLat = $gCtfinalCouchTreatmentLat = [];
        $gCtinitialVrt = $gCtvalueTreatmentVrt = $gCtfinalCouchTreatmentVrt = [];
        $gCtinitialLong = $gCtvalueTreatmentLong = $gCtfinalCouchTreatmentLong = [];
    }

    $data = array(
        'id'                                  => $id,
        'gCtRad'                              => $gCtRad,
        'explode_referenceOfLaser'            => $explode_referenceOfLaser,
        'Position_of_Patient'                 => $this->masterModel->referensi(678),
        'totalTreatment'                      => $totalTreatment,
        'gCtid'                               => $gCtid,
        'gCtinitialLat'                       => $gCtinitialLat,
        'gCtvalueTreatmentLat'                => $gCtvalueTreatmentLat,
        'gCtfinalCouchTreatmentLat'           => $gCtfinalCouchTreatmentLat,
        'gCtinitialVrt'                       => $gCtinitialVrt,
        'gCtvalueTreatmentVrt'                => $gCtvalueTreatmentVrt,
        'gCtfinalCouchTreatmentVrt'           => $gCtfinalCouchTreatmentVrt,
        'gCtinitialLong'                      => $gCtinitialLong,
        'gCtvalueTreatmentLong'               => $gCtvalueTreatmentLong,
        'gCtfinalCouchTreatmentLong'          => $gCtfinalCouchTreatmentLong,
        'positonPatient'                      => $this->masterModel->referensi(678),
        'Mask'                                => $this->masterModel->referensi(1856),
        'Contrast'                            => $this->masterModel->referensi(680),
        'Slicethickness'                      => $this->Ct_simulatorModel->referensi(681),
        'Base_Plate'                          => $this->masterModel->referensi(682),
        'Fixation'                            => $this->masterModel->referensi(683),
        'Neck_Extention'                      => $this->masterModel->referensi(684),
        'Mouth_Bite'                          => $this->masterModel->referensi(685),
        'Scar_Marking'                        => $this->masterModel->referensi(686),
        'Arm_Pos'                             => $this->masterModel->referensi(687),
        'Breastboard'                         => $this->masterModel->referensi(688),
        'Knee_Rest'                           => $this->masterModel->referensi(689),
        'Bolus'                               => $this->masterModel->referensi(690),
        'Vac_Lock'                            => $this->masterModel->referensi(691),
        'Matras'                              => $this->masterModel->referensi(692),
        'referenceOfLaser'                    => $this->masterModel->referensi(700),
        'initialLat'                          => $this->masterModel->referensi(701),
        'initialVrt'                          => $this->masterModel->referensi(702),
        'initialLong'                         => $this->masterModel->referensi(703),
        'initialLat3'                         => $this->masterModel->referensi(701),
        'initialVrt3'                         => $this->masterModel->referensi(702),
        'initialLong3'                        => $this->masterModel->referensi(703),
        'initialLat3a'                        => $this->masterModel->referensi(701),
        'initialVrt3a'                        => $this->masterModel->referensi(702),
        'initialLong3a'                       => $this->masterModel->referensi(703),
        'FootRate'                            => $this->masterModel->referensi(999),
        'linacMachine'                        => $this->masterModel->referensi(696),
        'imagefusion'                         => $this->masterModel->referensi(1852),
        'face'                                => $this->masterModel->referensi(1853),
        'supportingpatientimage'              => $this->masterModel->referensi(1854),
        'bladder'                             => $this->masterModel->referensi(1855),
        'radiografer'                         => $this->masterModel->radiografer(),
        'getNomr'                             => $this->pengkajianAwalModel->getNomr($nokun),
        'idCtSimDr'                           => $idctdr
    );

    // Pilih view berdasarkan ID
    $view = !empty($id) ? 'editCtSimulatorRad' : 'addCtSimulatorRad';

    $this->load->view('Pengkajian/radioTerapi/' . $view, $data);
  }



  public function modalCtFis()
  {
      $id = $this->input->post('id') ?? null;
      $nokun = $this->input->post('nokun') ?? null;
      $idctdr = $this->input->post('idctdr') ?? null;

      // Data yang tidak bergantung pada $id
      $commonData = [
          'coordinatesX'         => $this->masterModel->referensi(704),
          'coordinatesY'         => $this->masterModel->referensi(705),
          'coordinatesZ'         => $this->masterModel->referensi(706),
          'coordinatesX3'        => $this->masterModel->referensi(704),
          'coordinatesY3'        => $this->masterModel->referensi(705),
          'coordinatesZ3'        => $this->masterModel->referensi(706),
          'coordinatesX3a'       => $this->masterModel->referensi(704),
          'coordinatesY3a'       => $this->masterModel->referensi(705),
          'coordinatesZ3a'       => $this->masterModel->referensi(706),
          'SignX'                => $this->masterModel->referensi(707),
          'SignY'                => $this->masterModel->referensi(708),
          'SignZ'                => $this->masterModel->referensi(709),
          'CouchMovementX'       => $this->masterModel->referensi(710),
          'CouchMovementY'       => $this->masterModel->referensi(711),
          'CouchMovementZ'       => $this->masterModel->referensi(712),
          'fismed'               => $this->masterModel->fismed(),
          'getNomr'              => $this->pengkajianAwalModel->getNomr($nokun),
          'idCtSimDr'              => $idctdr,
      ];

      if (empty($id)) {
          // Jika $id tidak ada, load view addCtSimulatorFis dengan data umum
          $this->load->view('Pengkajian/radioTerapi/addCtSimulatorFis', $commonData);
          return; // Keluar dari fungsi setelah memuat view
      }

      // Jika $id ada, tambahkan data yang bergantung pada $id
      $gCtFis = $this->Ct_simulatorModel->getCtSimulatorFis($id);
      $gCtIso = $this->Ct_simulatorModel->getIsocenterFisikaMedis($id);
      $totalIsoCenter = count($gCtIso);

      $data = array_merge($commonData, [
          'id'                   => $id,
          'gCtFis'               => $gCtFis,
          'totalIsoCenter'       => $totalIsoCenter,
          'gCtIsoid'             => array_column($gCtIso, 'id'),
          'gCtIsotreatmentField' => array_column($gCtIso, 'treatmentField'),
          'gCtIsovalueX'         => array_column($gCtIso, 'valueX'),
          'gCtIsocoordinatesX'   => array_column($gCtIso, 'coordinatesX'),
          'gCtIsovalueY'         => array_column($gCtIso, 'valueY'),
          'gCtIsocoordinatesY'   => array_column($gCtIso, 'coordinatesY'),
          'gCtIsovalueZ'         => array_column($gCtIso, 'valueZ'),
          'gCtIsocoordinatesZ'   => array_column($gCtIso, 'coordinatesZ'),
          'gCtIsotpsAp'          => array_column($gCtIso, 'tpsAp'),
          'gCtIsotpsRl'          => array_column($gCtIso, 'tpsRl'),
          'gCtIsotpsLl'          => array_column($gCtIso, 'tpsLl'),
          'gCtIsoactualAp'       => array_column($gCtIso, 'actualAp'),
          'gCtIsoactualRl'       => array_column($gCtIso, 'actualRl'),
          'gCtIsoactualLl'       => array_column($gCtIso, 'actualLl'),
      ]);

      $this->load->view('Pengkajian/radioTerapi/editCtSimulatorFis', $data);
  }

  public function updateCtSimulatorDr()
  {

    $id = $this->input->post('id');

    $data= array(
      'nomr' => $this->input->post('nomr')?? null,
      'ctScanRange' => $this->input->post('ctScanRangeEdit'),
      'ctScanTo'    => $this->input->post('ctScanToEdit'),
      'bladeer'     => $this->input->post('bladeerEdit'),
      'deskripsi'   => $this->input->post('ctSimulatorDokterEdit'),
    );
    // var_dump($data);exit;
    $this->Ct_simulatorModel->updateCt_simulatorDr($data,$id);
  }

  public function updateCtSimulatorRadiografer()
  {
    $this->db->trans_begin();
    $id           = $this->input->post('id');
    $idTreatment  = $this->input->post('idTreatment');
    $idTreatment2 = $this->input->post('idTreatment2');
    $idTreatment2a = $this->input->post('idTreatment2a');
    $post = $this->input->post();

    $data= array(
      // 'nomr'             => $post["nomr"]?? null,
      'positionPatient'  => implode(',', $post["positionPatientEdit"])?? null,
      'mask'             => $post["maskEdit"],
      'contrast'         => $post["contrastEdit"],
      'slicethickness'   => $post["slicethicknessEdit"],
      'basePlate'        => $post["basePlateEdit"],
      'headrestNo'       => $post["headrestNoEdit"],
      'fixation'         => $post["fixationEdit"],
      'neckExtention'    => $post["neckExtentionEdit"],
      'mouthBite'        => $post["mouthBiteEdit"],
      'scarMarking'      => $post["scarMarkingEdit"],
      'armPos'           => $post["armPosEdit"],
      'breasboard'       => $post["breasboardEdit"],
      'indexPosition'    => $post["indexPositionEdit"],
      'kneeRest'         => $post["kneeRestEdit"],
      'bolus'            => $post["bolusEdit"],
      'vacLock'          => $post["vacLockEdit"],
      'matras'           => $post["matrasEdit"],
      'footRate'         => $post["footRateEdit"],
      'referenceOfLaser' => implode(',',$post["referenceOfLaserEdit"]),
      'linacMachine'     => $post["linacMachineEdit"],
      'rol_right_ket'    => $post["referenceOfLaserTextEdit"][0],
      'rol_left_ket'     => $post["referenceOfLaserTextEdit"][1],
      'imagefusion'     => $post['imagefusion']?? null,
      'face'            => $post['face']?? null,
      'supportpatientimage'     => $post['supportingpatientimageEdit']?? null,
      'ket'             => $post['supportingpatientimageEdit']=='6259'?$post['ketEdit']: null,
      'bladder'         => $post['bladder']?? null,
      'oleh2'          => $post['oleh2']?? null,
    );
    // echo "<pre>"; print_r($data); echo "</pre>"; exit();
    $this->Ct_simulatorModel->updateCt_simulatorRadiografer($data,$id);

    $idnote = $post['idnote']?? null;
    $setupNote= $post["setupNoteEdit"]?? array();
    $setupNotenew= $post["setupNotenew"]?? array();
    foreach ($idnote as $key => $idnoteitem) {
      $dataimage= array(
        'note'       => $setupNote[$key]
      );
      $iditem = $this->Ct_simulatorModel->update_ctsimulatorsetupnote($dataimage,$idnoteitem);
      
    }
    $this->Ct_simulatorModel->del_ctsimulatorsetupnote($idnote,$id);

    if(isset($post["setupNotenew"])){
      // Cek apakah file di-upload
      $setupNoteimage= $_FILES["setupNoteimage"]?? array();
      // var_dump(' $setupNote');
      // var_dump(count($setupNote)>0);
      if(count($setupNotenew)>0){
        $uploadPath = '../upload_emr/radioterapi/';
          $namagambar=null;
          for ($i = 0; $i < count($setupNotenew); $i++) {

            // var_dump($setupNoteimage);exit;
            // var_dump($setupNoteimage['name'][$i]);
            if(isset($setupNoteimage['name'][$i]) && $setupNoteimage['name'][$i]){
            $fileNameParts = pathinfo($setupNoteimage['name'][$i]);
            $fileExtension = strtolower($fileNameParts['extension']);
            $namagambar="image_".$id."_".$i."_".date("Ymd").".".$fileExtension;


            $_FILES['image']['name'] = $namagambar;
            $_FILES['image']['type'] = $setupNoteimage['type'][$i];
            $_FILES['image']['tmp_name'] = $setupNoteimage['tmp_name'][$i];
            $_FILES['image']['error'] = $setupNoteimage['error'][$i];
            $_FILES['image']['size'] = $setupNoteimage['size'][$i];

            if (!is_dir($uploadPath)) {
              mkdir($uploadPath, 0755, true); // Membuat direktori secara rekursif
            }
            $config['upload_path'] = $uploadPath;
            $config['allowed_types'] = 'jpg|jpeg|png';
            $config['max_size'] = 2048 * 6; // Maksimal ukuran file dalam KB
            $config['overwrite'] = TRUE; 

            $this->upload->initialize($config);

            if ($this->upload->do_upload('image')) {
                $data = $this->upload->data();
            } else {
            }

          }else{
            $namagambar=null;
          }
          
          $dataimage= array(
            'idradiografi'            => $id,
            'note'       => $setupNotenew[$i],
            'gambar'       => $namagambar
          );
            // var_dump($dataimage);
          $iditem = $this->Ct_simulatorModel->simpanCt_simulatorRadiograferImage($dataimage);
            
        }
      }
 
  }

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function updateCtSimulatorFisikaMedis()
  {
    $this->db->trans_begin();
    $id     = $this->input->post('id');
    $idIso  = $this->input->post('idIso');
    $idIso2 = $this->input->post('idIso2');
    $idIso2a = $this->input->post('idIso2a');
    $post   = $this->input->post();

    $data= array(
      'nomr' => $post["nomr"]?? null,
      'tglVerificationMachine' => $post["tglVerificationMachineEdit"],
      'coordinatesX'           => $post["coordinatesXEdit1"],
      'coordinatesY'           => $post["coordinatesYEdit1"],
      'coordinatesZ'           => $post["coordinatesZEdit1"],
      'SignX'                  => $post["SignXEdit"],
      'SignY'                  => $post["SignYEdit"],
      'SignZ'                  => $post["SignZEdit"],
      'CouchMovementX'         => $post["CouchMovementXEdit"],
      'CouchMovementY'         => $post["CouchMovementYEdit"],
      'CouchMovementZ'         => $post["CouchMovementZEdit"],
      'oleh2'                   => $post['oleh2']?? null,
    );

    $this->Ct_simulatorModel->updateCt_simulatorFisikaMedis($data,$id);

    $dataIsocenter= array(
      'treatmentField'           => $post["treatmentFieldEdit"],
      'valueX'                   => $post["valueXEdit"],
      'coordinatesX'             => $post["coordinatesXEdit"],
      'valueY'                   => $post["valueYEdit"],
      'coordinatesY'             => $post["coordinatesYEdit"],
      'valueZ'                   => $post["valueZEdit"],
      'coordinatesZ'             => $post["coordinatesZEdit"],
      'tpsAp'                    => $post["tpsApEdit"],
      'tpsRl'                    => $post["tpsRlEdit"],
      'tpsLl'                    => $post["tpsLlEdit"],
      'actualAp'                 => $post["actualApEdit"],
      'actualRl'                 => $post["actualRlEdit"],
      'actualLl'                 => $post["actualLlEdit"],
    );

    $this->Ct_simulatorModel->updateIsocenter_FisikaMedis($dataIsocenter,$idIso);

    if (isset($post['treatmentFieldEdit2'])) {
      $dataIsocenter2= array(
        'treatmentField'           => $post["treatmentFieldEdit2"],
        'valueX'                   => $post["valueXEdit2"],
        'coordinatesX'             => $post["coordinatesXEdit2"],
        'valueY'                   => $post["valueYEdit2"],
        'coordinatesY'             => $post["coordinatesYEdit2"],
        'valueZ'                   => $post["valueZEdit2"],
        'coordinatesZ'             => $post["coordinatesZEdit2"],
        'tpsAp'                    => $post["tpsApEdit2"],
        'tpsRl'                    => $post["tpsRlEdit2"],
        'tpsLl'                    => $post["tpsLlEdit2"],
        'actualAp'                 => $post["actualApEdit2"],
        'actualRl'                 => $post["actualRlEdit2"],
        'actualLl'                 => $post["actualLlEdit2"],
      );
      $this->Ct_simulatorModel->updateIsocenter_FisikaMedis($dataIsocenter2,$idIso2);
    }

    if (isset($post['treatmentFieldEdit2a'])) {
      $dataIsocenter2a= array(
        'treatmentField'           => $post["treatmentFieldEdit2a"],
        'valueX'                   => $post["valueXEdit2a"],
        'coordinatesX'             => $post["coordinatesXEdit2a"],
        'valueY'                   => $post["valueYEdit2a"],
        'coordinatesY'             => $post["coordinatesYEdit2a"],
        'valueZ'                   => $post["valueZEdit2a"],
        'coordinatesZ'             => $post["coordinatesZEdit2a"],
        'tpsAp'                    => $post["tpsApEdit2a"],
        'tpsRl'                    => $post["tpsRlEdit2a"],
        'tpsLl'                    => $post["tpsLlEdit2a"],
        'actualAp'                 => $post["actualApEdit2a"],
        'actualRl'                 => $post["actualRlEdit2a"],
        'actualLl'                 => $post["actualLlEdit2a"],
      );
      $this->Ct_simulatorModel->updateIsocenter_FisikaMedis($dataIsocenter2a,$idIso2a);
    }

    if (isset($post['treatmentFieldEdit3'])) {
      $dataIsocenter3= array(
        'idCtSimulatorFisikaMedis' => $id,
        'treatmentField'           => $post["treatmentFieldEdit3"],
        'valueX'                   => $post["valueXEdit3"],
        'coordinatesX'             => $post["coordinatesXEdit3"],
        'valueY'                   => $post["valueYEdit3"],
        'coordinatesY'             => $post["coordinatesYEdit3"],
        'valueZ'                   => $post["valueZEdit3"],
        'coordinatesZ'             => $post["coordinatesZEdit3"],
        'tpsAp'                    => $post["tpsApEdit3"],
        'tpsRl'                    => $post["tpsRlEdit3"],
        'tpsLl'                    => $post["tpsLlEdit3"],
        'actualAp'                 => $post["actualApEdit3"],
        'actualRl'                 => $post["actualRlEdit3"],
        'actualLl'                 => $post["actualLlEdit3"],
      );
      $this->Ct_simulatorModel->simpanIsocenter_FisikaMedis($dataIsocenter3);
    }

    if (isset($post['treatmentFieldEdit3a'])) {
      $dataIsocenter3a= array(
        'idCtSimulatorFisikaMedis' => $id,
        'treatmentField'           => $post["treatmentFieldEdit3a"],
        'valueX'                   => $post["valueXEdit3a"],
        'coordinatesX'             => $post["coordinatesXEdit3a"],
        'valueY'                   => $post["valueYEdit3a"],
        'coordinatesY'             => $post["coordinatesYEdit3a"],
        'valueZ'                   => $post["valueZEdit3a"],
        'coordinatesZ'             => $post["coordinatesZEdit3a"],
        'tpsAp'                    => $post["tpsApEdit3a"],
        'tpsRl'                    => $post["tpsRlEdit3a"],
        'tpsLl'                    => $post["tpsLlEdit3a"],
        'actualAp'                 => $post["actualApEdit3a"],
        'actualRl'                 => $post["actualRlEdit3a"],
        'actualLl'                 => $post["actualLlEdit3a"],
      );
      $this->Ct_simulatorModel->simpanIsocenter_FisikaMedis($dataIsocenter3a);
    }



    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function checkCT()
  {
    $nokun = $this->input->post('nokun'); // Ambil data nokun dari request
    $result = $this->Ct_simulatorModel->getCTByNokun($nokun);

    if ($result) {
      echo json_encode(['status' => 'exist']); // Data ditemukan
    } else {
      echo json_encode(['status' => 'not_found']); // Data tidak ditemukan
    }
  }
}

/* End of file Ct_simulator.php */
/* Location: ./application/controllers/radioterapi/Ct_simulator.php */
