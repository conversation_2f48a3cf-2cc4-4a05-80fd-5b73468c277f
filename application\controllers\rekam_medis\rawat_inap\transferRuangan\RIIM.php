<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RIIM extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');

    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/rawat_inap/transferRuangan/RIIMModel',
      )
    );
  }

  public function index()
  {
    $data = array(
      'nokun' => $this->uri->segment(6),
      'jumlah' => $this->RIIMModel->history($this->uri->segment(6), 'jumlah'),
      'lukaInfeksius' => $this->masterModel->referensi(1462),
      'infeksiMRSA' => $this->masterModel->referensi(1463),
      'gangguanJiwa' => $this->masterModel->referensi(1464),
      'hamilMenyusui' => $this->masterModel->referensi(1465),
      'AMLALL' => $this->masterModel->referensi(1466),
      'ANCkurang' => $this->masterModel->referensi(1467),
      'keganasanHematologi' => $this->masterModel->referensi(1468),
      'tidakDemam' => $this->masterModel->referensi(1469),
      'ANClebih' => $this->masterModel->referensi(1470),
      'tidakStabil' => $this->masterModel->referensi(1471),
      'meninggal' => $this->masterModel->referensi(1472),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/transferRuangan/RIIM/index', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->RIIMModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $diizinkan = null;

          $data = array(
            'id' => isset($post['id']) ? $post['id'] : null,
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'luka_infeksius' => isset($post['luka_infeksius']) ? $post['luka_infeksius'] : null,
            'infeksi_mrsa' => isset($post['infeksi_mrsa']) ? $post['infeksi_mrsa'] : null,
            'gangguan_jiwa' => isset($post['gangguan_jiwa']) ? $post['gangguan_jiwa'] : null,
            'hamil_menyusui' => isset($post['hamil_menyusui']) ? $post['hamil_menyusui'] : null,
            'aml_all' => isset($post['aml_all']) ? $post['aml_all'] : null,
            'anc_kurang' => isset($post['anc_kurang']) ? $post['anc_kurang'] : null,
            'keganasan_hematologi' => isset($post['keganasan_hematologi']) ? $post['keganasan_hematologi'] : null,
            'diizinkan_masuk' => isset($post['diizinkan_masuk']) ? $post['diizinkan_masuk'] : null,
            'tidak_demam' => isset($post['tidak_demam']) ? $post['tidak_demam'] : null,
            'anc_lebih' => isset($post['anc_lebih']) ? $post['anc_lebih'] : null,
            'tidak_stabil' => isset($post['tidak_stabil']) ? $post['tidak_stabil'] : null,
            'meninggal' => isset($post['meninggal']) ? $post['meninggal'] : null,
            'diizinkan_keluar' => isset($post['diizinkan_keluar']) ? $post['diizinkan_keluar'] : null,
            'oleh' => $this->session->userdata('id'),
            'status' => '1',
          );

          // echo '<pre>';print_r($data);exit();
          $this->RIIMModel->replace($data);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->RIIMModel->get($post['id'], true);
        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data,
          )
        );
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/transferRuangan/RIIM/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->RIIMModel->history($nokun, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        if ($h->diizinkan_masuk == 0) {
          $status = '<p class="text-warning">Ditolak masuk</p>';
        } elseif ($h->diizinkan_masuk == 1) {
          if ($h->diizinkan_keluar == null) {
            $status = '<p class="text-success">Diizinkan masuk</p>';
          } elseif ($h->diizinkan_keluar == 0) {
            $status = '<p class="text-warning">Ditolak keluar</p>';
          } elseif ($h->diizinkan_keluar == 1) {
            $status = '<p class="text-primary">Diizinkan keluar</p>';
          }
        }
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->jam)),
        $status,
        $h->pengisi,
        date('d-m-Y, H.i.s', strtotime($h->updated_at)),
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-riim' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-riim' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-riim' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array('status' => 0,);
    $this->RIIMModel->ubah($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}