<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Pasien extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if(!in_array(50,$this->session->userdata('akses'))){
            redirect('login');
        }

        $this->load->model(array('MasterModel','whatsapp/WhatsappPasienModel'));
        $this->load->library('whatsapp');
    }

    public function index() {

        // $ruangan = $this->masterModel->ruanganRskd();
        $data = array(
        'title' => 'Halaman WhatsApp Pasien',
        'isi'   => 'Whatsapp/pasien'
        );
        $this->load->view('layout/wrapper',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->WhatsappPasienModel->rules;
                $this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $nomor = '+62'.substr(trim($post['nomor']), 1);
                    $jenis = "kami sampaikan";
                    $selamat = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")));
                    if($post['jenis']){
                        if($post['jenis'] == 1){
                            $jenis = "info hasil LAB";
                        }
                    }
                    $res = $this->whatsapp->send($nomor, array($selamat,$jenis,$post['pesan']));
                    $res = json_encode($res['data']['data']);
                    $message = json_decode($res);
                    // $this->db->trans_begin();

                    // if ($this->db->trans_status() === false) {
                    //     $this->db->trans_rollback();
                    //     $result = array('status' => 'failed');
                    // } else {
                    //     $this->db->trans_commit();
                    //     $result = array('status' => 'success');
                    // }

                    if($message[0]->status == 'failed') {
            			echo json_encode(array('status' => 'failed', 'message' => $message[0]->message));
                        exit;
                    }

                    $data = array(
                        'NORM' => $post['pasien'],
                        'NOMOR' => $post['nomor'],
                        'PESAN' => $post['pesan'],
                        'KIRIM' => $message[0]->status,
                        'OLEH' => $this->session->userdata("id"),
                    );

                    $this->db->insert('layanan.wa_pasien', $data);

                    if($this->db->affected_rows() > 0){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }

    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->WhatsappPasienModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            // $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_pengkajian_luka" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> PASIEN;      
            $sub_array[] = $row -> PESAN;
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> OLEH;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->WhatsappPasienModel->total_count(),
            "recordsFiltered"   => $this->WhatsappPasienModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }

    public function getPasien()
    {
        $result = $this->MasterModel->pasien();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['NORM'];
            $sub_array['text'] = $row['NAMA'] ." [ ". $row['NORM'] ." ]";
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
        );
        echo json_encode($data);
    }

    public function nomor()
    {
        $norm = $this->input->post('norm');
        $data = $this->MasterModel->nomorPasien($norm);

        echo json_encode($data->NOMOR);
    }
}