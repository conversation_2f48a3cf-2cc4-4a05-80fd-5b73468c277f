<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Biaya extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == FALSE) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('keuanganModel', 'masterModel'));
    }

    public function index()
    {
        $dataJenisRealisasi = $this->keuanganModel->getJenisBiaya();
        // $ruangan = $this->keuanganModel->getRuangan();
        $alokasiRuangan = $this->keuanganModel->getAlokasiRuangan();
        $listBiaya = $this->keuanganModel->listBiaya();

        $data = array(
            'title' => 'Halaman Realisasi Sistem Informasi Manajemen Anggaran',
            'isi'   => 'Keuangan/realisasi/ruangan',
            'dataJenisRealisasi'      => $dataJenisRealisasi,
            'alokasiRuangan'      => $alokasiRuangan,
            'listBiaya'      => $listBiaya,
        );

        $this->load->view('layout/wrapper', $data);
    }

    public function getAlokasi()
    {
        $dataAlokasi = $this->keuanganModel->alokasi();

        echo json_encode($dataAlokasi);
    }

    public function simpanBiaya()
    {
        $data = array(
            'ID_JENIS_REALISASI' => $this->input->post("jenisBiaya"),
            'ID_ALOKASI'  => $this->input->post("ruangan"),
            'BIAYA'       => $this->input->post("biaya"),
            'TANGGAL'     => $this->input->post("tanggal"),
            'OLEH'        => $this->session->userdata('id'),
        );

        $simpan_realiasi = $this->db->insert('keuangan.belanja_ruangan', $data);
    }
}

/* End of file Biaya.php */
/* Location: ./application/controllers/keuangan/Biaya.php */
