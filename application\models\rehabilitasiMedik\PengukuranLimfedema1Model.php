<?php
defined('BASEPATH') or exit('No direct script access allowed');
class PengukuranLimfedema1Model extends MY_Model
{
    protected $_table_name = 'medis.tb_pengukuran_limfedema1 pl1';
    protected $_primary_key = 'pl1.id';
    protected $_order_by = 'pl1.id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'tanggal',
                'label' => 'Tanggal',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'sisi[]',
                'label' => 'Sisi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesSimpan()
    {
        return [
            [
                'field' => 'bagian',
                'label' => 'Bagian',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function simpan($data)
    {
        $this->db->insert('medis.tb_pengukuran_limfedema1', $data);
        return $this->db->insert_id();
    }

    public function ubah($id, $data)
    {
        $this->db->where('id_limfedema');
        $this->db->update('medis.tb_pengukuran_limfedema1', $data);
    }

    function dataTersimpan($nomr, $jumlah = false)
    {
        $this->db->select('pl1.id, pl1.tanggal, bagian.id_variabel id_bagian, bagian.variabel bagian');
        $this->db->from($this->_table_name);
        $this->db->join('db_master.variabel bagian', 'bagian.id_variabel = pl1.bagian', 'left');
        $this->db->where('pl1.nomr', $nomr);
        $this->db->where('pl1.status', 1);
        $this->db->order_by('pl1.tanggal', 'desc');
        $query = $this->db->get();
        if ($jumlah) {
            return $query->num_rows();
        } else {
            return $query->result_array();
        }
    }
}

/* End of file PengukuranLimfedema1Model.php */
/* Location: ./application/models/rehabilitasiMedik/PengukuranLimfedema1Model.php */