<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PewsModel extends CI_Model {

  public function simpanPews($data)
  {
    $this->db->trans_begin();
    $this->db->insert('keperawatan.tb_pews', $data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function tbl_pews($nomr)
  {
    $query = $this->db->query(
      "SELECT pews.id ID,
      pews.tanggal TANGGAL,
      master.getNamaLengkapPegawai(ap.NIP) OLEH,
      mv.nilai NILAIPRILAKU,
      mv2.nilai NILAIKARDIOVASKULAR,
      mv3.nilai NILAIPERNAFASAN,
      mr.DESKRIPSI RUANGAN
      ,(mv.nilai+mv2.nilai+mv3.nilai)TOTALPEWS
      FROM keperawatan.tb_pews pews
      LEFT JOIN db_master.variabel mv ON mv.id_variabel = pews.perilaku
      LEFT JOIN db_master.variabel mv2 ON mv2.id_variabel = pews.kardio_vaskuler
      LEFT JOIN db_master.variabel mv3 ON mv3.id_variabel = pews.pernafasan
      LEFT JOIN aplikasi.pengguna ap ON ap.ID = pews.oleh
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = pews.nokun
      LEFT JOIN master.ruangan mr ON mr.ID=pk.RUANGAN
      WHERE pews.nomr = '$nomr' AND pews.`status` = 1
      ORDER BY pews.id DESC
      LIMIT 1"
    );
    return $query;
  }

  public function deskTotalPews($idEws)
  {
    $query = $this->db->query(
      "SELECT pews.`*`,mv.variabel DATAPERILAKU, mv2.variabel DATAKARDIOVASKULAR,
      mv3.variabel PERNAFASAN, master.getNamaLengkapPegawai(ap.NIP) OLEH,
      mv.nilai NILAIPRILAKU, mv2.nilai NILAIKARDIOVASKULAR, mv3.nilai NILAIPERNAFASAN,mr.DESKRIPSI RUANGAN
      ,(mv.nilai+mv2.nilai+mv3.nilai)TOTALPEWS
      FROM keperawatan.tb_pews pews
      LEFT JOIN db_master.variabel mv ON mv.id_variabel = pews.perilaku
      LEFT JOIN db_master.variabel mv2 ON mv2.id_variabel = pews.kardio_vaskuler
      LEFT JOIN db_master.variabel mv3 ON mv3.id_variabel = pews.pernafasan
      LEFT JOIN aplikasi.pengguna ap ON ap.ID = pews.oleh
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = pews.nokun
      LEFT JOIN master.ruangan mr ON mr.ID=pk.RUANGAN
      WHERE pews.id = '$idEws'"
    );
    return $query->row_array();
  }

  public function deskTblPews($nomr, $nokun, $tanggal)
  {
    $query = $this->db->query(
      "SELECT pews.`*`,mv.variabel DATAPERILAKU, mv2.variabel DATAKARDIOVASKULAR,
      mv3.variabel PERNAFASAN, master.getNamaLengkapPegawai(ap.NIP) OLEH,
      mv.nilai NILAIPRILAKU, mv2.nilai NILAIKARDIOVASKULAR, mv3.nilai NILAIPERNAFASAN,mr.DESKRIPSI RUANGAN
      ,(mv.nilai+mv2.nilai+mv3.nilai)TOTALPEWS
      FROM keperawatan.tb_pews pews
      LEFT JOIN db_master.variabel mv ON mv.id_variabel = pews.perilaku
      LEFT JOIN db_master.variabel mv2 ON mv2.id_variabel = pews.kardio_vaskuler
      LEFT JOIN db_master.variabel mv3 ON mv3.id_variabel = pews.pernafasan
      LEFT JOIN aplikasi.pengguna ap ON ap.ID = pews.oleh
      LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = pews.nokun
      LEFT JOIN master.ruangan mr ON mr.ID=pk.RUANGAN
      WHERE pews.nomr = '$nomr' AND pews.nokun = '$nokun' AND pews.`status` = 1 AND DATE(pews.tanggal) = '$tanggal'
      ORDER BY pews.tanggal DESC"
    );
    return $query->result_array();
  }

}

/* End of file PewsModel.php */
/* Location: ./application/models/pews/PewsModel.php */
