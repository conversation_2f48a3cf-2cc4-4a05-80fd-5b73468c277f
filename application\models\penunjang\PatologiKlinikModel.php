<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PatologiKlinikModel extends MY_Model
{
  protected $_table_name = 'layanan.order_lab';
  protected $_primary_key = 'NOMOR';
  protected $_order_by = 'NOMOR';
  protected $_order_by_type = 'DESC';

  public $rules = [
    'KUNJUNGAN' => [
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => [
        'required' => '%s <PERSON>ajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      ]
    ],
  ];

  function __construct()
  {
    parent::__construct();
  }

  public function tindakan($tujuanPK)
  {
    $this->db->select('t.ID, t.NAMA, tt.TARIF');
    $this->db->from('master.tindakan_ruangan tr');
    $this->db->join('master.tindakan t', 't.ID = tr.TINDAKAN', 'left');
    $this->db->join('master.tarif_tindakan tt', 'tt.TINDAKAN = t.ID', 'left');
    $this->db->where('tr.RUANGAN', $tujuanPK);
    $this->db->where('t.STATUS !=', 0);
    $this->db->where('tt.KELAS', 0);
    $this->db->where('tt.STATUS', 1);
    $this->db->order_by('t.ID');
    $query = $this->db->get();
    return $query->result();
  }

  public function simpan($data)
  {
    $this->db->insert('layanan.order_lab', $data);
  }

  public function simpanDetail($data)
  {
    $this->db->insert_batch('layanan.order_detil_lab', $data);
  }

  public function ubah($data, $nomor)
  {
    $this->db->where('layanan.order_lab.NOMOR', $nomor);
    $this->db->update('layanan.order_lab', $data);
  }

  public function history($norm, $param)
  {
    if (isset($param)) {
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->select('ol.NOMOR');
      } elseif ($param == 'tabel') {
        // Tabel history
        $this->db->select(
          "ol.NOMOR no_lab, ol.TANGGAL tanggal, r_awal.DESKRIPSI ruang_awal, ol.TANGGAL_RENCANA tanggal_rencana,
          r_tujuan.DESKRIPSI ruang_tujuan, master.getNamaLengkapPegawai(dok.NIP) dokter_perujuk,
          master.getNamaLengkapPegawai(ap.NIP) pengirim, ol.STATUS status,
          IF(
            ol.STATUS = 1, 'Order terkirim',
            IF(
                ol.STATUS = 2, 'Diterima/proses',
                CONCAT('Order Dibatalkan oleh: ', master. getNamaLengkapPegawai(ap.NIP))
              )
          ) ket_status"
        );
      }
    }
    $this->db->from('layanan.order_lab ol');
    $this->db->join('pendaftaran.kunjungan k_awal', 'k_awal.NOMOR = ol.KUNJUNGAN', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k_awal.NOPEN', 'left');
    $this->db->join('master.ruangan r_awal', 'r_awal.ID = k_awal.RUANGAN', 'left');
    $this->db->join('master.dokter dok', 'dok.ID = ol.DOKTER_ASAL', 'left');
    $this->db->join('pendaftaran.kunjungan k_tujuan', 'k_tujuan.REF = ol.NOMOR', 'left');
    $this->db->join('master.ruangan r_tujuan', 'r_tujuan.ID = ol.TUJUAN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = ol.OLEH', 'left');
    $this->db->where('r_tujuan.JENIS', 5);
    $this->db->where('r_tujuan.JENIS_KUNJUNGAN', 4);
    $this->db->where('p.NORM', $norm);
    if ($param == 'jumlah') {
      // Jumlah history
      $query = $this->db->get();
      return $query->num_rows();
    } elseif ($param == 'tabel') {
      // Tabel history
      $this->db->order_by('ol.TANGGAL', 'desc');
      $query = $this->db->get();
      return $query;
    } else {
      return null;
    }
  }

  public function detail($nomor)
  {
    $this->db->select(
      "ol.NOMOR no_lab, ol.TANGGAL tanggal, master.getNamaLengkapPegawai(ap.NIP) pengirim,
      master.getNamaLengkapPegawai(dpjp.NIP) DPJP, r_awal.DESKRIPSI ruang_awal, ol.TANGGAL_RENCANA tanggal_rencana,
      master.getNamaLengkapPegawai(dok.NIP) dokter_perujuk, r_tujuan.DESKRIPSI ruang_tujuan,
      ol.STATUS status, r_awal.ID as id_ruangan_awal,
      IF(
        ol.STATUS = 1, 'Diterima/proses',
        IF(
          ol.STATUS = 2, 'Final', CONCAT('Order Dibatalkan oleh: ',master.getNamaLengkapPegawai(ap.NIP))
        )
      ) ket_status"
    );
    $this->db->from('layanan.order_lab ol');
    $this->db->join('pendaftaran.kunjungan k_awal', 'k_awal.NOMOR = ol.KUNJUNGAN', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = k_awal.NOPEN', 'left');
    $this->db->join('master.ruangan r_awal', 'r_awal.ID = k_awal.RUANGAN', 'left');
    $this->db->join('master.dokter dok', 'dok.ID = ol.DOKTER_ASAL', 'left');
    $this->db->join('pendaftaran.kunjungan k_tujuan', 'k_tujuan.REF = ol.NOMOR', 'left');
    $this->db->join('master.ruangan r_tujuan', 'r_tujuan.ID = ol.TUJUAN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = ol.OLEH', 'left');
    $this->db->join('pendaftaran.tujuan_pasien tp', 'p.NOMOR = tp.NOPEN', 'left');
    $this->db->join('master.dokter dpjp', 'dpjp.ID = tp.DOKTER', 'left');
    $this->db->where('ol.TUJUAN', '105070101');
    $this->db->where('ol.NOMOR', $nomor);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function tabelDetail($id)
  {
    $this->db->select('t.ID, t.NAMA, tt.TARIF');
    $this->db->from('layanan.order_detil_lab odl');
    $this->db->join('master.tindakan t', 't.ID = odl.TINDAKAN', 'left');
    $this->db->join('master.tarif_tindakan tt', 'tt.TINDAKAN = t.ID', 'left');
    $this->db->where('tt.KELAS', 0);
    $this->db->where('tt.STATUS', 1);
    $this->db->where('odl.ORDER_ID', $id);
    $query = $this->db->get();
    return $query->result();
  }
}

// End of file PatologiKlinikModel.php
// Location: ./application/models/penunjang/PatologiKlinikModel.php