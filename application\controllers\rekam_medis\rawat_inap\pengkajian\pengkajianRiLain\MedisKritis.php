<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MedisKritis extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel','rekam_medis/MedisModel','pengkajianAwalModel','rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel','rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel', 'rekam_medis/TbBbModel'));
  }

  public function index()
  {
    // $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
    $_POST['jenis'] = 10;
    if($this->uri->segment(3) == 1){
      $_POST['jenis'] = 999;
    }else{
      $_POST['idemr'] = $this->uri->segment(3);
    }
    $pasien = $this->MedisModel->getNomrRawatInap($this->uri->segment(2));

    $data = array(
      'pasien' => $pasien,
      'anamnesis'             => $this->masterModel->referensi(54),
      'riwayatPenyakitDahulu' => $this->masterModel->referensi(55),
      'Ecog'                  => $this->masterModel->referensi(30),
      'mata'                  => $this->masterModel->referensi(31),
      'leher'                 => $this->masterModel->referensi(32),
      'dadaIramaJantung'      => $this->masterModel->referensi(33),
      'dadaSuaraNafas'        => $this->masterModel->referensi(34),
      'perutHati'             => $this->masterModel->referensi(35),
      'perutLimpa'            => $this->masterModel->referensi(36),
      'ekstremitasAtasKanan'  => $this->masterModel->referensi(37),
      'ekstremitasAtasKiri'   => $this->masterModel->referensi(38),
      'ekstremitasBawahKanan' => $this->masterModel->referensi(39),
      'ekstremitasBawahKiri'  => $this->masterModel->referensi(40),
      'kulitTurgor'           => $this->masterModel->referensi(41),
      'kulitSianosis'         => $this->masterModel->referensi(42),
      'refleks'               => $this->masterModel->referensi(43),
      'kelenjarGetahBening'   => $this->masterModel->referensi(44),
      'aksesVaskuler'         => $this->masterModel->referensi(802),
      'aksesVaskulerLokasi'   => $this->masterModel->referensi(803),
      'aksesVaskulerCDL'      => $this->masterModel->referensi(804),
      'tumor'                 => $this->masterModel->referensi(45),
      
      'sistemSaraf'           => $this->masterModel->referensi(1302),
      'pupil'                 => $this->masterModel->referensi(1303),
      'sistemPernapasan'      => $this->masterModel->referensi(1305),
      'edema'                 => $this->masterModel->referensi(1308),
      'ikterik'               => $this->masterModel->referensi(1309),
      'asites'                => $this->masterModel->referensi(1310),
      'sistemHematologi'      => $this->masterModel->referensi(1311),
      'skoring'               => $this->masterModel->referensi(1312),
      'abdomen'               => $this->masterModel->referensi(1313),
      'bisingUsus'            => $this->masterModel->referensi(1314),

      'sisiTubuh'        => $this->masterModel->referensi(49),
      'stadium'          => $this->masterModel->stadium(),
      'tujuanPengobatan' => $this->masterModel->referensi(266),
      'diet'             => $this->masterModel->referensi(56),
      'jenisDiet'        => $this->masterModel->referensi(57),

      'kunjungan_pk' => $this->pengkajianAwalModel->kunjungan_pk($pasien['NORM']),
      'sitologi'     => $this->pengkajianAwalModel->sitologi($pasien['NORM']),
      'histologi'    => $this->pengkajianAwalModel->histologi($pasien['NORM']),
      'tindakan_rad' => $this->pengkajianAwalModel->tindakan_rad($pasien['NORM']),

      'performanceStatus_2' => $this->masterModel->referensi(262),
      'Ecog' => $this->masterModel->referensi(239),
      'Karnofsky' => $this->masterModel->referensi(240),
      'Lansky' => $this->masterModel->referensi(267),

      'klinis' => $this->masterModel->referensi(256)

    );

    // echo "<pre>";print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/pengkajianRiMedisKritis',$data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $this->db->trans_begin();
          $post = $this->input->post();

          $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();

          $dataMedis = array(
            'id_emr'         => $getIdEmr,
            'nopen'          => $post['nopen'],
            'nokun'          => $post['nokun'],
            'jenis'          => $post['jenis'],
            'created_by'     => $this->session->userdata('id'),
            'flag'           => '1',
          );

          $dataAnamnesa = array(
            'id_emr' => $getIdEmr,
            'id_auto_allo' => $post['anamnesis'],
            'allo_nama' => isset($post['nama']) ? $post['nama'] : "",
            'hubungan_dengan_pasien' => isset($post['hubunganDenganPasien']) ? $post['hubunganDenganPasien'] : "",
            'harapan_keluarga_pasien' => isset($post['harapanKeluargaPasien']) ? $post['harapanKeluargaPasien'] : "",
            'keluhan_utama' => isset($post['keluhanUtama']) ? $post['keluhanUtama'] : "",
            'riwayat_sakit_sekarang' => isset($post['riwayatPenyakitSekarang']) ? $post['riwayatPenyakitSekarang'] : "",
            'riwayat_penyakit_dahulu' => isset($post['riwayatPenyakitDahulu']) ? $post['riwayatPenyakitDahulu'] : "",
            'desk_riwayat_penyakit_dahulu' => isset($post['deskRiwayatPenyakitDahulu']) ? $post['deskRiwayatPenyakitDahulu'] : "",
          );

          if (isset($post['status_lokalis_not']) == "on") {
            $lokaslisNot = 1;
          } else {
            $lokaslisNot = 0;
          }

          $dataPemeriksaanFisik = array(
            'id_emr' => $getIdEmr,
            'performance_status' => isset($post['performanceStatus']) ? $post['performanceStatus'] : "",
            'performance_status_anak' => isset($post['performanceStatus_anak']) ? $post['performanceStatus_anak'] : "",
            'performance_status_anak_score' => isset($post['performanceStatus_anak_skor']) ? $post['performanceStatus_anak_skor'] : "",

            'irama_jantung' => isset($post['iramaJantung']) ? $post['iramaJantung'] : "",
            'desk_irama_jantung' => isset($post['iramaJantungTdkTerartur']) ? $post['iramaJantungTdkTerartur'] : "",
            'suara_napas' => isset($post['dadaSuaraNafas']) ? $post['dadaSuaraNafas'] : "",
            'desk_suara_napas' => isset($post['suaraNafasTdkNormal']) ? $post['suaraNafasTdkNormal'] : "",
            'sianosis' => isset($post['kulitSianosis']) ? $post['kulitSianosis'] : "",
            
            'sistem_saraf' => isset($post['sistemSaraf']) ? $post['sistemSaraf'] : null,
            'gcs_e' => isset($post['gcs_e']) ? $post['gcs_e'] : null,
            'gcs_v' => isset($post['gcs_v']) ? $post['gcs_v'] : null,
            'gcs_m' => isset($post['gcs_m']) ? $post['gcs_m'] : null,
            'gcs_total' => isset($post['gcs_total']) ? $post['gcs_total'] : null,
            'gcs_v_ket' => isset($post['gcs_v_ket']) ? $post['gcs_v_ket'] : null,
            'gcs_total_ket' => isset($post['gcs_total_ket']) ? $post['gcs_total_ket'] : null,
            'fs_e' => isset($post['fs_e']) ? $post['fs_e'] : null,
            'fs_m' => isset($post['fs_m']) ? $post['fs_m'] : null,
            'fs_b' => isset($post['fs_b']) ? $post['fs_b'] : null,
            'fs_r' => isset($post['fs_r']) ? $post['fs_r'] : null,
            'fs_total' => isset($post['fs_total']) ? $post['fs_total'] : null,
            'pupil' => isset($post['pupil']) ? $post['pupil'] : null,
            'pupil_lain' => isset($post['lainLainPupil']) ? $post['lainLainPupil'] : null,
            'retieks_cahaya' => isset($post['retieksCahaya']) ? $post['retieksCahaya'] : null,
            'letralisasi' => isset($post['letralisasi']) ? $post['letralisasi'] : null,
            'paralisis' => isset($post['paralisis']) ? $post['paralisis'] : null,
            'sistem_pernapasan' => isset($post['sistemPernapasan']) ? $post['sistemPernapasan'] : null,
            'edema' => isset($post['edema']) ? $post['edema'] : null,
            'ikterik' => isset($post['ikterik']) ? $post['ikterik'] : null,
            'asites' => isset($post['asites']) ? $post['asites'] : null,
            'sistem_hematologi' => isset($post['sistemHematologi']) ? json_encode($post['sistemHematologi']) : null,
            'lokasi_pendarahan' => isset($post['lokasiSistemHematologi']) ? $post['lokasiSistemHematologi'] : null,
            'na_skoring' => isset($post['status_skoring_not']) ? 1 : 0,
            'skoring' => isset($post['skoring']) ? $post['skoring'] : null,
            'sofa' => isset($post['sebutkanSOFA']) ? $post['sebutkanSOFA'] : null,
            'pelod2' => isset($post['sebutkanPELOD2']) ? $post['sebutkanPELOD2'] : null,
            'abdomen' => isset($post['abdomen']) ? $post['abdomen'] : null,
            'bising_usus' => isset($post['bisingUsus']) ? $post['bisingUsus'] : null,
            'na_lokalis' => isset($lokaslisNot) ? $lokaslisNot : "",
            'perut_hati' => isset($post['perutHati']) ? $post['perutHati'] : "",
            'desk_hati' => isset($post['perutHatiTeraba']) ? $post['perutHatiTeraba'] : "",
            'perut_limpa' => isset($post['perutLimpa']) ? $post['perutLimpa'] : "",
            'desk_limpa' => isset($post['perutLimpaTeraba']) ? $post['perutLimpaTeraba'] : "",
            'kgb' => isset($post['kelenjarGetahBening']) ? $post['kelenjarGetahBening'] : "",
            'desk_kgb' => isset($post['deskKelenjarGetahBening']) ? $post['deskKelenjarGetahBening'] : "",
            'tumor' => isset($post['tumor']) ? $post['tumor'] : "",
            'jenis_tumor' => isset($post['deskTumor']) ? $post['deskTumor'] : "",
          );

          $dataPenunjangLainya = array(
            'id_emr' => $getIdEmr,
            'penunjang_lain' => isset($post['penunjangLainya']) ? $post['penunjangLainya'] : "",
          );

          if (isset($post['klasifikasi_not']) == "on") {
            $klasifikasiNot = 1;
          } else {
            $klasifikasiNot = 0;
          }

          $dataMedisKeperawatan = array(
            'id_emr' => $getIdEmr,
            'desk_diagnosa_medis' => isset($post['deskDiagnosisMedis']) ? $post['deskDiagnosisMedis'] : "",
            'desk_diagnosa_kanker' => isset($post['deskDiagnosaKanker']) ? $post['deskDiagnosaKanker'] : "",
            'sisi_tubuh' => isset($post['sisiTubuh']) ? $post['sisiTubuh'] : "",
            'klasifikasi_t' => isset($post['klasifikasi_T']) ? $post['klasifikasi_T'] : "",
            'klasifikasi_n' => isset($post['klasifikasi_N']) ? $post['klasifikasi_N'] : "",
            'klasifikasi_m' => isset($post['klasifikasi_M']) ? $post['klasifikasi_M'] : "",
            'klasifikasi_not' => isset($klasifikasiNot) ? $klasifikasiNot : "",
            'stadium' => isset($post['stadium']) ? $post['stadium'] : "",
            'tujuan_pengobatan' => isset($post['tujuanPengobatan']) ? $post['tujuanPengobatan'] : "",
          );

          $masalahKesehatanMedis = array(
            'id_emr' => $getIdEmr,
            'masalahKesehatan' => isset($post['masalahKesehatanMedis']) ? json_encode($post['masalahKesehatanMedis']) : "",
          );

          $dataPerencanaan = array(
            'id_emr' => $getIdEmr,
            'pemeriksaan_penunjang' => isset($post['pemeriksaanPenunjang']) ? $post['pemeriksaanPenunjang'] : "",
            'terapi_tindakan' => isset($post['terapiTindakan']) ? $post['terapiTindakan'] : "",
            'diet' => isset($post['diet']) ? $post['diet'] : "",
            'jenis_diet' => isset($post['jenisDiet']) ? $post['jenisDiet'] : "",
            'diet_lainnya' => isset($post['dietLainya']) ? $post['dietLainya'] : "",
          );

          if (!empty($post['idemr'])) {
            $this->db->replace('medis.tb_anamnesa', $dataAnamnesa);
            $this->db->replace('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
            $this->db->replace('medis.tb_penunjang_lain', $dataPenunjangLainya);
            $this->db->replace('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
            $this->db->replace('medis.tb_masalahkesehatanmedis', $masalahKesehatanMedis);
            $this->db->replace('medis.tb_perencanaan', $dataPerencanaan);
            if ($this->db->replace('medis.tb_medis', $dataMedis)) {
              $result = array('status' => 'success', 'pesan' => 'ubah');
            }
          } else {
            $result = array('status' => 'failed');
            $this->db->insert('medis.tb_anamnesa', $dataAnamnesa);
            $this->db->insert('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
            $this->db->insert('medis.tb_penunjang_lain', $dataPenunjangLainya);
            $this->db->insert('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
            $this->db->insert('medis.tb_masalahkesehatanmedis', $masalahKesehatanMedis);
            $this->db->insert('medis.tb_perencanaan', $dataPerencanaan);

            if ($this->db->insert('medis.tb_medis', $dataMedis)) {
              $result = array('status' => 'success');
            }
          }

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
      }

      else if($param == 'count'){
        $result = $this->MedisDewasaModel->get_count();;
        echo json_encode($result);
      }

      else if($param == 'ambil'){
        $post = $this->input->post(NULL,TRUE);
        $dataMedisDewasaModel = $this->MedisDewasaModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data'   => $dataMedisDewasaModel
        ));
      }

    }
  }

  public function datatables(){
    $result = $this->MedisDewasaModel->datatables();

    $data = array();
    foreach ($result as $row){
      $sub_array = array();
      $sub_array[] = '<a class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasaMedis" data-id="'.$row -> ID_EMR.'"><i class="fa fa-eye"></i> Lihat</a>';
      $sub_array[] = $row -> TANGGAL_PENGKAJIAN_MEDIS;
      $sub_array[] = $row -> RUANGAN;
      $sub_array[] = $row -> DPJP;
      $sub_array[] = $row -> USER_MEDIS;

      $data[] = $sub_array;
    }

    $output = array(
      "draw"              => intval($_POST["draw"]),
      "recordsTotal"      => $this->MedisDewasaModel->total_count(),
      "recordsFiltered"   => $this->MedisDewasaModel->filter_count(),
      "data"              => $data
    );
    echo json_encode($output);
  }

}

/* End of file MedisDewasa.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasa.php */
