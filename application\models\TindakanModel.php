<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TindakanModel extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    public function tindakancppt($id_ruangan)
    {
        $igd = ['105140101', '105140102'];
        $tower = ['105011602', '105011701', '105011801', '105011901', '105030108'];
        
        if (in_array($id_ruangan, $igd)) {
            $query = $this->db->query("SELECT mt.ID id, mt.NAMA text, tar.TARIF tarif
            FROM master.tindakan_ruangan mtr
            LEFT JOIN master.tindakan mt ON mt.ID = mtr.TINDAKAN
            LEFT JOIN master.tarif_tindakan tar ON mt.ID=tar.TINDAKAN AND tar.KELAS=0 AND tar.STATUS=1
            LEFT JOIN master.tindakan_mapping tm ON tm.ID_TINDAKAN = mt.ID
            WHERE 
            mtr.RUANGAN=$id_ruangan AND
            tm.RUANGAN=$id_ruangan
            AND mtr.STATUS=1
            ORDER BY mt.NAMA ASC");
        } elseif (in_array($id_ruangan, $tower)) {
            $query = $this->db->query("SELECT mt.ID id, mt.NAMA text, tar.TARIF tarif
            FROM master.tindakan_ruangan mtr
            LEFT JOIN master.tindakan mt ON mt.ID = mtr.TINDAKAN
            LEFT JOIN master.tarif_tindakan tar ON mt.ID=tar.TINDAKAN AND tar.KELAS=0 AND tar.STATUS=1
            WHERE 
            mtr.RUANGAN=$id_ruangan
            AND mtr.STATUS=1
            AND mt.ID NOT IN (4610, 4773)
            ORDER BY mt.NAMA ASC");
        } else {
            $query = $this->db->query("SELECT mt.ID id, mt.NAMA text, tar.TARIF tarif
            FROM master.tindakan_ruangan mtr
            LEFT JOIN master.tindakan mt ON mt.ID = mtr.TINDAKAN
            LEFT JOIN master.tarif_tindakan tar ON mt.ID=tar.TINDAKAN AND tar.KELAS=0 AND tar.STATUS=1
            WHERE 
            mtr.RUANGAN=$id_ruangan
            AND mtr.STATUS=1
            ORDER BY mt.NAMA ASC");
        }

        return $query->result_array();
    }

    public function simpantindakancppt($nokun = "", $tindakan = [], $oleh = "", $olehDok = "")
    {
        $statusPengguna = $this->session->userdata('status');
        $olehTM = $this->session->userdata('id');
        if ($statusPengguna == 2) {
            $query_perawat = $this->db->query("
                SELECT mp.ID as oleh 
                FROM aplikasi.pengguna ap 
                LEFT JOIN master.perawat mp ON mp.NIP=ap.NIP 
                WHERE ap.ID = ?", [$oleh])->row();
            $oleh = $query_perawat ? $query_perawat->oleh : $oleh;
        }
        $id_tindakan_medis = $this->db->query("SELECT generator.generateIdTindakanMedis(NOW()) as ID")->row()->ID;

        if (!$id_tindakan_medis) {
            return ['success' => false, 'message' => 'Gagal mendapatkan ID tindakan medis.'];
        }

        $tanggal = date("Y-m-d H:i:s");

        $query = "
            INSERT IGNORE INTO layanan.tindakan_medis (ID, KUNJUNGAN, TINDAKAN, TANGGAL, OLEH)
            VALUES (?, ?, ?, ?, ?)";

        $this->db->query($query, [
            $id_tindakan_medis, 
            $nokun, 
            $tindakan, 
            $tanggal, 
            $olehTM
        ]);

        if ($this->db->affected_rows() > 0) {
            $data_petugas = [
                'TINDAKAN_MEDIS' => $id_tindakan_medis,
                'JENIS' => $statusPengguna == 1 ? 1 : 3,
                'MEDIS' => $statusPengguna == 1 ? $olehDok : $oleh,
            ];

            $this->db->insert('layanan.petugas_tindakan_medis', $data_petugas);

            return ['success' => true, 'ID' => $id_tindakan_medis];
        } else {
            return ['success' => false, 'message' => 'Tidak ada data yang disimpan di tindakan medis.'];
        }
    }
}
