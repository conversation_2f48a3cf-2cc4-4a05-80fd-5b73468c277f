<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RIIMModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_riim';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  public function __construct()
  {
    parent::__construct();
  }

  public function replace($data)
  {
    $this->db->replace('keperawatan.tb_riim', $data);
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_riim.id', $id);
    $this->db->update('keperawatan.tb_riim', $data);
  }

  public function history($nokun, $param)
  {
    if ($param == 'jumlah') {
      $this->db->select('r.id');
    } elseif ($param == 'tabel') {
      $this->db->select(
        'r.id, r.tanggal, r.jam, r.diizinkan_masuk, r.diizinkan_keluar, master.getNamaLengkapPegawai(peng.NIP) pengisi,
        r.updated_at, r.status'
      );
    }
    $this->db->from('keperawatan.tb_riim r');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = r.oleh', 'left');
    $this->db->where('r.nokun', $nokun);
    if ($param == 'jumlah') {
      $this->db->where('r.status', 1);
      $query = $this->db->get();
      return $query->num_rows();
    } elseif ($param == 'tabel') {
      $this->db->order_by('r.updated_at', 'DESC');
      $query = $this->db->get();
      return $query;
    } else {
      return null;
    }
  }

  public function detail($id)
  {
    $this->db->select(
      'r.id, r.tanggal, r.jam, r.luka_infeksius, r.infeksi_mrsa, r.gangguan_jiwa, r.hamil_menyusui, r.aml_all,
      r.anc_kurang, r.keganasan_hematologi, r.diizinkan_masuk, r.tidak_demam, r.anc_lebih, r.tidak_stabil, r.meninggal,
      r.diizinkan_keluar, master.getNamaLengkapPegawai(peng.NIP) pengisi, r.updated_at, r.status'
    );
    $this->db->from('keperawatan.tb_riim r');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = r.oleh', 'left');
    $this->db->where('r.id', $id);
    $query = $this->db->get();
    return $query->result_array();
  }
}