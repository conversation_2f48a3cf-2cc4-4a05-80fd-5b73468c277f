<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProtokolKemo extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $nopen = $this->uri->segment(4);
    $norm = $this->uri->segment(3);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $pilihProtokolKemo = $this->masterModel->pilihProtokolKemo();
    $duplikatHistoryProkem = $this->pengkajianAwalModel->duplikatHistoryProkem($norm);
    $listDrUmum = $this->masterModel->listDrUmum();
    $hProtokolKemo = $this->pengkajianAwalModel->historyProtokolKemo($norm);
    $tbBbCpptAkhirPrtklKemo = $this->masterModel->tbBbCpptAkhirPrtklKemo($nopen);
    $diagnosisCpptAkhirPrtklKemo = $this->masterModel->diagnosisCpptAkhirPrtklKemo($nopen);

    $data = array(
      'getNomr' => $getNomr,
      'norm' => $norm,
      'pilihProtokolKemo' => $pilihProtokolKemo,
      'duplikatHistoryProkem' => $duplikatHistoryProkem,
      'listDrUmum' => $listDrUmum,
      'tbBbCpptAkhirPrtklKemo' => $tbBbCpptAkhirPrtklKemo,
      'diagnosisCpptAkhirPrtklKemo' => $diagnosisCpptAkhirPrtklKemo,
      'hProtokolKemo' => $hProtokolKemo,
    );

    $this->load->view('Pengkajian/protokolKemoterapi/index', $data);
  }

}