<?php
defined('BASEPATH') or exit('No direct script access allowed');

class KlaimRehabModel extends MY_Model
{
    protected $_table = 'medis.tb_klaim_rehab kr';
    protected $_primary_key = 'kr.id';
    protected $_order_by = 'kr.id';
    protected $_order_by_type = 'desc';
    protected $_urutan_kolom = [null, 'tata_laksana', 'tgl_pertama', 'tgl_terakhir', null];
    protected $_pencarian_kolom = ['master.mrconso', 'kr.tanggal', 'krk.tanggal'];

    public function __construct()
    {
        parent::__construct();
    }

    public function rules()
    {
        return [
            [
                'field' => 'nomr',
                'label' => 'Nomor MR',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'tanggal',
                'label' => 'Tanggal',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'anamnesis',
                'label' => 'Anamnesis',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'diagnosis',
                'label' => 'Diagnosis',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'tata_laksana',
                'label' => 'Tata Laksana',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'pemeriksaan_fisik',
                'label' => 'Pemeriksaan fisik dan uji fungsi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'evaluasi',
                'label' => 'Evaluasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'akibat_kerja',
                'label' => 'Suspek penyakit akibat kerja',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesAkibatKerja()
    {
        return [
            [
                'field' => 'keterangan_akibat_kerja',
                'label' => 'Keterangan suspek penyakit akibat kerja',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    // List terapis
    public function listTerapis()
    {
        $this->db->select(
            'ap.ID ID_PENGGUNA, master.getNamaLengkapPegawai(p.NIP) TERAPIS, p.PROFESI ID_PROFESI, ref.DESKRIPSI JENIS'
        );
        $this->db->from('master.pegawai p');
        $this->db->join('master.referensi ref', 'ref.ID = p.PROFESI AND ref.JENIS = 36', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.NIP = p.NIP AND ap.STATUS = 1', 'left');
        $this->db->where_in('p.PROFESI', [9, 13, 15, 16]);
        $this->db->group_by('ap.ID');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function getCPPT($nokun)
    {
        $query = $this->db->query(
            "SELECT
                    c.id id_cppt,
                    c.subyektif,
                    c.obyektif,
                    c.analisis,
                    c.perencanaan,
                    c.instruksi,
                    c.oleh oleh_cppt,
                    c.verif_oleh oleh_verif_cppt
            FROM
                    keperawatan.tb_cppt c
                    LEFT JOIN aplikasi.pengguna peng ON peng.ID = c.oleh
                    LEFT JOIN master.pegawai peg ON peg.NIP = peng.NIP
            WHERE
                    c.nokun = '$nokun' AND peg.SMF = 29
            ORDER BY
                    c.id DESC 
                    LIMIT 1"
        );

        return $query->row_array();
    }

    public function simpan($data)
    {
        $this->db->insert('medis.tb_klaim_rehab', $data);
        return $this->db->insert_id();
    }

    public function tabel($nomr)
    {
        // mulai query kunjungan
        $this->db->select('id_klaim_rehab, MAX(tanggal) tgl_terakhir');
        $this->db->from('medis.tb_klaim_rehab_kunjungan');
        $this->db->where('status !=', 0);
        $this->db->group_by('id_klaim_rehab');
        $this->db->order_by('tanggal', 'DESC');
        $subquery = $this->db->get_compiled_select();
        $this->db->reset_query();
        // akhir query kunjungan

        $this->db->select(
            "kr.id, kr.tanggal tgl_pertama, krk.tgl_terakhir, kr.tata_laksana"
        );
        $this->db->from($this->_table);
        $this->db->join("($subquery) krk", 'krk.id_klaim_rehab = kr.id', 'left');
        $this->db->where('kr.status', 1);
        $this->db->where('kr.nomr', $nomr);
        $this->db->group_by($this->_primary_key);

        $i = 0;
        foreach ($this->_pencarian_kolom as $pk) { // Loop kolom
            if (!isset($_POST['search']['value']) && empty($_POST['search']['value'])) {
                $_POST['search']['value'] = null;
            }

            if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
                if ($i === 0) { // Loop pertama
                    $this->db->group_start();
                    $this->db->like($pk, $_POST['search']['value']);
                } else {
                    $this->db->or_like($pk, $_POST['search']['value']);
                }

                if (count($this->_pencarian_kolom) - 1 == $i) { // Loop terakhir
                    $this->db->group_end(); // Tutup kurung
                }
            }
            $i++;
        }

        if (isset($_POST['order'])) { // Pemrosesan order
            $this->db->order_by($this->_urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } elseif (isset($this->urutan)) {
            $urutan = $this->_urutan;
            $this->db->order_by(key($urutan), $urutan[key($urutan)]);
        }
    }

    function ambil($nomr)
    {
        $this->tabel($nomr);
        if (isset($_POST['length']) && $_POST['length'] < 1) {
            $_POST['length'] = '10';
        } else {
            $_POST['length'] = $_POST['length'];
        }

        if (isset($_POST['start']) && $_POST['start'] > 1) {
            $_POST['start'] = $_POST['start'];
        }

        $this->db->limit($_POST['length'], $_POST['start']);
        // print_r($_POST);die;
        $query = $this->db->get();
        // print_r($this->db->last_query()); exit;
        return $query->result();
    }

    function hitung_tersaring($nomr)
    {
        $this->tabel($nomr);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function hitung_semua($nomr)
    {
        $this->tabel($nomr);
        return $this->db->count_all_results();
    }

    function detail($id)
    {
        $this->db->select(
            'kr.id, kr.tanggal, kr.anamnesis, kr.pemeriksaan_fisik, kr.tata_laksana, kr.evaluasi, kr.diagnosis,
            kr.akibat_kerja, kr.keterangan_akibat_kerja'
        );
        $this->db->from($this->_table);
        $this->db->where($this->_primary_key, $id);
        $query = $this->db->get();
        return $query->row_array();
    }
}

/* End of file KlaimRehabModel.php */
/* Location: ./application/models/rehabilitasiMedik/klaimRehab/KlaimRehabModel.php */