<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPraOperasiModel extends MY_Model
{
    protected $_table = 'medis.tb_pengkajian_operasi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }
    public function rules()
    {
        return [
            [
                'field' => 'keluhan_utama',
                'label' => 'Keluhan utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_penyakit_sekarang',
                'label' => 'Riwayat penyakit sekarang',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_guna_obat',
                'label' => 'Riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'alergi',
                'label' => 'Riwayat alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'psikologis',
                'label' => 'Psikologis',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'hubungan_anggota_keluarga',
                'label' => 'Hubungan dengan anggota keluarga',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'pencari_nafkah_utama',
                'label' => 'Pencari nafkah utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'tinggal_serumah_dengan',
                'label' => 'Tinggal serumah dengan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'kesadaran',
                'label' => 'Kesadaran',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_fisik',
                'label' => 'Pemeriksaan fisik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_penunjang',
                'label' => 'Pemeriksaan penunjang/diagnostik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'diagnosis_pra_operasi',
                'label' => 'Diagnosis pra operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'indikasi_operasi',
                'label' => 'Indikasi operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'rencana_tindakan_operasi',
                'label' => 'Rencana tindakan operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'perkiraan_lama_operasi',
                'label' => 'Perkiraan lama operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_darah',
                'label' => 'Persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_alatkhusus',
                'label' => 'Persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesSimpan()
    {
        return [
            [
                'field' => 'nokun',
                'label' => 'Nomor kunjungan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'ruang_tujuan',
                'label' => 'Ruang tujuan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPenggunaanObat()
    {
        return [
            [
                'field' => 'desk_riwayat_guna_obat',
                'label' => 'Keterangan riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesAlergi()
    {
        return [
            [
                'field' => 'isi_alergi[]',
                'label' => 'Sebutkan riwayat alergi',
                'rules' => 'trim|required|max_length[50]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 50 karakter',
                ]
            ],
            [
                'field' => 'reaksi_alergi',
                'label' => 'Reaksi alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPsikologis()
    {
        return [
            [
                'field' => 'desk_psikologis',
                'label' => 'Sebutkan psikologis lainnya',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanDarah()
    {
        return [
            [
                'field' => 'ket_persiapan_darah',
                'label' => 'Keterangan persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanAlatKhusus()
    {
        return [
            [
                'field' => 'ket_persiapan_alatkhusus',
                'label' => 'Keterangan persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_pengkajian_operasi.id', $id);
        $this->db->update($this->_table, $data);
    }

    public function ambil($nokun)
    {
        $this->db->select('diagnosis_pra_operasi, rencana_tindakan_operasi, perkiraan_lama_operasi');
        $this->db->from($this->_table);
        $this->db->where('nokun', $nokun);
        $this->db->order_by('id', 'desc');
        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Method untuk menghitung jumlah operasi berdasarkan tanggal
     * Untuk ruang operasi ID=16 (Operasi Swasta Gedung C)
     */
    public function getJumlahOperasi($tanggal)
    {
        $this->db->select('COUNT(*) as jumlah');
        $this->db->from('remun_medis.perjanjian');
        $this->db->where('ID_RUANGAN', '105090104');
        $this->db->where('STATUS !=', '0');
        $this->db->where('TANGGAL', $tanggal);
        $query = $this->db->get();
        
        $result = $query->row_array();
        return $result['jumlah'] ?? 0;
    }

    /**
     * Method untuk mengecek apakah pasien sudah memiliki pendaftaran operasi pada tanggal tertentu
     * @param string $norm Nomor rekam medis pasien
     * @param string $tanggal Tanggal yang akan dicek
     * @return array|null Return data pasien jika sudah ada pendaftaran, null jika belum ada
     */
    public function cekPendaftaranOperasiByTanggal($norm, $tanggal)
    {
        $this->db->select('a.NOMR, a.TANGGAL, master.getNamaLengkap(a.NOMR) AS nama_pasien');
        $this->db->from('remun_medis.perjanjian a');
        $this->db->where('a.NOMR', $norm);
        $this->db->where('a.TANGGAL', $tanggal);
        $this->db->where('a.RENCANA', '11');
        $this->db->where('a.STATUS !=', '0');
        $query = $this->db->get();
        
        return $query->row_array();
    }

    /**
     * Method untuk mengambil detail operasi dengan server-side processing
     * JOIN antara remun_medis.perjanjian dan perjanjian.penjadwalan_operasi
     */
    public function getDetailOperasi($tanggal, $start = 0, $length = 10, $search = '')
    {
        // Base query untuk total records
        $this->db->select("
        a.TINDAKAN,        
        db_rekammedis.getNamaLengkapDokter(a.ID_DOKTER) AS nama_dokter,
        b.waktu_operasi AS waktu_mulai,
        ADDTIME(
            STR_TO_DATE(b.waktu_operasi, '%H:%i'),
            SEC_TO_TIME(CAST(b.durasi_operasi AS UNSIGNED) * 60)
        ) AS waktu_selesai", false); 
        $this->db->from('remun_medis.perjanjian a');
        $this->db->join('perjanjian.penjadwalan_operasi b', 'a.ID_WAITING_LIST_OPERASI = b.id_waiting_list_operasi', 'left');
        $this->db->where('a.ID_RUANGAN', '105090104');
        $this->db->where('a.STATUS !=', '0');
        $this->db->where('b.STATUS !=', '0');
        $this->db->where('a.TANGGAL', $tanggal);

        // Apply search filter if provided
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('a.TINDAKAN', $search);
            $this->db->or_like('db_rekammedis.getNamaLengkapDokter(a.ID_DOKTER)', $search);
            $this->db->or_like('b.waktu_operasi', $search);
            $this->db->group_end();
        }

        // Get total filtered records
        $total_filtered = $this->db->count_all_results('', FALSE);

        // Apply pagination
        $this->db->limit($length, $start);
        $this->db->order_by('b.waktu_operasi', 'ASC');
        
        $query = $this->db->get();
        $result = $query->result_array();

        // Get total records without filter
        $this->db->select('COUNT(*) as total');
        $this->db->from('remun_medis.perjanjian a');
        $this->db->join('perjanjian.penjadwalan_operasi b', 'a.ID_WAITING_LIST_OPERASI = b.id_waiting_list_operasi', 'left');
        $this->db->where('a.ID_RUANGAN', '105090104');
        $this->db->where('a.STATUS !=', '0');
        $this->db->where('b.STATUS !=', '0');
        $this->db->where('a.TANGGAL', $tanggal);
        $total_query = $this->db->get();
        $total_records = $total_query->row_array()['total'];

        // Process data untuk menghitung waktu selesai operasi
        $data = [];
        $no = $start + 1;
        foreach ($result as $row) {
            
            $data[] = [
                'no' => $no++,
                'waktu_mulai' => $row['waktu_mulai'],
                'waktu_selesai' => $row['waktu_selesai'],
                'tindakan' => $row['TINDAKAN'] ?? '-',
                'dokter' => $row['nama_dokter'] ?? '-',
                'ruangan' => 'Instalasi Bedah Gedung C'
            ];
        }

        return [
            'data' => $data,
            'recordsTotal' => $total_records,
            'recordsFiltered' => $total_filtered
        ];
    }
}

/* End of file PengkajianPraOperasiModel.php */
/* Location: ./application/models/operasi/PengkajianPraOperasiModel.php */
