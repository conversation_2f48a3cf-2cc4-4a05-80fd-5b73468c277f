<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LTBG extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/brakhiterapi/LTBGModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_ltbg = $this->uri->segment(3);
      $getLTBG = $this->LTBGModel->getLTBG($id_ltbg);
      
      $data = array(
        'nokun' => $nokun,
        'id_ltbg' => $id_ltbg,
        'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
        'listTeknikRadiasi' => $this->masterModel->referensi(1390),
        'listFraksi' => $this->masterModel->referensi(1391),
        'listTeknikAnestesi' => $this->masterModel->referensi(1368),
        'listSondeUterus' => $this->masterModel->referensi(1392),
        'listAplikator' => $this->masterModel->referensi(1393),
        'listPencitraan' => $this->masterModel->referensi(1394),
        'listTitikReferensi' => $this->masterModel->referensi(1395),
        'listPenyulit' => $this->masterModel->referensi(1396),
        'listPascaPengangkatan' => $this->masterModel->referensi(1397),
        'listOvoid' => $this->masterModel->referensi(1694),
        'listDr' => $this->masterModel->listDr(),
        'listRadiografer' => $this->masterModel->listRadiografer(),
        'listFisikaMedis' => $this->masterModel->listFisikaMedis(),
        'listPerawat' => $this->masterModel->listPerawat(),
        'getLTBG' => $getLTBG,
      );
      $this->load->view('rekam_medis/rawat_inap/brakhiterapi/ltbg', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();
         //echo "<pre>";print_r($post['id_cpis']);echo "</pre>";
          
          $dataLTBG = array(
            'id' => isset($post['id_ltbg']) ? $post['id_ltbg']: "",
            'nokun' => $post['nokun'],
            'tanggal' => isset($post['tanggal']) ? date('Y-m-d',strtotime($post['tanggal'])) : "",
            'jam' => isset($post['jam']) ? $post['jam'] : "",
            'diagnosis' => isset($post['diagnosis']) ? $post['diagnosis'] : "",
            'dosis1' => isset($post['dosis1']) ? $post['dosis1'] : "",
            'dosis2' => isset($post['dosis2']) ? $post['dosis2'] : "",
            'teknik_radiasi' => isset($post['teknik_radiasi']) ? $post['teknik_radiasi'] : "",
            'fraksi' => isset($post['fraksi']) ? $post['fraksi'] : "",
            'teknik_anestesi' => isset($post['teknik_anestesi']) ? $post['teknik_anestesi'] : "",
            'teknik_anestesi_lainnya' => isset($post['teknik_anestesi_lainnya']) ? $post['teknik_anestesi_lainnya'] : "",
            'inspeksi' => isset($post['inspeksi']) ? $post['inspeksi'] : "",
            'vagina_touche' => isset($post['vagina_touche']) ? $post['vagina_touche'] : "",
            'inspekulo' => isset($post['inspekulo']) ? $post['inspekulo'] : "",
            'sonde_uterus' => isset($post['sonde_uterus']) ? $post['sonde_uterus'] : "",
            'ukuran_sonde_uterus' => isset($post['ukuran_sonde_uterus']) ? $post['ukuran_sonde_uterus'] : "",
            'aplikator' => isset($post['aplikator']) ? json_encode($post['aplikator']) : "",
            'ukuran_intra' => isset($post['ukuran_intra']) ? $post['ukuran_intra'] : "",
            'ovoid' => isset($post['ovoid']) ? json_encode($post['ovoid']) : "",
            'ukuran_cylinder' => isset($post['ukuran_cylinder']) ? $post['ukuran_cylinder'] : "",
            'ukuran_diameter' => isset($post['ukuran_diameter']) ? $post['ukuran_diameter'] : "",
            'jml_diameter' => isset($post['jml_diameter']) ? $post['jml_diameter'] : "",
            'panjang_diameter' => isset($post['panjang_diameter']) ? $post['panjang_diameter'] : "",
            'instruksi_khusus' => isset($post['instruksi_khusus']) ? $post['instruksi_khusus'] : "",
            'membuat_pencitraan' => isset($post['membuat_pencitraan']) ? json_encode($post['membuat_pencitraan']) : "",
            'jenis_radioaktif' => isset($post['jenis_radioaktif']) ? $post['jenis_radioaktif'] : "",
            'titik_referensi' => isset($post['titik_referensi']) ? json_encode($post['titik_referensi']) : "",
            'titikreferensi_lain' => isset($post['titikreferensi_lain']) ? $post['titikreferensi_lain'] : "",
            'preskripsi_dosis' => isset($post['preskripsi_dosis']) ? $post['preskripsi_dosis'] : "",
            'hrctv' => isset($post['hrctv']) ? $post['hrctv'] : "",
            'irctv' => isset($post['irctv']) ? $post['irctv'] : "",
            'bladderd90' => isset($post['bladderd90']) ? $post['bladderd90'] : "",
            'bladder' => isset($post['bladder']) ? $post['bladder'] : "",
            'rectum' => isset($post['rectum']) ? $post['rectum'] : "",
            'rectumd90' => isset($post['rectumd90']) ? $post['rectumd90'] : "",
            'rectumd2cc' => isset($post['rectumd2cc']) ? $post['rectumd2cc'] : "",
            'bladderd2cc' => isset($post['bladderd2cc']) ? $post['bladderd2cc'] : "",
            'waktu_penyinaran' => isset($post['waktu_penyinaran']) ? $post['waktu_penyinaran'] : "",
            'waktu_pengangkatan' => isset($post['waktu_pengangkatan']) ? $post['waktu_pengangkatan'] : "",
            'penyulit' => isset($post['penyulit']) ? $post['penyulit'] : "",
            'pasca_pengangkatan' => isset($post['pasca_pengangkatan']) ? $post['pasca_pengangkatan'] : "",
            'dokter_pelaksana' => isset($post['dokter_pelaksana']) ? $post['dokter_pelaksana'] : "",
            'fisikawan' => isset($post['fisikawan']) ? $post['fisikawan'] : "",
            'radiografer' => isset($post['radiografer']) ? $post['radiografer'] : "",
            'perawat' => isset($post['perawat']) ? $post['perawat'] : "",
            'oleh' => $this->session->userdata('id'),
            'created_at' => isset($post['backDateLtbgRi']) ? date('Y-m-d H:i:s', strtotime($post['tglBackDateLtbgRi'])) : date("Y-m-d H:i:s"),
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_ltbg'])) {
            $this->db->replace('keperawatan.tb_ltbg', $dataLTBG);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_ubah');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_ltbg', $dataLTBG);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->LTBGModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->LTBGModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editLTBG" data-toggle="modal" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> created_at;
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->LTBGModel->total_count(),
            "recordsFiltered"   => $this->LTBGModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}