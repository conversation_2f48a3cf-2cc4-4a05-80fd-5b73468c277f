<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DashboardKeuangan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('keuanganModel','masterModel'));
  }

  public function index()
  {
    $dashboard        = $this->keuanganModel->dashboard();
    // $dashboardRuangan = $this->keuanganModel->dashboardRuangan();
    $realisasi1            = $this->keuanganModel->realisasi1();
    $realisasi2            = $this->keuanganModel->realisasi2();
    $realisasi3            = $this->keuanganModel->realisasi3();
    $realisasi4            = $this->keuanganModel->realisasi4();
    $realisasiRm           = $this->keuanganModel->alokasiRealisasi(1);
    $sumrealisasiRm        = $this->keuanganModel->sumAlokasiRealisasi(1);
    $realisasiBlu          = $this->keuanganModel->alokasiRealisasi(2);
    $sumrealisasiBlu       = $this->keuanganModel->sumAlokasiRealisasi(2);
    $realisasiRm2018_2019  = $this->keuanganModel->alokasiRealisasi2018_2019(1);
    $realisasiBlu2018_2019 = $this->keuanganModel->alokasiRealisasi2018_2019(2);

    // echo "<pre>";print_r($realisasi1);exit();
    $data = array(
      'title'            => 'Halaman Sistem Informasi Manajemen Anggaran',
      'isi'              => 'Keuangan/dashboard/index',
      'dashboard'        => $dashboard,
      // 'dashboardRuangan' => $dashboardRuangan,
      'realisasi1'      => $realisasi1,
      'realisasi2'      => $realisasi2,
      'realisasi3'      => $realisasi3,
      'realisasi4'      => $realisasi4,
      'realisasiRm'     => $realisasiRm,
      'realisasiBlu'    => $realisasiBlu,
      'sumrealisasiRm'  => $sumrealisasiRm,
      'sumrealisasiBlu' => $sumrealisasiBlu,
      'realisasiRm2018_2019' => $realisasiRm2018_2019,
      'realisasiBlu2018_2019' => $realisasiBlu2018_2019,
    );
    // echo "<pre>";print_r($data);exit();
    $this->load->view('layout/wrapper',$data);
  }


}

/* End of file DashboardKeuangan.php */
/* Location: ./application/controllers/keuangan/DashboardKeuangan.php */

