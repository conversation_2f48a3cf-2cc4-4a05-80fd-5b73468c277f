<?php
defined('BASEPATH') or exit('No direct script access allowed');

class UploadRmModel extends CI_Model
{

  public function insertRm($data)
  {
    $this->db->insert('db_master.tb_upload_file_mr', $data);
  }

  public function listUpload()
  {
    $query = $this->db->query("
      SELECT tuf.*, master.getNamaLengkapPegawai(ap.NIP) PETUGAS_UPLOAD, master.getNamaLengkap(mp.NORM) NAMA_PASIEN
      FROM db_master.tb_upload_file_mr tuf
        LEFT JOIN aplikasi.pengguna ap ON ap.ID = tuf.OLEH
        LEFT JOIN master.pasien mp ON mp.NORM = tuf.NORM
      ORDER BY tuf.ID DESC
    ");

    return $query->result_array();
  }

  public function get_listUpload($nomr)
  {
    $query = $this->db->query("
      SELECT tuf.*, master.getNamaLengkapPegawai(ap.NIP) PETUGAS_UPLOAD, master.getNamaLengkap(mp.NORM) NAMA_PASIEN,dv.variabel BERKAS
        FROM db_master.tb_upload_file_mr tuf
          LEFT JOIN aplikasi.pengguna ap ON ap.ID = tuf.OLEH
          LEFT JOIN master.pasien mp ON mp.NORM = tuf.NORM
          LEFT JOIN db_master.variabel dv ON dv.id_variabel = tuf.JENIS_BERKAS
      WHERE tuf.NORM = '$nomr'
      ORDER BY tuf.ID DESC
    ");

    return $query->result_array();
  }

  public function tabelVBPJS($nomr)
  {
    $this->db->select('id, norm, master.getNamaLengkap(norm) nama, nobpjs, jenis_data, file');
    $this->db->from('bpjs.uploads_bpjs');
    $this->db->where('status !=', 0);
    $this->db->where('norm', $nomr);
    $this->db->order_by('id');
    return $this->db->get();
  }

  public function listVBPJS()
  {
    $query = $this->db->query("
      SELECT ub.id, ub.norm, master.getNamaLengkap(ub.norm) nama, ub.tanggal, ub.nobpjs, file
      FROM bpjs.uploads_bpjs ub
    ");

    return $query->result_array();
  }

  public function downloadFile($id)
  {
    $query = $this->db->query("
      SELECT *
      FROM db_master.tb_upload_file_mr tuf
      WHERE tuf.ID = '$id'
    ");
    return $query->row_array();
  }

  public function downloadFileVBPJS($id)
  {
    $query = $this->db->query("SELECT * FROM bpjs.uploads_bpjs ub WHERE ub.id = '$id'");
    return $query->row_array();
  }
}

/* End of file UploadRmModel.php */
/* Location: ./application/models/UploadRmModel.php */