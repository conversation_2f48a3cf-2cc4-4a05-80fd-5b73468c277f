<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SkalaBradenModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_skala_braden';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib Angka',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  function replace($data)
  {
    $this->db->replace('keperawatan.tb_skala_braden', $data);
  }

  function history($nokun)
  {
    $this->db->select(
      "s.id, s.tanggal, ps.variabel persepsi_sensori, k.variabel kelembaban, a.variabel aktivitas, m.variabel mobilitas,
      n.variabel nutrisi, g.variabel gesekan, s.naikkan_risiko, s.skor, s.tingkat,
      IF(s.tingkat = 2, 'Tinggi', IF(s.tingkat = 1, 'Sedang', 'Rendah')) ket_tingkat,
      master.getNamaLengkapPegawai(peng.NIP) pengisi"
    );
    $this->db->from('keperawatan.tb_skala_braden s');
    $this->db->join('db_master.variabel ps', 'ps.id_variabel = s.persepsi_sensori', 'left');
    $this->db->join('db_master.variabel k', 'k.id_variabel = s.kelembaban', 'left');
    $this->db->join('db_master.variabel a', 'a.id_variabel = s.aktivitas', 'left');
    $this->db->join('db_master.variabel m', 'm.id_variabel = s.mobilitas', 'left');
    $this->db->join('db_master.variabel n', 'n.id_variabel = s.nutrisi', 'left');
    $this->db->join('db_master.variabel g', 'g.id_variabel = s.gesekan', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = s.nokun', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = s.oleh', 'left');
    $this->db->where('s.status', 1);
    $this->db->where('s.nokun', $nokun);
    $this->db->order_by('s.tanggal', 'DESC');

    $query = $this->db->get();
    return $query->result_array();
  }

  function detail($id)
  {
    $this->db->select(
      's.id, s.tanggal, s.persepsi_sensori, s.kelembaban, s.aktivitas, s.mobilitas, s.nutrisi, s.gesekan,
      s.naikkan_risiko, s.skor, s.tingkat'
    );
    $this->db->from('keperawatan.tb_skala_braden s');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = s.nokun', 'left');
    $this->db->where('s.id', $id);
    $this->db->order_by('s.updated_at', 'DESC');

    $query = $this->db->get();
    return $query->result_array();
  }

  function jumlah($nokun)
  {
    $this->db->select('s.id');
    $this->db->from('keperawatan.tb_skala_braden s');
    $this->db->where('s.status', 1);
    $this->db->where('s.nokun', $nokun);
    $query = $this->db->get();
    return $query->num_rows();
  }
}
