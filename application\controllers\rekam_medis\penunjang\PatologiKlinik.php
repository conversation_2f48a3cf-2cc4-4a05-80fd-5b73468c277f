<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PatologiKlinik extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            array(
                'masterModel',
                'pengkajianAwalModel',
                'rekam_medis/penunjang/patologi_klinik/PatologiKlinikModel'
            )
        );
        $this->load->library('whatsapp');
    }

    public function getRuanganPK(){
        $id = $this->input->post('ID_DOKTER',TRUE);
        $penjamin = $this->input->post('PENJAMIN',TRUE) == 2 ? 1 : 2;
        $data = $this->PatologiKlinikModel->getRuanganPK($id,$penjamin)->row_array();
        echo json_encode($data);
    }

    public function action($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($param == 'tambah' || $param == 'ubah') {
                $this->db->trans_begin();
                $post = $this->input->post();
                // echo '<pre>';print_r($post);exit();

                // Mulai simpan
                $status = 1;
                $oleh = $this->session->userdata('id');
                $tanggal = isset($post['tanggal']) ? $post['tanggal'] : date('Y-m-d H:i:s');
                $ruangAwal = isset($post['ruang_awal']) ? $post['ruang_awal'] : null;
                $kode = $this->pengkajianAwalModel->generateNoOrderLab($ruangAwal, $tanggal);
                $tanggalFormatted = date('Y-m-d', strtotime($tanggal));
                $tanggalRencanaFormatted = isset($post['tanggal_rencana']) ? $post['tanggal_rencana'] : date('Y-m-d');


                if (isset($post['rencanaOrder']) && $post['rencanaOrder'] == 1) {
                    $data_order_rencana = [
                        'id_order_lab'      => $kode,
                        'tanggal_order'     => $tanggal,
                        'tanggal_rencana'   => $tanggalRencanaFormatted,
                        'updated_by'        => $oleh
                    ];
                    // echo '<pre>';print_r($data_order_rencana);
                    $this->db->insert('layanan.order_rencana_lab', $data_order_rencana);
                    $status = 0;
                    $oleh = '1907';
                     //awal whatsapp
                    $data_pasien = $this->pengkajianAwalModel->getNomr($post['kunjungan']);

                    $dataPerjanjian = [
                        'NOMR'          => $data_pasien['NORM'],
                        'NAMAPASIEN'    => $data_pasien['NAMA_PASIEN'],
                        'ID_DOKTER'     => $post['dokter_perujuk'],
                        // 'ID_RUANGAN'    => ($post['ruangPerjanjian'] == 0 && $post['penjamin_pasien'] == 2) ? "105020705" : ($post['ruangPerjanjian'] == 0 && $post['penjamin_pasien'] != 2) ? "105021201" : $post['ruangPerjanjian'],
                        'ID_RUANGAN'    => 105070101,
                        'TANGGAL'       => isset($post['tanggal_rencana']) ? $post['tanggal_rencana'] : date('Y-m-d'),
                        'NOMOR'         => $data_pasien['NOTLPN'],
                        'RENCANA'       => 4,
                        'OLEH'          => 2808,
                        'STATUS'        => 1
                    ];

                    // Cek apakah sudah ada data dengan NOMR, TANGGAL yang sama dan RENCANA = 1
                    $this->db->where('NOMR', $data_pasien['NORM']);
                    $this->db->where('TANGGAL', isset($post['tanggal_rencana']) ? $post['tanggal_rencana'] : date('Y-m-d'));
                    $this->db->where('RENCANA', 1);
                    $this->db->where('STATUS', 1);
                    $existing_perjanjian = $this->db->get('remun_medis.perjanjian');

                    // Hanya insert jika belum ada data yang sama
                    if ($existing_perjanjian->num_rows() == 0) {
                        $this->db->insert('remun_medis.perjanjian', $dataPerjanjian);
                    }

                    $nomorwa = '+62'.substr(trim($data_pasien['NOTLPN']), 1) ?? '';
                    if (!empty($nomorwa)) {
                        $pembukaan = "kami sampaikan Informasi Rencana Pemeriksaan Laboratorium di rawat inap";
                        $selamat = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")));
                        $nomr = $data_pasien['NORM'];
                        $tanggalrencana = isset($post['tanggal_rencana']) ? $post['tanggal_rencana'] : date('Y-m-d');
                        // Format tanggal ke bahasa Indonesia untuk WhatsApp
                        $tanggalrencana_indo = longdate_indo($tanggalrencana);
                        $isilabpk = isset($post['isiLabPk']) ? $post['isiLabPk'] : '';
                        if (!empty($isilabpk)) {
                            preg_match_all('/<li>(.*?)<\/li>/', $isilabpk, $matches);
                            $items = $matches[1] ?? []; // Ambil isi dalam <li> jika ada
                            $isilabpk = !empty($items) ? implode(", ", $items) : null;
                        } else {
                            $isilabpk = null;
                        }
                        $pesan = "Pasien dengan MR *$nomr*, harap untuk datang pada $tanggalrencana_indo mulai pukul 08.00 s.d. 13.00 wib, dengan terlebih dahulu melakukan finger scan/Scan Jari serta mencetak surat registrasi di kiosk/loket pendaftaran, kemudian *menuju ke counter C laboratorium Patologi Klinik untuk pengambilan sampel atas pemeriksaan: $isilabpk*.";
                        try{
                            $this->whatsapp->send($nomorwa, array($selamat,$pembukaan,$pesan));
                        } catch (Exception $e) {

                        }
                    }
                }

                $dataOrderLab = array(
                    'NOMOR' => $kode,
                    'KUNJUNGAN' => isset($post['kunjungan']) ? $post['kunjungan'] : null,
                    'TANGGAL' => $tanggal,
                    'DOKTER_ASAL' => isset($post['dokter_perujuk']) ? $post['dokter_perujuk'] : null,
                    'TUJUAN' => isset($post['tujuan']) ? $post['tujuan'] : null,
                    'CITO' => isset($post['cito']) ? $post['cito'] : 0,
                    'OLEH' => $oleh,
                    'ALASAN' => isset($post['alasan']) ? $post['alasan'] : null,
                    'TANGGAL_RENCANA' => isset($post['tanggal_rencana']) ? $post['tanggal_rencana'] : date('Y-m-d'),
                    'JENIS' => 2,
                    'STATUS' => $status
                );
                // echo '<pre>';print_r($dataOrderLab);

                $dataDetailOrderLab = array();
                $index = 0;
                $id = array();
                if (isset($post['tindakanLab'])) {
                    $arrayTindakan = array_values(array_diff($post['tindakanLab'], ['0-128', '0-129']));
                    // echo '<pre>';print_r($arrayTindakan);exit();
                    foreach ($arrayTindakan as $input) {
                        if ($arrayTindakan[$index] != null && !in_array(($arrayTindakan[$index]), $id)) {
                            $tindakan[$index] = explode('-', ($arrayTindakan[$index]));
                            array_push($id, $arrayTindakan[$index]);
                            array_push(
                                $dataDetailOrderLab,
                                array(
                                    'ORDER_ID' => $kode,
                                    'TINDAKAN' => $tindakan[$index][0],
                                    'REF' => null,
                                    'REF_TINDAKAN_EMR' => $tindakan[$index][1],
                                    'DESKRIPSI' => $post['bahan'][$index] ?? null,
                                )
                            );
                        }
                        $index++;
                    }
                }
                // echo '<pre>';print_r($dataDetailOrderLab);

                $this->db->insert('layanan.order_lab', $dataOrderLab);
                $this->db->insert_batch('layanan.order_detil_lab', $dataDetailOrderLab);
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            } elseif ($param == 'batal') {
                $data = $this->pengkajianAwalModel->cekStatusPK();
                if ($data['STATUS'] == 1) {
                    $this->db->set('STATUS', 0);
                    $this->db->where('NOMOR', $this->input->post('id'));
                    $this->db->update('layanan.order_lab');
                    $result = array('status' => 'success', 'pesan' => 'Berhasil Di Batalkan');
                } elseif ($data['STATUS'] == 2) {
                    $result = array('status' => 'failed', 'pesan' => 'Status Sudah Di Terima');
                } elseif ($data['STATUS'] == 0) {
                    $result = array('status' => 'failed', 'pesan' => 'Status Di Batalkan');
                }

                echo json_encode($result);
            }
        }
    }

    public function index()
    {
        $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
        //($pasien['ID_RUANGAN'] == '105140101' || $pasien['ID_RUANGAN'] == '105140102')
        if($pasien['ID_RUANGAN'] == '105140101' || $pasien['ID_RUANGAN'] == '105140102'){
            $cekLayananIGD = $this->PatologiKlinikModel->dataTindakanPenangananIGD($this->uri->segment(2))->row_array();
            $dataTindakanPatologiKlinik = array();
            $resultTindakanPatologiKlinikSub = $this->masterModel->tindakanPenunjangIGD('105070101', $cekLayananIGD['PENANGANAN_IGD']);
            $jenisTindakan = array();
            $jenisTindakan['id'] = 0;
            $jenisTindakan['tindakan'] = 0;
            $jenisTindakan['jenis'] = 'Tindakan Patologi Klinik [IGD]';
            $jenisTindakan['flag'] = 0;
            $subJenisTindakan = array();
            foreach ($resultTindakanPatologiKlinikSub->result() as $subTindakanPatologiKlinik) {
                $subSubJenisTindakan = array();
                $subSubJenisTindakan['id'] = $subTindakanPatologiKlinik->id;
                $subSubJenisTindakan['tindakan'] = $subTindakanPatologiKlinik->tindakan;
                $subSubJenisTindakan['jenis'] = $subTindakanPatologiKlinik->deskripsi;
                $subSubJenisTindakan['flag'] = $subTindakanPatologiKlinik->flag;
                $subJenisTindakan[] = $subSubJenisTindakan;
            }
            $jenisTindakan['subJenis'] = $subJenisTindakan;
            $dataTindakanPatologiKlinik[] = $jenisTindakan;
        }else{
            $resultTindakanPatologiKlinik = $this->masterModel->tindakanPenunjang('105070101');
            $dataTindakanPatologiKlinik = array();
            foreach ($resultTindakanPatologiKlinik->result() as $tindakanPatologiKlinik) {
                $resultTindakanPatologiKlinikSub = $this->masterModel->tindakanPenunjang('105070101', $tindakanPatologiKlinik->id);
                $jenisTindakan = array();
                if ($resultTindakanPatologiKlinikSub->num_rows() > 0) {
                    $jenisTindakan['id'] = $tindakanPatologiKlinik->id;
                    $jenisTindakan['tindakan'] = $tindakanPatologiKlinik->tindakan;
                    $jenisTindakan['jenis'] = $tindakanPatologiKlinik->deskripsi;
                    $jenisTindakan['flag'] = $tindakanPatologiKlinik->flag;
                    $subJenisTindakan = array();
                    foreach ($resultTindakanPatologiKlinikSub->result() as $subTindakanPatologiKlinik) {
                        $subSubJenisTindakan = array();
                        $resultTindakanPatologiKlinikSubSub = $this->masterModel->tindakanPenunjang('105070101', $subTindakanPatologiKlinik->id);
                        if ($resultTindakanPatologiKlinikSubSub->num_rows() > 0) {
                            $subSubSubJenisTindakan = array();
                            $subSubJenisTindakan['id'] = $subTindakanPatologiKlinik->id;
                            $subSubJenisTindakan['tindakan'] = $subTindakanPatologiKlinik->tindakan;
                            $subSubJenisTindakan['jenis'] = $subTindakanPatologiKlinik->deskripsi;
                            $subSubJenisTindakan['flag'] = $subTindakanPatologiKlinik->flag;
                            foreach ($resultTindakanPatologiKlinikSubSub->result() as $subSubTindakanPatologiKlinik) {
                                $subSubSubSubJenisTindakan = array();
                                $resultTindakanPatologiKlinikSubSubSub = $this->masterModel->tindakanPenunjang('105070101', $subSubTindakanPatologiKlinik->id);
                                if ($resultTindakanPatologiKlinikSubSubSub->num_rows() > 0) {
                                    $subSubSubSubSubJenisTindakan = array();
                                    $subSubSubSubJenisTindakan['id'] = $subSubTindakanPatologiKlinik->id;
                                    $subSubSubSubJenisTindakan['tindakan'] = $subSubTindakanPatologiKlinik->tindakan;
                                    $subSubSubSubJenisTindakan['jenis'] = $subSubTindakanPatologiKlinik->deskripsi;
                                    $subSubSubSubJenisTindakan['flag'] = $subSubTindakanPatologiKlinik->flag;
                                    foreach ($resultTindakanPatologiKlinikSubSubSub->result() as $subSubSubTindakanPatologiKlinik) {
                                        $subSubSubSubSubSubJenisTindakan = array();
                                        $subSubSubSubSubSubJenisTindakan['id'] = $subSubSubTindakanPatologiKlinik->id;
                                        $subSubSubSubSubSubJenisTindakan['tindakan'] = $subSubSubTindakanPatologiKlinik->tindakan;
                                        $subSubSubSubSubSubJenisTindakan['jenis'] = $subSubSubTindakanPatologiKlinik->deskripsi;
                                        $subSubSubSubSubSubJenisTindakan['flag'] = $subSubSubTindakanPatologiKlinik->flag;
                                        $subSubSubSubSubJenisTindakan[] = $subSubSubSubSubSubJenisTindakan;
                                    }
                                    $subSubSubSubJenisTindakan['subJenis'] = $subSubSubSubSubJenisTindakan;
                                } else {
                                    $subSubSubSubJenisTindakan['id'] = $subSubTindakanPatologiKlinik->id;
                                    $subSubSubSubJenisTindakan['tindakan'] = $subSubTindakanPatologiKlinik->tindakan;
                                    $subSubSubSubJenisTindakan['jenis'] = $subSubTindakanPatologiKlinik->deskripsi;
                                    $subSubSubSubJenisTindakan['flag'] = $subSubTindakanPatologiKlinik->flag;
                                }
                                $subSubSubJenisTindakan[] = $subSubSubSubJenisTindakan;
                            }
                            $subSubJenisTindakan['subJenis'] = $subSubSubJenisTindakan;
                        } else {
                            $subSubJenisTindakan['id'] = $subTindakanPatologiKlinik->id;
                            $subSubJenisTindakan['tindakan'] = $subTindakanPatologiKlinik->tindakan;
                            $subSubJenisTindakan['jenis'] = $subTindakanPatologiKlinik->deskripsi;
                            $subSubJenisTindakan['flag'] = $subTindakanPatologiKlinik->flag;
                        }
                        $subJenisTindakan[] = $subSubJenisTindakan;
                    }
                    $jenisTindakan['subJenis'] = $subJenisTindakan;
                } else {
                    $jenisTindakan['id'] = $tindakanPatologiKlinik->id;
                    $jenisTindakan['tindakan'] = $tindakanPatologiKlinik->tindakan;
                    $jenisTindakan['jenis'] = $tindakanPatologiKlinik->deskripsi;
                    $jenisTindakan['flag'] = $tindakanPatologiKlinik->flag;
                }
                $dataTindakanPatologiKlinik[] = $jenisTindakan;
            }
        }
        $data = array(
            'disabled' => null,
            'listDrUmum' => $this->masterModel->listDrUmum(),
            'pasien' => $pasien,
            'ruangPK' => $this->masterModel->ruangPK(),
            'dataTindakanPatologiKlinik' => $dataTindakanPatologiKlinik,
            'cekPenangananIGD' => $cekLayananIGD,
            'dataTindakanPenangananIGD' => $this->PatologiKlinikModel->dataTindakanPenangananIGD($this->uri->segment(2))->result_array(),
        );
        // echo '<pre>';print_r($dataTindakanPatologiKlinik);exit();
        $this->load->view('rekam_medis/penunjang/patologi_klinik/index', $data);
    }

    public function datatables()
    {
        $result = $this->PatologiKlinikModel->datatables();
        $i = 1;
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = $i;
            // $sub_array[] = $row->NAMA;
            $sub_array[] = $row->PARAMETER;
            $sub_array[] = $row->HASIL;
            $sub_array[] = $row->NILAI;
            $sub_array[] = $row->SATUAN;
            $sub_array[] = $row->KETERANGAN;
            $sub_array[] = $row->LIS_FLAG;

            $data[] = $sub_array;
            $i++;
        }

        $output = array(
            'draw' => intval($_POST['draw']),
            'recordsTotal' => $this->PatologiKlinikModel->total_count(),
            'recordsFiltered' => $this->PatologiKlinikModel->filter_count(),
            'data' => $data
        );
        echo json_encode($output);
    }

    public function datatablesKritis()
    {
        $result = $this->PatologiKlinikModel->datatables();
        $i = 1;
        $data = array();
        foreach ($result as $row) {
            // if($row->LIS_FLAG != "" && $row->LIS_FLAG != NULL){
                $sub_array = array();
                $sub_array[] = $i;
                // $sub_array[] = $row->NAMA;
                $sub_array[] = $row->PARAMETER;
                $sub_array[] = $row->HASIL;
                $sub_array[] = $row->NILAI;
                $sub_array[] = $row->SATUAN;
                $sub_array[] = $row->KETERANGAN;
                $sub_array[] = $row->LIS_FLAG;

                $data[] = $sub_array;
                $i++;
            // }
        }

        $output = array(
            'draw' => intval($_POST['draw']),
            'recordsTotal' => $this->PatologiKlinikModel->total_count(),
            'recordsFiltered' => $this->PatologiKlinikModel->filter_count(),
            'data' => $data
        );
        echo json_encode($output);
    }

    public function datatables_hasilLabPK()
    {
        $result = $this->PatologiKlinikModel->datatables();
        $i = 1;
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array[] = $i;
            // $sub_array[] = $row->NAMA;
            $sub_array[] = $row->PARAMETER;
            $sub_array[] = $row->HASIL;
            $sub_array[] = $row->NILAI;
            $sub_array[] = $row->SATUAN;
            $sub_array[] = $row->KETERANGAN;
            $sub_array[] = $row->LIS_FLAG;

            $data[] = $sub_array;
            $i++;
        }

        $output = array(
            'draw' => intval($_POST['draw']),
            'recordsTotal' => $this->PatologiKlinikModel->total_count(),
            'recordsFiltered' => $this->PatologiKlinikModel->filter_count(),
            'data' => $data
        );
        echo json_encode($output);
    }

    function simpanLogHasilLabKritis(){
        $this->db->trans_begin();
        $post = $this->input->post();

        $cekNokun = $this->PatologiKlinikModel->cekNokunLogKritis($post['nokun'])->num_rows();

        if($cekNokun == 0){
            $dataLogHasilLabKritis = [
                'norm'          => $post['norm'],
                'nokun'         => $post['nokun'],
                'tgl_lab'       => $post['tgl_lab'],
                'tgl_sampling'  => $post['tgl_sampling'],
                'tgl_hasil'     => $post['tgl_hasil'],
            ];
            // echo '<pre>';print_r($dataLogHasilLabKritis);
            $this->db->insert('log.log_hasil_lab_kritis', $dataLogHasilLabKritis);

            if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
            } else {
                $this->db->trans_commit();
                $result = array('status' => 'success');
            }

            echo json_encode($result);
        }
    }
}

// End of file PatologiKlinik.php
// Location: ./application/controllers/rekam_medis/penunjang/PatologiKlinik.php