<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPraSedasi extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            array(
                'masterModel',
                'pengkajianAwalModel',
                'sedasi/PengkajianPraSedasiModel',
                'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
            )
        );
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listKajianSistem = $this->masterModel->listKajianSistem();
        $listKajianSistem1 = $this->masterModel->listKajianSistem1();
        $listKajianSistem2 = $this->masterModel->listKajianSistem2();
        $listKajianSistem3 = $this->masterModel->listKajianSistem3();
        $listKajianSistem4 = $this->masterModel->listKajianSistem4();
        $listKajianSistem5 = $this->masterModel->listKajianSistem5();
        $listKajianSistem6 = $this->masterModel->listKajianSistem6();
        $listKajianSistem7 = $this->masterModel->listKajianSistem7();
        $listKajianSistem8 = $this->masterModel->listKajianSistem8();
        $listKajianSistem9 = $this->masterModel->listKajianSistem9();
        $listKajianSistem10 = $this->masterModel->listKajianSistem10();
        $listKajianSistem11 = $this->masterModel->listKajianSistem11();
        $listKajianSistem12 = $this->masterModel->listKajianSistem12();
        $listKajianSistem13 = $this->masterModel->listKajianSistem13();
        $listKajianSistem14 = $this->masterModel->listKajianSistem14();
        $listKajianSistem15 = $this->masterModel->listKajianSistem15();
        $listPemriksaanFisik = $this->masterModel->listPemriksaanFisik();
        $listPemriksaanFisik1 = $this->masterModel->listPemriksaanFisik1();
        $listPemriksaanFisik2 = $this->masterModel->listPemriksaanFisik2();
        $listPemriksaanFisik3 = $this->masterModel->listPemriksaanFisik3();
        $listPemriksaanFisik4 = $this->masterModel->listPemriksaanFisik4();
        $listPemriksaanFisik5 = $this->masterModel->listPemriksaanFisik5();
        $listPemriksaanFisik6 = $this->masterModel->listPemriksaanFisik6();
        $listPemriksaanPenunjang = $this->masterModel->listPemriksaanPenunjang();
        $listKlasifikasiAsa = $this->masterModel->listKlasifikasiAsa();
        $listMonitoring = $this->masterModel->listMonitoring();
        $listPerawatanPascaSedasi = $this->masterModel->listPerawatanPascaSedasi();
        $listAlergi = $this->masterModel->referensi(1023);
        $listMerokok = $this->masterModel->referensi(1024);
        $listAlkohol = $this->masterModel->referensi(1025);
        $listKopitehsoda = $this->masterModel->referensi(1026);
        $listOlahraga = $this->masterModel->referensi(1027);
        $listAspirin = $this->masterModel->referensi(1028);
        $listObatantisakit = $this->masterModel->referensi(1029);
        $listPerdarahan = $this->masterModel->referensi(1030);
        $listJantung = $this->masterModel->referensi(1031);
        $listPembekuan = $this->masterModel->referensi(1032);
        $listIramajantung = $this->masterModel->referensi(1033);
        $listPembiusan = $this->masterModel->referensi(1034);
        $listHipertensi = $this->masterModel->referensi(1035);
        $listDemamtinggi = $this->masterModel->referensi(1036);
        $listTuberkulosis = $this->masterModel->referensi(1037);
        $listDiabetes = $this->masterModel->referensi(1038);
        $listPenyakitberat = $this->masterModel->referensi(1039);
        $listPerdarahan_e = $this->masterModel->referensi(1040);
        $listJantung_e = $this->masterModel->referensi(1041);
        $listPembekuan_e = $this->masterModel->referensi(1042);
        $listIramajantung_e = $this->masterModel->referensi(1043);
        $listMaag_e = $this->masterModel->referensi(1044);
        $listAnemia_e = $this->masterModel->referensi(1045);
        $listAsma_e = $this->masterModel->referensi(1046);
        $listPingsan_e = $this->masterModel->referensi(1047);
        $listMengorok_e = $this->masterModel->referensi(1048);
        $listPembiusan_e = $this->masterModel->referensi(1049);
        $listHipertensi_e = $this->masterModel->referensi(1050);
        $listHepatitis_e = $this->masterModel->referensi(1051);
        $listKejang_e = $this->masterModel->referensi(1052);
        $listDemamtinggi_e = $this->masterModel->referensi(1053);
        $listPenyakitbawaan_e = $this->masterModel->referensi(1054);
        $listDiabetes_e = $this->masterModel->referensi(1055);
        $listPenyakitberat_e = $this->masterModel->referensi(1056);
        $listTransfusiDarah = $this->masterModel->referensi(1057);
        $listHiv = $this->masterModel->referensi(1058);
        $listPasienmemakai = $this->masterModel->referensi(1059);
        $listMenyusui = $this->masterModel->referensi(1060);
        //////// History Pra Sedasi ////////
        // $historyPengkajianPraSedasi = $this->pengkajianAwalModel->historyPengkajianPraSedasi($getNomr['NORM']);
        $listPengkajianPraSedasi = $this->pengkajianAwalModel->listPengkajianPraSedasi($getNomr['NORM']);
        //////// History Pasien Pra Sedasi ////////
        // $historyPasienPraSedasi = $this->pengkajianAwalModel->historyPasienPraSedasi($getNomr['NORM']);
        $cekPengPraAnes = $this->PengkajianPraSedasiModel->jumlah($getNomr['NORM']);

        $data = array(
            'nokun' => $nokun,
            'getNomr' => $getNomr,
            'cekPengPraAnes' => $cekPengPraAnes,
            'listKajianSistem' => $listKajianSistem,
            'listKajianSistem1' => $listKajianSistem1,
            'listKajianSistem2' => $listKajianSistem2,
            'listKajianSistem3' => $listKajianSistem3,
            'listKajianSistem4' => $listKajianSistem4,
            'listKajianSistem5' => $listKajianSistem5,
            'listKajianSistem6' => $listKajianSistem6,
            'listKajianSistem7' => $listKajianSistem7,
            'listKajianSistem8' => $listKajianSistem8,
            'listKajianSistem9' => $listKajianSistem9,
            'listKajianSistem10' => $listKajianSistem10,
            'listKajianSistem11' => $listKajianSistem11,
            'listKajianSistem12' => $listKajianSistem12,
            'listKajianSistem13' => $listKajianSistem13,
            'listKajianSistem14' => $listKajianSistem14,
            'listKajianSistem15' => $listKajianSistem15,
            'pilihanCPPT' => $this->masterModel->referensi(1407),
            'listPemriksaanFisik' => $listPemriksaanFisik,
            'listPemriksaanFisik1' => $listPemriksaanFisik1,
            'listPemriksaanFisik2' => $listPemriksaanFisik2,
            'listPemriksaanFisik3' => $listPemriksaanFisik3,
            'listPemriksaanFisik4' => $listPemriksaanFisik4,
            'listPemriksaanFisik5' => $listPemriksaanFisik5,
            'listPemriksaanFisik6' => $listPemriksaanFisik6,
            'listPemriksaanPenunjang' => $listPemriksaanPenunjang,
            'listKlasifikasiAsa' => $listKlasifikasiAsa,
            'teknikKhusus' => $this->masterModel->referensi(1747),
            'listMonitoring' => $listMonitoring,
            'pilihanAlatKhusus' => $this->masterModel->referensi(1748),
            'listPerawatanPascaSedasi' => $listPerawatanPascaSedasi,
            //For Pasien
            'listAlergi' => $listAlergi,
            'listMerokok' => $listMerokok,
            'listAlkohol' => $listAlkohol,
            'listKopitehsoda' => $listKopitehsoda,
            'listOlahraga' => $listOlahraga,
            'listAspirin' => $listAspirin,
            'listObatantisakit' => $listObatantisakit,
            'listPerdarahan' => $listPerdarahan,
            'listJantung' => $listJantung,
            'listPembekuan' => $listPembekuan,
            'listIramajantung' => $listIramajantung,
            'listPembiusan' => $listPembiusan,
            'listHipertensi' => $listHipertensi,
            'listDemamtinggi' => $listDemamtinggi,
            'listTuberkulosis' => $listTuberkulosis,
            'listDiabetes' => $listDiabetes,
            'listPenyakitberat' => $listPenyakitberat,
            'listPerdarahan_e' => $listPerdarahan_e,
            'listJantung_e' => $listJantung_e,
            'listPembekuan_e' => $listPembekuan_e,
            'listIramajantung_e' => $listIramajantung_e,
            'listMaag_e' => $listMaag_e,
            'listAnemia_e' => $listAnemia_e,
            'listAsma_e' => $listAsma_e,
            'listPingsan_e' => $listPingsan_e,
            'listMengorok_e' => $listMengorok_e,
            'listPembiusan_e' => $listPembiusan_e,
            'listHipertensi_e' => $listHipertensi_e,
            'listHepatitis_e' => $listHepatitis_e,
            'listKejang_e' => $listKejang_e,
            'listDemamtinggi_e' => $listDemamtinggi_e,
            'listPenyakitbawaan_e' => $listPenyakitbawaan_e,
            'listDiabetes_e' => $listDiabetes_e,
            'listPenyakitberat_e' => $listPenyakitberat_e,
            'listTransfusiDarah' => $listTransfusiDarah,
            'listHiv' => $listHiv,
            'listPasienmemakai' => $listPasienmemakai,
            'listMenyusui' => $listMenyusui,
            // 'historyPengkajianPraSedasi' => $historyPengkajianPraSedasi,
            'listPengkajianPraSedasi' => $listPengkajianPraSedasi,
            // 'historyPasienPraSedasi' => $historyPasienPraSedasi,
        );

        $this->load->view('Pengkajian/sedasi/pengkajianPraSedasi/index', $data);
    }

    //SIMPAN FORM PRA SEDASI
    public function simpanFormPengkajianPraSedasi()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $this->db->trans_begin();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $rules = $this->PengkajianPraSedasiModel->rules;
            $this->form_validation->set_rules($rules);
            if ($this->form_validation->run() == true) {
                $pengguna = isset($post['pengguna']) ? $this->session->userdata('id') : null;
                $id_pra = isset($post['id_pra']) ? $post['id_pra'] : null;
                $kunjungan = isset($post['nokun']) ? $post['nokun'] : null;
                $jenisPengkajian = isset($post['jenis']) ? $post['jenis'] : null;
                $kajiansistem = isset($post['kajiansistem']) ? $post['kajiansistem'] : null;
                $kso1 = isset($post['kso1']) ? $post['kso1'] : null;
                $kso2 = isset($post['kso2']) ? $post['kso2'] : null;
                $kso3 = isset($post['kso3']) ? $post['kso3'] : null;
                $kso4 = isset($post['kso4']) ? $post['kso4'] : null;
                $kso5 = isset($post['kso5']) ? $post['kso5'] : null;
                $kso6 = isset($post['kso6']) ? $post['kso6'] : null;
                $kso7 = isset($post['kso7']) ? $post['kso7'] : null;
                $kso8 = isset($post['kso8']) ? $post['kso8'] : null;
                $kso9 = isset($post['kso9']) ? $post['kso9'] : null;
                $kso10 = isset($post['kso10']) ? $post['kso10'] : null;
                $kso11 = isset($post['kso11']) ? $post['kso11'] : null;
                $kso12 = isset($post['kso12']) ? $post['kso12'] : null;
                $kso13 = isset($post['kso13']) ? $post['kso13'] : null;
                $kso14 = isset($post['kso14']) ? $post['kso14'] : null;
                $kso15 = isset($post['kso15']) ? $post['kso15'] : null;
                $kso16 = isset($post['kso16']) ? $post['kso16'] : null;
                $skor = isset($post['skor']) ? $post['skor'] : null;
                $gigipalsu = isset($post['gigipalsu']) ? $post['gigipalsu'] : null;
                $lpkfo1 = isset($post['lpkfo1']) ? $post['lpkfo1'] : null;
                $lpkfo2 = isset($post['lpkfo2']) ? $post['lpkfo2'] : null;
                $lpkfo3 = isset($post['lpkfo3']) ? $post['lpkfo3'] : null;
                $lpkfo4 = isset($post['lpkfo4']) ? $post['lpkfo4'] : null;
                $lpkfo5 = isset($post['lpkfo5']) ? $post['lpkfo5'] : null;
                $lpkfo6 = isset($post['lpkfo6']) ? $post['lpkfo6'] : null;
                $jelaskan2265 = isset($post['jelaskan2265']) ? $post['jelaskan2265'] : null;
                $jelaskan2266 = isset($post['jelaskan2266']) ? $post['jelaskan2266'] : null;
                $jelaskan2267 = isset($post['jelaskan2267']) ? $post['jelaskan2267'] : null;
                $jelaskan2268 = isset($post['jelaskan2268']) ? $post['jelaskan2268'] : null;
                $jelaskan2269 = isset($post['jelaskan2269']) ? $post['jelaskan2269'] : null;
                $jelaskan2270 = isset($post['jelaskan2270']) ? $post['jelaskan2270'] : null;
                $jelaskan2270 = isset($post['jelaskan2270']) ? $post['jelaskan2270'] : null;
                $keteranganPemeriksaanFisik = isset($post['keterangan_pemeriksaan_fisik']) ? $post['keterangan_pemeriksaan_fisik'] : null;
                $lppketerangan2273 = isset($post['lppketerangan2273']) ? $post['lppketerangan2273'] : null;
                $lppketerangan2276 = isset($post['lppketerangan2276']) ? $post['lppketerangan2276'] : null;
                $lppketerangan2279 = isset($post['lppketerangan2279']) ? $post['lppketerangan2279'] : null;
                $lppketerangan2282 = isset($post['lppketerangan2282']) ? $post['lppketerangan2282'] : null;
                $lppketerangan2274 = isset($post['lppketerangan2274']) ? $post['lppketerangan2274'] : null;
                $lppketerangan2277 = isset($post['lppketerangan2277']) ? $post['lppketerangan2277'] : null;
                $lppketerangan2280 = isset($post['lppketerangan2280']) ? $post['lppketerangan2280'] : null;
                $lppketerangan2283 = isset($post['lppketerangan2283']) ? $post['lppketerangan2283'] : null;
                $lppketerangan2275 = isset($post['lppketerangan2275']) ? $post['lppketerangan2275'] : null;
                $lppketerangan2278 = isset($post['lppketerangan2278']) ? $post['lppketerangan2278'] : null;
                $lppketerangan2281 = isset($post['lppketerangan2281']) ? $post['lppketerangan2281'] : null;
                $lppketerangan2284 = isset($post['lppketerangan2284']) ? $post['lppketerangan2284'] : null;
                $keteranganPemeriksaanPenunjang = isset($post['keterangan_pemeriksaan_penunjang']) ? $post['keterangan_pemeriksaan_penunjang'] : null;
                $diagnosis = isset($post['diagnosis']) ? json_encode($post['diagnosis']) : null;
                $klasifikasiasa = isset($post['klasifikasiasa']) ? json_encode($post['klasifikasiasa']) : null;
                $penyulitsedasi = isset($post['penyulitsedasi']) ? $post['penyulitsedasi'] : null;
                $penyulitsedasilain = isset($post['penyulitlain']) ? json_encode($post['penyulitlain']) : null;
                $catatantl = isset($post['catatantl']) ? $post['catatantl'] : null;
                $jelaskancatatantl = isset($post['jelaskancatatantl']) ? $post['jelaskancatatantl'] : null;
                $tekniksedasi = isset($post['tekniksedasi']) ? $post['tekniksedasi'] : null;
                $teknikGA = isset($post['teknik_ga']) ? $post['teknik_ga'] : null;
                $teknikRegional = isset($post['teknik_regional']) ? $post['teknik_regional'] : null;
                $tekniklainlain = isset($post['tekniklainlain']) ? $post['tekniklainlain'] : null;
                $teknikHipotensi = isset($post['teknik_hipotensi']) ? $post['teknik_hipotensi'] : null;
                $teknikKhusus = isset($post['teknik_khusus']) ? json_encode($post['teknik_khusus']) : null;
                $keteranganTeknikKhusus = isset($post['keterangan_teknik_khusus']) ? $post['keterangan_teknik_khusus'] : null;
                $monitoring = isset($post['monitoring']) ? json_encode($post['monitoring']) : null;
                $ketmonitoring2301 = isset($post['ketmonitoring2301']) ? $post['ketmonitoring2301'] : null;
                $ketmonitoring2306 = isset($post['ketmonitoring2306']) ? $post['ketmonitoring2306'] : null;
                $ketmonitoring2307 = isset($post['ketmonitoring2307']) ? $post['ketmonitoring2307'] : null;
                $ketmonitoring2309 = isset($post['ketmonitoring2309']) ? $post['ketmonitoring2309'] : null;
                $alatkhusus = isset($post['alatkhusus']) ? $post['alatkhusus'] : null;
                $pilihanAlatKhusus = isset($post['pilihan_alat_khusus']) ? json_encode($post['pilihan_alat_khusus']) : null;
                $jelaskanalatkhusus = isset($post['jelaskanalatkhusus']) ? $post['jelaskanalatkhusus'] : null;
                $rencanaperawatan = isset($post['rencanaperawatan']) ? $post['rencanaperawatan'] : null;
                $perawatanlain = isset($post['perawatanlain']) ? $post['perawatanlain'] : null;
                $puasamulai = isset($post['puasamulai']) ? $post['puasamulai'] : null;
                $premedikasi = isset($post['premedikasi']) ? $post['premedikasi'] : null;
                $transportasi = isset($post['transportasi']) ? $post['transportasi'] : null;
                $rencana = isset($post['rencana']) ? $post['rencana'] : null;
                $catatanpersiapan = isset($post['catatanpersiapan']) ? $post['catatanpersiapan'] : null;

                if ($id_pra == '') {
                    $dataMedis = array(
                        'nokun' => $kunjungan,
                        'jenis' => $jenisPengkajian,
                        'oleh' => $pengguna,
                    );
                    $id_pra = $this->pengkajianAwalModel->insertPraAnesSed($dataMedis);
                }

                $data = array(
                    'id_pra' => $id_pra,
                    'nokun' => $kunjungan,
                    'hilangnya_gigi' => $kso1,
                    'masalah_leher' => $kso2,
                    'denyut_jantung' => $kso3,
                    'batuk' => $kso4,
                    'sesak' => $kso5,
                    'menderita_infeksi' => $kso6,
                    'saluran_napas' => $kso7,
                    'sakit_dada' => $kso8,
                    'muntah' => $kso9,
                    'pingsan' => $kso10,
                    'stroke' => $kso11,
                    'kejang' => $kso12,
                    'sedang_hamil' => $kso13,
                    'kelainan_tulang' => $kso14,
                    'obesitas' => $kso15,
                    'keterangan_kajian_sistem' => $kso16,
                    'skor_mallampati' => $skor,
                    'gigi_palsu' => $gigipalsu,
                    'jantung' => $lpkfo1,
                    'jantung_jelaskan' => $jelaskan2265,
                    'paru_paru' => $lpkfo2,
                    'paru_paru_jelaskan' => $jelaskan2266,
                    'abdomen' => $lpkfo3,
                    'abdomen_jelaskan' => $jelaskan2267,
                    'tulang_belakang' => $lpkfo4,
                    'tulang_belakang_jelaskan' => $jelaskan2268,
                    'ekstremitas' => $lpkfo5,
                    'ekstremitas_jelaskan' => $jelaskan2269,
                    'neurologi' => $lpkfo6,
                    'neurologi_jelaskan' => $jelaskan2270,
                    'keterangan_pemeriksaan_fisik' => $keteranganPemeriksaanFisik,
                    'hbht' => $lppketerangan2273,
                    'glukosa' => $lppketerangan2274,
                    'naci' => $lppketerangan2275,
                    'leukosit' => $lppketerangan2276,
                    'tes_kehamilan' => $lppketerangan2277,
                    'ureum' => $lppketerangan2278,
                    'pt' => $lppketerangan2279,
                    'ekg' => $lppketerangan2280,
                    'kreatinin' => $lppketerangan2281,
                    'trombosit' => $lppketerangan2282,
                    'kallium' => $lppketerangan2283,
                    'rothorax' => $lppketerangan2284,
                    'keterangan_pemeriksaan_penunjang' => $keteranganPemeriksaanPenunjang,
                    'diagnosis' => $diagnosis,
                    'klasifikasi_asa' => $klasifikasiasa,
                    'penyulit_sedasi' => $penyulitsedasi,
                    'penyulit_sedasi_lain' => $penyulitsedasilain,
                    'tindak_lanjut' => $catatantl,
                    'tindak_lanjut_jelaskan' => $jelaskancatatantl,
                    'teknik_sedasi' => $tekniksedasi,
                    'teknik_ga' => $teknikGA,
                    'teknik_regional' => $teknikRegional,
                    'teknik_lain_lain' => $tekniklainlain,
                    'teknik_hipotensi' => $teknikHipotensi,
                    'teknik_khusus' => $teknikKhusus,
                    'keterangan_teknik_khusus' => $keteranganTeknikKhusus,
                    'monitoring' => $monitoring,
                    'ekg_lead_ket' => $ketmonitoring2301,
                    'cvp_ket' => $ketmonitoring2306,
                    'arteri_line_ket' => $ketmonitoring2307,
                    'monitoring_lain' => $ketmonitoring2309,
                    'alat_khusus' => $alatkhusus,
                    'pilihan_alat_khusus' => $pilihanAlatKhusus,
                    'alat_khusus_sebutkan' => $jelaskanalatkhusus,
                    'pasca_sedasi' => $rencanaperawatan,
                    'pasca_sedasi_lain' => $perawatanlain,
                    'puasa_mulai' => $puasamulai,
                    'pre_medikasi' => $premedikasi,
                    'transportasi' => $transportasi,
                    'rencana' => $rencana,
                    'catatan_persiapan' => $catatanpersiapan,
                    'oleh' => $pengguna,
                );

                // echo '<pre>';print_r($data);exit();

                if ($jenisPengkajian == 1) {
                    $hasilPengkajian = 18;
                } elseif ($jenisPengkajian == 2) {
                    $hasilPengkajian = 17;
                } else {
                    $hasilPengkajian = '';
                }

                $dataTandaVital = array(
                    'data_source' => $hasilPengkajian,
                    'ref' => $id_pra,
                    'nomr' => isset($post['nomr']) ? $post['nomr'] : '',
                    'nokun' => $post['nokun'],
                    'td_sistolik' => isset($post['td_sistolik']) ? $post['td_sistolik'] : '',
                    'td_diastolik' => isset($post['td_diastolik']) ? $post['td_diastolik'] : '',
                    'nadi' => isset($post['nadi']) ? $post['nadi'] : '',
                    'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : '',
                    'suhu' => isset($post['suhu']) ? $post['suhu'] : '',
                    'oleh' => $pengguna,
                    'status' => 1,
                );

                if ($post['id_pra'] == '') {
                    // echo '<pre>';print_r($data);exit();
                    $this->PengkajianPraSedasiModel->simpan($data);
                    $this->OTKeperawatanModel->simpanTandaVital($dataTandaVital);
                } else {
                    $this->PengkajianPraSedasiModel->ubah($id_pra, $data);
                    $this->OTKeperawatanModel->ubahTandaVitalSedAnes($id_pra, $hasilPengkajian, $dataTandaVital);
                }

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
            } else {
                $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
            }
            echo json_encode($result);
        }
    }

    public function simpanFormPasienPraSedasi()
    {
        $kunjungan = $this->input->post('nokun');
        $pengguna = $this->input->post("pengguna");
        $pilihJenisPengkajian = $this->input->post("pilihJenisPengkajian");
        $alergi = $this->input->post("alergi");
        $sebutkanalergi = $this->input->post("sebutkanalergi");
        $merokok = $this->input->post("merokok");
        $jmlmerokok = $this->input->post("jmlmerokok");
        $alkohol = $this->input->post("alkohol");
        $jmlalkohol = $this->input->post("jmlalkohol");
        $kopiteh = $this->input->post("kopiteh");
        $jmlkopiteh = $this->input->post("jmlkopiteh");
        $olahraga = $this->input->post("olahraga");
        $jmlolahraga = $this->input->post("jmlolahraga");
        $obatresep = $this->input->post("obatresep");
        $obatbebas = $this->input->post("obatbebas");
        $aspirin = $this->input->post("aspirin");
        $dosisaspirin = $this->input->post("dosisaspirin");
        $obatantisakit = $this->input->post("obatantisakit");
        $dosisobatantisakit = $this->input->post("dosisobatantisakit");
        $perdarahan = $this->input->post("perdarahan");
        $jantung = $this->input->post("jantung");
        $pembekuan = $this->input->post("pembekuan");
        $iramajantung = $this->input->post("iramajantung");
        $pembiusan = $this->input->post("pembiusan");
        $hipertensi = $this->input->post("hipertensi");
        $demamtinggi = $this->input->post("demamtinggi");
        $tuberkulosis = $this->input->post("tuberkulosis");
        $diabetes = $this->input->post("diabetes");
        $penyakitberat = $this->input->post("penyakitberat");
        $jelaskanpenyakitkeluarga = $this->input->post("jelaskanpenyakitkeluarga");
        $perdarahan_e = $this->input->post("perdarahan_e");
        $jantung_e = $this->input->post("jantung_e");
        $pembekuan_e = $this->input->post("pembekuan_e");
        $iramajantung_e = $this->input->post("iramajantung_e");
        $maag_e = $this->input->post("maag_e");
        $anemia_e = $this->input->post("anemia_e");
        $asma_e = $this->input->post("asma_e");
        $pingsan_e = $this->input->post("pingsan_e");
        $mengorok_e = $this->input->post("mengorok_e");
        $pembiusan_e = $this->input->post("pembiusan_e");
        $hipertensi_e = $this->input->post("hipertensi_e");
        $hepatitis_e = $this->input->post("hepatitis_e");
        $kejang_e = $this->input->post("kejang_e");
        $demamtinggi_e = $this->input->post("demamtinggi_e");
        $penyakitbawaan_e = $this->input->post("penyakitbawaan_e");
        $diabetes_e = $this->input->post("diabetes_e");
        $penyakitberat_e = $this->input->post("penyakitberat_e");
        $jelaskanpenyakitkeluarga_e = $this->input->post("jelaskanpenyakitkeluarga_e");
        $transfusidarah = $this->input->post("transfusidarah");
        $transfusidarah_thn = $this->input->post("transfusidarah_thn");
        $hiv = $this->input->post("hiv");
        $hiv_thn = $this->input->post("hiv_thn");
        $hiv_hasil = $this->input->post("hiv_hasil");
        $riwayatoperasi = $this->input->post("riwayatoperasi");
        $reaksianestesilokal = $this->input->post("reaksianestesilokal");
        $reaksianestesiregional = $this->input->post("reaksianestesiregional");
        $reaksianestesiumum = $this->input->post("reaksianestesiumum");
        $tanggalperiksaterakhir = $this->input->post("tanggalperiksaterakhir");
        $dimana = $this->input->post("dimana");
        $untukpenyakit = $this->input->post("untukpenyakit");
        $pasienmemakai = $this->input->post("pasienmemakai");
        $pasienmemakailain = $this->input->post("pasienmemakailain");
        $jmlkehamilan = $this->input->post("jmlkehamilan");
        $jmlanak = $this->input->post("jmlanak");
        $mensterakhir = $this->input->post("mensterakhir");
        $menyusui = $this->input->post("menyusui");

        $dataPasien = array(
            'nokun' => $kunjungan,
            'alergi' => $alergi,
            'sebutkan_alergi' => $sebutkanalergi,
            'merokok' => $merokok,
            'jmlmerokok' => $jmlmerokok,
            'alkohol' => $alkohol,
            'jmlalkohol' => $jmlalkohol,
            'kopiteh' => $kopiteh,
            'jmlkopiteh' => $jmlkopiteh,
            'olahraga' => $olahraga,
            'jmlolahraga' => $jmlolahraga,
            'obatresep' => $obatresep,
            'obatbebas' => $obatbebas,
            'aspirin' => $aspirin,
            'dosisaspirin' => $dosisaspirin,
            'obatantisakit' => $obatantisakit,
            'dosisobatantisakit' => $dosisobatantisakit,
            'perdarahan' => $perdarahan,
            'jantung' => $jantung,
            'pembekuan' => $pembekuan,
            'iramajantung' => $iramajantung,
            'pembiusan' => $pembiusan,
            'hipertensi' => $hipertensi,
            'demamtinggi' => $demamtinggi,
            'tuberkulosis' => $tuberkulosis,
            'diabetes' => $diabetes,
            'penyakitberat' => $penyakitberat,
            'jelaskanpenyakitkeluarga' => $jelaskanpenyakitkeluarga,
            'perdarahan_e' => $perdarahan_e,
            'jantung_e' => $jantung_e,
            'pembekuan_e' => $pembekuan_e,
            'iramajantung_e' => $iramajantung_e,
            'maag_e' => $maag_e,
            'anemia_e' => $anemia_e,
            'asma_e' => $asma_e,
            'pingsan_e' => $pingsan_e,
            'mengorok_e' => $mengorok_e,
            'pembiusan_e' => $pembiusan_e,
            'hipertensi_e' => $hipertensi_e,
            'hepatitis_e' => $hepatitis_e,
            'kejang_e' => $kejang_e,
            'demamtinggi_e' => $demamtinggi_e,
            'penyakitbawaan_e' => $penyakitbawaan_e,
            'diabetes_e' => $diabetes_e,
            'penyakitberat_e' => $penyakitberat_e,
            'jelaskanpenyakitkeluarga_e' => $jelaskanpenyakitkeluarga_e,
            'transfusidarah' => $transfusidarah,
            'transfusidarah_thn' => $transfusidarah_thn,
            'hiv' => $hiv,
            'hiv_thn' => $hiv_thn,
            'hiv_hasil' => $hiv_hasil,
            'riwayatoperasi' => $riwayatoperasi,
            'reaksianestesilokal' => $reaksianestesilokal,
            'reaksianestesiregional' => $reaksianestesiregional,
            'reaksianestesiumum' => $reaksianestesiumum,
            'tanggalperiksaterakhir' => $tanggalperiksaterakhir,
            'dimana' => $dimana,
            'untukpenyakit' => $untukpenyakit,
            'pasienmemakai' => isset($pasienmemakai) ? implode($pasienmemakai, ',') : "",
            'pasienmemakailain' => $pasienmemakailain,
            'jmlkehamilan' => $jmlkehamilan,
            'jmlanak' => $jmlanak,
            'mensterakhir' => $mensterakhir,
            'menyusui' => $menyusui,
            'jenis' => $pilihJenisPengkajian,
            'oleh' => $pengguna,
        );

        // echo "<pre>data anestesi/sedasi ";print_r($dataPasien);echo "</pre>";
        // exit();

        $getIdPra = $this->pengkajianAwalModel->insertPraAnesSed($dataPasien);

        $dataPengkajian = array(
            'id_pra' => $getIdPra,
            'nokun' => $kunjungan,
        );

        $getIdPeng = $this->pengkajianAwalModel->insertDataPengkajian($dataPengkajian);

        if ($pilihJenisPengkajian == 1) {
            $hasilPengkajian = 18;
        } elseif ($pilihJenisPengkajian == 2) {
            $hasilPengkajian = 17;
        } else {
            $hasilPengkajian = "";
        }

        $dataTandaVital = array(
            'data_source' => $hasilPengkajian,
            'ref' => $getIdPra,
        );

        // echo "<pre>data anestesi/sedasi ";print_r($dataPasien);echo "</pre>";
        // exit();

        $this->db->trans_begin();
        // $this->db->insert('keperawatan.tb_riwayat_kesehatan_sebelumnya', $dataPasien);
        // $this->db->insert('medis.tb_pengkajian_pra_sedasi', $dataPengkajian);
        $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function ubahFormPasienPraSedasi()
    {
        $id_pasienprasedasi = $this->input->post("id_pasienprasedasi");
        $alergi = $this->input->post("alergi_edit");
        $sebutkanalergi = $this->input->post("sebutkanalergi_edit");
        $merokok = $this->input->post("merokok_edit");
        $jmlmerokok = $this->input->post("jmlmerokok_edit");
        $alkohol = $this->input->post("alkohol_edit");
        $jmlalkohol = $this->input->post("jmlalkohol_edit");
        $kopiteh = $this->input->post("kopiteh_edit");
        $jmlkopiteh = $this->input->post("jmlkopiteh_edit");
        $olahraga = $this->input->post("olahraga_edit");
        $jmlolahraga = $this->input->post("jmlolahraga_edit");
        $obatresep = $this->input->post("obatresep_edit");
        $obatbebas = $this->input->post("obatbebas_edit");
        $aspirin = $this->input->post("aspirin_edit");
        $dosisaspirin = $this->input->post("dosisaspirin_edit");
        $obatantisakit = $this->input->post("obatantisakit_edit");
        $dosisobatantisakit = $this->input->post("dosisobatantisakit_edit");
        $perdarahan = $this->input->post("perdarahan_edit");
        $jantung = $this->input->post("jantung_edit");
        $pembekuan = $this->input->post("pembekuan_edit");
        $iramajantung = $this->input->post("iramajantung_edit");
        $pembiusan = $this->input->post("pembiusan_edit");
        $hipertensi = $this->input->post("hipertensi_edit");
        $demamtinggi = $this->input->post("demamtinggi_edit");
        $tuberkulosis = $this->input->post("tuberkulosis_edit");
        $diabetes = $this->input->post("diabetes_edit");
        $penyakitberat = $this->input->post("penyakitberat_edit");
        $jelaskanpenyakitkeluarga = $this->input->post("jelaskanpenyakitkeluarga_edit");
        $perdarahan_e = $this->input->post("perdarahan_e_edit");
        $jantung_e = $this->input->post("jantung_e_edit");
        $pembekuan_e = $this->input->post("pembekuan_e_edit");
        $iramajantung_e = $this->input->post("iramajantung_e_edit");
        $maag_e = $this->input->post("maag_e_edit");
        $anemia_e = $this->input->post("anemia_e_edit");
        $asma_e = $this->input->post("asma_e_edit");
        $pingsan_e = $this->input->post("pingsan_e_edit");
        $mengorok_e = $this->input->post("mengorok_e_edit");
        $pembiusan_e = $this->input->post("pembiusan_e_edit");
        $hipertensi_e = $this->input->post("hipertensi_e_edit");
        $hepatitis_e = $this->input->post("hepatitis_e_edit");
        $kejang_e = $this->input->post("kejang_e_edit");
        $demamtinggi_e = $this->input->post("demamtinggi_e_edit");
        $penyakitbawaan_e = $this->input->post("penyakitbawaan_e_edit");
        $diabetes_e = $this->input->post("diabetes_e_edit");
        $penyakitberat_e = $this->input->post("penyakitberat_e_edit");
        $jelaskanpenyakitkeluarga_e = $this->input->post("jelaskanpenyakitkeluarga_e_edit");
        $transfusidarah = $this->input->post("transfusidarah_edit");
        $transfusidarah_thn = $this->input->post("transfusidarah_thn_edit");
        $hiv = $this->input->post("hiv_edit");
        $hiv_thn = $this->input->post("hiv_thn_edit");
        $hiv_hasil = $this->input->post("hiv_hasil_edit");
        $riwayatoperasi = $this->input->post("riwayatoperasi_edit");
        $reaksianestesilokal = $this->input->post("reaksianestesilokal_edit");
        $reaksianestesiregional = $this->input->post("reaksianestesiregional_edit");
        $reaksianestesiumum = $this->input->post("reaksianestesiumum_edit");
        $tanggalperiksaterakhir = $this->input->post("tanggalperiksaterakhir_edit");
        $dimana = $this->input->post("dimana_edit");
        $untukpenyakit = $this->input->post("untukpenyakit_edit");
        $pasienmemakai = $this->input->post("pasienmemakai_edit");
        $pasienmemakailain = $this->input->post("pasienmemakailain_edit");
        $jmlkehamilan = $this->input->post("jmlkehamilan_edit");
        $jmlanak = $this->input->post("jmlanak_edit");
        $mensterakhir = $this->input->post("mensterakhir_edit");
        $menyusui = $this->input->post("menyusui_edit");

        $dataPasienUbah = array(
            'alergi' => $alergi,
            'sebutkan_alergi' => $sebutkanalergi,
            'merokok' => $merokok,
            'jmlmerokok' => $jmlmerokok,
            'alkohol' => $alkohol,
            'jmlalkohol' => $jmlalkohol,
            'kopiteh' => $kopiteh,
            'jmlkopiteh' => $jmlkopiteh,
            'olahraga' => $olahraga,
            'jmlolahraga' => $jmlolahraga,
            'obatresep' => $obatresep,
            'obatbebas' => $obatbebas,
            'aspirin' => $aspirin,
            'dosisaspirin' => $dosisaspirin,
            'obatantisakit' => $obatantisakit,
            'dosisobatantisakit' => $dosisobatantisakit,
            'perdarahan' => $perdarahan,
            'jantung' => $jantung,
            'pembekuan' => $pembekuan,
            'iramajantung' => $iramajantung,
            'pembiusan' => $pembiusan,
            'hipertensi' => $hipertensi,
            'demamtinggi' => $demamtinggi,
            'tuberkulosis' => $tuberkulosis,
            'diabetes' => $diabetes,
            'penyakitberat' => $penyakitberat,
            'jelaskanpenyakitkeluarga' => $jelaskanpenyakitkeluarga,
            'perdarahan_e' => $perdarahan_e,
            'jantung_e' => $jantung_e,
            'pembekuan_e' => $pembekuan_e,
            'iramajantung_e' => $iramajantung_e,
            'maag_e' => $maag_e,
            'anemia_e' => $anemia_e,
            'asma_e' => $asma_e,
            'pingsan_e' => $pingsan_e,
            'mengorok_e' => $mengorok_e,
            'pembiusan_e' => $pembiusan_e,
            'hipertensi_e' => $hipertensi_e,
            'hepatitis_e' => $hepatitis_e,
            'kejang_e' => $kejang_e,
            'demamtinggi_e' => $demamtinggi_e,
            'penyakitbawaan_e' => $penyakitbawaan_e,
            'diabetes_e' => $diabetes_e,
            'penyakitberat_e' => $penyakitberat_e,
            'jelaskanpenyakitkeluarga_e' => $jelaskanpenyakitkeluarga_e,
            'transfusidarah' => $transfusidarah,
            'transfusidarah_thn' => $transfusidarah_thn,
            'hiv' => $hiv,
            'hiv_thn' => $hiv_thn,
            'hiv_hasil' => $hiv_hasil,
            'riwayatoperasi' => $riwayatoperasi,
            'reaksianestesilokal' => $reaksianestesilokal,
            'reaksianestesiregional' => $reaksianestesiregional,
            'reaksianestesiumum' => $reaksianestesiumum,
            'tanggalperiksaterakhir' => $tanggalperiksaterakhir,
            'dimana' => $dimana,
            'untukpenyakit' => $untukpenyakit,
            'pasienmemakai' => isset($pasienmemakai) ? implode($pasienmemakai, ',') : "",
            'pasienmemakailain' => $pasienmemakailain,
            'jmlkehamilan' => $jmlkehamilan,
            'jmlanak' => $jmlanak,
            'mensterakhir' => $mensterakhir,
            'menyusui' => $menyusui,
            'jenis' => "1",
        );

        $this->db->trans_begin();

        $this->db->where('keperawatan.tb_riwayat_kesehatan_sebelumnya.id', $id_pasienprasedasi);
        $this->db->update('keperawatan.tb_riwayat_kesehatan_sebelumnya', $dataPasienUbah);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function history()
    {
        $post = $this->input->post();
        $data = array('nomr' => $post['nomr'], 'nokun' => $post['nokun']);
        // echo '<pre>';print_r($data);exit();
        $this->load->view('Pengkajian/sedasi/pengkajianPraSedasi/history', $data);
    }

    public function tabel()
    {
        $draw = intval($this->input->post('draw'));
        $nomr = $this->input->post('nomr');
        $nokun = $this->input->post('nokun');
        $history = $this->PengkajianPraSedasiModel->history($nomr);
        $data = array();
        $no = 1;
        $jenis = null;
        $cetak = null;
        // echo '<pre>';print_r($history);exit();

        foreach ($history->result() as $h) {
            // if($h->nokun == $nokun){
                if ($h->jenis == 1) {
                    $jenis = 'Sedasi';
                    $cetak = "<a href='/reports/simrskd/pengkajianpra/PengkajianPraSedasi.php?format=pdf&ID=" . $h->id . "' class='btn btn-sm btn-warning waves-effect' target='_blank'><i class='fa fa-print'></i> Cetak</a>";
                } elseif ($h->jenis == 2) {
                    $jenis = 'Anestesi';
                    $cetak = "<a href='/reports/simrskd/pengkajianpra/PengkajianPraAnestesi.php?format=pdf&ID=" . $h->id . "' class='btn btn-sm btn-warning waves-effect' target='_blank'><i class='fa fa-print'></i> Cetak</a>";
                }

                $data[] = array(
                    $no,
                    date('d-m-Y', strtotime($h->created_at)),
                    date('H:i', strtotime($h->created_at)),
                    $jenis,
                    "<div class='btn-group' role='group'>
                    <button type='button' href='#modal-detail-pasien-ppsa' class='btn btn-sm btn-success waves-effect tbl-detail-pasien-ppsa' data-toggle='modal' data-id='" . $h->id . "'>
                    <i class='fas fa-edit'></i> Form pasien
                    </button>
                    <a class='btn btn-sm btn-primary waves-effect tbl-detail-ppsa' data-id='" . $h->id . "-" . $h->jenis . "'>
                    <i class='fa fa-eye'></i> Lihat
                    </a>
                    " . $cetak . "
                    </div>",
                );
                $no++;
            // }
        }

        $output = array(
            'draw' => $draw,
            'recordsTotal' => $history->num_rows(),
            'recordsFiltered' => $history->num_rows(),
            'data' => $data
        );
        echo json_encode($output);
    }

    ///////////////// History Pra Sedasi ///////////////////////////////////
    public function lihathistoryPengkajianPraSedasi()
    {
        $id = $this->input->post('id');
        $historyDetailPraSedasi = $this->pengkajianAwalModel->historyDetailPraSedasi($id);
        $listKajianSistem = $this->masterModel->listKajianSistem();
        $listKajianSistem1 = $this->masterModel->listKajianSistem1();
        $listKajianSistem2 = $this->masterModel->listKajianSistem2();
        $listKajianSistem3 = $this->masterModel->listKajianSistem3();
        $listKajianSistem4 = $this->masterModel->listKajianSistem4();
        $listKajianSistem5 = $this->masterModel->listKajianSistem5();
        $listKajianSistem6 = $this->masterModel->listKajianSistem6();
        $listKajianSistem7 = $this->masterModel->listKajianSistem7();
        $listKajianSistem8 = $this->masterModel->listKajianSistem8();
        $listKajianSistem9 = $this->masterModel->listKajianSistem9();
        $listKajianSistem10 = $this->masterModel->listKajianSistem10();
        $listKajianSistem11 = $this->masterModel->listKajianSistem11();
        $listKajianSistem12 = $this->masterModel->listKajianSistem12();
        $listKajianSistem13 = $this->masterModel->listKajianSistem13();
        $listKajianSistem14 = $this->masterModel->listKajianSistem14();
        $listKajianSistem15 = $this->masterModel->listKajianSistem15();
        $listPemriksaanFisik = $this->masterModel->listPemriksaanFisik();
        $listPemriksaanFisik1 = $this->masterModel->listPemriksaanFisik1();
        $listPemriksaanFisik2 = $this->masterModel->listPemriksaanFisik2();
        $listPemriksaanFisik3 = $this->masterModel->listPemriksaanFisik3();
        $listPemriksaanFisik4 = $this->masterModel->listPemriksaanFisik4();
        $listPemriksaanFisik5 = $this->masterModel->listPemriksaanFisik5();
        $listPemriksaanFisik6 = $this->masterModel->listPemriksaanFisik6();
        $listPemriksaanPenunjang = $this->masterModel->listPemriksaanPenunjang();
        $listKlasifikasiAsa = $this->masterModel->listKlasifikasiAsa();
        $listMonitoring = $this->masterModel->listMonitoring();
        $listPerawatanPascaSedasi = $this->masterModel->listPerawatanPascaSedasi();

        echo '
        <ul class="nav nav-tabs">
            ';
        foreach ($historyDetailPraSedasi as $lpps) :

            echo '<li class="nav-item col-sm-12 col-md-12">
                <a href="" data-toggle="tab" aria-expanded="false" align="center" class="nav-link active">
                    STATUS KESEHATAN SAAT INI
                </a>
                </br>
                <div class="row">
                    <label class="col-md-12 col-form-label" style="margin-left:10px;">A. Kajian sistem</label>';
            foreach ($listKajianSistem as $ks) :
                echo '              <div class="col-md-6">
                            <div class="row form-group">
                                <label class="col-sm-12 col-md-7 col-form-label" for="kajiansistem' . $ks['id_variabel'] . '">
                                ' . $ks['variabel'] . '
                                </label>
                                <div class="col-sm-12 col-md-5">
                                    <div class="row">';
                if ($ks['id_variabel'] == 2204) {
                    foreach ($listKajianSistem1 as $kso1) :
                        echo '                             <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="kso1" value="' . $kso1['id_variabel'] . '" class="kso1" id="kso_' . $kso1['id_variabel'] . '" ';
                        if ($kso1['id_variabel'] == $lpps['hilangnya_gigi']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="kso_' . $kso1['id_variabel'] . '" class="form-check-label">' . $kso1['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2205) {
                    foreach ($listKajianSistem2 as $kso2) :
                        echo '                              <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="kso2" value="' . $kso2['id_variabel'] . '" class="kso2" id="kso2_' . $kso2['id_variabel'] . '" ';
                        if ($kso2['id_variabel'] == $lpps['masalah_leher']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="kso2_' . $kso2['id_variabel'] . '" class="form-check-label">' . $kso2['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2206) {
                    foreach ($listKajianSistem3 as $kso3) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso3" value="' . $kso3['id_variabel'] . '" class="kso3" id="kso3_' . $kso3['id_variabel'] . '" ';
                        if ($kso3['id_variabel'] == $lpps['denyut_jantung']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso3_' . $kso3['id_variabel'] . '" class="form-check-label">' . $kso3['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2207) {
                    foreach ($listKajianSistem4 as $kso4) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso4" value="' . $kso4['id_variabel'] . '" class="kso4" id="kso4_' . $kso4['id_variabel'] . '" ';
                        if ($kso4['id_variabel'] == $lpps['batuk']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso4_' . $kso4['id_variabel'] . '" class="form-check-label">' . $kso4['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2208) {
                    foreach ($listKajianSistem5 as $kso5) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso5" value="' . $kso5['id_variabel'] . '" class="kso5" id="kso5_' . $kso5['id_variabel'] . '" ';
                        if ($kso5['id_variabel'] == $lpps['sesak']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso5_' . $kso5['id_variabel'] . '" class="form-check-label">' . $kso5['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2209) {
                    foreach ($listKajianSistem6 as $kso6) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso6" value="' . $kso6['id_variabel'] . '" class="kso6" id="kso6_' . $kso6['id_variabel'] . '" ';
                        if ($kso6['id_variabel'] == $lpps['menderita_infeksi']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso6_' . $kso6['id_variabel'] . '" class="form-check-label">' . $kso6['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2210) {
                    foreach ($listKajianSistem7 as $kso7) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso7" value="' . $kso7['id_variabel'] . '" class="kso7" id="kso7_' . $kso7['id_variabel'] . '" ';
                        if ($kso7['id_variabel'] == $lpps['saluran_napas']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso7_' . $kso7['id_variabel'] . '" class="form-check-label">' . $kso7['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2211) {
                    foreach ($listKajianSistem8 as $kso8) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso8" value="' . $kso8['id_variabel'] . '" class="kso8" id="kso8_' . $kso8['id_variabel'] . '" ';
                        if ($kso8['id_variabel'] == $lpps['sakit_dada']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso8_' . $kso8['id_variabel'] . '" class="form-check-label">' . $kso8['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2212) {
                    foreach ($listKajianSistem9 as $kso9) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso9" value="' . $kso9['id_variabel'] . '" class="kso9" id="kso9_' . $kso9['id_variabel'] . '" ';
                        if ($kso9['id_variabel'] == $lpps['muntah']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso9_ ' . $kso9['id_variabel'] . '" class="form-check-label">' . $kso9['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2213) {
                    foreach ($listKajianSistem10 as $kso10) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso10" value="' . $kso10['id_variabel'] . '" class="kso10" id="kso10_' . $kso10['id_variabel'] . '" ';
                        if ($kso10['id_variabel'] == $lpps['pingsan']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso10_' . $kso10['id_variabel'] . '" class="form-check-label">' . $kso10['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2214) {
                    foreach ($listKajianSistem11 as $kso11) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso11" value="' . $kso11['id_variabel'] . '" class="kso11" id="kso11_' . $kso11['id_variabel'] . '" ';
                        if ($kso11['id_variabel'] == $lpps['stroke']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso11_' . $kso11['id_variabel'] . '" class="form-check-label">' . $kso11['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2215) {
                    foreach ($listKajianSistem12 as $kso12) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso12" value="' . $kso12['id_variabel'] . '" class="kso12" id="kso12_' . $kso12['id_variabel'] . '" ';
                        if ($kso12['id_variabel'] == $lpps['kejang']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso12_' . $kso12['id_variabel'] . '" class="form-check-label">' . $kso12['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2216) {
                    foreach ($listKajianSistem13 as $kso13) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso13" value="' . $kso13['id_variabel'] . '" class="kso13" id="kso13_' . $kso13['id_variabel'] . '" ';
                        if ($kso13['id_variabel'] == $lpps['sedang_hamil']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso13_' . $kso13['id_variabel'] . '" class="form-check-label">' . $kso13['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2217) {
                    foreach ($listKajianSistem14 as $kso14) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso14" value="' . $kso14['id_variabel'] . '" class="kso14" id="kso14_' . $kso14['id_variabel'] . '" ';
                        if ($kso14['id_variabel'] == $lpps['kelainan_tulang']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso14_' . $kso14['id_variabel'] . '" class="form-check-label">' . $kso14['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                } else if ($ks['id_variabel'] == 2218) {
                    foreach ($listKajianSistem15 as $kso15) :
                        echo '                          <div class="col form-check">
                                            <div class="radio radio-primary form-check-input jarak2">
                                                <input type="radio" name="kso15" value="' . $kso15['id_variabel'] . '" class="kso15" id="kso15_' . $kso15['id_variabel'] . '" ';
                        if ($kso15['id_variabel'] == $lpps['obesitas']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                  >
                                                <label for="kso15_' . $kso15['id_variabel'] . '" class="form-check-label">' . $kso15['variabel'] . '</label>
                                            </div>
                                        </div>';
                    endforeach;
                }
                echo '                      </div>
                                </div>
                            </div>
                        </div>';
            endforeach;
            echo '      <label class="col-md-12">Keterangan</label>
                    <div class="col-md-12">
                        <input type="text" class="form-control" id="kso16" name="kso16"
                        placeholder="[ Tuliskan Keterangan ]" value="' . $lpps['keterangan_kajian_sistem'] . '" autocomplete="off">
                    </div>
                </div>

                <label class="col-md-12 col-form-label" style="margin-left:10px;">B. Pemeriksaan fisik</label>
                <label class="col-md-12">Tanda Vital :</label>
                <div class="row form-group" style="margin-left:2px;">
                    <label class="col-md-2">Tekanan Darah</label>
                    <div class="col-md-2">
                        <input type="text" class="form-control" id="ekg" name="ekg"
                        placeholder="[ mmHg ]" value="' . $lpps['tekanan_darah'] . '/' . $lpps['per_tekanan_darah'] . '" autocomplete="off">
                    </div>
                    <label class="col-md-1">Nadi</label>
                    <div class="col-md-2">
                        <input type="text" class="form-control" id="ekg" name="ekg"
                        placeholder="[ x/menit ]" value="' . $lpps['nadi'] . '" autocomplete="off">
                    </div>
                    <label class="col-md-2">Frequensi Napas</label>
                    <div class="col-md-2">
                        <input type="text" class="form-control" id="ekg" name="ekg"
                        placeholder="[ x/menit ]" value="' . $lpps['pernapasan'] . '" autocomplete="off">
                    </div>
                </div>

                <div class="row form-group col-sm-12">
                    <label class="col-md-1">Suhu</label>
                    <div class="col-md-2">
                        <input type="text" class="form-control" id="ekg" name="ekg"
                        placeholder="[ derajat ]" value="' . $lpps['suhu'] . '" autocomplete="off">
                    </div>
                </div>

                <div class="row form-group col-sm-12">
                    <label class="col-md-3">Skor Mallampati</label>
                    <input type="text" class="form-control col-md-9" id="skor" name="skor"
                        placeholder="[ skor ]" value="' . $lpps['skor_mallampati'] . '" autocomplete="off">
                </div>

                <div class="row form-group col-sm-12">
                    <label class="col-md-3">Gigi Palsu</label>
                    <input type="text" class="form-control col-md-9" id="gigipalsu" name="gigipalsu"
                        placeholder="[ gigi palsu ]" value="' . $lpps['gigi_palsu'] . '" autocomplete="off">
                </div>

                <div class="row form-group col-sm-12">';
            foreach ($listPemriksaanFisik as $lpkf) :
                echo '          <div class="col-md-12">
                            <div class="row form-group">
                                <label class="col-sm-12 col-md-4 col-form-label" for="pemeriksaanfisik' . $lpkf['id_variabel'] . '">
                                ' . $lpkf['variabel'] . '
                                </label>
                                <div class="col-sm-12 col-md-4">
                                    <div class="row">';
                if ($lpkf['id_variabel'] == 2265) {
                    foreach ($listPemriksaanFisik1 as $lpkfo1) :
                        echo '                              <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="lpkfo1" value="' . $lpkfo1['id_variabel'] . '" class="lpkfo1" id="lpkfo1_' . $lpkfo1['id_variabel'] . '" ';
                        if ($lpkfo1['id_variabel'] == $lpps['jantung']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="lpkfo1_' . $lpkfo1['id_variabel'] . '" class="form-check-label">' . $lpkfo1['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                    echo '                          <div class="col-md-12" id="idlpkf2265" style="';
                    if ($lpps['jantung'] == '2272') {
                        "display:block;";
                    } else {
                        echo "display:none;";
                    }
                    echo '                              margin-left:250px">
                                            <textarea class="form-control lpkfojelaskan2265" name="jelaskan2265"
                                            placeholder="[ Jelaskan ]" autocomplete="off">' . $lpps['jantung_jelaskan'] . '</textarea>
                                        </div>';
                } else if ($lpkf['id_variabel'] == 2266) {
                    foreach ($listPemriksaanFisik2 as $lpkfo2) :
                        echo '                              <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="lpkfo2" value="' . $lpkfo2['id_variabel'] . '" class="lpkfo2" id="lpkfo2_' . $lpkfo2['id_variabel'] . '" ';
                        if ($lpkfo2['id_variabel'] == $lpps['paru_paru']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="lpkfo2_' . $lpkfo2['id_variabel'] . '" class="form-check-label">' . $lpkfo2['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                    echo '                              <div class="col-md-12" id="idlpkf2266" style=" ';
                    if ($lpps['paru_paru'] == '2381') {
                        echo "display:block;";
                    } else {
                        echo "display:none;";
                    }
                    echo '                              margin-left:250px">
                                                <textarea class="form-control lpkfojelaskan2266" name="jelaskan2266"
                                                placeholder="[ Jelaskan ]" autocomplete="off">' . $lpps['paru_paru_jelaskan'] . '</textarea>
                                            </div>';
                } else if ($lpkf['id_variabel'] == 2267) {
                    foreach ($listPemriksaanFisik3 as $lpkfo3) :
                        echo '                              <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="lpkfo3" value="' . $lpkfo3['id_variabel'] . '" class="lpkfo3" id="lpkfo3_' . $lpkfo3['id_variabel'] . '" ';
                        if ($lpkfo3['id_variabel'] == $lpps['abdomen']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="lpkfo3_' . $lpkfo3['id_variabel'] . '" class="form-check-label">' . $lpkfo3['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                    echo '                              <div class="col-md-12" id="idlpkf2267" style=" ';
                    if ($lpps['abdomen'] == '2393') {
                        echo "display:block;";
                    } else {
                        echo "display:none;";
                    }
                    echo '                              margin-left:250px">
                                                <textarea class="form-control lpkfojelaskan2267" name="jelaskan2267"
                                                placeholder="[ Jelaskan ]" autocomplete="off">' . $lpps['abdomen_jelaskan'] . '</textarea>
                                            </div>';
                } else if ($lpkf['id_variabel'] == 2268) {
                    foreach ($listPemriksaanFisik4 as $lpkfo4) :
                        echo '                              <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="lpkfo4" value="' . $lpkfo4['id_variabel'] . '" class="lpkfo4" id="lpkfo4_' . $lpkfo4['id_variabel'] . '" ';
                        if ($lpkfo4['id_variabel'] == $lpps['tulang_belakang']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="lpkfo4_' . $lpkfo4['id_variabel'] . '" class="form-check-label">' . $lpkfo4['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                    echo '                              <div class="col-md-12" id="idlpkf2268" style=" ';
                    if ($lpps['tulang_belakang'] == '2397') {
                        echo "display:block;";
                    } else {
                        echo "display:none;";
                    }
                    echo '                              margin-left:250px">
                                                <textarea class="form-control lpkfojelaskan2268" name="jelaskan2268"
                                                placeholder="[ Jelaskan ]" autocomplete="off">' . $lpps['tulang_belakang_jelaskan'] . '</textarea>
                                            </div>';
                } else if ($lpkf['id_variabel'] == 2269) {
                    foreach ($listPemriksaanFisik5 as $lpkfo5) :
                        echo '                              <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="lpkfo5" value="' . $lpkfo5['id_variabel'] . '" class="lpkfo5" id="lpkfo5_' . $lpkfo5['id_variabel'] . '" ';
                        if ($lpkfo5['id_variabel'] == $lpps['ekstremitas']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="lpkfo5_' . $lpkfo5['id_variabel'] . '" class="form-check-label">' . $lpkfo5['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                    echo '                              <div class="col-md-12" id="idlpkf2269" style=" ';
                    if ($lpps['ekstremitas'] == '2402') {
                        echo "display:block;";
                    } else {
                        echo "display:none;";
                    }
                    echo '                              margin-left:250px">
                                                <textarea class="form-control lpkfojelaskan2269" name="jelaskan2269"
                                                placeholder="[ Jelaskan ]" autocomplete="off">' . $lpps['ekstremitas_jelaskan'] . '</textarea>
                                            </div>';
                } else if ($lpkf['id_variabel'] == 2270) {
                    foreach ($listPemriksaanFisik6 as $lpkfo6) :
                        echo '                              <div class="col form-check">
                                                <div class="radio radio-primary form-check-input jarak2">
                                                    <input type="radio" name="lpkfo6" value="' . $lpkfo6['id_variabel'] . '" class="lpkfo6" id="lpkfo6_' . $lpkfo6['id_variabel'] . '" ';
                        if ($lpkfo6['id_variabel'] == $lpps['neurologi']) {
                            echo "checked";
                        } else {
                            echo "";
                        }
                        echo '                                      >
                                                    <label for="lpkfo6_' . $lpkfo6['id_variabel'] . '" class="form-check-label">' . $lpkfo6['variabel'] . '</label>
                                                </div>
                                            </div>';
                    endforeach;
                    echo '                              <div class="col-md-12" id="idlpkf2270" style=" ';
                    if ($lpps['neurologi'] == '2404') {
                        echo "display:block;";
                    } else {
                        echo "display:none;";
                    }
                    echo '                              margin-left:250px">
                                                <textarea class="form-control lpkfojelaskan2270" name="jelaskan2270"
                                                placeholder="[ Jelaskan ]" autocomplete="off">' . $lpps['neurologi_jelaskan'] . '</textarea>
                                            </div>';
                }
                echo '                      </div>
                                </div>
                            </div>
                        </div>';
            endforeach;
            echo '  </div>

                <label class="col-md-12 col-form-label">C. Pemeriksaan penunjang (bila tersedia)</label>
                <div class="col-sm-12 col-md-12">
                    <div class="row">';
            foreach ($listPemriksaanPenunjang as $lpp) :
                echo '              <label class="col-form-label col-md-3">' . $lpp['variabel'] . '</label>
                            <div class="col-md-12" id="idlpp' . $lpp['id_variabel'] . '">
                                <input type="text" class="form-control lppketerangan' . $lpp['id_variabel'] . '" name="lppketerangan' . $lpp['id_variabel'] . '"
                                placeholder="[ Keterangan ]"
                                value=" ';
                if ($lpp['id_variabel'] == 2273) {
                    echo $lpps['hbht'];
                } else if ($lpp['id_variabel'] == 2274) {
                    echo $lpps['glukosa'];
                } else if ($lpp['id_variabel'] == 2275) {
                    echo $lpps['naci'];
                } else if ($lpp['id_variabel'] == 2276) {
                    echo $lpps['leukosit'];
                } else if ($lpp['id_variabel'] == 2277) {
                    echo $lpps['tes_kehamilan'];
                } else if ($lpp['id_variabel'] == 2278) {
                    echo $lpps['ureum'];
                } else if ($lpp['id_variabel'] == 2279) {
                    echo $lpps['pt'];
                } else if ($lpp['id_variabel'] == 2280) {
                    echo $lpps['ekg'];
                } else if ($lpp['id_variabel'] == 2281) {
                    echo $lpps['kreatinin'];
                } else if ($lpp['id_variabel'] == 2282) {
                    echo $lpps['trombosit'];
                } else if ($lpp['id_variabel'] == 2283) {
                    echo $lpps['kallium'];
                } else if ($lpp['id_variabel'] == 2284) {
                    echo $lpps['rothorax'];
                }
                echo '                  "
                                autocomplete="off"></textarea>
                            </div>';
            endforeach;
            echo '      </div>
                </div>

                <div class="col-sm-12 col-md-12">
                    <div class="form-group">
                        <label class="form-label">D. Diagnosis (ICD X)</label>
                        <select multiple data-role="tagsinput" name="diagnosis[]"
                            class="form-control diagnosis">';
            $diagnosisicd = explode(',', $lpps['diagnosis']);
            foreach ($diagnosisicd as $diag) :
                echo "<option value='$diag'>$diag</option>";
            endforeach;
            echo '          </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">KLASIFIKASI ASA</label>
                        <div id="klasifikasiasa" class="col-sm-12 col-md-9">
                            <div class="row">
                                <div class="checkbox checkbox-primary jarak2">';
            foreach ($listKlasifikasiAsa as $lka) :
                echo '                          <div class="checkbox checkbox-primary jarak2">
                                        <input type="checkbox" name="klasifikasiasa[]" class="klasifikasiasa" value="' . $lka['id_variabel'] . '" id="klasifikasiasa' . $lka['id_variabel'] . '" ';
                $klasifikasiasa = explode(',', $lpps['klasifikasi_asa']);
                foreach ($klasifikasiasa as $klasifikasiasa) :
                    if ($klasifikasiasa == $lka['id_variabel']) {
                        echo "checked";
                    } else {
                        echo "";
                    }
                endforeach;
                echo '                          >
                                        <label for="klasifikasiasa' . $lka['id_variabel'] . '">
                                            ' . $lka['variabel'] . '
                                        </label>
                                    </div>';
            endforeach;
            echo '                  </div>
                                </br>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">E. Penyulit sedasi lain</label>
                        <div class="row">
                            <div class="radio radio-primary jarak2">
                                <input type="radio" name="penyulitsedasi" class="penyulitsedasi" value="2297" id="penyulitsedasi1" ';
            if ($lpps['penyulit_sedasi'] == 2297) {
                echo "checked";
            } else {
                echo "";
            }
            echo '                  >
                                <label for="penyulitsedasi1">Ada</label>
                            </div>
                            <div class="radio radio-primary jarak2">
                                <input type="radio" name="penyulitsedasi" class="penyulitsedasi" value="2298" id="penyulitsedasi2" ';
            if ($lpps['penyulit_sedasi'] == 2298) {
                echo "checked";
            } else {
                echo "";
            }
            echo '                  >
                                <label for="penyulitsedasi2">Tidak Ada</label>
                            </div>
                            <div class="form-group col-md-12" style=" ';
            if ($lpps['penyulit_sedasi'] == 2298) {
                echo 'display:none;';
            } else {
                echo 'display:block;';
            }
            echo '              " id="idpenyulitlain">
                                <select multiple data-role="tagsinput" name="penyulitlain[]"
                                        placeholder="[ Jelaskan ]" class="form-control penyulitlain" id="penyulitlainid"> ';

            $penyulitlain = explode(',', $lpps['penyulit_sedasi_lain']);
            foreach ($penyulitlain as $penyulitlain) :
                echo "<option value='$penyulitlain'>$penyulitlain</option>";
            endforeach;

            echo '                  </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">F. Catatan tindak lanjut</label>
                        <div class="row">
                            <div class="radio radio-primary jarak2">
                                <input type="radio" name="catatantl" class="catatantl" value="2299" id="catatantl1" ';
            if ($lpps['tindak_lanjut'] == 2299) {
                echo "checked";
            } else {
                echo "";
            }
            echo '                  >
                                <label for="catatantl1">Ada</label>
                            </div>
                            <div class="radio radio-primary jarak2">
                                <input type="radio" name="catatantl" class="catatantl" value="2300" id="catatantl2" ';
            if ($lpps['tindak_lanjut'] == 2300) {
                echo "checked";
            } else {
                echo "";
            }
            echo '                  >
                                <label for="catatantl2">Tidak Ada</label>
                            </div>
                            <div class="form-group col-md-12" style=" ';
            if ($lpps['tindak_lanjut'] == 2300) {
                echo 'display:none;';
            } else {
                echo 'display:block;';
            }
            echo '              " id="idjelaskancatatantl">
                                <input type="text" name="jelaskancatatantl"
                                placeholder="[ Jelaskan ]" class="form-control jelaskancatatantl" id="jelaskancatatantlid">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">G. Perencanaan</label>
                    </div>
                    <div class="row form-group">
                        <label class="form-label col-md-2">1.Teknik Sedasi:</label>
                        <label class="form-label col-md-1"> Sedasi</label>
                        <input type="text" name="tekniksedasi"
                        placeholder="[ Tuliskan Sedasi ]" value="' . $lpps['teknik_sedasi'] . '" class="form-control col-md-9">
                    </div>
                    <div class="row form-group">
                        <label class="form-label col-md-3" align="right">Lain - lain </label>
                        <input type="text" name="tekniklainlain"
                        placeholder="[ Tuliskan Lain - lain ]" value="' . $lpps['teknik_lain_lain'] . '" class="form-control col-md-9">
                    </div>
                    <div class="row form-group">
                        <label class="form-label col-md-12">2. Monitoring</label>
                        <div class="row">';
            foreach ($listMonitoring as $lm) :
                echo '              <div class="col-md-6">
                                <div class="checkbox checkbox-primary jarak2">
                                    <input type="checkbox" name="monitoring[]" class="monitoring" value="' . $lm['id_variabel'] . '" id="monitoring' . $lm['id_variabel'] . '" ';

                $monitoring = explode(',', $lpps['monitoring']);
                foreach ($monitoring as $monitoring) :
                    if ($lm['id_variabel'] == $monitoring) {
                        echo "checked";
                    } else {
                        echo "";
                    }
                endforeach;
                echo '                      >
                                    <label for="monitoring' . $lm['id_variabel'] . '">
                                    ' . $lm['variabel'] . '
                                    </label>
                                    <input style="display:none;" id="id' . $lm['id_variabel'] . '" type="text" name="ketmonitoring' . $lm['id_variabel'] . '"
                                    placeholder="[ Keterangan ]" class="form-control col-md-9 class' . $lm['id_variabel'] . '">
                                </div>
                            </div>';
            endforeach;
            echo '              </br>
                        </div>
                    </div>
                    <div class="row form-group">
                        <label class="form-label col-md-12">3. Alat Khusus</label>
                        <div class="row">
                            <div class="radio radio-primary jarak2">
                                <input type="radio" name="alatkhusus" class="alatkhusus" value="2312" id="alatkhusus1" ';
            if ($lpps['alat_khusus'] == 2312) {
                echo "checked";
            } else {
                echo "";
            }
            echo '                  >
                                <label for="alatkhusus1">Tidak ada</label>
                            </div>
                            <div class="radio radio-primary jarak2">
                                <input type="radio" name="alatkhusus" class="alatkhusus" value="2313" id="alatkhusus2" ';
            if ($lpps['alat_khusus'] == 2313) {
                echo "checked";
            } else {
                echo "";
            }
            echo '                  >
                                <label for="alatkhusus2">Ada</label>
                            </div>
                            <div class="form-group col-md-12" style=" ';
            if ($lpps['alat_khusus'] == 2313) {
                echo "display:block;";
            } else {
                echo "display:none;";
            }
            echo '              margin-left:80px;" id="idjelaskanalatkhusus">
                                <input type="text" name="jelaskanalatkhusus"
                                placeholder="[ Jelaskan ]" value="' . $lpps['alat_khusus_sebutkan'] . '" class="form-control jelaskanalatkhusus" id="jelaskanalatkhususid">
                            </div>
                        </div>
                    </div>
                    <div class="row form-group">
                        <label class="form-label col-md-12">4. Perawatan pasca sedasi</label>
                        <div class="col-sm-12 col-md-12">
                            <div class="row">';
            foreach ($listPerawatanPascaSedasi as $lrp) :
                echo '                  <div class="col form-check">
                                    <div class="radio radio-primary form-check-input jarak2">
                                        <input type="radio" name="rencanaperawatan" value="' . $lrp['id_variabel'] . '" class="rencanaperawatan" id="pilih_rencana_perawatan_' . $lrp['id_variabel'] . '" ';
                if ($lpps['pasca_sedasi'] == $lrp['id_variabel']) {
                    echo "checked";
                } else {
                    echo "";
                }
                echo '                          >
                                        <label for="pilih_rencana_perawatan_' . $lrp['id_variabel'] . '" class="form-check-label">' . $lrp['variabel'] . '</label>
                                    </div>
                                </div>';
            endforeach;
            echo '              </div>
                            </br>
                            </br>
                            <div class="form-group col-md-6">
                                <input type="text" name="perawatanlain"
                                placeholder="[ Lainnya ]" value="' . $lpps['pasca_sedasi_lain'] . '" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row form-group">
                        <label class="form-label col-md-12">H. Perawatan Pra Sedasi</label>
                        <label class="form-label col-md-12">Puasa mulai</label>
                        <div class="form-group col-md-12">
                            <input type="text" name="puasamulai"
                            placeholder="[ Tuliskan waktunya ]" value="' . $lpps['puasa_mulai'] . '" class="form-control">
                        </div>
                        <label class="form-label col-md-12">Pre medikasi ruang tindakan</label>
                        <div class="form-group col-md-12">
                            <input type="text" name="premedikasi"
                            placeholder="[ Tuliskan ruang tindakan ]" value="' . $lpps['pre_medikasi'] . '" class="form-control">
                        </div>
                        <label class="form-label col-md-12">Transportasi ke</label>
                        <div class="form-group col-md-12">
                            <input type="text" name="transportasi"
                            placeholder="[ Tuliskan transportasi ]" value="' . $lpps['transportasi'] . '" class="form-control">
                        </div>
                        <label class="form-label col-md-12">Rencana Tindakan</label>
                        <div class="form-group col-md-12">
                            <input type="text" name="rencana"
                            placeholder="[ Tuliskan rencana tindakan ]" value="' . $lpps['rencana'] . '" class="form-control">
                        </div>
                        <label class="form-label col-md-12">CATATAN PERSIAPAN PRA SEDASI</label>
                        <div class="form-group col-md-12">
                            <textarea name="catatanpersiapan"
                            placeholder="[ Tuliskan Catatan ]" class="form-control">' . $lpps['catatan_persiapan'] . '</textarea>
                        </div>
                    </div>
                </div>
                </br>
            </li>';
        endforeach;
        echo '</ul>';
    }

    public function lihathistoryPasienPraSedasi()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $pasien = $this->pengkajianAwalModel->historyDetailPasienPraSedasi($id);

        $listAlergi = $this->masterModel->referensi(1023);
        $listMerokok = $this->masterModel->referensi(1024);
        $listAlkohol = $this->masterModel->referensi(1025);
        $listKopitehsoda = $this->masterModel->referensi(1026);
        $listOlahraga = $this->masterModel->referensi(1027);
        $listAspirin = $this->masterModel->referensi(1028);
        $listObatantisakit = $this->masterModel->referensi(1029);
        $listPerdarahan = $this->masterModel->referensi(1030);
        $listJantung = $this->masterModel->referensi(1031);
        $listPembekuan = $this->masterModel->referensi(1032);
        $listIramajantung = $this->masterModel->referensi(1033);
        $listPembiusan = $this->masterModel->referensi(1034);
        $listHipertensi = $this->masterModel->referensi(1035);
        $listDemamtinggi = $this->masterModel->referensi(1036);
        $listTuberkulosis = $this->masterModel->referensi(1037);
        $listDiabetes = $this->masterModel->referensi(1038);
        $listPenyakitberat = $this->masterModel->referensi(1039);
        $listPerdarahan_e = $this->masterModel->referensi(1040);
        $listJantung_e = $this->masterModel->referensi(1041);
        $listPembekuan_e = $this->masterModel->referensi(1042);
        $listIramajantung_e = $this->masterModel->referensi(1043);
        $listMaag_e = $this->masterModel->referensi(1044);
        $listAnemia_e = $this->masterModel->referensi(1045);
        $listAsma_e = $this->masterModel->referensi(1046);
        $listPingsan_e = $this->masterModel->referensi(1047);
        $listMengorok_e = $this->masterModel->referensi(1048);
        $listPembiusan_e = $this->masterModel->referensi(1049);
        $listHipertensi_e = $this->masterModel->referensi(1050);
        $listHepatitis_e = $this->masterModel->referensi(1051);
        $listKejang_e = $this->masterModel->referensi(1052);
        $listDemamtinggi_e = $this->masterModel->referensi(1053);
        $listPenyakitbawaan_e = $this->masterModel->referensi(1054);
        $listDiabetes_e = $this->masterModel->referensi(1055);
        $listPenyakitberat_e = $this->masterModel->referensi(1056);
        $listTransfusiDarah = $this->masterModel->referensi(1057);
        $listHiv = $this->masterModel->referensi(1058);
        $listPasienmemakai = $this->masterModel->referensi(1059);
        $listMenyusui = $this->masterModel->referensi(1060);

        $dataEdit = array(
            'pasien' => $pasien,
            'getNomr' => $getNomr,
            'listAlergi' => $listAlergi,
            'listMerokok' => $listMerokok,
            'listAlkohol' => $listAlkohol,
            'listKopitehsoda' => $listKopitehsoda,
            'listOlahraga' => $listOlahraga,
            'listAspirin' => $listAspirin,
            'listObatantisakit' => $listObatantisakit,
            'listPerdarahan' => $listPerdarahan,
            'listJantung' => $listJantung,
            'listPembekuan' => $listPembekuan,
            'listIramajantung' => $listIramajantung,
            'listPembiusan' => $listPembiusan,
            'listHipertensi' => $listHipertensi,
            'listDemamtinggi' => $listDemamtinggi,
            'listTuberkulosis' => $listTuberkulosis,
            'listDiabetes' => $listDiabetes,
            'listPenyakitberat' => $listPenyakitberat,
            'listPerdarahan_e' => $listPerdarahan_e,
            'listJantung_e' => $listJantung_e,
            'listPembekuan_e' => $listPembekuan_e,
            'listIramajantung_e' => $listIramajantung_e,
            'listMaag_e' => $listMaag_e,
            'listAnemia_e' => $listAnemia_e,
            'listAsma_e' => $listAsma_e,
            'listPingsan_e' => $listPingsan_e,
            'listMengorok_e' => $listMengorok_e,
            'listPembiusan_e' => $listPembiusan_e,
            'listHipertensi_e' => $listHipertensi_e,
            'listHepatitis_e' => $listHepatitis_e,
            'listKejang_e' => $listKejang_e,
            'listDemamtinggi_e' => $listDemamtinggi_e,
            'listPenyakitbawaan_e' => $listPenyakitbawaan_e,
            'listDiabetes_e' => $listDiabetes_e,
            'listPenyakitberat_e' => $listPenyakitberat_e,
            'listTransfusiDarah' => $listTransfusiDarah,
            'listHiv' => $listHiv,
            'listPasienmemakai' => $listPasienmemakai,
            'listMenyusui' => $listMenyusui,
        );
        // echo '<pre>';print_r($dataEdit);exit();

        $this->load->view('Pengkajian/sedasi/pengkajianPraSedasi/modalViewEditPasienPraSedasi', $dataEdit);
    }

    public function lihatSttsKesSaatIniAnesSed()
    {
        $id = $this->input->post('id');
        $nokun = $this->input->post('nokun');
        $jenis = $this->input->post('jenis');
        if ($jenis == 1) {
            $hasilPengkajian = 18;
        } elseif ($jenis == 2) {
            $hasilPengkajian = 17;
        } else {
            $hasilPengkajian = "";
        }
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $getPra = $this->pengkajianAwalModel->getEditSttsKesAnesSed($id, $hasilPengkajian);

        $listKajianSistem = $this->masterModel->listKajianSistem();
        $listKajianSistem1 = $this->masterModel->listKajianSistem1();
        $listKajianSistem2 = $this->masterModel->listKajianSistem2();
        $listKajianSistem3 = $this->masterModel->listKajianSistem3();
        $listKajianSistem4 = $this->masterModel->listKajianSistem4();
        $listKajianSistem5 = $this->masterModel->listKajianSistem5();
        $listKajianSistem6 = $this->masterModel->listKajianSistem6();
        $listKajianSistem7 = $this->masterModel->listKajianSistem7();
        $listKajianSistem8 = $this->masterModel->listKajianSistem8();
        $listKajianSistem9 = $this->masterModel->listKajianSistem9();
        $listKajianSistem10 = $this->masterModel->listKajianSistem10();
        $listKajianSistem11 = $this->masterModel->listKajianSistem11();
        $listKajianSistem12 = $this->masterModel->listKajianSistem12();
        $listKajianSistem13 = $this->masterModel->listKajianSistem13();
        $listKajianSistem14 = $this->masterModel->listKajianSistem14();
        $listKajianSistem15 = $this->masterModel->listKajianSistem15();
        $listPemriksaanFisik = $this->masterModel->listPemriksaanFisik();
        $listPemriksaanFisik1 = $this->masterModel->listPemriksaanFisik1();
        $listPemriksaanFisik2 = $this->masterModel->listPemriksaanFisik2();
        $listPemriksaanFisik3 = $this->masterModel->listPemriksaanFisik3();
        $listPemriksaanFisik4 = $this->masterModel->listPemriksaanFisik4();
        $listPemriksaanFisik5 = $this->masterModel->listPemriksaanFisik5();
        $listPemriksaanFisik6 = $this->masterModel->listPemriksaanFisik6();
        $listPemriksaanPenunjang = $this->masterModel->listPemriksaanPenunjang();
        $listKlasifikasiAsa = $this->masterModel->listKlasifikasiAsa();
        $listMonitoring = $this->masterModel->listMonitoring();
        $listPerawatanPascaSedasi = $this->masterModel->listPerawatanPascaSedasi();
        $listAlergi = $this->masterModel->referensi(1023);
        $listMerokok = $this->masterModel->referensi(1024);
        $listAlkohol = $this->masterModel->referensi(1025);
        $listKopitehsoda = $this->masterModel->referensi(1026);
        $listOlahraga = $this->masterModel->referensi(1027);
        $listAspirin = $this->masterModel->referensi(1028);
        $listObatantisakit = $this->masterModel->referensi(1029);
        $listPerdarahan = $this->masterModel->referensi(1030);
        $listJantung = $this->masterModel->referensi(1031);
        $listPembekuan = $this->masterModel->referensi(1032);
        $listIramajantung = $this->masterModel->referensi(1033);
        $listPembiusan = $this->masterModel->referensi(1034);
        $listHipertensi = $this->masterModel->referensi(1035);
        $listDemamtinggi = $this->masterModel->referensi(1036);
        $listTuberkulosis = $this->masterModel->referensi(1037);
        $listDiabetes = $this->masterModel->referensi(1038);
        $listPenyakitberat = $this->masterModel->referensi(1039);
        $listPerdarahan_e = $this->masterModel->referensi(1040);
        $listJantung_e = $this->masterModel->referensi(1041);
        $listPembekuan_e = $this->masterModel->referensi(1042);
        $listIramajantung_e = $this->masterModel->referensi(1043);
        $listMaag_e = $this->masterModel->referensi(1044);
        $listAnemia_e = $this->masterModel->referensi(1045);
        $listAsma_e = $this->masterModel->referensi(1046);
        $listPingsan_e = $this->masterModel->referensi(1047);
        $listMengorok_e = $this->masterModel->referensi(1048);
        $listPembiusan_e = $this->masterModel->referensi(1049);
        $listHipertensi_e = $this->masterModel->referensi(1050);
        $listHepatitis_e = $this->masterModel->referensi(1051);
        $listKejang_e = $this->masterModel->referensi(1052);
        $listDemamtinggi_e = $this->masterModel->referensi(1053);
        $listPenyakitbawaan_e = $this->masterModel->referensi(1054);
        $listDiabetes_e = $this->masterModel->referensi(1055);
        $listPenyakitberat_e = $this->masterModel->referensi(1056);
        $listTransfusiDarah = $this->masterModel->referensi(1057);
        $listHiv = $this->masterModel->referensi(1058);
        $listPasienmemakai = $this->masterModel->referensi(1059);
        $listMenyusui = $this->masterModel->referensi(1060);
        $listAlergi = $this->masterModel->referensi(1023);

        $dataEdit = array(
            // 'pasien' => $pasien,
            'getNomr' => $getNomr,
            'listAlergi' => $listAlergi,
            'jenis' => $jenis,
            'id_pra' => $id,
            'getPra' => $getPra,
            'listKajianSistem' => $listKajianSistem,
            'listKajianSistem1' => $listKajianSistem1,
            'listKajianSistem2' => $listKajianSistem2,
            'listKajianSistem3' => $listKajianSistem3,
            'listKajianSistem4' => $listKajianSistem4,
            'listKajianSistem5' => $listKajianSistem5,
            'listKajianSistem6' => $listKajianSistem6,
            'listKajianSistem7' => $listKajianSistem7,
            'listKajianSistem8' => $listKajianSistem8,
            'listKajianSistem9' => $listKajianSistem9,
            'listKajianSistem10' => $listKajianSistem10,
            'listKajianSistem11' => $listKajianSistem11,
            'listKajianSistem12' => $listKajianSistem12,
            'listKajianSistem13' => $listKajianSistem13,
            'listKajianSistem14' => $listKajianSistem14,
            'listKajianSistem15' => $listKajianSistem15,
            'listPemriksaanFisik' => $listPemriksaanFisik,
            'listPemriksaanFisik1' => $listPemriksaanFisik1,
            'listPemriksaanFisik2' => $listPemriksaanFisik2,
            'listPemriksaanFisik3' => $listPemriksaanFisik3,
            'listPemriksaanFisik4' => $listPemriksaanFisik4,
            'listPemriksaanFisik5' => $listPemriksaanFisik5,
            'listPemriksaanFisik6' => $listPemriksaanFisik6,
            'listPemriksaanPenunjang' => $listPemriksaanPenunjang,
            'listKlasifikasiAsa' => $listKlasifikasiAsa,
            'listMonitoring' => $listMonitoring,
            'listPerawatanPascaSedasi' => $listPerawatanPascaSedasi,
            //For Pasien
            'listAlergi' => $listAlergi,
            'listMerokok' => $listMerokok,
            'listAlkohol' => $listAlkohol,
            'listKopitehsoda' => $listKopitehsoda,
            'listOlahraga' => $listOlahraga,
            'listAspirin' => $listAspirin,
            'listObatantisakit' => $listObatantisakit,
            'listPerdarahan' => $listPerdarahan,
            'listJantung' => $listJantung,
            'listPembekuan' => $listPembekuan,
            'listIramajantung' => $listIramajantung,
            'listPembiusan' => $listPembiusan,
            'listHipertensi' => $listHipertensi,
            'listDemamtinggi' => $listDemamtinggi,
            'listTuberkulosis' => $listTuberkulosis,
            'listDiabetes' => $listDiabetes,
            'listPenyakitberat' => $listPenyakitberat,
            'listPerdarahan_e' => $listPerdarahan_e,
            'listJantung_e' => $listJantung_e,
            'listPembekuan_e' => $listPembekuan_e,
            'listIramajantung_e' => $listIramajantung_e,
            'listMaag_e' => $listMaag_e,
            'listAnemia_e' => $listAnemia_e,
            'listAsma_e' => $listAsma_e,
            'listPingsan_e' => $listPingsan_e,
            'listMengorok_e' => $listMengorok_e,
            'listPembiusan_e' => $listPembiusan_e,
            'listHipertensi_e' => $listHipertensi_e,
            'listHepatitis_e' => $listHepatitis_e,
            'listKejang_e' => $listKejang_e,
            'listDemamtinggi_e' => $listDemamtinggi_e,
            'listPenyakitbawaan_e' => $listPenyakitbawaan_e,
            'listDiabetes_e' => $listDiabetes_e,
            'listPenyakitberat_e' => $listPenyakitberat_e,
            'listTransfusiDarah' => $listTransfusiDarah,
            'listHiv' => $listHiv,
            'listPasienmemakai' => $listPasienmemakai,
            'listMenyusui' => $listMenyusui,
        );

        $this->load->view('Pengkajian/sedasi/pengkajianPraSedasi/modalSttsKesSaatIniAnesSed', $dataEdit);
    }

    public function detail()
    {
        $post = $this->input->post(null, true);
        // echo '<pre>';print_r($post);exit();
        $data = $this->PengkajianPraSedasiModel->detail($post['id'], $post['dataSource']);
        echo json_encode(
            array(
                'status' => 'success',
                'data' => $data,
            )
        );
    }
}

/* End of file PengkajianPraSedasi.php */
/* Location: ./application/controllers/sedasi/PengkajianPraSedasi.php */