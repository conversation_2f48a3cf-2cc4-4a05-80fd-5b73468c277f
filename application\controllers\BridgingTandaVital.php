<?php
defined('BASEPATH') or exit('No direct script access allowed');

class BridgingTandaVital extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    // if ($this->session->userdata('logged_in') == false) {
    //   redirect('login');
    // }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {

    header('Content-type: application/json');

    $data = json_decode(file_get_contents('php://input'));
    log_message('debug', json_encode($data), false);

    $simpanLog = array(
      'data_source'       => 49,
      'ref'               => $data->RecordNo,
      'nomr'              => $data->Member->BarCode,
      'measure_time'      => $data->MeasureTime,
      'log'               => json_encode($data),
      'http_code'         => http_response_code(),
    );
    $this->db->insert('db_pasien.tb_log_bridging', $simpanLog);

    if(http_response_code() == 200 && $data->Member->Name != "Guest"){
      $getDt = $this->pengkajianAwalModel->getNokunYgSedangAktif($data->Member->BarCode);
      $simpanTandaVital = array(
        'data_source'   => 49,
        'ref'           => $data->RecordNo,
        'nomr'          => $data->Member->BarCode,
        'nokun'         => isset($getDt['NOKUN']) ? $getDt['NOKUN'] : 0,
        'td_sistolik'   => $data->BloodPressure->HighPressure,
        'td_diastolik'  => $data->BloodPressure->LowPressure,
        'nadi'          => $data->BloodPressure->Pulse,
        'suhu'          => $data->Temperature->Temperature,
        'oleh'          => 9902,
        'status'        => 1,
        'created_at'    => $data->MeasureTime,
      );
      $this->db->insert('db_pasien.tb_tanda_vital', $simpanTandaVital);

      $simpanTinggiBerat = array(
        'data_source'   => 49,
        'ref'           => $data->RecordNo,
        'nomr'          => $data->Member->BarCode,
        'nokun'         => isset($getDt['NOKUN']) ? $getDt['NOKUN'] : 0,
        'tb'            => $data->Height->Height,
        'bb'            => $data->Height->Weight,
        'oleh'          => 9902,
        'status'        => 1,
        'created_at'    => $data->MeasureTime,
      );

      $this->db->insert('db_pasien.tb_tb_bb', $simpanTinggiBerat);

      $simpanSaturasi = array(
        'data_source'   => 49,
        'ref'           => $data->RecordNo,
        'nomr'          => $data->Member->BarCode,
        'nokun'         => isset($getDt['NOKUN']) ? $getDt['NOKUN'] : 0,
        'saturasi_o2'   => $data->Bo->Oxygen,
        'oleh'          => 9902,
        'status'        => 1,
        'created_at'    => $data->MeasureTime,
      );

      $this->db->insert('db_pasien.tb_o2', $simpanSaturasi);

      echo json_encode(array("success"=>"true","message"=> "Upload successful"));
    }else{
      if($data->Member->Name == "Guest"){
        echo json_encode(array("success"=>"true","message"=> "Upload successful"));
      }else{
        echo json_encode(array("success"=>"false","message"=> "error message"));
      }
    }
  }

}