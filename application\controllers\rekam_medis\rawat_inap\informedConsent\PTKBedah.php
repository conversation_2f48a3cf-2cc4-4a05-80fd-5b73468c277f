<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PTKBedah extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/informedConsent/PTKBedahModel'
    ));
  }

  public function index(){
  	// $norm = $this->uri->segment(6);
    // $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      // 'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'DasarDiagnosisPTKBedah' => $this->masterModel->referensi(1808),
      'TindakanKedokteranPTKBedah' => $this->masterModel->referensi(1809),
      'IndikasiTindakanPTKBedah' => $this->masterModel->referensi(1810),
      'TujuanPengobatanPTKBedah' => $this->masterModel->referensi(1811),
      'TujuanTindakanPTKBedah' => $this->masterModel->referensi(1812),
      'RisikoPTKBedah' => $this->masterModel->referensi(1813),
      'KomplikasiPTKBedah' => $this->masterModel->referensi(1814),
      'PrognosisPTKBedah' => $this->masterModel->referensi(1815),
      'AlternatifPTKBedah' => $this->masterModel->referensi(1816),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );
     // print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/informedConsent/PTKBedah/index', $data);
  }

  public function simpanPTKBedah()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $date = $this->input->post('datePickerPTKBedah');
    $tglPersetujuanPTKBedah = date('Y-m-d h:i', strtotime($date));

    $dataInformedConcent = array (
      'nokun'                  => $post['nokun'],
      'jenis_informed_consent' => 3032,
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanPTKBedah'],
      'penerima_informasi'     => $post['penerimaInformasiPTKBedah'],
      'pemberi_informasi'      => $post['pemberiInformasiPTKBedah'],
      'oleh'                   => $this->session->userdata('id'),
    );
    // echo "<pre>";print_r($dataInformedConcent);echo "</pre>";

    $idInformedConcent = $this->PTKBedahModel->simpanInformedConcent($dataInformedConcent);

    $dataPTKBedah = array (
      // 'id_informed_consent'              => 1,
      'id_informed_consent'              => $idInformedConcent,
      'diagnosis_wd_dd'                  => isset($post["DiagnosisWDDDPTKBedah"]) ? $post['DiagnosisWDDDPTKBedah'] : NULL,
      'dasar_diagnosis'                  => implode(',',$post["DasarDiagnosisPTKBedah"]),
      'dasar_diagnosis_desk'             => isset($post["deskDasarDiagnosisPTKBedah"]) ? $post['deskDasarDiagnosisPTKBedah'] : NULL,
      'tindakan_kedokteran'              => implode(',',$post["TindakanKedokteranPTKBedah"]),
      'indikasi_tindakan'                => implode(',',$post["IndikasiTindakanPTKBedah"]),
      'indikasi_tindakan_desk'           => isset($post["deskIndikasiTindakanPTKBedah"]) ? $post['deskIndikasiTindakanPTKBedah'] : NULL,
      'tujuan_pengobatan'                => implode(',',$post["TujuanPengobatanPTKBedah"]),
      'tujuan_pengobatan_desk'           => isset($post["deskTujuanPengobatanPTKBedah"]) ? $post['deskTujuanPengobatanPTKBedah'] : NULL,
      'tujuan_tindakan'                  => implode(',',$post["TujuanTindakanPTKBedah"]),
      'tujuan_tindakan_desk'             => isset($post["deskTujuanTindakanPTKBedah"]) ? $post['deskTujuanTindakanPTKBedah'] : NULL,
      'risiko'                           => implode(',',$post["RisikoPTKBedah"]),
      'risiko_desk'                      => isset($post["deskRisikoPTKBedah"]) ? $post['deskRisikoPTKBedah'] : NULL,
      'komplikasi'                       => implode(',',$post["KomplikasiPTKBedah"]),
      'komplikasi_desk'                  => isset($post["deskKomplikasiPTKBedah"]) ? $post['deskKomplikasiPTKBedah'] : NULL,
      'prognosis'                        => implode(',',$post["PrognosisPTKBedah"]),
      'alternatif'                       => implode(',',$post["AlternatifPTKBedah"]),
      'alternatif_desk'                  => isset($post["deskAlternatifPTKBedah"]) ? $post['deskAlternatifPTKBedah'] : NULL,
      'ttd_menerangkan'                  => file_get_contents($this->input->post('ttd_menerang_PTKBedah')),
      'ttd_menerima'                     => file_get_contents($this->input->post('ttd_terima_PTKBedah')),
    );
    // echo "<pre>";print_r($dataPTKBedah);echo "</pre>";

    $this->PTKBedahModel->simpanPTKBedah($dataPTKBedah);

    $dataPersetujuanPTKBedah = array(
      // 'id_informed_consent'        => 1,
      'id_informed_consent'        => $idInformedConcent,
      'nama_keluarga'              => $post['namaPTKBedah'],
      'umur_keluarga'              => $post['umurPTKBedah'],
      'jk_keluarga'                => $post['jenis_kelaminPTKBedah'],
      'alamat_keluarga'            => $post['alamatPTKBedah'],
      'tindakan'                   => $post['tindakanPTKBedah'],
      'hub_keluarga_dgn_pasien'    => $post['hubunganPTKBedah'],
      'tanggal_persetujuan'        => $tglPersetujuanPTKBedah,
      'ttd_menyatakan'             => file_get_contents($this->input->post('sign_Menyatakan_PTKBedah')),
      'ttd_saksi_keluarga'         => file_get_contents($this->input->post('sign_KeluargaPTKBedah')),
      'ttd_saksi_rumah_sakit'      => file_get_contents($this->input->post('sign_RumahSakitPTKBedah')),
      'saksi_keluarga'             => $post['nama_keluarga_PTKBedah'],
      'saksi_rumah_sakit'          => $post['nama_saksi_rs_PTKBedah'],
      'status_persetujuan'         => 1
    );
    // echo "<pre>";print_r($dataPersetujuanPTKBedah);echo "</pre>";exit();

    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran',$dataPersetujuanPTKBedah);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historyPTKBedah()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    // $nomr = $this->uri->segment(6);
    $listPTKBedah = $this->PTKBedahModel->listHistoryInformedConsentPTKBedah($nomr);

    $data = array();
    $no = 1;
    foreach ($listPTKBedah->result() as $PTKBedah) {

      $data[] = array(
        $no,
        $PTKBedah->nokun,
        $PTKBedah->DOKTERPELAKSANA,
        $PTKBedah->OLEH,
        date("d-m-Y H:i:s",strtotime($PTKBedah->tanggal)),
        '<a href="#modalPTKBedah" class="btn btn-primary btn-block" data-id="'.$PTKBedah->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fas fa-edit"></i> Edit</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listPTKBedah->num_rows(),
      "recordsFiltered" => $listPTKBedah->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalPTKBedah()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    $gpPTKBedah = $this->PTKBedahModel->getPTKBedah($id);
    $nokun = $gpPTKBedah['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    // $explode_diagnosis_wd_dd       = explode(',' , $gpPTKBedah['diagnosis_wd_dd']);
    $explode_dasar_diagnosis       = explode(',' , $gpPTKBedah['dasar_diagnosis']);
    $explode_tindakan_kedokteran   = explode(',' , $gpPTKBedah['tindakan_kedokteran']);
    $explode_indikasi_tindakan     = explode(',' , $gpPTKBedah['indikasi_tindakan']);
    $explode_tujuan_pengobatan     = explode(',' , $gpPTKBedah['tujuan_pengobatan']);
    $explode_tujuan_tindakan       = explode(',' , $gpPTKBedah['tujuan_tindakan']);
    $explode_risiko                = explode(',' , $gpPTKBedah['risiko']);
    $explode_komplikasi            = explode(',' , $gpPTKBedah['komplikasi']);
    $explode_prognosis             = explode(',' , $gpPTKBedah['prognosis']);
    $explode_alternatif            = explode(',' , $gpPTKBedah['alternatif']);
    // $explode_lain_lain             = explode(',' , $gpPTKBedah['lain_lain']);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'gpPTKBedah' => $gpPTKBedah,
    //   'explode_diagnosis_wd_dd'       => $explode_diagnosis_wd_dd,
      'explode_dasar_diagnosis'       => $explode_dasar_diagnosis,
      'explode_tindakan_kedokteran'   => $explode_tindakan_kedokteran,
      'explode_indikasi_tindakan'     => $explode_indikasi_tindakan,
      'explode_tujuan_pengobatan'     => $explode_tujuan_pengobatan,
      'explode_tujuan_tindakan'       => $explode_tujuan_tindakan,
      'explode_risiko'                => $explode_risiko,
      'explode_komplikasi'            => $explode_komplikasi,
      'explode_prognosis'             => $explode_prognosis,
      'explode_alternatif'            => $explode_alternatif,
    //   'explode_lain_lain'             => $explode_lain_lain,
      // Informed Consent
      'DasarDiagnosisPTKBedah' => $this->masterModel->referensi(1808),
      'TindakanKedokteranPTKBedah' => $this->masterModel->referensi(1809),
      'IndikasiTindakanPTKBedah' => $this->masterModel->referensi(1810),
      'TujuanPengobatanPTKBedah' => $this->masterModel->referensi(1811),
      'TujuanTindakanPTKBedah' => $this->masterModel->referensi(1812),
      'RisikoPTKBedah' => $this->masterModel->referensi(1813),
      'KomplikasiPTKBedah' => $this->masterModel->referensi(1814),
      'PrognosisPTKBedah' => $this->masterModel->referensi(1815),
      'AlternatifPTKBedah' => $this->masterModel->referensi(1816),
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );

    $this->load->view('rekam_medis/rawat_inap/informedConsent/PTKBedah/view_edit', $data);
  }

  public function updatePTKBedah()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    $idPTKBedah = $this->input->post('idPTKBedah');
    $post = $this->input->post();

    $dataInformedConcent = array (
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanPTKBedah_edit'],
      'penerima_informasi'     => $post['penerimaInformasiPTKBedah_edit'],
      'pemberi_informasi'      => $post['pemberiInformasiPTKBedah_edit'],
    );

    $this->PTKBedahModel->updateInformedConcent($dataInformedConcent,$id);

    $dataPTKBedah_edit = array (
      'diagnosis_wd_dd'                  => isset($post["DiagnosisWDDDPTKBedah_edit"]) ? $post['DiagnosisWDDDPTKBedah_edit'] : NULL,
      'dasar_diagnosis'                  => implode(',',$post["DasarDiagnosisPTKBedah_edit"]),
      'dasar_diagnosis_desk'             => isset($post["deskDasarDiagnosisPTKBedah_edit"]) ? $post['deskDasarDiagnosisPTKBedah_edit'] : NULL,
      'tindakan_kedokteran'              => implode(',',$post["TindakanKedokteranPTKBedah_edit"]),
      'indikasi_tindakan'                => implode(',',$post["IndikasiTindakanPTKBedah_edit"]),
      'indikasi_tindakan_desk'           => isset($post["deskIndikasiTindakanPTKBedah_edit"]) ? $post['deskIndikasiTindakanPTKBedah_edit'] : NULL,
      'tujuan_pengobatan'                => implode(',',$post["TujuanPengobatanPTKBedah_edit"]),
      'tujuan_pengobatan_desk'           => isset($post["deskTujuanPengobatanPTKBedah_edit"]) ? $post['deskTujuanPengobatanPTKBedah_edit'] : NULL,
      'tujuan_tindakan'                  => implode(',',$post["TujuanTindakanPTKBedah_edit"]),
      'tujuan_tindakan_desk'             => isset($post["deskTujuanTindakanPTKBedah_edit"]) ? $post['deskTujuanTindakanPTKBedah_edit'] : NULL,
      'risiko'                           => implode(',',$post["RisikoPTKBedah_edit"]),
      'risiko_desk'                      => isset($post["deskRisikoPTKBedah_edit"]) ? $post['deskRisikoPTKBedah_edit'] : NULL,
      'komplikasi'                       => implode(',',$post["KomplikasiPTKBedah_edit"]),
      'komplikasi_desk'                  => isset($post["deskKomplikasiPTKBedah_edit"]) ? $post['deskKomplikasiPTKBedah_edit'] : NULL,
      'prognosis'                        => implode(',',$post["PrognosisPTKBedah_edit"]),   
      'alternatif'                       => implode(',',$post["AlternatifPTKBedah_edit"]),
      'alternatif_desk'                  => isset($post["deskAlternatifPTKBedah_edit"]) ? $post['deskAlternatifPTKBedah_edit'] : NULL,
      'oleh_edit'                        => $this->session->userdata('id'),
    );

    // print_r($dataPTKBedah_edit);exit();

    $this->PTKBedahModel->updatePTKBedah($dataPTKBedah_edit,$idPTKBedah);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}