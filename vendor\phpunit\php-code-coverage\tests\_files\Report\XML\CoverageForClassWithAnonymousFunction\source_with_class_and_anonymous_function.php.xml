<?xml version="1.0"?>
<phpunit xmlns="http://schema.phpunit.de/coverage/1.0">
  <file name="source_with_class_and_anonymous_function.php">
    <totals>
      <lines total="19" comments="2" code="17" executable="8" executed="7" percent="87.50%"/>
      <methods count="2" tested="1" percent="50.00%"/>
      <functions count="0" tested="0" percent=""/>
      <classes count="1" tested="0" percent="0.00%"/>
      <traits count="0" tested="0" percent=""/>
    </totals>
    <class name="CoveredClassWithAnonymousFunctionInStaticMethod" start="3" executable="8" executed="7" crap="2.01">
      <package full="" name="" sub="" category=""/>
      <namespace name=""/>
      <method name="runAnonymous" signature="runAnonymous()" start="5" end="18" crap="1.04" executable="3" executed="2" coverage="66.666666666667"/>
      <method name="anonymous function" signature="anonymous function (&amp;$val, $key)" start="11" end="13" crap="1" executable="2" executed="2" coverage="100"/>
    </class>
    <coverage>
      <line nr="7">
        <covered by="ClassWithAnonymousFunction"/>
      </line>
      <line nr="9">
        <covered by="ClassWithAnonymousFunction"/>
      </line>
      <line nr="12">
        <covered by="ClassWithAnonymousFunction"/>
      </line>
      <line nr="13">
        <covered by="ClassWithAnonymousFunction"/>
      </line>
      <line nr="14">
        <covered by="ClassWithAnonymousFunction"/>
      </line>
      <line nr="17">
        <covered by="ClassWithAnonymousFunction"/>
      </line>
      <line nr="18">
        <covered by="ClassWithAnonymousFunction"/>
      </line>
    </coverage>
  </file>
</phpunit>
