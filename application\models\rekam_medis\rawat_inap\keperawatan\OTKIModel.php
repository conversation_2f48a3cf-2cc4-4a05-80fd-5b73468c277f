<?php
defined('BASEPATH') or exit('No direct script access allowed');

class OTKIModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_observasi_tindakan';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function simpan($data)
  {
    $this->db->insert('keperawatan.tb_otki', $data);
  }

  public function ubah($idObservasi, $dataOTKI)
  {
    $this->db->where('keperawatan.tb_otki.id_observasi_tindakan', $idObservasi);
    $this->db->update('keperawatan.tb_otki', $dataOTKI);
  }

  public function history($nokun, $param, $id)
  {
    if (isset($id)) {
      // Detail
      $this->db->select(
        'ot.id, tv.nomr, ot.nokun, ot.tanggal, ot.jam, otki.protese, otki.dosis_pemberian_1, otki.tgl_pemberian_1,
        otki.dosis_pemberian_2, otki.tgl_pemberian_2, otki.dosis_pemberian_3, otki.tgl_pemberian_3,
        otki.dosis_pemberian_4, otki.tgl_pemberian_4, otki.dosis_pemberian_5, otki.tgl_pemberian_5,
        otki.dosis_iodium_1, otki.tgl_iodium_1, otki.dosis_iodium_2, otki.tgl_iodium_2, otki.dosis_iodium_3,
        otki.tgl_iodium_3, otki.dosis_iodium_4, otki.tgl_iodium_4, otki.dosis_iodium_5, otki.tgl_iodium_5,
        tv.td_sistolik, tv.td_diastolik, tv.nadi, tv.pernapasan, tv.suhu, ot.perifer, otki.cairan_masuk,
        otki.cairan_keluar, ot.tindakan_keperawatan, otki.pasien_diantar, otki.dengan, otki.cek_persiapan, otki.demo,
        otki.observasi, otki.banyaknya_obat, otki.memberi_obat, otki.mencatat_urin, otki.mengambil_sampel,
        otki.pengukuran_radiasi, otki.form_perjanjian, master.getNamaLengkapPegawai(peng.NIP) perawat, ot.status'
      );
    } elseif (isset($param)) {
      if ($param == 'jumlah') {
        // Jumlah history
        $this->db->select('ot.id');
      } elseif ($param == 'tabel') {
        // Tabel history
        $this->db->select(
          'ot.id, ot.tanggal, ot.updated_at, ot.jam, master.getNamaLengkapPegawai(peng.NIP) pengisi, ot.status'
        );
      }
    }
    $this->db->from('keperawatan.tb_observasi_tindakan ot');
    $this->db->join('db_pasien.tb_tanda_vital tv', 'ot.id = tv.ref AND tv.data_source = 32');
    $this->db->join('keperawatan.tb_otki otki', 'ot.id = otki.id_observasi_tindakan', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = ot.oleh', 'left');
    if (isset($id)) {
      // Detail
      $this->db->where('ot.id', $id);
      $query = $this->db->get();
      return $query->result_array();
    } elseif (isset($param)) {
      $this->db->where('ot.nokun', $nokun);
      if ($param == 'jumlah') {
        // Jumlah
        $this->db->where('ot.status', 1);
        $query = $this->db->get();
        return $query->num_rows();
      } elseif ($param == 'tabel') {
        // Tabel history
        $this->db->order_by('ot.updated_at', 'desc');
        return $this->db->get();
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}

/* End of file OTKIModel.php */
/* Location: ./application/models/rawat_inap/keperawatan/OTKIModel.php */