<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanOperasiDetailModel extends MY_Model
{
    function __construct()
    {
        parent::__construct();
    }

    public function praBedah($id)
    {
        $this->db->select('pra_bedah.CODE id_pra_bedah, pra_bedah.STR pra_bedah');
        $this->db->from('master.mrconso pra_bedah');
        $this->db->join(
            'medis.tb_laporan_operasi_pra_bedah lopr',
            "lopr.diagnosis_pra_bedah_multiple = pra_bedah.CODE AND pra_bedah.SAB = 'ICD10_1998'",
            'left'
        );
        $this->db->where('lopr.status', 1);
        $this->db->where('lopr.id_laporan_operasi', $id);
        $this->db->group_by('pra_bedah.CODE');
        $query = $this->db->get();
        return $query->result_array();
    }
    public function pascaBedah($id)
    {
        $this->db->select('pasca_bedah.CODE id_pasca_bedah, pasca_bedah.STR pasca_bedah');
        $this->db->from('master.mrconso pasca_bedah');
        $this->db->join(
            'medis.tb_laporan_operasi_pasca_bedah lopr',
            "lopr.diagnosis_pasca_bedah_multiple = pasca_bedah.CODE AND pasca_bedah.SAB = 'ICD10_1998'",
            'left'
        );
        $this->db->where('lopr.status', 1);
        $this->db->where('lopr.id_laporan_operasi', $id);
        $this->db->group_by('pasca_bedah.CODE');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function tindakan($id)
    {
        $this->db->select('tindakan.CODE id_tindakan, tindakan.STR tindakan');
        $this->db->from('master.mrconso tindakan');
        $this->db->join(
            'medis.tb_laporan_operasi_detail lod',
            "lod.tindakan_operasi = tindakan.CODE AND tindakan.SAB = 'ICD9CM_2005'",
            'left'
        );
        $this->db->where('lod.status', 1);
        $this->db->where('lod.id_laporan_operasi', $id);
        $this->db->group_by('tindakan.CODE');
        $query = $this->db->get();
        return $query->result_array();
    }
}

// End of file LaporanOperasiDetailModel.php
// Location: ./application/models/operasi/LaporanOperasiDetailModel.php