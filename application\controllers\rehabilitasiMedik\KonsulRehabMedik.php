<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class KonsulRehabMedik extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Bangkok');
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rehabilitasiMedik/KonsulRehabMedikModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $data = array(
      'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
      'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'transportasi' => $this->masterModel->referensi(323),
      'pelayananRehabMedik' => $this->masterModel->referensi(326),
      'historyRehabMedik' => $this->KonsulRehabMedikModel->tampilHistoryRehabMedik(),
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/rehabilitasiMedik/konsultasi/index', $data);
  }

  public function simpanFormRehabMedik()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $jenisRuangan = $post['jenis_ruangan'];

    // Memilih ruangan awal
    if ($jenisRuangan == '1') {
      $ruanganAwal = $post['ruangan_awal_1'];
    } elseif ($jenisRuangan == '2') {
      $ruanganAwal = $post['ruangan_awal_2'];
    }

    $data = array(
      'ruangan_awal' => $ruanganAwal,
      'kunjungan' => $post['nokun'],
      'dokter_pengirim' => $post['dokter_pengirim'],
      'dokter_rehab_medik' => $post['dokter_tujuan'],
      'tanggal' => date('Y-m-d H:i:s'),
      'tanggal_update' => date('Y-m-d H:i:s'),
      'goal_rehab' => $post['goal_rehab'],
      'transportasi' => $post['transportasi'],
      'transportasi_lainnya' => $post['transportasi_lainnya'],
      'perhatian_khusus' => $post['perhatian_khusus'],
      'pelayanan_rehab_medik' => $post['pelayanan_rehab_medik'],
      'pelayanan_rehab_medik_lainnya' => $post['pelayanan_rehab_medik_lainnya'],
      'ikhtisar_klinik' => $post['ikhtisar_klinik'],
      'program_rehab' => $post['program_rehab'],
      'oleh' => $this->session->userdata('id'),
      'status' => '1',
    );
    $rehab = $this->KonsulRehabMedikModel->insertRehabMedik($data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detailHistoryRehabMedik()
  {
    $id = $this->input->post('id');
    $nokun = $this->uri->segment(5);
    $data = array(
      'id' => $id,
      'isiModal' => $this->KonsulRehabMedikModel->detailHistoryRehabMedik($id),
      'ruanganRawatJalan' => $this->masterModel->ruanganRawatJalan(),
      'ruanganRawatInap' => $this->masterModel->ruanganRawatInap(),
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'transportasi' => $this->masterModel->referensi(323),
      'pelayananRehabMedik' => $this->masterModel->referensi(326),
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/rehabilitasiMedik/konsultasi/history/modal', $data);
  }

  public function ubahRehabMedik()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $data = array(
      'tanggal_update' => date('Y-m-d H:i:s'),
      'goal_rehab' => $post['goal_rehab'],
      'transportasi' => $post['transportasi'],
      'transportasi_lainnya' => $post['transportasi_lainnya'],
      'perhatian_khusus' => $post['perhatian_khusus'],
      'pelayanan_rehab_medik' => $post['pelayanan_rehab_medik'],
      'pelayanan_rehab_medik_lainnya' => $post['pelayanan_rehab_medik_lainnya'],
      'ikhtisar_klinik' => $post['ikhtisar_klinik'],
      'program_rehab' => $post['program_rehab'],
      'oleh' => $this->session->userdata('id'),
      'status' => '1',
    );
    $where = array('id' => $post['id']);
    $rehab = $this->KonsulRehabMedikModel->ubahRehabMedik($where, $data, 'medis.tb_rehab_medik');
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}