<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PersetujuanHemodialisis extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Bangkok');
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'hemodialisis/PersetujuanHemodialisisModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $data = array(
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'diagnosis' => $this->masterModel->referensi(1091),
      'dasarDiagnosis' => $this->masterModel->referensi(1092),
      'tindakanKedokteran' => $this->masterModel->referensi(1093),
      'indikasiTindakan' => $this->masterModel->referensi(1094),
      'tataCara' => $this->masterModel->referensi(1095),
      'tujuanTindakan' => $this->masterModel->referensi(1096),
      'risiko' => $this->masterModel->referensi(1097),
      'komplikasi' => $this->masterModel->referensi(1098),
      'prognosis' => $this->masterModel->referensi(1099),
      'jenisKelamin' => $this->masterModel->referensi(965),
      'tujuanPengobatan' => $this->masterModel->tujuanPengobatan(),
      'historyPersetujuanHemodialisis' => $this->PersetujuanHemodialisisModel->historyPersetujuanHemodialisis(),
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/hemodialisa/persetujuan/index', $data);
  }

  public function simpanPTH()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $dataInformedConsent = array(
      'nokun' => $post['nokun'],
      'jenis_informed_consent' => 3030,
      'dokter_pelaksana' => $post['dokter_pelaksana'],
      'penerima_informasi' => $post['penerima_informasi'],
      'oleh' => $this->session->userdata['id'],
    );
    $idInformedConsent = $this->PersetujuanHemodialisisModel->simpanInformedConsent($dataInformedConsent);

    $dataPTH = array(
      'id_informed_consent' => $idInformedConsent,
      'tanggal' => $post['tanggal'],
      'diagnosis' => isset($post['diagnosis']) ? implode('-', $post['diagnosis']) : null,
      'dasar_diagnosis' => isset($post['dasar_diagnosis']) ? implode('-', $post['dasar_diagnosis']) : null,
      'keterangan_dasar_diagnosis' => $post['keterangan_dasar_diagnosis'],
      'tindakan_kedokteran' => isset($post['tindakan_kedokteran']) ? implode('-', $post['tindakan_kedokteran']) : null,
      'indikasi_tindakan' => isset($post['indikasi_tindakan']) ? implode('-', $post['indikasi_tindakan']) : null,
      'tata_cara' => isset($post['tata_cara']) ? implode('-', $post['tata_cara']) : null,
      'tujuan_tindakan' => isset($post['tujuan_tindakan']) ? implode('-', $post['tujuan_tindakan']) : null,
      'risiko' => isset($post['risiko']) ? implode('-', $post['risiko']) : null,
      'komplikasi' => isset($post['komplikasi']) ? implode('-', $post['komplikasi']) : null,
      'prognosis' => isset($post['prognosis']) ? implode('-', $post['prognosis']) : null,
      'alternatif_risiko' => $post['alternatif_risiko'],
      'lainnya' => $post['lainnya'],
      'ttd_menerangkan' => file_get_contents($this->input->post('ttd_menerangkan')),
      'ttd_menerima' => file_get_contents($this->input->post('ttd_menerima')),
      'status' => '1',
    );
    /*echo "<pre>";print_r($data);exit();*/
    $simpanPTH = $this->PersetujuanHemodialisisModel->simpanPTH($dataPTH);

    $dataPersetujuanTindakanKedokteran = array(
      'id_informed_consent' => $idInformedConsent,
      'nama_keluarga' => $post['nama'],
      'umur_keluarga' => $post['umur'],
      'jk_keluarga' => $post['jenis_kelamin'],
      'alamat_keluarga' => $post['alamat'],
      'tindakan' => $post['tindakan'],
      'hub_keluarga_dgn_pasien' => $post['hubungan'],
      'tanggal_persetujuan' => $post['tanggal_setuju'],
      'ttd_menyatakan' => file_get_contents($this->input->post('ttd_menyatakan')),
      'ttd_saksi_keluarga' => file_get_contents($this->input->post('ttd_keluarga')),
      'ttd_saksi_rumah_sakit' => file_get_contents($this->input->post('ttd_rumah_sakit')),
      'saksi_keluarga' => $post['nama_keluarga'],
      'saksi_rumah_sakit' => $post['nama_saksi_rs'],
    );
    $this->PersetujuanHemodialisisModel->simpanPersetujuanTidakanKedokteran($dataPersetujuanTindakanKedokteran);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function modalPTH()
  {
    $id = $this->input->post('id');
    $nokun = $this->input->post('nokun');
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $detailPTH = $this->PersetujuanHemodialisisModel->detailPTH($id);
    $data = array(
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'diagnosis' => $this->masterModel->referensi(1091),
      'dasarDiagnosis' => $this->masterModel->referensi(1092),
      'tindakanKedokteran' => $this->masterModel->referensi(1093),
      'indikasiTindakan' => $this->masterModel->referensi(1094),
      'tataCara' => $this->masterModel->referensi(1095),
      'tujuanTindakan' => $this->masterModel->referensi(1096),
      'risiko' => $this->masterModel->referensi(1097),
      'komplikasi' => $this->masterModel->referensi(1098),
      'prognosis' => $this->masterModel->referensi(1099),
      'jenisKelamin' => $this->masterModel->referensi(965),
      'tujuanPengobatan' => $this->masterModel->tujuanPengobatan(),
      'getNomr' => $getNomr,
      'detailPTH' => $detailPTH,

      // Isi checkbox
      'isiDiagnosis' => isset($detailPTH['diagnosis']) ? explode('-', $detailPTH['diagnosis']) : null,
      'isiDasarDiagnosis' => isset($detailPTH['dasar_diagnosis']) ? explode('-', $detailPTH['dasar_diagnosis']) : null,
      'isiTindakanKedokteran' => isset($detailPTH['tindakan_kedokteran']) ? explode('-', $detailPTH['tindakan_kedokteran']) : null,
      'isiIndikasiTindakan' => isset($detailPTH['indikasi_tindakan']) ? explode('-', $detailPTH['indikasi_tindakan']) : null,
      'isiTataCara' => isset($detailPTH['tata_cara']) ? explode('-', $detailPTH['tata_cara']) : null,
      'isiTujuanTindakan' => isset($detailPTH['tujuan_tindakan']) ? explode('-', $detailPTH['tujuan_tindakan']) : null,
      'isiRisiko' => isset($detailPTH['risiko']) ? explode('-', $detailPTH['risiko']) : null,
      'isiKomplikasi' => isset($detailPTH['komplikasi']) ? explode('-', $detailPTH['komplikasi']) : null,
      'isiPrognosis' => isset($detailPTH['prognosis']) ? explode('-', $detailPTH['prognosis']) : null,
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/hemodialisa/persetujuan/history/modal', $data);
  }

  public function batalPTH()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $data = array(
      'status' => 0,
    );
    $id = array('id' => $post['id_tic']);
    $this->PersetujuanHemodialisisModel->ubahInformedConcent($id, $data);

    $data = array(
      'tgl_update' => date("Y-m-d H:i:s"),
      'status' => 0,
    );
    $id = array('id_informed_consent' => $post['id_tic']);
    $this->PersetujuanHemodialisisModel->ubahPTH($id, $data);

    $data = array(
      'status_persetujuan' => 0,
    );
    $id = array('id_informed_consent' => $post['id_tic']);
    $this->PersetujuanHemodialisisModel->ubahTPTK($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}