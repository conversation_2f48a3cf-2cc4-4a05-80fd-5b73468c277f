<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class InstrumenMmse extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','geriatri/InstrumenMmse_Model'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);

    $data = array(
      'getNomr' => $getNomr,
      'dominisasiHemisfer' => $this->masterModel->referensi(940),
      'kesadaranResponden' => $this->masterModel->referensi(941),
    );
    $this->load->view('Pengkajian/geriatri/instrumenEvalusiMmse/index', $data);
  }

  public function simpanFInstrumenMmse()
  {
    if($this->input->post("catatanNotApp") == 'on'){
      $not_catatan = 1;
    }else{
      $not_catatan = 0;
    }

    $data= array(
      'nokun'              => $this->input->post('nokun'),
      'dominansi'          => $this->input->post('dominisasiHemisfer'),
      'kesadaranResponden' => $this->input->post('kesadaranResponden'),
      'mmse_1'             => $this->input->post('mmse1'),
      'mmse_2'             => $this->input->post('mmse2'),
      'mmse_3'             => $this->input->post('mmse3'),
      'mmse_4'             => $this->input->post('mmse4'),
      'mmse_5'             => $this->input->post('mmse5'),
      'mmse_6'             => $this->input->post('mmse6'),
      'mmse_7'             => $this->input->post('mmse7'),
      'mmse_8'             => $this->input->post('mmse8'),
      'mmse_9'             => $this->input->post('mmse9'),
      'mmse_10'            => $this->input->post('mmse10'),
      'mmse_11'            => $this->input->post('mmse11'),
      'catatan'            => $this->input->post('catatanMmse'),
      'not_catatan'        => $not_catatan,
      'oleh'               => $this->session->userdata('id'),
    );
    $this->InstrumenMmse_Model->simpanFInstrumenMmse($data);
  }

  public function historyInstrumenMmse()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    $listInstrumenMmse = $this->InstrumenMmse_Model->listInstrumenEvaluasi($nomr);

    $data = array();
    $no = 1;
    foreach ($listInstrumenMmse->result() as $ltm) {
      $data[] = array(
        $no,
        $ltm->nokun,
        date("d-m-Y H:i:s",strtotime($ltm->tanggal)),
        '<a href="#editModalMmse" class="btn btn-custom btn-block" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="'.$ltm->id.'"><i class="fas fa-edit"></i> Edit </a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listInstrumenMmse->num_rows(),
      "recordsFiltered" => $listInstrumenMmse->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalInstrumenMmse()
  {
    $id = $this->input->post('id');
    $datInstrumenMmse = $this->InstrumenMmse_Model->getInstrumenMmse($id);

    $data = array(
      'id'                 => $id,
      'datInstrumenMmse'   => $datInstrumenMmse,
      'dominisasiHemisfer' => $this->masterModel->referensi(940),
      'kesadaranResponden' => $this->masterModel->referensi(941),
    );

    $this->load->view('Pengkajian/geriatri/instrumenEvalusiMmse/edit', $data);
  }

  public function updateFInstrumenMmse()
  {
    $id = $this->input->post('id');

    if($this->input->post("catatanNotAppEdit") == ''){
      $not_catatanEdit = 0;
    }else{
      $not_catatanEdit = 1;
    }

    $data= array(
      'dominansi'          => $this->input->post('dominisasiHemisferEdit'),
      'kesadaranResponden' => $this->input->post('kesadaranRespondenEdit'),
      'mmse_1'             => $this->input->post('mmse1Edit'),
      'mmse_2'             => $this->input->post('mmse2Edit'),
      'mmse_3'             => $this->input->post('mmse3Edit'),
      'mmse_4'             => $this->input->post('mmse4Edit'),
      'mmse_5'             => $this->input->post('mmse5Edit'),
      'mmse_6'             => $this->input->post('mmse6Edit'),
      'mmse_7'             => $this->input->post('mmse7Edit'),
      'mmse_8'             => $this->input->post('mmse8Edit'),
      'mmse_9'             => $this->input->post('mmse9Edit'),
      'mmse_10'            => $this->input->post('mmse10Edit'),
      'mmse_11'            => $this->input->post('mmse11Edit'),
      'catatan'            => $this->input->post('catatanMmseEdit'),
      'not_catatan'        => $not_catatanEdit,
    );
    // echo "<pre>";print_r($data);exit();
    $this->InstrumenMmse_Model->updateFInstrumenMmse($data, $id);
  }

}

/* End of file InstrumenMmse.php */
/* Location: ./application/controllers/geriatri/InstrumenMmse.php */
