<?php  if ( ! defined('BASEPATH')) exit('No direct script access allowed');
if ( ! function_exists('tanggal<PERSON>erah'))
{
    function tanggalMerah($value)
    {
        $array = json_decode(file_get_contents(base_url('assets/admin/dist/calendar.json')),true);
        // $array = json_decode(file_get_contents("https://raw.githubusercontent.com/guangrei/Json-Indonesia-holidays/master/calendar.json"),true);
        //check tanggal merah berdasarkan libur nasional
        if(isset($array[$value])){
            echo"tanggal merah ".$array[$value]["deskripsi"];
        }elseif(date("D",strtotime($value))==="Sun"){
            echo"Hari Minggu";
        }elseif(date("D",strtotime($value))==="Sat"){
            echo"Hari Sabtu";  
        }else{
            echo"bukan tanggal merah";
        }
    }
}