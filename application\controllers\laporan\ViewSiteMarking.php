<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ViewSiteMarking extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('laporanModel','masterModel','siteMarkingModel'));
  }

  public function index()
  {
    $data = array(
      'title' => 'Halaman View Site Marking',
      'isi'   => 'Laporan/SiteMarking/cariMr',
    );
    $this->load->view('layout/wrapper',$data);
  }

  public function dataSiteMarking()
  {
    $nomr               = $this->input->post('nomr');
    $namaPasien         = $this->laporanModel->namaPasien($nomr);
    $laporanSiteMarking = $this->siteMarkingModel->listSiteMarking($nomr)->result_array();

    $data = array(
      'title'              => 'Halaman View Site Marking',
      'isi'                => 'Laporan/SiteMarking/index',
      'nomr'               => $nomr,
      'namaPasien'         => $namaPasien,
      'laporanSiteMarking' => $laporanSiteMarking
    );


    $this->load->view('layout/wrapper',$data);
  }

  public function tblSiteMarkingList()
  {
    $draw   = intval($this->input->post("draw"));
    $start  = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblSiteMarking = $this->siteMarkingModel->listSiteMarking($nomr);

    $data = array();
    $no = 1;
    foreach ($tblSiteMarking->result() as $tsm) {
      $data[] = array(
        $no,
        $tsm->judul,
        $tsm->nokun,
        date("d-m-Y H:i:s",strtotime($tsm->tanggal)),
        $tsm->OLEH,
        '<a href="#detailSiteMarkingL" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $tsm->id . '"><i class="fas fa-edit"></i> Edit</a>'
      );

      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $tblSiteMarking->num_rows(),
      "recordsFiltered" => $tblSiteMarking->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function detailSiteMarking()
  {
    $id = $this->input->post("id");
    $hasilFotoSm = $this->siteMarkingModel->hasilFotoSm($id);

    echo '<div class="modal-header">';
    echo '<h4 class="modal-title" id="myModalLabel">Summary List ' . date("d-m-Y", strtotime($hasilFotoSm["tanggal"])) . '</h4>';
    echo '<button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>';
    echo '</div>';
    echo '<div class="modal-body">';

    echo '
    <div class="row">
    <div class="col-sm-12">
    <div class="form-group">
    <h4 class="card-title">History Tanggal [ <span style="color:#e96048;">' . date("d-m-Y H:i:s",strtotime($hasilFotoSm['tanggal'])) . '</span> ]</h4>
    </div>
    </div>
    </div>

    <div class="row">
    <div class="col-sm-12">
    <div class="form-group">
    <label for="judulSiteMarking">Judul Site Marking</label>
    <input type="text" class="form-control" placeholder="[ Judul Site Marking ]" value="' . $hasilFotoSm['judul'] . '" readonly>
    </div>
    </div>
    </div>

    <div class="row">
    <div class="col-sm-12">
    <div class="form-group">
    <label>Hasil Foto Site Marking</label><br>
    <img src="data:image;base64,' . base64_encode($hasilFotoSm['data']) . '">
    </div>
    </div>
    </div>

    <div class="row">
    <div class="col-sm-12">
    <div class="form-group">
    <label for="catatan">Catatan</label>
    <textarea class="form-control" cols="15" rows="10" placeholder="[Catatan ]"  readonly>' . $hasilFotoSm['catatan'] . '</textarea>
    </div>
    </div>
    </div>';
    echo '</div>';
    echo '<div class="modal-footer">';

    echo '<a href="/reports/simrskd/sitemarking/sitemarking.php?format=pdf&id=' . $hasilFotoSm['id'] . '" class="btn btn-success btn-block btn-sm" target="_blank"><i class="fa fa-eye"></i> Cetak</a>';
    echo '</div>';

  }

}

/* End of file ViewSiteMarking.php */
/* Location: ./application/controllers/laporan/ViewSiteMarking.php */
