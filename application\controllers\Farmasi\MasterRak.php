<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MasterRak extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('Farmasi/MappingRakObatModel','masterModel','masterModel'));
  }

  public function index()
  {
    $data = array(
      'title'                => 'Master Rak Obat',
      'isi'                  => 'Farmasi/masterRak',
      'depoFarmasi'          => $this->MappingRakObatModel->depoFarmasi(),
      'historyMasterFarmasi' => $this->MappingRakObatModel->historyMasterFarmasi()->result_array(),

    );
    $this->load->view('layout/wrapper',$data);
  }

  public function pilihRuangan()
  {
    $id = $this->input->post('id');
    $ruanganPenyimpanan = $this->MappingRakObatModel->ruanganPenyimpanan($id);
    echo '<select name="ruanganPenyimpanan" class="form-control">';
    echo '<option disabled selected>Pilih Depo Farmasi</option>';
    foreach($ruanganPenyimpanan as $rp):
      echo '<option value="'.$rp['ID_RUANG_PENYIMPANAN'].'">'.$rp['NAMA_RUANGAN_PENYIMPANAN'].'</option>';
    endforeach;
    echo '</select>';
  }

  public function simpanMasterFarmasi()
  {
    $post = $this->input->post();

    $data= array(
      'id_ruangan_penyimpanan' => $post["ruanganPenyimpanan"],
      'nama_rak'               => $post["namaRakObat"],
      'jumlah_slot'            => $post["jumlahSlotRak"],
      'keterangan_rak'         => $post["keterangan"],
      'oleh'                   => $this->session->userdata("id"),
    );

    $this->MappingRakObatModel->simpanMasterFarmasi($data);
  }


}

/* End of file MasterRak.php */
/* Location: ./application/controllers/Farmasi/MasterRak.php */
