<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPaliatifRIModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_keperawatan';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>ka.'
      ),
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function getNomrPalRI($nopen)
  {
    $query = $this->db->query(
      "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
        , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , pas.JENIS_KELAMIN ID_JK
        , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
        , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
        , p.TANGGAL TANGGAL_DAFTAR
        , r.JENIS_KUNJUNGAN
        , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
        , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
        ) DESKRIPSI_DIAGNOSA_MASUK
        , ref.ID IDPENJAMIN
        , ref.DESKRIPSI PENJAMIN
        , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
        dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
        )))) STATUS_KUNJUNGAN
        , penggu.ID ID_USER
        , dok.ID ID_DOKTER
        , pas.TANGGAL_LAHIR
        , dtt.NAME_PIC
        , pk.REF
        , mkp.NOMOR NOTLPN
        ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) ID_TUJUAN_FARMASI

        , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) TUJUAN_FARMASI
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
        , ppk.NAMA RUJUKAN_DARI
        , refdar.DESKRIPSI GOL_DARAH
        , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=1
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_KEPERAWATAN_DEWASA_RI
        , (SELECT id_emr FROM medis.tb_medis kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=8
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_MEDIS_DEWASA_RI

        FROM pendaftaran.pendaftaran p
        LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
        LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
        LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND refdar.JENIS=6
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
        LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
        LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM

        WHERE tp.`STATUS` not in (0,1)
        AND p.`STATUS`!= 0
        AND pk.`STATUS` != 0 AND p.NOMOR = '$nopen'
        GROUP BY dtt.ID #DESC"
    );
    return $query->row_array();
  }

  public function getPengkajianPaliatif($idemr)
  {
    $query = $this->db->query('SELECT kp.id_emr ID_EMR, kp.created_at TANGGAL_PEMBUATAN_EMR
    , master.getNamaLengkapPegawai(peng.NIP) USER

    , p.NOMOR NOPEN, p.TANGGAL TANGGAL_DAFTAR, rp.DESKRIPSI RUANGAN_PENDAFTARAN
    , refpj.DESKRIPSI PENJAMIN
    , dm.ICD DIAGNOSA_MASUK
    , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1) DESKRIPSI_DIAGNOSA_MASUK
    , master.getNamaLengkapPegawai(dpjp.NIP) DPJP

    , pk.NOMOR KUNJUNGAN, pk.MASUK TANGGAL_MASUK_RUANGAN, pk.KELUAR TANGGAL_KELUAR_RUANGAN
    , rk.DESKRIPSI RUANGAN_KUNJUNGAN

    , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
    , IF(pas.JENIS_KELAMIN=1,"Laki-Laki","Perempuan") JK
    , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
    , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
    , TIMEDIFF(NOW(),(SELECT kep.created_at FROM keperawatan.tb_keperawatan kep
    LEFT JOIN pendaftaran.pendaftaran pen ON pen.NOMOR = kep.nopen
    WHERE kep.status=1 AND pen.NORM=p.NORM ORDER BY kep.created_at DESC LIMIT 1)) DURATION_KEPERAWATAN
    , IF(TIMEDIFF(NOW(), kp.created_at)<"24:00:00", 1,0) STATUS_EDIT

    , db_master.getIDAsuhanKeperawatan(kp.id_emr) ID_ASUHAN_KEPERAWATAN
    , db_master.getAsuhanKeperawatan(kp.id_emr) ASUHAN_KEPERAWATAN
    , db_master.getIDAsuhanKeperawatanDiagnosa(kp.id_emr) ID_DIAGNOSA_KEP
    , db_master.getAsuhanKeperawatanDiagnosa(kp.id_emr) DIAGNOSA_KEP
    , db_master.getIDAsuhanKeperawatanNOC(kp.id_emr) ID_NOC
    , db_master.getAsuhanKeperawatanNOC(kp.id_emr) NOC
    , db_master.getIDAsuhanKeperawatanNIC(kp.id_emr) ID_NIC
    , db_master.getAsuhanKeperawatanNIC(kp.id_emr) NIC
    , kp.`status` STATUS_EMR
    , db_master.getLainLainIDDiagnosa(kp.id_emr) ID_DIAGNOSA_LAIN_LAIN
    , db_master.getLainLainDiagnosa(kp.id_emr) DIAGNOSA_LAIN_LAIN

    , db_master.getLainLainIDNOC(kp.id_emr) ID_NOC_LAIN_LAIN
    , db_master.getLainLainNOC(kp.id_emr) NOC_LAIN_LAIN

    , db_master.getLainLainIDNIC(kp.id_emr) ID_NIC_LAIN_LAIN
    , db_master.getLainLainNIC(kp.id_emr) NIC_LAIN_LAIN

    /*KESEHATAN SEKARANG PALIATIF*/
    ,ripal.tujuan_perawatan_paliatif
    ,ripal.tujuan_perawatan_lainnya
    ,ripal.keluhan_utama
    ,ripal.lama_keluhan
    ,ripal.diagnosis
    ,ripal.metastasis

    /*KESEHATAN SEBELUMNYA PALIATIF*/
    ,rke.riwayat_penyakit
    ,rke.riwayat_penyakit_lainnya
    ,rke.riwayat_pengobatan_operasi
    ,rke.operasi_sebutkan
    ,rke.tanggal_operasi
    ,rke.riwayat_pengobatan_radiasi
    ,rke.radiasi_sebutkan
    ,rke.tanggal_radiasi
    ,rke.riwayat_pengobatan_terapi_sistemik
    ,rke.terapi_sistemik_sebutkan
    ,rke.tanggal_terapi_sistemik
    ,rke.riwayat_pengobatan_lainnya
    ,rke.alergi
    ,rke.isi_alergi
    ,rke.reaksi_alergi
    ,rke.riwayat_lab_pk
    ,rke.hasil_lab_pk
    ,rke.riwayat_radiologi
    ,rke.hasil_rad
    ,rke.riwayat_pa
    ,rke.hasil_pa

    /*STATUS KESEHATAN SAAT INI PALIATIF*/
    ,pf.kesadaran
    , pf.tekanan_darah TEKANAN_DARAH,pf.per_tekanan_darah PER_TEKANAN_DARAH
    , pf.pernapasan PERNAPASAN
    , pf.nadi NADI
    , pf.suhu SUHU
    , pf.skala_nyeri SKALA_NYERI
    , pf.skala_lelah SKALA_LELAH
    , pf.skala_mual SKALA_MUAL
    , pf.skala_depresi SKALA_DEPRESI
    , pf.skala_cemas SKALA_CEMAS
    , pf.skala_mengantuk SKALA_MENGANTUK
    , pf.skala_nafsu_makan SKALA_NAFSU_MAKAN
    , pf.skala_sehat SKALA_SEHAT
    , pf.skala_sesak_napas SKALA_SESAK_NAFAS
    , pf.skala_masalah SKALA_MASALAH_BERAT
    , pf.psikologi
    , pf.sosial_ekonomi
    , pf.spiritual_budaya
    , pf.topik_pembelajaran
    , pf.topik_pembelajaran_lainnya
    , pf.media_pembelajaran
    , pf.media_pembelajaran_lainnya
	, pf.esas_na
    , pf.mcgill_na
    , kolab.id_variabel_kolaborasi ID_KOLABORASI
    , kolab.sebutkan SEBUTKAN_KOLABORASI

    , nyeri.id_variabel ID_NYERI
    , metod.id_variabel ID_METODE_NYERI
    , sekor.id_variabel ID_SKOR_NYERI
    , peman.farmakologi FARMAKOLOGI
    , peman.non_farmakologi NON_FARMAKOLOGI
    , efeks.id_variabel ID_EFEK_SAMPING
    , peman.keterangan_efek_samping KETERANGAN_EFEK_SAMPING
    , provo.id_variabel ID_PROVOACTIVE
    , quali.id_variabel ID_QUALITY
    , pf.sebutkan_quality QUALITY_LAIN2, pf.isi_regio REGIO, pf.severity SEVERITY
    , nyeti.id_variabel ID_NYERI_TIME
    , pf.isi_time DURASI_NYERI
    , fungs.id_variabel ID_STATUS_FUNGSIONAL
    , pf.skrining_resiko_jatuh_rj RISIKO_JATUH
    , pusin.id_variabel ID_RESIKO_JATUH
    , berdi.id_variabel ID_RESIKO_JATUH_BERDIRI
    , jatuh.id_variabel ID_RESKIO_JATUH_6BLN


    FROM keperawatan.tb_keperawatan kp

    LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kp.nopen
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
    LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
    LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR

    LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
    LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
    LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
    LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
    LEFT JOIN master.ruangan rp ON rp.ID = tp.RUANGAN
    LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS=10

    LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.created_by

    LEFT JOIN keperawatan.tb_riwayat_kesehatan rke ON rke.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel valerg ON valerg.id_variabel = rke.alergi
    LEFT JOIN db_master.variabel vtran ON vtran.id_variabel = rke.riwayat_transfusi
    LEFT JOIN db_master.variabel vralg ON vralg.id_variabel = rke.reaksi_transfusi
    LEFT JOIN db_master.variabel vkebi ON vkebi.id_variabel = rke.kebiasaan
    LEFT JOIN db_master.variabel vrkan ON vrkan.id_variabel = rke.riwayat_kanker
    LEFT JOIN db_master.variabel vmet ON vmet.id_variabel = rke.riwayat_metabolik
    LEFT JOIN db_master.variabel vdd ON vdd.id_variabel = rke.deteksidini

    /* ANYELIR START RIWAYAT KESEHATAN*/
    LEFT JOIN db_master.variabel vekst ON vekst.id_variabel = rke.riwayat_ekstravasasi
    LEFT JOIN db_master.variabel halab ON halab.id_variabel = rke.hasil_laboratorium
    LEFT JOIN db_master.variabel habmp ON habmp.id_variabel = rke.hasil_BMP
    LEFT JOIN db_master.variabel kemot ON kemot.id_variabel = rke.kemoterapi
    LEFT JOIN db_master.variabel tinpe ON tinpe.id_variabel = rke.tindakan_perawatan
    LEFT JOIN db_master.variabel graft ON graft.id_variabel = rke.riwayat_graft
    /* ANYELIR END RIWAYAT KESEHATAN */


    LEFT JOIN keperawatan.tb_pemeriksaan_fisik pf ON pf.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel pksdr ON pksdr.id_variabel = pf.kesadaran
    LEFT JOIN db_master.variabel penbb ON penbb.id_variabel = pf.penurunan_bb
    LEFT JOIN db_master.variabel asmak ON asmak.id_variabel = pf.asupan_berkurang
    LEFT JOIN db_master.variabel nyeri ON nyeri.id_variabel = pf.nyeri
    LEFT JOIN db_master.variabel provo ON provo.id_variabel = pf.provocative
    LEFT JOIN db_master.variabel quali ON quali.id_variabel = pf.quality
    LEFT JOIN db_master.variabel nyeti ON nyeti.id_variabel = pf.time
    LEFT JOIN db_master.variabel fungs ON fungs.id_variabel = pf.status_fungsionl
    LEFT JOIN db_master.variabel pusin ON pusin.id_variabel = pf.vertigo
    LEFT JOIN db_master.variabel berdi ON berdi.id_variabel = pf.sulit_berdiri
    LEFT JOIN db_master.variabel jatuh ON jatuh.id_variabel = pf.jatuh_dlm_6
    LEFT JOIN db_master.variabel psiko ON psiko.id_variabel = pf.psikologis
    LEFT JOIN db_master.variabel hukel ON hukel.id_variabel = pf.hub_keluarga
    LEFT JOIN db_master.variabel fut ON fut.id_variabel = pf.nafkah_utama
    LEFT JOIN db_master.variabel serum ON serum.id_variabel = pf.tinggal
    LEFT JOIN db_master.variabel spiri ON spiri.id_variabel = pf.keyakinan

    LEFT JOIN db_master.variabel alban ON alban.id_variabel = pf.alat_bantu

    LEFT JOIN keperawatan.tb_pemantauan_nyeri peman ON peman.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel metod ON metod.id_variabel = peman.metode
    LEFT JOIN db_master.variabel sekor ON sekor.id_variabel = peman.skor
    LEFT JOIN db_master.variabel efeks ON efeks.id_variabel = peman.efek_samping


    LEFT JOIN keperawatan.tb_edukasi_keperawatan ek ON ek.id_emr = kp.id_emr
    LEFT JOIN db_master.variabel pendi ON pendi.id_variabel = ek.tingkat_pendidikan
    LEFT JOIN db_master.variabel bahas ON bahas.id_variabel = ek.bahasa
    LEFT JOIN db_master.variabel pener ON pener.id_variabel = ek.penerjemah
    LEFT JOIN db_master.variabel infok ON infok.id_variabel = ek.informasi
    LEFT JOIN db_master.variabel hamba ON hamba.id_variabel = ek.hambatan
    LEFT JOIN db_master.variabel belaj ON belaj.id_variabel = ek.pembelajaran
    LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan pak ON pak.id_emr = kp.id_emr
    LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON akd.ID = pak.id_asuhan_keperawatan_detil

    /*PALIATIF*/
    LEFT JOIN keperawatan.tb_riwayat_kesehatan_paliatif ripal ON ripal.id_emr= kp.id_emr
    LEFT JOIN keperawatan.tb_kolaborasi kolab ON kolab.id_emr=kp.id_emr
    WHERE kp.`status`=1 AND kp.id_emr="' . $idemr . '" AND kp.created_by IS NOT NULL');
    return $query->row_array();
  }
}

/* End of file PengkajianPaliatifRIModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/PengkajianPaliatifRI/PengkajianPaliatifRIModel.php */
