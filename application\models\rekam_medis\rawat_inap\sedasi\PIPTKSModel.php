<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PIPTKSModel extends MY_Model{
	protected $_table_name = 'medis.tb_validasi_malnutrisi';
	protected $_primary_key = 'nopen';
	protected $_order_by = 'nopen';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib <PERSON>.',
                        'numeric' => '%s Wajib <PERSON>.'
                ),
        ),		
    );

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('piptks.id, piptks.nokun, piptks.created_at tanggal, ru.DESKRIPSI ruangan, peng.NAMA user');
        $this->db->from('keperawatan.tb_piptks piptks');
        $this->db->join('pendaftaran.kunjungan kun','kun.NOMOR = piptks.nokun','LEFT');
        $this->db->join('pendaftaran.pendaftaran pen','pen.NOMOR = kun.NOPEN','LEFT');
        $this->db->join('master.ruangan ru','kun.RUANGAN = ru.ID','LEFT');
        $this->db->join('aplikasi.pengguna peng','piptks.oleh = peng.ID','LEFT');
        $this->db->where('pen.NORM',$this->input->post('nomr'));
        $this->db->order_by('piptks.created_at', 'DESC');
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count(){
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id_piptks)
    {
      $query = $this->db->query(
        'SELECT piptks.`*` FROM keperawatan.tb_piptks piptks
        WHERE piptks.id = "'.$id_piptks.'"'
      );
      return $query->row_array();
    }

}
