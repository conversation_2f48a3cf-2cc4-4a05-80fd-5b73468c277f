<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MedisDewasa extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel','rekam_medis/MedisModel','pengkajianAwalModel','rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel','rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel', 'rekam_medis/TbBbModel', 'rekam_medis/rawat_inap/keperawatan/pemantauanNyeriModel'));
  }

  public function index()
  {
    // $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
    $pasien = $this->MedisModel->getNomrRawatInap($this->uri->segment(2));

    $data = array(
      'pasien' => $pasien,
      'anamnesis'             => $this->masterModel->referensi(54),
      'riwayatPenyakitDahulu' => $this->masterModel->referensi(55),
      'Ecogg'                  => $this->masterModel->referensi(30),
      'mata'                  => $this->masterModel->referensi(31),
      'leher'                 => $this->masterModel->referensi(32),
      'dadaIramaJantung'      => $this->masterModel->referensi(33),
      'dadaSuaraNafas'        => $this->masterModel->referensi(34),
      'perutHati'             => $this->masterModel->referensi(35),
      'perutLimpa'            => $this->masterModel->referensi(36),
      'ekstremitasAtasKanan'  => $this->masterModel->referensi(37),
      'ekstremitasAtasKiri'   => $this->masterModel->referensi(38),
      'ekstremitasBawahKanan' => $this->masterModel->referensi(39),
      'ekstremitasBawahKiri'  => $this->masterModel->referensi(40),
      'kulitTurgor'           => $this->masterModel->referensi(41),
      'kulitSianosis'         => $this->masterModel->referensi(42),
      'refleks'               => $this->masterModel->referensi(43),
      'kelenjarGetahBening'   => $this->masterModel->referensi(44),
      'aksesVaskuler'         => $this->masterModel->referensi(802),
      'aksesVaskulerLokasi'   => $this->masterModel->referensi(803),
      'aksesVaskulerCDL'      => $this->masterModel->referensi(804),
      'tumor'                 => $this->masterModel->referensi(45),

      'sisiTubuh'        => $this->masterModel->referensi(49),
      'stadium'          => $this->masterModel->stadium(),
      'tujuanPengobatan' => $this->masterModel->referensi(266),
      'diet'             => $this->masterModel->referensi(56),
      'jenisDiet'        => $this->masterModel->referensi(57),

      'kunjungan_pk' => $this->pengkajianAwalModel->kunjungan_pk($pasien['NORM']),
      'sitologi'     => $this->pengkajianAwalModel->sitologi($pasien['NORM']),
      'histologi'    => $this->pengkajianAwalModel->histologi($pasien['NORM']),
      'tindakan_rad' => $this->pengkajianAwalModel->tindakan_rad($pasien['NORM']),

      'performanceStatus_2' => $this->masterModel->referensi(262),
      'Ecog' => $this->masterModel->referensi(239),
      'Karnofsky' => $this->masterModel->referensi(240),
      'Lansky' => $this->masterModel->referensi(267),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS'   => $this->masterModel->referensi(114),
      'skalaNyeriWBR'   => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS'   => $this->masterModel->referensi(133),
      
      'klinis' => $this->masterModel->referensi(256),
      'masalahKesehatan' => $this->masterModel->referensi(181),
      'listVBPJS' => $this->pengkajianAwalModel->listVBPJS($pasien['NORM']),
    );

    // echo "<pre>";print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRI/pengkajianRiDewasaMedis',$data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $this->db->trans_begin();
          $post = $this->input->post();

          $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();


          if(isset($post['tinggi_badan'])){
            $dataTbBb = array(
              'data_source' => 7,
              'nomr' => $this->input->post('nomr'),
              'nokun' => $this->input->post('nokun'),
              'jenis' => 2,
              'tb' => $this->input->post('tinggi_badan'),
              'bb' => $this->input->post('berat_badan'),
              'oleh' =>  $this->session->userdata("id"),
            );
            $id_tb_bb = $this->input->post('id_tb_bb');
            if(empty($id_tb_bb)){
                $id_tb_bb = $this->TbBbModel->insert($dataTbBb);
            }
          }

          if(isset($post['skrining_nyeri'])){
            $dataPemantauanNyeri = array(
              'nokun' => $post['nokun'],
              'data_source' => 7,
              'metode' => $post['skrining_nyeri'],
              'skor' => $post['skrining_nyeri'] != 17 ? $post['skor_nyeri'] : "",
              'created_by' => $this->session->userdata('id'),
            );

            $id_nyeri = $this->input->post('id_nyeri');
            if (empty($id_nyeri)) {
                $id_nyeri = $this->pemantauanNyeriModel->insert($dataPemantauanNyeri);
            }
          }

          $dataMedis = array(
            'id_emr'         => $getIdEmr,
            'nopen'          => $post['nopen'],
            'nokun'          => $post['nokun'],
            'protokol_kemo'  => isset($post['protokol_kemo']) ? $post['protokol_kemo'] : "",
            'siklus_ke'      => isset($post['siklus_ke']) ? $post['siklus_ke'] : "",
            'jenis'          => $post['jenis'],
            'created_by'     => $this->session->userdata('id'),
            'flag'           => '1',
            'tb_bb'          => $id_tb_bb,
            'skrining_nyeri' => $id_nyeri,
          );

          $dataAnamnesa = array(
            'id_emr' => $getIdEmr,
            'id_auto_allo' => $post['anamnesis'],
            'allo_nama' => isset($post['nama']) ? $post['nama'] : "",
            'hubungan_dengan_pasien' => isset($post['hubunganDenganPasien']) ? $post['hubunganDenganPasien'] : "",
            'harapan_keluarga_pasien' => isset($post['harapanKeluargaPasien']) ? $post['harapanKeluargaPasien'] : "",
            'keluhan_utama' => isset($post['keluhanUtama']) ? $post['keluhanUtama'] : "",
            'riwayat_sakit_sekarang' => isset($post['riwayatPenyakitSekarang']) ? $post['riwayatPenyakitSekarang'] : "",
            'riwayat_penyakit_dahulu' => isset($post['riwayatPenyakitDahulu']) ? $post['riwayatPenyakitDahulu'] : "",
            'desk_riwayat_penyakit_dahulu' => isset($post['deskRiwayatPenyakitDahulu']) ? $post['deskRiwayatPenyakitDahulu'] : "",
          );

          if (isset($post['status_lokalis_not']) == "on") {
            $lokaslisNot = 1;
          } else {
            $lokaslisNot = 0;
          }

          $dataPemeriksaanFisik = array(
            'id_emr' => $getIdEmr,
            'performance_status' => isset($post['performanceStatus']) ? $post['performanceStatus'] : "",
            'performance_status_anak' => isset($post['performanceStatus_anak']) ? $post['performanceStatus_anak'] : "",
            'performance_status_anak_score' => isset($post['performanceStatus_anak_skor']) ? $post['performanceStatus_anak_skor'] : "",
            'mata' => isset($post['mata']) ? $post['mata'] : "",
            'mata_lainnya' => isset($post['mataLainya']) ? $post['mataLainya'] : "",
            'leher' => isset($post['leher']) ? $post['leher'] : "",
            'desk_leher_tidak_normal' => isset($post['leherTdkNormal']) ? $post['leherTdkNormal'] : "",
            'irama_jantung' => isset($post['iramaJantung']) ? $post['iramaJantung'] : "",
            'desk_irama_jantung' => isset($post['iramaJantungTdkTerartur']) ? $post['iramaJantungTdkTerartur'] : "",
            'suara_napas' => isset($post['dadaSuaraNafas']) ? $post['dadaSuaraNafas'] : "",
            'desk_suara_napas' => isset($post['suaraNafasTdkNormal']) ? $post['suaraNafasTdkNormal'] : "",
            'perut_hati' => isset($post['perutHati']) ? $post['perutHati'] : "",
            'desk_hati' => isset($post['perutHatiTeraba']) ? $post['perutHatiTeraba'] : "",
            'perut_limpa' => isset($post['perutLimpa']) ? $post['perutLimpa'] : "",
            'desk_limpa' => isset($post['perutLimpaTeraba']) ? $post['perutLimpaTeraba'] : "",
            'eks_atas_kanan' => isset($post['ekstremitasAtasKanan']) ? $post['ekstremitasAtasKanan'] : "",
            'desk_eks_atas_kanan' => isset($post['deskEkstremitasAtasKanan']) ? $post['deskEkstremitasAtasKanan'] : "",
            'eks_atas_kiri' => isset($post['ekstremitasAtasKiri']) ? $post['ekstremitasAtasKiri'] : "",
            'desk_eks_atas_kiri' => isset($post['deskEkstremitasAtasKiri']) ? $post['deskEkstremitasAtasKiri'] : "",
            'eks_bawah_kanan' => isset($post['ekstremitasBawahKanan']) ? $post['ekstremitasBawahKanan'] : "",
            'desk_eks_bawah_kanan' => isset($post['deskEkstremitasBawahKanan']) ? $post['deskEkstremitasBawahKanan'] : "",
            'eks_bawah_kiri' => isset($post['ekstremitasBawahKiri']) ? $post['ekstremitasBawahKiri'] : "",
            'desk_eks_bawah_kiri' => isset($post['deskEkstremitasBawahKiri']) ? $post['deskEkstremitasBawahKiri'] : "",
            'turgor' => isset($post['kulitTurgor']) ? $post['kulitTurgor'] : "",
            'sianosis' => isset($post['kulitSianosis']) ? $post['kulitSianosis'] : "",
            'refleks' => isset($post['refleks']) ? $post['refleks'] : "",
            'desk_refleks' => isset($post['desRefleks']) ? $post['desRefleks'] : "",
            'kgb' => isset($post['kelenjarGetahBening']) ? $post['kelenjarGetahBening'] : "",
            'desk_kgb' => isset($post['deskKelenjarGetahBening']) ? $post['deskKelenjarGetahBening'] : "",
            'tumor' => isset($post['tumor']) ? $post['tumor'] : "",
            'jenis_tumor' => isset($post['deskTumor']) ? $post['deskTumor'] : "",
            'na_lokalis' => isset($lokaslisNot) ? $lokaslisNot : "",
            'klinis' => isset($post['klinis_fisik']) ? $post['klinis_fisik'] : "",
            'luas_permukaan_badan' => isset($post['luas_berat_badan']) ? $post['luas_berat_badan'] : "",
            'bb_u' => isset($post['bb_u']) ? $post['bb_u'] : "",
            'tb_u' => isset($post['tb_u']) ? $post['tb_u'] : "",
            'bb_tb' => isset($post['bb_tb']) ? $post['bb_tb'] : "",
            'imt' => isset($post['imt']) ? $post['imt'] : "",
            'status_gizi' => isset($post['status_gizi']) ? $post['status_gizi'] : "",
            'lainnya' => isset($post['pemeriksaanFisikLain']) ? $post['pemeriksaanFisikLain'] : "",
          );

          $dataPenunjangLainya = array(
            'id_emr' => $getIdEmr,
            'penunjang_lain' => isset($post['penunjangLainya']) ? $post['penunjangLainya'] : "",
          );

          if (isset($post['klasifikasi_not']) == "on") {
            $klasifikasiNot = 1;
          } else {
            $klasifikasiNot = 0;
          }

          $dataMedisKeperawatan = array(
            'id_emr' => $getIdEmr,
            'desk_diagnosa_medis' => isset($post['deskDiagnosisMedis']) ? $post['deskDiagnosisMedis'] : "",
            'desk_diagnosa_kanker' => isset($post['deskDiagnosaKanker']) ? $post['deskDiagnosaKanker'] : "",
            'sisi_tubuh' => isset($post['sisiTubuh']) ? $post['sisiTubuh'] : "",
            'klasifikasi_t' => isset($post['klasifikasi_T']) ? $post['klasifikasi_T'] : "",
            'klasifikasi_n' => isset($post['klasifikasi_N']) ? $post['klasifikasi_N'] : "",
            'klasifikasi_m' => isset($post['klasifikasi_M']) ? $post['klasifikasi_M'] : "",
            'klasifikasi_not' => isset($klasifikasiNot) ? $klasifikasiNot : "",
            'stadium' => isset($post['stadium']) ? $post['stadium'] : "",
            'tujuan_pengobatan' => isset($post['tujuanPengobatan']) ? $post['tujuanPengobatan'] : "",
            'jika_ada_masalah_medis' => isset($post['masalahKesehatan']) ? $post['masalahKesehatan'] : "",
            'jika_ada_masalah_lainnya' => isset($post['deskJikaAdaMasalahLain']) ? $post['deskJikaAdaMasalahLain'] : "",
          );

          $masalahKesehatanMedis = array(
            'id_emr' => $getIdEmr,
            'masalahKesehatan' => isset($post['masalahKesehatanMedis']) ? json_encode($post['masalahKesehatanMedis']) : "",
          );

          $dataPerencanaan = array(
            'id_emr' => $getIdEmr,
            'pemeriksaan_penunjang' => isset($post['pemeriksaanPenunjang']) ? $post['pemeriksaanPenunjang'] : "",
            'terapi_tindakan' => isset($post['terapiTindakan']) ? $post['terapiTindakan'] : "",
            'diet' => isset($post['diet']) ? $post['diet'] : "",
            'jenis_diet' => isset($post['jenisDiet']) ? $post['jenisDiet'] : "",
            'kalori' => isset($post['kalori']) ? $post['kalori'] : "",
            'diet_lainnya' => isset($post['dietLainya']) ? $post['dietLainya'] : "",
            'target_pengobatan' => isset($post['target_pengobatan']) ? $post['target_pengobatan'] : "",
          );

          if (!empty($post['idemr'])) {
            $this->db->replace('medis.tb_anamnesa', $dataAnamnesa);
            $this->db->replace('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
            $this->db->replace('medis.tb_penunjang_lain', $dataPenunjangLainya);
            $this->db->replace('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
            $this->db->replace('medis.tb_masalahkesehatanmedis', $masalahKesehatanMedis);
            $this->db->replace('medis.tb_perencanaan', $dataPerencanaan);
            if ($this->db->replace('medis.tb_medis', $dataMedis)) {
              $result = array('status' => 'success', 'pesan' => 'ubah');
            }
            
            if(isset($post['tinggi_badan'])){
              $this->TbBbModel->update(array('ref' => $getIdEmr), array('id' => $id_tb_bb));
            }
          } else {
            $result = array('status' => 'failed');
            $this->db->insert('medis.tb_anamnesa', $dataAnamnesa);
            $this->db->insert('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
            $this->db->insert('medis.tb_penunjang_lain', $dataPenunjangLainya);
            $this->db->insert('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
            $this->db->insert('medis.tb_masalahkesehatanmedis', $masalahKesehatanMedis);
            $this->db->insert('medis.tb_perencanaan', $dataPerencanaan);
            if(isset($post['tinggi_badan'])){
              $this->TbBbModel->update(array('ref' => $getIdEmr), array('id' => $id_tb_bb));
            }

            if ($this->db->insert('medis.tb_medis', $dataMedis)) {
              $result = array('status' => 'success');
            }
          }

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
      }

      else if($param == 'count'){
        $result = $this->MedisDewasaModel->get_count();;
        echo json_encode($result);
      }

      else if($param == 'ambil'){
        $post = $this->input->post(NULL,TRUE);
        $dataMedisDewasaModel = $this->MedisDewasaModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data'   => $dataMedisDewasaModel
        ));
      }

    }
  }

  public function datatables(){
    $result = $this->MedisDewasaModel->datatables();

    $data = array();
    foreach ($result as $row){
      $sub_array = array();
      $sub_array[] = '<a class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasaMedis" data-id="'.$row -> ID_EMR.'"><i class="fa fa-eye"></i> Lihat</a>';
      $sub_array[] = $row -> TANGGAL_PENGKAJIAN_MEDIS;
      $sub_array[] = $row -> RUANGAN;
      $sub_array[] = $row -> DPJP;
      $sub_array[] = $row -> USER_MEDIS;

      $data[] = $sub_array;
    }

    $output = array(
      "draw"              => intval($_POST["draw"]),
      "recordsTotal"      => $this->MedisDewasaModel->total_count(),
      "recordsFiltered"   => $this->MedisDewasaModel->filter_count(),
      "data"              => $data
    );
    echo json_encode($output);
  }

}

/* End of file MedisDewasa.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasa.php */
