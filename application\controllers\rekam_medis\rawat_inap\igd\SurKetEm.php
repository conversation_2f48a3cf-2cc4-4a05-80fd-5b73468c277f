<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SurKetEm extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel', 'rekam_medis/MedisModel', 'rekam_medis/rawat_inap/igd/SurKetEmModel'));
  }

  public function index()
  {
    $norm = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $kunjungan_pk = $this->pengkajianAwalModel->kunjungan_pk($norm);
    $sitologi = $this->pengkajianAwalModel->sitologi($norm);
    $histologi = $this->pengkajianAwalModel->histologi($norm);
    $tindakan_rad = $this->pengkajianAwalModel->tindakan_rad($norm);
    $diagnosaUtama = $this->SurKetEmModel->diagnosa_utama();
    $historySurKetEm = $this->SurKetEmModel->historySurKetEm($norm);
    $terapiObat = $this->SurKetEmModel->terapiObat($nopen);

    $data = array(
      'norm' => $norm,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'kunjungan_pk' => $kunjungan_pk,
      'sitologi' => $sitologi,
      'histologi' => $histologi,
      'tindakan_rad' => $tindakan_rad,
      'diagnosis_utama' => $diagnosaUtama,
      'historySurKetEm' => $historySurKetEm,
      'terapiObat' => $terapiObat,
      'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
      'idSke' => "",
      'getSur' => "",
      'dataSekunderUpdate' => "",
      'dataTindPro' => "",
    );

    $this->load->view('rekam_medis/rawat_inap/igd/surKetEm', $data);
  }

  public function icd9()
  {
    $result = $this->SurKetEmModel->icd9();
    $data = array();
    foreach ($result as $row ) {
      $sub_array = array();
      $sub_array['id'] = $row -> CODE .'-'. $row -> STR;
      $sub_array['text'] = $row -> CODE .' - '. $row -> STR;
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function icd10()
  {
    $result = $this->SurKetEmModel->icd10();
    $data = array();
    foreach ($result as $row ) {
      $sub_array = array();
      $sub_array['id'] = $row -> CODE .'-'. $row -> STR;
      $sub_array['text'] = $row -> CODE .' - '. $row -> STR;
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function viewEditSurKetEm($norm, $nopen, $nokun, $id)
  {

    $norm = $norm;
    $nopen = $nopen;
    $nokun = $nokun;
    $kunjungan_pk = $this->pengkajianAwalModel->kunjungan_pk($norm);
    $sitologi = $this->pengkajianAwalModel->sitologi($norm);
    $histologi = $this->pengkajianAwalModel->histologi($norm);
    $tindakan_rad = $this->pengkajianAwalModel->tindakan_rad($norm);
    $diagnosaUtama = $this->SurKetEmModel->diagnosa_utama();
    $diagnosaSekunder = $this->SurKetEmModel->diagnosa_utama();
    $tindakanProcedure = $this->SurKetEmModel->tindakan_procedure();
    $historySurKetEm = $this->SurKetEmModel->historySurKetEm($norm);
    $getHistorySurKetEm = $this->SurKetEmModel->getHistorySurKetEm($id);
    $terapiObat = $this->SurKetEmModel->terapiObat($nopen);

    $data = array(
      'norm' => $norm,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'kunjungan_pk' => $kunjungan_pk,
      'sitologi' => $sitologi,
      'histologi' => $histologi,
      'tindakan_rad' => $tindakan_rad,
      'diagnosis_utama' => $diagnosaUtama,
      'diagnosis_sekunder' => $diagnosaSekunder,
      'tindakan_procedure' => $tindakanProcedure,
      'historySurKetEm' => $historySurKetEm,
      'getSur' => $getHistorySurKetEm,
      // 'dataDiagSek' => $dataDiagSek,
      'dataSekunderUpdate' => $getHistorySurKetEm['diagnosa_sekunder'],
      'dataTindPro' => $getHistorySurKetEm['tindakan_procedure'],
      'terapiObat' => $terapiObat,
      'pasien' => $this->pengkajianAwalModel->getNomr($nokun),
      'idSke' => $id
    );

    $this->load->view('rekam_medis/rawat_inap/igd/surKetEm', $data);
  }

  public function simpanSurKetEm()
  {
    $post = $this->input->post();

    $data = array(
      'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
      // 'tglmasuk' => date('Y-m-d',strtotime($post['tglMasuk'])),
      // 'jmmasuk' => isset($post['jamMsk']) ? $post['jamMsk'] : "",
      'tglkeluar' => date('Y-m-d',strtotime($post['tglKeluar'])),
      'jmkeluar' => isset($post['jamKeluar']) ? $post['jamKeluar'] : "",
      'pemeriksaan_fisik' => isset($post['pemFisSurKetEmOld']) ? $post['pemFisSurKetEmOld'] : "",
      'pemeriksaan_dari_luar' => isset($post['penunjangLainya']) ? $post['penunjangLainya'] : "",
      'diagnosa_utama' => isset($post['pilihDiagnosaSurKetEmOld']) ? $post['pilihDiagnosaSurKetEmOld'] : "",
      'catatan_diagnosa_utama' => isset($post['catDiagUtaSurKetEmOld']) ? $post['catDiagUtaSurKetEmOld'] : "",
      'diagnosa_sekunder' => isset($post['pilihDiagnosaSekSurKetEmOld']) ? json_encode($post['pilihDiagnosaSekSurKetEmOld']) : "",
      'catatan_diagnosa_sekunder' => isset($post['catDiagSekSurKetEmOld']) ? $post['catDiagSekSurKetEmOld'] : "",
      'tindakan_procedure' => isset($post['tindakanProSurKetEmOld']) ? json_encode($post['tindakanProSurKetEmOld']) : "",
      'catatan_tindakan_procedure' => isset($post['catTindakanProSurKetEmOld']) ? $post['catTindakanProSurKetEmOld'] : "",
      // 'terapi' => isset($post['catTerapiProSurKetEmOld']) ? $post['catTerapiProSurKetEmOld'] : "",
      'kondisi_pasien_saat_pulang' => isset($post['kondisiPasienPulngSurKetEmOld']) ? $post['kondisiPasienPulngSurKetEmOld'] : "",
      'oleh' => isset($post['oleh']) ? $post['oleh'] : "",
    );

    // echo '<pre>';print_r($data);echo '</pre>'; exit();

    if (!empty($post['idSke'])) {
      $this->db->where('tb_surat_keterangan_emergency.id', $post['idSke']);
      $this->db->update('medis.tb_surat_keterangan_emergency', $data);

      $dataTandaVital = array(
        'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
        'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
        'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
        'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
        'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
        'oleh' => $this->session->userdata('id'),
        'status' => 1,
      );

      $this->db->where('tb_tanda_vital.data_source', 24);
      $this->db->where('tb_tanda_vital.ref', $post['idSke']);
      $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);

    }else{
      $getIdData = $this->SurKetEmModel->simpanData($data);

      $dataTandaVital = array(
        'data_source' => 24,
        'ref' => $getIdData,
        'nomr' => isset($post['norm']) ? $post['norm'] : "",
        'nokun' => $post['nokun'],
        'td_sistolik' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : "",
        'td_diastolik' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : "",
        'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : "",
        'nadi' => isset($post['nadi']) ? $post['nadi'] : "",
        'suhu' => isset($post['suhu']) ? $post['suhu'] : "",
        'map' => isset($post['MAP']) ? $post['MAP'] : "",
        'oleh' => $this->session->userdata('id'),
        'status' => 1,
      );

      $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);

    }
  }

}