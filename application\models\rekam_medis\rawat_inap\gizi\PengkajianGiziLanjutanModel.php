<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianGiziLanjutanModel extends MY_Model
{
    protected $_table_name = 'medis.tb_pengkajian_gizi_lanjutan';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public $rules = array(
        'nopen' => array(
            'field' => 'nopen',
            'label' => 'Nomor Kunjungan',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                'required' => '%s Wajib <PERSON>isi.',
                'numeric' => '%s Wajib Ang<PERSON>.'
            ),
        ),

        'berat_badan' => array(
            'field' => 'berat_badan',
            'label' => 'Berat badan',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'tinggi_badan' => array(
            'field' => 'tinggi_badan',
            'label' => 'Tinggi badan',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'lila' => array(
            'field' => 'lila',
            'label' => 'Lila',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'tilut' => array(
            'field' => 'tilut',
            'label' => 'Tilut',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'perubahan_bb' => array(
            'field' => 'perubahan_bb',
            'label' => 'Perubahan BB',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'energi_assesmen' => array(
            'field' => 'energi_assesmen',
            'label' => 'Energi',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'protein_assesmen' => array(
            'field' => 'protein_assesmen',
            'label' => 'Protein',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'lemak_assesmen' => array(
            'field' => 'lemak_assesmen',
            'label' => 'Lemak',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'kh_assesmen' => array(
            'field' => 'kh_assesmen',
            'label' => 'KH',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'energi_intervensi' => array(
            'field' => 'energi_intervensi',
            'label' => 'Energi',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'protein_intervensi' => array(
            'field' => 'protein_intervensi',
            'label' => 'Protein',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'lemak_intervensi' => array(
            'field' => 'lemak_intervensi',
            'label' => 'Lemak',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),

        'kh_intervensi' => array(
            'field' => 'kh_intervensi',
            'label' => 'KH',
            'rules' => 'trim|numeric',
            'errors' => array(
                'numeric' => '%s Wajib Angka.'
            ),
        ),
    );

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
        $this->db->select('kp.`*`,ru.DESKRIPSI ruangan, peng.NAMA user');
        $this->db->from('medis.tb_pengkajian_gizi_lanjutan kp');
        $this->db->join('pendaftaran.kunjungan kun', 'kun.NOMOR = kp.nokun', 'LEFT');
        $this->db->join('pendaftaran.pendaftaran pen', 'pen.NOMOR = kun.NOPEN', 'LEFT');
        $this->db->join('master.ruangan ru', 'kun.RUANGAN = ru.ID', 'LEFT');
        $this->db->join('aplikasi.pengguna peng', 'kp.oleh = peng.ID', 'LEFT');
        $this->db->where('kp.STATUS !=', '0');
        $this->db->where('pen.NORM', $this->input->post('nomr'));
        //$this->db->group_by('kp.nokun');
        $this->db->order_by('kp.tanggal', 'DESC');
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function getPengkajian($id_gizi)
    {
        $query = $this->db->query(
            'SELECT *, tb.tb, tb.bb 
        FROM medis.tb_pengkajian_gizi_lanjutan gl
        left join db_pasien.tb_tb_bb tb ON tb.ref=gl.id
        WHERE gl.id = "' . $id_gizi . '"'
        );
        return $query->row_array();
    }

    public function getTbbb($nokun)
    {
        $query = $this->db->query(
            'SELECT tbb.bb BB, tbb.tb TB, round(tbb.bb/((tbb.tb/100)*(tbb.tb/100)),2) IMT, 
            mp.JENIS_KELAMIN, year(curdate())-year(mp.TANGGAL_LAHIR)USIA,
            round(IF(mp.JENIS_KELAMIN=1,((10*tbb.tb)+(6.25*tbb.bb)-(5*(year(curdate())-year(mp.TANGGAL_LAHIR)))+5),((10*tbb.tb)+(6.25*tbb.bb)-(5*(year(curdate())-year(mp.TANGGAL_LAHIR)))-161)),2) ENERGI
            FROM db_pasien.tb_tb_bb tbb
            LEFT JOIN master.pasien mp ON tbb.nomr=mp.NORM
        WHERE tbb.nokun="' . $nokun . '" AND tbb.tb !=" "
        ORDER BY tbb.created_at DESC LIMIT 1'
        );
        return $query->row_array();
    }

    public function getPersen($nokun)
    {
        $query = $this->db->query(
            'SELECT stg.dlm_6_bln_persen PERSEN
            FROM medis.tb_penilaian_status_gizi stg
            WHERE stg.nokun="' . $nokun . '"'
        );
        return $query->row_array();
    }

    public function getDiagnosa($nokun)
    {
        $query = $this->db->query(
            'SELECT dia.diagnosa_masuk DIAGNOSA
            FROM keperawatan.tb_keperawatan dia
            WHERE dia.nokun="' . $nokun . '" AND dia.jenis=5'
        );
        return $query->row_array();
    }
}
