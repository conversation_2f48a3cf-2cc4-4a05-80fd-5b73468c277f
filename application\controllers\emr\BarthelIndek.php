<?php
defined('BASEPATH') or exit('No direct script access allowed');

class BarthelIndek extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(6);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listRangsangbab = $this->masterModel->referensi(834);
        $listRangsangberkemih = $this->masterModel->referensi(835);
        $listMembersihkandiri = $this->masterModel->referensi(836);
        $listPenggunaankloset = $this->masterModel->referensi(837);
        $listMakan = $this->masterModel->referensi(838);
        $listBerubahposisi = $this->masterModel->referensi(839);
        $listBerpindah = $this->masterModel->referensi(840);
        $listMemakaibaju = $this->masterModel->referensi(841);
        $listNaiktangga = $this->masterModel->referensi(842);
        $listMandi = $this->masterModel->referensi(843);
        $historyBarthelIndek = $this->pengkajianAwalModel->historyBarthelIndek($getNomr['NORM']);

        $data = array(
            'getNomr' => $getNomr,
            'listRangsangbab' => $listRangsangbab,
            'listRangsangberkemih' => $listRangsangberkemih,
            'listMembersihkandiri' => $listMembersihkandiri,
            'listPenggunaankloset' => $listPenggunaankloset,
            'listMakan' => $listMakan,
            'listBerubahposisi' => $listBerubahposisi,
            'listBerpindah' => $listBerpindah,
            'listMemakaibaju' => $listMemakaibaju,
            'listNaiktangga' => $listNaiktangga,
            'listMandi' => $listMandi,
            'historyBarthelIndek' => $historyBarthelIndek,
        );
        
        $this->load->view('Pengkajian/emr/barthelIndek/index', $data);
    }

    public function simpanFormBarthelIndek()
    {
        $kunjungan = $this->input->post("nokun");
        $pengguna = $this->input->post("pengguna");
        $rangsangbab = $this->input->post("rangsangbab");
        $rangsangberkemih = $this->input->post("rangsangberkemih");
        $bersihkandiri = $this->input->post("bersihkandiri");
        $penggunaankloset = $this->input->post("penggunaankloset");
        $makan = $this->input->post("makan");
        $berubahposisi = $this->input->post("berubahposisi");
        $berpindah = $this->input->post("berpindah");
        $memakaibaju = $this->input->post("memakaibaju");
        $naiktangga = $this->input->post("naiktangga");
        $mandi = $this->input->post("mandi");

        $data = array(
            'nokun' => $kunjungan,
            'rangsang_bab' => $rangsangbab,
            'rangsang_berkemih' => $rangsangberkemih,
            'bersihkan_diri' => $bersihkandiri,
            'penggunaan_kloset' => $penggunaankloset,
            'makan' => $makan,
            'berubah_posisi' => $berubahposisi,
            'berpindah' => $berpindah,
            'memakai_baju' => $memakaibaju,
            'naik_tangga' => $naiktangga,
            'mandi' => $mandi,
            'oleh' => $pengguna, 
        );

        $this->db->trans_begin();
        $this->db->insert('keperawatan.tb_barthel_indek', $data);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryBarthelIndek()
    {
        $id = $this->input->post('id');
        $HistoryBarthelIndek = $this->pengkajianAwalModel->HistoryDetailBarthelIndek($id);
        $listRangsangbab = $this->masterModel->referensi(834);
        $listRangsangberkemih = $this->masterModel->referensi(835);
        $listMembersihkandiri = $this->masterModel->referensi(836);
        $listPenggunaankloset = $this->masterModel->referensi(837);
        $listMakan = $this->masterModel->referensi(838);
        $listBerubahposisi = $this->masterModel->referensi(839);
        $listBerpindah = $this->masterModel->referensi(840);
        $listMemakaibaju = $this->masterModel->referensi(841);
        $listNaiktangga = $this->masterModel->referensi(842);
        $listMandi = $this->masterModel->referensi(843);
        
        $data = array(
            'HistoryBarthelIndek' => $HistoryBarthelIndek,
            'listRangsangbab' => $listRangsangbab,
            'listRangsangberkemih' => $listRangsangberkemih,
            'listMembersihkandiri' => $listMembersihkandiri,
            'listPenggunaankloset' => $listPenggunaankloset,
            'listMakan' => $listMakan,
            'listBerubahposisi' => $listBerubahposisi,
            'listBerpindah' => $listBerpindah,
            'listMemakaibaju' => $listMemakaibaju,
            'listNaiktangga' => $listNaiktangga,
            'listMandi' => $listMandi
        );

        $this->load->view('Pengkajian/emr/barthelIndek/modalViewEditBarthelIndek', $data); 
    }

    public function updateFormBarthelIndek()
    {
        $id_barthelindek = $this->input->post("id_barthelindek");
        $rangsangbab = $this->input->post("rangsangbab_edit");
        $rangsangberkemih = $this->input->post("rangsangberkemih_edit");
        $bersihkandiri = $this->input->post("bersihkandiri_edit");
        $penggunaankloset = $this->input->post("penggunaankloset_edit");
        $makan = $this->input->post("makan_edit");
        $berubahposisi = $this->input->post("berubahposisi_edit");
        $berpindah = $this->input->post("berpindah_edit");
        $memakaibaju = $this->input->post("memakaibaju_edit");
        $naiktangga = $this->input->post("naiktangga_edit");
        $mandi = $this->input->post("mandi_edit");

        $dataUpdate = array(
            'rangsang_bab' => $rangsangbab,
            'rangsang_berkemih' => $rangsangberkemih,
            'bersihkan_diri' => $bersihkandiri,
            'penggunaan_kloset' => $penggunaankloset,
            'makan' => $makan,
            'berubah_posisi' => $berubahposisi,
            'berpindah' => $berpindah,
            'memakai_baju' => $memakaibaju,
            'naik_tangga' => $naiktangga,
            'mandi' => $mandi,
        );
        $this->db->trans_begin();

        
        $this->db->where('keperawatan.tb_barthel_indek.id', $this->input->post('id_barthelindek'));
        $this->db->update('keperawatan.tb_barthel_indek', $dataUpdate);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }
}

?>