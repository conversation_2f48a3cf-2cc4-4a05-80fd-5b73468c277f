<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ViewAllCppt extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('laporanModel','masterModel','rekam_medis/rawat_inap/catatanTerintegrasi/CpptModel','pendaftaranModel'));
  }

  public function index()
  {
    $data = array(
      'title' => 'Halaman View All Cppt',
      'isi'   => 'Laporan/ViewAllCppt/cariMr',
    );
    $this->load->view('layout/wrapper',$data);
  }

  public function dataCppt()
  {
    $nomr = $this->input->post('nomr');
    // $namaPasien  = $this->laporanModel->namaPasien($nomr);
    // $allLaporanCppt = $this->laporanModel->allLaporanCppt($nomr);

    // $laporanCppt = array();
    // foreach ($allLaporanCppt as $alc ) {
    //   $resultLaporanDokter = $this->laporanModel->laporanCppt(1,$alc['nokun']);
    //   $resultLaporanPerawat = $this->laporanModel->laporanCppt(2,$alc['nokun']);

    //   $sub_array = array();
    //   $sub_array['nokun']         = $alc['nokun'];
    //   $sub_array['NAMAPASIEN']    = $alc['NAMAPASIEN'];
    //   $sub_array['NORM']          = $alc['NORM'];
    //   $sub_array['TANGGAL_LAHIR'] = $alc['TANGGAL_LAHIR'];
    //   $sub_array['isi'] = array();
    //   foreach ($resultLaporanPerawat as $perawat) {
    //     array_push($sub_array['isi'],$perawat);
    //   }
    //   foreach ($resultLaporanDokter as $dataDokter) {
    //     array_push($sub_array['isi'],$dataDokter);
    //   }

    //   $laporanCppt[] = $sub_array;
    // }

    $data = array(
      'title'          => 'Halaman View All Cppt',
      'isi'            => 'rekam_medis/rawat_inap/catatanTerintegrasi/cpptList',
      // 'nomr'           => $nomr,
      'cppt'           => $this->CpptModel->get_history(FALSE),
      'pendaftaran'    => '',
      'nopen'          => 0,
      // 'nomr'           => $nomr,
      // 'namaPasien'     => $namaPasien,
      // 'allLaporanCppt' => $allLaporanCppt,
      // 'laporanCppt'    => $laporanCppt
    );


    $this->load->view('layout/wrapper',$data);
  }

}

/* End of file ViewAllCppt.php */
/* Location: ./application/controllers/laporan/ViewAllCppt.php */
