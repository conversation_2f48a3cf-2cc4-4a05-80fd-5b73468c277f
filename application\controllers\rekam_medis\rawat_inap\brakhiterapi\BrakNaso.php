<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>rakNaso extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/brakhiterapi/BrakNasoModel'
    ));
  }

  public function index(){
  	$norm = $this->uri->segment(6);
    $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    $data = array(
      'nopen' => $nopen,
      'norm' => $norm,
      'nokun' => $nokun,
      // 'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'historyBrakNaso' => $this->BrakNasoModel->historyBrakNaso($norm),
      'listTeknikRadiasi' => $this->masterModel->referensi(1390),
      'teknikAnastesi' => $this->masterModel->referensi(1398),
      'teknikAnastesiPelepasan' => $this->masterModel->referensi(1398),
      'suction' => $this->masterModel->referensi(1399),
      'suctionPelepasan' => $this->masterModel->referensi(1399),
      'listPencitraan' => $this->masterModel->referensi(1394),
      'tube' => $this->masterModel->referensi(1400),
      'tubu' => $this->masterModel->referensi(1401),
      'listDr' => $this->masterModel->listDr(),
      'listRadiografer' => $this->masterModel->listRadiografer(),
      'listFisikaMedis' => $this->masterModel->listFisikaMedis(),
      'listPerawat' => $this->masterModel->listPerawat(),
    );
    $this->load->view('rekam_medis/rawat_inap/brakhiterapi/braknaso/index', $data);
  }

  ///////////// Modal view hasil brakhiterapi nasofaring ////////////////
  public function viewEditBrakNaso()
  {
    $idbraknaso = $this->input->post('idbraknaso');
    $getBrakNaso = $this->BrakNasoModel->getBrakNaso($idbraknaso);

    $data = array(
      'idbraknaso' => $idbraknaso,
      'getBrakNaso' => $getBrakNaso,
      'listTeknikRadiasi' => $this->masterModel->referensi(1390),
      'teknikAnastesi' => $this->masterModel->referensi(1398),
      'teknikAnastesiPelepasan' => $this->masterModel->referensi(1398),
      'suction' => $this->masterModel->referensi(1399),
      'suctionPelepasan' => $this->masterModel->referensi(1399),
      'listPencitraan' => $this->masterModel->referensi(1394),
      'tube' => $this->masterModel->referensi(1400),
      'tubu' => $this->masterModel->referensi(1401),
      'listDr' => $this->masterModel->listDr(),
      'listRadiografer' => $this->masterModel->listRadiografer(),
      'listFisikaMedis' => $this->masterModel->listFisikaMedis(),
      'listPerawat' => $this->masterModel->listPerawat(),
    );

    $this->load->view('rekam_medis/rawat_inap/brakhiterapi/braknaso/editBrakNaso', $data);
  }

  public function simpanBrakNaso($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();

        $dataBrakNaso = array(         
          'nokun' => isset($post['nokun']) ? $post['nokun'] : "",
          'tanggal' => date('Y-m-d',strtotime($post['tanggal'])),
          'jam' => isset($post['jam']) ? $post['jam'] : "",
          'diagnosis' => isset($post['diagnosis']) ? $post['diagnosis'] : "",
          'dosis1' => isset($post['dosis1']) ? $post['dosis1'] : "",
          'dosis2' => isset($post['dosis2']) ? $post['dosis2'] : "",
          'teknik_radiasi' => isset($post['teknik_radiasi']) ? $post['teknik_radiasi'] : "",
          'teknik_anastesi' => isset($post['teknikAnastesiBrakNasoLm']) ? $post['teknikAnastesiBrakNasoLm'] : "",
          'teknik_anastesi_desk' => isset($post['deskTeknikAnastesiBrakNasoLm']) ? $post['deskTeknikAnastesiBrakNasoLm'] : "",
          'menilai_status_lokalis' => isset($post['menilaiStatusBrakNasoLm']) ? $post['menilaiStatusBrakNasoLm'] : "",
          'suction' => isset($post['suctionBrakNasoLm']) ? $post['suctionBrakNasoLm'] : "",
          'suction_desk' => isset($post['deskSuctionBrakNasoLm']) ? $post['deskSuctionBrakNasoLm'] : "",
          'instruksi_khusus' => isset($post['instruksiKhususBrakNasoLm']) ? $post['instruksiKhususBrakNasoLm'] : "",
          'list_pencitraan' => isset($post['listPencitraanBrakNasoLm']) ? $post['listPencitraanBrakNasoLm'] : "",
          'tube' => isset($post['tubeBrakNasoLm']) ? $post['tubeBrakNasoLm'] : "",
          'tubu' => isset($post['tubuBrakNasoLm']) ? $post['tubuBrakNasoLm'] : "",
          'aktivitas_sumber' => isset($post['aktivitasSumberBrakNasoLm']) ? $post['aktivitasSumberBrakNasoLm'] : "",
          'naso_kanan' => isset($post['nasoFaringKananBrakNasoLm']) ? $post['nasoFaringKananBrakNasoLm'] : "",
          'hipo1' => isset($post['hipofisis1BrakNasoLm']) ? $post['hipofisis1BrakNasoLm'] : "",
          'naso_kiri' => isset($post['nasoFaringKiriBrakNasoLm']) ? $post['nasoFaringKiriBrakNasoLm'] : "",
          'chias' => isset($post['chiasmaBrakNasoLm']) ? $post['chiasmaBrakNasoLm'] : "",
          'rouvier' => isset($post['rouvierBrakNasoLm']) ? $post['rouvierBrakNasoLm'] : "",
          'retina' => isset($post['retinaBrakNasoLm']) ? $post['retinaBrakNasoLm'] : "",
          'palatum_kiri' => isset($post['palatumKiriBrakNasoLm']) ? $post['palatumKiriBrakNasoLm'] : "",
          'hidung_kiri' => isset($post['hidungKiriBrakNasoLm']) ? $post['hidungKiriBrakNasoLm'] : "",
          'palatum_kanan' => isset($post['palatumKananBrakNasoLm']) ? $post['palatumKananBrakNasoLm'] : "",
          'hidung_kanan' => isset($post['hidungKananBrakNasoLm']) ? $post['hidungKananBrakNasoLm'] : "",
          'hipo2' => isset($post['hipofisis2BrakNasoLm']) ? $post['hipofisis2BrakNasoLm'] : "",
          'waktu_penyinaran' => isset($post['waktuPenyinaranBrakNasoLm']) ? $post['waktuPenyinaranBrakNasoLm'] : "",
          'dokter_pelaksana' => isset($post['dokter_pelaksanaBrakNasoLm']) ? $post['dokter_pelaksanaBrakNasoLm'] : "",
          'fisikawan' => isset($post['fisikawanBrakNasoLm']) ? $post['fisikawanBrakNasoLm'] : "",
          'radiografer' => isset($post['radiograferBrakNasoLm']) ? $post['radiograferBrakNasoLm'] : "",
          'perawat' => isset($post['perawatBrakNasoLm']) ? $post['perawatBrakNasoLm'] : "",
          'tanggal_lepas' => date('Y-m-d',strtotime($post['tanggalPelepasan'])),
          'jam_lepas' => isset($post['jamPelepasan']) ? $post['jamPelepasan'] : "",
          'teknik_anastesi_lepas' => isset($post['teknikAnastesiPelepasanBrakNasoLm']) ? $post['teknikAnastesiPelepasanBrakNasoLm'] : "",
          'teknik_anastesi_lepas_desk' => isset($post['deskTeknikAnastesiPelepasanBrakNasoLm']) ? $post['deskTeknikAnastesiPelepasanBrakNasoLm'] : "",
          'suction_lepas' => isset($post['suctionPelepasanBrakNasoLm']) ? $post['suctionPelepasanBrakNasoLm'] : "",
          'suction_lepas_desk' => isset($post['deskSuctionPelepasanBrakNasoLm']) ? $post['deskSuctionPelepasanBrakNasoLm'] : "",
          'instruksi_khusus_lepas' => isset($post['instruksiKhususPelepasanBrakNasoLm']) ? $post['instruksiKhususPelepasanBrakNasoLm'] : "",
          'penjelasan_lain_lepas' => isset($post['penjelasanLainnyaBrakNasoLm']) ? $post['penjelasanLainnyaBrakNasoLm'] : "",
          'dokter_pelaksana_lepas' => isset($post['dokter_pelaksanaPelepasanBrakNasoLm']) ? $post['dokter_pelaksanaPelepasanBrakNasoLm'] : "",
          'perawat_lepas' => isset($post['perawatPelepasanBrakNasoLm']) ? $post['perawatPelepasanBrakNasoLm'] : "",
          'oleh' => isset($post['oleh']) ? $post['oleh'] : "",
        );

        // echo "<pre>data Brakhiterapi Nasofaring ";print_r($dataBrakNaso);echo "</pre>";
        // exit();

        $this->db->trans_begin();
        if (!empty($post['idbraknaso'])) {
          $this->db->where('tb_brak_naso.id', $post['idbraknaso']);
          $this->db->update('keperawatan.tb_brak_naso', $dataBrakNaso);
        } else {
          $result = array('status' => 'failed');
          $this->db->insert('keperawatan.tb_brak_naso', $dataBrakNaso);
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }

        echo json_encode($result);
      }
    }
  }

}