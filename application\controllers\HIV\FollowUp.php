<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FollowUp extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/TbBbModel',
        'HIV/FollowUpModel'
      )
    );
  }

  public function index($id = null)
  {
    $nokun = $this->uri->segment(3);
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      'id' => $id,
      'pasien' => $pasien,
      'pilihanCPPT' => $this->masterModel->referensi(1407),
      'statusFungsional' => $this->masterModel->referensi(1749),
      'statusKehamilan' => $this->masterModel->referensi(1750),
      'infeksiOportunistis' => $this->masterModel->referensi(1751),
      'statusTB' => $this->masterModel->referensi(1752),
      'hasilAkhir' => $this->masterModel->referensi(1753),
      'adherenceART' => $this->masterModel->referensi(1754),
      'efekSamping' => $this->masterModel->referensi(1755),
      'diberikanKondom' => $this->masterModel->referensi(1756),
      'akhirFollowUp' => $this->masterModel->referensi(1757),
      'jumlah' => $this->FollowUpModel->history($pasien['NOKUN'], 'jumlah'),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/HIV/FollowUp/index', $data);
  }

  public function simpan()
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      $rules = $this->FollowUpModel->rules;
      $this->form_validation->set_rules($rules);
      if ($this->form_validation->run() == true) {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $id = isset($post['id']) ? $post['id'] : null;
        $nokun = isset($post['nokun']) ? $post['nokun'] : null;
        $nomr = isset($post['nomr']) ? $post['nomr'] : null;

        // Mulai data
        $data = array(
          'nokun' => $nokun,
          'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
          'waktu' => isset($post['waktu']) ? $post['waktu'] : null,
          'tanggal_kunjungan' => isset($post['tanggal_kunjungan']) ? $post['tanggal_kunjungan'] : null,
          'tanggal_rencana' => isset($post['tanggal_rencana']) ? $post['tanggal_rencana'] : null,
          'rujuk_masuk' => isset($post['rujuk_masuk']) ? $post['rujuk_masuk'] : null,
          'dengan_art' => isset($post['dengan_art']) ? $post['dengan_art'] : null,
          'klinik_sebelumnya' => isset($post['klinik_sebelumnya']) ? $post['klinik_sebelumnya'] : null,
          'status_fungsional' => isset($post['status_fungsional']) ? $post['status_fungsional'] : null,
          'stad_klinis' => isset($post['stad_klinis']) ? $post['stad_klinis'] : null,
          'status_kehamilan' => isset($post['status_kehamilan']) ? $post['status_kehamilan'] : null,
          'metode_kb' => isset($post['metode_kb']) ? $post['metode_kb'] : null,
          'infeksi_oportunistis' => isset($post['infeksi_oportunistis']) ? json_encode($post['infeksi_oportunistis']) : null,
          'ket_infeksi_oportunistis' => isset($post['ket_infeksi_oportunistis']) ? $post['ket_infeksi_oportunistis'] : null,
          'obat_io' => isset($post['obat_io']) ? $post['obat_io'] : null,
          'status_tb' => isset($post['status_tb']) ? $post['status_tb'] : null,
          'ppk' => isset($post['ppk']) ? $post['ppk'] : null,
          'ppi' => isset($post['ppi']) ? $post['ppi'] : null,
          'hasil_akhir' => isset($post['hasil_akhir']) ? $post['hasil_akhir'] : null,
          'obat_arv' => isset($post['obat_arv']) ? $post['obat_arv'] : null,
          'sisa_obat_arv' => isset($post['sisa_obat_arv']) ? $post['sisa_obat_arv'] : null,
          'adherence_art' => isset($post['adherence_art']) ? $post['adherence_art'] : null,
          'efek_samping' => isset($post['efek_samping']) ? json_encode($post['efek_samping']) : null,
          'ket_efek_samping' => isset($post['ket_efek_samping']) ? $post['ket_efek_samping'] : null,
          'jumlah_cd4' => isset($post['jumlah_cd4']) ? $post['jumlah_cd4'] : null,
          'hasil_lab' => isset($post['hasil_lab']) ? $post['hasil_lab'] : null,
          'diberikan_kondom' => isset($post['diberikan_kondom']) ? $post['diberikan_kondom'] : null,
          'jumlah_kondom' => isset($post['jumlah_kondom']) ? $post['jumlah_kondom'] : null,
          'rujuk' => isset($post['rujuk']) ? $post['rujuk'] : null,
          'akhir_follow_up' => isset($post['akhir_follow_up']) ? $post['akhir_follow_up'] : null,
          'oleh' => $this->session->userdata['id'],
          'created_at' => date('Y-m-d H:i:s'),
          'status' => 1,
        );
        // Akhir data

        // Mulai simpan
        // echo '<pre>';print_r($data);exit();
        if (!empty($id)) {
          $this->FollowUpModel->ubah($data, $id);
        } else {
          $idBaru = $this->FollowUpModel->simpan($data);
        }
        // Akhir simpan

        // Mulai simpan tinggi dan berat badan
        $data = array(
          'data_source' => 47,
          'ref' => !empty($id) ? $id : $idBaru,
          'nomr' => $nomr,
          'nokun' => $nokun,
          'tb' => isset($post['tb']) ? $post['tb'] : null,
          'bb' => isset($post['bb']) ? $post['bb'] : null,
          'oleh' => $this->session->userdata['id'],
          'status' => 1,
        );
        // echo '<pre>';print_r($data);exit();
        if (!empty($id)) {
          $this->TbBbModel->ubahRef($data, $id);
        } else {
          $this->TbBbModel->insert($data);
        }
        // Akhir simpan tinggi dan berat badan

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      } else {
        $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
      }
      echo json_encode($result);
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nokun' => $post['nokun']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/HIV/FollowUp/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->FollowUpModel->history($nokun, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      // Mulai periksa status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }
      // Akhir periksa status

      // Mulai data
      $data[] = array(
        $no++,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->waktu)),
        date('d-m-Y', strtotime($h->tanggal_kunjungan)),
        date('d-m-Y', strtotime($h->tanggal_rencana)),
        $h->pengisi,
        date('d-m-Y, H:i:s', strtotime($h->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-fuart' class='btn btn-sm btn-danger waves-effect tbl-batal-fuart' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' class='btn btn-sm btn-primary waves-effect tbl-detail-fuart' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      // Akhir data
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array('status' => 0);
    $this->FollowUpModel->ubah($data, $id);
    $this->TbBbModel->ubahRef($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detail()
  {
    $post = $this->input->post(null, true);
    $detail = $this->FollowUpModel->detail($post['id']);
    // echo '<pre>';print_r($detail);exit();
    echo json_encode(
      array(
        'status' => 'succes',
        'data' => $detail,
      )
    );
  }
}

/* End of file FollowUp.php */
/* Location: ./application/controllers/HIV/FollowUp.php */