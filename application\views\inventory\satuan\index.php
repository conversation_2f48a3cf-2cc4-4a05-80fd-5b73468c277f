<div class="row">
	<div class="col-sm-12">
		<h4 class="page-title">Data Satuan</h4>
	</div>
</div>
<!-- end page title -->
<div class="row">
	<div class="col-12">
		<div class="card-box">
			<div class="table-responsive">
				<p class="text-muted font-14 m-b-30">
					<button type="button" id="btn-tambah" data-toggle="modal" data-target="#tambahsatuan" class="btn btn-info">
						<span class="fas fa-plus"></span>  Tambah Satuan
					</button>
				</p>
				<table class="table table-striped table-bordered table-hover" id="satuan">
					<thead>
						<tr>
							<th width="10%">No.</th>
							<th><PERSON><PERSON></th>
							<th width="10%">Aksi</th>
						</tr>
					</thead>
					<tbody>
						<?php $no=0; foreach ($satuan as $dt){ ?>
							<tr>
								<td><?php echo ++$no;?></td>
								<td><?php echo $dt['SATUAN'];?></td>
								<td>
									<a
									href="javascript:;"
									data-id="<?php echo $dt['ID_SATUAN'] ?>"
									data-nama="<?php echo $dt['SATUAN'] ?>"
									data-toggle="modal" data-target="#edit-datasatuan">
									<button  data-toggle="modal" data-target="#ubah-datasatuan" class="btn btn-info"><span class='fas fa-edit'></span> Ubah</button>
								</a>
								<!-- <a href="#" class="btn btn-danger">Hapus</a> -->
							</td>
						</tr>
					<?php } ?>
				</tbody>
			</table>
		</div>
	</div><!-- end col -->
</div>
</div>
<!-- Modal tambah -->
<div id="tambahsatuan" class="modal fade">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<span>Form tambah satuan</span>
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">
					<span id="modal-title"></span>
				</h4>
			</div>
			<div class="modal-body">
				<?php echo form_open('inventory/Satuan/tambah'); ?>
				<div class="form-group row">
					<label class="col-sm-2  col-form-label" for="simpleinput">Nama Satuan</label>
					<div class="col-sm-10">
						<input type="text" id="simpleinput" name="NAMA" class="form-control" placeholder="Nama Satuan">
					</div>
				</div>
				<button type="submit" name="submit" class="btn btn-primary"><span class='fas fa-save'></span> Simpan</button>
			</form>
		</div>
	</div>
</div>
</div>

<div aria-hidden="true" aria-labelledby="myModalLabel" role="dialog" tabindex="-1" id="edit-datasatuan" class="modal fade">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<span>Form Update Satuan</span>
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<h4 class="modal-title">
					<span id="modal-title"></span>
				</h4>
			</div>
			<div class="modal-body">
				<form action="<?php echo base_url('inventory/Satuan/ubah')?>" method="post">
					<div class="form-group row">
						<input type="hidden" id="id" name="ID">
						<label class="col-sm-2  col-form-label" for="simpleinput">Nama Satuan</label>
						<div class="col-sm-10">
							<input type="text" id="nama" name="NAMA" class="form-control" placeholder="Nama Satuan">
						</div>
					</div>
					<div class="modal-footer">
						<button class="btn btn-info" type="submit"><span class='fas fa-save'></span>  Simpan</button>
						<button type="button" class="btn btn-warning" data-dismiss="modal"> Batal</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<script>
	$(document).ready(function() {
		$('#edit-datasatuan').on('show.bs.modal', function (event) {
var div = $(event.relatedTarget) // Tombol dimana modal di tampilkan
var modal          = $(this)

// Isi nilai pada field
modal.find('#id').attr("value",div.data('id'));
modal.find('#nama').attr("value",div.data('nama'));
});
	});
</script>
<script>
	$(document).ready(function() {
		$('#satuan').DataTable({
			responsive: true
		});
	});
</script>
