<?php
defined('BASEPATH') or exit('No direct script access allowed');

class OksigenModel extends MY_Model
{
    protected $_table_name = 'db_pasien.tb_o2';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    public function ubah($id, $data)
    {
        $this->db->where('db_pasien.tb_o2.id', $id);
        $this->db->update('db_pasien.tb_o2', $data);
    }

    function table_query()
    {
        $this->db->select('o.*, ds.deskripsi, `master`.getNamaLengkapPegawai(p.NIP) oleh_desc');
        $this->db->from('db_pasien.tb_o2 o');
        $this->db->join('db_master.tb_data_source ds', 'o.data_source = ds.id', 'left');
        $this->db->join('aplikasi.pengguna p', 'o.oleh = p.ID', 'left');
        $this->db->join('pendaftaran.kunjungan k', 'o.nokun = k.NOMOR', 'left');
        $this->db->order_by('o.created_at', 'DESC');
        if ($this->input->post('nokun')) {
            $this->db->where('o.nokun', $this->input->post('nokun'));
            $this->db->limit('1');
        }

        if ($this->input->post('nopen')) {
            $this->db->where('k.NOPEN', $this->input->post('nopen'));
            $this->db->limit('1');
        }

        if ($this->input->post('id')) {
            $this->db->where('o.id', $this->input->post('id'));
        }
    }

    function get_table($single = TRUE)
    {
        $this->db->where('o.status', 1);
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }

    public function grafik_saturasi($nopen)
    {
        $this->db->select(
            "o.saturasi_o2,
            CONCAT(DATE_FORMAT(o.created_at, '%d-%m-%Y'), ' <strong>(', DATE_FORMAT(o.created_at, '%H.%i.%s'), ')</strong>') waktu"
        );
        $this->db->from('db_pasien.tb_o2 o');
        $this->db->join('pendaftaran.kunjungan k', 'o.nokun = k.NOMOR', 'left');
        $this->db->where('o.status', 1);
        $this->db->where('k.NOPEN', $nopen);
        $this->db->order_by('o.created_at');
        $query = $this->db->get();
        return $query->result_array();
    }
}

/* End of file OksigenModel.php */
/* Location: ./application/models/rekam_medis/OksigenModel.php */