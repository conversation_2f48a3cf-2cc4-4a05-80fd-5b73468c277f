<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cppt extends CI_Controller
{

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'EresepModel',
        'hemodialisaModel',
        'geriatri/InstrumenMNA_Model',
        'laporanModel'
      ]
    );
  }

  public function index()
  {
    $nomr  = $this->uri->segment(4);
    $nopen = $this->uri->segment(5);
    $nokun = $this->uri->segment(6);

    $statusPengguna      = $_SESSION['status'];
    $id_pengguna         = $this->session->userdata('id');
    $getNomr             = $this->pengkajianAwalModel->getNomr($nokun);
    $getGeriatriTerakhir = $this->pengkajianAwalModel->etGeriatriTerakhir($nomr);
    $getp3gTerakhir      = $this->pengkajianAwalModel->getp3gTerakhir($nomr);

    $alertKeperawatan = $this->pengkajianAwalModel->alertCppt($nokun, 2, $id_pengguna);
    $alertMedis       = $this->pengkajianAwalModel->alertCppt($nokun, 1, $id_pengguna);
    $VkeperwatanCppt  = $this->pengkajianAwalModel->VkeperwatanCppt($nokun);
    $tandaVitalCppt   = $this->pengkajianAwalModel->tandaVitalCppt($nokun);
    $alertCpptKonsul  = $this->pengkajianAwalModel->alertCpptKonsul($nokun);
    // echo "<pre>";print_r($getNomr);exit();

    $data = [
      'id_pengguna'      => $id_pengguna,
      'nomr'             => $nomr,
      'nopen'            => $nopen,
      'nokun'            => $nokun,
      'statusPengguna'   => $statusPengguna,
      'getNomr'          => $getNomr,
      'alertKeperawatan' => $alertKeperawatan,
      'alertMedis'       => $alertMedis,
      'VkeperwatanCppt'  => $VkeperwatanCppt,
      'tandaVitalCppt'   => $tandaVitalCppt,
      'alertCpptKonsul'  => $alertCpptKonsul,

      'skriningResikoJatuhPusing'  => $this->masterModel->referensi(120),
      'skriningResikoJatuhBerdiri' => $this->masterModel->referensi(121),
      'skriningResikoJatuh6Bulan'  => $this->masterModel->referensi(122),
      'skriningNyeri'              => $this->masterModel->referensi(7),
      'skalaNyeriNRS'              => $this->masterModel->referensi(114),
      'skalaNyeriWBR'              => $this->masterModel->referensi(115),
      'skalaNyeriFLACC'            => $this->masterModel->referensi(123),
      'skalaNyeriBPS'              => $this->masterModel->referensi(133),
      'efeksampingNRS'             => $this->masterModel->referensi(118),
      'efeksampingWBR'             => $this->masterModel->referensi(119),
      'efeksampingFLACC'           => $this->masterModel->referensi(131),
      'efeksampingBPS'             => $this->masterModel->referensi(134),
      'statusnyeriNRS'             => $this->masterModel->referensi(136),
      'statusnyeriWBR'             => $this->masterModel->referensi(136),
      'statusnyeriFLACC'           => $this->masterModel->referensi(136),
      'statusnyeriBPS'             => $this->masterModel->referensi(136),
      'pengkajianNyeriProvocative' => $this->masterModel->referensi(8),
      'pengkajianNyeriQuality'     => $this->masterModel->referensi(9),
      'pengkajianNyeriTime'        => $this->masterModel->referensi(12),
      'formAsuhanKeperawatan'      => $this->masterModel->referensi(148),
      'diagnosa_kesehatan'         => $this->masterModel->referensi(525),
      'kontrolKembali'             => $this->masterModel->referensi(1850),
      'listDr'                     => $this->masterModel->listDr(),
      'getGeriatriTerakhir'        => $getGeriatriTerakhir,
      'getp3gTerakhir'             => $getp3gTerakhir,
      'profesi_user'               => $this->session->userdata('profesi')
    ];

    $this->load->view('Pengkajian/emr/form-cppt-keperawatan-rj', $data);    
  }
}

/* End of file Cppt.php */
/* Location: ./application/controllers/cppt/Cppt.php */
