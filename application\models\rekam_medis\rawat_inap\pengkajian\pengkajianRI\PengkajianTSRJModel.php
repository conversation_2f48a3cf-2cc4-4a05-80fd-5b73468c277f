<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>gkajianTSRJModel extends MY_Model {
  protected $_table_name = 'keperawatan.tb_keperawatan';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>.',
        'numeric' => '%s Wajib <PERSON>.'
      ),
    ),
  );

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('pk.NOMOR NOKUN
        , rk.ID ID_RUANGAN
        , rk.DESKRIPSI RUANGAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , kp.id_emr ID_EMR_PERAWAT
        , kp.created_at TANGGAL_PENGKAJIAN_PERAWAT
        , master.getNamaLengkapPegawai(peng.NIP) USER_PERAWAT
        , md.id_emr ID_EMR_MEDIS
        , md.created_at TANGGAL_PENGKAJIAN_MEDIS
        , master.getNamaLengkapPegawai(pemed.NIP) USER_MEDIS
        , p.NOMOR NOPEN
        , p.NORM
        , master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
        , kp.created_by ID_USER_PERAWAT
        , md.created_by ID_USER_MEDIS
        , master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR_TAHUN
        , master.getCariUmur(p.TANGGAL, pas.TANGGAL_LAHIR) UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18, 2, 1) USIA
        , kp.jenis JENIS_PENGKAJIAN_PERAWAT
        , md.jenis JENIS_PENGKAJIAN_MEDIS
        ,HOUR(TIMEDIFF(NOW(),md.created_at)) DURASI_MEDIS,IF(HOUR(TIMEDIFF(NOW(),md.created_at))<=24,1,0) STATUS_EDIT_MEDIS
        ,HOUR(TIMEDIFF(NOW(),kp.created_at)) DURASI_PERAWAT,IF(HOUR(TIMEDIFF(NOW(),kp.created_at))<=24,1,0) STATUS_EDIT_PERAWAT');
    $this->db->from('pendaftaran.kunjungan pk');
    $this->db->join('keperawatan.tb_keperawatan kp','pk.NOMOR = kp.nokun AND kp.flag=1 AND kp.`status`=1','LEFT');
    $this->db->join('medis.tb_medis md','pk.NOMOR = md.nokun AND md.flag=1 AND md.`status`=1','LEFT');
    $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
    $this->db->join('pendaftaran.tujuan_pasien tp','tp.NOPEN = p.NOMOR','LEFT');
    $this->db->join('pendaftaran.penjamin pj','pj.NOPEN = p.NOMOR','LEFT');
    $this->db->join('master.diagnosa_masuk dm','dm.ID = p.DIAGNOSA_MASUK','LEFT');
    $this->db->join('master.dokter dpjp','dpjp.ID = tp.DOKTER','LEFT');
    $this->db->join('master.pasien pas','pas.NORM = p.NORM','LEFT');
    $this->db->join('master.ruangan rk','rk.ID = pk.RUANGAN','LEFT');
    $this->db->join('master.ruangan rp','rp.ID = tp.RUANGAN','LEFT');
    $this->db->join('master.referensi refpj','refpj.ID = pj.JENIS AND refpj.JENIS=10','LEFT');
    $this->db->join('aplikasi.pengguna peng','peng.ID = kp.created_by','LEFT');
    $this->db->join('aplikasi.pengguna pemed','pemed.ID = md.created_by','LEFT');
    

    $this->db->where('p.NORM',$this->input->post('nomr'));
    $this->db->where("(kp.id_emr IS NOT NULL OR md.id_emr IS NOT NULL)");
    $this->db->where("(kp.jenis!=6 OR md.jenis!=6)");
    $this->db->group_by('pk.NOMOR');
    $this->db->order_by('pk.MASUK ', 'DESC');
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }

  function get_count_indeksBarthel($nokun){
    $query = $this->db->query(
      "SELECT COUNT(tbbi.nokun) JUMLAH
      FROM keperawatan.tb_barthel_indek tbbi
      WHERE tbbi.nokun = '$nokun'"
    );
    if ($query->num_rows() > 0) {
      return $query->row()->JUMLAH;
    }
    return false;
  }

  // Simpan tanda vital
  public function simpanTandaVital($dataTandaVital)
  {
    $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
    return $this->db->insert_id();
  }

  // Simpan kesadaran
  public function simpanKesadaran($dataKesedaran)
  {
    $this->db->insert('db_pasien.tb_kesadaran', $dataKesedaran);
    return $this->db->insert_id();
}  

  // Simpan O2
  public function simpanO2($dataO2)
  {
    $this->db->insert('db_pasien.tb_o2', $dataO2);
    return $this->db->insert_id();
  }

  public function getNomrRadioterapiRi($nopen)
    {
        $query = $this->db->query(
            "SELECT peg.SMF ID_SMF, refsmf.DESKRIPSI SMF, master.getNamaLengkapPegawai(dok.NIP) DOKTER_TUJUAN, peg.SMF
        , pk.NOMOR NOKUN, pk.NOPEN , p.NORM NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
        , pas.JENIS_KELAMIN ID_JK
        , IF(pas.JENIS_KELAMIN=1,'Laki-Laki', 'Perempuan') JK
        , concat(master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR), ' Tahun') UMUR
        , IF (master.getCariUmurTahun(p.TANGGAL, pas.TANGGAL_LAHIR) >= 18,2,1) USIA
        , p.TANGGAL TANGGAL_DAFTAR
        , r.JENIS_KUNJUNGAN
        , IF(pk.REF IS NULL, r.DESKRIPSI, rk.DESKRIPSI) RUANGAN_TUJUAN
        , pk.MASUK TANGGAL_KUNJUNGAN
        , IF(pk.REF IS NULL, r.ID, rk.ID) ID_RUANGAN
        , dm.ICD DIAGNOSA_MASUK , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=dm.ICD LIMIT 1
        ) DESKRIPSI_DIAGNOSA_MASUK
        , ref.ID IDPENJAMIN
        , ref.DESKRIPSI PENJAMIN
        , IF(tp.`STATUS`=1,4,pk.`STATUS`) status_pasien , IF(tp.`STATUS`=1,'Pasien belum diterima',IF(tp.`STATUS`=0,'Pasien
        dibatalkan',(IF(pk.`STATUS`=1,'Pasien berada di ruangan ini',IF(pk.`STATUS`=2,'Pasien sudah final','Kunjungan dibatalkan')
        )))) STATUS_KUNJUNGAN
        , penggu.ID ID_USER
        , dok.ID ID_DOKTER
        , pas.TANGGAL_LAHIR
        , dtt.NAME_PIC
        , pk.REF
        , mkp.NOMOR NOTLPN
        ,  (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'105050102',IF(ruangs.JENIS_KUNJUNGAN=14,'105050135','105050101'))
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) ID_TUJUAN_FARMASI

        , (SELECT IF(ruangs.JENIS_KUNJUNGAN=3,'Farmasi Rawat Inap','Farmasi Rawat Jalan')
        FROM pendaftaran.kunjungan tpas
        LEFT JOIN master.ruangan ruangs ON ruangs.ID = tpas.RUANGAN
        WHERE tpas.NOMOR = pk.NOMOR
        ) TUJUAN_FARMASI
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),2,1) JENIS_RUANGAN
        , IF(IF(pk.REF IS NULL, IF(r.ID IN ('105140101','105020901'), 2, r.JENIS_KUNJUNGAN), IF(rk.ID IN ('105140101','105020901'), 2, rk.JENIS_KUNJUNGAN)) IN (2,3),'IGD, HD & RI','RJ') DESKRIPSI_JENIS_RUANGAN
        , ppk.NAMA RUJUKAN_DARI
        , refdar.DESKRIPSI GOL_DARAH
        , (SELECT id_emr FROM keperawatan.tb_keperawatan kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=16
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_KEPERAWATAN_DEWASA_RI
        , (SELECT id_emr FROM medis.tb_medis kepe
                WHERE kepe.nopen=p.NOMOR
                    AND kepe.`status`=1
                    AND kepe.jenis=16
                    AND kepe.flag=1
                ORDER BY kepe.created_at DESC 
                LIMIT 1) ID_EMR_MEDIS_DEWASA_RI

        FROM pendaftaran.pendaftaran p
        LEFT JOIN master.pasien pas ON pas.NORM = p.NORM
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = p.NOMOR
        LEFT JOIN master.ppk ppk ON ppk.BPJS = srp.PPK
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
        LEFT JOIN master.referensi refdar ON refdar.ID = pas.GOLONGAN_DARAH AND refdar.JENIS=6
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN master.dokter dok ON dok.ID = tp.DOKTER
        LEFT JOIN master.pegawai peg ON peg.NIP = dok.NIP
        LEFT JOIN master.referensi refsmf ON refsmf.ID = peg.SMF AND refsmf.JENIS=26
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN aplikasi.pengguna penggu ON penggu.NIP = dok.NIP
        LEFT JOIN db_foto.tb_takePhoto dtt ON dtt.NOMR = p.NORM
        LEFT JOIN master.kontak_pasien mkp ON mkp.NORM = pas.NORM

        WHERE tp.`STATUS` not in (0,1)
        AND p.`STATUS`!= 0
        AND pk.`STATUS` != 0 AND p.NOMOR = '$nopen'
        GROUP BY dtt.ID #DESC"
        );
        return $query->row_array();
    }

public function getBarthelIndek($nokun)
{
  $query = $this->db->query(
            "SELECT * FROM keperawatan.tb_barthel_indek tbbi
            WHERE tbbi.nokun = '$nokun' AND tbbi.ref = 17"
          );
  return $query->row_array();
}
    // Get Pengkajian Rawat Inap Radioterapi
public function getPengkajian($idemr)
{
  $query = $this->db->query(
    'SELECT kep.id_emr ID_EMR, kep.nopen, kep.nokun, kep.jenis, kep.diagnosa_masuk, kep.status_verif
    , anam.ID_AUTO_ALLO, anam.allo_nama ALLO_NAMA, anam.hubungan_dengan_pasien HUBUNGAN_DENGAN_PASIEN, anam.info_dari_keluarga_pasien INFO_DARI_KELUARGA_PASIEN
    , rkes.riwayat_ekstravasasi RIWAYAT_EKSTRAVASASI, rkes.isi_ekstravasasi ISI_EKSTRAVASASI, rkes.pilih_ekstravasasi_foto PILIH_EKSTRAVASASI_FOTO, rkes.ekstravasasi_depan EKSTRAVASASI_DEPAN
    , rkes.ekstravasasi_belakang EKSTRAVASASI_BELAKANG, rkes.hasil_laboratorium HASIL_LABORATORIUM, rkes.isi_laboratorium ISI_LABORATORIUM, rkes.hasil_BMP HASIL_BMP, rkes.isi_BMP ISI_BMP
    , rkes.kemoterapi KEMOTERAPI, rkes.isi_kemoterapi ISI_KEMOTERAPI, rkes.tindakan_perawatan TINDAKAN_PERAWATAN, rkes.perawatan_lain PERAWATAN_LAIN, rkes.riwayat_graft RIWAYAT_GRAFT
    , tbbb.tb TB, tbbb.bb BB, sadar.kesadaran ID_VARIABEL_KESADARAN, tv.td_sistolik TD_SISTOLIK, tv.td_diastolik TD_DIASTOLIK, tv.nadi NADI, tv.pernapasan PERNAPASAN, tv.suhu SUHU
    , sn.metode METODE, sn.skor SKOR_NYERI, sn.provokative PROVOKATIVE, sn.quality QUALITY, sn.quality_lainnya QUALITY_LAINNYA, sn.regio REGIO, sn.severity SEVERITY, sn.time ID_TIME, sn.ket_time KET_TIME
    , pf.rhesus, pf.skala_nyeri SKALA_NYERI, pf.skala_lelah SKALA_LELAH, pf.skala_mual SKALA_MUAL, pf.skala_depresi SKALA_DEPRESI, pf.skala_cemas SKALA_CEMAS
    , pf.skala_mengantuk SKALA_MENGANTUK, pf.skala_nafsu_makan SKALA_NAFSU_MAKAN, pf.skala_sehat SKALA_SEHAT, pf.skala_sesak_napas SKALA_SESAK_NAPAS, pf.skala_masalah SKALA_MASALAH, pf.keluhan_pasien KELUHAN_PASIEN
    , pf.masalah_kesehatan_keperawatan MASALAH_KESEHATAN, pf.tinggal TINGGAL, pf.keyakinan KEYAKINAN, pf.sebutkan_keyakinan SEBUTKAN_KEYAKINAN, pf.pengobatan_alternatif PENGOBATAN_ALTERNATIF, pf.sebutkan_pengobatan_alternatif SEBUTKAN_PENGOBATAN_ALTERNATIF
    , pf.pengobatan_bertentangan PENGOBATAN_BERTENTANGAN, pf.sebutkan_pengobatan_bertentangan SEBUTKAN_PENGOBATAN_BERTENTANGAN, pf.vertigo VERTIGO, pf.sulit_berdiri SULIT_BERDIRI, pf.jatuh_dlm_6 JATUH_DLM_6
    , pf.psikologis ID_PSIKOLOGIS, pf.isi_psikologi ISI_PSIKOLOGI, pf.hub_keluarga HUB_KELUARGA, pf.nafkah_utama NAFKAH_UTAMA
    , pf.apakah_ingin_masuk_keews APAKAH_INGIN_MASUK_KEEWS
    , ek.tingkat_pendidikan PENDIDIKAN, ek.bahasa ID_BAHASA, ek.bahasa_daerah BAHASA_DAERAH, ek.bahasa_lain BAHASA_LAIN2, ek.penerjemah ID_PENERJEMAH, ek.penerjemah_lain PENERJEMAH_LAIN2, ek.informasi ID_INFORMASI
	  , ews.id ID_EWS
    , odua.id ID_o2
    , odua.saturasi_o2 SATURASI_O2
    , odua.penggunaan_o2 PENGGUNAAN_O2
    , (SELECT vit.id FROM db_pasien.tb_tanda_vital vit
    WHERE vit.data_source=31 AND vit.`status`=1 
    AND vit.nomr=p.NORM AND vit.ref=kep.id_emr
    ORDER BY vit.created_at DESC 
    LIMIT 1) ID_TABEL_TANDA_VITAL 
    , (SELECT kes.id FROM db_pasien.tb_kesadaran kes
    LEFT JOIN db_master.variabel kesad ON kesad.id_variabel=kes.kesadaran
    WHERE kes.data_source=31 AND kes.`status`=1 
    AND kes.nomr=p.NORM AND kes.ref=kep.id_emr
    ORDER BY kes.created_at DESC 
    LIMIT 1) ID_TABEL_KESADARAN
    , (SELECT
    CONCAT(\'[\',
    GROUP_CONCAT(\'"\', hb.id_variabel, \'"\'
    SEPARATOR \',\'),
    \']\')
    FROM keperawatan.tb_alat_bantu hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE hb.id_emr = kep.id_emr AND hb.`status` = 1) ID_ARRAY_ALAT_BANTU,
    (SELECT hb.keterangan
    FROM keperawatan.tb_alat_bantu hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE kep.id_emr = hb.id_emr AND hb.keterangan != \'\'
    LIMIT 1) ALAT_BANTU_LAIN2
    , (SELECT CONCAT(\'[\',GROUP_CONCAT(\'"\', ham.id_variabel, \'"\'SEPARATOR \',\'),\']\')
    FROM keperawatan.tb_hambatan hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE hb.id_emr = kep.id_emr AND hb.`status` = 1) ID_ARRAY_HAMBATAN
    , (SELECT hb.keterangan
    FROM keperawatan.tb_hambatan hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE kep.id_emr = hb.id_emr AND hb.keterangan != \'\'
    LIMIT 1) HAMBATAN_LAIN2
    , (SELECT CONCAT(\'[\',GROUP_CONCAT(\'"\', hb.id_variabel, \'"\'SEPARATOR \',\'),\']\')
    FROM keperawatan.tb_kebutuhan_pembelajaran hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE hb.id_emr = kep.id_emr AND hb.`status` = 1) ID_ARRAY_KEB_PEMBELAJARAN
    , (SELECT hb.keterangan
    FROM keperawatan.tb_kebutuhan_pembelajaran hb
    LEFT JOIN db_master.variabel ham ON ham.id_variabel = hb.id_variabel
    WHERE kep.id_emr = hb.id_emr AND hb.keterangan != \'\'
    LIMIT 1) KEB_PEMBELAJARAN
    , db_master.getIDAsuhanKeperawatan(kep.id_emr) ID_ASUHAN_KEPERAWATAN
    , db_master.getAsuhanKeperawatan(kep.id_emr) ASUHAN_KEPERAWATAN
    , db_master.getIDAsuhanKeperawatanDiagnosa(kep.id_emr) ID_DIAGNOSA_KEP
    , db_master.getAsuhanKeperawatanDiagnosa(kep.id_emr) DIAGNOSA_KEP
    , db_master.getIDAsuhanKeperawatanNOC(kep.id_emr) ID_NOC
    , db_master.getAsuhanKeperawatanNOC(kep.id_emr) NOC
    , db_master.getIDAsuhanKeperawatanNIC(kep.id_emr) ID_NIC
    , db_master.getAsuhanKeperawatanNIC(kep.id_emr) NIC
    , kep.`status` STATUS_EMR, db_master.getLainLainIDDiagnosa(kep.id_emr) ID_DIAGNOSA_LAIN_LAIN
    , db_master.getLainLainDiagnosa(kep.id_emr) DIAGNOSA_LAIN_LAIN
    , db_master.getLainLainIDNOC(kep.id_emr) ID_NOC_LAIN_LAIN, db_master.getLainLainNOC(kep.id_emr) NOC_LAIN_LAIN
    , db_master.getLainLainIDNIC(kep.id_emr) ID_NIC_LAIN_LAIN, db_master.getLainLainNIC(kep.id_emr) NIC_LAIN_LAIN
    , master.getNamaLengkapPegawai(peng.NIP) user_yg_verif
    , keperawatan.getMasalahKesehatanParentID(kep.id_emr) MASALAH_KESEHATAN_PARENT_ID
    , keperawatan.getMasalahKesehatanParentDESC(kep.id_emr) MASALAH_KESEHATAN_PARENT_DESC

    , keperawatan.getMasalahKesehatanChildID(kep.id_emr) MASALAH_KESEHATAN_CHILD_ID
    , keperawatan.getMasalahKesehatanChildDESC(kep.id_emr) MASALAH_KESEHATAN_CHILD_DESC
    FROM keperawatan.tb_keperawatan kep
    LEFT JOIN keperawatan.tb_anamnesa_perawat anam ON kep.id_emr = anam.id_emr
    LEFT JOIN keperawatan.tb_riwayat_kesehatan rkes ON kep.id_emr = rkes.id_emr
    LEFT JOIN db_pasien.tb_tb_bb tbbb ON tbbb.ref = kep.id_emr
    LEFT JOIN db_pasien.tb_kesadaran sadar ON sadar.ref = kep.id_emr
    LEFT JOIN db_pasien.tb_tanda_vital tv ON tv.ref = kep.id_emr
    LEFT JOIN keperawatan.tb_skrining_nyeri sn ON sn.ref = kep.id_emr
    LEFT JOIN keperawatan.tb_pemeriksaan_fisik pf ON pf.id_emr = kep.id_emr
    LEFT JOIN keperawatan.tb_edukasi_keperawatan ek ON ek.id_emr = kep.id_emr
    LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = kep.NOPEN
    LEFT JOIN keperawatan.tb_ews ews ON ews.ref = kep.id_emr AND ews.`status`=1
    LEFT JOIN db_pasien.tb_o2 odua ON odua.ref = kep.id_emr AND odua.data_source=31 AND odua.`status`=1
    LEFT JOIN aplikasi.pengguna peng ON peng.ID = kep.verif_oleh
    WHERE kep.id_emr = "'.$idemr.'" AND kep.jenis = 17 AND kep.status=1'
);
return $query->row_array();
}

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
