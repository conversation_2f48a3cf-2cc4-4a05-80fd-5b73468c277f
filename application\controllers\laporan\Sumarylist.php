<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Sumarylist extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('laporanModel','masterModel','pengkajianAwalModel'));
  }

  public function index()
  {
    $data = array(
      'title' => 'Halaman Laporan Sumarylist',
      'isi'   => 'Laporan/Sumarylist/cariMr',
    );
    $this->load->view('layout/wrapper',$data);
  }

  public function dataSummarylist()
  {

    $nomr        = $this->input->post('nomr');
    $namaPasien  = $this->laporanModel->namaPasien($nomr);
    $tblSummaryL = $this->pengkajianAwalModel->tblSummary($nomr)->result_array();
    $dataPasien = $this->masterModel->dataDiriPasien($nomr);

    $data = array(
      'title'       => 'Halaman Laporan Sumarylist',
      'isi'         => 'Laporan/Sumarylist/index',
      'nomr'        => $nomr,
      'namaPasien'  => $namaPasien,
      'tblSummaryL' => $tblSummaryL,
      'dataPasien' => $dataPasien,
    );
    $this->load->view('layout/wrapper',$data);
  }

  public function tblSummarylist()
  {
    $draw   = intval($this->input->post("draw"));
    $start  = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));

    $nomr = $this->input->post('nomr');

    $tblSummaryL = $this->pengkajianAwalModel->tblSummary($nomr);

    $data = array();
    $no = 1;
    foreach ($tblSummaryL->result() as $ts) {
      $data[] = array(
        $no,
        date("d-m-Y H:i:s",strtotime($ts->tanggal)),
        $ts->RUANGASAL,
        $ts->NAMADOKTER,
        '<a href="#detailSummaryL" class="btn btn-primary btn-block btn-sm" data-toggle="modal" data-backdrop="static" data-keyboard="false" data-id="' . $ts->id . '"><i class="fas fa-edit"></i> Edit</a>'
      );

      $no++;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $tblSummaryL->num_rows(),
      "recordsFiltered" => $tblSummaryL->num_rows(),
      "data" => $data
    );
    echo json_encode($output);
  }

  public function detailSummary()
  {
    $id = $this->input->post("id");
    $datSum = $this->pengkajianAwalModel->detailSum($id);

    echo '<div class="modal-header">';
    echo '<h4 class="modal-title" id="myModalLabel">Summary List ' . date("d-m-Y", strtotime($datSum["tanggal"])) . '</h4>';
    echo '<button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>';
    echo '</div>';
    echo '<form id="formEditSum">';
    echo '<input type="hidden" value="' . $datSum['id'] . '" name="idSum">';
    echo '<div class="modal-body">';
    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Diagnosis</label>';
    echo '<textarea class="form-control" name="editDiagnosis" cols="30" rows="5" placeholder="Jelaskan" readonly>' . $datSum['diagnosis'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Operasi</label>';
    echo '<textarea class="form-control" name="editOperasi" cols="30" rows="5" placeholder="Jelaskan" readonly>' . $datSum['operasi'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Kemoterapi</label>';
    echo '<textarea class="form-control" name="editKemoterapi" cols="30" rows="5" placeholder="Jelaskan" readonly>' . $datSum['kemoterapi'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Radiasi</label>';
    echo '<textarea class="form-control" name="editRadiasi"  cols="30" rows="5" placeholder="Jelaskan" readonly>' . $datSum['radiasi'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Pengobatan / Tindakan Lainya</label>';
    echo '<textarea class="form-control" name="editPengobatan" cols="30" rows="5" placeholder="Jelaskan" readonly>' . $datSum['pengobatan'] . '</textarea>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<div class="row">';
    echo '<div class="col-md-12">';
    echo '<div class="form-group">';
    echo '<label for="">Dokter</label>';
    echo '<input type="text" class="form-control" value="' . $datSum['NAMADOKTER'] . '" readonly>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<div class="modal-footer">';
    echo '<button type="button" class="btn btn-warning" data-dismiss="modal"><i class="fa fa-refresh"></i> Close</button>';
    echo '</div>';
    echo '</div>';
    echo '</form>';
  }

  public function detailSLP()
    {
        $id = $this->input->post("id");
        $datSLP = $this->pengkajianAwalModel->detailSum($id);

        echo '<div class="modal-header">';
        echo '<h4 class="modal-title" id="myModalLabel">Summary List ' . date("d-m-Y", strtotime($datSLP["tanggal"])) . '</h4>';
        echo '<button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>';
        echo '</div>';
        echo '<form id="formEditSLP">';
        echo '<input type="hidden" value="' . $datSLP['id'] . '" name="idSum">';
        echo '<div class="modal-body">';
        echo '<div class="row">';
        echo '<div class="col-md-12">';
        echo '<div class="form-group">';
        echo '<label for="">Diagnosis</label>';
        echo '<textarea class="form-control" name="editDiagnosis" rows="5" placeholder="Jelaskan">' . $datSLP['diagnosis'] . '</textarea>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '<div class="row">';
        echo '<div class="col-md-12">';
        echo '<div class="form-group">';
        echo '<label for="">Tatalaksana (Operasi, Terapi Sistemik, Radiasi)</label>';
        echo '<textarea class="form-control" name="edittatalaksana" rows="10" placeholder="Jelaskan">' . $datSLP['tata_laksana'] . '</textarea>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '<div class="row">';
        echo '<div class="col-md-12">';
        echo '<div class="form-group">';
        echo '<label for="">Catatan Penting Lainnya</label>';
        echo '<textarea class="form-control" name="editcatatanPenting" rows="10" placeholder="Jelaskan">' . $datSLP['catatan_penting'] . '</textarea>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        echo '<div class="row">';
        echo '<div class="col-md-12">';
        echo '<div class="form-group">';
        echo '<label for="">Dokter</label>';
        echo '<input type="text" class="form-control" value="' . $datSLP['NAMADOKTER'] . '" readonly>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '<div class="modal-footer">';
        echo '<button type="button" class="btn btn-warning" data-dismiss="modal"><i class="fa fa-refresh"></i> Close</button>';
        if ($this->session->userdata('id') == $datSLP['oleh'] && $datSLP['STATUSBUTTON'] == 1) {
            echo '<button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> Simpan</button>';
        }
        echo '</div>';
        echo '</div>';
        echo '</form>';
    }

}

/* End of file Sumarylist.php */
/* Location: ./application/controllers/laporan/Sumarylist.php */
