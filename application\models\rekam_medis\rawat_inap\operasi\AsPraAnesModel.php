<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AsPraAnesModel extends MY_Model {

  public function historyAsPraAnes($nokun)
  {
    $query = $this->db->query("SELECT apa.created_at TANGGAL_ASPRAANES, rk.DESKRIPSI RUANGAN_ASPRAANES, peng.NAMA NAMA_ASPRAANES
        , apa.id ID, p.NOMOR NOPEN, p.NORM NORM, apa.nokun NOKUN

        FROM medis.tb_assesmen_pra_anestesi_pasien apa

        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = apa.nokun
        LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
        LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
        LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
        LEFT JOIN aplikasi.pengguna peng ON peng.ID = apa.oleh

        WHERE apa.status=1 AND apa.nokun='$nokun'

        ORDER BY apa.created_at DESC");
    return $query->result_array();
}

public function simpanAsPraAnesDokter($dataAsPraAnesDokter)
{
  $this->db->insert('medis.tb_assesmen_pra_anestesi_dokter', $dataAsPraAnesDokter);
  return $this->db->insert_id();
}  

public function simpanTbBb($dataTbBb)
{
  $this->db->insert('db_pasien.tb_tb_bb', $dataTbBb);
  return $this->db->insert_id();
}  

public function simpanTandaVital($dataTandaVital)
{
  $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
  return $this->db->insert_id();
}  

public function getAsPraAnes($id)
{
    $query = $this->db->query(
        "SELECT *
        FROM medis.tb_assesmen_pra_anestesi_pasien ap
        WHERE ap.id='$id' AND ap.`status`='1'"
    );
    return $query->row_array();
}

public function getAsPraAnesDokter($id)
{
    $query = $this->db->query(
        "SELECT apad.`*`, tbbt.tb TB, tbbt.bb BB, tbbt.jenis JENIS_TB_BB, tv.td_sistolik TD_SISTOLIK, tv.td_diastolik TD_DIASTOLIK, tv.nadi NADI, tv.suhu SUHU
        FROM medis.tb_assesmen_pra_anestesi_dokter apad

        LEFT JOIN db_pasien.tb_tb_bb tbbt ON apad.id_tb_bb = tbbt.id 
        LEFT JOIN db_pasien.tb_tanda_vital tv ON apad.id_tanda_vital = tv.id 

        WHERE apad.id_praanes_pasien = '$id' AND apad.`status`= '1'"
    );
    return $query->row_array();
}

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
