<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PengkajianStoma extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		if ($this->session->userdata('logged_in') == false) {
			redirect('login');
		}

		if (!in_array(8, $this->session->userdata('akses'))) {
			redirect('login');
		}

		date_default_timezone_set("Asia/Bangkok");
		$this->load->model(array('masterModel', 'pengkajianAwalModel'));
	}

  public function index()
  {
    $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $jenisStoma = $this->masterModel->referensi(561);
    $ureterostomyStent = $this->masterModel->referensi(562);
    $conduitStent = $this->masterModel->referensi(563);
    $tipeStoma = $this->masterModel->referensi(564);
    $tipeStomaYaTidak = $this->masterModel->referensi(565);
    $keluaranStoma = $this->masterModel->referensi(566);
    $mukosaStoma = $this->masterModel->referensi(567);
    $kelembapan = $this->masterModel->referensi(568);
    $edema = $this->masterModel->referensi(569);
    $kulitSekitarStoma = $this->masterModel->referensi(570);
    $komplikasiStoma = $this->masterModel->referensi(571);
    $komplikasiStomaYaTidak = $this->masterModel->referensi(572);
    $jenisKantong = $this->masterModel->referensi(573);
    $aksesoris = $this->masterModel->referensi(574);
    $pemilihanKantong = $this->masterModel->referensi(575);
    $kebutuhanPembelajaranStoma = $this->masterModel->referensi(576);
    $kontrolPoliklinikStoma = $this->masterModel->referensi(560);
    $masalahkeperawatan_stoma = $this->masterModel->referensi(939);
    $psikososial = $this->masterModel->referensi(602);
    $riwayatAlergi = $this->masterModel->referensi(2);
    $skriningResikoJatuhPusing = $this->masterModel->referensi(120);
    $skriningResikoJatuhBerdiri = $this->masterModel->referensi(121);
    $skriningResikoJatuh6Bulan = $this->masterModel->referensi(122);
    $skriningNyeri = $this->masterModel->referensi(7);
    $skalaNyeriNRS = $this->masterModel->referensi(114);
    $skalaNyeriWBR = $this->masterModel->referensi(115);
    $skalaNyeriFLACC = $this->masterModel->referensi(123);
    $skalaNyeriBPS = $this->masterModel->referensi(133);
    $efeksampingNRS = $this->masterModel->referensi(118);
    $efeksampingWBR = $this->masterModel->referensi(119);
    $efeksampingFLACC = $this->masterModel->referensi(131);
    $efeksampingBPS = $this->masterModel->referensi(134);
    $statusnyeriNRS = $this->masterModel->referensi(136);
    $statusnyeriWBR = $this->masterModel->referensi(136);
    $statusnyeriFLACC = $this->masterModel->referensi(136);
    $statusnyeriBPS = $this->masterModel->referensi(136);
    $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
    $pengkajianNyeriQuality = $this->masterModel->referensi(9);
    $pengkajianNyeriTime = $this->masterModel->referensi(12);
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
    $ruanganRawatInap = $this->masterModel->ruanganRawatInap();
    $historyPengkajianStoma = $this->pengkajianAwalModel->historyPengkajianStoma();
    $getPengkajianStoma = '';
    if ($this->uri->segment(8) != "") {
      $id_stoma = $this->uri->segment(8);
      $getPengkajianStoma = $this->pengkajianAwalModel->getPengkajianStoma($id_stoma);
    }

    $data = array(
      'getNomr' => $getNomr,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
      'getPengkajianStoma' => $getPengkajianStoma,
      'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
      'pengkajianNyeriQuality' => $pengkajianNyeriQuality,
      'pengkajianNyeriTime' => $pengkajianNyeriTime,
      'skalaNyeriNRS' => $skalaNyeriNRS,
      'skalaNyeriWBR' => $skalaNyeriWBR,
      'skalaNyeriFLACC' => $skalaNyeriFLACC,
      'skalaNyeriBPS' => $skalaNyeriBPS,
      'efeksampingNRS' => $efeksampingNRS,
      'efeksampingWBR' => $efeksampingWBR,
      'efeksampingFLACC' => $efeksampingFLACC,
      'efeksampingBPS' => $efeksampingBPS,
      'statusnyeriNRS' => $statusnyeriNRS,
      'statusnyeriWBR' => $statusnyeriWBR,
      'statusnyeriFLACC' => $statusnyeriFLACC,
      'statusnyeriBPS' => $statusnyeriBPS,
      'skriningNyeri' => $skriningNyeri,
      'psikososial' => $psikososial,
      'skriningResikoJatuhPusing' => $skriningResikoJatuhPusing,
      'skriningResikoJatuhBerdiri' => $skriningResikoJatuhBerdiri,
      'skriningResikoJatuh6Bulan' => $skriningResikoJatuh6Bulan,
      'riwayatAlergi' => $riwayatAlergi,
      'jenisStoma' => $jenisStoma,
      'ureterostomyStent' => $ureterostomyStent,
      'conduitStent' => $conduitStent,
      'tipeStoma' => $tipeStoma,
      'tipeStomaYaTidak' => $tipeStomaYaTidak,
      'keluaranStoma' => $keluaranStoma,
      'mukosaStoma' => $mukosaStoma,
      'kelembapan' => $kelembapan,
      'edema' => $edema,
      'kulitSekitarStoma' => $kulitSekitarStoma,
      'komplikasiStoma' => $komplikasiStoma,
      'komplikasiStomaYaTidak' => $komplikasiStomaYaTidak,
      'jenisKantong' => $jenisKantong,
      'aksesoris' => $aksesoris,
      'pemilihanKantong' => $pemilihanKantong,
      'kebutuhanPembelajaranStoma' => $kebutuhanPembelajaranStoma,
      'kontrolPoliklinikStoma' => $kontrolPoliklinikStoma,
      'masalahkeperawatan_stoma' => $masalahkeperawatan_stoma,
      'historyPengkajianStoma' => $historyPengkajianStoma,
      'status_pasien' => $this->masterModel->referensi(1726),
      'konsultasi_dpjp' => $this->masterModel->referensi(1727),
    );

    // print_r($data);exit();
    $this->load->view('Pengkajian/luka/pengkajianStoma', $data);
  }

	public function action_pengkajianstoma($param){
		if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
			if ($param == 'tambah' || $param == 'ubah'){
				$post = $this->input->post();
				$get_id_stoma = !empty($post['id_stoma']) ? $post['id_stoma'] : $this->pengkajianAwalModel->getIdEmr();

				if (isset($post['lokasi_stoma']) == "on") {
					$lokasi_stoma = 1;
				} else {
					$lokasi_stoma = 0;
				}

				$datapengkajianstoma = array(
					'id_stoma' 						=> $get_id_stoma,
					'nokun' 							=> isset($post['nokun']) ? $post['nokun'] : null,
					'tanggal_pengkajian' 	=> isset($post['tanggal_pengkajian']) ? $post['tanggal_pengkajian'] : null,
					'pukul_pengkajian' 		=> isset($post['pukul_pengkajian']) ? $post['pukul_pengkajian'] : null,
					'jenis_rawat' 				=> isset($post['jenis_rawat']) ? $post['jenis_rawat'] : null,
					'ruangan_stoma' => isset($post['ruangan_stoma']) ? $post['ruangan_stoma'] : null,
					'status_pasien' => isset($post['status_pasien_stoma']) ? $post['status_pasien_stoma'] : null,
					'konsultasi_dpjp' => isset($post['konsultasi_dpjp_stoma']) ? $post['konsultasi_dpjp_stoma'] : null,
					'diagnosis_stoma' => isset($post['diagnosis_stoma']) ? $post['diagnosis_stoma'] : null,
					'riwayat_alergi' => isset($post['riwayat_alergi_stoma']) ? $post['riwayat_alergi_stoma'] : null,
					'isi_riwayat_alergi' => isset($post['sebutkanalergi_stoma']) ? $post['sebutkanalergi_stoma'] : null,
					'status_lokalis_not' => $lokasi_stoma,
					'ureterostomyStent' => isset($post['ureterostomyStent1911']) ? $post['ureterostomyStent1911'] : null,
					'ileal' => isset($post['ureterostomyStent1912']) ? $post['ureterostomyStent1912'] : null,
					'tipe_stoma' => isset($post['tipe_stoma']) ? $post['tipe_stoma'] : null,
					'loop_stoma' => isset($post['tipe_stoma_ya']) ? $post['tipe_stoma_ya'] : null,
					'keluaran_stoma' => isset($post['keluaran_stoma']) ? $post['keluaran_stoma'] : null,
					'mukosa_stoma' => isset($post['mukosa_stoma']) ? $post['mukosa_stoma'] : null,
					'kelembaban_stoma' => isset($post['kelembapan_stoma']) ? $post['kelembapan_stoma'] : null,
					'edema_stoma' => isset($post['edema_stoma']) ? $post['edema_stoma'] : null,
					'ukuran_stoma' => isset($post['ukuran_stoma']) ? $post['ukuran_stoma'] : null,
					'ketinggian_stoma' => isset($post['ketinggian_stoma']) ? $post['ketinggian_stoma'] : null,
					'kulit_sekitar_stoma' => isset($post['kulit_sekitar_stoma']) ? $post['kulit_sekitar_stoma'] : null,
					'komplikasi_stoma' => isset($post['komplikasi_stoma']) ? $post['komplikasi_stoma'] : null,
					'jenis_kantong_stoma' => isset($post['jenis_kantong_stoma']) ? $post['jenis_kantong_stoma'] : null,
					'status_psikososial' => isset($post['psikososial_stoma']) ? $post['psikososial_stoma'] : null,
					'isi_status_psikososial' => isset($post['psikososialLainnya']) ? $post['psikososialLainnya'] : null,
					'perawatan_kulit' => isset($post['perawatanKulSekStom']) ? $post['perawatanKulSekStom'] : null,
					'stoma_frekuensi' => isset($post['stoma_frekuensi']) ? $post['stoma_frekuensi'] : null,
					'pemilihan_kantong' => isset($post['pemilihan_kantong']) ? $post['pemilihan_kantong'] : null,
					'kebutuhan_pembelajaran_stoma' => isset($post['kebutuhan_pembelajaran_stoma']) ? $post['kebutuhan_pembelajaran_stoma'] : null,
					'kontrol_poliklinik_luka' => isset($post['kontrol_poliklinikstom']) ? $post['kontrol_poliklinikstom'] : null,
					'tanggal_kontrol' => isset($post['tanggalKontrolPoliklinik']) ? $post['tanggalKontrolPoliklinik'] : null,
					'status' => 1,
					'oleh' => isset($post['pengisi']) ? $post['pengisi'] : null,

          'masalahkeperawatan_stoma' => isset($post['masalahkeperawatan_stoma']) ? json_encode($post['masalahkeperawatan_stoma']) : null,
          'masalahkeperawatanlainnya' => isset($post['masalahkeperawatanlainnya']) ? $post['masalahkeperawatanlainnya'] : null,

          'vertigo' => isset($post['skriningresikojatuhpusing_stoma']) ? $post['skriningresikojatuhpusing_stoma'] : null,
          'berdiri' => isset($post['skriningresikojatuhberdiri_stoma']) ? $post['skriningresikojatuhberdiri_stoma'] : null,
          'jatuh' => isset($post['skriningresikojatuh6bulan_stoma']) ? $post['skriningresikojatuh6bulan_stoma'] : null,

          'skrining_nyeri' => isset($post['skrining_nyeri']) ? $post['skrining_nyeri'] : null,
          'skala_nyeri' => isset($post['skor_nyeri']) ? $post['skor_nyeri'] : null,
          'farmakologi' => isset($post['farmakologi']) ? $post['farmakologi'] : null,
          'non_farmakologi' => isset($post['non_farmakologi']) ? $post['non_farmakologi'] : null,
          'efek_samping' => isset($post['efek_samping']) ? $post['efek_samping'] : null,
          'efek_samping_lainnya' => isset($post['efek_samping_lain']) ? $post['efek_samping_lain'] : null,
          'propocative' => isset($post['provocative']) ? $post['provocative'] : null,
          'quality' => isset($post['quality']) ? $post['quality'] : null,
          'quality_lainnya' => isset($post['quality_lainnya']) ? $post['quality_lainnya'] : null,
          'regio' => isset($post['regio']) ? $post['regio'] : null,
          'severity' => isset($post['severity']) ? $post['severity'] : null,
          'time' => isset($post['time']) ? $post['time'] : null,
          'time_lainnya' => isset($post['time_lainnya']) ? $post['time_lainnya'] : null,

          'tekanan_darah_1' => isset($post['tekanan_darah_1']) ? $post['tekanan_darah_1'] : null,
          'tekanan_darah_2' => isset($post['tekanan_darah_2']) ? $post['tekanan_darah_2'] : null,
          'pernapasan' => isset($post['pernapasan']) ? $post['pernapasan'] : null,
          'nadi' => isset($post['nadi']) ? $post['nadi'] : null,
          'suhu' => isset($post['suhu']) ? $post['suhu'] : null,
          'one_two_piece' => isset($post['one_two_piece']) ? $post['one_two_piece'] : null,
				);
        // print_r($datapengkajianstoma);exit();

				$datajenis_stoma = array();
				$indexjenis_stoma = 0;
				if (isset($post['jenis_stoma'])) {
					foreach ($post['jenis_stoma'] as $input) {
						if ($post['jenis_stoma'][$indexjenis_stoma] != "") {
							array_push(
								$datajenis_stoma, array(
									'id_stoma' => $get_id_stoma,
									'jenis_stoma' => $post['jenis_stoma'][$indexjenis_stoma],
								)
							);
						}
						$indexjenis_stoma++;
					}
				}
		   //print_r($datajenis_stoma);exit();

				$datakomplikasi_stoma = array();
				$indexkomplikasi_stoma = 0;
				if (isset($post['komStomaJikaYa'])) {
					foreach ($post['komStomaJikaYa'] as $input) {
						if ($post['komStomaJikaYa'][$indexkomplikasi_stoma] != "") {
							array_push(
								$datakomplikasi_stoma, array(
									'id_stoma' => $get_id_stoma,
									'komplikasi_ya' => $post['komStomaJikaYa'][$indexkomplikasi_stoma],
								)
							);
						}
						$indexkomplikasi_stoma++;
					}
				}
				//print_r($datakomplikasi_stoma);exit();

				$datakebutuhan_pembelajaran_stoma = array();
				$indexkebutuhan_pembelajaran_stoma = 0;
				if (isset($post['kebutuhan_pem_stoma'])) {
					foreach ($post['kebutuhan_pem_stoma'] as $input) {
						if ($post['kebutuhan_pem_stoma'][$indexkebutuhan_pembelajaran_stoma] != "") {
							array_push(
								$datakebutuhan_pembelajaran_stoma, array(
									'id_stoma' => $get_id_stoma,
									'kebutuhan_pembelajaran' => $post['kebutuhan_pem_stoma'][$indexkebutuhan_pembelajaran_stoma],
								)
							);
						}
						$indexkebutuhan_pembelajaran_stoma++;
					}
				}  
				//print_r($datakebutuhan_pembelajaran_stoma);exit();
				
				$dataaksesoris_stoma = array();
				$indexaksesoris_stoma = 0;
				if (isset($post['aksesoris_stoma'])) {
					foreach ($post['aksesoris_stoma'] as $input) {
						if ($post['aksesoris_stoma'][$indexaksesoris_stoma] != "") {
							array_push(
								$dataaksesoris_stoma, array(
									'id_stoma' => $get_id_stoma,
									'aksesoris' => $post['aksesoris_stoma'][$indexaksesoris_stoma],
								)
							);
						}
						$indexaksesoris_stoma++;
					}
				} 

				if (!empty($post['id_stoma'])) {
					$this->db->delete('keperawatan.tb_stoma_jenis', array('id_stoma' => $get_id_stoma));
					$this->db->delete('keperawatan.tb_stoma_kebutuhan_pembelajaran', array('id_stoma' => $get_id_stoma));
					$this->db->delete('keperawatan.tb_stoma_komplikasi_ya', array('id_stoma' => $get_id_stoma));
					$this->db->delete('keperawatan.tb_stoma_aksesoris', array('id_stoma' => $get_id_stoma));
					if (isset($post['jenis_stoma'])) {
            $this->db->insert_batch('keperawatan.tb_stoma_jenis', $datajenis_stoma);
          }
          if (isset($post['kebutuhan_pem_stoma'])) {
            $this->db->insert_batch('keperawatan.tb_stoma_kebutuhan_pembelajaran', $datakebutuhan_pembelajaran_stoma);
          }
          if (isset($post['komStomaJikaYa'])) {
            $this->db->insert_batch('keperawatan.tb_stoma_komplikasi_ya', $datakomplikasi_stoma);
          }
          if (isset($post['aksesoris_stoma'])) {
            $this->db->insert_batch('keperawatan.tb_stoma_aksesoris', $dataaksesoris_stoma);
          }
					$this->db->replace('keperawatan.tb_stoma', $datapengkajianstoma);
					$result = array('status' => 'success', 'pesan' => 'ubah');
				}else {
					$this->db->insert('keperawatan.tb_stoma', $datapengkajianstoma);
					if (isset($post['jenis_stoma'])) {
						$this->db->insert_batch('keperawatan.tb_stoma_jenis', $datajenis_stoma);
					}
					if (isset($post['kebutuhan_pem_stoma'])) {
						$this->db->insert_batch('keperawatan.tb_stoma_kebutuhan_pembelajaran', $datakebutuhan_pembelajaran_stoma);
					}
					if (isset($post['komStomaJikaYa'])) {
						$this->db->insert_batch('keperawatan.tb_stoma_komplikasi_ya', $datakomplikasi_stoma);
					}
					if (isset($post['aksesoris_stoma'])) {
						$this->db->insert_batch('keperawatan.tb_stoma_aksesoris', $dataaksesoris_stoma);
					}
						$result = array('status' => 'success');
					}
					echo json_encode($result);
			}
		}
	}

  public function view_stoma()
  {
    $id  = $this->input->post('id');
    $getPengkajianStoma = $this->pengkajianAwalModel->getPengkajianStoma($id);
    $jenisStoma = $this->masterModel->referensi(561);
    $ureterostomyStent = $this->masterModel->referensi(562);
    $conduitStent = $this->masterModel->referensi(563);
    $tipeStoma = $this->masterModel->referensi(564);
    $tipeStomaYaTidak = $this->masterModel->referensi(565);
    $keluaranStoma = $this->masterModel->referensi(566);
    $mukosaStoma = $this->masterModel->referensi(567);
    $kelembapan = $this->masterModel->referensi(568);
    $edema = $this->masterModel->referensi(569);
    $kulitSekitarStoma = $this->masterModel->referensi(570);
    $komplikasiStoma = $this->masterModel->referensi(571);
    $komplikasiStomaYaTidak = $this->masterModel->referensi(572);
    $jenisKantong = $this->masterModel->referensi(573);
    $aksesoris = $this->masterModel->referensi(574);
    $pemilihanKantong = $this->masterModel->referensi(575);
    $kebutuhanPembelajaranStoma = $this->masterModel->referensi(576);
    $kontrolPoliklinikStoma = $this->masterModel->referensi(560);
    $masalahkeperawatan_stoma = $this->masterModel->referensi(939);
    $psikososial = $this->masterModel->referensi(602);
    $riwayatAlergi = $this->masterModel->referensi(2);
    $skriningResikoJatuhPusing = $this->masterModel->referensi(120);
    $skriningResikoJatuhBerdiri = $this->masterModel->referensi(121);
    $skriningResikoJatuh6Bulan = $this->masterModel->referensi(122);
    $skriningNyeri = $this->masterModel->referensi(7);
    $skalaNyeriNRS = $this->masterModel->referensi(114);
    $skalaNyeriWBR = $this->masterModel->referensi(115);
    $skalaNyeriFLACC = $this->masterModel->referensi(123);
    $skalaNyeriBPS = $this->masterModel->referensi(133);
    $efeksampingNRS = $this->masterModel->referensi(118);
    $efeksampingWBR = $this->masterModel->referensi(119);
    $efeksampingFLACC = $this->masterModel->referensi(131);
    $efeksampingBPS = $this->masterModel->referensi(134);
    $statusnyeriNRS = $this->masterModel->referensi(136);
    $statusnyeriWBR = $this->masterModel->referensi(136);
    $statusnyeriFLACC = $this->masterModel->referensi(136);
    $statusnyeriBPS = $this->masterModel->referensi(136);
    $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
    $pengkajianNyeriQuality = $this->masterModel->referensi(9);
    $pengkajianNyeriTime = $this->masterModel->referensi(12);
    $ruanganRawatJalan = $this->masterModel->ruanganRawatJalan();
        $ruanganRawatInap = $this->masterModel->ruanganRawatInap();

    // print_r($getPengkajianStoma);exit();

    $data = array(
      // 'get_adl' => $get_adl,
      'ruanganRawatJalan' => $ruanganRawatJalan,
      'ruanganRawatInap' => $ruanganRawatInap,
      'getPengkajianStoma' => $getPengkajianStoma,
      'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
      'pengkajianNyeriQuality' => $pengkajianNyeriQuality,
      'pengkajianNyeriTime' => $pengkajianNyeriTime,
      'skalaNyeriNRS' => $skalaNyeriNRS,
      'skalaNyeriWBR' => $skalaNyeriWBR,
      'skalaNyeriFLACC' => $skalaNyeriFLACC,
      'skalaNyeriBPS' => $skalaNyeriBPS,
      'efeksampingNRS' => $efeksampingNRS,
      'efeksampingWBR' => $efeksampingWBR,
      'efeksampingFLACC' => $efeksampingFLACC,
      'efeksampingBPS' => $efeksampingBPS,
      'statusnyeriNRS' => $statusnyeriNRS,
      'statusnyeriWBR' => $statusnyeriWBR,
      'statusnyeriFLACC' => $statusnyeriFLACC,
      'statusnyeriBPS' => $statusnyeriBPS,
      'skriningNyeri' => $skriningNyeri,
      'psikososial' => $psikososial,
      'skriningResikoJatuhPusing' => $skriningResikoJatuhPusing,
      'skriningResikoJatuhBerdiri' => $skriningResikoJatuhBerdiri,
      'skriningResikoJatuh6Bulan' => $skriningResikoJatuh6Bulan,
      'riwayatAlergi' => $riwayatAlergi,
      'jenisStoma' => $jenisStoma,
      'ureterostomyStent' => $ureterostomyStent,
      'conduitStent' => $conduitStent,
      'tipeStoma' => $tipeStoma,
      'tipeStomaYaTidak' => $tipeStomaYaTidak,
      'keluaranStoma' => $keluaranStoma,
      'mukosaStoma' => $mukosaStoma,
      'kelembapan' => $kelembapan,
      'edema' => $edema,
      'kulitSekitarStoma' => $kulitSekitarStoma,
      'komplikasiStoma' => $komplikasiStoma,
      'komplikasiStomaYaTidak' => $komplikasiStomaYaTidak,
      'jenisKantong' => $jenisKantong,
      'aksesoris' => $aksesoris,
      'pemilihanKantong' => $pemilihanKantong,
      'kebutuhanPembelajaranStoma' => $kebutuhanPembelajaranStoma,
      'kontrolPoliklinikStoma' => $kontrolPoliklinikStoma,
      'masalahkeperawatan_stoma' => $masalahkeperawatan_stoma,
    );
    $this->load->view('Pengkajian/luka/view',$data);
  }
}
/* End of file PengkajianStoma.php */
/* Location: ./application/controllers/luka/PengkajianStoma.php */
