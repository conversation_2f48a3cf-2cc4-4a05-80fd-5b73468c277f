<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Adjustment extends ci_controller{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(43, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->library('cart');
    $this->load->model(array('inventory/Model_barang','inventory/Model_permintaan','inventory/Model_adjustment'));
        // chek_session();
  }

  function index()
  {
    {
      $barang     = $this->Model_barang->tampilkan_data();
      $gudang     = $this->Model_permintaan->tampilkan_gudang()->result();
      $detil     = $this->Model_adjustment->dataAdjustment()->result();
      $data       = array(
        'title'         => 'Halaman Input Permintaan',
        'isi'           => 'inventory/Adjustment/index',
        'barang'        => $barang,
        'gudang'        => $gudang,
        'detil'         => $detil
      );
      $this->load->view('layout/wrapper',$data);
    }

  }


  function tambah(){
    if(isset($_POST['submit'])){
      $oleh         = $this->session->userdata("id");
      $NOMOR        = $this->Model_adjustment->get_no_adjust();
      $GUDANG       =   $this->input->post('GUDANG');
      $JUMLAH       =   $this->input->post('JUMLAH');
      $BARANG       =   $this->input->post('BARANG');
      $STOK         =   $this->input->post('STOK');
      $KETERANGAN   =   $this->input->post('KETERANGAN');
      $HARGA        =   $this->input->post('HARGA');
      $STOKAKHIR    =  $JUMLAH+$STOK;
      $oleh = $this->session->userdata("id");
//echo "<pre>";print_r($_POST);exit();
      $data       =   array
      ( 'NOMOR'             => $NOMOR,
        'ASAL'              =>$GUDANG,
        'OLEH'              =>$oleh
      );

      $datadetil  =   array
      ( 'NOMOR_PERMINTAAN'  =>$NOMOR,
        'BARANG'            =>$BARANG,
        'STOK_AWAL'         =>$STOK,
        'STOK_AKHIR'        =>$STOKAKHIR,
        'JUMLAH_RETUR'      =>$JUMLAH,
        'KETERANGAN'        =>$KETERANGAN
      );

      $this->db->insert('invenumum.permintaan_perubahan_stok',$data);
      $this->db->insert('invenumum.permintaan_perubahan_stok_detil',$datadetil);
      $insert_id = $this->db->insert_id();
      $datajust  =   array
      ( 'BARANG_RUANGAN'    =>$BARANG,
        'JENIS'             =>4,
        'REF'               =>$insert_id,
        'STOK'              =>$STOKAKHIR,
        'JUMLAH'            =>$STOKAKHIR,
        'HARGA'             =>$HARGA
      );
      $this->db->insert('invenumum.transaksi_stok_ruangan',$datajust);
      redirect('inventory/Adjustment');
    }
  }

  public function dataAdjust()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listadjust = $this->Model_adjustment->dataAdjustment();

    //echo "<pre>";print_r($listPasien);exit();
    $data  = array();
    $no    =1;
    foreach($listadjust->result() as $lp) {


     $data[] = array(
      $no,
      $lp->NOMOR,
      $lp->BARANG,
      $lp->JUMLAH_RETUR,
      $lp->KETERANGAN,
      // '<a href="#" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-id="'.$lp->NOMOR.'"><i class="fas fa-print"></i> Cetak</a>',

    );
     $no++;
   }

   $output = array(
    "draw"            => $draw,
    "recordsTotal"    => $listadjust->num_rows(),
    "recordsFiltered" => $listadjust->num_rows(),
    "data"            => $data
  );
   echo json_encode($output);
 }

 function tampil_stok_barang(){
  $id=$this->input->post('id');
  $data=$this->Model_adjustment->get_subkategori_nama($id);
  echo json_encode($data);
}

function tampil_id_barang(){
  $id=$this->input->post('id');
  $data=$this->Model_adjustment->get_subkategori($id);
  echo json_encode($data);
}


}