<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormulirPMFAK extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    public function index()
    {
        $nokun = $this->uri->segment(4);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listPalliative = $this->masterModel->listPalliative();
        $historyPMFAK = $this->pengkajianAwalModel->historyPMFAK($getNomr['NORM']);

        $data = array(
            'nokun' => $nokun,
            'getNomr' => $getNomr,
            'listPalliative' => $listPalliative,
            'historyPMFAK' => $historyPMFAK
        );

        $this->load->view('Pengkajian/igd/formulirPMFAK/index', $data);
    }

    ///////////////////////////////////////////// Permintaan Dirawat ///////////////////////////////////////////
    public function simpanFormPermintaanDirawat()
    {
        $kunjungan = $this->input->post("nokun");
        $kasus = $this->input->post("radio_emergency");
        $rencanaperawatan = $this->input->post("rencanaperawatan");
        $ruangan = $this->input->post("ruangan");
        $diagnosis = $this->input->post("diagnosis");
        $rencana = $this->input->post("pilih_rencana");
        $hasilyangdiharapkan = $this->input->post("hasilyangdiharapkan");
        $keadaanumum = $this->input->post("keadaanumum");
        $prosedurmedislain = $this->input->post("prosedurmedislain");
        $keumlainlain = $this->input->post("keumlainlain");
        $jenis = $this->input->post("pilih_cito");
        $tanggal_rencana = $this->input->post("tanggalrencana");
        $terapitindakan = $this->input->post("tindakan");
        $diet = $this->input->post("diet");
        $dietlainnya = $this->input->post("dietlainnya");
        $kebpel = $this->input->post("kebpel");
        $bentuk = $this->input->post("bentuk");
        $user = $this->input->post("user");

        if ($diet == '172') {
            $jenisdiet = $this->input->post("jenisdiet");

            $data = array(
                'kunjungan' => $kunjungan,
                'kasus' => $kasus,
                'rencana_perawatan' => $rencanaperawatan,
                'ruangan' => $ruangan,
                'diagnosis_masuk' => $diagnosis,
                'rencana' => $rencana,
                'hasil_yang_diharapkan' => $hasilyangdiharapkan,
                'perbaikan_keadaan_umum' => isset($keadaanumum) ? json_encode($keadaanumum) : "",
                'perbaikan_lain_lain' => $keumlainlain,
                'prosedur_medis_lain' => $prosedurmedislain,
                'jenis' => $jenis,
                'tanggal' => date('Y-m-d', strtotime($tanggal_rencana)),
                'terapi_tindakan' => $terapitindakan,
                'diet' => $diet,
                'jenis_diet' => $jenisdiet,
                'diet_lainnya' => $dietlainnya,
                'kebutuhan_pelayanan' => isset($kebpel) ? json_encode($kebpel) : "",
                'bentuk' => $bentuk,
                'oleh' => $user,
            );
        } elseif ($diet == '173') {
            $data = array(
                'kunjungan' => $kunjungan,
                'kasus' => $kasus,
                'rencana_perawatan' => $rencanaperawatan,
                'ruangan' => $ruangan,
                'diagnosis_masuk' => $diagnosis,
                'rencana' => $rencana,
                'hasil_yang_diharapkan' => $hasilyangdiharapkan,
                'perbaikan_keadaan_umum' => isset($keadaanumum) ? json_encode($keadaanumum) : "",
                'perbaikan_lain_lain' => $keumlainlain,
                'prosedur_medis_lain' => $prosedurmedislain,
                'jenis' => $jenis,
                'tanggal' => date('Y-m-d', strtotime($tanggal_rencana)),
                'terapi_tindakan' => $terapitindakan,
                'diet' => $diet,
                'kebutuhan_pelayanan' => isset($kebpel) ? json_encode($kebpel) : "",
                'bentuk' => $bentuk,
                'oleh' => $user,
            );
        }

        
        $this->db->trans_begin();
        
        $this->db->insert('medis.tb_permintaan_rawat', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function lihatHistoryPermintaanDirawat()
    {
        $id = $this->input->post('id');
        $permintaandirawat = $this->pengkajianAwalModel->historyDetailPermintaanDirawat($id);
        $listkebutuhanpelayanan = $this->masterModel->referensi(266);
        $listpku = $this->masterModel->listPerbaikanKeadaanUmum();
        $listKasus = $this->masterModel->listKasus();
        $listRencanaPerawatan = $this->masterModel->listRencanaPerawatan();
        $listRencana = $this->masterModel->listRencana();
        $listDiet = $this->masterModel->listDiet();
        $listPerbaikanKeadaanUmum = $this->masterModel->listPerbaikanKeadaanUmum();
        $listJenisDiet = $this->masterModel->listJenisDiet();
        $listRuangan = $this->masterModel->listRuangan();
        $listOdc = $this->masterModel->listOdc();
        $listRawatInap = $this->masterModel->listRawatInap();
        $listRawatKhusus = $this->masterModel->listRawatKhusus();
        $listIntensiveCare = $this->masterModel->listIntensiveCare();

        $data = array(
            'id' => $id,
            'permintaandirawat' => $permintaandirawat,
            'listkebutuhanpelayanan' => $listkebutuhanpelayanan,
            'listpku' => $listpku,
            'listKasus' => $listKasus,
            'listRencanaPerawatan' => $listRencanaPerawatan,
            'listRencana' => $listRencana,
            'listDiet' => $listDiet,
            'listPerbaikanKeadaanUmum' => $listPerbaikanKeadaanUmum,
            'listJenisDiet' => $listJenisDiet,
            'listRuangan' => $listRuangan,
            'listOdc' => $listOdc,
            'listRawatInap' => $listRawatInap,
            'listRawatKhusus' => $listRawatKhusus,
            'listIntensiveCare' => $listIntensiveCare
        );

        $this->load->view('Pengkajian/permintaanDirawat/modalViewEditPermintaanDirawat',$data);
    }

    public function ubahFormPermintaanDirawat()
    {
        $id_permintaandirawat = $this->input->post("id_permintaandirawat");
        $kasus = $this->input->post("radio_emergency_edit");
        $rencanaperawatan = $this->input->post("rencanaperawatan_edit");
        $ruangan = $this->input->post("ruangan_edit");
        $diagnosis = $this->input->post("diagnosis_edit");
        $rencana = $this->input->post("pilih_rencana_edit");
        $hasilyangdiharapkan = $this->input->post("hasilyangdiharapkan_edit");
        $keadaanumum = $this->input->post("keadaanumum_edit");
        $prosedurmedislain = $this->input->post("prosedurmedislain_edit");
        $keumlainlain = $this->input->post("keumlainlain_edit");
        $jenis = $this->input->post("pilih_cito_edit");
        $tanggal_rencana = $this->input->post("tanggalrencana_edit");
        $terapitindakan = $this->input->post("tindakan_edit");
        $diet = $this->input->post("diet_edit");
        $dietlainnya = $this->input->post("dietlainnya_edit");
        $kebpel = $this->input->post("kebpel_edit");
        $bentuk = $this->input->post("bentuk_edit");
        $user = $this->input->post("user_edit");

        if ($diet == '172') {
            $jenisdiet = $this->input->post("jenisdiet_edit");

            $dataUbah = array(
                'kasus' => $kasus,
                'rencana_perawatan' => $rencanaperawatan,
                'ruangan' => $ruangan,
                'diagnosis_masuk' => $diagnosis,
                'rencana' => $rencana,
                'hasil_yang_diharapkan' => $hasilyangdiharapkan,
                'perbaikan_keadaan_umum' => isset($keadaanumum) ? json_encode($keadaanumum) : "",
                'perbaikan_lain_lain' => $keumlainlain,
                'prosedur_medis_lain' => $prosedurmedislain,
                'jenis' => $jenis,
                'tanggal' => date('Y-m-d', strtotime($tanggal_rencana)),
                'terapi_tindakan' => $terapitindakan,
                'diet' => $diet,
                'jenis_diet' => $jenisdiet,
                'diet_lainnya' => $dietlainnya,
                'kebutuhan_pelayanan' => isset($kebpel) ? json_encode($kebpel) : "",
                'bentuk' => $bentuk,
                'oleh' => $user,
            );
        } elseif ($diet == '173') {
            $dataUbah = array(
                'kasus' => $kasus,
                'rencana_perawatan' => $rencanaperawatan,
                'ruangan' => $ruangan,
                'diagnosis_masuk' => $diagnosis,
                'rencana' => $rencana,
                'hasil_yang_diharapkan' => $hasilyangdiharapkan,
                'perbaikan_keadaan_umum' => isset($keadaanumum) ? json_encode($keadaanumum) : "",
                'perbaikan_lain_lain' => $keumlainlain,
                'prosedur_medis_lain' => $prosedurmedislain,
                'jenis' => $jenis,
                'tanggal' => date('Y-m-d', strtotime($tanggal_rencana)),
                'terapi_tindakan' => $terapitindakan,
                'diet' => $diet,
                'kebutuhan_pelayanan' => isset($kebpel) ? json_encode($kebpel) : "",
                'bentuk' => $bentuk,
                'oleh' => $user,
            );
        }

        
        $this->db->trans_begin();
        
        $this->db->where('medis.tb_permintaan_rawat.id', $id_permintaandirawat);
        $this->db->update('medis.tb_permintaan_rawat', $dataUbah);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }


}
?>