<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MappingRakObat extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('Farmasi/MappingRakObatModel','masterModel','EresepModel'));
  }

  public function index()
  {
    $data = array(
      'title' => 'Mapping Rak Obat',
      'isi'   => 'Farmasi/mappingObat',
    );
    $this->load->view('layout/wrapper',$data);
  }

  public function tblMappingFarmasi()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $historyMasterFarmasi = $this->MappingRakObatModel->historyMasterFarmasi();

    $no = 1;
    $data = array();
    foreach ($historyMasterFarmasi->result() as $hmf) {
      $data[] = array(
        $no,
        $hmf->NAMADEPO,
        $hmf->RUANGANPENYIMPANAN,
        $hmf->nama_rak,
        "<a href='#' data-toggle='modal' data-target='#modalMappingObat' data-id='" . $hmf->id . "' class='btn btn-sm btn-primary btn-block' data-backdrop='static' data-keyboard='false'><i class='fa fa-check'></i> Pilih</a>",
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $historyMasterFarmasi->num_rows(),
      "recordsFiltered" => $historyMasterFarmasi->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalMappingObat()
  {
    $id = $this->input->post('id');

    $getMasterFarmasi = $this->MappingRakObatModel->getMasterFarmasi($id);

    $data = array(
      'getMasterFarmasi' => $getMasterFarmasi,
    );
    $this->load->view('Farmasi/modalMappingObat', $data);
  }

  public function obat()
  {
    $tf = $this->uri->segment(4);
    $result = $this->MappingRakObatModel->obat($tf);
    $json = array();
    foreach ($result as $row) {
      $json[] = array('id' => $row['ID_BARANG_RUANGAN'], 'text' => $row['NAMA']);
    }
    echo json_encode($json);
  }

  public function tblHistoryMappingFarmasi()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $id=$this->input->post('idRak');

    $historyPerak = $this->MappingRakObatModel->historyPerak($id);

    $no = 1;
    $data = array();
    foreach ($historyPerak->result() as $hp) {
      $data[] = array(
        $no,
        $hp->NAMA_OBAT,
        $hp->SLOT_PENYIMPANAN,
        $hp->USER,
        date("d-m-Y H:i:s",strtotime($hp->TANGGAL_INPUT)),
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $historyPerak->num_rows(),
      "recordsFiltered" => $historyPerak->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function simpanModalRak()
  {
    $post = $this->input->post();

    $data= array(
      'id_barang_ruangan' => $post["obat"],
      'id_rak_gudang'     => $post["id_rak_gudang"],
      'slot'              => $post["slotRak"],
      'oleh'              => $this->session->userdata("id"),
    );

    $this->MappingRakObatModel->simpanModalRak($data);
  }

   public function tblHistoryInsertMappingFarmasi()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $tblhistoryPerakk = $this->MappingRakObatModel->tblhistoryPerak();

    $no = 1;
    $data = array();
    foreach ($tblhistoryPerakk->result() as $thp) {
      $data[] = array(
        $no,
        $thp->DEPO,
        $thp->LOKASI_GUDANG,
        $thp->NAMA_RAK,
        $thp->NAMA_OBAT,
        'Slot Ke-'.$thp->SLOT_PENYIMPANAN,
        $thp->KATEGORI,
        $thp->USER,
        date("d-m-Y H:i:s",strtotime($thp->TANGGAL_INPUT)),
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $tblhistoryPerakk->num_rows(),
      "recordsFiltered" => $tblhistoryPerakk->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

}

/* End of file MappingRakObat.php */
/* Location: ./application/controllers/Farmasi/MappingRakObat.php */
