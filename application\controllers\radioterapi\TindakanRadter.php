<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TindakanRadter extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel','radioterapi/tindakanRadterModel'));
  }

  public function index()
  {
    $nomr  = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $pasientindkradter   = $this->tindakanRadterModel->pasientindkradter();

    $data = array(
      'title' => 'Data pasien tindakan radioterapi',
      'isi'   => 'Pengkajian/radioTerapi/pasientindakradter',
      'nomr'  => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'pasientindkradter'   => $pasientindkradter,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function editform()
  {
    $nomr  = $this->uri->segment(3);
    $nopen = $this->uri->segment(4);
    $nokun = $this->uri->segment(5);
    $pasientindkradter   = $this->tindakanRadterModel->pasientindkradter();

    $data = array(
      'title' => 'Edit pasien tindakan radioterapi',
      'isi'   => 'Pengkajian/radioTerapi/form_verifmedis',
      'nomr'  => $nomr,
      'nopen' => $nopen,
      'nokun' => $nokun,
      'pasientindkradter'   => $pasientindkradter,
    );

    $this->load->view('layout/wrapper', $data);
  }

  public function listPasien()
  {

    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));

    $listPasien = $this->tindakanRadterModel->pasientindkradter();

    //echo "<pre>";print_r($listPasien);exit();
    $data  = array();
    $no    =1;
    foreach($listPasien->result() as $lp) {

      if($lp->STATUS == 1){

        $ok = "<p class='text-success'>Sudah Diverif</p>";
      }else{

        $ok = "<p class='text-warning'>Belum diverif</p>";
      }


      $data[] = array(
        $no,
        date("d-m-Y",strtotime($lp->TGL_KUNJUNGAN)),
        $lp->NORM,
        $lp->NAMAPASIEN,
        $lp->DOKTERDPJP,
        $ok,
        '<a href="#pilihPasienRadter" class="btn btn-sm btn-block btn-primary" data-toggle="modal" data-id="'.$lp->ID.'"><i class="fas fa-edit"></i> Pilih</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listPasien->num_rows(),
      "recordsFiltered" => $listPasien->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function pilihpasien()
  {
    $id = $this->input->post('id');

    //echo "<pre>";print_r($id);exit();

    $isiModal = $this->ambilHistoryRater($id);
    $skriningNyeri = $this->masterModel->referensi(7);
    $RisikoJatuhTR = $this->masterModel->referensi(259);
    $ps2 = $this->masterModel->referensi(262);
    $terapiRater = $this->masterModel->referensi(242);
    $skalaNyeriNRS = $this->masterModel->referensi(114);
    $skalaNyeriWBR = $this->masterModel->referensi(115);
    $skalaNyeriFLACC = $this->masterModel->referensi(123);
    $skalaNyeriBPS = $this->masterModel->referensi(133);
    $efeksampingNRS = $this->masterModel->referensi(118);
    $pengkajianNyeriProvocative = $this->masterModel->referensi(8);
    $pengkajianNyeriQuality = $this->masterModel->referensi(9);
    $pengkajianNyeriTime = $this->masterModel->referensi(12);
    $Ecog = $this->masterModel->referensi(239);
    $Karnofsky = $this->masterModel->referensi(240);
    $Lansky = $this->masterModel->referensi(267);
    $kesadaran = $this->masterModel->referensi(5);
    // print_r($ruanganSimrsKd);exit();
    $data = array(
      'isiModal' => $isiModal,
      'skriningNyeri' => $skriningNyeri,
      'RisikoJatuhTR' => $RisikoJatuhTR,
      'ps2' => $ps2,
      'terapiRater' => $terapiRater,
      'skalaNyeriNRS' => $skalaNyeriNRS,
      'skalaNyeriWBR' => $skalaNyeriWBR,
      'skalaNyeriFLACC' => $skalaNyeriFLACC,
      'skalaNyeriBPS' => $skalaNyeriBPS,
      'efeksampingNRS' => $efeksampingNRS,
      'pengkajianNyeriProvocative' => $pengkajianNyeriProvocative,
      'pengkajianNyeriQuality' => $pengkajianNyeriQuality,
      'pengkajianNyeriTime' => $pengkajianNyeriTime,
      'dataECOG' => $Ecog,
      'dataKarnofsky' => $Karnofsky,
      'datalansky' => $Lansky,
      'kesadaran' => $kesadaran,

    );

    $this->load->view('Pengkajian/radioTerapi/form_verifmedis',$data);

  }

  public function ambilHistoryRater($id)
  {
    $detailHistory = $this->pengkajianAwalModel->modalHistoryRater($id);
    $data = array();
    foreach ($detailHistory as $main) {
      $isi_array = array();
      $isi_array['id'] = $main['id'];
      $isi_array['kunjungan'] = $main['kunjungan'];
      $isi_array['tanggal'] = $main['tanggal'];
      $isi_array['waktu'] = $main['waktu'];
      $isi_array['keluhan'] = $main['keluhan'];
      $isi_array['tekanan_darah'] = $main['tekanan_darah'];
      $isi_array['per_tekanan_darah'] = $main['per_tekanan_darah'];
      $isi_array['pernapasan'] = $main['pernapasan'];
      $isi_array['nadi'] = $main['nadi'];
      $isi_array['suhu'] = $main['suhu'];
      $isi_array['risiko_jatuh_tr'] = $main['risiko_jatuh_tr'];
      $isi_array['nyeri'] = $main['nyeri'];
      $isi_array['skor_nyeri'] = $main['skor_nyeri'];
      $isi_array['farmakologi'] = $main['farmakologi'];
      $isi_array['non_farmakologi'] = $main['non_farmakologi'];
      $isi_array['efek_samping'] = $main['efek_samping'];
      $isi_array['keterangan_efek_samping'] = $main['keterangan_efek_samping'];
      $isi_array['provocative'] = $main['provocative'];
      $isi_array['quality'] = $main['quality'];
      $isi_array['sebutkan_quality'] = $main['sebutkan_quality'];
      $isi_array['isi_regio'] = $main['isi_regio'];
      $isi_array['severity'] = $main['severity'];
      $isi_array['time'] = $main['time'];
      $isi_array['isi_time'] = $main['isi_time'];
      $isi_array['id_performance'] = $main['id_performance'];
      $isi_array['id_skor_performance'] = $main['id_skor_performance'];
      $isi_array['id_terapi'] = $main['id_terapi'];
      $isi_array['isi_terapi_ditunda'] = $main['isi_terapi_ditunda'];
      $isi_array['kesadaran'] = $main['kesadaran'];
      $data[] = $isi_array;
    }
    return $data;
  }

  public function simpan_verifdokter($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        //$nokun = $this->uri->segment(5);
        $oleh = $this->session->userdata("id");
        $post = $this->input->post();
        $nokun = $post['kunjungan'];
        $datanokun = $this->tindakanRadterModel->getNokunVerif($nokun);
       //echo "<pre>";print_r($datanokun);exit();
        $dataterapi = array(
            //'risiko_jatuh_tr' => isset($post['risiko_jatuh_tr']) ? $post['risiko_jatuh_tr'] : "",
          'kunjungan' => isset($post['kunjungan']) ? $post['kunjungan'] : "",
          // 'performance_status' => isset($post['performance_status']) ? $post['performance_status'] : "",
          // 'skor_performance' => isset($post['skor_performance']) ? $post['skor_performance'] : "",
          'terapi' => isset($post['terapi']) ? $post['terapi'] : "",
          'terapi_ditunda' => isset($post['terapi_ditunda']) ? $post['terapi_ditunda'] : "",
          'oleh' => $oleh,
        );
        //echo "<pre>";print_r($dataterapi);exit();
        $this->db->trans_begin();

        if (!empty($datanokun)) {
          if($this->db->replace('medis.tb_tindakan_radioterapi_medis',$dataterapi)){
            $this->db->where(array('kunjungan' => $post['kunjungan']));
          }
        }else {
          $this->db->insert('medis.tb_tindakan_radioterapi_medis', $dataterapi);
        }
        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }
}