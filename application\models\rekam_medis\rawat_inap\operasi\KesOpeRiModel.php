<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class KesOpeRiModel extends MY_Model {

  public function simpanKesOpe($data)
  {
      $this->db->insert('keperawatan.tb_keselamatan_tindakan_operasi', $data);
      return $this->db->insert_id();
  }

  public function historyKesOpeRi($norm)
    {
        $query = $this->db->query("SELECT kp.id ID_KTO, kp.nokun NOKUN, kp.created_at TANGGAL_KTO
            , master.getNamaLengkapPegawai(peng.NIP) USER
            , master.getNamaLengkapPegawai(dpjp.NIP) DPJP
            , rk.DESKRIPSI RUANGAN_KUNJUNGAN
            , p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN
            , p.NOMOR NOPEN

            FROM keperawatan.tb_keselamatan_tindakan_operasi kp

            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
            LEFT JOIN master.diagnosa_masuk dm ON dm.ID = p.DIAGNOSA_MASUK
            LEFT JOIN master.dokter dpjp ON dpjp.ID = tp.DOKTER
            LEFT JOIN master.ruangan rk ON rk.ID = pk.RUANGAN
            LEFT JOIN aplikasi.pengguna peng ON peng.ID = kp.oleh

            WHERE kp.status=1 AND p.NORM='$norm'

            ORDER BY kp.created_at DESC");
        return $query->result_array();
    }

    public function getKesOpeRi($id)
    {
        $query = $this->db->query("SELECT kto.id ID_KTO, kto.nokun NOKUN, kto.diagnosis DIAGNOSIS, kto.tindakan_operasi TINDAKAN_OPERASI
            , kto.dokter_operator DOKTER_OPERATOR
            , kto.waktu_dikirim WAKTU_DIKIRIM
            , kto.asal_ruangan ASAL_RUANGAN
            , kto.identitas_pasien_sesuai IDENTITAS_PASIEN_SESUAI
            , kto.rencana_tindakan RENCANA_TINDAKAN
            , kto.rencana_tindakan_sesuai RENCANA_TINDAKAN_SESUAI
            , kto.penandaan_lokasi PENANDAAN_LOKASI
            , kto.penandaan_loaksi_sesuai PENANDAAN_LOKASI_SESUAI
            , kto.tindakan_bedah TINDAKAN_BEDAH
            , kto.tindakan_anestesi TINDAKAN_ANESTESI
            , kto.pemeriksaan_foto PEMERIKSAAN_FOTO
            , kto.pemeriksaan_foto_sesuai PEMERIKSAAN_FOTO_SESUAI
            , kto.pemeriksaan_ct_scan PEMERIKSAAN_CT_SCAN
            , kto.pemeriksaan_ct_scan_sesuai PEMERIKSAAN_CT_SCAN_SESUAI
            , kto.pemeriksaan_mri PEMERIKSAAN_MRI
            , kto.pemeriksaan_mri_sesuai PEMERIKSAAN_MRI_SESUAI
            , kto.pemeriksaan_lainnya PEMERIKSAAN_LAINNYA
            , kto.pemeriksaan_lainnya_sesuai PEMERIKSAAN_LAINNYA_SESUAI
            , kto.persediaan_darah_tersedia PERSEDIAAN_DARAH_TERSEDIA
            , kto.persediaan_darah_gol PERSEDIAAN_DARAH_GOL
            , kto.persediaan_darah_prc PERSEDIAAN_DARAH_PRC
            , kto.persediaan_darah_ffp PERSEDIAAN_DARAH_FFP
            , kto.persediaan_darah_lain PERSEDIAAN_DARAH_LAIN
            , kto.persediaan_darah_sit PERSEDIAAN_DARAH_SIT
            , kto.rencana_pemasangan_alat RENCANA_PEMASANGAN_ALAT
            , kto.rencana_pemasangan_alat_desk RENCANA_PEMASANGAN_ALAT_DESK
            , tb.jenis JENIS_CEK
            , tb.tb TINGGI_BADAN
            , tb.bb BERAT_BADAN
            , tv.td_sistolik TD_SISTOLIK
            , tv.td_diastolik TD_DIASTOLIK
            , tv.nadi NADI
            , tv.pernapasan PERNAPASAN
            , tv.suhu SUHU
            FROM keperawatan.tb_keselamatan_tindakan_operasi kto
            LEFT JOIN db_pasien.tb_tb_bb tb ON kto.id = tb.ref AND tb.data_source='27'
            LEFT JOIN db_pasien.tb_tanda_vital tv ON kto.id = tv.ref AND tv.data_source='27'
            WHERE kto.id='$id' AND kto.`status`='1'");
        return $query->row_array();
    }

    public function cekDataAkhir($nokun)
    {
        $query = $this->db->query("
            SELECT COUNT(kto.nokun) JUMLAH
            FROM keperawatan.tb_keselamatan_tindakan_operasi kto
            LEFT JOIN db_pasien.tb_tb_bb tb ON kto.id = tb.ref AND tb.data_source='27'
            LEFT JOIN db_pasien.tb_tanda_vital tv ON kto.id = tv.ref AND tv.data_source='27'
            WHERE kto.nokun='$nokun'
            ORDER BY kto.created_at DESC
            ");
        return $query->row_array();
    }

    public function getInOUtKesOpeRi($id)
    {
        $query = $this->db->query("SELECT kto.id ID_KTO, kto.nokun NOKUN, kto.diagnosis DIAGNOSIS, kto.tindakan_operasi TINDAKAN_OPERASI
            , kto.dokter_operator DOKTER_OPERATOR, kto.waktu_dikirim WAKTU_DIKIRIM
            , kto.asal_ruangan ASAL_RUANGAN, kto.identitas_pasien_sesuai IDENTITAS_PASIEN_SESUAI
            , kto.rencana_tindakan RENCANA_TINDAKAN, kto.rencana_tindakan_sesuai RENCANA_TINDAKAN_SESUAI
            , kto.penandaan_lokasi PENANDAAN_LOKASI, kto.penandaan_loaksi_sesuai PENANDAAN_LOKASI_SESUAI
            , kto.tindakan_bedah TINDAKAN_BEDAH, kto.tindakan_anestesi TINDAKAN_ANESTESI
            , kto.pemeriksaan_foto PEMERIKSAAN_FOTO, kto.pemeriksaan_foto_sesuai PEMERIKSAAN_FOTO_SESUAI
            , kto.pemeriksaan_ct_scan PEMERIKSAAN_CT_SCAN, kto.pemeriksaan_ct_scan_sesuai PEMERIKSAAN_CT_SCAN_SESUAI
            , kto.pemeriksaan_mri PEMERIKSAAN_MRI, kto.pemeriksaan_mri_sesuai PEMERIKSAAN_MRI_SESUAI
            , kto.pemeriksaan_lainnya PEMERIKSAAN_LAINNYA, kto.pemeriksaan_lainnya_sesuai PEMERIKSAAN_LAINNYA_SESUAI
            , kto.persediaan_darah_tersedia PERSEDIAAN_DARAH_TERSEDIA, kto.persediaan_darah_gol PERSEDIAAN_DARAH_GOL
            , kto.persediaan_darah_prc PERSEDIAAN_DARAH_PRC, kto.persediaan_darah_ffp PERSEDIAAN_DARAH_FFP
            , kto.persediaan_darah_lain PERSEDIAAN_DARAH_LAIN, kto.persediaan_darah_sit PERSEDIAAN_DARAH_SIT
            , kto.rencana_pemasangan_alat RENCANA_PEMASANGAN_ALAT
            , kto.rencana_pemasangan_alat_desk RENCANA_PEMASANGAN_ALAT_DESK
            , kto.oleh OLEH_TINDAKAN

            , ksi.tgl_pukul_signin TANGGAL_PUKUL_SIGNIN, ksi.identitas_pasien_signin IDENTITAS_PASIEN_SIGNIN
            , ksi.identitas_pasien_desk_signin IDENTITAS_PASIEN_DESK_SIGNIN
            , ksi.tindakan_operasi_signin TINDAKAN_OPERASI_SIGNIN, ksi.tindakan_operasi_desk_signin TINDAKAN_OPERASI_DESK_SIGNIN
            , ksi.sisi_operasi_signin SISI_OPERASI_SIGNIN, ksi.sisi_operasi_desk_signin SISI_OPERASI_DESK_SIGNIN
            , ksi.surat_izin_signin SURAT_IZIN_SIGNIN, ksi.surat_izin_desk_signin SURAT_IZIN_DESK_SIGN
            , ksi.sudah_beri_signin SUDAH_BERI_SIGNIN, ksi.mesin_obat_signin MESIN_OBAT_SIGNIN
            , ksi.sudah_pasang_signin SUDAH_PASANG_SIGNIN, ksi.punya_alergi_signin PUNYA_ALERGI_SIGNIN
            , ksi.gangguan_nafas_signin GANGGUAN_NAFAS_SIGNIN, ksi.resiko_perdarahan_signin RESIKO_PERDARAHAN_SIGNIN
            , ksi.dokter_anestesi DOKTER_ANESTESI_SIGNIN, ksi.penata_anestesi PENATA_ANESTESI_SIGNIN
            , ksi.oleh OLEH_SIGNIN

            , kt.tgl_pukul_timeout TANGGAL_PUKUL_TIMEOUT, kt.identitas_pasien_timeout IDENTITAS_PASIEN_TIMEOUT
            , kt.identitas_pasien_desk_timeout IDENTITAS_PASIEN_DESK_TIMEOUT, kt.tindakan_operasi_timeout TINDAKAN_OPERASI_TIMEOUT
            , kt.tindakan_operasi_desk_timeout TINDAKAN_OPERASI_DESK_TIMEOUT, kt.sisi_operasi_timeout SISI_OPERASI_TIMEOUT
            , kt.sebutkan_nama_timeout SEBUTKAN_NAMA_TIMEOUT
            , kt.sisi_operasi_desk_timeout SISI_OPERASI_DESK_TIMEOUT, kt.surat_izin_timeout SURAT_IZIN_TIMEOUT
            , kt.surat_izin_desk_timeout SURAT_IZIN_DESK_TIMEOUT, kt.berikan_antibiotik_timeout BERIKAN_ANTIBIOTIK_TIMEOUT
            , kt.jam_pemberian JAM_PEMBERIAN_TIMEOUT, kt.antisipasi_dokter_bedah ANTISIPASI_DOKTER_BEDAH_TIMEOUT
            , kt.antisipasi_dokter_anestesi ANTISIPASI_DOKTER_ANESTESI_TIMEOUT, kt.persiapan_instrumen PERSIAPAN_INSTRUMEN_TIMEOUT
            , kt.persiapan_masalah PERSIAPAN_MASALAH_TIMEOUT, kt.persiapan_sebutkan PERSIAPAN_SEBUTKAN_TIMEOUT
            , kt.persiapan_implan PERSIAPAN_IMPLAN_TIMEOUT, kt.persiapan_foto_radiologi PERSIAPAN_FOTO_RADIOLOGI_TIMEOUT
            , kt.perawat_sirkuler PERAWAT_SIRKULER_TIMEOUT, kt.oleh OLEH_TIMEOUT

            , kso.tgl_pukul_signout TANGGAL_PUKUL_SIGNOUT, kso.konfirmasi_nama_tindakan KONFIRMASI_NAMA_TINDAKAN_SIGNOUT
            , kso.konfirmasi_kelengkapan KONFIRMASI_KELENGKAPAN_SIGNOUT, kso.konfirmasi_spesimen KONFIRMASI_SPESIMEN_SIGNOUT
            , kso.konfirmasi_spesimen_desk KONFIRMASI_SPESIMEN_DESK_SIGNOUT, kso.konfirmasi_masalah KONFIRMASI_MASALAH_SIGNOUT
            , kso.konfirmasi_masalah_desk KONFIRMASI_MASALAH_DESK_SIGNOUT, kso.adakah_masalah ADAKAH_MASALAH_SIGNOUT
            , kso.adakah_masalah_desk ADAKAH_MASALAH_DESK_SIGNOUT, kso.dokter_anestesi DOKTER_ANESTESI_SIGNOUT
            , kso.dokter_operator DOKTER_OPERATOR_SIGNOUT, kso.perawat_sirkuler PERAWAT_SIRKULER_SIGNOUT
            , kso.oleh OLEH_SIGNOUT
            FROM keperawatan.tb_keselamatan_tindakan_operasi kto
            LEFT JOIN keperawatan.tb_kesope_signin ksi ON kto.id = ksi.idkto
            LEFT JOIN keperawatan.tb_kesope_timeout kt ON kto.id = kt.idkto
            LEFT JOIN keperawatan.tb_kesope_signout kso ON kto.id = kso.idkto
            WHERE kto.id='$id' AND kto.`status`='1'");
            return $query->row_array();
    }

}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
