<?php
defined('BASEPATH') or exit('No direct script access allowed');

class RadiologiHistoryModel extends MY_Model
{
    protected $_table_name = '';
    protected $_primary_key = '';
    protected $_order_by = '';
    protected $_order_by_type = '';

    function __construct()
    {
        parent::__construct();
    }

    function table_query()
    {
      $norm = $this->input->post('norm');
      $this->db->select("orla.NOMOR NOMOR_LAB, orla.TANGGAL TANGGAL_ORDER
      , ras.DESKRIPSI RUANG_ASAL
      , rut.ID ID_TUJUAN, rut.DESKRIPSI RUANG_TUJUAN, orla.TANGGAL_RENCANA, master.getNamaLengkapPegawai(dok.NIP) DOKTER_ASAL
      , orla.`STATUS` STATUS_ORDER
      , IF(orla.`STATUS`=1, 'Order Terkirim', IF(orla.`STATUS`=2, 'Diterima/Proses', CONCAT(' Order Dibatalkan Oleh: ',master.getNamaLengkapPegawai(peng.NIP)))) STATUS_ORDER
      , master.getNamaLengkapPegawai(peng.NIP) USER
      , (SELECT
          GROUP_CONCAT(t.NAMA SEPARATOR ', ')
          FROM layanan.tindakan_medis tm
          , master.tindakan t
          , pendaftaran.kunjungan pku
          WHERE  tm.KUNJUNGAN=pkut.NOMOR
          AND pku.`STATUS`!=0
          AND tm.TINDAKAN=t.ID
          AND pku.NOMOR=pkut.NOMOR
          ORDER BY tm.ID) TINDAKAN");
      $this->db->from("layanan.order_rad orla");
      $this->db->join('pendaftaran.kunjungan pk','pk.NOMOR = orla.KUNJUNGAN','LEFT');
      $this->db->join('pendaftaran.pendaftaran p','p.NOMOR = pk.NOPEN','LEFT');
      $this->db->join('master.ruangan ras','ras.ID = pk.RUANGAN','LEFT');
      $this->db->join('master.dokter dok','dok.ID = orla.DOKTER_ASAL','LEFT');
      $this->db->join('pendaftaran.kunjungan pkut','pkut.REF = orla.NOMOR','LEFT');
      $this->db->join('master.ruangan rut','rut.ID = orla.TUJUAN','LEFT');
      $this->db->join('aplikasi.pengguna peng','peng.ID = orla.OLEH','LEFT');
      $this->db->where('p.NORM', $norm);
      $this->db->order_by('orla.TANGGAL DESC');

      if (isset($_POST['status'])) {
        $this->db->where('orla.STATUS', $this->input->post('status'));
      }
    }

    function get_table($single = TRUE)
    {
        $this->table_query();
        $query = $this->db->get();
        if ($single == TRUE) {
            $method = 'row';
        } else {
            $method = 'result';
        }
        return $query->$method();
    }

    function get_count()
    {
        $this->table_query();
        return $this->db->count_all_results();
    }
}
