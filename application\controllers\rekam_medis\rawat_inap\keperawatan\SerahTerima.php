<?php
defined('BASEPATH') or exit('No direct script access allowed');

class serahTerima extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/keperawatan/serahTerimaModel'));
    }

    public function index() {

        $data = array(
            'pasien' => $this->pengkajianAwalModel->getNomr($this->uri->segment(4)),
            'listShiftIGD' => $this->masterModel->listShiftIGD(),
            'listserahTerimaDataPasienIGD' => $this->masterModel->listserahTerimaDataPasienIGD(),
            'listKesadaranIGD' => $this->masterModel->referensi(5),
            'listResikoJatuhIGD' => $this->masterModel->referensi(660),
            'listAlatTerpasangIGD' => $this->masterModel->listAlatTerpasangIGD(),
            'listPerawat' => $this->masterModel->listPerawat(),
            'listOksigenIGD' => $this->masterModel->listOksigenIGD(),
            'listMasalahAsuhanIGD' => $this->masterModel->listMasalahAsuhanIGD(),
            'formAsuhanKeperawatan' => $this->masterModel->referensi(148),
          );
      

        // echo $this->uri->segment(4);
        $this->load->view('rekam_medis/rawat_inap/keperawatan/serahTerima/index',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->serahTerimaModel->rules;
                $this->form_validation->set_rules($rules);
                // if($this->input->post('skrining_nyeri') != 17){
                //     $this->form_validation->set_rules($this->serahTerimaModel->rules_nyeri);
                //     if($this->input->post('efek_samping') != 337){
                //         $this->form_validation->set_rules($this->serahTerimaModel->rules_efek_samping);
                //     }
                // }

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $this->db->trans_begin();

                    $dataSerahTerima = array(
                        'nokun' => $post['nokun'],
                        'shiftKe' => $post['shiftKe'],
                        'datapasiensrigd' => isset($post['datapasiensrigd']) ? json_encode($post['datapasiensrigd']) : null,
                        'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : '',
                        'skornyeri' => isset($post['skornyeri']) ? $post['skornyeri'] : '',
                        'skorewspws' => isset($post['skorewspws']) ? $post['skorewspws'] : '',
                        'resikojatuh' => isset($post['resikojatuh']) ? $post['resikojatuh'] : '',
                        'tandaVital' => isset($post['tandaVital']) ? $post['tandaVital'] : '',
                        'balance' => isset($post['balance']) ? $post['balance'] : '',
                        'alatterpasang' => json_encode($post['alatterpasang']),
                        'oksigenIGD' => isset($post['oksigenIGD']) ? $post['oksigenIGD'] : '',
                        'oksigenLainAlatTerpasang' => isset($post['oksigenLainAlatTerpasang']) ? $post['oksigenLainAlatTerpasang'] : '',
                        'lainAlatTerpasang' => isset($post['lainAlatTerpasang']) ? $post['lainAlatTerpasang'] : '',
                        'pemeriksaanPenunjang' => $post['pemeriksaanPenunjang'],
                        'tindakanObat' => $post['tindakanObat'],
                        // 'masalahasuhan' => $post['masalahasuhan'],
                        // 'masalahAsukanLainnya' => isset($post['lainMasalahAsuhan']) ? $post['lainMasalahAsuhan'] : '',
                        'perencanaan' => $post['perencanaan'],
                        'evaluasiMasalah' => $post['evaluasiMasalah'],
                        'yangMenerima' => $post['yangMenerima'],
                        'tanggal' => $post['tanggal'],
                        'oleh' => $this->session->userdata('id'),
                        'status' => 1,
                    );                  

                    if(!empty($post['id'])){
                        $this->serahTerimaModel->update($dataSerahTerima, array('ID' => $post['id']));
                        
                        $this->db->delete('keperawatan.tb_serah_terima_masalah_asuhan', array('id_serah_terima' => $post['id']));
                        $dataMasalahAsuhan = array();
                        $index = 0;
                        if (isset($post['masalahasuhan'])) {
                            foreach ($post['masalahasuhan'] as $input) {
                                if ($post['masalahasuhan'][$index] != "") {
                                    array_push(
                                        $dataMasalahAsuhan,
                                        array(
                                            'id_asuhan' => $post['masalahasuhan'][$index],
                                            'id_serah_terima' => $post['id'],
                                        )
                                    );
                                }
                                $index++;
                            }
                            $this->db->insert_batch('keperawatan.tb_serah_terima_masalah_asuhan', $dataMasalahAsuhan);
                        }
    				}else{
                        $id = $this->serahTerimaModel->insert($dataSerahTerima);
                        $dataMasalahAsuhan = array();
                        $index = 0;
                        if (isset($post['masalahasuhan'])) {
                            foreach ($post['masalahasuhan'] as $input) {
                                if ($post['masalahasuhan'][$index] != "") {
                                    array_push(
                                        $dataMasalahAsuhan,
                                        array(
                                            'id_asuhan' => $post['masalahasuhan'][$index],
                                            'id_serah_terima' => $id,
                                        )
                                    );
                                }
                                $index++;
                            }
                            $this->db->insert_batch('keperawatan.tb_serah_terima_masalah_asuhan', $dataMasalahAsuhan);
                        }
    				}

                    if ($this->db->trans_status() === false) {
                        $this->db->trans_rollback();
                        $result = array('status' => 'failed');
                    } else {
                        $this->db->trans_commit();
                        $result = array('status' => 'success');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataSerahTerima = $this->serahTerimaModel->get($post['id'], true);
                
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataSerahTerima
                ));
            }else if($param == 'ambilNyeri'){
    			$post = $this->input->post(NULL,TRUE);
                $dataSerahTerima = $this->serahTerimaModel->get_table(true);
                
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataSerahTerima
                ));
            }else if($param == 'ambilMasalahAsuhan'){
    			$post = $this->input->post(NULL,TRUE);
                $this->db->select('id_asuhan')
                        ->from('keperawatan.tb_serah_terima_masalah_asuhan')
                        ->where('id_serah_terima',$post['id']);
                $query = $this->db->get(); 

                $dataMasalahAsuhan = $query->result();
                
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $dataMasalahAsuhan
                ));
            }else if($param == 'count'){
                $result = $this->serahTerimaModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->serahTerimaModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_serah_terima" data-id="'.$row -> ID.'"><i class="fa fa-eye"></i> Lihat</a><!--<a class="btn btn-warning btn-block btn-sm" href="/reports/simrskd/validasimalnutrisi/validasimalnutrisi.php?format=pdf&nopen='.$row -> NOKUN.'" target="_blank"><i class="fa fa-print"></i> Cetak</a>-->';
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;
            $sub_array[] = $row -> MENERIMA;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->serahTerimaModel->total_count(),
            "recordsFiltered"   => $this->serahTerimaModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}