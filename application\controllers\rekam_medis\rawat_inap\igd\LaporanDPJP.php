<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanDPJP extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/igd/LaporanDPJPModel'));
    }

    public function index() {
        $nokun = $this->uri->segment(2);
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);
        $tableMasalah = $this->LaporanDPJPModel->tableMasalah($nokun);

        $data = array(
            'listDrUmum' => $this->masterModel->listDrUmum(),
            'pasien' => $pasien,
            'tableMasalah' => $tableMasalah
        );

        $this->load->view('rekam_medis/rawat_inap/igd/laporanDPJP',$data);
    }

    public function respon_masalah() {
        $nokun = $this->uri->segment(2);
        $pasien = $this->pengkajianAwalModel->getNomr($nokun);

        $data = array(
            'id_masalah' => $this->input->post('id_masalah'),
            'masalah' => $this->input->post('masalah'),
            'pasien' => $pasien,
        );

        $this->load->view('rekam_medis/rawat_inap/igd/responLaporanDPJP', $data);
    }

    public function detail_masalah() {
        $id_masalah = $this->input->post('id_masalah');
        $getDetailLaporanDPJP = $this->LaporanDPJPModel->getDetailLaporanDPJP($id_masalah);
        $pasien = $this->pengkajianAwalModel->getNomr($getDetailLaporanDPJP['nokun']);


        $data = array(
            'id_masalah' => $id_masalah,
            'listDrUmum' => $this->masterModel->listDrUmum(),
            'history' => $getDetailLaporanDPJP,
            'pasien' => $pasien
        );

        $this->load->view('rekam_medis/rawat_inap/igd/viewEditLaporanDPJP', $data);
    }

    public function action_masalah($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah'){
                $post = $this->input->post();
                
                $data = array(
                    'nokun' => $post['nokun'],
                    'tanggal' => date('Y-m-d', strtotime($post['tanggal'])),
                    'jam' => $post['jam'],
                    'masalah' => $post['masalah'],
                    'dokter' => $post['dokter'],
                    'oleh_masalah' => $this->session->userdata("id")
                );

                $this->db->trans_begin();

                $this->db->insert('keperawatan.tb_laporan_dpjp', $data);

                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }else if($param == 'ubah'){
                $post = $this->input->post();
                
                $dataUbah = array(
                    'tanggal' => date('Y-m-d', strtotime($post['tanggal'])),
                    'jam' => $post['jam'],
                    'masalah' => $post['masalah'],
                    'dokter' => $post['dokter'],
                    'instruksi_dpjp' => $post['instruksi_dpjp'],
                    'tanggal_respon' => date('Y-m-d', strtotime($post['tanggal_respon'])),
                    'jam_respon' => $post['jam_respon']
                );

                $this->db->trans_begin();
                $this->db->where('keperawatan.tb_laporan_dpjp.id', $post['id_masalah']);
                $this->db->update('keperawatan.tb_laporan_dpjp', $dataUbah);
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
        }
    }

    public function action_respon($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah'){
                $post = $this->input->post();
                
                $data = array(
                    'instruksi_dpjp' => $post['instruksi_dpjp'],
                    'tanggal_respon' => date('Y-m-d', strtotime($post['tanggal_respon'])),
                    'jam_respon' => date('H:i', strtotime($post['jam_respon'])),
                    'oleh_respon' => $this->session->userdata("id"),
                    'status' => 2
                );

                $this->db->trans_begin();
                $this->db->where('keperawatan.tb_laporan_dpjp.id', $post['id_masalah']);
                $this->db->update('keperawatan.tb_laporan_dpjp', $data);
                
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = array('status' => 'failed');
                } else {
                    $this->db->trans_commit();
                    $result = array('status' => 'success');
                }
                echo json_encode($result);
            }
        }
    }
}