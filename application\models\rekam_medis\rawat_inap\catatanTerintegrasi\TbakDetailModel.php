<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TbakDetailModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_tbak_detail';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'ASC';

  function __construct()
  {
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('td.id, master.getNamaLengkapPegawai(md.NIP) dokter_tbak,td.instruksi_tbak');
    $this->db->from('keperawatan.tb_tbak_detail td');
    $this->db->join('master.dokter md', 'td.dokter_tbak = md.id', 'left');
    $this->db->where('td.status', 1);
    $this->db->order_by('td.id', 'ASC');
    if ($this->input->post('id_cppt')) {
      $this->db->where('td.id_cppt', $this->input->post('id_cppt'));
    }

    if ($this->input->post('id')) {
      $this->db->where('td.id', $this->input->post('id'));
    }
  }

  function get_table($single = FALSE)
  {
    $this->table_query();
    $query = $this->db->get();
    if ($single == TRUE) {
      $method = 'row';
    } else {
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count()
  {
    $this->table_query();
    return $this->db->count_all_results();
  }

  function notifTBAK($idPengguna, $param)
  {
    if ($param == 'jumlah') {
      // Jumlah
      $query = $this->db->query('CALL keperawatan.TBAKnotifjumlah(' . $idPengguna . ')');
      return $query->num_rows();
    } elseif ($param == 'notifikasi') {
      // Tabel notifikasi
      return $this->db->query('CALL keperawatan.TBAKnotif(' . $idPengguna . ', 0)');
    } elseif ($param == 'history') {
      // Tabel history
      return $this->db->query('CALL keperawatan.TBAKnotifHistory(' . $idPengguna . ')');
    } else {
      return null;
    }
  }

  public function tabel($nomr)
  {
    return $this->db->query('CALL keperawatan.TBAKmenu(' . $nomr . ')');
  }

  public function ubah($data, $id)
  {
    $this->db->where('keperawatan.tb_tbak_detail.id', $id);
    $this->db->update('keperawatan.tb_tbak_detail', $data);
  }

  public function simpan($data)
  {
    $this->db->insert_batch('keperawatan.tb_tbak_detail', $data);
  }
}

/* End of file TbakDetailModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/catatanTerintegrasi/TbakDetailModel.php */