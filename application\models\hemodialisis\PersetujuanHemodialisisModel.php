<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PersetujuanHemodialisisModel extends CI_Model
{
  public function simpanInformedConsent($data)
  {
    $this->db->insert('db_informed_consent.tb_informed_consent', $data);
    return $this->db->insert_id();
  }

  public function simpanPTH($data)
  {
    $this->db->insert('db_informed_consent.tb_pt_hemodialisis', $data);
  }

  public function simpanPersetujuanTidakanKedokteran($data)
  {
    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran', $data);
  }

  public function historyPersetujuanHemodialisis()
  {
    $this->db->select(
      'pp.NORM, tic.id, tic.nokun, master.getNamaLengkapPegawai(ap.NIP) oleh, pth.status,
      master.getNamaLengkapPegawai(md.NIP) dokt<PERSON>_<PERSON><PERSON><PERSON><PERSON>, tic.created_at tanggal, tic.jenis_informed_consent'
    );
    $this->db->from('db_informed_consent.tb_informed_consent tic');
    $this->db->join('db_informed_consent.tb_pt_hemodialisis pth', 'pth.id_informed_consent = tic.id', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = tic.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = tic.oleh', 'left');
    $this->db->join('master.dokter md', 'md.ID = tic.dokter_pelaksana', 'left');
    $this->db->where('pp.NORM', $this->uri->segment(4));
    $this->db->where('tic.jenis_informed_consent', 3030);
    $this->db->order_by('tic.created_at', 'desc');

    $query = $this->db->get();
    return $query->result_array();
  }

  public function detailPTH($id)
  {
    $this->db->select(
      'tic.id id_tic, tic.nokun, tic.jenis_informed_consent, tic.penerima_informasi, tic.dokter_pelaksana, tic.status,
      pth.id id_pth, pth.kunjungan, pth.tanggal, pth.diagnosis, pth.dasar_diagnosis, pth.keterangan_dasar_diagnosis,
      pth.tindakan_kedokteran, pth.indikasi_tindakan, pth.tata_cara, pth.tujuan_tindakan, pth.tujuan_pengobatan,
      pth.risiko, pth.komplikasi, pth.prognosis, pth.alternatif_risiko, pth.lainnya, pth.ttd_menerangkan,
      pth.ttd_menerima, tpth.id id_tpth, tpth.nama_keluarga, tpth.umur_keluarga, tpth.jk_keluarga, tpth.alamat_keluarga,
      tpth.tindakan, tpth.hub_keluarga_dgn_pasien, tpth.tanggal_persetujuan, tpth.ttd_menyatakan,
      tpth.ttd_saksi_keluarga, tpth.ttd_saksi_rumah_sakit, tpth.saksi_keluarga, tpth.saksi_rumah_sakit,
      tpth.status_persetujuan, master.getNamaLengkap(pp.NORM) nama_pasien,
      master.getCariUmurTahun(pp.TANGGAL, mp.TANGGAL_LAHIR) umur, tic.created_at tanggal, mp.JENIS_KELAMIN jk,
      master.getNamaLengkapPegawai(ap2.NIP) saksi_rs'
    );
    $this->db->from('db_informed_consent.tb_informed_consent tic');
    $this->db->join('db_informed_consent.tb_pt_hemodialisis pth', 'pth.id_informed_consent = tic.id', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = tic.nokun', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ap.ID = tic.oleh', 'left');
    $this->db->join('master.dokter md', 'md.ID = tic.dokter_pelaksana', 'left');
    $this->db->join('db_informed_consent.tb_persetujuan_tindakan_kedokteran tpth', 'tpth.id_informed_consent = tic.id', 'left');
    $this->db->join('master.pasien mp', 'mp.NORM = pp.NORM', 'left');
    $this->db->join('aplikasi.pengguna ap2', 'ap2.ID = tpth.saksi_rumah_sakit', 'left');
    $this->db->where('tic.id', $id);

    $query = $this->db->get();
    return $query->row_array();
  }

  public function ubahInformedConcent($id, $data)
  {
    $this->db->where($id);
    $this->db->update('db_informed_consent.tb_informed_consent', $data);
  }

  public function ubahPTH($id, $data)
  {
    $this->db->where($id);
    $this->db->update('db_informed_consent.tb_pt_hemodialisis', $data);
  }

  public function ubahTPTK($id, $data)
  {
    $this->db->where($id);
    $this->db->update('db_informed_consent.tb_persetujuan_tindakan_kedokteran', $data);
  }
}