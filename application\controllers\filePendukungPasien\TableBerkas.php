<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class TableBerkas extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');
    }
    if(!in_array(2,$this->session->userdata('akses')) OR !in_array(4,$this->session->userdata('akses'))){
      redirect('login');
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('uploadRmModel','masterModel'));
  }

  public function index()
  {
    $listUpload = $this->uploadRmModel->listUpload();

    $data = array(
      'title'      => 'Halaman Data Upload Rm',
      'isi'        => 'uploadRm/tableData',
      'listUpload' => $listUpload,
    );

    $this->load->view('layout/wrapper',$data);
  }

  public function getFileEmr ()
  {
    $id = $this->input->post('id');
    $embed = $this->uploadRmModel->downloadFile($id);
    ?>
      <embed class="media" src="<?=base_url();?>bankdata/upload_emr/uploadRM/<?php echo $embed['NAME'] ?>" width="100%;" height="700px;"></embed>
      <!-- <embed class="media" src="<?=base_url();?>bankdata/<?php echo $embed['NAME'] ?>" width="100%;" height="700px;"></embed> -->
    <?php
  }

}

/* End of file TableBerkas.php */
/* Location: ./application/controllers/filePendukungPasien/TableBerkas.php */
