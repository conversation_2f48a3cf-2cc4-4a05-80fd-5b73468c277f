<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ScanModel extends MY_Model {

   public function getScan($nomr)
  {
    $query = $this->db->query("SELECT
                                sb.*,
                                mp.*,
                                tjr.NAMA JENIS_BERKAS
                              FROM
                                db_layanan.tb_scan_berkas sb
                              LEFT JOIN master.pasien mp on mp.NORM = sb.NOMR
                              LEFT JOIN master.tb_jenis_rekammedis tjr ON tjr.ID = SUBSTRING_INDEX(sb.FILE, '.', 1)
                              WHERE
                                sb.NOMR = '$nomr'
                              ORDER BY DATE_FORMAT(STR_TO_DATE(sb.SUBFOLDER,'%d%m%Y'), '%Y-%m-%d') DESC");
    return $query;
  }

   public function gethistori_pasien($nomr){
    $nomr=(int)$nomr;
    if($nomr>0){
      $stquery="SELECT p.NOMOR NOPEN, p.NORM, master.getNamaLengkap(p.NORM) NAMA_PASIEN, p.TANGGAL TGLMASUK
      , CASE 
        WHEN lpp.TANGGAL IS NOT NULL THEN lpp.TANGGAL 
        WHEN lpp.TANGGAL IS NULL AND p.`STATUS`=2 
          THEN (SELECT pkus.KELUAR FROM pendaftaran.kunjungan pkus
                LEFT JOIN pendaftaran.tujuan_pasien tpas ON tpas.NOPEN = pkus.NOPEN
                LEFT JOIN master.ruangan ras ON ras.ID = tpas.RUANGAN
              WHERE pkus.NOPEN = p.NOMOR AND pkus.`STATUS`=2
                AND ras.JENIS_KUNJUNGAN!=4
              ORDER BY pkus.KELUAR DESC LIMIT 1
              )
          ELSE null
          END AS TGLKELUAR
        , r.DESKRIPSI RUANGAN
        , IF(r.JENIS_KUNJUNGAN IN (3), 2, 1) IDJENIS
        , IF(r.JENIS_KUNJUNGAN IN (3), 'Rawat Inap', 'Rawat Jalan') JENIS
        , master.getNamaLengkapPegawai(doks.NIP) DPJP
          
      FROM pendaftaran.pendaftaran p
        
        LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
        LEFT JOIN master.ruangan r ON r.ID = tp.RUANGAN
        LEFT JOIN master.referensi pjm ON pjm.ID = pj.JENIS AND pjm.JENIS=10
        LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
        LEFT JOIN layanan.pasien_pulang lpp ON lpp.NOPEN = p.NOMOR AND lpp.`STATUS`!=0
        
      WHERE p.NORM=$nomr AND p.`STATUS`!=0
        AND r.JENIS_KUNJUNGAN NOT IN (4)
        
      GROUP BY p.NOMOR
      ORDER BY p.TANGGAL DESC";
    
      $query = $this->db->query($stquery);
      return $query->result_array();
    }else{
      return array();
    }
  }

   public function gethistori_pasienold($nomr){
    $nomr=(int)$nomr;
    if($nomr>0){
      $stquery="SELECT 
      dp.id NOPEN, dp.nomr NORM, master.getNamaLengkap(dp.nomr) NAMA_PASIEN, dp.tanggal TGLMASUK
      ,'' AS TGLKELUAR,'' AS RUANGAN
      , dp.jenis  IDJENIS
      , IF(dp.jenis IN (2), 'Rawat Inap', IF(dp.jenis IN (1), 'Rawat Jalan','Tidak diketahui')) JENIS
      ,master.getNamaLengkapPegawai(doks.NIP) DPJP
      
      FROM  db_layanan.tb_scan_pendaftaran dp 
      LEFT JOIN master.dokter doks ON doks.ID = dp.dokter
      
      WHERE dp.nomr=$nomr 
      ORDER BY dp.tanggal DESC";
    
      $query = $this->db->query($stquery);
      return $query->result_array();
    }else{
      return array();
    }
  }

   public function getberkas($nomr="",$nopen="",$nmf=""){
    $stquery="SELECT tsb.namafile,tsb.lokasi, tsb.idjenis_berkas jenis, (date(tsb.created_at)= CURDATE()) AS 'modif'
        	FROM db_layanan.tb_scan_berkas2 tsb
        	WHERE tsb.nomr=$nomr
        	AND tsb.nopen='$nopen'
        	".($nmf!=''?" AND tsb.namafile='$nmf' ":'')."
        	AND tsb.deleted_at IS NULL ";
          // echo $stquery;exit;
    $query = $this->db->query($stquery);
    if($nmf!=''){
      return $query->row();
    }else{
      return $query->result_array();
    }
  }

   public function delberkas($nmf="",$nomr="",$nopen="",$userid=""){
    if($nmf){
    $stquery="UPDATE db_layanan.tb_scan_berkas2 SET deleted_at=now(), deleted_by='$userid'
        	WHERE nomr=$nomr
        	AND nopen='$nopen'
        	AND namafile='$nmf'
        	AND created_by=$userid
        	AND date(created_at)=CURDATE() 
        	";
          // echo $stquery;exit; 
    $query = $this->db->query($stquery);
    return $this->db->affected_rows();
    }
  }

  public function BerkasScanPendaftaran($nomr){
    $stquery="CALL db_layanan.BerkasScanPendaftaran($nomr)";
    $query = $this->db->query($stquery);
      return $query->result_array();
  }
  
  public function getberkasshow($nomr="",$nopen="",$tanggal=""){
    $stquery="CALL db_layanan.BerkasScanFile('$nomr', '$nopen', '$tanggal')";
    // $stquery="SELECT tsb.namafile,tsb.lokasi, tsb.idjenis_berkas jenis, jb.nama_berkas,kb.nama_kategori
    //     	FROM db_layanan.tb_scan_berkas2 tsb
    //       LEFT JOIN db_layanan.jenis_berkas jb ON jb.id=tsb.idjenis_berkas
    //       LEFT JOIN db_layanan.kategori_berkas kb ON kb.id=jb.id_kategori
    //     	WHERE tsb.nomr=$nomr
    //     	AND tsb.nopen='$nopen'
    //       ORDER BY kb.id,jb.id";
          // echo $stquery;exit;
    $query = $this->db->query($stquery);
      return $query->result_array();

  }

  public function actaddhistory($data){
    if($data['nomr'] && $data['tanggal'] && $data['jenis']){
      $qcek="SELECT 1
        	FROM db_layanan.tb_scan_pendaftaran dp
        	WHERE dp.nomr='".$data['nomr']."'
        	AND date(dp.tanggal)=date('".$data['tanggal']."')";
            // echo $qcek;exit;
      $query = $this->db->query($qcek);
      $check = $query->num_rows();
      // var_dump($check);exit;
      if($check>0){
        return 'ada';
      }else{
        $stquery="INSERT INTO db_layanan.tb_scan_pendaftaran (nomr,tanggal,dokter,jenis) VALUES ('".$data['nomr']."','".$data['tanggal']."','".$data['dokter']."','".$data['jenis']."') ";
        // echo $stquery;exit;
        $query = $this->db->query($stquery);
        return $query;
      }
    }

  }

  public function kategoriberkasrm3($layanan='') {
    $string="SELECT  a.id, a.nama_kategori, a.jenis_layanan
    FROM db_layanan.kategori_berkas a
    WHERE a.status = 1
    AND a.jenis_layanan='" . $layanan . "'
    ORDER BY a.nama_kategori";
    // echo "<br>".$string;exit;
      $query = $this->db->query($string);

      return $query->result_array();
  }

  public function jenisBerkasrm3($kategori='')
  {
    $string="SELECT  a.id,a.id_kategori, a.nama_berkas, b.nama_kategori
    FROM db_layanan.jenis_berkas a
    LEFT JOIN db_layanan.kategori_berkas b ON b.id=a.id_kategori
    WHERE a.status = 1
    AND b.status=1
    AND a.id_kategori='" . $kategori . "'
    ORDER BY a.nama_berkas ";
    // echo "<br>".$string;exit;
      $query = $this->db->query($string);

      return $query->result_array();
  }

  public function cekberkasrm3($nomr='',$jenisberkasid='',$tglform1='',$tglform2=''){
    $stquery="SELECT 1
        	FROM db_layanan.tb_scan_berkas3 tsb
        	WHERE tsb.nomr='$nomr'
          AND tsb.idjenis_berkas='$jenisberkasid'
          AND tsb.periode_awal='$tglform1'
          AND tsb.periode_akhir='$tglform2'
        	AND tsb.deleted_at IS NULL ";
          // echo $stquery;exit;
    $query = $this->db->query($stquery);
    return $query->num_rows();
  
  }

  public function listberkasrm3($nomr=''){
    $string="SELECT tsb.id,tsb.cara_upload,tsb.jenis_pelayanan,tsb.namafile,tsb.lokasi, tsb.periode_awal,tsb.Periode_akhir,tsb.created_at
    ,jb.id idjenisberkas, jb.nama_berkas, kb.id idjeniskategori, kb.nama_kategori
	  FROM db_layanan.tb_scan_berkas3 tsb
    LEFT JOIN db_layanan.jenis_berkas jb ON tsb.idjenis_berkas=jb.id
    LEFT JOIN db_layanan.kategori_berkas kb ON jb.id_kategori=kb.id
    WHERE tsb.nomr='$nomr'
    AND tsb.deleted_at IS NULL
    ORDER BY tsb.periode_awal desc,tsb.created_at desc";
    
    $query  = $this->db->query($string);

    return $query->result();
  }

  public function hapusBerkasrm3($param=''){
    $string="UPDATE db_layanan.tb_scan_berkas3 set deleted_at=now(), deleted_by='".$param['oleh']."'
    WHERE nomr='".$param['nomr']."' AND id='".$param['id']."'";
    // echo $string;exit;
    $query  = $this->db->query($string);
    if($query){
      return true;
    }else{
      return false;
    }
  }
}
?>