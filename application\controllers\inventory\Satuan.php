<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>tuan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
  }

  if (!in_array(25, $this->session->userdata('akses'))) {
      redirect('login');
  }

  date_default_timezone_set("Asia/Bangkok");
  $this->load->model(array('inventory/Model_satuan'));
}

public function index()
{
    $satuan         =  $this->Model_satuan->tampil_data();
    $data = array(
      'title'        => 'Halaman Master Barang',
      'isi'          => 'inventory/satuan/index',
      'satuan'       => $satuan,
  );
    $this->load->view('layout/wrapper',$data);
}


 function tambah(){
  $data = array(
    'SATUAN'    => $this->input->post('NAMA')
  );
  $this->Model_satuan->tambah($data);
  $this->session->set_flashdata('notif','<div class="alert alert-success" role="alert"> Data Berhasil ditambahkan <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button></div>');
  redirect('inventory/Satuan');
}

function ubah(){
  $id = $this->input->post('ID');
  $data = array(
    'SATUAN'    => $this->input->post('NAMA'),
  );
  $this->Model_satuan->ubah($data,$id);
  $this->session->set_flashdata('notif','<div class="alert alert-success" role="alert"> Data Berhasil diubah <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button></div>');
  redirect('inventory/Satuan');
}

function delete()
{
    $id=  $this->uri->segment(3);
    $this->model_barang->delete($id);
    redirect('Barang');
}
}