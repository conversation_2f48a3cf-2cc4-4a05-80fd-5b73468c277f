<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MedisBrakhiterapi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    $this->load->model(array('masterModel','rekam_medis/MedisModel','pengkajianAwalModel','rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel','rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel', 'rekam_medis/TbBbModel'));
  }

  public function index()
  {
    // $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
    $pasien = $this->MedisModel->getBrakhiterapi($this->uri->segment(2));

    $data = array(
      'pasien' => $pasien,
      
      'Ecog'                  => $this->masterModel->referensi(30),

      'sisiTubuh'        => $this->masterModel->referensi(49),
      'stadium'          => $this->masterModel->stadium(),
      'tujuanPengobatan' => $this->masterModel->referensi(266),

      'kunjungan_pk' => $this->pengkajianAwalModel->kunjungan_pk($pasien['NORM']),
      'sitologi'     => $this->pengkajianAwalModel->sitologi($pasien['NORM']),
      'histologi'    => $this->pengkajianAwalModel->histologi($pasien['NORM']),
      'tindakan_rad' => $this->pengkajianAwalModel->tindakan_rad($pasien['NORM']),

      'performanceStatus_2' => $this->masterModel->referensi(262),

      'klinis' => $this->masterModel->referensi(256),
      'teknik' => $this->masterModel->referensi(162),
      'fraksi' => $this->masterModel->referensi(1317),

    );

    // echo "<pre>";print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/pengkajian/pengkajianRiLain/pengkajianRiMedisBrakhiterapi',$data);
  }

  public function action($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $this->db->trans_begin();
          $post = $this->input->post();

          $getIdEmr = !empty($post['idemr']) ? $post['idemr'] : $this->pengkajianAwalModel->getIdEmr();

          $dataMedis = array(
            'id_emr'         => $getIdEmr,
            'nopen'          => $post['nopen'],
            'nokun'          => $post['nokun'],
            'jenis'          => $post['jenis'],
            'created_by'     => $this->session->userdata('id'),
            'flag'           => '1',
            'created_at' => isset($post['backDateBrakhiterapiRadioterapiRi']) ? date('Y-m-d H:i:s', strtotime($post['tglBackDateBrakhiterapiRadioterapiRi'])) : date("Y-m-d H:i:s"),
          );

          $dataAnamnesa = array(
            'id_emr' => $getIdEmr,
            'keluhan_utama' => isset($post['keluhanUtama']) ? $post['keluhanUtama'] : "",
          );

          if (isset($post['status_lokalis_not']) == "on") {
            $lokaslisNot = 1;
          } else {
            $lokaslisNot = 0;
          }

          $dataPemeriksaanFisik = array(
            'id_emr' => $getIdEmr,
            'performance_status' => isset($post['performanceStatus']) ? $post['performanceStatus'] : "",
          );

          $dataPenunjangLainya = array(
            'id_emr' => $getIdEmr,
            'penunjang_lain' => isset($post['penunjangLainya']) ? $post['penunjangLainya'] : "",
          );

          if (isset($post['klasifikasi_not']) == "on") {
            $klasifikasiNot = 1;
          } else {
            $klasifikasiNot = 0;
          }

          $dataMedisKeperawatan = array(
            'id_emr' => $getIdEmr,
            'desk_diagnosa_medis' => isset($post['deskDiagnosisMedis']) ? $post['deskDiagnosisMedis'] : "",
            'desk_diagnosa_kanker' => isset($post['deskDiagnosaKanker']) ? $post['deskDiagnosaKanker'] : "",
            'klasifikasi_t' => isset($post['klasifikasi_T']) ? $post['klasifikasi_T'] : "",
            'klasifikasi_n' => isset($post['klasifikasi_N']) ? $post['klasifikasi_N'] : "",
            'klasifikasi_m' => isset($post['klasifikasi_M']) ? $post['klasifikasi_M'] : "",
            'klasifikasi_not' => isset($klasifikasiNot) ? $klasifikasiNot : "",
            'stadium' => isset($post['stadium']) ? $post['stadium'] : "",
          );

          $masalahKesehatanMedis = array(
            'id_emr' => $getIdEmr,
            'masalahKesehatan' => isset($post['masalahKesehatanMedis']) ? json_encode($post['masalahKesehatanMedis']) : "",
          );

          $dataPerencanaan = array(
            'id_emr' => $getIdEmr,
            'pemeriksaan_penunjang' => isset($post['pemeriksaanPenunjang']) ? $post['pemeriksaanPenunjang'] : "",
            'terapi_tindakan' => isset($post['terapiTindakan']) ? $post['terapiTindakan'] : "",
          );

          $instruksiMedis = array(
            'id_emr' => $getIdEmr,
            'rencana_tindakan' => isset($post['rencanaTindakan']) ? $post['rencanaTindakan'] : "",
            'teknik' => isset($post['teknik']) ? $post['teknik'] : 0,
            'lokasi' => isset($post['lokasiPemasangan']) ? $post['lokasiPemasangan'] : "",
            'dosis_per_fraksi' => isset($post['dosis']) ? $post['dosis'] : "",
            'jumlah_fraksi' => isset($post['fraksi']) ? $post['fraksi'] : "",
          );

          if (!empty($post['idemr'])) {
            $this->db->replace('medis.tb_anamnesa', $dataAnamnesa);
            $this->db->replace('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
            $this->db->replace('medis.tb_penunjang_lain', $dataPenunjangLainya);
            $this->db->replace('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
            $this->db->replace('medis.tb_masalahkesehatanmedis', $masalahKesehatanMedis);
            $this->db->replace('medis.tb_perencanaan', $dataPerencanaan);
            $this->db->replace('medis.tb_instruksi_medis_radioterapi', $instruksiMedis);
            if ($this->db->replace('medis.tb_medis', $dataMedis)) {
              $result = array('status' => 'success', 'pesan' => 'ubah');
            }
          } else {
            $result = array('status' => 'failed');
            $this->db->insert('medis.tb_anamnesa', $dataAnamnesa);
            $this->db->insert('medis.tb_pemeriksaan_fisik', $dataPemeriksaanFisik);
            $this->db->insert('medis.tb_penunjang_lain', $dataPenunjangLainya);
            $this->db->insert('medis.tb_masalah_medis_kep', $dataMedisKeperawatan);
            $this->db->insert('medis.tb_masalahkesehatanmedis', $masalahKesehatanMedis);
            $this->db->insert('medis.tb_perencanaan', $dataPerencanaan);
            $this->db->insert('medis.tb_instruksi_medis_radioterapi', $instruksiMedis);

            if ($this->db->insert('medis.tb_medis', $dataMedis)) {
              $result = array('status' => 'success');
            }
          }

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
      }

      else if($param == 'count'){
        $result = $this->MedisDewasaModel->get_count();;
        echo json_encode($result);
      }

      else if($param == 'ambil'){
        $post = $this->input->post(NULL,TRUE);
        $dataMedisDewasaModel = $this->MedisDewasaModel->get($post['nokun'], true);

        echo json_encode(array(
          'status' => 'success',
          'data'   => $dataMedisDewasaModel
        ));
      }

    }
  }

  public function datatables(){
    $result = $this->MedisDewasaModel->datatables();

    $data = array();
    foreach ($result as $row){
      $sub_array = array();
      $sub_array[] = '<a class="btn btn-primary btn-block btn-sm historyPengkajianRiDewasaMedis" data-id="'.$row -> ID_EMR.'"><i class="fa fa-eye"></i> Lihat</a>';
      $sub_array[] = $row -> TANGGAL_PENGKAJIAN_MEDIS;
      $sub_array[] = $row -> RUANGAN;
      $sub_array[] = $row -> DPJP;
      $sub_array[] = $row -> USER_MEDIS;

      $data[] = $sub_array;
    }

    $output = array(
      "draw"              => intval($_POST["draw"]),
      "recordsTotal"      => $this->MedisDewasaModel->total_count(),
      "recordsFiltered"   => $this->MedisDewasaModel->filter_count(),
      "data"              => $data
    );
    echo json_encode($output);
  }

}

/* End of file MedisDewasa.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasa.php */
