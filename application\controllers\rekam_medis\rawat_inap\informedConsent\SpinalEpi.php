<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SpinalEpi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array(
      'masterModel',
      'pengkajianAwalModel',
      'rekam_medis/rawat_inap/pengkajian/pengkajianRI/DewasaModel',
      'rekam_medis/MedisModel',
      'rekam_medis/rawat_inap/informedConsent/SpinalEpiModel'
    ));
  }

  public function index(){
  	// $norm = $this->uri->segment(6);
    // $nopen = $this->uri->segment(7);
    $nokun = $this->uri->segment(8);
    // $nokun = $this->uri->segment(6);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $data = array(
      // 'nopen' => $nopen,
      // 'norm' => $norm,
      // 'nokun' => $nokun,
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'DiagnosisWDDDSpinalEpi' => $this->masterModel->referensi(974),
      'DasarDiagnosisSpinalEpi' => $this->masterModel->referensi(975),
      'TindakanKedokteranSpinalEpi' => $this->masterModel->referensi(1596),
      'IndikasiTindakanSpinalEpi' => $this->masterModel->referensi(977),
      'TataCaraSpinalEpi' => $this->masterModel->referensi(1597),
      'TujuanTindakanSpinalEpi' => $this->masterModel->referensi(1598),
      'RisikoSpinalEpi' => $this->masterModel->referensi(1599),
      'KomplikasiSpinalEpi' => $this->masterModel->referensi(1600),
      'PrognosisSpinalEpi' => $this->masterModel->referensi(982),
      'AlternatifSpinalEpi' => $this->masterModel->referensi(1601),
      'LainLainSpinalEpi' => $this->masterModel->referensi(1602),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );
     // print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/informedConsent/spinalEpi/index', $data);
  }

  public function simpanSpinalEpi()
  {
    $this->db->trans_begin();

    $post = $this->input->post();

    $date = $this->input->post('datePickerSpinalEpi');
    $tglPersetujuanSpinalEpi = date('Y-m-d h:i', strtotime($date));

    $dataInformedConcent = array (
      'nokun'                  => $post['nokun'],
      'jenis_informed_consent' => 3032,
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanSpinalEpi'],
      'penerima_informasi'     => $post['penerimaInformasiSpinalEpi'],
      'pemberi_informasi'      => $post['pemberiInformasiSpinalEpi'],
      'oleh'                   => $this->session->userdata('id'),
    );
    // echo "<pre>";print_r($dataInformedConcent);echo "</pre>";

    $idInformedConcent = $this->SpinalEpiModel->simpanInformedConcent($dataInformedConcent);

    $dataSpinalEpi = array (
      // 'id_informed_consent'              => 1,
      'id_informed_consent'              => $idInformedConcent,
      'diagnosis_wd_dd'                  => implode(',',$post["DiagnosisWDDDSpinalEpi"]),
      'dasar_diagnosis'                  => implode(',',$post["DasarDiagnosisSpinalEpi"]),
      'tindakan_kedokteran'              => implode(',',$post["TindakanKedokteranSpinalEpi"]),
      'indikasi_tindakan'                => implode(',',$post["IndikasiTindakanSpinalEpi"]),
      'tata_cara'                        => implode(',',$post["TataCaraSpinalEpi"]),
      'tujuan_tindakan'                  => implode(',',$post["TujuanTindakanSpinalEpi"]),
      'risiko'                           => implode(',',$post["RisikoSpinalEpi"]),
      'komplikasi'                       => implode(',',$post["KomplikasiSpinalEpi"]),
      'prognosis'                        => implode(',',$post["PrognosisSpinalEpi"]),
      'alternatif'                       => implode(',',$post["AlternatifSpinalEpi"]),
      'lain_lain'                        => implode(',',$post["LainLainSpinalEpi"]),
      'ttd_menerangkan'                  => file_get_contents($this->input->post('ttd_menerang_spinalEpi')),
      'ttd_menerima'                     => file_get_contents($this->input->post('ttd_terima_spinalEpi')),
    );
    // echo "<pre>";print_r($dataSpinalEpi);echo "</pre>";

    $this->SpinalEpiModel->simpanSpinalEpi($dataSpinalEpi);

    $dataPersetujuanSpinalEpi = array(
      // 'id_informed_consent'        => 1,
      'id_informed_consent'        => $idInformedConcent,
      'nama_keluarga'              => $post['namaSpinalEpi'],
      'umur_keluarga'              => $post['umurSpinalEpi'],
      'jk_keluarga'                => $post['jenis_kelaminSpinalEpi'],
      'alamat_keluarga'            => $post['alamatSpinalEpi'],
      'tindakan'                   => $post['tindakanSpinalEpi'],
      'hub_keluarga_dgn_pasien'    => $post['hubunganSpinalEpi'],
      'tanggal_persetujuan'        => $tglPersetujuanSpinalEpi,
      'ttd_menyatakan'             => file_get_contents($this->input->post('sign_Menyatakan_SpinalEpi')),
      'ttd_saksi_keluarga'         => file_get_contents($this->input->post('sign_KeluargaSpinalEpi')),
      'ttd_saksi_rumah_sakit'      => file_get_contents($this->input->post('sign_RumahSakitSpinalEpi')),
      'saksi_keluarga'             => $post['nama_keluarga_SpinalEpi'],
      'saksi_rumah_sakit'          => $post['nama_saksi_rs_SpinalEpi']
    );
    // echo "<pre>";print_r($dataPersetujuanSpinalEpi);echo "</pre>";exit();

    $this->db->insert('db_informed_consent.tb_persetujuan_tindakan_kedokteran',$dataPersetujuanSpinalEpi);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function historySpinalEpi()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $nomr = $this->input->post('nomr');
    // $nomr = $this->uri->segment(6);
    $listSpinalEpi = $this->SpinalEpiModel->listHistoryInformedConsentSpinalEpi($nomr);

    $data = array();
    $no = 1;
    foreach ($listSpinalEpi->result() as $SpinalEpi) {

      $data[] = array(
        $no,
        $SpinalEpi->nokun,
        $SpinalEpi->DOKTERPELAKSANA,
        $SpinalEpi->OLEH,
        date("d-m-Y H:i:s",strtotime($SpinalEpi->tanggal)),
        '<a href="#modalSpinalEpi" class="btn btn-primary btn-block" data-id="'.$SpinalEpi->id.'" data-toggle="modal" data-backdrop="static" data-keyboard="false"><i class="fas fa-edit"></i> Edit</a>',
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listSpinalEpi->num_rows(),
      "recordsFiltered" => $listSpinalEpi->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function modalSpinalEpi()
  {
    $id = $this->input->post('id');
    // $nokun = $this->uri->segment(8);
    $gpSpinalEpi = $this->SpinalEpiModel->getSpinalEpi($id);
    $nokun = $gpSpinalEpi['nokun'];
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $explode_diagnosis_wd_dd       = explode(',' , $gpSpinalEpi['diagnosis_wd_dd']);
    $explode_dasar_diagnosis       = explode(',' , $gpSpinalEpi['dasar_diagnosis']);
    $explode_tindakan_kedokteran   = explode(',' , $gpSpinalEpi['tindakan_kedokteran']);
    $explode_indikasi_tindakan     = explode(',' , $gpSpinalEpi['indikasi_tindakan']);
    $explode_tata_cara             = explode(',' , $gpSpinalEpi['tata_cara']);
    $explode_tujuan_tindakan       = explode(',' , $gpSpinalEpi['tujuan_tindakan']);
    $explode_risiko                = explode(',' , $gpSpinalEpi['risiko']);
    $explode_komplikasi            = explode(',' , $gpSpinalEpi['komplikasi']);
    $explode_prognosis             = explode(',' , $gpSpinalEpi['prognosis']);
    $explode_alternatif            = explode(',' , $gpSpinalEpi['alternatif']);
    $explode_lain_lain             = explode(',' , $gpSpinalEpi['lain_lain']);
    // echo "<pre>";print_r($explode_diagnosis_wd_dd);exit();

    $data = array(
      'id' => $id,
      'gpSpinalEpi' => $gpSpinalEpi,
      'explode_diagnosis_wd_dd'       => $explode_diagnosis_wd_dd,
      'explode_dasar_diagnosis'       => $explode_dasar_diagnosis,
      'explode_tindakan_kedokteran'   => $explode_tindakan_kedokteran,
      'explode_indikasi_tindakan'     => $explode_indikasi_tindakan,
      'explode_tata_cara'             => $explode_tata_cara,
      'explode_tujuan_tindakan'       => $explode_tujuan_tindakan,
      'explode_risiko'                => $explode_risiko,
      'explode_komplikasi'            => $explode_komplikasi,
      'explode_prognosis'             => $explode_prognosis,
      'explode_alternatif'            => $explode_alternatif,
      'explode_lain_lain'             => $explode_lain_lain,
      // Informed Consent
      'DiagnosisWDDDSpinalEpi' => $this->masterModel->referensi(974),
      'DasarDiagnosisSpinalEpi' => $this->masterModel->referensi(975),
      'TindakanKedokteranSpinalEpi' => $this->masterModel->referensi(1596),
      'IndikasiTindakanSpinalEpi' => $this->masterModel->referensi(977),
      'TataCaraSpinalEpi' => $this->masterModel->referensi(1597),
      'TujuanTindakanSpinalEpi' => $this->masterModel->referensi(1598),
      'RisikoSpinalEpi' => $this->masterModel->referensi(1599),
      'KomplikasiSpinalEpi' => $this->masterModel->referensi(1600),
      'PrognosisSpinalEpi' => $this->masterModel->referensi(982),
      'AlternatifSpinalEpi' => $this->masterModel->referensi(1601),
      'LainLainSpinalEpi' => $this->masterModel->referensi(1602),
      'listDrUmum' => $this->masterModel->listDrUmum(),
      'jenis_kelamin' => $this->masterModel->referensi(965),
      'getNomr' => $getNomr,
    );

    $this->load->view('rekam_medis/rawat_inap/informedConsent/spinalEpi/view_edit', $data);
  }

  public function updateSpinalEpi()
  {
    $this->db->trans_begin();

    $id    = $this->input->post('id');
    $idSpinalEpi = $this->input->post('idSpinalEpi');
    $post = $this->input->post();

    $dataInformedConcent = array (
      'dokter_pelaksana'       => $post['dokterPelaksanaTindakanSpinalEpi_edit'],
      'penerima_informasi'     => $post['penerimaInformasiSpinalEpi_edit'],
      'pemberi_informasi'      => $post['pemberiInformasiSpinalEpi_edit'],
    );

    $this->SpinalEpiModel->updateInformedConcent($dataInformedConcent,$id);

    $dataSpinalEpi_edit = array (
      'diagnosis_wd_dd'                  => implode(',',$post["DiagnosisWDDDSpinalEpi_edit"]),
      'dasar_diagnosis'                  => implode(',',$post["DasarDiagnosisSpinalEpi_edit"]),
      'tindakan_kedokteran'              => implode(',',$post["TindakanKedokteranSpinalEpi_edit"]),
      'indikasi_tindakan'                => implode(',',$post["IndikasiTindakanSpinalEpi_edit"]),
      'tata_cara'                        => implode(',',$post["TataCaraSpinalEpi_edit"]),
      'tujuan_tindakan'                  => implode(',',$post["TujuanTindakanSpinalEpi_edit"]),
      'risiko'                           => implode(',',$post["RisikoSpinalEpi_edit"]),
      'komplikasi'                       => implode(',',$post["KomplikasiSpinalEpi_edit"]),
      'prognosis'                        => implode(',',$post["PrognosisSpinalEpi_edit"]),
      'alternatif'                       => implode(',',$post["AlternatifSpinalEpi_edit"]),
      'lain_lain'                        => implode(',',$post["LainLainSpinalEpi_edit"]),
    );

    // print_r($dataPTKemoRhabdomiosarkoma_edit);exit();

    $this->SpinalEpiModel->updateSpinalEpi($dataSpinalEpi_edit,$idSpinalEpi);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

}