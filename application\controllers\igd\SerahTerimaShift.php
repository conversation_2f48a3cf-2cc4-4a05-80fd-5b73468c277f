<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SerahTerimaShift extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Bangkok');
    $this->load->model(array('masterModel', 'pengkajianAwalModel', 'igd/SerahTerimaShiftModel'));
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $getNomr['NORM'];
    $data = array(
      'getNomr' => $getNomr,
      'nokun' => $nokun,
      'listShiftIGD' => $this->masterModel->listShiftIGD(),
      'listserahTerimaDataPasienIGD' => $this->masterModel->listserahTerimaDataPasienIGD(),
      'listKesadaranIGD' => $this->masterModel->listKesadaranIGD(),
      'listResikoJatuhIGD' => $this->masterModel->listResikoJatuhIGD(),
      'listAlatTerpasangIGD' => $this->masterModel->listAlatTerpasangIGD(),
      'listPerawat' => $this->masterModel->listPerawat(),
      'listOksigenIGD' => $this->masterModel->listOksigenIGD(),
      'listMasalahAsuhanIGD' => $this->masterModel->listMasalahAsuhanIGD(),
      'historySerahTerimaIGD' => $this->SerahTerimaShiftModel->history($nomr),
    );
    /*echo '<pre>';print_r($data);exit();*/
    $this->load->view('Pengkajian/igd/serahTerimaShift/index', $data);
  }

  public function simpanSerahTerimaShiftIgd()
  {
    $this->db->trans_begin();
    $post = $this->input->post();

    $data = array(
      'nokun' => $post['nokun'],
      'shiftKe' => $post['shiftKe'],
      'datapasiensrigd' => json_encode($post['datapasiensrigd']),
      'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : '',
      'skornyeri' => isset($post['skornyeri']) ? $post['skornyeri'] : '',
      'skorewspws' => isset($post['skorewspws']) ? $post['skorewspws'] : '',
      'resikojatuh' => isset($post['resikojatuh']) ? $post['resikojatuh'] : '',
      'tandaVital' => isset($post['tandaVital']) ? $post['tandaVital'] : '',
      'balance' => isset($post['balance']) ? $post['balance'] : '',
      'alatterpasang' => json_encode($post['alatterpasang']),
      'oksigenIGD' => isset($post['oksigenIGD']) ? $post['oksigenIGD'] : '',
      'lainAlatTerpasang' => isset($post['lainAlatTerpasang']) ? $post['lainAlatTerpasang'] : '',
      'pemeriksaanPenunjang' => $post['pemeriksaanPenunjang'],
      'tindakanObat' => $post['tindakanObat'],
      'masalahasuhan' => $post['masalahasuhan'],
      'perencanaan' => $post['perencanaan'],
      'evaluasiMasalah' => $post['evaluasiMasalah'],
      'yangMenerima' => $post['yangMenerima'],
      'oleh' => $this->session->userdata('id'),
      'status' => 1,
    );
    /*echo '<pre>';print_r($data);exit();*/
    $this->SerahTerimaShiftModel->simpan($data);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function detailSerahTerimaShiftIgd()
  {
    $id = $this->input->post('id');
    $data = array(
      'listserahTerimaDataPasienIGD' => $this->masterModel->listserahTerimaDataPasienIGD(),
      'listAlatTerpasangIGD' => $this->masterModel->listAlatTerpasangIGD(),
      'listMasalahAsuhanIGD' => $this->masterModel->listMasalahAsuhanIGD(),
      'listShiftIGD' => $this->masterModel->listShiftIGD(),
      'listKesadaranIGD' => $this->masterModel->listKesadaranIGD(),
      'listResikoJatuhIGD' => $this->masterModel->listResikoJatuhIGD(),
      'listOksigenIGD' => $this->masterModel->listOksigenIGD(),
      'listPerawat' => $this->masterModel->listPerawat(),
      'dataShiftIGD' => $this->SerahTerimaShiftModel->detailHistory($id),
    );
    /*echo "<pre>";print_r($data);exit();*/
    $this->load->view('Pengkajian/igd/serahTerimaShift/history/ubah', $data);
  }
}