<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Odontogram extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','OdontogramModel'));
    }

    public function index(){
        $data = array(
            'getNomr' => $this->pengkajianAwalModel->getNomr($this->uri->segment(5)),
            'occlusi' => $this->masterModel->referensi(453),
            'torus_palatinus' => $this->masterModel->referensi(454),
            'torus_mandibularis' => $this->masterModel->referensi(455),
            'palatum' => $this->masterModel->referensi(456),
            'diastema' => $this->masterModel->referensi(457),
            'gigi_anomali' => $this->masterModel->referensi(458),
            'foto' => $this->masterModel->referensi(459),
            'foto_rontgen' => $this->masterModel->referensi(460),
        );

        $this->load->view('Pengkajian/gigi/odontogram',$data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->OdontogramModel->rules;
    			$this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();

                    $data = array(
                        'kunjungan' => $post['nokun'],

                        'P11_51' => $post['11_51'],
                        'P12_52' => $post['12_52'],
                        'P13_53' => $post['13_53'],
                        'P14_54' => $post['14_54'],
                        'P15_55' => $post['15_55'],
                        'P16' => $post['16'],
                        'P17' => $post['17'],
                        'P18' => $post['18'],
                        'P21_61' => $post['21_61'],
                        'P22_62' => $post['22_62'],
                        'P23_63' => $post['23_63'],
                        'P24_64' => $post['24_64'],
                        'P25_65' => $post['25_65'],
                        'P26' => $post['26'],
                        'P27' => $post['27'],
                        'P28' => $post['28'],
                        'P48' => $post['48'],
                        'P47' => $post['47'],
                        'P46' => $post['46'],
                        'P45_85' => $post['45_85'],
                        'P44_84' => $post['44_84'],
                        'P43_83' => $post['43_83'],
                        'P42_82' => $post['42_82'],
                        'P41_81' => $post['41_81'],
                        'P38' => $post['38'],
                        'P37' => $post['37'],
                        'P36' => $post['36'],
                        'P35_75' => $post['35_75'],
                        'P34_74' => $post['34_74'],
                        'P33_73' => $post['33_73'],
                        'P32_72' => $post['32_72'],
                        'P31_71' => $post['31_71'],

                        'occlusi' => $post['occlusi'],
                        'torus_palatinus' => $post['torus_palatinus'],
                        'torus_mandibularis' => $post['torus_mandibularis'],
                        'palatum' => $post['palatum'],
                        'diastema' => $post['diastema'],
                        'diastema_lainnya' => isset($post['diastema_lainnya']) ? $post['diastema_lainnya'] : null,
                        'gigi_anomali' => $post['gigi_anomali'],
                        'gigi_anomali_lainnya' => isset($post['gigi_anomali_lainnya']) ? $post['gigi_anomali_lainnya'] : null,
                        'lain_lain' => $post['lain_lain'],
                        'd' => $post['d'],
                        'm' => $post['m'],
                        'f' => $post['f'],
                        'jumlah_foto' => $post['jumlah_foto'],
                        'foto' => $post['foto'],
                        'jumlah_rontgen' => $post['jumlah_rontgen'],
                        'rontgen' => $post['foto_rontgen'],
                        'gambar_gigi' => file_get_contents($this->input->post('odontogramVal')),
                        'gambar_mulut' => file_get_contents($this->input->post('mulutVal')),
                        'oleh' => $this->session->userdata("id"),
                    );

    				if($this->db->replace('medis.tb_odontogram',$data)){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataOdontogram = $this->OdontogramModel->get($post['nokun'], true);
                $data = array();
    			if(!empty($dataOdontogram)){
                    $data['kunjungan'] = $dataOdontogram->kunjungan;
                    $data['tanggal'] = $dataOdontogram->tanggal;

                    $data['P11_51'] = $dataOdontogram->P11_51;
                    $data['P12_52'] = $dataOdontogram->P12_52;
                    $data['P13_53'] = $dataOdontogram->P13_53;
                    $data['P14_54'] = $dataOdontogram->P14_54;
                    $data['P15_55'] = $dataOdontogram->P15_55;
                    $data['P16'] = $dataOdontogram->P16;
                    $data['P17'] = $dataOdontogram->P17;
                    $data['P18'] = $dataOdontogram->P18;
                    $data['P21_61'] = $dataOdontogram->P21_61;
                    $data['P22_62'] = $dataOdontogram->P22_62;
                    $data['P23_63'] = $dataOdontogram->P23_63;
                    $data['P24_64'] = $dataOdontogram->P24_64;
                    $data['P25_65'] = $dataOdontogram->P25_65;
                    $data['P26'] = $dataOdontogram->P26;
                    $data['P27'] = $dataOdontogram->P27;
                    $data['P28'] = $dataOdontogram->P28;
                    $data['P48'] = $dataOdontogram->P48;
                    $data['P47'] = $dataOdontogram->P47;
                    $data['P46'] = $dataOdontogram->P46;
                    $data['P45_85'] = $dataOdontogram->P45_85;
                    $data['P44_84'] = $dataOdontogram->P44_84;
                    $data['P43_83'] = $dataOdontogram->P43_83;
                    $data['P42_82'] = $dataOdontogram->P42_82;
                    $data['P41_81'] = $dataOdontogram->P41_81;
                    $data['P38'] = $dataOdontogram->P38;
                    $data['P37'] = $dataOdontogram->P37;
                    $data['P36'] = $dataOdontogram->P36;
                    $data['P35_75'] = $dataOdontogram->P35_75;
                    $data['P34_74'] = $dataOdontogram->P34_74;
                    $data['P33_73'] = $dataOdontogram->P33_73;
                    $data['P32_72'] = $dataOdontogram->P32_72;
                    $data['P31_71'] = $dataOdontogram->P31_71;

                    $data['occlusi'] = $dataOdontogram->occlusi;
                    $data['torus_palatinus'] = $dataOdontogram->torus_palatinus;
                    $data['torus_mandibularis'] = $dataOdontogram->torus_mandibularis;
                    $data['palatum'] = $dataOdontogram->palatum;
                    $data['diastema'] = $dataOdontogram->diastema;
                    $data['diastema_lainnya'] = $dataOdontogram->diastema_lainnya;
                    $data['gigi_anomali'] = $dataOdontogram->gigi_anomali;
                    $data['gigi_anomali_lainnya'] = $dataOdontogram->gigi_anomali_lainnya;
                    $data['lain_lain'] = $dataOdontogram->lain_lain;
                    $data['d'] = $dataOdontogram->d;
                    $data['m'] = $dataOdontogram->m;
                    $data['f'] = $dataOdontogram->f;
                    $data['jumlah_foto'] = $dataOdontogram->jumlah_foto;
                    $data['foto'] = $dataOdontogram->foto;
                    $data['jumlah_rontgen'] = $dataOdontogram->jumlah_rontgen;
                    $data['rontgen'] = $dataOdontogram->rontgen;
                    $data['gambar_gigi'] = base64_encode($dataOdontogram->gambar_gigi);
                    $data['gambar_mulut'] = base64_encode($dataOdontogram->gambar_mulut);

    				echo json_encode(array(
    					'status' => 'success',
                        'data' => $data
                    ));
    			}else{
                    echo json_encode(array(
    					'status' => 'success',
                        'data' => $data
                    ));
                }
            }else if($param == 'count'){
                $result = $this->OdontogramModel->get_count();;
                echo json_encode($result);
            }
            //else if($param == 'tolak'){
    		// 	$post = $this->input->post(NULL,TRUE);
    		// 	if(!empty($post['kunjungan'])){
            //         $data = array(
            //             'KUNJUNGAN' => $post['kunjungan'],
            //             'NOMOR_LAB' => $post['nolab'],
            //             'CATATAN' => $post['catatan'],
            //             'OLEH' => $this->session->userdata('id'),
            //         );
    		// 		$this->Batal_histologi_model->insert($data);
            //         $result = array('status' => 'success');
    		// 	}

    		// 	echo json_encode($result);
    		// }else if($param == 'aktif'){
    		// 	$post = $this->input->post(NULL,TRUE);
    		// 	if(!empty($post['id'])){
            //         if($post['status'] == '1'){
        	// 			$data = array(
        	// 				'STATUS' => '2'
        	// 			);
            //         }else{
            //             $data = array(
            //                 'STATUS' => '1'
            //             );
            //         }
    		// 		$this->Histologi_model->update($data, array('ID' => $post['id']));
            //         $result = array('status' => 'success');
    		// 	}

    		// 	echo json_encode($result);
    		// }
    	}
    }

    public function datatables(){
        $result = $this->OdontogramModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_odontogram" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> TANGGAL_ODONTO;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;      
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->OdontogramModel->total_count(),
            "recordsFiltered"   => $this->OdontogramModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}