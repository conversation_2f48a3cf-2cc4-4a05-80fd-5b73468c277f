<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SkalaMorse extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('masterModel', 'pengkajianAwalModel'));
  }

  public function index()
  {
    $post = $this->input->post();
    $idSkalaMorse = $this->uri->segment(8);
    $nomr = $this->uri->segment(4);
    $historySkalaMorse = $this->pengkajianAwalModel->historySkalaMorse($nomr);
    $getSkalaMorse = $this->pengkajianAwalModel->getSkalaMorse($idSkalaMorse);

    $data = array(
      'historySkalaMorse'    => $historySkalaMorse,
      'getSkalaMorse'        => $getSkalaMorse
    );

    $this->load->view('Pengkajian/igd/skalaMorse/index', $data);
  }

  public function simpanSkalaMorse()
  {
    $post = $this->input->post();
    $date = $this->input->post('tanggal');
    $jam = $this->input->post('jam');
    $diagnosisMedis = $this->input->post('diagnosisMedisSkalaMorse');
    $riwayatJatuhBaruIniSkalaMorse = $this->input->post('riwayatJatuhBaruIniSkalaMorse');
    $mengalamiAgitasiSkalaMorse = $this->input->post('mengalamiAgitasiSkalaMorse');
    $gangguanPenglihatanSkalaMorse = $this->input->post('gangguanPenglihatanSkalaMorse');
    $perluKetoiletSkalaMorse = $this->input->post('perluKetoiletSkalaMorse');
    $mampuMobilisasiAmanSkalaMorse = $this->input->post('mampuMobilisasiAmanSkalaMorse');
    $mampuMobilisasiTidakStabilSkalaMorse = $this->input->post('mampuMobilisasiTidakStabilSkalaMorse');
    $tidakMampuBerjalanSkalaMorse = $this->input->post('tidakMampuBerjalanSkalaMorse');
    // $tidakMampuBerjalanSkalaMorse = isset($post['tidakMampuBerjalanSkalaMorse']) ? "1" : null,
    $totalSkalaMorse = $this->input->post('totalSkalaMorse');
    $nokun = $this->input->post('nokun');
    $oleh = $this->input->post('pengguna');
    $id = $this->input->post('idSkalaMorse');

    $tglSkalaMorse = date('Y-m-d', strtotime($date));

    $data = array(
      'nokun'                            => $nokun,
      'tanggal'                          => $tglSkalaMorse,
      'jam'                              => $jam,
      'diagnosis_medis'                  => $diagnosisMedis,
      'riwayat_jatuh_baru'               => $riwayatJatuhBaruIniSkalaMorse,
      'mengalami_agitasi'                => $mengalamiAgitasiSkalaMorse,
      'gangguan_penglihatan'             => $gangguanPenglihatanSkalaMorse,
      'perlu_ketoilet'                   => $perluKetoiletSkalaMorse,
      'mobilisasi_aman'                  => $mampuMobilisasiAmanSkalaMorse,
      'mobilisasi_mandiri'               => $mampuMobilisasiTidakStabilSkalaMorse,
      'tidak_mampu_berjalan'             => isset($post['tidakMampuBerjalanSkalaMorse']) ? "1" : null,
      'total_skala_morse'                => $totalSkalaMorse,
      'oleh'                             => $oleh,
      'status'                           => 1
    );
    // echo'<pre>';print_r($data);exit();
    
    if (!empty($id)) {
      $this->db->where('id', $id);
      $this->db->update('keperawatan.tb_penilaian_risiko_skala_morse', $data);
    }else{
      $this->db->insert('keperawatan.tb_penilaian_risiko_skala_morse', $data);
      $idSkalaMorse = $this->db->insert_id();
    }
  }

  public function simpanPencegahanRisikoJatuh()
  {
    $post = $this->input->post();
    $id = $this->input->post('idSkalaMorse');
    $date = $this->input->post('tanggal_ViewFormSkalaMorse');
    $jam = $this->input->post('jam_ViewFormSkalaMorse');
    $oleh = $this->input->post('pengguna');

    $tglSkalaMorse = date('Y-m-d', strtotime($date));

    $data = array(
      'id_pengkajian'                  => $id,
      'tanggal'                        => $tglSkalaMorse,
      'jam'                            => $jam,
      'jenis_pengkajian'               => 2,
      'resiko_rendah'                  => isset($post["pencegahanrisikojatuhrendahSkalaMorse"]) ? implode(',',$post["pencegahanrisikojatuhrendahSkalaMorse"]) : "",
      'resiko_tinggi'                  => isset($post["pencegahanrisikojatuhtinggiSkalaMorse"]) ? implode(',',$post["pencegahanrisikojatuhtinggiSkalaMorse"]) : "",
      'oleh'                           => $oleh,
      'status'                         => 1
    );
    // echo'<pre>';print_r($data);echo "</pre>";exit();
    $this->db->insert('keperawatan.tb_pencegahan_pasien_jatuh', $data);
    $idSkalaMorse = $this->db->insert_id();
  }

  public function editSkalaMorse()
  {
    $post = $this->input->post();
    $id = $this->input->post('idSkalaMorse_edit');
    $idSkalaMorse = $this->input->post('idSkalaMorse');
    $date = $this->input->post('tanggal_editFormSkalaMorse');
    $jam = $this->input->post('jam_editFormSkalaMorse');
    $oleh = $this->input->post('pengguna');

    $tglSkalaMorse = date('Y-m-d', strtotime($date));

    $data = array(
      'id_pengkajian'                  => $idSkalaMorse,
      'tanggal'                        => $tglSkalaMorse,
      'jam'                            => $jam,
      'jenis_pengkajian'               => 2,
      'resiko_rendah'                  => implode(',',$post["pencegahanrisikojatuhrendahSkalaMorseEdit"]),
      // 'resiko_tinggi'                  => implode(',',$post["pencegahanrisikojatuhtinggiSkalaMorse"]),
      'resiko_tinggi'                  => isset($post["pencegahanrisikojatuhtinggiSkalaMorseEdit"]) ? implode(',',$post["pencegahanrisikojatuhtinggiSkalaMorseEdit"]) : "",
      'oleh'                           => $oleh,
      'status'                         => 1
    );
    // echo'<pre>';print_r($data);exit();
    // $this->db->insert('keperawatan.tb_pencegahan_pasien_jatuh', $data);
    // $idSkalaMorse = $this->db->insert_id();
     $this->db->where('id', $id);
      $this->db->update('keperawatan.tb_pencegahan_pasien_jatuh', $data);
  }

  ///////////// Modal view pelaksanaan pencegahan pasien jatuh ////////////////
  public function viewHasilPelaksanaanPencegahanPasienJatuh()
  {
    $nokun = $this->input->post('nokun');
    $idview = $this->input->post('idview');
    $norm = $this->input->post('norm');
    $total = $this->input->post('total');
    $getDataPasien = $this->pengkajianAwalModel->getNomr($nokun);
    $historyPencegahanSkalaMorse = $this->pengkajianAwalModel->hisPencegahanSkalaMorse($norm);
    $listIntervensiPencegahanJatuhRisikoRendah = $this->masterModel->referensi(1012);
    $listIntervensiPencegahanJatuhRisikoTinggi = $this->masterModel->referensi(1013);

    $data = array(
      'getPasien' => $getDataPasien,
      'idview' => $idview,
      'historyPencegahanSkalaMorse' => $historyPencegahanSkalaMorse,
      'total' => $total,
      'listIntervensiPencegahanJatuhRisikoRendah' => $listIntervensiPencegahanJatuhRisikoRendah,
      'listIntervensiPencegahanJatuhRisikoTinggi' => $listIntervensiPencegahanJatuhRisikoTinggi,
      'nokun' => $nokun
    );

    $this->load->view('Pengkajian/igd/skalaMorse/modalFormPelaksanaanPencegahanPasienJatuh', $data);
  }

  ///////////// Modal view edit pelaksanaan pencegahan pasien jatuh ////////////////
  public function viewEditPencegahanSkalaMorse()
  {
    $idhis = $this->input->post('idhis');
    $id = $this->input->post('id');
    $total = $this->input->post('total');
    $getSkalaMorse = $this->pengkajianAwalModel->getPencegahanSkalaMorse($idhis);
    $listIntervensiPencegahanJatuhRisikoRendah = $this->masterModel->referensi(1012);
    $listIntervensiPencegahanJatuhRisikoTinggi = $this->masterModel->referensi(1013);
    $explode_rendah       = explode(',' , $getSkalaMorse['resiko_rendah']);
    $explode_tinggi       = explode(',' , $getSkalaMorse['resiko_tinggi']);

    $data = array(
      'idhis' => $idhis,
      'id' => $id,
      'total' => $total,
      'listIntervensiPencegahanJatuhRisikoRendah' => $listIntervensiPencegahanJatuhRisikoRendah,
      'listIntervensiPencegahanJatuhRisikoTinggi' => $listIntervensiPencegahanJatuhRisikoTinggi,
      'explode_rendah' => $explode_rendah,
      'explode_tinggi' => $explode_tinggi,
      'getSkalaMorse' => $getSkalaMorse
    );

    $this->load->view('Pengkajian/igd/skalaMorse/editPencegahanSkalaMorse', $data);
  }


}

/* End of file Pews.php */
/* Location: ./application/controllers/igd/Pews.php */
