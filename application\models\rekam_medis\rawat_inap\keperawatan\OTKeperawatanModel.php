<?php
defined('BASEPATH') or exit('No direct script access allowed');

class OTKeperawatanModel extends MY_Model
{
  protected $_table_name = 'keperawatan.tb_observasi_tindakan';
  protected $_primary_key = 'id';
  protected $_order_by = 'id';
  protected $_order_by_type = 'DESC';

  var $tabel = 'keperawatan.tb_observasi_tindakan ot';
  var $urutan_kolom = array(
    null,
    'tanggal',
    'jam',
    'kesadaran',
    'td_sistolik',
    'td_diastolik',
    'nadi',
    'pernapasan',
    'suhu',
    'imt',
    'lingkar_perut',
    'tanggal_ews',
    'waktu_ews',
    'saturasi_o2',
    'penggunaan_o2',
    'score_ews',
    'cvp',
    'wsd',
    'perifer',
    'total_oral',
    'total_ngt_pemasukan',
    'total_transfusi',
    'total_parenteral',
    'muntah',
    'ngt_pengeluaran',
    'bak',
    'jenis_bab',
    'bab',
    'pendarahan',
    'total_wsd_drain',
    'dinas',
    'jam_dinas',
    'iwl',
    'balance',
    'igds',
    'lainnya',
    'balance_24_jam',
    'perawat',
    null
  );
  var $pencarian_kolom = array(
    'ot.tanggal',
    'ot.jam',
    'v.variabel',
    'tv.td_sistolik',
    'tv.td_diastolik',
    'tv.nadi',
    'tv.pernapasan',
    'tv.suhu',
    'ot.imt',
    'ot.lingkar_perut',
    'o2.tanggal',
    'o2.waktu',
    'o2.saturasi_o2',
    'o2.penggunaan_o2',
    'e.score_ews',
    'ot.cvp',
    'ot.wsd',
    'ot.perifer',
    'ot.muntah',
    'ot.ngt_pengeluaran',
    'ot.bak',
    'ot.jenis_bab',
    'ot.bab',
    'ot.pendarahan',
    'd.variabel',
    'ot.jam_dinas',
    'ot.iwl',
    'ot.balance',
    'ot.igds',
    'ot.lainnya',
    'ot.balance_24_jam',
    'master.getNamaLengkapPegawai(peng.NIP)'
  );
  var $urutan = array(
    'ot.tanggal' => 'desc',
    'ot.jam' => 'desc'
  );

  public $rules = array(
    'nokun' => array(
      'field' => 'nokun',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib Diisi.',
        'numeric' => '%s Wajib Angka',
      )
    ),
  );

  function __construct()
  {
    parent::__construct();
  }

  public function intervensi($nomr)
  {
    $this->db->select('akd.ID, akd.DESKRIPSI');
    $this->db->from('db_master.tb_asuhan_keperawatan_detil akd');
    $this->db->join(
      'keperawatan.tb_perencanaan_asuhan_keperawatan pak',
      'pak.id_asuhan_keperawatan_detil = akd.ID',
      'left'
    );
    $this->db->join('db_master.tb_asuhan_keperawatan ak', 'ak.ID = akd.ID_ASUHAN', 'left');
    $this->db->where('pak.id_emr', $nomr);
    $this->db->where('pak.status', '1');
    $this->db->where('akd.JENIS', '3');
    $query = $this->db->get();
    return $query->result_array();
  }

  public function simpanObservasi($dataObservasi)
  {
    $this->db->insert('keperawatan.tb_observasi_tindakan', $dataObservasi);
    return $this->db->insert_id();
  }

  public function ubahObservasi($idObservasi, $dataObservasi)
  {
    $this->db->where('keperawatan.tb_observasi_tindakan.id', $idObservasi);
    $this->db->update('keperawatan.tb_observasi_tindakan', $dataObservasi);
  }

  public function simpanKesadaran($dataKesadaran)
  {
    $this->db->insert('db_pasien.tb_kesadaran', $dataKesadaran);
    return $this->db->insert_id();
  }

  public function ubahKesadaran($idObservasi, $dataSource, $dataKesadaran)
  {
    $this->db->where('db_pasien.tb_kesadaran.ref', $idObservasi)->or_where('db_pasien.tb_kesadaran.id_otk', $idObservasi);
    $this->db->where('db_pasien.tb_kesadaran.data_source', $dataSource);
    $this->db->update('db_pasien.tb_kesadaran', $dataKesadaran);
  }

  public function updatePakaiIDKesadaran($idKesadaran, $data)
  {
    $this->db->where('db_pasien.tb_kesadaran.id', $idKesadaran);
    $this->db->update('db_pasien.tb_kesadaran', $data);
  }

  public function ubahTbBb($idObservasi, $dataSource, $dataTbBb)
  {
    $this->db->where('db_pasien.tb_tb_bb.ref', $idObservasi)->or_where('db_pasien.tb_tb_bb.id_otk', $idObservasi);
    $this->db->where('db_pasien.tb_tb_bb.data_source', $dataSource);
    $this->db->update('db_pasien.tb_tb_bb', $dataTbBb);
  }

  public function updatePakaiIDTbBb($idTbBb, $data)
  {
    $this->db->where('db_pasien.tb_tb_bb.id', $idTbBb);
    $this->db->update('db_pasien.tb_tb_bb', $data);
  }

  public function simpanTandaVital($dataTandaVital)
  {
    $this->db->insert('db_pasien.tb_tanda_vital', $dataTandaVital);
    return $this->db->insert_id();
  }

  public function ubahTandaVital($idObservasi, $dataSource, $dataTandaVital)
  {
    $this->db->where('db_pasien.tb_tanda_vital.ref', $idObservasi)->or_where('db_pasien.tb_tanda_vital.id_otk', $idObservasi);
    $this->db->where('db_pasien.tb_tanda_vital.data_source', $dataSource);
    $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
  }

  public function updatePakaiIDTandaVital($idTandaVital, $data)
  {
    $this->db->where('db_pasien.tb_tanda_vital.id', $idTandaVital);
    $this->db->update('db_pasien.tb_tanda_vital', $data);
  }

  public function ubahTandaVitalSedAnes($id_pra, $hasilPengkajian, $dataTandaVital)
  {
    $this->db->where('db_pasien.tb_tanda_vital.ref', $id_pra);
    $this->db->where('db_pasien.tb_tanda_vital.data_source', $hasilPengkajian);
    $this->db->update('db_pasien.tb_tanda_vital', $dataTandaVital);
  }

  public function simpanOksigen($dataOksigen)
  {
    $this->db->insert('db_pasien.tb_o2', $dataOksigen);
    return $this->db->insert_id();
  }

  public function ubahOksigen($idObservasi, $dataSource, $dataOksigen)
  {
    $this->db->where('db_pasien.tb_o2.ref', $idObservasi)->or_where('db_pasien.tb_o2.id_otk', $idObservasi);
    $this->db->where('db_pasien.tb_o2.data_source', $dataSource);
    $this->db->update('db_pasien.tb_o2', $dataOksigen);
  }

  public function updatePakaiIDOksigen($idOksigen, $data)
  {
    $this->db->where('db_pasien.tb_o2.id', $idOksigen);
    $this->db->update('db_pasien.tb_o2', $data);
  }

  public function simpanIntervensi($dataIntervensi, $idObservasi)
  {
    $this->db->delete('keperawatan.tb_intervensi_observasi_tindakan', array('id_observasi_tindakan' => $idObservasi));
    $this->db->insert_batch('keperawatan.tb_intervensi_observasi_tindakan', $dataIntervensi);
  }

  public function ubahIntervensi($idObservasi, $dataIntervensi)
  {
    $this->db->where('keperawatan.tb_intervensi_observasi_tindakan.id_observasi_tindakan', $idObservasi);
    $this->db->update('keperawatan.tb_intervensi_observasi_tindakan', $dataIntervensi);
  }

  public function simpanTindakan($dataTindakan, $idObservasi)
  {
    $this->db->delete('keperawatan.tb_observasi_tindakan_detail', array('id_observasi_tindakan' => $idObservasi));
    $this->db->insert_batch('keperawatan.tb_observasi_tindakan_detail', $dataTindakan);
  }

  public function ubahTindakan($idObservasi, $dataTindakan)
  {
    $this->db->where('keperawatan.tb_observasi_tindakan_detail.id_observasi_tindakan', $idObservasi);
    $this->db->update('keperawatan.tb_observasi_tindakan_detail', $dataTindakan);
  }

  public function tindakanKeperawatan($idAsuhanKeperawatan)
  {
    $this->db->select('tk.ID, tk.TINDAKAN_KEPERAWATAN');
    $this->db->from('db_master.tb_tindakan_keperawatan tk');
    $this->db->join('db_master.tb_kategori_asuhan_keperawatan kak', 'kak.ID = tk.ID_KATEGORI_ASKEP', 'left');
    $this->db->join('db_master.tb_maping_tindakan_keperawatan mtk', 'mtk.ID_KATEGORI = kak.ID', 'left');
    $this->db->join('db_master.tb_asuhan_keperawatan ak', 'ak.ID = mtk.ID_ASUHAN', 'left');
    $this->db->where('ak.ID_VARIABEL', $idAsuhanKeperawatan);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function history($nokun = null, $nomr = null)
  {
    $this->db->select(
      'ot.id, ot.tanggal, ot.jam, IF(v.variabel IS NULL, vx.variabel, v.variabel) kesadaran,
      IF(tv.td_sistolik IS NULL, tvx.td_sistolik, tv.td_sistolik) td_sistolik,
      IF(tv.td_diastolik IS NULL, tvx.td_diastolik, tv.td_diastolik) td_diastolik,
      IF(tv.nadi IS NULL, tvx.nadi, tv.nadi) nadi, IF(tv.pernapasan IS NULL, tvx.pernapasan, tv.pernapasan) pernapasan,
      IF(tv.suhu IS NULL, tvx.suhu, tv.suhu) suhu, ot.imt, ot.lingkar_perut,
      IF(o2.tanggal IS NULL, o2x.tanggal, o2.tanggal) tanggal_ews, IF(o2.waktu IS NULL, o2x.waktu, o2.waktu) waktu_ews,
      IF(o2.saturasi_o2 IS NULL, o2x.saturasi_o2, o2.saturasi_o2) saturasi_o2,
      IF(o2.penggunaan_o2 IS NULL, o2x.penggunaan_o2, o2.penggunaan_o2) penggunaan_o2,
      IF(e.score_ews IS NULL, ex.score_ews, e.score_ews) score_ews, ot.cvp, ot.wsd, ot.perifer,
      (ot.oral + ot.oral_2 + ot.oral_3) total_oral,
      (ot.ngt_pemasukan + ot.ngt_pemasukan_2 + ot.ngt_pemasukan_3) total_ngt_pemasukan,
      (ot.transfusi + ot.transfusi_2 + ot.transfusi_3) total_transfusi,
      (ot.parenteral + ot.parenteral_2 + ot.parenteral_3 + ot.parenteral_4 + ot.parenteral_5) total_parenteral,
      ot.muntah, ot.ngt_pengeluaran, ot.bak, ot.jenis_bab, ot.bab, ot.pendarahan,
      (ot.wsd_drain + ot.wsd_drain_2 + ot.wsd_drain_3 + ot.wsd_drain_4) total_wsd_drain, d.variabel dinas,
      ot.jam_dinas, ot.iwl, ot.balance, ot.igds, ot.lainnya, ot.balance_24_jam,
      master.getNamaLengkapPegawai(peng.NIP) perawat, ot.status'
    );
    $this->db->from($this->tabel);
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ot.NOKUN', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join(
      'db_pasien.tb_kesadaran k',
      'CAST(ot.id AS char) = k.ref AND k.data_source = 5 AND ot.nokun = k.nokun',
      'left'
    ); // Kesadaran lama
    $this->db->join(
      'db_pasien.tb_kesadaran kx',
      'CAST(ot.id AS char) = kx.id_otk AND kx.data_source = 5 AND ot.nokun = kx.nokun',
      'left'
    ); // Kesadaran baru
    $this->db->join(
      'db_pasien.tb_tanda_vital tv',
      'CAST(ot.id AS char) = tv.ref AND tv.data_source = 5 AND ot.nokun = tv.nokun',
      'left'
    ); // Tanda vital lama
    $this->db->join(
      'db_pasien.tb_tanda_vital tvx',
      'CAST(ot.id AS char) = tvx.id_otk AND tvx.data_source = 5 AND ot.nokun = tvx.nokun',
      'left'
    ); // Tanda vital baru
    $this->db->join(
      'db_pasien.tb_o2 o2',
      'CAST(ot.id AS char) = o2.ref AND o2.data_source = 5 AND ot.nokun = o2.nokun',
      'left'
    ); // Oksigen lama
    $this->db->join(
      'db_pasien.tb_o2 o2x',
      'CAST(ot.id AS char) = o2x.id_otk AND o2x.data_source = 5 AND ot.nokun = o2x.nokun',
      'left'
    ); // Oksigen baru
    $this->db->join('db_master.variabel v', 'v.id_variabel = k.kesadaran', 'left'); // Variabel kesadaran lama
    $this->db->join('db_master.variabel vx', 'vx.id_variabel = kx.kesadaran', 'left'); // Variabel kesadaran baru
    $this->db->join('db_master.variabel d', 'd.id_variabel = ot.dinas', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = ot.oleh', 'left');
    $this->db->join(
      'keperawatan.tb_ews e',
      'e.id_tanda_vital = tv.id AND e.id_kesadaran = k.id AND e.id_o2 = o2.id AND e.data_source = 5 AND ot.nokun = e.nokun',
      'left'
    ); // EWS lama
    $this->db->join(
      'keperawatan.tb_ews ex',
      'ex.id_tanda_vital = tvx.id AND ex.id_kesadaran = kx.id AND ex.id_o2 = o2x.id AND ex.data_source = 5 AND ot.nokun = ex.nokun',
      'left'
    ); // EWS baru
    if (isset($nokun) && $nokun != '') {
      $this->db->where('ot.nokun', $nokun);
    } elseif (isset($nomr) && $nomr != '') {
      $this->db->where('p.NORM', $nomr);
    }

    $i = 0;
    foreach ($this->pencarian_kolom as $pk) { // Loop kolom
      if (isset($_POST['search']['value']) && !empty($_POST['search']['value'])) {
        $_POST['search']['value'] = $_POST['search']['value'];
      } else {
        $_POST['search']['value'] = '';
      }

      if ($_POST['search']['value']) { // Jika datatable tidak mengirim POST untuk pencarian
        if ($i === 0) { // Loop pertama
          $this->db->group_start();
          $this->db->like($pk, $_POST['search']['value']);
        } else {
          $this->db->or_like($pk, $_POST['search']['value']);
        }

        if (count($this->pencarian_kolom) - 1 == $i) { // Loop terakhir
          $this->db->group_end(); // Tutup kurung
        }
      }
      $i++;
    }

    if (isset($_POST['order'])) { // Pemrosesan order
      $this->db->order_by($this->urutan_kolom[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
    } elseif (isset($this->urutan)) {
      $urutan = $this->urutan;
      $this->db->order_by(key($urutan), $urutan[key($urutan)]);
    }
  }

  public function ambilHistory($nokun = null, $nomr = null)
  {
    if (isset($nokun) && $nokun != '') {
      $this->history($nokun, null);
    } elseif (isset($nomr) && $nomr != '') {
      $this->history(null, $nomr);
    }

    if (isset($_POST['length']) && $_POST['length'] < 1) {
      $_POST['lenght'] = '10';
    } else {
      $_POST['length'] = $_POST['length'];
    }

    if (isset($_POST['start']) && $_POST['start'] > 1) {
      $_POST['start'] = $_POST['start'];
    }

    $this->db->limit($_POST['length'], $_POST['start']);
    // print_r($_POST); die;
    $query = $this->db->get();
    return $query->result();
  }

  public function hitungTersaringHistory($nokun = null, $nomr = null)
  {
    if (isset($nokun) && $nokun != '') {
      $this->history($nokun, null);
    } elseif (isset($nomr) && $nomr != '') {
      $this->history(null, $nomr);
    }

    $query = $this->db->get();
    return $query->num_rows();
  }

  public function hitungSemuaHistory($nokun = null, $nomr = null)
  {
    $this->db->from($this->tabel);
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ot.NOKUN', 'left');
    $this->db->join('pendaftaran.pendaftaran p', 'p.NOMOR = pk.NOPEN', 'left');
    $this->db->join(
      'db_pasien.tb_kesadaran k',
      'CAST(ot.id AS char) = k.ref AND k.data_source = 5 AND ot.nokun = k.nokun',
      'left'
    ); // Kesadaran lama
    $this->db->join(
      'db_pasien.tb_kesadaran kx',
      'CAST(ot.id AS char) = kx.id_otk AND kx.data_source = 5 AND ot.nokun = kx.nokun',
      'left'
    ); // Kesadaran baru
    $this->db->join(
      'db_pasien.tb_tanda_vital tv',
      'CAST(ot.id AS char) = tv.ref AND tv.data_source = 5 AND ot.nokun = tv.nokun',
      'left'
    ); // Tanda vital lama
    $this->db->join(
      'db_pasien.tb_tanda_vital tvx',
      'CAST(ot.id AS char) = tvx.id_otk AND tvx.data_source = 5 AND ot.nokun = tvx.nokun',
      'left'
    ); // Tanda vital baru
    $this->db->join(
      'db_pasien.tb_o2 o2',
      'CAST(ot.id AS char) = o2.ref AND o2.data_source = 5 AND ot.nokun = o2.nokun',
      'left'
    ); // Oksigen lama
    $this->db->join(
      'db_pasien.tb_o2 o2x',
      'CAST(ot.id AS char) = o2x.id_otk AND o2x.data_source = 5 AND ot.nokun = o2x.nokun',
      'left'
    ); // Oksigen baru
    $this->db->join('db_master.variabel v', 'v.id_variabel = k.kesadaran', 'left'); // Variabel kesadaran lama
    $this->db->join('db_master.variabel vx', 'vx.id_variabel = kx.kesadaran', 'left'); // Variabel kesadaran baru
    $this->db->join('db_master.variabel d', 'd.id_variabel = ot.dinas', 'left');
    $this->db->join('aplikasi.pengguna peng', 'peng.ID = ot.oleh', 'left');
    $this->db->join(
      'keperawatan.tb_ews e',
      'e.id_tanda_vital = tv.id AND e.id_kesadaran = k.id AND e.id_o2 = o2.id AND e.data_source = 5 AND ot.nokun = e.nokun',
      'left'
    ); // EWS lama
    $this->db->join(
      'keperawatan.tb_ews ex',
      'ex.id_tanda_vital = tvx.id AND ex.id_kesadaran = kx.id AND ex.id_o2 = o2x.id AND ex.data_source = 5 AND ot.nokun = ex.nokun',
      'left'
    ); // EWS baru
    if (isset($nokun) && $nokun != '') {
      $this->db->where('ot.nokun', $nokun);
    } elseif (isset($nomr) && $nomr != '') {
      $this->db->where('p.NORM', $nomr);
    }

    return $this->db->count_all_results();
  }

  public function balance($jenis, $nomr, $nokun = null, $tanggalMulai = null, $tanggalAkhir = null)
  {
    if ($jenis == 5770) {
      // Balance per hari
      $query = $this->db->query(
        "SELECT a.*, a.pemasukan - (a.pengeluaran + a.iwl) balance_baru
        FROM (
          SELECT
            r.DESKRIPSI ruangan, ot.tanggal, ot.jam, ot.jam_dinas, ot.status, ot.dinas dinas_id,
            SUM(IFNULL(ot.oral, 0) + IFNULL(ot.oral_2, 0) + IFNULL(ot.oral_3, 0) + IFNULL(ot.ngt_pemasukan, 0) + IFNULL(ot.ngt_pemasukan_2, 0) + IFNULL(ot.ngt_pemasukan_3, 0) + IFNULL(ot.transfusi, 0) + IFNULL(ot.transfusi_2, 0) + IFNULL(ot.transfusi_3, 0) + IFNULL(ot.parenteral, 0) + IFNULL(ot.parenteral_2, 0) + IFNULL(ot.parenteral_3, 0) + IFNULL(ot.parenteral_4, 0) + IFNULL(ot.parenteral_5, 0)) pemasukan,
            SUM(IFNULL(ot.muntah, 0) + IFNULL(ot.ngt_pengeluaran, 0) + IFNULL(ot.bak, 0) + IFNULL(ot.bab, 0) + IFNULL(ot.pendarahan, 0) + IFNULL(ot.wsd_drain, 0) + IFNULL(ot.wsd_drain_2, 0) + IFNULL(ot.wsd_drain_3, 0) + IFNULL(ot.wsd_drain_4, 0)) pengeluaran,
            SUM(IFNULL(ot.iwl, 0)) iwl,
            SUM(IFNULL(ot.balance, 0)) balance,
            IF(d.variabel IS NULL, 'lain-lain', d.variabel) dinas
          FROM keperawatan.tb_observasi_tindakan ot
          LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ot.NOKUN
          LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
          LEFT JOIN db_master.variabel d ON d.id_variabel = ot.dinas
          LEFT JOIN aplikasi.pengguna peng ON peng.ID = ot.oleh
          LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
          WHERE p.NORM = '$nomr' AND ot.status != 0 AND ot.balance_24_jam = 1
          GROUP BY ot.tanggal
          ORDER BY ot.updated_at DESC
        ) a"
      );
    } elseif ($jenis == 5769) {
      // Balance per dinas
      $query = $this->db->query(
        "SELECT a.*, a.pemasukan - (a.pengeluaran + a.iwl) balance_baru
        FROM (
          SELECT
            r.DESKRIPSI ruangan, ot.tanggal, ot.jam, ot.jam_dinas, ot.status, ot.dinas dinas_id,
            SUM(IFNULL(ot.oral, 0) + IFNULL(ot.oral_2, 0) + IFNULL(ot.oral_3, 0) + IFNULL(ot.ngt_pemasukan, 0) + IFNULL(ot.ngt_pemasukan_2, 0) + IFNULL(ot.ngt_pemasukan_3, 0) + IFNULL(ot.transfusi, 0) + IFNULL(ot.transfusi_2, 0) + IFNULL(ot.transfusi_3, 0) + IFNULL(ot.parenteral, 0) + IFNULL(ot.parenteral_2, 0) + IFNULL(ot.parenteral_3, 0) + IFNULL(ot.parenteral_4, 0) + IFNULL(ot.parenteral_5, 0)) pemasukan,
            SUM(IFNULL(ot.muntah, 0) + IFNULL(ot.ngt_pengeluaran, 0) + IFNULL(ot.bak, 0) + IFNULL(ot.bab, 0) + IFNULL(ot.pendarahan, 0) + IFNULL(ot.wsd_drain, 0) + IFNULL(ot.wsd_drain_2, 0) + IFNULL(ot.wsd_drain_3, 0) + IFNULL(ot.wsd_drain_4, 0)) pengeluaran,
            SUM(IFNULL(ot.iwl, 0)) iwl,
            SUM(IFNULL(ot.balance, 0)) balance,
            IF(d.variabel IS NULL, 'lain-lain', d.variabel) dinas
          FROM keperawatan.tb_observasi_tindakan ot
          LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ot.NOKUN
          LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
          LEFT JOIN db_master.variabel d ON d.id_variabel = ot.dinas
          LEFT JOIN aplikasi.pengguna peng ON peng.ID = ot.oleh
          LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
          WHERE p.NORM = '$nomr' AND ot.status != 0
          GROUP BY ot.tanggal, ot.dinas
          ORDER BY ot.updated_at DESC
        ) a"
      );
    } elseif ($jenis == 5768) {
      // Balance per nokun
      $query = $this->db->query(
        "SELECT a.*, a.pemasukan - (a.pengeluaran + a.iwl) balance_baru
        FROM (
          SELECT
            r.DESKRIPSI ruangan, ot.tanggal, ot.jam, ot.jam_dinas, ot.status, ot.dinas dinas_id,
            SUM(IFNULL(ot.oral, 0) + IFNULL(ot.oral_2, 0) + IFNULL(ot.oral_3, 0) + IFNULL(ot.ngt_pemasukan, 0) + IFNULL(ot.ngt_pemasukan_2, 0) + IFNULL(ot.ngt_pemasukan_3, 0) + IFNULL(ot.transfusi, 0) + IFNULL(ot.transfusi_2, 0) + IFNULL(ot.transfusi_3, 0) + IFNULL(ot.parenteral, 0) + IFNULL(ot.parenteral_2, 0) + IFNULL(ot.parenteral_3, 0) + IFNULL(ot.parenteral_4, 0) + IFNULL(ot.parenteral_5, 0)) pemasukan,
            SUM(IFNULL(ot.muntah, 0) + IFNULL(ot.ngt_pengeluaran, 0) + IFNULL(ot.bak, 0) + IFNULL(ot.bab, 0) + IFNULL(ot.pendarahan, 0) + IFNULL(ot.wsd_drain, 0) + IFNULL(ot.wsd_drain_2, 0) + IFNULL(ot.wsd_drain_3, 0) + IFNULL(ot.wsd_drain_4, 0)) pengeluaran,
            SUM(IFNULL(ot.iwl, 0)) iwl,
            SUM(IFNULL(ot.balance, 0)) balance,
            IF(d.variabel IS NULL, 'lain-lain', d.variabel) dinas
          FROM keperawatan.tb_observasi_tindakan ot
          LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ot.NOKUN
          LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
          LEFT JOIN db_master.variabel d ON d.id_variabel = ot.dinas
          LEFT JOIN aplikasi.pengguna peng ON peng.ID = ot.oleh
          LEFT JOIN master.ruangan r ON r.ID = pk.RUANGAN
          WHERE p.NORM = '$nomr' AND ot.status != 0
          GROUP BY ot.nokun
          ORDER BY ot.updated_at DESC
        ) a"
      );
    } elseif ($jenis == '24 jam') {
      $query = $this->db->query(
        "SELECT
          ot1.tanggal_concat, MAX(ot1.tanggal_concat) tanggal_terakhir, SUM(ot1.oral) oral,
          SUM(ot1.oral_2) oral_2, SUM(ot1.oral_3) oral_3, SUM(ot1.ngt_pemasukan) ngt_pemasukan,
          SUM(ot1.ngt_pemasukan_2) ngt_pemasukan_2, SUM(ot1.ngt_pemasukan_3) ngt_pemasukan_3,
          SUM(ot1.transfusi) transfusi, SUM(ot1.transfusi_2) transfusi_2, SUM(ot1.transfusi_3) transfusi_3,
          SUM(ot1.parenteral) parenteral, SUM(ot1.parenteral_2) parenteral_2,
          SUM(ot1.parenteral_3) parenteral_3, SUM(ot1.parenteral_4) parenteral_4,
          SUM(ot1.parenteral_5) parenteral_5, SUM(ot1.muntah) muntah,
          SUM(ot1.ngt_pengeluaran) ngt_pengeluaran, SUM(ot1.bak) bak, ot1.jenis_bab,
          SUM(IF(ot1.jenis_bab = 5554, ot1.bab, 0)) bab, SUM(ot1.pendarahan) pendarahan,
          SUM(ot1.wsd_drain) wsd_drain, SUM(ot1.wsd_drain_2) wsd_drain_2, SUM(ot1.wsd_drain_3) wsd_drain_3,
          SUM(ot1.wsd_drain_4) wsd_drain_4
        FROM (
          SELECT
            STR_TO_DATE(CONCAT(ot2.tanggal, ' ', ot2.jam), '%Y-%m-%d %H:%i:%s') tanggal_concat, ot2.oral, ot2.oral_2,
            ot2.oral_3, ot2.ngt_pemasukan, ot2.ngt_pemasukan_2, ot2.ngt_pemasukan_3, ot2.transfusi, ot2.transfusi_2,
            ot2.transfusi_3, ot2.parenteral, ot2.parenteral_2, ot2.parenteral_3, ot2.parenteral_4, ot2.parenteral_5,
            ot2.muntah, ot2.ngt_pengeluaran, ot2.bak, ot2.jenis_bab, ot2.bab, ot2.pendarahan, ot2.wsd_drain,
            ot2.wsd_drain_2, ot2.wsd_drain_3, ot2.wsd_drain_4
          FROM keperawatan.tb_observasi_tindakan ot2
          LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = ot2.nokun
          LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
          WHERE ot2.status != 0 AND pk.NOMOR = '$nokun'
          ORDER BY ot2.tanggal DESC, ot2.jam DESC
          ) ot1
        WHERE ot1.tanggal_concat BETWEEN '$tanggalMulai' AND '$tanggalAkhir'"
      );
    }
    return $query->result_array();
  }

  public function detail($id)
  {
    $this->db->select(
      "(
        SELECT GROUP_CONCAT(akd.DESKRIPSI SEPARATOR '~')
        FROM keperawatan.tb_intervensi_observasi_tindakan otd
        LEFT JOIN keperawatan.tb_perencanaan_asuhan_keperawatan pak ON pak.id = otd.id_intervensi
        LEFT JOIN db_master.tb_asuhan_keperawatan_detil akd ON akd.ID = pak.id
        WHERE otd.id_observasi_tindakan = ot.id
      ) intervensi,
      (
        SELECT GROUP_CONCAT(pak.TINDAKAN_KEPERAWATAN SEPARATOR '~')
        FROM keperawatan.tb_observasi_tindakan_detail otd
        LEFT JOIN db_master.tb_tindakan_keperawatan pak ON pak.id = otd.id_pak
        WHERE otd.id_observasi_tindakan = ot.id
      ) tinkep,
      ot.tindakan_keperawatan"
    );
    $this->db->from('keperawatan.tb_observasi_tindakan ot');
    $this->db->where('ot.id', $id);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function detailIntervensi($id)
  {
    $this->db->select('akd.DESKRIPSI');
    $this->db->from('keperawatan.tb_intervensi_observasi_tindakan iot');
    $this->db->join('db_master.tb_asuhan_keperawatan_detil akd', 'akd.ID = iot.id_intervensi', 'left');
    $this->db->join('db_master.tb_asuhan_keperawatan ak', 'ak.ID = akd.ID_ASUHAN', 'left');
    $this->db->where('iot.id_observasi_tindakan', $id);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function detailTindakan($id)
  {
    $this->db->select('pak.TINDAKAN_KEPERAWATAN, otd.waktu');
    $this->db->from('keperawatan.tb_observasi_tindakan_detail otd');
    $this->db->join('db_master.tb_tindakan_keperawatan pak', 'pak.id = otd.id_pak', 'left');
    $this->db->join('keperawatan.tb_observasi_tindakan ot', 'ot.id = otd.id_observasi_tindakan', 'left');
    $this->db->where('ot.id', $id);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function detailTindakanLain($id)
  {
    $this->db->select('ot.tindakan_keperawatan, ot.waktu_tindakan_keperawatan_lainnya');
    $this->db->from('keperawatan.tb_observasi_tindakan ot');
    $this->db->where('ot.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }

  public function detailAnyelir($id)
  {
    $this->db->select('v.variabel');
    $this->db->from('keperawatan.tb_keperawatan_anyelir ka');
    $this->db->join('db_master.variabel v', 'v.id_variabel = ka.tindakan', 'left');
    $this->db->join('keperawatan.tb_observasi_tindakan ot', 'ot.id = ka.id_observasi_tindakan', 'left');
    $this->db->where('ot.id', $id);
    $query = $this->db->get();
    return $query->result_array();
  }

  public function cairan($id)
  {
    $this->db->select(
      'ot.id, ot.oral, ot.ket_oral, ot.oral_2, ot.ket_oral_2, ot.oral_3, ot.ket_oral_3, ot.ngt_pemasukan,
      ot.ket_ngt_pemasukan, ot.ngt_pemasukan_2, ot.ket_ngt_pemasukan_2, ot.ngt_pemasukan_3, ot.ket_ngt_pemasukan_3,
      ot.transfusi, ot.ket_transfusi, ot.transfusi_2, ot.ket_transfusi_2, ot.transfusi_3, ot.ket_transfusi_3,
      ot.ket_transfusi_5, ot.parenteral, ot.ket_parenteral, ot.parenteral_2, ot.ket_parenteral_2, ot.parenteral_3,
      ot.ket_parenteral_3, ot.parenteral_4, ot.ket_parenteral_4, ot.parenteral_5, ot.ket_parenteral_5, ot.muntah,
      ot.ket_muntah, ot.ngt_pengeluaran, ot.ket_ngt_pengeluaran, ot.bak, ot.ket_bak, ot.jenis_bab, ot.bab, ot.ket_bab,
      ot.pendarahan, ot.ket_pendarahan, ot.wsd_drain, ot.ket_wsd_drain, ot.wsd_drain_2, ot.ket_wsd_drain_2,
      ot.wsd_drain_3, ot.ket_wsd_drain_3, ot.wsd_drain_4, ot.ket_wsd_drain_4, ot.iwl'
    );
    $this->db->from('keperawatan.tb_observasi_tindakan ot');
    $this->db->where('ot.id', $id);
    $query = $this->db->get();
    return $query->row_array();
  }
}

/* End of file OTKeperawatanModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel.php */