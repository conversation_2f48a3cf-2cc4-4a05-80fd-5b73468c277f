<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ReevaluasiRad extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('reevaluasi/ReevaluasiRadModel'));
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->ReevaluasiRadModel->rules;
                $this->form_validation->set_rules($rules);
                if($this->input->post('ruangan') == 1){
                    $this->form_validation->set_rules($this->ReevaluasiRadModel->rules_rawat_jalan);
                }elseif($this->input->post('ruangan') == 2){
                    $this->form_validation->set_rules($this->ReevaluasiRadModel->rules_rawat_inap);
                }

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();

                    $data = array(
                        'kunjungan' => $post['nokun'],
                        'rawat' => isset($post['ruangan']) ? $post['ruangan'] : 0,
                        'ruangan' => $post['ruangan'] == 1 ? $post['reevaluasi_rad_jalan_ruangan'] : $post['reevaluasi_rad_inap_ruangan'],
                        'rs_perujuk' => $post['rs_perujuk'],
                        'ketersediaan_foto' => isset($post['ketersediaan_foto']) ? $post['ketersediaan_foto'] : null,
                        'jelaskan_ketersediaan_foto' => isset($post['jelaskan_ketersediaan_foto']) ? $post['jelaskan_ketersediaan_foto'] : null,
                        'jenis_pemeriksaan' => isset($post['jenis_pemeriksaan']) ? $post['jenis_pemeriksaan'] : null,
                        'waktu_pemeriksaan' => isset($post['waktu_pemeriksaan']) ? $post['waktu_pemeriksaan'] : null,
                        'indikasi_klinis' => isset($post['indikasi_klinis']) ? $post['indikasi_klinis'] : null,
                        'oleh' => $this->session->userdata("id"),
                    );

    				if($this->db->replace('medis.tb_reevaluasi_rad',$data)){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
            }else if($param == 'ambil'){
    			$post = $this->input->post(NULL,TRUE);
                $dataReevaluasi = $this->ReevaluasiRadModel->get($post['nokun'], true);
                $data = array();
    			if(!empty($dataReevaluasi)){
    				echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataReevaluasi
                    ));
    			}else{
                    echo json_encode(array(
    					'status' => 'success',
                        'data' => $dataReevaluasi
                    ));
                }
            }else if($param == 'count'){
                $result = $this->ReevaluasiRadModel->get_count();;
                echo json_encode($result);
            }
    	}
    }

    public function datatables(){
        $result = $this->ReevaluasiRadModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm history_reevaluasi_rad" data-id="'.$row -> NOKUN.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = $row -> TANGGAL_ODONTO;
            $sub_array[] = $row -> RUANGAN_KUNJUNGAN;
            $sub_array[] = $row -> DPJP;
            $sub_array[] = $row -> USER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->ReevaluasiRadModel->total_count(),
            "recordsFiltered"   => $this->ReevaluasiRadModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}
