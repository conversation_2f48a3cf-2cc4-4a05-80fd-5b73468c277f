<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CSOASEMA extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/rawat_inap/invasifAnestesi/CSOASEMAModel'
      )
    );
  }

  public function index()
  {
    $pasien = $this->pengkajianAwalModel->getNomr($this->uri->segment(2));
    $data = array(
      'pasien' => $pasien,
      'jumlah' => $this->CSOASEMAModel->history($pasien['NORM'], 'jumlah'),
      'scope' => $this->masterModel->referensi(1525),
      'tube' => $this->masterModel->referensi(1526),
      'airwayDevice' => $this->masterModel->referensi(1527),
      'introducer' => $this->masterModel->referensi(1529),
      'connection' => $this->masterModel->referensi(1530),
      'suction' => $this->masterModel->referensi(1531),
      'sambungSelang' => $this->masterModel->referensi(1533),
      'bagging' => $this->masterModel->referensi(1534),
      'ventilator' => $this->masterModel->referensi(1535),
      'vaporizer' => $this->masterModel->referensi(1536),
      'absorber' => $this->masterModel->referensi(1537),
      'corrigated' => $this->masterModel->referensi(1538),
      'filter' => $this->masterModel->referensi(1539),
      'facemask' => $this->masterModel->referensi(1540),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/invasifAnestesi/CSOASEMA/index', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->CSOASEMAModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $data = array(
            'id' => isset($post['id']) ? $post['id'] : null,
            'nomr' => isset($post['nomr']) ? $post['nomr'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,

            // Mulai elemen persiapan alat
            'scope' => isset($post['scope']) ? implode('-', $post['scope']) : null,
            'tube' => isset($post['tube']) ? implode('-', $post['tube']) : null,
            'airway_device' => isset($post['airway_device']) ? implode('-', $post['airway_device']) : null,
            'tape' => isset($post['tape']) ? $post['tape'] : null,
            'introducer' => isset($post['introducer']) ? implode('-', $post['introducer']) : null,
            'connection' => isset($post['connection']) ? implode('-', $post['connection']) : null,
            'suction' => isset($post['suction']) ? implode('-', $post['suction']) : null,
            // Selesai elemen persiapan alat

            // Mulai elemen persiapan obat
            'premedikasi_po' => isset($post['premedikasi_po']) ? $post['premedikasi_po'] : null,
            'induksi_po' => isset($post['induksi_po']) ? $post['induksi_po'] : null,
            'pelumpuh_otot_po' => isset($post['pelumpuh_otot_po']) ? $post['pelumpuh_otot_po'] : null,
            'reverse_antidotum_po' => isset($post['reverse_antidotum_po']) ? $post['reverse_antidotum_po'] : null,
            'emergensi_po' => isset($post['emergensi_po']) ? $post['emergensi_po'] : null,
            'analgetik_po' => isset($post['analgetik_po']) ? $post['analgetik_po'] : null,
            // Selesai elemen persiapan obat

            // Mulai elemen epidural/spinal
            'premedikasi_es' => isset($post['premedikasi_es']) ? $post['premedikasi_es'] : null,

            'suntik_lokal_es' => isset($post['suntik_lokal_es']) ? $post['suntik_lokal_es'] : null,
            'spinal_es' => isset($post['spinal_es']) ? $post['spinal_es'] : null,
            'emergensi_es' => isset($post['emergensi_es']) ? $post['emergensi_es'] : null,
            'analgetik_es' => isset($post['analgetik_es']) ? $post['analgetik_es'] : null,
            // Selesai elemen epidural/spinal

            // Mulai checklist mesin anestesi
            'ket_sambung_kabel' => isset($post['ket_sambung_kabel']) ? $post['ket_sambung_kabel'] : null,
            'sambung_selang' => isset($post['sambung_selang']) ? implode('-', $post['sambung_selang']) : null,
            'ket_sambung_selang' => isset($post['ket_sambung_selang']) ? $post['ket_sambung_selang'] : null,
            'bagging' => isset($post['bagging']) ? implode('-', $post['bagging']) : null,
            'ket_bagging' => isset($post['ket_bagging']) ? $post['ket_bagging'] : null,
            'ventilator' => isset($post['ventilator']) ? implode('-', $post['ventilator']) : null,
            'ket_ventilator' => isset($post['ket_ventilator']) ? $post['ket_ventilator'] : null,
            'vaporizer' => isset($post['vaporizer']) ? implode('-', $post['vaporizer']) : null,
            'ket_vaporizer' => isset($post['ket_vaporizer']) ? $post['ket_vaporizer'] : null,
            'absorber' => isset($post['absorber']) ? implode('-', $post['absorber']) : null,
            'ket_absorber' => isset($post['ket_absorber']) ? $post['ket_absorber'] : null,
            'corrigated' => isset($post['corrigated']) ? implode('-', $post['corrigated']) : null,
            'ket_corrigated' => isset($post['ket_corrigated']) ? $post['ket_corrigated'] : null,
            'filter' => isset($post['filter']) ? implode('-', $post['filter']) : null,
            'ket_filter' => isset($post['ket_filter']) ? $post['ket_filter'] : null,
            'facemask' => isset($post['facemask']) ? implode('-', $post['facemask']) : null,
            'ket_facemask' => isset($post['ket_facemask']) ? $post['ket_facemask'] : null,
            // Selesai checklist mesin anestesi

            'oleh' => $this->session->userdata('id'),
            'status' => '1',
          );
          // echo '<pre>';print_r($data);exit();
          $this->CSOASEMAModel->replace($data);
          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->CSOASEMAModel->get($post['id'], true);
        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data,
          )
        );
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $data = array('nomr' => $post['nomr']);
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/invasifAnestesi/CSOASEMA/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->post('draw'));
    $nomr = $this->input->post('nomr');
    $history = $this->CSOASEMAModel->history($nomr, 'tabel');
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;
    // echo '<pre>';print_r($nomr);exit();

    foreach ($history->result() as $h) {
      // Status
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H.i', strtotime($h->jam)),
        $status,
        $h->pengisi,
        date('d-m-Y, H.i', strtotime($h->updated_at)),
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-csoasema' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-csoasema' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-csoasema' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    // echo '<pre>';print_r($id);exit();

    $data = array('status' => 0,);
    $this->CSOASEMAModel->ubah($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}
