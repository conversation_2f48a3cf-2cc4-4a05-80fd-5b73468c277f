<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PemberianIntravenaModel extends CI_Model
{

  public function historyBatalPemberianIntravena($id)
  {
    $query =  $this->db->query(
      "SELECT * FROM keperawatan.tb_pemberian_cairan_intravena intra
          LEFT JOIN keperawatan.tb_pemberian_cairan_intravena_isi intra_isi ON intra.id = intra_isi.id_intravena
          WHERE intra.id = '$id' limit 1"
    );
    return $query->row_array();
  }

  public function historyPemberianIntravenaIsi($id)
  {
    $this->db->SELECT('intravenaisi.*, master.getNamaLengkapPegawai(intravenaisi.perawat1) nama_perawat1, master.getNamaLengkapPegawai(intravenaisi.perawat2) nama_perawat2, master.getNamaLengkapPegawai(dok.NIP) dokter, isicairan.variabel isicairan');
    $this->db->FROM('keperawatan.tb_pemberian_cairan_intravena_isi intravenaisi');
    $this->db->join('keperawatan.tb_pemberian_cairan_intravena intravena', 'intravena.id = intravenaisi.id_intravena', 'left');
    $this->db->join('master.dokter dok', 'dok.ID = intravena.dokter', 'left');
    $this->db->join('db_master.variabel isicairan', 'intravenaisi.isi_cairan = isicairan.id_variabel', 'left');
    $this->db->where('intravena.id=' . $id . '');
    $this->db->where('intravenaisi.status', 1);

    $query = $this->db->get();
    return $query->result_array();
  }

  public function historyDetailPemberianIntravenaIsi($id)
  {
    $query = $this->db->query(
      "SELECT intravenaisi.*, b.NAMA NAMA_CAIRAN
          FROM keperawatan.tb_pemberian_cairan_intravena_isi intravenaisi
          LEFT JOIN keperawatan.tb_pemberian_cairan_intravena i ON intravenaisi.id_intravena = i.id
          LEFT JOIN inventory.barang b ON i.nama_cairan = b.ID
          WHERE intravenaisi.id = '$id'"
    );
    return $query->row_array();
  }

  public function riwayatPemberianIntravena($nomr)
  {
    $query = $this->db->query(
      "SELECT i.id, b.NAMA CAIRAN, d.nama TRANSFUSI
          , CONCAT(IF(i.kecepatan_dosis IS NULL,' - ',i.kecepatan_dosis), ' '
          , IF(i.kecepatan_jam IS NULL,' - ',CONCAT('/ ',i.kecepatan_jam))) KECEPATAN
          , (SELECT CONCAT(GROUP_CONCAT('• ',b.NAMA, ' ['
            , IF(ar.dosis_obat_tambahan IS NULL,' - ',ar.dosis_obat_tambahan), ' '
            , IF(ar.satuan_obat_tambahan IS NULL
            , ' - ',ar.satuan_obat_tambahan),']','' SEPARATOR '\r')) OBAT_TAMBAHAN
            FROM keperawatan.tb_pemberian_cairan_intravena_array ar
            LEFT JOIN inventory.barang b ON b.ID = ar.nama_obat_tambahan
            WHERE ar.id_intravena=i.id) OBAT_TAMBAHAN
          , i.created_at TANGGAL
          , IF(i.status_stop=0, i.keterangan_stop_pemberian, 'Lanjutkan Pemberian') KETERANGAN_STOP
          , master.getNamaLengkapPegawai(md.NIP) DOKTER
          , i.status_stop, i.nokun
          FROM keperawatan.tb_pemberian_cairan_intravena i
          LEFT JOIN pendaftaran.kunjungan pk on i.nokun = pk.NOMOR
          LEFT JOIN pendaftaran.pendaftaran pp on pk.NOPEN = pp.NOMOR
          LEFT JOIN master.pasien mp on pp.NORM = mp.NORM
          LEFT JOIN master.dokter md on i.dokter = md.ID
          LEFT JOIN inventory.barang b ON b.ID = i.nama_cairan
          LEFT JOIN inventory.satuan sat ON sat.ID = b.SATUAN_DOSIS
          LEFT JOIN db_master.tb_darah d ON i.nama_transfusi = d.id
          WHERE pp.NORM='$nomr' AND i.status = 1 AND pp.`STATUS` = 2 order by i.created_at DESC"
    );
    return $query->result_array();
  }

  public function historyPemberianIntravena($nomr)
  {
    $query = $this->db->query(
      "SELECT i.id, b.NAMA CAIRAN, d.nama TRANSFUSI
          , CONCAT(IF(i.kecepatan_dosis IS NULL,' - ',i.kecepatan_dosis), ' '
          , IF(i.kecepatan_jam IS NULL,' - ',CONCAT('/ ',i.kecepatan_jam))) KECEPATAN
          , (SELECT CONCAT(GROUP_CONCAT('• ',b.NAMA, ' ['
            , IF(ar.dosis_obat_tambahan IS NULL,' - ',ar.dosis_obat_tambahan), ' '
            , IF(ar.satuan_obat_tambahan IS NULL
            , ' - ',ar.satuan_obat_tambahan),']','' SEPARATOR '\r')) OBAT_TAMBAHAN
            FROM keperawatan.tb_pemberian_cairan_intravena_array ar
            LEFT JOIN inventory.barang b ON b.ID = ar.nama_obat_tambahan
            WHERE ar.id_intravena=i.id) OBAT_TAMBAHAN
          , i.created_at TANGGAL
          , IF(i.status_stop=0, i.keterangan_stop_pemberian, 'Lanjutkan Pemberian') KETERANGAN_STOP
          , master.getNamaLengkapPegawai(md.NIP) DOKTER
          , i.status_stop, i.nokun
          FROM keperawatan.tb_pemberian_cairan_intravena i
          LEFT JOIN pendaftaran.kunjungan pk on i.nokun = pk.NOMOR
          LEFT JOIN pendaftaran.pendaftaran pp on pk.NOPEN = pp.NOMOR
          LEFT JOIN master.pasien mp on pp.NORM = mp.NORM
          LEFT JOIN master.dokter md on i.dokter = md.ID
          LEFT JOIN inventory.barang b ON b.ID = i.nama_cairan
          LEFT JOIN inventory.satuan sat ON sat.ID = b.SATUAN_DOSIS
          LEFT JOIN db_master.tb_darah d ON i.nama_transfusi = d.id
          WHERE pp.NORM='$nomr' AND i.status = 1 AND pp.`STATUS` = 1 order by i.created_at DESC"
    );
    return $query->result_array();
  }

  public function historyDetailPemberianIntravena($id)
  {
    $query = $this->db->query(
      "SELECT i.*, master.getNamaLengkapPegawai(d.NIP) NAMA_DOKTER
        FROM keperawatan.tb_pemberian_cairan_intravena i
        LEFT JOIN master.dokter d ON i.dokter = d.ID
        WHERE i.id = '$id'"
    );
    return $query->row_array();
  }

  public function historyDetailPemberianIntravenaArr($id)
  {
    $query = $this->db->query(
      "SELECT ia.`*`, b.NAMA nama_obat_tambahan_desc FROM keperawatan.tb_pemberian_cairan_intravena_array ia
        LEFT JOIN inventory.barang b ON ia.nama_obat_tambahan = b.ID
        WHERE ia.id_intravena='$id'"
    );
    return $query->result_array();
  }

  public function getLabelBlue($id)
  {
    $query = $this->db->query(
      "SELECT master.getNamaLengkapPegawai(d.NIP) NAMA_DOKTER, b.NAMA NAMA_CAIRAN, tr.nama NAMA_TRANSFUSI, i.kecepatan_dosis, i.kecepatan_jam FROM keperawatan.tb_pemberian_cairan_intravena i
      LEFT JOIN master.dokter d ON i.dokter = d.ID
      LEFT JOIN inventory.barang b ON i.nama_cairan = b.ID
      LEFT JOIN db_master.tb_darah tr ON i.nama_transfusi = tr.id
      WHERE i.id = '$id'"
    );
    return $query->row_array();
  }

  public function getLabelYellow($id)
  {
    $query = $this->db->query(
      "SELECT b.NAMA NAMA_OBAT, arr.dosis_obat_tambahan, arr.satuan_obat_tambahan FROM keperawatan.tb_pemberian_cairan_intravena_array arr
      LEFT JOIN inventory.barang b ON arr.nama_obat_tambahan = b.ID
      WHERE arr.id_intravena = '$id'"
    );
    return $query->result_array();
  }

  public function ambilIntravenaTerakhir($jenis)
  {
    $keterangan = null;
    if ($jenis == 'transfusi') {
      $keterangan = 'd.nama';
    } elseif ($jenis == 'parenteral') {
      $keterangan = 'b.NAMA';
    }
    $this->db->select('ci.jumlah_cairan, ' . $keterangan .' keterangan, timestamp(ci.tanggal, ci.dimulai) waktu, master.getNamaLengkapPegawai(ap.NIP) oleh');
    $this->db->from('keperawatan.tb_pemberian_cairan_intravena_isi ci');
    $this->db->join('keperawatan.tb_pemberian_cairan_intravena c', 'c.id = ci.id_intravena', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ci.oleh = ap.ID', 'left');
    $this->db->join('pendaftaran.kunjungan pk', 'c.nokun = pk.NOMOR', 'left');
    if ($jenis == 'transfusi') {
      $this->db->join('db_master.tb_darah d', 'c.nama_transfusi = d.id');
    } elseif ($jenis == 'parenteral') {
      $this->db->join('inventory.barang b', 'c.nama_cairan = b.ID');
    }
    $this->db->order_by('waktu', 'desc');

    if ($this->input->post('id')) {
      $this->db->where('t.id', $this->input->post('id'));
      $query = $this->db->get();
      return $query->row();
    } else {
      if ($this->input->post('nokun')) {
        $this->db->where('c.nokun', $this->input->post('nokun'));
      } elseif ($this->input->post('nopen')) {
        $this->db->where('pk.NOPEN', $this->input->post('nopen'));
      }
      if ($jenis == 'transfusi') {
        $this->db->limit(3);
      } elseif ($jenis == 'parenteral') {
        $this->db->limit(5);
      }
      $this->db->limit(5);
      $query = $this->db->get();
      return $query->result_array();
    }
  }
}

/* End of file InstrumenMNA_Model.php */
/* Location: ./application/models/geriatri/InstrumenMNA_Model.php */
