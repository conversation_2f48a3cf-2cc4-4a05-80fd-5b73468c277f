<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PemulanganPasienRiModel extends MY_Model {
  protected $_table_name = 'keperawatan.tb_keperawatan';
  protected $_primary_key = 'nopen';
  protected $_order_by = 'nopen';
  protected $_order_by_type = 'DESC';

  public $rules = array(
    'nopen' => array(
      'field' => 'nopen',
      'label' => 'Nomor Kunjungan',
      'rules' => 'trim|numeric|required',
      'errors' => array(
        'required' => '%s Wajib <PERSON>isi.',
        'numeric' => '%s Wajib <PERSON>.'
      ),
    ),
  );

  function __construct(){
    parent::__construct();
  }

  function table_query()
  {
    $this->db->select('dp.*, peng.NAMA oleh_nama');
    $this->db->from('keperawatan.tb_discharge_planning dp');
    $this->db->join('pendaftaran.kunjungan penkun','dp.nokun = penkun.NOMOR','LEFT');
    $this->db->join('pendaftaran.pendaftaran penpen','penkun.NOPEN = penpen.NOMOR','LEFT');
    $this->db->join('aplikasi.pengguna peng','dp.oleh = peng.ID','LEFT');
    
    $this->db->where('penpen.NORM',$this->input->post('nomr'));
    $this->db->where('dp.status', 1);
  }

  function get_table($single = TRUE){
    $this->table_query();
    $query = $this->db->get();
    if($single == TRUE){
      $method = 'row';
    }

    else{
      $method = 'result';
    }
    return $query->$method();
  }

  function get_count(){
    $this->table_query();
    return $this->db->count_all_results();
  }




  public function getNomrRawatInap($nokun)
  {
      $query = $this->db->query(
          "SELECT penpen.NORM, penkun.MASUK TGL_MASUK FROM pendaftaran.kunjungan penkun 
          LEFT JOIN pendaftaran.pendaftaran penpen ON penkun.NOPEN = penpen.NOMOR
          WHERE penkun.NOMOR = '$nokun'"
      );
      return $query->row_array();
  }

  // Get Pengkajian Rawat Inap Remaja
  public function getPengkajian($nokun)
  {
    $query = $this->db->query(
      'SELECT dp.*, penpen.TANGGAL tgl_masuk, pulang.TANGGAL tgl_keluar_pasien_pulang FROM keperawatan.tb_discharge_planning dp
      LEFT JOIN pendaftaran.kunjungan penkun ON dp.nokun = penkun.NOMOR
      LEFT JOIN pendaftaran.pendaftaran penpen ON penpen.NOMOR = penkun.NOPEN
      LEFT JOIN layanan.pasien_pulang pulang ON penpen.NOMOR = pulang.NOPEN
      WHERE dp.nokun = "'.$nokun.'" AND dp.status = 1 '
    );
    return $query->row_array();
  }
  



}

/* End of file MedisDewasaModel.php */
/* Location: ./application/models/rekam_medis/rawat_inap/pengkajian/pengkajianRI/MedisDewasaModel.php */
