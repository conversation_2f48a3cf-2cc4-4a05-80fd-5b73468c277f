<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PIPTKAU extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->model(array('masterModel','pengkajianAwalModel','rekam_medis/rawat_inap/informedConsent/PIPTKAUModel'));
    }

    public function index() {
      $nokun = $this->uri->segment(2);
      $id_piptkau = $this->uri->segment(3);
      
      $data = array(
        'nokun' => $nokun,
        'id_piptkau' => isset($id_piptkau) ? $id_piptkau : "",
        'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
        'listDrUmum' => $this->masterModel->listDrUmum(),
        'listPegawai' => $this->masterModel->listAllPegawai(),
        'listTatacara' => $this->masterModel->referensi(1604),
        'listTujuan' => $this->masterModel->referensi(1605),
        'listRisiko' => $this->masterModel->referensi(1606),
        'listKomplikasi' => $this->masterModel->referensi(1607),
        'listPrognosis' => $this->masterModel->referensi(1608),
        'jenis_kelamin' => $this->masterModel->referensi(965),
        'getPengkajian' => $this->PIPTKAUModel->getPengkajian($id_piptkau),
      );
      $this->load->view('rekam_medis/rawat_inap/informedConsent/PIPTKAU', $data);
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
          $post = $this->input->post();

          $data = array(
            'nokun' => $post['nokun'],
            'dokter_pelaksana' => isset($post['dokter_pelaksana']) ? $post['dokter_pelaksana']: "",
            'pemberi_informasi' => isset($post['pemberi_informasi']) ? $post['pemberi_informasi']: "",
            'penerima_informasi' => isset($post['penerima_informasi']) ? $post['penerima_informasi']: "",
            'diagnosis' => isset($post['diagnosis']) ? $post['diagnosis']: "",
            'informasi_diagnosis' => isset($post['informasi_diagnosis']) ? $post['informasi_diagnosis']: "",
            'dasar_diagnosis' => isset($post['dasar_diagnosis']) ? $post['dasar_diagnosis']: "",
            'informasi_dasar_diagnosis' => isset($post['informasi_dasar_diagnosis']) ? $post['informasi_dasar_diagnosis']: "",
            'tindakan_kedokteran' => isset($post['tindakan_kedokteran']) ? $post['tindakan_kedokteran']: "",
            'informasi_tindakan_kedokteran' => isset($post['informasi_tindakan_kedokteran']) ? $post['informasi_tindakan_kedokteran']: "",
            'indikasi_tindakan' => isset($post['indikasi_tindakan']) ? $post['indikasi_tindakan']: "",
            'informasi_indikasi_tindakan' => isset($post['informasi_indikasi_tindakan']) ? $post['informasi_indikasi_tindakan']: "",
            'tata_cara' => isset($post['tata_cara']) ? $post['tata_cara']: "",
            'informasi_tata_cara' => isset($post['informasi_tata_cara']) ? json_encode($post['informasi_tata_cara']): "",
            'informasi_tata_cara_lain' => isset($post['informasi_tata_cara_lain']) ? $post['informasi_tata_cara_lain']: "",
            'tujuan' => isset($post['tujuan']) ? $post['tujuan']: "",
            'informasi_tujuan' => isset($post['informasi_tujuan']) ? json_encode($post['informasi_tujuan']): "",
            'informasi_tujuan_lain' => isset($post['informasi_tujuan_lain']) ? $post['informasi_tujuan_lain']: "",
            'risiko' => isset($post['risiko']) ? $post['risiko']: "",
            'informasi_risiko' => isset($post['informasi_risiko']) ? json_encode($post['informasi_risiko']): "",
            'informasi_risiko_lain' => isset($post['informasi_risiko_lain']) ? $post['informasi_risiko_lain']: "",
            'komplikasi' => isset($post['komplikasi']) ? $post['komplikasi']: "",
            'informasi_komplikasi' => isset($post['informasi_komplikasi']) ? json_encode($post['informasi_komplikasi']): "",
            'informasi_komplikasi_lain' => isset($post['informasi_komplikasi_lain']) ? $post['informasi_komplikasi_lain']: "",
            'prognosis' => isset($post['prognosis']) ? $post['prognosis']: "",
            'informasi_prognosis_lain' => isset($post['informasi_prognosis_lain']) ? $post['informasi_prognosis_lain']: "",
            'informasi_prognosis' => isset($post['informasi_prognosis']) ? json_encode($post['informasi_prognosis']): "",
            'aternatif_risiko' => isset($post['aternatif_risiko']) ? $post['aternatif_risiko']: "",
            'informasi_aternatif_risiko' => isset($post['informasi_aternatif_risiko']) ? $post['informasi_aternatif_risiko']: "",
            'pemberian_analgetik' => isset($post['pemberian_analgetik']) ? $post['pemberian_analgetik']: "",
            'informasi_pemberian_analgetik' => isset($post['informasi_pemberian_analgetik']) ? $post['informasi_pemberian_analgetik']: "",
            'ttd_dokter' => isset($post['ttd_dokter']) ? file_get_contents($post['ttd_dokter']): "",
            'ttd_keluarga' => isset($post['ttd_keluarga']) ? file_get_contents($post['ttd_keluarga']): "",
            'nama_keluarga' => isset($post['nama_keluarga']) ? $post['nama_keluarga']: "",
            'umur_keluarga' => isset($post['umur_keluarga']) ? $post['umur_keluarga']: "",
            'jenis_kelamin' => isset($post['jenis_kelamin']) ? $post['jenis_kelamin']: "",
            'alamat_keluarga' => isset($post['alamat_keluarga']) ? $post['alamat_keluarga']: "",
            'tindakan' => isset($post['tindakan']) ? $post['tindakan']: "",
            'hubungan_keluarga' => isset($post['hubungan_keluarga']) ? $post['hubungan_keluarga']: "",
            'tanggal_persetujuan' => isset($post['tanggal_persetujuan']) ? $post['tanggal_persetujuan']: "",
            'ttd_menyatakan' => isset($post['ttd_menyatakan']) ? file_get_contents($post['ttd_menyatakan']): "",
            'ttd_saksi_keluarga' => isset($post['ttd_saksi_keluarga']) ? file_get_contents($post['ttd_saksi_keluarga']): "",
            'ttd_rumah_sakit' => isset($post['ttd_rumah_sakit']) ? file_get_contents($post['ttd_rumah_sakit']): "",
            'nama_menyatakan' => isset($post['nama_menyatakan']) ? $post['nama_menyatakan']: "",
            'nama_saksi_keluarga' => isset($post['nama_saksi_keluarga']) ? $post['nama_saksi_keluarga']: "",
            'nama_saksi_rs' => isset($post['nama_saksi_rs']) ? $post['nama_saksi_rs']: "",
            'oleh' => $this->session->userdata('id')
          );

          $dataUbah = array(
            'nokun' => $post['nokun'],
            'dokter_pelaksana' => isset($post['dokter_pelaksana']) ? $post['dokter_pelaksana']: "",
            'pemberi_informasi' => isset($post['pemberi_informasi']) ? $post['pemberi_informasi']: "",
            'penerima_informasi' => isset($post['penerima_informasi']) ? $post['penerima_informasi']: "",
            'diagnosis' => isset($post['diagnosis']) ? $post['diagnosis']: "",
            'informasi_diagnosis' => isset($post['informasi_diagnosis']) ? $post['informasi_diagnosis']: "",
            'dasar_diagnosis' => isset($post['dasar_diagnosis']) ? $post['dasar_diagnosis']: "",
            'informasi_dasar_diagnosis' => isset($post['informasi_dasar_diagnosis']) ? $post['informasi_dasar_diagnosis']: "",
            'tindakan_kedokteran' => isset($post['tindakan_kedokteran']) ? $post['tindakan_kedokteran']: "",
            'informasi_tindakan_kedokteran' => isset($post['informasi_tindakan_kedokteran']) ? $post['informasi_tindakan_kedokteran']: "",
            'indikasi_tindakan' => isset($post['indikasi_tindakan']) ? $post['indikasi_tindakan']: "",
            'informasi_indikasi_tindakan' => isset($post['informasi_indikasi_tindakan']) ? $post['informasi_indikasi_tindakan']: "",
            'tata_cara' => isset($post['tata_cara']) ? $post['tata_cara']: "",
            'informasi_tata_cara' => isset($post['informasi_tata_cara']) ? json_encode($post['informasi_tata_cara']): "",
            'informasi_tata_cara_lain' => isset($post['informasi_tata_cara_lain']) ? $post['informasi_tata_cara_lain']: "",
            'tujuan' => isset($post['tujuan']) ? $post['tujuan']: "",
            'informasi_tujuan' => isset($post['informasi_tujuan']) ? json_encode($post['informasi_tujuan']): "",
            'informasi_tujuan_lain' => isset($post['informasi_tujuan_lain']) ? $post['informasi_tujuan_lain']: "",
            'risiko' => isset($post['risiko']) ? $post['risiko']: "",
            'informasi_risiko' => isset($post['informasi_risiko']) ? json_encode($post['informasi_risiko']): "",
            'informasi_risiko_lain' => isset($post['informasi_risiko_lain']) ? $post['informasi_risiko_lain']: "",
            'komplikasi' => isset($post['komplikasi']) ? $post['komplikasi']: "",
            'informasi_komplikasi' => isset($post['informasi_komplikasi']) ? json_encode($post['informasi_komplikasi']): "",
            'informasi_komplikasi_lain' => isset($post['informasi_komplikasi_lain']) ? $post['informasi_komplikasi_lain']: "",
            'prognosis' => isset($post['prognosis']) ? $post['prognosis']: "",
            'informasi_prognosis_lain' => isset($post['informasi_prognosis_lain']) ? $post['informasi_prognosis_lain']: "",
            'informasi_prognosis' => isset($post['informasi_prognosis']) ? json_encode($post['informasi_prognosis']): "",
            'aternatif_risiko' => isset($post['aternatif_risiko']) ? $post['aternatif_risiko']: "",
            'informasi_aternatif_risiko' => isset($post['informasi_aternatif_risiko']) ? $post['informasi_aternatif_risiko']: "",
            'pemberian_analgetik' => isset($post['pemberian_analgetik']) ? $post['pemberian_analgetik']: "",
            'informasi_pemberian_analgetik' => isset($post['informasi_pemberian_analgetik']) ? $post['informasi_pemberian_analgetik']: "",
            'nama_keluarga' => isset($post['nama_keluarga']) ? $post['nama_keluarga']: "",
            'umur_keluarga' => isset($post['umur_keluarga']) ? $post['umur_keluarga']: "",
            'jenis_kelamin' => isset($post['jenis_kelamin']) ? $post['jenis_kelamin']: "",
            'alamat_keluarga' => isset($post['alamat_keluarga']) ? $post['alamat_keluarga']: "",
            'tindakan' => isset($post['tindakan']) ? $post['tindakan']: "",
            'hubungan_keluarga' => isset($post['hubungan_keluarga']) ? $post['hubungan_keluarga']: "",
            'tanggal_persetujuan' => isset($post['tanggal_persetujuan']) ? $post['tanggal_persetujuan']: "",
            'nama_menyatakan' => isset($post['nama_menyatakan']) ? $post['nama_menyatakan']: "",
            'nama_saksi_keluarga' => isset($post['nama_saksi_keluarga']) ? $post['nama_saksi_keluarga']: "",
            'nama_saksi_rs' => isset($post['nama_saksi_rs']) ? $post['nama_saksi_rs']: "",
            'oleh' => $this->session->userdata('id')
          );

          $this->db->trans_begin();
        
          if (!empty($post['id_piptkau'])) {
            $this->db->where('keperawatan.tb_piptkau.id', $post['id_piptkau']);
            $this->db->update('keperawatan.tb_piptkau', $dataUbah);
            if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
            } else {
              $this->db->trans_commit();
              $result = array('status' => 'success_simpan');
            }
    
            echo json_encode($result);
          }else{
              $this->db->insert('keperawatan.tb_piptkau', $data);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
          }

        }else if($param == 'count'){
          $result = $this->PIPTKAUModel->get_count();;
          echo json_encode($result);
        }
      }
    }

    public function datatables(){
        $result = $this->PIPTKAUModel->datatables();

        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = '<a class="btn btn-primary btn-block btn-sm editPIPTKAU" data-id="'.$row -> id.'"><i class="fa fa-eye"></i> Lihat</a>';
            $sub_array[] = date('d M Y H:i:s', strtotime($row -> tanggal));
            $sub_array[] = $row -> ruangan;
            $sub_array[] = $row -> user;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),  
            "recordsTotal"      => $this->PIPTKAUModel->total_count(),
            "recordsFiltered"   => $this->PIPTKAUModel->filter_count(),
            "data"              => $data
        );
        echo json_encode($output);
    }
}