<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MedikasiObat extends CI_Controller{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }
    
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }
    
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('masterModel', 'pengkajianAwalModel'));
    }

    
    public function index()
    {
        $nokun = $this->uri->segment(6);
        $id_medikasi_obat = $this->uri->segment(8);
        $getPengkajian = $this->pengkajianAwalModel->HistoryDetailMedikasiObat($id_medikasi_obat);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listPemberianObat = $this->masterModel->referensi(717);
        $historyMedikasiObat = $this->pengkajianAwalModel->historyMedikasiObat($getNomr['NORM']);

        $data = array(
            'id_medikasi_obat' => $id_medikasi_obat,
            'getPengkajian' => isset($getPengkajian) ? $getPengkajian: "",
            'getNomr' => $getNomr,
            'listPemberianObat' => $listPemberianObat,
            'historyMedikasiObat' => $historyMedikasiObat,
            'category' => 1
        );

        $this->load->view('Pengkajian/sedasi/medikasiObat/index', $data);
    }

    public function indexRawatInap()
    {
        $nokun = $this->uri->segment(2);
        $id_medikasi_obat = $this->uri->segment(3);
        $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
        $listPemberianObat = $this->masterModel->referensi(717);
        $historyMedikasiObat = $this->pengkajianAwalModel->historyMedikasiObat($getNomr['NORM']);
        $getPengkajian = $this->pengkajianAwalModel->HistoryDetailMedikasiObat($id_medikasi_obat);

        $data = array(
            'id_medikasi_obat' => $id_medikasi_obat,
            'getPengkajian' => isset($getPengkajian) ? $getPengkajian: "",
            'getNomr' => $getNomr,
            'listPemberianObat' => $listPemberianObat,
            'historyMedikasiObat' => $historyMedikasiObat,
            'category' => 2
        );

        $this->load->view('Pengkajian/sedasi/medikasiObat/index', $data);
    }

    public function action($param){
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
          if($param == 'tambah' || $param == 'ubah'){
            $kunjungan = $this->input->post("nokun");
            $obatmedikasi = $this->input->post("obatmedikasi");
            $pemberianmedikasi = $this->input->post("pemberianmedikasi");
            $waktumedikasi = $this->input->post("waktumedikasi");
            $efekmedikasi = $this->input->post("efekmedikasi");
            $id_medikasi_obat = $this->input->post("id_medikasi_obat");

            $data = array(
                'id' => isset($id_medikasi_obat) ? $id_medikasi_obat : "",
                'nokun' => $kunjungan,
                'nama_obat' => $obatmedikasi,
                'pemberian' => $pemberianmedikasi,
                'waktu' => $waktumedikasi,
                'efek' => $efekmedikasi
            );

            $this->db->trans_begin();
        
            if (!empty($id_medikasi_obat)) {
              $this->db->replace('medis.tb_sedasi_medikasi_obat', $data);
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_ubah');
              }
      
              echo json_encode($result);
            }else{
              $this->db->insert('medis.tb_sedasi_medikasi_obat', $data);
  
              if ($this->db->trans_status() === false) {
                $this->db->trans_rollback();
                $result = array('status' => 'failed');
              } else {
                $this->db->trans_commit();
                $result = array('status' => 'success_simpan');
              }
      
              echo json_encode($result);
            }

          }
        }
    }

}

?>