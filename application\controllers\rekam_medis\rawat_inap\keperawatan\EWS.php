<?php
defined('BASEPATH') or exit('No direct script access allowed');

class EWS extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');

    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'FormulirTriaseModel',
        'rekam_medis/rawat_inap/keperawatan/EWSModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
      )
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(2);
    $idEmr = isset($this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr) ? $this->pengkajianAwalModel->ambilIdEmr($nokun)->id_emr : null;
    $pasien = $this->pengkajianAwalModel->getNomr($nokun);
    $nomr = $pasien['NORM'];
    $data = array(
      'pasien' => $pasien,
      'idEmr' => $idEmr,
      'nokun' => $nokun,
      'pilihanCPPT' => $this->masterModel->referensi(1407),
      'vitalTriaseTerbaru' => $this->FormulirTriaseModel->vitalTriaseTerbaru($nokun),
      'penggunaan' => $this->masterModel->referensi(129),
      'kesadaran' => $this->masterModel->referensi(5),
      'listPerawat' => $this->masterModel->listPerawat(),
      'history' => $this->EWSModel->history($nomr),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/EWS/index', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $rules = $this->EWSModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $oleh = $this->session->userdata['id'];
          $status = 1;
          $dataSource = 3;
          $nokun = isset($post['nokun']) ? $post['nokun'] : null;
          $nomr = isset($post['nomr']) ? $post['nomr'] : null;

          // Simpan ke Tanda Vital
          $dataTandaVital = array(
            'data_source' => $dataSource,
            'ref' => null,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'td_sistolik' => isset($post['td_sistolik']) ? round($post['td_sistolik'], 2) : null,
            'td_diastolik' => isset($post['td_diastolik']) ? round($post['td_diastolik'], 2) : null,
            'nadi' => isset($post['nadi']) ? round($post['nadi'], 2) : null,
            'pernapasan' => isset($post['pernapasan']) ? round($post['pernapasan'], 2) : null,
            'suhu' => isset($post['suhu']) ? round($post['suhu'], 2) : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataTandaVital);exit();
          $idTandaVital = $this->OTKeperawatanModel->simpanTandaVital($dataTandaVital);

          // Simpan ke Oksigen
          $dataOksigen = array(
            'data_source' => $dataSource,
            'ref' => null,
            'nomr' => $nomr,
            'nokun' => $nokun,
            'saturasi_o2' => isset($post['saturasi_o2']) ? round($post['saturasi_o2'], 2) : null,
            'penggunaan_o2' => isset($post['penggunaan_o2']) ? $post['penggunaan_o2'] : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataOksigen);exit();
          $idOksigen = $this->OTKeperawatanModel->simpanOksigen($dataOksigen);

          // Simpan ke Kesadaran
          $dataKesadaran = array(
            'data_source' => $dataSource,
            'ref' => null,
            'nokun' => $nokun,
            'nomr' => $nomr,
            'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataKesadaran);exit();
          $idKesadaran = $this->OTKeperawatanModel->simpanKesadaran($dataKesadaran);

          // Simpan ke EWS
          $dataEWS = array(
            'id_tanda_vital' => $idTandaVital,
            'id_kesadaran' => $idKesadaran,
            'id_o2' => $idOksigen,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'score_ews' => isset($post['skor']) ? $post['skor'] : null,
            'ref' => null,
            'nokun' => $nokun,
            'perawat2' => isset($post['perawat2']) ? $post['perawat2'] : null,
            'oleh' => $oleh,
            'status' => $status,
          );
          // echo '<pre>';print_r($dataEWS);exit();
          $idEWS = $this->EWSModel->simpanEWS($dataEWS);

          // Memasukkan ID EWS sebagai ref
          $data = array(
            'ref' => $idEWS,
          );
          $this->OTKeperawatanModel->updatePakaiIDTandaVital($idTandaVital, $data);
          $this->OTKeperawatanModel->updatePakaiIDKesadaran($idKesadaran, $data);
          $this->OTKeperawatanModel->updatePakaiIDOksigen($idOksigen, $data);
        }

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      } elseif ($param == 'ambil') {
        $post = $this->input->post(null, true);
        $data = $this->EWSModel->detail($post['id'], true);
        echo json_encode(
          array(
            'status' => 'success',
            'data' => $data,
          )
        );
      }
    }
  }

  public function tabelKeterangan()
  {
    $draw = intval($this->input->post('draw'));
    $nokun = $this->input->post('nokun');
    $history = $this->EWSModel->detailHistory($nokun);
    $data = array();
    $no = 1;
    // echo '<pre>';print_r($nokun);exit();

    foreach ($history->result() as $h) {
      $data[] = array(
        $no,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H:i', strtotime($h->jam)),
        $h->score_ews,
        $h->pernapasan,
        $h->nadi,
        $h->td_sistolik,
        $h->td_diastolik,
        $h->suhu,
        $h->saturasi_o2,
        $h->penggunaan_o2,
        $h->kesadaran,
        $h->pengisi,
      );
      $no++;
    }
    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    // echo '<pre>';print_r($output);exit();
    echo json_encode($output);
  }

  public function keterangan()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    // echo '<pre>';print_r($post);exit();
    if (isset($id)) {
      $data = array(
        'lokasi' => 'EWS',
        'keterangan' => $this->EWSModel->keterangan($id, null),
      );
    } elseif (isset($post['nokun'])) {
      $data = array(
        'lokasi' => 'dashboard',
        'keterangan' => $this->EWSModel->keterangan(null, $post['nokun']),
      );
    }
    // echo '<pre>';print_r($data);exit();
    $this->load->view('rekam_medis/rawat_inap/keperawatan/EWS/keterangan', $data);
  }
}

/* End of file EWS.php */
/* Location: ./application/controllers/rekam_medis/rawat_inap/keperawatan/EWS.php */