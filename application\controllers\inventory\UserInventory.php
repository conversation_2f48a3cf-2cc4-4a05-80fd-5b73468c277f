<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class UserInventory extends CI_Controller {
	public function __construct()
	{
		parent::__construct();
		if ($this->session->userdata('logged_in') == false) {
			redirect('login');
		}

		if (!in_array(27, $this->session->userdata('akses'))) {
			redirect('login');
		}

		date_default_timezone_set("Asia/Bangkok");
		$this->load->model(array('inventory/Model_user_inventory','masterModel'));
	}

	public function index()
	{
		$pengguna = $this->Model_user_inventory->datapengguna()->result();
		$unit = $this->Model_user_inventory->instalasi()->result();
		$data = array(
			'title'       	=> 'Halaman Input Barang',
			'isi'         	=> 'inventory/UserInventory/UserInventory',
			'pengguna'		=>  $pengguna,
			'unit'			=>  $unit,
		);
		$this->load->view('layout/wrapper',$data);
	}

	function insert()
	{
		if(isset($_POST['submit'])){
			$USER       =   $this->input->post('USER');
			$UNIT   =   $this->input->post('UNIT');
//echo "<pre>";print_r($_POST);exit();
			$sql = $this->db->query("SELECT USER FROM invenumum.user_inventory where USER='$USER'");
			$cek_nama = $sql->num_rows();
			if ($cek_nama > 0) {
				$this->session->set_flashdata('warning', 'Nama barang sudah ada...!');
				redirect('inventory/UserInventory');
			}else{
				$data           = 	array
				(   'USER'  =>$USER,
					'UNIT'  =>$UNIT,
				);
				$this->Model_user_inventory->post($data);
				redirect('inventory/UserInventory');
			}
		}
	}

	public function listUser()
	{
		$draw   = intval($this->input->get("draw"));
		$start  = intval($this->input->get("start"));
		$length = intval($this->input->get("length"));
		$listUser = $this->Model_user_inventory->datauser();
    // echo "<pre>";print_r($listPegawai);exit();
		$data  = array();
		$no    =1;
		foreach($listUser->result() as $lp) {
			if($lp->STATUS == 1){
				$check = "checked" ;
			}else{
				$check = "" ;
			}
			$data[] = array(
				$no,
				$lp->NAMA,
				$lp->UNIT,
				'<div class="checkbox checkbox-primary">
				<input type="checkbox" id="statusBarang'.$lp->ID.'" value="'.$lp->ID.'"  class="Dbarang" '.$check.'>
				<label for="statusBarang'.$lp->ID.'"></label>
				</div>',

				'<a href="#formedituser" class="btn btn-md btn-block btn-info" data-toggle="modal" data-id="' . $lp->ID . '"><i class="fas fa-edit"></i> Ubah</a>',

			);
			$no++;
		}

		$output = array(
			"draw"            => $draw,
			"recordsTotal"    => $listUser->num_rows(),
			"recordsFiltered" => $listUser->num_rows(),
			"data"            => $data
		);
		echo json_encode($output);
	}


	public function ambildatuser($id)
	{
		$datauser = $this->Model_user_inventory->modaldatauser($id);
		$data = array();
		foreach ($datauser as $main) {
			$isi_array = array();
			$isi_array['ID'] 	= $main['ID'];
			$isi_array['USER'] 	= $main['USER'];
			$isi_array['ID_UNIT'] 	= $main['ID_UNIT'];
			$isi_array['NAMA'] 		= $main['NAMA'];
			$isi_array['UNIT'] 		= $main['UNIT'];
			$isi_array['STATUS'] 	= $main['STATUS'];
			$data[] = $isi_array;
		}
		return $data;
	}

	public function update()
	{
		$id = $this->input->post('ID');
		$data = array(
			'USER'    => $this->input->post('USER'),
			'UNIT'    => $this->input->post('UNIT'),
		);
     //echo "<pre>";print_r($data);exit();
		$this->Model_user_inventory->update($id, $data);
		redirect('inventory/UserInventory');
	}

	public function form_edit_user()
	{
		$id       	= $this->input->post('id');
		$isiModal 	= $this->ambildatuser($id);
		$pengguna 	= $this->Model_user_inventory->datapengguna()->result();
		$unit 		= $this->Model_user_inventory->instalasi()->result();
		$data     	= array(
			'isiModal' 	=> $isiModal,
			'pengguna'	=>  $pengguna,
			'unit'		=> 	$unit,
		);
		$this->load->view('inventory/UserInventory/modalEditUser', $data);
	}

	public function sBarangAktif()
	{
		$id = $this->input->post('id');

		$data = array(
			'STATUS' => 1,
		);

		$this->Model_barang_gudang->sBarangAktif($id,$data);
	}

	public function sBarangNonAktif()
	{
		$id = $this->input->post('id');

		$data = array(
			'STATUS' => 0,
		);

		$this->Model_barang_gudang->sBarangNonAktif($id,$data);
	}


}
