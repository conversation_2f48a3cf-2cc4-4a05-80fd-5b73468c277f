<?php
defined('BASEPATH') or exit('No direct script access allowed');

class FormulirPindahRuangan extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'transferRuangan/FormulirPindahRuanganModel',
        'rekam_medis/rawat_inap/keperawatan/OTKeperawatanModel',
        'rekam_medis/rawat_inap/keperawatan/PemantauanNyeriModel',
        'rekam_medis/TbBbModel',
      )
    );
  }

  public function index()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;

    $data = array(
      'ruanganRskd' => $this->masterModel->ruanganRskd(),
      'listDr' => $this->masterModel->listDrUmum(),
      'pilihanCPPT' => $this->masterModel->referensi(1407),
      'riwayatPengobatan' => $this->masterModel->referensi(329),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'riwayatPenyakitMenular' => $this->masterModel->referensi(338),
      'kondisiSaatIni' => $this->masterModel->referensi(330),
      'kesadaran' => $this->masterModel->referensi(5),
      'skor' => $this->masterModel->referensi(331),
      'skriningNyeri' => $this->masterModel->referensi(7),
      'skalaNyeriNRS' => $this->masterModel->referensi(114),
      'skalaNyeriWBR' => $this->masterModel->referensi(115),
      'skalaNyeriFLACC' => $this->masterModel->referensi(123),
      'skalaNyeriBPS' => $this->masterModel->referensi(133),
      'efeksamping' => $this->masterModel->referensi(118),
      'provocative' => $this->masterModel->referensi(8),
      'quality' => $this->masterModel->referensi(9),
      'time' => $this->masterModel->referensi(12),
      'risikoJatuh' => $this->masterModel->referensi(333),
      'alatBantu' => $this->masterModel->referensi(19),
      'pemeriksaanPenunjang' => $this->masterModel->referensi(351),
      'terapi' => $this->masterModel->referensi(334),
      'nutrisi' => $this->masterModel->referensi(335),
      'perawatan' => $this->masterModel->referensi(336),
      'lain' => $this->masterModel->referensi(337),
      'listPerawat' => $this->masterModel->listPerawat(),
    );

    if (isset($id)) {
      // Form detail
      $data['id'] = $id;
      $data['detail'] = $this->FormulirPindahRuanganModel->history(null, null, $id);
      $data['pasien'] = $this->pengkajianAwalModel->getNomr($data['detail'][0]['kunjungan']);
      $data['isiRiwayatPengobatan'] = isset($data['detail'][0]['riwayat_pengobatan']) ? explode('-', $data['detail'][0]['riwayat_pengobatan']) : null;
      $data['isiKondisiSaatIni'] = isset($data['detail'][0]['kondisi_saat_ini']) ? explode('-', $data['detail'][0]['kondisi_saat_ini']) : null;
      $data['isiAlatBantu'] = isset($data['detail'][0]['alat_bantu']) ? explode('-', $data['detail'][0]['alat_bantu']) : null;
      $data['isiPemeriksaanPenunjang'] = isset($data['detail'][0]['pemeriksaan_penunjang']) ? explode('-', $data['detail'][0]['pemeriksaan_penunjang']) : null;
      $data['isiPerawatan'] = isset($data['detail'][0]['perawatan']) ? explode('-', $data['detail'][0]['perawatan']) : null;
      $data['isiLain'] = isset($data['detail'][0]['lain']) ? explode('-', $data['detail'][0]['lain']) : null;
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/formulirPindahRuangan/detail', $data);
    } else {
      $nokun = !empty($this->uri->segment(6)) ? $this->uri->segment(6) : $this->uri->segment(4);
      $pasien = $this->pengkajianAwalModel->getNomr($nokun);
      $nomr = $pasien['NORM'];
      $nopen = $pasien['NOPEN'];
      $data['nokun'] = $nokun;
      $data['pasien'] = $pasien;
      $data['nomr'] = $nomr;
      $data['jumlah'] = $this->FormulirPindahRuanganModel->history($nomr, 'jumlah', null);
      $data['alergiTerakhir'] = $this->pengkajianAwalModel->alergiTerakhir($nomr);
      $data['risikoJatuhTerakhir'] = json_encode($this->pengkajianAwalModel->risikoJatuhTerakhir($nopen));
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/formulirPindahRuangan/index', $data);
    }
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpan') {
        $this->form_validation->set_rules($this->FormulirPindahRuanganModel->rules);
        $this->form_validation->set_rules($this->OTKeperawatanModel->rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();
          $nokun = isset($post['nokun']) ? $post['nokun'] : null;
          $nomr = isset($post['nomr']) ? $post['nomr'] : null;
          $oleh = $this->session->userdata['id'];
          $status = 1;
          $dataSource = 19;
          $ref = null;

          // Simpan ke Pindah Ruangan
          $data = array(
            'tujuan' => isset($post['tujuan']) ? $post['tujuan'] : null,
            'tanggal' => isset($post['tanggal']) ? $post['tanggal'] : null,
            'jam' => isset($post['jam']) ? $post['jam'] : null,
            'dpjp' => isset($post['dpjp']) ? $post['dpjp'] : null,
            'diagnosis' => isset($post['diagnosis']) ? $post['diagnosis'] : null,
            'tanggal_operasi' => isset($post['tanggal_operasi']) ? $post['tanggal_operasi'] : null,
            'waktu_operasi' => isset($post['waktu_operasi']) ? $post['waktu_operasi'] : null,
            'alasan_pindah' => isset($post['alasan_pindah']) ? $post['alasan_pindah'] : null,
            'masalah_utama' => isset($post['masalah_utama']) ? $post['masalah_utama'] : null,
            'kondisi_saat_ini' => isset($post['kondisi_saat_ini']) ? implode('-', $post['kondisi_saat_ini']) : null,
            'ket_kondisi_saat_ini' => $post['ket_kondisi_saat_ini'],
            'riwayat_alergi' => isset($post['riwayat_alergi']) ? $post['riwayat_alergi'] : null,
            'ket_riwayat_alergi' => isset($post['ket_riwayat_alergi']) ? $post['ket_riwayat_alergi'] : null,
            'riwayat_penyakit_menular' => isset($post['riwayat_penyakit_menular']) ? $post['riwayat_penyakit_menular'] : null,
            'ket_riwayat_penyakit_menular' => isset($post['ket_riwayat_penyakit_menular']) ? $post['ket_riwayat_penyakit_menular'] : null,
            'riwayat_pengobatan' => isset($post['riwayat_pengobatan']) ? implode('-', $post['riwayat_pengobatan']) : null,
            'ket_riwayat_pengobatan' => $post['ket_riwayat_pengobatan'],
            'skor' => isset($post['skor']) ? $post['skor'] : null,
            'ket_skor' => isset($post['ket_skor']) ? $post['ket_skor'] : null,
            'risiko_jatuh' => isset($post['risiko_jatuh']) ? $post['risiko_jatuh'] : null,
            'periksa_lab' => isset($post['periksa_lab']) ? $post['periksa_lab'] : null,
            'periksa_radiologi' => isset($post['periksa_radiologi']) ? $post['periksa_radiologi'] : null,
            'input' => isset($post['input']) ? $post['input'] : null,
            'output' => isset($post['output']) ? $post['output'] : null,
            'alat_bantu' => isset($post['alat_bantu']) ? implode('-', $post['alat_bantu']) : null,
            'ket_alat_bantu' => isset($post['ket_alat_bantu']) ? $post['ket_alat_bantu'] : null,
            'prosedur_cairan_parenteral' => isset($post['prosedur_cairan_parenteral']) ? $post['prosedur_cairan_parenteral'] : null,
            'prosedur_transfusi' => isset($post['prosedur_transfusi']) ? $post['prosedur_transfusi'] : null,
            'prosedur_oral' => isset($post['prosedur_oral']) ? $post['prosedur_oral'] : null,
            'prosedur_injeksi' => isset($post['prosedur_injeksi']) ? $post['prosedur_injeksi'] : null,
            'prosedur_oksigenisasi' => isset($post['prosedur_oksigenisasi']) ? $post['prosedur_oksigenisasi'] : null,
            'prosedur_tindakan' => isset($post['prosedur_tindakan']) ? $post['prosedur_tindakan'] : null,
            'masalah' => isset($post['masalah']) ? $post['masalah'] : null,
            'pemeriksaan_penunjang' => isset($post['pemeriksaan_penunjang']) ? implode('-', $post['pemeriksaan_penunjang']) : null,
            'ket_pemeriksaan_penunjang' => isset($post['ket_pemeriksaan_penunjang']) ? $post['ket_pemeriksaan_penunjang'] : null,
            'cairan_parenteral' => isset($post['cairan_parenteral']) ? $post['cairan_parenteral'] : null,
            'transfusi' => isset($post['transfusi']) ? $post['transfusi'] : null,
            'oral' => isset($post['oral']) ? $post['oral'] : null,
            'injeksi' => isset($post['injeksi']) ? $post['injeksi'] : null,
            'oksigenisasi' => isset($post['oksigenisasi']) ? $post['oksigenisasi'] : null,
            'tindakan' => isset($post['tindakan']) ? $post['tindakan'] : null,
            'nutrisi_diet' => isset($post['nutrisi_diet']) ? $post['nutrisi_diet'] : null,
            'nutrisi_parenteral' => isset($post['nutrisi_parenteral']) ? $post['nutrisi_parenteral'] : null,
            'perawatan' => isset($post['perawatan']) ? implode('-', $post['perawatan']) : null,
            'tanggal_perawatan_cvc' => isset($post['tanggal_perawatan_cvc']) ? $post['tanggal_perawatan_cvc'] : null,
            'tanggal_perawatan_luka' => isset($post['tanggal_perawatan_luka']) ? $post['tanggal_perawatan_luka'] : null,
            'tanggal_perawatan_stoma' => isset($post['tanggal_perawatan_stoma']) ? $post['tanggal_perawatan_stoma'] : null,
            'tanggal_perawatan_ngt' => isset($post['tanggal_perawatan_ngt']) ? $post['tanggal_perawatan_ngt'] : null,
            'tanggal_perawatan_dc' => isset($post['tanggal_perawatan_dc']) ? $post['tanggal_perawatan_dc'] : null,
            'tanggal_perawatan_drain' => isset($post['tanggal_perawatan_drain']) ? $post['tanggal_perawatan_drain'] : null,
            'tanggal_perawatan_lainnya' => isset($post['tanggal_perawatan_lainnya']) ? $post['tanggal_perawatan_lainnya'] : null,
            'ket_perawatan' => isset($post['ket_perawatan']) ? $post['ket_perawatan'] : null,
            'konsultasi' => isset($post['konsultasi']) ? $post['konsultasi'] : null,
            'lain' => isset($post['lain']) ? implode('-', $post['lain']) : null,
            'ket_lain' => isset($post['ket_lain']) ? $post['ket_lain'] : null,
            'dokter_penyetuju' => isset($post['dokter_penyetuju']) ? $post['dokter_penyetuju'] : null,
            'penerima' => isset($post['penerima']) ? $post['penerima'] : null,
          );

          if (isset($post['id'])) {
            // Ubah Formulir Perpindahan Pasien Antar Ruang
            // echo '<pre>';print_r($data);exit();
            $ref = $post['id'];
            $this->FormulirPindahRuanganModel->ubah($ref, $data);
          } else {
            // Simpan Formulir Perpindahan Pasien Antar Ruang
            $data['pengirim'] = $oleh;
            $data['kunjungan'] = $nokun;
            // echo '<pre>';print_r($data);exit();
            $ref = $this->FormulirPindahRuanganModel->simpan($data);
          }

          // Tinggi dan Berat Badan
          $dataTBB = array(
            'jenis' => isset($post['tb_bb']) ? ($post != '4663' ? 1 : 0) : 0,
            'tb' => isset($post['tb']) ? round($post['tb'], 2) : null,
            'bb' => isset($post['bb']) ? round($post['bb'], 2) : null,
            'oleh' => $oleh,
          );
          // echo '<pre>';print_r($dataTBB);exit();
          if (isset($post['id'])) {
            // Ubah Tinggi dan Berat Badan
            $this->OTKeperawatanModel->ubahTbBb($ref, $dataSource, $dataTBB);
          } else {
            // Simpan Tinggi dan Berat Badan
            $dataTBB['data_source'] = $dataSource;
            $dataTBB['ref'] = $ref;
            $dataTBB['nomr'] = $nomr;
            $dataTBB['nokun'] = $nokun;
            $dataTBB['status'] = $status;
            $this->TbBbModel->insert($dataTBB);
          }

          // Kesadaran
          $dataKesadaran = array(
            'kesadaran' => isset($post['kesadaran']) ? $post['kesadaran'] : null,
            'oleh' => $oleh,
          );
          // echo '<pre>';print_r($dataKesadaran);exit();
          if (isset($post['id'])) {
            // Ubah Kesadaran
            $this->OTKeperawatanModel->ubahKesadaran($ref, $dataSource, $dataKesadaran);
          } else {
            // Simpan Kesadaran
            $dataKesadaran['data_source'] = $dataSource;
            $dataKesadaran['ref'] = $ref;
            $dataKesadaran['nokun'] = $nokun;
            $dataKesadaran['nomr'] = $nomr;
            $dataKesadaran['status'] = $status;
            $this->OTKeperawatanModel->simpanKesadaran($dataKesadaran);
          }

          // Tanda Vital
          $dataTV = array(
            'td_sistolik' => isset($post['td_sistolik']) ? round($post['td_sistolik'], 2) : null,
            'td_diastolik' => isset($post['td_diastolik']) ? round($post['td_diastolik'], 2) : null,
            'nadi' => isset($post['nadi']) ? round($post['nadi'], 2) : null,
            'pernapasan' => isset($post['pernapasan']) ? round($post['pernapasan'], 2) : null,
            'suhu' => isset($post['suhu']) ? round($post['suhu'], 2) : null,
            'oleh' => $oleh,
          );
          // echo '<pre>';print_r($dataTV);exit();
          if (isset($post['id'])) {
            // Ubah Tanda Vital
            $this->OTKeperawatanModel->ubahTandaVital($ref, $dataSource, $dataTV);
          } else {
            // Simpan Tanda Vital
            $dataTV['data_source'] = $dataSource;
            $dataTV['ref'] = $ref;
            $dataTV['nomr'] = $nomr;
            $dataTV['nokun'] = $nokun;
            $dataTV['status'] = $status;
            $this->OTKeperawatanModel->simpanTandaVital($dataTV);
          }

          // Pemantauan Nyeri
          $dataNyeri = array(
            'metode' => isset($post['skrining_nyeri']) ? $post['skrining_nyeri'] : null,
            'skor' => isset($post['skala_nyeri']) ? $post['skala_nyeri'] : null,
            'farmakologi' => isset($post['farmakologi']) ? $post['farmakologi'] : null,
            'non_farmakologi' => isset($post['non_farmakologi']) ? $post['non_farmakologi'] : null,
            'efek_samping' => isset($post['efek_samping']) ? $post['efek_samping'] : null,
            'ket_efek_samping' => isset($post['ket_efek_samping']) ? $post['ket_efek_samping'] : null,
            'provokative' => isset($post['provocative']) ? $post['provocative'] : null,
            'quality' => isset($post['quality']) ? $post['quality'] : null,
            'quality_lainnya' => isset($post['ket_quality']) ? $post['ket_quality'] : null,
            'regio' => isset($post['regio']) ? $post['regio'] : null,
            'severity' => isset($post['severity']) ? $post['severity'] : null,
            'time' => isset($post['time']) ? $post['time'] : null,
            'ket_time' => isset($post['ket_time']) ? $post['ket_time'] : null,
            'created_by' => $oleh,
          );
          // echo '<pre>';print_r($dataNyeri);exit();
          if (isset($post['id'])) {
            // Ubah Pemantauan Nyeri
            $this->PemantauanNyeriModel->ubah($ref, $dataSource, $dataNyeri);
          } else {
            // Simpan Pemantauan Nyeri
            $dataNyeri['nokun'] = $nokun;
            $dataNyeri['data_source'] = $dataSource;
            $dataNyeri['ref'] = $ref;
            $dataNyeri['status'] = $status;
            $this->PemantauanNyeriModel->simpan($dataNyeri);
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function history()
  {
    $post = $this->input->post();
    $uri4 = $this->uri->segment(4);
    $data = array(
      'laporan' => isset($post['nomr']) ? false : true,
      'nomr' => isset($post['nomr']) ? $post['nomr'] : $uri4,
    );
    $this->load->view('Pengkajian/formulirPindahRuangan/history', $data);
  }

  public function tabel()
  {
    $draw = intval($this->input->POST('draw'));
    $nomr = $this->input->POST('nomr');
    $history = $this->FormulirPindahRuanganModel->history($nomr, 'tabel', null);
    $data = array();
    $no = 1;
    $disabled = null;
    $status = null;

    foreach ($history->result() as $h) {
      if ($h->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($h->status == 1) {
        $disabled = '';
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        $h->NORM,
        $h->ruang_awal,
        $h->ruang_tujuan,
        $h->pengirim,
        date('d-m-Y', strtotime($h->tanggal)),
        date('H:i', strtotime($h->jam)),
        $h->dpjp,
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-pr' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-pr' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-detail-pr' class='btn btn-sm btn-primary waves-effect' id='tbl-detail-pr' data-toggle='modal' data-id='" . $h->id . "' $disabled>
            <i class='fa fa-eye'></i> Lihat
          </button>
          <a href='/reports/simrskd/PemindahanRuangan/PindahRuangan.php?format=pdf&id=" . $h->id . "' class='btn btn-sm btn-warning waves-effect' target='_blank'>
            <i class='fa fa-print'></i> Cetak
          </a>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $history->num_rows(),
      'recordsFiltered' => $history->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = $post['id'];

    $data = array(
      'status' => 0,
    );
    $this->FormulirPindahRuanganModel->ubah($id, $data);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }
}

/* End of file FormulirPindahRuangan */
/* Location: ./application/controllers/transferRuangan/FormulirPindahRuangan */