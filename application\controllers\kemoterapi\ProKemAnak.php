<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ProKemAnak extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses')) && !in_array(44, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      array(
        'masterModel',
        'pengkajianAwalModel',
        'rekam_medis/TbBbModel',
        'kemoterapi/ProKemAnakModel',
      )
    );
  }

  public function index()
  {
    $data = array(
      'nokun' => $this->uri->segment(2),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/kemoterapi/ProKemAnak/index', $data);
  }

  public function tabelProKem()
  {
    $draw = intval($this->input->POST('draw'));
    $nokun = $this->input->post('nokun');
    // echo '<pre>';print_r($nokun);exit();
    $tabelProKem = $this->ProKemAnakModel->tabelProKem($nokun);
    $data = array();
    $no = 1;
    $disabled = null;
    // echo '<pre>';print_r($tabelProKem);exit();

    foreach ($tabelProKem->result() as $t) {
      if ($t->status == 0) {
        $disabled = 'disabled';
        $status = '<p class="text-danger">Dibatalkan</p>';
      } elseif ($t->status == 1) {
        $disabled = null;
        $status = '<p class="text-success">Diterima</p>';
      }

      $data[] = array(
        $no,
        date('d-m-Y', strtotime($t->tgl_diagnosis)),
        $t->prokem,
        $t->DPJP,
        $t->pengisi,
        date('d-m-Y, H:i:s', strtotime($t->created_at)),
        $status,
        "<div class='btn-group' role='group'>
          <button type='button' href='#modal-batal-pro-kem-anak' class='btn btn-sm btn-danger waves-effect' id='tbl-batal-pro-kem-anak' data-toggle='modal' data-id='" . $t->id . "' $disabled>
            <i class='fa fa-window-close'></i> Batal
          </button>
          <button type='button' href='#modal-terapi-pro-kem-anak' class='btn btn-sm btn-success waves-effect' id='tbl-terapi-pro-kem-anak' data-toggle='modal' data-id='" . $t->id . "-" . $t->id_pro_kem . "-" . $nokun . "' $disabled>
            <i class='fas fa-procedures'></i> Terapi
          </button>
        </div>",
      );
      $no++;
    }

    $output = array(
      'draw' => $draw,
      'recordsTotal' => $tabelProKem->num_rows(),
      'recordsFiltered' => $tabelProKem->num_rows(),
      'data' => $data
    );
    echo json_encode($output);
  }

  public function tambah()
  {
    $post = $this->input->post();
    $data = array(
      'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
      'pilihProKem' => $this->ProKemAnakModel->pilihProKem(),
      'listDr' => $this->masterModel->listDrUmum(),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/kemoterapi/ProKemAnak/tambah', $data);
  }

  public function tabelObat()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array(
      'tabelObat' => $this->ProKemAnakModel->tabelObat($id, null, null),
    );
    //  echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/kemoterapi/ProKemAnak/tabelObat', $data);
  }

  public function aksi($param)
  {
    $this->db->trans_begin();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'simpanProKem') {
        $rules = $this->ProKemAnakModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();

          // Mulai simpan data protokol kemoterapi
          $data = array(
            'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
            'tgl_diagnosis' => isset($post['tgl_diagnosis']) ? $post['tgl_diagnosis'] : null,
            'pro_kem' => isset($post['pro_kem']) ? $post['pro_kem'] : null,
            'dpjp' => isset($post['dpjp']) ? $post['dpjp'] : null,
            'status' => 1,
            'oleh' => $this->session->userdata['id'],
          );
          // echo '<pre>';print_r($data);exit();
          $id = $this->ProKemAnakModel->simpanProKem($data);
          // Akhir simpan data protokol kemoterapi

          // Mulai simpan data obat protokol kemoterapi
          if (isset($post['id_pro_kem_anak_detail'])) {
            $i = 0;
            $dataObat = array();
            foreach ($post['id_pro_kem_anak_detail'] as $o) {
              $dataObat[$i] = array(
                'id_form_pro_kem_anak' => $id,
                'id_pro_kem_anak_detail' => $post['id_pro_kem_anak_detail'][$i],
              );
              $i++;
            }
            // echo '<pre>';print_r($dataObat);exit();
            $this->ProKemAnakModel->simpanObat($dataObat);
          }
          // Akhir simpan data obat protokol kemoterapi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'simpanTerapi') {
        $rules = $this->ProKemAnakModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $nokun = isset($post['nokun']) ? $post['nokun'] : null;
          $pasien = $this->pengkajianAwalModel->getNomr($nokun);
          $oleh = $this->session->userdata['id'];
          $status = 1;
          $waktu = date('Y-m-d H:i:s');
          // echo '<pre>';print_r($post);exit();

          if (isset($post['id_terapi'])) {
            $idTerapi = $post['id_terapi'];

            // Mulai ubah terapi
            $dataTerapi = array(
              'tgl_terapi' => isset($post['tgl_terapi']) ? $post['tgl_terapi'] : null,
              'oleh' => $oleh,
            );
            // echo '<pre>';print_r($dataTerapi);exit();
            $this->ProKemAnakModel->ubahTerapi($dataTerapi, $idTerapi);
            // Akhir ubah terapi

            // Mulai ubah tinggi dan berat badan
            $data = array(
              'tb' => isset($post['tinggi']) ? round($post['tinggi'], 2) : null,
              'bb' => isset($post['berat']) ? round($post['berat'], 2) : null,
              'lpb' => isset($post['lpb']) ? round($post['lpb'], 2) : null,
              'oleh' => $oleh,
            );
            // echo '<pre>';print_r($data);exit();
            $this->TbBbModel->ubahRef($data, $idTerapi);
            // Akhir ubah tinggi dan berat badan

            // Mulai batal data dosis siklus kemoterapi
            $data = array('status' => 0);
            $this->ProKemAnakModel->ubahSiklus($data, $idTerapi);
            // Akhir batal data dosis siklus kemoterapi

            // Mulai simpan data dosis siklus kemoterapi
            if (isset($post['id_form_pro_kem_anak_obat'])) {
              $i = 0;
              $dataSiklus = array();
              foreach ($post['id_form_pro_kem_anak_obat'] as $s) {
                $dataSiklus[$i] = array(
                  'id_form_pro_kem_anak_terapi' => $idTerapi,
                  'id_form_pro_kem_anak_obat' => $post['id_form_pro_kem_anak_obat'][$i],
                  'dosis' => $post['dosis'][$i],
                  'keterangan' => $post['keterangan'][$i],
                );
                $i++;
              }
              // echo '<pre>';print_r($dataSiklus);exit();
              $id = $this->ProKemAnakModel->simpanSiklus($dataSiklus);
            }
            // Akhir simpan data dosis siklus kemoterapi
          } else {
            // Mulai simpan data terapi
            $dataTerapi = array(
              'id_form_pro_kem_anak' => isset($post['id']) ? $post['id'] : null,
              'id_pro_kem_anak_pemberian' => isset($post['siklus']) ? $post['siklus'] : null,
              'tgl_terapi' => isset($post['tgl_terapi']) ? $post['tgl_terapi'] : null,
              'oleh' => $oleh,
              'status' => $status,
              'created_at' => $waktu,
            );
            // echo '<pre>';print_r($dataTerapi);exit();
            $id = $this->ProKemAnakModel->simpanTerapi($dataTerapi);
            // Akhir simpan data terapi

            // Mulai simpan ke tinggi dan berat badan
            $data = array(
              'data_source' => 32,
              'ref' => $id,
              'nomr' => $pasien['NORM'],
              'nokun' => $nokun,
              'tb' => isset($post['tinggi']) ? round($post['tinggi'], 2) : null,
              'bb' => isset($post['berat']) ? round($post['berat'], 2) : null,
              'lpb' => isset($post['lpb']) ? round($post['lpb'], 2) : null,
              'oleh' => $oleh,
              'status' => $status,
              'created_at' => $waktu,
            );
            // echo '<pre>';print_r($data);exit();
            $this->TbBbModel->insert($data);
            // Akhir simpan ke tinggi dan berat badan

            // Mulai simpan data dosis siklus kemoterapi
            if (isset($post['id_form_pro_kem_anak_obat'])) {
              $i = 0;
              $dataSiklus = array();
              $dataPemberianTerapi = array();
              foreach ($post['id_form_pro_kem_anak_obat'] as $s) {
                $dataSiklus[$i] = array(
                  'id_form_pro_kem_anak_terapi' => $id,
                  'id_form_pro_kem_anak_obat' => $post['id_form_pro_kem_anak_obat'][$i],
                  'dosis' => $post['dosis'][$i],
                  'keterangan' => $post['keterangan'][$i],
                  'oleh' => $oleh,
                  'status' => $status,
                  'created_at' => $waktu,
                );
                // echo '<pre>';print_r($dataSiklus);exit();
                $idSiklus[$i] = $this->ProKemAnakModel->simpanSiklus($dataSiklus[$i]);

                $j = 0;
                $tglRencanaPemberian[$i] = explode(', ', $post['tgl_rencana_pemberian'][$i]);
                foreach ($tglRencanaPemberian[$i] as $t) {
                  $bagTglRencanaPemberian[$i][$j] = explode('/', $tglRencanaPemberian[$i][$j]);
                  $hari[$i][$j] = $bagTglRencanaPemberian[$i][$j][0];
                  $bulan[$i][$j] = $bagTglRencanaPemberian[$i][$j][1];
                  $tahun[$i][$j] = $bagTglRencanaPemberian[$i][$j][2];
                  $tanggalBaru[$i][$j] = array($bagTglRencanaPemberian[$i][$j][2], $bagTglRencanaPemberian[$i][$j][1], $bagTglRencanaPemberian[$i][$j][0]);
                  $dataPemberianTerapi[$i][$j] = array(
                    'id_form_pro_kem_anak_siklus' => $idSiklus[$i],
                    'tgl_rencana_pemberian' => implode('-', $tanggalBaru[$i][$j]),
                    'oleh' => $oleh,
                    'status' => $status,
                    'created_at' => $waktu,
                  );
                  // echo '<pre>';print_r($dataPemberianTerapi[$i]);exit();
                  $this->ProKemAnakModel->simpanPemberianTerapi($dataPemberianTerapi[$i][$j]);
                  $j++;
                }
                $i++;
              }
            }
            // Akhir simpan data dosis siklus kemoterapi
          }

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'hapusTerapi') {
        $rules = $this->ProKemAnakModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          $idTerapi = $post['id_terapi'];
          $status = 0;
          // echo '<pre>';print_r($idTerapi);exit();

          // Mulai ubah terapi
          $dataTerapi = array('status' => $status);
          // echo '<pre>';print_r($dataTerapi);exit();
          $this->ProKemAnakModel->ubahTerapi($dataTerapi, $idTerapi);
          // Akhir ubah terapi

          // Mulai batal data dosis siklus kemoterapi
          $data = array('status' => 0);
          $this->ProKemAnakModel->ubahSiklus($data, $idTerapi);
          // Akhir batal data dosis siklus kemoterapi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      } elseif ($param == 'ubahPemberianTerapi') {
        $rules = $this->ProKemAnakModel->rules;
        $this->form_validation->set_rules($rules);
        if ($this->form_validation->run() == true) {
          $post = $this->input->post();
          // echo '<pre>';print_r($post);exit();

          // Mulai simpan data pemberian terapi
          if (isset($post['id_pemberian_terapi'])) {
            $i = 0;
            $dataPemberianTerapi = array();
            foreach ($post['id_pemberian_terapi'] as $pt) {
              $dataPemberianTerapi[$i] = array(
                'keterangan_pemberian' => $post['keterangan_pemberian'][$i],
                'mulai' => $post['mulai'][$i],
                'selesai' => $post['selesai'][$i],
                'perawat_1' => $post['perawat_1'][$i],
                'perawat_2' => $post['perawat_2'][$i]
              );
              // echo '<pre>';print_r($dataPemberianTerapi[$i]);exit();
              $this->ProKemAnakModel->ubahPemberianTerapi($dataPemberianTerapi[$i], $post['id_pemberian_terapi'][$i]);
              $i++;
            }
          }
          // Akhir simpan data pemberian terapi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
          } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
          }
        } else {
          $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
        }
        echo json_encode($result);
      }
    }
  }

  public function terapi()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $idProKem = isset($post['id_pro_kem']) ? $post['id_pro_kem'] : null;
    $siklusTabel = $this->ProKemAnakModel->siklus($idProKem, null, null);
    $jenisSiklus = $this->ProKemAnakModel->siklus($idProKem, null, 'jenis');
    $historyTerapi = array();
    $i = 0;
    $j = 0; // Sebagai jumlah siklus dan ID siklus
    foreach ($jenisSiklus as $js) {
      $k = 1;
      foreach ($siklusTabel as $s) {
        if ($js['JENIS'] == $s['JENIS']) {
          $jenisSiklus[$i]['JUMLAH'] = $k++;
          $historyTerapi[++$j] = $this->ProKemAnakModel->historyTerapi($id, $j, 'tabel');
        }
      }
      $i++;
    }
    $data = array(
      'id' => $id,
      'idProKem' => $idProKem,
      'nokun' => isset($post['nokun']) ? $post['nokun'] : null,
      'siklus' => $this->ProKemAnakModel->siklus($idProKem, $id, null),
      'siklusTersimpan' => $this->ProKemAnakModel->siklus($idProKem, $id, 'tersimpan'),
      'jumlahSiklus' => $j,
      'siklusTabel' => $siklusTabel,
      'jenisSiklus' => $jenisSiklus,
      'tabelObat' => $this->ProKemAnakModel->tabelObat($idProKem, null, null),
      'historyTerapi' => $historyTerapi,
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/kemoterapi/ProKemAnak/terapi/index', $data);
  }

  public function tabelDosis($param)
  {
    $post = $this->input->post();
    $idProKem = isset($post['id_pro_kem']) ? $post['id_pro_kem'] : null;
    $idSiklus = isset($post['id_siklus']) ? $post['id_siklus'] : null;
    if ($param == 'input') {
      $data = array(
        'tabelDosis' => $this->ProKemAnakModel->tabelDosis($idSiklus, $idProKem),
      );
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/kemoterapi/ProKemAnak/terapi/tabelDosis', $data);
    } else if ($param = 'history') {
      $data = array(
        'historyTerapi' => $this->ProKemAnakModel->historyTerapi($idSiklus, $idProKem, null),
      );
      // echo '<pre>';print_r($data);exit();
      $this->load->view('Pengkajian/kemoterapi/ProKemAnak/terapi/history', $data);
    }
  }

  public function batal()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    // echo '<pre>';print_r($id);exit();

    $data = array('status' => 0);
    $this->ProKemAnakModel->ubahProKem($data, $id);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  public function pemberianTerapi()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array(
      'id' => $id,
      'listPerawat' => $this->masterModel->listPerawat(),
      'pemberianTerapi' => $this->ProKemAnakModel->pemberianTerapi($id),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/kemoterapi/ProKemAnak/terapi/pemberianTerapi', $data);
  }

  public function keteranganPemberian()
  {
    $post = $this->input->post();
    $id = isset($post['id']) ? $post['id'] : null;
    $data = array(
      'keteranganPemberian' => $this->ProKemAnakModel->keteranganPemberian($id),
    );
    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/kemoterapi/ProKemAnak/terapi/keteranganPemberian', $data);
  }
}

/* End of file ProKemAnak.php */
/* Location: ./application/controllers/kemoterapi/ProKem.php */